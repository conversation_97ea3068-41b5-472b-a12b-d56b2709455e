version: "3.5"

services:

  mongo:
    image: rdocker.ptin.corppt.com/extsw/mongodb:4.4.6
    hostname: naportal-ci.c.ptin.corppt.com
    container_name: mongo
    volumes:
      - ./files/mongo/mongod.conf:/etc/mongod.conf
      - ./files/mongo/data/db/:/var/lib/mongo
    ports:
      - "27017:27017"
    deploy:
      resources:
        limits:
          memory: 3G

  mongo-sidecar-it:
    image: rdocker.ptin.corppt.com/nadm/nadm-mongo-client-sidecar:5.5
    hostname: mongo-sidecar-it
    environment:
      - MONGO_URI
      - NADM_DB_NAME=veacs
    depends_on:
      - mongo
    volumes:
      - ./files/mongo-sidecar-it/replica_conf.json:/replica_config/replica_conf.json
      - ./files/mongo-sidecar-it/ZZZ_configure-users.js:/docker-entrypoint-scripts.d/ZZZ_configure-users.js

  nadm:
    image: rdocker.ptin.corppt.com/nadm/nadm:5.5
    hostname: naportal-ci
    container_name: nadm
    volumes:
      - ./files/nadm/all.yml:/config/all.yml
      - ./logs/nadm:/var/log/alticelabs/naem/
    depends_on:
      - mongo
    ports:
      - "9090:9090"
      - "5560:5555"

  na-portal-data-management-sidecar:
    hostname: na-portal-data-management-sidecar
    container_name: na-portal-data-management-sidecar
    image: rdocker.ptin.corppt.com/na-portal-tst/na-portal-data-management-sidecar:10.3.0
    volumes:
      - /tmp/catalogs:/tmp
    entrypoint: "bash"
    tty: true
