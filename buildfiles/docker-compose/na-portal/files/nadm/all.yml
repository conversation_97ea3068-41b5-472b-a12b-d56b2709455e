---

naem_java_opts: -Xmx512M -Xms512M -Xss256K

naem_nbi_host: naportal-ci.c.ptin.corppt.com
naem_nbi_port: 9090
nadm_db_name: veacs
naem_db_profile: mongolocal
mongodb_uri: "mongodb://naportal:<EMAIL>:27017/naportal"

nadm_hostname: naportal-ci
ansible_hostname: naportal-ci

log4j2:
  rollingFile:
    SERVER:
      file: "/var/log/alticelabs/naem/server.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    AUDIT-INTERACTIONS:
      file: "/var/log/alticelabs/naem/audit-interactions.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    AUDIT-PAYLOAD:
      file: "/var/log/alticelabs/naem/audit-payload.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    LIB-EXCEPTION:
      file: "/var/log/alticelabs/naem/exceptions.lib.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    BOOTSTRAP:
      file: "/var/log/alticelabs/naem/bootstrap.log"
      maxFileSize: "100MB"
      maxBackupIndex: "1"
    LOGGING-EVENTDISPATCHER:
      file: "/var/log/alticelabs/naem/eventdispatcher.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    LOGGING-EVENTS:
      file: "/var/log/alticelabs/naem/events.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    LOGGING-ENVELOPES:
      file: "/var/log/alticelabs/naem/envelopes.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    LOGGING-COMMUNICATOR-OUT:
      file: "/var/log/alticelabs/naem/communicator-out.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    NOSSIS:
      file: "/var/log/alticelabs/naem/nossis.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    LIBS:
      file: "/var/log/alticelabs/naem/lib.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    ASF:
      file: "/var/log/alticelabs/naem/asf.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    STATISTICS-RESOURCES:
      file: "/var/log/alticelabs/naem/statistics.resources.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    STATISTICS-MAILBOX:
      file: "/var/log/alticelabs/naem/statistics.mailbox.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    EXCEPTIONS-SERVER:
      file: "/var/log/alticelabs/naem/exceptions.server.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    CORE-PLUGIN-ESPER:
      file: "/var/log/alticelabs/naem/core-plugin-esper.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    CORE-PLUGIN-DBMANAGER-MONGO:
      file: "/var/log/alticelabs/naem/core-plugin-dbmanager-mongodb.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    CORE-PLUGIN-NA:
      file: "/var/log/alticelabs/naem/core-plugin-na.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    WF-NAEM-CORE-FILTERUPDATER:
      file: "/var/log/alticelabs/naem/wf-naem-core-filterupdater.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    WF-NAEM-CORE-EVENTDISPATCHER:
      file: "/var/log/alticelabs/naem/wf-naem-core-eventdispatcher.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    WF-NAEM-MANAGEMENT-FILTERING:
      file: "/var/log/alticelabs/naem/wf-naem-management-filtering.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    EXCEPTIONS-WORKFLOWS:
      file: "/var/log/alticelabs/naem/exceptions.workflows.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    LOGGING-WEB-SERVICE:
      file: "/var/log/alticelabs/naem/webservice-io.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    NADM-REST-AUDIT:
      file: "/var/log/alticelabs/naem/nadm-rest-audit.log"
      maxFileSize: "10MB"
      maxBackupIndex: "2"
    NADM-REST-COUNT-STATS:
      file: "/var/log/alticelabs/naem/nadm-rest-count-stats.log"
      maxFileSize: "10MB"
      maxBackupIndex: "2"
  loggers:
    - { name: "pt.ptinovacao.asf", level: "info", refs: [ "ASF", "EXCEPTIONS-SERVER:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.asf.resourcemanagement.ResourceManagerPlugin.allocations", level: "info", refs: [ "STATISTICS-RESOURCES", "EXCEPTIONS-SERVER:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.nacommons.audit.core.AuditWriter.interaction", level: "info", refs: [ "AUDIT-INTERACTIONS", "LIB-EXCEPTION:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.nacommons.audit.core.AuditWriter.payload", level: "info", refs: [ "AUDIT-PAYLOAD", "LIB-EXCEPTION:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.nacommons.plugins.esper.EsperPlugin", level: "info", refs: [ "CORE-PLUGIN-ESPER", "EXCEPTIONS-SERVER:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.nacommons.plugins.dbmanager.core.connectors.mongo.MongoDao", level: "info", refs: [ "CORE-PLUGIN-DBMANAGER-MONGO", "EXCEPTIONS-SERVER:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.nacommons.plugins.http.client.HttpPlugin", level: "info", refs: [ "LOGGING-WEB-SERVICE" ], additivity: "false" }
    - { name: "pt.ptinovacao.nacommons.plugins.http.webservice.io", level: "info", refs: [ "LOGGING-WEB-SERVICE" ], additivity: "false" }
    - { name: "pt.ptinovacao.nacommons.plugins.http.collector.HttpCollectorPlugin.webserviceio", level: "info", refs: [ "LOGGING-WEB-SERVICE" ], additivity: "false" }
    - { name: "pt.ptinovacao.nacommons.plugins.http.client.HttpPluginMediator", level: "info", refs: [ "LOGGING-WEB-SERVICE" ], additivity: "false" }
    - { name: "pt.ptinovacao.nacommons.naclient", level: "info", refs: [ "CORE-PLUGIN-NA", "EXCEPTIONS-SERVER:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.nadm.filter.AuditUtils", level: "info", refs: ["NADM-REST-AUDIT", "LIB-EXCEPTION"], additivity: "false" }
    - { name: "pt.ptinovacao.naem.commons.core.boot", level: "info", refs: [ "BOOTSTRAP", "EXCEPTIONS-SERVER:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.naem.workflows.filtering.FilterWorkflow", level: "warn", refs: [ "WF-NAEM-CORE-EVENTDISPATCHER", "EXCEPTIONS-WORKFLOWS:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.naem.workflows.filtering.FilterUpdater", level: "info", refs: [ "WF-NAEM-CORE-FILTERUPDATER", "EXCEPTIONS-WORKFLOWS:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.naem.workflows.filtering.manager.FilterManager", level: "info", refs: [ "WF-NAEM-MANAGEMENT-FILTERING", "EXCEPTIONS-WORKFLOWS:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.naem.workflows.filtering.manager.FilterManagerHttpAdapter", level: "info", refs: [ "WF-NAEM-MANAGEMENT-FILTERING", "EXCEPTIONS-WORKFLOWS:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.naem.workflows", level: "info", refs: [ "SERVER", "EXCEPTIONS-WORKFLOWS:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.naem.commons.core.kernel.broker.FilteringLogger", level: "info", refs: [ "LOGGING-EVENTDISPATCHER" ], additivity: "false" }
    - { name: "pt.ptinovacao.naem.commons.core.kernel.broker.EventLogger", level: "info", refs: [ "LOGGING-EVENTS" ], additivity: "false" }
    - { name: "pt.ptinovacao.naem.commons.core.kernel.broker.EnvelopeLogger", level: "info", refs: [ "LOGGING-ENVELOPES" ], additivity: "false" }
    - { name: "pt.ptinovacao.naem.plugins.communicator.CommunicatorPlugin", level: "info", refs: [ "LOGGING-COMMUNICATOR-OUT" ], additivity: "false" }
    - { name: "pt.ptinovacao.nossisfrmwrk", level: "info", refs: [ "NOSSIS", "EXCEPTIONS-SERVER:error" ], additivity: "false" }
    - { name: "httpclient.wire", level: "info", refs: [ "LIBS", "LIB-EXCEPTION:error" ], additivity: "false" }
    - { name: "com", level: "warn", refs: [ "LIBS", "LIB-EXCEPTION:error" ], additivity: "false" }
    - { name: "org", level: "warn", refs: [ "LIBS", "LIB-EXCEPTION:error" ], additivity: "false" }
    - { name: "COUNT_STATS", level: "info", refs: ["NADM-REST-COUNT-STATS", "LIB-EXCEPTION"], additivity: "false" }
    - { name: "pt.ptinovacao.nacommons.utils.akka.mailbox.CommonsMailboxStatistics", level: "info", refs: ["STATISTICS-MAILBOX"], additivity: "false" }
  root:
    level: "info"
    refs: [ "SERVER", "EXCEPTIONS-SERVER:error" ]

