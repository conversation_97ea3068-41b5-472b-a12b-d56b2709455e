<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>pt.ptinovacao.na-commons</groupId>
        <artifactId>na-commons-pom</artifactId>
        <version>5.1.0</version>
    </parent>

    <groupId>pt.ptinovacao.na-portal</groupId>
    <artifactId>na-portal</artifactId>
    <version>100.0.0</version>
    <packaging>pom</packaging>

    <!-- COMMON PROPERTIES -->
    <properties>
        <jira.login>ci-na-portal</jira.login>
        <jira.password>c1-na-portal</jira.password>
        <!-- NA-COMMONS variables -->
        <ud.key>NAPORTAL</ud.key>
        <application.name>na-portal</application.name>
        <version.pt.ptinovacao.na-commons>5.1.0</version.pt.ptinovacao.na-commons>
        <version.pt.ptinovacao.na-commons.max>6.0.0</version.pt.ptinovacao.na-commons.max>
        <!-- maven-compiler-plugin -->
        <version.java>1.8</version.java>
        <java.source.level>${version.java}</java.source.level>
        <java.target.level>${version.java}</java.target.level>

        <!-- OTHERS -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <!--***************** MAIN DEPENDENCIES VERSIONS (MAY CHANGE OVER RELEASES!) ****************-->

        <!-- hibernate -->
        <version.org.hibernate.ogm>5.0.4.Final</version.org.hibernate.ogm>
        <version.org.hibernate.hql>1.3.0.Final</version.org.hibernate.hql>
        <version.org.hibernate.hibernate-core>5.4.33.Final</version.org.hibernate.hibernate-core>
        <version.org.hibernate.hibernate-validator>5.4.3.Final</version.org.hibernate.hibernate-validator>
        <version.org.hibernate.entitymanager>5.4.33.Final</version.org.hibernate.entitymanager>

        <!-- jboss deps -->
        <version.javax.enterprise.cdi-api>1.2</version.javax.enterprise.cdi-api>
        <version.javax.activation>1.1.1</version.javax.activation>
        <version.javax.interceptor>1.2</version.javax.interceptor>
        <version.org.jberet>1.2.1.Final</version.org.jberet>
        <version.org.jboss.logmanager>2.1.7.Final</version.org.jboss.logmanager>
        <version.org.jboss.jboss-logging-annotations>2.1.0.Final</version.org.jboss.jboss-logging-annotations>
        <version.org.jboss.jboss-logging-processor>2.1.0.Final</version.org.jboss.jboss-logging-processor>
        <version.org.hibernate.javax.persistence.hibernate-jpa-2.1-api>1.0.0.Final</version.org.hibernate.javax.persistence.hibernate-jpa-2.1-api>
        <version.org.jboss.dmr>1.3.0.Final</version.org.jboss.dmr>
        <version.org.jboss.weld>2.3.SP1</version.org.jboss.weld>
        <version.org.jboss.logging.jboss-logging>3.3.0.Final</version.org.jboss.logging.jboss-logging>

        <version.org.eclipse.persistence.eclipselink>2.6.4</version.org.eclipse.persistence.eclipselink>
        <version.javax.validation>1.1.0.Final</version.javax.validation>
        <version.javax.el-api>3.0.0</version.javax.el-api>
        <version.javax.javee-api>7.0</version.javax.javee-api>
        <version.javax.jcr>2.0</version.javax.jcr>
        <version.org.jboss.spec>1.0.0.Final</version.org.jboss.spec>
        <version.org.jboss.ejb3.jboss-ejb3-ext-api>2.2.0.Final</version.org.jboss.ejb3.jboss-ejb3-ext-api>
        <version.io.undertow>2.2.32.Final</version.io.undertow>
        <version.org.infinispan>8.1.6.Final</version.org.infinispan>

        <!-- shared -->
        <version.backport-util-concurrent>3.1</version.backport-util-concurrent>
        <version.com.google.guava>23.0</version.com.google.guava>
        <version.com.google.code.findbugs>3.0.2</version.com.google.code.findbugs>
        <version.commons-cli>1.3</version.commons-cli>
        <version.com.thoughtworks.paranamer>2.8</version.com.thoughtworks.paranamer>
        <version.digester3>3.2</version.digester3>
        <version.javax.inject>1</version.javax.inject>
        <version.org.apache.james>0.6</version.org.apache.james>
        <version.org.apache.jackrabbit>2.21.9</version.org.apache.jackrabbit>
        <version.org.glassfish.javax.json>1.0.3</version.org.glassfish.javax.json>
        <version.org.mnode.ical4j>1.0.6</version.org.mnode.ical4j>
        <version.org.powermock>1.6.2</version.org.powermock>
        <version.org.yaml.snakeyaml>1.15</version.org.yaml.snakeyaml>
        <version.saxon>9.1.0.8</version.saxon>
        <version.xerces.xercesImpl>2.9.1</version.xerces.xercesImpl>
        <!-- database drivers versions -->
        <version.com.oracle.jdbc.ojdbc7>12.1.0.2</version.com.oracle.jdbc.ojdbc7>
        <version.org.postgresql>42.7.3</version.org.postgresql>

        <!-- PTIN SSO Dependencies -->
        <version.pt.ptinovacao.na>10.1.0</version.pt.ptinovacao.na>
        <version.pt.ptinovacao.nadm>5.5.0</version.pt.ptinovacao.nadm>
        <version.pt.ptinovacao.naem>5.5.0</version.pt.ptinovacao.naem>
        <version.pt.ptinovacao.naem.max>6.0.0</version.pt.ptinovacao.naem.max>
        <!--Note : na-smart2m-wsdl is in a earlier version because we support the deprecated version of wsdl -->
        <version.pt.ptinovacao.na.na-smart2m-wsdl>4.6.2</version.pt.ptinovacao.na.na-smart2m-wsdl>

        <version.pt.ptinovacao.nossis-inv>4.17.0</version.pt.ptinovacao.nossis-inv>
        <version.pt.ptinovacao.nossis-tsc>5.1.0</version.pt.ptinovacao.nossis-tsc>
        <version.pt.ptinovacao.nossis-ui>3.21.0</version.pt.ptinovacao.nossis-ui>
        <version.pt.ptinovacao.nossis-security>2.11.0</version.pt.ptinovacao.nossis-security>

        <!-- WEB Dependencies versions -->
        <version.com.typesafe.play>2.6.25</version.com.typesafe.play>
        <version.com.typesafe.play.play-json>2.6.14</version.com.typesafe.play.play-json>
        <version.com.typesafe.play.twirl-api>1.3.16</version.com.typesafe.play.twirl-api>

        <version.com.google.inject>4.1.0</version.com.google.inject>

        <version.org.scala-lang.modules>1.0.6</version.org.scala-lang.modules>

        <version.commons.io>2.4</version.commons.io>

        <!-- jboss-as versions-->
        <version.jbossas-standalone>10.0.4</version.jbossas-standalone>
        <!--*****************************************************************************************-->

        <!-- External dependencies-->
        <version.javax.persistence>2.2</version.javax.persistence>
        <version.javax.xml.bind>2.3.1</version.javax.xml.bind>
        <version.net.sf.ehcache>2.10.9.2</version.net.sf.ehcache>
        <version.io.netty>4.1.111.Final</version.io.netty>
        <version.org.springframework>5.3.35</version.org.springframework>
        <version.org.wildfly.core>1.0.2.Final</version.org.wildfly.core>
        <version.org.wildfly.security>1.1.2.Final</version.org.wildfly.security>
        <version.org.animal-sniffer-annotations>1.14</version.org.animal-sniffer-annotations>
        <version.org.error_prone_annotations>2.0.18</version.org.error_prone_annotations>
        <version.org.j2objc-annotations>1.1</version.org.j2objc-annotations>
        <version.org.jboss-annotations-api_1.2_spec>1.0.0.Final</version.org.jboss-annotations-api_1.2_spec>
        <version.org.jboss-jaxrs-api_2.0_spec>1.0.1.Beta1</version.org.jboss-jaxrs-api_2.0_spec>
        <version.org.jboss.transaction-spi>7.1.0.Final</version.org.jboss.transaction-spi>
        <version.org.apache.commons.dbcp2>2.9.0</version.org.apache.commons.dbcp2>
        <version.org.antlr>2.7.7</version.org.antlr>
        <version.mimepull>1.7</version.mimepull>
        <version.org.glassfish.hk2.external.javax.inject>2.5.0-b30</version.org.glassfish.hk2.external.javax.inject>
        <version.org.glassfish.jersey.media.jackson>2.19</version.org.glassfish.jersey.media.jackson>
        <version.org.mongodb.mongo-java-driver>3.3.0</version.org.mongodb.mongo-java-driver>
        <version.org.core4j.core4j>0.5</version.org.core4j.core4j>
        <version.org.javassist>3.27.0-GA</version.org.javassist>
        <version.com.lmax>3.4.4</version.com.lmax>
        <!-- TEST dependencies -->
        <version.com.googlecode.jmockit>0.999.11</version.com.googlecode.jmockit>
        <version.org.seleniumhq.selenium>3.14.0</version.org.seleniumhq.selenium>
        <version.net.schmizz>0.8.1</version.net.schmizz>
        <version.info.cukes.gherkin>2.12.2</version.info.cukes.gherkin>
        <version.info.cukes>1.2.4</version.info.cukes>
        <version.commons-codec>1.10</version.commons-codec>
        <version.hamcrest>1.3</version.hamcrest>
        <version.jchronic>0.2.6</version.jchronic>
        <version.junit>4.11</version.junit>
        <version.org.apache.httpcomponents>4.5.2</version.org.apache.httpcomponents>
        <version.org.specs2.specs2>1.6.1</version.org.specs2.specs2>
        <version.org.specs2.specs2-scalaz-core>6.0.1</version.org.specs2.specs2-scalaz-core>
        <version.xml-apis>1.4.01</version.xml-apis>
        <version.pt.ptinovacao.ssov>4.2.0</version.pt.ptinovacao.ssov>

        <!-- Prometheus exporter -->
        <version.io.prometheus.jmx>0.14.0</version.io.prometheus.jmx>

        <!-- Open telemetry -->
        <version.io.opentelemetry.javaagent>2.1.0</version.io.opentelemetry.javaagent>

        <na-portal.releases.repository.id>releases</na-portal.releases.repository.id>
        <na-portal.releases.repository.url>https://maven.ptin.corppt.com/na-portal-releases</na-portal.releases.repository.url>
        <na-portal.snapshots.repository.id>snapshots</na-portal.snapshots.repository.id>
        <na-portal.snapshots.repository.url>https://maven.ptin.corppt.com/na-portal-snapshots</na-portal.snapshots.repository.url>

        <version.io.kokuwa.maven.helm-maven-plugin>6.2.0</version.io.kokuwa.maven.helm-maven-plugin>
        <!-- update to this version in na-commons base pom -->
        <version.maven-dependency-plugin>3.3.0</version.maven-dependency-plugin>
        <version.maven-resources-plugin>3.3.1</version.maven-resources-plugin>

    </properties>

    <scm>
        <developerConnection>scm:svn:https://svn.ptin.corppt.com/repo/na-portal/trunk</developerConnection>
        <connection>scm:svn:https://svn.ptin.corppt.com/repo/na-portal/trunk</connection>
    </scm>

    <url>http://wiki.ptin.corppt.com/display/NAPORTAL/Home</url>

    <distributionManagement>
        <repository>
            <id>${na-portal.releases.repository.id}</id>
            <name>Project Releases</name>
            <url>${na-portal.releases.repository.url}</url>
        </repository>
        <snapshotRepository>
            <id>${na-portal.snapshots.repository.id}</id>
            <name>Project Snapshots</name>
            <url>${na-portal.snapshots.repository.url}</url>
        </snapshotRepository>
    </distributionManagement>

    <pluginRepositories>
        <!-- TH'A HOUSE PTIN REPOSITORY -->
        <pluginRepository>
            <id>ptin_repo</id>
            <url>https://maven.ptin.corppt.com/repo</url>
        </pluginRepository>
    </pluginRepositories>

    <repositories>
        <!-- TH'A HOUSE PTIN REPOSITORY -->
        <repository>
            <id>ptin_repo</id>
            <url>https://maven.ptin.corppt.com/repo</url>
        </repository>
    </repositories>


    <!-- ////////////////////////////// MAIN POM MODULES //////////////////////////////////////////-->
    <modules>
        <module>src/common</module>
        <module>src/core-jboss</module>
        <module>src/core</module>
        <module>src/nadm</module>
        <module>src/frontend</module>
        <module>tests</module>
        <module>src/dist</module>
    </modules>

    <!-- ******************************* INTERNAL DEPENDENCY MANAGEMENT ************************** -->
    <dependencyManagement>
        <dependencies>
            <!-- force transitive dependency version coming from play-ehcache_2.12
             to solve vulnerabilities issues -->
            <dependency>
                <groupId>net.sf.ehcache</groupId>
                <artifactId>ehcache</artifactId>
                <version>${version.net.sf.ehcache}</version>
            </dependency>
            <!-- force transitive dependency version coming from play-netty-server_2.12
            to solve vulnerabilities issues -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-bom</artifactId>
                <version>${version.io.netty}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- force transitive dependency version coming from play-java-forms_2.12
            to solve vulnerabilities issues -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-framework-bom</artifactId>
                <version>${version.org.springframework}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.typesafe</groupId>
                <artifactId>config</artifactId>
                <version>${version.com.typesafe.config}</version>
            </dependency>
            <dependency>
                <groupId>org.wildfly.core</groupId>
                <artifactId>wildfly-controller</artifactId>
                <version>${version.org.wildfly.core}</version>
            </dependency>
            <dependency>
                <groupId>org.wildfly.core</groupId>
                <artifactId>wildfly-controller-client</artifactId>
                <version>${version.org.wildfly.core}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${version.com.google.guava}</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${version.joda-time}</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib</artifactId>
                <version>${version.org.jetbrains.kotlin}</version>
            </dependency>
            <dependency>
                <groupId>org.core4j</groupId>
                <artifactId>core4j</artifactId>
                <version>${version.org.core4j.core4j}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>*</artifactId>
                        <groupId>*</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- **** FRONTEND **** -->
            <dependency>
                <groupId>javax.el</groupId>
                <artifactId>javax.el-api</artifactId>
                <version>${version.javax.el-api}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${version.commons.io}</version>
            </dependency>
            <dependency>
                <groupId>com.typesafe.play</groupId>
                <artifactId>play_${version.org.scala.base}</artifactId>
                <version>${version.com.typesafe.play}</version>
            </dependency>
            <dependency>
                <groupId>com.typesafe.play</groupId>
                <artifactId>play-cache_${version.org.scala.base}</artifactId>
                <version>${version.com.typesafe.play}</version>
            </dependency>
            <dependency>
                <groupId>com.typesafe.play</groupId>
                <artifactId>play-java_${version.org.scala.base}</artifactId>
                <version>${version.com.typesafe.play}</version>
            </dependency>
            <dependency>
                <groupId>com.typesafe.play</groupId>
                <artifactId>play-streams_${version.org.scala.base}</artifactId>
                <version>${version.com.typesafe.play}</version>
            </dependency>
            <dependency>
                <groupId>com.typesafe.play</groupId>
                <artifactId>play-ws_${version.org.scala.base}</artifactId>
                <version>${version.com.typesafe.play}</version>
            </dependency>
            <dependency>
                <groupId>com.typesafe.play</groupId>
                <artifactId>play-json_${version.org.scala.base}</artifactId>
                <version>${version.com.typesafe.play.play-json}</version>
            </dependency>
            <dependency>
                <groupId>com.typesafe.play</groupId>
                <artifactId>play-netty-server_${version.org.scala.base}</artifactId>
                <version>${version.com.typesafe.play}</version>
            </dependency>
            <dependency>
                <groupId>com.typesafe.play</groupId>
                <artifactId>twirl-api_${version.org.scala.base}</artifactId>
                <version>${version.com.typesafe.play.twirl-api}</version>
            </dependency>
            <dependency>
                <groupId>com.typesafe.play</groupId>
                <artifactId>filters-helpers_${version.org.scala.base}</artifactId>
                <version>${version.com.typesafe.play}</version>
            </dependency>
            <dependency>
                <groupId>com.typesafe.play</groupId>
                <artifactId>play-docs_${version.org.scala.base}</artifactId>
                <version>${version.com.typesafe.play}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-tsc</groupId>
                <artifactId>nossis-tsc-frontend-catalog</artifactId>
                <version>${version.pt.ptinovacao.nossis-tsc}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-tsc</groupId>
                <artifactId>nossis-tsc-frontend-catalog-assets</artifactId>
                <version>${version.pt.ptinovacao.nossis-tsc}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.findbugs</groupId>
                <artifactId>annotations</artifactId>
                <version>${version.com.google.code.findbugs}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.findbugs</groupId>
                <artifactId>jsr305</artifactId>
                <version>${version.com.google.code.findbugs}</version>
            </dependency>
            <dependency>
                <groupId>com.google.inject</groupId>
                <artifactId>guice</artifactId>
                <version>${version.com.google.inject}</version>
            </dependency>
            <!-- To download csv -->
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongodb-driver-sync</artifactId>
                <version>${version.org.mongodb}</version>
            </dependency>
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongodb-driver-core</artifactId>
                <version>${version.org.mongodb}</version>
            </dependency>
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>bson</artifactId>
                <version>${version.org.mongodb}</version>
            </dependency>
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongo-java-driver</artifactId>
                <version>${version.org.mongodb.mongo-java-driver}</version>
            </dependency>
            <!-- jackson deps -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${version.com.fasterxml.jackson}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${version.com.fasterxml.jackson}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jdk8</artifactId>
                <version>${version.com.fasterxml.jackson}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${version.com.fasterxml.jackson}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-joda</artifactId>
                <version>${version.com.fasterxml.jackson}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${version.com.fasterxml.jackson}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.module</groupId>
                <artifactId>jackson-module-jaxb-annotations</artifactId>
                <version>${version.com.fasterxml.jackson}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.module</groupId>
                <artifactId>jackson-module-paranamer</artifactId>
                <version>${version.com.fasterxml.jackson}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.module</groupId>
                <artifactId>jackson-module-scala_${version.org.scala.base}</artifactId>
                <version>${version.com.fasterxml.jackson}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-xml</artifactId>
                <version>${version.com.fasterxml.jackson}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-yaml</artifactId>
                <version>${version.com.fasterxml.jackson}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.jaxrs</groupId>
                <artifactId>jackson-jaxrs-json-provider</artifactId>
                <version>${version.com.fasterxml.jackson}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.media</groupId>
                <artifactId>jersey-media-json-jackson</artifactId>
                <version>${version.org.glassfish.jersey.media.jackson}</version>
            </dependency>
            <dependency>
                <groupId>com.thoughtworks.paranamer</groupId>
                <artifactId>paranamer</artifactId>
                <version>${version.com.thoughtworks.paranamer}</version>
            </dependency>
            <dependency>
                <groupId>com.martiansoftware</groupId>
                <artifactId>jsap</artifactId>
                <version>${version.com.martiansoftware}</version>
            </dependency>
            <!-- HIBERNATE OGM MATERIALS-->
            <dependency>
                <groupId>org.hibernate.ogm</groupId>
                <artifactId>hibernate-ogm-mongodb</artifactId>
                <version>${version.org.hibernate.ogm}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.ogm</groupId>
                <artifactId>hibernate-ogm-core</artifactId>
                <version>${version.org.hibernate.ogm}</version>
            </dependency>

            <!--  ################ JBOSS  DEPS   ################ -->
            <dependency>
                <groupId>javax.inject</groupId>
                <artifactId>javax.inject</artifactId>
                <version>${version.javax.inject}</version>
            </dependency>
            <dependency>
                <groupId>javax.enterprise</groupId>
                <artifactId>cdi-api</artifactId>
                <version>${version.javax.enterprise.cdi-api}</version>
            </dependency>
            <dependency>
                <groupId>javax.activation</groupId>
                <artifactId>activation</artifactId>
                <version>${version.javax.activation}</version>
            </dependency>
            <dependency>
                <groupId>javax.interceptor</groupId>
                <artifactId>javax.interceptor-api</artifactId>
                <version>${version.javax.interceptor}</version>
            </dependency>
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${version.javax.validation}</version>
            </dependency>
            <dependency>
                <groupId>javax</groupId>
                <artifactId>javaee-api</artifactId>
                <version>${version.javax.javee-api}</version>
            </dependency>
            <dependency>
                <groupId>javax.jcr</groupId>
                <artifactId>jcr</artifactId>
                <version>${version.javax.jcr}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.spec.javax.ejb</groupId>
                <artifactId>jboss-ejb-api_3.2_spec</artifactId>
                <version>${version.org.jboss.spec}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.spec.javax.transaction</groupId>
                <artifactId>jboss-transaction-api_1.2_spec</artifactId>
                <version>${version.org.jboss.spec}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.spec.javax.annotation</groupId>
                <artifactId>jboss-annotations-api_1.2_spec</artifactId>
                <version>${version.org.jboss.spec}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.spec.javax.servlet</groupId>
                <artifactId>jboss-servlet-api_3.1_spec</artifactId>
                <version>${version.org.jboss.spec}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.ejb3</groupId>
                <artifactId>jboss-ejb3-ext-api</artifactId>
                <version>${version.org.jboss.ejb3.jboss-ejb3-ext-api}</version>
            </dependency>
            <!-- €€€€€€€ xml-apis €€€€ -->
            <dependency>
                <groupId>xml-apis</groupId>
                <artifactId>xml-apis</artifactId>
                <version>${version.xml-apis}</version>
            </dependency>
            <!-- €€€€€€€ UNDERTOW €€€€ -->
            <dependency>
                <groupId>io.undertow</groupId>
                <artifactId>undertow-core</artifactId>
                <version>${version.io.undertow}</version>
            </dependency>
            <dependency>
                <groupId>io.undertow</groupId>
                <artifactId>undertow-servlet</artifactId>
                <version>${version.io.undertow}</version>
            </dependency>
            <!-- €€€€€€€€ INFINISPAN €€€€€€€€ -->
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-core</artifactId>
                <version>${version.org.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-commons</artifactId>
                <version>${version.org.infinispan}</version>
            </dependency>
            <!-- €€€€€ LOGGING  €€€€€ -->
            <dependency>
                <groupId>org.jboss.logging</groupId>
                <artifactId>jboss-logging</artifactId>
                <version>${version.org.jboss.logging.jboss-logging}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.logmanager</groupId>
                <artifactId>jboss-logmanager</artifactId>
                <version>${version.org.jboss.logmanager}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.logging</groupId>
                <artifactId>jboss-logging-annotations</artifactId>
                <version>${version.org.jboss.jboss-logging-annotations}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.logging</groupId>
                <artifactId>jboss-logging-processor</artifactId>
                <version>${version.org.jboss.jboss-logging-processor}</version>
            </dependency>

            <!-- €€€€€ RESTEASY  €€€€€ -->
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-undertow</artifactId>
                <version>${version.org.jboss.resteasy}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-servlet-initializer</artifactId>
                <version>${version.org.jboss.resteasy}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-multipart-provider</artifactId>
                <version>${version.org.jboss.resteasy}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>async-http-servlet-3.0</artifactId>
                <version>${version.org.jboss.resteasy}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-client</artifactId>
                <version>${version.org.jboss.resteasy}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-jaxrs</artifactId>
                <version>${version.org.jboss.resteasy}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-jackson2-provider</artifactId>
                <version>${version.org.jboss.resteasy}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss</groupId>
                <artifactId>jboss-dmr</artifactId>
                <version>${version.org.jboss.dmr}</version>
            </dependency>
            <!-- €€€€€€€€ HIBERNATE €€€€€€€€€€ -->
            <dependency>
                <groupId>org.hibernate.javax.persistence</groupId>
                <artifactId>hibernate-jpa-2.1-api</artifactId>
                <version>${version.org.hibernate.javax.persistence.hibernate-jpa-2.1-api}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${version.org.hibernate.hibernate-validator}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-core</artifactId>
                <version>${version.org.hibernate.hibernate-core}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.hql</groupId>
                <artifactId>hibernate-hql-parser</artifactId>
                <version>${version.org.hibernate.hql}</version>
            </dependency>
            <!-- €€€€€€€€ JBERET €€€€€€€€€€ -->
            <dependency>
                <groupId>org.jberet</groupId>
                <artifactId>jberet-core</artifactId>
                <version>${version.org.jberet}</version>
            </dependency>
            <!-- In order to compile and deploy applications using the Java Batch API an implementation is required -->
            <dependency>
                <groupId>org.jboss.spec.javax.batch</groupId>
                <artifactId>jboss-batch-api_1.0_spec</artifactId>
                <version>${version.org.jboss.spec}</version>
            </dependency>

            <!-- ############# END JBOSS DIRECT LIBS ################## -->

            <!-- ********************* EXTERNAL DEPENDENCIES ********************* -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${version.org.slf4j}</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-simple</artifactId>
                <version>${version.org.slf4j}</version>
            </dependency>
            <dependency>
                <groupId>javax.ws.rs</groupId>
                <artifactId>javax.ws.rs-api</artifactId>
                <version>${version.javax.ws.rs}</version>
            </dependency>
            <dependency>
                <groupId>javax.persistence</groupId>
                <artifactId>javax.persistence-api</artifactId>
                <version>${version.javax.persistence}</version>
            </dependency>
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${version.javax.xml.bind}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-1.2-api</artifactId>
                <version>${version.log4j2}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${version.log4j2}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>${version.log4j2}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${version.log4j2}</version>
            </dependency>
            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>${version.com.lmax}</version>
                <scope>runtime</scope>
            </dependency>

            <dependency>
                <groupId>org.glassfish</groupId>
                <artifactId>javax.json</artifactId>
                <version>${version.org.glassfish.javax.json}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.james</groupId>
                <artifactId>apache-mime4j</artifactId>
                <version>${version.org.apache.james}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.jackrabbit</groupId>
                <artifactId>jackrabbit-jcr-commons</artifactId>
                <version>${version.org.apache.jackrabbit}</version>
            </dependency>
            <dependency>
                <groupId>org.odata4j</groupId>
                <artifactId>odata4j-core</artifactId>
                <version>${version.org.odata4j}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>eclipselink</artifactId>
                        <groupId>org.eclipse.persistence</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.eclipse.persistence</groupId>
                        <artifactId>javax.persistence</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${version.org.yaml.snakeyaml}</version>
            </dependency>
            <dependency>
                <groupId>org.mnode.ical4j</groupId>
                <artifactId>ical4j</artifactId>
                <version>${version.org.mnode.ical4j}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>animal-sniffer-annotations</artifactId>
                <version>${version.org.animal-sniffer-annotations}</version>
            </dependency>
            <dependency>
                <groupId>com.google.errorprone</groupId>
                <artifactId>error_prone_annotations</artifactId>
                <version>${version.org.error_prone_annotations}</version>
            </dependency>
            <dependency>
                <groupId>com.google.j2objc</groupId>
                <artifactId>j2objc-annotations</artifactId>
                <version>${version.org.j2objc-annotations}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.spec.javax.ws.rs</groupId>
                <artifactId>jboss-jaxrs-api_2.0_spec</artifactId>
                <version>${version.org.jboss-jaxrs-api_2.0_spec}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-jaxrs-services</artifactId>
                <version>${version.org.jboss.resteasy}</version>
            </dependency>
            <!-- backport-util-concurrent is an ical4j transitive -->
            <dependency>
                <groupId>backport-util-concurrent</groupId>
                <artifactId>backport-util-concurrent</artifactId>
                <version>${version.backport-util-concurrent}</version>
            </dependency>
            <dependency>
                <groupId>net.sf.saxon</groupId>
                <artifactId>saxon</artifactId>
                <version>${version.saxon}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-digester3</artifactId>
                <version>${version.digester3}</version>
            </dependency>
            <dependency>
                <groupId>com.oracle</groupId>
                <artifactId>ojdbc7</artifactId>
                <version>${version.com.oracle.jdbc.ojdbc7}</version>
            </dependency>

            <dependency>
                <groupId>org.jvnet.jaxb2_commons</groupId>
                <artifactId>jaxb2-basics-runtime</artifactId>
                <version>${version.org.jvnet.jaxb2_commons}</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${version.commons-lang}</version>
            </dependency>
            <dependency>
                <groupId>commons-cli</groupId>
                <artifactId>commons-cli</artifactId>
                <version>${version.commons-cli}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${version.org.apache.commons.commons-lang3}</version>
            </dependency>
            <dependency>
                <groupId>xerces</groupId>
                <artifactId>xercesImpl</artifactId>
                <version>${version.xerces.xercesImpl}</version>
            </dependency>
            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>${version.org.javassist}</version>
            </dependency>
            <!-- *********************** NA-PORTAL DEPS *********************** -->
            <dependency>
                <groupId>pt.ptinovacao.na-portal.it</groupId>
                <artifactId>na-portal-integration-tests-create-entities-operations-versions</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-nadm-web-components-translator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-ref-data-cache</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-ref-data-template-translator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-web-components</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-error-commons</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-commons-model</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-db-commons</artifactId>
                <version>${project.version}</version>
                <type>ejb</type>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-db-commons</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-db-mpt</artifactId>
                <version>${project.version}</version>
                <type>ejb</type>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-db-mpt</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-mpt-dispatcher</artifactId>
                <version>${project.version}</version>
                <type>ejb</type>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-mpt-dispatcher</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-ref-data-messaging-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-ref-data-cache-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-mpt-scheduler</artifactId>
                <version>${project.version}</version>
                <type>ejb</type>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-mpt-scheduler</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-mpt-engine</artifactId>
                <version>${project.version}</version>
                <type>ejb</type>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-mpt-engine</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-mpt-resource-manager</artifactId>
                <version>${project.version}</version>
                <type>ejb</type>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-mpt-resource-manager</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-core-request-engine</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-core-request-engine-bundle</artifactId>
                <version>${project.version}</version>
                <type>war</type>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-ref-data-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-ref-data-health-check</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-ref-data-messaging-rabbitmq</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-health-check-rest-services</artifactId>
                <version>${project.version}</version>
                <type>war</type>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-mpt-rest-services</artifactId>
                <version>${project.version}</version>
                <type>war</type>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-go-rest-services</artifactId>
                <version>${project.version}</version>
                <type>war</type>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-go-model</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-mpt-model</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-mpt-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-rest-commons</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- auditing modules -->
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-auditing-workflows</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-auditing-schemas</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-ref-data-cache-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-mpt-go-bundle</artifactId>
                <version>${project.version}</version>
                <type>ear</type>
            </dependency>

            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-frontend-basemodule</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-frontend-basemodule-assets</artifactId>
                <version>${project.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-frontend-catalog-basemodule</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-frontend-catalog-basemodule-assets</artifactId>
                <version>${project.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-frontend-router</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-frontend-router-assets</artifactId>
                <version>${project.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-frontend-portal</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-frontend-portal-assets</artifactId>
                <version>${project.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-frontend-monitoring</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-frontend-monitoring-assets</artifactId>
                <version>${project.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-frontend-operations-catalog</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-frontend-operations-catalog-assets</artifactId>
                <version>${project.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-frontend-reference-data</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-frontend-reference-data-assets</artifactId>
                <version>${project.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-frontend-go</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-frontend-go-assets</artifactId>
                <version>${project.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-frontend-nadm</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-frontend-nadm-assets</artifactId>
                <version>${project.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-frontend-mpt</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-frontend-mpt-assets</artifactId>
                <version>${project.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-frontend-diagnostics</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-frontend-diagnostics-assets</artifactId>
                <version>${project.version}</version>
                <scope>runtime</scope>
            </dependency>
            <!-- *********************** PTIN DEPS *********************** -->
            <dependency>
                <groupId>pt.ptinovacao.nossis-frmwrk</groupId>
                <artifactId>exceptions</artifactId>
                <version>${version.pt.ptinovacao.nossis-frmwrk}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-inv</groupId>
                <artifactId>nossis-inv-backend-core-http-api-rest</artifactId>
                <version>${version.pt.ptinovacao.nossis-inv}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-inv</groupId>
                <artifactId>nossis-inv-backend-core-coding</artifactId>
                <version>${version.pt.ptinovacao.nossis-inv}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-inv</groupId>
                <artifactId>nossis-inv-backend-entities-catalog</artifactId>
                <version>${version.pt.ptinovacao.nossis-inv}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-inv</groupId>
                <artifactId>nossis-inv-kernel-cache-management</artifactId>
                <version>${version.pt.ptinovacao.nossis-inv}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-inv</groupId>
                <artifactId>nossis-inv-kernel-http-api-rest</artifactId>
                <version>${version.pt.ptinovacao.nossis-inv}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-inv</groupId>
                <artifactId>nossis-inv-kernel-monitoring</artifactId>
                <version>${version.pt.ptinovacao.nossis-inv}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-inv</groupId>
                <artifactId>nossis-inv-kernel-catalog-client-api</artifactId>
                <version>${version.pt.ptinovacao.nossis-inv}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-inv</groupId>
                <artifactId>nossis-inv-kernel-json</artifactId>
                <version>${version.pt.ptinovacao.nossis-inv}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-inv</groupId>
                <artifactId>nossis-inv-backend-core-persistence-api</artifactId>
                <version>${version.pt.ptinovacao.nossis-inv}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-inv</groupId>
                <artifactId>nossis-inv-backend-modules-catalog</artifactId>
                <version>${version.pt.ptinovacao.nossis-inv}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-inv</groupId>
                <artifactId>nossis-inv-backend-modules-historic</artifactId>
                <version>${version.pt.ptinovacao.nossis-inv}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-inv</groupId>
                <artifactId>nossis-inv-backend-modules-catalog-editor</artifactId>
                <version>${version.pt.ptinovacao.nossis-inv}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-inv</groupId>
                <artifactId>nossis-inv-kernel-persistence-plugins-arjuna-base</artifactId>
                <version>${version.pt.ptinovacao.nossis-inv}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-inv</groupId>
                <artifactId>nossis-inv-kernel-persistence-plugins-arjuna-jts</artifactId>
                <version>${version.pt.ptinovacao.nossis-inv}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-commons</groupId>
                <artifactId>na-commons-interfaces-na-ws-api</artifactId>
                <version>${version.pt.ptinovacao.na-commons}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-commons</groupId>
                <artifactId>na-commons-rabbitmq-channel-provider</artifactId>
                <version>${version.pt.ptinovacao.na-commons}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-commons</groupId>
                <artifactId>na-commons-na-cli-client</artifactId>
                <version>${version.pt.ptinovacao.na-commons}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-commons</groupId>
                <artifactId>na-commons-utils-guard</artifactId>
                <version>${version.pt.ptinovacao.na-commons}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-commons</groupId>
                <artifactId>na-commons-webrepository-api</artifactId>
                <version>${version.pt.ptinovacao.na-commons}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na</groupId>
                <artifactId>na-smart2m-wsdl</artifactId>
                <version>${version.pt.ptinovacao.na.na-smart2m-wsdl}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na</groupId>
                <artifactId>na-core-utils</artifactId>
                <version>${version.pt.ptinovacao.na}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na</groupId>
                <artifactId>na-order-followup-dto</artifactId>
                <version>${version.pt.ptinovacao.na}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na</groupId>
                <artifactId>na-operations-catalog-entities-cache</artifactId>
                <version>${version.pt.ptinovacao.na}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na</groupId>
                <artifactId>na-operations-entities-client-model</artifactId>
                <version>${version.pt.ptinovacao.na}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na</groupId>
                <artifactId>na-swe-order-model</artifactId>
                <version>${version.pt.ptinovacao.na}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na</groupId>
                <artifactId>na-swe-order-followup-persistence</artifactId>
                <version>${version.pt.ptinovacao.na}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na</groupId>
                <artifactId>na-swe-dsl-external-actions-model</artifactId>
                <version>${version.pt.ptinovacao.na}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na</groupId>
                <artifactId>na-swe-dao-model</artifactId>
                <version>${version.pt.ptinovacao.na}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na</groupId>
                <artifactId>na-swe-external-systems-handling-model</artifactId>
                <version>${version.pt.ptinovacao.na}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na</groupId>
                <artifactId>na-swe-async-notifications-handling-model</artifactId>
                <version>${version.pt.ptinovacao.na}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na</groupId>
                <artifactId>na-operations-catalog-cluster-service</artifactId>
                <version>${version.pt.ptinovacao.na}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-commons</groupId>
                <artifactId>na-commons-pom-devtools-resources</artifactId>
                <version>${version.pt.ptinovacao.na-commons}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-commons</groupId>
                <artifactId>na-commons-audit-core</artifactId>
                <version>${version.pt.ptinovacao.na-commons}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-commons</groupId>
                <artifactId>na-commons-plugins-dbmanager-core</artifactId>
                <version>${version.pt.ptinovacao.na-commons}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-commons</groupId>
                <artifactId>na-commons-plugins-http-plugin</artifactId>
                <version>${version.pt.ptinovacao.na-commons}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-commons</groupId>
                <artifactId>na-commons-plugins-cluster-service-register-plugin</artifactId>
                <version>${version.pt.ptinovacao.na-commons}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-commons</groupId>
                <artifactId>na-commons-wfe-api</artifactId>
                <version>${version.pt.ptinovacao.na-commons}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-commons</groupId>
                <artifactId>na-commons-wfe-adapters</artifactId>
                <version>${version.pt.ptinovacao.na-commons}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-commons</groupId>
                <artifactId>na-commons-utils-logging</artifactId>
                <version>${version.pt.ptinovacao.na-commons}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-commons</groupId>
                <artifactId>na-commons-utils-mongo</artifactId>
                <version>${version.pt.ptinovacao.na-commons}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-commons</groupId>
                <artifactId>na-commons-plugins-dbmanager-schema</artifactId>
                <version>${version.pt.ptinovacao.na-commons}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-commons</groupId>
                <artifactId>na-commons-http-client-api</artifactId>
                <version>${version.pt.ptinovacao.na-commons}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-commons</groupId>
                <artifactId>na-commons-http-client-impl</artifactId>
                <version>${version.pt.ptinovacao.na-commons}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-commons</groupId>
                <artifactId>na-commons-cluster-service-locator-core</artifactId>
                <version>${version.pt.ptinovacao.na-commons}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-commons</groupId>
                <artifactId>na-commons-asf-channel-message</artifactId>
                <version>${version.pt.ptinovacao.na-commons}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-commons</groupId>
                <artifactId>na-commons-asf-channel-messaging-impl-rabbitmq</artifactId>
                <version>${version.pt.ptinovacao.na-commons}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nadm</groupId>
                <artifactId>nadm-naem-rest-commons</artifactId>
                <version>${version.pt.ptinovacao.nadm}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-ui</groupId>
                <artifactId>nossis-ui-security-adapter</artifactId>
                <version>${version.pt.ptinovacao.nossis-ui}</version>
                <exclusions>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.typesafe.play</groupId>
                        <artifactId>play-logback_${version.org.scala.base}</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-ui</groupId>
                <artifactId>nossis-ui-menu-models</artifactId>
                <version>${version.pt.ptinovacao.nossis-ui}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-ui</groupId>
                <artifactId>nossis-ui-menu</artifactId>
                <version>${version.pt.ptinovacao.nossis-ui}</version>
                <exclusions>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.typesafe.play</groupId>
                        <artifactId>play-logback_${version.org.scala.base}</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-ui</groupId>
                <artifactId>nossis-ui-security-adapter-assets</artifactId>
                <version>${version.pt.ptinovacao.nossis-ui}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-ui</groupId>
                <artifactId>nossis-ui-messages</artifactId>
                <version>${version.pt.ptinovacao.nossis-ui}</version>
                <exclusions>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.typesafe.play</groupId>
                        <artifactId>play-logback_${version.org.scala.base}</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-ui</groupId>
                <artifactId>nossis-ui-portal-frmwrk</artifactId>
                <version>${version.pt.ptinovacao.nossis-ui}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.typesafe.play</groupId>
                        <artifactId>play-logback_${version.org.scala.base}</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.typesafe.play</groupId>
                        <artifactId>play-java-jpa_${version.org.scala.base}</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-ui</groupId>
                <artifactId>nossis-ui-portal-frmwrk-assets</artifactId>
                <version>${version.pt.ptinovacao.nossis-ui}</version>
                <scope>runtime</scope>
            </dependency>
            <!-- ASF DEPS -->
            <dependency>
                <groupId>pt.ptinovacao.asf</groupId>
                <artifactId>asf-configuration</artifactId>
                <version>${version.pt.ptinovacao.asf}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.asf</groupId>
                <artifactId>asf-messaging-api</artifactId>
                <version>${version.pt.ptinovacao.asf}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.asf</groupId>
                <artifactId>asf-cluster-api</artifactId>
                <version>${version.pt.ptinovacao.asf}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.asf</groupId>
                <artifactId>asf-cluster-api-enumerations</artifactId>
                <version>${version.pt.ptinovacao.asf}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.asf</groupId>
                <artifactId>asf-cluster-implementations-zookeeper</artifactId>
                <version>${version.pt.ptinovacao.asf}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.asf</groupId>
                <artifactId>asf-serializers</artifactId>
                <version>${version.pt.ptinovacao.asf}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.asf</groupId>
                <artifactId>asf-components-manager</artifactId>
                <version>${version.pt.ptinovacao.asf}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.asf</groupId>
                <artifactId>asf-kernel</artifactId>
                <version>${version.pt.ptinovacao.asf}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.kamon</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.asf</groupId>
                <artifactId>asf-model</artifactId>
                <version>${version.pt.ptinovacao.asf}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.asf</groupId>
                <artifactId>asf-workflow-mongodb</artifactId>
                <version>${version.pt.ptinovacao.asf}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.asf</groupId>
                <artifactId>asf-plugins-scheduler</artifactId>
                <version>${version.pt.ptinovacao.asf}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.asf</groupId>
                <artifactId>asf-plugins-brokerimpl</artifactId>
                <version>${version.pt.ptinovacao.asf}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.asf</groupId>
                <artifactId>asf-plugins-jetty</artifactId>
                <version>${version.pt.ptinovacao.asf}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.asf</groupId>
                <artifactId>asf-startup</artifactId>
                <version>${version.pt.ptinovacao.asf}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.asf</groupId>
                <artifactId>asf-notification-adapter</artifactId>
                <version>${version.pt.ptinovacao.asf}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.asf</groupId>
                <artifactId>asf-plugins-notification</artifactId>
                <version>${version.pt.ptinovacao.asf}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.asf</groupId>
                <artifactId>asf-plugins-statistics</artifactId>
                <version>${version.pt.ptinovacao.asf}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-security</groupId>
                <artifactId>nossis-security-aaapi</artifactId>
                <version>${version.pt.ptinovacao.nossis-security}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-security</groupId>
                <artifactId>nossis-security-provider-sca</artifactId>
                <version>${version.pt.ptinovacao.nossis-security}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-security</groupId>
                <artifactId>nossis-security-provider-iam</artifactId>
                <version>${version.pt.ptinovacao.nossis-security}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.nossis-security</groupId>
                <artifactId>nossis-security-cache-ehcache</artifactId>
                <version>${version.pt.ptinovacao.nossis-security}</version>
            </dependency>
            <dependency>
                <groupId>com.rabbitmq</groupId>
                <artifactId>amqp-client</artifactId>
                <version>${version.com.rabbitmq}</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${version.junit}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>${version.org.powermock}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito</artifactId>
                <version>${version.org.powermock}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-core</artifactId>
                <version>${version.org.powermock}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-all</artifactId>
                <version>${version.org.mockito.mockito-all}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest-all</artifactId>
                <version>${version.hamcrest}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>io.prometheus.jmx</groupId>
                <artifactId>jmx_prometheus_javaagent</artifactId>
                <version>${version.io.prometheus.jmx}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>io.opentelemetry.javaagent</groupId>
                <artifactId>opentelemetry-javaagent</artifactId>
                <version>${version.io.opentelemetry.javaagent}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.containers</groupId>
                <artifactId>jersey-container-servlet-core</artifactId>
                <version>${version.org.glassfish.jersey}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.inject</groupId>
                <artifactId>jersey-hk2</artifactId>
                <version>${version.org.glassfish.jersey}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.hk2.external</groupId>
                <artifactId>javax.inject</artifactId>
                <version>${version.org.glassfish.hk2.external.javax.inject}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss</groupId>
                <artifactId>jboss-transaction-spi</artifactId>
                <version>${version.org.jboss.transaction-spi}</version>
            </dependency>
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${version.org.postgresql}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-dbcp2</artifactId>
                <version>${version.org.apache.commons.dbcp2}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-entitymanager</artifactId>
                <version>${version.org.hibernate.entitymanager}</version>
            </dependency>
            <dependency>
                <groupId>com.typesafe.akka</groupId>
                <artifactId>akka-remote_${version.org.scala.base}</artifactId>
                <version>${version.com.typesafe.akka}</version>
            </dependency>
            <dependency>
                <groupId>com.typesafe.akka</groupId>
                <artifactId>akka-actor_${version.org.scala.base}</artifactId>
                <version>${version.com.typesafe.akka}</version>
            </dependency>
            <dependency>
                <groupId>com.typesafe.akka</groupId>
                <artifactId>akka-slf4j_${version.org.scala.base}</artifactId>
                <version>${version.com.typesafe.akka}</version>
            </dependency>
            <dependency>
                <groupId>com.typesafe.akka</groupId>
                <artifactId>akka-stream_${version.org.scala.base}</artifactId>
                <version>${version.com.typesafe.akka}</version>
            </dependency>
            <dependency>
                <groupId>antlr</groupId>
                <artifactId>antlr</artifactId>
                <version>${version.org.antlr}</version>
            </dependency>
            <dependency>
                <groupId>org.jvnet.mimepull</groupId>
                <artifactId>mimepull</artifactId>
                <version>${version.mimepull}</version>
            </dependency>

            <dependency>
                <groupId>org.scala-lang</groupId>
                <artifactId>scala-library</artifactId>
                <version>${version.org.scala-lang}</version>
            </dependency>
            <dependency>
                <groupId>org.scala-lang.modules</groupId>
                <artifactId>scala-xml_${version.org.scala.base}</artifactId>
                <version>${version.org.scala-lang.modules}</version>
            </dependency>
        </dependencies>

    </dependencyManagement>
    <!-- ***************************************************************************************** -->

    <build>
        <finalName>${project.artifactId}</finalName>
        <testSourceDirectory>src/test/java</testSourceDirectory>
        <!-- COMMON PLUGIN CONFIGURATIONS -->
        <pluginManagement>
            <plugins>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>${version.maven-clean-plugin}</version>
                    <configuration>
                        <filesets>
                            <fileset>
                                <directory>${project.basedir}</directory>
                                <includes>
                                    <include>${project.artifactId}*.tar.gz</include>
                                </includes>
                            </fileset>
                        </filesets>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>${version.maven-install-plugin}</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>${version.maven-dependency-plugin}</version>
                    <configuration>
                        <failOnWarning>true</failOnWarning>
                        <ignoreNonCompile>true</ignoreNonCompile>
                    </configuration>
                    <executions>
                        <execution>
                            <id>process-dependency-analyze</id>
                            <goals>
                                <goal>analyze</goal>
                            </goals>
                            <phase>verify</phase>
                        </execution>
                    </executions>
                </plugin>

                <plugin>
                    <groupId>com.spotify</groupId>
                    <artifactId>dockerfile-maven-plugin</artifactId>
                    <version>${version.dockerfile-maven-plugin}</version>
                </plugin>

                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>sonar-maven-plugin</artifactId>
                    <version>2.6</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${version.org.apache.maven.surefire}</version>
                    <configuration>
                        <argLine>-Duser.timezone=UTC</argLine>
                        <childDelegation>true</childDelegation>
                        <forkMode>once</forkMode>
                        <includes>
                            <include>**/*Test.java</include>
                        </includes>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${version.maven-jar-plugin}</version>
                    <configuration>
                        <archive>
                            <manifest>
                                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                            </manifest>
                        </archive>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <version>${version.org.apache.maven.surefire}</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-ear-plugin</artifactId>
                    <version>2.10</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <version>2.4</version>
                    <configuration>
                        <filteringDeploymentDescriptors>true</filteringDeploymentDescriptors>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-ejb-plugin</artifactId>
                    <version>2.3</version>
                    <configuration>
                        <ejbVersion>3.2</ejbVersion>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${version.maven-compiler-plugin}</version>
                    <configuration>
                        <source>${java.source.level}</source>
                        <target>${java.target.level}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                        <showDeprecation>true</showDeprecation>
                        <showWarnings>true</showWarnings>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>${version.maven-deploy-plugin}</version>
                </plugin>

            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <version>${version.maven-clean-plugin}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>${version.maven-dependency-plugin}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <version>${version.maven-install-plugin}</version>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>github</id>
            <properties>
                <na-portal.releases.repository.id>github</na-portal.releases.repository.id>
                <na-portal.releases.repository.url>https://maven.pkg.github.com/AlticeLabsProjects/nossis-orchestration</na-portal.releases.repository.url>
                <na-portal.snapshots.repository.id>github</na-portal.snapshots.repository.id>
                <na-portal.snapshots.repository.url>https://maven.pkg.github.com/AlticeLabsProjects/nossis-orchestration</na-portal.snapshots.repository.url>
            </properties>
        </profile>
        <profile>
            <id>gar</id>
            <properties>
                <na-portal.releases.repository.id>gar</na-portal.releases.repository.id>
                <na-portal.releases.repository.url>https://${na-portal.releases.repository.gar.region}-maven.pkg.dev/registry-ext/mavenrepo-${na-portal.releases.repository.gar.customer}</na-portal.releases.repository.url>
                <na-portal.snapshots.repository.id>gar</na-portal.snapshots.repository.id>
                <na-portal.snapshots.repository.url>https://${na-portal.releases.repository.gar.region}-maven.pkg.dev/registry-ext/mavenrepo-${na-portal.releases.repository.gar.customer}</na-portal.snapshots.repository.url>
            </properties>
        </profile>
    </profiles>
</project>
