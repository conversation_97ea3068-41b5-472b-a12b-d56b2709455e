na.portal.operationscatalog.module.title                                                        = Catalog
na.portal.operationscatalog.module.description                                                  = The Catalog module is divided in two sections: Entities and Operations. The Entities section enables the design of Customer Facing Service (CFS), Resource Facing Services (RFS) and Resources. Attribute types can also be defined and then associated as attributes to the entities with defined restrictions associated to its base type. The base types used to create the attribute types are number, string and enum, which are all simple types, but also the component complex type that can use previously created attribute types to create complex structure. The catalog also allows the design of relationships between CFSs, RFSs and Resources. The catalog has versioning capabilities so that the entities definition, their attributes and their relationship is always associated to a specific version. The Operations section allows the definition of operations on Customer Facing Service (CFS), Resource Facing Services (RFS) and Resources. Each operation must have name, an associated base entity (CFS, RFS or LR), the workflow name to be executed and also the input and output attributes associated to the base entity or the entities related to it. For the input attributes additional restrictions can be defined, if the original attribute also has restrictions the union from those restrictions will be used. The catalog has versioning capabilities so that the operations definition is always associated to a specific version.
na.portal.operationscatalog.submodule.title                                                     = Operations

na.portal.operationscatalog.home.label.title                                                    = Versions
na.portal.operationscatalog.home.label.subtitle                                                 = Operations catalog module

na.portal.operationscatalog.nav.context                                                         = Operations catalog
na.portal.operationscatalog.nav.version                                                         = Versions
na.portal.operationscatalog.nav.cfs                                                             = Customer services
na.portal.operationscatalog.nav.rfs                                                             = Network services
na.portal.operationscatalog.nav.resource                                                        = Resources
na.portal.operationscatalog.nav.responseCode                                                    = Response code
na.portal.operationscatalog.nav.create                                                          = Create
