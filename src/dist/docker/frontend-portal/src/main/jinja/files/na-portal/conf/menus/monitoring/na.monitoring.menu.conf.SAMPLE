{% if na_portal_install_monitoring_module | string | lower == 'true' or na_portal_install_monitoring_module | string | lower == 'yes' %}
{
  type = "simple-menu"
  id = "monitoring"
  title-key = "na.monitoring.title"
  css-class = "fuxicons fuxicons-monitoring"
  authorization {
    resources = ["monitoring|R"]
  }
  url = {
    type = "iframe"
    url = "{{ monitoring_portal_address }}{{ na_portal_path_prefix }}/monitoring/home"
    attrs = [
      {"name":"data-main-link-of-module", "value":"monitoring"}
    ]
  }
}
{% endif %}
