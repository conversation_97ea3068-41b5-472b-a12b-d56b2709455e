na.portal.operationscatalog.module.title                                                        = Catalogue
na.portal.operationscatalog.module.description                                                  = Le module Catalogue est divisé en deux sections : Entités et Opérations. La section Entités permet la conception de services orientés client (CFS), de services orientés ressources (RFS) et de ressources. Les types d'attributs peuvent également être définis puis associés en tant qu'attributs aux entités avec des restrictions définies associées à leur type de base. Les types de base utilisés pour créer les types d'attributs sont number, string et enum, qui sont tous des types simples, mais également le type complexe de composants qui peut utiliser des types d'attributs précédemment créés pour créer une structure complexe. Le catalogue permet également de concevoir des relations entre les CFS, les RFS et les ressources. Le catalogue dispose de fonctionnalités de gestion de versions afin que la définition des entités, leurs attributs et leurs relations soient toujours associés à une version spécifique. La section Opérations permet de définir les opérations sur le Customer Facing Service (CFS), les Resource Facing Services (RFS) et les Ressources. Chaque opération doit avoir un nom, une entité de base associée (CFS, RFS ou LR), le nom du workflow à exécuter ainsi que les attributs d'entrée et de sortie associés à l'entité de base ou aux entités qui lui sont liées. Pour les attributs d'entrée, des restrictions supplémentaires peuvent être définies. Si l'attribut d'origine a également des restrictions, l'union de ces restrictions sera utilisée. Le catalogue dispose de fonctionnalités de gestion de versions afin que la définition des opérations soit toujours associée à une version spécifique.
na.portal.operationscatalog.submodule.title                                                     = Operations

na.portal.operationscatalog.home.label.title                                                    = Versions
na.portal.operationscatalog.home.label.subtitle                                                 = Module du catalogue d''opérations

na.portal.operationscatalog.nav.context                                                         = Catalogue d''opérations
na.portal.operationscatalog.nav.version                                                         = Versions
na.portal.operationscatalog.nav.cfs                                                             = Service de client
na.portal.operationscatalog.nav.rfs                                                             = Service de réseau
na.portal.operationscatalog.nav.resource                                                        = Ressources
na.portal.operationscatalog.nav.responseCode                                                    = Code de réponse
na.portal.operationscatalog.nav.create                                                          = Créer
