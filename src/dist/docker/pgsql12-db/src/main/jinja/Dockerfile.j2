{% set udversion = project.version.value|cut('-SNAPSHOT') -%}
{% set is_snapshot = project.version.value is string_endingwith('-SNAPSHOT') -%}
FROM rdocker.ptin.corppt.com/na-portal{% if is_snapshot %}-tst{% endif %}/na-portal-pgsql-db:{{ udversion }}

LABEL com.alticelabs.name="{{ docker.image.name.value }}"
LABEL com.alticelabs.version="{{ udversion }}"
LABEL com.alticelabs.group="{{ docker.group.value }}"
LABEL com.alticelabs.summary="{{ docker.summary.value }}"
LABEL com.alticelabs.license="{{ docker.license.value }}"
LABEL com.alticelabs.packager="{{ docker.packager.value }}"
LABEL com.alticelabs.os="{{ docker.os.value }}"
LABEL com.alticelabs.release={% if is_snapshot %}"r{{ buildNumber.value }}-{{ docker.timestamp.value }}"{% else %}"{{ docker.timestamp.value }}"{% endif %}

COPY --chown=2016:2016 config/all.yml /config/all.yml
COPY --chown=2016:2016 config/defaults-na-portal-pgsql12-db.yml /config/defaults-na-portal-pgsql12-db.yml
COPY --chown=2016:2016 config/environment.yml /config/environment.yml
COPY --chown=2016:2016 docker-entrypoint-scripts-base.d/ /docker-entrypoint-scripts-base.d/

ENV DEFAULTS_INV="$DEFAULTS_INV /config/defaults-na-portal-pgsql12-db.yml"
