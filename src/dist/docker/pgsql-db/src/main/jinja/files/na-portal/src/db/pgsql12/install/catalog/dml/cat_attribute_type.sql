INSERT INTO cat_attribute_type (ID, CREATED_AT, UPDATED_AT, USER_CREATE, USER_UPDATE, CAN_ADD_ENUMERATED, DESCRIPTION, MAX_DATE, MAX_DOUBLE, MAX_LENGTH, MAX_LONG, MIN_DATE, MIN_DOUBLE, MIN_LONG, NAME, REG_EXP_VALIDATION, TYPE, UNIT, ID_STATE_MACHINE, SYSTEM, ENUMERATED_QUERY, CATALOG_SHOW, MIN_LENGTH)
VALUES (1000, localtimestamp, localtimestamp, 'system', 'system', 1, 'boolean, built with enum', null, null, 1, null, null, null, null, 'customBoolean', null, 'STRING', null, null, 1, null, 1, null);
INSERT INTO cat_attribute_type (ID, CREATED_AT, UPDATED_AT, USER_CREATE, USER_UPDATE, CAN_ADD_ENUMERATED, DESC<PERSON>PTION, MAX_DATE, MAX_DOUBLE, MAX_LENGTH, MAX_LONG, MIN_DATE, MIN_DOUBLE, MIN_LONG, NAME, REG_EXP_VALIDATION, TYPE, UNIT, ID_STATE_MACHINE, SYSTEM, ENUMERATED_QUERY, CATALOG_SHOW, MIN_LENGTH)
VALUES (1001, localtimestamp, localtimestamp, 'system', 'system', 0, 'string100, valid xml tag characters only', null, null, 100, null, null, null, null, 'string100validxmltagchars', '^(?!xml)[A-Za-z._:][A-Za-z0-9._:]+$', 'STRING', null, null, 1, null, 1, null);
INSERT INTO cat_attribute_type (ID, CREATED_AT, UPDATED_AT, USER_CREATE, USER_UPDATE, CAN_ADD_ENUMERATED, DESCRIPTION, MAX_DATE, MAX_DOUBLE, MAX_LENGTH, MAX_LONG, MIN_DATE, MIN_DOUBLE, MIN_LONG, NAME, REG_EXP_VALIDATION, TYPE, UNIT, ID_STATE_MACHINE, SYSTEM, ENUMERATED_QUERY, CATALOG_SHOW, MIN_LENGTH)
VALUES (1002, localtimestamp, localtimestamp, 'system', 'system', 0, 'stringClob', null, null, 50000, null, null, null, null, 'stringClob', null, 'OBJECT', null, null, 1, null, 1, null);
INSERT INTO cat_attribute_type (ID, CREATED_AT, UPDATED_AT, USER_CREATE, USER_UPDATE, CAN_ADD_ENUMERATED, DESCRIPTION, MAX_DATE, MAX_DOUBLE, MAX_LENGTH, MAX_LONG, MIN_DATE, MIN_DOUBLE, MIN_LONG, NAME, REG_EXP_VALIDATION, TYPE, UNIT, ID_STATE_MACHINE, SYSTEM, ENUMERATED_QUERY, CATALOG_SHOW, MIN_LENGTH)
VALUES (1003, localtimestamp, localtimestamp, 'system', 'system', 0, 'string500', null, null, 500, null, null, null, null, 'string500', null, 'STRING', null, null, 1, null, 1, null);
INSERT INTO cat_attribute_type (ID, CREATED_AT, UPDATED_AT, USER_CREATE, USER_UPDATE, CAN_ADD_ENUMERATED, DESCRIPTION, MAX_DATE, MAX_DOUBLE, MAX_LENGTH, MAX_LONG, MIN_DATE, MIN_DOUBLE, MIN_LONG, NAME, REG_EXP_VALIDATION, TYPE, UNIT, ID_STATE_MACHINE, SYSTEM, ENUMERATED_QUERY, CATALOG_SHOW, MIN_LENGTH)
VALUES (1004, localtimestamp, localtimestamp, 'system', 'system', 0, 'string1000', null, null, 1000, null, null, null, null, 'string1000', null, 'STRING', null, null, 1, null, 1, null);
INSERT INTO cat_attribute_type (ID, CREATED_AT, UPDATED_AT, USER_CREATE, USER_UPDATE, CAN_ADD_ENUMERATED, DESCRIPTION, MAX_DATE, MAX_DOUBLE, MAX_LENGTH, MAX_LONG, MIN_DATE, MIN_DOUBLE, MIN_LONG, NAME, REG_EXP_VALIDATION, TYPE, UNIT, ID_STATE_MACHINE, SYSTEM, ENUMERATED_QUERY, CATALOG_SHOW, MIN_LENGTH)
VALUES (1005, localtimestamp, localtimestamp, 'system', 'system', 0, 'string2000', null, null, 2000, null, null, null, null, 'string2000', null, 'STRING', null, null, 1, null, 1, null);