CREATE TABLE STEP_EXECUTION (
  STEPEXECUTIONID    BIGSERIAL NOT NULL,
  JOBEX<PERSON><PERSON>IONID     BIGINT             NOT NULL,
  VERSION            INTEGER,
  STEP<PERSON><PERSON>           VARCHAR(255),
  <PERSON><PERSON><PERSON><PERSON><PERSON>          TIMESTAMP,
  ENDTI<PERSON>            TIMESTAMP,
  <PERSON><PERSON><PERSON>TATUS        VARCHAR(30),
  EXITSTATUS         VARCHAR(512),
  EXECUTIONEXCEPTION VARCHAR(2048),
  PERSISTENTUSERDATA BYTEA,
  READCOUNT          INTEGER,
  WRITECOUNT         INTEGER,
  COMMITCOUNT        INTEGER,
  ROL<PERSON><PERSON><PERSON><PERSON>COUNT      INTEGER,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>UN<PERSON>      INTEGER,
  PROCESSSKIPCOUNT   INTEGER,
  <PERSON><PERSON><PERSON><PERSON>OUNT        INTEGER,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>UNT     INTEGER,
  READE<PERSON>HECKPOINTINFO  BYTEA,
  WR<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>NFO  BYTEA
) TABLESPACE :TS_MPT_DATA;