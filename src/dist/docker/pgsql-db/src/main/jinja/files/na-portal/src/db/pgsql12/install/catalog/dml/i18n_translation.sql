INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1000, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Catálogos', 1000);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1001, localtimestamp, localtimestamp, 'system', 'system', 'en', 'Catalogs', 1000);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (3000, localtimestamp, localtimestamp, 'system', 'system', 'fr', 'Catalogues', 1000);

INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1002, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Tipo', 1001);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1003, localtimestamp, localtimestamp, 'system', 'system', 'en', 'Type', 1001);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (3001, localtimestamp, localtimestamp, 'system', 'system', 'fr', 'Type', 1001);

INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1004, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Nome', 1002);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1005, localtimestamp, localtimestamp, 'system', 'system', 'en', 'Name', 1002);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (3002, localtimestamp, localtimestamp, 'system', 'system', 'fr', 'Nom', 1002);

INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1006, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Código Externo', 1003);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1007, localtimestamp, localtimestamp, 'system', 'system', 'en', 'External Code', 1003);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (3003, localtimestamp, localtimestamp, 'system', 'system', 'fr', 'Code externe', 1003);

INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1008, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Data de Criação', 1004);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1009, localtimestamp, localtimestamp, 'system', 'system', 'en', 'Creation Date', 1004);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (3004, localtimestamp, localtimestamp, 'system', 'system', 'fr', 'Date de création', 1004);

INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1010, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Data da última Atualização', 1005);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1011, localtimestamp, localtimestamp, 'system', 'system', 'en', 'Date of Last Update', 1005);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (3005, localtimestamp, localtimestamp, 'system', 'system', 'fr', 'Date de Dernière Mise à Jour', 1005);

INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1012, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Criado por', 1006);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1013, localtimestamp, localtimestamp, 'system', 'system', 'en', 'Created by', 1006);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (3006, localtimestamp, localtimestamp, 'system', 'system', 'fr', 'Créé par', 1006);

INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1014, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Atualizado por', 1007);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1015, localtimestamp, localtimestamp, 'system', 'system', 'en', 'Updated by', 1007);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (3007, localtimestamp, localtimestamp, 'system', 'system', 'fr', 'Mis à Jour Par', 1007);

INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1016, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Estado', 1008);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1017, localtimestamp, localtimestamp, 'system', 'system', 'en', 'State', 1008);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (3008, localtimestamp, localtimestamp, 'system', 'system', 'fr', 'État', 1008);

INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1018, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Características', 1009);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1019, localtimestamp, localtimestamp, 'system', 'system', 'en', 'Characteristics', 1009);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (3009, localtimestamp, localtimestamp, 'system', 'system', 'fr', 'Caractéristiques', 1009);

INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1020, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Atributos', 1010);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1021, localtimestamp, localtimestamp, 'system', 'system', 'en', 'Attributes', 1010);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (3010, localtimestamp, localtimestamp, 'system', 'system', 'fr', 'Attributs', 1010);

INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1022, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Atributos Específicos', 1011);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1023, localtimestamp, localtimestamp, 'system', 'system', 'en', 'Specific Attributes', 1011);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (3011, localtimestamp, localtimestamp, 'system', 'system', 'fr', 'Attributs Spécifiques', 1011);

INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1024, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Ativo', 1012);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1025, localtimestamp, localtimestamp, 'system', 'system', 'en', 'Active', 1012);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (3012, localtimestamp, localtimestamp, 'system', 'system', 'fr', 'Actif', 1012);

INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1026, localtimestamp, localtimestamp, 'system', 'system', 'pt', 'Inativo', 1013);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (1027, localtimestamp, localtimestamp, 'system', 'system', 'en', 'Inactive', 1013);
INSERT INTO i18n_translation (id, created_at, updated_at, user_create, user_update, locale, translation, id_label)
VALUES (3013, localtimestamp, localtimestamp, 'system', 'system', 'fr', 'Inactif', 1013);