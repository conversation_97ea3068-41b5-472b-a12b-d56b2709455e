-- Header
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (1, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 1, 'idCatEntity', 'SELECT_TREE_SEARCH', 'FULL', 1, 1, NULL, 1001, 1, 0, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (2, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 0, 'idCatEntity', 'SELECT_TREE_SEARCH', 'FULL', 1, 1, NULL, 1001, 2, 0, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (3, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 0, 'idCatEntity', 'SELECT_TREE_SEARCH', 'FULL', 1, 1, NULL, 1001, 3, 0, 0);

-- Base

-- create - 1
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (4, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 1, 'name', 'INPUT', 'FULL', 1, 1, NULL, 1002, 1, 1, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (5, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'state', 'SELECT', 'FULL', 1, 1, NULL, 1008, 1, 1, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (6, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 3, 'externalCode', 'INPUT', 'FULL', 1, 1, NULL, 1003, 1, 1, 0);

-- view - 2
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (7, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 1, 'name', 'INPUT', 'FULL', 1, 1, NULL, 1002, 2, 1, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (8, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'state', 'SELECT', 'FULL', 1, 1, NULL, 1008, 2, 1, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (9, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 3, 'externalCode', 'INPUT', 'FULL', 1, 1, NULL, 1003, 2, 1, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (10, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 4, 'createdAt', 'INPUT', 'FULL', 1, 1, NULL, 1004, 2, 1, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (11, localtimestamp, localtimestamp, 'system', 'system', 2, 0, 4, 'userCreate', 'INPUT', 'FULL', 1, 1, NULL, 1006, 2, 1, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (12, localtimestamp, localtimestamp, 'system', 'system', 1, 0, 5, 'updatedAt', 'INPUT', 'FULL', 1, 1, NULL, 1005, 2, 1, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (13, localtimestamp, localtimestamp, 'system', 'system', 2, 0, 5, 'userUpdate', 'INPUT', 'FULL', 1, 1, NULL, 1007, 2, 1, 0);

-- edit - 3
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (14, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 1, 'name', 'INPUT', 'FULL', 1, 1, NULL, 1002, 3, 1, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (15, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'state', 'SELECT', 'FULL', 1, 1, NULL, 1008, 3, 1, 1);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (16, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 3, 'externalCode', 'INPUT', 'FULL', 1, 1, NULL, 1003, 3, 1, 0);

-- search - 4
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (21, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 0, 'idCatEntity', 'SELECT_TREE_SEARCH', 'FULL', 6, 1, NULL, 1001, 4, 1, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (22, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 1, 'name', 'INPUT', 'FULL', 6, 1, NULL, 1002, 4, 1, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (23, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 2, 'state', 'SELECT', 'FULL', 6, 1, NULL, 1008, 4, 1, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (24, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 3, 'externalCode', 'INPUT', 'FULL', 6, 1, NULL, 1003, 4, 1, 0);

-- search (pesquisa contextualizada) - 5
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (25, localtimestamp, localtimestamp, 'system', 'system', 1, 1, 0, 'idCatEntity', 'SELECT_TREE_SEARCH', 'FULL', 1, 1, NULL, 1001, 5, 1, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (26, localtimestamp, localtimestamp, 'system', 'system', 2, 1, 0, 'name', 'INPUT', 'FULL', 2, 1, NULL, 1002, 5, 1, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (27, localtimestamp, localtimestamp, 'system', 'system', 3, 1, 0, 'state', 'SELECT', 'FULL', 1, 1, NULL, 1008, 5, 1, 0);
INSERT INTO uielement (id, created_at, updated_at, user_create, user_update, col, is_editable, line, name, ui_component, ui_component_width, ui_element_width, is_visible, id_cat_entity_relationship, id_cat_ins_attribute, id_uicontext, id_uigroup, is_required)
VALUES (28, localtimestamp, localtimestamp, 'system', 'system', 4, 1, 0, 'externalCode', 'INPUT', 'FULL', 2, 1, NULL, 1003, 5, 1, 0);