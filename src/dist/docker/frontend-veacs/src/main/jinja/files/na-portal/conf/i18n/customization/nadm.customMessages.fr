customKey.keyId.1stSidekey = 1ère latéral
customKey.keyId.1stKeyOnTheLeftColumnOfThe1stmodule = 1ère du 1er module colonne gauche
customKey.keyId.1stKeyOnTheRightColumnOfThe1stModule = 1ère du 1er module colonne droite
customKey.keyId.1stKeyOnTheLeftColumnOfThe2ndModule = 1ère du 2ème module colonne gauche
customKey.keyId.1stKeyOnTheRightColumnOfThe2ndModule = 1ère du 2ème module colonne droite
customKey.keyId.1stKeyOnTheLeftColumnOfThe3rdModule = 1ère du 3ème module colonne gauche
customKey.keyId.1stKeyOnTheRightColumnOfThe3rdModule = 1ère du 3ème module colonne droite
customKey.keyId.1stKeyOnTheLeftColumnOfThe4thModule = 1ère du 4ème module colonne gauche
customKey.keyId.1stKeyOnTheRightColumnOfThe4thModule =1ère du 4ème module colonne droite
customKey.keyId.1stKeyOnTheLeftColumnOfThe5thModule = 1ère du 5ème module colonne gauche
customKey.keyId.1stKeyOnTheRightColumnOfThe5thModule = 1ère du 5ème module colonne droite
customKey.keyId.1stKeyOnTheLeftColumnOfThe6thModule = 1ère du 6ème module colonne gauche
customKey.keyId.1stKeyOnTheRightColumnOfThe6thModule = 1ère du 6ème module colonne droite
customKey.keyId.1stKeyOfThe1stPageOfThe1stModule = 1er de la 1ère page du 1er module
customKey.keyId.1stKeyOfThe2ndPageOfThe1stModule = 1er de la 2ème page du 1er module
customKey.keyId.1stKeyOfThe1stPageOfThe2ndModule = 1er de la 1ère page du 2ème module
customKey.keyId.1stKeyOfThe2ndPageOfThe2ndModule = 1er de la 2ème page du 2ème module
customKey.keyId.1stKeyOfThe1stPageOfThe3rdModule = 1er de la 1ère page du 3ème module
customKey.keyId.1stKeyOfThe2ndPageOfThe3rdModule = 1er de la 2ème page du 3ème module
customKey.keyId.1stKeyOfThe1stPageOfThe4thModule = 1er de la 1ère page du 4ème module
customKey.keyId.1stKeyOfThe2ndPageOfThe4thModule = 1er de la 2ème page du 4ème module
customKey.keyId.1stKeyOfThe1stPageOfThe5thModule = 1er de la 1ère page du 5ème module
customKey.keyId.1stKeyOfThe2ndPageOfThe5thModule = 1er de la 2ème page du 5ème module
customKey.keyId.1stKeyOfThe1stPageOfThe6thModule = 1er de la 1ère page du 6ème module
customKey.keyId.1stKeyOfThe2ndPageOfThe6thModule = 1er de la 2ème page du 6ème module
customKey.keyId.1stKey2ndPageOfTheScreen = 1ère de la 2ème page de l''écran
customKey.keyId.1stKey3rdPageOfTheScreen = 1ère de la 3ème page de l''écran
customKey.keyId.1stKeyOfThe1stModule = 1ère du 1er module
customKey.keyId.1stKeyOfThe2ndModule = 1ère du 2ème module
customKey.type.opt.SpeedDial = Numérotation rapide
customKey.type.opt.CallPickup = Appel décroché
customKey.type.opt.blfWithSpeedDial = BLF avec numérotation rapide
customKey.type.opt.blfWithCallPickup = BLF avec prise d''appel
customKey.type.opt.blfWithCallPickupAndSpeedDial = BLF avec prise d''appel et Numérotation rapide
customKey.type.opt.blf = BLF
customKey.type.opt.aggregateLine = Ligne Agrégée
customKey.type.opt.intercomPaging = Intercom Paging
customKey.type.opt.multicastPaging = Multicast Paging
customKey.type.opt.callPark = Call Park
customKey.type.opt.monitoredCallPark = Monitored Call Park

NADM.genericPhone.callPark = Call Park Management
NADM.genericPhone.callPark.monitoring = Call Park Monitoring

NADM.genericPhone.customKeys.customKey.type.intercomPaging = Intercom Paging
NADM.genericPhone.customKeys.customKey.type.multicastPaging = Multicast Paging

NADM.genericPhone.multicastPaging = Multicast Paging
NADM.genericPhone.multicastPaging.address = Adresse Multicast
NADM.genericPhone.multicastPaging.senderId = Id d''Envoi
NADM.genericPhone.multicastPaging.codecs = Multicast Paging Codec
NADM.genericPhone.multicastPaging.priorityActive = Paging Priority Active
NADM.genericPhone.multicastPaging.pagingBarge = Paging Barge
NADM.genericPhone.multicastPaging.pagingBargeDefault = Paging Barge Default
NADM.genericPhone.multicastPaging.senderChannel = Canal du Multicast d''envoi
NADM.genericPhone.multicastPaging.nameDefault = Nom du canal Default
NADM.genericPhone.multicastPaging.channelDefault = Canal Default
NADM.genericPhone.multicastPaging.namePriority = Nom du canal Priority
NADM.genericPhone.multicastPaging.channelPriority = Canal Priority
NADM.genericPhone.multicastPaging.nameEmergency = Nom du canal Emergency
NADM.genericPhone.multicastPaging.channelEmergency = Canal Emergency
NADM.genericPhone.multicastPaging.listeningAddress1 = Adresse d''écoute Multicast Priorité 1
NADM.genericPhone.multicastPaging.listeningName1 = Nom du Multicast d''écoute Priorité 1
NADM.genericPhone.multicastPaging.listeningChannel1 = Canal du Multicast d''écoute 1
NADM.genericPhone.multicastPaging.listeningAddress2 = Adresse d''écoute Multicast Priorité 2
NADM.genericPhone.multicastPaging.listeningName2 = Nom du Multicast d''écoute Priorité 2
NADM.genericPhone.multicastPaging.listeningChannel2 = Canal du Multicast d''écoute 2
NADM.genericPhone.multicastPaging.listeningAddress3 = Adresse d''écoute Multicast Priorité 3
NADM.genericPhone.multicastPaging.listeningName3 = Nom du Multicast d''écoute Priorité 3
NADM.genericPhone.multicastPaging.listeningChannel3 = Canal du Multicast d''écoute 3
NADM.genericPhone.multicastPaging.listeningAddress4 = Adresse d''écoute Multicast Priorité 4
NADM.genericPhone.multicastPaging.listeningName4 = Nom du Multicast d''écoute Priorité 4
NADM.genericPhone.multicastPaging.listeningChannel4 = Canal du Multicast d''écoute 4
NADM.genericPhone.multicastPaging.listeningAddress5 = Adresse d''écoute Multicast Priorité 5
NADM.genericPhone.multicastPaging.listeningName5 = Nom du Multicast d''écoute Priorité 5
NADM.genericPhone.multicastPaging.listeningChannel5 = Canal du Multicast d''écoute 5
NADM.genericPhone.multicastPaging.listeningAddress6 = Adresse d''écoute Multicast Priorité 6
NADM.genericPhone.multicastPaging.listeningName6 = Nom du Multicast d''écoute Priorité 6
NADM.genericPhone.multicastPaging.listeningChannel6 = Canal du Multicast d''écoute 6
NADM.genericPhone.multicastPaging.listeningAddress7 = Adresse d''écoute Multicast Priorité 7
NADM.genericPhone.multicastPaging.listeningName7 = Nom du Multicast d''écoute Priorité 7
NADM.genericPhone.multicastPaging.listeningChannel7 = Canal du Multicast d''écoute 7
NADM.genericPhone.multicastPaging.listeningAddress8 = Adresse d''écoute Multicast Priorité 8
NADM.genericPhone.multicastPaging.listeningName8 = Nom du Multicast d''écoute Priorité 8
NADM.genericPhone.multicastPaging.listeningChannel8 = Canal du Multicast d''écoute 8
NADM.genericPhone.multicastPaging.listeningAddress9 = Adresse d''écoute Multicast Priorité 9
NADM.genericPhone.multicastPaging.listeningName9 = Nom du Multicast d''écoute Priorité 9
NADM.genericPhone.multicastPaging.listeningChannel9 = Canal du Multicast d''écoute 9
NADM.genericPhone.multicastPaging.listeningAddress10 = Adresse d''écoute Multicast Priorité 10
NADM.genericPhone.multicastPaging.listeningName10 = Nom du Multicast d''écoute Priorité 10
NADM.genericPhone.multicastPaging.listeningChannel10 = Canal du Multicast d''écoute 10

NADM.Equipment = Équipement
NADM.name = Adresse MAC
NADM.nameSN = Numéro de Série
NADM.deviceInfo.time = Temps
NADM.deviceInfo.time.dateformat = Format de la date
NADM.deviceInfo.time.timeformat = Format du temps
NADM.deviceInfo.time.timezone = Fuseau horaire
NADM.deviceInfo.daylightSavingTime = Horaire d''été
NADM.deviceInfo.daylightSavingTime.enable = Activer
NADM.deviceInfo.daylightSavingTime.type = Type
NADM.deviceInfo.daylightSavingTime.startWeekDay = Premier jour de la semaine
NADM.deviceInfo.daylightSavingTime.startWeekDayOcurrence = Nº de l''occurrence du premier jour de la semaine
NADM.deviceInfo.daylightSavingTime.startMonth = Mois du début
NADM.deviceInfo.daylightSavingTime.startDay = Jour du début
NADM.deviceInfo.daylightSavingTime.startHour = Heure du début
NADM.deviceInfo.daylightSavingTime.endWeekDay = Dernier jour de la semaine
NADM.deviceInfo.daylightSavingTime.endWeekDayOcurrence = Nº de l''occurrence du dernier jour de la semaine
NADM.deviceInfo.daylightSavingTime.endMonth = Mois de fin
NADM.deviceInfo.daylightSavingTime.endDay = Jour de fin
NADM.deviceInfo.daylightSavingTime.endHour = Heure de fin
NADM.deviceInfo.daylightSavingTime.offset = Offset
NADM.deviceInfo.daylightSavingTime.options = Options
NADM.deviceInfo.DNS = DNS
NADM.deviceInfo.DNS.primary = DNS Primaire
NADM.deviceInfo.DNS.secondary = DNS Secondaire
NADM.deviceInfo.NTP = NTP
NADM.deviceInfo.NTP.primary = NTP Primaire
NADM.deviceInfo.NTP.secondary = NTP Secondaire
NADM.deviceInfo.lan = Lan
NADM.deviceInfo.lan.addressingType = Type d''adresse 
NADM.deviceInfo.lan.ipAddress = Adresse IP
NADM.deviceInfo.lan.subnetMask = Masque du sous-réseau
NADM.deviceInfo.lan.defaultGateway = Default Gateway
NADM.deviceInfo.ldap = LDAP
NADM.deviceInfo.ldap.enable = Activer
NADM.deviceInfo.ldap.host = Host
NADM.deviceInfo.ldap.port = Port
NADM.deviceInfo.ldap.Utilisateur = Utilisateur
NADM.deviceInfo.ldap.password = Mot de passe
NADM.deviceInfo.ldap.base = Base
NADM.deviceInfo.ldap.sort = Mettre par ordre
NADM.deviceInfo.ldap.nameFilter = Filtre du Nom
NADM.deviceInfo.ldap.numberFilter = Filtre du Numéro
NADM.deviceInfo.ldap.displayNameAttributes = Attributs Nom d''affichage
NADM.deviceInfo.ldap.displayNumberAttributes = Attriuts Numéro d''affichage
NADM.deviceInfo.ldap.displayName = Paramètres d''affichage
NADM.deviceInfo.ldap.lookupDisplayName = Recherche par Display Name
NADM.deviceInfo.ldap.lookup = Recherche pendant l''appel
NADM.deviceInfo.ldap.clientDn = DN du Client
NADM.deviceInfo.ldap.firstNameFilter = Filtre Prénom
NADM.deviceInfo.ldap.lastNameFilter = Filtre Nom
NADM.deviceInfo.ldap.searchItem3 = Item de Recherche 3
NADM.deviceInfo.ldap.searchFilter3 = Filtre de Recherche 3
NADM.deviceInfo.ldap.searchItem4 = Item de Recherche 4
NADM.deviceInfo.ldap.searchFilter4 = Filtre de Recherche 4
NADM.deviceInfo.ldap.displayAttributes = Attributs d''affichage
NADM.deviceInfo.ldap.mode = Mode
NADM.deviceInfo.ldap.version = Version
NADM.deviceInfo.ldap.maxHits = Nº maximum de résultats
NADM.deviceInfo.ldap.duInitialQuery = Faire une première consultation
NADM.deviceInfo.ldap.predictText = Prévoir le texte
NADM.deviceInfo.ldap.authMethod = Méthode d''authentification
NADM.deviceInfo.ldap.numberMapping = Mappage des numéros
NADM.deviceInfo.ldap.corpDirName = Nom de l''annuaire d''entreprise
NADM.deviceInfo.ldap.displayFirstNameAttribute = Attribut du Prénom d''affichage
NADM.deviceInfo.ldap.displayLastNameAttribute = Attribut du Nom d''affichage
NADM.deviceInfo.ldap.displayWorkNumberAttribute = Attribut Numéro de Travail d''affichage
NADM.deviceInfo.ldap.displayHomeNumberAttribute = Attribut Numéro de la Maison d''affichage
NADM.deviceInfo.ldap.displayMobileNumberAttribute = Attribut Numéro du Portable d''affichage

NADM.genericPhone = Paramètres Génériques
NADM.genericPhone.language = Langue
NADM.genericPhone.voiceVlanId = ID de Vlan de Voix
NADM.genericPhone.dataVlanId = ID de Vlan de Données
NADM.genericPhone.speakerGain = Gain de haut-parleur
NADM.genericPhone.handsetGain = Gain du combiné
NADM.genericPhone.headsetGain = Gain du casque
NADM.genericPhone.userPassword = Mot de passe de l''Utilisateur
NADM.genericPhone.adminPassword = Mot de passe de l''Administrateur
NADM.genericPhone.screensaverEnable = Activer économiseur d''écran 
NADM.genericPhone.screensaverWaitTime = Temps d''attente pour l''économiseur d''écran
NADM.genericPhone.screensaverImageSelect = Choisir l''image pour l''économiseur d''écran
NADM.genericPhone.backgroundImageURL = URL pour l''image de fond
NADM.genericPhone.backgroundImageText = Texte de fond
NADM.genericPhone.backgroundImageSelect = Choisir Image de fond
NADM.genericPhone.callPickupCode = Code de CallPickup
NADM.genericPhone.callPickupAudioNotification = Notification Audio pour CallPickup
NADM.genericPhone.callPickupTone = Tom de CallPickup
NADM.genericPhone.blfSubscriptionTimeout = BLF Subscription Time Out
NADM.genericPhone.customKeyPrimaryLine = Ligne primaire associée aux touches dédiées
NADM.genericPhone.customKeys = Touches dédiées
NADM.genericPhone.customKeys.customKey = Touches dédiées
NADM.genericPhone.customKeys.customKey.keyId = Touche ID
NADM.genericPhone.customKeys.customKey.line = Ligne
NADM.genericPhone.customKeys.customKey.name = Nom
NADM.genericPhone.customKeys.customKey.monitor = Ligne a controler
NADM.genericPhone.customKeys.customKey.type = Type
NADM.genericPhone.stationName = Nom de station
NADM.genericPhone.dialplan = Dial Plan
NADM.genericPhone.dialplan.rule = Règle
NADM.genericPhone.dialplan.areaCode = indicatif régional
NADM.genericPhone.dialplan.areaCode.code = code
NADM.genericPhone.dialplan.areaCode.minLength = Taille minimum
NADM.genericPhone.dialplan.areaCode.maxLength = Taille maximum
NADM.genericPhone.dialplan.blockOut = Blocage des numéros sortants
NADM.genericPhone.dialplan.dialNow = Dial Now
NADM.genericPhone.codecs = Codecs
NADM.genericPhone.callProgressTones = Call Progress Tones
NADM.genericPhone.callProgressTones.toneScheme = Tone Scheme
NADM.genericPhone.callProgressTones.busyTone = Busy Tone
NADM.genericPhone.callProgressTones.callWaitingTone = Call Waiting Tone
NADM.genericPhone.callProgressTones.callWaitingRingBackTone = Call Waiting Ring Back Tone
NADM.genericPhone.callProgressTones.confirmTone = Confirm Tone
NADM.genericPhone.callProgressTones.dialTone = Dial Tone
NADM.genericPhone.callProgressTones.holdingTone = Holding Tone
NADM.genericPhone.callProgressTones.mwiTone = MWI Tone
NADM.genericPhone.callProgressTones.offHookWarningTone = Off Hook Warning Tone
NADM.genericPhone.callProgressTones.secondDialTone = Second Dial Tone
NADM.genericPhone.callProgressTones.ringBackTone = Ring Back Tone
NADM.genericPhone.callProgressTones.cadence = Cadence
NADM.genericPhone.callProgressTones.frequency1 = Fréquence 1 (Hz)
NADM.genericPhone.callProgressTones.frequency2 = Fréquence 2 (Hz)
NADM.genericPhone.callProgressTones.frequency3 = Fréquence 3 (Hz)
NADM.genericPhone.callProgressTones.frequency4 = Fréquence 4 (Hz)
NADM.genericPhone.callProgressTones.toneOn1 = Tonnalitée On 1 (10ms)
NADM.genericPhone.callProgressTones.toneOn2 = Tonnalitée On 2 (10ms)
NADM.genericPhone.callProgressTones.toneOn3 = Tonnalitée On 3 (10ms)
NADM.genericPhone.callProgressTones.toneOn4 = Tonnalitée On 4 (10ms)
NADM.genericPhone.callProgressTones.toneOff1 = Tonnalitée Off 1 (10ms)
NADM.genericPhone.callProgressTones.toneOff2 = Tonnalitée Off 2 (10ms)
NADM.genericPhone.callProgressTones.toneOff3 = Tonnalitée Off 3 (10ms)
NADM.genericPhone.callProgressTones.toneOff4 = Tonnalitée Off 4 (10ms)
NADM.genericPhone.callProgressTones.frequencyLevel1 = Niveau de Fréquence 1 (- dBm)
NADM.genericPhone.callProgressTones.frequencyLevel2 = Niveau de Fréquence 2 (- dBm)
NADM.genericPhone.callProgressTones.frequencyLevel3 = Niveau de Fréquence 3 (- dBm)
NADM.genericPhone.callProgressTones.frequencyLevel4 = Niveau de Fréquence 4 (- dBm)
NADM.genericPhone.callProgressTones.gs.frequencyLevel = Niveau de Fréquence
NADM.genericPhone.callProgressTones.duration = Duration (s)
NADM.genericPhone.callProgressTones.yealink.duration = Duration (msec)
NADM.genericPhone.messageWaitingIndicator = Message Waiting Indicator
NADM.genericPhone.callProgressTones.cisco.toneOn1 = Tonnalitée On 1 (s)
NADM.genericPhone.callProgressTones.cisco.toneOn2 = Tonnalitée On 2 (s)
NADM.genericPhone.callProgressTones.cisco.toneOn3 = Tonnalitée On 3 (s)
NADM.genericPhone.callProgressTones.cisco.toneOn4 = Tonnalitée On 4 (s)
NADM.genericPhone.callProgressTones.cisco.toneOff1 = Tonnalitée Off 1 (s)
NADM.genericPhone.callProgressTones.cisco.toneOff2 = Tonnalitée Off 2 (s)
NADM.genericPhone.callProgressTones.cisco.toneOff3 = Tonnalitée Off 3 (s)
NADM.genericPhone.callProgressTones.cisco.toneOff4 = Tonnalitée Off 4 (s)
NADM.genericPhone.callProgressTones.yealink.toneOn1 = Tonnalitée On 1 (ms)
NADM.genericPhone.callProgressTones.yealink.toneOn2 = Tonnalitée On 2 (ms)
NADM.genericPhone.callProgressTones.yealink.toneOn3 = Tonnalitée On 3 (ms)
NADM.genericPhone.callProgressTones.yealink.toneOn4 = Tonnalitée On 4 (ms)
NADM.genericPhone.callProgressTones.yealink.toneOff1 = Tonnalitée Off 1 (ms)
NADM.genericPhone.callProgressTones.yealink.toneOff2 = Tonnalitée Off 2 (ms)
NADM.genericPhone.callProgressTones.yealink.toneOff3 = Tonnalitée Off 3 (ms)
NADM.genericPhone.callProgressTones.yealink.toneOff4 = Tonnalitée Off 4 (ms)
NADM.genericPhone.callWaiting = Call Waiting
NADM.genericPhone.callWaiting.enable = Activer
NADM.genericPhone.callWaiting.enableTone = Activer sonnerie
NADM.genericPhone.callWaiting.onCode = Code d''activation
NADM.genericPhone.callWaiting.offCode = Code de désactivation

NADM.firmwareUpgrade = Mise à jour du Firmware
NADM.firmwareUpgrade.enable = Activer
NADM.firmwareUpgrade.expectedVersion = Version attendue
NADM.firmwareUpgrade.migrateConfig = Migration des Configurations
NADM.firmwareUpgrade.scheduleEnabled = Activer calendrier
NADM.firmwareUpgrade.scheduleStartTime = Horaire du début
NADM.firmwareUpgrade.scheduleEndTime = Horaire de fin
NADM.firmwareUpgrade.file = Fichier du Firmware 
NADM.firmwareUpgrade.customUrl = URL personnalisé pour mise à jour
NADM.firmwareUpgrade.associatedEquipmentEnabled = Activer mise à jour pour l''équipement associé
NADM.firmwareUpgrade.associatedEquipmentFile = Fichier du firmware pour l''équipement associé
NADM.firmwareUpgrade.associatedEquipmentCustomUrl = URL personnalisé pour mise à jour de l''équipement associé

NADM.services = Services
NADM.services.service = Service
NADM.services.service.name = Nom
NADM.services.service.serviceData = Données de Service
NADM.services.service.serviceData.voiceProfile = Données de Service
NADM.services.service.voiceProfile = Profil vocal
NADM.services.service.voiceProfile.line = Ligne
NADM.services.service.voiceProfile.password = Mot de passe
NADM.services.service.voiceProfile.proxy = Proxy
NADM.services.service.voiceProfile.outboundproxy = OutboundProxy
NADM.services.service.voiceProfile.codecs = Codecs
NADM.service.service.voiceProfile.dialplan = Dial Plan
NADM.services.service.voiceProfile.displayName = Display Name
NADM.advancedData = Advanced Data

NADM.regex.mac = Erro: Adresse MAC invalide
NADM.regex.ip = Erro: Adresse IP invalide

NADM.genericduorSystem = Système de porte générique
NADM.genericduorSystem.guestSettings = Configurations pour les invités
NADM.genericduorSystem.enableDTMF = Activer Fréquence Vocale
NADM.genericduorSystem.remotePin = Pin Remote
NADM.genericduorSystem.duorBellMode = Mode sonnette
NADM.genericduorSystem.callMode = Mode d''appel