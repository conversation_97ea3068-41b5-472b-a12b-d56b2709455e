location {{ na_portal_nginx_go_location }} {

  proxy_pass http://na-portal-frontend-go;

  location ~* \.(css|js|png|gif)$ {
    proxy_pass http://na-portal-frontend-go;
    expires 365d;
    add_header Pragma public;
    add_header Cache-Control "public";
  }

  # any route matching */assets/* will be cached by the browser.
  location ~*\/assets\/~*  {
    proxy_pass http://na-portal-frontend-go;
    expires 365d;
    add_header Pragma public;
    add_header Cache-Control "public";
  }
}