#!/bin/bash

set -e

DEFAULTS_INV_ARR=("/config/defaults-na-portal-web-balancer.yml" $DEFAULTS_INV)
CONFIGURATOR=/opt/alticelabs/na-commons/bin/na-commons-utils-jinja-configurator/jinja-configurator.py

python ${CONFIGURATOR} /config/all.yml /opt/alticelabs/na-portal/conf/ /docker-entrypoint-scripts-base.d/ ${TEMPLATE_DIRS} \
  ${DEFAULTS_INV_ARR[@]/#/-d }

echo configuring nginx...
chmod +x /docker-entrypoint-scripts-base.d/configure_nginx.sh
/docker-entrypoint-scripts-base.d/configure_nginx.sh

echo starting nginx...
exec /usr/sbin/nginx -g 'daemon off;'
