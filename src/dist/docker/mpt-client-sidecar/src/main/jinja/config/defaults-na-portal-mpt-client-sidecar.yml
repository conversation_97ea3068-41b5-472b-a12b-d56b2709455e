---

#' Indicates the port that can connect to the mpt client agent.
#' $integer
#' %default
na_portal_mpt_client_port: 4001

#' In case we want to install the mpt-cli/mpt-agent in a different machine from the backend one,
#' this attribute indicates the ip of that machine.
#' $string
#' %default
na_portal_mpt_client_host: "{{ na_portal_be_nbi_host }}"

#' Indicates which environment the app is running on. This information will be used in graylog and console
#' and that's why the variable is only used when the graylog and/or console key are defined in the log4j2 dictionary.
#' $string
#' %default
application_environment: "stdout"

## logging
#' Indicates the type of product (fulfillment or device management).
#' Utilized in the graylog for log4j2 appender to give context about which application its logging.
#' *Note:* This variable only makes sense if the _log4j2_ variable isn't redefined.
#' $string
#' %default
product_type: "FULFILLMENT"
#' Dictionary in which each key should be the name of the appender and the values are the same that are used in the _rollingFile_ appender from the _log4j2_ variable.
#' $dictionary
#' %default
#' @"{ SERVER: { file: '/var/log/alticelabs/na/na-mpt-client/server.log', maxFileSize: '100MB', maxBackupIndex: '12' } }"
rollingFile_appenders_customization: {}
#' Dictionary list, in which each object must contain information about the loggers that will be added by the solution/customization.
#' $list
#' %default
#' @"- { name: "LOGGER_CUSTOMIZATION", level: "info", refs: [ "APPENDER" ] }"
loggers_customization: []

#' Dictionary used to configure log4j2. There's three possible keys for the appenders (all three can be defined at the same time):
#' * _graylog_
#' ** *appender_name*: appender name (mandatory)
#' ** *application_name*: name of the application that is being logged, important for graylog (mandatory, format type NOSSIS_<FULFILLMENT or DM>)
#' ** *service_name*: name of the service that is being logged, important for graylog (mandatory, format type <UD>_<MODULE>)
#' ** *layout*: Logs layout definition (optional)
#' ** *compressionThreshold*: Occurs compression when the data is bigger than the number of defined bytes (optional).
#' ** *compressionType*: Type of compression to use (optional, default value is 'OFF')
#' ** *use_host_in_prefix*: Boolean that specifies if it's intended to add the host name to the docker hostname (optional)
#' ** *thresholdFilter*: Inclusion only of the logs with level equal or higher than the defined (optional)
#' * _rollingFile_
#' ** *file*: path to the log file (mandatory)
#' ** *maxBackupIndex*: maximum number of backup files (not necessary if timeInterval is defined)
#' ** *maxFileSize*: Maximum size of log files (not necessary if timeInterval is defined)
#' ** *timeInterval*: The frequency in which a rollover must occur according to the most specific time unit in the used date pattern (not necessary if maxBackupIndex is defined)
#' ** *timeModulate*: Specifies if the interval should be adjusted to make the next rollover occur in the interval limit (optional)
#' ** *filePermissions*: File permission level (optional)
#' ** *fileOwner*: owner that the file will be created (optional)
#' ** *fileGroup*: group that the file will be created (optional)
#' ** *layout*: Definition of logs layout (optional)
#' ** *thresholdFilter*: Inclusion only of the logs with level equal or superior to the defined (optional)
#' * _console_
#' ** *name*: name of the application that is being logged (mandatory)
#' ** *compressionType*: Type of compression to use (optional, default value is 'OFF')
#' ** *compressionThreshold*: Occurs compression when the data is bigger than the number of defined bytes (optional)
#' ** *layout*: Specification of the logs layout (optional)
#' ** *thresholdFilter*: Inclusion only of the logs with level equal or superior to the defined (optional)
#' Other than this, there's also the need to define key loggers, which is a list of dictionaries and contain the information related with
#' the loggers (name, level, refs and additivity), and the key root, which is a dictionary and contains the keys levels and refs.
#' *Note*: The appender _rollingFile_ is a dictionary and can contain more than one entry (the other 2 appenders can't)
#' $dictionary
#' %default
log4j2:
  console:
    name: "NOSSIS_{{ product_type }}_NA-PORTAL_MPT_CLIENT_SIDECAR"
    layout: "%d [%t] %-5p %c - %m%n"
    compressionType: "OFF"
  loggers:
    - name: "pt.ptinovacao.na.portal.mpt.client"
      level: "debug"
      refs: ["NOSSIS_{{ product_type }}_NA-PORTAL_MPT_CLIENT_SIDECAR"]
      additivity: "false"
  root:
    level: "warn"
    refs: [ "NOSSIS_{{ product_type }}_NA-PORTAL_MPT_CLIENT_SIDECAR" ]