exception.90001.title=Resource not found
exception.90002.title=Internal error
exception.90003.title=Update service error
exception.90004.title=Processing error

nadm.module.title = NADM
nadm.module.description = NADM description
nadm.mega.menu.navinitial = Homepage
nadm.home.label.title = NADM

### Navigation ###
na.portal.nadm.nav.search = Search
na.portal.nadm.nav.globalEquip = Equipments configuration
na.portal.nadm.nav.client = Clients
na.portal.nadm.nav.services = Services
na.portal.nadm.nav.equipment = Equipments

### Search ###
nadm.search.equipconfig.title = Equipments configuration
nadm.search.service.title = Services
nadm.search.client.title = Clients
nadm.search.equipment.title = Equipments
na.portal.nadm.associations.search.service.title = Service search

### Detail ###

na.portal.nadm.detail.clientequipment.edit = Edit equipment configuration
na.portal.nadm.detail.clientequipment.view = Equipment configuration
na.portal.nadm.detail.clientequipment.create = Create equipment configuration
na.portal.nadm.detail.equipmentassociation.view = Association
na.portal.nadm.detail.equipmentassociation.view.firmwareFilename = Firmware file

### Create ###
na.portal.nadm.create.equipment = Create equipment
na.portal.nadm.create.service = Create service

### Sections ###
nadm.detail.section.identification = Identification
nadm.detail.section.characteristics = Characteristics

## TABS ##
na.portal.nadm.tabname.equipModel = Equipments configuration
na.portal.nadm.tabname.characteristics = Characteristics
na.portal.nadm.tabname.associations = Associations

# FORM BLOCKS
na.portal.nadm.blockname.characteristics = General characteristics
na.portal.nadm.blockname.characteristics.base = Basic characterization

###########FIELDS###########
na.portal.nadm.field.beginDate.label = Begin date/time
na.portal.nadm.field.endDate.label = End date/time
na.portal.nadm.field.user.label = Client
na.portal.nadm.field.user.placeholder = Client identifier
na.portal.nadm.field.contactIP.label = Contact IP
na.portal.nadm.field.contactIP.placeholder = Contact IP
na.portal.nadm.field.tags.label = Tags
na.portal.nadm.field.tags.placeholder = All tags
na.portal.nadm.field.tags.newTagLabel = (New tag)
na.portal.nadm.field.equipid.label = Identifier
na.portal.nadm.field.equipid.placeholder = Equipment identifier
na.portal.nadm.field.modificationBeginDate.label = Modified since
na.portal.nadm.field.modificationEndDate.label = to
na.portal.nadm.field.manufacturers.allOptions = All manufacturers
na.portal.nadm.field.model.allOptions = All models
na.portal.nadm.field.version.allOptions = All versions
na.portal.nadm.field.service.label = Service
na.portal.nadm.field.name.label = Name
na.portal.nadm.field.service.placeholder = Service
na.portal.nadm.field.serviceType.allOptions = All types

na.portal.nadm.lastContactTime.since = Last contacted since
na.portal.nadm.lastContactTime.to = to

na.portal.nadm.firstContactTime.since = First contacted since
na.portal.nadm.firstContactTime.to = to


###########FORM###########
na.portal.nadm.manufacturer = Manufacturer
na.portal.nadm.model = Model
na.portal.nadm.version = Version
na.portal.nadm.client = Client
na.portal.nadm.state = State
na.portal.nadm.provisionStatus.ALL = All
na.portal.nadm.provisionStatus.PROVISIONED = Provisioned
na.portal.nadm.provisionStatus.NOT_PROVISIONED = Not provisioned
na.portal.nadm.globalEquipment.impact = Impact in clients and equipments
na.portal.nadm.equipment.impact = Impact in this equipment
na.portal.nadm.clientEquipment.impact  = Impact in equipments
na.portal.nadm.revert = Revert
na.portal.nadm.inheritValue = Apply inherited value
na.portal.nadm.editValue = Edit value

###########Datatables###########
na.portal.nadm.name = Name
na.portal.nadm.resourceId = Index
na.portal.nadm.clientId = Client
na.portal.nadm.contactIp = Contact IP
na.portal.nadm.tags = Tags
na.portal.nadm.equipIdentifier = Identifier
na.portal.nadm.lastContactTime = Last contact time
na.portal.nadm.firstContactTime = First contact time
na.portal.nadm.numEquipments = Equipments no.
na.portal.nadm.numClients = Clients no.
na.portal.nadm.numServices = Services no.
na.portal.nadm.actions = Actions
na.portal.nadm.type = Type
na.portal.nadm.equipments = Equipments
na.portal.nadm.provisionedEquipments = Provisioned equipments
na.portal.nadm.nonProvisionedEquipments = Non provisioned equipments
na.portal.nadm.numEquipmentModels = Models no.
na.portal.nadm.origin = Origin
na.portal.nadm.description = Description
na.portal.nadm.value = Value
na.portal.nadm.impact = Impact
na.portal.nadm.confirm = Confirm
na.portal.nadm.apply = Apply
na.portal.nadm.cancel = Cancel
na.portal.nadm.create = Create
na.portal.nadm.characterize.equipment = Characterize equipment
na.portal.nadm.characterize.service = Characterize service
na.portal.nadm.lastModificationTime = Last modification
na.portal.nadm.add.custom.key = add custom key

#### Tooltips  ####
na.portal.nadm.icon.external=The attribute value is what is filled in external system.
na.portal.nadm.icon.global=The settings are the same as global device settings in the system.
na.portal.nadm.icon.client=There are client-specific settings.

######## Notification ##############
na.portal.nadm.characterize.after.fill = You can characterize after filling the identification form

########### NADM ERROR MESSAGES ###########

na.portal.nadm.tab.content.error.title = Operation error
na.portal.nadm.tab.content.error.description = An error occurred during the operation.
na.portal.nadm.associations.type.error.title = Type required
na.portal.nadm.associations.type.error.description = Type required to define an associations
na.portal.nadm.associations.resourceocupied.error.title = Resource not empty
na.portal.nadm.associations.resourceocupied.error.description = The chosen resource is not empty
na.portal.nadm.associations.error.creating.association = Error creating association
na.portal.nadm.associations.service.required.title = Service required
na.portal.nadm.associations.service.required.description = Its mandatory to select a service
na.portal.nadm.associations.type.service.associate = associate service type
na.portal.nadm.associations.type.service.select.line = Select
na.portal.nadm.associations.type.service.line = Line
na.portal.nadm.associations.first.key.each.module = <b>Line {0}</b>
na.portal.nadm.associations.service.total.and.available.slots = <b>{0}</b> available slots of <b>{1}</b> existing slots to associate services
na.portal.nadm.associations.service.info.slots.only.one = only <b>{0}</b> (filled/total): <b>{1}</b>
na.portal.nadm.associations.service.info.slots.both = <b>{0}</b> or <b>{1}</b>: <b>{2}</b>
na.portal.nadm.associations.service.info.slots.only.one.less = only <b>{0}</b>: <b>{1}</b>
na.portal.nadm.associations.service.info.slots = Existing slots to {2}, {3}, {4}
na.portal.nadm.associations.service.search.form.all = All
na.portal.nadm.associations.service.info.all.slots.available = There is no service types associated to this equipment yet.

########### NADM MODALS ###############

na.portal.nadm.modal.entity.delete.title                 = Remove
na.portal.nadm.modal.entity.clone.equipment.title        = Clone equipment
na.portal.nadm.modal.entity.clone.equipment.subtitle     = In this area it is possible to clone an equipment and create a new one with the same configurations.
na.portal.nadm.modal.entity.remove                       = The equipment "{0}" will be removed
na.portal.nadm.modal.global.entity.remove.title          = Remove equipment configuration
na.portal.nadm.modal.global.entity.remove                = The equipment configuration {0} will be deleted. Do you want to confirm?
na.portal.nadm.modal.client.remove                       = The client "{0}" will be removed
na.portal.nadm.modal.service.remove                      = The service "{0}" will be removed
na.portal.nadm.modal.success.remove.equipment             = Equipment successfully deleted
na.portal.nadm.modal.success.remove.equipment.message     = The equipment "{0}" has been successfully deleted.
na.portal.nadm.modal.success.remove.equipment.massive           = Equipments successfully deleted
na.portal.nadm.modal.success.remove.equipment.massive.message   = Successfully deleted {0} equipments.
na.portal.nadm.modal.success.remove.global.equipment         = Equipments configuration successfully deleted
na.portal.nadm.modal.success.remove.global.equipment.message = {0} equipments configuration successfully deleted
na.portal.nadm.modal.success.remove.client                = Client successfully deleted
na.portal.nadm.modal.success.remove.client.message        = Client "{0}" has been successfully deleted.
na.portal.nadm.modal.success.remove.service               = Service successfully deleted
na.portal.nadm.modal.success.remove.service.message       = The service "{0}" has been successfully deleted.
na.portal.nadm.modal.success.create.client.equipment     = Configuration successfully created
na.portal.nadm.modal.success.create.client.equipment.message = Configuration "{0}" has been successfully created
na.portal.nadm.modal.success.edit.client.equipment       = Configuration successfully edited
na.portal.nadm.modal.success.edit.client.equipment.message = Configuration "{0}" has been successfully edited
na.portal.nadm.modal.success.update.global.equipment         = Configuration and template successfully updated
na.portal.nadm.modal.success.update.global.equipment.message = {0} equipments configuration and template successfully updated
na.portal.nadm.modal.cannotremove                        = can not delete configuration
na.portal.nadm.modal.cannotremove.message                = Can not delete configuration "{0}" it exists {1} instance(s) of equipment of it.
na.portal.nadm.modal.error.remove.equipment.massive         = Error removing equipments
na.portal.nadm.modal.error.remove.equipment.massive.message = The removal of {0} of {1} selected devices was not possible
na.portal.nadm.modal.error.equipment.list.button            = view equipment list with errors

na.portal.nadm.modal.clientName.field.label         = Client name
na.portal.nadm.modal.clientName.title               = Create client
na.portal.nadm.modal.clientName.field.description   = Description

na.portal.nadm.modal.removeEquipment.title          = Remove equipments
na.portal.nadm.modal.removeEquipment.description    = {0} equipments will be removed. Do you want to confirm?

na.portal.nadm.modal.Association.remove = Remove resource association from line {0}?

########### DIALOG ############
na.portal.nadm.modal.modifications.body = Leaving this tab may cause data lost. Do you wish to proceed?
na.portal.nadm.modal.modifications.page = Leaving this page may cause data lost. Save modifications?
na.portal.nadm.modal.modifications.formReset = The form will be reset. Do you wish to continue?
na.portal.nadm.modal.modifications.header = Modifications

######## BUTTON #############
na.portal.nadm.button.associate                             = associate

######## Operations ########
na.portal.nadm.button.bulkoperations.group                  = Select desired action
na.portal.nadm.bulkoperations.button                        = bulk operations
na.portal.nadm.bulkoperations.tags.button                   = manage tags
na.portal.nadm.bulkoperations.export.button                 = export exporter
na.portal.nadm.bulkoperations.delete.button                 = remove
na.portal.nadm.bulkoperations.question.when                 = When do you want to perform this action?
na.portal.nadm.bulkoperations.scheduling.alert.mpt          = MPT - bulk operations
na.portal.nadm.bulkoperations.scheduling.alert.title        = Scheduling
na.portal.nadm.bulkoperations.scheduling.alert.text         = If you want to define or change a schedule for this action, you can do so at: <b>{0}</b>, in the edition mode.
na.portal.nadm.bulkoperations.radio.start.now               = Immediately
na.portal.nadm.bulkoperations.radio.start.late              = Later
na.portal.nadm.bulkoperations.export.button                 = export
na.portal.nadm.operation.upload.button						= upload Firmware
na.portal.nadm.operation.created.title                      = Request to execute operation {0} performed
na.portal.nadm.operation.now.created.content                = {0} execution progress available on: {1}
na.portal.nadm.operation.later.created.content              = To define or change the <b>schedule</b> of this operation, you can access the module: {1}
na.portal.nadm.operation.clone.created.title                = Request to clone equipment performed
na.portal.nadm.operation.clone.created.content              = Clone progress available on {0}
na.portal.nadm.operation.created.contentLink.individual     = GO - Generic Operations
na.portal.nadm.operation.created.contentLink.massive        = MPT - Mass Provision Tool
na.portal.nadm.operation.created.contentLink.massive.scheduling = MPT - Scheduling of the operation
na.portal.nadm.operation.all.equipments.notification.title  = Selected equipments
na.portal.nadm.operation.close.max.selectable.equipments.notification.title = Selection close to the maximum allowed
na.portal.nadm.operation.close.max.selectable.equipments.notification.content = Individual equipment selection is close to maximum. It is suggested that you refine your search filter.
na.portal.nadm.operation.exceeded.max.selectable.equipments.notification.title = Selection exceeded the maximum allowed equipment
na.portal.nadm.operation.exceeded.max.selectable.equipments.notification.content = As your selection has exceeded the maximum number of equipment allowed, it is suggested that you deselect equipment or refine your search filter. Otherwise you will not be able to carry out massive operations.
na.portal.nadm.operation.close.max.deselectable.equipments.notification.title = Deselection close to the maximum allowed
na.portal.nadm.operation.close.max.deselectable.equipments.notification.content = Individual equipment deselection is close to maximum. It is suggested that you refine your search filter.
na.portal.nadm.operation.exceeded.max.deselectable.equipments.notification.title = Deselection exceeded the maximum allowed equipment
na.portal.nadm.operation.exceeded.max.deselectable.equipments.notification.content = As your deselection has exceeded the maximum number of equipment allowed, it is suggested that you deselect equipment or refine your search filter. Otherwise you will not be able to carry out massive operations.
na.portal.nadm.operation.all.equipments.notification.content = {0} / {1} equipments selected
na.portal.nadm.operation.all.equipments.notification.select.all = select all
na.portal.nadm.operation.all.equipments.notification.unselect.all = unselect all
na.portal.nadm.operation.confirm.individual.action.message  = {0} action will be made to equipment with identifier {1}. Do you want to confirm this action?
na.portal.nadm.operation.confirm.global.individual.action.message  = Action {0} will be performed for {1} equipment configuration. Do you want to confirm this action?
na.portal.nadm.operation.confirm.massive.action.message     = {0} action will be made to all selected equipments.
na.portal.nadm.manage.tags.title                            = Manage tags
na.portal.nadm.manage.tags.subtitle                         = In this area it is possible to associate or remove equipment tags for better management of these
na.portal.nadm.manage.tags.button.calculating               = calculating
na.portal.nadm.manage.tags.button.selected.equipments       = selected equipments
na.portal.nadm.manage.tags.button.selected.equipments.title = all selected equipments
na.portal.nadm.manage.tags.button.associated.tags           = associated tags
na.portal.nadm.manage.tags.button.associated.tags.title     = configured tags on selected equipment
na.portal.nadm.manage.tags.button.common.tags               = common tags
na.portal.nadm.manage.tags.button.common.tags.title         = common tags to selected equipments
na.portal.nadm.manage.tags.select.label                     = Tags
na.portal.nadm.manage.tags.select.placeholder               = Without tags
na.portal.nadm.manage.tags.select.newTag                    = (new tag)
na.portal.nadm.manage.tags.success.individual.title         = equipment configured successfully
na.portal.nadm.manage.tags.success.individual.message       = tags on equipment {0} managed successfully
na.portal.nadm.manage.tags.success.massive.title            = equipments configured successfully
na.portal.nadm.manage.tags.error.massive.title              = Error in tag management
na.portal.nadm.manage.tags.success.massive.message          = tags on selected equipments managed successfully
na.portal.nadm.manage.tags.error.massive.message            = The association/removal of the tags on {0} of the {1} selected devices was not possible
na.portal.nadm.operation.error.massive.action.notification.message = {0} / {1} Equipment with error in the operation of {2}
na.portal.nadm.operation.error.massive.action.notification.button = back to equipment list
na.portal.nadm.operation.repeat.action.error.mode           = repeat action

######### OTHER ###############
na.portal.nadm.association.of = {0} association
na.portal.nadm.error.create.association = Error associating resource
na.portal.nadm.invalid.client.name = Invalid client name
na.portal.nadm.field.all           = select all
na.portal.nadm.uploadfile.label.sending = Sending
na.portal.nadm.uploadfile.label.waiting.server = Waiting for server confirmation
na.portal.nadm.uploadfile.sending.failed.title = Error uploading the firmware file
na.portal.nadm.uploadfile.sending.failed.message = A problem has occurred while uploading the chosen file. Due to this problem the operations were not executed. Please contact the administrator and try later.

######### GO INTEGRATION ###############
na.portal.go.buttons.configureOptionalFields = configure optional fields

######### FILTER ###############
nadm.filter.input.name.label = Filter name
nadm.filter.select.placeholder = Select a filter
nadm.filter.button.save.popover.title = Save filter
nadm.filter.input.name.placeholder = Filter name
nadm.filter.button.delete.title = Delete filter
nadm.filter.button.edit.title = Save
nadm.filter.button.save.title = Save as