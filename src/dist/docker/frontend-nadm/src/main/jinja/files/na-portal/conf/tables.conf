global equipments search {
   route = "na.nadm.controllers.routes.Search.searchGlobalEquipment"
   columns = [
        {
            name: manufacturer-col,
            title: na.portal.nadm.manufacturer
            dataProperty: manufacturer,
            sortable: true,
            initialSortDirection: descendant,
            visible: true
        }, {
            name: model-col,
            title: na.portal.nadm.model
            dataProperty: model,
            sortable: true,
            visible: true
        }, {
            name: version-col,
            title: na.portal.nadm.version
            dataProperty: version,
            sortable: true,
            visible: true
        }, {
            name: equipNumber-col,
            title: na.portal.nadm.numEquipments
            dataProperty: equipments,
            sortable: false,
            visible: true
        }, {
            name: clientNumber-col,
            title: na.portal.nadm.numClients
            dataProperty: clients,
            sortable: false,
            visible: true
        }, {
            name: serviceNumber-col,
            title: na.portal.nadm.numServices
            dataProperty: services,
            sortable: false,
            visible: true
        }, {
            name: actions-col,
            title: na.portal.nadm.actions
            sortable: false,
            customRenderCallName: na.datatableTemplates.nadm.globalEquipmentSearchActions,
            columnWidth: { type: fixed, value: 80px },
            visible: true
        }

   ]
}

equipments search {
 route =  "na.nadm.controllers.routes.Search.searchEquipment"
 features = [
    { name = "select rows" }
 ]
 columns = [
     {
        name: identifier-col,
        title: na.portal.nadm.equipIdentifier
        dataProperty: identifier,
        sortable: true,
        initialSortDirection: descendant,
        visible: true
     }, {
        name: manufacturer-col,
        title: na.portal.nadm.manufacturer
        dataProperty: manufacturer,
        sortable: true,
        visible: true
     }, {
        name: model-col,
        title: na.portal.nadm.model
        dataProperty: model,
        sortable: true,
        visible: true
     }, {
       name: version-col,
       title: na.portal.nadm.version
       dataProperty: version,
       sortable: true,
       visible: true
     }, {
       name: clientId-col,
       title: na.portal.nadm.clientId
       dataProperty: clientId,
       sortable: true,
       visible: true
     }, {
       name: contactIp-col,
       title: na.portal.nadm.contactIp
       dataProperty: contactIp,
       sortable: true,
       visible: true
     }, {
       name: tags-col,
       title: na.portal.nadm.tags
       dataProperty: tags,
       sortable: true,
       customRenderCallName: na.datatableTemplates.nadm.equipmentTags,
       columnWidth: 120px,
       visible: true,
       columnWidth: { type: fixed, value: 125px }
     }, {
       name: state-col,
       title: na.portal.nadm.state
       dataProperty: state,
       sortable: false,
       visible: true
     }, {
       name: lastModificationTime-col,
       title: na.portal.nadm.lastModificationTime
       dataProperty: lastModificationTime,
       sortable: true,
       visible: true
     }, {
       name: firstContactTime-col,
       title: na.portal.nadm.firstContactTime
       dataProperty: firstContactTime,
       sortable: true,
       visible: true
     }, {
       name: lastContactTime-col,
       title: na.portal.nadm.lastContactTime
       dataProperty: lastContactTime,
       sortable: true,
       visible: true
     }, {
       name: actions-col,
       title: na.portal.nadm.actions
       sortable: false,
       customRenderCallName: na.datatableTemplates.nadm.equipmentSearchActions,
       columnWidth: { type: fixed, value: 80px },
       visible: true
     }
 ]
}

clients search {
    route = "na.nadm.controllers.routes.Search.searchClient"
    columns = [
        {
            name: clientId-col,
            title: na.portal.nadm.client
            dataProperty: clientId,
            sortable: true,
            initialSortDirection: descendant,
            columnWidth: { type: fixed, value: 180px },
            visible: true
        }, {
            name: equipments-col,
            title: na.portal.nadm.equipments
            dataProperty: equipments,
            sortable: false,
            visible: true
        }, {
            name: provisionedEquipments-col,
            title: na.portal.nadm.provisionedEquipments
            dataProperty: provisionedEquipments,
            sortable: false,
            visible: true
        }, {
            name: nonProvisionedEquipments-col,
            title: na.portal.nadm.nonProvisionedEquipments
            dataProperty: nonProvisionedEquipments,
            sortable: false,
            visible: true
        }, {
            name: services-col,
            title: na.portal.nadm.numServices
            dataProperty: services,
            sortable: false,
            visible: true
        }, {
            name: actions-col,
            title: na.portal.nadm.actions
            sortable: false,
            customRenderCallName: na.datatableTemplates.nadm.clientSearchActions,
            columnWidth: { type: fixed, value: 80px },
            visible: true
        }
    ]
}

client equipments search {
    columns = [
        {
            name: manufacturer-col,
            title: na.portal.nadm.manufacturer
            dataProperty: manufacturer,
            sortable: true,
            initialSortDirection: descendant,
            visible: true
        }, {
            name: model-col,
            title: na.portal.nadm.model
            dataProperty: model,
            sortable: true,
            visible: true
        }, {
            name: version-col,
            title: na.portal.nadm.version
            dataProperty: version,
            sortable: true,
            visible: true
        }, {
            name: equipments-col,
            title: na.portal.nadm.equipments
            dataProperty: equipments,
            sortable: false,
            visible: true
        }, {
            name: actions-col,
            title: na.portal.nadm.actions
            sortable: false,
            customRenderCallName: na.datatableTemplates.nadm.clientEquipmentSearchActions,
            columnWidth: { type: fixed, value: 80px },
            visible: true
        }
    ]
}


services search {
    route = "na.nadm.controllers.routes.Search.searchService"
    columns = [
        {
            name: Service-col,
            title: na.portal.nadm.field.service.label
            dataProperty: serviceName,
            sortable: true,
            initialSortDirection: descendant,
            visible: true,
            columnWidth: { type: fixed, value: 100px }
        }, {
            name: Type-col,
            title: na.portal.nadm.type
            dataProperty: typeI18n,
            sortable: false,
            visible: true
        }, {
            name: ClientId-col,
            title: na.portal.nadm.clientId
            dataProperty: clientId,
            sortable: true,
            visible: true
        }, {
            name: EquipNumber-col,
            title: na.portal.nadm.numEquipments
            dataProperty: equipments,
            sortable: false,
            visible: true
        }, {
            name: actions-col,
            title: na.portal.nadm.actions
            sortable: false,
            customRenderCallName: na.datatableTemplates.nadm.serviceSearchActions,
            columnWidth: { type: fixed, value: 80px },
            visible: true
        }
    ]
}

equipment associations search {
    columns = [
        {
            name: ResourceId-col,
            title: na.portal.nadm.resourceId
            dataProperty: resourceId,
            sortable: true,
            initialSortDirection: ascendant,
            visible: true,
            customRenderCallName: na.datatableTemplates.nadm.expandableRow,
            columnWidth: { type: fixed, value: 350px }
        }, {
            name: ResourceType-col,
            title: na.portal.nadm.type
            dataProperty: type,
            sortable: true,
            visible: true
        }, {
            name: Name-col,
            title: na.portal.nadm.name
            dataProperty: associationName,
            sortable: true,
            visible: true
        }, {
            name: actions-col,
            title: na.portal.nadm.actions
            sortable: false,
            customRenderCallName: na.datatableTemplates.nadm.equipmentAssociationsSearchActions,
            columnWidth: { type: fixed, value: 80px },
            visible: true
        }
    ]
    rowGroup = {
		groupByColumn: "ResourceType-col"
	}
}

equipment association type services search {
    route = "na.nadm.controllers.routes.Search.searchService"
    features = [
        { name = "radio column" }
    ]
    columns = [
        {
            name: Service-col,
            title: na.portal.nadm.field.service.label
            dataProperty: serviceName,
            sortable: true,
            initialSortDirection: descendant,
            visible: true,
            columnWidth: { type: fixed, value: 100px }
        }, {
            name: EquipNumber-col,
            title: na.portal.nadm.numEquipments
            dataProperty: equipments,
            sortable: false,
            visible: true
        }
    ]
}