---
{%- set udversion = project.version.value|cut('-SNAPSHOT') %}
{%- set isSNAPSHOT = project.version.value is string_endingwith('-SNAPSHOT') %}


## Mandatory

#' NA Portal IAM host certificate path
#' $string
#' %mandatory
#na_portal_iam_host_cert_path:


## Defaults

#' NA Portal Frontend Portal docker options (For example: port mapping, volume mounts, etc.).
#' $string
#' %default
#' @ "-p <port_host>:<port_container> -v <path_host>:<path_container>"
na_portal_frontend_nadm_docker_opts: ""

#' NA Portal Frontend Portal docker command and arguments
#' *Note*: Default NA Portal Frontend Portal image entrypoint ignores the value defined here.
#' $string
#' %default
na_portal_frontend_nadm_docker_command: ""

#' NA Portal Frontend docker registry host
#' $string
#' %default
docker_registry_host: "rdocker.ptin.corppt.com"

#' NA Portal Frontend Portal image
#' $string
#' %mandatory
#' @ "{% raw %}{{ docker_registry_host }}{% endraw %}/na-portal{% if isSNAPSHOT %}-tst{% endif %}/na-portal-frontend-portal:{{ udversion }}"
#na_portal_frontend_nadm_docker_image:

#' NA Portal Frontend Nadm exposed port
#' $integer
#' %default
na_portal_frontend_nadm_exposed_port: 19007

#' Directory where logs will be stored in the host machine.
#' $string
#' %default
na_portal_frontend_nadm_logs_dir: "/var/log/alticelabs/na-portal/frontend-nadm"

#' User that mirrors the user in the docker container.
#' If changed, make sure the docker user has permissions to read/write in the na_portal_frontend_logs_dir.
#' $string
#' %default
na_portal_frontend_nadm_host_user: 2016

#' Group that mirrors the group in the docker container.
#' If changed, make sure the docker user has permissions to read/write in the na_portal_frontend_logs_dir.
#' $string
#' %default
na_portal_frontend_nadm_host_group: 2016

#' Whether to use provided IAM certificate file.
#' $boolean
#' %default
use_provided_iam_conn_cert_file: False


## Optionals

#' NA Portal Frontend Portal docker optional variables
#' Variable where the optional and default values can be defined.
#' By default, all.yml template only expect the mandatory variables.
#' To define optional variables or to change the value of the default ones, this variable should be defined.
#' This is a list composed with "param" and "value", where "param" is the name of the variable to define and "value" the intended value.
#' *Note*: If "value" is an object (dictionary or list), it is recommended to define the value in an auxiliary variable and then convert it to a json in the "value" field. The "na_portal_frontend_parameter_name3" parameter in the example shows how it can be done.
#' $list
#' %optional
#' @  - param: "<na_portal_frontend_nadm_parameter_name1>"
#' @    value: <na_portal_frontend_nadm_parameter_value1>
#' @  - param: "<na_portal_frontend_nadm_parameter_name2>"
#' @    value: <na_portal_frontend_nadm_parameter_value2>
#' @  - param: "<na_portal_frontend_nadm_parameter_name3>"
#' @    value: "{% raw %}{{ <na_portal_frontend_nadm_parameter_auxiliary_object_value> | to_json }}{% endraw %}"
#na_portal_frontend_nadm_optional_variables:

#' IAM Source host certificate file location
#' $string
#' %optional
#iam_source_host_cert_file:

#' Path to i18n customization directory
#' There should be a i18n file for each supported language (pt, en, fr), each file should contain variables like the following:
#'  - nossis.portal.main.licenced.to
#'  - nossis.portal.main.licence.conditions
#' $string
#' %optional
#na_portal_frontend_i18n_customization_dir_path:

## Reference to mandatory variables

#' All mandatory variables from "[all|#all]" section should also be defined here in the pdocker context.
#' *Note:* This is not a variable, it's here only to make the reference to the "[all|#all]" section.
#' $string
#' %mandatory
#(only for documentation) Mandatory Variables: