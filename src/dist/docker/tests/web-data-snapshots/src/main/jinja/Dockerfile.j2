{% set udversion = project.version.value|cut('-SNAPSHOT') %}
{% set isSNAPSHOT = project.version.value is string_endingwith('-SNAPSHOT') %}
{% set nacommons = version.pt.ptinovacao.get('na-commons').value|cut('-SNAPSHOT') %}
FROM rdocker.ptin.corppt.com/na-commons{% if version.pt.ptinovacao.get('na-commons').value is string_endingwith '-SNAPSHOT' %}-tst{% endif %}/na-commons-{{ docker.os.value }}-jdk8:{{ version.pt.ptinovacao.get('na-commons').value|cut('-SNAPSHOT') }}

LABEL \
com.alticelabs.name="{{ docker.image.name.value }}" \
com.alticelabs.version="{{ udversion }}" \
com.alticelabs.group="{{ docker.group.value }}" \
com.alticelabs.summary="{{ docker.summary.value }}" \
com.alticelabs.license="{{ docker.license.value }}" \
com.alticelabs.packager="{{ docker.packager.value }}" \
com.alticelabs.os="{{ docker.os.value }}" \
com.alticelabs.release={% if isSNAPSHOT %}"r{{ buildNumber.value }}-{{ docker.timestamp.value }}"{% else %}"{{ docker.timestamp.value }}"{% endif %}

COPY files/entrypoint.sh /entrypoint.sh
COPY config/ /config/
COPY files/na-portal/conf/. /opt/alticelabs/na-portal/conf/
COPY files/na-portal/lib/. /opt/alticelabs/na-portal/lib/

USER root

RUN chmod +x /entrypoint.sh

ENV TEMPLATE_DIRS=""
ENV DEFAULTS_INV=""

ENTRYPOINT ["/entrypoint.sh"]