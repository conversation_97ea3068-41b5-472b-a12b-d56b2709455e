<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>na-portal-dist-docker-tests</artifactId>
        <groupId>pt.ptinovacao.na-portal</groupId>
        <version>10.55.0</version>
    </parent>

    <artifactId>na-portal-dist-docker-tests-create-entities-operations-versions</artifactId>
    <packaging>pom</packaging>

    <dependencies>
        <dependency>
            <groupId>pt.ptinovacao.asf</groupId>
            <artifactId>asf-serializers</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-portal.it</groupId>
            <artifactId>na-portal-integration-tests-create-entities-operations-versions</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>docker</id>
            <properties>
                <docker.context.directory>${project.basedir}/target/jinja</docker.context.directory>
                <docker.image.name>na-portal-it-create-entities-operations-versions</docker.image.name>
                <docker.file>Dockerfile</docker.file>
                <docker.summary>NA Portal Integration Tests Create Entities and Operations Tests</docker.summary>
                <docker.group>ptin/prod</docker.group>
                <docker.os>centos7</docker.os>
            </properties>
            <build>
                <plugins>
                    <!-- check for required properties -->
                    <plugin>
                        <artifactId>maven-enforcer-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>check-required-docker-properties</id>
                                <phase>prepare-package</phase>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- build and push image -->
                    <plugin>
                        <groupId>com.spotify</groupId>
                        <artifactId>dockerfile-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>default</id>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-dependency-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>get-dependencies</id>
                                <phase>prepare-package</phase>
                                <goals>
                                    <goal>copy-dependencies</goal>
                                </goals>
                                <configuration>
                                    <outputDirectory>${project.build.directory}/jinja/lib</outputDirectory>
                                    <includeScope>runtime</includeScope>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <!-->
                    Jinja maven plugin
                    Generate Dockerfiles
                    <!-->
                    <plugin>
                        <groupId>pt.ptinovacao.na-commons</groupId>
                        <artifactId>na-commons-maven-plugin-jinja</artifactId>
                        <version>${version.pt.ptinovacao.na-commons}</version>
                        <executions>
                            <execution>
                                <phase>prepare-package</phase>
                                <goals>
                                    <goal>jinja</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <inventory>/path/to/inventory/dot/yml</inventory>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>