{% set udversion = project.version.value|cut('-SNAPSHOT') %}
{% set isSNAPSHOT = project.version.value is string_endingwith('-SNAPSHOT') %}
{% set na = version.pt.ptinovacao.get('na').value|cut('-SNAPSHOT') %}
FROM rdocker.ptin.corppt.com/na{% if version.pt.ptinovacao.get('na').value is string_endingwith '-SNAPSHOT' %}-tst{% endif %}/na-operations-catalog-versions-manager:{{ na }}

LABEL \
com.alticelabs.name="{{ docker.image.name.value }}" \
com.alticelabs.version="{{ udversion }}" \
com.alticelabs.group="{{ docker.group.value }}" \
com.alticelabs.summary="{{ docker.summary.value }}" \
com.alticelabs.license="{{ docker.license.value }}" \
com.alticelabs.packager="{{ docker.packager.value }}" \
com.alticelabs.os="{{ docker.os.value }}" \
com.alticelabs.release={% if isSNAPSHOT %}"r{{ buildNumber.value }}-{{ docker.timestamp.value }}"{% else %}"{{ docker.timestamp.value }}"{% endif %}

COPY --chown=2016:2016 config /config
COPY --chown=2016:2016 lib /opt/alticelabs/na-operations-catalog-versions-manager/lib/lib.d

ENV DEFAULTS_INV="$DEFAULTS_INV /config/defaults-na-create-entities-operations-versions.yml"