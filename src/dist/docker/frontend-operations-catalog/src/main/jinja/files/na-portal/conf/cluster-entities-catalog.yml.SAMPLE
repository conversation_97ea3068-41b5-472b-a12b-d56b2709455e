%YAML 1.2
---
zookeeperRoot: nossis-tsc-root

externalClassesOrComponents:

  - name: selectorFactory
    className: pt.ptinovacao.asf.serviceregistry.api.serviceselectors.RandomMinTimeSelectorFactory

zookeeperClusters:

  - name: nossis-tsc-clusters
    connectString: {{ entities_catalog_zkClient_zookeeperNodes }}

    applicationClusters:

      - name: nossis-tsc-backend-cluster
        node:
          ipOrHost:

    serviceGroups:
      - name: nossis-tsc-rest-backend-service
        services:
          - name: nossis-tsc-rest
            protocol: NONE

        instanceSelectorsFactories:
          - ref: selectorFactory
            pattern: .*
            parameters:
              useMs: true
              statsTimeIntervalMs: 10000
              historyWeights:
                - 0.6
                - 0.3
                - 0.1
              goToQuarantineAfterErrors: 3
              sleepTimeQuarantineMs:
                - 1000
                - 4000
                - 16000
                - 120000