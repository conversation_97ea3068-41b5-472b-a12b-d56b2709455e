%YAML 1.2
---
zookeeperRoot: na-operations-catalog-root

externalClassesOrComponents:

  - name: selectorFactory
    className: pt.ptinovacao.asf.serviceregistry.api.serviceselectors.RandomMinTimeSelectorFactory

zookeeperClusters:

  - name: na-operations-catalog-clusters
    connectString: {{ operations_catalog_zkClient_zookeeperNodes }}


    applicationClusters:

      - name: na-operations-catalog-backend-cluster
        node:
          ipOrHost:

    serviceGroups:
      - name: na-operations-catalog-rest-backend-service
        services:
          - name: na-operations-catalog-rest
            protocol: NONE

        instanceSelectorsFactories:
          - ref: selectorFactory
            pattern: .*
            parameters:
              useMs: true
              statsTimeIntervalMs: 10000
              historyWeights:
                - 0.6
                - 0.3
                - 0.1
              goToQuarantineAfterErrors: 3
              sleepTimeQuarantineMs:
                - 1000
                - 4000
                - 16000
                - 120000
                - 240000