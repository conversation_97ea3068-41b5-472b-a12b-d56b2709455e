// -------------------
// --- datasources ---
// -------------------

idCatEntity = {
  implementation = na.catalog.basemodule.ui.dynamic.implementations.ServerDataSelect2
  parameters = {
    allowClear = true
    placeholder = na.portal.reference.data.idCatEntity.placeholder
    route = na.reference.data.controllers.routes.SearchController.getAuthorizedCatalogs
  }
}

// ----------------------
// --- entity lateral ---
// ----------------------

na_catalog.entityLateral.search.idCatEntity = ${idCatEntity}

// --------------
// --- entity ---
// --------------

na_catalog.entity.create.idCatEntity = ${idCatEntity}

na_catalog.entity.view.idCatEntity = ${idCatEntity}

na_catalog.entity.edit.idCatEntity = ${idCatEntity}

na_catalog.entity.search.idCatEntity = ${idCatEntity}

// ----------------------
// --- instance model ---
// ----------------------

catalogInstance = {
  entity.search.idCatEntity = ${idCatEntity}
  entityLateral.search.idCatEntity = ${idCatEntity}
}

// ---------------------------------------------------------------------------------------------------------------------
// --- CATALOG INSTANCES: add new catalog instances here
// ---------------------------------------------------------------------------------------------------------------------

# e.g: na_catalog.olt_list = ${catalogInstance}