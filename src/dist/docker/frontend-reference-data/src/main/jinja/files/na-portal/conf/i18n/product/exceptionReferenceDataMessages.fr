
exception.00001.developer.message      = Execption éventuellement efectué par BadGateway,GatewayTimeout où Timeout
exception.00001.title                  = Service REST API temporairement indisponible
exception.00001.user.message           = Service temporairement indisponible
exception.00002.developer.message      = Impossible de terminer l''opération
exception.00002.title                  = Erreur non définie
exception.00002.user.message           = Impossible de terminer l''opération
exception.00003.developer.message      = Erreur de configurations
exception.00003.title                  = Erreur de configurations
exception.00003.user.message           = Erreur de configurations
exception.00007.title                  = Erreur dans la demande
exception.404.title                    = Entité non trouvée
exception.EXCEPTIONLOCATION00002.title = Condition préalable non respectée
exception.FMWKCOMMON00011.title        = Condition préalable non respectée
exception.FMWKCOMMON00013.title        = Condition préalable non respectée
exception.FMWKCOMMON00016.title        = Condition préalable non respectée
exception.FMWKCOMMON00030.title        = Condition préalable non respectée