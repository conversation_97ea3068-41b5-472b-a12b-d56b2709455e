<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>na-portal-dist</artifactId>
        <groupId>pt.ptinovacao.na-portal</groupId>
        <version>10.55.0</version>
    </parent>

    <artifactId>na-portal-dist-docker</artifactId>
    <packaging>pom</packaging>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencyManagement>
        <!-- ######################################################################################### -->
        <!-- # Dependency declared here to be explicitly used as a dependency in docker distribution # -->
        <!-- # modules, so that maven builds dependant docker images in sequence.                    # -->
        <!-- ######################################################################################### -->
        <dependencies>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-dist-docker-frontend</artifactId>
                <version>${project.version}</version>
                <scope>provided</scope>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-dist-docker-frontend-base</artifactId>
                <version>${project.version}</version>
                <scope>provided</scope>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-dist-docker-frontend-nadm</artifactId>
                <version>${project.version}</version>
                <scope>provided</scope>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal</groupId>
                <artifactId>na-portal-dist-docker-pgsql-db</artifactId>
                <version>${project.version}</version>
                <scope>provided</scope>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
                <type>pom</type>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>pt.ptinovacao.na-commons</groupId>
                    <artifactId>na-commons-maven-plugin-jinja</artifactId>
                    <version>${version.pt.ptinovacao.na-commons}</version>
                    <extensions>true</extensions>
                    <executions>
                        <execution>
                            <id>jinja</id>
                            <phase>prepare-package</phase>
                            <goals>
                                <goal>jinja</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>pt.ptinovacao.na-commons</groupId>
                    <artifactId>na-commons-maven-plugin-yamldoc</artifactId>
                    <version>${version.pt.ptinovacao.na-commons}</version>
                    <executions>
                        <execution>
                            <id>yamldoc</id>
                            <goals>
                                <goal>yamldoc</goal>
                            </goals>
                            <phase>deploy</phase>
                        </execution>
                    </executions>
                    <configuration>
                        <directory>${basedir}/target/jinja/config</directory>
                        <exporterConf>
                            <type>wiki</type>
                            <wiki>
                                <baseUrl>https://alabs.atlassian.net/wiki</baseUrl>
                                <buildnumber>r${buildNumber}</buildnumber>
                                <title>${docker.image.name} ${ud.version}</title>
                                <space>${ud.key}</space>
                                <parentPageTitle>${ud.key} services ${ud.version}</parentPageTitle>
                                <username>${maven.yamldoc.plugin.username}</username>
                                <password>${maven.yamldoc.plugin.password}</password>
                            </wiki>
                        </exporterConf>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <modules>
        <module>core</module>
        <module>data-management-sidecar</module>
        <module>frontend-base</module>
        <module>frontend-portal</module>
        <module>frontend-go</module>
        <module>frontend-monitoring</module>
        <module>frontend-mpt</module>
        <module>frontend-nadm</module>
        <module>frontend-operations-catalog</module>
        <module>frontend-reference-data</module>
        <module>frontend-diagnostics</module>
        <module>frontend-veacs</module>
        <module>pgsql-db</module>
        <module>pgsql11-db</module>
        <module>pgsql12-db</module>
        <module>iam-configurator</module>
        <module>iam-configurator-api</module>
        <module>jboss-mpt-go</module>
        <module>mpt-client</module>
        <module>mpt-client-sidecar</module>
        <module>naem-dbmanager</module>
        <module>naem-dbmanager-mongo-client-sidecar</module>
        <module>tests</module>
        <module>web-balancer</module>
    </modules>

    <profiles>
        <profile>
            <id>github-dev</id>
            <properties>
                <github.docker.tag>${docker.tag}</github.docker.tag>
            </properties>
        </profile>
        <profile>
            <id>github-release</id>
            <properties>
                <github.docker.tag>${ud.version}</github.docker.tag>
            </properties>
        </profile>
        <profile>
            <id>github</id>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>com.spotify</groupId>
                            <artifactId>dockerfile-maven-plugin</artifactId>
                            <executions>
                                <execution>
                                    <id>deploy-github</id>
                                    <phase>deploy</phase>
                                    <goals>
                                        <goal>tag</goal>
                                        <goal>push</goal>
                                    </goals>
                                    <configuration>
                                        <repository>ghcr.io/alticelabsprojects/nossis-orchestration/${docker.image.name}</repository>
                                        <tag>${github.docker.tag}</tag>
                                    </configuration>
                                </execution>
                            </executions>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
    </profiles>

</project>
