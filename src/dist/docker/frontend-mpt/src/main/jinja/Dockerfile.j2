{% set udversion = project.version.value|cut('-SNAPSHOT') -%}
{% set isSNAPSHOT = project.version.value is string_endingwith('-SNAPSHOT') -%}
FROM rdocker.ptin.corppt.com/na-portal{% if project.version.value is string_endingwith('-SNAPSHOT') %}-tst{% endif %}/na-portal-frontend-base:{{ udversion }}

LABEL com.alticelabs.name="{{ docker.image.name.value }}"
LABEL com.alticelabs.version="{{ udversion }}"
LABEL com.alticelabs.group="{{ docker.group.value }}"
LABEL com.alticelabs.summary="{{ docker.summary.value }}"
LABEL com.alticelabs.license="{{ docker.license.value }}"
LABEL com.alticelabs.packager="{{ docker.packager.value }}"
LABEL com.alticelabs.os="{{ docker.os.value }}"
LABEL com.alticelabs.release={% if isSNAPSHOT %}"r{{ buildNumber.value }}-{{ docker.timestamp.value }}"{% else %}"{{ docker.timestamp.value }}"{% endif %}

USER root

COPY --chown=2016:2016 config/all.yml /config/all.yml
COPY --chown=2016:2016 config/defaults-na-portal-frontend-mpt.yml /config/defaults-na-portal-frontend-mpt.yml
COPY --chown=2016:2016 files/na-portal/. /opt/alticelabs/na-portal/

RUN (groupadd -f alabs || true) && groupmod -o -g 2016 alabs && \
    (useradd -f -1 -g alabs alabs || true) && usermod -o -u 2016 alabs

ENV DEFAULTS_INV="$DEFAULTS_INV /config/defaults-na-portal-frontend-mpt.yml"

ENV NOSSIS_UI_PORT=19006

EXPOSE $NOSSIS_UI_PORT

USER 2016:2016
