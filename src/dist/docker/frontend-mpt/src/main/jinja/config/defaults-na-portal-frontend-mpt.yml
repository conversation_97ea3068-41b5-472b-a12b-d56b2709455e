---

#' Mpt additional java options
#' $string
#' %default
na_portal_mpt_java_opts: -Xmx256M -Xms256M -Xss256K

#' Portal additional java options
#' $string
#' %default
nossis_ui_java_opts: "{{ na_portal_mpt_java_opts }}"

#' Dictionary used to configure log4j2. There's three possible keys for the appenders (all three can be defined at the same time):
#' * _graylog_
#' ** *appender_name*: appender name (mandatory)
#' ** *application_name*: name of the application that is being logged, important for graylog (mandatory, format type NOSSIS_<FULFILLMENT or DM>)
#' ** *service_name*: name of the service that is being logged, important for graylog (mandatory, format type <UD>_<MODULE>)
#' ** *layout*: Logs layout definition (optional)
#' ** *compressionThreshold*: Occurs compression when the data is bigger than the number of defined bytes (optional).
#' ** *compressionType*: Type of compression to use (optional, defaults to 'OFF' if graylog_protocol is 'tcp')
#' ** *use_host_in_prefix*: <PERSON><PERSON><PERSON> that specifies if it's intended to add the host name to the docker hostname (optional)
#' ** *thresholdFilter*: Inclusion only of the logs with level equal or higher than the defined (optional)
#' * _rollingFile_
#' ** *file*: path to the log file (mandatory)
#' ** *maxBackupIndex*: maximum number of backup files (not necessary if timeInterval is defined)
#' ** *maxFileSize*: Maximum size of log files (not necessary if timeInterval is defined)
#' ** *timeInterval*: The frequency in which a rollover must occur according to the most specific time unit in the used date pattern (not necessary if maxBackupIndex is defined)
#' ** *timeModulate*: Specifies if the interval should be adjusted to make the next rollover occur in the interval limit (optional)
#' ** *filePermissions*: permission level for files (optional)
#' ** *fileOwner*: owner that the file will be created (optional)
#' ** *fileGroup*: group that the file will be created (optional)
#' ** *layout*: Definition of logs layout (optional)
#' ** *thresholdFilter*: Inclusion only of the logs with level equal or superior to the defined (optional)
#' * _console_
#' ** *name*: name of the application that is being logged (mandatory)
#' ** *compressionType*: Type of compression to use (optional, default value is 'OFF')
#' ** *compressionThreshold*: Occurs compression when the data is bigger than the number of defined bytes (optional)
#' ** *layout*: Specification of the logs layout (optional)
#' ** *thresholdFilter*: Inclusion only of the logs with level equal or superior to the defined (optional)
#' Other than this, there's also the need to define key loggers, which is a list of dictionaries and contain the information related with
#' the loggers (name, level, refs and additivity), and the key root, which is a dictionary and contains the keys levels and refs.
#' *Note*: The appender _rollingFile_ is a dictionary and can contain more than one entry (the other 2 appenders can't)
#' $dictionary
#' %default
log4j2:
  graylog:
    appender_name: "GRAYLOG_APPENDER"
    application_name: "NOSSIS_{{ product_type }}"
    service_name: "NA-PORTAL_FRONTEND_MPT"
  loggers:
    - name: "play"
      level: "info"
      refs: ["GRAYLOG_APPENDER"]
      additivity: "false"
    - name: "application"
      level: "info"
      refs: [ "GRAYLOG_APPENDER"]
      additivity: "false"
    - name: "base"
      level: "info"
      refs: ["GRAYLOG_APPENDER"]
      additivity: "false"
    - name: "mpt"
      level: "info"
      refs: ["GRAYLOG_APPENDER"]
      additivity: "false"
  root:
    level: "info"
    refs: [ "GRAYLOG_APPENDER" ]

