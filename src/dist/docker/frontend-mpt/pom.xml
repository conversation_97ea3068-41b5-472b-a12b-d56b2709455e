<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>pt.ptinovacao.na-portal</groupId>
        <artifactId>na-portal-dist-docker</artifactId>
        <version>10.55.0</version>
    </parent>

    <artifactId>na-portal-dist-docker-frontend-mpt</artifactId>

    <properties>
        <docker.context.directory>${project.basedir}/target/jinja</docker.context.directory>
        <docker.svn.name>${application.name}</docker.svn.name>
        <docker.image.name>na-portal-frontend-mpt</docker.image.name>
        <docker.file>Dockerfile</docker.file>
        <docker.summary>NA Portal Frontend Mpt image</docker.summary>
        <docker.packager>${ud.key}</docker.packager>
        <docker.group>ptin/prod</docker.group>
        <docker.os>centos7</docker.os>
        <dockerfile.build.cacheFrom>rdocker.ptin.corppt.com/na-portal/na-portal-frontend-base:${ud.version},rdocker.ptin.corppt.com/na-portal-tst/na-portal-frontend-base:${ud.version}</dockerfile.build.cacheFrom>
        <dockerfile.build.pullNewerImage>false</dockerfile.build.pullNewerImage>
    </properties>

    <dependencies>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-dist-docker-frontend-base</artifactId>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-frontend-mpt</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-frontend-mpt-assets</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-frontend-catalog-basemodule-assets</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.typesafe.play</groupId>
            <artifactId>play_${version.org.scala.base}</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.typesafe.play</groupId>
            <artifactId>play-netty-server_${version.org.scala.base}</artifactId>
            <scope>runtime</scope>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>docker</id>
            <build>
                <plugins>
                    <!-- check for required properties -->
                    <plugin>
                        <artifactId>maven-enforcer-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>check-required-docker-properties</id>
                                <phase>prepare-package</phase>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- copy external dependencies -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-dependency-plugin</artifactId>
                        <version>${version.maven-dependency-plugin}</version>
                        <executions>
                            <execution>
                                <id>copy-internal-dependencies</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>copy-dependencies</goal>
                                </goals>
                                <configuration>
                                    <outputDirectory>${project.build.directory}/jinja/files/na-portal/lib</outputDirectory>
                                    <includeScope>runtime</includeScope>
                                    <stripVersion>true</stripVersion>
                                    <includeGroupIds>${project.groupId}</includeGroupIds>
                                </configuration>
                            </execution>
                            <execution>
                                <id>copy-external-dependencies</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>copy-dependencies</goal>
                                </goals>
                                <configuration>
                                    <outputDirectory>${project.build.directory}/jinja/files/na-portal/lib</outputDirectory>
                                    <includeScope>runtime</includeScope>
                                    <stripVersion>false</stripVersion>
                                    <excludeGroupIds>${project.groupId}</excludeGroupIds>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <!-->
                    Jinja maven plugin
                    Generate Dockerfiles
                    <!-->
                    <plugin>
                        <groupId>pt.ptinovacao.na-commons</groupId>
                        <artifactId>na-commons-maven-plugin-jinja</artifactId>
                        <version>${version.pt.ptinovacao.na-commons}</version>
                        <executions>
                            <execution>
                                <phase>prepare-package</phase>
                                <goals>
                                    <goal>jinja</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- build and push image -->
                    <plugin>
                        <groupId>com.spotify</groupId>
                        <artifactId>dockerfile-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>default</id>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>documentation</id>
            <build>
                <plugins>
                    <!-->
                    Jinja maven plugin
                    Generate Dockerfiles
                    <!-->
                    <plugin>
                        <groupId>pt.ptinovacao.na-commons</groupId>
                        <artifactId>na-commons-maven-plugin-jinja</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>buildnumber-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>buildnumber-revision</id>
                                <phase>generate-resources</phase>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>pt.ptinovacao.na-commons</groupId>
                        <artifactId>na-commons-maven-plugin-yamldoc</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>