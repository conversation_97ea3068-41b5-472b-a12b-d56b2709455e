<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>na-portal-dist-docker</artifactId>
        <groupId>pt.ptinovacao.na-portal</groupId>
        <version>10.55.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>na-portal-dist-docker-naem-dbmanager-mongo-client-sidecar</artifactId>
    <packaging>pom</packaging>

    <properties>
        <docker.context.directory>${project.basedir}/target/jinja</docker.context.directory>
        <docker.image.name>na-portal-naem-dbmanager-mongo-client-sidecar</docker.image.name>
        <docker.file>Dockerfile</docker.file>
        <docker.summary>NA Portal Naem Dbmanager Mongo Client Sidecar image</docker.summary>
        <docker.group>ptin/prod</docker.group>
        <docker.os>centos7</docker.os>
    </properties>

    <profiles>
        <profile>
            <id>docker</id>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-enforcer-plugin</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>com.spotify</groupId>
                        <artifactId>dockerfile-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>default</id>
                            </execution>
                        </executions>
                    </plugin>

                    <!-->
                    Jinja maven plugin
                    Generate Dockerfiles
                    <!-->
                    <plugin>
                        <groupId>pt.ptinovacao.na-commons</groupId>
                        <artifactId>na-commons-maven-plugin-jinja</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>documentation</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>buildnumber-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>buildnumber-revision</id>
                                <phase>generate-resources</phase>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>pt.ptinovacao.na-commons</groupId>
                        <artifactId>na-commons-maven-plugin-yamldoc</artifactId>
                        <configuration>
                            <directory>${basedir}/src/main/jinja/files/config</directory>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
