{% set udversion = project.version.value|cut('-SNAPSHOT') %}
{% set isSNAPSHOT = project.version.value is string_endingwith('-SNAPSHOT') %}
{% set nacommons = version.pt.ptinovacao.get('na-commons').value|cut('-SNAPSHOT') %}
FROM rdocker.ptin.corppt.com/na-commons{% if version.pt.ptinovacao.get('na-commons').value is string_endingwith('-SNAPSHOT') %}-tst{% endif %}/na-commons-mongo-client-sidecar:{{ nacommons }}

LABEL com.alticelabs.name="{{ docker.image.name.value }}"
LABEL com.alticelabs.version="{{ udversion }}"
LABEL com.alticelabs.group="{{ docker.group.value }}"
LABEL com.alticelabs.summary="{{ docker.summary.value }}"
LABEL com.alticelabs.license="{{ docker.license.value }}"
LABEL com.alticelabs.packager="{{ docker.packager.value }}"
LABEL com.alticelabs.os="{{ docker.os.value }}"
LABEL com.alticelabs.release={% if isSNAPSHOT %}"r{{ buildNumber.value }}-{{ docker.timestamp.value }}"{% else %}"{{ docker.timestamp.value }}"{% endif %}

ENV WORKFLOW_CONFIGS="add"

COPY --chown=2016:2016 files/na-portal/. /docker-entrypoint-scripts.d/

USER 2016:2016
