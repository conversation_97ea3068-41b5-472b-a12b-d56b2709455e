RoleName,ResourceName,ResourcePermIAM,ResourcePermService
IAM :: NA User Manager,IAM :: Active Sessions,,RD
IAM :: NA User Manager,IAM :: Accounts,X,CRUD
IAM :: NA User Manager,IAM :: Child Contexts,X,CRUD
IAM :: NA User Manager,IAM :: Context Group Members,X,CRUD
IAM :: NA User Manager,IAM :: Context to User Group Role,X,CRU
IAM :: NA User Manager,IAM :: Context to User Role,X,CRU
IAM :: NA User Manager,IAM :: User Group Members,X,CRUD
IAM :: NA User Manager,IAM :: User Group Roles,X,CRUD
IAM :: NA User Manager,IAM :: User Roles,X,CRUD
IAM :: NA User Manager,IAM :: Context Groups,X,RUD
IAM :: NA User Manager,IAM :: Contexts,X,RUD
IAM :: NA User Manager,IAM :: Stores,X,CRUD
IAM :: NA User Manager,IAM :: Store Entity Attributes,X,CRUD
IAM :: NA User Manager,IAM :: Store Entity Properties,X,CRUD
IAM :: NA User Manager,IAM :: User Groups,X,RUD
IAM :: NA User Manager,IAM :: Users,X,RUD
IAM :: NA User Manager,IAM :: Account Attributes,X,RUD
IAM :: NA User Manager,IAM :: Password and Username Policies,,R
IAM :: NA User Manager,IAM :: Reset Password,X,
IAM :: NA User Manager,IAM :: Tags,,R
