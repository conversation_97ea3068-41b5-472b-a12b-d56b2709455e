ServiceName,ServiceDescription,ServiceEnabled,ServiceUrl,ServiceLogoutBehavior,ServiceIssuer,ServiceSamlAssertionConsumerServiceUrl,ServiceSamlLogoutUrl,ServiceSamlEncryptedResponsesAssertions,ServiceSamlSignedRequests,ServiceSamlSignedResponses,ServiceSamlSignedResponsesAssertions,ServicePkiPublicCertificate,ClaimName,ClaimDefault,ClaimRequired,StoreName,AttributeId,AttributeName,AuthnProcedureName,AuthnProcedureDefault,ServiceCredentialClientId,ServiceCredentialClientSecret,ServiceCredentialSignature,RoleName
NA Portal,NA Portal service,true,{{ na_portal_iam_login_url | replace("loginPost", "login") }},slo,na-portal,{{ na_portal_iam_login_url }},{{ na_portal_iam_logout_url }},false,true,true,true,{{ na_portal_public_key }},username,true,true,,1,username,Local,true,{{ na_portal_iam_oauth_client }},{{ na_portal_iam_oauth_secret }},{{ na_portal_iam_credential_signature }},Service :: NA
NA Portal,NA Portal service,true,{{ na_portal_iam_login_url | replace("loginPost", "login") }},slo,na-portal,{{ na_portal_iam_login_url }},{{ na_portal_iam_logout_url }},false,true,true,true,{{ na_portal_public_key }},email,true,true,,2,email,,,{{ na_portal_iam_oauth_client }},{{ na_portal_iam_oauth_secret }},{{ na_portal_iam_credential_signature }},Service :: NA
NA Portal,NA Portal service,true,{{ na_portal_iam_login_url | replace("loginPost", "login") }},slo,na-portal,{{ na_portal_iam_login_url }},{{ na_portal_iam_logout_url }},false,true,true,true,{{ na_portal_public_key }},token,true,true,,3,accessToken,,,{{ na_portal_iam_oauth_client }},{{ na_portal_iam_oauth_secret }},{{ na_portal_iam_credential_signature }},Service :: NA
NA Portal,NA Portal service,true,{{ na_portal_iam_login_url | replace("loginPost", "login") }},slo,na-portal,{{ na_portal_iam_login_url }},{{ na_portal_iam_logout_url }},false,true,true,true,{{ na_portal_public_key }},language,true,false,,4,userLanguage,,,{{ na_portal_iam_oauth_client }},{{ na_portal_iam_oauth_secret }},{{ na_portal_iam_credential_signature }},Service :: NA
NA Portal,NA Portal service,true,{{ na_portal_iam_login_url | replace("loginPost", "login") }},slo,na-portal,{{ na_portal_iam_login_url }},{{ na_portal_iam_logout_url }},false,true,true,true,{{ na_portal_public_key }},name,true,true,,5,name,,,{{ na_portal_iam_oauth_client }},{{ na_portal_iam_oauth_secret }},{{ na_portal_iam_credential_signature }},Service :: NA