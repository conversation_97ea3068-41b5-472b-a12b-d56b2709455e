<PERSON><PERSON><PERSON>,Context<PERSON><PERSON>,ContextPermIAM
NA :: Admin,NA :: CATALOG :: catalog,R
NA :: Admin,NA :: CATALOG :: catalog :: NA_CATALOG.EQ,R
NA :: Admin,NA :: CATALOG :: catalog :: NA_CATALOG.M5,R
NA :: Admin,NA :: CATALOG :: catalog :: NA_CATALOG.M6,R
NA :: Admin,NA :: CATALOG :: catalog :: NA_CATALOG.OLT_LIST,R
NA :: Admin,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTS,R
NA :: Admin,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSCISCO,R
NA :: Admin,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSGRANDSTREAM,R
NA :: Admin,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSLINKSYS,R
NA :: Admin,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSPOLYCOM,R
NA :: Admin,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSSNOM,R
NA :: Admin,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSYEALINK,R
NA :: Admin,NA :: CATALOG :: catalog :: NA_CATALOG.PHONES_LIST,R
NA :: Admin,NA :: GO :: genericOperationsFamilies,R
NA :: Admin,NA :: GO :: genericOperationsFamilies :: ABC-ADO,R
NA :: Admin,NA :: GO :: genericOperationsFamilies :: GPON,R
NA :: Admin,NA :: GO :: genericOperationsFamilies :: HMNT,R
NA :: Admin,NA :: GO :: genericOperationsFamilies :: TEST,R
NA :: Admin,NA :: GO :: genericOperationsFamilies :: VEACS,R
NA :: Admin,NA :: GO :: genericOperationsRfs,R
NA :: Admin,NA :: GO :: genericOperationsRfs :: rfsDeviceManagement,R
NA :: Admin,NA :: GO :: genericOperationsRfs :: rfsGponDiagnostics,R
NA :: Admin,NA :: GO :: genericOperationsRfs :: rfsProviderNetworkAccess,R
NA :: Admin,NA :: GO :: genericOperationsRfs :: rfsTest,R
NA :: Admin,NA :: GO :: genericOperationsRfs :: rfsVoiceAbc,R
NA :: Admin,NA :: MPT :: mptFamilies,R
NA :: Admin,NA :: MPT :: mptFamilies :: bbrasAudit,R
NA :: Admin,NA :: MPT :: mptFamilies :: check,R
NA :: Admin,NA :: MPT :: mptFamilies :: cqgpon,R
NA :: Admin,NA :: MPT :: mptFamilies :: dslam,R
NA :: Admin,NA :: MPT :: mptFamilies :: dtn,R
NA :: Admin,NA :: MPT :: mptFamilies :: eventBroker,R
NA :: Admin,NA :: MPT :: mptFamilies :: gpon,R
NA :: Admin,NA :: MPT :: mptFamilies :: gponAudit,R
NA :: Admin,NA :: MPT :: mptFamilies :: gponEmp,R
NA :: Admin,NA :: MPT :: mptFamilies :: hnm,R
NA :: Admin,NA :: MPT :: mptFamilies :: homeNetwork,R
NA :: Admin,NA :: MPT :: mptFamilies :: homenetworkAudit,R
NA :: Admin,NA :: MPT :: mptFamilies :: imsSec,R
NA :: Admin,NA :: MPT :: mptFamilies :: imsSipTrunking,R
NA :: Admin,NA :: MPT :: mptFamilies :: imsVoIPClass5,R
NA :: Admin,NA :: MPT :: mptFamilies :: iptvAudit,R
NA :: Admin,NA :: MPT :: mptFamilies :: iptvGestaoAccount,R
NA :: Admin,NA :: MPT :: mptFamilies :: iptvGestaoSTB,R
NA :: Admin,NA :: MPT :: mptFamilies :: parque,R
NA :: Admin,NA :: MPT :: mptFamilies :: pstn,R
NA :: Admin,NA :: MPT :: mptFamilies :: radiusRede,R
NA :: Admin,NA :: MPT :: mptFamilies :: rollingUpgrade,R
NA :: Admin,NA :: MPT :: mptFamilies :: smart2m,R
NA :: Admin,NA :: MPT :: mptFamilies :: SUM,R
NA :: Admin,NA :: MPT :: mptFamilies :: VEACS,R
NA :: CATALOG :: Admin,NA :: CATALOG :: catalog,R
NA :: CATALOG :: Admin,NA :: CATALOG :: catalog :: NA_CATALOG.EQ,R
NA :: CATALOG :: Admin,NA :: CATALOG :: catalog :: NA_CATALOG.M5,R
NA :: CATALOG :: Admin,NA :: CATALOG :: catalog :: NA_CATALOG.M6,R
NA :: CATALOG :: Admin,NA :: CATALOG :: catalog :: NA_CATALOG.OLT_LIST,R
NA :: CATALOG :: Admin,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTS,R
NA :: CATALOG :: Admin,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSCISCO,R
NA :: CATALOG :: Admin,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSGRANDSTREAM,R
NA :: CATALOG :: Admin,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSLINKSYS,R
NA :: CATALOG :: Admin,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSPOLYCOM,R
NA :: CATALOG :: Admin,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSSNOM,R
NA :: CATALOG :: Admin,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSYEALINK,R
NA :: CATALOG :: Admin,NA :: CATALOG :: catalog :: NA_CATALOG.PHONES_LIST,R
NA :: CATALOG :: Consult,NA :: CATALOG :: catalog,R
NA :: CATALOG :: Consult,NA :: CATALOG :: catalog :: NA_CATALOG.EQ,R
NA :: CATALOG :: Consult,NA :: CATALOG :: catalog :: NA_CATALOG.M5,R
NA :: CATALOG :: Consult,NA :: CATALOG :: catalog :: NA_CATALOG.M6,R
NA :: CATALOG :: Consult,NA :: CATALOG :: catalog :: NA_CATALOG.OLT_LIST,R
NA :: CATALOG :: Consult,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTS,R
NA :: CATALOG :: Consult,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSCISCO,R
NA :: CATALOG :: Consult,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSGRANDSTREAM,R
NA :: CATALOG :: Consult,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSLINKSYS,R
NA :: CATALOG :: Consult,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSPOLYCOM,R
NA :: CATALOG :: Consult,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSSNOM,R
NA :: CATALOG :: Consult,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSYEALINK,R
NA :: CATALOG :: Consult,NA :: CATALOG :: catalog :: NA_CATALOG.PHONES_LIST,R
NA :: CATALOG :: Oper,NA :: CATALOG :: catalog,R
NA :: CATALOG :: Oper,NA :: CATALOG :: catalog :: NA_CATALOG.EQ,R
NA :: CATALOG :: Oper,NA :: CATALOG :: catalog :: NA_CATALOG.M5,R
NA :: CATALOG :: Oper,NA :: CATALOG :: catalog :: NA_CATALOG.M6,R
NA :: CATALOG :: Oper,NA :: CATALOG :: catalog :: NA_CATALOG.OLT_LIST,R
NA :: CATALOG :: Oper,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTS,R
NA :: CATALOG :: Oper,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSCISCO,R
NA :: CATALOG :: Oper,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSGRANDSTREAM,R
NA :: CATALOG :: Oper,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSLINKSYS,R
NA :: CATALOG :: Oper,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSPOLYCOM,R
NA :: CATALOG :: Oper,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSSNOM,R
NA :: CATALOG :: Oper,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSYEALINK,R
NA :: CATALOG :: Oper,NA :: CATALOG :: catalog :: NA_CATALOG.PHONES_LIST,R
NA :: CATALOG :: Create,NA :: CATALOG :: catalog,R
NA :: CATALOG :: Create,NA :: CATALOG :: catalog :: NA_CATALOG.EQ,R
NA :: CATALOG :: Create,NA :: CATALOG :: catalog :: NA_CATALOG.M5,R
NA :: CATALOG :: Create,NA :: CATALOG :: catalog :: NA_CATALOG.M6,R
NA :: CATALOG :: Create,NA :: CATALOG :: catalog :: NA_CATALOG.OLT_LIST,R
NA :: CATALOG :: Create,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTS,R
NA :: CATALOG :: Create,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSCISCO,R
NA :: CATALOG :: Create,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSGRANDSTREAM,R
NA :: CATALOG :: Create,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSLINKSYS,R
NA :: CATALOG :: Create,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSPOLYCOM,R
NA :: CATALOG :: Create,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSSNOM,R
NA :: CATALOG :: Create,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSYEALINK,R
NA :: CATALOG :: Create,NA :: CATALOG :: catalog :: NA_CATALOG.PHONES_LIST,R
NA :: GO :: Admin,NA :: GO :: genericOperationsFamilies,R
NA :: GO :: Admin,NA :: GO :: genericOperationsFamilies :: ABC-ADO,R
NA :: GO :: Admin,NA :: GO :: genericOperationsFamilies :: GPON,R
NA :: GO :: Admin,NA :: GO :: genericOperationsFamilies :: HMNT,R
NA :: GO :: Admin,NA :: GO :: genericOperationsFamilies :: TEST,R
NA :: GO :: Admin,NA :: GO :: genericOperationsFamilies :: VEACS,R
NA :: GO :: Admin,NA :: GO :: genericOperationsRfs,R
NA :: GO :: Admin,NA :: GO :: genericOperationsRfs :: rfsDeviceManagement,R
NA :: GO :: Admin,NA :: GO :: genericOperationsRfs :: rfsGponDiagnostics,R
NA :: GO :: Admin,NA :: GO :: genericOperationsRfs :: rfsProviderNetworkAccess,R
NA :: GO :: Admin,NA :: GO :: genericOperationsRfs :: rfsTest,R
NA :: GO :: Admin,NA :: GO :: genericOperationsRfs :: rfsVoiceAbc,R
NA :: GO :: Consult,NA :: GO :: genericOperationsFamilies,R
NA :: GO :: Consult,NA :: GO :: genericOperationsFamilies :: ABC-ADO,R
NA :: GO :: Consult,NA :: GO :: genericOperationsFamilies :: GPON,R
NA :: GO :: Consult,NA :: GO :: genericOperationsFamilies :: HMNT,R
NA :: GO :: Consult,NA :: GO :: genericOperationsFamilies :: TEST,R
NA :: GO :: Consult,NA :: GO :: genericOperationsFamilies :: VEACS,R
NA :: GO :: Consult,NA :: GO :: genericOperationsRfs,R
NA :: GO :: Consult,NA :: GO :: genericOperationsRfs :: rfsDeviceManagement,R
NA :: GO :: Consult,NA :: GO :: genericOperationsRfs :: rfsGponDiagnostics,R
NA :: GO :: Consult,NA :: GO :: genericOperationsRfs :: rfsProviderNetworkAccess,R
NA :: GO :: Consult,NA :: GO :: genericOperationsRfs :: rfsTest,R
NA :: GO :: Consult,NA :: GO :: genericOperationsRfs :: rfsVoiceAbc,R
NA :: GO :: Oper,NA :: GO :: genericOperationsFamilies,R
NA :: GO :: Oper,NA :: GO :: genericOperationsFamilies :: ABC-ADO,R
NA :: GO :: Oper,NA :: GO :: genericOperationsFamilies :: GPON,R
NA :: GO :: Oper,NA :: GO :: genericOperationsFamilies :: HMNT,R
NA :: GO :: Oper,NA :: GO :: genericOperationsFamilies :: TEST,R
NA :: GO :: Oper,NA :: GO :: genericOperationsFamilies :: VEACS,R
NA :: GO :: Oper,NA :: GO :: genericOperationsRfs,R
NA :: GO :: Oper,NA :: GO :: genericOperationsRfs :: rfsDeviceManagement,R
NA :: GO :: Oper,NA :: GO :: genericOperationsRfs :: rfsGponDiagnostics,R
NA :: GO :: Oper,NA :: GO :: genericOperationsRfs :: rfsProviderNetworkAccess,R
NA :: GO :: Oper,NA :: GO :: genericOperationsRfs :: rfsTest,R
NA :: GO :: Oper,NA :: GO :: genericOperationsRfs :: rfsVoiceAbc,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: bbrasAudit,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: check,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: cqgpon,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: dslam,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: dtn,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: eventBroker,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: gpon,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: gponAudit,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: gponEmp,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: hnm,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: homeNetwork,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: homenetworkAudit,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: imsSec,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: imsSipTrunking,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: imsVoIPClass5,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: iptvAudit,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: iptvGestaoAccount,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: iptvGestaoSTB,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: parque,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: pstn,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: radiusRede,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: rollingUpgrade,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: smart2m,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: SUM,R
NA :: MPT :: Admin,NA :: MPT :: mptFamilies :: VEACS,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: bbrasAudit,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: check,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: cqgpon,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: dslam,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: dtn,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: eventBroker,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: gpon,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: gponAudit,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: gponEmp,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: hnm,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: homeNetwork,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: homenetworkAudit,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: imsSec,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: imsSipTrunking,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: imsVoIPClass5,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: iptvAudit,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: iptvGestaoAccount,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: iptvGestaoSTB,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: parque,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: pstn,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: radiusRede,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: rollingUpgrade,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: smart2m,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: SUM,R
NA :: MPT :: Consult,NA :: MPT :: mptFamilies :: VEACS,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: bbrasAudit,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: check,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: cqgpon,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: dslam,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: dtn,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: eventBroker,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: gpon,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: gponAudit,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: gponEmp,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: hnm,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: homeNetwork,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: homenetworkAudit,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: imsSec,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: imsSipTrunking,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: imsVoIPClass5,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: iptvAudit,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: iptvGestaoAccount,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: iptvGestaoSTB,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: parque,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: pstn,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: radiusRede,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: rollingUpgrade,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: smart2m,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: SUM,R
NA :: MPT :: Oper,NA :: MPT :: mptFamilies :: VEACS,R
Service :: NA,NA :: CATALOG :: catalog,R
Service :: NA,NA :: CATALOG :: catalog :: NA_CATALOG.EQ,R
Service :: NA,NA :: CATALOG :: catalog :: NA_CATALOG.M5,R
Service :: NA,NA :: CATALOG :: catalog :: NA_CATALOG.M6,R
Service :: NA,NA :: CATALOG :: catalog :: NA_CATALOG.OLT_LIST,R
Service :: NA,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTS,R
Service :: NA,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSCISCO,R
Service :: NA,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSGRANDSTREAM,R
Service :: NA,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSLINKSYS,R
Service :: NA,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSPOLYCOM,R
Service :: NA,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSSNOM,R
Service :: NA,NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSYEALINK,R
Service :: NA,NA :: CATALOG :: catalog :: NA_CATALOG.PHONES_LIST,R
Service :: NA,NA :: GO :: genericOperationsFamilies,R
Service :: NA,NA :: GO :: genericOperationsFamilies :: ABC-ADO,R
Service :: NA,NA :: GO :: genericOperationsFamilies :: GPON,R
Service :: NA,NA :: GO :: genericOperationsFamilies :: HMNT,R
Service :: NA,NA :: GO :: genericOperationsFamilies :: TEST,R
Service :: NA,NA :: GO :: genericOperationsFamilies :: VEACS,R
Service :: NA,NA :: GO :: genericOperationsRfs,R
Service :: NA,NA :: GO :: genericOperationsRfs :: rfsDeviceManagement,R
Service :: NA,NA :: GO :: genericOperationsRfs :: rfsGponDiagnostics,R
Service :: NA,NA :: GO :: genericOperationsRfs :: rfsProviderNetworkAccess,R
Service :: NA,NA :: GO :: genericOperationsRfs :: rfsTest,R
Service :: NA,NA :: GO :: genericOperationsRfs :: rfsVoiceAbc,R
Service :: NA,NA :: MPT :: mptFamilies,R
Service :: NA,NA :: MPT :: mptFamilies :: bbrasAudit,R
Service :: NA,NA :: MPT :: mptFamilies :: check,R
Service :: NA,NA :: MPT :: mptFamilies :: cqgpon,R
Service :: NA,NA :: MPT :: mptFamilies :: dslam,R
Service :: NA,NA :: MPT :: mptFamilies :: dtn,R
Service :: NA,NA :: MPT :: mptFamilies :: eventBroker,R
Service :: NA,NA :: MPT :: mptFamilies :: gpon,R
Service :: NA,NA :: MPT :: mptFamilies :: gponAudit,R
Service :: NA,NA :: MPT :: mptFamilies :: gponEmp,R
Service :: NA,NA :: MPT :: mptFamilies :: hnm,R
Service :: NA,NA :: MPT :: mptFamilies :: homeNetwork,R
Service :: NA,NA :: MPT :: mptFamilies :: homenetworkAudit,R
Service :: NA,NA :: MPT :: mptFamilies :: imsSec,R
Service :: NA,NA :: MPT :: mptFamilies :: imsSipTrunking,R
Service :: NA,NA :: MPT :: mptFamilies :: imsVoIPClass5,R
Service :: NA,NA :: MPT :: mptFamilies :: iptvAudit,R
Service :: NA,NA :: MPT :: mptFamilies :: iptvGestaoAccount,R
Service :: NA,NA :: MPT :: mptFamilies :: iptvGestaoSTB,R
Service :: NA,NA :: MPT :: mptFamilies :: parque,R
Service :: NA,NA :: MPT :: mptFamilies :: pstn,R
Service :: NA,NA :: MPT :: mptFamilies :: radiusRede,R
Service :: NA,NA :: MPT :: mptFamilies :: rollingUpgrade,R
Service :: NA,NA :: MPT :: mptFamilies :: smart2m,R
Service :: NA,NA :: MPT :: mptFamilies :: SUM,R
Service :: NA,NA :: MPT :: mptFamilies :: VEACS,R
