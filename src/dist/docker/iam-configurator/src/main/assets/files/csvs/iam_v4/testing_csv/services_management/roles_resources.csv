RoleName,ResourceName,ResourcePermIAM,ResourcePermService
IAM :: NA Services Manager,IAM :: Active Sessions,R,RD
IAM :: NA Services Manager,IAM :: Child Contexts,X,CRUD
IAM :: NA Services Manager,IAM :: Child Resources,X,CRUD
IAM :: NA Services Manager,IAM :: Claims,X,CRUD
IAM :: NA Services Manager,IAM :: Claim Source Overrides,X,CRUD
IAM :: NA Services Manager,IAM :: Context Group Members,X,CRUD
IAM :: NA Services Manager,IAM :: OAuth Client,X,CRUD
IAM :: NA Services Manager,IAM :: Resource Group Members,X,CRUD
IAM :: NA Services Manager,IAM :: Role Permissions over Attributes,X,CRUD
IAM :: NA Services Manager,IAM :: Role Permissions over Authentication Procedures,X,CRUD
IAM :: NA Services Manager,IAM :: Role Permissions over Context Groups,X,CRUD
IAM :: NA Services Manager,IAM :: Role Permissions over Contexts,X,CRUD
IAM :: NA Services Manager,IAM :: Role Permissions over Resource Groups,X,CRUD
IAM :: NA Services Manager,IAM :: Role Permissions over Resources,X,CRUD
IAM :: NA Services Manager,IAM :: Role Permissions over Roles,X,CRUD
IAM :: NA Services Manager,IAM :: Role Permissions over Service Groups,X,CRUD
IAM :: NA Services Manager,IAM :: Role Permissions over Services,X,CRUD
IAM :: NA Services Manager,IAM :: Role Permissions over Stores,X,CRUD
IAM :: NA Services Manager,IAM :: Role Permissions over User Groups,X,CRUD
IAM :: NA Services Manager,IAM :: Role Permissions over Users,X,CRUD
IAM :: NA Services Manager,IAM :: Role Service-Permissions over Resource Groups,X,CRUD
IAM :: NA Services Manager,IAM :: Role Service-Permissions over Resources,X,CRUD
IAM :: NA Services Manager,IAM :: Service Authentication Procedures,X,CRUD
IAM :: NA Services Manager,IAM :: Service Group Members,X,CRUD
IAM :: NA Services Manager,IAM :: Service Name ID definition,X,CRUD
IAM :: NA Services Manager,IAM :: Service Roles,X,CRUD
IAM :: NA Services Manager,IAM :: Service Tags,X,CRUD
IAM :: NA Services Manager,IAM :: Context Groups,X,CRUD
IAM :: NA Services Manager,IAM :: Contexts,X,CRUD
IAM :: NA Services Manager,IAM :: Resource Groups,X,CRUD
IAM :: NA Services Manager,IAM :: Resources,X,CRUD
IAM :: NA Services Manager,IAM :: Roles,X,CRUD
IAM :: NA Services Manager,IAM :: Service Groups,X,CRUD
IAM :: NA Services Manager,IAM :: Services,X,CRUDX
IAM :: NA Services Manager,IAM :: User Groups,X,CRUD
IAM :: NA Services Manager,IAM :: Tags,R,CRUD
IAM :: NA Services Manager,NA :: CATALOG,RUD,CRUDX
IAM :: NA Services Manager,NA :: CATALOG :: catalog,RUD,CRUDX
IAM :: NA Services Manager,NA :: GO,RUD,CRUDX
IAM :: NA Services Manager,NA :: GO :: GenericOperations,RUD,CRUDX
IAM :: NA Services Manager,NA :: GO :: GenericOperationsAdmin,RUD,CRUDX
IAM :: NA Services Manager,NA :: MONITORING,RUD,CRUDX
IAM :: NA Services Manager,NA :: MONITORING :: monitoring,RUD,CRUDX
IAM :: NA Services Manager,NA :: MPT,RUD,CRUDX
IAM :: NA Services Manager,NA :: MPT :: operations,RUD,CRUDX
IAM :: NA Services Manager,NA :: OPERATIONS CATALOG,RUD,CRUDX
IAM :: NA Services Manager,NA :: OPERATIONS CATALOG :: operations-catalog,RUD,CRUDX
IAM :: NA Services Manager,NA :: VEACS,RUD,CRUDX
IAM :: NA Services Manager,NA :: VEACS :: device,RUD,CRUDX
IAM :: NA Services Manager,NOSSIS :: NOSSIS,RUD,CRUDX
