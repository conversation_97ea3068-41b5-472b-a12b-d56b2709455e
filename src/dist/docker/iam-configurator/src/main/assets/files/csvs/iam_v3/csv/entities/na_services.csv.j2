ServiceName,ServiceDescription,ServiceEnabled,ServiceUrl,ServiceLogoutBehavior,ServiceIssuer,ServiceSamlLogoutUrl,ServiceSamlEncryptedResponsesAssertions,ServiceSamlSignedRequests,ServiceSamlSignedResponses,ServiceSamlSignedResponsesAssertions,ServicePkiPublicCertificate,ClaimName,ClaimDefault,ClaimRequired,StoreName,AttributeId,AttributeName,AuthnProcedureName,AuthnProcedureDefault
NA Portal,NA Portal service,true,{{ na_portal_iam_login_url }},slo,na-portal,{{ na_portal_iam_logout_url }},false,true,true,false,{{ na_portal_public_key }},username,true,true,,1,username,Local,true
NA Portal,NA Portal service,true,{{ na_portal_iam_login_url }},slo,na-portal,{{ na_portal_iam_logout_url }},false,true,true,false,{{ na_portal_public_key }},email,true,true,,2,email,,
NA Portal,NA Portal service,true,{{ na_portal_iam_login_url }},slo,na-portal,{{ na_portal_iam_logout_url }},false,true,true,false,{{ na_portal_public_key }},token,true,true,,3,accessToken,,
NA Portal,NA Portal service,true,{{ na_portal_iam_login_url }},slo,na-portal,{{ na_portal_iam_logout_url }},false,true,true,false,{{ na_portal_public_key }},language,true,false,,4,userLanguage,,
NA Portal,NA Portal service,true,{{ na_portal_iam_login_url }},slo,na-portal,{{ na_portal_iam_logout_url }},false,true,true,false,{{ na_portal_public_key }},name,true,true,,5,name,,