RoleN<PERSON>,<PERSON><PERSON><PERSON><PERSON>,RoleEnabled,RelatedRoleName,RelatedRolePermIAM
NA :: Admin,,true,NA :: Admin,R
NA :: CATALOG :: Admin,Perfil administrador Catálogos,true,NA :: CATALOG :: Admin,R
NA :: CATALOG :: Consult,Perfil consulta catálogos,true,NA :: CATALOG :: Consult,R
NA :: CATALOG :: Create,,true,NA :: CATALOG :: Create,R
NA :: CATALOG :: Dummy,,true,NA :: CATALOG :: Dummy,R
NA :: CATALOG :: Oper,,true,NA :: CATALOG :: Oper,R
NA :: GO :: Admin,Perfil Administrador GO,true,NA :: GO :: Admin,R
NA :: GO :: Consult,Perfil consulta módulo GO,true,NA :: GO :: Consult,R
NA :: GO :: Dummy,Perfil manequim do módulo GO,true,NA :: GO :: Dummy,R
NA :: GO :: Execute,Perfil executor do módulo GO,true,NA :: GO :: Execute,R
NA :: GO :: Oper,Perfil Operador do módulo GO,true,NA :: GO :: Oper,R
NA :: MONITORING :: Admin,Perfil de administrador do módulo de Monitoria,true,NA :: MONITORING :: Admin,R
NA :: MONITORING :: Consult,Perfil de consulta do módulo de Monitoria,true,NA :: MONITORING :: Consult,R
NA :: MONITORING :: Oper,Perfil de operador do módulo de Monitoria,true,NA :: MONITORING :: Oper,R
NA :: MPT :: Admin,Perfil administrador módulo MPT,true,NA :: MPT :: Admin,R
NA :: MPT :: Consult,Perfil consulta módulo MPT,true,NA :: MPT :: Consult,R
NA :: MPT :: Dummy,Perfil manequim módulo MPT,true,NA :: MPT :: Dummy,R
NA :: MPT :: Oper,Perfil operador módulo MPT,true,NA :: MPT :: Oper,R
NA :: OPERATIONS CATALOG :: Admin,Perfil de administrador do módulo do catálogo de operações,true,NA :: OPERATIONS CATALOG :: Admin,R
NA :: OPERATIONS CATALOG :: Consult,Perfil de consulta do módulo do catálogo de operações,true,NA :: OPERATIONS CATALOG :: Consult,R
NA :: OPERATIONS CATALOG :: Oper,Perfil de operador do módulo do catálogo de operações,true,NA :: OPERATIONS CATALOG :: Oper,R
NA :: VEACS :: Admin,Perfil administrador do módulo VEACS,true,NA :: VEACS :: Admin,R
NA :: VEACS :: Consult,Perfil de consulta do módulo VEACS,true,NA :: VEACS :: Consult,R
NA :: VEACS :: Dummy,Perfil manequim do módulo VEACS,true,NA :: VEACS :: Dummy,R
NA :: VEACS :: Oper,Perfil operador do módulo VEACS,true,NA :: VEACS :: Oper,R
NOSSIS :: NOSSIS,,true,NOSSIS :: NOSSIS,R
Service :: NA,,true,Service :: NA,R
