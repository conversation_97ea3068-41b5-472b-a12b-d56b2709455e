User<PERSON>ame,UserDescription,UserEnabled,<PERSON>N<PERSON>,ContextName
ADMIN_CATALOG,Administrador do módulo Reference Data,true,NA :: CATALOG :: Admin,NA :: CATALOG :: catalog
ADMIN_CATALOG_EN,Reference Data module administrator,true,NA :: CATALOG :: Admin,NA :: CATALOG :: catalog
ADMIN_CATALOG_FR,Administrateur du module Reference Data,true,NA :: CATALOG :: Admin,NA :: CATALOG :: catalog
ADMIN_GO,Administrador do módulo Generic Operations,true,NA :: GO :: Admin,NA :: GO :: genericOperationsFamilies
ADMIN_GO,Administrador do módulo Generic Operations,true,NA :: GO :: Admin,NA :: GO :: genericOperationsRfs
ADMIN_GO,Administrador do módulo Generic Operations,true,NOSSIS :: NOSSIS,
ADMIN_GO_EN,Generic Operations module administrator,true,NA :: GO :: Admin,NA :: GO :: genericOperationsFamilies
ADMI<PERSON>_GO_EN,Generic Operations module administrator,true,NA :: GO :: Admin,NA :: GO :: genericOperationsRfs
ADMIN_GO_EN,Generic Operations module administrator,true,NOSSIS :: NOSSIS,
ADMIN_GO_FR,Administrateur du module Generic Operations,true,NA :: GO :: Admin,NA :: GO :: genericOperationsFamilies
ADMIN_GO_FR,Administrateur du module Generic Operations,true,NA :: GO :: Admin,NA :: GO :: genericOperationsRfs
ADMIN_GO_FR,Administrateur du module Generic Operations,true,NOSSIS :: NOSSIS,
ADMIN_MONIT,Administrador do módulo Monitoria,true,NA :: MONITORING :: Admin,
ADMIN_MONIT_EN,Monitoring module administrator,true,NA :: MONITORING :: Admin,
ADMIN_MONIT_FR,Administrateur du module Monitoring,true,NA :: MONITORING :: Admin,
ADMIN_MPT,Administrador do módulo MPT,true,NA :: MPT :: Admin,NA :: MPT :: mptFamilies
ADMIN_MPT,Administrador do módulo MPT,true,NOSSIS :: NOSSIS,
ADMIN_MPT_EN,MPT module administrator,true,NA :: MPT :: Admin,NA :: MPT :: mptFamilies
ADMIN_MPT_EN,MPT module administrator,true,NOSSIS :: NOSSIS,
ADMIN_MPT_FR,Administrateur du module MPT,true,NOSSIS :: NOSSIS,
ADMIN_MPT_FR,Administrateur du module MPT,true,NA :: MPT :: Admin,NA :: MPT :: mptFamilies
ADMIN_OPER_CATALOG,Administrador do módulo Catálogo de Operações,true,NA :: OPERATIONS CATALOG :: Admin,
ADMIN_OPER_CAT_EN,Operations Catalog module administrator,true,NA :: OPERATIONS CATALOG :: Admin,
ADMIN_OPER_CAT_FR,Administrateur du module Operations Catalog,true,NA :: OPERATIONS CATALOG :: Admin,
ADMIN_PORTAL,Administrador do Portal NA,true,NA :: Admin,NA :: CATALOG :: catalog
ADMIN_PORTAL,Administrador do Portal NA,true,NA :: Admin,NA :: GO :: genericOperationsFamilies
ADMIN_PORTAL,Administrador do Portal NA,true,NA :: Admin,NA :: GO :: genericOperationsRfs
ADMIN_PORTAL,Administrador do Portal NA,true,NA :: Admin,NA :: MPT :: mptFamilies
ADMIN_PORTAL,Administrador do Portal NA,true,NOSSIS :: NOSSIS,
ADMIN_PORTAL_EN,NA Portal administrator,true,NA :: Admin,NA :: CATALOG :: catalog
ADMIN_PORTAL_EN,NA Portal administrator,true,NA :: Admin,NA :: GO :: genericOperationsFamilies
ADMIN_PORTAL_EN,NA Portal administrator,true,NA :: Admin,NA :: GO :: genericOperationsRfs
ADMIN_PORTAL_EN,NA Portal administrator,true,NA :: Admin,NA :: MPT :: mptFamilies
ADMIN_PORTAL_EN,NA Portal administrator,true,NOSSIS :: NOSSIS,
ADMIN_PORTAL_FR,Administrateur du NA Portal,true,NA :: Admin,NA :: CATALOG :: catalog
ADMIN_PORTAL_FR,Administrateur du NA Portal,true,NA :: Admin,NA :: GO :: genericOperationsFamilies
ADMIN_PORTAL_FR,Administrateur du NA Portal,true,NA :: Admin,NA :: GO :: genericOperationsRfs
ADMIN_PORTAL_FR,Administrateur du NA Portal,true,NA :: Admin,NA :: MPT :: mptFamilies
ADMIN_PORTAL_FR,Administrateur du NA Portal,true,NOSSIS :: NOSSIS,
ADMIN_VEACS,Administrador do módulo VEACS,true,NA :: VEACS :: Admin,
ADMIN_VEACS,Administrador do módulo VEACS,true,NOSSIS :: NOSSIS,
ADMIN_VEACS_EN,VEACS module administrator,true,NA :: VEACS :: Admin,
ADMIN_VEACS_EN,VEACS module administrator,true,NOSSIS :: NOSSIS,
ADMIN_VEACS_FR,Administrateur du module VEACS,true,NA :: VEACS :: Admin,
ADMIN_VEACS_FR,Administrateur du module VEACS,true,NOSSIS :: NOSSIS,
CONSULT_CATALOG_EN,Reference Data module consult user,true,NA :: CATALOG :: Consult,NA :: CATALOG :: catalog
CONSULT_CATALOG_EN,Reference Data module consult user,true,NOSSIS :: NOSSIS,
CONSULT_CATALOG_FR,Module Reference Data consulter un utilisateur,true,NA :: CATALOG :: Consult,NA :: CATALOG :: catalog
CONSULT_CATALOG_FR,Module Reference Data consulter un utilisateur,true,NOSSIS :: NOSSIS,
CONSULT_MONIT,Utilizador de consulta do módulo Monitoria,true,NA :: MONITORING :: Consult,
CONSULT_MONIT_EN,Monitoring module consult user,true,NA :: MONITORING :: Consult,
CONSULT_MONIT_FR,Module Monitoring consulter un utilisateur,true,NA :: MONITORING :: Consult,
CONSULT_OPER_CATALOG,Utilizador de consulta do módulo Catálogo de Operações,true,NA :: OPERATIONS CATALOG :: Consult,
CONSULT_OPER_CAT_EN,Operations Catalog module consult user,true,NA :: OPERATIONS CATALOG :: Consult,
CONSULT_OPER_CAT_FR,Module Operations Catalog consulter un utilisateur,true,NA :: OPERATIONS CATALOG :: Consult,
OPER_CATALOG,Operador do módulo Reference Data,true,NA :: CATALOG :: Oper,NA :: CATALOG :: catalog
OPER_CATALOG,Operador do módulo Reference Data,true,NOSSIS :: NOSSIS,
OPER_CATALOG_CONSULT,Utilizador de consulta do módulo Reference Data,true,NA :: CATALOG :: Consult,NA :: CATALOG :: catalog
OPER_CATALOG_CONSULT,Utilizador de consulta do módulo Reference Data,true,NOSSIS :: NOSSIS,
OPER_CATALOG_EN,Reference Data module operation user,true,NA :: CATALOG :: Oper,NA :: CATALOG :: catalog
OPER_CATALOG_EN,Reference Data module operation user,true,NOSSIS :: NOSSIS,
OPER_CATALOG_FR,Module Reference Data utilisateur d'opération,true,NA :: CATALOG :: Oper,NA :: CATALOG :: catalog
OPER_CATALOG_FR,Module Reference Data utilisateur d'opération,true,NOSSIS :: NOSSIS,
OPER_GO,Operador do módulo Generic Operations,true,NA :: GO :: Oper,NA :: GO :: genericOperationsFamilies
OPER_GO,Operador do módulo Generic Operations,true,NA :: GO :: Oper,NA :: GO :: genericOperationsRfs
OPER_GO,Operador do módulo Generic Operations,true,NOSSIS :: NOSSIS,
OPER_GO_CONSULT,Utilizador de consulta do módulo Generic Operations,true,NA :: GO :: Consult,NA :: GO :: genericOperationsFamilies
OPER_GO_CONSULT,Utilizador de consulta do módulo Generic Operations,true,NA :: GO :: Consult,NA :: GO :: genericOperationsRfs
OPER_GO_CONSULT,Utilizador de consulta do módulo Generic Operations,true,NOSSIS :: NOSSIS,
OPER_GO_CONSULT_EN,Generic Operations module consult user,true,NA :: GO :: Consult,NA :: GO :: genericOperationsFamilies
OPER_GO_CONSULT_EN,Generic Operations module consult user,true,NA :: GO :: Consult,NA :: GO :: genericOperationsRfs
OPER_GO_CONSULT_EN,Generic Operations module consult user,true,NOSSIS :: NOSSIS,
OPER_GO_CONSULT_FR,Module Generic Operations consulter un utilisateur,true,NA :: GO :: Consult,NA :: GO :: genericOperationsFamilies
OPER_GO_CONSULT_FR,Module Generic Operations consulter un utilisateur,true,NA :: GO :: Consult,NA :: GO :: genericOperationsRfs
OPER_GO_CONSULT_FR,Module Generic Operations consulter un utilisateur,true,NOSSIS :: NOSSIS,
OPER_GO_EN,Generic Operations module operation user,true,NA :: GO :: Oper,NA :: GO :: genericOperationsFamilies
OPER_GO_EN,Generic Operations module operation user,true,NA :: GO :: Oper,NA :: GO :: genericOperationsRfs
OPER_GO_EN,Generic Operations module operation user,true,NOSSIS :: NOSSIS,
OPER_GO_FR,Module Generic Operations utilisateur d'opération,true,NA :: GO :: Oper,NA :: GO :: genericOperationsFamilies
OPER_GO_FR,Module Generic Operations utilisateur d'opération,true,NA :: GO :: Oper,NA :: GO :: genericOperationsRfs
OPER_GO_FR,Module Generic Operations utilisateur d'opération,true,NOSSIS :: NOSSIS,
OPER_HIST_ORDER,Operador de ordens do módulo Histórico,true,NA :: History :: External System,
OPER_HIST_ORDER,Operador de ordens do módulo Histórico,true,NA :: History :: Orders,
OPER_HIST_ORDER,Operador de ordens do módulo Histórico,true,NOSSIS :: NOSSIS,
OPER_HIST_ORDER_EN,Historic module orders operation user,true,NA :: History :: External System,
OPER_HIST_ORDER_EN,Historic module orders operation user,true,NA :: History :: Orders,
OPER_HIST_ORDER_EN,Historic module orders operation user,true,NOSSIS :: NOSSIS,
OPER_HIST_ORDER_FR,Module Historic orders utilisateur d'opération,true,NA :: History :: External System,
OPER_HIST_ORDER_FR,Module Historic orders utilisateur d'opération,true,NA :: History :: Orders,
OPER_HIST_ORDER_FR,Module Historic orders utilisateur d'opération,true,NOSSIS :: NOSSIS,
OPER_HIST_ORDER_NOEX,Operador de ordens do módulo Histórico%2C sem acesso a External System,true,NA :: History :: Orders,
OPER_HIST_ORDER_NOEX,Operador de ordens do módulo Histórico%2C sem acesso a External System,true,NOSSIS :: NOSSIS,
OPERHISTORDERNOEX_EN,Historic module orders operation user%2C without access to External System,true,NA :: History :: Orders,
OPERHISTORDERNOEX_EN,Historic module orders operation user%2C without access to External System,true,NOSSIS :: NOSSIS,
OPERHISTORDERNOEX_FR,Module Historic orders utilisateur d'opération%2C sans accès au système externe,true,NA :: History :: Orders,
OPERHISTORDERNOEX_FR,Module Historic orders utilisateur d'opération%2C sans accès au système externe,true,NOSSIS :: NOSSIS,
OPER_HIST_RFS,Operador de RFS do módulo Histórico,true,NA :: History :: External System,
OPER_HIST_RFS,Operador de RFS do módulo Histórico,true,NA :: History :: RFS,NA :: History :: rfs
OPER_HIST_RFS,Operador de RFS do módulo Histórico,true,NOSSIS :: NOSSIS,
OPER_HIST_RFS_EN,Historic module RFS operation user,true,NA :: History :: External System,
OPER_HIST_RFS_EN,Historic module RFS operation user,true,NA :: History :: RFS,NA :: History :: rfs
OPER_HIST_RFS_EN,Historic module RFS operation user,true,NOSSIS :: NOSSIS,
OPER_HIST_RFS_FR,Module Historic utilisateur d'opération RFS,true,NA :: History :: External System,
OPER_HIST_RFS_FR,Module Historic utilisateur d'opération RFS,true,NA :: History :: RFS,NA :: History :: rfs
OPER_HIST_RFS_FR,Module Historic utilisateur d'opération RFS,true,NOSSIS :: NOSSIS,
OPER_HIST_RFS_NOEX,Operador de RFS do módulo Histórico%2C sem acesso a External System,true,NA :: History :: RFS,NA :: History :: rfs :: rfsSft
OPER_HIST_RFS_NOEX,Operador de RFS do módulo Histórico%2C sem acesso a External System,true,NOSSIS :: NOSSIS,
OPERHISTRFSNOEX_EN,Historic module RFS operation user%2C without access to External System,true,NA :: History :: RFS,NA :: History :: rfs :: rfsSft
OPERHISTRFSNOEX_EN,Historic module RFS operation user%2C without access to External System,true,NOSSIS :: NOSSIS,
OPERHISTRFSNOEX_FR,Module Historic utilisateur d'opération RFS%2C sans accès au système externe,true,NA :: History :: RFS,NA :: History :: rfs :: rfsSft
OPERHISTRFSNOEX_FR,Module Historic utilisateur d'opération RFS%2C sans accès au système externe,true,NOSSIS :: NOSSIS,
OPER_MONIT,Operador do módulo Monitoria,true,NA :: MONITORING :: Oper,
OPER_MONIT_EN,Monitoring module operation user,true,NA :: MONITORING :: Oper,
OPER_MONIT_FR,Module Monitoring utilisateur d'opération,true,NA :: MONITORING :: Oper,
OPER_MPT,Operador do módulo MPT,true,NA :: MPT :: Oper,NA :: MPT :: mptFamilies
OPER_MPT,Operador do módulo MPT,true,NOSSIS :: NOSSIS,
OPER_MPT_CONSULT,Utilizador de consulta do módulo MPT,true,NA :: MPT :: Consult,NA :: MPT :: mptFamilies
OPER_MPT_CONSULT,Utilizador de consulta do módulo MPT,true,NOSSIS :: NOSSIS,
OPER_MPT_CONSULT_EN,MPT module consult user,true,NA :: MPT :: Consult,NA :: MPT :: mptFamilies
OPER_MPT_CONSULT_EN,MPT module consult user,true,NOSSIS :: NOSSIS,
OPER_MPT_CONSULT_FR,Module MPT consulter un utilisateur,true,NA :: MPT :: Consult,NA :: MPT :: mptFamilies
OPER_MPT_CONSULT_FR,Module MPT consulter un utilisateur,true,NOSSIS :: NOSSIS,
OPER_MPT_EN,MPT module operation user,true,NA :: MPT :: Oper,NA :: MPT :: mptFamilies
OPER_MPT_EN,MPT module operation user,true,NOSSIS :: NOSSIS,
OPER_MPT_FR,Module MPT utilisateur d'opération,true,NA :: MPT :: Oper,NA :: MPT :: mptFamilies
OPER_MPT_FR,Module MPT utilisateur d'opération,true,NOSSIS :: NOSSIS,
OPER_OPER_CATALOG,Operador do módulo Catálogo de Operações,true,NA :: OPERATIONS CATALOG :: Oper,
OPER_OPER_CAT_EN,Operations Catalog module operation user,true,NA :: OPERATIONS CATALOG :: Oper,
OPER_OPER_CAT_FR,Module Operations Catalog utilisateur d'opération,true,NA :: OPERATIONS CATALOG :: Oper,
OPER_VEACS,Operador do módulo VEACS,true,NA :: VEACS :: Oper,
OPER_VEACS,Operador do módulo VEACS,true,NOSSIS :: NOSSIS,
OPER_VEACS_CONSULT_EN,VEACS module consult user,true,NA :: VEACS :: Consult,
OPER_VEACS_CONSULT_EN,VEACS module consult user,true,NOSSIS :: NOSSIS,
OPER_VEACS_CONSULT_FR,Module VEACS consulter un utilisateur,true,NA :: VEACS :: Consult,
OPER_VEACS_CONSULT_FR,Module VEACS consulter un utilisateur,true,NOSSIS :: NOSSIS,
OPER_VEACS_CONSULT,Utilizador de consulta do módulo VEACS,true,NA :: VEACS :: Consult,
OPER_VEACS_CONSULT,Utilizador de consulta do módulo VEACS,true,NOSSIS :: NOSSIS,
OPER_VEACS_EN,VEACS module operation user,true,NA :: VEACS :: Oper,
OPER_VEACS_EN,VEACS module operation user,true,NOSSIS :: NOSSIS,
OPER_VEACS_FR,Module VEACS utilisateur d'opération,true,NA :: VEACS :: Oper,
OPER_VEACS_FR,Module VEACS utilisateur d'opération,true,NOSSIS :: NOSSIS,
