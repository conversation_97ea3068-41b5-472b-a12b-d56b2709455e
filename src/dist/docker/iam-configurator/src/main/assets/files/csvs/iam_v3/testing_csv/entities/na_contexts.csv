ContextName,ContextExternalId,ContextEnabled,ParentContextName
NA :: CATALOG :: catalog,catalog,true,
NA :: CATALOG :: catalog :: NA_CATALOG.EQ,catalog/NA_CATALOG.EQ,true,NA :: CATALOG :: catalog
NA :: CATALOG :: catalog :: NA_CATALOG.M5,catalog/NA_CATALOG.M5,true,NA :: CATALOG :: catalog
NA :: CATALOG :: catalog :: NA_CATALOG.M6,catalog/NA_CATALOG.M6,true,NA :: CATALOG :: catalog
NA :: CATALOG :: catalog :: NA_CATALOG.OLT_LIST,catalog/NA_CATALOG.OLT_LIST,true,NA :: CATALOG :: catalog
NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTS,NA_CATALOG.PHONEDEFAULTS,true,NA :: CATALOG :: catalog
NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSCISCO,catalog/NA_CATALOG.PHONEDEFAULTSCISCO,true,NA :: CATALOG :: catalog
NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSGRANDSTREAM,catalog/NA_CATALOG.PHONEDEFAULTSGRANDSTREAM,true,NA :: CATALOG :: catalog
NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSLINKSYS,catalog/NA_CATALOG.PHONEDEFAULTSLINKSYS,true,NA :: CATALOG :: catalog
NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSPOLYCOM,catalog/NA_CATALOG.PHONEDEFAULTSPOLYCOM,true,NA :: CATALOG :: catalog
NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSSNOM,catalog/NA_CATALOG.PHONEDEFAULTSSNOM,true,NA :: CATALOG :: catalog
NA :: CATALOG :: catalog :: NA_CATALOG.PHONEDEFAULTSYEALINK,catalog/NA_CATALOG.PHONEDEFAULTSYEALINK,true,NA :: CATALOG :: catalog
NA :: CATALOG :: catalog :: NA_CATALOG.PHONES_LIST,catalog/NA_CATALOG.PHONES_LIST,true,NA :: CATALOG :: catalog
NA :: CATALOG :: catalog :: NA_CATALOG.TEST_EQ,catalog/NA_CATALOG.TEST_EQ,true,NA :: CATALOG :: catalog
NA :: GO :: genericOperationsFamilies,genericOperationsFamilies,true,
NA :: GO :: genericOperationsFamilies :: ABC-ADO,genericOperationsFamilies/ABC-ADO,true,NA :: GO :: genericOperationsFamilies
NA :: GO :: genericOperationsFamilies :: GPON,genericOperationsFamilies/GPON,true,NA :: GO :: genericOperationsFamilies
NA :: GO :: genericOperationsFamilies :: HMNT,genericOperationsFamilies/HMNT,true,NA :: GO :: genericOperationsFamilies
NA :: GO :: genericOperationsFamilies :: TEST,genericOperationsFamilies/TEST,true,NA :: GO :: genericOperationsFamilies
NA :: GO :: genericOperationsFamilies :: VEACS,genericOperationsFamilies/VEACS,true,NA :: GO :: genericOperationsFamilies
NA :: GO :: genericOperationsRfs,genericOperationsRfs,true,
NA :: GO :: genericOperationsRfs :: rfsDeviceManagement,genericOperationsRfs/rfsDeviceManagement,true,NA :: GO :: genericOperationsRfs
NA :: GO :: genericOperationsRfs :: rfsGponDiagnostics,genericOperationsRfs/rfsGponDiagnostics,true,NA :: GO :: genericOperationsRfs
NA :: GO :: genericOperationsRfs :: rfsProviderNetworkAccess,genericOperationsRfs/rfsProviderNetworkAccess,true,NA :: GO :: genericOperationsRfs
NA :: GO :: genericOperationsRfs :: rfsTest,genericOperationsRfs/rfsTest,true,NA :: GO :: genericOperationsRfs
NA :: GO :: genericOperationsRfs :: rfsVoiceAbc,genericOperationsRfs/rfsVoiceAbc,true,NA :: GO :: genericOperationsRfs
NA :: MPT :: mptFamilies,mptFamilies,true,
NA :: MPT :: mptFamilies :: bbrasAudit,mptFamilies/bbrasAudit,true,NA :: MPT :: mptFamilies
NA :: MPT :: mptFamilies :: check,mptFamilies/check,true,NA :: MPT :: mptFamilies
NA :: MPT :: mptFamilies :: cqgpon,mptFamilies/cqgpon,true,NA :: MPT :: mptFamilies
NA :: MPT :: mptFamilies :: dslam,mptFamilies/dslam,true,NA :: MPT :: mptFamilies
NA :: MPT :: mptFamilies :: dtn,mptFamilies/dtn,true,NA :: MPT :: mptFamilies
NA :: MPT :: mptFamilies :: eventBroker,mptFamilies/eventBroker,true,NA :: MPT :: mptFamilies
NA :: MPT :: mptFamilies :: gpon,mptFamilies/gpon,true,NA :: MPT :: mptFamilies
NA :: MPT :: mptFamilies :: gponAudit,mptFamiliesgponAudit,true,NA :: MPT :: mptFamilies
NA :: MPT :: mptFamilies :: gponEmp,mptFamilies/gponEmp,true,NA :: MPT :: mptFamilies
NA :: MPT :: mptFamilies :: hnm,mptFamilies/hnm,true,NA :: MPT :: mptFamilies
NA :: MPT :: mptFamilies :: homeNetwork,mptFamilies/homeNetwork,true,NA :: MPT :: mptFamilies
NA :: MPT :: mptFamilies :: homenetworkAudit,mptFamilies/homenetworkAudit,true,NA :: MPT :: mptFamilies
NA :: MPT :: mptFamilies :: imsSec,mptFamilies/imsSec,true,NA :: MPT :: mptFamilies
NA :: MPT :: mptFamilies :: imsSipTrunking,mptFamilies/imsSipTrunking,true,NA :: MPT :: mptFamilies
NA :: MPT :: mptFamilies :: imsVoIPClass5,mptFamilies/imsVoIPClass5,true,NA :: MPT :: mptFamilies
NA :: MPT :: mptFamilies :: iptvAudit,mptFamilies/iptvAudit,true,NA :: MPT :: mptFamilies
NA :: MPT :: mptFamilies :: iptvGestaoAccount,mptFamilies/iptvGestaoAccount,true,NA :: MPT :: mptFamilies
NA :: MPT :: mptFamilies :: iptvGestaoSTB,mptFamilies/iptvGestaoSTB,true,NA :: MPT :: mptFamilies
NA :: MPT :: mptFamilies :: parque,mptFamilies/parque,true,NA :: MPT :: mptFamilies
NA :: MPT :: mptFamilies :: pstn,mptFamilies/pstn,true,NA :: MPT :: mptFamilies
NA :: MPT :: mptFamilies :: radiusRede,mptFamilies/radiusRede,true,NA :: MPT :: mptFamilies
NA :: MPT :: mptFamilies :: rollingUpgrade,mptFamilies/rollingUpgrade,true,NA :: MPT :: mptFamilies
NA :: MPT :: mptFamilies :: smart2m,mptFamilies/smart2m,true,NA :: MPT :: mptFamilies
NA :: MPT :: mptFamilies :: SUM,mptFamilies/SUM,true,NA :: MPT :: mptFamilies
NA :: MPT :: mptFamilies :: VEACS,mptFamilies/VEACS,true,NA :: MPT :: mptFamilies