{% set udversion = project.version.value|cut('-SNAPSHOT') -%}
{% set isSNAPSHOT = project.version.value is string_endingwith('-SNAPSHOT') -%}
FROM rdocker.ptin.corppt.com/na-commons{% if version.pt.ptinovacao.get('na-commons').value is string_endingwith '-SNAPSHOT' %}-tst{% endif %}/na-commons-iam-configurator:{{ version.pt.ptinovacao.get('na-commons').value|cut('-SNAPSHOT') }}

LABEL com.alticelabs.name="{{ docker.image.name.value }}"
LABEL com.alticelabs.version="{{ udversion }}"
LABEL com.alticelabs.group="{{ docker.group.value }}"
LABEL com.alticelabs.summary="{{ docker.summary.value }}"
LABEL com.alticelabs.license="{{ docker.license.value }}"
LABEL com.alticelabs.packager="{{ docker.packager.value }}"
LABEL com.alticelabs.os="{{ docker.os.value }}"
LABEL com.alticelabs.release={% if isSNAPSHOT %}"r{{ buildNumber.value }}-{{ docker.timestamp.value }}"{% else %}"{{ docker.timestamp.value }}"{% endif %}

COPY --chown=2016:2016 /config/defaults-na-portal-iam-configurator.yml /config/defaults-na-portal-iam-configurator.yml
COPY --chown=2016:2016 config/all.yml /config/all.yml
COPY --chown=2016:2016 /files/csvs/ /opt/alticelabs/na-portal/conf/na-portal-iam-csvs/

ENV DEFAULTS_INV="$DEFAULTS_INV /config/defaults-na-portal-iam-configurator.yml"
