<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>na-portal-dist-docker</artifactId>
        <groupId>pt.ptinovacao.na-portal</groupId>
        <version>10.55.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>na-portal-dist-docker-iam-configurator-api</artifactId>
    <packaging>pom</packaging>

    <properties>
        <docker.context.directory>${project.basedir}/target/jinja</docker.context.directory>
        <docker.svn.name>${application.name}</docker.svn.name>
        <docker.image.name>na-portal-iam-configurator-api</docker.image.name>
        <docker.file>Dockerfile</docker.file>
        <docker.summary>NA Portal IAM configurator api image</docker.summary>
        <docker.packager>${ud.key}</docker.packager>
        <docker.group>ptin/prod</docker.group>
        <docker.os>centos7</docker.os>
        <version.pt.ptinovacao.iam>4</version.pt.ptinovacao.iam>
    </properties>

    <profiles>
        <profile>
            <id>docker</id>
            <build>
                <plugins>
                    <!-- check for required properties -->
                    <plugin>
                        <artifactId>maven-enforcer-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>check-required-docker-properties</id>
                                <phase>prepare-package</phase>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- build and push image -->
                    <plugin>
                        <groupId>com.spotify</groupId>
                        <artifactId>dockerfile-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>default</id>
                            </execution>
                        </executions>
                    </plugin>

                    <!-->
                    Jinja maven plugin
                    Generate Dockerfiles
                    <!-->
                    <plugin>
                        <groupId>pt.ptinovacao.na-commons</groupId>
                        <artifactId>na-commons-maven-plugin-jinja</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>documentation</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>buildnumber-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>buildnumber-revision</id>
                                <phase>generate-resources</phase>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>pt.ptinovacao.na-commons</groupId>
                        <artifactId>na-commons-maven-plugin-yamldoc</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
