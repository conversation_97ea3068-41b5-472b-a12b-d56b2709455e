RoleName,RoleDescription,RoleExpirationDate,RoleEnabled,RoleServiceRole,ParentRoleName,AttributeName,AttributePermIAM,AuthnProcedureName,AuthnProcedurePermIAM,ResourceName,ResourcePermIAM,ResourcePermService,ResourceGroupName,ResourceGroupPermIAM,ResourceGroupPermService,ContextName,ContextPermIAM,ContextGroupName,ContextGroupPermIAM,RelatedRoleName,RelatedRoleExternalId,RelatedRolePermIAM,ServiceName,ServicePermIAM,ServiceGroupName,ServiceGroupPermIAM,StoreName,StorePermIAM,UserId,UserName,UserPermIAM,UserGroupName,UserGroupPermIAM
NetQ :: Admin,Netq administration role,,TRUE,FALSE,,,,,,NETQ :: SHOW,R,XR,,,,,,,,,,,NA Portal,XR,,,,,,,,,
