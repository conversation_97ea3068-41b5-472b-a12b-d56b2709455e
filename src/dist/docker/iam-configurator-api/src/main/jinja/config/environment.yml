---

#' Path for file(s) that contain(s) additional customization variables.
#' *Note:* To add a new default inventory file the configuration should be:
#' `DEFAULTS_INV="$DEFAULTS_INV /<config-dir>/<inventory-file>.yml"`
#' otherwise $DEFAULTS_INV is overridden.
#' $string
#' %default
DEFAULTS_INV: "$DEFAULTS_INV /config/defaults-na-portal-iam-configurator-api.yml"

#' Name of the environment variable in which the configurator will save certificate (/certificates/public-cert.pem) information for templating.
#' $string
#' %default
IAM_PUBLIC_CERT_VAR: "na_portal_public_key"

#' Whether the IAM configurator should fail in case of missing the /certificates/public-cert.pem certificate.
#' $boolean
#' %default
FAIL_ON_MISSING_CERT: true