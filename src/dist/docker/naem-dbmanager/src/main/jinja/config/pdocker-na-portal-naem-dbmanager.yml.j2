---
{%- set udversion = project.version.value|cut('-SNAPSHOT') %}
{%- set isSNAPSHOT = project.version.value is string_endingwith('-SNAPSHOT') %}


## Defaults

#' NA Portal NAEM DB Manager docker options (For example: port mapping, volume mounts, etc.).
#' $string
#' %default
#' @ "-p <port_host>:<port_container> -v <path_host>:<path_container>"
na_portal_naem_dbmanager_docker_opts: ""

#' NA Portal NAEM DB Manager docker image
#' $string
#' %default
na_portal_naem_dbmanager_docker_image: "{% raw %}{{ docker_registry_host }}{% endraw %}/na-portal{% if isSNAPSHOT %}-tst{% endif %}/na-portal-naem-dbmanager:{{ udversion }}"

#' NA Portal NAEM DB Manager docker command and arguments
#' *Note*: Default NA Portal NAEM DB Manager image entrypoint ignores the value defined here.
#' $string
#' %default
na_portal_naem_dbmanager_docker_command: ""

#' NA Portal NAEM DB Manager docker registry host
#' $string
#' %default
docker_registry_host: "rdocker.ptin.corppt.com"

#' NA Portal NAEM DB Manager client sidecar image
#' $string
#' %default
na_portal_naem_dbmanager_mongo_client_sidecar_image: "{% raw %}{{ docker_registry_host }}{% endraw %}/na-portal{% if isSNAPSHOT %}-tst{% endif %}/na-portal-naem-dbmanager-mongo-client-sidecar:{{ udversion }}"

#' Directory where inventory will be located in the host machine.
#' $string
#' %default
na_portal_naem_dbmanager_docker_inventory_path: "/opt/alticelabs/na-portal/conf/na-portal-naem-dbmanager/config/all.yml"

#' Directory where logs will be stored in the host machine.
#' $string
#' %default
na_portal_naem_dbmanager_data_logs_dir: "/var/log/alticelabs/na-portal/na-portal-naem-dbmanager/"


## Optionals

#' NA Portal NAEM DB Manager docker optional variables
#' Variable where the optional and default values can be defined.
#' By default, all.yml template only expect the mandatory variables.
#' To define optional variables or to change the value of the default ones, this variable should be defined.
#' This is a list composed with "param" and "value", where "param" is the name of the variable to define and "value" the intended value.
#' *Note*: If "value" is an object (dictionary or list), it is recommended to define the value in an auxiliary variable and then convert it to a json in the "value" field. The "na_portal_naem_dbmanager_parameter_name3" parameter in the example shows how it can be done.
#' $list
#' %optional
#' @  - param: "<na_portal_naem_dbmanager_parameter_name1>"
#' @    value: <na_portal_naem_dbmanager_parameter_value1>
#' @  - param: "<na_portal_naem_dbmanager_parameter_name2>"
#' @    value: <na_portal_naem_dbmanager_parameter_value2>
#' @  - param: "<na_portal_naem_dbmanager_parameter_name3>"
#' @    value: "{% raw %}{{ <na_portal_naem_dbmanager_parameter_auxiliary_object_value> | to_json }}{% endraw %}"
#na_portal_naem_dbmanager_optional_variables:

## Reference to mandatory variables

#' All mandatory variables from "[all|#all]" section should also be defined here in the pdocker context.
#' *Note:* This is not a variable, it's here only to make the reference to the "[all|#all]" section.
#' $string
#' %mandatory
#(only for documentation) Mandatory Variables: