<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>na-portal-dist-docker</artifactId>
        <groupId>pt.ptinovacao.na-portal</groupId>
        <version>10.3.0-SNAPSHOT</version>
    </parent>

    <artifactId>na-portal-dist-docker-naem-dbmanager</artifactId>
    <packaging>pom</packaging>

    <properties>
        <docker.context.directory>${project.basedir}/target/jinja</docker.context.directory>
        <docker.svn.name>${application.name}</docker.svn.name>
        <docker.file>Dockerfile</docker.file>
        <docker.image.name>na-portal-naem-dbmanager</docker.image.name>
        <docker.summary>NA Portal Naem Dbmanager image</docker.summary>
        <docker.packager>${ud.key}</docker.packager>
        <docker.group>ptin/prod</docker.group>
        <docker.mongo.version>3.6</docker.mongo.version>
        <docker.os>centos7</docker.os>
    </properties>

    <dependencies>
        <!-- NAPORTAL Model -->
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-auditing-schemas</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-auditing-workflows</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-error-commons</artifactId>
            <scope>runtime</scope>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>docker</id>
            <build>
                <plugins>
                    <!-- check for required properties -->
                    <plugin>
                        <artifactId>maven-enforcer-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>check-required-docker-properties</id>
                                <phase>prepare-package</phase>
                            </execution>
                        </executions>
                    </plugin>

                    <!-- Fetch necessary libs -->
                    <plugin>
                        <artifactId>maven-dependency-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>copy-external-dependencies</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>copy-dependencies</goal>
                                </goals>
                                <configuration>
                                    <outputDirectory>${project.build.directory}/jinja/files/na-portal/lib</outputDirectory>
                                    <includeScope>runtime</includeScope>
                                    <stripVersion>false</stripVersion>
                                    <excludeGroupIds>${project.groupId}</excludeGroupIds>
                                </configuration>
                            </execution>
                            <execution>
                                <id>copy-project-dependencies</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>copy-dependencies</goal>
                                </goals>
                                <configuration>
                                    <outputDirectory>${project.build.directory}/jinja/files/na-portal/lib</outputDirectory>
                                    <includeScope>runtime</includeScope>
                                    <stripVersion>true</stripVersion>
                                    <includeGroupIds>${project.groupId}</includeGroupIds>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>

                    <plugin>
                        <groupId>com.spotify</groupId>
                        <artifactId>dockerfile-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>default</id>
                            </execution>
                        </executions>
                    </plugin>

                    <!-->
                    Jinja maven plugin
                    Generate Dockerfiles
                    <!-->
                    <plugin>
                        <groupId>pt.ptinovacao.na-commons</groupId>
                        <artifactId>na-commons-maven-plugin-jinja</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>documentation</id>
            <build>
                <plugins>
                    <!-->
                    Jinja maven plugin
                    Generate Dockerfiles
                    <!-->
                    <plugin>
                        <groupId>pt.ptinovacao.na-commons</groupId>
                        <artifactId>na-commons-maven-plugin-jinja</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>buildnumber-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>buildnumber-revision</id>
                                <phase>generate-resources</phase>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>pt.ptinovacao.na-commons</groupId>
                        <artifactId>na-commons-maven-plugin-yamldoc</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
