---
###### iam ######
#' {anchor:na_portal_iam_host}Identifies the hostname and port where the intended iam is running.
#' Port is always mandatory, even if it is the default.
#' $string
#' %mandatory
#' @ localhost:4432
#na_portal_iam_host:

#' Oauth client of NA Portal service.
#' $string
#' %mandatory
#' @ Wc2k9hhuT1iHKe3w5WJknw
#na_portal_iam_oauth_client:

#' NA Portal service IAM oauth client secret.
#' $string
#' %mandatory
#' @ zk0wdE3BeCQrrLdm8pl3ja
#na_portal_iam_oauth_secret:

#' NA Portal service keystore's secret.
#' If no value is defined, then a random UUID will be generated.
#' $string
#' %optional
#' @ 123456
#na_portal_iam_keystore_secret:

#' Identifies the hostname/servername where the intended iam is running.
#' If defined, the argument _-servername_ will be added to the openssl command used to download iam connection
#' certificate when [use_provided_iam_conn_cert_file|#use_provided_iam_conn_cert_file] is _False_.
#' $string
#' %optional
#' @ localhost
#na_portal_iam_server_name:

#' Na Portal Play HTTP 16 character-long secret key used for CSRF tokens and encryption.
#' If no value is defined, then a random UUID will be generated.
#' $string
#' %optional
#' @ d5bb6bfc-ce5c-4168-a147-9b2189f6f891
#na_portal_play_secret_key:

#' Na Portal login address that is capable of handling SAML messages.
#' Used also in csvs to indicate the service url.
#' $string
#' %mandatory
#' @ "https://localhost/na/loginPost"
#na_portal_iam_login_url:

#' IAM public certificate URL. If undefined, IAM public certificate URL will be built using
#' [na_portal_iam_host|#na_portal_iam_host].
#' $string
#' %optional
#' @ "localhost:443"
#iam_download_pem_url:

#' URL that NAPORTAL should use for IAM API, overwrites na_portal_iam_host variable.
#' *Note:* Changing this value is not recommended and should only be modified in advanced configuration scenarios, where the na_portal_iam_host can't be used to configure nossis-security.
#' $string
#' %optional
#' @ "localhost:443"
#iam_api_base_url:

#' IAM SAML IDP URL, overwrites na_portal_iam_host variable.
#' *Note:* Changing this value is not recommended and should only be modified in advanced configuration scenarios, where the na_portal_iam_host can't be used to configure nossis-security.
#' $string
#' %optional
#' @ "localhost:443"
#iam_idp_url:

###### LOGGING ######

#' Specifies the host where the graylog is running for wanted logs,
#' it's only mandatory when the graylog key is defined on the log4j2 dictionary.
#' $string
#' %mandatory
#' @ "localhost"
#graylog_host:

#' Specifies the port in which the graylog is running for wanted logs,
#' it's only mandatory when the graylog key is defined on the log4j2 dictionary.
#' $integer
#' %mandatory
#' @ "1233"
#graylog_port:

#' Specifies the environment in which the application is running. This information will be used at graylog and console,
#' therefore it's mandatory when the graylog key and/or console are defined on the log4j2 dictionary.
#' $string
#' %mandatory
#' @ "dev"
#application_environment:
