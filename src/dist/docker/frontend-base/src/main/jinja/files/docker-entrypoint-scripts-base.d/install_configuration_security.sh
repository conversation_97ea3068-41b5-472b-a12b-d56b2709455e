#!/bin/bash

set -e

CONF_FILE=/opt/alticelabs/na-portal/conf/security_variables.conf

if [ ! -f $CONF_FILE ]; then
        echo "Security configuration file was not found!"
        exit 1
fi

source $CONF_FILE

# import service certificate
[ -f "$KEYSTORE_PATH" ] && rm "$KEYSTORE_PATH"
# when certificate.pem contains private key it needs to be exported to another format so the keystore does not ignore the private key
if openssl pkcs12 -export -in "$CERT_PATH" -out /certificates/na-portal-iam-service-public-cert.p12 \
	-name "$SERVICE_ALIAS" -password pass:"$KEYSTORE_SECRET" &>/dev/null; then
    	echo "Private key exists in certificate"
    	keytool -importkeystore -noprompt -destkeystore "$KEYSTORE_PATH" \
    		-srckeystore /certificates/na-portal-iam-service-public-cert.p12 -srcstoretype PKCS12 -alias "$SERVICE_ALIAS" \
    		-srcstorepass "$KEYSTORE_SECRET" -deststorepass "$KEYSTORE_SECRET"
else
    echo "Private key does not exist in certificate"
    keytool -import -noprompt -keystore "$KEYSTORE_PATH" \
          -alias "$SERVICE_ALIAS" -storepass "$KEYSTORE_SECRET" -rfc \
          -file "$CERT_PATH"
fi

[ -z "$IAM_PEM_URL" ] && IAM_PEM_URL="https://$IAM_HOST/idp/iam.pem"
curl -X GET "$IAM_PEM_URL" --insecure -q > /certificates/iam.pem

# importing iam certificate
keytool -import -noprompt -keystore "$KEYSTORE_PATH" -alias iam \
      -storepass "$KEYSTORE_SECRET" -file /certificates/iam.pem

# download connection certificate
if [ "$USE_IAM_CONN_CERT" = "false" ]; then
  ARGUMENTS=(s_client -showcerts -connect "$IAM_HOST")
  if [ -n "$IAM_SERVER_NAME" ]; then
    ARGUMENTS+=(-servername "$IAM_SERVER_NAME")
  fi
  echo "Downloading iam connection certificate"
  openssl "${ARGUMENTS[@]}" < /dev/null | openssl x509 -outform PEM > "$IAM_CONN_CERT"
fi

# add iam certificate to JVM keystore
if keytool -list -keystore "$KEYSTORE_PATH" -alias "$IAM_HOST" -file "$IAM_CONN_CERT" -noprompt -storepass "$KEYSTORE_SECRET" &>/dev/null; then
  echo "$IAM_HOST Alias for iam certificate already exists in jre! Nothing more to add"
else
  echo "Add iam certificate with $IAM_HOST alias to jre"
  keytool -import -noprompt -keystore "$KEYSTORE_PATH" -storepass "$KEYSTORE_SECRET" \
      -alias "$IAM_HOST" -file "$IAM_CONN_CERT"
fi

# add BotSchool API certificate to JVM keystore
BOTSCHOOL_CERT_FILE="/tmp/botschool-api.pem"
if keytool -list -keystore "$KEYSTORE_PATH" -alias "botschool-api" -noprompt -storepass "$KEYSTORE_SECRET" &>/dev/null; then
  echo "BotSchool API certificate already exists in keystore! Nothing more to add"
else
  echo "Add BotSchool API certificate to keystore"
  openssl s_client -showcerts -connect api.ng.botschool.ai:443 -servername api.ng.botschool.ai < /dev/null 2>/dev/null | openssl x509 -outform PEM > "$BOTSCHOOL_CERT_FILE"
  keytool -import -noprompt -keystore "$KEYSTORE_PATH" -storepass "$KEYSTORE_SECRET" \
      -alias "botschool-api" -file "$BOTSCHOOL_CERT_FILE"
  rm -f "$BOTSCHOOL_CERT_FILE"
fi