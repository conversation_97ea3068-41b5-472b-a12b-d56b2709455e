# ----------------------------------------------------------------------------------------------------------------------
# -- This is repeated because it's "nailed" in the template from nossis-inv frontend base
# ----------------------------------------------------------------------------------------------------------------------

netwin.basemodule.results.label.title               = Gestão de dados

netwin.input.nomatches                              = sem resultados

netwin.dateTimePicker.dateFormat                    = YYYY-MM-DD
netwin.dateTimePicker.minDate                       = 2014-01-01
netwin.dateTimePicker.maxDate                       = 2099-12-31
netwin.dateTimePicker.separator                     = -

netwin.selectbox.placeholder                        = Selecione

netwin.project.modal.header.delete                  = Apagar
netwin.project.modal.body.delete                    = Deseja mesmo apagar esta instancia?
netwin.modal.navigate.button.confirm                = confirmar
netwin.modal.navigate.button.cancel                 = cancelar

netwin.attribute.boolean.no                         = Não
netwin.attribute.boolean.yes                        = Sim

netwin.splitter.hide                                = Minimizar
netwin.splitter.show                                = Maximizar

netwin.button.label.cancel                          = Cancelar
netwin.button.label.create                          = Criar
netwin.button.label.save                            = Guardar

netwin.basemodule.lateralFilter.title               = Filtros
netwin.basemodule.lateralSearch.title               = pesquisa
netwin.basemodule.search.search                     = Pesquisar
netwin.basemodule.search.clean                      = Limpar
netwin.basemodule.requiredFields                    = Campos de preenchimento obrigatório

netwin.datatables.action.label.view                 = consultar
netwin.datatables.action.label.add                  = adicionar
netwin.datatables.action.label.clone                = clonar
netwin.datatables.action.label.columnfilter         = gerir colunas
netwin.datatables.action.label.delete               = eliminar
netwin.datatables.action.label.edit                 = editar
netwin.datatables.action.label.remove               = eliminar
netwin.datatables.columnFilter.placeholder          = pesquisar
netwin.datatables.columnFilter.search.placeholder   = pesquisar...
netwin.datatables.action.label.moreoptions          = opções

netwin.notification.fuximissing.message             = Falha no carregamento de scripts de estilização
netwin.notification.fuximissing.title               = Fuxi em falta
netwin.notification.required                        = campo obrigatório
netwin.notification.regex                           = Formato ou valor inválido
netwin.notification.numeric                         = Introduza um valor numérico
netwin.notification.double                          = Introduza um valor numérico
netwin.notification.range.prefix                    = Valor tem de ser
netwin.notification.range.lessThan                  = menor ou igual a {0}
netwin.notification.range.greaterThan               = maior ou igual a {0}
netwin.notification.range.and                       = e
netwin.notification.length.prefix                   = Valor tem de ter no
netwin.notification.length.min                      = mínimo {0}
netwin.notification.length.and                      = e no
netwin.notification.length.max                      = máximo {0}
netwin.notification.length.character                = caracter
netwin.notification.length.characters               = caracteres