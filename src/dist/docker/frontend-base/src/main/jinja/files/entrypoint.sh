#!/bin/bash

set -e

DEFAULTS_INV_ARR=("/config/defaults-na-portal-frontend-base.yml" $DEFAULTS_INV)
CONFIGURATOR="/opt/alticelabs/na-commons/bin/na-commons-utils-jinja-configurator/jinja-configurator.py"
ENTRYPOINT_SCRIPTS_DIR=${ENTRYPOINT_SCRIPTS_DIR:-"/docker-entrypoint-scripts.d"}

AUTO_GEN_PORTAL_SECRET_KEY="$(uuidgen)" \
  python ${CONFIGURATOR} /config/all.yml /opt/alticelabs/na-portal/ /docker-entrypoint-scripts-base.d/ ${ENTRYPOINT_SCRIPTS_DIR} ${TEMPLATE_DIRS} \
    ${DEFAULTS_INV_ARR[@]/#/-d } || exit $?


# install product packages, if any exist
sh_files=($(find /docker-entrypoint-scripts-base.d -name "*.sh"))
if [ ${#sh_files[@]} -gt 0 ]; then
  echo "Entrypoint scripts files found in /docker-entrypoint-scripts-base.d, executing them..."
  for script in ${sh_files[@]}; do
    echo "$0: running $script"; $script
  done
else
  echo "No entrypoint scripts files found in /docker-entrypoint-scripts-base.d"
fi

# install solution packages, if any exist
sh_files=($(find $ENTRYPOINT_SCRIPTS_DIR -name "*.sh"))
if [ ${#sh_files[@]} -gt 0 ]; then
  echo "Entrypoint scripts files found in $ENTRYPOINT_SCRIPTS_DIR, executing them..."
  for script in ${sh_files[@]}; do
    echo "$0: running $script"; $script
  done
else
  echo "No entrypoint scripts files found in $ENTRYPOINT_SCRIPTS_DIR."
fi

CERT_FILE='/certificates/na-portal-iam-service-public-cert.pem'
if [ ! -f  $CERT_FILE ]; then
    echo "certificate file not found at $CERT_FILE"
    exit 1
fi

source /opt/alticelabs/na-portal/conf/daemon.conf

# start daemon
exec /usr/java/default/bin/java $JAVA_OPTS -cp $CP play.core.server.ProdServerStart "$CONF_DIR"