########################################## EXCEPTIONS ##########################################
#exception.<code>.title = EXCEPTION TITLE
#exception.<code>.user.message = EXCEPTION USER MESSAGE

exception.NA_00001.title = Serviço indisponível
exception.NA_00001.user.message = O Serviço encontra-se temporariamente indisponível. Tente mais tarde.
exception.NA_00002.title = Operação não concluída.
exception.NA_00002.user.message = Devido a um problema não foi possivel concluir a operação. Contate os administradores.
exception.NA_00003.title = Erro de configuração
exception.NA_00003.user.message = Devido a um erro de configuração, não é possível utilizar esta página.
exception.NA_00004.title = Erro no pedido
exception.NA_00004.user.message = Ocorreu um erro ao processar a resposta.
exception.NA_00005.title = Recurso inexistente
exception.NA_00005.user.message = O recurso que pretende não existe.
exception.NA_00006.title = Recurso existente
exception.NA_00006.user.message = O recurso que pretende salvar já existe.
exception.NA_00007.title = Erro no pedido
exception.NA_00007.user.message = Pedido com parâmetros inválidos.
exception.NA_00008.title = Erro
exception.NA_00008.user.message = Aconteceu um erro durante a operação.

# FILTER EXCEPTIONS
exception.NA_FILTER_00005.title = Filtro inexistente
exception.NA_FILTER_00005.user.message = O filtro que pretende não existe.
exception.NA_FILTER_00006.title = Filtro existente
exception.NA_FILTER_00006.user.message = O filtro que pretende salvar já existe.
exception.NA_FILTER_00007.title = Erro no pedido
exception.NA_FILTER_00007.user.message = Ocorreu um problema com o pedido executado.
exception.NA_FILTER_00008.title = Erro
exception.NA_FILTER_00008.user.message = Aconteceu um erro durante a operação.
exception.NA_FILTER_00009.title = Erro no pedido
exception.NA_FILTER_00009.user.message = Faltam campos obrigatórios no pedido submetido.
exception.NA_FILTER_00010.title = Filtro inválido
exception.NA_FILTER_00010.user.message = Não é possível criar um filtro vazio.