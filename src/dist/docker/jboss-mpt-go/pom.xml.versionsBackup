<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>na-portal-dist-docker</artifactId>
        <groupId>pt.ptinovacao.na-portal</groupId>
        <version>10.3.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>na-portal-dist-docker-jboss-mpt-go</artifactId>
    <packaging>pom</packaging>

    <properties>
        <docker.jbossas.version>10*</docker.jbossas.version>
        <docker.context.directory>${project.basedir}/target/jinja</docker.context.directory>
        <docker.svn.name>${application.name}</docker.svn.name>
        <docker.file>Dockerfile</docker.file>
        <docker.image.name>na-portal-jboss-mpt-go</docker.image.name>
        <docker.summary>NA Portal JBoss MPT/GO image</docker.summary>
        <docker.packager>${ud.key}</docker.packager>
        <docker.group>ptin/prod</docker.group>
        <docker.os>centos7</docker.os>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>runtime</scope>
        </dependency>
        <!-- jmx -->
        <dependency>
            <groupId>io.prometheus.jmx</groupId>
            <artifactId>jmx_prometheus_javaagent</artifactId>
            <scope>runtime</scope>
        </dependency>
        <!-- na-portal -->
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-commons-model</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-mpt-go-bundle</artifactId>
            <scope>runtime</scope>
            <type>ear</type>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-core-request-engine-bundle</artifactId>
            <scope>runtime</scope>
            <type>war</type>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>docker</id>
            <build>
                <plugins>
                    <!-- check for required properties -->
                    <plugin>
                        <artifactId>maven-enforcer-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>check-required-docker-properties</id>
                                <phase>prepare-package</phase>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- Exploded zip will place hibernate ogm libs to the build target dir and fetch necessary libs -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-dependency-plugin</artifactId>
                        <version>${version.maven-dependency-plugin}</version>
                        <executions>
                            <execution>
                                <id>copy-hibernate-wildfly10-modules</id>
                                <phase>validate</phase>
                                <goals>
                                    <goal>unpack</goal>
                                </goals>
                                <configuration>
                                    <artifactItems>
                                        <artifactItem>
                                            <groupId>org.hibernate.ogm</groupId>
                                            <artifactId>hibernate-ogm-modules-wildfly10</artifactId>
                                            <version>${version.org.hibernate.ogm}</version>
                                            <type>zip</type>
                                            <overWrite>false</overWrite>
                                            <outputDirectory>${project.build.directory}/jinja/files/na-portal/conf/modules</outputDirectory>
                                            <!-- NOTE: For other OGM stores developer should enable them here and do a proper package -->
                                            <excludes>**/ogm/cassandra/**, **/ogm/couchdb/**,**/ogm/ehcache/**,**/ogm/infinispan/**,**/ogm/neo4j/**</excludes>
                                        </artifactItem>
                                    </artifactItems>
                                </configuration>
                            </execution>
                            <execution>
                                <id>copy-external-dependencies</id>
                                <phase>prepare-package</phase>
                                <goals>
                                    <goal>copy-dependencies</goal>
                                </goals>
                                <configuration>
                                    <outputDirectory>${project.build.directory}/jinja/files/na-portal/lib</outputDirectory>
                                    <includeScope>runtime</includeScope>
                                    <stripVersion>false</stripVersion>
                                    <excludeGroupIds>${project.groupId}</excludeGroupIds>
                                </configuration>
                            </execution>
                            <execution>
                                <id>copy-project-dependencies</id>
                                <phase>prepare-package</phase>
                                <goals>
                                    <goal>copy-dependencies</goal>
                                </goals>
                                <configuration>
                                    <outputDirectory>${project.build.directory}/jinja/files/na-portal/lib</outputDirectory>
                                    <includeScope>runtime</includeScope>
                                    <stripVersion>true</stripVersion>
                                    <includeGroupIds>${project.groupId}</includeGroupIds>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>

                    <!-- build and push image -->
                    <plugin>
                        <groupId>com.spotify</groupId>
                        <artifactId>dockerfile-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>default</id>
                            </execution>
                        </executions>
                    </plugin>

                    <!-->
                    Jinja maven plugin
                    Generate Dockerfiles
                    <!-->
                    <plugin>
                        <groupId>pt.ptinovacao.na-commons</groupId>
                        <artifactId>na-commons-maven-plugin-jinja</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>documentation</id>
            <build>
                <plugins>
                    <!-->
                    Jinja maven plugin
                    Generate Dockerfiles
                    <!-->
                    <plugin>
                        <groupId>pt.ptinovacao.na-commons</groupId>
                        <artifactId>na-commons-maven-plugin-jinja</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>buildnumber-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>buildnumber-revision</id>
                                <phase>generate-resources</phase>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>pt.ptinovacao.na-commons</groupId>
                        <artifactId>na-commons-maven-plugin-yamldoc</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>