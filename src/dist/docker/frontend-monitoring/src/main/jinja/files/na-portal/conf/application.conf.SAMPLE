include "nossisaaapi.conf"
include "monitoring-app.conf"
include "monitoring-ssl.conf"
include "base-app.conf"

# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# This is the main configuration file for the application
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

# ~~~~~~~~~~~~
# Play configs
# ~~~~~~~~~~~~

play {

  akka.jvm-exit-on-fatal-error = off
  temp = /tmp

  ws {
    # Enable compression in WS API
    compressionEnabled = true

    # Override default timeout (120 seconds) to 30 minutes, specific timeouts will be set on services
    # this value should be enough to download a 300MB bulk file.
    # Value in milliseconds
    timeout.connection = 120s
    timeout.idle = 1800s
    timeout.request = 1800s
  }

}

play.modules.enabled += "na.monitoring.settings.MonitoringModule"

# The application languages
play.i18n.langs = [ {{ PORTAL_LANGS }} ]


play.filters.enabled=[]

# Messages
nossis.messages {
  resources.path = [
    "/opt/alticelabs/na-portal/conf/i18n/product",
    "/opt/alticelabs/na-portal/conf/i18n/customization"
  ]
}

healthcheck {
  target.router = monitoring.Routes
}

play.http {

  router = na.router.RouterWithHealthCheck

  errorHandler = "na.naportalbase.settings.NaErrorHandler"

  context = "{{ na_portal_path_prefix }}"
  contextApp = "{{ na_portal_path_prefix }}/monitoring"

  secret.key = "{{ na_portal_play_secret_key | default(AUTO_GEN_PORTAL_SECRET_KEY) }}"

  # Session configuration
  session = {

    # The cookie name
    cookieName = "PLAY_SESSION"

    # Whether the secure attribute of the cookie should be set to true
    secure = true

    # The max age to set on the cookie.
    # If null, the cookie expires when the user closes their browser.
    # An important thing to note, this only sets when the browser will discard the cookie.
    maxAge = null

    # Whether the HTTP only attribute of the cookie should be set to true
    httpOnly = true

    # The value of the SameSite attribute of the cookie. Set to null for no SameSite attribute.
    sameSite = "lax"

    # The domain to set on the session cookie
    # If null, does not set a domain on the session cookie.
    domain = null

    # The session path
    # Must start with /.
    path = ${play.http.context}

    jwt {
      # The JWT signature algorithm to use on the session cookie
      # uses 'alg' https://tools.ietf.org/html/rfc7515#section-4.1.1
      signatureAlgorithm = "HS256"

      # The time after which the session is automatically invalidated.
      # Use 'exp' https://tools.ietf.org/html/rfc7519#section-4.1.4
      expiresAfter = ${play.http.session.maxAge}

      # The amount of clock skew to accept between servers when performing date checks
      # If you have NTP or roughtime synchronizing between servers, you can enhance
      # security by tightening this value.
      clockSkew = 5 minutes

      # The claim key under which all user data is stored in the JWT.
      dataClaim = "data"
    }
  }
}

