# SSL configuration for monitoring module
# This configuration uses the same keystore pattern as other modules

play.ws.ssl {
  # Use the IAM keystore which includes all trusted certificates
  trustManager = {
    stores = [
      { type = "JKS", path = "/keystore/iam-keystore.jks", password = "{{ na_portal_iam_keystore_secret | default(AUTO_GEN_PORTAL_SECRET_KEY) }}" }
    ]
  }
  
  # Enable hostname verification for production
  loose {
    acceptAnyCertificate = false
    allowWeakCiphers = false
    allowWeakProtocols = false
    allowLegacyHelloMessages = false
    allowUnsafeRenegotiation = false
    disableHostnameVerification = false
  }
}
