na.monitoring.backend.host = {{ na_portal_monitoring_order_followup_backend_host }}
na.monitoring.backend.port = {{ na_portal_monitoring_order_followup_backend_port }}

na.monitoring.backend.swe.host = {{ na_portal_monitoring_swe_backend_host }}
na.monitoring.backend.swe.port = {{ na_portal_monitoring_swe_backend_port }}

# states
na.monitoring.states = [
  { value: VALIDATING,   text: na.portal.monitoring.home.filters.state.validating,   icon: badge-validating,    execution: none }
  { value: ACKNOWLEDGED, text: na.portal.monitoring.home.filters.state.acknowledged, icon: badge-acknowledged,  execution: none }
  { value: CANCELLED,    text: na.portal.monitoring.home.filters.state.canceled,     icon: badge-canceled,      execution: error }
  { value: HELD,         text: na.portal.monitoring.home.filters.state.held,         icon: badge-held,          execution: error }
  { value: FAILED,       text: na.portal.monitoring.home.filters.state.failed,       icon: badge-held,          execution: error }
  { value: IN_PROGRESS,  text: na.portal.monitoring.home.filters.state.in.progress,  icon: badge-in-progress,   execution: executing }
  { value: PARTIAL,      text: na.portal.monitoring.home.filters.state.partial,      icon: badge-partial,       execution: executing }
  { value: PENDING,      text: na.portal.monitoring.home.filters.state.pending,      icon: badge-pending,       execution: executing }
  { value: REJECTED,     text: na.portal.monitoring.home.filters.state.rejected,     icon: badge-rejected,      execution: none }
  { value: COMPLETED,    text: na.portal.monitoring.home.filters.state.completed,    icon: badge-completed,     execution: success }
]

# External systems whitelist
{% if na_portal_monitoring_external_systems_whitelist is defined %}
na.monitoring.order.create.system.whitelist = {{ na_portal_monitoring_external_systems_whitelist }}
{% endif %}

# Operation version states allowed to create monitoring orders, the value should be list of states
# Possible states: DEVELOPMENT, TESTING, ABANDONED, PRODUCTION, BLOCKED, DEPRECATED
na.monitoring.order.create.allowedVersionStates = [TESTING, DEPRECATED, PRODUCTION]

#filter by
na.monitoring.search.date.filterby = [ systemEntry, lastUpdate ]

na.monitoring.possible.order.actions = [ cancel, remove, skip, retry, rollback, cancelWithRollback ]

na.monitoring.statespriority = [ HELD, IN_PROGRESS, COMPLETED ]


# validation error codes to determine if validation response has a structure or signature error
na.monitoring.validation.structure.errorcode = "CL_01000"
na.monitoring.validation.signature.errorcode = "CL_02000"

# Default daterangepicker interval: LAST_30_MINUTES, LAST_HOUR, LAST_12_HOURS, LAST_24_HOURS, LAST_WEEK, LAST_MONTH
# Fallback: LAST_24_HOURS
na.monitoring.datetimepicker.default.interval = {{ na_portal_monitoring_default_date_interval }}

# Service Locator config file path
# Fallback: /opt/alticelabs/na-portal/conf/cluster-client-config.yml
na.monitoring.service.locator.configfile = {{ na_portal_monitoring_service_locator_configfile }}

{% if na_portal_monitoring_requester_callback_url is defined %}
na.monitoring.requesterCallback.url = "{{ na_portal_monitoring_requester_callback_url }}"
{% else %}
na.monitoring.requesterCallback.base.url = "{{ na_portal_monitoring_requester_callback_base_url }}"
{% endif %}

{% if na_portal_monitoring_feature_whitelist is defined %}
na.monitoring.features.whitelist = {{ na_portal_monitoring_feature_whitelist }}
{% endif %}

{% if na_portal_monitoring_feature_blacklist is defined %}
na.monitoring.features.blacklist = {{ na_portal_monitoring_feature_blacklist }}
{% endif %}

na.monitoring.tables = { include "tables.conf" }

# BOT School API Configuration
na.monitoring.botschool.api.url = "{{ na_portal_monitoring_botschool_api_url }}"
na.monitoring.botschool.api.key = "{{ na_portal_monitoring_botschool_api_key }}"
na.monitoring.botschool.prompt.template = "{{ na_portal_monitoring_botschool_prompt_template }}"