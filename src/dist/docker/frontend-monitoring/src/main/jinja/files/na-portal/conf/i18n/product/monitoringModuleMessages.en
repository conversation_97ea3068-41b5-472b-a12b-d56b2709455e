na.portal.monitoring.module.title                                               = Monitoring
na.portal.monitoring.module.description                                         = The monitoring module provides a set of tools so the user can monitor the execution of the Service Orders received by the system. It allows the search based on a set of predefined criteria, to execute actions on Service Orders that are not in a final state and also to resend notifications associated to the state transitions or that exist in the workflows. It also allows the creation of new Service Orders based on the operations and entities created on the Catalog module.

na.portal.monitoring.nav.context                                                = Monitoring

na.portal.monitoring.home.title                                                 = Monitoring
na.portal.monitoring.home.description                                           = Monitoring module

# filters

na.portal.monitoring.home.filters.search                                        = Search
na.portal.monitoring.home.filters.search.by.id                                  = search by id
na.portal.monitoring.home.filters.search.with.multiple.fields                   = search with multiple fields
na.portal.monitoring.home.filters.id.search.orderId                             = Order ID
na.portal.monitoring.home.filters.id.search.serviceId                           = Service ID
na.portal.monitoring.home.filters.id.search.input.placeholder.orderId           = Type the order ID you wish to check
na.portal.monitoring.home.filters.id.search.input.placeholder.serviceId         = Type the service ID you wish to check

na.portal.monitoring.home.filters.add.fields                                    = add fields
na.portal.monitoring.home.filters.need.to.refresh                               = you need to refresh to update the last 24 hours
na.portal.monitoring.home.filters.more                                          = more
na.portal.monitoring.home.filters.less                                          = less
na.portal.monitoring.home.filters.all                                           = all

na.portal.monitoring.home.filters.items.state                                   = State
na.portal.monitoring.home.filters.items.category                                = Category
na.portal.monitoring.home.filters.items.system                                  = System
na.portal.monitoring.home.filters.items.operation                               = Operation
na.portal.monitoring.home.filters.items.service.type                            = Service type
na.portal.monitoring.home.filters.items.response.code                           = Response code
na.portal.monitoring.home.filters.items.response.message                        = Response message

na.portal.monitoring.home.filters.state.validating                              = validating
na.portal.monitoring.home.filters.state.acknowledged                            = acknowledged
na.portal.monitoring.home.filters.state.canceled                                = cancelled
na.portal.monitoring.home.filters.state.held                                    = held
na.portal.monitoring.home.filters.state.in.progress                             = in progress
na.portal.monitoring.home.filters.state.partial                                 = partial
na.portal.monitoring.home.filters.state.pending                                 = pending
na.portal.monitoring.home.filters.state.rejected                                = rejected
na.portal.monitoring.home.filters.state.completed                               = completed
na.portal.monitoring.home.filters.state.failed                                  = failed

na.portal.monitoring.home.filters.system.na                                     = Network Activator
na.portal.monitoring.home.filters.system.om                                     = Order Manager

# date range picker

na.portal.monitoring.datarangepicker.message.refresh                            = you need to refresh to update the {0}
na.portal.monitoring.datarangepicker.message.LAST_30_MINUTES                    = last 30 minutes
na.portal.monitoring.datarangepicker.message.LAST_HOUR                          = last hour
na.portal.monitoring.datarangepicker.message.LAST_12_HOURS                      = last 12 hours
na.portal.monitoring.datarangepicker.message.LAST_24_HOURS                      = last 24 hours
na.portal.monitoring.datarangepicker.message.LAST_WEEK                          = last week
na.portal.monitoring.datarangepicker.message.LAST_MONTH                         = last month

na.portal.monitoring.datarangepicker.LAST_30_MINUTES                            = Last 30 minutes
na.portal.monitoring.datarangepicker.LAST_HOUR                                  = Last hour
na.portal.monitoring.datarangepicker.LAST_12_HOURS                              = Last 12 hours
na.portal.monitoring.datarangepicker.LAST_24_HOURS                              = Last 24 hours
na.portal.monitoring.datarangepicker.LAST_WEEK                                  = Last week
na.portal.monitoring.datarangepicker.LAST_MONTH                                 = Last month
na.portal.monitoring.datarangepicker.CUSTOM_RANGE                               = Custom range

na.portal.monitoring.datarangepicker.dateformat                                 = MM/DD/YYYY HH:mm:ss
na.portal.monitoring.datarangepicker.applyButton                                = apply
na.portal.monitoring.datarangepicker.cancelButton                               = cancel

na.portal.monitoring.datarangepicker.sunday                                     = Sun
na.portal.monitoring.datarangepicker.monday                                     = Mon
na.portal.monitoring.datarangepicker.tuesday                                    = Tue
na.portal.monitoring.datarangepicker.wednesday                                  = Wed
na.portal.monitoring.datarangepicker.thursday                                   = Thu
na.portal.monitoring.datarangepicker.friday                                     = Fri
na.portal.monitoring.datarangepicker.saturday                                   = Sat

na.portal.monitoring.datarangepicker.january                                    = January
na.portal.monitoring.datarangepicker.february                                   = February
na.portal.monitoring.datarangepicker.march                                      = March
na.portal.monitoring.datarangepicker.april                                      = April
na.portal.monitoring.datarangepicker.may                                        = May
na.portal.monitoring.datarangepicker.june                                       = June
na.portal.monitoring.datarangepicker.july                                       = July
na.portal.monitoring.datarangepicker.august                                     = August
na.portal.monitoring.datarangepicker.september                                  = September
na.portal.monitoring.datarangepicker.october                                    = October
na.portal.monitoring.datarangepicker.november                                   = November
na.portal.monitoring.datarangepicker.december                                   = December

# orders table

na.portal.monitoring.orders.table.order.id                                      = Order ID
na.portal.monitoring.orders.table.category                                      = Category
na.portal.monitoring.orders.table.description                                   = Description
na.portal.monitoring.orders.table.state                                         = State
na.portal.monitoring.orders.table.customer                                      = Customer
na.portal.monitoring.orders.table.technician                                    = Technician
na.portal.monitoring.orders.table.system                                        = System
na.portal.monitoring.orders.table.system.entry                                  = System entry
na.portal.monitoring.orders.table.update.last                                   = Last Update
na.portal.monitoring.orders.table.actions                                       = Actions
na.portal.monitoring.orders.table.actions.options                               = options
na.portal.monitoring.orders.table.error.message.single.item                     = {0} in order item called {1} from {2}
na.portal.monitoring.orders.table.error.message.multiple.items                  = <b> There are {0} order items in failure</b>. See in context what happened.
na.portal.monitoring.orders.table.info.message.single.item                      = {0} in order item called {1} from {2}
na.portal.monitoring.orders.table.info.message.multiple.items                   = <b> There are {0} order items with messages</b>. See in context what happened.
na.portal.monitoring.orders.table.info.message.validating.order                 = Order is being validated

# orders item table

na.portal.monitoring.order.items.table.operation.service                        = Operation / Service
na.portal.monitoring.order.items.table.description                              = Description
na.portal.monitoring.order.items.table.state                                    = State
na.portal.monitoring.order.items.table.has.process                              = Has process?
na.portal.monitoring.order.items.table.service.id                               = Service ID
na.portal.monitoring.order.items.table.actions                                  = Actions

# order item resume

na.portal.monitoring.order.item.resume.table.operation.category                 = Operation / Category
na.portal.monitoring.order.item.resume.table.service.id                         = Service ID
na.portal.monitoring.order.item.resume.table.state                              = State
na.portal.monitoring.order.item.resume.table.duration                           = Duration
na.portal.monitoring.order.item.resume.table.end.date                           = Last Update
na.portal.monitoring.order.item.resume.table.has.process                        = Has process
na.portal.monitoring.order.item.resume.table.actions                            = Actions

# orders state

na.portal.monitoring.order.state.sent                                           = sent
na.portal.monitoring.order.state.failed                                         = failed

# orders response retry table

na.portal.monitoring.order.response.retry.table.retry.number                    = #
na.portal.monitoring.order.response.retry.table.date.time                       = Date / hour
na.portal.monitoring.order.response.retry.table.state                           = State
na.portal.monitoring.order.response.retry.table.state.sent                      = sent
na.portal.monitoring.order.response.retry.table.state.failed                    = failed
na.portal.monitoring.order.response.retry.table.message                         = Message

# orders response page

na.portal.monitoring.order.response.label.response                              = Response
na.portal.monitoring.order.response.label.destination                           = To
na.portal.monitoring.order.response.label.orderHistoryCheck                     = You can check the <b>history</b>.
na.portal.monitoring.order.response.label.responseSuccess                       = Response was successfully sent.
na.portal.monitoring.order.response.label.delivering                            = Delivering response.
na.portal.monitoring.order.response.label.responseFailed                        = Response failed. Error {0}.
na.portal.monitoring.order.response.tooltip.seeTable                            = See in table
na.portal.monitoring.order.response.tooltip.seeCode                             = See in code
na.portal.monitoring.order.activity.response.label.responseFailed               = Response failed. Error <b>{0}</b>.
na.portal.monitoring.order.activity.response.label.inProgress                   = Reponse in progress.
na.portal.monitoring.order.activity.response.label.success                      = Response was successfully sent.
na.portal.monitoring.order.button.summary                                       = Summarize
na.portal.monitoring.order.summary.modal.title                                  = Order summary

# orders notification details table

na.portal.monitoring.order.notification.details.table.errorIssues               = {0} of {1} had error issues.
na.portal.monitoring.order.notification.details.table.system                    = System
na.portal.monitoring.order.notification.details.table.systems                   = Systems
na.portal.monitoring.order.notification.details.table.attempt                   = Attempt
na.portal.monitoring.order.notification.details.table.attempts                  = Attempts
na.portal.monitoring.order.notification.details.table.dateTime                  = Date
na.portal.monitoring.order.notification.details.table.type                      = Type
na.portal.monitoring.order.notification.details.table.summary                   = Summary
na.portal.monitoring.order.notification.details.table.state                     = State
na.portal.monitoring.order.notification.details.table.logs_history              = Logs History
na.portal.monitoring.order.notification.details.table.state.success             = Success
na.portal.monitoring.order.notification.details.table.state.error               = Error

# orders validation page

na.portal.monitoring.order.validation.response.label.response                   = Information model
na.portal.monitoring.order.validation.messages.table.code                       = Code
na.portal.monitoring.order.validation.messages.table.message                    = Message
na.portal.monitoring.order.validation.messages.table.description                = Description
na.portal.monitoring.order.validation.messages.global                           = <b>{0} ({1} - {2}).</b> See below for more details.

# orders

na.portal.monitoring.order.from.system                                          = from
na.portal.monitoring.order.status.validation                                    = Validation
na.portal.monitoring.order.status.execution                                     = Execution
na.portal.monitoring.order.status.response                                      = Response
na.portal.monitoring.order.status.notifications                                 = Notifications

na.portal.monitoring.order.date.time.second                                     = second
na.portal.monitoring.order.date.time.seconds                                    = seconds
na.portal.monitoring.order.date.time.minute                                     = minute
na.portal.monitoring.order.date.time.minutes                                    = minutes
na.portal.monitoring.order.date.time.hour                                       = hour
na.portal.monitoring.order.date.time.hours                                      = hours
na.portal.monitoring.order.date.time.day                                        = day
na.portal.monitoring.order.date.time.days                                       = days
na.portal.monitoring.order.date.time.week                                       = week
na.portal.monitoring.order.date.time.weeks                                      = weeks
na.portal.monitoring.order.date.time.month                                      = one month ago
na.portal.monitoring.order.date.time.months                                     = months

na.portal.monitoring.order.date.day.today                                       = today
na.portal.monitoring.order.date.day.yesterday                                   = yesterday
na.portal.monitoring.order.date.weekday.0                                       = sunday
na.portal.monitoring.order.date.weekday.1                                       = monday
na.portal.monitoring.order.date.weekday.2                                       = tuesday
na.portal.monitoring.order.date.weekday.3                                       = wednesday
na.portal.monitoring.order.date.weekday.4                                       = thursday
na.portal.monitoring.order.date.weekday.5                                       = friday
na.portal.monitoring.order.date.weekday.6                                       = saturday

na.portal.monitoring.order.date.format.full                                     = {0} at {1}h
na.portal.monitoring.order.date.format.short                                    = {0} {1} ago
na.portal.monitoring.order.date.format.duration                                 = {0} {1}
na.portal.monitoring.order.date.format.relativeTime                             = {0}, on {2} {1}

# Order activity

na.portal.monitoring.order.tooltip.skip                                         = skip
na.portal.monitoring.order.tooltip.rollback                                     = rollback
na.portal.monitoring.order.tooltip.retry                                        = retry
na.portal.monitoring.order.tooltip.retry_new_request                            = retry
na.portal.monitoring.order.tooltip.restart                                      = restart
na.portal.monitoring.order.tooltip.report                                       = report
na.portal.monitoring.order.tooltip.complete                                     = complete

# Order activity external System interaction
na.portal.monitoring.order.ext.sys.interaction.origin                         = From
na.portal.monitoring.order.ext.sys.interaction.destination                    = To
na.portal.monitoring.order.ext.sys.interaction.date                           = Date
na.portal.monitoring.order.ext.sys.interaction.details                        = Details
na.portal.monitoring.order.ext.sys.interaction.request                        = Request
na.portal.monitoring.order.ext.sys.interaction.sync.response                  = Sync response
na.portal.monitoring.order.ext.sys.interaction.async.response                 = Async Response

# modal

na.portal.monitoring.order.modal.button.complete                                = Complete
na.portal.monitoring.order.modal.button.retry                                   = Retry
na.portal.monitoring.order.modal.button.skip                                    = Skip
na.portal.monitoring.order.modal.button.rollback                                = Rollback
na.portal.monitoring.order.modal.button.cancel                                  = Cancel
na.portal.monitoring.order.modal.button.confirm                                 = Confirm
na.portal.monitoring.order.modal.button.restart                                 = Restart
na.portal.monitoring.order.modal.label.skip                                     = Tell us why you skip this activity
na.portal.monitoring.order.modal.label.rollback                                 = Tell us why you did the rollback
na.portal.monitoring.order.modal.label.retry                                    = Tell us why you did the retry
na.portal.monitoring.order.modal.label.complete                                 = Tell us why did you completed the activity
na.portal.monitoring.order.modal.label.restart                                  = Tell us why you did the restart
na.portal.monitoring.order.modal.label.finish                                   = Tell us why did you finished the order
na.portal.monitoring.order.modal.label.cancel                                   = Tell us why did you cancel the order
na.portal.monitoring.order.modal.label.cancelWithRollback                       = Tell us why did you cancel the order
na.portal.monitoring.order.modal.label.remove                                   = Tell us why did you remove the order
na.portal.monitoring.order.modal.title.skip                                     = Skip {0}
na.portal.monitoring.order.modal.title.rollback                                 = Rollback {0}
na.portal.monitoring.order.modal.title.retry                                    = Retry {0}
na.portal.monitoring.order.modal.title.complete                                 = Complete {0}
na.portal.monitoring.order.modal.title.restart                                  = Restart {0}
na.portal.monitoring.order.modal.title.skip.individual							= Skip {0}
na.portal.monitoring.order.modal.title.skip.massive								= Skip orders
na.portal.monitoring.order.modal.title.rollback.individual					    = Rollback {0}
na.portal.monitoring.order.modal.title.rollback.massive							= Rollback orders
na.portal.monitoring.order.modal.title.retry.individual							= Retry {0}
na.portal.monitoring.order.modal.title.retry.massive							= Retry orders
na.portal.monitoring.order.modal.title.cancel.individual                        = Cancel {0}
na.portal.monitoring.order.modal.title.cancel.massive                           = Cancel orders
na.portal.monitoring.order.modal.title.cancelWithRollback.individual            = Cancel and rollback {0}
na.portal.monitoring.order.modal.title.cancelWithRollback.massive               = Cancel and rollback orders
na.portal.monitoring.order.modal.title.remove.individual                        = Remove {0}
na.portal.monitoring.order.modal.title.remove.massive                           = Remove orders
na.portal.monitoring.order.modal.title.report                                   = Notes and actions about {0}
na.portal.monitoring.order.modal.emptyNotes                                     = (empty notes)
na.portal.monitoring.order.modal.tab.responseDetail                             = Response detail
na.portal.monitoring.order.modal.tab.response                                   = Response
na.portal.monitoring.order.modal.tab.request                                    = Request
na.portal.monitoring.order.modal.tab.history                                    = History

na.portal.monitoring.orders.modal.alert.partial.action.title                   = Action will be partially applied to these orders
na.portal.monitoring.orders.modal.alert.partial.action.text                    = Some orders will not be processed as their current state is incompatible with this action.

na.portal.monitoring.notifications.modals.alert.confirm.retry.system.title              = Retry notification
na.portal.monitoring.notifications.modals.alert.confirm.retry.system.content            = Are you sure you want to notify this system again?
na.portal.monitoring.notifications.modals.alert.confirm.retry.all.systems.title         = Retry notifications
na.portal.monitoring.notifications.modals.alert.confirm.retry.all.systems.content       = Are you sure you want to notify all systems again?
na.portal.monitoring.notifications.modals.details.error                                 = Failed Response.
na.portal.monitoring.notifications.modals.details.success                               = Response sent successfully.

# external system interactions
na.portal.monitoring.ext.sys.interactions.table.datetime                        = Date / hour
na.portal.monitoring.ext.sys.interactions.table.description                     = Description
na.portal.monitoring.ext.sys.interactions.table.author                          = Made by
na.portal.monitoring.ext.sys.interactions.table.actions                         = Actions
na.portal.monitoring.ext.sys.interactions.table.raw.data                        = Raw data
na.portal.monitoring.ext.sys.interactions.details                               = Details

na.portal.monitoring.ext.sys.interactions.filter.title                          = You can filter data by
na.portal.monitoring.ext.sys.interactions.filter.task.manual                    = Manual tasks
na.portal.monitoring.ext.sys.interactions.filter.task.automatic                 = Automatic tasks
na.portal.monitoring.ext.sys.interactions.filter.notifications                  = Notifications

na.portal.monitoring.ext.sys.tab.execution                                      = Execution
na.portal.monitoring.ext.sys.tab.history                                        = History

# splitter

na.portal.monitoring.splitter.tip.close                                         = Close
na.portal.monitoring.splitter.tip.open                                          = Open
na.portal.monitoring.splitter.tip.resize                                        = Resize
na.portal.monitoring.splitter.tip.slide                                         = Slide Open

# notification

na.portal.monitoring.action.complete.success.title                              = Activity finished successfully
na.portal.monitoring.action.complete.success.content                            = Activity "{1}" finished successfully
na.portal.monitoring.action.success.title                                       = {0} executed successfully
na.portal.monitoring.action.success.content                                     = {0} action executed successfully on activity "{1}"
na.portal.monitoring.order.action.skip.success.title                            = Order successfully skipped
na.portal.monitoring.order.action.skip.success.content                          = Skip order {0} action performed successfully
na.portal.monitoring.order.action.rollback.success.title                        = Order successfully rollback
na.portal.monitoring.order.action.rollback.success.content                      = Rollback order {0} action performed successfully
na.portal.monitoring.order.action.remove.success.title                          = Order successfully removed
na.portal.monitoring.order.action.remove.success.content                        = Remove order {0} action performed successfully
na.portal.monitoring.order.massive.action.skip.success.title                    = Orders successfully skipped
na.portal.monitoring.order.massive.action.skip.success.content                  = Skip order action performed successfully
na.portal.monitoring.order.massive.action.skip.partial.title                    = Orders partially skipped
na.portal.monitoring.order.massive.action.skip.partial.content                  = Skip order action performed partially
na.portal.monitoring.order.massive.action.rollback.success.title                = Orders successfully rollback
na.portal.monitoring.order.massive.action.rollback.success.content              = Rollback order action performed successfully
na.portal.monitoring.order.massive.action.rollback.partial.title                = Orders partially rollback
na.portal.monitoring.order.massive.action.rollback.partial.content              = Rollback order action performed partially
na.portal.monitoring.order.action.retry.success.title                           = Order successfully retried
na.portal.monitoring.order.action.retry.success.content                         = Retry order {0} action performed successfully
na.portal.monitoring.order.massive.action.retry.success.title                   = Orders successfully retried
na.portal.monitoring.order.massive.action.retry.success.content                 = Retry order action performed successfully
na.portal.monitoring.order.massive.action.retry.partial.title                   = Orders partially retried
na.portal.monitoring.order.massive.action.retry.partial.content                 = Retry order action performed partially
na.portal.monitoring.order.action.cancel.success.title                          = Order successfully canceled
na.portal.monitoring.order.action.cancel.success.content                        = Cancel order {0} action performed successfully
na.portal.monitoring.order.massive.action.cancel.success.title                  = Orders successfully canceled
na.portal.monitoring.order.massive.action.cancel.success.content                = Cancel order action performed successfully
na.portal.monitoring.order.massive.action.cancel.partial.title                  = Orders partially canceled
na.portal.monitoring.order.massive.action.cancel.partial.content                = Cancel order action performed partially
na.portal.monitoring.order.massive.action.remove.success.title                  = Orders successfully removed
na.portal.monitoring.order.massive.action.remove.success.content                = Remove order action performed successfully
na.portal.monitoring.order.massive.action.remove.partial.title                  = Orders partially removed
na.portal.monitoring.order.massive.action.remove.partial.content                = Remove order action performed partially
na.portal.monitoring.order.massive.action.invalid.states.title                  = Massive Action: Invalid States
na.portal.monitoring.order.massive.action.selected.invalid                      = Selected order is in an invalid state for this action
na.portal.monitoring.order.massive.action.all.selected.invalid                  = All selected orders are in an invalid state for this action
na.portal.monitoring.order.select.all.notification.title                        = Selected orders
na.portal.monitoring.order.massive.action.failed.orders                         = Action not applied to {0} order(s)
na.portal.monitoring.order.select.all.notification.content                      = {0} / {1} Selected orders
na.portal.monitoring.order.select.all.notification.select.all                   = select all
na.portal.monitoring.order.select.all.notification.unselect.all                 = unselect all
na.portal.monitoring.order.view.timeline.graph.not.found.title                  = Graph and timeline not found
na.portal.monitoring.order.view.timeline.graph.not.found.message                = Graph and timeline is not created yet, try again later
na.portal.monitoring.order.view.timeline.graph.unexpected.error.title           = An unexpected error has occurred!
na.portal.monitoring.order.view.timeline.graph.unexpected.error.message         = Could not load graph and timeline
na.portal.monitoring.order.systems.notification.retry.success.title             = Successfully notified systems.
na.portal.monitoring.order.systems.notification.retry.success.content           = All systems of notification "{0}" successfully notified.
na.portal.monitoring.order.system.notification.retry.success.title              = Successfully notified system.
na.portal.monitoring.order.system.notification.retry.success.content            = System "{0}" successfully notified.

# timeline notification

na.portal.monitoring.timeline.activity.notification.system.retry            = Retry
na.portal.monitoring.timeline.activity.notification.system.retry.title      = Retry Sent
na.portal.monitoring.timeline.activity.notification.system.retry.content    = Notification {0} retry sent to system {1}.
na.portal.monitoring.timeline.activity.notification.resume                  = 1 system
na.portal.monitoring.timeline.activity.notification.resume.plural           = {0} systems
na.portal.monitoring.timeline.activity.notification.resume.missing          = No notification information available.
na.portal.monitoring.timeline.activity.notification.resume.issues           = {0} of {1} had error issues.

# search date filter by

na.portal.monitoring.search.date.filterby                                       = filter by
na.portal.monitoring.search.date.filterby.systemEntry                           = system entry
na.portal.monitoring.search.date.filterby.lastUpdate                            = last update

# operations

na.portal.monitoring.button.bulkoperations.group                  = Select desired action
na.portal.monitoring.bulkoperations.button                        = bulk operations
na.portal.monitoring.order.possible.actions                       = possible actions
na.portal.monitoring.order.action.cancel                          = cancel
na.portal.monitoring.order.action.complete                        = complete
na.portal.monitoring.order.action.retry                           = retry
na.portal.monitoring.order.action.skip                            = skip
na.portal.monitoring.order.action.rollback                        = rollback
na.portal.monitoring.order.action.cancelWithRollback              = cancel and rollback
na.portal.monitoring.order.action.remove                          = remove
na.portal.monitoring.order.action.pause                           = pause
na.portal.monitoring.bulkoperations.tags.button                   = manage tags
na.portal.monitoring.bulkoperations.delete.button                 = remove

# order operation attribute view
# this section comes from operations catalog ( na.portal.operationscatalog.* = na.portal.monitoring.operation.view.* )

na.portal.monitoring.operation.view.tab.characteristics                                        = Characteristics
na.portal.monitoring.operation.view.tab.section.general.characteristics                        = General characteristics
na.portal.monitoring.operation.view.tab.section.input.attributes                               = Input attributes
na.portal.monitoring.operation.view.tab.section.execution.attributes                           = Execution attributes
na.portal.monitoring.operation.view.tab.section.output.attributes                              = Output attributes

# this part comes from opercat dynamic form DB
na.portal.monitoring.operation.view.version  = Version
na.portal.monitoring.operation.view.tags  = Tags
na.portal.monitoring.operation.view.workflow  = Workflow

na.portal.monitoring.operation.view.table.column.label.attributes_entities = Attributes / Entities
na.portal.monitoring.operation.view.table.column.label.attributes = Attributes
na.portal.monitoring.operation.view.table.column.label.cardinality = Cardinality
na.portal.monitoring.operation.view.table.column.label.validationRules = Validation Rule
na.portal.monitoring.operation.view.table.column.label.enrichmentRule = Enrichment Rule
na.portal.monitoring.operation.view.table.column.label.value = Value

# order create

na.portal.monitoring.order.create.definition                                = Definition
na.portal.monitoring.order.create.label                                     = Create
na.portal.monitoring.order.create.button.label.execute                      = execute
na.portal.monitoring.order.create.sidebar.title                             = Definition
na.portal.monitoring.order.create.sidebar.system                            = System
na.portal.monitoring.order.create.sidebar.user                              = User
na.portal.monitoring.order.create.sidebar.operations                        = Operations
na.portal.monitoring.order.create.sidebar.version                           = Version
na.portal.monitoring.order.create.sidebar.placeholder.missing.system        = Please select a system
na.portal.monitoring.order.create.sidebar.placeholder.select.system         = Select a system
na.portal.monitoring.order.create.sidebar.placeholder.select.user           = Select a user
na.portal.monitoring.order.create.sidebar.placeholder.select.operations     = Select the desired operations
na.portal.monitoring.order.create.notification.failure.message              = Please check all the highlighted fields
na.portal.monitoring.order.create.notification.failure.title                = The form could not be saved
na.portal.monitoring.order.create.button.optional.parameters.label          = add optional parameters

na.portal.monitoring.order.create.attribute.form.order.parameters.order.header.title                          = Order
na.portal.monitoring.order.create.attribute.form.operation.parameters.base.characteristics.header.title       = Base characteristics
na.portal.monitoring.order.create.attribute.form.operation.parameters.specific.characteristics.header.title   = Specific characteristics

na.portal.monitoring.order.create.attribute.form.related-party.attributes.header.title                  = Attributes
na.portal.monitoring.order.create.attribute.form.related-party.properties.input.key                     = Key
na.portal.monitoring.order.create.attribute.form.related-party.properties.button.add                    = Add
na.portal.monitoring.order.create.attribute.form.related-party.properties.input.key.error.missing       = The key is mandatory
na.portal.monitoring.order.create.attribute.form.related-party.properties.input.key.error.duplicated    = THe key must not be duplicated
na.portal.monitoring.order.create.attribute.form.related-party.properties.button.remove                 = Remove

na.portal.monitoring.order.create.attribute.form.notification.order.required.title                                   = Must select a system and an operation
na.portal.monitoring.order.create.attribute.form.notification.order.required.description                             = Please confirm that the fields are selected.
na.portal.monitoring.order.create.attribute.form.notification.order.invalid.version.state.title                      = Invalid version state
na.portal.monitoring.order.create.attribute.form.notification.order.invalid.version.state.description                = Please select a version with a valid state.
na.portal.monitoring.order.create.attribute.form.notification.order.no.attributes.description                        = This operation has no associated attributes

na.portal.monitoring.order.create.attribute.form.parameter.tab.order     = order parameters
na.portal.monitoring.order.create.attribute.form.parameter.tab.operation = operation parameters

na.portal.monitoring.order.create.attribute.form.button.submit.message   = Order submitted successfully