---

#' Default daterangepicker interval: LAST_30_MINUTES, LAST_HOUR, LAST_12_HOURS, LAST_24_HOURS, LAST_WEEK, LAST_MONTH
#' $string
#' %default
na_portal_monitoring_default_date_interval: LAST_24_HOURS

#' Specifies the path for Portal service locator configuration file
#' $string
#' %default
na_portal_monitoring_service_locator_configfile: /opt/alticelabs/na-portal/conf/cluster-client-config.yml

#' Portal additional java options
#' $string
#' %default
na_portal_monitoring_java_opts: -Xmx256M -Xms256M -Xss256K

#' Portal additional java options
#' $string
#' %default
nossis_ui_java_opts: "{{ na_portal_monitoring_java_opts }}"

#' Enables only the specified features of monitoring module
#' Must be a string where the content is an array of string, in JSON format
#' Supported features:
#'  - order-create
#'
#' If na_portal_monitoring_feature_blacklist variable is defined it will disable the features in the
#' whitelist
#'
#' $string
#' %default
#' @ '["order-create"]'
na_portal_monitoring_feature_whitelist: '[]'

#' Dictionary used to configure log4j2. There's three possible keys for the appenders (all three can be defined at the same time):
#' * _graylog_
#' ** *appender_name*: appender name (mandatory)
#' ** *application_name*: name of the application that is being logged, important for graylog (mandatory, format type NOSSIS_<FULFILLMENT or DM>)
#' ** *service_name*: name of the service that is being logged, important for graylog (mandatory, format type <UD>_<MODULE>)
#' ** *layout*: Logs layout definition (optional)
#' ** *compressionThreshold*: Occurs compression when the data is bigger than the number of defined bytes (optional).
#' ** *compressionType*: Type of compression to use (optional, defaults to 'OFF' if graylog_protocol is 'tcp')
#' ** *use_host_in_prefix*: Boolean that specifies if it's intended to add the host name to the docker hostname (optional)
#' ** *thresholdFilter*: Inclusion only of the logs with level equal or higher than the defined (optional)
#' * _rollingFile_
#' ** *file*: path to the log file (mandatory)
#' ** *maxBackupIndex*: maximum number of backup files (not necessary if timeInterval is defined)
#' ** *maxFileSize*: Maximum size of log files (not necessary if timeInterval is defined)
#' ** *timeInterval*: The frequency in which a rollover must occur according to the most specific time unit in the used date pattern (not necessary if maxBackupIndex is defined)
#' ** *timeModulate*: Specifies if the interval should be adjusted to make the next rollover occur in the interval limit (optional)
#' ** *filePermissions*: permission level for files (optional)
#' ** *fileOwner*: owner that the file will be created (optional)
#' ** *fileGroup*: group that the file will be created (optional)
#' ** *layout*: Definition of logs layout (optional)
#' ** *thresholdFilter*: Inclusion only of the logs with level equal or superior to the defined (optional)
#' * _console_
#' ** *name*: name of the application that is being logged (mandatory)
#' ** *compressionType*: Type of compression to use (optional, default value is 'OFF')
#' ** *compressionThreshold*: Occurs compression when the data is bigger than the number of defined bytes (optional)
#' ** *layout*: Specification of the logs layout (optional)
#' ** *thresholdFilter*: Inclusion only of the logs with level equal or superior to the defined (optional)
#' Other than this, there's also the need to define key loggers, which is a list of dictionaries and contain the information related with
#' the loggers (name, level, refs and additivity), and the key root, which is a dictionary and contains the keys levels and refs.
#' *Note*: The appender _rollingFile_ is a dictionary and can contain more than one entry (the other 2 appenders can't)
#' $dictionary
#' %default
log4j2:
  graylog:
    appender_name: "GRAYLOG_APPENDER"
    application_name: "NOSSIS_{{ product_type }}"
    service_name: "NA-PORTAL_FRONTEND_MONITORING"
  loggers:
    - name: "play"
      level: "info"
      refs: ["GRAYLOG_APPENDER"]
      additivity: "false"
    - name: "application"
      level: "info"
      refs: [ "GRAYLOG_APPENDER"]
      additivity: "false"
    - name: "base"
      level: "info"
      refs: ["GRAYLOG_APPENDER"]
      additivity: "false"
    - name: "monitoring"
      level: "info"
      refs: ["GRAYLOG_APPENDER"]
      additivity: "false"
  root:
    level: "info"
    refs: [ "GRAYLOG_APPENDER" ]

#' Default BOT School API URL for order summary generation
#' $string
#' %default
na_portal_monitoring_botschool_api_url: "https://api.ng.botschool.ai/rest-interface/chat-llms"

#' Default BOT School API key for authentication
#' $string
#' %default
na_portal_monitoring_botschool_api_key: "1e24e0f4ea214780846a6c00d777c3d5"

#' Default BOT School prompt template for order summary generation
#' $string
#' %default
na_portal_monitoring_botschool_prompt_template: "I am going to give you details of a Orchestration Service order in JSON. Please summarize it into a short paragraph in plain text suitable for a manager, using a maximum of 200 words. Ensure the text is formatted using <strong> tags for the order ID, externalId and serviceId and <br> tags for new lines. Write the whole text in english language and format the summary like this example: 'The order <strong>TT-2024164-000037783</strong>, with externalId <strong>Test</strong>, for serviceId <strong> test</strong> was created on June 12, 2024 and took 1 minute to get to current state closed, on June 13, 2024. It has 2 order items: cfs.1 is completed and cfs.2 had an error' Here are the details for the service order: "

