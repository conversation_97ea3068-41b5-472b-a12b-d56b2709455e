---

#' List of zookeeper endpoints that contain instances of entities catalog services.
#' $string
#' %mandatory
#' @ localhost:32181
#na_portal_monitoring_zkClient_zookeeperNodes:

#' Specifies order followup backend host
#' $string
#' %mandatory
#' @ localhost
#na_portal_monitoring_order_followup_backend_host:

#' Specifies order followup backend port
#' $string
#' %mandatory
#' @ 39006
#na_portal_monitoring_order_followup_backend_port:

#' Specifies swe backend host
#' $string
#' %mandatory
#' @ localhost
#na_portal_monitoring_swe_backend_host:

#' Specifies swe backend host
#' $string
#' %mandatory
#' @ 39006
#na_portal_monitoring_swe_backend_port:

#' Specify base URL for SWE service order callback requester, if na_portal_monitoring_requester_callback_url is defined
#' this does nothing, required if na_portal_monitoring_requester_callback_url is undefined
#' *Note*: The URL must point to the Play instance instead of the web balancer
#' $string
#' %mandatory
#' @ http://<play-host>:<play-port>/
#na_portal_monitoring_requester_callback_base_url:

#' Specify URL for SWE service order callback requester, overrides na_portal_monitoring_requester_callback_base_url
#' $string
#' %optional
#' @ http://localhost:35000/request-callback-path
#na_portal_monitoring_requester_callback_url:

#' Allows the whitelisting of external systems
#' List of system names
#' $string
#' %optional
#' @ '["NA", "OM", "user_DEVELOPMENT_WITH_SUBSCRIBER"]'
#na_portal_monitoring_external_systems_whitelist:

#' Disables specified features of monitoring module
#' Must be a string where the content is an array of string, in JSON format
#' Supported features:
#'  - order-create
#'
#' If na_portal_monitoring_feature_whitelist variable is defined instead of all but
#' blacklisted variables are enable, the enabled features are going to be the ones in
#' the whitelist that are not blacklisted with na_portal_monitoring_feature_blacklist variable
#' $string
#' %optional
#' @ '["order-create"]'
#na_portal_monitoring_feature_blacklist:

#' Specifies BOT School API URL for order summary generation
#' $string
#' %mandatory
#' @ https://api.ng.botschool.ai/rest-interface/chat-llms
#na_portal_monitoring_botschool_api_url:

#' Specifies BOT School API key for authentication
#' $string
#' %mandatory
#' @ 1e24e0f4ea214780846a6c00d777c3d5
#na_portal_monitoring_botschool_api_key:

#' Specifies BOT School prompt template for order summary generation
#' $string
#' %mandatory
#' @ "I am going to give you details of a Orchestration Service order in JSON. Please summarize it into a short paragraph in plain text suitable for a manager, using a maximum of 200 words. Ensure the text is formatted using <strong> tags for the order ID, externalId and serviceId and <br> tags for new lines. Write the whole text in english language and format the summary like this example: 'The order <strong>TT-2024164-000037783</strong>, with externalId <strong>Test</strong>, for serviceId <strong> test</strong> was created on June 12, 2024 and took 1 minute to get to current state closed, on June 13, 2024. It has 2 order items: cfs.1 is completed and cfs.2 had an error' Here are the details for the service order: "
#na_portal_monitoring_botschool_prompt_template:

