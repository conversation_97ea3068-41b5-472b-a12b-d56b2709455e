{% set udversion = project.version.value|cut('-SNAPSHOT') -%}
{% set isSNAPSHOT = project.version.value is string_endingwith('-SNAPSHOT') -%}
{% set nacommons_version = version.pt.ptinovacao.get('na-commons').value|cut('-SNAPSHOT') %}
{% set is_nacommons_snapshot = version.pt.ptinovacao.get('na-commons').value is string_endingwith('-SNAPSHOT') %}
FROM rdocker.ptin.corppt.com/na-commons{% if is_nacommons_snapshot %}-tst{% endif %}/na-commons-{{ docker.os.value }}-jdk8:{{ nacommons_version }}

LABEL com.alticelabs.name="{{ docker.image.name.value }}"
LABEL com.alticelabs.version="{{ udversion }}"
LABEL com.alticelabs.group="{{ docker.group.value }}"
LABEL com.alticelabs.summary="{{ docker.summary.value }}"
LABEL com.alticelabs.license="{{ docker.license.value }}"
LABEL com.alticelabs.packager="{{ docker.packager.value }}"
LABEL com.alticelabs.os="{{ docker.os.value }}"
LABEL com.alticelabs.release={% if isSNAPSHOT %}"r{{ buildNumber.value }}-{{ docker.timestamp.value }}"{% else %}"{{ docker.timestamp.value }}"{% endif %}

USER root

COPY --chown=2016:2016 files/entrypoint.sh /entrypoint.sh
COPY --chown=2016:2016 config/all.yml /config/all.yml
COPY --chown=2016:2016 config/defaults-na-portal-mpt-client.yml /config/defaults-na-portal-mpt-client.yml
COPY --chown=2016:2016 config/environment.yml /config/environment.yml
COPY --chown=2016:2016 files/na-portal /opt/alticelabs/na-portal

RUN (groupadd -f alabs || true) && groupmod -o -g 2016 alabs && \
    (useradd -f -1 -g alabs alabs || true) && usermod -o -u 2016 alabs && \
    mkdir -p /var/log/alticelabs/na-portal && \
    chown -R 2016:2016 /opt/alticelabs/na-portal /var/log/alticelabs/na-portal /opt/alticelabs/na-portal && \
    chmod +x /entrypoint.sh /opt/alticelabs/na-portal/bin/mpt-client.sh

ENV TEMPLATE_DIRS=""
ENV DEFAULTS_INV=""

USER 2016:2016

ENTRYPOINT ["/entrypoint.sh"]