<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="2.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:fn="http://www.w3.org/2005/xpath-functions">
  <xsl:output method="xml" version="1.0" encoding="UTF-8" indent="yes"/>
  
  <xsl:variable name="requestInfo" select="responseNA/requestNA"/>
  <xsl:variable name="responseNA" select="responseNA/info/rfsResponseList/rfsResponse/lrResponseList/lrResponse"/>
  
  <xsl:variable name="requestParamsNumber" select="count($requestInfo/param)"/>
  
  <xsl:variable name="errorCode" select="$responseNA/response/@code"/>
  
  
    <!-- OK vs NOK -->
  <xsl:variable name="statusExecution">
    <xsl:choose>
      <xsl:when test="exists(responseNA/info/mptInternalError)">NOK</xsl:when>
      <xsl:when test="contains($responseNA/response/@code,'_000')">OK</xsl:when>
      <xsl:otherwise>NOK</xsl:otherwise>
    </xsl:choose>
  </xsl:variable>
    
    <!-- Status Message -->
  <xsl:variable name="statusMessage">
    <xsl:value-of select="$responseNA/response/text()"/>
  </xsl:variable>   
  
    <!-- Status Description -->
  <xsl:variable name="statusDescription">
    <xsl:choose>
         <!-- se houver um erro core NA -->
      <xsl:when test="exists(responseNA/info/mptInternalError)"><xsl:value-of select="responseNA/info/mptInternalError/text()"/></xsl:when>
        <!-- se correr normalmente -->
      <xsl:otherwise><xsl:value-of select="$responseNA/description/text()"/></xsl:otherwise>
    </xsl:choose>
  </xsl:variable>
  
  
  <xsl:template match="/">
    <ordersResult>
      <orderResult>
        <statusExecution><xsl:value-of select="$statusExecution"/></statusExecution>
        <statusMessage>
          <xsl:value-of select="normalize-space($statusMessage)"/><xsl:if test="string(normalize-space($statusMessage)) and string(normalize-space($statusDescription))"> - </xsl:if><xsl:value-of select="normalize-space($statusDescription)"/>
        </statusMessage>
        
        <param name="dateTime" position="0"><xsl:value-of select="current-dateTime()"/></param>
        <param name="operation" position="1"><xsl:value-of select="'getOntInfo'"/></param>
        
          <!-- dados do request -->
        <param name="olt_vendor" position="2"><xsl:value-of select="$requestInfo/param[@position='1']"/></param>        
        <param name="olt_model" position="3"><xsl:value-of select="$requestInfo/param[@position='2']"/></param>        
        <param name="olt_version" position="4"><xsl:value-of select="$requestInfo/param[@position='3']"/></param>        
        <param name="olt_name" position="5"><xsl:value-of select="$requestInfo/param[@position='4']"/></param>                
        <param name="pon" position="6"><xsl:value-of select="$requestInfo/param[@position='5']"/></param>        
        <param name="ontid" position="7"><xsl:value-of select="$requestInfo/param[@position='6']"/></param>        

          <!-- dados lidos da resposta -->
        <param name="idAcesso" position="8"><xsl:value-of select="$responseNA/data/ont/info/otherInfo/numeroPedido/text()"/></param>
        <param name="nrTelefone" position="9"><xsl:value-of select="$responseNA/data/ont/info/otherInfo/numeroTelefone/text()"/></param>
        <param name="ont_model" position="10"><xsl:value-of select="$responseNA/data/ont/info/model/text()"/></param>
        <param name="serialNumber" position="11"><xsl:value-of select="$responseNA/data/ont/info/serialNumber/text()"/></param>
          <!-- o importante -->
        <param name="adminState" position="12"><xsl:value-of select="$responseNA/data/ont/info/administrativeState/text()"/></param>
        <param name="operState" position="13"><xsl:value-of select="$responseNA/data/ont/info/operationalStatus/text()"/></param>
        <param name="registered" position="14"><xsl:value-of select="$responseNA/data/ont/info/registered/text()"/></param>
        <param name="rf_adminState" position="15"><xsl:value-of select="$responseNA/data/ont/info/rfOverlayAdminState/text()"/></param>
        <param name="rf_operState" position="16"><xsl:value-of select="$responseNA/data/ont/info/rfOverlayOperState/text()"/></param>
        <param name="rf_filter" position="17"><xsl:value-of select="$responseNA/data/ont/info/rfFilter/text()"/></param>
        <param name="hwVersion" position="18"><xsl:value-of select="$responseNA/data/ont/info/hwVersion/text()"/></param>
        <param name="rateLimitOpt" position="19"><xsl:value-of select="$responseNA/data/ont/info/otherInfo/rateLimitOpt/text()"/></param>
        <param name="swVersionPlanned" position="20"><xsl:value-of select="$responseNA/data/ont/info/otherInfo/swVersionPlanned/text()"/></param>
          <!-- potencias opticas -->
        <param name="opticalRxPower" position="21"><xsl:value-of select="$responseNA/data/ontPonInterface/info/opticalRxPower/text()"/></param>
        <param name="opticalTxPower" position="22"><xsl:value-of select="$responseNA/data/ontPonInterface/info/opticalTxPower/text()"/></param>

      </orderResult>
    </ordersResult>

  </xsl:template>
  
</xsl:stylesheet>
