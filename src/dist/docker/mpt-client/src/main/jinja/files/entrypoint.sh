#!/bin/bash

set -e

DEFAULTS_INV_ARR=("/config/defaults-na-portal-mpt-client.yml" $DEFAULTS_INV)
CONFIGURATOR="/opt/alticelabs/na-commons/bin/na-commons-utils-jinja-configurator/jinja-configurator.py"
python ${CONFIGURATOR} /config/all.yml /opt/alticelabs/na-portal ${TEMPLATE_DIRS} ${DEFAULTS_INV_ARR[@]/#/-d }

CONF_FILE=/opt/alticelabs/na-portal/conf/mpt-client.conf

source $CONF_FILE

# JAVA
JAVA_HOME=/usr/java/default
JAVA=$JAVA_HOME/bin/java
JAVA_PARAMS="-Duser.timezone=UTC -Xms64m -Xmx128m -XX:MaxPermSize=64m -Xloggc:${LOG_HOME}/${APP_AGENT_NAME}-gc.log -XX:+PrintGCDetails ${APP_ENV_OPTS}"
CLASSPATH=$LIB_HOME/*

# One IO thread per CPU core is a reasonable default!!!
# IO threads perform non blocking tasks, and should never perform blocking operations because they are responsible for
# multiple connections, so while the operation is blocking other connections will essentially hang
IO_THREADS_PER_CPU_CORE=${IO_THREADS_PER_CPU_CORE:-"2"}

# Generally this should be reasonably high, at least 10 per CPU core!!!
# The number of threads in the workers blocking task thread pool. When performing blocking operations such as Servlet
# requests threads from this pool will be used.
WORKER_THREADS=${WORKER_THREADS:-"20"}

$JAVA -cp "$CLASSPATH" $JAVA_PARAMS pt.ptinovacao.na.portal.mpt.agent.resteasy.UndertowJaxrsServerAgent $AGENT_PORT \
    $AGENT_ADDRESS $IO_THREADS_PER_CPU_CORE $WORKER_THREADS $MPT_CORE_ADDRESS $MPT_CORE_PORT
