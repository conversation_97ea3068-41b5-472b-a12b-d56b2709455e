
na.go.backend.address = "{{ na_portal_be_mpt_go_host }}"
na.go.backend.port = "{{ na_portal_be_mpt_go_port }}"
na.portal.base.naem.ip.address = "{{ naem_nbi_host }}"
na.portal.base.naem.address.port ="{{ naem_nbi_port }}"

#history search delta
go.delta=10
#GO export limit
go.export.limit=5000
#filters top value
go.filters.top=20
#GO DATABASE FIELDS
#THE FIELDS OF INTEREST ARE ONLY THE ONES USED ON THE OPERATION SEARCH TABLE
# Left side = Datatable Column ID -> maps to mData property, and this property maps to the object field

go.operation.base.upload.filesDir = "{{ na_portal_fe_file_repo_dir }}"

go.mapping.for.operations.with.upload = {{ na_portal_fe_mapping_opers_with_firmware_upload }}

go.database.operation.fields =
  {
    "startDate" = "datetime", #INSERT_DATE
    "sInsertionDate" = "datetime", #INSERT_DATE
    "endDate" = "datetime", #INSERT_DATE
    "orderKey" = "orderKey",
    "username" = "username",
    "family" = "family",
    "operationName" = "operationName",
    "rfs" = "rfs",
    "executionState" = "executionState",
    "errorMessage" = "ERROR_MSG",
    "filterName" = "filterName"
  }

go.database.template.fields =
  {
    "startDate" = "lastModified", #INSERT_DATE
    "sInsertionDate" = "lastModified", #INSERT_DATE
    "endDate" = "lastModified", #INSERT_DATE
    "orderKey" = "orderKey",
    "family" = "familyName",
    "operationName" = "templateName",
    "rfs" = "rfsName",
    "templateState" = "active",
    "errorMessage" = "ERROR_MSG",
    "filterName" = "filterName"
  }

na.go.tables = { include "tables.conf" }