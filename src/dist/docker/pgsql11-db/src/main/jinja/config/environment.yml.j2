{% set ud_version = project.version.value | cut('-SNAPSHOT') -%}
{% set nossis_inv_version = version.pt.ptinovacao.get('nossis-inv').value | cut('-SNAPSHOT') -%}
{{ raw }}
#' Path for directory(ies) that contain(s) one or more template files.
#' $string
#' %optional
#' @ "/opt/alticelabs/<ud>/ /opt/alticelabs/<ud-2>/"
#TEMPLATE_DIRS:

#' *This variable cannot be redefined, it is only here to help you by giving more information*.
#' To use this sidecar you need to add a command to the entrypoint, the valid ones are on the example column.
#' *These commands need to be added after the one specified at the documentation of this base image.*
#' $string
#' %default
VALID_MODULES: "--mpt-go, --catalog"

#' Path for file(s) that contain(s) additional customization variables.
#' *Note:* To add a new default inventory file the configuration should be:
#' `DEFAULTS_INV="$DEFAULTS_INV /<config-dir>/<inventory-file>.yml"`
#' otherwise $DEFAULTS_INV is overridden.
#' $string
#' %default
DEFAULTS_INV: "$DEFAULTS_INV /config/defaults-na-portal-pgsql11-db.yml"

#' The default password through which to connect to the db
#' $string
#' %default
DB_DEFAULT_PWD: "naportal"

#' The pgsql database port.
#' $integer
#' %default
DB_PORT: 5432

#' NAPORTAL version used for database migration purposes.
#' *Notes*
#' * Should only be set when downgrading the database version.
#' * Value must not be higher than {{ endraw }}{{ ud_version }}{{ raw }}.
#' $string
#' %optional
#' @ "{{ endraw }}{{ ud_version }}{{ raw }}"
#NA_PORTAL_VERSION:

#' Nossis Inventory version used for database migration purposes.
#' *Notes*
#' * Should only be set when downgrading the database version.
#' * Value must not be higher than {{ endraw }}{{ nossis_inv_version }}{{ raw }}.
#' * Only used for data management upgrade/downgrade.
#' $string
#' %optional
#' @ "{{ endraw }}{{ nossis_inv_version }}{{ raw }}"
#NOSSIS_INV_VERSION:
{{ endraw }}
