<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>pt.ptinovacao.na-portal</groupId>
        <artifactId>na-portal-dist-rpm</artifactId>
        <version>10.55.0</version>
    </parent>

    <artifactId>na-portal-dist-rpm-automation</artifactId>
    <packaging>pom</packaging>

    <profiles>
        <profile>
            <id>rpm</id>
            <properties>
                <rpm.summary>Network Activator Portal Automation Scripts RPM</rpm.summary>
                <application.component.name>automation</application.component.name>
                <dir.automation>${basedir}/../../../../automation</dir.automation>
            </properties>
            <build>
                <plugins>

                    <!--
                    ****************************************************************************************************
                    *** enforcer
                    ****************************************************************************************************
                    -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-enforcer-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>rpm</id>
                                <phase>validate</phase>
                            </execution>
                            <execution>
                                <id>rpm-automation</id>
                                <phase>validate</phase>
                            </execution>
                        </executions>
                    </plugin>

                    <!--
                    ****************************************************************************************************
                    *** resources
                    ****************************************************************************************************
                     -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-resources-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>rpm-automation</id>
                                <phase>prepare-package</phase>
                            </execution>
                        </executions>
                    </plugin>

                    <!--
                    ****************************************************************************************************
                    *** rpm
                    ****************************************************************************************************
                    -->
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>rpm-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>rpm-automation</id>
                                <phase>package</phase>
                                <configuration>
                                    <requires>
                                        <require>${rpm.requires.na-commons-automation}</require>
                                        <require>${rpm.requires.naem-automation}</require>
                                    </requires>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>