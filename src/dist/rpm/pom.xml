<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>pt.ptinovacao.na-portal</groupId>
        <artifactId>na-portal-dist</artifactId>
        <version>10.55.0</version>
    </parent>

    <artifactId>na-portal-dist-rpm</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>automation</module>
    </modules>

    <profiles>
        <profile>
            <id>rpm</id>
            <properties>
                <!-- ########################################## -->
                <!-- CUSTOM PROPERTIES -->
                <rpm.group>ptin/prod</rpm.group>
                <!-- ########################################## -->

                <!-- ############################################################################################################################ -->
                <!-- this will be transformed by groovy-maven-plugin:rpm-version-handler -->
                <rvh.rpm.version.self>${project.version}</rvh.rpm.version.self>

                <!--NA COMMONS-->
                <rvh.rpm.version.na-commons>${version.pt.ptinovacao.na-commons}</rvh.rpm.version.na-commons>
                <!--NAEM-->
                <rvh.rpm.version.naem>${version.pt.ptinovacao.naem}</rvh.rpm.version.naem>

                <!-- ############################################################################################################################ -->
                <!-- RPM REQUIRES -->


                <!-- AUTOMATION -->
                <rpm.requires.na-commons-automation>na-commons-automation &gt;= ${rpm.version.na-commons}, na-commons-automation &lt; ${version.pt.ptinovacao.na-commons.max}</rpm.requires.na-commons-automation>
                <rpm.requires.naem-automation>naem-automation &gt;= ${rpm.version.naem}, naem-automation &lt; ${version.pt.ptinovacao.naem.max}</rpm.requires.naem-automation>
            </properties>
        </profile>
    </profiles>
</project>