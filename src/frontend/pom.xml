<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>pt.ptinovacao.na-portal</groupId>
        <artifactId>na-portal</artifactId>
        <version>10.55.0</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>na-portal-frontend</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>play</module>
    </modules>

    <properties>
        <sonar.js.exclusions>**/external/**/*,**/target/**/*</sonar.js.exclusions>
        <sonar.web.exclusions>**/generated/**/*,**/target/**/*</sonar.web.exclusions>
        <sonar.xml.exclusions>**/generated/**/*,**/target/**/*</sonar.xml.exclusions>
        <sonar.java.exclusions>**/generated/**/*,**/target/**/*</sonar.java.exclusions>
        <sonar.js.source>play</sonar.js.source>
        <sonar.web.source>play</sonar.web.source>
        <sonar.xml.source>play</sonar.xml.source>
    </properties>

</project>