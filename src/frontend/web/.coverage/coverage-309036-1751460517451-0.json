{"result": [{"scriptId": "6", "url": "node:internal/per_context/primordials", "functions": [{"functionName": "SafeIterator", "ranges": [{"startOffset": 9149, "endOffset": 9220, "count": 241}], "isBlockCoverage": false}, {"functionName": "next", "ranges": [{"startOffset": 9225, "endOffset": 9274, "count": 327}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9279, "endOffset": 9324, "count": 240}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10889, "endOffset": 10956, "count": 237}], "isBlockCoverage": false}, {"functionName": "SafeMap", "ranges": [{"startOffset": 11614, "endOffset": 11642, "count": 379}], "isBlockCoverage": false}, {"functionName": "SafeSet", "ranges": [{"startOffset": 11943, "endOffset": 11971, "count": 145}], "isBlockCoverage": false}, {"functionName": "SafeFinalizationRegistry", "ranges": [{"startOffset": 12395, "endOffset": 12451, "count": 2}], "isBlockCoverage": false}, {"functionName": "SafeWeakRef", "ranges": [{"startOffset": 12604, "endOffset": 12642, "count": 9}], "isBlockCoverage": false}]}, {"scriptId": "9", "url": "node:internal/bootstrap/realm", "functions": [{"functionName": "internalBinding", "ranges": [{"startOffset": 5981, "endOffset": 6243, "count": 45}], "isBlockCoverage": false}, {"functionName": "canBeRequiredByUsers", "ranges": [{"startOffset": 9476, "endOffset": 9551, "count": 48}], "isBlockCoverage": true}, {"functionName": "canBeRequiredWithoutScheme", "ranges": [{"startOffset": 9562, "endOffset": 9656, "count": 766}], "isBlockCoverage": true}, {"functionName": "normalizeRequirableId", "ranges": [{"startOffset": 9667, "endOffset": 10012, "count": 604}, {"startOffset": 9743, "endOffset": 9905, "count": 0}, {"startOffset": 9961, "endOffset": 9985, "count": 24}, {"startOffset": 9985, "endOffset": 10011, "count": 580}], "isBlockCoverage": true}, {"functionName": "getCanBeRequiredByUsersWithoutSchemeList", "ranges": [{"startOffset": 10275, "endOffset": 10384, "count": 1}], "isBlockCoverage": true}, {"functionName": "compileForPublicLoader", "ranges": [{"startOffset": 10542, "endOffset": 11205, "count": 24}, {"startOffset": 10623, "endOffset": 10815, "count": 0}, {"startOffset": 10879, "endOffset": 11176, "count": 7}, {"startOffset": 11138, "endOffset": 11142, "count": 0}], "isBlockCoverage": true}, {"functionName": "compileForInternalLoader", "ranges": [{"startOffset": 12487, "endOffset": 13309, "count": 462}], "isBlockCoverage": false}, {"functionName": "requireBuiltin", "ranges": [{"startOffset": 13504, "endOffset": 13869, "count": 439}], "isBlockCoverage": false}]}, {"scriptId": "10", "url": "node:internal/errors", "functions": [{"functionName": "setInternalPrepareStackTrace", "ranges": [{"startOffset": 2771, "endOffset": 2862, "count": 1}], "isBlockCoverage": true}, {"functionName": "wrappedFn", "ranges": [{"startOffset": 14690, "endOffset": 14896, "count": 2491}], "isBlockCoverage": false}]}, {"scriptId": "11", "url": "node:internal/assert", "functions": [{"functionName": "assert", "ranges": [{"startOffset": 157, "endOffset": 307, "count": 279}, {"startOffset": 205, "endOffset": 305, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "12", "url": "node:internal/bootstrap/node", "functions": [{"functionName": "get", "ranges": [{"startOffset": 3830, "endOffset": 3880, "count": 706}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 3886, "endOffset": 3944, "count": 1}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 4128, "endOffset": 4547, "count": 1}, {"startOffset": 4187, "endOffset": 4469, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 13230, "endOffset": 13266, "count": 270}], "isBlockCoverage": true}]}, {"scriptId": "14", "url": "node:internal/async_hooks", "functions": [{"functionName": "newAsyncId", "ranges": [{"startOffset": 14549, "endOffset": 14619, "count": 706}], "isBlockCoverage": true}, {"functionName": "getDefaultTriggerAsyncId", "ranges": [{"startOffset": 14994, "endOffset": 15284, "count": 706}, {"startOffset": 15250, "endOffset": 15283, "count": 0}], "isBlockCoverage": true}, {"functionName": "hasHooks", "ranges": [{"startOffset": 16096, "endOffset": 16159, "count": 3530}], "isBlockCoverage": true}, {"functionName": "enabledHooksExist", "ranges": [{"startOffset": 16161, "endOffset": 16220, "count": 706}], "isBlockCoverage": true}, {"functionName": "initHooksExist", "ranges": [{"startOffset": 16222, "endOffset": 16277, "count": 706}], "isBlockCoverage": true}, {"functionName": "destroyHooksExist", "ranges": [{"startOffset": 16338, "endOffset": 16399, "count": 706}], "isBlockCoverage": true}, {"functionName": "emitBeforeScript", "ranges": [{"startOffset": 16890, "endOffset": 17066, "count": 706}, {"startOffset": 17038, "endOffset": 17064, "count": 0}], "isBlockCoverage": true}, {"functionName": "emitAfterScript", "ranges": [{"startOffset": 17069, "endOffset": 17189, "count": 706}, {"startOffset": 17133, "endOffset": 17158, "count": 0}], "isBlockCoverage": true}, {"functionName": "pushAsyncContext", "ranges": [{"startOffset": 17534, "endOffset": 18104, "count": 706}, {"startOffset": 17754, "endOffset": 17804, "count": 0}], "isBlockCoverage": true}, {"functionName": "popAsyncContext", "ranges": [{"startOffset": 18169, "endOffset": 18780, "count": 706}, {"startOffset": 18285, "endOffset": 18298, "count": 0}, {"startOffset": 18377, "endOffset": 18483, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "15", "url": "node:internal/validators", "functions": [{"functionName": "isInt32", "ranges": [{"startOffset": 1087, "endOffset": 1146, "count": 138}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3063, "endOffset": 3526, "count": 1}, {"startOffset": 3235, "endOffset": 3303, "count": 0}, {"startOffset": 3337, "endOffset": 3405, "count": 0}, {"startOffset": 3442, "endOffset": 3522, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4381, "endOffset": 4493, "count": 3657}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5898, "endOffset": 6012, "count": 1}, {"startOffset": 5955, "endOffset": 6010, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13109, "endOffset": 13225, "count": 756}], "isBlockCoverage": false}]}, {"scriptId": "16", "url": "node:internal/util", "functions": [{"functionName": "getDeprecationWarningEmitter", "ranges": [{"startOffset": 2398, "endOffset": 3047, "count": 14}], "isBlockCoverage": false}, {"functionName": "shouldEmitW<PERSON>ning", "ranges": [{"startOffset": 2497, "endOffset": 2507, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2544, "endOffset": 3044, "count": 0}], "isBlockCoverage": false}, {"functionName": "deprecate", "ranges": [{"startOffset": 3796, "endOffset": 4850, "count": 14}], "isBlockCoverage": false}, {"functionName": "deprecated", "ranges": [{"startOffset": 4160, "endOffset": 4462, "count": 0}], "isBlockCoverage": false}, {"functionName": "normalizeEncoding", "ranges": [{"startOffset": 5373, "endOffset": 5504, "count": 138}, {"startOffset": 5443, "endOffset": 5461, "count": 0}, {"startOffset": 5477, "endOffset": 5503, "count": 0}], "isBlockCoverage": true}, {"functionName": "spliceOne", "ranges": [{"startOffset": 13699, "endOffset": 13826, "count": 2}, {"startOffset": 13780, "endOffset": 13810, "count": 0}], "isBlockCoverage": true}, {"functionName": "exposeInterface", "ranges": [{"startOffset": 16032, "endOffset": 16252, "count": 1}], "isBlockCoverage": false}, {"functionName": "defineLazyProperties", "ranges": [{"startOffset": 16751, "endOffset": 17684, "count": 1}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 16970, "endOffset": 17113, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 17216, "endOffset": 17412, "count": 0}], "isBlockCoverage": false}, {"functionName": "defineReplaceableLazyAttribute", "ranges": [{"startOffset": 17686, "endOffset": 18586, "count": 1}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 17898, "endOffset": 18134, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 18239, "endOffset": 18310, "count": 0}], "isBlockCoverage": false}, {"functionName": "exposeLazyInterfaces", "ranges": [{"startOffset": 18588, "endOffset": 18688, "count": 1}], "isBlockCoverage": false}, {"functionName": "setOwnProperty", "ranges": [{"startOffset": 19883, "endOffset": 20073, "count": 278}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22175, "endOffset": 22303, "count": 822}, {"startOffset": 22219, "endOffset": 22281, "count": 2}], "isBlockCoverage": true}, {"functionName": "setupCoverageHooks", "ranges": [{"startOffset": 22431, "endOffset": 23108, "count": 1}, {"startOffset": 22901, "endOffset": 23078, "count": 0}], "isBlockCoverage": true}, {"functionName": "guessHandleType", "ranges": [{"startOffset": 24498, "endOffset": 24595, "count": 4}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 24597, "endOffset": 25141, "count": 9}], "isBlockCoverage": false}, {"functionName": "WeakReference", "ranges": [{"startOffset": 24672, "endOffset": 24739, "count": 9}], "isBlockCoverage": true}, {"functionName": "incRef", "ranges": [{"startOffset": 24743, "endOffset": 24961, "count": 0}], "isBlockCoverage": false}, {"functionName": "decRef", "ranges": [{"startOffset": 24965, "endOffset": 25093, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 25097, "endOffset": 25139, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "17", "url": "node:internal/options", "functions": [{"functionName": "getCLIOptionsFromBinding", "ranges": [{"startOffset": 495, "endOffset": 623, "count": 609}, {"startOffset": 553, "endOffset": 599, "count": 1}], "isBlockCoverage": true}, {"functionName": "getEmbedderOptions", "ranges": [{"startOffset": 745, "endOffset": 889, "count": 5}, {"startOffset": 801, "endOffset": 861, "count": 1}], "isBlockCoverage": true}, {"functionName": "refreshOptions", "ranges": [{"startOffset": 891, "endOffset": 947, "count": 1}], "isBlockCoverage": true}, {"functionName": "getOptionValue", "ranges": [{"startOffset": 949, "endOffset": 1072, "count": 609}], "isBlockCoverage": true}]}, {"scriptId": "18", "url": "node:internal/util/types", "functions": [{"functionName": "isUint8Array", "ranges": [{"startOffset": 236, "endOffset": 342, "count": 488}], "isBlockCoverage": true}, {"functionName": "isBigInt64Array", "ranges": [{"startOffset": 1232, "endOffset": 1344, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "22", "url": "node:internal/util/debuglog", "functions": [{"functionName": "initializeDebugEnv", "ranges": [{"startOffset": 503, "endOffset": 976, "count": 1}, {"startOffset": 595, "endOffset": 932, "count": 0}], "isBlockCoverage": true}, {"functionName": "testEnabled", "ranges": [{"startOffset": 870, "endOffset": 927, "count": 0}], "isBlockCoverage": false}, {"functionName": "testEnabled", "ranges": [{"startOffset": 958, "endOffset": 969, "count": 4}], "isBlockCoverage": true}, {"functionName": "noop", "ranges": [{"startOffset": 1404, "endOffset": 1412, "count": 4607}], "isBlockCoverage": true}, {"functionName": "debuglogImpl", "ranges": [{"startOffset": 1533, "endOffset": 2091, "count": 4}, {"startOffset": 1608, "endOffset": 2063, "count": 3}, {"startOffset": 1627, "endOffset": 2016, "count": 0}], "isBlockCoverage": true}, {"functionName": "debug", "ranges": [{"startOffset": 1716, "endOffset": 2009, "count": 0}], "isBlockCoverage": false}, {"functionName": "debuglog", "ranges": [{"startOffset": 2311, "endOffset": 3337, "count": 5}], "isBlockCoverage": false}, {"functionName": "init", "ranges": [{"startOffset": 2342, "endOffset": 2438, "count": 4}], "isBlockCoverage": true}, {"functionName": "debug", "ranges": [{"startOffset": 2453, "endOffset": 2847, "count": 4}, {"startOffset": 2700, "endOffset": 2730, "count": 1}, {"startOffset": 2737, "endOffset": 2776, "count": 1}, {"startOffset": 2783, "endOffset": 2837, "count": 2}], "isBlockCoverage": true}, {"functionName": "test", "ranges": [{"startOffset": 2877, "endOffset": 2946, "count": 0}], "isBlockCoverage": false}, {"functionName": "logger", "ranges": [{"startOffset": 2965, "endOffset": 3160, "count": 4}, {"startOffset": 3013, "endOffset": 3043, "count": 1}, {"startOffset": 3050, "endOffset": 3089, "count": 1}, {"startOffset": 3096, "endOffset": 3150, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3231, "endOffset": 3265, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "23", "url": "node:events", "functions": [{"functionName": "EventEmitter", "ranges": [{"startOffset": 6575, "endOffset": 6644, "count": 6}], "isBlockCoverage": false}, {"functionName": "checkListener", "ranges": [{"startOffset": 8095, "endOffset": 8173, "count": 50}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8281, "endOffset": 8329, "count": 6}], "isBlockCoverage": false}, {"functionName": "hasEventListener", "ranges": [{"startOffset": 8446, "endOffset": 8625, "count": 10}, {"startOffset": 8516, "endOffset": 8550, "count": 0}], "isBlockCoverage": true}, {"functionName": "EventEmitter.init", "ranges": [{"startOffset": 10152, "endOffset": 10820, "count": 6}], "isBlockCoverage": false}, {"functionName": "_getMaxListeners", "ranges": [{"startOffset": 12311, "endOffset": 12460, "count": 6}], "isBlockCoverage": false}, {"functionName": "emit", "ranges": [{"startOffset": 13617, "endOffset": 15920, "count": 1285}], "isBlockCoverage": false}, {"functionName": "_addListener", "ranges": [{"startOffset": 15923, "endOffset": 17839, "count": 37}], "isBlockCoverage": false}, {"functionName": "addListener", "ranges": [{"startOffset": 18017, "endOffset": 18109, "count": 34}], "isBlockCoverage": false}, {"functionName": "prependListener", "ranges": [{"startOffset": 18396, "endOffset": 18499, "count": 3}], "isBlockCoverage": true}, {"functionName": "onceWrapper", "ranges": [{"startOffset": 18502, "endOffset": 18766, "count": 1}, {"startOffset": 18704, "endOffset": 18764, "count": 0}], "isBlockCoverage": true}, {"functionName": "_onceWrap", "ranges": [{"startOffset": 18768, "endOffset": 19009, "count": 3}], "isBlockCoverage": true}, {"functionName": "once", "ranges": [{"startOffset": 19200, "endOffset": 19326, "count": 3}], "isBlockCoverage": true}, {"functionName": "removeListener", "ranges": [{"startOffset": 19936, "endOffset": 21299, "count": 10}, {"startOffset": 20059, "endOffset": 20071, "count": 0}, {"startOffset": 20170, "endOffset": 20199, "count": 6}, {"startOffset": 20201, "endOffset": 20572, "count": 4}, {"startOffset": 20266, "endOffset": 20564, "count": 2}, {"startOffset": 20348, "endOffset": 20405, "count": 0}, {"startOffset": 20493, "endOffset": 20554, "count": 0}, {"startOffset": 20572, "endOffset": 21273, "count": 6}, {"startOffset": 20610, "endOffset": 21273, "count": 5}, {"startOffset": 20691, "endOffset": 20830, "count": 8}, {"startOffset": 20728, "endOffset": 20760, "count": 4}, {"startOffset": 20762, "endOffset": 20820, "count": 4}, {"startOffset": 20868, "endOffset": 20880, "count": 1}, {"startOffset": 20880, "endOffset": 20920, "count": 4}, {"startOffset": 20920, "endOffset": 21094, "count": 2}, {"startOffset": 21000, "endOffset": 21047, "count": 1}, {"startOffset": 21094, "endOffset": 21221, "count": 4}, {"startOffset": 21221, "endOffset": 21265, "count": 0}, {"startOffset": 21273, "endOffset": 21298, "count": 9}], "isBlockCoverage": true}, {"functionName": "listenerCount", "ranges": [{"startOffset": 24551, "endOffset": 25256, "count": 2}], "isBlockCoverage": false}, {"functionName": "arrayClone", "ranges": [{"startOffset": 25508, "endOffset": 25981, "count": 207}], "isBlockCoverage": false}]}, {"scriptId": "25", "url": "node:buffer", "functions": [{"functionName": "createPool", "ranges": [{"startOffset": 3893, "endOffset": 4081, "count": 3}], "isBlockCoverage": false}, {"functionName": "alignPool", "ranges": [{"startOffset": 4097, "endOffset": 4219, "count": 370}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8395, "endOffset": 8423, "count": 1}], "isBlockCoverage": true}, {"functionName": "from", "ranges": [{"startOffset": 8681, "endOffset": 9646, "count": 370}], "isBlockCoverage": false}, {"functionName": "fromStringFast", "ranges": [{"startOffset": 13030, "endOffset": 13682, "count": 370}], "isBlockCoverage": false}, {"functionName": "fromString", "ranges": [{"startOffset": 13931, "endOffset": 14291, "count": 370}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 15733, "endOffset": 15787, "count": 702}], "isBlockCoverage": true}, {"functionName": "isEncoding", "ranges": [{"startOffset": 16155, "endOffset": 16306, "count": 138}], "isBlockCoverage": true}, {"functionName": "write", "ranges": [{"startOffset": 17925, "endOffset": 17989, "count": 370}], "isBlockCoverage": false}, {"functionName": "toString", "ranges": [{"startOffset": 24765, "endOffset": 25380, "count": 234}, {"startOffset": 24885, "endOffset": 25067, "count": 0}, {"startOffset": 25073, "endOffset": 25379, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "26", "url": "node:internal/buffer", "functions": [{"functionName": "FastBuffer", "ranges": [{"startOffset": 26365, "endOffset": 26465, "count": 607}], "isBlockCoverage": false}, {"functionName": "markAsUntransferable", "ranges": [{"startOffset": 29764, "endOffset": 30013, "count": 3}], "isBlockCoverage": false}, {"functionName": "createUnsafeBuffer", "ranges": [{"startOffset": 30291, "endOffset": 30427, "count": 3}], "isBlockCoverage": false}, {"functionName": "reconnectZeroFillToggle", "ranges": [{"startOffset": 30682, "endOffset": 30754, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "28", "url": "node:internal/process/per_thread", "functions": [{"functionName": "toggleTraceCategoryState", "ranges": [{"startOffset": 12335, "endOffset": 12665, "count": 1}, {"startOffset": 12415, "endOffset": 12590, "count": 0}, {"startOffset": 12622, "endOffset": 12663, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "29", "url": "node:internal/fs/utils", "functions": [{"functionName": "assertEncoding", "ranges": [{"startOffset": 3557, "endOffset": 3757, "count": 416}, {"startOffset": 3608, "endOffset": 3639, "count": 138}, {"startOffset": 3641, "endOffset": 3755, "count": 0}], "isBlockCoverage": true}, {"functionName": "getOptions", "ranges": [{"startOffset": 7391, "endOffset": 8017, "count": 418}, {"startOffset": 7475, "endOffset": 7507, "count": 416}, {"startOffset": 7509, "endOffset": 7541, "count": 2}, {"startOffset": 7541, "endOffset": 7578, "count": 416}, {"startOffset": 7578, "endOffset": 7696, "count": 138}, {"startOffset": 7696, "endOffset": 7818, "count": 278}, {"startOffset": 7735, "endOffset": 7818, "count": 0}, {"startOffset": 7818, "endOffset": 7932, "count": 416}, {"startOffset": 7932, "endOffset": 7996, "count": 0}, {"startOffset": 7996, "endOffset": 8016, "count": 416}], "isBlockCoverage": true}, {"functionName": "StatsBase", "ranges": [{"startOffset": 9349, "endOffset": 9656, "count": 2}], "isBlockCoverage": true}, {"functionName": "StatsBase.isFile", "ranges": [{"startOffset": 9781, "endOffset": 9838, "count": 2}], "isBlockCoverage": true}, {"functionName": "msFromTimeSpec", "ranges": [{"startOffset": 10437, "endOffset": 10519, "count": 8}], "isBlockCoverage": true}, {"functionName": "dateFromMs", "ranges": [{"startOffset": 11006, "endOffset": 11117, "count": 8}], "isBlockCoverage": true}, {"functionName": "Stats", "ranges": [{"startOffset": 12289, "endOffset": 12831, "count": 2}], "isBlockCoverage": true}, {"functionName": "Stats._checkModeProperty", "ranges": [{"startOffset": 13172, "endOffset": 13402, "count": 2}, {"startOffset": 13209, "endOffset": 13287, "count": 0}, {"startOffset": 13289, "endOffset": 13356, "count": 0}], "isBlockCoverage": true}, {"functionName": "getStatsFromBinding", "ranges": [{"startOffset": 13520, "endOffset": 14599, "count": 2}, {"startOffset": 13600, "endOffset": 14126, "count": 0}], "isBlockCoverage": true}, {"functionName": "stringToFlags", "ranges": [{"startOffset": 15001, "endOffset": 16257, "count": 138}, {"startOffset": 15082, "endOffset": 15137, "count": 0}, {"startOffset": 15160, "endOffset": 15186, "count": 0}, {"startOffset": 15243, "endOffset": 15254, "count": 0}, {"startOffset": 15276, "endOffset": 15313, "count": 0}, {"startOffset": 15318, "endOffset": 15344, "count": 0}, {"startOffset": 15349, "endOffset": 15361, "count": 0}, {"startOffset": 15383, "endOffset": 15419, "count": 0}, {"startOffset": 15425, "endOffset": 15472, "count": 0}, {"startOffset": 15477, "endOffset": 15488, "count": 0}, {"startOffset": 15510, "endOffset": 15567, "count": 0}, {"startOffset": 15573, "endOffset": 15619, "count": 0}, {"startOffset": 15624, "endOffset": 15635, "count": 0}, {"startOffset": 15657, "endOffset": 15712, "count": 0}, {"startOffset": 15718, "endOffset": 15766, "count": 0}, {"startOffset": 15771, "endOffset": 15782, "count": 0}, {"startOffset": 15804, "endOffset": 15862, "count": 0}, {"startOffset": 15867, "endOffset": 15878, "count": 0}, {"startOffset": 15900, "endOffset": 15958, "count": 0}, {"startOffset": 15964, "endOffset": 16011, "count": 0}, {"startOffset": 16016, "endOffset": 16027, "count": 0}, {"startOffset": 16049, "endOffset": 16105, "count": 0}, {"startOffset": 16110, "endOffset": 16121, "count": 0}, {"startOffset": 16143, "endOffset": 16199, "count": 0}, {"startOffset": 16203, "endOffset": 16256, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18284, "endOffset": 18996, "count": 488}, {"startOffset": 18346, "endOffset": 18368, "count": 0}, {"startOffset": 18370, "endOffset": 18477, "count": 0}, {"startOffset": 18668, "endOffset": 18688, "count": 0}, {"startOffset": 18758, "endOffset": 18826, "count": 0}, {"startOffset": 18845, "endOffset": 18995, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19041, "endOffset": 19176, "count": 209}], "isBlockCoverage": true}]}, {"scriptId": "30", "url": "node:internal/url", "functions": [{"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3494, "endOffset": 4283, "count": 282}], "isBlockCoverage": false}, {"functionName": "get hasPort", "ranges": [{"startOffset": 4062, "endOffset": 4127, "count": 0}], "isBlockCoverage": false}, {"functionName": "get hasSearch", "ranges": [{"startOffset": 4131, "endOffset": 4206, "count": 282}], "isBlockCoverage": true}, {"functionName": "get hasHash", "ranges": [{"startOffset": 4210, "endOffset": 4281, "count": 282}], "isBlockCoverage": true}, {"functionName": "isURL", "ranges": [{"startOffset": 21737, "endOffset": 21862, "count": 582}, {"startOffset": 21788, "endOffset": 21804, "count": 94}, {"startOffset": 21805, "endOffset": 21831, "count": 94}, {"startOffset": 21832, "endOffset": 21858, "count": 94}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 22189, "endOffset": 33222, "count": 282}], "isBlockCoverage": false}, {"functionName": "setURLSearchParamsModified", "ranges": [{"startOffset": 22319, "endOffset": 22807, "count": 0}], "isBlockCoverage": false}, {"functionName": "URL", "ranges": [{"startOffset": 22816, "endOffset": 23280, "count": 282}, {"startOffset": 22912, "endOffset": 22960, "count": 0}, {"startOffset": 23065, "endOffset": 23096, "count": 188}], "isBlockCoverage": true}, {"functionName": "parse", "ranges": [{"startOffset": 23291, "endOffset": 23533, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 23537, "endOffset": 24257, "count": 0}], "isBlockCoverage": false}, {"functionName": "#getSearchFromContext", "ranges": [{"startOffset": 24261, "endOffset": 24595, "count": 0}], "isBlockCoverage": false}, {"functionName": "#getSearchFromParams", "ranges": [{"startOffset": 24599, "endOffset": 24710, "count": 0}], "isBlockCoverage": false}, {"functionName": "#ensureSearchParamsUpdated", "ranges": [{"startOffset": 24714, "endOffset": 25153, "count": 376}, {"startOffset": 24982, "endOffset": 25149, "count": 0}], "isBlockCoverage": true}, {"functionName": "#updateContext", "ranges": [{"startOffset": 25401, "endOffset": 27046, "count": 282}, {"startOffset": 25510, "endOffset": 25531, "count": 0}, {"startOffset": 25532, "endOffset": 25630, "count": 0}, {"startOffset": 26326, "endOffset": 27042, "count": 0}], "isBlockCoverage": true}, {"functionName": "toString", "ranges": [{"startOffset": 27050, "endOffset": 27235, "count": 282}], "isBlockCoverage": true}, {"functionName": "get href", "ranges": [{"startOffset": 27239, "endOffset": 27424, "count": 94}], "isBlockCoverage": true}, {"functionName": "set href", "ranges": [{"startOffset": 27428, "endOffset": 27647, "count": 0}], "isBlockCoverage": false}, {"functionName": "get origin", "ranges": [{"startOffset": 27665, "endOffset": 28534, "count": 0}], "isBlockCoverage": false}, {"functionName": "get protocol", "ranges": [{"startOffset": 28538, "endOffset": 28642, "count": 188}], "isBlockCoverage": true}, {"functionName": "set protocol", "ranges": [{"startOffset": 28646, "endOffset": 28819, "count": 0}], "isBlockCoverage": false}, {"functionName": "get username", "ranges": [{"startOffset": 28823, "endOffset": 29050, "count": 0}], "isBlockCoverage": false}, {"functionName": "set username", "ranges": [{"startOffset": 29054, "endOffset": 29227, "count": 0}], "isBlockCoverage": false}, {"functionName": "get password", "ranges": [{"startOffset": 29231, "endOffset": 29454, "count": 0}], "isBlockCoverage": false}, {"functionName": "set password", "ranges": [{"startOffset": 29458, "endOffset": 29631, "count": 0}], "isBlockCoverage": false}, {"functionName": "get host", "ranges": [{"startOffset": 29635, "endOffset": 30066, "count": 0}], "isBlockCoverage": false}, {"functionName": "set host", "ranges": [{"startOffset": 30070, "endOffset": 30235, "count": 0}], "isBlockCoverage": false}, {"functionName": "get hostname", "ranges": [{"startOffset": 30239, "endOffset": 30521, "count": 94}, {"startOffset": 30405, "endOffset": 30430, "count": 0}], "isBlockCoverage": true}, {"functionName": "set hostname", "ranges": [{"startOffset": 30525, "endOffset": 30698, "count": 0}], "isBlockCoverage": false}, {"functionName": "get port", "ranges": [{"startOffset": 30702, "endOffset": 30810, "count": 0}], "isBlockCoverage": false}, {"functionName": "set port", "ranges": [{"startOffset": 30814, "endOffset": 30979, "count": 0}], "isBlockCoverage": false}, {"functionName": "get pathname", "ranges": [{"startOffset": 30983, "endOffset": 31275, "count": 282}, {"startOffset": 31049, "endOffset": 31099, "count": 0}, {"startOffset": 31132, "endOffset": 31180, "count": 0}], "isBlockCoverage": true}, {"functionName": "set pathname", "ranges": [{"startOffset": 31279, "endOffset": 31452, "count": 0}], "isBlockCoverage": false}, {"functionName": "get search", "ranges": [{"startOffset": 31456, "endOffset": 31653, "count": 0}], "isBlockCoverage": false}, {"functionName": "set search", "ranges": [{"startOffset": 31657, "endOffset": 31861, "count": 0}], "isBlockCoverage": false}, {"functionName": "get searchParams", "ranges": [{"startOffset": 31879, "endOffset": 32237, "count": 0}], "isBlockCoverage": false}, {"functionName": "get hash", "ranges": [{"startOffset": 32241, "endOffset": 32456, "count": 0}], "isBlockCoverage": false}, {"functionName": "set hash", "ranges": [{"startOffset": 32460, "endOffset": 32625, "count": 0}], "isBlockCoverage": false}, {"functionName": "toJSON", "ranges": [{"startOffset": 32629, "endOffset": 32812, "count": 0}], "isBlockCoverage": false}, {"functionName": "canParse", "ranges": [{"startOffset": 32823, "endOffset": 33220, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPathFromURLPosix", "ranges": [{"startOffset": 42504, "endOffset": 43025, "count": 94}, {"startOffset": 42567, "endOffset": 42623, "count": 0}, {"startOffset": 42701, "endOffset": 42984, "count": 11661}, {"startOffset": 42732, "endOffset": 42980, "count": 0}], "isBlockCoverage": true}, {"functionName": "fileURLToPath", "ranges": [{"startOffset": 43027, "endOffset": 43439, "count": 94}, {"startOffset": 43154, "endOffset": 43175, "count": 0}, {"startOffset": 43205, "endOffset": 43269, "count": 0}, {"startOffset": 43307, "endOffset": 43348, "count": 0}, {"startOffset": 43381, "endOffset": 43408, "count": 0}], "isBlockCoverage": true}, {"functionName": "encodePathChars", "ranges": [{"startOffset": 43992, "endOffset": 46009, "count": 94}, {"startOffset": 44099, "endOffset": 44180, "count": 0}, {"startOffset": 44229, "endOffset": 44306, "count": 0}, {"startOffset": 44354, "endOffset": 44435, "count": 0}, {"startOffset": 44483, "endOffset": 44571, "count": 0}, {"startOffset": 44618, "endOffset": 44697, "count": 0}, {"startOffset": 44744, "endOffset": 44823, "count": 0}, {"startOffset": 44870, "endOffset": 44948, "count": 0}, {"startOffset": 44995, "endOffset": 45081, "count": 0}, {"startOffset": 45128, "endOffset": 45219, "count": 0}, {"startOffset": 45381, "endOffset": 45464, "count": 0}, {"startOffset": 45511, "endOffset": 45603, "count": 0}, {"startOffset": 45650, "endOffset": 45729, "count": 0}, {"startOffset": 45776, "endOffset": 45861, "count": 0}, {"startOffset": 45908, "endOffset": 45987, "count": 0}], "isBlockCoverage": true}, {"functionName": "pathToFileURL", "ranges": [{"startOffset": 46011, "endOffset": 47870, "count": 94}, {"startOffset": 46133, "endOffset": 46179, "count": 0}, {"startOffset": 46181, "endOffset": 47319, "count": 0}, {"startOffset": 47345, "endOffset": 47375, "count": 0}, {"startOffset": 47668, "endOffset": 47680, "count": 0}, {"startOffset": 47682, "endOffset": 47721, "count": 0}, {"startOffset": 47724, "endOffset": 47775, "count": 0}, {"startOffset": 47781, "endOffset": 47797, "count": 0}], "isBlockCoverage": true}, {"functionName": "toPathIfFileURL", "ranges": [{"startOffset": 47872, "endOffset": 48009, "count": 488}, {"startOffset": 47968, "endOffset": 48008, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "33", "url": "node:path", "functions": [{"functionName": "isPosixPathSeparator", "ranges": [{"startOffset": 2158, "endOffset": 2235, "count": 246599}], "isBlockCoverage": true}, {"functionName": "normalizeString", "ranges": [{"startOffset": 2470, "endOffset": 4418, "count": 2010}, {"startOffset": 2687, "endOffset": 4402, "count": 246441}, {"startOffset": 2720, "endOffset": 2762, "count": 244431}, {"startOffset": 2762, "endOffset": 2853, "count": 2010}, {"startOffset": 2805, "endOffset": 2811, "count": 1852}, {"startOffset": 2811, "endOffset": 2853, "count": 158}, {"startOffset": 2853, "endOffset": 2886, "count": 244589}, {"startOffset": 2886, "endOffset": 4303, "count": 27749}, {"startOffset": 2918, "endOffset": 2931, "count": 25224}, {"startOffset": 2933, "endOffset": 2958, "count": 2669}, {"startOffset": 2958, "endOffset": 4260, "count": 25080}, {"startOffset": 2980, "endOffset": 4015, "count": 35}, {"startOffset": 3009, "endOffset": 3035, "count": 19}, {"startOffset": 3036, "endOffset": 3110, "count": 0}, {"startOffset": 3111, "endOffset": 3185, "count": 0}, {"startOffset": 3219, "endOffset": 3696, "count": 19}, {"startOffset": 3339, "endOffset": 3415, "count": 0}, {"startOffset": 3696, "endOffset": 3865, "count": 16}, {"startOffset": 3724, "endOffset": 3865, "count": 0}, {"startOffset": 3875, "endOffset": 4007, "count": 16}, {"startOffset": 3938, "endOffset": 3956, "count": 0}, {"startOffset": 4015, "endOffset": 4260, "count": 25045}, {"startOffset": 4061, "endOffset": 4130, "count": 23052}, {"startOffset": 4130, "endOffset": 4205, "count": 1993}, {"startOffset": 4260, "endOffset": 4303, "count": 27730}, {"startOffset": 4303, "endOffset": 4398, "count": 216840}, {"startOffset": 4331, "endOffset": 4345, "count": 5854}, {"startOffset": 4347, "endOffset": 4368, "count": 285}, {"startOffset": 4368, "endOffset": 4398, "count": 216555}], "isBlockCoverage": true}, {"functionName": "resolve", "ranges": [{"startOffset": 36750, "endOffset": 37854, "count": 1850}, {"startOffset": 36872, "endOffset": 36892, "count": 3187}, {"startOffset": 36899, "endOffset": 37204, "count": 3184}, {"startOffset": 37030, "endOffset": 37057, "count": 2}, {"startOffset": 37057, "endOffset": 37204, "count": 3182}, {"startOffset": 37233, "endOffset": 37408, "count": 0}, {"startOffset": 37793, "endOffset": 37843, "count": 0}, {"startOffset": 37844, "endOffset": 37849, "count": 0}], "isBlockCoverage": true}, {"functionName": "normalize", "ranges": [{"startOffset": 37920, "endOffset": 38533, "count": 160}, {"startOffset": 38006, "endOffset": 38017, "count": 0}, {"startOffset": 38345, "endOffset": 38439, "count": 1}, {"startOffset": 38377, "endOffset": 38388, "count": 0}, {"startOffset": 38427, "endOffset": 38432, "count": 0}, {"startOffset": 38439, "endOffset": 38473, "count": 159}, {"startOffset": 38473, "endOffset": 38485, "count": 1}, {"startOffset": 38485, "endOffset": 38509, "count": 159}, {"startOffset": 38509, "endOffset": 38521, "count": 73}, {"startOffset": 38522, "endOffset": 38528, "count": 86}], "isBlockCoverage": true}, {"functionName": "isAbsolute", "ranges": [{"startOffset": 38600, "endOffset": 38756, "count": 291}], "isBlockCoverage": true}, {"functionName": "join", "ranges": [{"startOffset": 38825, "endOffset": 39189, "count": 73}, {"startOffset": 38874, "endOffset": 38885, "count": 0}, {"startOffset": 38950, "endOffset": 39079, "count": 148}, {"startOffset": 39114, "endOffset": 39125, "count": 0}], "isBlockCoverage": true}, {"functionName": "toNamespacedPath", "ranges": [{"startOffset": 41705, "endOffset": 41781, "count": 1693}], "isBlockCoverage": true}, {"functionName": "dirname", "ranges": [{"startOffset": 41847, "endOffset": 42523, "count": 507}, {"startOffset": 41930, "endOffset": 41941, "count": 0}, {"startOffset": 42115, "endOffset": 42368, "count": 7043}, {"startOffset": 42186, "endOffset": 42270, "count": 507}, {"startOffset": 42270, "endOffset": 42362, "count": 6536}, {"startOffset": 42396, "endOffset": 42423, "count": 0}, {"startOffset": 42460, "endOffset": 42472, "count": 0}], "isBlockCoverage": true}, {"functionName": "basename", "ranges": [{"startOffset": 42619, "endOffset": 45049, "count": 139}, {"startOffset": 42680, "endOffset": 42713, "count": 0}, {"startOffset": 42845, "endOffset": 42865, "count": 0}, {"startOffset": 42866, "endOffset": 42897, "count": 0}, {"startOffset": 42899, "endOffset": 44423, "count": 0}, {"startOffset": 44471, "endOffset": 44956, "count": 1875}, {"startOffset": 44542, "endOffset": 44766, "count": 139}, {"startOffset": 44766, "endOffset": 44950, "count": 1736}, {"startOffset": 44788, "endOffset": 44950, "count": 139}, {"startOffset": 44984, "endOffset": 44994, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "35", "url": "node:internal/process/task_queues", "functions": [{"functionName": "setHasTickScheduled", "ranges": [{"startOffset": 1059, "endOffset": 1145, "count": 472}, {"startOffset": 1135, "endOffset": 1138, "count": 236}, {"startOffset": 1139, "endOffset": 1142, "count": 236}], "isBlockCoverage": true}, {"functionName": "processTicksAndRejections", "ranges": [{"startOffset": 1445, "endOffset": 2424, "count": 236}, {"startOffset": 1547, "endOffset": 2279, "count": 706}, {"startOffset": 1748, "endOffset": 1781, "count": 234}, {"startOffset": 1781, "endOffset": 2155, "count": 472}, {"startOffset": 1868, "endOffset": 1901, "count": 1}, {"startOffset": 1914, "endOffset": 1956, "count": 471}, {"startOffset": 1969, "endOffset": 2020, "count": 0}, {"startOffset": 2033, "endOffset": 2093, "count": 0}, {"startOffset": 2106, "endOffset": 2133, "count": 0}, {"startOffset": 2217, "endOffset": 2238, "count": 0}], "isBlockCoverage": true}, {"functionName": "nextTick", "ranges": [{"startOffset": 2568, "endOffset": 3442, "count": 706}, {"startOffset": 2669, "endOffset": 2676, "count": 0}, {"startOffset": 2724, "endOffset": 2738, "count": 234}, {"startOffset": 2743, "endOffset": 2780, "count": 1}, {"startOffset": 2785, "endOffset": 2836, "count": 471}, {"startOffset": 2841, "endOffset": 2906, "count": 0}, {"startOffset": 2911, "endOffset": 3054, "count": 0}, {"startOffset": 3083, "endOffset": 3109, "count": 236}, {"startOffset": 3354, "endOffset": 3414, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "36", "url": "node:internal/process/promises", "functions": [{"functionName": "", "ranges": [{"startOffset": 1267, "endOffset": 1379, "count": 4}], "isBlockCoverage": false}, {"functionName": "setHasRejectionToWarn", "ranges": [{"startOffset": 4097, "endOffset": 4187, "count": 236}, {"startOffset": 4177, "endOffset": 4180, "count": 0}], "isBlockCoverage": true}, {"functionName": "processPromiseRejections", "ranges": [{"startOffset": 12037, "endOffset": 13386, "count": 236}, {"startOffset": 12197, "endOffset": 12376, "count": 0}, {"startOffset": 12582, "endOffset": 13292, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "37", "url": "node:internal/fixed_queue", "functions": [{"functionName": "isEmpty", "ranges": [{"startOffset": 3130, "endOffset": 3182, "count": 1884}], "isBlockCoverage": true}, {"functionName": "isFull", "ranges": [{"startOffset": 3186, "endOffset": 3253, "count": 706}], "isBlockCoverage": true}, {"functionName": "push", "ranges": [{"startOffset": 3257, "endOffset": 3344, "count": 706}], "isBlockCoverage": true}, {"functionName": "shift", "ranges": [{"startOffset": 3348, "endOffset": 3563, "count": 942}, {"startOffset": 3441, "endOffset": 3453, "count": 236}, {"startOffset": 3453, "endOffset": 3562, "count": 706}], "isBlockCoverage": true}, {"functionName": "isEmpty", "ranges": [{"startOffset": 3683, "endOffset": 3730, "count": 942}], "isBlockCoverage": true}, {"functionName": "push", "ranges": [{"startOffset": 3734, "endOffset": 3998, "count": 706}, {"startOffset": 3775, "endOffset": 3968, "count": 0}], "isBlockCoverage": true}, {"functionName": "shift", "ranges": [{"startOffset": 4002, "endOffset": 4257, "count": 942}, {"startOffset": 4094, "endOffset": 4115, "count": 472}, {"startOffset": 4117, "endOffset": 4236, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "39", "url": "node:timers", "functions": [{"functionName": "clearTimeout", "ranges": [{"startOffset": 5082, "endOffset": 5448, "count": 1}, {"startOffset": 5125, "endOffset": 5144, "count": 0}, {"startOffset": 5146, "endOffset": 5213, "count": 0}, {"startOffset": 5276, "endOffset": 5446, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "40", "url": "node:internal/process/execution", "functions": [{"functionName": "tryGetCwd", "ranges": [{"startOffset": 945, "endOffset": 1275, "count": 1}, {"startOffset": 1006, "endOffset": 1273, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "43", "url": "node:internal/source_map/source_map_cache", "functions": [{"functionName": "setSourceMapsEnabled", "ranges": [{"startOffset": 1729, "endOffset": 2313, "count": 1}, {"startOffset": 1837, "endOffset": 2020, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "44", "url": "node:internal/modules/helpers", "functions": [{"functionName": "", "ranges": [{"startOffset": 1182, "endOffset": 1207, "count": 1}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1676, "endOffset": 1808, "count": 278}], "isBlockCoverage": true}, {"functionName": "initializeCjsConditions", "ranges": [{"startOffset": 1924, "endOffset": 2415, "count": 1}, {"startOffset": 2103, "endOffset": 2107, "count": 0}], "isBlockCoverage": true}, {"functionName": "getCjsConditions", "ranges": [{"startOffset": 2482, "endOffset": 2609, "count": 94}, {"startOffset": 2547, "endOffset": 2583, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadBuiltinModule", "ranges": [{"startOffset": 2836, "endOffset": 3216, "count": 24}, {"startOffset": 2925, "endOffset": 2942, "count": 0}], "isBlockCoverage": true}, {"functionName": "lazyModule", "ranges": [{"startOffset": 3309, "endOffset": 3422, "count": 138}, {"startOffset": 3353, "endOffset": 3401, "count": 1}], "isBlockCoverage": true}, {"functionName": "makeRequireFunction", "ranges": [{"startOffset": 4011, "endOffset": 6543, "count": 138}, {"startOffset": 4150, "endOffset": 4213, "count": 0}, {"startOffset": 4280, "endOffset": 5551, "count": 0}], "isBlockCoverage": true}, {"functionName": "require", "ranges": [{"startOffset": 4423, "endOffset": 5546, "count": 0}], "isBlockCoverage": false}, {"functionName": "require", "ranges": [{"startOffset": 5573, "endOffset": 5713, "count": 386}], "isBlockCoverage": true}, {"functionName": "resolve", "ranges": [{"startOffset": 5899, "endOffset": 6045, "count": 0}], "isBlockCoverage": false}, {"functionName": "paths", "ranges": [{"startOffset": 6190, "endOffset": 6312, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasStartedUserCJSExecution", "ranges": [{"startOffset": 11438, "endOffset": 11512, "count": 1}], "isBlockCoverage": true}, {"functionName": "setHasStartedUserCJSExecution", "ranges": [{"startOffset": 11516, "endOffset": 11593, "count": 138}], "isBlockCoverage": true}, {"functionName": "hasStartedUserESMExecution", "ranges": [{"startOffset": 11597, "endOffset": 11671, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "45", "url": "node:fs", "functions": [{"functionName": "isFileType", "ranges": [{"startOffset": 5197, "endOffset": 5457, "count": 3582}, {"startOffset": 5396, "endOffset": 5416, "count": 220}], "isBlockCoverage": true}, {"functionName": "readFileSync", "ranges": [{"startOffset": 11296, "endOffset": 12935, "count": 138}, {"startOffset": 11418, "endOffset": 11449, "count": 0}, {"startOffset": 11622, "endOffset": 11712, "count": 0}, {"startOffset": 11713, "endOffset": 11753, "count": 0}, {"startOffset": 11841, "endOffset": 11851, "count": 0}, {"startOffset": 11852, "endOffset": 11855, "count": 0}, {"startOffset": 11985, "endOffset": 12934, "count": 0}], "isBlockCoverage": true}, {"functionName": "readdirSync", "ranges": [{"startOffset": 38545, "endOffset": 39053, "count": 1}, {"startOffset": 38682, "endOffset": 38748, "count": 0}, {"startOffset": 38775, "endOffset": 38828, "count": 0}, {"startOffset": 39015, "endOffset": 39041, "count": 0}], "isBlockCoverage": true}, {"functionName": "statSync", "ranges": [{"startOffset": 43084, "endOffset": 43424, "count": 70}, {"startOffset": 43358, "endOffset": 43385, "count": 0}, {"startOffset": 43385, "endOffset": 43423, "count": 2}], "isBlockCoverage": true}, {"functionName": "splitRoot", "ranges": [{"startOffset": 69496, "endOffset": 69706, "count": 141}, {"startOffset": 69563, "endOffset": 69686, "count": 282}, {"startOffset": 69641, "endOffset": 69680, "count": 141}, {"startOffset": 69686, "endOffset": 69705, "count": 0}], "isBlockCoverage": true}, {"functionName": "encodeRealpathResult", "ranges": [{"startOffset": 69711, "endOffset": 69998, "count": 139}, {"startOffset": 69796, "endOffset": 69826, "count": 0}, {"startOffset": 69846, "endOffset": 69997, "count": 0}], "isBlockCoverage": true}, {"functionName": "nextPart", "ranges": [{"startOffset": 70404, "endOffset": 70479, "count": 1901}], "isBlockCoverage": true}, {"functionName": "realpathSync", "ranges": [{"startOffset": 70653, "endOffset": 74472, "count": 279}, {"startOffset": 70777, "endOffset": 70795, "count": 0}, {"startOffset": 70921, "endOffset": 70926, "count": 278}, {"startOffset": 70956, "endOffset": 70991, "count": 140}, {"startOffset": 70991, "endOffset": 71553, "count": 139}, {"startOffset": 71553, "endOffset": 71743, "count": 0}, {"startOffset": 71743, "endOffset": 71880, "count": 139}, {"startOffset": 71880, "endOffset": 74399, "count": 1901}, {"startOffset": 71992, "endOffset": 72123, "count": 141}, {"startOffset": 72123, "endOffset": 72281, "count": 1760}, {"startOffset": 72368, "endOffset": 72396, "count": 1889}, {"startOffset": 72376, "endOffset": 72381, "count": 1881}, {"startOffset": 72398, "endOffset": 72535, "count": 1681}, {"startOffset": 72489, "endOffset": 72513, "count": 0}, {"startOffset": 72535, "endOffset": 72596, "count": 220}, {"startOffset": 72596, "endOffset": 72601, "count": 212}, {"startOffset": 72638, "endOffset": 72687, "count": 0}, {"startOffset": 72687, "endOffset": 73876, "count": 220}, {"startOffset": 72983, "endOffset": 73008, "count": 0}, {"startOffset": 73049, "endOffset": 73137, "count": 218}, {"startOffset": 73093, "endOffset": 73098, "count": 211}, {"startOffset": 73137, "endOffset": 73558, "count": 2}, {"startOffset": 73497, "endOffset": 73550, "count": 0}, {"startOffset": 73558, "endOffset": 73791, "count": 2}, {"startOffset": 73791, "endOffset": 73796, "count": 1}, {"startOffset": 73840, "endOffset": 73870, "count": 2}, {"startOffset": 73876, "endOffset": 74168, "count": 2}, {"startOffset": 74168, "endOffset": 74191, "count": 0}, {"startOffset": 74193, "endOffset": 74395, "count": 0}, {"startOffset": 74399, "endOffset": 74408, "count": 139}, {"startOffset": 74408, "endOffset": 74413, "count": 138}], "isBlockCoverage": true}]}, {"scriptId": "50", "url": "node:util", "functions": [{"functionName": "inherits", "ranges": [{"startOffset": 7972, "endOffset": 8610, "count": 7}, {"startOffset": 8057, "endOffset": 8114, "count": 0}, {"startOffset": 8173, "endOffset": 8240, "count": 0}, {"startOffset": 8283, "endOffset": 8413, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "52", "url": "node:internal/process/permission", "functions": [{"functionName": "isEnabled", "ranges": [{"startOffset": 332, "endOffset": 576, "count": 927}, {"startOffset": 392, "endOffset": 537, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "55", "url": "node:internal/console/constructor", "functions": [{"functionName": "value", "ranges": [{"startOffset": 5763, "endOffset": 6408, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 5972, "endOffset": 6067, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6079, "endOffset": 6109, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6239, "endOffset": 6338, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6350, "endOffset": 6380, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeGlobalConsole", "ranges": [{"startOffset": 20356, "endOffset": 21346, "count": 1}, {"startOffset": 20675, "endOffset": 21345, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21241, "endOffset": 21342, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "57", "url": "node:internal/event_target", "functions": [{"functionName": "defineEventHandler", "ranges": [{"startOffset": 29893, "endOffset": 31359, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 30053, "endOffset": 30189, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 30288, "endOffset": 31126, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "61", "url": "node:internal/bootstrap/switches/is_main_thread", "functions": [{"functionName": "createWritableStdioStream", "ranges": [{"startOffset": 1424, "endOffset": 3026, "count": 2}, {"startOffset": 1716, "endOffset": 1910, "count": 0}, {"startOffset": 1916, "endOffset": 1928, "count": 0}, {"startOffset": 1933, "endOffset": 2645, "count": 0}, {"startOffset": 2651, "endOffset": 2905, "count": 0}], "isBlockCoverage": true}, {"functionName": "write", "ranges": [{"startOffset": 2841, "endOffset": 2888, "count": 0}], "isBlockCoverage": false}, {"functionName": "addCleanup", "ranges": [{"startOffset": 3639, "endOffset": 3730, "count": 3}, {"startOffset": 3693, "endOffset": 3728, "count": 0}], "isBlockCoverage": true}, {"functionName": "getStdout", "ranges": [{"startOffset": 3732, "endOffset": 4424, "count": 5}, {"startOffset": 3769, "endOffset": 3783, "count": 4}, {"startOffset": 3783, "endOffset": 4423, "count": 1}], "isBlockCoverage": true}, {"functionName": "cleanupStdout", "ranges": [{"startOffset": 4090, "endOffset": 4268, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStderr", "ranges": [{"startOffset": 4426, "endOffset": 5117, "count": 2}, {"startOffset": 4463, "endOffset": 5116, "count": 1}], "isBlockCoverage": true}, {"functionName": "cleanupStderr", "ranges": [{"startOffset": 4783, "endOffset": 4961, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStdin", "ranges": [{"startOffset": 5119, "endOffset": 7610, "count": 2}, {"startOffset": 5154, "endOffset": 5222, "count": 1}, {"startOffset": 5222, "endOffset": 5326, "count": 0}, {"startOffset": 5332, "endOffset": 5466, "count": 0}, {"startOffset": 5472, "endOffset": 5484, "count": 1}, {"startOffset": 5489, "endOffset": 6271, "count": 1}, {"startOffset": 5800, "endOffset": 5828, "count": 0}, {"startOffset": 5830, "endOffset": 6001, "count": 0}, {"startOffset": 6277, "endOffset": 6501, "count": 0}, {"startOffset": 6505, "endOffset": 6770, "count": 1}, {"startOffset": 6772, "endOffset": 7609, "count": 1}], "isBlockCoverage": true}, {"functionName": "read", "ranges": [{"startOffset": 6458, "endOffset": 6467, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7080, "endOffset": 7122, "count": 0}], "isBlockCoverage": false}, {"functionName": "onpause", "ranges": [{"startOffset": 7128, "endOffset": 7368, "count": 0}], "isBlockCoverage": false}, {"functionName": "cleanupStdin", "ranges": [{"startOffset": 7383, "endOffset": 7456, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "62", "url": "node:internal/v8/startup_snapshot", "functions": [{"functionName": "isBuildingSnapshot", "ranges": [{"startOffset": 433, "endOffset": 504, "count": 7}], "isBlockCoverage": false}, {"functionName": "runDeserializeCallbacks", "ranges": [{"startOffset": 831, "endOffset": 1004, "count": 1}, {"startOffset": 910, "endOffset": 1002, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "63", "url": "node:internal/process/signal", "functions": [{"functionName": "isSignal", "ranges": [{"startOffset": 238, "endOffset": 334, "count": 4}], "isBlockCoverage": false}, {"functionName": "startListeningIfSignal", "ranges": [{"startOffset": 398, "endOffset": 908, "count": 4}], "isBlockCoverage": false}]}, {"scriptId": "65", "url": "node:internal/modules/cjs/loader", "functions": [{"functionName": "", "ranges": [{"startOffset": 4692, "endOffset": 4783, "count": 1}, {"startOffset": 4739, "endOffset": 4775, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4832, "endOffset": 4875, "count": 1}], "isBlockCoverage": true}, {"functionName": "stat", "ranges": [{"startOffset": 6287, "endOffset": 6701, "count": 1188}, {"startOffset": 6385, "endOffset": 6483, "count": 1187}, {"startOffset": 6461, "endOffset": 6479, "count": 423}, {"startOffset": 6483, "endOffset": 6556, "count": 765}, {"startOffset": 6556, "endOffset": 6570, "count": 764}, {"startOffset": 6572, "endOffset": 6682, "count": 185}, {"startOffset": 6682, "endOffset": 6700, "count": 765}], "isBlockCoverage": true}, {"functionName": "update<PERSON><PERSON><PERSON>n", "ranges": [{"startOffset": 7192, "endOffset": 7394, "count": 363}, {"startOffset": 7264, "endOffset": 7274, "count": 362}, {"startOffset": 7291, "endOffset": 7344, "count": 362}, {"startOffset": 7301, "endOffset": 7343, "count": 225}, {"startOffset": 7346, "endOffset": 7392, "count": 361}], "isBlockCoverage": true}, {"functionName": "reportModuleToWatchMode", "ranges": [{"startOffset": 7511, "endOffset": 7667, "count": 524}, {"startOffset": 7592, "endOffset": 7607, "count": 0}, {"startOffset": 7609, "endOffset": 7665, "count": 0}], "isBlockCoverage": true}, {"functionName": "reportModuleNotFoundToWatchMode", "ranges": [{"startOffset": 7851, "endOffset": 8090, "count": 22}, {"startOffset": 7952, "endOffset": 7967, "count": 0}, {"startOffset": 7969, "endOffset": 8088, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8037, "endOffset": 8079, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 8181, "endOffset": 8931, "count": 138}, {"startOffset": 8487, "endOffset": 8497, "count": 0}, {"startOffset": 8515, "endOffset": 8879, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10914, "endOffset": 10939, "count": 1}], "isBlockCoverage": true}, {"functionName": "initializeCJS", "ranges": [{"startOffset": 11659, "endOffset": 12270, "count": 1}], "isBlockCoverage": true}, {"functionName": "tryPackage", "ranges": [{"startOffset": 13154, "endOffset": 14433, "count": 50}, {"startOffset": 13276, "endOffset": 13357, "count": 0}, {"startOffset": 13451, "endOffset": 13495, "count": 5}, {"startOffset": 13496, "endOffset": 13563, "count": 0}, {"startOffset": 13589, "endOffset": 14414, "count": 0}], "isBlockCoverage": true}, {"functionName": "tryFile", "ranges": [{"startOffset": 14794, "endOffset": 15057, "count": 484}, {"startOffset": 14883, "endOffset": 14894, "count": 215}, {"startOffset": 14894, "endOffset": 14923, "count": 269}, {"startOffset": 14923, "endOffset": 14951, "count": 0}, {"startOffset": 14952, "endOffset": 14975, "count": 269}, {"startOffset": 14978, "endOffset": 15021, "count": 0}, {"startOffset": 15021, "endOffset": 15056, "count": 269}], "isBlockCoverage": true}, {"functionName": "tryExtensions", "ranges": [{"startOffset": 15322, "endOffset": 15542, "count": 200}, {"startOffset": 15411, "endOffset": 15524, "count": 340}, {"startOffset": 15490, "endOffset": 15520, "count": 130}, {"startOffset": 15524, "endOffset": 15541, "count": 70}], "isBlockCoverage": true}, {"functionName": "findLongestRegisteredExtension", "ranges": [{"startOffset": 15726, "endOffset": 16202, "count": 138}, {"startOffset": 15950, "endOffset": 16184, "count": 139}, {"startOffset": 16001, "endOffset": 16014, "count": 0}, {"startOffset": 16152, "endOffset": 16180, "count": 138}, {"startOffset": 16184, "endOffset": 16201, "count": 0}], "isBlockCoverage": true}, {"functionName": "trySelf<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 16325, "endOffset": 16618, "count": 290}, {"startOffset": 16377, "endOffset": 16394, "count": 1}, {"startOffset": 16394, "endOffset": 16452, "count": 289}, {"startOffset": 16452, "endOffset": 16616, "count": 0}], "isBlockCoverage": true}, {"functionName": "trySelf", "ranges": [{"startOffset": 16826, "endOffset": 17754, "count": 290}, {"startOffset": 16885, "endOffset": 16902, "count": 1}, {"startOffset": 16902, "endOffset": 17024, "count": 289}, {"startOffset": 17025, "endOffset": 17050, "count": 124}, {"startOffset": 17052, "endOffset": 17075, "count": 165}, {"startOffset": 17075, "endOffset": 17122, "count": 124}, {"startOffset": 17122, "endOffset": 17148, "count": 0}, {"startOffset": 17148, "endOffset": 17314, "count": 124}, {"startOffset": 17210, "endOffset": 17285, "count": 0}, {"startOffset": 17314, "endOffset": 17752, "count": 0}], "isBlockCoverage": true}, {"functionName": "resolveExports", "ranges": [{"startOffset": 18102, "endOffset": 18920, "count": 298}, {"startOffset": 18309, "endOffset": 18324, "count": 87}, {"startOffset": 18339, "endOffset": 18350, "count": 87}, {"startOffset": 18350, "endOffset": 18451, "count": 211}, {"startOffset": 18451, "endOffset": 18473, "count": 189}, {"startOffset": 18475, "endOffset": 18918, "count": 94}, {"startOffset": 18755, "endOffset": 18914, "count": 0}], "isBlockCoverage": true}, {"functionName": "getDefaultExtensions", "ranges": [{"startOffset": 18982, "endOffset": 19477, "count": 182}, {"startOffset": 19125, "endOffset": 19153, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19401, "endOffset": 19473, "count": 728}, {"startOffset": 19425, "endOffset": 19473, "count": 182}], "isBlockCoverage": true}, {"functionName": "Module._findPath", "ranges": [{"startOffset": 19770, "endOffset": 23329, "count": 291}, {"startOffset": 19880, "endOffset": 19903, "count": 2}, {"startOffset": 19903, "endOffset": 19966, "count": 289}, {"startOffset": 19943, "endOffset": 19966, "count": 0}, {"startOffset": 20099, "endOffset": 20122, "count": 14}, {"startOffset": 20122, "endOffset": 20705, "count": 277}, {"startOffset": 20265, "endOffset": 20704, "count": 275}, {"startOffset": 20344, "endOffset": 20698, "count": 0}, {"startOffset": 20760, "endOffset": 20914, "count": 87}, {"startOffset": 20877, "endOffset": 20910, "count": 16}, {"startOffset": 20914, "endOffset": 20976, "count": 277}, {"startOffset": 20976, "endOffset": 23310, "count": 516}, {"startOffset": 21117, "endOffset": 21127, "count": 500}, {"startOffset": 21128, "endOffset": 21224, "count": 499}, {"startOffset": 21162, "endOffset": 21200, "count": 0}, {"startOffset": 21231, "endOffset": 21254, "count": 217}, {"startOffset": 21254, "endOffset": 21282, "count": 299}, {"startOffset": 21282, "endOffset": 21422, "count": 298}, {"startOffset": 21375, "endOffset": 21416, "count": 94}, {"startOffset": 21422, "endOffset": 21552, "count": 205}, {"startOffset": 21552, "endOffset": 22785, "count": 203}, {"startOffset": 21574, "endOffset": 22558, "count": 8}, {"startOffset": 21607, "endOffset": 21796, "count": 7}, {"startOffset": 21662, "endOffset": 21722, "count": 0}, {"startOffset": 21796, "endOffset": 22550, "count": 1}, {"startOffset": 21850, "endOffset": 22490, "count": 0}, {"startOffset": 22581, "endOffset": 22779, "count": 195}, {"startOffset": 22661, "endOffset": 22713, "count": 180}, {"startOffset": 22785, "endOffset": 22805, "count": 205}, {"startOffset": 22805, "endOffset": 22816, "count": 72}, {"startOffset": 22818, "endOffset": 23036, "count": 50}, {"startOffset": 22920, "endOffset": 22968, "count": 2}, {"startOffset": 23036, "endOffset": 23056, "count": 205}, {"startOffset": 23056, "endOffset": 23132, "count": 183}, {"startOffset": 23132, "endOffset": 23310, "count": 22}, {"startOffset": 23310, "endOffset": 23328, "count": 0}], "isBlockCoverage": true}, {"functionName": "Module._nodeModulePaths", "ranges": [{"startOffset": 25406, "endOffset": 26594, "count": 138}, {"startOffset": 25678, "endOffset": 25717, "count": 0}, {"startOffset": 26038, "endOffset": 26472, "count": 16478}, {"startOffset": 26134, "endOffset": 26342, "count": 1741}, {"startOffset": 26161, "endOffset": 26301, "count": 1592}, {"startOffset": 26342, "endOffset": 26466, "count": 14737}, {"startOffset": 26362, "endOffset": 26466, "count": 3958}, {"startOffset": 26397, "endOffset": 26423, "count": 2366}, {"startOffset": 26423, "endOffset": 26458, "count": 1592}], "isBlockCoverage": true}, {"functionName": "Module._resolveLookupPaths", "ranges": [{"startOffset": 26729, "endOffset": 28016, "count": 290}, {"startOffset": 26809, "endOffset": 26875, "count": 0}, {"startOffset": 26960, "endOffset": 27159, "count": 91}, {"startOffset": 27040, "endOffset": 27090, "count": 75}, {"startOffset": 27091, "endOffset": 27158, "count": 0}, {"startOffset": 27161, "endOffset": 27488, "count": 199}, {"startOffset": 27221, "endOffset": 27236, "count": 198}, {"startOffset": 27238, "endOffset": 27348, "count": 198}, {"startOffset": 27348, "endOffset": 27388, "count": 1}, {"startOffset": 27477, "endOffset": 27483, "count": 0}, {"startOffset": 27488, "endOffset": 27556, "count": 91}, {"startOffset": 27557, "endOffset": 27576, "count": 91}, {"startOffset": 27578, "endOffset": 27827, "count": 0}, {"startOffset": 27827, "endOffset": 28015, "count": 91}], "isBlockCoverage": true}, {"functionName": "Module._load", "ranges": [{"startOffset": 31138, "endOffset": 35016, "count": 387}, {"startOffset": 31221, "endOffset": 32049, "count": 386}, {"startOffset": 31680, "endOffset": 32045, "count": 73}, {"startOffset": 31860, "endOffset": 31932, "count": 0}, {"startOffset": 31977, "endOffset": 32045, "count": 0}, {"startOffset": 32049, "endOffset": 32102, "count": 314}, {"startOffset": 32102, "endOffset": 32374, "count": 0}, {"startOffset": 32374, "endOffset": 32527, "count": 314}, {"startOffset": 32527, "endOffset": 33453, "count": 152}, {"startOffset": 32648, "endOffset": 33453, "count": 0}, {"startOffset": 33453, "endOffset": 33513, "count": 162}, {"startOffset": 33513, "endOffset": 33596, "count": 24}, {"startOffset": 33596, "endOffset": 33726, "count": 138}, {"startOffset": 33750, "endOffset": 34127, "count": 138}, {"startOffset": 33768, "endOffset": 33953, "count": 1}, {"startOffset": 33953, "endOffset": 34003, "count": 137}, {"startOffset": 34127, "endOffset": 34988, "count": 138}, {"startOffset": 34331, "endOffset": 34734, "count": 0}, {"startOffset": 34916, "endOffset": 34984, "count": 0}, {"startOffset": 34988, "endOffset": 35015, "count": 138}], "isBlockCoverage": true}, {"functionName": "Module._resolveFilename", "ranges": [{"startOffset": 35478, "endOffset": 38364, "count": 314}, {"startOffset": 35575, "endOffset": 35600, "count": 24}, {"startOffset": 35600, "endOffset": 35650, "count": 290}, {"startOffset": 35650, "endOffset": 35669, "count": 0}, {"startOffset": 35671, "endOffset": 36522, "count": 0}, {"startOffset": 36522, "endOffset": 36617, "count": 290}, {"startOffset": 36617, "endOffset": 36665, "count": 0}, {"startOffset": 36667, "endOffset": 37309, "count": 0}, {"startOffset": 37309, "endOffset": 37470, "count": 290}, {"startOffset": 37470, "endOffset": 37666, "count": 0}, {"startOffset": 37666, "endOffset": 37826, "count": 290}, {"startOffset": 37826, "endOffset": 38363, "count": 0}], "isBlockCoverage": true}, {"functionName": "finalizeEsmResolution", "ranges": [{"startOffset": 38791, "endOffset": 39372, "count": 94}, {"startOffset": 38989, "endOffset": 39118, "count": 0}, {"startOffset": 39237, "endOffset": 39371, "count": 0}], "isBlockCoverage": true}, {"functionName": "Module.load", "ranges": [{"startOffset": 40021, "endOffset": 40688, "count": 138}, {"startOffset": 40356, "endOffset": 40386, "count": 0}, {"startOffset": 40388, "endOffset": 40440, "count": 0}], "isBlockCoverage": true}, {"functionName": "Module.require", "ranges": [{"startOffset": 40975, "endOffset": 41274, "count": 386}, {"startOffset": 41035, "endOffset": 41153, "count": 0}], "isBlockCoverage": true}, {"functionName": "wrapSafe", "ranges": [{"startOffset": 46694, "endOffset": 48701, "count": 138}, {"startOffset": 46987, "endOffset": 47859, "count": 0}, {"startOffset": 47924, "endOffset": 48382, "count": 83}, {"startOffset": 47970, "endOffset": 48132, "count": 1}, {"startOffset": 48132, "endOffset": 48378, "count": 82}, {"startOffset": 48574, "endOffset": 48681, "count": 0}], "isBlockCoverage": true}, {"functionName": "Module._compile", "ranges": [{"startOffset": 49102, "endOffset": 50633, "count": 138}, {"startOffset": 49201, "endOffset": 49211, "count": 0}, {"startOffset": 49229, "endOffset": 49382, "count": 0}, {"startOffset": 49567, "endOffset": 49599, "count": 0}, {"startOffset": 49632, "endOffset": 49805, "count": 0}, {"startOffset": 50029, "endOffset": 50059, "count": 1}, {"startOffset": 50150, "endOffset": 50184, "count": 1}, {"startOffset": 50186, "endOffset": 50397, "count": 0}, {"startOffset": 50593, "endOffset": 50614, "count": 1}], "isBlockCoverage": true}, {"functionName": "getMaybeCachedSource", "ranges": [{"startOffset": 50887, "endOffset": 51292, "count": 138}, {"startOffset": 51050, "endOffset": 51125, "count": 0}], "isBlockCoverage": true}, {"functionName": "Module._extensions..js", "ranges": [{"startOffset": 51471, "endOffset": 53565, "count": 138}, {"startOffset": 51745, "endOffset": 51767, "count": 0}, {"startOffset": 51863, "endOffset": 53354, "count": 0}, {"startOffset": 53395, "endOffset": 53429, "count": 55}, {"startOffset": 53433, "endOffset": 53516, "count": 0}], "isBlockCoverage": true}, {"functionName": "isRelative", "ranges": [{"startOffset": 56411, "endOffset": 56752, "count": 277}, {"startOffset": 56494, "endOffset": 56511, "count": 190}, {"startOffset": 56511, "endOffset": 56556, "count": 87}, {"startOffset": 56557, "endOffset": 56599, "count": 87}, {"startOffset": 56600, "endOffset": 56643, "count": 16}, {"startOffset": 56644, "endOffset": 56749, "count": 0}], "isBlockCoverage": true}, {"functionName": "Module._initPaths", "ranges": [{"startOffset": 56872, "endOffset": 57837, "count": 1}, {"startOffset": 56913, "endOffset": 56938, "count": 0}, {"startOffset": 56990, "endOffset": 57013, "count": 0}, {"startOffset": 57221, "endOffset": 57263, "count": 0}, {"startOffset": 57562, "endOffset": 57704, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "66", "url": "node:internal/modules/package_json_reader", "functions": [{"functionName": "read", "ranges": [{"startOffset": 1112, "endOffset": 3862, "count": 860}, {"startOffset": 1208, "endOffset": 1245, "count": 784}, {"startOffset": 1245, "endOffset": 1922, "count": 76}, {"startOffset": 1922, "endOffset": 1931, "count": 0}, {"startOffset": 1932, "endOffset": 1946, "count": 0}, {"startOffset": 1947, "endOffset": 1969, "count": 76}, {"startOffset": 1993, "endOffset": 3812, "count": 48}, {"startOffset": 2061, "endOffset": 2657, "count": 0}, {"startOffset": 3083, "endOffset": 3129, "count": 16}, {"startOffset": 3189, "endOffset": 3235, "count": 0}, {"startOffset": 3346, "endOffset": 3405, "count": 1}, {"startOffset": 3377, "endOffset": 3404, "count": 0}, {"startOffset": 3407, "endOffset": 3447, "count": 1}, {"startOffset": 3481, "endOffset": 3677, "count": 1}, {"startOffset": 3602, "endOffset": 3655, "count": 0}, {"startOffset": 3705, "endOffset": 3808, "count": 0}, {"startOffset": 3812, "endOffset": 3861, "count": 76}], "isBlockCoverage": true}, {"functionName": "readPackage", "ranges": [{"startOffset": 3930, "endOffset": 4020, "count": 860}], "isBlockCoverage": true}, {"functionName": "readPackageScope", "ranges": [{"startOffset": 4237, "endOffset": 5057, "count": 428}, {"startOffset": 4424, "endOffset": 4994, "count": 599}, {"startOffset": 4673, "endOffset": 4719, "count": 0}, {"startOffset": 4721, "endOffset": 4748, "count": 0}, {"startOffset": 4815, "endOffset": 4842, "count": 0}, {"startOffset": 4913, "endOffset": 4990, "count": 428}, {"startOffset": 4994, "endOffset": 5056, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "67", "url": "node:internal/modules/esm/utils", "functions": [{"functionName": "initializeDefaultConditions", "ranges": [{"startOffset": 1785, "endOffset": 2290, "count": 1}, {"startOffset": 1968, "endOffset": 1972, "count": 0}, {"startOffset": 2085, "endOffset": 2089, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeESM", "ranges": [{"startOffset": 10706, "endOffset": 11092, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "68", "url": "node:internal/process/pre_execution", "functions": [{"functionName": "prepareMainThreadExecution", "ranges": [{"startOffset": 1060, "endOffset": 1244, "count": 1}], "isBlockCoverage": true}, {"functionName": "prepareExecution", "ranges": [{"startOffset": 2474, "endOffset": 4718, "count": 1}, {"startOffset": 3485, "endOffset": 3589, "count": 0}, {"startOffset": 4477, "endOffset": 4639, "count": 0}], "isBlockCoverage": true}, {"functionName": "setupSymbolDisposePolyfill", "ranges": [{"startOffset": 4720, "endOffset": 5455, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupUserModules", "ranges": [{"startOffset": 5457, "endOffset": 6205, "count": 1}, {"startOffset": 5827, "endOffset": 5858, "count": 0}], "isBlockCoverage": true}, {"functionName": "refreshRuntimeOptions", "ranges": [{"startOffset": 6207, "endOffset": 6263, "count": 1}], "isBlockCoverage": true}, {"functionName": "patchProcessObject", "ranges": [{"startOffset": 6690, "endOffset": 9027, "count": 1}, {"startOffset": 7675, "endOffset": 7727, "count": 0}], "isBlockCoverage": true}, {"functionName": "addReadOnlyProcessAlias", "ranges": [{"startOffset": 9029, "endOffset": 9310, "count": 13}, {"startOffset": 9150, "endOffset": 9308, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupWarningHandler", "ranges": [{"startOffset": 9312, "endOffset": 9832, "count": 1}, {"startOffset": 9687, "endOffset": 9826, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9716, "endOffset": 9818, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 9907, "endOffset": 10326, "count": 1}, {"startOffset": 9981, "endOffset": 10141, "count": 0}, {"startOffset": 10235, "endOffset": 10324, "count": 0}], "isBlockCoverage": true}, {"functionName": "setupEventsource", "ranges": [{"startOffset": 10394, "endOffset": 10519, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupWebCrypto", "ranges": [{"startOffset": 10623, "endOffset": 11575, "count": 1}, {"startOffset": 10758, "endOffset": 10775, "count": 0}, {"startOffset": 11264, "endOffset": 11573, "count": 0}], "isBlockCoverage": true}, {"functionName": "cryptoThisCheck", "ranges": [{"startOffset": 10949, "endOffset": 11125, "count": 0}], "isBlockCoverage": false}, {"functionName": "get crypto", "ranges": [{"startOffset": 11426, "endOffset": 11525, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupCodeCoverage", "ranges": [{"startOffset": 11577, "endOffset": 12098, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupCustomEvent", "ranges": [{"startOffset": 12204, "endOffset": 12481, "count": 1}, {"startOffset": 12343, "endOffset": 12360, "count": 0}], "isBlockCoverage": true}, {"functionName": "setupStacktracePrinterOnSigint", "ranges": [{"startOffset": 12483, "endOffset": 12708, "count": 1}, {"startOffset": 12585, "endOffset": 12707, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeReport", "ranges": [{"startOffset": 12710, "endOffset": 12959, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 12855, "endOffset": 12950, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupDebugEnv", "ranges": [{"startOffset": 12961, "endOffset": 13192, "count": 1}, {"startOffset": 13112, "endOffset": 13190, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeReportSignalHandlers", "ranges": [{"startOffset": 13254, "endOffset": 13442, "count": 1}, {"startOffset": 13342, "endOffset": 13440, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeHeapSnapshotSignalHandlers", "ranges": [{"startOffset": 13444, "endOffset": 14178, "count": 1}, {"startOffset": 13639, "endOffset": 14176, "count": 0}], "isBlockCoverage": true}, {"functionName": "doWriteHeapSnapshot", "ranges": [{"startOffset": 13752, "endOffset": 13906, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 14098, "endOffset": 14170, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupTraceCategoryState", "ranges": [{"startOffset": 14180, "endOffset": 14439, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupInspectorHooks", "ranges": [{"startOffset": 14441, "endOffset": 15023, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupNetworkInspection", "ranges": [{"startOffset": 15025, "endOffset": 15338, "count": 1}, {"startOffset": 15162, "endOffset": 15336, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeDeprecations", "ranges": [{"startOffset": 15533, "endOffset": 17485, "count": 1}, {"startOffset": 16245, "endOffset": 16547, "count": 16}, {"startOffset": 16290, "endOffset": 16522, "count": 0}, {"startOffset": 16864, "endOffset": 17061, "count": 0}, {"startOffset": 17089, "endOffset": 17483, "count": 0}], "isBlockCoverage": true}, {"functionName": "setupChildProcessIpcChannel", "ranges": [{"startOffset": 17487, "endOffset": 18052, "count": 1}, {"startOffset": 17563, "endOffset": 18050, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeClusterIPC", "ranges": [{"startOffset": 18054, "endOffset": 18324, "count": 1}, {"startOffset": 18141, "endOffset": 18322, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializePermission", "ranges": [{"startOffset": 18326, "endOffset": 20323, "count": 1}, {"startOffset": 18468, "endOffset": 19897, "count": 0}], "isBlockCoverage": true}, {"functionName": "binding", "ranges": [{"startOffset": 18492, "endOffset": 18579, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20155, "endOffset": 20315, "count": 6}, {"startOffset": 20233, "endOffset": 20309, "count": 0}], "isBlockCoverage": true}, {"functionName": "readPolicyFromDisk", "ranges": [{"startOffset": 20325, "endOffset": 22144, "count": 1}, {"startOffset": 20453, "endOffset": 22142, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeCJSLoader", "ranges": [{"startOffset": 22146, "endOffset": 22267, "count": 1}], "isBlockCoverage": true}, {"functionName": "initializeESMLoader", "ranges": [{"startOffset": 22269, "endOffset": 22852, "count": 1}, {"startOffset": 22605, "endOffset": 22850, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeSourceMapsHandlers", "ranges": [{"startOffset": 22854, "endOffset": 23052, "count": 1}], "isBlockCoverage": true}, {"functionName": "initializeFrozenIntrinsics", "ranges": [{"startOffset": 23054, "endOffset": 23241, "count": 1}, {"startOffset": 23139, "endOffset": 23239, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadPreloadModules", "ranges": [{"startOffset": 23348, "endOffset": 23696, "count": 1}, {"startOffset": 23542, "endOffset": 23694, "count": 0}], "isBlockCoverage": true}, {"functionName": "markBootstrapComplete", "ranges": [{"startOffset": 23698, "endOffset": 23792, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "70", "url": "node:internal/modules/run_main", "functions": [{"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 569, "endOffset": 1672, "count": 1}, {"startOffset": 743, "endOffset": 847, "count": 0}, {"startOffset": 1140, "endOffset": 1151, "count": 0}, {"startOffset": 1378, "endOffset": 1646, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldUseESMLoader", "ranges": [{"startOffset": 1833, "endOffset": 3041, "count": 1}, {"startOffset": 1939, "endOffset": 1955, "count": 0}, {"startOffset": 2429, "endOffset": 2445, "count": 0}, {"startOffset": 2561, "endOffset": 2577, "count": 0}, {"startOffset": 2640, "endOffset": 2657, "count": 0}, {"startOffset": 2881, "endOffset": 2914, "count": 0}, {"startOffset": 2919, "endOffset": 2955, "count": 0}], "isBlockCoverage": true}, {"functionName": "executeUserEntryPoint", "ranges": [{"startOffset": 5520, "endOffset": 6505, "count": 1}, {"startOffset": 6089, "endOffset": 6503, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6257, "endOffset": 6497, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "71", "url": "node:internal/dns/utils", "functions": [{"functionName": "initializeDns", "ranges": [{"startOffset": 5468, "endOffset": 5949, "count": 1}, {"startOffset": 5611, "endOffset": 5816, "count": 0}, {"startOffset": 5864, "endOffset": 5948, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5889, "endOffset": 5945, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "73", "url": "node:internal/bootstrap/switches/does_own_process_state", "functions": [{"functionName": "wrappedCwd", "ranges": [{"startOffset": 3781, "endOffset": 3884, "count": 2}, {"startOffset": 3833, "endOffset": 3862, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "80", "url": "node:internal/main/run_main_module", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1182, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "81", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/faucet/bin/cmd.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1982, "count": 1}, {"startOffset": 561, "endOffset": 564, "count": 0}, {"startOffset": 755, "endOffset": 775, "count": 0}, {"startOffset": 836, "endOffset": 1124, "count": 0}, {"startOffset": 1126, "endOffset": 1297, "count": 0}, {"startOffset": 1299, "endOffset": 1981, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 589, "endOffset": 682, "count": 1}, {"startOffset": 646, "endOffset": 680, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 712, "endOffset": 726, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 864, "endOffset": 1069, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1180, "endOffset": 1228, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1354, "endOffset": 1403, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1776, "endOffset": 1812, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1834, "endOffset": 1979, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "82", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/faucet/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3298, "count": 1}], "isBlockCoverage": true}, {"functionName": "module.exports", "ranges": [{"startOffset": 649, "endOffset": 3296, "count": 1}], "isBlockCoverage": true}, {"functionName": "trimWidth", "ranges": [{"startOffset": 713, "endOffset": 896, "count": 168}, {"startOffset": 796, "endOffset": 881, "count": 0}], "isBlockCoverage": true}, {"functionName": "updateName", "ranges": [{"startOffset": 899, "endOffset": 1042, "count": 82}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1075, "endOffset": 1555, "count": 84}, {"startOffset": 1124, "endOffset": 1135, "count": 0}, {"startOffset": 1172, "endOffset": 1182, "count": 83}, {"startOffset": 1186, "endOffset": 1217, "count": 83}, {"startOffset": 1221, "endOffset": 1244, "count": 2}, {"startOffset": 1249, "endOffset": 1296, "count": 2}, {"startOffset": 1296, "endOffset": 1413, "count": 82}, {"startOffset": 1311, "endOffset": 1321, "count": 81}, {"startOffset": 1323, "endOffset": 1413, "count": 81}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1577, "endOffset": 2226, "count": 202}, {"startOffset": 1619, "endOffset": 1629, "count": 0}, {"startOffset": 1653, "endOffset": 1657, "count": 0}, {"startOffset": 1672, "endOffset": 1901, "count": 0}, {"startOffset": 2034, "endOffset": 2176, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2247, "endOffset": 2532, "count": 3}, {"startOffset": 2311, "endOffset": 2322, "count": 2}, {"startOffset": 2322, "endOffset": 2397, "count": 1}, {"startOffset": 2397, "endOffset": 2529, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2456, "endOffset": 2495, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2587, "endOffset": 3278, "count": 1}, {"startOffset": 2640, "endOffset": 2709, "count": 0}, {"startOffset": 2992, "endOffset": 3024, "count": 0}, {"startOffset": 3026, "endOffset": 3157, "count": 0}, {"startOffset": 3221, "endOffset": 3242, "count": 0}, {"startOffset": 3271, "endOffset": 3274, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2829, "endOffset": 2974, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "83", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/through2/through2.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 957, "count": 1}, {"startOffset": 46, "endOffset": 85, "count": 0}], "isBlockCoverage": true}, {"functionName": "noop", "ranges": [{"startOffset": 165, "endOffset": 229, "count": 0}], "isBlockCoverage": false}, {"functionName": "ctor", "ranges": [{"startOffset": 231, "endOffset": 812, "count": 1}, {"startOffset": 311, "endOffset": 385, "count": 0}, {"startOffset": 758, "endOffset": 791, "count": 0}], "isBlockCoverage": true}, {"functionName": "Through2", "ranges": [{"startOffset": 449, "endOffset": 640, "count": 2}, {"startOffset": 523, "endOffset": 639, "count": 1}], "isBlockCoverage": true}, {"functionName": "make", "ranges": [{"startOffset": 814, "endOffset": 902, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "84", "url": "node:stream", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5095, "count": 1}], "isBlockCoverage": false}, {"functionName": "fn", "ranges": [{"startOffset": 2552, "endOffset": 2712, "count": 0}], "isBlockCoverage": false}, {"functionName": "fn", "ranges": [{"startOffset": 3214, "endOffset": 3352, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4400, "endOffset": 4432, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4530, "endOffset": 4571, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4664, "endOffset": 4705, "count": 0}], "isBlockCoverage": false}, {"functionName": "_uint8ArrayToBuffer", "ranges": [{"startOffset": 4886, "endOffset": 5093, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "85", "url": "node:internal/streams/operators", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10772, "count": 1}], "isBlockCoverage": false}, {"functionName": "compose", "ranges": [{"startOffset": 1019, "endOffset": 1599, "count": 0}], "isBlockCoverage": false}, {"functionName": "map", "ranges": [{"startOffset": 1601, "endOffset": 4704, "count": 0}], "isBlockCoverage": false}, {"functionName": "asIndexedPairs", "ranges": [{"startOffset": 4706, "endOffset": 5179, "count": 0}], "isBlockCoverage": false}, {"functionName": "some", "ranges": [{"startOffset": 5181, "endOffset": 5329, "count": 0}], "isBlockCoverage": false}, {"functionName": "every", "ranges": [{"startOffset": 5331, "endOffset": 5659, "count": 0}], "isBlockCoverage": false}, {"functionName": "find", "ranges": [{"startOffset": 5661, "endOffset": 5803, "count": 0}], "isBlockCoverage": false}, {"functionName": "for<PERSON>ach", "ranges": [{"startOffset": 5805, "endOffset": 6175, "count": 0}], "isBlockCoverage": false}, {"functionName": "filter", "ranges": [{"startOffset": 6177, "endOffset": 6504, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReduceAwareErrMissingArgs", "ranges": [{"startOffset": 6720, "endOffset": 6834, "count": 0}], "isBlockCoverage": false}, {"functionName": "reduce", "ranges": [{"startOffset": 6838, "endOffset": 8280, "count": 0}], "isBlockCoverage": false}, {"functionName": "toArray", "ranges": [{"startOffset": 8282, "endOffset": 8711, "count": 0}], "isBlockCoverage": false}, {"functionName": "flatMap", "ranges": [{"startOffset": 8713, "endOffset": 8907, "count": 0}], "isBlockCoverage": false}, {"functionName": "toIntegerOrInfinity", "ranges": [{"startOffset": 8909, "endOffset": 9231, "count": 0}], "isBlockCoverage": false}, {"functionName": "drop", "ranges": [{"startOffset": 9233, "endOffset": 9781, "count": 0}], "isBlockCoverage": false}, {"functionName": "take", "ranges": [{"startOffset": 9783, "endOffset": 10453, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "86", "url": "node:internal/abort_controller", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12429, "count": 1}], "isBlockCoverage": false}, {"functionName": "lazyMessageChannel", "ranges": [{"startOffset": 1609, "endOffset": 1742, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyMakeTransferable", "ranges": [{"startOffset": 1744, "endOffset": 1902, "count": 0}], "isBlockCoverage": false}, {"functionName": "customInspect", "ranges": [{"startOffset": 2372, "endOffset": 2631, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateThisAbortSignal", "ranges": [{"startOffset": 2633, "endOffset": 2758, "count": 0}], "isBlockCoverage": false}, {"functionName": "setWeakAbortSignalTimeout", "ranges": [{"startOffset": 3340, "endOffset": 3740, "count": 0}], "isBlockCoverage": false}, {"functionName": "AbortSignal", "ranges": [{"startOffset": 3784, "endOffset": 3844, "count": 0}], "isBlockCoverage": false}, {"functionName": "get aborted", "ranges": [{"startOffset": 3881, "endOffset": 3964, "count": 0}], "isBlockCoverage": false}, {"functionName": "get reason", "ranges": [{"startOffset": 3997, "endOffset": 4076, "count": 0}], "isBlockCoverage": false}, {"functionName": "throwIfAborted", "ranges": [{"startOffset": 4080, "endOffset": 4196, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4200, "endOffset": 4329, "count": 0}], "isBlockCoverage": false}, {"functionName": "abort", "ranges": [{"startOffset": 4407, "endOffset": 4551, "count": 0}], "isBlockCoverage": false}, {"functionName": "timeout", "ranges": [{"startOffset": 4629, "endOffset": 4895, "count": 0}], "isBlockCoverage": false}, {"functionName": "any", "ranges": [{"startOffset": 4982, "endOffset": 6325, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6329, "endOffset": 7097, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7101, "endOffset": 7455, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7459, "endOffset": 8066, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8070, "endOffset": 8322, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8326, "endOffset": 8774, "count": 0}], "isBlockCoverage": false}, {"functionName": "ClonedAbortSignal", "ranges": [{"startOffset": 8778, "endOffset": 8862, "count": 0}], "isBlockCoverage": false}, {"functionName": "ClonedAbortSignal.<computed>", "ranges": [{"startOffset": 8907, "endOffset": 8915, "count": 0}], "isBlockCoverage": false}, {"functionName": "createAbortSignal", "ranges": [{"startOffset": 9393, "endOffset": 9806, "count": 0}], "isBlockCoverage": false}, {"functionName": "abortSignal", "ranges": [{"startOffset": 9808, "endOffset": 10170, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 10172, "endOffset": 10828, "count": 0}], "isBlockCoverage": false}, {"functionName": "transferableAbortSignal", "ranges": [{"startOffset": 10976, "endOffset": 11168, "count": 0}], "isBlockCoverage": false}, {"functionName": "transferableAbortController", "ranges": [{"startOffset": 11240, "endOffset": 11329, "count": 0}], "isBlockCoverage": false}, {"functionName": "aborted", "ranges": [{"startOffset": 11423, "endOffset": 11980, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "87", "url": "node:internal/streams/end-of-stream", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8492, "count": 1}], "isBlockCoverage": false}, {"functionName": "isRequest", "ranges": [{"startOffset": 891, "endOffset": 986, "count": 0}], "isBlockCoverage": false}, {"functionName": "nop", "ranges": [{"startOffset": 1000, "endOffset": 1008, "count": 0}], "isBlockCoverage": false}, {"functionName": "eos", "ranges": [{"startOffset": 1011, "endOffset": 7061, "count": 0}], "isBlockCoverage": false}, {"functionName": "eosWeb", "ranges": [{"startOffset": 7063, "endOffset": 7972, "count": 0}], "isBlockCoverage": false}, {"functionName": "finished", "ranges": [{"startOffset": 7974, "endOffset": 8432, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "88", "url": "node:internal/streams/compose", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5511, "count": 1}], "isBlockCoverage": false}, {"functionName": "compose", "ranges": [{"startOffset": 592, "endOffset": 5509, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "89", "url": "node:internal/streams/pipeline", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12389, "count": 1}], "isBlockCoverage": false}, {"functionName": "destroyer", "ranges": [{"startOffset": 1056, "endOffset": 1485, "count": 0}], "isBlockCoverage": false}, {"functionName": "popCallback", "ranges": [{"startOffset": 1487, "endOffset": 1815, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeAsyncIterable", "ranges": [{"startOffset": 1817, "endOffset": 2103, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromReadable", "ranges": [{"startOffset": 2105, "endOffset": 2279, "count": 0}], "isBlockCoverage": false}, {"functionName": "pumpToNode", "ranges": [{"startOffset": 2281, "endOffset": 3325, "count": 0}], "isBlockCoverage": false}, {"functionName": "pumpToWeb", "ranges": [{"startOffset": 3327, "endOffset": 3925, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipeline", "ranges": [{"startOffset": 3927, "endOffset": 4020, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipelineImpl", "ranges": [{"startOffset": 4022, "endOffset": 10539, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipe", "ranges": [{"startOffset": 10541, "endOffset": 12342, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "90", "url": "node:internal/streams/destroy", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7354, "count": 1}], "isBlockCoverage": false}, {"functionName": "checkError", "ranges": [{"startOffset": 487, "endOffset": 798, "count": 2}, {"startOffset": 531, "endOffset": 796, "count": 0}], "isBlockCoverage": true}, {"functionName": "destroy", "ranges": [{"startOffset": 908, "endOffset": 1785, "count": 1}, {"startOffset": 1077, "endOffset": 1081, "count": 0}, {"startOffset": 1182, "endOffset": 1259, "count": 0}, {"startOffset": 1620, "endOffset": 1727, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1646, "endOffset": 1721, "count": 0}], "isBlockCoverage": false}, {"functionName": "_destroy", "ranges": [{"startOffset": 1787, "endOffset": 2411, "count": 1}, {"startOffset": 2372, "endOffset": 2409, "count": 0}], "isBlockCoverage": true}, {"functionName": "onDestroy", "ranges": [{"startOffset": 1847, "endOffset": 2316, "count": 1}, {"startOffset": 1889, "endOffset": 1910, "count": 0}, {"startOffset": 2159, "endOffset": 2181, "count": 0}, {"startOffset": 2196, "endOffset": 2256, "count": 0}], "isBlockCoverage": true}, {"functionName": "emitErrorCloseNT", "ranges": [{"startOffset": 2413, "endOffset": 2500, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitCloseNT", "ranges": [{"startOffset": 2502, "endOffset": 2822, "count": 1}, {"startOffset": 2791, "endOffset": 2820, "count": 0}], "isBlockCoverage": true}, {"functionName": "emitErrorNT", "ranges": [{"startOffset": 2824, "endOffset": 3171, "count": 0}], "isBlockCoverage": false}, {"functionName": "undestroy", "ranges": [{"startOffset": 3173, "endOffset": 3863, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 3865, "endOffset": 5004, "count": 0}], "isBlockCoverage": false}, {"functionName": "construct", "ranges": [{"startOffset": 5006, "endOffset": 5422, "count": 0}], "isBlockCoverage": false}, {"functionName": "constructNT", "ranges": [{"startOffset": 5424, "endOffset": 6163, "count": 0}], "isBlockCoverage": false}, {"functionName": "isRequest", "ranges": [{"startOffset": 6165, "endOffset": 6261, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitCloseLegacy", "ranges": [{"startOffset": 6263, "endOffset": 6323, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitErrorCloseLegacy", "ranges": [{"startOffset": 6325, "endOffset": 6445, "count": 0}], "isBlockCoverage": false}, {"functionName": "destroyer", "ranges": [{"startOffset": 6480, "endOffset": 7262, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "91", "url": "node:internal/streams/duplex", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6588, "count": 1}], "isBlockCoverage": false}, {"functionName": "Duplex", "ranges": [{"startOffset": 2350, "endOffset": 4313, "count": 4}, {"startOffset": 2414, "endOffset": 2441, "count": 0}, {"startOffset": 3128, "endOffset": 3264, "count": 0}, {"startOffset": 3302, "endOffset": 3477, "count": 1}, {"startOffset": 3529, "endOffset": 3555, "count": 0}, {"startOffset": 3608, "endOffset": 3636, "count": 0}, {"startOffset": 3690, "endOffset": 3720, "count": 0}, {"startOffset": 3775, "endOffset": 3807, "count": 0}, {"startOffset": 3860, "endOffset": 3888, "count": 0}, {"startOffset": 3945, "endOffset": 3981, "count": 0}, {"startOffset": 4013, "endOffset": 4050, "count": 0}, {"startOffset": 4054, "endOffset": 4096, "count": 0}, {"startOffset": 4160, "endOffset": 4311, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4194, "endOffset": 4305, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5461, "endOffset": 5673, "count": 234}, {"startOffset": 5559, "endOffset": 5590, "count": 0}, {"startOffset": 5634, "endOffset": 5666, "count": 0}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 5679, "endOffset": 5940, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyWebStreams", "ranges": [{"startOffset": 6014, "endOffset": 6178, "count": 0}], "isBlockCoverage": false}, {"functionName": "Duplex.fromWeb", "ranges": [{"startOffset": 6197, "endOffset": 6315, "count": 0}], "isBlockCoverage": false}, {"functionName": "Duplex.toWeb", "ranges": [{"startOffset": 6333, "endOffset": 6422, "count": 0}], "isBlockCoverage": false}, {"functionName": "Duplex.from", "ranges": [{"startOffset": 6455, "endOffset": 6586, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "92", "url": "node:internal/streams/legacy", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3251, "count": 1}], "isBlockCoverage": false}, {"functionName": "Stream", "ranges": [{"startOffset": 130, "endOffset": 178, "count": 6}], "isBlockCoverage": true}, {"functionName": "Stream.pipe", "ranges": [{"startOffset": 292, "endOffset": 2094, "count": 1}, {"startOffset": 798, "endOffset": 836, "count": 0}, {"startOffset": 838, "endOffset": 905, "count": 0}], "isBlockCoverage": true}, {"functionName": "ondata", "ranges": [{"startOffset": 344, "endOffset": 472, "count": 370}, {"startOffset": 422, "endOffset": 437, "count": 0}, {"startOffset": 439, "endOffset": 468, "count": 0}], "isBlockCoverage": true}, {"functionName": "ondrain", "ranges": [{"startOffset": 506, "endOffset": 603, "count": 0}], "isBlockCoverage": false}, {"functionName": "onend", "ranges": [{"startOffset": 933, "endOffset": 1019, "count": 0}], "isBlockCoverage": false}, {"functionName": "onclose", "ranges": [{"startOffset": 1024, "endOffset": 1156, "count": 0}], "isBlockCoverage": false}, {"functionName": "onerror", "ranges": [{"startOffset": 1215, "endOffset": 1341, "count": 0}], "isBlockCoverage": false}, {"functionName": "cleanup", "ranges": [{"startOffset": 1487, "endOffset": 1906, "count": 1}], "isBlockCoverage": true}, {"functionName": "eventNames", "ranges": [{"startOffset": 2127, "endOffset": 2393, "count": 0}], "isBlockCoverage": false}, {"functionName": "prependListener", "ranges": [{"startOffset": 2396, "endOffset": 3203, "count": 3}, {"startOffset": 2655, "endOffset": 3018, "count": 0}, {"startOffset": 3024, "endOffset": 3201, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "93", "url": "node:internal/streams/readable", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 51364, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1880, "endOffset": 1905, "count": 1}], "isBlockCoverage": true}, {"functionName": "nop", "ranges": [{"startOffset": 2867, "endOffset": 2875, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeBitMapDescriptor", "ranges": [{"startOffset": 3771, "endOffset": 3993, "count": 19}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3846, "endOffset": 3890, "count": 242}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 3896, "endOffset": 3985, "count": 26}, {"startOffset": 3926, "endOffset": 3946, "count": 3}, {"startOffset": 3946, "endOffset": 3979, "count": 23}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 6167, "endOffset": 6255, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6261, "endOffset": 6427, "count": 3}, {"startOffset": 6291, "endOffset": 6371, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 6504, "endOffset": 6604, "count": 370}, {"startOffset": 6572, "endOffset": 6601, "count": 0}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 6610, "endOffset": 6840, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6909, "endOffset": 6997, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7003, "endOffset": 7169, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7239, "endOffset": 7329, "count": 370}, {"startOffset": 7293, "endOffset": 7315, "count": 0}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 7335, "endOffset": 7504, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7573, "endOffset": 7676, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7682, "endOffset": 7949, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReadableState", "ranges": [{"startOffset": 7962, "endOffset": 9958, "count": 4}, {"startOffset": 8362, "endOffset": 8390, "count": 0}, {"startOffset": 8453, "endOffset": 8481, "count": 0}, {"startOffset": 8729, "endOffset": 8765, "count": 0}, {"startOffset": 8956, "endOffset": 8984, "count": 3}, {"startOffset": 9107, "endOffset": 9137, "count": 0}, {"startOffset": 9416, "endOffset": 9445, "count": 0}, {"startOffset": 9446, "endOffset": 9476, "count": 0}, {"startOffset": 9525, "endOffset": 9684, "count": 0}, {"startOffset": 9857, "endOffset": 9956, "count": 0}], "isBlockCoverage": true}, {"functionName": "onConstructed", "ranges": [{"startOffset": 10002, "endOffset": 10119, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable", "ranges": [{"startOffset": 10122, "endOffset": 11156, "count": 0}], "isBlockCoverage": true}, {"functionName": "Readable._destroy", "ranges": [{"startOffset": 11293, "endOffset": 11325, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.<computed>", "ranges": [{"startOffset": 11376, "endOffset": 11414, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.<computed>", "ranges": [{"startOffset": 11458, "endOffset": 11714, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.push", "ranges": [{"startOffset": 11943, "endOffset": 12212, "count": 606}, {"startOffset": 12143, "endOffset": 12209, "count": 0}], "isBlockCoverage": true}, {"functionName": "Readable.unshift", "ranges": [{"startOffset": 12308, "endOffset": 12575, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkUnshiftByteMode", "ranges": [{"startOffset": 12579, "endOffset": 13645, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkUnshiftObjectMode", "ranges": [{"startOffset": 13647, "endOffset": 13887, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkUnshiftValue", "ranges": [{"startOffset": 13889, "endOffset": 14222, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkPushByteMode", "ranges": [{"startOffset": 14224, "endOffset": 15616, "count": 606}, {"startOffset": 14318, "endOffset": 14404, "count": 2}, {"startOffset": 14404, "endOffset": 14439, "count": 604}, {"startOffset": 14439, "endOffset": 14604, "count": 370}, {"startOffset": 14604, "endOffset": 14957, "count": 234}, {"startOffset": 14663, "endOffset": 14957, "count": 0}, {"startOffset": 14957, "endOffset": 14992, "count": 604}, {"startOffset": 14994, "endOffset": 15097, "count": 0}, {"startOffset": 15097, "endOffset": 15137, "count": 604}, {"startOffset": 15137, "endOffset": 15221, "count": 0}, {"startOffset": 15221, "endOffset": 15278, "count": 604}, {"startOffset": 15278, "endOffset": 15301, "count": 0}, {"startOffset": 15301, "endOffset": 15372, "count": 604}, {"startOffset": 15372, "endOffset": 15384, "count": 0}, {"startOffset": 15386, "endOffset": 15543, "count": 0}, {"startOffset": 15543, "endOffset": 15615, "count": 604}], "isBlockCoverage": true}, {"functionName": "readableAddChunkPushObjectMode", "ranges": [{"startOffset": 15618, "endOffset": 16215, "count": 0}], "isBlockCoverage": false}, {"functionName": "canPushMore", "ranges": [{"startOffset": 16217, "endOffset": 16547, "count": 604}, {"startOffset": 16522, "endOffset": 16543, "count": 0}], "isBlockCoverage": true}, {"functionName": "addChunk", "ranges": [{"startOffset": 16549, "endOffset": 17493, "count": 604}, {"startOffset": 16864, "endOffset": 16910, "count": 0}, {"startOffset": 17033, "endOffset": 17459, "count": 0}], "isBlockCoverage": true}, {"functionName": "Readable.isPaused", "ranges": [{"startOffset": 17525, "endOffset": 17680, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.setEncoding", "ranges": [{"startOffset": 17744, "endOffset": 18314, "count": 0}], "isBlockCoverage": false}, {"functionName": "computeNewHighWaterMark", "ranges": [{"startOffset": 18375, "endOffset": 18726, "count": 0}], "isBlockCoverage": false}, {"functionName": "howMuchToRead", "ranges": [{"startOffset": 18839, "endOffset": 19320, "count": 472}, {"startOffset": 18887, "endOffset": 18944, "count": 4}, {"startOffset": 18950, "endOffset": 18959, "count": 470}, {"startOffset": 18959, "endOffset": 19007, "count": 2}, {"startOffset": 19007, "endOffset": 19016, "count": 0}, {"startOffset": 19016, "endOffset": 19219, "count": 2}, {"startOffset": 19144, "endOffset": 19190, "count": 0}, {"startOffset": 19219, "endOffset": 19313, "count": 0}, {"startOffset": 19314, "endOffset": 19317, "count": 0}], "isBlockCoverage": true}, {"functionName": "Readable.read", "ranges": [{"startOffset": 19417, "endOffset": 24092, "count": 472}, {"startOffset": 19601, "endOffset": 19619, "count": 4}, {"startOffset": 19619, "endOffset": 19686, "count": 468}, {"startOffset": 19650, "endOffset": 19686, "count": 0}, {"startOffset": 19851, "endOffset": 19900, "count": 0}, {"startOffset": 19921, "endOffset": 19956, "count": 4}, {"startOffset": 20142, "endOffset": 20188, "count": 468}, {"startOffset": 20189, "endOffset": 20341, "count": 465}, {"startOffset": 20272, "endOffset": 20298, "count": 0}, {"startOffset": 20343, "endOffset": 20520, "count": 0}, {"startOffset": 20664, "endOffset": 20739, "count": 3}, {"startOffset": 20739, "endOffset": 22034, "count": 469}, {"startOffset": 22034, "endOffset": 22075, "count": 0}, {"startOffset": 22077, "endOffset": 22462, "count": 469}, {"startOffset": 22462, "endOffset": 22540, "count": 2}, {"startOffset": 22540, "endOffset": 23164, "count": 467}, {"startOffset": 22849, "endOffset": 22901, "count": 0}, {"startOffset": 23128, "endOffset": 23160, "count": 0}, {"startOffset": 23164, "endOffset": 23194, "count": 469}, {"startOffset": 23194, "endOffset": 23219, "count": 0}, {"startOffset": 23219, "endOffset": 23358, "count": 469}, {"startOffset": 23339, "endOffset": 23342, "count": 0}, {"startOffset": 23358, "endOffset": 23540, "count": 0}, {"startOffset": 23540, "endOffset": 23926, "count": 469}, {"startOffset": 23863, "endOffset": 23896, "count": 2}, {"startOffset": 23904, "endOffset": 23922, "count": 0}, {"startOffset": 23926, "endOffset": 23947, "count": 469}, {"startOffset": 23947, "endOffset": 24005, "count": 0}, {"startOffset": 24007, "endOffset": 24075, "count": 0}, {"startOffset": 24075, "endOffset": 24091, "count": 469}], "isBlockCoverage": true}, {"functionName": "onEofChunk", "ranges": [{"startOffset": 24095, "endOffset": 25057, "count": 2}, {"startOffset": 24193, "endOffset": 24200, "count": 0}, {"startOffset": 24252, "endOffset": 24274, "count": 0}, {"startOffset": 24298, "endOffset": 24485, "count": 0}, {"startOffset": 24551, "endOffset": 24753, "count": 0}], "isBlockCoverage": true}, {"functionName": "emitReadable", "ranges": [{"startOffset": 25258, "endOffset": 25589, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitReadable_", "ranges": [{"startOffset": 25591, "endOffset": 26263, "count": 2}, {"startOffset": 26182, "endOffset": 26224, "count": 0}, {"startOffset": 26225, "endOffset": 26240, "count": 0}], "isBlockCoverage": true}, {"functionName": "maybeReadMore", "ranges": [{"startOffset": 26613, "endOffset": 26820, "count": 604}, {"startOffset": 26725, "endOffset": 26818, "count": 466}], "isBlockCoverage": true}, {"functionName": "maybeReadMore_", "ranges": [{"startOffset": 26822, "endOffset": 28651, "count": 466}, {"startOffset": 28317, "endOffset": 28435, "count": 465}, {"startOffset": 28365, "endOffset": 28434, "count": 0}, {"startOffset": 28437, "endOffset": 28615, "count": 465}], "isBlockCoverage": true}, {"functionName": "Readable._read", "ranges": [{"startOffset": 28922, "endOffset": 28988, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.pipe", "ranges": [{"startOffset": 29017, "endOffset": 33527, "count": 1}, {"startOffset": 29134, "endOffset": 29358, "count": 0}, {"startOffset": 29478, "endOffset": 29503, "count": 0}, {"startOffset": 29619, "endOffset": 29627, "count": 0}, {"startOffset": 29676, "endOffset": 29700, "count": 0}, {"startOffset": 33398, "endOffset": 33416, "count": 0}, {"startOffset": 33460, "endOffset": 33509, "count": 0}], "isBlockCoverage": true}, {"functionName": "onunpipe", "ranges": [{"startOffset": 29770, "endOffset": 29996, "count": 0}], "isBlockCoverage": false}, {"functionName": "onend", "ranges": [{"startOffset": 30000, "endOffset": 30058, "count": 1}], "isBlockCoverage": true}, {"functionName": "cleanup", "ranges": [{"startOffset": 30103, "endOffset": 30995, "count": 0}], "isBlockCoverage": false}, {"functionName": "pause", "ranges": [{"startOffset": 30999, "endOffset": 32001, "count": 0}], "isBlockCoverage": false}, {"functionName": "ondata", "ranges": [{"startOffset": 32031, "endOffset": 32191, "count": 234}, {"startOffset": 32165, "endOffset": 32187, "count": 0}], "isBlockCoverage": true}, {"functionName": "onerror", "ranges": [{"startOffset": 32313, "endOffset": 32724, "count": 0}], "isBlockCoverage": false}, {"functionName": "onclose", "ranges": [{"startOffset": 32904, "endOffset": 32987, "count": 0}], "isBlockCoverage": false}, {"functionName": "onfinish", "ranges": [{"startOffset": 33021, "endOffset": 33126, "count": 0}], "isBlockCoverage": false}, {"functionName": "unpipe", "ranges": [{"startOffset": 33163, "endOffset": 33229, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipeOnDrain", "ranges": [{"startOffset": 33530, "endOffset": 34207, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.unpipe", "ranges": [{"startOffset": 34238, "endOffset": 34939, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.on", "ranges": [{"startOffset": 35065, "endOffset": 36074, "count": 14}, {"startOffset": 35197, "endOffset": 35626, "count": 2}, {"startOffset": 35426, "endOffset": 35446, "count": 0}, {"startOffset": 35626, "endOffset": 36057, "count": 12}, {"startOffset": 35655, "endOffset": 36057, "count": 0}], "isBlockCoverage": true}, {"functionName": "Readable.removeListener", "ranges": [{"startOffset": 36169, "endOffset": 36876, "count": 4}, {"startOffset": 36369, "endOffset": 36755, "count": 0}, {"startOffset": 36779, "endOffset": 36814, "count": 0}, {"startOffset": 36816, "endOffset": 36859, "count": 0}], "isBlockCoverage": true}, {"functionName": "Readable.removeAllListeners", "ranges": [{"startOffset": 36979, "endOffset": 37573, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateReadableListening", "ranges": [{"startOffset": 37576, "endOffset": 38278, "count": 0}], "isBlockCoverage": false}, {"functionName": "nReadingNextTick", "ranges": [{"startOffset": 38280, "endOffset": 38368, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.resume", "ranges": [{"startOffset": 38522, "endOffset": 39027, "count": 2}, {"startOffset": 38874, "endOffset": 38921, "count": 0}], "isBlockCoverage": true}, {"functionName": "resume", "ranges": [{"startOffset": 39030, "endOffset": 39203, "count": 2}], "isBlockCoverage": true}, {"functionName": "resume_", "ranges": [{"startOffset": 39205, "endOffset": 39519, "count": 2}, {"startOffset": 39502, "endOffset": 39517, "count": 0}], "isBlockCoverage": true}, {"functionName": "Readable.pause", "ranges": [{"startOffset": 39548, "endOffset": 39860, "count": 0}], "isBlockCoverage": false}, {"functionName": "flow", "ranges": [{"startOffset": 39863, "endOffset": 40014, "count": 4}, {"startOffset": 40011, "endOffset": 40012, "count": 0}], "isBlockCoverage": true}, {"functionName": "Readable.wrap", "ranges": [{"startOffset": 40198, "endOffset": 41219, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.<computed>", "ranges": [{"startOffset": 41264, "endOffset": 41316, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.iterator", "ranges": [{"startOffset": 41349, "endOffset": 41492, "count": 0}], "isBlockCoverage": false}, {"functionName": "streamToAsyncIterator", "ranges": [{"startOffset": 41495, "endOffset": 41744, "count": 0}], "isBlockCoverage": false}, {"functionName": "createAsyncIterator", "ranges": [{"startOffset": 41746, "endOffset": 42822, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 43041, "endOffset": 43430, "count": 1}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 43436, "endOffset": 43566, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 43648, "endOffset": 43712, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 43794, "endOffset": 44002, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44090, "endOffset": 44156, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44237, "endOffset": 44319, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44401, "endOffset": 44461, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 44472, "endOffset": 44581, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44657, "endOffset": 44711, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44791, "endOffset": 44879, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44957, "endOffset": 45042, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 45111, "endOffset": 45195, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 45240, "endOffset": 45324, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 45395, "endOffset": 45482, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 45488, "endOffset": 45777, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 45852, "endOffset": 45940, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 46081, "endOffset": 46126, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 46206, "endOffset": 46264, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 46270, "endOffset": 46431, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromList", "ranges": [{"startOffset": 46737, "endOffset": 49189, "count": 0}], "isBlockCoverage": false}, {"functionName": "endReadable", "ranges": [{"startOffset": 49191, "endOffset": 49417, "count": 3}], "isBlockCoverage": true}, {"functionName": "endReadableNT", "ranges": [{"startOffset": 49419, "endOffset": 50312, "count": 3}, {"startOffset": 49605, "endOffset": 49626, "count": 2}, {"startOffset": 49628, "endOffset": 50310, "count": 2}, {"startOffset": 49713, "endOffset": 49746, "count": 1}, {"startOffset": 49748, "endOffset": 49802, "count": 0}, {"startOffset": 50202, "endOffset": 50230, "count": 1}, {"startOffset": 50265, "endOffset": 50300, "count": 1}], "isBlockCoverage": true}, {"functionName": "endWritableNT", "ranges": [{"startOffset": 50314, "endOffset": 50474, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.from", "ranges": [{"startOffset": 50492, "endOffset": 50561, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyWebStreams", "ranges": [{"startOffset": 50626, "endOffset": 50790, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.fromWeb", "ranges": [{"startOffset": 50811, "endOffset": 50945, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.toWeb", "ranges": [{"startOffset": 50965, "endOffset": 51099, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.wrap", "ranges": [{"startOffset": 51118, "endOffset": 51362, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "94", "url": "node:internal/streams/add-abort-signal", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1741, "count": 1}], "isBlockCoverage": false}, {"functionName": "validateAbortSignal", "ranges": [{"startOffset": 570, "endOffset": 729, "count": 0}], "isBlockCoverage": false}, {"functionName": "addAbortSignal", "ranges": [{"startOffset": 764, "endOffset": 1075, "count": 0}], "isBlockCoverage": false}, {"functionName": "module.exports.addAbortSignalNoValidate", "ranges": [{"startOffset": 1120, "endOffset": 1739, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "95", "url": "node:internal/streams/state", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1332, "count": 1}], "isBlockCoverage": false}, {"functionName": "highWaterMarkFrom", "ranges": [{"startOffset": 287, "endOffset": 454, "count": 9}, {"startOffset": 385, "endOffset": 408, "count": 0}, {"startOffset": 445, "endOffset": 451, "count": 0}], "isBlockCoverage": true}, {"functionName": "getDefaultHighWaterMark", "ranges": [{"startOffset": 456, "endOffset": 586, "count": 7}, {"startOffset": 523, "endOffset": 555, "count": 0}], "isBlockCoverage": true}, {"functionName": "setDefaultHighWaterMark", "ranges": [{"startOffset": 588, "endOffset": 799, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHighWaterMark", "ranges": [{"startOffset": 801, "endOffset": 1234, "count": 9}, {"startOffset": 948, "endOffset": 1160, "count": 2}, {"startOffset": 992, "endOffset": 1129, "count": 0}, {"startOffset": 1160, "endOffset": 1233, "count": 7}], "isBlockCoverage": true}]}, {"scriptId": "96", "url": "node:string_decoder", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5141, "count": 1}], "isBlockCoverage": false}, {"functionName": "normalizeEncoding", "ranges": [{"startOffset": 2172, "endOffset": 2422, "count": 0}], "isBlockCoverage": false}, {"functionName": "StringDecoder", "ranges": [{"startOffset": 2633, "endOffset": 2832, "count": 0}], "isBlockCoverage": false}, {"functionName": "write", "ranges": [{"startOffset": 3155, "endOffset": 3540, "count": 0}], "isBlockCoverage": false}, {"functionName": "end", "ranges": [{"startOffset": 3817, "endOffset": 4008, "count": 0}], "isBlockCoverage": false}, {"functionName": "text", "ranges": [{"startOffset": 4219, "endOffset": 4376, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4514, "endOffset": 4727, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4819, "endOffset": 4882, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4975, "endOffset": 5090, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "97", "url": "node:internal/streams/from", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4341, "count": 1}], "isBlockCoverage": false}, {"functionName": "from", "ranges": [{"startOffset": 241, "endOffset": 4316, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "98", "url": "node:internal/streams/writable", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33628, "count": 1}], "isBlockCoverage": false}, {"functionName": "nop", "ranges": [{"startOffset": 2779, "endOffset": 2796, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeBitMapDescriptor", "ranges": [{"startOffset": 3852, "endOffset": 4074, "count": 20}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3927, "endOffset": 3971, "count": 8}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 3977, "endOffset": 4066, "count": 34}, {"startOffset": 4007, "endOffset": 4027, "count": 10}, {"startOffset": 4027, "endOffset": 4060, "count": 24}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 7038, "endOffset": 7116, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7122, "endOffset": 7288, "count": 3}, {"startOffset": 7152, "endOffset": 7232, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 7358, "endOffset": 7458, "count": 382}, {"startOffset": 7409, "endOffset": 7443, "count": 4}, {"startOffset": 7444, "endOffset": 7455, "count": 378}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 7464, "endOffset": 7737, "count": 1}, {"startOffset": 7502, "endOffset": 7564, "count": 0}, {"startOffset": 7581, "endOffset": 7642, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 7814, "endOffset": 7914, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7920, "endOffset": 8150, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8291, "endOffset": 8368, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 8374, "endOffset": 8538, "count": 370}, {"startOffset": 8439, "endOffset": 8482, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 8725, "endOffset": 8825, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 8831, "endOffset": 9028, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9098, "endOffset": 9176, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 9182, "endOffset": 9349, "count": 0}], "isBlockCoverage": false}, {"functionName": "WritableState", "ranges": [{"startOffset": 9362, "endOffset": 11664, "count": 4}, {"startOffset": 9640, "endOffset": 9668, "count": 0}, {"startOffset": 9731, "endOffset": 9759, "count": 0}, {"startOffset": 10050, "endOffset": 10086, "count": 0}, {"startOffset": 10140, "endOffset": 10171, "count": 1}, {"startOffset": 10278, "endOffset": 10306, "count": 3}, {"startOffset": 10429, "endOffset": 10459, "count": 0}, {"startOffset": 10716, "endOffset": 10722, "count": 0}, {"startOffset": 10754, "endOffset": 10783, "count": 0}, {"startOffset": 10784, "endOffset": 10814, "count": 0}, {"startOffset": 10863, "endOffset": 11072, "count": 0}], "isBlockCoverage": true}, {"functionName": "resetBuffer", "ranges": [{"startOffset": 11666, "endOffset": 11830, "count": 4}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 11868, "endOffset": 11997, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 12093, "endOffset": 12204, "count": 0}], "isBlockCoverage": false}, {"functionName": "onConstructed", "ranges": [{"startOffset": 12253, "endOffset": 12439, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable", "ranges": [{"startOffset": 12442, "endOffset": 13548, "count": 0}], "isBlockCoverage": false}, {"functionName": "value", "ranges": [{"startOffset": 13630, "endOffset": 13834, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.pipe", "ranges": [{"startOffset": 13935, "endOffset": 14003, "count": 0}], "isBlockCoverage": false}, {"functionName": "_write", "ranges": [{"startOffset": 14006, "endOffset": 15404, "count": 370}, {"startOffset": 14110, "endOffset": 14137, "count": 0}, {"startOffset": 14182, "endOffset": 14227, "count": 0}, {"startOffset": 14366, "endOffset": 14389, "count": 0}, {"startOffset": 14396, "endOffset": 14516, "count": 0}, {"startOffset": 14553, "endOffset": 14695, "count": 0}, {"startOffset": 14764, "endOffset": 15015, "count": 0}, {"startOffset": 15071, "endOffset": 15120, "count": 0}, {"startOffset": 15166, "endOffset": 15216, "count": 0}, {"startOffset": 15229, "endOffset": 15320, "count": 0}], "isBlockCoverage": true}, {"functionName": "Writable.write", "ranges": [{"startOffset": 15433, "endOffset": 15624, "count": 370}, {"startOffset": 15488, "endOffset": 15521, "count": 0}, {"startOffset": 15523, "endOffset": 15568, "count": 0}], "isBlockCoverage": true}, {"functionName": "Writable.cork", "ranges": [{"startOffset": 15653, "endOffset": 15751, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.uncork", "ranges": [{"startOffset": 15782, "endOffset": 16020, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDefaultEncoding", "ranges": [{"startOffset": 16063, "endOffset": 16388, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeOr<PERSON>uffer", "ranges": [{"startOffset": 16578, "endOffset": 17782, "count": 370}, {"startOffset": 16695, "endOffset": 16698, "count": 0}, {"startOffset": 16829, "endOffset": 17236, "count": 0}, {"startOffset": 17296, "endOffset": 17335, "count": 0}, {"startOffset": 17526, "endOffset": 17547, "count": 0}, {"startOffset": 17562, "endOffset": 17600, "count": 0}], "isBlockCoverage": true}, {"functionName": "doWrite", "ranges": [{"startOffset": 17784, "endOffset": 18221, "count": 0}], "isBlockCoverage": false}, {"functionName": "onwriteError", "ranges": [{"startOffset": 18223, "endOffset": 18608, "count": 0}], "isBlockCoverage": false}, {"functionName": "onwrite", "ranges": [{"startOffset": 18610, "endOffset": 21216, "count": 370}, {"startOffset": 18727, "endOffset": 18801, "count": 0}, {"startOffset": 18895, "endOffset": 18917, "count": 0}, {"startOffset": 19066, "endOffset": 19680, "count": 0}, {"startOffset": 19731, "endOffset": 19772, "count": 0}, {"startOffset": 19849, "endOffset": 19870, "count": 0}, {"startOffset": 20304, "endOffset": 20424, "count": 0}, {"startOffset": 20508, "endOffset": 20567, "count": 0}, {"startOffset": 20585, "endOffset": 21151, "count": 0}, {"startOffset": 21157, "endOffset": 21210, "count": 0}], "isBlockCoverage": true}, {"functionName": "afterWriteTick", "ranges": [{"startOffset": 21218, "endOffset": 21405, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterWrite", "ranges": [{"startOffset": 21407, "endOffset": 21915, "count": 0}], "isBlockCoverage": false}, {"functionName": "errorBuffer", "ranges": [{"startOffset": 21987, "endOffset": 22538, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 22604, "endOffset": 24274, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable._write", "ranges": [{"startOffset": 24304, "endOffset": 24473, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.end", "ranges": [{"startOffset": 24537, "endOffset": 26177, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON>sh", "ranges": [{"startOffset": 26180, "endOffset": 26609, "count": 0}], "isBlockCoverage": false}, {"functionName": "onFinish", "ranges": [{"startOffset": 26611, "endOffset": 27243, "count": 0}], "isBlockCoverage": false}, {"functionName": "prefinish", "ranges": [{"startOffset": 27245, "endOffset": 27769, "count": 0}], "isBlockCoverage": false}, {"functionName": "finishMaybe", "ranges": [{"startOffset": 27771, "endOffset": 28270, "count": 0}], "isBlockCoverage": false}, {"functionName": "finish", "ranges": [{"startOffset": 28272, "endOffset": 28915, "count": 0}], "isBlockCoverage": false}, {"functionName": "callFinishedCallbacks", "ranges": [{"startOffset": 28917, "endOffset": 29235, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 29319, "endOffset": 29422, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 29470, "endOffset": 29576, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 29582, "endOffset": 29832, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 29879, "endOffset": 30282, "count": 372}, {"startOffset": 30202, "endOffset": 30275, "count": 371}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 30288, "endOffset": 30422, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 30477, "endOffset": 30595, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 30652, "endOffset": 30772, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 30825, "endOffset": 30920, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 30972, "endOffset": 31088, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31144, "endOffset": 31297, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31357, "endOffset": 31454, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31507, "endOffset": 31600, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31653, "endOffset": 31743, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31812, "endOffset": 31909, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31968, "endOffset": 32225, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.destroy", "ranges": [{"startOffset": 32303, "endOffset": 32583, "count": 1}, {"startOffset": 32448, "endOffset": 32485, "count": 0}, {"startOffset": 32487, "endOffset": 32534, "count": 0}], "isBlockCoverage": true}, {"functionName": "Writable._destroy", "ranges": [{"startOffset": 32671, "endOffset": 32703, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.<computed>", "ranges": [{"startOffset": 32754, "endOffset": 32792, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyWebStreams", "ranges": [{"startOffset": 32857, "endOffset": 33021, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.fromWeb", "ranges": [{"startOffset": 33042, "endOffset": 33176, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.toWeb", "ranges": [{"startOffset": 33196, "endOffset": 33303, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.<computed>", "ranges": [{"startOffset": 33347, "endOffset": 33626, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "99", "url": "node:stream/promises", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 917, "count": 1}], "isBlockCoverage": false}, {"functionName": "pipeline", "ranges": [{"startOffset": 318, "endOffset": 869, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "100", "url": "node:internal/streams/transform", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7123, "count": 1}], "isBlockCoverage": false}, {"functionName": "Transform", "ranges": [{"startOffset": 3920, "endOffset": 5500, "count": 1}, {"startOffset": 3990, "endOffset": 4020, "count": 0}, {"startOffset": 4295, "endOffset": 4301, "count": 0}, {"startOffset": 4338, "endOffset": 4717, "count": 0}, {"startOffset": 5044, "endOffset": 5080, "count": 0}, {"startOffset": 5133, "endOffset": 5161, "count": 0}], "isBlockCoverage": true}, {"functionName": "final", "ranges": [{"startOffset": 5502, "endOffset": 5946, "count": 0}], "isBlockCoverage": false}, {"functionName": "prefinish", "ranges": [{"startOffset": 5948, "endOffset": 6029, "count": 0}], "isBlockCoverage": false}, {"functionName": "Transform._transform", "ranges": [{"startOffset": 6101, "endOffset": 6196, "count": 0}], "isBlockCoverage": false}, {"functionName": "Transform._write", "ranges": [{"startOffset": 6228, "endOffset": 6965, "count": 0}], "isBlockCoverage": false}, {"functionName": "Transform._read", "ranges": [{"startOffset": 6996, "endOffset": 7121, "count": 232}, {"startOffset": 7032, "endOffset": 7119, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "101", "url": "node:internal/streams/passthrough", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1762, "count": 1}], "isBlockCoverage": false}, {"functionName": "PassThrough", "ranges": [{"startOffset": 1529, "endOffset": 1671, "count": 0}], "isBlockCoverage": false}, {"functionName": "PassThrough._transform", "ranges": [{"startOffset": 1708, "endOffset": 1760, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "102", "url": "node:internal/streams/duplexpair", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1373, "count": 1}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 219, "endOffset": 1136, "count": 0}], "isBlockCoverage": false}, {"functionName": "duplexPair", "ranges": [{"startOffset": 1138, "endOffset": 1343, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "103", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/xtend/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 474, "count": 1}], "isBlockCoverage": true}, {"functionName": "extend", "ranges": [{"startOffset": 96, "endOffset": 473, "count": 1}, {"startOffset": 184, "endOffset": 452, "count": 2}, {"startOffset": 283, "endOffset": 446, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "104", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/xtend/node_modules/object-keys/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 52, "count": 1}, {"startOffset": 29, "endOffset": 49, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "105", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/xtend/has-keys.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 162, "count": 1}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 26, "endOffset": 161, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "106", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/duplexer/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1884, "count": 1}], "isBlockCoverage": true}, {"functionName": "for<PERSON>ach", "ranges": [{"startOffset": 211, "endOffset": 373, "count": 3}, {"startOffset": 299, "endOffset": 371, "count": 0}], "isBlockCoverage": true}, {"functionName": "duplex", "ranges": [{"startOffset": 375, "endOffset": 1883, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 635, "endOffset": 680, "count": 0}], "isBlockCoverage": false}, {"functionName": "proxyWriter", "ranges": [{"startOffset": 846, "endOffset": 1024, "count": 3}], "isBlockCoverage": true}, {"functionName": "method", "ranges": [{"startOffset": 926, "endOffset": 1018, "count": 235}], "isBlockCoverage": true}, {"functionName": "proxyReader", "ranges": [{"startOffset": 1030, "endOffset": 1350, "count": 2}], "isBlockCoverage": true}, {"functionName": "method", "ranges": [{"startOffset": 1110, "endOffset": 1344, "count": 0}], "isBlockCoverage": false}, {"functionName": "proxyStream", "ranges": [{"startOffset": 1356, "endOffset": 1599, "count": 2}], "isBlockCoverage": true}, {"functionName": "reemit", "ranges": [{"startOffset": 1438, "endOffset": 1593, "count": 370}], "isBlockCoverage": true}, {"functionName": "handleEnd", "ranges": [{"startOffset": 1605, "endOffset": 1813, "count": 1}, {"startOffset": 1647, "endOffset": 1677, "count": 0}], "isBlockCoverage": true}, {"functionName": "reemit", "ranges": [{"startOffset": 1819, "endOffset": 1881, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "107", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/tap-parser/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5182, "count": 1}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 460, "endOffset": 1221, "count": 2}, {"startOffset": 518, "endOffset": 602, "count": 1}, {"startOffset": 602, "endOffset": 625, "count": 0}, {"startOffset": 625, "endOffset": 1220, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 879, "endOffset": 981, "count": 1}, {"startOffset": 924, "endOffset": 949, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1089, "endOffset": 1217, "count": 0}], "isBlockCoverage": false}, {"functionName": "Parser._write", "ranges": [{"startOffset": 1249, "endOffset": 1497, "count": 234}, {"startOffset": 1376, "endOffset": 1441, "count": 291}], "isBlockCoverage": true}, {"functionName": "Parser._onassert", "ranges": [{"startOffset": 1529, "endOffset": 2092, "count": 202}, {"startOffset": 1625, "endOffset": 1637, "count": 0}, {"startOffset": 1639, "endOffset": 1658, "count": 0}, {"startOffset": 1697, "endOffset": 1711, "count": 0}, {"startOffset": 1732, "endOffset": 1752, "count": 0}, {"startOffset": 1860, "endOffset": 1944, "count": 0}, {"startOffset": 1959, "endOffset": 1992, "count": 201}, {"startOffset": 1994, "endOffset": 2090, "count": 0}], "isBlockCoverage": true}, {"functionName": "Parser._onplan", "ranges": [{"startOffset": 2122, "endOffset": 2834, "count": 1}, {"startOffset": 2226, "endOffset": 2345, "count": 0}, {"startOffset": 2390, "endOffset": 2489, "count": 0}, {"startOffset": 2512, "endOffset": 2774, "count": 0}], "isBlockCoverage": true}, {"functionName": "Parser._online", "ranges": [{"startOffset": 2865, "endOffset": 3739, "count": 291}, {"startOffset": 2929, "endOffset": 3039, "count": 1}, {"startOffset": 2991, "endOffset": 2997, "count": 0}, {"startOffset": 3039, "endOffset": 3737, "count": 290}, {"startOffset": 3080, "endOffset": 3123, "count": 84}, {"startOffset": 3123, "endOffset": 3737, "count": 206}, {"startOffset": 3159, "endOffset": 3522, "count": 202}, {"startOffset": 3401, "endOffset": 3472, "count": 0}, {"startOffset": 3522, "endOffset": 3737, "count": 4}, {"startOffset": 3560, "endOffset": 3703, "count": 1}, {"startOffset": 3703, "endOffset": 3737, "count": 3}], "isBlockCoverage": true}, {"functionName": "Parser._checkAssertionStart", "ranges": [{"startOffset": 3782, "endOffset": 4112, "count": 1}, {"startOffset": 3856, "endOffset": 3863, "count": 0}, {"startOffset": 3893, "endOffset": 3900, "count": 0}, {"startOffset": 3924, "endOffset": 3931, "count": 0}, {"startOffset": 3997, "endOffset": 4111, "count": 0}], "isBlockCoverage": true}, {"functionName": "Parser._finished", "ranges": [{"startOffset": 4144, "endOffset": 5180, "count": 1}, {"startOffset": 4226, "endOffset": 4316, "count": 0}, {"startOffset": 4472, "endOffset": 4485, "count": 0}, {"startOffset": 4487, "endOffset": 4583, "count": 0}, {"startOffset": 4602, "endOffset": 4633, "count": 0}, {"startOffset": 4635, "endOffset": 4747, "count": 0}, {"startOffset": 4902, "endOffset": 4997, "count": 0}, {"startOffset": 5050, "endOffset": 5143, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "108", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/tap-parser/node_modules/readable-stream/readable.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 470, "count": 1}, {"startOffset": 428, "endOffset": 469, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "109", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/tap-parser/node_modules/readable-stream/lib/_stream_readable.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 25959, "count": 1}, {"startOffset": 1436, "endOffset": 1524, "count": 0}, {"startOffset": 1825, "endOffset": 1860, "count": 0}], "isBlockCoverage": true}, {"functionName": "EE.listenerCount", "ranges": [{"startOffset": 1455, "endOffset": 1523, "count": 0}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 1843, "endOffset": 1857, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReadableState", "ranges": [{"startOffset": 1916, "endOffset": 4169, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable", "ranges": [{"startOffset": 4171, "endOffset": 4433, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.push", "ranges": [{"startOffset": 4661, "endOffset": 5005, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.unshift", "ranges": [{"startOffset": 5100, "endOffset": 5211, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunk", "ranges": [{"startOffset": 5214, "endOffset": 6587, "count": 0}], "isBlockCoverage": false}, {"functionName": "needMoreData", "ranges": [{"startOffset": 6992, "endOffset": 7160, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.setEncoding", "ranges": [{"startOffset": 7223, "endOffset": 7433, "count": 0}], "isBlockCoverage": false}, {"functionName": "roundUpToNextPowerOf2", "ranges": [{"startOffset": 7491, "endOffset": 7701, "count": 0}], "isBlockCoverage": false}, {"functionName": "howMuchToRead", "ranges": [{"startOffset": 7703, "endOffset": 8571, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.read", "ranges": [{"startOffset": 8668, "endOffset": 12209, "count": 0}], "isBlockCoverage": false}, {"functionName": "chunkInvalid", "ranges": [{"startOffset": 12212, "endOffset": 12473, "count": 0}], "isBlockCoverage": false}, {"functionName": "onEofChunk", "ranges": [{"startOffset": 12476, "endOffset": 12828, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitReadable", "ranges": [{"startOffset": 13029, "endOffset": 13376, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitReadable_", "ranges": [{"startOffset": 13378, "endOffset": 13481, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeReadMore", "ranges": [{"startOffset": 13831, "endOffset": 14013, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeReadMore_", "ranges": [{"startOffset": 14015, "endOffset": 14402, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable._read", "ranges": [{"startOffset": 14673, "endOffset": 14740, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.pipe", "ranges": [{"startOffset": 14769, "endOffset": 18261, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipeOnDrain", "ranges": [{"startOffset": 18264, "endOffset": 18570, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.unpipe", "ranges": [{"startOffset": 18601, "endOffset": 19727, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.on", "ranges": [{"startOffset": 19852, "endOffset": 20643, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.resume", "ranges": [{"startOffset": 20854, "endOffset": 21100, "count": 0}], "isBlockCoverage": false}, {"functionName": "resume", "ranges": [{"startOffset": 21103, "endOffset": 21279, "count": 0}], "isBlockCoverage": false}, {"functionName": "resume_", "ranges": [{"startOffset": 21281, "endOffset": 21449, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.pause", "ranges": [{"startOffset": 21478, "endOffset": 21706, "count": 0}], "isBlockCoverage": false}, {"functionName": "flow", "ranges": [{"startOffset": 21709, "endOffset": 21919, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.wrap", "ranges": [{"startOffset": 22103, "endOffset": 23473, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromList", "ranges": [{"startOffset": 23661, "endOffset": 25164, "count": 0}], "isBlockCoverage": false}, {"functionName": "endReadable", "ranges": [{"startOffset": 25166, "endOffset": 25735, "count": 0}], "isBlockCoverage": false}, {"functionName": "for<PERSON>ach", "ranges": [{"startOffset": 25737, "endOffset": 25833, "count": 0}], "isBlockCoverage": false}, {"functionName": "indexOf", "ranges": [{"startOffset": 25835, "endOffset": 25958, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "110", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/tap-parser/node_modules/isarray/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 120, "count": 1}, {"startOffset": 31, "endOffset": 118, "count": 0}], "isBlockCoverage": true}, {"functionName": "module.exports", "ranges": [{"startOffset": 34, "endOffset": 118, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "111", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/core-util-is/lib/util.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3039, "count": 1}], "isBlockCoverage": true}, {"functionName": "isArray", "ranges": [{"startOffset": 1284, "endOffset": 1418, "count": 0}], "isBlockCoverage": false}, {"functionName": "isBoolean", "ranges": [{"startOffset": 1447, "endOffset": 1509, "count": 0}], "isBlockCoverage": false}, {"functionName": "isNull", "ranges": [{"startOffset": 1542, "endOffset": 1589, "count": 0}], "isBlockCoverage": false}, {"functionName": "isNullOrUndefined", "ranges": [{"startOffset": 1616, "endOffset": 1673, "count": 1}], "isBlockCoverage": true}, {"functionName": "isNumber", "ranges": [{"startOffset": 1722, "endOffset": 1782, "count": 0}], "isBlockCoverage": false}, {"functionName": "isString", "ranges": [{"startOffset": 1813, "endOffset": 1873, "count": 234}], "isBlockCoverage": true}, {"functionName": "isSymbol", "ranges": [{"startOffset": 1904, "endOffset": 1964, "count": 0}], "isBlockCoverage": false}, {"functionName": "isUndefined", "ranges": [{"startOffset": 1995, "endOffset": 2049, "count": 0}], "isBlockCoverage": false}, {"functionName": "isRegExp", "ranges": [{"startOffset": 2086, "endOffset": 2162, "count": 0}], "isBlockCoverage": false}, {"functionName": "isObject", "ranges": [{"startOffset": 2193, "endOffset": 2269, "count": 0}], "isBlockCoverage": false}, {"functionName": "isDate", "ranges": [{"startOffset": 2300, "endOffset": 2370, "count": 0}], "isBlockCoverage": false}, {"functionName": "isError", "ranges": [{"startOffset": 2397, "endOffset": 2493, "count": 0}], "isBlockCoverage": false}, {"functionName": "isFunction", "ranges": [{"startOffset": 2522, "endOffset": 2586, "count": 470}], "isBlockCoverage": true}, {"functionName": "isPrimitive", "ranges": [{"startOffset": 2621, "endOffset": 2872, "count": 0}], "isBlockCoverage": false}, {"functionName": "objectToString", "ranges": [{"startOffset": 2964, "endOffset": 3038, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "112", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/inherits/inherits.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 250, "count": 1}, {"startOffset": 108, "endOffset": 117, "count": 0}, {"startOffset": 154, "endOffset": 249, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "113", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/tap-parser/node_modules/readable-stream/lib/_stream_writable.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13069, "count": 1}], "isBlockCoverage": true}, {"functionName": "WriteReq", "ranges": [{"startOffset": 1606, "endOffset": 1720, "count": 0}], "isBlockCoverage": false}, {"functionName": "WritableState", "ranges": [{"startOffset": 1722, "endOffset": 4736, "count": 1}, {"startOffset": 1829, "endOffset": 1834, "count": 0}, {"startOffset": 2094, "endOffset": 2098, "count": 0}, {"startOffset": 2154, "endOffset": 2159, "count": 0}, {"startOffset": 2351, "endOffset": 2417, "count": 0}], "isBlockCoverage": true}, {"functionName": "WritableState.onwrite", "ranges": [{"startOffset": 4107, "endOffset": 4150, "count": 234}], "isBlockCoverage": true}, {"functionName": "Writable", "ranges": [{"startOffset": 4738, "endOffset": 5147, "count": 1}, {"startOffset": 4963, "endOffset": 4991, "count": 0}, {"startOffset": 4997, "endOffset": 5026, "count": 0}], "isBlockCoverage": true}, {"functionName": "Writable.pipe", "ranges": [{"startOffset": 5243, "endOffset": 5320, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeAfterEnd", "ranges": [{"startOffset": 5324, "endOffset": 5559, "count": 0}], "isBlockCoverage": false}, {"functionName": "validChunk", "ranges": [{"startOffset": 5870, "endOffset": 6256, "count": 234}, {"startOffset": 5966, "endOffset": 5996, "count": 0}, {"startOffset": 5997, "endOffset": 6036, "count": 0}, {"startOffset": 6037, "endOffset": 6063, "count": 0}, {"startOffset": 6065, "endOffset": 6238, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6181, "endOffset": 6213, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.write", "ranges": [{"startOffset": 6285, "endOffset": 6825, "count": 234}, {"startOffset": 6405, "endOffset": 6450, "count": 0}, {"startOffset": 6504, "endOffset": 6564, "count": 0}, {"startOffset": 6642, "endOffset": 6673, "count": 0}], "isBlockCoverage": true}, {"functionName": "cb", "ranges": [{"startOffset": 6603, "endOffset": 6616, "count": 234}], "isBlockCoverage": true}, {"functionName": "Writable.cork", "ranges": [{"startOffset": 6854, "endOffset": 6922, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.uncork", "ranges": [{"startOffset": 6953, "endOffset": 7224, "count": 0}], "isBlockCoverage": false}, {"functionName": "decodeChunk", "ranges": [{"startOffset": 7227, "endOffset": 7432, "count": 234}, {"startOffset": 7368, "endOffset": 7414, "count": 0}], "isBlockCoverage": true}, {"functionName": "writeOr<PERSON>uffer", "ranges": [{"startOffset": 7621, "endOffset": 8196, "count": 234}, {"startOffset": 7811, "endOffset": 7814, "count": 0}, {"startOffset": 7992, "endOffset": 8015, "count": 0}, {"startOffset": 8058, "endOffset": 8111, "count": 0}], "isBlockCoverage": true}, {"functionName": "doWrite", "ranges": [{"startOffset": 8198, "endOffset": 8494, "count": 234}, {"startOffset": 8375, "endOffset": 8412, "count": 0}], "isBlockCoverage": true}, {"functionName": "onwriteError", "ranges": [{"startOffset": 8496, "endOffset": 8765, "count": 0}], "isBlockCoverage": false}, {"functionName": "onwriteStateUpdate", "ranges": [{"startOffset": 8767, "endOffset": 8910, "count": 234}], "isBlockCoverage": true}, {"functionName": "onwrite", "ranges": [{"startOffset": 8912, "endOffset": 9577, "count": 234}, {"startOffset": 9076, "endOffset": 9118, "count": 0}, {"startOffset": 9352, "endOffset": 9393, "count": 0}, {"startOffset": 9511, "endOffset": 9571, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9434, "endOffset": 9503, "count": 234}], "isBlockCoverage": true}, {"functionName": "afterWrite", "ranges": [{"startOffset": 9579, "endOffset": 9740, "count": 234}], "isBlockCoverage": true}, {"functionName": "onwriteDrain", "ranges": [{"startOffset": 9933, "endOffset": 10079, "count": 234}, {"startOffset": 10017, "endOffset": 10077, "count": 0}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 10145, "endOffset": 11587, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable._write", "ranges": [{"startOffset": 11617, "endOffset": 11687, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.end", "ranges": [{"startOffset": 11751, "endOffset": 12271, "count": 1}, {"startOffset": 11849, "endOffset": 11909, "count": 0}, {"startOffset": 11946, "endOffset": 11991, "count": 0}, {"startOffset": 12035, "endOffset": 12063, "count": 0}, {"startOffset": 12111, "endOffset": 12157, "count": 0}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON>sh", "ranges": [{"startOffset": 12275, "endOffset": 12427, "count": 469}, {"startOffset": 12335, "endOffset": 12366, "count": 1}, {"startOffset": 12367, "endOffset": 12395, "count": 1}, {"startOffset": 12396, "endOffset": 12423, "count": 1}], "isBlockCoverage": true}, {"functionName": "prefinish", "ranges": [{"startOffset": 12429, "endOffset": 12558, "count": 1}], "isBlockCoverage": true}, {"functionName": "finishMaybe", "ranges": [{"startOffset": 12560, "endOffset": 12838, "count": 235}, {"startOffset": 12650, "endOffset": 12821, "count": 1}, {"startOffset": 12780, "endOffset": 12817, "count": 0}], "isBlockCoverage": true}, {"functionName": "endWritable", "ranges": [{"startOffset": 12840, "endOffset": 13068, "count": 1}, {"startOffset": 12945, "endOffset": 13044, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "114", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/tap-parser/node_modules/readable-stream/lib/_stream_duplex.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2811, "count": 1}, {"startOffset": 1430, "endOffset": 1522, "count": 0}], "isBlockCoverage": true}, {"functionName": "objectKeys", "ranges": [{"startOffset": 1433, "endOffset": 1522, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1822, "endOffset": 1934, "count": 7}, {"startOffset": 1878, "endOffset": 1932, "count": 6}], "isBlockCoverage": true}, {"functionName": "Duplex", "ranges": [{"startOffset": 1938, "endOffset": 2382, "count": 0}], "isBlockCoverage": false}, {"functionName": "onend", "ranges": [{"startOffset": 2413, "endOffset": 2712, "count": 0}], "isBlockCoverage": false}, {"functionName": "for<PERSON>ach", "ranges": [{"startOffset": 2714, "endOffset": 2810, "count": 1}, {"startOffset": 2786, "endOffset": 2808, "count": 7}], "isBlockCoverage": true}]}, {"scriptId": "115", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/tap-parser/node_modules/readable-stream/lib/_stream_transform.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7350, "count": 1}], "isBlockCoverage": true}, {"functionName": "TransformState", "ranges": [{"startOffset": 3703, "endOffset": 3951, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterTransform", "ranges": [{"startOffset": 3953, "endOffset": 4457, "count": 0}], "isBlockCoverage": false}, {"functionName": "Transform", "ranges": [{"startOffset": 4460, "endOffset": 5238, "count": 0}], "isBlockCoverage": false}, {"functionName": "Transform.push", "ranges": [{"startOffset": 5267, "endOffset": 5402, "count": 0}], "isBlockCoverage": false}, {"functionName": "Transform._transform", "ranges": [{"startOffset": 5885, "endOffset": 5956, "count": 0}], "isBlockCoverage": false}, {"functionName": "Transform._write", "ranges": [{"startOffset": 5988, "endOffset": 6322, "count": 0}], "isBlockCoverage": false}, {"functionName": "Transform._read", "ranges": [{"startOffset": 6494, "endOffset": 6884, "count": 0}], "isBlockCoverage": false}, {"functionName": "done", "ranges": [{"startOffset": 6888, "endOffset": 7349, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "116", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/tap-parser/node_modules/readable-stream/lib/_stream_passthrough.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1727, "count": 1}], "isBlockCoverage": true}, {"functionName": "PassThrough", "ranges": [{"startOffset": 1494, "endOffset": 1636, "count": 0}], "isBlockCoverage": false}, {"functionName": "PassThrough._transform", "ranges": [{"startOffset": 1673, "endOffset": 1725, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "117", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/faucet/node_modules/sprintf-js/src/sprintf.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9250, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 39, "endOffset": 9223, "count": 1}, {"startOffset": 8861, "endOffset": 9185, "count": 0}], "isBlockCoverage": true}, {"functionName": "sprintf", "ranges": [{"startOffset": 625, "endOffset": 788, "count": 202}], "isBlockCoverage": true}, {"functionName": "vsprintf", "ranges": [{"startOffset": 794, "endOffset": 891, "count": 0}], "isBlockCoverage": false}, {"functionName": "sprintf_format", "ranges": [{"startOffset": 897, "endOffset": 5839, "count": 202}, {"startOffset": 1118, "endOffset": 5811, "count": 1414}, {"startOffset": 1171, "endOffset": 1226, "count": 808}, {"startOffset": 1226, "endOffset": 5801, "count": 606}, {"startOffset": 1378, "endOffset": 1801, "count": 0}, {"startOffset": 1840, "endOffset": 1937, "count": 0}, {"startOffset": 2163, "endOffset": 2214, "count": 0}, {"startOffset": 2265, "endOffset": 2307, "count": 202}, {"startOffset": 2293, "endOffset": 2306, "count": 0}, {"startOffset": 2309, "endOffset": 2425, "count": 0}, {"startOffset": 2472, "endOffset": 2534, "count": 202}, {"startOffset": 2591, "endOffset": 2690, "count": 0}, {"startOffset": 2711, "endOffset": 2819, "count": 0}, {"startOffset": 2840, "endOffset": 2849, "count": 202}, {"startOffset": 2870, "endOffset": 2957, "count": 202}, {"startOffset": 2978, "endOffset": 3108, "count": 0}, {"startOffset": 3129, "endOffset": 3291, "count": 0}, {"startOffset": 3312, "endOffset": 3452, "count": 0}, {"startOffset": 3473, "endOffset": 3621, "count": 0}, {"startOffset": 3642, "endOffset": 3749, "count": 0}, {"startOffset": 3770, "endOffset": 3935, "count": 404}, {"startOffset": 3866, "endOffset": 3898, "count": 0}, {"startOffset": 3956, "endOffset": 4123, "count": 0}, {"startOffset": 4144, "endOffset": 4360, "count": 0}, {"startOffset": 4381, "endOffset": 4474, "count": 0}, {"startOffset": 4495, "endOffset": 4662, "count": 0}, {"startOffset": 4683, "endOffset": 4791, "count": 0}, {"startOffset": 4812, "endOffset": 4934, "count": 0}, {"startOffset": 4996, "endOffset": 5049, "count": 0}, {"startOffset": 5121, "endOffset": 5149, "count": 202}, {"startOffset": 5151, "endOffset": 5295, "count": 0}, {"startOffset": 5427, "endOffset": 5478, "count": 0}, {"startOffset": 5584, "endOffset": 5642, "count": 0}, {"startOffset": 5687, "endOffset": 5705, "count": 0}, {"startOffset": 5731, "endOffset": 5749, "count": 0}], "isBlockCoverage": true}, {"functionName": "sprintf_parse", "ranges": [{"startOffset": 5890, "endOffset": 8600, "count": 202}, {"startOffset": 5952, "endOffset": 6001, "count": 201}, {"startOffset": 6001, "endOffset": 6086, "count": 1}, {"startOffset": 6086, "endOffset": 8547, "count": 7}, {"startOffset": 6143, "endOffset": 6200, "count": 4}, {"startOffset": 6200, "endOffset": 8486, "count": 3}, {"startOffset": 6263, "endOffset": 6315, "count": 0}, {"startOffset": 6415, "endOffset": 7601, "count": 0}, {"startOffset": 7715, "endOffset": 7847, "count": 0}, {"startOffset": 8379, "endOffset": 8486, "count": 0}, {"startOffset": 8547, "endOffset": 8599, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9018, "endOffset": 9168, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "118", "url": "node:internal/modules/esm/resolve", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 46464, "count": 1}], "isBlockCoverage": false}, {"functionName": "emitTrailingSlashPatternDeprecation", "ranges": [{"startOffset": 2799, "endOffset": 3432, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitInvalidSegmentDeprecation", "ranges": [{"startOffset": 3945, "endOffset": 4687, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitLegacyIndexDeprecation", "ranges": [{"startOffset": 5124, "endOffset": 6304, "count": 0}], "isBlockCoverage": false}, {"functionName": "legacyMainResolve", "ranges": [{"startOffset": 7395, "endOffset": 8155, "count": 0}], "isBlockCoverage": false}, {"functionName": "finalizeResolution", "ranges": [{"startOffset": 8939, "endOffset": 10695, "count": 0}], "isBlockCoverage": false}, {"functionName": "importNotDefined", "ranges": [{"startOffset": 11109, "endOffset": 11320, "count": 0}], "isBlockCoverage": false}, {"functionName": "exportsNotFound", "ranges": [{"startOffset": 11716, "endOffset": 11911, "count": 0}], "isBlockCoverage": false}, {"functionName": "throwInvalidSubpath", "ranges": [{"startOffset": 12549, "endOffset": 12932, "count": 0}], "isBlockCoverage": false}, {"functionName": "invalidPackageTarget", "ranges": [{"startOffset": 13388, "endOffset": 13766, "count": 0}], "isBlockCoverage": false}, {"functionName": "resolvePackageTargetString", "ranges": [{"startOffset": 15122, "endOffset": 17861, "count": 94}, {"startOffset": 15291, "endOffset": 15302, "count": 0}, {"startOffset": 15303, "endOffset": 15339, "count": 0}, {"startOffset": 15341, "endOffset": 15425, "count": 0}, {"startOffset": 15475, "endOffset": 16018, "count": 0}, {"startOffset": 16110, "endOffset": 16720, "count": 0}, {"startOffset": 16939, "endOffset": 17023, "count": 0}, {"startOffset": 17067, "endOffset": 17860, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15793, "endOffset": 15806, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 16319, "endOffset": 16332, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 16465, "endOffset": 16478, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 17202, "endOffset": 17215, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 17442, "endOffset": 17455, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 17795, "endOffset": 17808, "count": 0}], "isBlockCoverage": false}, {"functionName": "isArrayIndex", "ranges": [{"startOffset": 18053, "endOffset": 18197, "count": 48}, {"startOffset": 18149, "endOffset": 18194, "count": 0}], "isBlockCoverage": true}, {"functionName": "resolvePackageTarget", "ranges": [{"startOffset": 18992, "endOffset": 21242, "count": 178}, {"startOffset": 19185, "endOffset": 19339, "count": 94}, {"startOffset": 19339, "endOffset": 21125, "count": 84}, {"startOffset": 19371, "endOffset": 20217, "count": 42}, {"startOffset": 19402, "endOffset": 19428, "count": 0}, {"startOffset": 19748, "endOffset": 19896, "count": 0}, {"startOffset": 19936, "endOffset": 19963, "count": 0}, {"startOffset": 19998, "endOffset": 20055, "count": 0}, {"startOffset": 20089, "endOffset": 20151, "count": 0}, {"startOffset": 20153, "endOffset": 20217, "count": 0}, {"startOffset": 20217, "endOffset": 21125, "count": 42}, {"startOffset": 20370, "endOffset": 20598, "count": 48}, {"startOffset": 20428, "endOffset": 20592, "count": 0}, {"startOffset": 20641, "endOffset": 21050, "count": 46}, {"startOffset": 20698, "endOffset": 20720, "count": 6}, {"startOffset": 20722, "endOffset": 21044, "count": 42}, {"startOffset": 20993, "endOffset": 21006, "count": 0}, {"startOffset": 21050, "endOffset": 21125, "count": 0}, {"startOffset": 21125, "endOffset": 21241, "count": 0}], "isBlockCoverage": true}, {"functionName": "isConditionalExportsMainSugar", "ranges": [{"startOffset": 21526, "endOffset": 22469, "count": 94}, {"startOffset": 21658, "endOffset": 21674, "count": 0}, {"startOffset": 21730, "endOffset": 21747, "count": 0}, {"startOffset": 21887, "endOffset": 22438, "count": 236}, {"startOffset": 21997, "endOffset": 22054, "count": 94}, {"startOffset": 22054, "endOffset": 22434, "count": 142}, {"startOffset": 22110, "endOffset": 22434, "count": 0}], "isBlockCoverage": true}, {"functionName": "packageExportsResolve", "ranges": [{"startOffset": 22966, "endOffset": 25541, "count": 94}, {"startOffset": 23174, "endOffset": 23211, "count": 0}, {"startOffset": 23613, "endOffset": 23687, "count": 0}, {"startOffset": 23718, "endOffset": 25540, "count": 0}], "isBlockCoverage": true}, {"functionName": "patternKeyCompare", "ranges": [{"startOffset": 25915, "endOffset": 26470, "count": 0}], "isBlockCoverage": false}, {"functionName": "packageImportsResolve", "ranges": [{"startOffset": 26957, "endOffset": 29414, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPackageType", "ranges": [{"startOffset": 29528, "endOffset": 29641, "count": 0}], "isBlockCoverage": false}, {"functionName": "parsePackageName", "ranges": [{"startOffset": 29806, "endOffset": 30864, "count": 0}], "isBlockCoverage": false}, {"functionName": "packageResolve", "ranges": [{"startOffset": 31188, "endOffset": 33260, "count": 0}], "isBlockCoverage": false}, {"functionName": "isBareSpecifier", "ranges": [{"startOffset": 33371, "endOffset": 33481, "count": 0}], "isBlockCoverage": false}, {"functionName": "isRelativeSpecifier", "ranges": [{"startOffset": 33600, "endOffset": 33879, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldBeTreatedAsRelativeOrAbsolutePath", "ranges": [{"startOffset": 34028, "endOffset": 34219, "count": 0}], "isBlockCoverage": false}, {"functionName": "moduleResolve", "ranges": [{"startOffset": 34582, "endOffset": 35954, "count": 0}], "isBlockCoverage": false}, {"functionName": "resolveAsCommonJS", "ranges": [{"startOffset": 36154, "endOffset": 37980, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkIfDisallowedImport", "ranges": [{"startOffset": 38360, "endOffset": 39732, "count": 0}], "isBlockCoverage": false}, {"functionName": "throwIfInvalidParentURL", "ranges": [{"startOffset": 39872, "endOffset": 40145, "count": 0}], "isBlockCoverage": false}, {"functionName": "defaultResolve", "ranges": [{"startOffset": 40769, "endOffset": 44767, "count": 0}], "isBlockCoverage": false}, {"functionName": "decorateErrorWithCommonJSHints", "ranges": [{"startOffset": 45030, "endOffset": 45643, "count": 0}], "isBlockCoverage": false}, {"functionName": "defaultResolve", "ranges": [{"startOffset": 46069, "endOffset": 46460, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "119", "url": "node:internal/modules/esm/package_config", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2215, "count": 1}], "isBlockCoverage": false}, {"functionName": "getPackageScopeConfig", "ranges": [{"startOffset": 1072, "endOffset": 2165, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "120", "url": "node:internal/modules/esm/get_format", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8075, "count": 1}], "isBlockCoverage": false}, {"functionName": "node:", "ranges": [{"startOffset": 1056, "endOffset": 1087, "count": 0}], "isBlockCoverage": false}, {"functionName": "detectModuleFormat", "ranges": [{"startOffset": 1255, "endOffset": 1496, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDataProtocolModuleFormat", "ranges": [{"startOffset": 1557, "endOffset": 1773, "count": 0}], "isBlockCoverage": false}, {"functionName": "extname", "ranges": [{"startOffset": 2048, "endOffset": 2407, "count": 0}], "isBlockCoverage": false}, {"functionName": "underNodeModules", "ranges": [{"startOffset": 2637, "endOffset": 2858, "count": 0}], "isBlockCoverage": false}, {"functionName": "warnTypelessPackageJsonFile", "ranges": [{"startOffset": 2901, "endOffset": 3528, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFileProtocolModuleFormat", "ranges": [{"startOffset": 3669, "endOffset": 6736, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHttpProtocolModuleFormat", "ranges": [{"startOffset": 6872, "endOffset": 7217, "count": 0}], "isBlockCoverage": false}, {"functionName": "defaultGetFormatWithoutErrors", "ranges": [{"startOffset": 7362, "endOffset": 7597, "count": 0}], "isBlockCoverage": false}, {"functionName": "defaultGetFormat", "ranges": [{"startOffset": 7742, "endOffset": 7965, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "121", "url": "node:internal/modules/esm/formats", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1798, "count": 1}], "isBlockCoverage": false}, {"functionName": "mimeToFormat", "ranges": [{"startOffset": 660, "endOffset": 998, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFormatOfExtensionlessFile", "ranges": [{"startOffset": 1368, "endOffset": 1704, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "122", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/array.prototype.foreach/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 782, "count": 1}], "isBlockCoverage": true}, {"functionName": "for<PERSON>ach", "ranges": [{"startOffset": 529, "endOffset": 644, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "123", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/define-properties/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1268, "count": 1}], "isBlockCoverage": true}, {"functionName": "isFunction", "ranges": [{"startOffset": 287, "endOffset": 380, "count": 0}], "isBlockCoverage": false}, {"functionName": "defineProperty", "ranges": [{"startOffset": 470, "endOffset": 823, "count": 21}, {"startOffset": 535, "endOffset": 687, "count": 0}, {"startOffset": 768, "endOffset": 821, "count": 0}], "isBlockCoverage": true}, {"functionName": "defineProperties", "ranges": [{"startOffset": 849, "endOffset": 1167, "count": 7}, {"startOffset": 913, "endOffset": 927, "count": 0}, {"startOffset": 1088, "endOffset": 1165, "count": 21}], "isBlockCoverage": true}]}, {"scriptId": "124", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/object-keys/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 823, "count": 1}, {"startOffset": 185, "endOffset": 214, "count": 0}], "isBlockCoverage": true}, {"functionName": "keys", "ranges": [{"startOffset": 144, "endOffset": 184, "count": 7}], "isBlockCoverage": true}, {"functionName": "shimObjectKeys", "ranges": [{"startOffset": 266, "endOffset": 793, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "125", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/object-keys/isArguments.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 422, "count": 1}], "isBlockCoverage": true}, {"functionName": "isArguments", "ranges": [{"startOffset": 72, "endOffset": 420, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "126", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/define-data-property/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2838, "count": 1}, {"startOffset": 300, "endOffset": 379, "count": 0}], "isBlockCoverage": true}, {"functionName": "defineDataProperty", "ranges": [{"startOffset": 737, "endOffset": 2836, "count": 43}, {"startOffset": 863, "endOffset": 933, "count": 0}, {"startOffset": 968, "endOffset": 999, "count": 0}, {"startOffset": 1001, "endOffset": 1073, "count": 0}, {"startOffset": 1137, "endOffset": 1161, "count": 0}, {"startOffset": 1163, "endOffset": 1250, "count": 0}, {"startOffset": 1277, "endOffset": 1313, "count": 22}, {"startOffset": 1314, "endOffset": 1338, "count": 0}, {"startOffset": 1340, "endOffset": 1425, "count": 0}, {"startOffset": 1452, "endOffset": 1488, "count": 0}, {"startOffset": 1489, "endOffset": 1513, "count": 0}, {"startOffset": 1515, "endOffset": 1604, "count": 0}, {"startOffset": 1631, "endOffset": 1667, "count": 0}, {"startOffset": 1669, "endOffset": 1740, "count": 0}, {"startOffset": 1799, "endOffset": 1805, "count": 0}, {"startOffset": 1847, "endOffset": 1861, "count": 22}, {"startOffset": 1862, "endOffset": 1868, "count": 21}, {"startOffset": 1914, "endOffset": 1928, "count": 0}, {"startOffset": 1971, "endOffset": 1985, "count": 0}, {"startOffset": 2205, "endOffset": 2224, "count": 22}, {"startOffset": 2225, "endOffset": 2243, "count": 21}, {"startOffset": 2283, "endOffset": 2290, "count": 0}, {"startOffset": 2291, "endOffset": 2308, "count": 0}, {"startOffset": 2378, "endOffset": 2385, "count": 21}, {"startOffset": 2386, "endOffset": 2401, "count": 0}, {"startOffset": 2425, "endOffset": 2834, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "127", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/has-property-descriptors/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 817, "count": 1}], "isBlockCoverage": true}, {"functionName": "hasPropertyDescriptors", "ranges": [{"startOffset": 160, "endOffset": 382, "count": 6}, {"startOffset": 291, "endOffset": 362, "count": 0}, {"startOffset": 365, "endOffset": 381, "count": 0}], "isBlockCoverage": true}, {"functionName": "hasArrayLengthDefineBug", "ranges": [{"startOffset": 434, "endOffset": 773, "count": 1}, {"startOffset": 574, "endOffset": 593, "count": 0}, {"startOffset": 671, "endOffset": 771, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "128", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/get-intrinsic/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13326, "count": 1}, {"startOffset": 420, "endOffset": 491, "count": 0}, {"startOffset": 993, "endOffset": 1009, "count": 0}, {"startOffset": 1132, "endOffset": 1230, "count": 0}, {"startOffset": 1318, "endOffset": 1329, "count": 0}, {"startOffset": 1433, "endOffset": 1444, "count": 0}, {"startOffset": 1535, "endOffset": 1546, "count": 0}, {"startOffset": 1648, "endOffset": 1659, "count": 0}, {"startOffset": 1897, "endOffset": 1908, "count": 0}, {"startOffset": 1963, "endOffset": 1974, "count": 0}, {"startOffset": 2042, "endOffset": 2053, "count": 0}, {"startOffset": 2130, "endOffset": 2141, "count": 0}, {"startOffset": 2230, "endOffset": 2241, "count": 0}, {"startOffset": 2564, "endOffset": 2575, "count": 0}, {"startOffset": 2647, "endOffset": 2658, "count": 0}, {"startOffset": 2746, "endOffset": 2757, "count": 0}, {"startOffset": 2892, "endOffset": 2903, "count": 0}, {"startOffset": 2968, "endOffset": 2979, "count": 0}, {"startOffset": 3045, "endOffset": 3056, "count": 0}, {"startOffset": 3206, "endOffset": 3217, "count": 0}, {"startOffset": 3262, "endOffset": 3273, "count": 0}, {"startOffset": 3312, "endOffset": 3323, "count": 0}, {"startOffset": 3413, "endOffset": 3424, "count": 0}, {"startOffset": 3625, "endOffset": 3636, "count": 0}, {"startOffset": 3689, "endOffset": 3700, "count": 0}, {"startOffset": 3821, "endOffset": 3832, "count": 0}, {"startOffset": 3902, "endOffset": 3913, "count": 0}, {"startOffset": 4003, "endOffset": 4014, "count": 0}, {"startOffset": 4122, "endOffset": 4133, "count": 0}, {"startOffset": 4263, "endOffset": 4274, "count": 0}, {"startOffset": 4309, "endOffset": 4320, "count": 0}, {"startOffset": 4499, "endOffset": 4510, "count": 0}, {"startOffset": 4590, "endOffset": 4601, "count": 0}, {"startOffset": 4676, "endOffset": 4687, "count": 0}, {"startOffset": 4756, "endOffset": 4767, "count": 0}, {"startOffset": 4853, "endOffset": 4864, "count": 0}, {"startOffset": 4921, "endOffset": 4932, "count": 0}, {"startOffset": 4989, "endOffset": 5000, "count": 0}], "isBlockCoverage": true}, {"functionName": "getEvalledConstructor", "ranges": [{"startOffset": 192, "endOffset": 332, "count": 0}], "isBlockCoverage": false}, {"functionName": "throwTypeError", "ranges": [{"startOffset": 516, "endOffset": 556, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 589, "endOffset": 988, "count": 1}, {"startOffset": 929, "endOffset": 981, "count": 0}], "isBlockCoverage": true}, {"functionName": "getProto", "ranges": [{"startOffset": 1151, "endOffset": 1187, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 5304, "endOffset": 5974, "count": 0}], "isBlockCoverage": false}, {"functionName": "stringToPath", "ranges": [{"startOffset": 9443, "endOffset": 9994, "count": 80}, {"startOffset": 9567, "endOffset": 9582, "count": 62}, {"startOffset": 9584, "endOffset": 9664, "count": 0}, {"startOffset": 9687, "endOffset": 9703, "count": 62}, {"startOffset": 9705, "endOffset": 9785, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9834, "endOffset": 9974, "count": 142}, {"startOffset": 9911, "endOffset": 9952, "count": 0}], "isBlockCoverage": true}, {"functionName": "getBaseIntrinsic", "ranges": [{"startOffset": 10041, "endOffset": 10717, "count": 80}, {"startOffset": 10172, "endOffset": 10257, "count": 0}, {"startOffset": 10369, "endOffset": 10408, "count": 0}, {"startOffset": 10444, "endOffset": 10460, "count": 0}, {"startOffset": 10462, "endOffset": 10570, "count": 0}, {"startOffset": 10647, "endOffset": 10716, "count": 0}], "isBlockCoverage": true}, {"functionName": "GetIntrinsic", "ranges": [{"startOffset": 10737, "endOffset": 13324, "count": 80}, {"startOffset": 10833, "endOffset": 10906, "count": 0}, {"startOffset": 10933, "endOffset": 10969, "count": 29}, {"startOffset": 10971, "endOffset": 11044, "count": 0}, {"startOffset": 11088, "endOffset": 11204, "count": 0}, {"startOffset": 11291, "endOffset": 11295, "count": 0}, {"startOffset": 11525, "endOffset": 11608, "count": 0}, {"startOffset": 11667, "endOffset": 13307, "count": 62}, {"startOffset": 11891, "endOffset": 11908, "count": 0}, {"startOffset": 11913, "endOffset": 12001, "count": 0}, {"startOffset": 12042, "endOffset": 12077, "count": 0}, {"startOffset": 12213, "endOffset": 12260, "count": 33}, {"startOffset": 12260, "endOffset": 13304, "count": 29}, {"startOffset": 12312, "endOffset": 12481, "count": 0}, {"startOffset": 12523, "endOffset": 13142, "count": 24}, {"startOffset": 13033, "endOffset": 13066, "count": 0}, {"startOffset": 13068, "endOffset": 13098, "count": 0}, {"startOffset": 13142, "endOffset": 13212, "count": 5}], "isBlockCoverage": true}]}, {"scriptId": "129", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/has-symbols/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 420, "count": 1}], "isBlockCoverage": true}, {"functionName": "hasNativeSymbols", "ranges": [{"startOffset": 131, "endOffset": 418, "count": 2}, {"startOffset": 200, "endOffset": 217, "count": 0}, {"startOffset": 253, "endOffset": 270, "count": 0}, {"startOffset": 315, "endOffset": 332, "count": 0}, {"startOffset": 373, "endOffset": 390, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "130", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/has-symbols/shams.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1761, "count": 1}], "isBlockCoverage": true}, {"functionName": "hasSymbols", "ranges": [{"startOffset": 90, "endOffset": 1759, "count": 5}, {"startOffset": 203, "endOffset": 220, "count": 0}, {"startOffset": 279, "endOffset": 1198, "count": 0}, {"startOffset": 1200, "endOffset": 1320, "count": 0}, {"startOffset": 1322, "endOffset": 1429, "count": 0}, {"startOffset": 1431, "endOffset": 1758, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "131", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/has-proto/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 197, "count": 1}], "isBlockCoverage": true}, {"functionName": "hasProto", "ranges": [{"startOffset": 81, "endOffset": 195, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "132", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/function-bind/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 126, "count": 1}, {"startOffset": 107, "endOffset": 124, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "133", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/function-bind/implementation.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2043, "count": 1}], "isBlockCoverage": true}, {"functionName": "concatty", "ranges": [{"startOffset": 230, "endOffset": 450, "count": 0}], "isBlockCoverage": false}, {"functionName": "slicy", "ranges": [{"startOffset": 465, "endOffset": 644, "count": 0}], "isBlockCoverage": false}, {"functionName": "joiny", "ranges": [{"startOffset": 659, "endOffset": 865, "count": 0}], "isBlockCoverage": false}, {"functionName": "bind", "ranges": [{"startOffset": 885, "endOffset": 2041, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "134", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/hasown/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 234, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "135", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/gopd/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 263, "count": 1}, {"startOffset": 177, "endOffset": 235, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "136", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/call-bind/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1212, "count": 1}, {"startOffset": 370, "endOffset": 397, "count": 0}, {"startOffset": 584, "endOffset": 662, "count": 0}, {"startOffset": 1167, "endOffset": 1211, "count": 0}], "isBlockCoverage": true}, {"functionName": "callBind", "ranges": [{"startOffset": 683, "endOffset": 984, "count": 22}, {"startOffset": 766, "endOffset": 820, "count": 0}], "isBlockCoverage": true}, {"functionName": "applyBind", "ranges": [{"startOffset": 1003, "endOffset": 1075, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "137", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/set-function-length/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1170, "count": 1}], "isBlockCoverage": true}, {"functionName": "setFunctionLength", "ranges": [{"startOffset": 302, "endOffset": 1168, "count": 22}, {"startOffset": 374, "endOffset": 428, "count": 0}, {"startOffset": 528, "endOffset": 602, "count": 0}, {"startOffset": 836, "endOffset": 882, "count": 0}, {"startOffset": 994, "endOffset": 1021, "count": 0}, {"startOffset": 1022, "endOffset": 1031, "count": 0}, {"startOffset": 1107, "endOffset": 1151, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "138", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/call-bind/callBound.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 413, "count": 1}], "isBlockCoverage": true}, {"functionName": "callBoundIntrinsic", "ranges": [{"startOffset": 177, "endOffset": 411, "count": 17}, {"startOffset": 390, "endOffset": 410, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "139", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/RequireObjectCoercible.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 70, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "140", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/5/CheckObjectCoercible.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 345, "count": 1}], "isBlockCoverage": true}, {"functionName": "CheckObjectCoercible", "ranges": [{"startOffset": 177, "endOffset": 343, "count": 405}, {"startOffset": 248, "endOffset": 326, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "141", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/array.prototype.foreach/implementation.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1427, "count": 1}], "isBlockCoverage": true}, {"functionName": "for<PERSON>ach", "ranges": [{"startOffset": 848, "endOffset": 1425, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "142", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/Call.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 616, "count": 1}, {"startOffset": 244, "endOffset": 284, "count": 0}], "isBlockCoverage": true}, {"functionName": "Call", "ranges": [{"startOffset": 357, "endOffset": 614, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "143", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/IsArray.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 118, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "144", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/helpers/IsArray.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 349, "count": 1}, {"startOffset": 171, "endOffset": 233, "count": 0}, {"startOffset": 268, "endOffset": 347, "count": 0}], "isBlockCoverage": true}, {"functionName": "IsArray", "ranges": [{"startOffset": 271, "endOffset": 347, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "145", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/Get.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 597, "count": 1}], "isBlockCoverage": true}, {"functionName": "Get", "ranges": [{"startOffset": 302, "endOffset": 595, "count": 202}, {"startOffset": 362, "endOffset": 433, "count": 0}, {"startOffset": 470, "endOffset": 567, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "146", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/object-inspect/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18882, "count": 1}, {"startOffset": 180, "endOffset": 186, "count": 0}, {"startOffset": 301, "endOffset": 307, "count": 0}, {"startOffset": 539, "endOffset": 545, "count": 0}, {"startOffset": 660, "endOffset": 666, "count": 0}, {"startOffset": 839, "endOffset": 845, "count": 0}, {"startOffset": 968, "endOffset": 974, "count": 0}, {"startOffset": 1101, "endOffset": 1107, "count": 0}, {"startOffset": 1717, "endOffset": 1723, "count": 0}, {"startOffset": 1880, "endOffset": 1886, "count": 0}, {"startOffset": 2132, "endOffset": 2142, "count": 0}, {"startOffset": 2184, "endOffset": 2190, "count": 0}, {"startOffset": 2292, "endOffset": 2316, "count": 0}, {"startOffset": 2342, "endOffset": 2531, "count": 0}, {"startOffset": 3403, "endOffset": 3409, "count": 0}, {"startOffset": 12973, "endOffset": 13014, "count": 0}], "isBlockCoverage": true}, {"functionName": "gPO", "ranges": [{"startOffset": 2426, "endOffset": 2514, "count": 0}], "isBlockCoverage": false}, {"functionName": "addNumericSeparator", "ranges": [{"startOffset": 2534, "endOffset": 3256, "count": 0}], "isBlockCoverage": false}, {"functionName": "inspect_", "ranges": [{"startOffset": 3429, "endOffset": 11013, "count": 0}], "isBlockCoverage": false}, {"functionName": "wrapQuotes", "ranges": [{"startOffset": 11016, "endOffset": 11180, "count": 0}], "isBlockCoverage": false}, {"functionName": "quote", "ranges": [{"startOffset": 11182, "endOffset": 11256, "count": 0}], "isBlockCoverage": false}, {"functionName": "isArray", "ranges": [{"startOffset": 11258, "endOffset": 11393, "count": 0}], "isBlockCoverage": false}, {"functionName": "isDate", "ranges": [{"startOffset": 11394, "endOffset": 11527, "count": 0}], "isBlockCoverage": false}, {"functionName": "isRegExp", "ranges": [{"startOffset": 11528, "endOffset": 11665, "count": 0}], "isBlockCoverage": false}, {"functionName": "isError", "ranges": [{"startOffset": 11666, "endOffset": 11801, "count": 0}], "isBlockCoverage": false}, {"functionName": "isString", "ranges": [{"startOffset": 11802, "endOffset": 11939, "count": 0}], "isBlockCoverage": false}, {"functionName": "isNumber", "ranges": [{"startOffset": 11940, "endOffset": 12077, "count": 0}], "isBlockCoverage": false}, {"functionName": "isBoolean", "ranges": [{"startOffset": 12078, "endOffset": 12217, "count": 0}], "isBlockCoverage": false}, {"functionName": "isSymbol", "ranges": [{"startOffset": 12327, "endOffset": 12708, "count": 1}, {"startOffset": 12379, "endOffset": 12458, "count": 0}, {"startOffset": 12520, "endOffset": 12560, "count": 0}, {"startOffset": 12561, "endOffset": 12576, "count": 0}, {"startOffset": 12578, "endOffset": 12707, "count": 0}], "isBlockCoverage": true}, {"functionName": "isBigInt", "ranges": [{"startOffset": 12710, "endOffset": 12926, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasOwn", "ranges": [{"startOffset": 12976, "endOffset": 13014, "count": 0}], "isBlockCoverage": false}, {"functionName": "has", "ranges": [{"startOffset": 13016, "endOffset": 13076, "count": 0}], "isBlockCoverage": false}, {"functionName": "toStr", "ranges": [{"startOffset": 13078, "endOffset": 13138, "count": 0}], "isBlockCoverage": false}, {"functionName": "nameOf", "ranges": [{"startOffset": 13140, "endOffset": 13317, "count": 0}], "isBlockCoverage": false}, {"functionName": "indexOf", "ranges": [{"startOffset": 13319, "endOffset": 13501, "count": 0}], "isBlockCoverage": false}, {"functionName": "isMap", "ranges": [{"startOffset": 13503, "endOffset": 13842, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWeakMap", "ranges": [{"startOffset": 13844, "endOffset": 14224, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWeakRef", "ranges": [{"startOffset": 14226, "endOffset": 14433, "count": 0}], "isBlockCoverage": false}, {"functionName": "isSet", "ranges": [{"startOffset": 14435, "endOffset": 14774, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWeakSet", "ranges": [{"startOffset": 14776, "endOffset": 15156, "count": 0}], "isBlockCoverage": false}, {"functionName": "isElement", "ranges": [{"startOffset": 15158, "endOffset": 15422, "count": 0}], "isBlockCoverage": false}, {"functionName": "inspectString", "ranges": [{"startOffset": 15424, "endOffset": 15934, "count": 0}], "isBlockCoverage": false}, {"functionName": "lowbyte", "ranges": [{"startOffset": 15936, "endOffset": 16204, "count": 0}], "isBlockCoverage": false}, {"functionName": "markBoxed", "ranges": [{"startOffset": 16206, "endOffset": 16267, "count": 0}], "isBlockCoverage": false}, {"functionName": "weakCollectionOf", "ranges": [{"startOffset": 16269, "endOffset": 16332, "count": 0}], "isBlockCoverage": false}, {"functionName": "collectionOf", "ranges": [{"startOffset": 16334, "endOffset": 16541, "count": 0}], "isBlockCoverage": false}, {"functionName": "singleLineValues", "ranges": [{"startOffset": 16543, "endOffset": 16718, "count": 0}], "isBlockCoverage": false}, {"functionName": "getIndent", "ranges": [{"startOffset": 16720, "endOffset": 17106, "count": 0}], "isBlockCoverage": false}, {"functionName": "indented<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 17108, "endOffset": 17319, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 17321, "endOffset": 18881, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "147", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/object-inspect/util.inspect.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 42, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "148", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/IsPropertyKey.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 202, "count": 1}], "isBlockCoverage": true}, {"functionName": "Is<PERSON><PERSON><PERSON><PERSON>ey", "ranges": [{"startOffset": 94, "endOffset": 200, "count": 611}, {"startOffset": 166, "endOffset": 197, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "149", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/Type.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 297, "count": 1}], "isBlockCoverage": true}, {"functionName": "Type", "ranges": [{"startOffset": 151, "endOffset": 295, "count": 1355}, {"startOffset": 198, "endOffset": 221, "count": 0}, {"startOffset": 250, "endOffset": 273, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "150", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/5/Type.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 438, "count": 1}], "isBlockCoverage": true}, {"functionName": "Type", "ranges": [{"startOffset": 82, "endOffset": 436, "count": 1355}, {"startOffset": 118, "endOffset": 139, "count": 0}, {"startOffset": 171, "endOffset": 197, "count": 0}, {"startOffset": 253, "endOffset": 276, "count": 830}, {"startOffset": 276, "endOffset": 305, "count": 525}, {"startOffset": 305, "endOffset": 328, "count": 121}, {"startOffset": 328, "endOffset": 382, "count": 404}, {"startOffset": 382, "endOffset": 434, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "151", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/HasProperty.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 512, "count": 1}], "isBlockCoverage": true}, {"functionName": "HasProperty", "ranges": [{"startOffset": 264, "endOffset": 510, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "152", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/IsCallable.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 108, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "153", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/is-callable/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3224, "count": 1}, {"startOffset": 575, "endOffset": 604, "count": 0}, {"startOffset": 609, "endOffset": 640, "count": 0}, {"startOffset": 1659, "endOffset": 2355, "count": 0}, {"startOffset": 2754, "endOffset": 3222, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 342, "endOffset": 388, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 482, "endOffset": 507, "count": 0}], "isBlockCoverage": false}, {"functionName": "isES6ClassFunction", "ranges": [{"startOffset": 699, "endOffset": 870, "count": 0}], "isBlockCoverage": false}, {"functionName": "tryFunctionToStr", "ranges": [{"startOffset": 897, "endOffset": 1057, "count": 0}], "isBlockCoverage": false}, {"functionName": "isDocumentDotAll", "ranges": [{"startOffset": 1578, "endOffset": 1623, "count": 0}], "isBlockCoverage": false}, {"functionName": "isDocumentDotAll", "ranges": [{"startOffset": 1826, "endOffset": 2349, "count": 0}], "isBlockCoverage": false}, {"functionName": "isCallable", "ranges": [{"startOffset": 2390, "endOffset": 2752, "count": 0}], "isBlockCoverage": false}, {"functionName": "isCallable", "ranges": [{"startOffset": 2756, "endOffset": 3222, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "154", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/LengthOfArrayLike.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 495, "count": 1}], "isBlockCoverage": true}, {"functionName": "LengthOfArrayLike", "ranges": [{"startOffset": 289, "endOffset": 465, "count": 202}, {"startOffset": 352, "endOffset": 425, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "155", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/ToLength.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 362, "count": 1}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 155, "endOffset": 360, "count": 202}, {"startOffset": 242, "endOffset": 255, "count": 81}, {"startOffset": 255, "endOffset": 317, "count": 121}, {"startOffset": 317, "endOffset": 345, "count": 0}, {"startOffset": 345, "endOffset": 359, "count": 121}], "isBlockCoverage": true}]}, {"scriptId": "156", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/helpers/maxSafeInteger.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 101, "count": 1}, {"startOffset": 56, "endOffset": 75, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "157", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/ToIntegerOrInfinity.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 462, "count": 1}], "isBlockCoverage": true}, {"functionName": "ToIntegerOrInfinity", "ranges": [{"startOffset": 269, "endOffset": 460, "count": 202}, {"startOffset": 375, "endOffset": 388, "count": 81}, {"startOffset": 388, "endOffset": 414, "count": 121}, {"startOffset": 414, "endOffset": 432, "count": 0}, {"startOffset": 432, "endOffset": 459, "count": 121}], "isBlockCoverage": true}]}, {"scriptId": "158", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/ToNumber.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 804, "count": 1}], "isBlockCoverage": true}, {"functionName": "ToNumber", "ranges": [{"startOffset": 371, "endOffset": 802, "count": 202}, {"startOffset": 447, "endOffset": 479, "count": 0}, {"startOffset": 513, "endOffset": 586, "count": 0}, {"startOffset": 619, "endOffset": 707, "count": 0}, {"startOffset": 740, "endOffset": 776, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "159", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/helpers/isPrimitive.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 151, "count": 1}], "isBlockCoverage": true}, {"functionName": "isPrimitive", "ranges": [{"startOffset": 32, "endOffset": 149, "count": 202}], "isBlockCoverage": true}]}, {"scriptId": "160", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/ToPrimitive.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 282, "count": 1}], "isBlockCoverage": true}, {"functionName": "ToPrimitive", "ranges": [{"startOffset": 146, "endOffset": 280, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "161", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-to-primitive/es2015.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2139, "count": 1}], "isBlockCoverage": true}, {"functionName": "OrdinaryToPrimitive", "ranges": [{"startOffset": 299, "endOffset": 930, "count": 0}], "isBlockCoverage": false}, {"functionName": "GetMethod", "ranges": [{"startOffset": 949, "endOffset": 1218, "count": 0}], "isBlockCoverage": false}, {"functionName": "ToPrimitive", "ranges": [{"startOffset": 1305, "endOffset": 2137, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "162", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-to-primitive/helpers/isPrimitive.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 151, "count": 1}], "isBlockCoverage": true}, {"functionName": "isPrimitive", "ranges": [{"startOffset": 32, "endOffset": 149, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "163", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/is-date-object/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 522, "count": 1}], "isBlockCoverage": true}, {"functionName": "tryDateGetDayCall", "ranges": [{"startOffset": 71, "endOffset": 186, "count": 0}], "isBlockCoverage": false}, {"functionName": "isDateObject", "ranges": [{"startOffset": 336, "endOffset": 520, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "164", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/has-tostringtag/shams.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 162, "count": 1}], "isBlockCoverage": true}, {"functionName": "hasToStringTagShams", "ranges": [{"startOffset": 80, "endOffset": 160, "count": 3}], "isBlockCoverage": true}]}, {"scriptId": "165", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/is-symbol/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 767, "count": 1}, {"startOffset": 634, "endOffset": 766, "count": 0}], "isBlockCoverage": true}, {"functionName": "isRealSymbolObject", "ranges": [{"startOffset": 221, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "isSymbol", "ranges": [{"startOffset": 399, "endOffset": 631, "count": 0}], "isBlockCoverage": false}, {"functionName": "isSymbol", "ranges": [{"startOffset": 661, "endOffset": 763, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "166", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/StringToNumber.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1367, "count": 1}], "isBlockCoverage": true}, {"functionName": "StringToNumber", "ranges": [{"startOffset": 836, "endOffset": 1365, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "167", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/safe-regex-test/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 434, "count": 1}], "isBlockCoverage": true}, {"functionName": "regexTester", "ranges": [{"startOffset": 256, "endOffset": 432, "count": 7}, {"startOffset": 308, "endOffset": 364, "count": 0}], "isBlockCoverage": true}, {"functionName": "test", "ranges": [{"startOffset": 373, "endOffset": 429, "count": 3}], "isBlockCoverage": true}]}, {"scriptId": "168", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/is-regex/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1405, "count": 1}, {"startOffset": 1163, "endOffset": 1403, "count": 0}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 346, "endOffset": 385, "count": 7}], "isBlockCoverage": true}, {"functionName": "isRegex", "ranges": [{"startOffset": 799, "endOffset": 1161, "count": 7}, {"startOffset": 868, "endOffset": 890, "count": 0}, {"startOffset": 1043, "endOffset": 1065, "count": 0}], "isBlockCoverage": true}, {"functionName": "isRegex", "ranges": [{"startOffset": 1165, "endOffset": 1403, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "169", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/string.prototype.trim/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 573, "count": 1}], "isBlockCoverage": true}, {"functionName": "trim", "ranges": [{"startOffset": 354, "endOffset": 441, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "170", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/string.prototype.trim/implementation.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1183, "count": 1}, {"startOffset": 359, "endOffset": 509, "count": 0}, {"startOffset": 688, "endOffset": 838, "count": 0}], "isBlockCoverage": true}, {"functionName": "trim", "ranges": [{"startOffset": 1041, "endOffset": 1181, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "171", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/ToString.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 391, "count": 1}], "isBlockCoverage": true}, {"functionName": "ToString", "ranges": [{"startOffset": 222, "endOffset": 389, "count": 202}, {"startOffset": 287, "endOffset": 360, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "172", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/string.prototype.trim/polyfill.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 547, "count": 1}], "isBlockCoverage": true}, {"functionName": "getPolyfill", "ranges": [{"startOffset": 155, "endOffset": 545, "count": 1}, {"startOffset": 519, "endOffset": 544, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "173", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/string.prototype.trim/shim.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 330, "count": 1}], "isBlockCoverage": true}, {"functionName": "shimStringTrim", "ranges": [{"startOffset": 117, "endOffset": 328, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "174", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/truncate.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 509, "count": 1}], "isBlockCoverage": true}, {"functionName": "truncate", "ranges": [{"startOffset": 216, "endOffset": 507, "count": 121}, {"startOffset": 266, "endOffset": 290, "count": 0}, {"startOffset": 292, "endOffset": 361, "count": 0}, {"startOffset": 382, "endOffset": 394, "count": 0}, {"startOffset": 428, "endOffset": 431, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "175", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/floor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 295, "count": 1}], "isBlockCoverage": true}, {"functionName": "floor", "ranges": [{"startOffset": 180, "endOffset": 293, "count": 121}, {"startOffset": 256, "endOffset": 272, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "176", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/helpers/isNaN.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 88, "count": 1}, {"startOffset": 45, "endOffset": 86, "count": 0}], "isBlockCoverage": true}, {"functionName": "isNaN", "ranges": [{"startOffset": 48, "endOffset": 86, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "177", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/helpers/isFinite.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 192, "count": 1}], "isBlockCoverage": true}, {"functionName": "module.exports", "ranges": [{"startOffset": 66, "endOffset": 190, "count": 121}, {"startOffset": 111, "endOffset": 135, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "178", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/ToObject.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 329, "count": 1}], "isBlockCoverage": true}, {"functionName": "ToObject", "ranges": [{"startOffset": 243, "endOffset": 327, "count": 202}], "isBlockCoverage": true}]}, {"scriptId": "179", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/is-string/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 560, "count": 1}], "isBlockCoverage": true}, {"functionName": "tryStringObject", "ranges": [{"startOffset": 78, "endOffset": 193, "count": 0}], "isBlockCoverage": false}, {"functionName": "isString", "ranges": [{"startOffset": 343, "endOffset": 558, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "180", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/array.prototype.foreach/polyfill.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 293, "count": 1}], "isBlockCoverage": true}, {"functionName": "getPolyfill", "ranges": [{"startOffset": 158, "endOffset": 291, "count": 1}, {"startOffset": 272, "endOffset": 288, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "181", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-array-method-boxes-properly/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 743, "count": 1}], "isBlockCoverage": true}, {"functionName": "properlyBoxed", "ranges": [{"startOffset": 17, "endOffset": 741, "count": 2}, {"startOffset": 605, "endOffset": 646, "count": 0}, {"startOffset": 724, "endOffset": 740, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 316, "endOffset": 418, "count": 2}, {"startOffset": 369, "endOffset": 413, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 454, "endOffset": 585, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "182", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/array.prototype.foreach/shim.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 327, "count": 1}], "isBlockCoverage": true}, {"functionName": "shim<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 117, "endOffset": 325, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "183", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/array.prototype.join/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 580, "count": 1}], "isBlockCoverage": true}, {"functionName": "join", "ranges": [{"startOffset": 353, "endOffset": 453, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "184", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/array.prototype.join/implementation.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1241, "count": 1}], "isBlockCoverage": true}, {"functionName": "join", "ranges": [{"startOffset": 598, "endOffset": 1200, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "185", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/array.prototype.join/polyfill.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1249, "count": 1}], "isBlockCoverage": true}, {"functionName": "getPolyfill", "ranges": [{"startOffset": 206, "endOffset": 1247, "count": 1}, {"startOffset": 259, "endOffset": 288, "count": 0}, {"startOffset": 551, "endOffset": 592, "count": 0}, {"startOffset": 701, "endOffset": 1214, "count": 0}], "isBlockCoverage": true}, {"functionName": "join", "ranges": [{"startOffset": 873, "endOffset": 1035, "count": 0}], "isBlockCoverage": false}, {"functionName": "join", "ranges": [{"startOffset": 1080, "endOffset": 1206, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "186", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/array.prototype.join/shim.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 331, "count": 1}], "isBlockCoverage": true}, {"functionName": "shimArrayPrototypeJoin", "ranges": [{"startOffset": 117, "endOffset": 329, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "187", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/array.prototype.map/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 705, "count": 1}], "isBlockCoverage": true}, {"functionName": "map", "ranges": [{"startOffset": 452, "endOffset": 572, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "188", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/array.prototype.map/implementation.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1606, "count": 1}], "isBlockCoverage": true}, {"functionName": "map", "ranges": [{"startOffset": 901, "endOffset": 1604, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "189", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/ArraySpeciesCreate.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1354, "count": 1}], "isBlockCoverage": true}, {"functionName": "ArraySpeciesCreate", "ranges": [{"startOffset": 485, "endOffset": 1351, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "190", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/ArrayCreate.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1602, "count": 1}, {"startOffset": 460, "endOffset": 603, "count": 0}], "isBlockCoverage": true}, {"functionName": "$setProto", "ranges": [{"startOffset": 479, "endOffset": 592, "count": 0}], "isBlockCoverage": false}, {"functionName": "ArrayCreate", "ranges": [{"startOffset": 684, "endOffset": 1600, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "191", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/helpers/isInteger.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 440, "count": 1}], "isBlockCoverage": true}, {"functionName": "isInteger", "ranges": [{"startOffset": 234, "endOffset": 437, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "192", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/IsConstructor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1144, "count": 1}, {"startOffset": 262, "endOffset": 347, "count": 0}, {"startOffset": 905, "endOffset": 1143, "count": 0}], "isBlockCoverage": true}, {"functionName": "[[Get]]", "ranges": [{"startOffset": 241, "endOffset": 255, "count": 0}], "isBlockCoverage": false}, {"functionName": "[[Get]]", "ranges": [{"startOffset": 571, "endOffset": 618, "count": 0}], "isBlockCoverage": false}, {"functionName": "IsConstructor", "ranges": [{"startOffset": 669, "endOffset": 902, "count": 0}], "isBlockCoverage": false}, {"functionName": "IsConstructor", "ranges": [{"startOffset": 931, "endOffset": 1140, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "193", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/GetIntrinsic.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 89, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "194", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/DefinePropertyOrThrow.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1461, "count": 1}], "isBlockCoverage": true}, {"functionName": "DefinePropertyOrThrow", "ranges": [{"startOffset": 695, "endOffset": 1459, "count": 3}, {"startOffset": 767, "endOffset": 838, "count": 0}, {"startOffset": 864, "endOffset": 942, "count": 0}, {"startOffset": 1093, "endOffset": 1121, "count": 0}, {"startOffset": 1260, "endOffset": 1349, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "195", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/helpers/isPropertyDescriptor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 730, "count": 1}], "isBlockCoverage": true}, {"functionName": "IsPropertyDescriptor", "ranges": [{"startOffset": 157, "endOffset": 728, "count": 6}, {"startOffset": 232, "endOffset": 252, "count": 0}, {"startOffset": 434, "endOffset": 547, "count": 8}, {"startOffset": 522, "endOffset": 544, "count": 0}, {"startOffset": 580, "endOffset": 612, "count": 2}, {"startOffset": 614, "endOffset": 712, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "196", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/helpers/DefineOwnProperty.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1591, "count": 1}, {"startOffset": 388, "endOffset": 420, "count": 0}], "isBlockCoverage": true}, {"functionName": "DefineOwnProperty", "ranges": [{"startOffset": 601, "endOffset": 1589, "count": 3}, {"startOffset": 718, "endOffset": 1260, "count": 0}, {"startOffset": 1295, "endOffset": 1312, "count": 0}, {"startOffset": 1315, "endOffset": 1337, "count": 0}, {"startOffset": 1340, "endOffset": 1353, "count": 0}, {"startOffset": 1356, "endOffset": 1389, "count": 0}, {"startOffset": 1393, "endOffset": 1518, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "197", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/FromPropertyDescriptor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 445, "count": 1}], "isBlockCoverage": true}, {"functionName": "FromPropertyDescriptor", "ranges": [{"startOffset": 265, "endOffset": 443, "count": 3}], "isBlockCoverage": true}]}, {"scriptId": "198", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/helpers/assertRecord.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2930, "count": 1}], "isBlockCoverage": true}, {"functionName": "isPropertyDescriptor", "ranges": [{"startOffset": 410, "endOffset": 1031, "count": 11}, {"startOffset": 627, "endOffset": 649, "count": 0}, {"startOffset": 674, "endOffset": 770, "count": 14}, {"startOffset": 742, "endOffset": 766, "count": 0}, {"startOffset": 857, "endOffset": 883, "count": 5}, {"startOffset": 898, "endOffset": 911, "count": 0}, {"startOffset": 913, "endOffset": 1013, "count": 0}], "isBlockCoverage": true}, {"functionName": "isIteratorRecord", "ranges": [{"startOffset": 1148, "endOffset": 1289, "count": 0}], "isBlockCoverage": false}, {"functionName": "isPromiseCapabilityRecord", "ranges": [{"startOffset": 1320, "endOffset": 1666, "count": 0}], "isBlockCoverage": false}, {"functionName": "isAsyncGeneratorRequestRecord", "ranges": [{"startOffset": 1701, "endOffset": 1955, "count": 0}], "isBlockCoverage": false}, {"functionName": "isRegExpRecord", "ranges": [{"startOffset": 1975, "endOffset": 2570, "count": 0}], "isBlockCoverage": false}, {"functionName": "assertRecord", "ranges": [{"startOffset": 2592, "endOffset": 2928, "count": 11}, {"startOffset": 2734, "endOffset": 2802, "count": 0}, {"startOffset": 2855, "endOffset": 2926, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "199", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/helpers/isMatchRecord.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 544, "count": 1}], "isBlockCoverage": true}, {"functionName": "isMatchRecord", "ranges": [{"startOffset": 128, "endOffset": 542, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "200", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/helpers/fromPropertyDescriptor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 580, "count": 1}], "isBlockCoverage": true}, {"functionName": "fromPropertyDescriptor", "ranges": [{"startOffset": 32, "endOffset": 578, "count": 3}, {"startOffset": 106, "endOffset": 125, "count": 0}, {"startOffset": 167, "endOffset": 204, "count": 0}, {"startOffset": 234, "endOffset": 279, "count": 1}, {"startOffset": 304, "endOffset": 337, "count": 2}, {"startOffset": 362, "endOffset": 395, "count": 0}, {"startOffset": 427, "endOffset": 476, "count": 1}, {"startOffset": 510, "endOffset": 563, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "201", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/IsAccessorDescriptor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 458, "count": 1}], "isBlockCoverage": true}, {"functionName": "IsAccessorDescriptor", "ranges": [{"startOffset": 207, "endOffset": 456, "count": 2}, {"startOffset": 279, "endOffset": 299, "count": 0}, {"startOffset": 439, "endOffset": 455, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "202", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/IsDataDescriptor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 461, "count": 1}], "isBlockCoverage": true}, {"functionName": "IsDataDescriptor", "ranges": [{"startOffset": 207, "endOffset": 459, "count": 6}, {"startOffset": 275, "endOffset": 295, "count": 0}, {"startOffset": 422, "endOffset": 442, "count": 4}, {"startOffset": 442, "endOffset": 458, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "203", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/SameValue.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 307, "count": 1}], "isBlockCoverage": true}, {"functionName": "SameValue", "ranges": [{"startOffset": 127, "endOffset": 305, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "204", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/ToPropertyDescriptor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1488, "count": 1}], "isBlockCoverage": true}, {"functionName": "ToPropertyDescriptor", "ranges": [{"startOffset": 326, "endOffset": 1486, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "205", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/ToBoolean.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 130, "count": 1}], "isBlockCoverage": true}, {"functionName": "ToBoolean", "ranges": [{"startOffset": 83, "endOffset": 128, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "206", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/CreateDataPropertyOrThrow.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 713, "count": 1}], "isBlockCoverage": true}, {"functionName": "CreateDataPropertyOrThrow", "ranges": [{"startOffset": 340, "endOffset": 711, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "207", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/CreateDataProperty.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 753, "count": 1}], "isBlockCoverage": true}, {"functionName": "CreateDataProperty", "ranges": [{"startOffset": 343, "endOffset": 751, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "208", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/OrdinaryDefineOwnProperty.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2354, "count": 1}], "isBlockCoverage": true}, {"functionName": "OrdinaryDefineOwnProperty", "ranges": [{"startOffset": 783, "endOffset": 2352, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "209", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/IsExtensible.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 504, "count": 1}, {"startOffset": 441, "endOffset": 502, "count": 0}], "isBlockCoverage": true}, {"functionName": "IsExtensible", "ranges": [{"startOffset": 358, "endOffset": 439, "count": 0}], "isBlockCoverage": false}, {"functionName": "IsExtensible", "ranges": [{"startOffset": 443, "endOffset": 502, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "210", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/ValidateAndApplyPropertyDescriptor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5949, "count": 1}], "isBlockCoverage": true}, {"functionName": "ValidateAndApplyPropertyDescriptor", "ranges": [{"startOffset": 942, "endOffset": 5947, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "211", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/helpers/isFullyPopulatedPropertyDescriptor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 265, "count": 1}], "isBlockCoverage": true}, {"functionName": "isFullyPopulatedPropertyDescriptor", "ranges": [{"startOffset": 32, "endOffset": 263, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "212", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/IsGenericDescriptor.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 555, "count": 1}], "isBlockCoverage": true}, {"functionName": "IsGenericDescriptor", "ranges": [{"startOffset": 303, "endOffset": 553, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "213", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/ToUint32.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 587, "count": 1}], "isBlockCoverage": true}, {"functionName": "ToUint32", "ranges": [{"startOffset": 295, "endOffset": 585, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "214", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/modulo.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 168, "count": 1}], "isBlockCoverage": true}, {"functionName": "modulo", "ranges": [{"startOffset": 122, "endOffset": 166, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "215", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/helpers/mod.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 179, "count": 1}], "isBlockCoverage": true}, {"functionName": "mod", "ranges": [{"startOffset": 58, "endOffset": 177, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "216", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/array.prototype.map/polyfill.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 289, "count": 1}], "isBlockCoverage": true}, {"functionName": "getPolyfill", "ranges": [{"startOffset": 158, "endOffset": 287, "count": 1}, {"startOffset": 268, "endOffset": 284, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "217", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/array.prototype.map/shim.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 325, "count": 1}], "isBlockCoverage": true}, {"functionName": "shimArrayPrototypeMap", "ranges": [{"startOffset": 117, "endOffset": 323, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "218", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/array.prototype.push/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 693, "count": 1}], "isBlockCoverage": true}, {"functionName": "push", "ranges": [{"startOffset": 445, "endOffset": 574, "count": 202}], "isBlockCoverage": true}]}, {"scriptId": "219", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/array.prototype.push/implementation.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1685, "count": 1}], "isBlockCoverage": true}, {"functionName": "push", "ranges": [{"startOffset": 1085, "endOffset": 1683, "count": 202}, {"startOffset": 1166, "endOffset": 1180, "count": 0}, {"startOffset": 1181, "endOffset": 1198, "count": 0}, {"startOffset": 1345, "endOffset": 1505, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1527, "endOffset": 1614, "count": 202}], "isBlockCoverage": true}]}, {"scriptId": "220", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/Set.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1250, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 447, "count": 1}], "isBlockCoverage": true}, {"functionName": "Set", "ranges": [{"startOffset": 534, "endOffset": 1248, "count": 404}, {"startOffset": 592, "endOffset": 663, "count": 0}, {"startOffset": 688, "endOffset": 764, "count": 0}, {"startOffset": 797, "endOffset": 872, "count": 0}, {"startOffset": 971, "endOffset": 993, "count": 0}, {"startOffset": 995, "endOffset": 1070, "count": 0}, {"startOffset": 1088, "endOffset": 1203, "count": 0}, {"startOffset": 1204, "endOffset": 1210, "count": 0}, {"startOffset": 1215, "endOffset": 1245, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "221", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/helpers/forEach.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 190, "count": 1}], "isBlockCoverage": true}, {"functionName": "for<PERSON>ach", "ranges": [{"startOffset": 32, "endOffset": 188, "count": 202}], "isBlockCoverage": true}]}, {"scriptId": "222", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/array.prototype.push/polyfill.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1879, "count": 1}], "isBlockCoverage": true}, {"functionName": "getPolyfill", "ranges": [{"startOffset": 564, "endOffset": 1877, "count": 1}, {"startOffset": 617, "endOffset": 646, "count": 0}, {"startOffset": 896, "endOffset": 1177, "count": 0}, {"startOffset": 1484, "endOffset": 1513, "count": 0}, {"startOffset": 1823, "endOffset": 1841, "count": 0}, {"startOffset": 1844, "endOffset": 1876, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 673, "endOffset": 868, "count": 1}], "isBlockCoverage": true}, {"functionName": "push", "ranges": [{"startOffset": 1041, "endOffset": 1173, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1279, "endOffset": 1452, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "223", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/es-abstract/2023/HasOwnProperty.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 557, "count": 1}], "isBlockCoverage": true}, {"functionName": "HasOwnProperty", "ranges": [{"startOffset": 300, "endOffset": 555, "count": 2}, {"startOffset": 359, "endOffset": 430, "count": 0}, {"startOffset": 455, "endOffset": 531, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "224", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/array.prototype.push/shim.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 331, "count": 1}], "isBlockCoverage": true}, {"functionName": "shimArrayPrototypePush", "ranges": [{"startOffset": 117, "endOffset": 329, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "225", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/array.prototype.slice/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 577, "count": 1}], "isBlockCoverage": true}, {"functionName": "slice", "ranges": [{"startOffset": 352, "endOffset": 458, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "226", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/array.prototype.slice/implementation.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2323, "count": 1}], "isBlockCoverage": true}, {"functionName": "slice", "ranges": [{"startOffset": 1060, "endOffset": 2282, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "227", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/array.prototype.slice/polyfill.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1041, "count": 1}], "isBlockCoverage": true}, {"functionName": "getPolyfill", "ranges": [{"startOffset": 468, "endOffset": 1039, "count": 1}, {"startOffset": 522, "endOffset": 551, "count": 0}, {"startOffset": 614, "endOffset": 755, "count": 0}, {"startOffset": 775, "endOffset": 1005, "count": 0}], "isBlockCoverage": true}, {"functionName": "slice", "ranges": [{"startOffset": 873, "endOffset": 1001, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "228", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/array.prototype.slice/shim.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 335, "count": 1}], "isBlockCoverage": true}, {"functionName": "shimArrayPrototypeSlice", "ranges": [{"startOffset": 117, "endOffset": 333, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "229", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/string.prototype.split/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 610, "count": 1}], "isBlockCoverage": true}, {"functionName": "split", "ranges": [{"startOffset": 354, "endOffset": 478, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "230", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/string.prototype.split/implementation.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 600, "count": 1}], "isBlockCoverage": true}, {"functionName": "split", "ranges": [{"startOffset": 450, "endOffset": 598, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "231", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/string.prototype.split/polyfill.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 927, "count": 1}], "isBlockCoverage": true}, {"functionName": "getPolyfill", "ranges": [{"startOffset": 83, "endOffset": 925, "count": 1}, {"startOffset": 138, "endOffset": 167, "count": 0}, {"startOffset": 397, "endOffset": 500, "count": 0}, {"startOffset": 861, "endOffset": 890, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "232", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/string.prototype.split/shim.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 327, "count": 1}], "isBlockCoverage": true}, {"functionName": "shimStringSplit", "ranges": [{"startOffset": 117, "endOffset": 325, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "233", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/minimist/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6196, "count": 1}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 15, "endOffset": 181, "count": 0}], "isBlockCoverage": false}, {"functionName": "isNumber", "ranges": [{"startOffset": 183, "endOffset": 366, "count": 0}], "isBlockCoverage": false}, {"functionName": "isConstructorOrProto", "ranges": [{"startOffset": 368, "endOffset": 501, "count": 0}], "isBlockCoverage": false}, {"functionName": "module.exports", "ranges": [{"startOffset": 520, "endOffset": 6194, "count": 1}, {"startOffset": 680, "endOffset": 718, "count": 0}, {"startOffset": 759, "endOffset": 774, "count": 0}, {"startOffset": 776, "endOffset": 805, "count": 0}, {"startOffset": 3170, "endOffset": 3267, "count": 0}, {"startOffset": 3308, "endOffset": 5817, "count": 0}, {"startOffset": 6071, "endOffset": 6108, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 863, "endOffset": 911, "count": 0}], "isBlockCoverage": false}, {"functionName": "aliasIsBoolean", "ranges": [{"startOffset": 939, "endOffset": 1046, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1087, "endOffset": 1287, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1339, "endOffset": 1499, "count": 0}], "isBlockCoverage": false}, {"functionName": "argDefined", "ranges": [{"startOffset": 1565, "endOffset": 1720, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1723, "endOffset": 2631, "count": 0}], "isBlockCoverage": false}, {"functionName": "setArg", "ranges": [{"startOffset": 2634, "endOffset": 2990, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3026, "endOffset": 3113, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5850, "endOffset": 6050, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6135, "endOffset": 6172, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "234", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/defined/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 178, "count": 1}], "isBlockCoverage": true}, {"functionName": "defined", "ranges": [{"startOffset": 32, "endOffset": 176, "count": 1}, {"startOffset": 97, "endOffset": 174, "count": 3}, {"startOffset": 142, "endOffset": 171, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "235", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/npm-which/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1070, "count": 1}], "isBlockCoverage": true}, {"functionName": "module.exports", "ranges": [{"startOffset": 95, "endOffset": 578, "count": 0}], "isBlockCoverage": false}, {"functionName": "module.exports.sync", "ranges": [{"startOffset": 602, "endOffset": 1069, "count": 1}, {"startOffset": 647, "endOffset": 652, "count": 0}, {"startOffset": 681, "endOffset": 697, "count": 0}, {"startOffset": 948, "endOffset": 975, "count": 0}, {"startOffset": 1038, "endOffset": 1047, "count": 0}, {"startOffset": 1051, "endOffset": 1068, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "236", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/npm-which/node_modules/which/which.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3184, "count": 1}, {"startOffset": 218, "endOffset": 223, "count": 0}], "isBlockCoverage": true}, {"functionName": "getNotFoundError", "ranges": [{"startOffset": 260, "endOffset": 371, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPathInfo", "ranges": [{"startOffset": 373, "endOffset": 1203, "count": 1}, {"startOffset": 485, "endOffset": 490, "count": 0}, {"startOffset": 586, "endOffset": 935, "count": 0}, {"startOffset": 1088, "endOffset": 1106, "count": 0}, {"startOffset": 1112, "endOffset": 1126, "count": 0}], "isBlockCoverage": true}, {"functionName": "which", "ranges": [{"startOffset": 1205, "endOffset": 2255, "count": 0}], "isBlockCoverage": false}, {"functionName": "whichSync", "ranges": [{"startOffset": 2257, "endOffset": 3183, "count": 1}, {"startOffset": 2493, "endOffset": 3065, "count": 2}, {"startOffset": 2560, "endOffset": 2589, "count": 0}, {"startOffset": 2597, "endOffset": 2629, "count": 0}, {"startOffset": 2686, "endOffset": 2710, "count": 0}, {"startOffset": 2712, "endOffset": 2749, "count": 0}, {"startOffset": 2933, "endOffset": 3033, "count": 1}, {"startOffset": 2970, "endOffset": 2985, "count": 0}, {"startOffset": 3042, "endOffset": 3055, "count": 1}, {"startOffset": 3065, "endOffset": 3096, "count": 0}, {"startOffset": 3102, "endOffset": 3182, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "237", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/isexe/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1192, "count": 1}, {"startOffset": 92, "endOffset": 128, "count": 0}], "isBlockCoverage": true}, {"functionName": "isexe", "ranges": [{"startOffset": 211, "endOffset": 928, "count": 0}], "isBlockCoverage": false}, {"functionName": "sync", "ranges": [{"startOffset": 930, "endOffset": 1191, "count": 70}, {"startOffset": 1042, "endOffset": 1047, "count": 0}, {"startOffset": 1053, "endOffset": 1189, "count": 68}, {"startOffset": 1131, "endOffset": 1157, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "238", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/isexe/mode.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 909, "count": 1}], "isBlockCoverage": true}, {"functionName": "isexe", "ranges": [{"startOffset": 66, "endOffset": 197, "count": 0}], "isBlockCoverage": false}, {"functionName": "sync", "ranges": [{"startOffset": 199, "endOffset": 279, "count": 70}], "isBlockCoverage": true}, {"functionName": "checkStat", "ranges": [{"startOffset": 281, "endOffset": 370, "count": 2}], "isBlockCoverage": true}, {"functionName": "checkMode", "ranges": [{"startOffset": 372, "endOffset": 908, "count": 2}, {"startOffset": 514, "endOffset": 531, "count": 0}, {"startOffset": 609, "endOffset": 626, "count": 0}, {"startOffset": 792, "endOffset": 825, "count": 0}, {"startOffset": 826, "endOffset": 859, "count": 0}, {"startOffset": 860, "endOffset": 892, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "239", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/npm-path/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4961, "count": 1}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 505, "endOffset": 1258, "count": 1}, {"startOffset": 574, "endOffset": 590, "count": 0}, {"startOffset": 629, "endOffset": 643, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 701, "endOffset": 1255, "count": 1}, {"startOffset": 739, "endOffset": 753, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 778, "endOffset": 1250, "count": 1}], "isBlockCoverage": true}, {"functionName": "getPathAsync", "ranges": [{"startOffset": 1304, "endOffset": 1602, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPathSync", "ranges": [{"startOffset": 1647, "endOffset": 1994, "count": 1}, {"startOffset": 1699, "endOffset": 1704, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1897, "endOffset": 1974, "count": 1}, {"startOffset": 1937, "endOffset": 1946, "count": 0}], "isBlockCoverage": true}, {"functionName": "setPathAsync", "ranges": [{"startOffset": 2292, "endOffset": 2634, "count": 0}], "isBlockCoverage": false}, {"functionName": "setPathSync", "ranges": [{"startOffset": 2679, "endOffset": 2812, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPathArr", "ranges": [{"startOffset": 2962, "endOffset": 3561, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3359, "endOffset": 3483, "count": 0}], "isBlockCoverage": false}, {"functionName": "whichNpm", "ranges": [{"startOffset": 3724, "endOffset": 4147, "count": 1}, {"startOffset": 3784, "endOffset": 3830, "count": 0}, {"startOffset": 3941, "endOffset": 4146, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3985, "endOffset": 4026, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4059, "endOffset": 4144, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPATH<PERSON>ey", "ranges": [{"startOffset": 4200, "endOffset": 4509, "count": 1}, {"startOffset": 4354, "endOffset": 4493, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4411, "endOffset": 4488, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPATHSeparator", "ranges": [{"startOffset": 4564, "endOffset": 4645, "count": 1}, {"startOffset": 4632, "endOffset": 4637, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "240", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/npm-path/node_modules/which/which.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3184, "count": 1}, {"startOffset": 218, "endOffset": 223, "count": 0}], "isBlockCoverage": true}, {"functionName": "getNotFoundError", "ranges": [{"startOffset": 260, "endOffset": 371, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPathInfo", "ranges": [{"startOffset": 373, "endOffset": 1203, "count": 1}, {"startOffset": 485, "endOffset": 490, "count": 0}, {"startOffset": 586, "endOffset": 935, "count": 0}, {"startOffset": 1088, "endOffset": 1106, "count": 0}, {"startOffset": 1112, "endOffset": 1126, "count": 0}], "isBlockCoverage": true}, {"functionName": "which", "ranges": [{"startOffset": 1205, "endOffset": 2255, "count": 0}], "isBlockCoverage": false}, {"functionName": "whichSync", "ranges": [{"startOffset": 2257, "endOffset": 3183, "count": 1}, {"startOffset": 2493, "endOffset": 3065, "count": 68}, {"startOffset": 2560, "endOffset": 2589, "count": 0}, {"startOffset": 2597, "endOffset": 2629, "count": 0}, {"startOffset": 2686, "endOffset": 2710, "count": 0}, {"startOffset": 2712, "endOffset": 2749, "count": 0}, {"startOffset": 2933, "endOffset": 3033, "count": 1}, {"startOffset": 2970, "endOffset": 2985, "count": 0}, {"startOffset": 3042, "endOffset": 3055, "count": 67}, {"startOffset": 3065, "endOffset": 3096, "count": 0}, {"startOffset": 3102, "endOffset": 3182, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "241", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/npm-path/find-prefix.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1681, "count": 1}], "isBlockCoverage": true}, {"functionName": "module.exports", "ranges": [{"startOffset": 68, "endOffset": 147, "count": 1}], "isBlockCoverage": true}, {"functionName": "readdir", "ranges": [{"startOffset": 149, "endOffset": 350, "count": 1}, {"startOffset": 250, "endOffset": 290, "count": 0}, {"startOffset": 320, "endOffset": 349, "count": 0}], "isBlockCoverage": true}, {"functionName": "findPrefix", "ranges": [{"startOffset": 401, "endOffset": 896, "count": 1}, {"startOffset": 777, "endOffset": 826, "count": 0}, {"startOffset": 843, "endOffset": 861, "count": 0}], "isBlockCoverage": true}, {"functionName": "cb", "ranges": [{"startOffset": 442, "endOffset": 560, "count": 1}, {"startOffset": 497, "endOffset": 559, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 519, "endOffset": 555, "count": 0}], "isBlockCoverage": false}, {"functionName": "findPrefix_", "ranges": [{"startOffset": 898, "endOffset": 1680, "count": 1}, {"startOffset": 1002, "endOffset": 1035, "count": 0}, {"startOffset": 1038, "endOffset": 1073, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1096, "endOffset": 1677, "count": 1}, {"startOffset": 1241, "endOffset": 1258, "count": 0}, {"startOffset": 1260, "endOffset": 1345, "count": 0}, {"startOffset": 1399, "endOffset": 1424, "count": 0}, {"startOffset": 1471, "endOffset": 1518, "count": 0}, {"startOffset": 1552, "endOffset": 1676, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "242", "url": "node:child_process", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27267, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1936, "endOffset": 1965, "count": 0}], "isBlockCoverage": false}, {"functionName": "fork", "ranges": [{"startOffset": 3430, "endOffset": 5105, "count": 0}], "isBlockCoverage": false}, {"functionName": "_fork<PERSON><PERSON>d", "ranges": [{"startOffset": 5107, "endOffset": 5596, "count": 0}], "isBlockCoverage": false}, {"functionName": "normalizeExecArgs", "ranges": [{"startOffset": 5598, "endOffset": 6112, "count": 0}], "isBlockCoverage": false}, {"functionName": "exec", "ranges": [{"startOffset": 6649, "endOffset": 6896, "count": 0}], "isBlockCoverage": false}, {"functionName": "customPromiseExecFunction", "ranges": [{"startOffset": 6932, "endOffset": 7296, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6953, "endOffset": 7293, "count": 0}], "isBlockCoverage": false}, {"functionName": "normalizeExecFileArgs", "ranges": [{"startOffset": 7433, "endOffset": 8331, "count": 0}], "isBlockCoverage": false}, {"functionName": "execFile", "ranges": [{"startOffset": 8944, "endOffset": 13708, "count": 0}], "isBlockCoverage": false}, {"functionName": "copyProcessEnvToEnv", "ranges": [{"startOffset": 13852, "endOffset": 14052, "count": 0}], "isBlockCoverage": false}, {"functionName": "normalizeSpawnArguments", "ranges": [{"startOffset": 14054, "endOffset": 19366, "count": 0}], "isBlockCoverage": false}, {"functionName": "abortChildProcess", "ranges": [{"startOffset": 19368, "endOffset": 19624, "count": 0}], "isBlockCoverage": false}, {"functionName": "spawn", "ranges": [{"startOffset": 20179, "endOffset": 21403, "count": 0}], "isBlockCoverage": false}, {"functionName": "spawnSync", "ranges": [{"startOffset": 22163, "endOffset": 23671, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkExecSyncError", "ranges": [{"startOffset": 23674, "endOffset": 24060, "count": 0}], "isBlockCoverage": false}, {"functionName": "execFileSync", "ranges": [{"startOffset": 24573, "endOffset": 25037, "count": 0}], "isBlockCoverage": false}, {"functionName": "execSync", "ranges": [{"startOffset": 25535, "endOffset": 25911, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateArgument<PERSON>ull<PERSON>heck", "ranges": [{"startOffset": 25914, "endOffset": 26137, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateArgumentsNullCheck", "ranges": [{"startOffset": 26140, "endOffset": 26302, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateTimeout", "ranges": [{"startOffset": 26305, "endOffset": 26493, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateMax<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 26496, "endOffset": 26769, "count": 0}], "isBlockCoverage": false}, {"functionName": "sanitizeKillSignal", "ranges": [{"startOffset": 26772, "endOffset": 27135, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "243", "url": "node:internal/child_process", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 31799, "count": 1}], "isBlockCoverage": false}, {"functionName": "send", "ranges": [{"startOffset": 2498, "endOffset": 2557, "count": 0}], "isBlockCoverage": false}, {"functionName": "got", "ranges": [{"startOffset": 2564, "endOffset": 2618, "count": 0}], "isBlockCoverage": false}, {"functionName": "send", "ranges": [{"startOffset": 2680, "endOffset": 2747, "count": 0}], "isBlockCoverage": false}, {"functionName": "got", "ranges": [{"startOffset": 2754, "endOffset": 2895, "count": 0}], "isBlockCoverage": false}, {"functionName": "send", "ranges": [{"startOffset": 2925, "endOffset": 4479, "count": 0}], "isBlockCoverage": false}, {"functionName": "postSend", "ranges": [{"startOffset": 4486, "endOffset": 5232, "count": 0}], "isBlockCoverage": false}, {"functionName": "got", "ranges": [{"startOffset": 5239, "endOffset": 5696, "count": 0}], "isBlockCoverage": false}, {"functionName": "send", "ranges": [{"startOffset": 5761, "endOffset": 5820, "count": 0}], "isBlockCoverage": false}, {"functionName": "got", "ranges": [{"startOffset": 5827, "endOffset": 5881, "count": 0}], "isBlockCoverage": false}, {"functionName": "send", "ranges": [{"startOffset": 5946, "endOffset": 6066, "count": 0}], "isBlockCoverage": false}, {"functionName": "got", "ranges": [{"startOffset": 6073, "endOffset": 6232, "count": 0}], "isBlockCoverage": false}, {"functionName": "stdioStringToArray", "ranges": [{"startOffset": 6243, "endOffset": 6658, "count": 0}], "isBlockCoverage": false}, {"functionName": "ChildProcess", "ranges": [{"startOffset": 6660, "endOffset": 8153, "count": 0}], "isBlockCoverage": false}, {"functionName": "flushStdio", "ranges": [{"startOffset": 8276, "endOffset": 8754, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSocket", "ranges": [{"startOffset": 8757, "endOffset": 8847, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHandleWrapType", "ranges": [{"startOffset": 8850, "endOffset": 9079, "count": 0}], "isBlockCoverage": false}, {"functionName": "closePending<PERSON><PERSON>le", "ranges": [{"startOffset": 9081, "endOffset": 9194, "count": 0}], "isBlockCoverage": false}, {"functionName": "ChildProcess.spawn", "ranges": [{"startOffset": 9228, "endOffset": 12799, "count": 0}], "isBlockCoverage": false}, {"functionName": "onErrorNT", "ranges": [{"startOffset": 12803, "endOffset": 12864, "count": 0}], "isBlockCoverage": false}, {"functionName": "onSpawnNT", "ranges": [{"startOffset": 12867, "endOffset": 12917, "count": 0}], "isBlockCoverage": false}, {"functionName": "ChildProcess.kill", "ranges": [{"startOffset": 12950, "endOffset": 13623, "count": 0}], "isBlockCoverage": false}, {"functionName": "ChildProcess.<computed>", "ranges": [{"startOffset": 13666, "endOffset": 13723, "count": 0}], "isBlockCoverage": false}, {"functionName": "ChildProcess.ref", "ranges": [{"startOffset": 13756, "endOffset": 13810, "count": 0}], "isBlockCoverage": false}, {"functionName": "ChildProcess.unref", "ranges": [{"startOffset": 13845, "endOffset": 13901, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 13904, "endOffset": 14901, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupChannel", "ranges": [{"startOffset": 15049, "endOffset": 27173, "count": 0}], "isBlockCoverage": false}, {"functionName": "isInternal", "ranges": [{"startOffset": 27208, "endOffset": 27518, "count": 0}], "isBlockCoverage": false}, {"functionName": "getValidStdio", "ranges": [{"startOffset": 27552, "endOffset": 30477, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSocketList", "ranges": [{"startOffset": 30480, "endOffset": 30796, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeClose", "ranges": [{"startOffset": 30799, "endOffset": 31000, "count": 0}], "isBlockCoverage": false}, {"functionName": "spawnSync", "ranges": [{"startOffset": 31002, "endOffset": 31673, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "244", "url": "node:net", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 68429, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1645, "endOffset": 1670, "count": 1}], "isBlockCoverage": true}, {"functionName": "noop", "ranges": [{"startOffset": 4172, "endOffset": 4180, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFlags", "ranges": [{"startOffset": 4653, "endOffset": 4747, "count": 0}], "isBlockCoverage": false}, {"functionName": "createHandle", "ranges": [{"startOffset": 4749, "endOffset": 5132, "count": 1}, {"startOffset": 4916, "endOffset": 4938, "count": 0}, {"startOffset": 4973, "endOffset": 5131, "count": 0}], "isBlockCoverage": true}, {"functionName": "getNewAsyncId", "ranges": [{"startOffset": 5135, "endOffset": 5273, "count": 5}, {"startOffset": 5230, "endOffset": 5248, "count": 0}], "isBlockCoverage": true}, {"functionName": "isPipeName", "ranges": [{"startOffset": 5276, "endOffset": 5359, "count": 0}], "isBlockCoverage": false}, {"functionName": "createServer", "ranges": [{"startOffset": 5560, "endOffset": 5664, "count": 0}], "isBlockCoverage": false}, {"functionName": "connect", "ranges": [{"startOffset": 5887, "endOffset": 6288, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDefaultAutoSelectFamily", "ranges": [{"startOffset": 6290, "endOffset": 6365, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDefaultAutoSelectFamily", "ranges": [{"startOffset": 6367, "endOffset": 6483, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDefaultAutoSelectFamilyAttemptTimeout", "ranges": [{"startOffset": 6485, "endOffset": 6588, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDefaultAutoSelectFamilyAttemptTimeout", "ranges": [{"startOffset": 6590, "endOffset": 6777, "count": 0}], "isBlockCoverage": false}, {"functionName": "normalizeArgs", "ranges": [{"startOffset": 7271, "endOffset": 7997, "count": 0}], "isBlockCoverage": false}, {"functionName": "initSocketHandle", "ranges": [{"startOffset": 8069, "endOffset": 8672, "count": 3}, {"startOffset": 8425, "endOffset": 8666, "count": 0}], "isBlockCoverage": true}, {"functionName": "closeSocketHandle", "ranges": [{"startOffset": 8674, "endOffset": 9014, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8793, "endOffset": 9006, "count": 1}, {"startOffset": 8895, "endOffset": 9000, "count": 0}], "isBlockCoverage": true}, {"functionName": "Socket", "ranges": [{"startOffset": 9266, "endOffset": 13893, "count": 3}, {"startOffset": 9326, "endOffset": 9353, "count": 0}, {"startOffset": 9381, "endOffset": 9510, "count": 0}, {"startOffset": 9580, "endOffset": 9828, "count": 0}, {"startOffset": 9890, "endOffset": 10086, "count": 0}, {"startOffset": 10648, "endOffset": 10674, "count": 0}, {"startOffset": 11080, "endOffset": 11188, "count": 2}, {"startOffset": 11188, "endOffset": 12436, "count": 1}, {"startOffset": 11716, "endOffset": 11754, "count": 0}, {"startOffset": 11843, "endOffset": 11884, "count": 0}, {"startOffset": 11885, "endOffset": 11897, "count": 0}, {"startOffset": 11899, "endOffset": 12432, "count": 0}, {"startOffset": 12523, "endOffset": 12600, "count": 0}, {"startOffset": 12601, "endOffset": 12647, "count": 0}, {"startOffset": 12649, "endOffset": 12864, "count": 0}, {"startOffset": 13432, "endOffset": 13601, "count": 0}, {"startOffset": 13633, "endOffset": 13660, "count": 0}, {"startOffset": 13688, "endOffset": 13740, "count": 0}], "isBlockCoverage": true}, {"functionName": "_unrefTimer", "ranges": [{"startOffset": 14066, "endOffset": 14196, "count": 605}, {"startOffset": 14168, "endOffset": 14190, "count": 0}], "isBlockCoverage": true}, {"functionName": "Socket._final", "ranges": [{"startOffset": 14316, "endOffset": 14936, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterShutdown", "ranges": [{"startOffset": 14939, "endOffset": 15085, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeAfterFIN", "ranges": [{"startOffset": 15292, "endOffset": 15800, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._onTimeout", "ranges": [{"startOffset": 15882, "endOffset": 16386, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setNoDelay", "ranges": [{"startOffset": 16420, "endOffset": 16797, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setKeepAlive", "ranges": [{"startOffset": 16833, "endOffset": 17439, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.address", "ranges": [{"startOffset": 17470, "endOffset": 17514, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 17600, "endOffset": 17644, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 17724, "endOffset": 17780, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 17891, "endOffset": 18206, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 18295, "endOffset": 18375, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 18463, "endOffset": 18508, "count": 235}], "isBlockCoverage": true}, {"functionName": "tryReadStart", "ranges": [{"startOffset": 18516, "endOffset": 18766, "count": 1}, {"startOffset": 18716, "endOffset": 18764, "count": 0}], "isBlockCoverage": true}, {"functionName": "Socket._read", "ranges": [{"startOffset": 18858, "endOffset": 19177, "count": 235}, {"startOffset": 19021, "endOffset": 19113, "count": 0}, {"startOffset": 19146, "endOffset": 19175, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19088, "endOffset": 19107, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.end", "ranges": [{"startOffset": 19204, "endOffset": 19360, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.resetAndDestroy", "ranges": [{"startOffset": 19398, "endOffset": 19748, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.pause", "ranges": [{"startOffset": 19776, "endOffset": 20114, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.resume", "ranges": [{"startOffset": 20144, "endOffset": 20328, "count": 1}, {"startOffset": 20177, "endOffset": 20196, "count": 0}, {"startOffset": 20197, "endOffset": 20212, "count": 0}, {"startOffset": 20213, "endOffset": 20243, "count": 0}, {"startOffset": 20245, "endOffset": 20274, "count": 0}], "isBlockCoverage": true}, {"functionName": "Socket.read", "ranges": [{"startOffset": 20356, "endOffset": 20542, "count": 238}, {"startOffset": 20390, "endOffset": 20409, "count": 0}, {"startOffset": 20410, "endOffset": 20425, "count": 0}, {"startOffset": 20426, "endOffset": 20456, "count": 0}, {"startOffset": 20458, "endOffset": 20487, "count": 0}], "isBlockCoverage": true}, {"functionName": "onReadableStreamEnd", "ranges": [{"startOffset": 20589, "endOffset": 20688, "count": 1}], "isBlockCoverage": true}, {"functionName": "Socket.destroySoon", "ranges": [{"startOffset": 20722, "endOffset": 20869, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._destroy", "ranges": [{"startOffset": 20901, "endOffset": 22415, "count": 1}, {"startOffset": 21198, "endOffset": 21204, "count": 0}, {"startOffset": 21424, "endOffset": 21669, "count": 0}, {"startOffset": 21710, "endOffset": 21946, "count": 0}, {"startOffset": 22165, "endOffset": 22236, "count": 0}, {"startOffset": 22258, "endOffset": 22413, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21499, "endOffset": 21584, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 21890, "endOffset": 21938, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._reset", "ranges": [{"startOffset": 22444, "endOffset": 22543, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._getpeername", "ranges": [{"startOffset": 22578, "endOffset": 22875, "count": 0}], "isBlockCoverage": false}, {"functionName": "protoGetter", "ranges": [{"startOffset": 22878, "endOffset": 23060, "count": 9}], "isBlockCoverage": true}, {"functionName": "bytesRead", "ranges": [{"startOffset": 23087, "endOffset": 23178, "count": 0}], "isBlockCoverage": false}, {"functionName": "remoteAddress", "ranges": [{"startOffset": 23211, "endOffset": 23277, "count": 0}], "isBlockCoverage": false}, {"functionName": "remoteFamily", "ranges": [{"startOffset": 23309, "endOffset": 23373, "count": 0}], "isBlockCoverage": false}, {"functionName": "remotePort", "ranges": [{"startOffset": 23403, "endOffset": 23463, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._getsockname", "ranges": [{"startOffset": 23500, "endOffset": 23776, "count": 0}], "isBlockCoverage": false}, {"functionName": "localAddress", "ranges": [{"startOffset": 23808, "endOffset": 23873, "count": 0}], "isBlockCoverage": false}, {"functionName": "localPort", "ranges": [{"startOffset": 23903, "endOffset": 23962, "count": 0}], "isBlockCoverage": false}, {"functionName": "localFamily", "ranges": [{"startOffset": 23993, "endOffset": 24056, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.<computed>", "ranges": [{"startOffset": 24097, "endOffset": 24144, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._writeGeneric", "ranges": [{"startOffset": 24180, "endOffset": 25092, "count": 370}, {"startOffset": 24402, "endOffset": 24746, "count": 0}, {"startOffset": 24827, "endOffset": 24883, "count": 0}, {"startOffset": 24937, "endOffset": 24973, "count": 0}, {"startOffset": 25052, "endOffset": 25090, "count": 0}], "isBlockCoverage": true}, {"functionName": "connect", "ranges": [{"startOffset": 24497, "endOffset": 24611, "count": 0}], "isBlockCoverage": false}, {"functionName": "onClose", "ranges": [{"startOffset": 24618, "endOffset": 24697, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._writev", "ranges": [{"startOffset": 25123, "endOffset": 25191, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._write", "ranges": [{"startOffset": 25221, "endOffset": 25302, "count": 370}], "isBlockCoverage": true}, {"functionName": "_bytesDispatched", "ranges": [{"startOffset": 25495, "endOffset": 25599, "count": 0}], "isBlockCoverage": false}, {"functionName": "bytes<PERSON>ritten", "ranges": [{"startOffset": 25631, "endOffset": 26560, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkBindError", "ranges": [{"startOffset": 26565, "endOffset": 27364, "count": 0}], "isBlockCoverage": false}, {"functionName": "internalConnect", "ranges": [{"startOffset": 27367, "endOffset": 29467, "count": 0}], "isBlockCoverage": false}, {"functionName": "internalConnectMultiple", "ranges": [{"startOffset": 29470, "endOffset": 32410, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.connect", "ranges": [{"startOffset": 32439, "endOffset": 34035, "count": 0}], "isBlockCoverage": false}, {"functionName": "reinitializeHandle", "ranges": [{"startOffset": 34078, "endOffset": 34232, "count": 0}], "isBlockCoverage": false}, {"functionName": "socketToDnsFamily", "ranges": [{"startOffset": 34235, "endOffset": 34381, "count": 0}], "isBlockCoverage": false}, {"functionName": "lookupAndConnect", "ranges": [{"startOffset": 34383, "endOffset": 38314, "count": 0}], "isBlockCoverage": false}, {"functionName": "lookupAndConnectMultiple", "ranges": [{"startOffset": 38316, "endOffset": 42415, "count": 0}], "isBlockCoverage": false}, {"functionName": "connectErrorNT", "ranges": [{"startOffset": 42417, "endOffset": 42476, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.ref", "ranges": [{"startOffset": 42502, "endOffset": 42689, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.unref", "ranges": [{"startOffset": 42718, "endOffset": 42911, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterConnect", "ranges": [{"startOffset": 42915, "endOffset": 44603, "count": 0}], "isBlockCoverage": false}, {"functionName": "addClientAbortSignalOption", "ranges": [{"startOffset": 44605, "endOffset": 45015, "count": 0}], "isBlockCoverage": false}, {"functionName": "createConnectionError", "ranges": [{"startOffset": 45017, "endOffset": 45538, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterConnectMultiple", "ranges": [{"startOffset": 45540, "endOffset": 46858, "count": 0}], "isBlockCoverage": false}, {"functionName": "internalConnectMultipleTimeout", "ranges": [{"startOffset": 46860, "endOffset": 47356, "count": 0}], "isBlockCoverage": false}, {"functionName": "addServerAbortSignalOption", "ranges": [{"startOffset": 47358, "endOffset": 47788, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server", "ranges": [{"startOffset": 47790, "endOffset": 49402, "count": 0}], "isBlockCoverage": false}, {"functionName": "toNumber", "ranges": [{"startOffset": 49513, "endOffset": 49578, "count": 0}], "isBlockCoverage": false}, {"functionName": "createServerHandle", "ranges": [{"startOffset": 49646, "endOffset": 51098, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupListenHandle", "ranges": [{"startOffset": 51100, "endOffset": 53794, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitErrorNT", "ranges": [{"startOffset": 53861, "endOffset": 53923, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitListeningNT", "ranges": [{"startOffset": 53926, "endOffset": 54041, "count": 0}], "isBlockCoverage": false}, {"functionName": "listenInCluster", "ranges": [{"startOffset": 54044, "endOffset": 55626, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.listen", "ranges": [{"startOffset": 55655, "endOffset": 60017, "count": 0}], "isBlockCoverage": false}, {"functionName": "lookupAndListen", "ranges": [{"startOffset": 60020, "endOffset": 60563, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 60645, "endOffset": 60688, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.address", "ranges": [{"startOffset": 60764, "endOffset": 61057, "count": 0}], "isBlockCoverage": false}, {"functionName": "onconnection", "ranges": [{"startOffset": 61060, "endOffset": 62963, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.getConnections", "ranges": [{"startOffset": 63112, "endOffset": 63865, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.close", "ranges": [{"startOffset": 63894, "endOffset": 64784, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.<computed>", "ranges": [{"startOffset": 64826, "endOffset": 64946, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server._emitCloseIfDrained", "ranges": [{"startOffset": 64988, "endOffset": 65374, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitCloseNT", "ranges": [{"startOffset": 65378, "endOffset": 65461, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.<computed>", "ranges": [{"startOffset": 65520, "endOffset": 65682, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 65904, "endOffset": 65940, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 65944, "endOffset": 65985, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 66065, "endOffset": 66096, "count": 1110}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 66100, "endOffset": 66136, "count": 4}], "isBlockCoverage": true}, {"functionName": "Server._setupWorker", "ranges": [{"startOffset": 66175, "endOffset": 66416, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.ref", "ranges": [{"startOffset": 66442, "endOffset": 66540, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.unref", "ranges": [{"startOffset": 66568, "endOffset": 66667, "count": 0}], "isBlockCoverage": false}, {"functionName": "_setSimultaneousAccepts", "ranges": [{"startOffset": 66809, "endOffset": 67478, "count": 0}], "isBlockCoverage": false}, {"functionName": "_setSimultaneousAccepts", "ranges": [{"startOffset": 67517, "endOffset": 67758, "count": 0}], "isBlockCoverage": false}, {"functionName": "get BlockList", "ranges": [{"startOffset": 67887, "endOffset": 67989, "count": 0}], "isBlockCoverage": false}, {"functionName": "get <PERSON>cket<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 67993, "endOffset": 68115, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "245", "url": "node:internal/stream_base_commons", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7170, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1057, "endOffset": 1082, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleWriteReq", "ranges": [{"startOffset": 1201, "endOffset": 2056, "count": 370}, {"startOffset": 1426, "endOffset": 1444, "count": 0}, {"startOffset": 1473, "endOffset": 1487, "count": 0}, {"startOffset": 1492, "endOffset": 1556, "count": 0}, {"startOffset": 1561, "endOffset": 1573, "count": 0}, {"startOffset": 1578, "endOffset": 1639, "count": 0}, {"startOffset": 1644, "endOffset": 1706, "count": 0}, {"startOffset": 1711, "endOffset": 1723, "count": 0}, {"startOffset": 1728, "endOffset": 1741, "count": 0}, {"startOffset": 1746, "endOffset": 1761, "count": 0}, {"startOffset": 1766, "endOffset": 1830, "count": 0}, {"startOffset": 1835, "endOffset": 2050, "count": 0}], "isBlockCoverage": true}, {"functionName": "onWriteComplete", "ranges": [{"startOffset": 2058, "endOffset": 2747, "count": 0}], "isBlockCoverage": false}, {"functionName": "createWriteWrap", "ranges": [{"startOffset": 2749, "endOffset": 2987, "count": 370}], "isBlockCoverage": true}, {"functionName": "writevGeneric", "ranges": [{"startOffset": 2989, "endOffset": 3628, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeGeneric", "ranges": [{"startOffset": 3630, "endOffset": 3835, "count": 370}], "isBlockCoverage": true}, {"functionName": "afterWriteDispatched", "ranges": [{"startOffset": 3837, "endOffset": 4144, "count": 370}, {"startOffset": 4004, "endOffset": 4059, "count": 0}], "isBlockCoverage": true}, {"functionName": "onStreamRead", "ranges": [{"startOffset": 4146, "endOffset": 6085, "count": 235}, {"startOffset": 4340, "endOffset": 4360, "count": 234}, {"startOffset": 4362, "endOffset": 5102, "count": 234}, {"startOffset": 4447, "endOffset": 4706, "count": 0}, {"startOffset": 4890, "endOffset": 5081, "count": 0}, {"startOffset": 5102, "endOffset": 5123, "count": 1}, {"startOffset": 5123, "endOffset": 5140, "count": 0}, {"startOffset": 5140, "endOffset": 5471, "count": 1}, {"startOffset": 5471, "endOffset": 5664, "count": 0}, {"startOffset": 5664, "endOffset": 5749, "count": 1}, {"startOffset": 5749, "endOffset": 5816, "count": 0}, {"startOffset": 5816, "endOffset": 6083, "count": 1}, {"startOffset": 5861, "endOffset": 5901, "count": 0}], "isBlockCoverage": true}, {"functionName": "setStreamTimeout", "ranges": [{"startOffset": 6087, "endOffset": 6963, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "246", "url": "node:diagnostics_channel", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10086, "count": 1}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 808, "endOffset": 1217, "count": 1}], "isBlockCoverage": false}, {"functionName": "#finalizers", "ranges": [{"startOffset": 888, "endOffset": 924, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 930, "endOffset": 1047, "count": 9}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1051, "endOffset": 1099, "count": 9}, {"startOffset": 1087, "endOffset": 1092, "count": 0}], "isBlockCoverage": true}, {"functionName": "incRef", "ranges": [{"startOffset": 1103, "endOffset": 1157, "count": 0}], "isBlockCoverage": false}, {"functionName": "decRef", "ranges": [{"startOffset": 1161, "endOffset": 1215, "count": 0}], "isBlockCoverage": false}, {"functionName": "markActive", "ranges": [{"startOffset": 1219, "endOffset": 1424, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeMarkInactive", "ranges": [{"startOffset": 1426, "endOffset": 1794, "count": 0}], "isBlockCoverage": false}, {"functionName": "defaultTransform", "ranges": [{"startOffset": 1796, "endOffset": 1846, "count": 0}], "isBlockCoverage": false}, {"functionName": "wrapStoreRun", "ranges": [{"startOffset": 1848, "endOffset": 2176, "count": 0}], "isBlockCoverage": false}, {"functionName": "subscribe", "ranges": [{"startOffset": 2258, "endOffset": 2492, "count": 0}], "isBlockCoverage": false}, {"functionName": "unsubscribe", "ranges": [{"startOffset": 2496, "endOffset": 2943, "count": 0}], "isBlockCoverage": false}, {"functionName": "bindStore", "ranges": [{"startOffset": 2947, "endOffset": 3115, "count": 0}], "isBlockCoverage": false}, {"functionName": "unbindStore", "ranges": [{"startOffset": 3119, "endOffset": 3318, "count": 0}], "isBlockCoverage": false}, {"functionName": "get hasSubscribers", "ranges": [{"startOffset": 3322, "endOffset": 3365, "count": 0}], "isBlockCoverage": false}, {"functionName": "publish", "ranges": [{"startOffset": 3369, "endOffset": 3709, "count": 0}], "isBlockCoverage": false}, {"functionName": "runStores", "ranges": [{"startOffset": 3713, "endOffset": 4052, "count": 0}], "isBlockCoverage": false}, {"functionName": "Channel", "ranges": [{"startOffset": 4074, "endOffset": 4215, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4226, "endOffset": 4411, "count": 0}], "isBlockCoverage": false}, {"functionName": "subscribe", "ranges": [{"startOffset": 4415, "endOffset": 4500, "count": 0}], "isBlockCoverage": false}, {"functionName": "unsubscribe", "ranges": [{"startOffset": 4504, "endOffset": 4541, "count": 0}], "isBlockCoverage": false}, {"functionName": "bindStore", "ranges": [{"startOffset": 4545, "endOffset": 4638, "count": 0}], "isBlockCoverage": false}, {"functionName": "unbindStore", "ranges": [{"startOffset": 4642, "endOffset": 4679, "count": 0}], "isBlockCoverage": false}, {"functionName": "get hasSubscribers", "ranges": [{"startOffset": 4683, "endOffset": 4727, "count": 0}], "isBlockCoverage": false}, {"functionName": "publish", "ranges": [{"startOffset": 4731, "endOffset": 4743, "count": 0}], "isBlockCoverage": false}, {"functionName": "runStores", "ranges": [{"startOffset": 4747, "endOffset": 4834, "count": 0}], "isBlockCoverage": false}, {"functionName": "channel", "ranges": [{"startOffset": 4874, "endOffset": 5140, "count": 9}, {"startOffset": 4952, "endOffset": 4967, "count": 0}, {"startOffset": 5000, "endOffset": 5027, "count": 0}, {"startOffset": 5029, "endOffset": 5109, "count": 0}], "isBlockCoverage": true}, {"functionName": "subscribe", "ranges": [{"startOffset": 5142, "endOffset": 5232, "count": 0}], "isBlockCoverage": false}, {"functionName": "unsubscribe", "ranges": [{"startOffset": 5234, "endOffset": 5328, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasSubscribers", "ranges": [{"startOffset": 5330, "endOffset": 5465, "count": 0}], "isBlockCoverage": false}, {"functionName": "assertChannel", "ranges": [{"startOffset": 5554, "endOffset": 5696, "count": 0}], "isBlockCoverage": false}, {"functionName": "tracingChannelFrom", "ranges": [{"startOffset": 5698, "endOffset": 6223, "count": 5}, {"startOffset": 5854, "endOffset": 5923, "count": 0}, {"startOffset": 5925, "endOffset": 6222, "count": 0}], "isBlockCoverage": true}, {"functionName": "TracingChannel", "ranges": [{"startOffset": 6250, "endOffset": 6475, "count": 1}, {"startOffset": 6321, "endOffset": 6471, "count": 5}], "isBlockCoverage": true}, {"functionName": "get hasSubscribers", "ranges": [{"startOffset": 6479, "endOffset": 6689, "count": 0}], "isBlockCoverage": false}, {"functionName": "subscribe", "ranges": [{"startOffset": 6693, "endOffset": 6845, "count": 0}], "isBlockCoverage": false}, {"functionName": "unsubscribe", "ranges": [{"startOffset": 6849, "endOffset": 7082, "count": 0}], "isBlockCoverage": false}, {"functionName": "traceSync", "ranges": [{"startOffset": 7086, "endOffset": 7597, "count": 0}], "isBlockCoverage": false}, {"functionName": "tracePromise", "ranges": [{"startOffset": 7601, "endOffset": 8787, "count": 0}], "isBlockCoverage": false}, {"functionName": "trace<PERSON><PERSON>back", "ranges": [{"startOffset": 8791, "endOffset": 9884, "count": 0}], "isBlockCoverage": false}, {"functionName": "tracingChannel", "ranges": [{"startOffset": 9888, "endOffset": 9976, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "247", "url": "node:internal/perf/observe", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16722, "count": 1}], "isBlockCoverage": false}, {"functionName": "queuePending", "ranges": [{"startOffset": 2600, "endOffset": 2861, "count": 0}], "isBlockCoverage": false}, {"functionName": "getObserverType", "ranges": [{"startOffset": 2863, "endOffset": 3203, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeDecrementObserverCounts", "ranges": [{"startOffset": 3205, "endOffset": 3626, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeIncrementObserverCount", "ranges": [{"startOffset": 3628, "endOffset": 3970, "count": 0}], "isBlockCoverage": false}, {"functionName": "performanceObserverSorter", "ranges": [{"startOffset": 4047, "endOffset": 4114, "count": 0}], "isBlockCoverage": false}, {"functionName": "PerformanceObserverEntryList", "ranges": [{"startOffset": 4156, "endOffset": 4384, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEntries", "ranges": [{"startOffset": 4388, "endOffset": 4527, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEntriesByType", "ranges": [{"startOffset": 4531, "endOffset": 4835, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEntriesByName", "ranges": [{"startOffset": 4839, "endOffset": 5332, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5336, "endOffset": 5584, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5936, "endOffset": 9533, "count": 0}], "isBlockCoverage": false}, {"functionName": "enqueue", "ranges": [{"startOffset": 10015, "endOffset": 10217, "count": 0}], "isBlockCoverage": false}, {"functionName": "bufferUserTiming", "ranges": [{"startOffset": 10278, "endOffset": 11304, "count": 0}], "isBlockCoverage": false}, {"functionName": "bufferResourceTiming", "ranges": [{"startOffset": 11592, "endOffset": 12856, "count": 0}], "isBlockCoverage": false}, {"functionName": "setResourceTimingBufferSize", "ranges": [{"startOffset": 12944, "endOffset": 13216, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDispatchBufferFull", "ranges": [{"startOffset": 13218, "endOffset": 13283, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearEntriesFromBuffer", "ranges": [{"startOffset": 13285, "endOffset": 13885, "count": 0}], "isBlockCoverage": false}, {"functionName": "filterBufferMapByNameAndType", "ranges": [{"startOffset": 13887, "endOffset": 14729, "count": 0}], "isBlockCoverage": false}, {"functionName": "observerCallback", "ranges": [{"startOffset": 14731, "endOffset": 15847, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasObserver", "ranges": [{"startOffset": 15884, "endOffset": 16003, "count": 0}], "isBlockCoverage": false}, {"functionName": "startPerf", "ranges": [{"startOffset": 16006, "endOffset": 16116, "count": 0}], "isBlockCoverage": false}, {"functionName": "stopPerf", "ranges": [{"startOffset": 16118, "endOffset": 16433, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "248", "url": "node:internal/perf/performance_entry", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3272, "count": 1}], "isBlockCoverage": false}, {"functionName": "isPerformanceEntry", "ranges": [{"startOffset": 675, "endOffset": 748, "count": 0}], "isBlockCoverage": false}, {"functionName": "PerformanceEntry", "ranges": [{"startOffset": 777, "endOffset": 1132, "count": 0}], "isBlockCoverage": false}, {"functionName": "get name", "ranges": [{"startOffset": 1136, "endOffset": 1236, "count": 0}], "isBlockCoverage": false}, {"functionName": "get entryType", "ranges": [{"startOffset": 1240, "endOffset": 1355, "count": 0}], "isBlockCoverage": false}, {"functionName": "get startTime", "ranges": [{"startOffset": 1359, "endOffset": 1474, "count": 0}], "isBlockCoverage": false}, {"functionName": "get duration", "ranges": [{"startOffset": 1478, "endOffset": 1590, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1594, "endOffset": 1838, "count": 0}], "isBlockCoverage": false}, {"functionName": "toJSON", "ranges": [{"startOffset": 1842, "endOffset": 2064, "count": 0}], "isBlockCoverage": false}, {"functionName": "createPerformanceEntry", "ranges": [{"startOffset": 2286, "endOffset": 2422, "count": 0}], "isBlockCoverage": false}, {"functionName": "get detail", "ranges": [{"startOffset": 2539, "endOffset": 2649, "count": 0}], "isBlockCoverage": false}, {"functionName": "toJSON", "ranges": [{"startOffset": 2653, "endOffset": 2904, "count": 0}], "isBlockCoverage": false}, {"functionName": "createPerformanceNodeEntry", "ranges": [{"startOffset": 2908, "endOffset": 3112, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "249", "url": "node:dgram", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28668, "count": 1}], "isBlockCoverage": false}, {"functionName": "lazyLoadCluster", "ranges": [{"startOffset": 2910, "endOffset": 3007, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket", "ranges": [{"startOffset": 3073, "endOffset": 4712, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSocket", "ranges": [{"startOffset": 4823, "endOffset": 4901, "count": 0}], "isBlockCoverage": false}, {"functionName": "startListening", "ranges": [{"startOffset": 4904, "endOffset": 5347, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 5349, "endOffset": 5914, "count": 0}], "isBlockCoverage": false}, {"functionName": "bufferSize", "ranges": [{"startOffset": 5916, "endOffset": 6208, "count": 0}], "isBlockCoverage": false}, {"functionName": "bindServerHandle", "ranges": [{"startOffset": 6276, "endOffset": 6772, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.bind", "ranges": [{"startOffset": 6798, "endOffset": 10253, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.connect", "ranges": [{"startOffset": 10283, "endOffset": 11079, "count": 0}], "isBlockCoverage": false}, {"functionName": "_connect", "ranges": [{"startOffset": 11083, "endOffset": 11429, "count": 0}], "isBlockCoverage": false}, {"functionName": "doConnect", "ranges": [{"startOffset": 11432, "endOffset": 12078, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.disconnect", "ranges": [{"startOffset": 12111, "endOffset": 12422, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.sendto", "ranges": [{"startOffset": 12527, "endOffset": 12968, "count": 0}], "isBlockCoverage": false}, {"functionName": "sliceBuffer", "ranges": [{"startOffset": 12972, "endOffset": 13732, "count": 0}], "isBlockCoverage": false}, {"functionName": "fixBufferList", "ranges": [{"startOffset": 13735, "endOffset": 14119, "count": 0}], "isBlockCoverage": false}, {"functionName": "enqueue", "ranges": [{"startOffset": 14122, "endOffset": 14549, "count": 0}], "isBlockCoverage": false}, {"functionName": "onListenSuccess", "ranges": [{"startOffset": 14552, "endOffset": 14690, "count": 0}], "isBlockCoverage": false}, {"functionName": "onListenError", "ranges": [{"startOffset": 14693, "endOffset": 14817, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearQueue", "ranges": [{"startOffset": 14820, "endOffset": 15017, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.send", "ranges": [{"startOffset": 15588, "endOffset": 18481, "count": 0}], "isBlockCoverage": false}, {"functionName": "doSend", "ranges": [{"startOffset": 18484, "endOffset": 19627, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterSend", "ranges": [{"startOffset": 19629, "endOffset": 19810, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.close", "ranges": [{"startOffset": 19837, "endOffset": 20405, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.<computed>", "ranges": [{"startOffset": 20447, "endOffset": 20580, "count": 0}], "isBlockCoverage": false}, {"functionName": "socketCloseNT", "ranges": [{"startOffset": 20584, "endOffset": 20638, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.address", "ranges": [{"startOffset": 20668, "endOffset": 20862, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.remoteAddress", "ranges": [{"startOffset": 20898, "endOffset": 21212, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setBroadcast", "ranges": [{"startOffset": 21248, "endOffset": 21400, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setTTL", "ranges": [{"startOffset": 21430, "endOffset": 21608, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setMulticastTTL", "ranges": [{"startOffset": 21647, "endOffset": 21843, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setMulticastLoopback", "ranges": [{"startOffset": 21887, "endOffset": 22091, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setMulticastInterface", "ranges": [{"startOffset": 22136, "endOffset": 22402, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.addMembership", "ranges": [{"startOffset": 22438, "endOffset": 22816, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.dropMembership", "ranges": [{"startOffset": 22854, "endOffset": 23235, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.addSourceSpecificMembership", "ranges": [{"startOffset": 23285, "endOffset": 23896, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.dropSourceSpecificMembership", "ranges": [{"startOffset": 23948, "endOffset": 24565, "count": 0}], "isBlockCoverage": false}, {"functionName": "healthCheck", "ranges": [{"startOffset": 24569, "endOffset": 24732, "count": 0}], "isBlockCoverage": false}, {"functionName": "stopReceiving", "ranges": [{"startOffset": 24735, "endOffset": 24899, "count": 0}], "isBlockCoverage": false}, {"functionName": "onMessage", "ranges": [{"startOffset": 24902, "endOffset": 25160, "count": 0}], "isBlockCoverage": false}, {"functionName": "onError", "ranges": [{"startOffset": 25163, "endOffset": 25278, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.ref", "ranges": [{"startOffset": 25304, "endOffset": 25411, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.unref", "ranges": [{"startOffset": 25440, "endOffset": 25549, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setRecvBufferSize", "ranges": [{"startOffset": 25590, "endOffset": 25647, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setSendBufferSize", "ranges": [{"startOffset": 25688, "endOffset": 25745, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.getRecvBufferSize", "ranges": [{"startOffset": 25786, "endOffset": 25843, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.getSendBufferSize", "ranges": [{"startOffset": 25884, "endOffset": 25941, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.getSendQueueSize", "ranges": [{"startOffset": 25980, "endOffset": 26049, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.getSendQueueCount", "ranges": [{"startOffset": 26089, "endOffset": 26159, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26278, "endOffset": 26332, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26405, "endOffset": 26461, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26614, "endOffset": 26671, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26747, "endOffset": 26806, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26962, "endOffset": 27019, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27095, "endOffset": 27154, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27306, "endOffset": 27359, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27431, "endOffset": 27486, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27638, "endOffset": 27695, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27771, "endOffset": 27830, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27937, "endOffset": 27972, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 28081, "endOffset": 28118, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 28403, "endOffset": 28439, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 28443, "endOffset": 28484, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "250", "url": "node:internal/dgram", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1851, "count": 1}], "isBlockCoverage": false}, {"functionName": "lookup4", "ranges": [{"startOffset": 478, "endOffset": 579, "count": 0}], "isBlockCoverage": false}, {"functionName": "lookup6", "ranges": [{"startOffset": 582, "endOffset": 677, "count": 0}], "isBlockCoverage": false}, {"functionName": "newHandle", "ranges": [{"startOffset": 679, "endOffset": 1323, "count": 0}], "isBlockCoverage": false}, {"functionName": "_createSocketHandle", "ranges": [{"startOffset": 1326, "endOffset": 1774, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "251", "url": "node:internal/socket_list", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2709, "count": 1}], "isBlockCoverage": false}, {"functionName": "SocketListSend", "ranges": [{"startOffset": 235, "endOffset": 376, "count": 0}], "isBlockCoverage": false}, {"functionName": "_request", "ranges": [{"startOffset": 380, "endOffset": 1035, "count": 0}], "isBlockCoverage": false}, {"functionName": "close", "ranges": [{"startOffset": 1039, "endOffset": 1190, "count": 0}], "isBlockCoverage": false}, {"functionName": "getConnections", "ranges": [{"startOffset": 1194, "endOffset": 1430, "count": 0}], "isBlockCoverage": false}, {"functionName": "SocketListReceive", "ranges": [{"startOffset": 1544, "endOffset": 2404, "count": 0}], "isBlockCoverage": false}, {"functionName": "add", "ranges": [{"startOffset": 2408, "endOffset": 2649, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "252", "url": "node:tty", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5045, "count": 1}], "isBlockCoverage": false}, {"functionName": "isatty", "ranges": [{"startOffset": 1526, "endOffset": 1632, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReadStream", "ranges": [{"startOffset": 1634, "endOffset": 2103, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReadStream.setRawMode", "ranges": [{"startOffset": 2252, "endOffset": 2473, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteStream", "ranges": [{"startOffset": 2476, "endOffset": 3442, "count": 2}, {"startOffset": 2545, "endOffset": 2572, "count": 0}, {"startOffset": 2609, "endOffset": 2638, "count": 0}, {"startOffset": 2720, "endOffset": 2765, "count": 0}], "isBlockCoverage": true}, {"functionName": "WriteStream._refreshSize", "ranges": [{"startOffset": 3733, "endOffset": 4167, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteStream.cursorTo", "ranges": [{"startOffset": 4223, "endOffset": 4363, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteStream.moveCursor", "ranges": [{"startOffset": 4400, "endOffset": 4546, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteStream.clearLine", "ranges": [{"startOffset": 4582, "endOffset": 4721, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteStream.clearScreenDown", "ranges": [{"startOffset": 4763, "endOffset": 4898, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteStream.getWindowSize", "ranges": [{"startOffset": 4938, "endOffset": 4988, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "253", "url": "node:internal/tty", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6478, "count": 1}], "isBlockCoverage": false}, {"functionName": "warnOnDeactivatedColors", "ranges": [{"startOffset": 2542, "endOffset": 2995, "count": 0}], "isBlockCoverage": false}, {"functionName": "getColorDepth", "ranges": [{"startOffset": 3153, "endOffset": 6149, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasColors", "ranges": [{"startOffset": 6151, "endOffset": 6424, "count": 0}], "isBlockCoverage": false}]}], "timestamp": 19866.356172}