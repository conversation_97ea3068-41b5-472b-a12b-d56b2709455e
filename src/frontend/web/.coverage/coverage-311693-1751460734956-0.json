{"result": [{"scriptId": "6", "url": "node:internal/per_context/primordials", "functions": [{"functionName": "SafeIterator", "ranges": [{"startOffset": 9149, "endOffset": 9220, "count": 30}], "isBlockCoverage": false}, {"functionName": "next", "ranges": [{"startOffset": 9225, "endOffset": 9274, "count": 321}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9279, "endOffset": 9324, "count": 25}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10889, "endOffset": 10956, "count": 18}], "isBlockCoverage": false}, {"functionName": "SafeMap", "ranges": [{"startOffset": 11614, "endOffset": 11642, "count": 125}], "isBlockCoverage": false}, {"functionName": "SafeSet", "ranges": [{"startOffset": 11943, "endOffset": 11971, "count": 113}], "isBlockCoverage": false}, {"functionName": "SafeFinalizationRegistry", "ranges": [{"startOffset": 12395, "endOffset": 12451, "count": 3}], "isBlockCoverage": false}, {"functionName": "SafeWeakRef", "ranges": [{"startOffset": 12604, "endOffset": 12642, "count": 15}], "isBlockCoverage": false}, {"functionName": "SafePromise", "ranges": [{"startOffset": 12790, "endOffset": 12832, "count": 60}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13337, "endOffset": 13611, "count": 10}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13482, "endOffset": 13606, "count": 10}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13512, "endOffset": 13561, "count": 10}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 14931, "endOffset": 15487, "count": 8}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 14966, "endOffset": 15486, "count": 8}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15357, "endOffset": 15466, "count": 10}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15829, "endOffset": 16282, "count": 11}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15864, "endOffset": 16281, "count": 11}], "isBlockCoverage": false}, {"functionName": "onFulfilled", "ranges": [{"startOffset": 15996, "endOffset": 16073, "count": 10}], "isBlockCoverage": false}]}, {"scriptId": "9", "url": "node:internal/bootstrap/realm", "functions": [{"functionName": "internalBinding", "ranges": [{"startOffset": 5981, "endOffset": 6243, "count": 64}], "isBlockCoverage": false}, {"functionName": "getOwn", "ranges": [{"startOffset": 6509, "endOffset": 6663, "count": 105}, {"startOffset": 6645, "endOffset": 6660, "count": 0}], "isBlockCoverage": true}, {"functionName": "canBeRequiredByUsers", "ranges": [{"startOffset": 9476, "endOffset": 9551, "count": 160}], "isBlockCoverage": true}, {"functionName": "canBeRequiredWithoutScheme", "ranges": [{"startOffset": 9562, "endOffset": 9656, "count": 413}], "isBlockCoverage": true}, {"functionName": "normalizeRequirableId", "ranges": [{"startOffset": 9667, "endOffset": 10012, "count": 291}, {"startOffset": 9743, "endOffset": 9905, "count": 0}, {"startOffset": 9961, "endOffset": 9985, "count": 23}, {"startOffset": 9985, "endOffset": 10011, "count": 268}], "isBlockCoverage": true}, {"functionName": "getCanBeRequiredByUsersWithoutSchemeList", "ranges": [{"startOffset": 10275, "endOffset": 10384, "count": 1}], "isBlockCoverage": true}, {"functionName": "compileForPublicLoader", "ranges": [{"startOffset": 10542, "endOffset": 11205, "count": 62}, {"startOffset": 10623, "endOffset": 10815, "count": 0}, {"startOffset": 10879, "endOffset": 11176, "count": 15}, {"startOffset": 11138, "endOffset": 11142, "count": 0}], "isBlockCoverage": true}, {"functionName": "getESMFacade", "ranges": [{"startOffset": 11209, "endOffset": 11843, "count": 3}, {"startOffset": 11247, "endOffset": 11266, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11580, "endOffset": 11683, "count": 3}], "isBlockCoverage": true}, {"functionName": "syncExports", "ranges": [{"startOffset": 12139, "endOffset": 12483, "count": 3}, {"startOffset": 12258, "endOffset": 12473, "count": 105}, {"startOffset": 12335, "endOffset": 12344, "count": 0}], "isBlockCoverage": true}, {"functionName": "compileForInternalLoader", "ranges": [{"startOffset": 12487, "endOffset": 13309, "count": 533}], "isBlockCoverage": false}, {"functionName": "requireBuiltin", "ranges": [{"startOffset": 13504, "endOffset": 13869, "count": 473}], "isBlockCoverage": false}]}, {"scriptId": "10", "url": "node:internal/errors", "functions": [{"functionName": "setInternalPrepareStackTrace", "ranges": [{"startOffset": 2771, "endOffset": 2862, "count": 1}], "isBlockCoverage": true}, {"functionName": "hideStackFrames", "ranges": [{"startOffset": 14657, "endOffset": 14954, "count": 4}], "isBlockCoverage": false}, {"functionName": "wrappedFn", "ranges": [{"startOffset": 14690, "endOffset": 14896, "count": 2504}], "isBlockCoverage": false}]}, {"scriptId": "11", "url": "node:internal/assert", "functions": [{"functionName": "assert", "ranges": [{"startOffset": 157, "endOffset": 307, "count": 216}, {"startOffset": 205, "endOffset": 305, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "12", "url": "node:internal/bootstrap/node", "functions": [{"functionName": "get", "ranges": [{"startOffset": 3830, "endOffset": 3880, "count": 6}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 3886, "endOffset": 3944, "count": 2}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4086, "endOffset": 4122, "count": 4}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 4128, "endOffset": 4547, "count": 3}, {"startOffset": 4187, "endOffset": 4469, "count": 1}, {"startOffset": 4252, "endOffset": 4266, "count": 0}, {"startOffset": 4267, "endOffset": 4315, "count": 0}, {"startOffset": 4317, "endOffset": 4352, "count": 0}, {"startOffset": 4469, "endOffset": 4518, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 8066, "endOffset": 8228, "count": 1}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 8330, "endOffset": 8526, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 12072, "endOffset": 12122, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 13230, "endOffset": 13266, "count": 174}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 14136, "endOffset": 14171, "count": 25}], "isBlockCoverage": true}]}, {"scriptId": "13", "url": "node:internal/timers", "functions": [{"functionName": "", "ranges": [{"startOffset": 4385, "endOffset": 4410, "count": 1}], "isBlockCoverage": true}, {"functionName": "initAsyncResource", "ranges": [{"startOffset": 5763, "endOffset": 6041, "count": 2}, {"startOffset": 5989, "endOffset": 6039, "count": 0}], "isBlockCoverage": true}, {"functionName": "Timeout", "ranges": [{"startOffset": 6147, "endOffset": 7193, "count": 2}, {"startOffset": 6295, "endOffset": 6647, "count": 0}, {"startOffset": 7004, "endOffset": 7011, "count": 1}, {"startOffset": 7012, "endOffset": 7018, "count": 1}], "isBlockCoverage": true}, {"functionName": "unref", "ranges": [{"startOffset": 7587, "endOffset": 7726, "count": 1}], "isBlockCoverage": true}, {"functionName": "TimersList", "ranges": [{"startOffset": 7935, "endOffset": 8241, "count": 2}], "isBlockCoverage": true}, {"functionName": "incRefCount", "ranges": [{"startOffset": 9618, "endOffset": 9784, "count": 2}], "isBlockCoverage": true}, {"functionName": "decRefCount", "ranges": [{"startOffset": 9786, "endOffset": 9953, "count": 2}], "isBlockCoverage": true}, {"functionName": "insert", "ranges": [{"startOffset": 11102, "endOffset": 11841, "count": 2}], "isBlockCoverage": true}, {"functionName": "setPosition", "ranges": [{"startOffset": 12905, "endOffset": 12976, "count": 2}], "isBlockCoverage": true}, {"functionName": "processTimers", "ranges": [{"startOffset": 15228, "endOffset": 15718, "count": 1}, {"startOffset": 15459, "endOffset": 15564, "count": 0}, {"startOffset": 15602, "endOffset": 15617, "count": 0}], "isBlockCoverage": true}, {"functionName": "listOnTimeout", "ranges": [{"startOffset": 15722, "endOffset": 18460, "count": 1}, {"startOffset": 15925, "endOffset": 17915, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "14", "url": "node:internal/async_hooks", "functions": [{"functionName": "newAsyncId", "ranges": [{"startOffset": 14549, "endOffset": 14619, "count": 6}], "isBlockCoverage": true}, {"functionName": "getDefaultTriggerAsyncId", "ranges": [{"startOffset": 14994, "endOffset": 15284, "count": 6}, {"startOffset": 15250, "endOffset": 15283, "count": 0}], "isBlockCoverage": true}, {"functionName": "hasHooks", "ranges": [{"startOffset": 16096, "endOffset": 16159, "count": 21}], "isBlockCoverage": true}, {"functionName": "enabledHooksExist", "ranges": [{"startOffset": 16161, "endOffset": 16220, "count": 3}], "isBlockCoverage": true}, {"functionName": "initHooksExist", "ranges": [{"startOffset": 16222, "endOffset": 16277, "count": 6}], "isBlockCoverage": true}, {"functionName": "destroyHooksExist", "ranges": [{"startOffset": 16338, "endOffset": 16399, "count": 5}], "isBlockCoverage": true}, {"functionName": "emitBeforeScript", "ranges": [{"startOffset": 16890, "endOffset": 17066, "count": 4}, {"startOffset": 17038, "endOffset": 17064, "count": 0}], "isBlockCoverage": true}, {"functionName": "emitAfterScript", "ranges": [{"startOffset": 17069, "endOffset": 17189, "count": 3}, {"startOffset": 17133, "endOffset": 17158, "count": 0}], "isBlockCoverage": true}, {"functionName": "pushAsyncContext", "ranges": [{"startOffset": 17534, "endOffset": 18104, "count": 4}, {"startOffset": 17754, "endOffset": 17804, "count": 0}], "isBlockCoverage": true}, {"functionName": "popAsyncContext", "ranges": [{"startOffset": 18169, "endOffset": 18780, "count": 3}, {"startOffset": 18285, "endOffset": 18298, "count": 0}, {"startOffset": 18377, "endOffset": 18483, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "15", "url": "node:internal/validators", "functions": [{"functionName": "isInt32", "ranges": [{"startOffset": 1087, "endOffset": 1146, "count": 103}], "isBlockCoverage": true}, {"functionName": "parseFileMode", "ranges": [{"startOffset": 1881, "endOffset": 2192, "count": 15}, {"startOffset": 1974, "endOffset": 2142, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2438, "endOffset": 2819, "count": 26}, {"startOffset": 2558, "endOffset": 2612, "count": 0}, {"startOffset": 2652, "endOffset": 2706, "count": 0}, {"startOffset": 2749, "endOffset": 2815, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3063, "endOffset": 3526, "count": 51}, {"startOffset": 3235, "endOffset": 3303, "count": 0}, {"startOffset": 3337, "endOffset": 3405, "count": 0}, {"startOffset": 3442, "endOffset": 3522, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3757, "endOffset": 4189, "count": 16}, {"startOffset": 3827, "endOffset": 3891, "count": 0}, {"startOffset": 3923, "endOffset": 3987, "count": 0}, {"startOffset": 4011, "endOffset": 4014, "count": 0}, {"startOffset": 4111, "endOffset": 4187, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4381, "endOffset": 4493, "count": 3071}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4735, "endOffset": 5181, "count": 15}, {"startOffset": 4813, "endOffset": 4867, "count": 0}, {"startOffset": 4957, "endOffset": 4971, "count": 0}, {"startOffset": 4997, "endOffset": 5179, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5369, "endOffset": 5702, "count": 1}, {"startOffset": 5440, "endOffset": 5700, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5513, "endOffset": 5574, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5898, "endOffset": 6012, "count": 5}, {"startOffset": 5955, "endOffset": 6010, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6594, "endOffset": 7613, "count": 36}, {"startOffset": 6685, "endOffset": 6925, "count": 3}, {"startOffset": 6736, "endOffset": 6808, "count": 0}, {"startOffset": 6847, "endOffset": 6919, "count": 0}, {"startOffset": 6925, "endOffset": 7609, "count": 33}, {"startOffset": 7038, "endOffset": 7055, "count": 32}, {"startOffset": 7057, "endOffset": 7129, "count": 0}, {"startOffset": 7227, "endOffset": 7249, "count": 0}, {"startOffset": 7251, "endOffset": 7323, "count": 0}, {"startOffset": 7479, "endOffset": 7529, "count": 0}, {"startOffset": 7531, "endOffset": 7603, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8567, "endOffset": 8845, "count": 1}, {"startOffset": 8629, "endOffset": 8692, "count": 0}, {"startOffset": 8725, "endOffset": 8843, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12692, "endOffset": 12909, "count": 1}, {"startOffset": 12739, "endOffset": 12835, "count": 0}, {"startOffset": 12837, "endOffset": 12907, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13109, "endOffset": 13225, "count": 49}], "isBlockCoverage": false}]}, {"scriptId": "16", "url": "node:internal/util", "functions": [{"functionName": "getDeprecationWarningEmitter", "ranges": [{"startOffset": 2398, "endOffset": 3047, "count": 16}], "isBlockCoverage": false}, {"functionName": "shouldEmitW<PERSON>ning", "ranges": [{"startOffset": 2497, "endOffset": 2507, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2544, "endOffset": 3044, "count": 0}], "isBlockCoverage": false}, {"functionName": "deprecate", "ranges": [{"startOffset": 3796, "endOffset": 4850, "count": 16}], "isBlockCoverage": false}, {"functionName": "deprecated", "ranges": [{"startOffset": 4160, "endOffset": 4462, "count": 0}], "isBlockCoverage": false}, {"functionName": "normalizeEncoding", "ranges": [{"startOffset": 5373, "endOffset": 5504, "count": 116}, {"startOffset": 5443, "endOffset": 5461, "count": 1}, {"startOffset": 5477, "endOffset": 5503, "count": 0}], "isBlockCoverage": true}, {"functionName": "promisify", "ranges": [{"startOffset": 11280, "endOffset": 13141, "count": 3}, {"startOffset": 11400, "endOffset": 11456, "count": 1}, {"startOffset": 11543, "endOffset": 11820, "count": 0}, {"startOffset": 12927, "endOffset": 13089, "count": 10}], "isBlockCoverage": true}, {"functionName": "fn", "ranges": [{"startOffset": 12020, "endOffset": 12539, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12066, "endOffset": 12533, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12120, "endOffset": 12483, "count": 2}, {"startOffset": 12159, "endOffset": 12200, "count": 0}, {"startOffset": 12241, "endOffset": 12261, "count": 0}, {"startOffset": 12263, "endOffset": 12428, "count": 0}], "isBlockCoverage": true}, {"functionName": "spliceOne", "ranges": [{"startOffset": 13699, "endOffset": 13826, "count": 1}, {"startOffset": 13780, "endOffset": 13810, "count": 0}], "isBlockCoverage": true}, {"functionName": "once", "ranges": [{"startOffset": 14946, "endOffset": 15285, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15070, "endOffset": 15282, "count": 1}, {"startOffset": 15106, "endOffset": 15125, "count": 0}, {"startOffset": 15238, "endOffset": 15246, "count": 0}], "isBlockCoverage": true}, {"functionName": "exposeInterface", "ranges": [{"startOffset": 16032, "endOffset": 16252, "count": 1}], "isBlockCoverage": false}, {"functionName": "defineLazyProperties", "ranges": [{"startOffset": 16751, "endOffset": 17684, "count": 1}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 16970, "endOffset": 17113, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 17216, "endOffset": 17412, "count": 0}], "isBlockCoverage": false}, {"functionName": "defineReplaceableLazyAttribute", "ranges": [{"startOffset": 17686, "endOffset": 18586, "count": 1}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 17898, "endOffset": 18134, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 18239, "endOffset": 18310, "count": 0}], "isBlockCoverage": false}, {"functionName": "exposeLazyInterfaces", "ranges": [{"startOffset": 18588, "endOffset": 18688, "count": 1}], "isBlockCoverage": false}, {"functionName": "setOwnProperty", "ranges": [{"startOffset": 19883, "endOffset": 20073, "count": 196}], "isBlockCoverage": true}, {"functionName": "getLazy", "ranges": [{"startOffset": 22094, "endOffset": 22306, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 22175, "endOffset": 22303, "count": 521}, {"startOffset": 22219, "endOffset": 22281, "count": 4}], "isBlockCoverage": true}, {"functionName": "setupCoverageHooks", "ranges": [{"startOffset": 22431, "endOffset": 23108, "count": 1}, {"startOffset": 22901, "endOffset": 23078, "count": 0}], "isBlockCoverage": true}, {"functionName": "countBinary<PERSON>nes", "ranges": [{"startOffset": 23195, "endOffset": 23448, "count": 30}], "isBlockCoverage": true}, {"functionName": "getCIDR", "ranges": [{"startOffset": 23450, "endOffset": 24426, "count": 7}, {"startOffset": 23640, "endOffset": 23700, "count": 4}, {"startOffset": 23745, "endOffset": 24392, "count": 139}, {"startOffset": 23777, "endOffset": 23857, "count": 108}, {"startOffset": 23813, "endOffset": 23840, "count": 104}, {"startOffset": 23840, "endOffset": 23857, "count": 4}, {"startOffset": 23857, "endOffset": 23960, "count": 35}, {"startOffset": 23960, "endOffset": 24388, "count": 32}, {"startOffset": 23982, "endOffset": 24052, "count": 2}, {"startOffset": 24010, "endOffset": 24044, "count": 0}, {"startOffset": 24052, "endOffset": 24382, "count": 30}, {"startOffset": 24232, "endOffset": 24374, "count": 2}, {"startOffset": 24299, "endOffset": 24337, "count": 0}], "isBlockCoverage": true}, {"functionName": "guessHandleType", "ranges": [{"startOffset": 24498, "endOffset": 24595, "count": 3}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 24597, "endOffset": 25141, "count": 9}], "isBlockCoverage": false}, {"functionName": "WeakReference", "ranges": [{"startOffset": 24672, "endOffset": 24739, "count": 9}], "isBlockCoverage": true}, {"functionName": "incRef", "ranges": [{"startOffset": 24743, "endOffset": 24961, "count": 0}], "isBlockCoverage": false}, {"functionName": "decRef", "ranges": [{"startOffset": 24965, "endOffset": 25093, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 25097, "endOffset": 25139, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "17", "url": "node:internal/options", "functions": [{"functionName": "getCLIOptionsFromBinding", "ranges": [{"startOffset": 495, "endOffset": 623, "count": 352}, {"startOffset": 553, "endOffset": 599, "count": 1}], "isBlockCoverage": true}, {"functionName": "getCLIOptionsInfoFromBinding", "ranges": [{"startOffset": 625, "endOffset": 743, "count": 1}], "isBlockCoverage": true}, {"functionName": "getEmbedderOptions", "ranges": [{"startOffset": 745, "endOffset": 889, "count": 5}, {"startOffset": 801, "endOffset": 861, "count": 1}], "isBlockCoverage": true}, {"functionName": "refreshOptions", "ranges": [{"startOffset": 891, "endOffset": 947, "count": 1}], "isBlockCoverage": true}, {"functionName": "getOptionValue", "ranges": [{"startOffset": 949, "endOffset": 1072, "count": 352}], "isBlockCoverage": true}]}, {"scriptId": "18", "url": "node:internal/util/types", "functions": [{"functionName": "isUint8Array", "ranges": [{"startOffset": 236, "endOffset": 342, "count": 268}], "isBlockCoverage": true}, {"functionName": "isBigInt64Array", "ranges": [{"startOffset": 1232, "endOffset": 1344, "count": 5}], "isBlockCoverage": true}]}, {"scriptId": "19", "url": "node:internal/linkedlist", "functions": [{"functionName": "peek", "ranges": [{"startOffset": 133, "endOffset": 225, "count": 1}, {"startOffset": 198, "endOffset": 224, "count": 0}], "isBlockCoverage": true}, {"functionName": "remove", "ranges": [{"startOffset": 260, "endOffset": 487, "count": 4}], "isBlockCoverage": true}, {"functionName": "append", "ranges": [{"startOffset": 543, "endOffset": 995, "count": 2}, {"startOffset": 594, "endOffset": 611, "count": 0}], "isBlockCoverage": true}, {"functionName": "isEmpty", "ranges": [{"startOffset": 997, "endOffset": 1057, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "20", "url": "node:internal/priority_queue", "functions": [{"functionName": "insert", "ranges": [{"startOffset": 692, "endOffset": 875, "count": 2}, {"startOffset": 826, "endOffset": 843, "count": 0}], "isBlockCoverage": true}, {"functionName": "peek", "ranges": [{"startOffset": 879, "endOffset": 917, "count": 2}], "isBlockCoverage": true}, {"functionName": "percolateUp", "ranges": [{"startOffset": 1646, "endOffset": 2134, "count": 2}, {"startOffset": 1821, "endOffset": 2043, "count": 0}], "isBlockCoverage": true}, {"functionName": "removeAt", "ranges": [{"startOffset": 2138, "endOffset": 2468, "count": 2}, {"startOffset": 2296, "endOffset": 2310, "count": 0}, {"startOffset": 2312, "endOffset": 2464, "count": 0}], "isBlockCoverage": true}, {"functionName": "shift", "ranges": [{"startOffset": 2472, "endOffset": 2626, "count": 1}, {"startOffset": 2573, "endOffset": 2580, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "21", "url": "node:internal/util/inspect", "functions": [{"functionName": "isUndetectableObject", "ranges": [{"startOffset": 3751, "endOffset": 3801, "count": 1}, {"startOffset": 3783, "endOffset": 3801, "count": 0}], "isBlockCoverage": true}, {"functionName": "inspect", "ranges": [{"startOffset": 9152, "endOffset": 11178, "count": 1}, {"startOffset": 10004, "endOffset": 10200, "count": 0}, {"startOffset": 10270, "endOffset": 10306, "count": 0}, {"startOffset": 10707, "endOffset": 10737, "count": 0}, {"startOffset": 10782, "endOffset": 10937, "count": 0}, {"startOffset": 10974, "endOffset": 11005, "count": 0}, {"startOffset": 11041, "endOffset": 11071, "count": 0}, {"startOffset": 11108, "endOffset": 11139, "count": 0}], "isBlockCoverage": true}, {"functionName": "stylizeNoColor", "ranges": [{"startOffset": 17324, "endOffset": 17370, "count": 1}], "isBlockCoverage": true}, {"functionName": "formatValue", "ranges": [{"startOffset": 24023, "endOffset": 26780, "count": 1}, {"startOffset": 24295, "endOffset": 26779, "count": 0}], "isBlockCoverage": true}, {"functionName": "formatNumber", "ranges": [{"startOffset": 53572, "endOffset": 54360, "count": 1}, {"startOffset": 53763, "endOffset": 53803, "count": 0}, {"startOffset": 53845, "endOffset": 54359, "count": 0}], "isBlockCoverage": true}, {"functionName": "formatPrimitive", "ranges": [{"startOffset": 54580, "endOffset": 55985, "count": 1}, {"startOffset": 54656, "endOffset": 55571, "count": 0}, {"startOffset": 55662, "endOffset": 55984, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 55369, "endOffset": 55408, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatWithOptions", "ranges": [{"startOffset": 73647, "endOffset": 73839, "count": 32}], "isBlockCoverage": true}, {"functionName": "formatWithOptionsInternal", "ranges": [{"startOffset": 74209, "endOffset": 78233, "count": 32}, {"startOffset": 74373, "endOffset": 78036, "count": 18}, {"startOffset": 74402, "endOffset": 74429, "count": 16}, {"startOffset": 74429, "endOffset": 74516, "count": 2}, {"startOffset": 74516, "endOffset": 77875, "count": 11}, {"startOffset": 74572, "endOffset": 77869, "count": 2}, {"startOffset": 74831, "endOffset": 74920, "count": 0}, {"startOffset": 74959, "endOffset": 75048, "count": 0}, {"startOffset": 75086, "endOffset": 75130, "count": 0}, {"startOffset": 75131, "endOffset": 75187, "count": 0}, {"startOffset": 75249, "endOffset": 75465, "count": 0}, {"startOffset": 75513, "endOffset": 75599, "count": 0}, {"startOffset": 75612, "endOffset": 76035, "count": 0}, {"startOffset": 76048, "endOffset": 76144, "count": 0}, {"startOffset": 76157, "endOffset": 76385, "count": 0}, {"startOffset": 76398, "endOffset": 76868, "count": 0}, {"startOffset": 76881, "endOffset": 77211, "count": 0}, {"startOffset": 77224, "endOffset": 77310, "count": 0}, {"startOffset": 77323, "endOffset": 77455, "count": 0}, {"startOffset": 77468, "endOffset": 77552, "count": 0}, {"startOffset": 77738, "endOffset": 77861, "count": 0}, {"startOffset": 77875, "endOffset": 78032, "count": 2}, {"startOffset": 77964, "endOffset": 78026, "count": 0}, {"startOffset": 78036, "endOffset": 78064, "count": 16}, {"startOffset": 78064, "endOffset": 78217, "count": 1}, {"startOffset": 78180, "endOffset": 78187, "count": 0}, {"startOffset": 78217, "endOffset": 78232, "count": 16}], "isBlockCoverage": true}]}, {"scriptId": "22", "url": "node:internal/util/debuglog", "functions": [{"functionName": "initializeDebugEnv", "ranges": [{"startOffset": 503, "endOffset": 976, "count": 1}, {"startOffset": 595, "endOffset": 932, "count": 0}], "isBlockCoverage": true}, {"functionName": "testEnabled", "ranges": [{"startOffset": 870, "endOffset": 927, "count": 0}], "isBlockCoverage": false}, {"functionName": "testEnabled", "ranges": [{"startOffset": 958, "endOffset": 969, "count": 9}], "isBlockCoverage": true}, {"functionName": "noop", "ranges": [{"startOffset": 1404, "endOffset": 1412, "count": 648}], "isBlockCoverage": true}, {"functionName": "debuglogImpl", "ranges": [{"startOffset": 1533, "endOffset": 2091, "count": 9}, {"startOffset": 1608, "endOffset": 2063, "count": 5}, {"startOffset": 1627, "endOffset": 2016, "count": 0}], "isBlockCoverage": true}, {"functionName": "debug", "ranges": [{"startOffset": 1716, "endOffset": 2009, "count": 0}], "isBlockCoverage": false}, {"functionName": "debuglog", "ranges": [{"startOffset": 2311, "endOffset": 3337, "count": 8}], "isBlockCoverage": false}, {"functionName": "init", "ranges": [{"startOffset": 2342, "endOffset": 2438, "count": 9}], "isBlockCoverage": true}, {"functionName": "debug", "ranges": [{"startOffset": 2453, "endOffset": 2847, "count": 9}, {"startOffset": 2700, "endOffset": 2730, "count": 4}, {"startOffset": 2737, "endOffset": 2776, "count": 4}, {"startOffset": 2783, "endOffset": 2837, "count": 1}], "isBlockCoverage": true}, {"functionName": "test", "ranges": [{"startOffset": 2877, "endOffset": 2946, "count": 0}], "isBlockCoverage": false}, {"functionName": "logger", "ranges": [{"startOffset": 2965, "endOffset": 3160, "count": 9}, {"startOffset": 3013, "endOffset": 3043, "count": 4}, {"startOffset": 3050, "endOffset": 3089, "count": 4}, {"startOffset": 3096, "endOffset": 3150, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3231, "endOffset": 3265, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "23", "url": "node:events", "functions": [{"functionName": "EventEmitter", "ranges": [{"startOffset": 6575, "endOffset": 6644, "count": 16}], "isBlockCoverage": false}, {"functionName": "checkListener", "ranges": [{"startOffset": 8095, "endOffset": 8173, "count": 37}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8281, "endOffset": 8329, "count": 2}], "isBlockCoverage": false}, {"functionName": "hasEventListener", "ranges": [{"startOffset": 8446, "endOffset": 8625, "count": 14}, {"startOffset": 8516, "endOffset": 8550, "count": 0}], "isBlockCoverage": true}, {"functionName": "EventEmitter.init", "ranges": [{"startOffset": 10152, "endOffset": 10820, "count": 16}], "isBlockCoverage": false}, {"functionName": "_getMaxListeners", "ranges": [{"startOffset": 12311, "endOffset": 12460, "count": 2}], "isBlockCoverage": false}, {"functionName": "emit", "ranges": [{"startOffset": 13617, "endOffset": 15920, "count": 108}], "isBlockCoverage": false}, {"functionName": "_addListener", "ranges": [{"startOffset": 15923, "endOffset": 17839, "count": 22}], "isBlockCoverage": false}, {"functionName": "addListener", "ranges": [{"startOffset": 18017, "endOffset": 18109, "count": 22}], "isBlockCoverage": false}, {"functionName": "onceWrapper", "ranges": [{"startOffset": 18502, "endOffset": 18766, "count": 1}, {"startOffset": 18665, "endOffset": 18704, "count": 0}], "isBlockCoverage": true}, {"functionName": "_onceWrap", "ranges": [{"startOffset": 18768, "endOffset": 19009, "count": 1}], "isBlockCoverage": true}, {"functionName": "once", "ranges": [{"startOffset": 19200, "endOffset": 19326, "count": 1}], "isBlockCoverage": true}, {"functionName": "removeListener", "ranges": [{"startOffset": 19936, "endOffset": 21299, "count": 14}, {"startOffset": 20059, "endOffset": 20071, "count": 0}, {"startOffset": 20170, "endOffset": 20199, "count": 2}, {"startOffset": 20201, "endOffset": 20572, "count": 12}, {"startOffset": 20266, "endOffset": 20313, "count": 0}, {"startOffset": 20348, "endOffset": 20405, "count": 0}, {"startOffset": 20493, "endOffset": 20554, "count": 11}, {"startOffset": 20572, "endOffset": 21273, "count": 2}, {"startOffset": 20610, "endOffset": 21273, "count": 1}, {"startOffset": 20728, "endOffset": 20760, "count": 0}, {"startOffset": 20868, "endOffset": 20880, "count": 0}, {"startOffset": 20920, "endOffset": 20933, "count": 0}], "isBlockCoverage": true}, {"functionName": "listenerCount", "ranges": [{"startOffset": 24551, "endOffset": 25256, "count": 2}], "isBlockCoverage": false}, {"functionName": "arrayClone", "ranges": [{"startOffset": 25508, "endOffset": 25981, "count": 65}], "isBlockCoverage": false}]}, {"scriptId": "25", "url": "node:buffer", "functions": [{"functionName": "alignPool", "ranges": [{"startOffset": 4097, "endOffset": 4219, "count": 26}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8395, "endOffset": 8423, "count": 1}], "isBlockCoverage": true}, {"functionName": "from", "ranges": [{"startOffset": 8681, "endOffset": 9646, "count": 26}], "isBlockCoverage": false}, {"functionName": "alloc", "ranges": [{"startOffset": 11465, "endOffset": 11737, "count": 5}, {"startOffset": 11576, "endOffset": 11589, "count": 0}, {"startOffset": 11590, "endOffset": 11601, "count": 0}, {"startOffset": 11603, "endOffset": 11704, "count": 0}], "isBlockCoverage": true}, {"functionName": "allocUnsafeSlow", "ranges": [{"startOffset": 12259, "endOffset": 12375, "count": 10}], "isBlockCoverage": true}, {"functionName": "fromStringFast", "ranges": [{"startOffset": 13030, "endOffset": 13682, "count": 26}], "isBlockCoverage": false}, {"functionName": "fromString", "ranges": [{"startOffset": 13931, "endOffset": 14291, "count": 26}], "isBlockCoverage": false}, {"functionName": "isEncoding", "ranges": [{"startOffset": 16155, "endOffset": 16306, "count": 111}], "isBlockCoverage": true}, {"functionName": "write", "ranges": [{"startOffset": 17925, "endOffset": 17989, "count": 26}], "isBlockCoverage": false}, {"functionName": "slice", "ranges": [{"startOffset": 18002, "endOffset": 18048, "count": 5}], "isBlockCoverage": true}, {"functionName": "getEncodingOps", "ranges": [{"startOffset": 21317, "endOffset": 23141, "count": 5}, {"startOffset": 21467, "endOffset": 21703, "count": 0}, {"startOffset": 21708, "endOffset": 22127, "count": 0}, {"startOffset": 22132, "endOffset": 22286, "count": 0}, {"startOffset": 22291, "endOffset": 22447, "count": 0}, {"startOffset": 22452, "endOffset": 22833, "count": 0}, {"startOffset": 22838, "endOffset": 22970, "count": 0}, {"startOffset": 22975, "endOffset": 23135, "count": 0}], "isBlockCoverage": true}, {"functionName": "toString", "ranges": [{"startOffset": 24765, "endOffset": 25380, "count": 5}, {"startOffset": 24837, "endOffset": 24885, "count": 0}, {"startOffset": 24937, "endOffset": 24947, "count": 0}, {"startOffset": 24977, "endOffset": 24987, "count": 0}, {"startOffset": 25055, "endOffset": 25067, "count": 0}, {"startOffset": 25083, "endOffset": 25121, "count": 0}, {"startOffset": 25147, "endOffset": 25157, "count": 0}, {"startOffset": 25193, "endOffset": 25227, "count": 0}, {"startOffset": 25298, "endOffset": 25339, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "26", "url": "node:internal/buffer", "functions": [{"functionName": "FastBuffer", "ranges": [{"startOffset": 26365, "endOffset": 26465, "count": 41}], "isBlockCoverage": false}, {"functionName": "createUnsafeBuffer", "ranges": [{"startOffset": 30291, "endOffset": 30427, "count": 10}], "isBlockCoverage": false}, {"functionName": "reconnectZeroFillToggle", "ranges": [{"startOffset": 30682, "endOffset": 30754, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "28", "url": "node:internal/process/per_thread", "functions": [{"functionName": "exit", "ranges": [{"startOffset": 4747, "endOffset": 5860, "count": 1}], "isBlockCoverage": true}, {"functionName": "buildAllowedFlags", "ranges": [{"startOffset": 7998, "endOffset": 12145, "count": 1}, {"startOffset": 8339, "endOffset": 8630, "count": 182}, {"startOffset": 8391, "endOffset": 8626, "count": 144}, {"startOffset": 8488, "endOffset": 8620, "count": 74}, {"startOffset": 9090, "endOffset": 9512, "count": 24}, {"startOffset": 9144, "endOffset": 9508, "count": 12}, {"startOffset": 9233, "endOffset": 9302, "count": 3}, {"startOffset": 9367, "endOffset": 9436, "count": 0}], "isBlockCoverage": true}, {"functionName": "isAccepted", "ranges": [{"startOffset": 8634, "endOffset": 9038, "count": 31}, {"startOffset": 8696, "endOffset": 8710, "count": 30}, {"startOffset": 8712, "endOffset": 8724, "count": 2}, {"startOffset": 8724, "endOffset": 8801, "count": 29}, {"startOffset": 8801, "endOffset": 8970, "count": 2}, {"startOffset": 8851, "endOffset": 8898, "count": 1}, {"startOffset": 8970, "endOffset": 9037, "count": 27}], "isBlockCoverage": true}, {"functionName": "trimLeadingDashes", "ranges": [{"startOffset": 9546, "endOffset": 9608, "count": 230}], "isBlockCoverage": true}, {"functionName": "NodeEnvironmentFlagsSet", "ranges": [{"startOffset": 9918, "endOffset": 9994, "count": 1}], "isBlockCoverage": true}, {"functionName": "add", "ranges": [{"startOffset": 10000, "endOffset": 10069, "count": 0}], "isBlockCoverage": false}, {"functionName": "delete", "ranges": [{"startOffset": 10075, "endOffset": 10148, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 10154, "endOffset": 10206, "count": 0}], "isBlockCoverage": false}, {"functionName": "has", "ranges": [{"startOffset": 10212, "endOffset": 11027, "count": 0}], "isBlockCoverage": false}, {"functionName": "entries", "ranges": [{"startOffset": 11033, "endOffset": 11198, "count": 0}], "isBlockCoverage": false}, {"functionName": "for<PERSON>ach", "ranges": [{"startOffset": 11204, "endOffset": 11381, "count": 0}], "isBlockCoverage": false}, {"functionName": "get size", "ranges": [{"startOffset": 11387, "endOffset": 11448, "count": 0}], "isBlockCoverage": false}, {"functionName": "values", "ranges": [{"startOffset": 11454, "endOffset": 11617, "count": 0}], "isBlockCoverage": false}, {"functionName": "toggleTraceCategoryState", "ranges": [{"startOffset": 12335, "endOffset": 12665, "count": 1}, {"startOffset": 12415, "endOffset": 12590, "count": 0}, {"startOffset": 12622, "endOffset": 12663, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "29", "url": "node:internal/fs/utils", "functions": [{"functionName": "lazyLoadFs", "ranges": [{"startOffset": 3476, "endOffset": 3555, "count": 1}], "isBlockCoverage": true}, {"functionName": "assertEncoding", "ranges": [{"startOffset": 3557, "endOffset": 3757, "count": 242}, {"startOffset": 3608, "endOffset": 3639, "count": 111}, {"startOffset": 3641, "endOffset": 3755, "count": 0}], "isBlockCoverage": true}, {"functionName": "Dirent", "ranges": [{"startOffset": 3776, "endOffset": 3907, "count": 17}], "isBlockCoverage": true}, {"functionName": "isFile", "ranges": [{"startOffset": 3976, "endOffset": 4033, "count": 17}], "isBlockCoverage": true}, {"functionName": "copyObject", "ranges": [{"startOffset": 4704, "endOffset": 4832, "count": 1}, {"startOffset": 4787, "endOffset": 4813, "count": 2}], "isBlockCoverage": true}, {"functionName": "getDirents", "ranges": [{"startOffset": 5491, "endOffset": 6606, "count": 1}, {"startOffset": 5715, "endOffset": 6388, "count": 17}, {"startOffset": 5784, "endOffset": 6310, "count": 0}, {"startOffset": 6453, "endOffset": 6604, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6049, "endOffset": 6300, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOptions", "ranges": [{"startOffset": 7391, "endOffset": 8017, "count": 247}, {"startOffset": 7475, "endOffset": 7507, "count": 242}, {"startOffset": 7509, "endOffset": 7541, "count": 5}, {"startOffset": 7541, "endOffset": 7578, "count": 242}, {"startOffset": 7578, "endOffset": 7696, "count": 110}, {"startOffset": 7696, "endOffset": 7818, "count": 132}, {"startOffset": 7735, "endOffset": 7818, "count": 0}, {"startOffset": 7818, "endOffset": 7932, "count": 242}, {"startOffset": 7932, "endOffset": 7996, "count": 0}, {"startOffset": 7996, "endOffset": 8016, "count": 242}], "isBlockCoverage": true}, {"functionName": "handleErrorFromBinding", "ranges": [{"startOffset": 8075, "endOffset": 8608, "count": 25}, {"startOffset": 8145, "endOffset": 8284, "count": 0}, {"startOffset": 8316, "endOffset": 8606, "count": 0}], "isBlockCoverage": true}, {"functionName": "StatsBase", "ranges": [{"startOffset": 9349, "endOffset": 9656, "count": 5}], "isBlockCoverage": true}, {"functionName": "StatsBase.isDirectory", "ranges": [{"startOffset": 9692, "endOffset": 9749, "count": 2}], "isBlockCoverage": true}, {"functionName": "StatsBase.isFile", "ranges": [{"startOffset": 9781, "endOffset": 9838, "count": 2}], "isBlockCoverage": true}, {"functionName": "msFromTimeSpec", "ranges": [{"startOffset": 10437, "endOffset": 10519, "count": 20}], "isBlockCoverage": true}, {"functionName": "dateFromMs", "ranges": [{"startOffset": 11006, "endOffset": 11117, "count": 20}], "isBlockCoverage": true}, {"functionName": "Stats", "ranges": [{"startOffset": 12289, "endOffset": 12831, "count": 5}], "isBlockCoverage": true}, {"functionName": "Stats._checkModeProperty", "ranges": [{"startOffset": 13172, "endOffset": 13402, "count": 4}, {"startOffset": 13209, "endOffset": 13287, "count": 0}, {"startOffset": 13289, "endOffset": 13356, "count": 0}], "isBlockCoverage": true}, {"functionName": "getStatsFromBinding", "ranges": [{"startOffset": 13520, "endOffset": 14599, "count": 5}, {"startOffset": 13600, "endOffset": 14126, "count": 0}], "isBlockCoverage": true}, {"functionName": "stringToFlags", "ranges": [{"startOffset": 15001, "endOffset": 16257, "count": 116}, {"startOffset": 15082, "endOffset": 15137, "count": 0}, {"startOffset": 15160, "endOffset": 15186, "count": 0}, {"startOffset": 15211, "endOffset": 15238, "count": 115}, {"startOffset": 15243, "endOffset": 15254, "count": 0}, {"startOffset": 15276, "endOffset": 15313, "count": 0}, {"startOffset": 15318, "endOffset": 15344, "count": 0}, {"startOffset": 15349, "endOffset": 15361, "count": 0}, {"startOffset": 15383, "endOffset": 15419, "count": 0}, {"startOffset": 15425, "endOffset": 15472, "count": 0}, {"startOffset": 15477, "endOffset": 15488, "count": 0}, {"startOffset": 15510, "endOffset": 15567, "count": 0}, {"startOffset": 15573, "endOffset": 15619, "count": 0}, {"startOffset": 15624, "endOffset": 15635, "count": 0}, {"startOffset": 15657, "endOffset": 15712, "count": 0}, {"startOffset": 15718, "endOffset": 15766, "count": 1}, {"startOffset": 15771, "endOffset": 15782, "count": 0}, {"startOffset": 15804, "endOffset": 15862, "count": 0}, {"startOffset": 15867, "endOffset": 15878, "count": 0}, {"startOffset": 15900, "endOffset": 15958, "count": 0}, {"startOffset": 15964, "endOffset": 16011, "count": 0}, {"startOffset": 16016, "endOffset": 16027, "count": 0}, {"startOffset": 16049, "endOffset": 16105, "count": 0}, {"startOffset": 16110, "endOffset": 16121, "count": 0}, {"startOffset": 16143, "endOffset": 16199, "count": 0}, {"startOffset": 16203, "endOffset": 16256, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17756, "endOffset": 18241, "count": 25}, {"startOffset": 17819, "endOffset": 17919, "count": 0}, {"startOffset": 17959, "endOffset": 18068, "count": 0}, {"startOffset": 18090, "endOffset": 18178, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18284, "endOffset": 18996, "count": 268}, {"startOffset": 18346, "endOffset": 18368, "count": 0}, {"startOffset": 18370, "endOffset": 18477, "count": 0}, {"startOffset": 18668, "endOffset": 18688, "count": 0}, {"startOffset": 18758, "endOffset": 18826, "count": 0}, {"startOffset": 18845, "endOffset": 18995, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19041, "endOffset": 19176, "count": 137}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19219, "endOffset": 19340, "count": 25}, {"startOffset": 19270, "endOffset": 19289, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21781, "endOffset": 22505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21957, "endOffset": 22501, "count": 1}, {"startOffset": 21988, "endOffset": 22120, "count": 0}, {"startOffset": 22140, "endOffset": 22163, "count": 0}, {"startOffset": 22165, "endOffset": 22196, "count": 0}, {"startOffset": 22226, "endOffset": 22247, "count": 0}, {"startOffset": 22249, "endOffset": 22467, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23815, "endOffset": 24284, "count": 1}, {"startOffset": 23899, "endOffset": 23915, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "30", "url": "node:internal/url", "functions": [{"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3494, "endOffset": 4283, "count": 142}], "isBlockCoverage": false}, {"functionName": "get hasPort", "ranges": [{"startOffset": 4062, "endOffset": 4127, "count": 0}], "isBlockCoverage": false}, {"functionName": "get hasSearch", "ranges": [{"startOffset": 4131, "endOffset": 4206, "count": 164}], "isBlockCoverage": true}, {"functionName": "get hasHash", "ranges": [{"startOffset": 4210, "endOffset": 4281, "count": 164}], "isBlockCoverage": true}, {"functionName": "isURL", "ranges": [{"startOffset": 21737, "endOffset": 21862, "count": 317}, {"startOffset": 21788, "endOffset": 21804, "count": 54}, {"startOffset": 21805, "endOffset": 21831, "count": 54}, {"startOffset": 21832, "endOffset": 21858, "count": 54}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 22189, "endOffset": 33222, "count": 142}], "isBlockCoverage": false}, {"functionName": "setURLSearchParamsModified", "ranges": [{"startOffset": 22319, "endOffset": 22807, "count": 0}], "isBlockCoverage": false}, {"functionName": "URL", "ranges": [{"startOffset": 22816, "endOffset": 23280, "count": 142}, {"startOffset": 22912, "endOffset": 22960, "count": 0}, {"startOffset": 23065, "endOffset": 23096, "count": 67}, {"startOffset": 23236, "endOffset": 23276, "count": 128}], "isBlockCoverage": true}, {"functionName": "parse", "ranges": [{"startOffset": 23291, "endOffset": 23533, "count": 6}, {"startOffset": 23356, "endOffset": 23404, "count": 0}, {"startOffset": 23504, "endOffset": 23521, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23537, "endOffset": 24257, "count": 0}], "isBlockCoverage": false}, {"functionName": "#getSearchFromContext", "ranges": [{"startOffset": 24261, "endOffset": 24595, "count": 6}, {"startOffset": 24331, "endOffset": 24594, "count": 0}], "isBlockCoverage": true}, {"functionName": "#getSearchFromParams", "ranges": [{"startOffset": 24599, "endOffset": 24710, "count": 0}], "isBlockCoverage": false}, {"functionName": "#ensureSearchParamsUpdated", "ranges": [{"startOffset": 24714, "endOffset": 25153, "count": 143}, {"startOffset": 24982, "endOffset": 25149, "count": 0}], "isBlockCoverage": true}, {"functionName": "#updateContext", "ranges": [{"startOffset": 25401, "endOffset": 27046, "count": 140}, {"startOffset": 25510, "endOffset": 25531, "count": 6}, {"startOffset": 25532, "endOffset": 25630, "count": 0}, {"startOffset": 26326, "endOffset": 27042, "count": 0}], "isBlockCoverage": true}, {"functionName": "toString", "ranges": [{"startOffset": 27050, "endOffset": 27235, "count": 52}], "isBlockCoverage": true}, {"functionName": "get href", "ranges": [{"startOffset": 27239, "endOffset": 27424, "count": 85}], "isBlockCoverage": true}, {"functionName": "set href", "ranges": [{"startOffset": 27428, "endOffset": 27647, "count": 0}], "isBlockCoverage": false}, {"functionName": "get origin", "ranges": [{"startOffset": 27665, "endOffset": 28534, "count": 0}], "isBlockCoverage": false}, {"functionName": "get protocol", "ranges": [{"startOffset": 28538, "endOffset": 28642, "count": 168}], "isBlockCoverage": true}, {"functionName": "set protocol", "ranges": [{"startOffset": 28646, "endOffset": 28819, "count": 0}], "isBlockCoverage": false}, {"functionName": "get username", "ranges": [{"startOffset": 28823, "endOffset": 29050, "count": 0}], "isBlockCoverage": false}, {"functionName": "set username", "ranges": [{"startOffset": 29054, "endOffset": 29227, "count": 0}], "isBlockCoverage": false}, {"functionName": "get password", "ranges": [{"startOffset": 29231, "endOffset": 29454, "count": 0}], "isBlockCoverage": false}, {"functionName": "set password", "ranges": [{"startOffset": 29458, "endOffset": 29631, "count": 0}], "isBlockCoverage": false}, {"functionName": "get host", "ranges": [{"startOffset": 29635, "endOffset": 30066, "count": 12}, {"startOffset": 29739, "endOffset": 29764, "count": 0}, {"startOffset": 29945, "endOffset": 29969, "count": 0}], "isBlockCoverage": true}, {"functionName": "set host", "ranges": [{"startOffset": 30070, "endOffset": 30235, "count": 0}], "isBlockCoverage": false}, {"functionName": "get hostname", "ranges": [{"startOffset": 30239, "endOffset": 30521, "count": 55}, {"startOffset": 30405, "endOffset": 30430, "count": 0}], "isBlockCoverage": true}, {"functionName": "set hostname", "ranges": [{"startOffset": 30525, "endOffset": 30698, "count": 0}], "isBlockCoverage": false}, {"functionName": "get port", "ranges": [{"startOffset": 30702, "endOffset": 30810, "count": 0}], "isBlockCoverage": false}, {"functionName": "set port", "ranges": [{"startOffset": 30814, "endOffset": 30979, "count": 0}], "isBlockCoverage": false}, {"functionName": "get pathname", "ranges": [{"startOffset": 30983, "endOffset": 31275, "count": 158}, {"startOffset": 31049, "endOffset": 31099, "count": 0}, {"startOffset": 31132, "endOffset": 31180, "count": 0}], "isBlockCoverage": true}, {"functionName": "set pathname", "ranges": [{"startOffset": 31279, "endOffset": 31452, "count": 0}], "isBlockCoverage": false}, {"functionName": "get search", "ranges": [{"startOffset": 31456, "endOffset": 31653, "count": 6}], "isBlockCoverage": true}, {"functionName": "set search", "ranges": [{"startOffset": 31657, "endOffset": 31861, "count": 6}], "isBlockCoverage": true}, {"functionName": "get searchParams", "ranges": [{"startOffset": 31879, "endOffset": 32237, "count": 0}], "isBlockCoverage": false}, {"functionName": "get hash", "ranges": [{"startOffset": 32241, "endOffset": 32456, "count": 6}, {"startOffset": 32285, "endOffset": 32347, "count": 0}, {"startOffset": 32373, "endOffset": 32455, "count": 0}], "isBlockCoverage": true}, {"functionName": "set hash", "ranges": [{"startOffset": 32460, "endOffset": 32625, "count": 6}], "isBlockCoverage": true}, {"functionName": "toJSON", "ranges": [{"startOffset": 32629, "endOffset": 32812, "count": 0}], "isBlockCoverage": false}, {"functionName": "canParse", "ranges": [{"startOffset": 32823, "endOffset": 33220, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPathFromURLPosix", "ranges": [{"startOffset": 42504, "endOffset": 43025, "count": 55}, {"startOffset": 42567, "endOffset": 42623, "count": 0}, {"startOffset": 42701, "endOffset": 42984, "count": 3655}, {"startOffset": 42732, "endOffset": 42980, "count": 0}], "isBlockCoverage": true}, {"functionName": "fileURLToPath", "ranges": [{"startOffset": 43027, "endOffset": 43439, "count": 55}, {"startOffset": 43154, "endOffset": 43175, "count": 6}, {"startOffset": 43175, "endOffset": 43269, "count": 49}, {"startOffset": 43205, "endOffset": 43269, "count": 0}, {"startOffset": 43307, "endOffset": 43348, "count": 0}, {"startOffset": 43381, "endOffset": 43408, "count": 0}], "isBlockCoverage": true}, {"functionName": "encodePathChars", "ranges": [{"startOffset": 43992, "endOffset": 46009, "count": 23}, {"startOffset": 44099, "endOffset": 44180, "count": 0}, {"startOffset": 44229, "endOffset": 44306, "count": 0}, {"startOffset": 44354, "endOffset": 44435, "count": 0}, {"startOffset": 44483, "endOffset": 44571, "count": 0}, {"startOffset": 44618, "endOffset": 44697, "count": 0}, {"startOffset": 44744, "endOffset": 44823, "count": 0}, {"startOffset": 44870, "endOffset": 44948, "count": 0}, {"startOffset": 44995, "endOffset": 45081, "count": 0}, {"startOffset": 45128, "endOffset": 45219, "count": 0}, {"startOffset": 45381, "endOffset": 45464, "count": 0}, {"startOffset": 45511, "endOffset": 45603, "count": 0}, {"startOffset": 45650, "endOffset": 45729, "count": 0}, {"startOffset": 45776, "endOffset": 45861, "count": 0}, {"startOffset": 45908, "endOffset": 45987, "count": 0}], "isBlockCoverage": true}, {"functionName": "pathToFileURL", "ranges": [{"startOffset": 46011, "endOffset": 47870, "count": 23}, {"startOffset": 46133, "endOffset": 46179, "count": 0}, {"startOffset": 46181, "endOffset": 47319, "count": 0}, {"startOffset": 47345, "endOffset": 47375, "count": 0}, {"startOffset": 47668, "endOffset": 47680, "count": 0}, {"startOffset": 47682, "endOffset": 47721, "count": 0}, {"startOffset": 47724, "endOffset": 47775, "count": 0}, {"startOffset": 47781, "endOffset": 47797, "count": 0}], "isBlockCoverage": true}, {"functionName": "toPathIfFileURL", "ranges": [{"startOffset": 47872, "endOffset": 48009, "count": 268}, {"startOffset": 47947, "endOffset": 47968, "count": 263}, {"startOffset": 47968, "endOffset": 48008, "count": 5}], "isBlockCoverage": true}]}, {"scriptId": "33", "url": "node:path", "functions": [{"functionName": "isPosixPathSeparator", "ranges": [{"startOffset": 2158, "endOffset": 2235, "count": 64442}], "isBlockCoverage": true}, {"functionName": "normalizeString", "ranges": [{"startOffset": 2470, "endOffset": 4418, "count": 1203}, {"startOffset": 2687, "endOffset": 4402, "count": 64331}, {"startOffset": 2720, "endOffset": 2762, "count": 63128}, {"startOffset": 2762, "endOffset": 2853, "count": 1203}, {"startOffset": 2805, "endOffset": 2811, "count": 1092}, {"startOffset": 2811, "endOffset": 2853, "count": 111}, {"startOffset": 2853, "endOffset": 2886, "count": 63239}, {"startOffset": 2886, "endOffset": 4303, "count": 9532}, {"startOffset": 2918, "endOffset": 2931, "count": 7913}, {"startOffset": 2933, "endOffset": 2958, "count": 1746}, {"startOffset": 2958, "endOffset": 4260, "count": 7786}, {"startOffset": 2980, "endOffset": 4015, "count": 44}, {"startOffset": 3009, "endOffset": 3035, "count": 25}, {"startOffset": 3036, "endOffset": 3110, "count": 1}, {"startOffset": 3111, "endOffset": 3185, "count": 1}, {"startOffset": 3187, "endOffset": 3875, "count": 43}, {"startOffset": 3219, "endOffset": 3696, "count": 24}, {"startOffset": 3339, "endOffset": 3415, "count": 0}, {"startOffset": 3696, "endOffset": 3865, "count": 19}, {"startOffset": 3724, "endOffset": 3865, "count": 0}, {"startOffset": 3875, "endOffset": 4007, "count": 20}, {"startOffset": 3938, "endOffset": 3956, "count": 1}, {"startOffset": 3957, "endOffset": 3963, "count": 19}, {"startOffset": 4015, "endOffset": 4260, "count": 7742}, {"startOffset": 4061, "endOffset": 4130, "count": 6558}, {"startOffset": 4130, "endOffset": 4205, "count": 1184}, {"startOffset": 4260, "endOffset": 4303, "count": 9508}, {"startOffset": 4303, "endOffset": 4398, "count": 53707}, {"startOffset": 4331, "endOffset": 4345, "count": 1208}, {"startOffset": 4347, "endOffset": 4368, "count": 272}, {"startOffset": 4368, "endOffset": 4398, "count": 53435}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 36606, "endOffset": 36625, "count": 1}], "isBlockCoverage": true}, {"functionName": "resolve", "ranges": [{"startOffset": 36750, "endOffset": 37854, "count": 1092}, {"startOffset": 36872, "endOffset": 36892, "count": 1921}, {"startOffset": 36899, "endOffset": 37204, "count": 1917}, {"startOffset": 37030, "endOffset": 37057, "count": 2}, {"startOffset": 37057, "endOffset": 37204, "count": 1915}, {"startOffset": 37233, "endOffset": 37408, "count": 1}, {"startOffset": 37793, "endOffset": 37843, "count": 0}, {"startOffset": 37844, "endOffset": 37849, "count": 0}], "isBlockCoverage": true}, {"functionName": "normalize", "ranges": [{"startOffset": 37920, "endOffset": 38533, "count": 111}, {"startOffset": 38006, "endOffset": 38017, "count": 0}, {"startOffset": 38345, "endOffset": 38439, "count": 0}, {"startOffset": 38473, "endOffset": 38485, "count": 0}, {"startOffset": 38509, "endOffset": 38521, "count": 29}, {"startOffset": 38522, "endOffset": 38528, "count": 82}], "isBlockCoverage": true}, {"functionName": "isAbsolute", "ranges": [{"startOffset": 38600, "endOffset": 38756, "count": 143}], "isBlockCoverage": true}, {"functionName": "join", "ranges": [{"startOffset": 38825, "endOffset": 39189, "count": 29}, {"startOffset": 38874, "endOffset": 38885, "count": 0}, {"startOffset": 38950, "endOffset": 39079, "count": 58}, {"startOffset": 39040, "endOffset": 39073, "count": 57}, {"startOffset": 39114, "endOffset": 39125, "count": 0}], "isBlockCoverage": true}, {"functionName": "toNamespacedPath", "ranges": [{"startOffset": 41705, "endOffset": 41781, "count": 879}], "isBlockCoverage": true}, {"functionName": "dirname", "ranges": [{"startOffset": 41847, "endOffset": 42523, "count": 419}, {"startOffset": 41930, "endOffset": 41941, "count": 0}, {"startOffset": 42115, "endOffset": 42368, "count": 5170}, {"startOffset": 42186, "endOffset": 42270, "count": 415}, {"startOffset": 42270, "endOffset": 42362, "count": 4755}, {"startOffset": 42396, "endOffset": 42423, "count": 4}, {"startOffset": 42417, "endOffset": 42422, "count": 0}, {"startOffset": 42423, "endOffset": 42452, "count": 415}, {"startOffset": 42460, "endOffset": 42472, "count": 0}, {"startOffset": 42472, "endOffset": 42522, "count": 415}], "isBlockCoverage": true}, {"functionName": "basename", "ranges": [{"startOffset": 42619, "endOffset": 45049, "count": 147}, {"startOffset": 42680, "endOffset": 42713, "count": 0}, {"startOffset": 42845, "endOffset": 42865, "count": 0}, {"startOffset": 42866, "endOffset": 42897, "count": 0}, {"startOffset": 42899, "endOffset": 44423, "count": 0}, {"startOffset": 44471, "endOffset": 44956, "count": 3060}, {"startOffset": 44542, "endOffset": 44766, "count": 98}, {"startOffset": 44766, "endOffset": 44950, "count": 2962}, {"startOffset": 44788, "endOffset": 44950, "count": 147}, {"startOffset": 44984, "endOffset": 44994, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "34", "url": "node:querystring", "functions": [{"functionName": "parse", "ranges": [{"startOffset": 9392, "endOffset": 14184, "count": 1}, {"startOffset": 9537, "endOffset": 9577, "count": 0}, {"startOffset": 9578, "endOffset": 9602, "count": 0}, {"startOffset": 9628, "endOffset": 9640, "count": 0}, {"startOffset": 9641, "endOffset": 9664, "count": 0}, {"startOffset": 9768, "endOffset": 9806, "count": 0}, {"startOffset": 9808, "endOffset": 10370, "count": 0}, {"startOffset": 10372, "endOffset": 10662, "count": 0}, {"startOffset": 10663, "endOffset": 10668, "count": 0}, {"startOffset": 10732, "endOffset": 14183, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "35", "url": "node:internal/process/task_queues", "functions": [{"functionName": "setHasTickScheduled", "ranges": [{"startOffset": 1059, "endOffset": 1145, "count": 17}, {"startOffset": 1135, "endOffset": 1138, "count": 4}, {"startOffset": 1139, "endOffset": 1142, "count": 13}], "isBlockCoverage": true}, {"functionName": "processTicksAndRejections", "ranges": [{"startOffset": 1445, "endOffset": 2424, "count": 14}, {"startOffset": 1501, "endOffset": 2304, "count": 15}, {"startOffset": 1547, "endOffset": 2279, "count": 4}, {"startOffset": 1748, "endOffset": 1781, "count": 0}, {"startOffset": 1914, "endOffset": 1956, "count": 0}, {"startOffset": 1969, "endOffset": 2020, "count": 0}, {"startOffset": 2033, "endOffset": 2093, "count": 0}, {"startOffset": 2106, "endOffset": 2133, "count": 0}, {"startOffset": 2164, "endOffset": 2246, "count": 3}, {"startOffset": 2217, "endOffset": 2238, "count": 0}, {"startOffset": 2246, "endOffset": 2279, "count": 3}, {"startOffset": 2279, "endOffset": 2304, "count": 14}, {"startOffset": 2304, "endOffset": 2358, "count": 13}], "isBlockCoverage": true}, {"functionName": "nextTick", "ranges": [{"startOffset": 2568, "endOffset": 3442, "count": 4}, {"startOffset": 2669, "endOffset": 2676, "count": 0}, {"startOffset": 2724, "endOffset": 2738, "count": 0}, {"startOffset": 2785, "endOffset": 2836, "count": 0}, {"startOffset": 2841, "endOffset": 2906, "count": 0}, {"startOffset": 2911, "endOffset": 3054, "count": 0}, {"startOffset": 3354, "endOffset": 3414, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "36", "url": "node:internal/process/promises", "functions": [{"functionName": "", "ranges": [{"startOffset": 1267, "endOffset": 1379, "count": 15}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1414, "endOffset": 1569, "count": 12}, {"startOffset": 1473, "endOffset": 1565, "count": 0}], "isBlockCoverage": true}, {"functionName": "setHasRejectionToWarn", "ranges": [{"startOffset": 4097, "endOffset": 4187, "count": 25}, {"startOffset": 4177, "endOffset": 4180, "count": 12}, {"startOffset": 4181, "endOffset": 4184, "count": 13}], "isBlockCoverage": true}, {"functionName": "promiseRejectHandler", "ranges": [{"startOffset": 4596, "endOffset": 5279, "count": 24}, {"startOffset": 4696, "endOffset": 4761, "count": 1}, {"startOffset": 4784, "endOffset": 4878, "count": 12}, {"startOffset": 4883, "endOffset": 4971, "count": 12}, {"startOffset": 4976, "endOffset": 5121, "count": 0}, {"startOffset": 5126, "endOffset": 5273, "count": 0}], "isBlockCoverage": true}, {"functionName": "unhandledRejection", "ranges": [{"startOffset": 6708, "endOffset": 6922, "count": 12}], "isBlockCoverage": true}, {"functionName": "handledRejection", "ranges": [{"startOffset": 6960, "endOffset": 7541, "count": 12}, {"startOffset": 7109, "endOffset": 7539, "count": 0}], "isBlockCoverage": true}, {"functionName": "getUnhandledRejectionsMode", "ranges": [{"startOffset": 11313, "endOffset": 11864, "count": 1}, {"startOffset": 11469, "endOffset": 11525, "count": 0}, {"startOffset": 11530, "endOffset": 11590, "count": 0}, {"startOffset": 11595, "endOffset": 11653, "count": 0}, {"startOffset": 11658, "endOffset": 11714, "count": 0}, {"startOffset": 11719, "endOffset": 11802, "count": 0}], "isBlockCoverage": true}, {"functionName": "processPromiseRejections", "ranges": [{"startOffset": 12037, "endOffset": 13386, "count": 13}, {"startOffset": 12197, "endOffset": 12376, "count": 0}, {"startOffset": 12582, "endOffset": 13292, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "37", "url": "node:internal/fixed_queue", "functions": [{"functionName": "isEmpty", "ranges": [{"startOffset": 3130, "endOffset": 3182, "count": 36}], "isBlockCoverage": true}, {"functionName": "isFull", "ranges": [{"startOffset": 3186, "endOffset": 3253, "count": 4}], "isBlockCoverage": true}, {"functionName": "push", "ranges": [{"startOffset": 3257, "endOffset": 3344, "count": 4}], "isBlockCoverage": true}, {"functionName": "shift", "ranges": [{"startOffset": 3348, "endOffset": 3563, "count": 18}, {"startOffset": 3441, "endOffset": 3453, "count": 14}, {"startOffset": 3453, "endOffset": 3562, "count": 4}], "isBlockCoverage": true}, {"functionName": "isEmpty", "ranges": [{"startOffset": 3683, "endOffset": 3730, "count": 18}], "isBlockCoverage": true}, {"functionName": "push", "ranges": [{"startOffset": 3734, "endOffset": 3998, "count": 4}, {"startOffset": 3775, "endOffset": 3968, "count": 0}], "isBlockCoverage": true}, {"functionName": "shift", "ranges": [{"startOffset": 4002, "endOffset": 4257, "count": 18}, {"startOffset": 4117, "endOffset": 4236, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "39", "url": "node:timers", "functions": [{"functionName": "", "ranges": [{"startOffset": 1886, "endOffset": 1911, "count": 1}], "isBlockCoverage": true}, {"functionName": "unenroll", "ranges": [{"startOffset": 2207, "endOffset": 3391, "count": 2}, {"startOffset": 2260, "endOffset": 2267, "count": 0}, {"startOffset": 2327, "endOffset": 2373, "count": 0}, {"startOffset": 2464, "endOffset": 2502, "count": 0}, {"startOffset": 2508, "endOffset": 2543, "count": 0}, {"startOffset": 2937, "endOffset": 3284, "count": 1}], "isBlockCoverage": true}, {"functionName": "setTimeout", "ranges": [{"startOffset": 4093, "endOffset": 4762, "count": 1}, {"startOffset": 4260, "endOffset": 4267, "count": 0}, {"startOffset": 4297, "endOffset": 4338, "count": 0}, {"startOffset": 4343, "endOffset": 4390, "count": 0}, {"startOffset": 4395, "endOffset": 4628, "count": 0}], "isBlockCoverage": true}, {"functionName": "clearTimeout", "ranges": [{"startOffset": 5082, "endOffset": 5448, "count": 6}, {"startOffset": 5125, "endOffset": 5144, "count": 2}, {"startOffset": 5146, "endOffset": 5213, "count": 2}, {"startOffset": 5213, "endOffset": 5274, "count": 4}, {"startOffset": 5276, "endOffset": 5446, "count": 0}], "isBlockCoverage": true}, {"functionName": "setInterval", "ranges": [{"startOffset": 5685, "endOffset": 6356, "count": 1}, {"startOffset": 5854, "endOffset": 5861, "count": 0}, {"startOffset": 5891, "endOffset": 5932, "count": 0}, {"startOffset": 5937, "endOffset": 5984, "count": 0}, {"startOffset": 5989, "endOffset": 6222, "count": 0}], "isBlockCoverage": true}, {"functionName": "clearInterval", "ranges": [{"startOffset": 6453, "endOffset": 6751, "count": 3}], "isBlockCoverage": true}]}, {"scriptId": "40", "url": "node:internal/process/execution", "functions": [{"functionName": "tryGetCwd", "ranges": [{"startOffset": 945, "endOffset": 1275, "count": 1}, {"startOffset": 1006, "endOffset": 1273, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "43", "url": "node:internal/source_map/source_map_cache", "functions": [{"functionName": "", "ranges": [{"startOffset": 344, "endOffset": 369, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 676, "endOffset": 804, "count": 1}], "isBlockCoverage": true}, {"functionName": "getSourceMapsEnabled", "ranges": [{"startOffset": 1579, "endOffset": 1642, "count": 7}], "isBlockCoverage": true}, {"functionName": "setSourceMapsEnabled", "ranges": [{"startOffset": 1729, "endOffset": 2313, "count": 1}, {"startOffset": 1837, "endOffset": 2020, "count": 0}], "isBlockCoverage": true}, {"functionName": "extractSourceURLMagicComment", "ranges": [{"startOffset": 2631, "endOffset": 3231, "count": 6}, {"startOffset": 2933, "endOffset": 2966, "count": 0}, {"startOffset": 3019, "endOffset": 3155, "count": 0}, {"startOffset": 3157, "endOffset": 3230, "count": 0}], "isBlockCoverage": true}, {"functionName": "maybeCacheSourceMap", "ranges": [{"startOffset": 4659, "endOffset": 6205, "count": 6}, {"startOffset": 4858, "endOffset": 4878, "count": 0}, {"startOffset": 4881, "endOffset": 4888, "count": 0}, {"startOffset": 5036, "endOffset": 5132, "count": 0}, {"startOffset": 5168, "endOffset": 5234, "count": 0}, {"startOffset": 5323, "endOffset": 5340, "count": 0}, {"startOffset": 5778, "endOffset": 5784, "count": 0}, {"startOffset": 5847, "endOffset": 5994, "count": 0}, {"startOffset": 6104, "endOffset": 6127, "count": 0}], "isBlockCoverage": true}, {"functionName": "dataFromUrl", "ranges": [{"startOffset": 7259, "endOffset": 7680, "count": 6}, {"startOffset": 7373, "endOffset": 7581, "count": 0}], "isBlockCoverage": true}, {"functionName": "lineLengths", "ranges": [{"startOffset": 7870, "endOffset": 8666, "count": 6}, {"startOffset": 8041, "endOffset": 8605, "count": 40265}, {"startOffset": 8417, "endOffset": 8440, "count": 39047}, {"startOffset": 8441, "endOffset": 8464, "count": 39047}, {"startOffset": 8466, "endOffset": 8601, "count": 1218}], "isBlockCoverage": true}, {"functionName": "sourceMapFromFile", "ranges": [{"startOffset": 8818, "endOffset": 9098, "count": 6}], "isBlockCoverage": true}]}, {"scriptId": "44", "url": "node:internal/modules/helpers", "functions": [{"functionName": "", "ranges": [{"startOffset": 1182, "endOffset": 1207, "count": 1}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1676, "endOffset": 1808, "count": 125}], "isBlockCoverage": true}, {"functionName": "initializeCjsConditions", "ranges": [{"startOffset": 1924, "endOffset": 2415, "count": 1}, {"startOffset": 2103, "endOffset": 2107, "count": 0}], "isBlockCoverage": true}, {"functionName": "getCjsConditions", "ranges": [{"startOffset": 2482, "endOffset": 2609, "count": 5}, {"startOffset": 2547, "endOffset": 2583, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadBuiltinModule", "ranges": [{"startOffset": 2836, "endOffset": 3216, "count": 62}, {"startOffset": 2925, "endOffset": 2942, "count": 0}], "isBlockCoverage": true}, {"functionName": "lazyModule", "ranges": [{"startOffset": 3309, "endOffset": 3422, "count": 95}, {"startOffset": 3353, "endOffset": 3401, "count": 1}], "isBlockCoverage": true}, {"functionName": "makeRequireFunction", "ranges": [{"startOffset": 4011, "endOffset": 6543, "count": 95}, {"startOffset": 4150, "endOffset": 4213, "count": 0}, {"startOffset": 4280, "endOffset": 5551, "count": 0}], "isBlockCoverage": true}, {"functionName": "require", "ranges": [{"startOffset": 4423, "endOffset": 5546, "count": 0}], "isBlockCoverage": false}, {"functionName": "require", "ranges": [{"startOffset": 5573, "endOffset": 5713, "count": 214}], "isBlockCoverage": true}, {"functionName": "resolve", "ranges": [{"startOffset": 5899, "endOffset": 6045, "count": 1}], "isBlockCoverage": true}, {"functionName": "paths", "ranges": [{"startOffset": 6190, "endOffset": 6312, "count": 0}], "isBlockCoverage": false}, {"functionName": "stripBOM", "ranges": [{"startOffset": 6755, "endOffset": 6910, "count": 2}, {"startOffset": 6837, "endOffset": 6890, "count": 0}], "isBlockCoverage": true}, {"functionName": "normalizeReferrerURL", "ranges": [{"startOffset": 9391, "endOffset": 9894, "count": 8}, {"startOffset": 9496, "endOffset": 9523, "count": 0}, {"startOffset": 9660, "endOffset": 9757, "count": 0}, {"startOffset": 9759, "endOffset": 9893, "count": 0}], "isBlockCoverage": true}, {"functionName": "hasStartedUserCJSExecution", "ranges": [{"startOffset": 11438, "endOffset": 11512, "count": 1}], "isBlockCoverage": true}, {"functionName": "setHasStartedUserCJSExecution", "ranges": [{"startOffset": 11516, "endOffset": 11593, "count": 95}], "isBlockCoverage": true}, {"functionName": "hasStartedUserESMExecution", "ranges": [{"startOffset": 11597, "endOffset": 11671, "count": 1}], "isBlockCoverage": true}, {"functionName": "setHasStartedUserESMExecution", "ranges": [{"startOffset": 11675, "endOffset": 11752, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "45", "url": "node:fs", "functions": [{"functionName": "makeCallback", "ranges": [{"startOffset": 4712, "endOffset": 4824, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4780, "endOffset": 4821, "count": 1}], "isBlockCoverage": true}, {"functionName": "makeStatsCallback", "ranges": [{"startOffset": 5005, "endOffset": 5172, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5078, "endOffset": 5169, "count": 2}, {"startOffset": 5109, "endOffset": 5124, "count": 0}], "isBlockCoverage": true}, {"functionName": "isFileType", "ranges": [{"startOffset": 5197, "endOffset": 5457, "count": 1407}, {"startOffset": 5396, "endOffset": 5416, "count": 175}], "isBlockCoverage": true}, {"functionName": "readFileSync", "ranges": [{"startOffset": 11296, "endOffset": 12935, "count": 103}, {"startOffset": 11418, "endOffset": 11449, "count": 0}, {"startOffset": 11622, "endOffset": 11712, "count": 0}, {"startOffset": 11713, "endOffset": 11753, "count": 0}, {"startOffset": 11841, "endOffset": 11851, "count": 0}, {"startOffset": 11852, "endOffset": 11855, "count": 0}, {"startOffset": 11985, "endOffset": 12934, "count": 0}], "isBlockCoverage": true}, {"functionName": "closeSync", "ranges": [{"startOffset": 13453, "endOffset": 13500, "count": 1}], "isBlockCoverage": true}, {"functionName": "openSync", "ranges": [{"startOffset": 14492, "endOffset": 14699, "count": 1}], "isBlockCoverage": true}, {"functionName": "writeSync", "ranges": [{"startOffset": 23179, "endOffset": 24312, "count": 25}, {"startOffset": 23410, "endOffset": 23560, "count": 0}, {"startOffset": 23599, "endOffset": 23615, "count": 0}, {"startOffset": 23640, "endOffset": 23665, "count": 0}, {"startOffset": 23765, "endOffset": 23801, "count": 0}, {"startOffset": 23992, "endOffset": 24262, "count": 0}], "isBlockCoverage": true}, {"functionName": "lstat", "ranges": [{"startOffset": 39879, "endOffset": 40528, "count": 2}, {"startOffset": 40140, "endOffset": 40175, "count": 0}, {"startOffset": 40177, "endOffset": 40374, "count": 0}], "isBlockCoverage": true}, {"functionName": "unlink", "ranges": [{"startOffset": 49656, "endOffset": 49883, "count": 1}], "isBlockCoverage": true}, {"functionName": "splitRoot", "ranges": [{"startOffset": 69496, "endOffset": 69706, "count": 104}, {"startOffset": 69563, "endOffset": 69686, "count": 208}, {"startOffset": 69641, "endOffset": 69680, "count": 104}, {"startOffset": 69686, "endOffset": 69705, "count": 0}], "isBlockCoverage": true}, {"functionName": "encodeRealpathResult", "ranges": [{"startOffset": 69711, "endOffset": 69998, "count": 103}, {"startOffset": 69796, "endOffset": 69826, "count": 0}, {"startOffset": 69846, "endOffset": 69997, "count": 0}], "isBlockCoverage": true}, {"functionName": "nextPart", "ranges": [{"startOffset": 70404, "endOffset": 70479, "count": 791}], "isBlockCoverage": true}, {"functionName": "realpathSync", "ranges": [{"startOffset": 70653, "endOffset": 74472, "count": 131}, {"startOffset": 70777, "endOffset": 70795, "count": 0}, {"startOffset": 70956, "endOffset": 70991, "count": 28}, {"startOffset": 70991, "endOffset": 71553, "count": 103}, {"startOffset": 71553, "endOffset": 71743, "count": 0}, {"startOffset": 71743, "endOffset": 71880, "count": 103}, {"startOffset": 71880, "endOffset": 74399, "count": 791}, {"startOffset": 71992, "endOffset": 72123, "count": 104}, {"startOffset": 72123, "endOffset": 72281, "count": 687}, {"startOffset": 72368, "endOffset": 72396, "count": 790}, {"startOffset": 72398, "endOffset": 72535, "count": 616}, {"startOffset": 72489, "endOffset": 72513, "count": 0}, {"startOffset": 72535, "endOffset": 72601, "count": 175}, {"startOffset": 72638, "endOffset": 72687, "count": 0}, {"startOffset": 72687, "endOffset": 73876, "count": 175}, {"startOffset": 72983, "endOffset": 73008, "count": 0}, {"startOffset": 73049, "endOffset": 73137, "count": 174}, {"startOffset": 73137, "endOffset": 73558, "count": 1}, {"startOffset": 73497, "endOffset": 73550, "count": 0}, {"startOffset": 73558, "endOffset": 73796, "count": 1}, {"startOffset": 73840, "endOffset": 73870, "count": 1}, {"startOffset": 73876, "endOffset": 74168, "count": 1}, {"startOffset": 74168, "endOffset": 74191, "count": 0}, {"startOffset": 74193, "endOffset": 74395, "count": 0}, {"startOffset": 74399, "endOffset": 74413, "count": 103}], "isBlockCoverage": true}, {"functionName": "lazyLoadStreams", "ranges": [{"startOffset": 82731, "endOffset": 82922, "count": 4}, {"startOffset": 82779, "endOffset": 82920, "count": 1}], "isBlockCoverage": true}, {"functionName": "get ReadStream", "ranges": [{"startOffset": 85261, "endOffset": 85329, "count": 2}], "isBlockCoverage": true}, {"functionName": "get WriteStream", "ranges": [{"startOffset": 85386, "endOffset": 85456, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 86562, "endOffset": 86658, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "47", "url": "node:internal/encoding", "functions": [{"functionName": "validateDecoder", "ranges": [{"startOffset": 1482, "endOffset": 1607, "count": 5}, {"startOffset": 1563, "endOffset": 1605, "count": 0}], "isBlockCoverage": true}, {"functionName": "getEncodingFromLabel", "ranges": [{"startOffset": 9404, "endOffset": 9583, "count": 1}, {"startOffset": 9515, "endOffset": 9582, "count": 0}], "isBlockCoverage": true}, {"functionName": "TextDecoder", "ranges": [{"startOffset": 11141, "endOffset": 12104, "count": 1}, {"startOffset": 11397, "endOffset": 11444, "count": 0}, {"startOffset": 11528, "endOffset": 11551, "count": 0}, {"startOffset": 11592, "endOffset": 11620, "count": 0}, {"startOffset": 12028, "endOffset": 12053, "count": 0}, {"startOffset": 12055, "endOffset": 12098, "count": 0}], "isBlockCoverage": true}, {"functionName": "decode", "ranges": [{"startOffset": 12381, "endOffset": 13076, "count": 5}, {"startOffset": 12550, "endOffset": 12558, "count": 0}, {"startOffset": 12668, "endOffset": 13075, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "52", "url": "node:internal/process/permission", "functions": [{"functionName": "isEnabled", "ranges": [{"startOffset": 332, "endOffset": 576, "count": 430}, {"startOffset": 392, "endOffset": 537, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "55", "url": "node:internal/console/constructor", "functions": [{"functionName": "value", "ranges": [{"startOffset": 5763, "endOffset": 6408, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 5972, "endOffset": 6067, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6079, "endOffset": 6109, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6239, "endOffset": 6338, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6350, "endOffset": 6380, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeGlobalConsole", "ranges": [{"startOffset": 20356, "endOffset": 21346, "count": 1}, {"startOffset": 20675, "endOffset": 21345, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21241, "endOffset": 21342, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "57", "url": "node:internal/event_target", "functions": [{"functionName": "defineEventHandler", "ranges": [{"startOffset": 29893, "endOffset": 31359, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 30053, "endOffset": 30189, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 30288, "endOffset": 31126, "count": 0}], "isBlockCoverage": false}, {"functionName": "EventEmitterMixin", "ranges": [{"startOffset": 31387, "endOffset": 32077, "count": 1}, {"startOffset": 31818, "endOffset": 31980, "count": 18}], "isBlockCoverage": true}, {"functionName": "MixedEventEmitter", "ranges": [{"startOffset": 31456, "endOffset": 31597, "count": 10}], "isBlockCoverage": true}]}, {"scriptId": "61", "url": "node:internal/bootstrap/switches/is_main_thread", "functions": [{"functionName": "createWritableStdioStream", "ranges": [{"startOffset": 1424, "endOffset": 3026, "count": 2}, {"startOffset": 1716, "endOffset": 1910, "count": 0}, {"startOffset": 1916, "endOffset": 1928, "count": 0}, {"startOffset": 1933, "endOffset": 2645, "count": 0}, {"startOffset": 2651, "endOffset": 2905, "count": 0}], "isBlockCoverage": true}, {"functionName": "write", "ranges": [{"startOffset": 2841, "endOffset": 2888, "count": 0}], "isBlockCoverage": false}, {"functionName": "addCleanup", "ranges": [{"startOffset": 3639, "endOffset": 3730, "count": 3}, {"startOffset": 3693, "endOffset": 3728, "count": 0}], "isBlockCoverage": true}, {"functionName": "getStdout", "ranges": [{"startOffset": 3732, "endOffset": 4424, "count": 5}, {"startOffset": 3769, "endOffset": 3783, "count": 4}, {"startOffset": 3783, "endOffset": 4423, "count": 1}], "isBlockCoverage": true}, {"functionName": "cleanupStdout", "ranges": [{"startOffset": 4090, "endOffset": 4268, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStderr", "ranges": [{"startOffset": 4426, "endOffset": 5117, "count": 5}, {"startOffset": 4463, "endOffset": 4477, "count": 4}, {"startOffset": 4477, "endOffset": 5116, "count": 1}], "isBlockCoverage": true}, {"functionName": "cleanupStderr", "ranges": [{"startOffset": 4783, "endOffset": 4961, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStdin", "ranges": [{"startOffset": 5119, "endOffset": 7610, "count": 1}, {"startOffset": 5154, "endOffset": 5167, "count": 0}, {"startOffset": 5332, "endOffset": 5466, "count": 0}, {"startOffset": 5472, "endOffset": 5484, "count": 0}, {"startOffset": 5489, "endOffset": 6271, "count": 0}, {"startOffset": 6277, "endOffset": 6501, "count": 0}], "isBlockCoverage": true}, {"functionName": "read", "ranges": [{"startOffset": 6458, "endOffset": 6467, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7080, "endOffset": 7122, "count": 0}], "isBlockCoverage": false}, {"functionName": "onpause", "ranges": [{"startOffset": 7128, "endOffset": 7368, "count": 0}], "isBlockCoverage": false}, {"functionName": "cleanupStdin", "ranges": [{"startOffset": 7383, "endOffset": 7456, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "62", "url": "node:internal/v8/startup_snapshot", "functions": [{"functionName": "isBuildingSnapshot", "ranges": [{"startOffset": 433, "endOffset": 504, "count": 7}], "isBlockCoverage": false}, {"functionName": "runDeserializeCallbacks", "ranges": [{"startOffset": 831, "endOffset": 1004, "count": 1}, {"startOffset": 910, "endOffset": 1002, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "63", "url": "node:internal/process/signal", "functions": [{"functionName": "isSignal", "ranges": [{"startOffset": 238, "endOffset": 334, "count": 15}], "isBlockCoverage": false}, {"functionName": "startListeningIfSignal", "ranges": [{"startOffset": 398, "endOffset": 908, "count": 15}], "isBlockCoverage": false}, {"functionName": "stopListeningIfSignal", "ranges": [{"startOffset": 910, "endOffset": 1105, "count": 12}, {"startOffset": 1012, "endOffset": 1048, "count": 2}, {"startOffset": 1050, "endOffset": 1103, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "64", "url": "node:url", "functions": [{"functionName": "Url", "ranges": [{"startOffset": 2176, "endOffset": 2452, "count": 4}], "isBlockCoverage": true}, {"functionName": "urlParse", "ranges": [{"startOffset": 3457, "endOffset": 4062, "count": 4}, {"startOffset": 3585, "endOffset": 3908, "count": 0}, {"startOffset": 3936, "endOffset": 3947, "count": 0}], "isBlockCoverage": true}, {"functionName": "isIpv6Hostname", "ranges": [{"startOffset": 4064, "endOffset": 4287, "count": 4}, {"startOffset": 4183, "endOffset": 4280, "count": 0}], "isBlockCoverage": true}, {"functionName": "parse", "ranges": [{"startOffset": 5108, "endOffset": 15186, "count": 4}, {"startOffset": 5580, "endOffset": 6646, "count": 192}, {"startOffset": 5843, "endOffset": 5911, "count": 4}, {"startOffset": 5869, "endOffset": 5878, "count": 0}, {"startOffset": 5911, "endOffset": 6058, "count": 188}, {"startOffset": 5927, "endOffset": 6001, "count": 0}, {"startOffset": 6017, "endOffset": 6058, "count": 0}, {"startOffset": 6180, "endOffset": 6234, "count": 0}, {"startOffset": 6243, "endOffset": 6284, "count": 0}, {"startOffset": 6317, "endOffset": 6382, "count": 0}, {"startOffset": 6391, "endOffset": 6557, "count": 0}, {"startOffset": 6571, "endOffset": 6642, "count": 0}, {"startOffset": 6889, "endOffset": 6937, "count": 0}, {"startOffset": 6945, "endOffset": 6998, "count": 0}, {"startOffset": 7004, "endOffset": 7334, "count": 0}, {"startOffset": 7491, "endOffset": 7949, "count": 0}, {"startOffset": 8429, "endOffset": 8454, "count": 0}, {"startOffset": 8765, "endOffset": 8806, "count": 0}, {"startOffset": 9327, "endOffset": 10757, "count": 80}, {"startOffset": 9373, "endOffset": 9387, "count": 0}, {"startOffset": 9396, "endOffset": 9416, "count": 0}, {"startOffset": 9425, "endOffset": 9630, "count": 0}, {"startOffset": 9639, "endOffset": 9655, "count": 0}, {"startOffset": 9664, "endOffset": 9687, "count": 0}, {"startOffset": 9696, "endOffset": 9714, "count": 0}, {"startOffset": 9723, "endOffset": 9746, "count": 0}, {"startOffset": 9755, "endOffset": 9775, "count": 0}, {"startOffset": 9784, "endOffset": 9813, "count": 0}, {"startOffset": 9822, "endOffset": 9852, "count": 0}, {"startOffset": 9861, "endOffset": 9886, "count": 0}, {"startOffset": 9895, "endOffset": 9923, "count": 0}, {"startOffset": 9932, "endOffset": 9955, "count": 0}, {"startOffset": 9964, "endOffset": 9993, "count": 0}, {"startOffset": 10002, "endOffset": 10026, "count": 0}, {"startOffset": 10035, "endOffset": 10217, "count": 0}, {"startOffset": 10226, "endOffset": 10241, "count": 0}, {"startOffset": 10250, "endOffset": 10274, "count": 4}, {"startOffset": 10283, "endOffset": 10469, "count": 4}, {"startOffset": 10478, "endOffset": 10702, "count": 0}, {"startOffset": 10745, "endOffset": 10751, "count": 4}, {"startOffset": 10796, "endOffset": 10890, "count": 0}, {"startOffset": 10915, "endOffset": 10976, "count": 0}, {"startOffset": 11266, "endOffset": 11285, "count": 0}, {"startOffset": 11624, "endOffset": 11657, "count": 0}, {"startOffset": 11821, "endOffset": 11940, "count": 0}, {"startOffset": 12937, "endOffset": 12990, "count": 0}, {"startOffset": 13030, "endOffset": 13047, "count": 0}, {"startOffset": 13082, "endOffset": 13087, "count": 0}, {"startOffset": 13223, "endOffset": 13344, "count": 0}, {"startOffset": 13730, "endOffset": 13964, "count": 88}, {"startOffset": 13797, "endOffset": 13869, "count": 0}, {"startOffset": 13907, "endOffset": 13928, "count": 0}, {"startOffset": 13930, "endOffset": 13960, "count": 0}, {"startOffset": 13992, "endOffset": 14330, "count": 0}, {"startOffset": 14358, "endOffset": 14486, "count": 0}, {"startOffset": 14536, "endOffset": 14580, "count": 0}, {"startOffset": 14616, "endOffset": 14629, "count": 0}, {"startOffset": 14722, "endOffset": 14796, "count": 0}, {"startOffset": 14877, "endOffset": 14907, "count": 0}, {"startOffset": 14958, "endOffset": 14972, "count": 0}, {"startOffset": 15004, "endOffset": 15009, "count": 0}], "isBlockCoverage": true}, {"functionName": "getHostname", "ranges": [{"startOffset": 15217, "endOffset": 16162, "count": 4}, {"startOffset": 15311, "endOffset": 16145, "count": 76}, {"startOffset": 15616, "endOffset": 16141, "count": 0}], "isBlockCoverage": true}, {"functionName": "autoEscapeStr", "ranges": [{"startOffset": 17219, "endOffset": 17925, "count": 4}, {"startOffset": 17336, "endOffset": 17702, "count": 88}, {"startOffset": 17487, "endOffset": 17698, "count": 0}, {"startOffset": 17777, "endOffset": 17924, "count": 0}], "isBlockCoverage": true}, {"functionName": "format", "ranges": [{"startOffset": 19849, "endOffset": 22226, "count": 4}, {"startOffset": 19911, "endOffset": 19985, "count": 0}, {"startOffset": 20018, "endOffset": 20023, "count": 0}, {"startOffset": 20056, "endOffset": 20061, "count": 0}, {"startOffset": 20180, "endOffset": 20423, "count": 0}, {"startOffset": 20451, "endOffset": 20484, "count": 0}, {"startOffset": 20486, "endOffset": 20538, "count": 0}, {"startOffset": 20577, "endOffset": 20593, "count": 0}, {"startOffset": 20681, "endOffset": 20697, "count": 0}, {"startOffset": 20786, "endOffset": 21192, "count": 88}, {"startOffset": 20832, "endOffset": 20999, "count": 0}, {"startOffset": 21006, "endOffset": 21182, "count": 0}, {"startOffset": 21212, "endOffset": 21349, "count": 0}, {"startOffset": 21480, "endOffset": 21512, "count": 0}, {"startOffset": 21537, "endOffset": 21544, "count": 0}, {"startOffset": 21625, "endOffset": 21651, "count": 0}, {"startOffset": 21683, "endOffset": 21965, "count": 0}, {"startOffset": 22023, "endOffset": 22058, "count": 0}, {"startOffset": 22064, "endOffset": 22082, "count": 0}, {"startOffset": 22096, "endOffset": 22142, "count": 0}, {"startOffset": 22148, "endOffset": 22170, "count": 0}], "isBlockCoverage": true}, {"functionName": "parseHost", "ranges": [{"startOffset": 31591, "endOffset": 31864, "count": 4}, {"startOffset": 31687, "endOffset": 31828, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "65", "url": "node:internal/modules/cjs/loader", "functions": [{"functionName": "", "ranges": [{"startOffset": 4692, "endOffset": 4783, "count": 1}, {"startOffset": 4739, "endOffset": 4775, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4832, "endOffset": 4875, "count": 1}], "isBlockCoverage": true}, {"functionName": "stat", "ranges": [{"startOffset": 6287, "endOffset": 6701, "count": 499}, {"startOffset": 6385, "endOffset": 6483, "count": 303}, {"startOffset": 6461, "endOffset": 6479, "count": 71}, {"startOffset": 6483, "endOffset": 6556, "count": 428}, {"startOffset": 6556, "endOffset": 6570, "count": 232}, {"startOffset": 6572, "endOffset": 6682, "count": 97}, {"startOffset": 6682, "endOffset": 6700, "count": 428}], "isBlockCoverage": true}, {"functionName": "update<PERSON><PERSON><PERSON>n", "ranges": [{"startOffset": 7192, "endOffset": 7394, "count": 156}, {"startOffset": 7264, "endOffset": 7274, "count": 155}, {"startOffset": 7291, "endOffset": 7344, "count": 155}, {"startOffset": 7301, "endOffset": 7343, "count": 59}, {"startOffset": 7346, "endOffset": 7392, "count": 154}], "isBlockCoverage": true}, {"functionName": "reportModuleToWatchMode", "ranges": [{"startOffset": 7511, "endOffset": 7667, "count": 311}, {"startOffset": 7592, "endOffset": 7607, "count": 0}, {"startOffset": 7609, "endOffset": 7665, "count": 0}], "isBlockCoverage": true}, {"functionName": "reportModuleNotFoundToWatchMode", "ranges": [{"startOffset": 7851, "endOffset": 8090, "count": 9}, {"startOffset": 7952, "endOffset": 7967, "count": 0}, {"startOffset": 7969, "endOffset": 8088, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8037, "endOffset": 8079, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 8181, "endOffset": 8931, "count": 97}, {"startOffset": 8487, "endOffset": 8497, "count": 0}, {"startOffset": 8515, "endOffset": 8879, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10914, "endOffset": 10939, "count": 1}], "isBlockCoverage": true}, {"functionName": "initializeCJS", "ranges": [{"startOffset": 11659, "endOffset": 12270, "count": 1}], "isBlockCoverage": true}, {"functionName": "tryPackage", "ranges": [{"startOffset": 13154, "endOffset": 14433, "count": 26}, {"startOffset": 13276, "endOffset": 13357, "count": 1}, {"startOffset": 13357, "endOffset": 13451, "count": 25}, {"startOffset": 13451, "endOffset": 13495, "count": 0}, {"startOffset": 13496, "endOffset": 13563, "count": 0}, {"startOffset": 13589, "endOffset": 14414, "count": 0}, {"startOffset": 14414, "endOffset": 14432, "count": 25}], "isBlockCoverage": true}, {"functionName": "tryFile", "ranges": [{"startOffset": 14794, "endOffset": 15057, "count": 172}, {"startOffset": 14883, "endOffset": 14894, "count": 105}, {"startOffset": 14894, "endOffset": 14923, "count": 67}, {"startOffset": 14923, "endOffset": 14951, "count": 0}, {"startOffset": 14952, "endOffset": 14975, "count": 67}, {"startOffset": 14978, "endOffset": 15021, "count": 0}, {"startOffset": 15021, "endOffset": 15056, "count": 67}], "isBlockCoverage": true}, {"functionName": "tryExtensions", "ranges": [{"startOffset": 15322, "endOffset": 15542, "count": 72}, {"startOffset": 15411, "endOffset": 15524, "count": 142}, {"startOffset": 15490, "endOffset": 15520, "count": 37}, {"startOffset": 15524, "endOffset": 15541, "count": 35}], "isBlockCoverage": true}, {"functionName": "findLongestRegisteredExtension", "ranges": [{"startOffset": 15726, "endOffset": 16202, "count": 97}, {"startOffset": 16001, "endOffset": 16014, "count": 0}, {"startOffset": 16184, "endOffset": 16201, "count": 0}], "isBlockCoverage": true}, {"functionName": "trySelf<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 16325, "endOffset": 16618, "count": 134}, {"startOffset": 16377, "endOffset": 16394, "count": 1}, {"startOffset": 16394, "endOffset": 16452, "count": 133}, {"startOffset": 16452, "endOffset": 16616, "count": 0}], "isBlockCoverage": true}, {"functionName": "trySelf", "ranges": [{"startOffset": 16826, "endOffset": 17754, "count": 134}, {"startOffset": 16885, "endOffset": 16902, "count": 1}, {"startOffset": 16902, "endOffset": 17024, "count": 133}, {"startOffset": 17025, "endOffset": 17050, "count": 53}, {"startOffset": 17052, "endOffset": 17075, "count": 80}, {"startOffset": 17075, "endOffset": 17122, "count": 53}, {"startOffset": 17122, "endOffset": 17148, "count": 0}, {"startOffset": 17148, "endOffset": 17314, "count": 53}, {"startOffset": 17210, "endOffset": 17285, "count": 0}, {"startOffset": 17314, "endOffset": 17752, "count": 0}], "isBlockCoverage": true}, {"functionName": "resolveExports", "ranges": [{"startOffset": 18102, "endOffset": 18920, "count": 130}, {"startOffset": 18309, "endOffset": 18324, "count": 82}, {"startOffset": 18339, "endOffset": 18350, "count": 82}, {"startOffset": 18350, "endOffset": 18451, "count": 48}, {"startOffset": 18451, "endOffset": 18473, "count": 39}, {"startOffset": 18475, "endOffset": 18918, "count": 5}, {"startOffset": 18755, "endOffset": 18914, "count": 0}], "isBlockCoverage": true}, {"functionName": "getDefaultExtensions", "ranges": [{"startOffset": 18982, "endOffset": 19477, "count": 63}, {"startOffset": 19125, "endOffset": 19153, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19401, "endOffset": 19473, "count": 252}, {"startOffset": 19425, "endOffset": 19473, "count": 63}], "isBlockCoverage": true}, {"functionName": "Module._findPath", "ranges": [{"startOffset": 19770, "endOffset": 23329, "count": 135}, {"startOffset": 19880, "endOffset": 19903, "count": 4}, {"startOffset": 19903, "endOffset": 19966, "count": 131}, {"startOffset": 19943, "endOffset": 19966, "count": 0}, {"startOffset": 20099, "endOffset": 20122, "count": 11}, {"startOffset": 20122, "endOffset": 20705, "count": 124}, {"startOffset": 20344, "endOffset": 20698, "count": 0}, {"startOffset": 20760, "endOffset": 20914, "count": 82}, {"startOffset": 20877, "endOffset": 20910, "count": 19}, {"startOffset": 20914, "endOffset": 20976, "count": 124}, {"startOffset": 20976, "endOffset": 23310, "count": 221}, {"startOffset": 21117, "endOffset": 21127, "count": 202}, {"startOffset": 21128, "endOffset": 21224, "count": 199}, {"startOffset": 21162, "endOffset": 21200, "count": 0}, {"startOffset": 21231, "endOffset": 21254, "count": 88}, {"startOffset": 21254, "endOffset": 21282, "count": 133}, {"startOffset": 21282, "endOffset": 21422, "count": 130}, {"startOffset": 21375, "endOffset": 21416, "count": 5}, {"startOffset": 21422, "endOffset": 22785, "count": 128}, {"startOffset": 21574, "endOffset": 22558, "count": 57}, {"startOffset": 21607, "endOffset": 21796, "count": 56}, {"startOffset": 21662, "endOffset": 21722, "count": 0}, {"startOffset": 21796, "endOffset": 22550, "count": 1}, {"startOffset": 21850, "endOffset": 22490, "count": 0}, {"startOffset": 22581, "endOffset": 22779, "count": 71}, {"startOffset": 22661, "endOffset": 22713, "count": 63}, {"startOffset": 22785, "endOffset": 22805, "count": 128}, {"startOffset": 22805, "endOffset": 22816, "count": 35}, {"startOffset": 22818, "endOffset": 23036, "count": 26}, {"startOffset": 22920, "endOffset": 22968, "count": 0}, {"startOffset": 23036, "endOffset": 23056, "count": 128}, {"startOffset": 23056, "endOffset": 23132, "count": 119}, {"startOffset": 23132, "endOffset": 23310, "count": 9}, {"startOffset": 23310, "endOffset": 23328, "count": 0}], "isBlockCoverage": true}, {"functionName": "Module._nodeModulePaths", "ranges": [{"startOffset": 25406, "endOffset": 26594, "count": 97}, {"startOffset": 25678, "endOffset": 25717, "count": 0}, {"startOffset": 26038, "endOffset": 26472, "count": 4603}, {"startOffset": 26134, "endOffset": 26342, "count": 642}, {"startOffset": 26161, "endOffset": 26301, "count": 491}, {"startOffset": 26342, "endOffset": 26466, "count": 3961}, {"startOffset": 26362, "endOffset": 26466, "count": 2349}, {"startOffset": 26397, "endOffset": 26423, "count": 1858}, {"startOffset": 26423, "endOffset": 26458, "count": 491}], "isBlockCoverage": true}, {"functionName": "Module._resolveLookupPaths", "ranges": [{"startOffset": 26729, "endOffset": 28016, "count": 134}, {"startOffset": 26809, "endOffset": 26875, "count": 0}, {"startOffset": 26960, "endOffset": 27159, "count": 87}, {"startOffset": 27040, "endOffset": 27090, "count": 64}, {"startOffset": 27091, "endOffset": 27158, "count": 0}, {"startOffset": 27161, "endOffset": 27488, "count": 47}, {"startOffset": 27221, "endOffset": 27236, "count": 46}, {"startOffset": 27238, "endOffset": 27348, "count": 46}, {"startOffset": 27348, "endOffset": 27388, "count": 1}, {"startOffset": 27477, "endOffset": 27483, "count": 0}, {"startOffset": 27488, "endOffset": 27556, "count": 87}, {"startOffset": 27557, "endOffset": 27576, "count": 87}, {"startOffset": 27578, "endOffset": 27827, "count": 0}, {"startOffset": 27827, "endOffset": 28015, "count": 87}], "isBlockCoverage": true}, {"functionName": "getExportsForCircularRequire", "ranges": [{"startOffset": 29472, "endOffset": 30472, "count": 1}, {"startOffset": 29591, "endOffset": 29632, "count": 0}, {"startOffset": 29634, "endOffset": 29874, "count": 0}, {"startOffset": 29997, "endOffset": 30290, "count": 0}, {"startOffset": 30292, "endOffset": 30444, "count": 0}], "isBlockCoverage": true}, {"functionName": "Module._load", "ranges": [{"startOffset": 31138, "endOffset": 35016, "count": 215}, {"startOffset": 31221, "endOffset": 32049, "count": 214}, {"startOffset": 31680, "endOffset": 32045, "count": 23}, {"startOffset": 31860, "endOffset": 31932, "count": 0}, {"startOffset": 31977, "endOffset": 32045, "count": 0}, {"startOffset": 32049, "endOffset": 32102, "count": 192}, {"startOffset": 32102, "endOffset": 32374, "count": 36}, {"startOffset": 32231, "endOffset": 32291, "count": 0}, {"startOffset": 32374, "endOffset": 32527, "count": 156}, {"startOffset": 32527, "endOffset": 33453, "count": 36}, {"startOffset": 32606, "endOffset": 32648, "count": 35}, {"startOffset": 32648, "endOffset": 32925, "count": 1}, {"startOffset": 32925, "endOffset": 33453, "count": 0}, {"startOffset": 33453, "endOffset": 33513, "count": 120}, {"startOffset": 33513, "endOffset": 33596, "count": 23}, {"startOffset": 33596, "endOffset": 33726, "count": 97}, {"startOffset": 33750, "endOffset": 34127, "count": 97}, {"startOffset": 33768, "endOffset": 33953, "count": 1}, {"startOffset": 33953, "endOffset": 34003, "count": 96}, {"startOffset": 34127, "endOffset": 34988, "count": 97}, {"startOffset": 34331, "endOffset": 34734, "count": 0}, {"startOffset": 34916, "endOffset": 34984, "count": 0}, {"startOffset": 34988, "endOffset": 35015, "count": 97}], "isBlockCoverage": true}, {"functionName": "Module._resolveFilename", "ranges": [{"startOffset": 35478, "endOffset": 38364, "count": 157}, {"startOffset": 35575, "endOffset": 35600, "count": 23}, {"startOffset": 35600, "endOffset": 35650, "count": 134}, {"startOffset": 35650, "endOffset": 35669, "count": 0}, {"startOffset": 35671, "endOffset": 36522, "count": 0}, {"startOffset": 36522, "endOffset": 36617, "count": 134}, {"startOffset": 36617, "endOffset": 36665, "count": 0}, {"startOffset": 36667, "endOffset": 37309, "count": 0}, {"startOffset": 37309, "endOffset": 37470, "count": 134}, {"startOffset": 37470, "endOffset": 37666, "count": 0}, {"startOffset": 37666, "endOffset": 37826, "count": 134}, {"startOffset": 37826, "endOffset": 38363, "count": 0}], "isBlockCoverage": true}, {"functionName": "finalizeEsmResolution", "ranges": [{"startOffset": 38791, "endOffset": 39372, "count": 5}, {"startOffset": 38989, "endOffset": 39118, "count": 0}, {"startOffset": 39237, "endOffset": 39371, "count": 0}], "isBlockCoverage": true}, {"functionName": "Module.load", "ranges": [{"startOffset": 40021, "endOffset": 40688, "count": 97}, {"startOffset": 40356, "endOffset": 40386, "count": 0}, {"startOffset": 40388, "endOffset": 40440, "count": 0}], "isBlockCoverage": true}, {"functionName": "Module.require", "ranges": [{"startOffset": 40975, "endOffset": 41274, "count": 214}, {"startOffset": 41035, "endOffset": 41153, "count": 0}], "isBlockCoverage": true}, {"functionName": "wrapSafe", "ranges": [{"startOffset": 46694, "endOffset": 48701, "count": 95}, {"startOffset": 46987, "endOffset": 47859, "count": 0}, {"startOffset": 47924, "endOffset": 48382, "count": 88}, {"startOffset": 47970, "endOffset": 48132, "count": 1}, {"startOffset": 48132, "endOffset": 48378, "count": 87}, {"startOffset": 48574, "endOffset": 48681, "count": 6}], "isBlockCoverage": true}, {"functionName": "Module._compile", "ranges": [{"startOffset": 49102, "endOffset": 50633, "count": 95}, {"startOffset": 49201, "endOffset": 49211, "count": 0}, {"startOffset": 49229, "endOffset": 49382, "count": 0}, {"startOffset": 49567, "endOffset": 49599, "count": 0}, {"startOffset": 49632, "endOffset": 49805, "count": 0}, {"startOffset": 50029, "endOffset": 50059, "count": 1}, {"startOffset": 50150, "endOffset": 50184, "count": 1}, {"startOffset": 50186, "endOffset": 50397, "count": 0}, {"startOffset": 50593, "endOffset": 50614, "count": 1}], "isBlockCoverage": true}, {"functionName": "getMaybeCachedSource", "ranges": [{"startOffset": 50887, "endOffset": 51292, "count": 95}, {"startOffset": 51050, "endOffset": 51125, "count": 0}], "isBlockCoverage": true}, {"functionName": "Module._extensions..js", "ranges": [{"startOffset": 51471, "endOffset": 53565, "count": 95}, {"startOffset": 51745, "endOffset": 51767, "count": 0}, {"startOffset": 51863, "endOffset": 53354, "count": 0}, {"startOffset": 53395, "endOffset": 53429, "count": 7}, {"startOffset": 53433, "endOffset": 53516, "count": 0}], "isBlockCoverage": true}, {"functionName": "Module._extensions..json", "ranges": [{"startOffset": 53747, "endOffset": 54153, "count": 2}, {"startOffset": 53857, "endOffset": 53867, "count": 0}, {"startOffset": 53885, "endOffset": 53987, "count": 0}, {"startOffset": 54070, "endOffset": 54151, "count": 0}], "isBlockCoverage": true}, {"functionName": "isRelative", "ranges": [{"startOffset": 56411, "endOffset": 56752, "count": 124}, {"startOffset": 56494, "endOffset": 56511, "count": 42}, {"startOffset": 56511, "endOffset": 56556, "count": 82}, {"startOffset": 56557, "endOffset": 56599, "count": 82}, {"startOffset": 56600, "endOffset": 56643, "count": 19}, {"startOffset": 56644, "endOffset": 56749, "count": 0}], "isBlockCoverage": true}, {"functionName": "Module._initPaths", "ranges": [{"startOffset": 56872, "endOffset": 57837, "count": 1}, {"startOffset": 56913, "endOffset": 56938, "count": 0}, {"startOffset": 56990, "endOffset": 57013, "count": 0}, {"startOffset": 57221, "endOffset": 57263, "count": 0}, {"startOffset": 57562, "endOffset": 57704, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "66", "url": "node:internal/modules/package_json_reader", "functions": [{"functionName": "read", "ranges": [{"startOffset": 1112, "endOffset": 3862, "count": 599}, {"startOffset": 1208, "endOffset": 1245, "count": 542}, {"startOffset": 1245, "endOffset": 1922, "count": 57}, {"startOffset": 1922, "endOffset": 1931, "count": 0}, {"startOffset": 1932, "endOffset": 1946, "count": 0}, {"startOffset": 1947, "endOffset": 1969, "count": 57}, {"startOffset": 1993, "endOffset": 3812, "count": 27}, {"startOffset": 2061, "endOffset": 2657, "count": 0}, {"startOffset": 2815, "endOffset": 2849, "count": 24}, {"startOffset": 2851, "endOffset": 2891, "count": 24}, {"startOffset": 2947, "endOffset": 2981, "count": 23}, {"startOffset": 2983, "endOffset": 3023, "count": 23}, {"startOffset": 3083, "endOffset": 3129, "count": 7}, {"startOffset": 3189, "endOffset": 3235, "count": 1}, {"startOffset": 3346, "endOffset": 3405, "count": 8}, {"startOffset": 3377, "endOffset": 3404, "count": 4}, {"startOffset": 3407, "endOffset": 3447, "count": 8}, {"startOffset": 3481, "endOffset": 3677, "count": 1}, {"startOffset": 3602, "endOffset": 3655, "count": 0}, {"startOffset": 3705, "endOffset": 3808, "count": 0}, {"startOffset": 3812, "endOffset": 3861, "count": 57}], "isBlockCoverage": true}, {"functionName": "readPackage", "ranges": [{"startOffset": 3930, "endOffset": 4020, "count": 570}], "isBlockCoverage": true}, {"functionName": "readPackageScope", "ranges": [{"startOffset": 4237, "endOffset": 5057, "count": 229}, {"startOffset": 4424, "endOffset": 4994, "count": 496}, {"startOffset": 4673, "endOffset": 4719, "count": 0}, {"startOffset": 4721, "endOffset": 4748, "count": 0}, {"startOffset": 4815, "endOffset": 4842, "count": 0}, {"startOffset": 4913, "endOffset": 4990, "count": 229}, {"startOffset": 4994, "endOffset": 5056, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "67", "url": "node:internal/modules/esm/utils", "functions": [{"functionName": "getDefaultConditions", "ranges": [{"startOffset": 1284, "endOffset": 1390, "count": 7}], "isBlockCoverage": true}, {"functionName": "getDefaultConditionsSet", "ranges": [{"startOffset": 1520, "endOffset": 1635, "count": 6}], "isBlockCoverage": true}, {"functionName": "initializeDefaultConditions", "ranges": [{"startOffset": 1785, "endOffset": 2290, "count": 1}, {"startOffset": 1968, "endOffset": 1972, "count": 0}, {"startOffset": 2085, "endOffset": 2089, "count": 0}], "isBlockCoverage": true}, {"functionName": "getConditionsSet", "ranges": [{"startOffset": 2360, "endOffset": 2719, "count": 6}, {"startOffset": 2473, "endOffset": 2681, "count": 0}], "isBlockCoverage": true}, {"functionName": "defaultImportModuleDynamicallyForScript", "ranges": [{"startOffset": 7976, "endOffset": 8284, "count": 2}], "isBlockCoverage": true}, {"functionName": "importModuleDynamicallyCallback", "ranges": [{"startOffset": 8917, "endOffset": 10394, "count": 2}, {"startOffset": 9212, "endOffset": 9373, "count": 0}, {"startOffset": 9692, "endOffset": 10393, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeESM", "ranges": [{"startOffset": 10706, "endOffset": 11092, "count": 1}], "isBlockCoverage": true}, {"functionName": "forceDefaultLoader", "ranges": [{"startOffset": 11220, "endOffset": 11283, "count": 1}], "isBlockCoverage": true}, {"functionName": "compileSourceTextModule", "ranges": [{"startOffset": 12924, "endOffset": 13385, "count": 5}, {"startOffset": 13064, "endOffset": 13075, "count": 0}, {"startOffset": 13181, "endOffset": 13203, "count": 0}, {"startOffset": 13282, "endOffset": 13368, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "68", "url": "node:internal/process/pre_execution", "functions": [{"functionName": "prepareMainThreadExecution", "ranges": [{"startOffset": 1060, "endOffset": 1244, "count": 1}], "isBlockCoverage": true}, {"functionName": "prepareExecution", "ranges": [{"startOffset": 2474, "endOffset": 4718, "count": 1}, {"startOffset": 3485, "endOffset": 3589, "count": 0}, {"startOffset": 4477, "endOffset": 4639, "count": 0}], "isBlockCoverage": true}, {"functionName": "setupSymbolDisposePolyfill", "ranges": [{"startOffset": 4720, "endOffset": 5455, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupUserModules", "ranges": [{"startOffset": 5457, "endOffset": 6205, "count": 1}, {"startOffset": 5827, "endOffset": 5858, "count": 0}], "isBlockCoverage": true}, {"functionName": "refreshRuntimeOptions", "ranges": [{"startOffset": 6207, "endOffset": 6263, "count": 1}], "isBlockCoverage": true}, {"functionName": "patchProcessObject", "ranges": [{"startOffset": 6690, "endOffset": 9027, "count": 1}, {"startOffset": 7675, "endOffset": 7727, "count": 0}], "isBlockCoverage": true}, {"functionName": "addReadOnlyProcessAlias", "ranges": [{"startOffset": 9029, "endOffset": 9310, "count": 13}, {"startOffset": 9150, "endOffset": 9308, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupWarningHandler", "ranges": [{"startOffset": 9312, "endOffset": 9832, "count": 1}, {"startOffset": 9687, "endOffset": 9826, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9716, "endOffset": 9818, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 9907, "endOffset": 10326, "count": 1}, {"startOffset": 9981, "endOffset": 10141, "count": 0}, {"startOffset": 10235, "endOffset": 10324, "count": 0}], "isBlockCoverage": true}, {"functionName": "setupEventsource", "ranges": [{"startOffset": 10394, "endOffset": 10519, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupWebCrypto", "ranges": [{"startOffset": 10623, "endOffset": 11575, "count": 1}, {"startOffset": 10758, "endOffset": 10775, "count": 0}, {"startOffset": 11264, "endOffset": 11573, "count": 0}], "isBlockCoverage": true}, {"functionName": "cryptoThisCheck", "ranges": [{"startOffset": 10949, "endOffset": 11125, "count": 0}], "isBlockCoverage": false}, {"functionName": "get crypto", "ranges": [{"startOffset": 11426, "endOffset": 11525, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupCodeCoverage", "ranges": [{"startOffset": 11577, "endOffset": 12098, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupCustomEvent", "ranges": [{"startOffset": 12204, "endOffset": 12481, "count": 1}, {"startOffset": 12343, "endOffset": 12360, "count": 0}], "isBlockCoverage": true}, {"functionName": "setupStacktracePrinterOnSigint", "ranges": [{"startOffset": 12483, "endOffset": 12708, "count": 1}, {"startOffset": 12585, "endOffset": 12707, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeReport", "ranges": [{"startOffset": 12710, "endOffset": 12959, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 12855, "endOffset": 12950, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupDebugEnv", "ranges": [{"startOffset": 12961, "endOffset": 13192, "count": 1}, {"startOffset": 13112, "endOffset": 13190, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeReportSignalHandlers", "ranges": [{"startOffset": 13254, "endOffset": 13442, "count": 1}, {"startOffset": 13342, "endOffset": 13440, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeHeapSnapshotSignalHandlers", "ranges": [{"startOffset": 13444, "endOffset": 14178, "count": 1}, {"startOffset": 13639, "endOffset": 14176, "count": 0}], "isBlockCoverage": true}, {"functionName": "doWriteHeapSnapshot", "ranges": [{"startOffset": 13752, "endOffset": 13906, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 14098, "endOffset": 14170, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupTraceCategoryState", "ranges": [{"startOffset": 14180, "endOffset": 14439, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupInspectorHooks", "ranges": [{"startOffset": 14441, "endOffset": 15023, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupNetworkInspection", "ranges": [{"startOffset": 15025, "endOffset": 15338, "count": 1}, {"startOffset": 15162, "endOffset": 15336, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeDeprecations", "ranges": [{"startOffset": 15533, "endOffset": 17485, "count": 1}, {"startOffset": 16245, "endOffset": 16547, "count": 16}, {"startOffset": 16290, "endOffset": 16522, "count": 0}, {"startOffset": 16864, "endOffset": 17061, "count": 0}, {"startOffset": 17089, "endOffset": 17483, "count": 0}], "isBlockCoverage": true}, {"functionName": "setupChildProcessIpcChannel", "ranges": [{"startOffset": 17487, "endOffset": 18052, "count": 1}, {"startOffset": 17563, "endOffset": 18050, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeClusterIPC", "ranges": [{"startOffset": 18054, "endOffset": 18324, "count": 1}, {"startOffset": 18141, "endOffset": 18322, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializePermission", "ranges": [{"startOffset": 18326, "endOffset": 20323, "count": 1}, {"startOffset": 18468, "endOffset": 19897, "count": 0}], "isBlockCoverage": true}, {"functionName": "binding", "ranges": [{"startOffset": 18492, "endOffset": 18579, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20155, "endOffset": 20315, "count": 6}, {"startOffset": 20233, "endOffset": 20309, "count": 0}], "isBlockCoverage": true}, {"functionName": "readPolicyFromDisk", "ranges": [{"startOffset": 20325, "endOffset": 22144, "count": 1}, {"startOffset": 20453, "endOffset": 22142, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeCJSLoader", "ranges": [{"startOffset": 22146, "endOffset": 22267, "count": 1}], "isBlockCoverage": true}, {"functionName": "initializeESMLoader", "ranges": [{"startOffset": 22269, "endOffset": 22852, "count": 1}, {"startOffset": 22605, "endOffset": 22850, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeSourceMapsHandlers", "ranges": [{"startOffset": 22854, "endOffset": 23052, "count": 1}], "isBlockCoverage": true}, {"functionName": "initializeFrozenIntrinsics", "ranges": [{"startOffset": 23054, "endOffset": 23241, "count": 1}, {"startOffset": 23139, "endOffset": 23239, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadPreloadModules", "ranges": [{"startOffset": 23348, "endOffset": 23696, "count": 1}, {"startOffset": 23542, "endOffset": 23694, "count": 0}], "isBlockCoverage": true}, {"functionName": "markBootstrapComplete", "ranges": [{"startOffset": 23698, "endOffset": 23792, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "70", "url": "node:internal/modules/run_main", "functions": [{"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 569, "endOffset": 1672, "count": 1}, {"startOffset": 743, "endOffset": 847, "count": 0}, {"startOffset": 1140, "endOffset": 1151, "count": 0}, {"startOffset": 1378, "endOffset": 1646, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldUseESMLoader", "ranges": [{"startOffset": 1833, "endOffset": 3041, "count": 1}, {"startOffset": 1939, "endOffset": 1955, "count": 0}, {"startOffset": 2429, "endOffset": 2445, "count": 0}, {"startOffset": 2561, "endOffset": 2577, "count": 0}, {"startOffset": 2640, "endOffset": 2657, "count": 0}, {"startOffset": 2881, "endOffset": 2914, "count": 0}, {"startOffset": 2919, "endOffset": 2955, "count": 0}], "isBlockCoverage": true}, {"functionName": "executeUserEntryPoint", "ranges": [{"startOffset": 5520, "endOffset": 6505, "count": 1}, {"startOffset": 6089, "endOffset": 6503, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6257, "endOffset": 6497, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "71", "url": "node:internal/dns/utils", "functions": [{"functionName": "initializeDns", "ranges": [{"startOffset": 5468, "endOffset": 5949, "count": 1}, {"startOffset": 5611, "endOffset": 5816, "count": 0}, {"startOffset": 5864, "endOffset": 5948, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5889, "endOffset": 5945, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "73", "url": "node:internal/bootstrap/switches/does_own_process_state", "functions": [{"functionName": "wrappedCwd", "ranges": [{"startOffset": 3781, "endOffset": 3884, "count": 2}, {"startOffset": 3833, "endOffset": 3862, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "80", "url": "node:internal/main/run_main_module", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1182, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "81", "url": "file:///usr/lib/node_modules/npm/bin/npm-cli.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 54, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "82", "url": "file:///usr/lib/node_modules/npm/lib/cli.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 407, "count": 1}, {"startOffset": 117, "endOffset": 147, "count": 0}, {"startOffset": 150, "endOffset": 190, "count": 0}], "isBlockCoverage": true}, {"functionName": "module.exports", "ranges": [{"startOffset": 344, "endOffset": 406, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 382, "endOffset": 405, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "83", "url": "node:module", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 397, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "84", "url": "node:internal/modules/esm/loader", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 35412, "count": 1}], "isBlockCoverage": false}, {"functionName": "newResolveCache", "ranges": [{"startOffset": 1594, "endOffset": 1724, "count": 1}], "isBlockCoverage": true}, {"functionName": "newLoadCache", "ranges": [{"startOffset": 1878, "endOffset": 1999, "count": 1}], "isBlockCoverage": true}, {"functionName": "getTranslators", "ranges": [{"startOffset": 2161, "endOffset": 2283, "count": 9}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3037, "endOffset": 28087, "count": 1}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 4147, "endOffset": 4340, "count": 1}, {"startOffset": 4235, "endOffset": 4292, "count": 0}], "isBlockCoverage": true}, {"functionName": "setCustomizations", "ranges": [{"startOffset": 5896, "endOffset": 6140, "count": 1}, {"startOffset": 5999, "endOffset": 6081, "count": 0}], "isBlockCoverage": true}, {"functionName": "eval", "ranges": [{"startOffset": 6144, "endOffset": 6567, "count": 0}], "isBlockCoverage": false}, {"functionName": "getModuleJobForImport", "ranges": [{"startOffset": 7195, "endOffset": 7447, "count": 12}], "isBlockCoverage": true}, {"functionName": "getModuleJobForRequireInImportedCJS", "ranges": [{"startOffset": 7948, "endOffset": 8205, "count": 0}], "isBlockCoverage": false}, {"functionName": "#getJobFromResolveResult", "ranges": [{"startOffset": 8787, "endOffset": 9263, "count": 12}, {"startOffset": 9123, "endOffset": 9242, "count": 8}], "isBlockCoverage": true}, {"functionName": "importSyncForRequire", "ranges": [{"startOffset": 9835, "endOffset": 11887, "count": 0}], "isBlockCoverage": false}, {"functionName": "getModuleJobForRequire", "ranges": [{"startOffset": 12311, "endOffset": 15766, "count": 0}], "isBlockCoverage": false}, {"functionName": "#translate", "ranges": [{"startOffset": 16336, "endOffset": 16636, "count": 8}, {"startOffset": 16495, "endOffset": 16558, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadAndTranslateForRequireInImportedCJS", "ranges": [{"startOffset": 17053, "endOffset": 17973, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadAndTranslate", "ranges": [{"startOffset": 18335, "endOffset": 18533, "count": 8}], "isBlockCoverage": true}, {"functionName": "#createModuleJob", "ranges": [{"startOffset": 19345, "endOffset": 20322, "count": 8}, {"startOffset": 19594, "endOffset": 19699, "count": 0}, {"startOffset": 19827, "endOffset": 19867, "count": 0}, {"startOffset": 19922, "endOffset": 19937, "count": 0}, {"startOffset": 19939, "endOffset": 19993, "count": 0}], "isBlockCoverage": true}, {"functionName": "import", "ranges": [{"startOffset": 20784, "endOffset": 21018, "count": 2}], "isBlockCoverage": true}, {"functionName": "register", "ranges": [{"startOffset": 21084, "endOffset": 21631, "count": 0}], "isBlockCoverage": false}, {"functionName": "resolve", "ranges": [{"startOffset": 22357, "endOffset": 22678, "count": 12}, {"startOffset": 22469, "endOffset": 22595, "count": 0}], "isBlockCoverage": true}, {"functionName": "#cachedDefaultResolve", "ranges": [{"startOffset": 23040, "endOffset": 23487, "count": 12}, {"startOffset": 23291, "endOffset": 23325, "count": 0}], "isBlockCoverage": true}, {"functionName": "#resolveAndMaybeBlockOnLoaderThread", "ranges": [{"startOffset": 23912, "endOffset": 24209, "count": 0}], "isBlockCoverage": false}, {"functionName": "resolveSync", "ranges": [{"startOffset": 24920, "endOffset": 25101, "count": 0}], "isBlockCoverage": false}, {"functionName": "defaultResolve", "ranges": [{"startOffset": 25313, "endOffset": 25653, "count": 12}], "isBlockCoverage": true}, {"functionName": "load", "ranges": [{"startOffset": 25968, "endOffset": 26199, "count": 8}, {"startOffset": 26025, "endOffset": 26086, "count": 0}], "isBlockCoverage": true}, {"functionName": "#loadAndMaybeBlockOnLoaderThread", "ranges": [{"startOffset": 26559, "endOffset": 26827, "count": 0}], "isBlockCoverage": false}, {"functionName": "#loadSync", "ranges": [{"startOffset": 27371, "endOffset": 27464, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateLoadResult", "ranges": [{"startOffset": 27468, "endOffset": 27619, "count": 8}, {"startOffset": 27526, "endOffset": 27615, "count": 0}], "isBlockCoverage": true}, {"functionName": "importMetaInitialize", "ranges": [{"startOffset": 27623, "endOffset": 27958, "count": 0}], "isBlockCoverage": false}, {"functionName": "forceLoadHooks", "ranges": [{"startOffset": 28019, "endOffset": 28085, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 28141, "endOffset": 30768, "count": 0}], "isBlockCoverage": false}, {"functionName": "createModuleLoader", "ranges": [{"startOffset": 31152, "endOffset": 32814, "count": 1}, {"startOffset": 31597, "endOffset": 32764, "count": 0}], "isBlockCoverage": true}, {"functionName": "readableURIEncode", "ranges": [{"startOffset": 31672, "endOffset": 32167, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 32487, "endOffset": 32575, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHooksProxy", "ranges": [{"startOffset": 32927, "endOffset": 33103, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOrInitializeCascadedLoader", "ranges": [{"startOffset": 33605, "endOffset": 33717, "count": 2}], "isBlockCoverage": true}, {"functionName": "register", "ranges": [{"startOffset": 34959, "endOffset": 35304, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "85", "url": "node:internal/modules/esm/assert", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3876, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1326, "endOffset": 1364, "count": 5}], "isBlockCoverage": true}, {"functionName": "validateAttributes", "ranges": [{"startOffset": 1785, "endOffset": 3160, "count": 8}, {"startOffset": 1981, "endOffset": 2108, "count": 0}, {"startOffset": 2180, "endOffset": 2325, "count": 0}, {"startOffset": 2590, "endOffset": 2650, "count": 0}, {"startOffset": 2656, "endOffset": 2764, "count": 0}, {"startOffset": 2770, "endOffset": 3154, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleInvalidType", "ranges": [{"startOffset": 3392, "endOffset": 3806, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "86", "url": "node:internal/source_map/source_map", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12184, "count": 1}], "isBlockCoverage": false}, {"functionName": "StringCharIterator", "ranges": [{"startOffset": 4050, "endOffset": 4126, "count": 0}], "isBlockCoverage": false}, {"functionName": "next", "ranges": [{"startOffset": 4164, "endOffset": 4242, "count": 0}], "isBlockCoverage": false}, {"functionName": "peek", "ranges": [{"startOffset": 4280, "endOffset": 4356, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasNext", "ranges": [{"startOffset": 4395, "endOffset": 4459, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4600, "endOffset": 10134, "count": 0}], "isBlockCoverage": false}, {"functionName": "isSeparator", "ranges": [{"startOffset": 10189, "endOffset": 10258, "count": 0}], "isBlockCoverage": false}, {"functionName": "decodeVLQ", "ranges": [{"startOffset": 10348, "endOffset": 11281, "count": 0}], "isBlockCoverage": false}, {"functionName": "cloneSourceMapV3", "ranges": [{"startOffset": 11348, "endOffset": 11660, "count": 0}], "isBlockCoverage": false}, {"functionName": "compareSourceMapEntry", "ranges": [{"startOffset": 11855, "endOffset": 12134, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "87", "url": "file:///usr/lib/node_modules/npm/lib/cli/validate-engines.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1930, "count": 1}], "isBlockCoverage": true}, {"functionName": "module.exports", "ranges": [{"startOffset": 549, "endOffset": 1929, "count": 1}], "isBlockCoverage": true}, {"functionName": "syntaxErrorHandler", "ranges": [{"startOffset": 1240, "endOffset": 1506, "count": 0}], "isBlockCoverage": false}, {"functionName": "off", "ranges": [{"startOffset": 1789, "endOffset": 1921, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "88", "url": "file:///usr/lib/node_modules/npm/lib/cli/entry.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2991, "count": 1}], "isBlockCoverage": true}, {"functionName": "module.exports", "ranges": [{"startOffset": 58, "endOffset": 2990, "count": 1}, {"startOffset": 1606, "endOffset": 1667, "count": 0}, {"startOffset": 1843, "endOffset": 1882, "count": 0}, {"startOffset": 1902, "endOffset": 2001, "count": 0}, {"startOffset": 2244, "endOffset": 2433, "count": 0}, {"startOffset": 2938, "endOffset": 2988, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2159, "endOffset": 2214, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2840, "endOffset": 2879, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "89", "url": "file:///usr/lib/node_modules/npm/node_modules/graceful-fs/graceful-fs.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12680, "count": 1}, {"startOffset": 529, "endOffset": 624, "count": 0}, {"startOffset": 859, "endOffset": 1061, "count": 0}, {"startOffset": 2292, "endOffset": 2430, "count": 0}, {"startOffset": 2590, "endOffset": 2606, "count": 0}, {"startOffset": 2608, "endOffset": 2667, "count": 0}], "isBlockCoverage": true}, {"functionName": "noop", "ranges": [{"startOffset": 626, "endOffset": 645, "count": 0}], "isBlockCoverage": false}, {"functionName": "publishQueue", "ranges": [{"startOffset": 647, "endOffset": 790, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 746, "endOffset": 783, "count": 3}], "isBlockCoverage": true}, {"functionName": "debug", "ranges": [{"startOffset": 927, "endOffset": 1061, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1492, "endOffset": 1899, "count": 1}], "isBlockCoverage": true}, {"functionName": "close", "ranges": [{"startOffset": 1518, "endOffset": 1797, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1930, "endOffset": 2221, "count": 1}], "isBlockCoverage": true}, {"functionName": "closeSync", "ranges": [{"startOffset": 1960, "endOffset": 2107, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2317, "endOffset": 2425, "count": 0}], "isBlockCoverage": false}, {"functionName": "patch", "ranges": [{"startOffset": 2669, "endOffset": 10035, "count": 2}, {"startOffset": 6760, "endOffset": 6877, "count": 0}], "isBlockCoverage": true}, {"functionName": "readFile", "ranges": [{"startOffset": 2948, "endOffset": 3507, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeFile", "ranges": [{"startOffset": 3572, "endOffset": 4166, "count": 0}], "isBlockCoverage": false}, {"functionName": "appendFile", "ranges": [{"startOffset": 4258, "endOffset": 4857, "count": 0}], "isBlockCoverage": false}, {"functionName": "copyFile", "ranges": [{"startOffset": 4939, "endOffset": 5516, "count": 0}], "isBlockCoverage": false}, {"functionName": "readdir", "ranges": [{"startOffset": 5617, "endOffset": 6711, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7295, "endOffset": 7338, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7349, "endOffset": 7394, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7500, "endOffset": 7544, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7555, "endOffset": 7601, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7763, "endOffset": 7810, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7821, "endOffset": 7870, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8016, "endOffset": 8064, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 8075, "endOffset": 8125, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReadStream", "ranges": [{"startOffset": 8180, "endOffset": 8400, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReadStream$open", "ranges": [{"startOffset": 8404, "endOffset": 8725, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteStream", "ranges": [{"startOffset": 8729, "endOffset": 8954, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteStream$open", "ranges": [{"startOffset": 8958, "endOffset": 9229, "count": 0}], "isBlockCoverage": false}, {"functionName": "createReadStream", "ranges": [{"startOffset": 9233, "endOffset": 9324, "count": 0}], "isBlockCoverage": false}, {"functionName": "createWriteStream", "ranges": [{"startOffset": 9328, "endOffset": 9421, "count": 0}], "isBlockCoverage": false}, {"functionName": "open", "ranges": [{"startOffset": 9466, "endOffset": 10020, "count": 0}], "isBlockCoverage": false}, {"functionName": "enqueue", "ranges": [{"startOffset": 10037, "endOffset": 10147, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetQueue", "ranges": [{"startOffset": 10396, "endOffset": 10864, "count": 1}, {"startOffset": 10497, "endOffset": 10787, "count": 0}], "isBlockCoverage": true}, {"functionName": "retry", "ranges": [{"startOffset": 10866, "endOffset": 12679, "count": 1}, {"startOffset": 11061, "endOffset": 12677, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "90", "url": "file:///usr/lib/node_modules/npm/node_modules/graceful-fs/polyfills.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10141, "count": 1}, {"startOffset": 259, "endOffset": 272, "count": 0}], "isBlockCoverage": true}, {"functionName": "process.cwd", "ranges": [{"startOffset": 163, "endOffset": 234, "count": 5}, {"startOffset": 192, "endOffset": 219, "count": 1}], "isBlockCoverage": true}, {"functionName": "process.chdir", "ranges": [{"startOffset": 416, "endOffset": 476, "count": 0}], "isBlockCoverage": false}, {"functionName": "patch", "ranges": [{"startOffset": 577, "endOffset": 10140, "count": 2}, {"startOffset": 774, "endOffset": 830, "count": 0}, {"startOffset": 832, "endOffset": 857, "count": 0}, {"startOffset": 916, "endOffset": 942, "count": 0}, {"startOffset": 2119, "endOffset": 2248, "count": 0}, {"startOffset": 2875, "endOffset": 3782, "count": 0}, {"startOffset": 3881, "endOffset": 3890, "count": 0}, {"startOffset": 4689, "endOffset": 4702, "count": 0}], "isBlockCoverage": true}, {"functionName": "fs.lchmod", "ranges": [{"startOffset": 1981, "endOffset": 2049, "count": 0}], "isBlockCoverage": false}, {"functionName": "fs.lchmodSync", "ranges": [{"startOffset": 2070, "endOffset": 2084, "count": 0}], "isBlockCoverage": false}, {"functionName": "fs.lchown", "ranges": [{"startOffset": 2137, "endOffset": 2209, "count": 0}], "isBlockCoverage": false}, {"functionName": "fs.lchownSync", "ranges": [{"startOffset": 2230, "endOffset": 2244, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2944, "endOffset": 3766, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3896, "endOffset": 4627, "count": 2}], "isBlockCoverage": true}, {"functionName": "read", "ranges": [{"startOffset": 3921, "endOffset": 4462, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4708, "endOffset": 5082, "count": 2}], "isBlockCoverage": true}, {"functionName": "fs.readSync", "ranges": [{"startOffset": 4740, "endOffset": 5081, "count": 0}], "isBlockCoverage": false}, {"functionName": "patchLchmod", "ranges": [{"startOffset": 5100, "endOffset": 6268, "count": 0}], "isBlockCoverage": false}, {"functionName": "patchLutimes", "ranges": [{"startOffset": 6272, "endOffset": 7328, "count": 0}], "isBlockCoverage": false}, {"functionName": "chmodFix", "ranges": [{"startOffset": 7332, "endOffset": 7581, "count": 6}, {"startOffset": 7374, "endOffset": 7385, "count": 2}, {"startOffset": 7385, "endOffset": 7580, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7397, "endOffset": 7577, "count": 0}], "isBlockCoverage": false}, {"functionName": "chmodFixSync", "ranges": [{"startOffset": 7585, "endOffset": 7810, "count": 6}, {"startOffset": 7631, "endOffset": 7642, "count": 2}, {"startOffset": 7642, "endOffset": 7809, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7654, "endOffset": 7806, "count": 0}], "isBlockCoverage": false}, {"functionName": "chownFix", "ranges": [{"startOffset": 7815, "endOffset": 8072, "count": 6}, {"startOffset": 7857, "endOffset": 7868, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7880, "endOffset": 8068, "count": 0}], "isBlockCoverage": false}, {"functionName": "chownFixSync", "ranges": [{"startOffset": 8076, "endOffset": 8309, "count": 6}, {"startOffset": 8122, "endOffset": 8133, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8145, "endOffset": 8305, "count": 0}], "isBlockCoverage": false}, {"functionName": "statFix", "ranges": [{"startOffset": 8313, "endOffset": 8936, "count": 6}, {"startOffset": 8354, "endOffset": 8365, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8466, "endOffset": 8932, "count": 2}, {"startOffset": 8842, "endOffset": 8884, "count": 0}], "isBlockCoverage": true}, {"functionName": "callback", "ranges": [{"startOffset": 8600, "endOffset": 8820, "count": 2}, {"startOffset": 8682, "endOffset": 8706, "count": 0}, {"startOffset": 8736, "endOffset": 8760, "count": 0}], "isBlockCoverage": true}, {"functionName": "statFixSync", "ranges": [{"startOffset": 8940, "endOffset": 9377, "count": 6}, {"startOffset": 8985, "endOffset": 8996, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9097, "endOffset": 9373, "count": 0}], "isBlockCoverage": false}, {"functionName": "chownErOk", "ranges": [{"startOffset": 9850, "endOffset": 10138, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "91", "url": "node:constants", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1713, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "92", "url": "file:///usr/lib/node_modules/npm/node_modules/graceful-fs/legacy-streams.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2655, "count": 1}], "isBlockCoverage": true}, {"functionName": "legacy", "ranges": [{"startOffset": 64, "endOffset": 2654, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "93", "url": "node:stream", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5095, "count": 1}], "isBlockCoverage": false}, {"functionName": "fn", "ranges": [{"startOffset": 2552, "endOffset": 2712, "count": 0}], "isBlockCoverage": false}, {"functionName": "fn", "ranges": [{"startOffset": 3214, "endOffset": 3352, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4400, "endOffset": 4432, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4530, "endOffset": 4571, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4664, "endOffset": 4705, "count": 0}], "isBlockCoverage": false}, {"functionName": "_uint8ArrayToBuffer", "ranges": [{"startOffset": 4886, "endOffset": 5093, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "94", "url": "node:internal/streams/operators", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10772, "count": 1}], "isBlockCoverage": false}, {"functionName": "compose", "ranges": [{"startOffset": 1019, "endOffset": 1599, "count": 0}], "isBlockCoverage": false}, {"functionName": "map", "ranges": [{"startOffset": 1601, "endOffset": 4704, "count": 0}], "isBlockCoverage": false}, {"functionName": "asIndexedPairs", "ranges": [{"startOffset": 4706, "endOffset": 5179, "count": 0}], "isBlockCoverage": false}, {"functionName": "some", "ranges": [{"startOffset": 5181, "endOffset": 5329, "count": 0}], "isBlockCoverage": false}, {"functionName": "every", "ranges": [{"startOffset": 5331, "endOffset": 5659, "count": 0}], "isBlockCoverage": false}, {"functionName": "find", "ranges": [{"startOffset": 5661, "endOffset": 5803, "count": 0}], "isBlockCoverage": false}, {"functionName": "for<PERSON>ach", "ranges": [{"startOffset": 5805, "endOffset": 6175, "count": 0}], "isBlockCoverage": false}, {"functionName": "filter", "ranges": [{"startOffset": 6177, "endOffset": 6504, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReduceAwareErrMissingArgs", "ranges": [{"startOffset": 6720, "endOffset": 6834, "count": 0}], "isBlockCoverage": false}, {"functionName": "reduce", "ranges": [{"startOffset": 6838, "endOffset": 8280, "count": 0}], "isBlockCoverage": false}, {"functionName": "toArray", "ranges": [{"startOffset": 8282, "endOffset": 8711, "count": 0}], "isBlockCoverage": false}, {"functionName": "flatMap", "ranges": [{"startOffset": 8713, "endOffset": 8907, "count": 0}], "isBlockCoverage": false}, {"functionName": "toIntegerOrInfinity", "ranges": [{"startOffset": 8909, "endOffset": 9231, "count": 0}], "isBlockCoverage": false}, {"functionName": "drop", "ranges": [{"startOffset": 9233, "endOffset": 9781, "count": 0}], "isBlockCoverage": false}, {"functionName": "take", "ranges": [{"startOffset": 9783, "endOffset": 10453, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "95", "url": "node:internal/abort_controller", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12429, "count": 1}], "isBlockCoverage": false}, {"functionName": "lazyMessageChannel", "ranges": [{"startOffset": 1609, "endOffset": 1742, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyMakeTransferable", "ranges": [{"startOffset": 1744, "endOffset": 1902, "count": 0}], "isBlockCoverage": false}, {"functionName": "customInspect", "ranges": [{"startOffset": 2372, "endOffset": 2631, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateThisAbortSignal", "ranges": [{"startOffset": 2633, "endOffset": 2758, "count": 0}], "isBlockCoverage": false}, {"functionName": "setWeakAbortSignalTimeout", "ranges": [{"startOffset": 3340, "endOffset": 3740, "count": 0}], "isBlockCoverage": false}, {"functionName": "AbortSignal", "ranges": [{"startOffset": 3784, "endOffset": 3844, "count": 0}], "isBlockCoverage": false}, {"functionName": "get aborted", "ranges": [{"startOffset": 3881, "endOffset": 3964, "count": 0}], "isBlockCoverage": false}, {"functionName": "get reason", "ranges": [{"startOffset": 3997, "endOffset": 4076, "count": 0}], "isBlockCoverage": false}, {"functionName": "throwIfAborted", "ranges": [{"startOffset": 4080, "endOffset": 4196, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4200, "endOffset": 4329, "count": 0}], "isBlockCoverage": false}, {"functionName": "abort", "ranges": [{"startOffset": 4407, "endOffset": 4551, "count": 0}], "isBlockCoverage": false}, {"functionName": "timeout", "ranges": [{"startOffset": 4629, "endOffset": 4895, "count": 0}], "isBlockCoverage": false}, {"functionName": "any", "ranges": [{"startOffset": 4982, "endOffset": 6325, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6329, "endOffset": 7097, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7101, "endOffset": 7455, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7459, "endOffset": 8066, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8070, "endOffset": 8322, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8326, "endOffset": 8774, "count": 0}], "isBlockCoverage": false}, {"functionName": "ClonedAbortSignal", "ranges": [{"startOffset": 8778, "endOffset": 8862, "count": 0}], "isBlockCoverage": false}, {"functionName": "ClonedAbortSignal.<computed>", "ranges": [{"startOffset": 8907, "endOffset": 8915, "count": 0}], "isBlockCoverage": false}, {"functionName": "createAbortSignal", "ranges": [{"startOffset": 9393, "endOffset": 9806, "count": 0}], "isBlockCoverage": false}, {"functionName": "abortSignal", "ranges": [{"startOffset": 9808, "endOffset": 10170, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 10172, "endOffset": 10828, "count": 0}], "isBlockCoverage": false}, {"functionName": "transferableAbortSignal", "ranges": [{"startOffset": 10976, "endOffset": 11168, "count": 0}], "isBlockCoverage": false}, {"functionName": "transferableAbortController", "ranges": [{"startOffset": 11240, "endOffset": 11329, "count": 0}], "isBlockCoverage": false}, {"functionName": "aborted", "ranges": [{"startOffset": 11423, "endOffset": 11980, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "96", "url": "node:internal/streams/end-of-stream", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8492, "count": 1}], "isBlockCoverage": false}, {"functionName": "isRequest", "ranges": [{"startOffset": 891, "endOffset": 986, "count": 0}], "isBlockCoverage": false}, {"functionName": "nop", "ranges": [{"startOffset": 1000, "endOffset": 1008, "count": 0}], "isBlockCoverage": false}, {"functionName": "eos", "ranges": [{"startOffset": 1011, "endOffset": 7061, "count": 0}], "isBlockCoverage": false}, {"functionName": "eosWeb", "ranges": [{"startOffset": 7063, "endOffset": 7972, "count": 0}], "isBlockCoverage": false}, {"functionName": "finished", "ranges": [{"startOffset": 7974, "endOffset": 8432, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "97", "url": "node:internal/streams/compose", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5511, "count": 1}], "isBlockCoverage": false}, {"functionName": "compose", "ranges": [{"startOffset": 592, "endOffset": 5509, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "98", "url": "node:internal/streams/pipeline", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12389, "count": 1}], "isBlockCoverage": false}, {"functionName": "destroyer", "ranges": [{"startOffset": 1056, "endOffset": 1485, "count": 0}], "isBlockCoverage": false}, {"functionName": "popCallback", "ranges": [{"startOffset": 1487, "endOffset": 1815, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeAsyncIterable", "ranges": [{"startOffset": 1817, "endOffset": 2103, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromReadable", "ranges": [{"startOffset": 2105, "endOffset": 2279, "count": 0}], "isBlockCoverage": false}, {"functionName": "pumpToNode", "ranges": [{"startOffset": 2281, "endOffset": 3325, "count": 0}], "isBlockCoverage": false}, {"functionName": "pumpToWeb", "ranges": [{"startOffset": 3327, "endOffset": 3925, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipeline", "ranges": [{"startOffset": 3927, "endOffset": 4020, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipelineImpl", "ranges": [{"startOffset": 4022, "endOffset": 10539, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipe", "ranges": [{"startOffset": 10541, "endOffset": 12342, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "99", "url": "node:internal/streams/destroy", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7354, "count": 1}], "isBlockCoverage": false}, {"functionName": "checkError", "ranges": [{"startOffset": 487, "endOffset": 798, "count": 0}], "isBlockCoverage": false}, {"functionName": "destroy", "ranges": [{"startOffset": 908, "endOffset": 1785, "count": 0}], "isBlockCoverage": false}, {"functionName": "_destroy", "ranges": [{"startOffset": 1787, "endOffset": 2411, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitErrorCloseNT", "ranges": [{"startOffset": 2413, "endOffset": 2500, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitCloseNT", "ranges": [{"startOffset": 2502, "endOffset": 2822, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitErrorNT", "ranges": [{"startOffset": 2824, "endOffset": 3171, "count": 0}], "isBlockCoverage": false}, {"functionName": "undestroy", "ranges": [{"startOffset": 3173, "endOffset": 3863, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 3865, "endOffset": 5004, "count": 0}], "isBlockCoverage": false}, {"functionName": "construct", "ranges": [{"startOffset": 5006, "endOffset": 5422, "count": 0}], "isBlockCoverage": false}, {"functionName": "constructNT", "ranges": [{"startOffset": 5424, "endOffset": 6163, "count": 0}], "isBlockCoverage": false}, {"functionName": "isRequest", "ranges": [{"startOffset": 6165, "endOffset": 6261, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitCloseLegacy", "ranges": [{"startOffset": 6263, "endOffset": 6323, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitErrorCloseLegacy", "ranges": [{"startOffset": 6325, "endOffset": 6445, "count": 0}], "isBlockCoverage": false}, {"functionName": "destroyer", "ranges": [{"startOffset": 6480, "endOffset": 7262, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "100", "url": "node:internal/streams/duplex", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6588, "count": 1}], "isBlockCoverage": false}, {"functionName": "Duplex", "ranges": [{"startOffset": 2350, "endOffset": 4313, "count": 3}, {"startOffset": 2414, "endOffset": 2441, "count": 0}, {"startOffset": 3128, "endOffset": 3264, "count": 0}, {"startOffset": 3302, "endOffset": 3477, "count": 0}, {"startOffset": 3529, "endOffset": 3555, "count": 0}, {"startOffset": 3608, "endOffset": 3636, "count": 0}, {"startOffset": 3690, "endOffset": 3720, "count": 0}, {"startOffset": 3775, "endOffset": 3807, "count": 0}, {"startOffset": 3860, "endOffset": 3888, "count": 0}, {"startOffset": 3945, "endOffset": 3981, "count": 0}, {"startOffset": 4013, "endOffset": 4050, "count": 0}, {"startOffset": 4054, "endOffset": 4096, "count": 0}, {"startOffset": 4160, "endOffset": 4311, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4194, "endOffset": 4305, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5461, "endOffset": 5673, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 5679, "endOffset": 5940, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyWebStreams", "ranges": [{"startOffset": 6014, "endOffset": 6178, "count": 0}], "isBlockCoverage": false}, {"functionName": "Duplex.fromWeb", "ranges": [{"startOffset": 6197, "endOffset": 6315, "count": 0}], "isBlockCoverage": false}, {"functionName": "Duplex.toWeb", "ranges": [{"startOffset": 6333, "endOffset": 6422, "count": 0}], "isBlockCoverage": false}, {"functionName": "Duplex.from", "ranges": [{"startOffset": 6455, "endOffset": 6586, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "101", "url": "node:internal/streams/legacy", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3251, "count": 1}], "isBlockCoverage": false}, {"functionName": "Stream", "ranges": [{"startOffset": 130, "endOffset": 178, "count": 3}], "isBlockCoverage": true}, {"functionName": "Stream.pipe", "ranges": [{"startOffset": 292, "endOffset": 2094, "count": 0}], "isBlockCoverage": false}, {"functionName": "eventNames", "ranges": [{"startOffset": 2127, "endOffset": 2393, "count": 0}], "isBlockCoverage": false}, {"functionName": "prependListener", "ranges": [{"startOffset": 2396, "endOffset": 3203, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "102", "url": "node:internal/streams/readable", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 51364, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1880, "endOffset": 1905, "count": 0}], "isBlockCoverage": false}, {"functionName": "nop", "ranges": [{"startOffset": 2867, "endOffset": 2875, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeBitMapDescriptor", "ranges": [{"startOffset": 3771, "endOffset": 3993, "count": 19}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3846, "endOffset": 3890, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 3896, "endOffset": 3985, "count": 25}, {"startOffset": 3926, "endOffset": 3946, "count": 3}, {"startOffset": 3946, "endOffset": 3979, "count": 22}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 6167, "endOffset": 6255, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6261, "endOffset": 6427, "count": 3}, {"startOffset": 6291, "endOffset": 6371, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 6504, "endOffset": 6604, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6610, "endOffset": 6840, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6909, "endOffset": 6997, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7003, "endOffset": 7169, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7239, "endOffset": 7329, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7335, "endOffset": 7504, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7573, "endOffset": 7676, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7682, "endOffset": 7949, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReadableState", "ranges": [{"startOffset": 7962, "endOffset": 9958, "count": 3}, {"startOffset": 8362, "endOffset": 8390, "count": 0}, {"startOffset": 8453, "endOffset": 8481, "count": 0}, {"startOffset": 8729, "endOffset": 8765, "count": 0}, {"startOffset": 9107, "endOffset": 9137, "count": 0}, {"startOffset": 9416, "endOffset": 9445, "count": 0}, {"startOffset": 9446, "endOffset": 9476, "count": 0}, {"startOffset": 9525, "endOffset": 9684, "count": 0}, {"startOffset": 9857, "endOffset": 9956, "count": 0}], "isBlockCoverage": true}, {"functionName": "onConstructed", "ranges": [{"startOffset": 10002, "endOffset": 10119, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable", "ranges": [{"startOffset": 10122, "endOffset": 11156, "count": 0}], "isBlockCoverage": true}, {"functionName": "Readable._destroy", "ranges": [{"startOffset": 11293, "endOffset": 11325, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.<computed>", "ranges": [{"startOffset": 11376, "endOffset": 11414, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.<computed>", "ranges": [{"startOffset": 11458, "endOffset": 11714, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.push", "ranges": [{"startOffset": 11943, "endOffset": 12212, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.unshift", "ranges": [{"startOffset": 12308, "endOffset": 12575, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkUnshiftByteMode", "ranges": [{"startOffset": 12579, "endOffset": 13645, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkUnshiftObjectMode", "ranges": [{"startOffset": 13647, "endOffset": 13887, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkUnshiftValue", "ranges": [{"startOffset": 13889, "endOffset": 14222, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkPushByteMode", "ranges": [{"startOffset": 14224, "endOffset": 15616, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkPushObjectMode", "ranges": [{"startOffset": 15618, "endOffset": 16215, "count": 0}], "isBlockCoverage": false}, {"functionName": "canPushMore", "ranges": [{"startOffset": 16217, "endOffset": 16547, "count": 0}], "isBlockCoverage": false}, {"functionName": "addChunk", "ranges": [{"startOffset": 16549, "endOffset": 17493, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.isPaused", "ranges": [{"startOffset": 17525, "endOffset": 17680, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.setEncoding", "ranges": [{"startOffset": 17744, "endOffset": 18314, "count": 0}], "isBlockCoverage": false}, {"functionName": "computeNewHighWaterMark", "ranges": [{"startOffset": 18375, "endOffset": 18726, "count": 0}], "isBlockCoverage": false}, {"functionName": "howMuchToRead", "ranges": [{"startOffset": 18839, "endOffset": 19320, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.read", "ranges": [{"startOffset": 19417, "endOffset": 24092, "count": 0}], "isBlockCoverage": false}, {"functionName": "onEofChunk", "ranges": [{"startOffset": 24095, "endOffset": 25057, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitReadable", "ranges": [{"startOffset": 25258, "endOffset": 25589, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitReadable_", "ranges": [{"startOffset": 25591, "endOffset": 26263, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeReadMore", "ranges": [{"startOffset": 26613, "endOffset": 26820, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeReadMore_", "ranges": [{"startOffset": 26822, "endOffset": 28651, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable._read", "ranges": [{"startOffset": 28922, "endOffset": 28988, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.pipe", "ranges": [{"startOffset": 29017, "endOffset": 33527, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipeOnDrain", "ranges": [{"startOffset": 33530, "endOffset": 34207, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.unpipe", "ranges": [{"startOffset": 34238, "endOffset": 34939, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.on", "ranges": [{"startOffset": 35065, "endOffset": 36074, "count": 4}, {"startOffset": 35197, "endOffset": 35626, "count": 0}, {"startOffset": 35655, "endOffset": 36057, "count": 0}], "isBlockCoverage": true}, {"functionName": "Readable.removeListener", "ranges": [{"startOffset": 36169, "endOffset": 36876, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.removeAllListeners", "ranges": [{"startOffset": 36979, "endOffset": 37573, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateReadableListening", "ranges": [{"startOffset": 37576, "endOffset": 38278, "count": 0}], "isBlockCoverage": false}, {"functionName": "nReadingNextTick", "ranges": [{"startOffset": 38280, "endOffset": 38368, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.resume", "ranges": [{"startOffset": 38522, "endOffset": 39027, "count": 0}], "isBlockCoverage": false}, {"functionName": "resume", "ranges": [{"startOffset": 39030, "endOffset": 39203, "count": 0}], "isBlockCoverage": false}, {"functionName": "resume_", "ranges": [{"startOffset": 39205, "endOffset": 39519, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.pause", "ranges": [{"startOffset": 39548, "endOffset": 39860, "count": 0}], "isBlockCoverage": false}, {"functionName": "flow", "ranges": [{"startOffset": 39863, "endOffset": 40014, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.wrap", "ranges": [{"startOffset": 40198, "endOffset": 41219, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.<computed>", "ranges": [{"startOffset": 41264, "endOffset": 41316, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.iterator", "ranges": [{"startOffset": 41349, "endOffset": 41492, "count": 0}], "isBlockCoverage": false}, {"functionName": "streamToAsyncIterator", "ranges": [{"startOffset": 41495, "endOffset": 41744, "count": 0}], "isBlockCoverage": false}, {"functionName": "createAsyncIterator", "ranges": [{"startOffset": 41746, "endOffset": 42822, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 43041, "endOffset": 43430, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 43436, "endOffset": 43566, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 43648, "endOffset": 43712, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 43794, "endOffset": 44002, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44090, "endOffset": 44156, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44237, "endOffset": 44319, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44401, "endOffset": 44461, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 44472, "endOffset": 44581, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44657, "endOffset": 44711, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44791, "endOffset": 44879, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44957, "endOffset": 45042, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 45111, "endOffset": 45195, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 45240, "endOffset": 45324, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 45395, "endOffset": 45482, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 45488, "endOffset": 45777, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 45852, "endOffset": 45940, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 46081, "endOffset": 46126, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 46206, "endOffset": 46264, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 46270, "endOffset": 46431, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromList", "ranges": [{"startOffset": 46737, "endOffset": 49189, "count": 0}], "isBlockCoverage": false}, {"functionName": "endReadable", "ranges": [{"startOffset": 49191, "endOffset": 49417, "count": 0}], "isBlockCoverage": false}, {"functionName": "endReadableNT", "ranges": [{"startOffset": 49419, "endOffset": 50312, "count": 0}], "isBlockCoverage": false}, {"functionName": "endWritableNT", "ranges": [{"startOffset": 50314, "endOffset": 50474, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.from", "ranges": [{"startOffset": 50492, "endOffset": 50561, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyWebStreams", "ranges": [{"startOffset": 50626, "endOffset": 50790, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.fromWeb", "ranges": [{"startOffset": 50811, "endOffset": 50945, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.toWeb", "ranges": [{"startOffset": 50965, "endOffset": 51099, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.wrap", "ranges": [{"startOffset": 51118, "endOffset": 51362, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "103", "url": "node:internal/streams/add-abort-signal", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1741, "count": 1}], "isBlockCoverage": false}, {"functionName": "validateAbortSignal", "ranges": [{"startOffset": 570, "endOffset": 729, "count": 0}], "isBlockCoverage": false}, {"functionName": "addAbortSignal", "ranges": [{"startOffset": 764, "endOffset": 1075, "count": 0}], "isBlockCoverage": false}, {"functionName": "module.exports.addAbortSignalNoValidate", "ranges": [{"startOffset": 1120, "endOffset": 1739, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "104", "url": "node:internal/streams/state", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1332, "count": 1}], "isBlockCoverage": false}, {"functionName": "highWaterMarkFrom", "ranges": [{"startOffset": 287, "endOffset": 454, "count": 6}, {"startOffset": 385, "endOffset": 408, "count": 0}, {"startOffset": 445, "endOffset": 451, "count": 0}], "isBlockCoverage": true}, {"functionName": "getDefaultHighWaterMark", "ranges": [{"startOffset": 456, "endOffset": 586, "count": 3}, {"startOffset": 523, "endOffset": 555, "count": 0}], "isBlockCoverage": true}, {"functionName": "setDefaultHighWaterMark", "ranges": [{"startOffset": 588, "endOffset": 799, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHighWaterMark", "ranges": [{"startOffset": 801, "endOffset": 1234, "count": 6}, {"startOffset": 948, "endOffset": 1160, "count": 3}, {"startOffset": 992, "endOffset": 1129, "count": 0}, {"startOffset": 1160, "endOffset": 1233, "count": 3}], "isBlockCoverage": true}]}, {"scriptId": "105", "url": "node:string_decoder", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5141, "count": 1}], "isBlockCoverage": false}, {"functionName": "normalizeEncoding", "ranges": [{"startOffset": 2172, "endOffset": 2422, "count": 5}, {"startOffset": 2272, "endOffset": 2405, "count": 0}], "isBlockCoverage": true}, {"functionName": "StringDecoder", "ranges": [{"startOffset": 2633, "endOffset": 2832, "count": 5}], "isBlockCoverage": true}, {"functionName": "write", "ranges": [{"startOffset": 3155, "endOffset": 3540, "count": 0}], "isBlockCoverage": false}, {"functionName": "end", "ranges": [{"startOffset": 3817, "endOffset": 4008, "count": 0}], "isBlockCoverage": false}, {"functionName": "text", "ranges": [{"startOffset": 4219, "endOffset": 4376, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4514, "endOffset": 4727, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4819, "endOffset": 4882, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4975, "endOffset": 5090, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "106", "url": "node:internal/streams/from", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4341, "count": 1}], "isBlockCoverage": false}, {"functionName": "from", "ranges": [{"startOffset": 241, "endOffset": 4316, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "107", "url": "node:internal/streams/writable", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33628, "count": 1}], "isBlockCoverage": false}, {"functionName": "nop", "ranges": [{"startOffset": 2779, "endOffset": 2796, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeBitMapDescriptor", "ranges": [{"startOffset": 3852, "endOffset": 4074, "count": 20}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3927, "endOffset": 3971, "count": 3}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 3977, "endOffset": 4066, "count": 30}, {"startOffset": 4007, "endOffset": 4027, "count": 3}, {"startOffset": 4027, "endOffset": 4060, "count": 27}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 7038, "endOffset": 7116, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7122, "endOffset": 7288, "count": 3}, {"startOffset": 7152, "endOffset": 7232, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 7358, "endOffset": 7458, "count": 9}, {"startOffset": 7409, "endOffset": 7443, "count": 0}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 7464, "endOffset": 7737, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7814, "endOffset": 7914, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7920, "endOffset": 8150, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8291, "endOffset": 8368, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 8374, "endOffset": 8538, "count": 18}, {"startOffset": 8439, "endOffset": 8482, "count": 2}, {"startOffset": 8482, "endOffset": 8532, "count": 16}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 8725, "endOffset": 8825, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 8831, "endOffset": 9028, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9098, "endOffset": 9176, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 9182, "endOffset": 9349, "count": 0}], "isBlockCoverage": false}, {"functionName": "WritableState", "ranges": [{"startOffset": 9362, "endOffset": 11664, "count": 3}, {"startOffset": 9640, "endOffset": 9668, "count": 0}, {"startOffset": 9731, "endOffset": 9759, "count": 0}, {"startOffset": 10050, "endOffset": 10086, "count": 0}, {"startOffset": 10140, "endOffset": 10171, "count": 0}, {"startOffset": 10429, "endOffset": 10459, "count": 0}, {"startOffset": 10716, "endOffset": 10722, "count": 0}, {"startOffset": 10754, "endOffset": 10783, "count": 0}, {"startOffset": 10784, "endOffset": 10814, "count": 0}, {"startOffset": 10863, "endOffset": 11072, "count": 0}], "isBlockCoverage": true}, {"functionName": "resetBuffer", "ranges": [{"startOffset": 11666, "endOffset": 11830, "count": 3}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 11868, "endOffset": 11997, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 12093, "endOffset": 12204, "count": 0}], "isBlockCoverage": false}, {"functionName": "onConstructed", "ranges": [{"startOffset": 12253, "endOffset": 12439, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable", "ranges": [{"startOffset": 12442, "endOffset": 13548, "count": 0}], "isBlockCoverage": false}, {"functionName": "value", "ranges": [{"startOffset": 13630, "endOffset": 13834, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.pipe", "ranges": [{"startOffset": 13935, "endOffset": 14003, "count": 0}], "isBlockCoverage": false}, {"functionName": "_write", "ranges": [{"startOffset": 14006, "endOffset": 15404, "count": 16}, {"startOffset": 14110, "endOffset": 14137, "count": 2}, {"startOffset": 14139, "endOffset": 14158, "count": 14}, {"startOffset": 14182, "endOffset": 14227, "count": 0}, {"startOffset": 14366, "endOffset": 14389, "count": 0}, {"startOffset": 14396, "endOffset": 14516, "count": 0}, {"startOffset": 14605, "endOffset": 14689, "count": 0}, {"startOffset": 14695, "endOffset": 15015, "count": 0}, {"startOffset": 15071, "endOffset": 15120, "count": 0}, {"startOffset": 15166, "endOffset": 15216, "count": 0}, {"startOffset": 15229, "endOffset": 15320, "count": 0}], "isBlockCoverage": true}, {"functionName": "Writable.write", "ranges": [{"startOffset": 15433, "endOffset": 15624, "count": 16}, {"startOffset": 15488, "endOffset": 15521, "count": 2}, {"startOffset": 15523, "endOffset": 15568, "count": 2}], "isBlockCoverage": true}, {"functionName": "Writable.cork", "ranges": [{"startOffset": 15653, "endOffset": 15751, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.uncork", "ranges": [{"startOffset": 15782, "endOffset": 16020, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDefaultEncoding", "ranges": [{"startOffset": 16063, "endOffset": 16388, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeOr<PERSON>uffer", "ranges": [{"startOffset": 16578, "endOffset": 17782, "count": 16}, {"startOffset": 16695, "endOffset": 16698, "count": 0}, {"startOffset": 16829, "endOffset": 17236, "count": 0}, {"startOffset": 17296, "endOffset": 17335, "count": 2}, {"startOffset": 17526, "endOffset": 17547, "count": 0}, {"startOffset": 17562, "endOffset": 17600, "count": 0}], "isBlockCoverage": true}, {"functionName": "doWrite", "ranges": [{"startOffset": 17784, "endOffset": 18221, "count": 0}], "isBlockCoverage": false}, {"functionName": "onwriteError", "ranges": [{"startOffset": 18223, "endOffset": 18608, "count": 0}], "isBlockCoverage": false}, {"functionName": "onwrite", "ranges": [{"startOffset": 18610, "endOffset": 21216, "count": 16}, {"startOffset": 18727, "endOffset": 18801, "count": 0}, {"startOffset": 18895, "endOffset": 18917, "count": 2}, {"startOffset": 18918, "endOffset": 18923, "count": 14}, {"startOffset": 19066, "endOffset": 19680, "count": 0}, {"startOffset": 19731, "endOffset": 19772, "count": 0}, {"startOffset": 19849, "endOffset": 19870, "count": 0}, {"startOffset": 20234, "endOffset": 20585, "count": 14}, {"startOffset": 20304, "endOffset": 20424, "count": 0}, {"startOffset": 20508, "endOffset": 20567, "count": 0}, {"startOffset": 20585, "endOffset": 21151, "count": 2}, {"startOffset": 20639, "endOffset": 20700, "count": 0}, {"startOffset": 20702, "endOffset": 20760, "count": 0}, {"startOffset": 21008, "endOffset": 21151, "count": 0}, {"startOffset": 21157, "endOffset": 21210, "count": 0}], "isBlockCoverage": true}, {"functionName": "afterWriteTick", "ranges": [{"startOffset": 21218, "endOffset": 21405, "count": 2}], "isBlockCoverage": true}, {"functionName": "afterWrite", "ranges": [{"startOffset": 21407, "endOffset": 21915, "count": 2}, {"startOffset": 21585, "endOffset": 21606, "count": 0}, {"startOffset": 21625, "endOffset": 21690, "count": 0}, {"startOffset": 21756, "endOffset": 21800, "count": 1}, {"startOffset": 21800, "endOffset": 21829, "count": 0}, {"startOffset": 21829, "endOffset": 21870, "count": 1}, {"startOffset": 21870, "endOffset": 21913, "count": 0}], "isBlockCoverage": true}, {"functionName": "errorBuffer", "ranges": [{"startOffset": 21987, "endOffset": 22538, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 22604, "endOffset": 24274, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable._write", "ranges": [{"startOffset": 24304, "endOffset": 24473, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.end", "ranges": [{"startOffset": 24537, "endOffset": 26177, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON>sh", "ranges": [{"startOffset": 26180, "endOffset": 26609, "count": 0}], "isBlockCoverage": false}, {"functionName": "onFinish", "ranges": [{"startOffset": 26611, "endOffset": 27243, "count": 0}], "isBlockCoverage": false}, {"functionName": "prefinish", "ranges": [{"startOffset": 27245, "endOffset": 27769, "count": 0}], "isBlockCoverage": false}, {"functionName": "finishMaybe", "ranges": [{"startOffset": 27771, "endOffset": 28270, "count": 0}], "isBlockCoverage": false}, {"functionName": "finish", "ranges": [{"startOffset": 28272, "endOffset": 28915, "count": 0}], "isBlockCoverage": false}, {"functionName": "callFinishedCallbacks", "ranges": [{"startOffset": 28917, "endOffset": 29235, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 29319, "endOffset": 29422, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 29470, "endOffset": 29576, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 29582, "endOffset": 29832, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 29879, "endOffset": 30282, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 30288, "endOffset": 30422, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 30477, "endOffset": 30595, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 30652, "endOffset": 30772, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 30825, "endOffset": 30920, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 30972, "endOffset": 31088, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31144, "endOffset": 31297, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31357, "endOffset": 31454, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31507, "endOffset": 31600, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31653, "endOffset": 31743, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31812, "endOffset": 31909, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31968, "endOffset": 32225, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.destroy", "ranges": [{"startOffset": 32303, "endOffset": 32583, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable._destroy", "ranges": [{"startOffset": 32671, "endOffset": 32703, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.<computed>", "ranges": [{"startOffset": 32754, "endOffset": 32792, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyWebStreams", "ranges": [{"startOffset": 32857, "endOffset": 33021, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.fromWeb", "ranges": [{"startOffset": 33042, "endOffset": 33176, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.toWeb", "ranges": [{"startOffset": 33196, "endOffset": 33303, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.<computed>", "ranges": [{"startOffset": 33347, "endOffset": 33626, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "108", "url": "node:stream/promises", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 917, "count": 1}], "isBlockCoverage": false}, {"functionName": "pipeline", "ranges": [{"startOffset": 318, "endOffset": 869, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "109", "url": "node:internal/streams/transform", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7123, "count": 1}], "isBlockCoverage": false}, {"functionName": "Transform", "ranges": [{"startOffset": 3920, "endOffset": 5500, "count": 0}], "isBlockCoverage": false}, {"functionName": "final", "ranges": [{"startOffset": 5502, "endOffset": 5946, "count": 0}], "isBlockCoverage": false}, {"functionName": "prefinish", "ranges": [{"startOffset": 5948, "endOffset": 6029, "count": 0}], "isBlockCoverage": false}, {"functionName": "Transform._transform", "ranges": [{"startOffset": 6101, "endOffset": 6196, "count": 0}], "isBlockCoverage": false}, {"functionName": "Transform._write", "ranges": [{"startOffset": 6228, "endOffset": 6965, "count": 0}], "isBlockCoverage": false}, {"functionName": "Transform._read", "ranges": [{"startOffset": 6996, "endOffset": 7121, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "110", "url": "node:internal/streams/passthrough", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1762, "count": 1}], "isBlockCoverage": false}, {"functionName": "PassThrough", "ranges": [{"startOffset": 1529, "endOffset": 1671, "count": 0}], "isBlockCoverage": false}, {"functionName": "PassThrough._transform", "ranges": [{"startOffset": 1708, "endOffset": 1760, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "111", "url": "node:internal/streams/duplexpair", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1373, "count": 1}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 219, "endOffset": 1136, "count": 0}], "isBlockCoverage": false}, {"functionName": "duplexPair", "ranges": [{"startOffset": 1138, "endOffset": 1343, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "112", "url": "file:///usr/lib/node_modules/npm/node_modules/graceful-fs/clone.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 496, "count": 1}, {"startOffset": 81, "endOffset": 125, "count": 0}], "isBlockCoverage": true}, {"functionName": "getPrototypeOf", "ranges": [{"startOffset": 84, "endOffset": 125, "count": 0}], "isBlockCoverage": false}, {"functionName": "clone", "ranges": [{"startOffset": 127, "endOffset": 495, "count": 1}, {"startOffset": 201, "endOffset": 211, "count": 0}, {"startOffset": 291, "endOffset": 333, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 377, "endOffset": 477, "count": 104}], "isBlockCoverage": true}]}, {"scriptId": "113", "url": "node:internal/fs/streams", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15271, "count": 1}], "isBlockCoverage": false}, {"functionName": "_construct", "ranges": [{"startOffset": 1082, "endOffset": 1985, "count": 0}], "isBlockCoverage": false}, {"functionName": "FileHandleOperations", "ranges": [{"startOffset": 2078, "endOffset": 3168, "count": 0}], "isBlockCoverage": false}, {"functionName": "close", "ranges": [{"startOffset": 3171, "endOffset": 3412, "count": 0}], "isBlockCoverage": false}, {"functionName": "_close", "ranges": [{"startOffset": 3414, "endOffset": 3536, "count": 0}], "isBlockCoverage": false}, {"functionName": "importFd", "ranges": [{"startOffset": 3538, "endOffset": 4543, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReadStream", "ranges": [{"startOffset": 4545, "endOffset": 6288, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6477, "endOffset": 6532, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6536, "endOffset": 6593, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6629, "endOffset": 6654, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReadStream._read", "ranges": [{"startOffset": 6830, "endOffset": 7961, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReadStream._destroy", "ranges": [{"startOffset": 7996, "endOffset": 8557, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReadStream.close", "ranges": [{"startOffset": 8589, "endOffset": 8675, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8755, "endOffset": 8789, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteStream", "ranges": [{"startOffset": 8818, "endOffset": 10816, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 11008, "endOffset": 11063, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 11067, "endOffset": 11124, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11161, "endOffset": 11186, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeAll", "ranges": [{"startOffset": 11337, "endOffset": 12130, "count": 0}], "isBlockCoverage": false}, {"functionName": "writevAll", "ranges": [{"startOffset": 12132, "endOffset": 12949, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteStream._write", "ranges": [{"startOffset": 12982, "endOffset": 13366, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteStream._writev", "ranges": [{"startOffset": 13401, "endOffset": 13963, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteStream._destroy", "ranges": [{"startOffset": 13999, "endOffset": 14560, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteStream.close", "ranges": [{"startOffset": 14593, "endOffset": 14975, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 15157, "endOffset": 15191, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "114", "url": "node:internal/fs/promises", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 34493, "count": 1}], "isBlockCoverage": false}, {"functionName": "lazyLoadCpPromises", "ranges": [{"startOffset": 3287, "endOffset": 3379, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyFsStreams", "ranges": [{"startOffset": 3441, "endOffset": 3524, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3553, "endOffset": 3603, "count": 1}], "isBlockCoverage": true}, {"functionName": "handleErrorFromBinding", "ranges": [{"startOffset": 3888, "endOffset": 4018, "count": 12}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4020, "endOffset": 10138, "count": 10}], "isBlockCoverage": false}, {"functionName": "FileHandle", "ranges": [{"startOffset": 4161, "endOffset": 4338, "count": 10}, {"startOffset": 4275, "endOffset": 4279, "count": 0}], "isBlockCoverage": true}, {"functionName": "getAsyncId", "ranges": [{"startOffset": 4342, "endOffset": 4399, "count": 0}], "isBlockCoverage": false}, {"functionName": "get fd", "ranges": [{"startOffset": 4403, "endOffset": 4439, "count": 20}], "isBlockCoverage": true}, {"functionName": "appendFile", "ranges": [{"startOffset": 4443, "endOffset": 4525, "count": 0}], "isBlockCoverage": false}, {"functionName": "chmod", "ranges": [{"startOffset": 4529, "endOffset": 4585, "count": 0}], "isBlockCoverage": false}, {"functionName": "chown", "ranges": [{"startOffset": 4589, "endOffset": 4653, "count": 0}], "isBlockCoverage": false}, {"functionName": "datasync", "ranges": [{"startOffset": 4657, "endOffset": 4709, "count": 0}], "isBlockCoverage": false}, {"functionName": "sync", "ranges": [{"startOffset": 4713, "endOffset": 4757, "count": 0}], "isBlockCoverage": false}, {"functionName": "read", "ranges": [{"startOffset": 4761, "endOffset": 4870, "count": 0}], "isBlockCoverage": false}, {"functionName": "readv", "ranges": [{"startOffset": 4874, "endOffset": 4955, "count": 0}], "isBlockCoverage": false}, {"functionName": "readFile", "ranges": [{"startOffset": 4959, "endOffset": 5026, "count": 0}], "isBlockCoverage": false}, {"functionName": "readLines", "ranges": [{"startOffset": 5030, "endOffset": 5173, "count": 0}], "isBlockCoverage": false}, {"functionName": "stat", "ranges": [{"startOffset": 5177, "endOffset": 5237, "count": 0}], "isBlockCoverage": false}, {"functionName": "truncate", "ranges": [{"startOffset": 5241, "endOffset": 5305, "count": 0}], "isBlockCoverage": false}, {"functionName": "utimes", "ranges": [{"startOffset": 5309, "endOffset": 5383, "count": 0}], "isBlockCoverage": false}, {"functionName": "write", "ranges": [{"startOffset": 5387, "endOffset": 5498, "count": 0}], "isBlockCoverage": false}, {"functionName": "writev", "ranges": [{"startOffset": 5502, "endOffset": 5585, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeFile", "ranges": [{"startOffset": 5589, "endOffset": 5670, "count": 0}], "isBlockCoverage": false}, {"functionName": "close", "ranges": [{"startOffset": 5682, "endOffset": 6474, "count": 10}, {"startOffset": 5716, "endOffset": 5754, "count": 0}, {"startOffset": 5785, "endOffset": 5826, "count": 0}, {"startOffset": 6052, "endOffset": 6413, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5994, "endOffset": 6036, "count": 10}], "isBlockCoverage": true}, {"functionName": "<computed>.SafePromisePrototypeFinally.<computed>", "ranges": [{"startOffset": 6137, "endOffset": 6249, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6252, "endOffset": 6397, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6479, "endOffset": 6538, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableWebStream", "ranges": [{"startOffset": 6733, "endOffset": 8419, "count": 0}], "isBlockCoverage": false}, {"functionName": "createReadStream", "ranges": [{"startOffset": 8717, "endOffset": 8868, "count": 0}], "isBlockCoverage": false}, {"functionName": "createWriteStream", "ranges": [{"startOffset": 9172, "endOffset": 9326, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9330, "endOffset": 9730, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9734, "endOffset": 9787, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9791, "endOffset": 9878, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9882, "endOffset": 9915, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9919, "endOffset": 10136, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleFdClose", "ranges": [{"startOffset": 10140, "endOffset": 10511, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10251, "endOffset": 10310, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10297, "endOffset": 10309, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10316, "endOffset": 10503, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleFdSync", "ranges": [{"startOffset": 10513, "endOffset": 10796, "count": 0}], "isBlockCoverage": false}, {"functionName": "fsCall", "ranges": [{"startOffset": 10798, "endOffset": 11258, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkAborted", "ranges": [{"startOffset": 11260, "endOffset": 11380, "count": 42}, {"startOffset": 11304, "endOffset": 11313, "count": 0}, {"startOffset": 11319, "endOffset": 11378, "count": 0}], "isBlockCoverage": true}, {"functionName": "writeFileHandle", "ranges": [{"startOffset": 11382, "endOffset": 12524, "count": 0}], "isBlockCoverage": false}, {"functionName": "readFileHandle", "ranges": [{"startOffset": 12526, "endOffset": 14927, "count": 10}, {"startOffset": 12678, "endOffset": 12708, "count": 5}, {"startOffset": 13062, "endOffset": 13100, "count": 5}, {"startOffset": 13101, "endOffset": 13107, "count": 5}, {"startOffset": 13133, "endOffset": 13181, "count": 0}, {"startOffset": 13214, "endOffset": 13252, "count": 0}, {"startOffset": 13523, "endOffset": 13595, "count": 0}, {"startOffset": 13779, "endOffset": 13783, "count": 0}, {"startOffset": 13868, "endOffset": 13935, "count": 0}, {"startOffset": 14029, "endOffset": 14040, "count": 0}, {"startOffset": 14097, "endOffset": 14157, "count": 0}, {"startOffset": 14180, "endOffset": 14358, "count": 5}, {"startOffset": 14201, "endOffset": 14215, "count": 0}, {"startOffset": 14217, "endOffset": 14327, "count": 0}, {"startOffset": 14358, "endOffset": 14433, "count": 5}, {"startOffset": 14433, "endOffset": 14586, "count": 0}, {"startOffset": 14587, "endOffset": 14601, "count": 0}, {"startOffset": 14621, "endOffset": 14921, "count": 0}], "isBlockCoverage": true}, {"functionName": "access", "ranges": [{"startOffset": 15080, "endOffset": 15318, "count": 0}], "isBlockCoverage": false}, {"functionName": "cp", "ranges": [{"startOffset": 15320, "endOffset": 15589, "count": 0}], "isBlockCoverage": false}, {"functionName": "copyFile", "ranges": [{"startOffset": 15591, "endOffset": 15973, "count": 0}], "isBlockCoverage": false}, {"functionName": "open", "ranges": [{"startOffset": 16093, "endOffset": 16481, "count": 12}, {"startOffset": 16477, "endOffset": 16480, "count": 10}], "isBlockCoverage": true}, {"functionName": "read", "ranges": [{"startOffset": 16483, "endOffset": 17924, "count": 0}], "isBlockCoverage": false}, {"functionName": "readv", "ranges": [{"startOffset": 17926, "endOffset": 18290, "count": 0}], "isBlockCoverage": false}, {"functionName": "write", "ranges": [{"startOffset": 18292, "endOffset": 19650, "count": 0}], "isBlockCoverage": false}, {"functionName": "writev", "ranges": [{"startOffset": 19652, "endOffset": 20117, "count": 0}], "isBlockCoverage": false}, {"functionName": "rename", "ranges": [{"startOffset": 20119, "endOffset": 20495, "count": 0}], "isBlockCoverage": false}, {"functionName": "truncate", "ranges": [{"startOffset": 20497, "endOffset": 20630, "count": 0}], "isBlockCoverage": false}, {"functionName": "ftrun<PERSON>", "ranges": [{"startOffset": 20632, "endOffset": 20871, "count": 0}], "isBlockCoverage": false}, {"functionName": "rm", "ranges": [{"startOffset": 20873, "endOffset": 21075, "count": 1}], "isBlockCoverage": true}, {"functionName": "rmdir", "ranges": [{"startOffset": 21077, "endOffset": 21526, "count": 0}], "isBlockCoverage": false}, {"functionName": "fdatasync", "ranges": [{"startOffset": 21528, "endOffset": 21697, "count": 0}], "isBlockCoverage": false}, {"functionName": "fsync", "ranges": [{"startOffset": 21699, "endOffset": 21860, "count": 0}], "isBlockCoverage": false}, {"functionName": "mkdir", "ranges": [{"startOffset": 21862, "endOffset": 22409, "count": 2}, {"startOffset": 21966, "endOffset": 22004, "count": 0}, {"startOffset": 22070, "endOffset": 22085, "count": 0}], "isBlockCoverage": true}, {"functionName": "readdirRecursive", "ranges": [{"startOffset": 22411, "endOffset": 24433, "count": 0}], "isBlockCoverage": false}, {"functionName": "readdir", "ranges": [{"startOffset": 24435, "endOffset": 25055, "count": 1}, {"startOffset": 24675, "endOffset": 24724, "count": 0}, {"startOffset": 25040, "endOffset": 25052, "count": 0}], "isBlockCoverage": true}, {"functionName": "readlink", "ranges": [{"startOffset": 25057, "endOffset": 25371, "count": 0}], "isBlockCoverage": false}, {"functionName": "symlink", "ranges": [{"startOffset": 25373, "endOffset": 26673, "count": 0}], "isBlockCoverage": false}, {"functionName": "fstat", "ranges": [{"startOffset": 26675, "endOffset": 26927, "count": 0}], "isBlockCoverage": false}, {"functionName": "lstat", "ranges": [{"startOffset": 26929, "endOffset": 27254, "count": 0}], "isBlockCoverage": false}, {"functionName": "stat", "ranges": [{"startOffset": 27256, "endOffset": 27578, "count": 13}, {"startOffset": 27538, "endOffset": 27577, "count": 3}], "isBlockCoverage": true}, {"functionName": "statfs", "ranges": [{"startOffset": 27580, "endOffset": 27909, "count": 0}], "isBlockCoverage": false}, {"functionName": "link", "ranges": [{"startOffset": 27911, "endOffset": 28304, "count": 0}], "isBlockCoverage": false}, {"functionName": "unlink", "ranges": [{"startOffset": 28306, "endOffset": 28524, "count": 0}], "isBlockCoverage": false}, {"functionName": "fchmod", "ranges": [{"startOffset": 28526, "endOffset": 28739, "count": 0}], "isBlockCoverage": false}, {"functionName": "chmod", "ranges": [{"startOffset": 28741, "endOffset": 29007, "count": 0}], "isBlockCoverage": false}, {"functionName": "lchmod", "ranges": [{"startOffset": 29009, "endOffset": 29237, "count": 0}], "isBlockCoverage": false}, {"functionName": "lchown", "ranges": [{"startOffset": 29239, "endOffset": 29571, "count": 0}], "isBlockCoverage": false}, {"functionName": "fchown", "ranges": [{"startOffset": 29573, "endOffset": 29850, "count": 0}], "isBlockCoverage": false}, {"functionName": "chown", "ranges": [{"startOffset": 29852, "endOffset": 30182, "count": 0}], "isBlockCoverage": false}, {"functionName": "utimes", "ranges": [{"startOffset": 30184, "endOffset": 30521, "count": 0}], "isBlockCoverage": false}, {"functionName": "futimes", "ranges": [{"startOffset": 30523, "endOffset": 30802, "count": 0}], "isBlockCoverage": false}, {"functionName": "lutimes", "ranges": [{"startOffset": 30804, "endOffset": 31146, "count": 0}], "isBlockCoverage": false}, {"functionName": "realpath", "ranges": [{"startOffset": 31148, "endOffset": 31430, "count": 0}], "isBlockCoverage": false}, {"functionName": "mkdtemp", "ranges": [{"startOffset": 31432, "endOffset": 31740, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeFile", "ranges": [{"startOffset": 31742, "endOffset": 32616, "count": 0}], "isBlockCoverage": false}, {"functionName": "isCustomIterable", "ranges": [{"startOffset": 32618, "endOffset": 32732, "count": 0}], "isBlockCoverage": false}, {"functionName": "appendFile", "ranges": [{"startOffset": 32734, "endOffset": 32975, "count": 0}], "isBlockCoverage": false}, {"functionName": "readFile", "ranges": [{"startOffset": 32977, "endOffset": 33321, "count": 12}, {"startOffset": 33094, "endOffset": 33100, "count": 0}, {"startOffset": 33141, "endOffset": 33178, "count": 0}, {"startOffset": 33256, "endOffset": 33320, "count": 10}], "isBlockCoverage": true}, {"functionName": "_watch", "ranges": [{"startOffset": 33323, "endOffset": 33982, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "115", "url": "node:internal/fs/dir", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8077, "count": 1}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON>", "ranges": [{"startOffset": 1177, "endOffset": 1993, "count": 0}], "isBlockCoverage": false}, {"functionName": "get path", "ranges": [{"startOffset": 1997, "endOffset": 2040, "count": 0}], "isBlockCoverage": false}, {"functionName": "read", "ranges": [{"startOffset": 2044, "endOffset": 2111, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2115, "endOffset": 3841, "count": 0}], "isBlockCoverage": false}, {"functionName": "processReadResult", "ranges": [{"startOffset": 3845, "endOffset": 4099, "count": 0}], "isBlockCoverage": false}, {"functionName": "readSyncRecursive", "ranges": [{"startOffset": 4103, "endOffset": 4682, "count": 0}], "isBlockCoverage": false}, {"functionName": "readSync", "ranges": [{"startOffset": 4686, "endOffset": 5662, "count": 0}], "isBlockCoverage": false}, {"functionName": "close", "ranges": [{"startOffset": 5666, "endOffset": 6355, "count": 0}], "isBlockCoverage": false}, {"functionName": "closeSync", "ranges": [{"startOffset": 6359, "endOffset": 6736, "count": 0}], "isBlockCoverage": false}, {"functionName": "entries", "ranges": [{"startOffset": 6740, "endOffset": 7003, "count": 0}], "isBlockCoverage": false}, {"functionName": "opendir", "ranges": [{"startOffset": 7183, "endOffset": 7763, "count": 0}], "isBlockCoverage": false}, {"functionName": "opendirSync", "ranges": [{"startOffset": 7765, "endOffset": 8020, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "116", "url": "node:internal/fs/watchers", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11262, "count": 1}], "isBlockCoverage": false}, {"functionName": "emitStop", "ranges": [{"startOffset": 1420, "endOffset": 1468, "count": 0}], "isBlockCoverage": false}, {"functionName": "StatWatcher", "ranges": [{"startOffset": 1470, "endOffset": 1700, "count": 0}], "isBlockCoverage": false}, {"functionName": "onchange", "ranges": [{"startOffset": 1820, "endOffset": 2177, "count": 0}], "isBlockCoverage": false}, {"functionName": "StatWatcher.<computed>", "ranges": [{"startOffset": 2565, "endOffset": 3403, "count": 0}], "isBlockCoverage": false}, {"functionName": "StatWatcher.start", "ranges": [{"startOffset": 3624, "endOffset": 3632, "count": 0}], "isBlockCoverage": false}, {"functionName": "StatWatcher.stop", "ranges": [{"startOffset": 3878, "endOffset": 4159, "count": 0}], "isBlockCoverage": false}, {"functionName": "StatWatcher.<computed>", "ranges": [{"startOffset": 4248, "endOffset": 4675, "count": 0}], "isBlockCoverage": false}, {"functionName": "StatWatcher.ref", "ranges": [{"startOffset": 4706, "endOffset": 4988, "count": 0}], "isBlockCoverage": false}, {"functionName": "StatWatcher.unref", "ranges": [{"startOffset": 5021, "endOffset": 5271, "count": 0}], "isBlockCoverage": false}, {"functionName": "FSWatcher", "ranges": [{"startOffset": 5275, "endOffset": 6247, "count": 0}], "isBlockCoverage": false}, {"functionName": "FSWatcher.<computed>", "ranges": [{"startOffset": 6784, "endOffset": 7698, "count": 0}], "isBlockCoverage": false}, {"functionName": "FSWatcher.start", "ranges": [{"startOffset": 7915, "endOffset": 7923, "count": 0}], "isBlockCoverage": false}, {"functionName": "FSWatcher.close", "ranges": [{"startOffset": 8046, "endOffset": 8383, "count": 0}], "isBlockCoverage": false}, {"functionName": "FSWatcher.ref", "ranges": [{"startOffset": 8412, "endOffset": 8481, "count": 0}], "isBlockCoverage": false}, {"functionName": "FSWatcher.unref", "ranges": [{"startOffset": 8512, "endOffset": 8583, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitCloseNT", "ranges": [{"startOffset": 8586, "endOffset": 8638, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8862, "endOffset": 8898, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 8902, "endOffset": 8943, "count": 0}], "isBlockCoverage": false}, {"functionName": "watch", "ranges": [{"startOffset": 8979, "endOffset": 11130, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "117", "url": "node:internal/fs/recursive_watch", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7216, "count": 1}], "isBlockCoverage": false}, {"functionName": "lazyLoadFsSync", "ranges": [{"startOffset": 716, "endOffset": 802, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 833, "endOffset": 7162, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "118", "url": "node:internal/readline/interface", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 39087, "count": 1}], "isBlockCoverage": false}, {"functionName": "InterfaceConstructor", "ranges": [{"startOffset": 3851, "endOffset": 9343, "count": 0}], "isBlockCoverage": false}, {"functionName": "Interface", "ranges": [{"startOffset": 9584, "endOffset": 9684, "count": 0}], "isBlockCoverage": false}, {"functionName": "get columns", "ranges": [{"startOffset": 9687, "endOffset": 9799, "count": 0}], "isBlockCoverage": false}, {"functionName": "setPrompt", "ranges": [{"startOffset": 9908, "endOffset": 9959, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPrompt", "ranges": [{"startOffset": 10053, "endOffset": 10096, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10100, "endOffset": 10291, "count": 0}], "isBlockCoverage": false}, {"functionName": "prompt", "ranges": [{"startOffset": 10430, "endOffset": 10684, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10688, "endOffset": 10995, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10999, "endOffset": 11241, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11245, "endOffset": 11332, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11336, "endOffset": 11504, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11508, "endOffset": 11705, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11709, "endOffset": 12887, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12891, "endOffset": 13896, "count": 0}], "isBlockCoverage": false}, {"functionName": "close", "ranges": [{"startOffset": 13980, "endOffset": 14151, "count": 0}], "isBlockCoverage": false}, {"functionName": "pause", "ranges": [{"startOffset": 14232, "endOffset": 14363, "count": 0}], "isBlockCoverage": false}, {"functionName": "resume", "ranges": [{"startOffset": 14455, "endOffset": 14591, "count": 0}], "isBlockCoverage": false}, {"functionName": "write", "ranges": [{"startOffset": 14862, "endOffset": 15021, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15025, "endOffset": 16759, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 16763, "endOffset": 17432, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 17436, "endOffset": 17835, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 17839, "endOffset": 20048, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20052, "endOffset": 20513, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20517, "endOffset": 20775, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20779, "endOffset": 21261, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 21265, "endOffset": 21746, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 21750, "endOffset": 22510, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 22514, "endOffset": 22941, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 22945, "endOffset": 23223, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 23227, "endOffset": 23485, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 23489, "endOffset": 23749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 23753, "endOffset": 23908, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 23912, "endOffset": 24587, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearLine", "ranges": [{"startOffset": 24591, "endOffset": 24740, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 24744, "endOffset": 24898, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 24902, "endOffset": 25089, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 25093, "endOffset": 25401, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 25405, "endOffset": 25713, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26148, "endOffset": 26782, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26786, "endOffset": 27494, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27569, "endOffset": 28509, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCursorPos", "ranges": [{"startOffset": 28686, "endOffset": 28855, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 28974, "endOffset": 29531, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 29568, "endOffset": 37818, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 38126, "endOffset": 38506, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "119", "url": "node:internal/readline/utils", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12396, "count": 1}], "isBlockCoverage": false}, {"functionName": "CSI", "ranges": [{"startOffset": 385, "endOffset": 579, "count": 7}, {"startOffset": 488, "endOffset": 563, "count": 10}, {"startOffset": 544, "endOffset": 559, "count": 3}], "isBlockCoverage": true}, {"functionName": "charLengthLeft", "ranges": [{"startOffset": 958, "endOffset": 1220, "count": 0}], "isBlockCoverage": false}, {"functionName": "charLengthAt", "ranges": [{"startOffset": 1222, "endOffset": 1484, "count": 0}], "isBlockCoverage": false}, {"functionName": "emit<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2213, "endOffset": 11840, "count": 0}], "isBlockCoverage": false}, {"functionName": "commonPrefix", "ranges": [{"startOffset": 11870, "endOffset": 12283, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "120", "url": "node:internal/readline/callbacks", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2754, "count": 1}], "isBlockCoverage": false}, {"functionName": "cursorTo", "ranges": [{"startOffset": 460, "endOffset": 1146, "count": 3}, {"startOffset": 534, "endOffset": 583, "count": 0}, {"startOffset": 616, "endOffset": 658, "count": 0}, {"startOffset": 682, "endOffset": 722, "count": 0}, {"startOffset": 745, "endOffset": 785, "count": 0}, {"startOffset": 834, "endOffset": 858, "count": 0}, {"startOffset": 861, "endOffset": 957, "count": 0}, {"startOffset": 988, "endOffset": 1023, "count": 0}, {"startOffset": 1079, "endOffset": 1104, "count": 0}], "isBlockCoverage": true}, {"functionName": "moveCursor", "ranges": [{"startOffset": 1210, "endOffset": 1722, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearLine", "ranges": [{"startOffset": 1869, "endOffset": 2272, "count": 3}, {"startOffset": 1943, "endOffset": 1992, "count": 0}, {"startOffset": 2041, "endOffset": 2137, "count": 0}, {"startOffset": 2166, "endOffset": 2189, "count": 0}, {"startOffset": 2218, "endOffset": 2230, "count": 0}], "isBlockCoverage": true}, {"functionName": "clearScreenDown", "ranges": [{"startOffset": 2349, "endOffset": 2672, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "121", "url": "file:///usr/lib/node_modules/semver/functions/satisfies.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 247, "count": 1}], "isBlockCoverage": true}, {"functionName": "satisfies", "ranges": [{"startOffset": 74, "endOffset": 219, "count": 1}, {"startOffset": 155, "endOffset": 188, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "122", "url": "file:///usr/lib/node_modules/semver/classes/range.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14938, "count": 1}], "isBlockCoverage": true}, {"functionName": "Range", "ranges": [{"startOffset": 102, "endOffset": 2112, "count": 1}, {"startOffset": 202, "endOffset": 426, "count": 0}, {"startOffset": 465, "endOffset": 622, "count": 0}, {"startOffset": 1428, "endOffset": 1498, "count": 0}, {"startOffset": 1781, "endOffset": 1817, "count": 0}, {"startOffset": 1947, "endOffset": 2062, "count": 2}, {"startOffset": 1978, "endOffset": 1992, "count": 1}, {"startOffset": 1994, "endOffset": 2052, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1131, "endOffset": 1161, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1386, "endOffset": 1399, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1725, "endOffset": 1746, "count": 2}], "isBlockCoverage": true}, {"functionName": "get range", "ranges": [{"startOffset": 2116, "endOffset": 2571, "count": 0}], "isBlockCoverage": false}, {"functionName": "format", "ranges": [{"startOffset": 2575, "endOffset": 2612, "count": 0}], "isBlockCoverage": false}, {"functionName": "toString", "ranges": [{"startOffset": 2616, "endOffset": 2655, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseRange", "ranges": [{"startOffset": 2659, "endOffset": 4942, "count": 2}, {"startOffset": 2842, "endOffset": 2868, "count": 0}, {"startOffset": 2898, "endOffset": 2911, "count": 0}, {"startOffset": 3010, "endOffset": 3037, "count": 0}, {"startOffset": 3141, "endOffset": 3165, "count": 0}, {"startOffset": 4038, "endOffset": 4277, "count": 0}, {"startOffset": 4662, "endOffset": 4765, "count": 3}, {"startOffset": 4691, "endOffset": 4722, "count": 0}, {"startOffset": 4792, "endOffset": 4811, "count": 1}, {"startOffset": 4813, "endOffset": 4846, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3852, "endOffset": 3895, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3981, "endOffset": 4020, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4144, "endOffset": 4270, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4582, "endOffset": 4624, "count": 3}], "isBlockCoverage": true}, {"functionName": "intersects", "ranges": [{"startOffset": 4946, "endOffset": 5576, "count": 0}], "isBlockCoverage": false}, {"functionName": "test", "ranges": [{"startOffset": 5644, "endOffset": 6026, "count": 1}, {"startOffset": 5679, "endOffset": 5705, "count": 0}, {"startOffset": 5818, "endOffset": 5859, "count": 0}, {"startOffset": 6005, "endOffset": 6025, "count": 0}], "isBlockCoverage": true}, {"functionName": "isNullSet", "ranges": [{"startOffset": 6524, "endOffset": 6551, "count": 5}], "isBlockCoverage": true}, {"functionName": "isAny", "ranges": [{"startOffset": 6566, "endOffset": 6585, "count": 1}], "isBlockCoverage": true}, {"functionName": "isSatisfiable", "ranges": [{"startOffset": 6707, "endOffset": 7113, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseComparator", "ranges": [{"startOffset": 7283, "endOffset": 7596, "count": 2}], "isBlockCoverage": true}, {"functionName": "isX", "ranges": [{"startOffset": 7610, "endOffset": 7661, "count": 12}], "isBlockCoverage": true}, {"functionName": "replaceTildes", "ranges": [{"startOffset": 7997, "endOffset": 8120, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8071, "endOffset": 8102, "count": 3}], "isBlockCoverage": true}, {"functionName": "replaceTilde", "ranges": [{"startOffset": 8143, "endOffset": 8817, "count": 3}, {"startOffset": 8190, "endOffset": 8208, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8248, "endOffset": 8814, "count": 0}], "isBlockCoverage": false}, {"functionName": "replaceCarets", "ranges": [{"startOffset": 9110, "endOffset": 9233, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9184, "endOffset": 9215, "count": 2}], "isBlockCoverage": true}, {"functionName": "replaceCaret", "ranges": [{"startOffset": 9256, "endOffset": 10558, "count": 2}, {"startOffset": 9335, "endOffset": 9353, "count": 0}, {"startOffset": 9406, "endOffset": 9412, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9443, "endOffset": 10555, "count": 1}, {"startOffset": 9535, "endOffset": 9557, "count": 0}, {"startOffset": 9575, "endOffset": 9628, "count": 0}, {"startOffset": 9646, "endOffset": 9804, "count": 0}, {"startOffset": 9818, "endOffset": 10170, "count": 0}, {"startOffset": 10220, "endOffset": 10424, "count": 0}], "isBlockCoverage": true}, {"functionName": "replaceXRanges", "ranges": [{"startOffset": 10583, "endOffset": 10736, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10686, "endOffset": 10718, "count": 3}], "isBlockCoverage": true}, {"functionName": "replaceXRange", "ranges": [{"startOffset": 10760, "endOffset": 12460, "count": 3}, {"startOffset": 10828, "endOffset": 10847, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10888, "endOffset": 12457, "count": 3}, {"startOffset": 11088, "endOffset": 11095, "count": 0}, {"startOffset": 11097, "endOffset": 11120, "count": 0}, {"startOffset": 11285, "endOffset": 11291, "count": 0}, {"startOffset": 11310, "endOffset": 11487, "count": 0}, {"startOffset": 11511, "endOffset": 12255, "count": 0}, {"startOffset": 12269, "endOffset": 12323, "count": 0}, {"startOffset": 12337, "endOffset": 12404, "count": 0}], "isBlockCoverage": true}, {"functionName": "replaceStars", "ranges": [{"startOffset": 12606, "endOffset": 12791, "count": 2}], "isBlockCoverage": true}, {"functionName": "replaceGTE0", "ranges": [{"startOffset": 12813, "endOffset": 12968, "count": 3}, {"startOffset": 12940, "endOffset": 12951, "count": 0}], "isBlockCoverage": true}, {"functionName": "hyphen<PERSON>eplace", "ranges": [{"startOffset": 13225, "endOffset": 13895, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13234, "endOffset": 13895, "count": 0}], "isBlockCoverage": false}, {"functionName": "testSet", "ranges": [{"startOffset": 13913, "endOffset": 14937, "count": 1}, {"startOffset": 13981, "endOffset": 14044, "count": 2}, {"startOffset": 14014, "endOffset": 14040, "count": 0}, {"startOffset": 14078, "endOffset": 14107, "count": 0}, {"startOffset": 14109, "endOffset": 14920, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "123", "url": "file:///usr/lib/node_modules/semver/internal/lrucache.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 802, "count": 1}], "isBlockCoverage": true}, {"functionName": "L<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 33, "endOffset": 98, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 102, "endOffset": 362, "count": 2}, {"startOffset": 209, "endOffset": 358, "count": 0}], "isBlockCoverage": true}, {"functionName": "delete", "ranges": [{"startOffset": 366, "endOffset": 416, "count": 2}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 420, "endOffset": 772, "count": 2}, {"startOffset": 620, "endOffset": 713, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "124", "url": "file:///usr/lib/node_modules/semver/internal/parse-options.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 338, "count": 1}], "isBlockCoverage": true}, {"functionName": "parseOptions", "ranges": [{"startOffset": 167, "endOffset": 307, "count": 13}, {"startOffset": 196, "endOffset": 222, "count": 5}, {"startOffset": 222, "endOffset": 259, "count": 8}, {"startOffset": 259, "endOffset": 287, "count": 0}, {"startOffset": 287, "endOffset": 306, "count": 8}], "isBlockCoverage": true}]}, {"scriptId": "125", "url": "file:///usr/lib/node_modules/semver/classes/comparator.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3631, "count": 1}], "isBlockCoverage": true}, {"functionName": "get ANY", "ranges": [{"startOffset": 114, "endOffset": 145, "count": 0}], "isBlockCoverage": false}, {"functionName": "Comparator", "ranges": [{"startOffset": 149, "endOffset": 696, "count": 3}, {"startOffset": 252, "endOffset": 372, "count": 0}, {"startOffset": 570, "endOffset": 599, "count": 0}], "isBlockCoverage": true}, {"functionName": "parse", "ranges": [{"startOffset": 700, "endOffset": 1196, "count": 3}, {"startOffset": 748, "endOffset": 771, "count": 0}, {"startOffset": 832, "endOffset": 896, "count": 0}, {"startOffset": 944, "endOffset": 948, "count": 0}, {"startOffset": 980, "endOffset": 1012, "count": 0}, {"startOffset": 1091, "endOffset": 1122, "count": 0}], "isBlockCoverage": true}, {"functionName": "toString", "ranges": [{"startOffset": 1200, "endOffset": 1239, "count": 0}], "isBlockCoverage": false}, {"functionName": "test", "ranges": [{"startOffset": 1243, "endOffset": 1623, "count": 2}, {"startOffset": 1367, "endOffset": 1392, "count": 0}, {"startOffset": 1431, "endOffset": 1552, "count": 0}], "isBlockCoverage": true}, {"functionName": "intersects", "ranges": [{"startOffset": 1627, "endOffset": 3337, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "126", "url": "file:///usr/lib/node_modules/semver/internal/re.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8141, "count": 1}], "isBlockCoverage": true}, {"functionName": "makeSafeRegex", "ranges": [{"startOffset": 1001, "endOffset": 1212, "count": 43}, {"startOffset": 1066, "endOffset": 1195, "count": 129}], "isBlockCoverage": true}, {"functionName": "createToken", "ranges": [{"startOffset": 1234, "endOffset": 1534, "count": 43}, {"startOffset": 1451, "endOffset": 1456, "count": 5}, {"startOffset": 1457, "endOffset": 1468, "count": 38}, {"startOffset": 1514, "endOffset": 1519, "count": 5}, {"startOffset": 1520, "endOffset": 1531, "count": 38}], "isBlockCoverage": true}]}, {"scriptId": "127", "url": "file:///usr/lib/node_modules/semver/internal/constants.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 873, "count": 1}, {"startOffset": 247, "endOffset": 293, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "128", "url": "file:///usr/lib/node_modules/semver/internal/debug.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 240, "count": 1}, {"startOffset": 105, "endOffset": 152, "count": 0}, {"startOffset": 155, "endOffset": 202, "count": 0}], "isBlockCoverage": true}, {"functionName": "debug", "ranges": [{"startOffset": 157, "endOffset": 202, "count": 0}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 207, "endOffset": 215, "count": 96}], "isBlockCoverage": true}]}, {"scriptId": "129", "url": "file:///usr/lib/node_modules/semver/functions/cmp.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 961, "count": 1}], "isBlockCoverage": true}, {"functionName": "cmp", "ranges": [{"startOffset": 195, "endOffset": 939, "count": 2}, {"startOffset": 238, "endOffset": 400, "count": 0}, {"startOffset": 406, "endOffset": 568, "count": 0}, {"startOffset": 574, "endOffset": 582, "count": 0}, {"startOffset": 587, "endOffset": 596, "count": 0}, {"startOffset": 601, "endOffset": 640, "count": 0}, {"startOffset": 646, "endOffset": 686, "count": 0}, {"startOffset": 692, "endOffset": 730, "count": 0}, {"startOffset": 736, "endOffset": 776, "count": 1}, {"startOffset": 782, "endOffset": 820, "count": 1}, {"startOffset": 826, "endOffset": 866, "count": 0}, {"startOffset": 872, "endOffset": 933, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "130", "url": "file:///usr/lib/node_modules/semver/functions/eq.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 126, "count": 1}], "isBlockCoverage": true}, {"functionName": "eq", "ranges": [{"startOffset": 62, "endOffset": 105, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "131", "url": "file:///usr/lib/node_modules/semver/functions/compare.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 170, "count": 1}], "isBlockCoverage": true}, {"functionName": "compare", "ranges": [{"startOffset": 74, "endOffset": 143, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "132", "url": "file:///usr/lib/node_modules/semver/classes/semver.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9297, "count": 1}], "isBlockCoverage": true}, {"functionName": "Se<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 325, "endOffset": 2250, "count": 9}, {"startOffset": 430, "endOffset": 633, "count": 4}, {"startOffset": 578, "endOffset": 627, "count": 0}, {"startOffset": 633, "endOffset": 773, "count": 5}, {"startOffset": 672, "endOffset": 773, "count": 0}, {"startOffset": 773, "endOffset": 812, "count": 5}, {"startOffset": 812, "endOffset": 912, "count": 0}, {"startOffset": 912, "endOffset": 1251, "count": 5}, {"startOffset": 1251, "endOffset": 1264, "count": 0}, {"startOffset": 1265, "endOffset": 1277, "count": 5}, {"startOffset": 1292, "endOffset": 1356, "count": 0}, {"startOffset": 1356, "endOffset": 1541, "count": 5}, {"startOffset": 1543, "endOffset": 1601, "count": 0}, {"startOffset": 1601, "endOffset": 1658, "count": 5}, {"startOffset": 1660, "endOffset": 1718, "count": 0}, {"startOffset": 1718, "endOffset": 1775, "count": 5}, {"startOffset": 1777, "endOffset": 1835, "count": 0}, {"startOffset": 1835, "endOffset": 1896, "count": 5}, {"startOffset": 1896, "endOffset": 1930, "count": 4}, {"startOffset": 1930, "endOffset": 2182, "count": 1}, {"startOffset": 2182, "endOffset": 2206, "count": 5}, {"startOffset": 2206, "endOffset": 2223, "count": 0}, {"startOffset": 2224, "endOffset": 2228, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1982, "endOffset": 2175, "count": 1}, {"startOffset": 2149, "endOffset": 2174, "count": 0}], "isBlockCoverage": true}, {"functionName": "format", "ranges": [{"startOffset": 2254, "endOffset": 2450, "count": 5}, {"startOffset": 2361, "endOffset": 2422, "count": 1}], "isBlockCoverage": true}, {"functionName": "toString", "ranges": [{"startOffset": 2454, "endOffset": 2495, "count": 0}], "isBlockCoverage": false}, {"functionName": "compare", "ranges": [{"startOffset": 2499, "endOffset": 2889, "count": 2}, {"startOffset": 2616, "endOffset": 2759, "count": 0}, {"startOffset": 2801, "endOffset": 2823, "count": 0}, {"startOffset": 2860, "endOffset": 2885, "count": 0}], "isBlockCoverage": true}, {"functionName": "compareMain", "ranges": [{"startOffset": 2893, "endOffset": 3184, "count": 2}, {"startOffset": 2951, "endOffset": 3004, "count": 0}, {"startOffset": 3069, "endOffset": 3121, "count": 1}, {"startOffset": 3122, "endOffset": 3174, "count": 0}], "isBlockCoverage": true}, {"functionName": "comparePre", "ranges": [{"startOffset": 3188, "endOffset": 4039, "count": 0}], "isBlockCoverage": false}, {"functionName": "compareBuild", "ranges": [{"startOffset": 4043, "endOffset": 4580, "count": 0}], "isBlockCoverage": false}, {"functionName": "inc", "ranges": [{"startOffset": 4733, "endOffset": 9269, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "133", "url": "file:///usr/lib/node_modules/semver/internal/identifiers.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 424, "count": 1}], "isBlockCoverage": true}, {"functionName": "compareIdentifiers", "ranges": [{"startOffset": 68, "endOffset": 292, "count": 3}, {"startOffset": 209, "endOffset": 212, "count": 1}, {"startOffset": 217, "endOffset": 290, "count": 2}, {"startOffset": 235, "endOffset": 239, "count": 0}, {"startOffset": 262, "endOffset": 265, "count": 0}, {"startOffset": 278, "endOffset": 282, "count": 1}, {"startOffset": 287, "endOffset": 290, "count": 1}], "isBlockCoverage": true}, {"functionName": "rcompareIdentifiers", "ranges": [{"startOffset": 322, "endOffset": 356, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "134", "url": "file:///usr/lib/node_modules/semver/functions/neq.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 128, "count": 1}], "isBlockCoverage": true}, {"functionName": "neq", "ranges": [{"startOffset": 63, "endOffset": 106, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "135", "url": "file:///usr/lib/node_modules/semver/functions/gt.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 124, "count": 1}], "isBlockCoverage": true}, {"functionName": "gt", "ranges": [{"startOffset": 62, "endOffset": 103, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "136", "url": "file:///usr/lib/node_modules/semver/functions/gte.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 127, "count": 1}], "isBlockCoverage": true}, {"functionName": "gte", "ranges": [{"startOffset": 63, "endOffset": 105, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "137", "url": "file:///usr/lib/node_modules/semver/functions/lt.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 124, "count": 1}], "isBlockCoverage": true}, {"functionName": "lt", "ranges": [{"startOffset": 62, "endOffset": 103, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "138", "url": "file:///usr/lib/node_modules/semver/functions/lte.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 127, "count": 1}], "isBlockCoverage": true}, {"functionName": "lte", "ranges": [{"startOffset": 63, "endOffset": 105, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "139", "url": "file:///usr/lib/node_modules/npm/lib/cli/exit-handler.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5270, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 135, "endOffset": 5239, "count": 1}], "isBlockCoverage": true}, {"functionName": "get #hasNpm", "ranges": [{"startOffset": 259, "endOffset": 302, "count": 2}], "isBlockCoverage": true}, {"functionName": "get #loaded", "ranges": [{"startOffset": 306, "endOffset": 357, "count": 4}], "isBlockCoverage": true}, {"functionName": "get #showExitErrorMessage", "ranges": [{"startOffset": 361, "endOffset": 528, "count": 1}, {"startOffset": 415, "endOffset": 441, "count": 0}, {"startOffset": 465, "endOffset": 490, "count": 0}], "isBlockCoverage": true}, {"functionName": "get #notLoadedOrExited", "ranges": [{"startOffset": 532, "endOffset": 605, "count": 1}, {"startOffset": 585, "endOffset": 601, "count": 0}], "isBlockCoverage": true}, {"functionName": "setNpm", "ranges": [{"startOffset": 609, "endOffset": 647, "count": 1}], "isBlockCoverage": true}, {"functionName": "ExitHandler", "ranges": [{"startOffset": 651, "endOffset": 771, "count": 1}], "isBlockCoverage": true}, {"functionName": "registerUncaughtHandlers", "ranges": [{"startOffset": 775, "endOffset": 929, "count": 1}], "isBlockCoverage": true}, {"functionName": "exit", "ranges": [{"startOffset": 933, "endOffset": 975, "count": 1}], "isBlockCoverage": true}, {"functionName": "#handleProcesExitAndReset", "ranges": [{"startOffset": 1007, "endOffset": 1492, "count": 1}], "isBlockCoverage": true}, {"functionName": "#handleProcessExit", "ranges": [{"startOffset": 1496, "endOffset": 2968, "count": 1}, {"startOffset": 1675, "endOffset": 1691, "count": 0}, {"startOffset": 1764, "endOffset": 1965, "count": 0}, {"startOffset": 1998, "endOffset": 2018, "count": 0}, {"startOffset": 2356, "endOffset": 2445, "count": 0}, {"startOffset": 2470, "endOffset": 2737, "count": 0}, {"startOffset": 2792, "endOffset": 2835, "count": 0}, {"startOffset": 2907, "endOffset": 2964, "count": 0}], "isBlockCoverage": true}, {"functionName": "#logConsoleError", "ranges": [{"startOffset": 2972, "endOffset": 3565, "count": 0}], "isBlockCoverage": false}, {"functionName": "#logNoNpmError", "ranges": [{"startOffset": 3569, "endOffset": 3897, "count": 2}, {"startOffset": 3640, "endOffset": 3896, "count": 0}], "isBlockCoverage": true}, {"functionName": "#handleExit", "ranges": [{"startOffset": 3915, "endOffset": 5237, "count": 1}, {"startOffset": 4006, "endOffset": 4103, "count": 0}, {"startOffset": 4336, "endOffset": 4527, "count": 0}, {"startOffset": 4561, "endOffset": 4576, "count": 0}, {"startOffset": 4586, "endOffset": 4593, "count": 0}, {"startOffset": 4854, "endOffset": 4864, "count": 0}, {"startOffset": 4899, "endOffset": 4902, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5146, "endOffset": 5232, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5183, "endOffset": 5231, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "140", "url": "file:///usr/lib/node_modules/npm/node_modules/proc-log/lib/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3380, "count": 1}], "isBlockCoverage": true}, {"functionName": "standard", "ranges": [{"startOffset": 296, "endOffset": 379, "count": 1}], "isBlockCoverage": true}, {"functionName": "error", "ranges": [{"startOffset": 392, "endOffset": 472, "count": 0}], "isBlockCoverage": false}, {"functionName": "buffer", "ranges": [{"startOffset": 486, "endOffset": 567, "count": 0}], "isBlockCoverage": false}, {"functionName": "flush", "ranges": [{"startOffset": 580, "endOffset": 660, "count": 3}], "isBlockCoverage": true}, {"functionName": "error", "ranges": [{"startOffset": 1103, "endOffset": 1180, "count": 0}], "isBlockCoverage": false}, {"functionName": "notice", "ranges": [{"startOffset": 1194, "endOffset": 1272, "count": 0}], "isBlockCoverage": false}, {"functionName": "warn", "ranges": [{"startOffset": 1284, "endOffset": 1360, "count": 6}], "isBlockCoverage": true}, {"functionName": "info", "ranges": [{"startOffset": 1372, "endOffset": 1448, "count": 3}], "isBlockCoverage": true}, {"functionName": "verbose", "ranges": [{"startOffset": 1463, "endOffset": 1542, "count": 10}], "isBlockCoverage": true}, {"functionName": "http", "ranges": [{"startOffset": 1554, "endOffset": 1630, "count": 0}], "isBlockCoverage": false}, {"functionName": "silly", "ranges": [{"startOffset": 1643, "endOffset": 1720, "count": 6}], "isBlockCoverage": true}, {"functionName": "timing", "ranges": [{"startOffset": 1734, "endOffset": 1812, "count": 10}], "isBlockCoverage": true}, {"functionName": "pause", "ranges": [{"startOffset": 1825, "endOffset": 1886, "count": 1}], "isBlockCoverage": true}, {"functionName": "resume", "ranges": [{"startOffset": 1900, "endOffset": 1962, "count": 2}], "isBlockCoverage": true}, {"functionName": "start", "ranges": [{"startOffset": 2098, "endOffset": 2447, "count": 10}, {"startOffset": 2279, "endOffset": 2424, "count": 8}, {"startOffset": 2322, "endOffset": 2336, "count": 7}, {"startOffset": 2338, "endOffset": 2383, "count": 7}, {"startOffset": 2383, "endOffset": 2424, "count": 1}, {"startOffset": 2424, "endOffset": 2446, "count": 2}], "isBlockCoverage": true}, {"functionName": "end", "ranges": [{"startOffset": 2168, "endOffset": 2242, "count": 9}], "isBlockCoverage": true}, {"functionName": "end", "ranges": [{"startOffset": 2458, "endOffset": 2528, "count": 1}], "isBlockCoverage": true}, {"functionName": "start", "ranges": [{"startOffset": 2699, "endOffset": 3032, "count": 1}, {"startOffset": 2864, "endOffset": 3009, "count": 0}], "isBlockCoverage": true}, {"functionName": "end", "ranges": [{"startOffset": 2758, "endOffset": 2827, "count": 1}], "isBlockCoverage": true}, {"functionName": "end", "ranges": [{"startOffset": 3043, "endOffset": 3104, "count": 0}], "isBlockCoverage": false}, {"functionName": "read", "ranges": [{"startOffset": 3116, "endOffset": 3371, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "141", "url": "file:///usr/lib/node_modules/npm/lib/utils/error-message.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15150, "count": 1}], "isBlockCoverage": true}, {"functionName": "errorMessage", "ranges": [{"startOffset": 200, "endOffset": 12905, "count": 0}], "isBlockCoverage": false}, {"functionName": "getExitCodeFromError", "ranges": [{"startOffset": 12936, "endOffset": 13079, "count": 0}], "isBlockCoverage": false}, {"functionName": "getError", "ranges": [{"startOffset": 13098, "endOffset": 15075, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "142", "url": "node:internal/modules/esm/resolve", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 46464, "count": 1}], "isBlockCoverage": false}, {"functionName": "emitTrailingSlashPatternDeprecation", "ranges": [{"startOffset": 2799, "endOffset": 3432, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitInvalidSegmentDeprecation", "ranges": [{"startOffset": 3945, "endOffset": 4687, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitLegacyIndexDeprecation", "ranges": [{"startOffset": 5124, "endOffset": 6304, "count": 0}], "isBlockCoverage": false}, {"functionName": "legacyMainResolve", "ranges": [{"startOffset": 7395, "endOffset": 8155, "count": 0}], "isBlockCoverage": false}, {"functionName": "finalizeResolution", "ranges": [{"startOffset": 8939, "endOffset": 10695, "count": 6}, {"startOffset": 9075, "endOffset": 9326, "count": 0}, {"startOffset": 9388, "endOffset": 9572, "count": 0}, {"startOffset": 9661, "endOffset": 9697, "count": 0}, {"startOffset": 9763, "endOffset": 9953, "count": 0}, {"startOffset": 9976, "endOffset": 10354, "count": 0}, {"startOffset": 10600, "endOffset": 10605, "count": 0}], "isBlockCoverage": true}, {"functionName": "importNotDefined", "ranges": [{"startOffset": 11109, "endOffset": 11320, "count": 0}], "isBlockCoverage": false}, {"functionName": "exportsNotFound", "ranges": [{"startOffset": 11716, "endOffset": 11911, "count": 0}], "isBlockCoverage": false}, {"functionName": "throwInvalidSubpath", "ranges": [{"startOffset": 12549, "endOffset": 12932, "count": 0}], "isBlockCoverage": false}, {"functionName": "invalidPackageTarget", "ranges": [{"startOffset": 13388, "endOffset": 13766, "count": 0}], "isBlockCoverage": false}, {"functionName": "resolvePackageTargetString", "ranges": [{"startOffset": 15122, "endOffset": 17861, "count": 9}, {"startOffset": 15291, "endOffset": 15302, "count": 0}, {"startOffset": 15303, "endOffset": 15339, "count": 0}, {"startOffset": 15341, "endOffset": 15425, "count": 0}, {"startOffset": 15475, "endOffset": 16018, "count": 0}, {"startOffset": 16110, "endOffset": 16720, "count": 0}, {"startOffset": 16939, "endOffset": 17023, "count": 0}, {"startOffset": 17067, "endOffset": 17860, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15793, "endOffset": 15806, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 16319, "endOffset": 16332, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 16465, "endOffset": 16478, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 17202, "endOffset": 17215, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 17442, "endOffset": 17455, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 17795, "endOffset": 17808, "count": 0}], "isBlockCoverage": false}, {"functionName": "isArrayIndex", "ranges": [{"startOffset": 18053, "endOffset": 18197, "count": 17}, {"startOffset": 18149, "endOffset": 18194, "count": 0}], "isBlockCoverage": true}, {"functionName": "resolvePackageTarget", "ranges": [{"startOffset": 18992, "endOffset": 21242, "count": 17}, {"startOffset": 19185, "endOffset": 19339, "count": 9}, {"startOffset": 19339, "endOffset": 21125, "count": 8}, {"startOffset": 19371, "endOffset": 20217, "count": 0}, {"startOffset": 20370, "endOffset": 20598, "count": 17}, {"startOffset": 20428, "endOffset": 20592, "count": 0}, {"startOffset": 20641, "endOffset": 21050, "count": 15}, {"startOffset": 20698, "endOffset": 20720, "count": 12}, {"startOffset": 20722, "endOffset": 21044, "count": 8}, {"startOffset": 20993, "endOffset": 21006, "count": 0}, {"startOffset": 21050, "endOffset": 21125, "count": 0}, {"startOffset": 21125, "endOffset": 21241, "count": 0}], "isBlockCoverage": true}, {"functionName": "isConditionalExportsMainSugar", "ranges": [{"startOffset": 21526, "endOffset": 22469, "count": 7}, {"startOffset": 21632, "endOffset": 21656, "count": 6}, {"startOffset": 21658, "endOffset": 21674, "count": 1}, {"startOffset": 21674, "endOffset": 21728, "count": 6}, {"startOffset": 21730, "endOffset": 21747, "count": 0}, {"startOffset": 21747, "endOffset": 21887, "count": 6}, {"startOffset": 21887, "endOffset": 22438, "count": 17}, {"startOffset": 21997, "endOffset": 22054, "count": 6}, {"startOffset": 22054, "endOffset": 22434, "count": 11}, {"startOffset": 22110, "endOffset": 22434, "count": 0}, {"startOffset": 22438, "endOffset": 22468, "count": 6}], "isBlockCoverage": true}, {"functionName": "packageExportsResolve", "ranges": [{"startOffset": 22966, "endOffset": 25541, "count": 7}, {"startOffset": 23174, "endOffset": 23211, "count": 2}, {"startOffset": 23613, "endOffset": 23687, "count": 0}, {"startOffset": 23718, "endOffset": 25540, "count": 0}], "isBlockCoverage": true}, {"functionName": "patternKeyCompare", "ranges": [{"startOffset": 25915, "endOffset": 26470, "count": 0}], "isBlockCoverage": false}, {"functionName": "packageImportsResolve", "ranges": [{"startOffset": 26957, "endOffset": 29414, "count": 2}, {"startOffset": 27119, "endOffset": 27272, "count": 0}, {"startOffset": 27856, "endOffset": 29348, "count": 0}, {"startOffset": 29358, "endOffset": 29413, "count": 0}], "isBlockCoverage": true}, {"functionName": "getPackageType", "ranges": [{"startOffset": 29528, "endOffset": 29641, "count": 0}], "isBlockCoverage": false}, {"functionName": "parsePackageName", "ranges": [{"startOffset": 29806, "endOffset": 30864, "count": 2}, {"startOffset": 29997, "endOffset": 30225, "count": 0}, {"startOffset": 30287, "endOffset": 30339, "count": 0}, {"startOffset": 30512, "endOffset": 30547, "count": 0}, {"startOffset": 30574, "endOffset": 30693, "count": 0}, {"startOffset": 30754, "endOffset": 30807, "count": 0}], "isBlockCoverage": true}, {"functionName": "packageResolve", "ranges": [{"startOffset": 31188, "endOffset": 33260, "count": 2}, {"startOffset": 31302, "endOffset": 31348, "count": 0}, {"startOffset": 31686, "endOffset": 31803, "count": 0}, {"startOffset": 31978, "endOffset": 33045, "count": 6}, {"startOffset": 32237, "endOffset": 32514, "count": 4}, {"startOffset": 32314, "endOffset": 32351, "count": 0}, {"startOffset": 32514, "endOffset": 32812, "count": 2}, {"startOffset": 32812, "endOffset": 33045, "count": 0}, {"startOffset": 33045, "endOffset": 33259, "count": 0}], "isBlockCoverage": true}, {"functionName": "isBareSpecifier", "ranges": [{"startOffset": 33371, "endOffset": 33481, "count": 0}], "isBlockCoverage": false}, {"functionName": "isRelativeSpecifier", "ranges": [{"startOffset": 33600, "endOffset": 33879, "count": 18}, {"startOffset": 33670, "endOffset": 33861, "count": 4}, {"startOffset": 33744, "endOffset": 33857, "count": 0}, {"startOffset": 33861, "endOffset": 33878, "count": 14}], "isBlockCoverage": true}, {"functionName": "shouldBeTreatedAsRelativeOrAbsolutePath", "ranges": [{"startOffset": 34028, "endOffset": 34219, "count": 18}, {"startOffset": 34114, "endOffset": 34131, "count": 0}, {"startOffset": 34160, "endOffset": 34176, "count": 0}], "isBlockCoverage": true}, {"functionName": "moduleResolve", "ranges": [{"startOffset": 34582, "endOffset": 35954, "count": 6}, {"startOffset": 34773, "endOffset": 34792, "count": 0}, {"startOffset": 35095, "endOffset": 35319, "count": 2}, {"startOffset": 35156, "endOffset": 35315, "count": 0}, {"startOffset": 35319, "endOffset": 35825, "count": 4}, {"startOffset": 35375, "endOffset": 35825, "count": 2}, {"startOffset": 35543, "endOffset": 35598, "count": 0}, {"startOffset": 35600, "endOffset": 35753, "count": 0}, {"startOffset": 35863, "endOffset": 35889, "count": 0}], "isBlockCoverage": true}, {"functionName": "resolveAsCommonJS", "ranges": [{"startOffset": 36154, "endOffset": 37980, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkIfDisallowedImport", "ranges": [{"startOffset": 38360, "endOffset": 39732, "count": 12}, {"startOffset": 38666, "endOffset": 39726, "count": 0}], "isBlockCoverage": true}, {"functionName": "throwIfInvalidParentURL", "ranges": [{"startOffset": 39872, "endOffset": 40145, "count": 12}, {"startOffset": 39949, "endOffset": 40000, "count": 0}, {"startOffset": 40037, "endOffset": 40057, "count": 0}, {"startOffset": 40059, "endOffset": 40143, "count": 0}], "isBlockCoverage": true}, {"functionName": "defaultResolve", "ranges": [{"startOffset": 40769, "endOffset": 44767, "count": 12}, {"startOffset": 40926, "endOffset": 40936, "count": 0}, {"startOffset": 40938, "endOffset": 41747, "count": 0}, {"startOffset": 41851, "endOffset": 41890, "count": 0}, {"startOffset": 41988, "endOffset": 42047, "count": 2}, {"startOffset": 42047, "endOffset": 42095, "count": 10}, {"startOffset": 42095, "endOffset": 42230, "count": 8}, {"startOffset": 42230, "endOffset": 42275, "count": 0}, {"startOffset": 42276, "endOffset": 42311, "count": 0}, {"startOffset": 42313, "endOffset": 42476, "count": 0}, {"startOffset": 42476, "endOffset": 42639, "count": 8}, {"startOffset": 42543, "endOffset": 42631, "count": 0}, {"startOffset": 42646, "endOffset": 42705, "count": 0}, {"startOffset": 42710, "endOffset": 42825, "count": 4}, {"startOffset": 42775, "endOffset": 42797, "count": 0}, {"startOffset": 43159, "endOffset": 43182, "count": 0}, {"startOffset": 43255, "endOffset": 43265, "count": 0}, {"startOffset": 43295, "endOffset": 43401, "count": 6}, {"startOffset": 43401, "endOffset": 43908, "count": 0}, {"startOffset": 43908, "endOffset": 44064, "count": 6}, {"startOffset": 44064, "endOffset": 44086, "count": 0}, {"startOffset": 44087, "endOffset": 44105, "count": 6}, {"startOffset": 44118, "endOffset": 44541, "count": 0}, {"startOffset": 44541, "endOffset": 44766, "count": 6}], "isBlockCoverage": true}, {"functionName": "decorateErrorWithCommonJSHints", "ranges": [{"startOffset": 45030, "endOffset": 45643, "count": 0}], "isBlockCoverage": false}, {"functionName": "defaultResolve", "ranges": [{"startOffset": 46069, "endOffset": 46460, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "143", "url": "node:internal/modules/esm/package_config", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2215, "count": 1}], "isBlockCoverage": false}, {"functionName": "getPackageScopeConfig", "ranges": [{"startOffset": 1072, "endOffset": 2165, "count": 10}, {"startOffset": 1190, "endOffset": 1910, "count": 27}, {"startOffset": 1324, "endOffset": 1344, "count": 0}, {"startOffset": 1534, "endOffset": 1569, "count": 10}, {"startOffset": 1569, "endOffset": 1886, "count": 17}, {"startOffset": 1886, "endOffset": 1906, "count": 0}, {"startOffset": 1910, "endOffset": 2164, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "144", "url": "node:internal/modules/esm/get_format", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8075, "count": 1}], "isBlockCoverage": false}, {"functionName": "node:", "ranges": [{"startOffset": 1056, "endOffset": 1087, "count": 0}], "isBlockCoverage": false}, {"functionName": "detectModuleFormat", "ranges": [{"startOffset": 1255, "endOffset": 1496, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDataProtocolModuleFormat", "ranges": [{"startOffset": 1557, "endOffset": 1773, "count": 0}], "isBlockCoverage": false}, {"functionName": "extname", "ranges": [{"startOffset": 2048, "endOffset": 2407, "count": 6}, {"startOffset": 2148, "endOffset": 2392, "count": 18}, {"startOffset": 2210, "endOffset": 2245, "count": 0}, {"startOffset": 2253, "endOffset": 2382, "count": 6}, {"startOffset": 2341, "endOffset": 2345, "count": 0}, {"startOffset": 2392, "endOffset": 2406, "count": 0}], "isBlockCoverage": true}, {"functionName": "underNodeModules", "ranges": [{"startOffset": 2637, "endOffset": 2858, "count": 0}], "isBlockCoverage": false}, {"functionName": "warnTypelessPackageJsonFile", "ranges": [{"startOffset": 2901, "endOffset": 3528, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFileProtocolModuleFormat", "ranges": [{"startOffset": 3669, "endOffset": 6736, "count": 6}, {"startOffset": 4004, "endOffset": 4496, "count": 0}, {"startOffset": 4503, "endOffset": 4626, "count": 0}, {"startOffset": 4633, "endOffset": 5411, "count": 0}, {"startOffset": 5421, "endOffset": 6735, "count": 0}], "isBlockCoverage": true}, {"functionName": "getHttpProtocolModuleFormat", "ranges": [{"startOffset": 6872, "endOffset": 7217, "count": 0}], "isBlockCoverage": false}, {"functionName": "defaultGetFormatWithoutErrors", "ranges": [{"startOffset": 7362, "endOffset": 7597, "count": 6}, {"startOffset": 7516, "endOffset": 7538, "count": 0}], "isBlockCoverage": true}, {"functionName": "defaultGetFormat", "ranges": [{"startOffset": 7742, "endOffset": 7965, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "145", "url": "node:internal/modules/esm/formats", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1798, "count": 1}], "isBlockCoverage": false}, {"functionName": "mimeToFormat", "ranges": [{"startOffset": 660, "endOffset": 998, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFormatOfExtensionlessFile", "ranges": [{"startOffset": 1368, "endOffset": 1704, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "146", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/redact/lib/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1093, "count": 1}], "isBlockCoverage": true}, {"functionName": "redact", "ranges": [{"startOffset": 127, "endOffset": 355, "count": 2}, {"startOffset": 183, "endOffset": 205, "count": 0}], "isBlockCoverage": true}, {"functionName": "splitAndRedact", "ranges": [{"startOffset": 432, "endOffset": 763, "count": 2}, {"startOffset": 616, "endOffset": 717, "count": 0}], "isBlockCoverage": true}, {"functionName": "redactLog", "ranges": [{"startOffset": 846, "endOffset": 1047, "count": 2}, {"startOffset": 888, "endOffset": 924, "count": 0}, {"startOffset": 1032, "endOffset": 1046, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 975, "endOffset": 1027, "count": 2}, {"startOffset": 1024, "endOffset": 1027, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "147", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/redact/lib/matchers.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2010, "count": 1}], "isBlockCoverage": true}, {"functionName": "predicate", "ranges": [{"startOffset": 879, "endOffset": 932, "count": 0}], "isBlockCoverage": false}, {"functionName": "predicate", "ranges": [{"startOffset": 1052, "endOffset": 1102, "count": 0}], "isBlockCoverage": false}, {"functionName": "predicate", "ranges": [{"startOffset": 1215, "endOffset": 1261, "count": 0}], "isBlockCoverage": false}, {"functionName": "predicate", "ranges": [{"startOffset": 1367, "endOffset": 1406, "count": 0}], "isBlockCoverage": false}, {"functionName": "replacement", "ranges": [{"startOffset": 1423, "endOffset": 1541, "count": 0}], "isBlockCoverage": false}, {"functionName": "predicate", "ranges": [{"startOffset": 1605, "endOffset": 1645, "count": 0}], "isBlockCoverage": false}, {"functionName": "replacement", "ranges": [{"startOffset": 1662, "endOffset": 1759, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "148", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/redact/lib/utils.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5744, "count": 1}], "isBlockCoverage": true}, {"functionName": "asterisk", "ranges": [{"startOffset": 204, "endOffset": 363, "count": 0}], "isBlockCoverage": false}, {"functionName": "escapeRegExp", "ranges": [{"startOffset": 529, "endOffset": 600, "count": 0}], "isBlockCoverage": false}, {"functionName": "urlEncodeRegexGroup", "ranges": [{"startOffset": 695, "endOffset": 896, "count": 0}], "isBlockCoverage": false}, {"functionName": "urlEncodeRegexTag", "ranges": [{"startOffset": 1016, "endOffset": 1246, "count": 0}], "isBlockCoverage": false}, {"functionName": "redactUrlHostnameMatcher", "ranges": [{"startOffset": 1337, "endOffset": 1615, "count": 0}], "isBlockCoverage": false}, {"functionName": "redactUrlSearchParamsMatcher", "ranges": [{"startOffset": 1733, "endOffset": 1979, "count": 0}], "isBlockCoverage": false}, {"functionName": "redactUrlPasswordMatcher", "ranges": [{"startOffset": 2070, "endOffset": 2304, "count": 2}, {"startOffset": 2285, "endOffset": 2298, "count": 0}], "isBlockCoverage": true}, {"functionName": "predicate", "ranges": [{"startOffset": 2130, "endOffset": 2155, "count": 0}], "isBlockCoverage": false}, {"functionName": "pattern", "ranges": [{"startOffset": 2168, "endOffset": 2251, "count": 0}], "isBlockCoverage": false}, {"functionName": "redactUrlReplacement", "ranges": [{"startOffset": 2335, "endOffset": 2511, "count": 0}], "isBlockCoverage": false}, {"functionName": "redactUrlMatcher", "ranges": [{"startOffset": 3066, "endOffset": 3173, "count": 0}], "isBlockCoverage": false}, {"functionName": "matcherFunctions", "ranges": [{"startOffset": 3218, "endOffset": 3373, "count": 0}], "isBlockCoverage": false}, {"functionName": "matcherFunctions", "ranges": [{"startOffset": 3389, "endOffset": 3800, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3402, "endOffset": 3800, "count": 2}, {"startOffset": 3492, "endOffset": 3497, "count": 0}, {"startOffset": 3641, "endOffset": 3723, "count": 0}, {"startOffset": 3779, "endOffset": 3799, "count": 0}], "isBlockCoverage": true}, {"functionName": "matcherFunctions", "ranges": [{"startOffset": 3817, "endOffset": 4208, "count": 0}], "isBlockCoverage": false}, {"functionName": "redactMatcher", "ranges": [{"startOffset": 4275, "endOffset": 4340, "count": 2}], "isBlockCoverage": true}, {"functionName": "redactMatchers", "ranges": [{"startOffset": 4416, "endOffset": 4664, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4433, "endOffset": 4664, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4519, "endOffset": 4654, "count": 2}, {"startOffset": 4589, "endOffset": 4598, "count": 0}], "isBlockCoverage": true}, {"functionName": "redactDynamicReplacement", "ranges": [{"startOffset": 4839, "endOffset": 4996, "count": 0}], "isBlockCoverage": false}, {"functionName": "redactFixedReplacement", "ranges": [{"startOffset": 5160, "endOffset": 5295, "count": 0}], "isBlockCoverage": false}, {"functionName": "redactUrlPassword", "ranges": [{"startOffset": 5323, "endOffset": 5424, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "149", "url": "file:///usr/lib/node_modules/npm/lib/npm.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14212, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 724, "endOffset": 14189, "count": 1}], "isBlockCoverage": true}, {"functionName": "get version", "ranges": [{"startOffset": 745, "endOffset": 788, "count": 4}], "isBlockCoverage": true}, {"functionName": "cmd", "ranges": [{"startOffset": 799, "endOffset": 1039, "count": 2}, {"startOffset": 856, "endOffset": 988, "count": 0}], "isBlockCoverage": true}, {"functionName": "Npm", "ranges": [{"startOffset": 2159, "endOffset": 2601, "count": 1}], "isBlockCoverage": true}, {"functionName": "load", "ranges": [{"startOffset": 2605, "endOffset": 2780, "count": 1}, {"startOffset": 2711, "endOffset": 2779, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2685, "endOffset": 2703, "count": 1}], "isBlockCoverage": true}, {"functionName": "#load", "ranges": [{"startOffset": 2784, "endOffset": 7554, "count": 1}, {"startOffset": 3296, "endOffset": 3378, "count": 0}, {"startOffset": 4296, "endOffset": 4301, "count": 0}, {"startOffset": 4439, "endOffset": 4511, "count": 0}, {"startOffset": 7261, "endOffset": 7287, "count": 0}, {"startOffset": 7289, "endOffset": 7373, "count": 0}, {"startOffset": 7409, "endOffset": 7485, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2844, "endOffset": 3156, "count": 1}, {"startOffset": 3031, "endOffset": 3150, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2948, "endOffset": 2956, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3203, "endOffset": 3227, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4816, "endOffset": 4945, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4885, "endOffset": 4944, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5156, "endOffset": 5297, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5232, "endOffset": 5296, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5648, "endOffset": 6527, "count": 1}], "isBlockCoverage": true}, {"functionName": "exec", "ranges": [{"startOffset": 7558, "endOffset": 7820, "count": 2}, {"startOffset": 7619, "endOffset": 7768, "count": 1}, {"startOffset": 7691, "endOffset": 7726, "count": 0}, {"startOffset": 7768, "endOffset": 7816, "count": 1}], "isBlockCoverage": true}, {"functionName": "#exec", "ranges": [{"startOffset": 7849, "endOffset": 9262, "count": 2}, {"startOffset": 8124, "endOffset": 8206, "count": 1}, {"startOffset": 8242, "endOffset": 8293, "count": 0}, {"startOffset": 8676, "endOffset": 8728, "count": 0}, {"startOffset": 8730, "endOffset": 9041, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9179, "endOffset": 9257, "count": 2}, {"startOffset": 9206, "endOffset": 9236, "count": 0}], "isBlockCoverage": true}, {"functionName": "unload", "ranges": [{"startOffset": 9423, "endOffset": 9509, "count": 1}], "isBlockCoverage": true}, {"functionName": "finish", "ranges": [{"startOffset": 9513, "endOffset": 10016, "count": 1}], "isBlockCoverage": true}, {"functionName": "exitErrorMessage", "ranges": [{"startOffset": 10020, "endOffset": 10601, "count": 0}], "isBlockCoverage": false}, {"functionName": "#handleError", "ranges": [{"startOffset": 10605, "endOffset": 11013, "count": 1}, {"startOffset": 10645, "endOffset": 10949, "count": 0}, {"startOffset": 10986, "endOffset": 11009, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10836, "endOffset": 10850, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10867, "endOffset": 10877, "count": 0}], "isBlockCoverage": false}, {"functionName": "#getError", "ranges": [{"startOffset": 11017, "endOffset": 11738, "count": 0}], "isBlockCoverage": false}, {"functionName": "get title", "ranges": [{"startOffset": 11742, "endOffset": 11783, "count": 1}], "isBlockCoverage": true}, {"functionName": "get loaded", "ranges": [{"startOffset": 11787, "endOffset": 11836, "count": 5}], "isBlockCoverage": true}, {"functionName": "get version", "ranges": [{"startOffset": 11840, "endOffset": 11896, "count": 4}], "isBlockCoverage": true}, {"functionName": "get command", "ranges": [{"startOffset": 11900, "endOffset": 11951, "count": 13}, {"startOffset": 11941, "endOffset": 11947, "count": 9}], "isBlockCoverage": true}, {"functionName": "get flatOptions", "ranges": [{"startOffset": 11955, "endOffset": 12168, "count": 8}, {"startOffset": 12104, "endOffset": 12148, "count": 4}], "isBlockCoverage": true}, {"functionName": "get color", "ranges": [{"startOffset": 12374, "endOffset": 12426, "count": 2}], "isBlockCoverage": true}, {"functionName": "get logColor", "ranges": [{"startOffset": 12430, "endOffset": 12488, "count": 1}], "isBlockCoverage": true}, {"functionName": "get noColorChalk", "ranges": [{"startOffset": 12492, "endOffset": 12556, "count": 0}], "isBlockCoverage": false}, {"functionName": "get chalk", "ranges": [{"startOffset": 12560, "endOffset": 12616, "count": 0}], "isBlockCoverage": false}, {"functionName": "get logChalk", "ranges": [{"startOffset": 12620, "endOffset": 12679, "count": 0}], "isBlockCoverage": false}, {"functionName": "get global", "ranges": [{"startOffset": 12683, "endOffset": 12783, "count": 2}], "isBlockCoverage": true}, {"functionName": "get silent", "ranges": [{"startOffset": 12787, "endOffset": 12841, "count": 0}], "isBlockCoverage": false}, {"functionName": "get lockfileVersion", "ranges": [{"startOffset": 12845, "endOffset": 12886, "count": 0}], "isBlockCoverage": false}, {"functionName": "get started", "ranges": [{"startOffset": 12890, "endOffset": 12942, "count": 0}], "isBlockCoverage": false}, {"functionName": "get logFiles", "ranges": [{"startOffset": 12946, "endOffset": 12998, "count": 1}], "isBlockCoverage": true}, {"functionName": "get #logsDir", "ranges": [{"startOffset": 13002, "endOffset": 13091, "count": 3}], "isBlockCoverage": true}, {"functionName": "get logPath", "ranges": [{"startOffset": 13095, "endOffset": 13168, "count": 2}], "isBlockCoverage": true}, {"functionName": "get npmRoot", "ranges": [{"startOffset": 13172, "endOffset": 13217, "count": 0}], "isBlockCoverage": false}, {"functionName": "get cache", "ranges": [{"startOffset": 13221, "endOffset": 13275, "count": 4}], "isBlockCoverage": true}, {"functionName": "get globalPrefix", "ranges": [{"startOffset": 13279, "endOffset": 13340, "count": 0}], "isBlockCoverage": false}, {"functionName": "get localPrefix", "ranges": [{"startOffset": 13344, "endOffset": 13403, "count": 1}], "isBlockCoverage": true}, {"functionName": "get localPackage", "ranges": [{"startOffset": 13407, "endOffset": 13468, "count": 0}], "isBlockCoverage": false}, {"functionName": "get globalDir", "ranges": [{"startOffset": 13472, "endOffset": 13643, "count": 0}], "isBlockCoverage": false}, {"functionName": "get localDir", "ranges": [{"startOffset": 13647, "endOffset": 13721, "count": 0}], "isBlockCoverage": false}, {"functionName": "get dir", "ranges": [{"startOffset": 13725, "endOffset": 13797, "count": 0}], "isBlockCoverage": false}, {"functionName": "get globalBin", "ranges": [{"startOffset": 13801, "endOffset": 13919, "count": 0}], "isBlockCoverage": false}, {"functionName": "get localBin", "ranges": [{"startOffset": 13923, "endOffset": 13981, "count": 0}], "isBlockCoverage": false}, {"functionName": "get bin", "ranges": [{"startOffset": 13985, "endOffset": 14057, "count": 0}], "isBlockCoverage": false}, {"functionName": "get prefix", "ranges": [{"startOffset": 14061, "endOffset": 14142, "count": 0}], "isBlockCoverage": false}, {"functionName": "get usage", "ranges": [{"startOffset": 14146, "endOffset": 14187, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "150", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/config/lib/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 30544, "count": 1}], "isBlockCoverage": true}, {"functionName": "fileExists", "ranges": [{"startOffset": 673, "endOffset": 753, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 711, "endOffset": 730, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 752, "count": 10}], "isBlockCoverage": true}, {"functionName": "dirExists", "ranges": [{"startOffset": 773, "endOffset": 858, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasOwnProperty", "ranges": [{"startOffset": 883, "endOffset": 945, "count": 42}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1371, "endOffset": 29528, "count": 1}], "isBlockCoverage": true}, {"functionName": "get typeDefs", "ranges": [{"startOffset": 1499, "endOffset": 1540, "count": 0}], "isBlockCoverage": false}, {"functionName": "Config", "ranges": [{"startOffset": 1544, "endOffset": 3486, "count": 1}, {"startOffset": 2109, "endOffset": 2289, "count": 158}, {"startOffset": 2199, "endOffset": 2283, "count": 18}, {"startOffset": 3011, "endOffset": 3078, "count": 7}, {"startOffset": 3390, "endOffset": 3427, "count": 7}], "isBlockCoverage": true}, {"functionName": "Config.data.set", "ranges": [{"startOffset": 3100, "endOffset": 3183, "count": 0}], "isBlockCoverage": false}, {"functionName": "Config.data.delete", "ranges": [{"startOffset": 3207, "endOffset": 3290, "count": 0}], "isBlockCoverage": false}, {"functionName": "get loaded", "ranges": [{"startOffset": 3490, "endOffset": 3533, "count": 88}], "isBlockCoverage": true}, {"functionName": "get prefix", "ranges": [{"startOffset": 3537, "endOffset": 3626, "count": 0}], "isBlockCoverage": false}, {"functionName": "find", "ranges": [{"startOffset": 3675, "endOffset": 4055, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4059, "endOffset": 4210, "count": 82}, {"startOffset": 4100, "endOffset": 4173, "count": 0}], "isBlockCoverage": true}, {"functionName": "#get", "ranges": [{"startOffset": 4324, "endOffset": 4606, "count": 91}, {"startOffset": 4374, "endOffset": 4398, "count": 42}, {"startOffset": 4400, "endOffset": 4472, "count": 0}, {"startOffset": 4514, "endOffset": 4522, "count": 49}, {"startOffset": 4550, "endOffset": 4578, "count": 42}, {"startOffset": 4579, "endOffset": 4590, "count": 51}, {"startOffset": 4591, "endOffset": 4602, "count": 40}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 4610, "endOffset": 5251, "count": 0}], "isBlockCoverage": false}, {"functionName": "get flat", "ranges": [{"startOffset": 5255, "endOffset": 5753, "count": 8}, {"startOffset": 5296, "endOffset": 5334, "count": 7}, {"startOffset": 5334, "endOffset": 5564, "count": 1}, {"startOffset": 5564, "endOffset": 5616, "count": 7}, {"startOffset": 5616, "endOffset": 5752, "count": 1}], "isBlockCoverage": true}, {"functionName": "delete", "ranges": [{"startOffset": 5757, "endOffset": 6144, "count": 0}], "isBlockCoverage": false}, {"functionName": "load", "ranges": [{"startOffset": 6148, "endOffset": 7156, "count": 1}, {"startOffset": 6185, "endOffset": 6262, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadDefaults", "ranges": [{"startOffset": 7160, "endOffset": 8633, "count": 1}, {"startOffset": 7511, "endOffset": 7726, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 8329, "endOffset": 8376, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 8384, "endOffset": 8571, "count": 1}], "isBlockCoverage": true}, {"functionName": "loadHome", "ranges": [{"startOffset": 8637, "endOffset": 8697, "count": 1}, {"startOffset": 8681, "endOffset": 8693, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadGlobalPrefix", "ranges": [{"startOffset": 8701, "endOffset": 9324, "count": 1}, {"startOffset": 8750, "endOffset": 8831, "count": 0}, {"startOffset": 8858, "endOffset": 8907, "count": 0}, {"startOffset": 8944, "endOffset": 9046, "count": 0}, {"startOffset": 9235, "endOffset": 9314, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadEnv", "ranges": [{"startOffset": 9328, "endOffset": 9849, "count": 1}, {"startOffset": 9439, "endOffset": 9796, "count": 111}, {"startOffset": 9481, "endOffset": 9497, "count": 18}, {"startOffset": 9499, "endOffset": 9525, "count": 94}, {"startOffset": 9525, "endOffset": 9796, "count": 17}], "isBlockCoverage": true}, {"functionName": "loadCLI", "ranges": [{"startOffset": 9853, "endOffset": 10530, "count": 1}, {"startOffset": 9916, "endOffset": 10086, "count": 40}, {"startOffset": 9941, "endOffset": 9971, "count": 15}, {"startOffset": 9973, "endOffset": 10080, "count": 0}], "isBlockCoverage": true}, {"functionName": "nopt.<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 10113, "endOffset": 10201, "count": 0}], "isBlockCoverage": false}, {"functionName": "get valid", "ranges": [{"startOffset": 10534, "endOffset": 10735, "count": 0}], "isBlockCoverage": false}, {"functionName": "validate", "ranges": [{"startOffset": 10739, "endOffset": 13448, "count": 10}, {"startOffset": 10774, "endOffset": 13150, "count": 2}, {"startOffset": 10879, "endOffset": 12977, "count": 14}, {"startOffset": 11048, "endOffset": 11075, "count": 12}, {"startOffset": 11076, "endOffset": 11099, "count": 10}, {"startOffset": 11101, "endOffset": 11131, "count": 6}, {"startOffset": 11131, "endOffset": 11206, "count": 8}, {"startOffset": 11272, "endOffset": 12969, "count": 6}, {"startOffset": 11628, "endOffset": 11778, "count": 12}, {"startOffset": 11673, "endOffset": 11766, "count": 0}, {"startOffset": 12223, "endOffset": 12959, "count": 24}, {"startOffset": 12268, "endOffset": 12947, "count": 0}, {"startOffset": 13010, "endOffset": 13124, "count": 0}, {"startOffset": 13150, "endOffset": 13444, "count": 8}], "isBlockCoverage": true}, {"functionName": "nopt.<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 13251, "endOffset": 13329, "count": 0}], "isBlockCoverage": false}, {"functionName": "repair", "ranges": [{"startOffset": 13606, "endOffset": 14579, "count": 0}], "isBlockCoverage": false}, {"functionName": "isDefault", "ranges": [{"startOffset": 14788, "endOffset": 15097, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 15101, "endOffset": 16137, "count": 0}], "isBlockCoverage": false}, {"functionName": "abbrev<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 16141, "endOffset": 16282, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 16286, "endOffset": 16419, "count": 0}], "isBlockCoverage": false}, {"functionName": "#getOneOfKeywords", "ranges": [{"startOffset": 16423, "endOffset": 16790, "count": 0}], "isBlockCoverage": false}, {"functionName": "#loadObject", "ranges": [{"startOffset": 16794, "endOffset": 18222, "count": 7}, {"startOffset": 16945, "endOffset": 17096, "count": 0}, {"startOffset": 17132, "endOffset": 17295, "count": 0}, {"startOffset": 17370, "endOffset": 17512, "count": 2}, {"startOffset": 17430, "endOffset": 17506, "count": 0}, {"startOffset": 17512, "endOffset": 18218, "count": 5}, {"startOffset": 17595, "endOffset": 18212, "count": 179}, {"startOffset": 17718, "endOffset": 18069, "count": 20}, {"startOffset": 17790, "endOffset": 17801, "count": 10}, {"startOffset": 17803, "endOffset": 18059, "count": 0}, {"startOffset": 18102, "endOffset": 18126, "count": 159}, {"startOffset": 18128, "endOffset": 18179, "count": 21}], "isBlockCoverage": true}, {"functionName": "checkUnknown", "ranges": [{"startOffset": 18226, "endOffset": 18807, "count": 21}, {"startOffset": 18286, "endOffset": 18803, "count": 11}, {"startOffset": 18325, "endOffset": 18349, "count": 4}, {"startOffset": 18349, "endOffset": 18380, "count": 7}, {"startOffset": 18380, "endOffset": 18545, "count": 0}, {"startOffset": 18545, "endOffset": 18626, "count": 7}, {"startOffset": 18626, "endOffset": 18662, "count": 6}, {"startOffset": 18664, "endOffset": 18797, "count": 6}], "isBlockCoverage": true}, {"functionName": "#checkDeprecated", "ranges": [{"startOffset": 18811, "endOffset": 18929, "count": 20}, {"startOffset": 18866, "endOffset": 18925, "count": 0}], "isBlockCoverage": true}, {"functionName": "parseField", "ranges": [{"startOffset": 18993, "endOffset": 19088, "count": 179}], "isBlockCoverage": true}, {"functionName": "#loadFile", "ranges": [{"startOffset": 19092, "endOffset": 19735, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19285, "endOffset": 19673, "count": 2}, {"startOffset": 19371, "endOffset": 19393, "count": 1}, {"startOffset": 19395, "endOffset": 19607, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19681, "endOffset": 19725, "count": 2}], "isBlockCoverage": true}, {"functionName": "loadBuiltinConfig", "ranges": [{"startOffset": 19739, "endOffset": 19834, "count": 1}], "isBlockCoverage": true}, {"functionName": "loadProjectConfig", "ranges": [{"startOffset": 19838, "endOffset": 21225, "count": 1}, {"startOffset": 20229, "endOffset": 20313, "count": 0}, {"startOffset": 20391, "endOffset": 20551, "count": 0}, {"startOffset": 21066, "endOffset": 21221, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadLocalPrefix", "ranges": [{"startOffset": 21229, "endOffset": 23708, "count": 1}, {"startOffset": 21324, "endOffset": 21379, "count": 0}, {"startOffset": 21556, "endOffset": 23634, "count": 11}, {"startOffset": 21728, "endOffset": 21749, "count": 0}, {"startOffset": 21751, "endOffset": 21774, "count": 0}, {"startOffset": 21870, "endOffset": 21927, "count": 1}, {"startOffset": 21889, "endOffset": 21927, "count": 0}, {"startOffset": 21929, "endOffset": 22222, "count": 1}, {"startOffset": 22128, "endOffset": 22156, "count": 0}, {"startOffset": 22222, "endOffset": 22268, "count": 10}, {"startOffset": 22270, "endOffset": 23628, "count": 0}, {"startOffset": 23663, "endOffset": 23704, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22621, "endOffset": 22644, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadUserConfig", "ranges": [{"startOffset": 23712, "endOffset": 23794, "count": 1}], "isBlockCoverage": true}, {"functionName": "loadGlobalConfig", "ranges": [{"startOffset": 23798, "endOffset": 23886, "count": 1}], "isBlockCoverage": true}, {"functionName": "save", "ranges": [{"startOffset": 23890, "endOffset": 25045, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearCredentialsByURI", "ranges": [{"startOffset": 25049, "endOffset": 25931, "count": 0}], "isBlockCoverage": false}, {"functionName": "setCredentialsByURI", "ranges": [{"startOffset": 25935, "endOffset": 27418, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCredentialsByURI", "ranges": [{"startOffset": 27501, "endOffset": 29321, "count": 0}], "isBlockCoverage": false}, {"functionName": "setEnvs", "ranges": [{"startOffset": 29492, "endOffset": 29526, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 29601, "endOffset": 30518, "count": 7}], "isBlockCoverage": true}, {"functionName": "ConfigData", "ranges": [{"startOffset": 29661, "endOffset": 29784, "count": 7}, {"startOffset": 29722, "endOffset": 29736, "count": 6}], "isBlockCoverage": true}, {"functionName": "get data", "ranges": [{"startOffset": 29788, "endOffset": 29827, "count": 299}], "isBlockCoverage": true}, {"functionName": "get valid", "ranges": [{"startOffset": 29831, "endOffset": 29873, "count": 0}], "isBlockCoverage": false}, {"functionName": "set source", "ranges": [{"startOffset": 29877, "endOffset": 30017, "count": 7}, {"startOffset": 29916, "endOffset": 29992, "count": 0}], "isBlockCoverage": true}, {"functionName": "get source", "ranges": [{"startOffset": 30021, "endOffset": 30064, "count": 7}], "isBlockCoverage": true}, {"functionName": "set loadError", "ranges": [{"startOffset": 30068, "endOffset": 30253, "count": 2}, {"startOffset": 30149, "endOffset": 30224, "count": 0}], "isBlockCoverage": true}, {"functionName": "get loadError", "ranges": [{"startOffset": 30257, "endOffset": 30307, "count": 0}], "isBlockCoverage": false}, {"functionName": "set raw", "ranges": [{"startOffset": 30311, "endOffset": 30475, "count": 5}, {"startOffset": 30384, "endOffset": 30453, "count": 0}], "isBlockCoverage": true}, {"functionName": "get raw", "ranges": [{"startOffset": 30479, "endOffset": 30516, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "151", "url": "file:///usr/lib/node_modules/npm/node_modules/walk-up-path/dist/commonjs/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 457, "count": 1}], "isBlockCoverage": true}, {"functionName": "walkUp", "ranges": [{"startOffset": 149, "endOffset": 397, "count": 1}, {"startOffset": 218, "endOffset": 395, "count": 11}, {"startOffset": 311, "endOffset": 341, "count": 1}, {"startOffset": 341, "endOffset": 389, "count": 10}], "isBlockCoverage": true}]}, {"scriptId": "152", "url": "node:internal/source_map/source_map_cache_map", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3653, "count": 1}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 915, "endOffset": 3564, "count": 1}], "isBlockCoverage": false}, {"functionName": "#cleanup", "ranges": [{"startOffset": 1366, "endOffset": 1740, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 2181, "endOffset": 2496, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2309, "endOffset": 2355, "count": 6}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2598, "endOffset": 2826, "count": 0}], "isBlockCoverage": false}, {"functionName": "get size", "ranges": [{"startOffset": 2979, "endOffset": 3032, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3036, "endOffset": 3562, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "153", "url": "file:///usr/lib/node_modules/npm/node_modules/ini/lib/ini.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7123, "count": 1}], "isBlockCoverage": true}, {"functionName": "encode", "ranges": [{"startOffset": 60, "endOffset": 2372, "count": 0}], "isBlockCoverage": false}, {"functionName": "splitSections", "ranges": [{"startOffset": 2374, "endOffset": 2960, "count": 0}], "isBlockCoverage": false}, {"functionName": "decode", "ranges": [{"startOffset": 2977, "endOffset": 5746, "count": 2}, {"startOffset": 3311, "endOffset": 4815, "count": 5}, {"startOffset": 3327, "endOffset": 3352, "count": 3}, {"startOffset": 3353, "endOffset": 3375, "count": 3}, {"startOffset": 3377, "endOffset": 3399, "count": 2}, {"startOffset": 3399, "endOffset": 3449, "count": 3}, {"startOffset": 3449, "endOffset": 3471, "count": 0}, {"startOffset": 3471, "endOffset": 3504, "count": 3}, {"startOffset": 3504, "endOffset": 3795, "count": 0}, {"startOffset": 3795, "endOffset": 3946, "count": 3}, {"startOffset": 3946, "endOffset": 4057, "count": 0}, {"startOffset": 4057, "endOffset": 4082, "count": 3}, {"startOffset": 4082, "endOffset": 4106, "count": 0}, {"startOffset": 4113, "endOffset": 4134, "count": 0}, {"startOffset": 4135, "endOffset": 4143, "count": 3}, {"startOffset": 4174, "endOffset": 4196, "count": 0}, {"startOffset": 4196, "endOffset": 4245, "count": 3}, {"startOffset": 4246, "endOffset": 4252, "count": 0}, {"startOffset": 4291, "endOffset": 4320, "count": 3}, {"startOffset": 4321, "endOffset": 4349, "count": 3}, {"startOffset": 4350, "endOffset": 4372, "count": 0}, {"startOffset": 4379, "endOffset": 4389, "count": 3}, {"startOffset": 4457, "endOffset": 4603, "count": 0}, {"startOffset": 4603, "endOffset": 4745, "count": 3}, {"startOffset": 4745, "endOffset": 4777, "count": 0}, {"startOffset": 4777, "endOffset": 4811, "count": 3}, {"startOffset": 4984, "endOffset": 5676, "count": 3}, {"startOffset": 5059, "endOffset": 5089, "count": 0}, {"startOffset": 5113, "endOffset": 5609, "count": 0}, {"startOffset": 5611, "endOffset": 5676, "count": 0}, {"startOffset": 5705, "endOffset": 5730, "count": 0}], "isBlockCoverage": true}, {"functionName": "isQuoted", "ranges": [{"startOffset": 5765, "endOffset": 5877, "count": 6}, {"startOffset": 5804, "endOffset": 5824, "count": 0}, {"startOffset": 5854, "endOffset": 5874, "count": 0}], "isBlockCoverage": true}, {"functionName": "safe", "ranges": [{"startOffset": 5892, "endOffset": 6156, "count": 0}], "isBlockCoverage": false}, {"functionName": "unsafe", "ranges": [{"startOffset": 6173, "endOffset": 7024, "count": 6}, {"startOffset": 6195, "endOffset": 6200, "count": 0}, {"startOffset": 6230, "endOffset": 6442, "count": 0}, {"startOffset": 6599, "endOffset": 6939, "count": 155}, {"startOffset": 6646, "endOffset": 6792, "count": 0}, {"startOffset": 6826, "endOffset": 6849, "count": 0}, {"startOffset": 6871, "endOffset": 6899, "count": 0}, {"startOffset": 6953, "endOffset": 6980, "count": 0}, {"startOffset": 7009, "endOffset": 7023, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "154", "url": "file:///usr/lib/node_modules/nopt/lib/nopt.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 955, "count": 1}], "isBlockCoverage": true}, {"functionName": "nopt", "ranges": [{"startOffset": 530, "endOffset": 777, "count": 1}, {"startOffset": 654, "endOffset": 659, "count": 0}, {"startOffset": 688, "endOffset": 693, "count": 0}], "isBlockCoverage": true}, {"functionName": "clean", "ranges": [{"startOffset": 779, "endOffset": 954, "count": 8}, {"startOffset": 883, "endOffset": 888, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "155", "url": "file:///usr/lib/node_modules/nopt/lib/nopt-lib.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12818, "count": 1}], "isBlockCoverage": true}, {"functionName": "hasOwn", "ranges": [{"startOffset": 129, "endOffset": 181, "count": 38}], "isBlockCoverage": true}, {"functionName": "getType", "ranges": [{"startOffset": 199, "endOffset": 503, "count": 38}, {"startOffset": 303, "endOffset": 340, "count": 20}, {"startOffset": 342, "endOffset": 476, "count": 0}], "isBlockCoverage": true}, {"functionName": "isTypeDef", "ranges": [{"startOffset": 523, "endOffset": 557, "count": 56}], "isBlockCoverage": true}, {"functionName": "hasTypeDef", "ranges": [{"startOffset": 577, "endOffset": 623, "count": 76}], "isBlockCoverage": true}, {"functionName": "doesNotHaveTypeDef", "ranges": [{"startOffset": 651, "endOffset": 695, "count": 0}], "isBlockCoverage": false}, {"functionName": "nopt", "ranges": [{"startOffset": 697, "endOffset": 1344, "count": 1}], "isBlockCoverage": true}, {"functionName": "value", "ranges": [{"startOffset": 1222, "endOffset": 1298, "count": 0}], "isBlockCoverage": false}, {"functionName": "clean", "ranges": [{"startOffset": 1346, "endOffset": 4815, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1973, "endOffset": 4812, "count": 38}, {"startOffset": 2004, "endOffset": 2024, "count": 0}, {"startOffset": 2258, "endOffset": 2290, "count": 20}, {"startOffset": 2327, "endOffset": 2377, "count": 0}, {"startOffset": 2408, "endOffset": 2435, "count": 18}, {"startOffset": 4503, "endOffset": 4541, "count": 0}, {"startOffset": 4543, "endOffset": 4648, "count": 0}, {"startOffset": 4667, "endOffset": 4729, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2506, "endOffset": 4330, "count": 38}, {"startOffset": 2703, "endOffset": 2725, "count": 0}, {"startOffset": 2756, "endOffset": 2828, "count": 0}, {"startOffset": 2860, "endOffset": 2933, "count": 0}, {"startOffset": 2936, "endOffset": 3009, "count": 0}, {"startOffset": 3048, "endOffset": 3060, "count": 0}, {"startOffset": 3062, "endOffset": 3130, "count": 0}, {"startOffset": 3167, "endOffset": 3191, "count": 0}, {"startOffset": 3193, "endOffset": 3268, "count": 0}, {"startOffset": 3298, "endOffset": 3700, "count": 20}, {"startOffset": 3359, "endOffset": 3700, "count": 0}, {"startOffset": 3700, "endOffset": 3792, "count": 18}, {"startOffset": 3792, "endOffset": 3814, "count": 0}, {"startOffset": 3815, "endOffset": 3884, "count": 0}, {"startOffset": 3886, "endOffset": 3912, "count": 0}, {"startOffset": 3912, "endOffset": 4048, "count": 18}, {"startOffset": 4048, "endOffset": 4264, "count": 0}, {"startOffset": 4264, "endOffset": 4329, "count": 18}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4339, "endOffset": 4358, "count": 38}], "isBlockCoverage": true}, {"functionName": "validate", "ranges": [{"startOffset": 4817, "endOffset": 6608, "count": 18}, {"startOffset": 4978, "endOffset": 5231, "count": 0}, {"startOffset": 5294, "endOffset": 5315, "count": 0}, {"startOffset": 5734, "endOffset": 5813, "count": 0}, {"startOffset": 5864, "endOffset": 5943, "count": 0}, {"startOffset": 6117, "endOffset": 6491, "count": 74}, {"startOffset": 6242, "endOffset": 6251, "count": 2}, {"startOffset": 6252, "endOffset": 6266, "count": 2}, {"startOffset": 6268, "endOffset": 6305, "count": 2}, {"startOffset": 6306, "endOffset": 6333, "count": 72}, {"startOffset": 6341, "endOffset": 6487, "count": 18}, {"startOffset": 6570, "endOffset": 6594, "count": 0}], "isBlockCoverage": true}, {"functionName": "parse", "ranges": [{"startOffset": 6610, "endOffset": 10743, "count": 1}, {"startOffset": 7181, "endOffset": 7325, "count": 0}, {"startOffset": 7378, "endOffset": 7395, "count": 0}, {"startOffset": 7397, "endOffset": 10716, "count": 0}], "isBlockCoverage": true}, {"functionName": "singleCharacters", "ranges": [{"startOffset": 10804, "endOffset": 11191, "count": 0}], "isBlockCoverage": false}, {"functionName": "resolveShort", "ranges": [{"startOffset": 11193, "endOffset": 12712, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "156", "url": "file:///usr/lib/node_modules/nopt/node_modules/abbrev/lib/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1315, "count": 1}], "isBlockCoverage": true}, {"functionName": "abbrev", "ranges": [{"startOffset": 25, "endOffset": 1247, "count": 2}, {"startOffset": 84, "endOffset": 109, "count": 0}, {"startOffset": 120, "endOffset": 126, "count": 0}, {"startOffset": 175, "endOffset": 250, "count": 198}, {"startOffset": 229, "endOffset": 246, "count": 0}, {"startOffset": 530, "endOffset": 1228, "count": 198}, {"startOffset": 591, "endOffset": 596, "count": 2}, {"startOffset": 677, "endOffset": 699, "count": 0}, {"startOffset": 768, "endOffset": 1015, "count": 930}, {"startOffset": 842, "endOffset": 871, "count": 717}, {"startOffset": 904, "endOffset": 933, "count": 744}, {"startOffset": 957, "endOffset": 972, "count": 384}, {"startOffset": 974, "endOffset": 1009, "count": 171}, {"startOffset": 1053, "endOffset": 1108, "count": 51}, {"startOffset": 1108, "endOffset": 1161, "count": 147}, {"startOffset": 1161, "endOffset": 1224, "count": 923}, {"startOffset": 1224, "endOffset": 1228, "count": 147}], "isBlockCoverage": true}, {"functionName": "lexSort", "ranges": [{"startOffset": 1249, "endOffset": 1314, "count": 545}, {"startOffset": 1292, "endOffset": 1295, "count": 0}, {"startOffset": 1304, "endOffset": 1307, "count": 444}, {"startOffset": 1308, "endOffset": 1312, "count": 101}], "isBlockCoverage": true}]}, {"scriptId": "157", "url": "file:///usr/lib/node_modules/nopt/lib/debug.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 181, "count": 1}, {"startOffset": 136, "endOffset": 167, "count": 0}], "isBlockCoverage": true}, {"functionName": "module.exports", "ranges": [{"startOffset": 138, "endOffset": 167, "count": 0}], "isBlockCoverage": false}, {"functionName": "module.exports", "ranges": [{"startOffset": 172, "endOffset": 180, "count": 322}], "isBlockCoverage": true}]}, {"scriptId": "158", "url": "file:///usr/lib/node_modules/nopt/lib/type-defs.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2030, "count": 1}], "isBlockCoverage": true}, {"functionName": "validateString", "ranges": [{"startOffset": 155, "endOffset": 221, "count": 2}], "isBlockCoverage": true}, {"functionName": "validatePath", "ranges": [{"startOffset": 223, "endOffset": 643, "count": 12}, {"startOffset": 282, "endOffset": 304, "count": 0}, {"startOffset": 325, "endOffset": 346, "count": 0}, {"startOffset": 442, "endOffset": 455, "count": 0}, {"startOffset": 532, "endOffset": 584, "count": 0}], "isBlockCoverage": true}, {"functionName": "validateNumber", "ranges": [{"startOffset": 645, "endOffset": 801, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateDate", "ranges": [{"startOffset": 803, "endOffset": 981, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateBoolean", "ranges": [{"startOffset": 983, "endOffset": 1256, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateUrl", "ranges": [{"startOffset": 1258, "endOffset": 1506, "count": 4}, {"startOffset": 1461, "endOffset": 1483, "count": 0}], "isBlockCoverage": true}, {"functionName": "validateStream", "ranges": [{"startOffset": 1508, "endOffset": 1621, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "159", "url": "node:os", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8453, "count": 1}], "isBlockCoverage": false}, {"functionName": "getCheckedFunction", "ranges": [{"startOffset": 2054, "endOffset": 2300, "count": 4}], "isBlockCoverage": true}, {"functionName": "checkError", "ranges": [{"startOffset": 2113, "endOffset": 2296, "count": 13}, {"startOffset": 2209, "endOffset": 2276, "count": 0}], "isBlockCoverage": true}, {"functionName": "getOSRelease", "ranges": [{"startOffset": 2682, "endOffset": 2695, "count": 1}], "isBlockCoverage": true}, {"functionName": "getOSType", "ranges": [{"startOffset": 2744, "endOffset": 2754, "count": 1}], "isBlockCoverage": true}, {"functionName": "getOSVersion", "ranges": [{"startOffset": 2806, "endOffset": 2819, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMachine", "ranges": [{"startOffset": 2869, "endOffset": 2882, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAvailableParallelism.<computed>", "ranges": [{"startOffset": 2930, "endOffset": 2961, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFreeMem.<computed>", "ranges": [{"startOffset": 2995, "endOffset": 3013, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHostname.<computed>", "ranges": [{"startOffset": 3048, "endOffset": 3067, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOSVersion.<computed>", "ranges": [{"startOffset": 3103, "endOffset": 3123, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOSType.<computed>", "ranges": [{"startOffset": 3156, "endOffset": 3173, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOSRelease.<computed>", "ranges": [{"startOffset": 3209, "endOffset": 3229, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMachine.<computed>", "ranges": [{"startOffset": 3263, "endOffset": 3281, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHomeDirectory.<computed>", "ranges": [{"startOffset": 3321, "endOffset": 3345, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTotalMem.<computed>", "ranges": [{"startOffset": 3380, "endOffset": 3399, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUptime.<computed>", "ranges": [{"startOffset": 3432, "endOffset": 3449, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadavg", "ranges": [{"startOffset": 3587, "endOffset": 3687, "count": 0}], "isBlockCoverage": false}, {"functionName": "cpus", "ranges": [{"startOffset": 3967, "endOffset": 4396, "count": 0}], "isBlockCoverage": false}, {"functionName": "arch", "ranges": [{"startOffset": 4427, "endOffset": 4469, "count": 0}], "isBlockCoverage": false}, {"functionName": "arch.<computed>", "ranges": [{"startOffset": 4496, "endOffset": 4514, "count": 0}], "isBlockCoverage": false}, {"functionName": "platform", "ranges": [{"startOffset": 4546, "endOffset": 4596, "count": 0}], "isBlockCoverage": false}, {"functionName": "platform.<computed>", "ranges": [{"startOffset": 4627, "endOffset": 4649, "count": 0}], "isBlockCoverage": false}, {"functionName": "tmpdir", "ranges": [{"startOffset": 4681, "endOffset": 5286, "count": 0}], "isBlockCoverage": false}, {"functionName": "tmpdir.<computed>", "ranges": [{"startOffset": 5315, "endOffset": 5329, "count": 0}], "isBlockCoverage": false}, {"functionName": "endianness", "ranges": [{"startOffset": 5366, "endOffset": 5413, "count": 0}], "isBlockCoverage": false}, {"functionName": "endianness.<computed>", "ranges": [{"startOffset": 5446, "endOffset": 5463, "count": 0}], "isBlockCoverage": false}, {"functionName": "networkInterfaces", "ranges": [{"startOffset": 5675, "endOffset": 6368, "count": 1}, {"startOffset": 5798, "endOffset": 5812, "count": 0}, {"startOffset": 5856, "endOffset": 6348, "count": 7}, {"startOffset": 6170, "endOffset": 6194, "count": 4}, {"startOffset": 6269, "endOffset": 6305, "count": 4}, {"startOffset": 6305, "endOffset": 6344, "count": 3}], "isBlockCoverage": true}, {"functionName": "setPriority", "ranges": [{"startOffset": 6452, "endOffset": 6741, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPriority", "ranges": [{"startOffset": 6799, "endOffset": 7054, "count": 0}], "isBlockCoverage": false}, {"functionName": "userInfo", "ranges": [{"startOffset": 7358, "endOffset": 7584, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "160", "url": "node:fs/promises", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 73, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "161", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/config/lib/type-defs.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1388, "count": 1}], "isBlockCoverage": true}, {"functionName": "validate<PERSON><PERSON>ver", "ranges": [{"startOffset": 197, "endOffset": 306, "count": 0}], "isBlockCoverage": false}, {"functionName": "validatePath", "ranges": [{"startOffset": 382, "endOffset": 497, "count": 12}, {"startOffset": 433, "endOffset": 455, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "162", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/config/lib/umask.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 914, "count": 1}], "isBlockCoverage": true}, {"functionName": "parse", "ranges": [{"startOffset": 14, "endOffset": 748, "count": 0}], "isBlockCoverage": false}, {"functionName": "validate", "ranges": [{"startOffset": 767, "endOffset": 875, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "163", "url": "file:///usr/lib/node_modules/semver/functions/valid.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 176, "count": 1}], "isBlockCoverage": true}, {"functionName": "valid", "ranges": [{"startOffset": 61, "endOffset": 152, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "164", "url": "file:///usr/lib/node_modules/semver/functions/parse.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 331, "count": 1}], "isBlockCoverage": true}, {"functionName": "parse", "ranges": [{"startOffset": 72, "endOffset": 306, "count": 1}, {"startOffset": 150, "endOffset": 174, "count": 0}, {"startOffset": 227, "endOffset": 304, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "165", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/config/lib/nerf-dart.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 455, "count": 1}], "isBlockCoverage": true}, {"functionName": "module.exports", "ranges": [{"startOffset": 251, "endOffset": 454, "count": 6}], "isBlockCoverage": true}]}, {"scriptId": "166", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/config/lib/env-replace.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 414, "count": 1}], "isBlockCoverage": true}, {"functionName": "module.exports", "ranges": [{"startOffset": 125, "endOffset": 413, "count": 244}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 156, "endOffset": 412, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "167", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/config/lib/parse-field.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2229, "count": 1}], "isBlockCoverage": true}, {"functionName": "parseField", "ranges": [{"startOffset": 262, "endOffset": 2199, "count": 181}, {"startOffset": 331, "endOffset": 351, "count": 115}, {"startOffset": 353, "endOffset": 371, "count": 109}, {"startOffset": 371, "endOffset": 659, "count": 72}, {"startOffset": 659, "endOffset": 696, "count": 58}, {"startOffset": 833, "endOffset": 855, "count": 71}, {"startOffset": 931, "endOffset": 1012, "count": 6}, {"startOffset": 952, "endOffset": 955, "count": 0}, {"startOffset": 1012, "endOffset": 1252, "count": 66}, {"startOffset": 1252, "endOffset": 1307, "count": 1}, {"startOffset": 1307, "endOffset": 1370, "count": 65}, {"startOffset": 1370, "endOffset": 1382, "count": 0}, {"startOffset": 1383, "endOffset": 1394, "count": 0}, {"startOffset": 1396, "endOffset": 1417, "count": 0}, {"startOffset": 1417, "endOffset": 1532, "count": 65}, {"startOffset": 1532, "endOffset": 1542, "count": 23}, {"startOffset": 1543, "endOffset": 1555, "count": 23}, {"startOffset": 1557, "endOffset": 1721, "count": 23}, {"startOffset": 1582, "endOffset": 1606, "count": 0}, {"startOffset": 1613, "endOffset": 1639, "count": 0}, {"startOffset": 1646, "endOffset": 1670, "count": 0}, {"startOffset": 1677, "endOffset": 1711, "count": 0}, {"startOffset": 1721, "endOffset": 1763, "count": 65}, {"startOffset": 1763, "endOffset": 1791, "count": 0}, {"startOffset": 1791, "endOffset": 1807, "count": 65}, {"startOffset": 1807, "endOffset": 1995, "count": 14}, {"startOffset": 1854, "endOffset": 1867, "count": 0}, {"startOffset": 1905, "endOffset": 1912, "count": 4}, {"startOffset": 1914, "endOffset": 1957, "count": 4}, {"startOffset": 1957, "endOffset": 1991, "count": 10}, {"startOffset": 1995, "endOffset": 2012, "count": 65}, {"startOffset": 2012, "endOffset": 2138, "count": 0}, {"startOffset": 2138, "endOffset": 2155, "count": 65}, {"startOffset": 2155, "endOffset": 2167, "count": 0}, {"startOffset": 2169, "endOffset": 2185, "count": 0}, {"startOffset": 2185, "endOffset": 2198, "count": 65}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 964, "endOffset": 1007, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "168", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/config/lib/set-envs.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3760, "count": 1}], "isBlockCoverage": true}, {"functionName": "env<PERSON><PERSON>", "ranges": [{"startOffset": 374, "endOffset": 523, "count": 2}, {"startOffset": 419, "endOffset": 459, "count": 1}, {"startOffset": 460, "endOffset": 521, "count": 1}], "isBlockCoverage": true}, {"functionName": "envVal", "ranges": [{"startOffset": 540, "endOffset": 715, "count": 4}, {"startOffset": 566, "endOffset": 604, "count": 1}, {"startOffset": 607, "endOffset": 715, "count": 3}, {"startOffset": 660, "endOffset": 664, "count": 0}, {"startOffset": 693, "endOffset": 699, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 576, "endOffset": 590, "count": 1}], "isBlockCoverage": true}, {"functionName": "sameConfigValue", "ranges": [{"startOffset": 741, "endOffset": 842, "count": 274}, {"startOffset": 777, "endOffset": 799, "count": 9}, {"startOffset": 800, "endOffset": 813, "count": 266}, {"startOffset": 816, "endOffset": 842, "count": 8}], "isBlockCoverage": true}, {"functionName": "sameArrayValue", "ranges": [{"startOffset": 867, "endOffset": 1265, "count": 8}, {"startOffset": 916, "endOffset": 938, "count": 0}, {"startOffset": 979, "endOffset": 1249, "count": 0}], "isBlockCoverage": true}, {"functionName": "setEnv", "ranges": [{"startOffset": 1282, "endOffset": 1425, "count": 2}, {"startOffset": 1382, "endOffset": 1397, "count": 1}, {"startOffset": 1399, "endOffset": 1423, "count": 1}], "isBlockCoverage": true}, {"functionName": "setEnvs", "ranges": [{"startOffset": 1443, "endOffset": 3733, "count": 1}, {"startOffset": 2176, "endOffset": 2855, "count": 168}, {"startOffset": 2240, "endOffset": 2245, "count": 10}, {"startOffset": 2265, "endOffset": 2287, "count": 150}, {"startOffset": 2289, "endOffset": 2311, "count": 22}, {"startOffset": 2311, "endOffset": 2367, "count": 146}, {"startOffset": 2367, "endOffset": 2613, "count": 128}, {"startOffset": 2559, "endOffset": 2607, "count": 0}, {"startOffset": 2613, "endOffset": 2851, "count": 18}, {"startOffset": 2775, "endOffset": 2794, "count": 16}, {"startOffset": 2797, "endOffset": 2845, "count": 2}, {"startOffset": 3353, "endOffset": 3405, "count": 0}, {"startOffset": 3631, "endOffset": 3643, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "169", "url": "file:///usr/lib/node_modules/npm/node_modules/which/lib/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3129, "count": 1}, {"startOffset": 608, "endOffset": 613, "count": 0}], "isBlockCoverage": true}, {"functionName": "getNotFoundError", "ranges": [{"startOffset": 717, "endOffset": 793, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPathInfo", "ranges": [{"startOffset": 815, "endOffset": 1669, "count": 1}, {"startOffset": 1110, "endOffset": 1288, "count": 0}, {"startOffset": 1307, "endOffset": 1630, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1463, "endOffset": 1499, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPathPart", "ranges": [{"startOffset": 1691, "endOffset": 1877, "count": 1}, {"startOffset": 1745, "endOffset": 1763, "count": 0}, {"startOffset": 1815, "endOffset": 1832, "count": 0}], "isBlockCoverage": true}, {"functionName": "which", "ranges": [{"startOffset": 1893, "endOffset": 2477, "count": 1}, {"startOffset": 2301, "endOffset": 2337, "count": 0}, {"startOffset": 2343, "endOffset": 2378, "count": 0}, {"startOffset": 2380, "endOffset": 2476, "count": 0}], "isBlockCoverage": true}, {"functionName": "whichSync", "ranges": [{"startOffset": 2497, "endOffset": 3081, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "170", "url": "file:///usr/lib/node_modules/npm/node_modules/which/node_modules/isexe/dist/cjs/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1951, "count": 1}, {"startOffset": 407, "endOffset": 488, "count": 0}, {"startOffset": 664, "endOffset": 706, "count": 0}, {"startOffset": 1680, "endOffset": 1687, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 88, "endOffset": 405, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 410, "endOffset": 487, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 571, "endOffset": 662, "count": 0}], "isBlockCoverage": true}, {"functionName": "__setModuleDefault", "ranges": [{"startOffset": 666, "endOffset": 706, "count": 0}], "isBlockCoverage": false}, {"functionName": "__importStar", "ranges": [{"startOffset": 759, "endOffset": 1039, "count": 2}, {"startOffset": 818, "endOffset": 1038, "count": 0}], "isBlockCoverage": true}, {"functionName": "__exportStar", "ranges": [{"startOffset": 1091, "endOffset": 1242, "count": 1}, {"startOffset": 1135, "endOffset": 1240, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "171", "url": "file:///usr/lib/node_modules/npm/node_modules/which/node_modules/isexe/dist/cjs/posix.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2072, "count": 1}], "isBlockCoverage": true}, {"functionName": "isexe", "ranges": [{"startOffset": 439, "endOffset": 741, "count": 1}, {"startOffset": 606, "endOffset": 739, "count": 0}], "isBlockCoverage": true}, {"functionName": "sync", "ranges": [{"startOffset": 915, "endOffset": 1203, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkStat", "ranges": [{"startOffset": 1244, "endOffset": 1304, "count": 1}], "isBlockCoverage": true}, {"functionName": "checkMode", "ranges": [{"startOffset": 1324, "endOffset": 2037, "count": 1}, {"startOffset": 1459, "endOffset": 1464, "count": 0}, {"startOffset": 1518, "endOffset": 1532, "count": 0}, {"startOffset": 1586, "endOffset": 1643, "count": 0}, {"startOffset": 1919, "endOffset": 1958, "count": 0}, {"startOffset": 1959, "endOffset": 1996, "count": 0}, {"startOffset": 1997, "endOffset": 2033, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "172", "url": "file:///usr/lib/node_modules/npm/node_modules/which/node_modules/isexe/dist/cjs/win32.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1877, "count": 1}], "isBlockCoverage": true}, {"functionName": "isexe", "ranges": [{"startOffset": 469, "endOffset": 777, "count": 0}], "isBlockCoverage": false}, {"functionName": "sync", "ranges": [{"startOffset": 973, "endOffset": 1267, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkPathExt", "ranges": [{"startOffset": 1311, "endOffset": 1753, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkStat", "ranges": [{"startOffset": 1773, "endOffset": 1842, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "173", "url": "file:///usr/lib/node_modules/npm/node_modules/which/node_modules/isexe/dist/cjs/options.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 112, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "174", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/config/lib/definitions/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2655, "count": 1}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 507, "endOffset": 789, "count": 7}, {"startOffset": 577, "endOffset": 773, "count": 178}, {"startOffset": 624, "endOffset": 638, "count": 167}, {"startOffset": 640, "endOffset": 681, "count": 134}, {"startOffset": 681, "endOffset": 769, "count": 44}, {"startOffset": 718, "endOffset": 738, "count": 43}, {"startOffset": 740, "endOffset": 769, "count": 1}], "isBlockCoverage": true}, {"functionName": "Object.entries.reduce.shorthands", "ranges": [{"startOffset": 853, "endOffset": 1066, "count": 158}, {"startOffset": 975, "endOffset": 1021, "count": 21}], "isBlockCoverage": true}]}, {"scriptId": "175", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/config/lib/definitions/definitions.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 74899, "count": 1}, {"startOffset": 1228, "endOffset": 1251, "count": 0}, {"startOffset": 1252, "endOffset": 1318, "count": 0}, {"startOffset": 1344, "endOffset": 1374, "count": 0}, {"startOffset": 1397, "endOffset": 1404, "count": 0}, {"startOffset": 1904, "endOffset": 1931, "count": 0}, {"startOffset": 1969, "endOffset": 1982, "count": 0}, {"startOffset": 13501, "endOffset": 13532, "count": 0}, {"startOffset": 43123, "endOffset": 43132, "count": 0}, {"startOffset": 71652, "endOffset": 71663, "count": 0}], "isBlockCoverage": true}, {"functionName": "maybeReadFile", "ranges": [{"startOffset": 329, "endOffset": 476, "count": 0}], "isBlockCoverage": false}, {"functionName": "buildOmitList", "ranges": [{"startOffset": 500, "endOffset": 1192, "count": 7}, {"startOffset": 539, "endOffset": 544, "count": 0}, {"startOffset": 569, "endOffset": 574, "count": 0}, {"startOffset": 654, "endOffset": 680, "count": 0}, {"startOffset": 716, "endOffset": 745, "count": 0}, {"startOffset": 776, "endOffset": 805, "count": 0}, {"startOffset": 822, "endOffset": 851, "count": 0}, {"startOffset": 883, "endOffset": 914, "count": 0}, {"startOffset": 947, "endOffset": 981, "count": 0}, {"startOffset": 1126, "endOffset": 1171, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1022, "endOffset": 1053, "count": 0}], "isBlockCoverage": false}, {"functionName": "getLocalAddresses", "ranges": [{"startOffset": 1481, "endOffset": 1690, "count": 1}, {"startOffset": 1655, "endOffset": 1688, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1554, "endOffset": 1594, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1569, "endOffset": 1593, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1608, "endOffset": 1641, "count": 3}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 2428, "endOffset": 2561, "count": 89}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2498, "endOffset": 2526, "count": 67}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 6316, "endOffset": 6412, "count": 1}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 9932, "endOffset": 10131, "count": 2}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 10410, "endOffset": 10524, "count": 1}, {"startOffset": 10469, "endOffset": 10518, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 10815, "endOffset": 10933, "count": 1}, {"startOffset": 10877, "endOffset": 10927, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 11276, "endOffset": 11696, "count": 1}, {"startOffset": 11397, "endOffset": 11695, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11612, "endOffset": 11637, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11652, "endOffset": 11689, "count": 0}], "isBlockCoverage": false}, {"functionName": "flatten", "ranges": [{"startOffset": 13899, "endOffset": 14177, "count": 1}, {"startOffset": 13970, "endOffset": 13977, "count": 0}, {"startOffset": 14011, "endOffset": 14017, "count": 0}, {"startOffset": 14091, "endOffset": 14098, "count": 0}, {"startOffset": 14132, "endOffset": 14138, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 15319, "endOffset": 15462, "count": 1}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 15658, "endOffset": 15754, "count": 1}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 19708, "endOffset": 19840, "count": 1}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 20064, "endOffset": 20195, "count": 1}, {"startOffset": 20142, "endOffset": 20147, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 20478, "endOffset": 20613, "count": 1}, {"startOffset": 20556, "endOffset": 20561, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 20898, "endOffset": 21033, "count": 1}, {"startOffset": 20976, "endOffset": 20981, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 21298, "endOffset": 21374, "count": 1}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 25075, "endOffset": 25226, "count": 1}, {"startOffset": 25171, "endOffset": 25220, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 25997, "endOffset": 26158, "count": 1}, {"startOffset": 26051, "endOffset": 26152, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 28255, "endOffset": 28416, "count": 1}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 35417, "endOffset": 35576, "count": 1}, {"startOffset": 35471, "endOffset": 35570, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 38008, "endOffset": 38240, "count": 1}, {"startOffset": 38104, "endOffset": 38153, "count": 0}, {"startOffset": 38191, "endOffset": 38234, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 39408, "endOffset": 39513, "count": 1}, {"startOffset": 39482, "endOffset": 39507, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 40051, "endOffset": 40139, "count": 1}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 41093, "endOffset": 41172, "count": 1}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 42566, "endOffset": 42753, "count": 1}, {"startOffset": 42693, "endOffset": 42747, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 43886, "endOffset": 43969, "count": 7}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 44708, "endOffset": 44804, "count": 1}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 45195, "endOffset": 45291, "count": 1}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 45980, "endOffset": 46168, "count": 1}, {"startOffset": 46070, "endOffset": 46162, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 46708, "endOffset": 46867, "count": 2}, {"startOffset": 46813, "endOffset": 46861, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 47446, "endOffset": 47605, "count": 1}, {"startOffset": 47551, "endOffset": 47599, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 49942, "endOffset": 50038, "count": 1}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 50457, "endOffset": 50895, "count": 1}, {"startOffset": 50534, "endOffset": 50541, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 54185, "endOffset": 54539, "count": 1}, {"startOffset": 54513, "endOffset": 54533, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 54812, "endOffset": 55027, "count": 1}, {"startOffset": 54913, "endOffset": 54962, "count": 0}, {"startOffset": 54985, "endOffset": 55026, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 55310, "endOffset": 55501, "count": 1}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 55790, "endOffset": 56310, "count": 1}, {"startOffset": 55896, "endOffset": 55945, "count": 0}, {"startOffset": 55996, "endOffset": 56047, "count": 0}, {"startOffset": 56070, "endOffset": 56304, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 56568, "endOffset": 57088, "count": 1}, {"startOffset": 56670, "endOffset": 56719, "count": 0}, {"startOffset": 56770, "endOffset": 56825, "count": 0}, {"startOffset": 56848, "endOffset": 57082, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 57610, "endOffset": 57774, "count": 2}, {"startOffset": 57693, "endOffset": 57697, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 58343, "endOffset": 58560, "count": 1}, {"startOffset": 58445, "endOffset": 58494, "count": 0}, {"startOffset": 58517, "endOffset": 58559, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 60160, "endOffset": 60440, "count": 1}, {"startOffset": 60249, "endOffset": 60269, "count": 0}, {"startOffset": 60270, "endOffset": 60283, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 60784, "endOffset": 60877, "count": 1}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 61067, "endOffset": 61227, "count": 1}, {"startOffset": 61147, "endOffset": 61163, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 61445, "endOffset": 61578, "count": 1}, {"startOffset": 61525, "endOffset": 61530, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 61760, "endOffset": 61922, "count": 1}, {"startOffset": 61840, "endOffset": 61856, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 62174, "endOffset": 62322, "count": 1}, {"startOffset": 62254, "endOffset": 62270, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 62830, "endOffset": 62988, "count": 1}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 64923, "endOffset": 65001, "count": 1}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 65656, "endOffset": 65735, "count": 1}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 69440, "endOffset": 70559, "count": 4}, {"startOffset": 69535, "endOffset": 69548, "count": 0}, {"startOffset": 69678, "endOffset": 69715, "count": 0}, {"startOffset": 70060, "endOffset": 70076, "count": 0}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 72992, "endOffset": 73098, "count": 1}], "isBlockCoverage": true}, {"functionName": "flatten", "ranges": [{"startOffset": 73835, "endOffset": 74336, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "176", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/config/lib/definitions/definition.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6672, "count": 1}], "isBlockCoverage": true}, {"functionName": "Definition", "ranges": [{"startOffset": 714, "endOffset": 1369, "count": 158}, {"startOffset": 938, "endOffset": 1005, "count": 133}, {"startOffset": 1037, "endOffset": 1097, "count": 157}, {"startOffset": 1166, "endOffset": 1297, "count": 140}, {"startOffset": 1200, "endOffset": 1240, "count": 12}, {"startOffset": 1240, "endOffset": 1291, "count": 128}, {"startOffset": 1319, "endOffset": 1365, "count": 155}], "isBlockCoverage": true}, {"functionName": "validate", "ranges": [{"startOffset": 1373, "endOffset": 1817, "count": 158}, {"startOffset": 1419, "endOffset": 1556, "count": 632}, {"startOffset": 1481, "endOffset": 1550, "count": 0}, {"startOffset": 1576, "endOffset": 1638, "count": 0}, {"startOffset": 1682, "endOffset": 1813, "count": 1010}, {"startOffset": 1720, "endOffset": 1807, "count": 0}], "isBlockCoverage": true}, {"functionName": "describe", "ranges": [{"startOffset": 1889, "endOffset": 2532, "count": 0}], "isBlockCoverage": false}, {"functionName": "describeUsage", "ranges": [{"startOffset": 2558, "endOffset": 4046, "count": 155}, {"startOffset": 2632, "endOffset": 2927, "count": 110}, {"startOffset": 2653, "endOffset": 2689, "count": 15}, {"startOffset": 2720, "endOffset": 2744, "count": 60}, {"startOffset": 2746, "endOffset": 2789, "count": 14}, {"startOffset": 2789, "endOffset": 2835, "count": 96}, {"startOffset": 2867, "endOffset": 2907, "count": 50}, {"startOffset": 2927, "endOffset": 2969, "count": 45}, {"startOffset": 2969, "endOffset": 3013, "count": 3}, {"startOffset": 3013, "endOffset": 3390, "count": 45}, {"startOffset": 3390, "endOffset": 3410, "count": 5}, {"startOffset": 3410, "endOffset": 3477, "count": 40}, {"startOffset": 3477, "endOffset": 3582, "count": 13}, {"startOffset": 3582, "endOffset": 3652, "count": 27}, {"startOffset": 3652, "endOffset": 3666, "count": 40}, {"startOffset": 3666, "endOffset": 3916, "count": 1}, {"startOffset": 3916, "endOffset": 3974, "count": 40}, {"startOffset": 3974, "endOffset": 4016, "count": 8}, {"startOffset": 4016, "endOffset": 4044, "count": 32}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3319, "endOffset": 3366, "count": 130}, {"startOffset": 3335, "endOffset": 3349, "count": 100}, {"startOffset": 3350, "endOffset": 3366, "count": 92}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3448, "endOffset": 3474, "count": 80}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3558, "endOffset": 3564, "count": 48}], "isBlockCoverage": true}, {"functionName": "describeType", "ranges": [{"startOffset": 4069, "endOffset": 5319, "count": 273}, {"startOffset": 4106, "endOffset": 4698, "count": 45}, {"startOffset": 4422, "endOffset": 4444, "count": 41}, {"startOffset": 4445, "endOffset": 4449, "count": 4}, {"startOffset": 4493, "endOffset": 4502, "count": 14}, {"startOffset": 4503, "endOffset": 4511, "count": 31}, {"startOffset": 4624, "endOffset": 4656, "count": 8}, {"startOffset": 4657, "endOffset": 4661, "count": 37}, {"startOffset": 4698, "endOffset": 4923, "count": 228}, {"startOffset": 4923, "endOffset": 4957, "count": 42}, {"startOffset": 4962, "endOffset": 4996, "count": 15}, {"startOffset": 5001, "endOffset": 5077, "count": 1}, {"startOffset": 5082, "endOffset": 5118, "count": 69}, {"startOffset": 5123, "endOffset": 5153, "count": 1}, {"startOffset": 5158, "endOffset": 5188, "count": 10}, {"startOffset": 5193, "endOffset": 5234, "count": 2}, {"startOffset": 5239, "endOffset": 5267, "count": 5}, {"startOffset": 5272, "endOffset": 5313, "count": 83}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4145, "endOffset": 4161, "count": 124}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4167, "endOffset": 4187, "count": 116}], "isBlockCoverage": true}, {"functionName": "describeValue", "ranges": [{"startOffset": 5407, "endOffset": 5475, "count": 216}, {"startOffset": 5439, "endOffset": 5460, "count": 84}, {"startOffset": 5461, "endOffset": 5474, "count": 132}], "isBlockCoverage": true}, {"functionName": "unindent", "ranges": [{"startOffset": 5494, "endOffset": 5718, "count": 0}], "isBlockCoverage": false}, {"functionName": "wrap", "ranges": [{"startOffset": 5733, "endOffset": 6053, "count": 0}], "isBlockCoverage": false}, {"functionName": "wrapAll", "ranges": [{"startOffset": 6071, "endOffset": 6642, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "177", "url": "file:///usr/lib/node_modules/npm/node_modules/ci-info/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2746, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 173, "endOffset": 213, "count": 51}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 294, "endOffset": 617, "count": 51}, {"startOffset": 355, "endOffset": 367, "count": 2}, {"startOffset": 368, "endOffset": 382, "count": 49}, {"startOffset": 521, "endOffset": 616, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 409, "endOffset": 454, "count": 51}], "isBlockCoverage": true}, {"functionName": "checkEnv", "ranges": [{"startOffset": 1191, "endOffset": 1736, "count": 51}, {"startOffset": 1269, "endOffset": 1286, "count": 46}, {"startOffset": 1286, "endOffset": 1380, "count": 5}, {"startOffset": 1380, "endOffset": 1557, "count": 1}, {"startOffset": 1557, "endOffset": 1579, "count": 4}, {"startOffset": 1579, "endOffset": 1652, "count": 1}, {"startOffset": 1652, "endOffset": 1735, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1605, "endOffset": 1647, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1686, "endOffset": 1733, "count": 3}], "isBlockCoverage": true}, {"functionName": "checkPR", "ranges": [{"startOffset": 1738, "endOffset": 2745, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "178", "url": "file:///usr/lib/node_modules/npm/lib/utils/npm-usage.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2064, "count": 1}], "isBlockCoverage": true}, {"functionName": "indent", "ranges": [{"startOffset": 137, "endOffset": 176, "count": 0}], "isBlockCoverage": false}, {"functionName": "indentNewline", "ranges": [{"startOffset": 199, "endOffset": 232, "count": 0}], "isBlockCoverage": false}, {"functionName": "module.exports", "ranges": [{"startOffset": 251, "endOffset": 1174, "count": 0}], "isBlockCoverage": false}, {"functionName": "cmdNames", "ranges": [{"startOffset": 1193, "endOffset": 1591, "count": 0}], "isBlockCoverage": false}, {"functionName": "cmdUsages", "ranges": [{"startOffset": 1611, "endOffset": 2063, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "179", "url": "file:///usr/lib/node_modules/npm/lib/utils/cmd-list.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2947, "count": 1}], "isBlockCoverage": true}, {"functionName": "deref", "ranges": [{"startOffset": 2217, "endOffset": 2892, "count": 3}, {"startOffset": 2236, "endOffset": 2252, "count": 0}, {"startOffset": 2352, "endOffset": 2415, "count": 0}, {"startOffset": 2516, "endOffset": 2891, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2384, "endOffset": 2410, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "180", "url": "file:///usr/lib/node_modules/npm/node_modules/abbrev/lib/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1379, "count": 1}], "isBlockCoverage": true}, {"functionName": "abbrev", "ranges": [{"startOffset": 25, "endOffset": 1311, "count": 0}], "isBlockCoverage": false}, {"functionName": "lexSort", "ranges": [{"startOffset": 1313, "endOffset": 1378, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "181", "url": "file:///usr/lib/node_modules/npm/lib/utils/log-file.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7971, "count": 1}], "isBlockCoverage": true}, {"functionName": "padZero", "ranges": [{"startOffset": 271, "endOffset": 338, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 340, "endOffset": 7943, "count": 1}], "isBlockCoverage": true}, {"functionName": "LogFiles", "ranges": [{"startOffset": 1102, "endOffset": 1301, "count": 1}], "isBlockCoverage": true}, {"functionName": "on", "ranges": [{"startOffset": 1305, "endOffset": 1356, "count": 1}], "isBlockCoverage": true}, {"functionName": "off", "ranges": [{"startOffset": 1360, "endOffset": 1435, "count": 1}], "isBlockCoverage": true}, {"functionName": "load", "ranges": [{"startOffset": 1439, "endOffset": 2766, "count": 1}, {"startOffset": 1541, "endOffset": 1561, "count": 0}, {"startOffset": 1834, "endOffset": 1854, "count": 0}, {"startOffset": 2264, "endOffset": 2421, "count": 22}, {"startOffset": 2357, "endOffset": 2411, "count": 16}, {"startOffset": 2516, "endOffset": 2539, "count": 0}], "isBlockCoverage": true}, {"functionName": "get files", "ranges": [{"startOffset": 2770, "endOffset": 2811, "count": 2}], "isBlockCoverage": true}, {"functionName": "get #isBuffered", "ranges": [{"startOffset": 2815, "endOffset": 2881, "count": 36}], "isBlockCoverage": true}, {"functionName": "#endStream", "ranges": [{"startOffset": 2885, "endOffset": 3027, "count": 1}], "isBlockCoverage": true}, {"functionName": "#logHandler", "ranges": [{"startOffset": 3045, "endOffset": 4165, "count": 38}, {"startOffset": 3180, "endOffset": 3201, "count": 37}, {"startOffset": 3203, "endOffset": 3223, "count": 3}, {"startOffset": 3223, "endOffset": 3297, "count": 35}, {"startOffset": 3297, "endOffset": 3317, "count": 0}, {"startOffset": 3317, "endOffset": 3345, "count": 35}, {"startOffset": 3345, "endOffset": 3501, "count": 22}, {"startOffset": 3501, "endOffset": 3589, "count": 13}, {"startOffset": 3589, "endOffset": 3609, "count": 4}, {"startOffset": 3609, "endOffset": 3736, "count": 9}, {"startOffset": 3736, "endOffset": 4109, "count": 0}, {"startOffset": 4109, "endOffset": 4161, "count": 9}], "isBlockCoverage": true}, {"functionName": "#formatLogItem", "ranges": [{"startOffset": 4169, "endOffset": 4527, "count": 35}, {"startOffset": 4308, "endOffset": 4324, "count": 10}, {"startOffset": 4326, "endOffset": 4351, "count": 10}, {"startOffset": 4351, "endOffset": 4437, "count": 25}, {"startOffset": 4437, "endOffset": 4444, "count": 0}], "isBlockCoverage": true}, {"functionName": "#getLogFilePath", "ranges": [{"startOffset": 4531, "endOffset": 4610, "count": 2}], "isBlockCoverage": true}, {"functionName": "#openLogFile", "ranges": [{"startOffset": 4614, "endOffset": 5876, "count": 1}, {"startOffset": 5480, "endOffset": 5602, "count": 0}, {"startOffset": 5671, "endOffset": 5872, "count": 0}], "isBlockCoverage": true}, {"functionName": "#cleanLogs", "ranges": [{"startOffset": 5880, "endOffset": 7941, "count": 1}, {"startOffset": 6796, "endOffset": 7262, "count": 17}, {"startOffset": 6826, "endOffset": 6856, "count": 0}, {"startOffset": 7118, "endOffset": 7153, "count": 12}, {"startOffset": 7164, "endOffset": 7198, "count": 12}, {"startOffset": 7209, "endOffset": 7254, "count": 11}, {"startOffset": 7345, "endOffset": 7369, "count": 0}, {"startOffset": 7575, "endOffset": 7663, "count": 0}, {"startOffset": 7678, "endOffset": 7867, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6670, "endOffset": 6734, "count": 16}], "isBlockCoverage": true}]}, {"scriptId": "182", "url": "file:///usr/lib/node_modules/npm/node_modules/fs-minipass/lib/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9884, "count": 1}], "isBlockCoverage": true}, {"functionName": "ReadStream", "ranges": [{"startOffset": 1145, "endOffset": 1836, "count": 0}], "isBlockCoverage": false}, {"functionName": "get fd", "ranges": [{"startOffset": 1840, "endOffset": 1876, "count": 0}], "isBlockCoverage": false}, {"functionName": "get path", "ranges": [{"startOffset": 1880, "endOffset": 1920, "count": 0}], "isBlockCoverage": false}, {"functionName": "write", "ranges": [{"startOffset": 1924, "endOffset": 1991, "count": 0}], "isBlockCoverage": false}, {"functionName": "end", "ranges": [{"startOffset": 1995, "endOffset": 2060, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2064, "endOffset": 2145, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2149, "endOffset": 2300, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2304, "endOffset": 2395, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2399, "endOffset": 2747, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2751, "endOffset": 2918, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2922, "endOffset": 3131, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3135, "endOffset": 3228, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3232, "endOffset": 3547, "count": 0}], "isBlockCoverage": false}, {"functionName": "emit", "ranges": [{"startOffset": 3551, "endOffset": 3965, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4013, "endOffset": 4209, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4213, "endOffset": 4743, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4747, "endOffset": 4929, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteStream", "ranges": [{"startOffset": 4966, "endOffset": 5796, "count": 1}, {"startOffset": 5006, "endOffset": 5011, "count": 0}, {"startOffset": 5274, "endOffset": 5282, "count": 0}, {"startOffset": 5339, "endOffset": 5349, "count": 0}, {"startOffset": 5397, "endOffset": 5408, "count": 0}, {"startOffset": 5474, "endOffset": 5495, "count": 0}, {"startOffset": 5610, "endOffset": 5616, "count": 0}, {"startOffset": 5710, "endOffset": 5723, "count": 0}], "isBlockCoverage": true}, {"functionName": "emit", "ranges": [{"startOffset": 5800, "endOffset": 5964, "count": 3}, {"startOffset": 5842, "endOffset": 5928, "count": 0}], "isBlockCoverage": true}, {"functionName": "get fd", "ranges": [{"startOffset": 5968, "endOffset": 6004, "count": 0}], "isBlockCoverage": false}, {"functionName": "get path", "ranges": [{"startOffset": 6008, "endOffset": 6048, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6052, "endOffset": 6145, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6149, "endOffset": 6258, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6262, "endOffset": 6606, "count": 1}, {"startOffset": 6310, "endOffset": 6342, "count": 0}, {"startOffset": 6343, "endOffset": 6356, "count": 0}, {"startOffset": 6357, "endOffset": 6380, "count": 0}, {"startOffset": 6382, "endOffset": 6434, "count": 0}, {"startOffset": 6448, "endOffset": 6480, "count": 0}], "isBlockCoverage": true}, {"functionName": "end", "ranges": [{"startOffset": 6610, "endOffset": 6908, "count": 1}, {"startOffset": 6640, "endOffset": 6674, "count": 0}], "isBlockCoverage": true}, {"functionName": "write", "ranges": [{"startOffset": 6912, "endOffset": 7339, "count": 25}, {"startOffset": 7029, "endOffset": 7114, "count": 0}, {"startOffset": 7185, "endOffset": 7270, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7343, "endOffset": 7461, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7465, "endOffset": 7974, "count": 26}, {"startOffset": 7499, "endOffset": 7531, "count": 0}, {"startOffset": 7570, "endOffset": 7604, "count": 0}, {"startOffset": 7636, "endOffset": 7668, "count": 0}, {"startOffset": 7733, "endOffset": 7752, "count": 1}, {"startOffset": 7754, "endOffset": 7853, "count": 1}, {"startOffset": 7853, "endOffset": 7956, "count": 25}, {"startOffset": 7881, "endOffset": 7956, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7978, "endOffset": 8344, "count": 1}, {"startOffset": 8053, "endOffset": 8094, "count": 0}, {"startOffset": 8100, "endOffset": 8340, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8299, "endOffset": 8333, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8348, "endOffset": 8557, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8607, "endOffset": 9184, "count": 1}, {"startOffset": 8794, "endOffset": 8818, "count": 0}, {"startOffset": 8820, "endOffset": 9075, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9188, "endOffset": 9370, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9374, "endOffset": 9731, "count": 25}, {"startOffset": 9623, "endOffset": 9721, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "183", "url": "file:///usr/lib/node_modules/npm/node_modules/minipass/dist/commonjs/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33796, "count": 1}, {"startOffset": 374, "endOffset": 427, "count": 0}], "isBlockCoverage": true}, {"functionName": "__importDefault", "ranges": [{"startOffset": 70, "endOffset": 151, "count": 1}, {"startOffset": 122, "endOffset": 127, "count": 0}], "isBlockCoverage": true}, {"functionName": "isStream", "ranges": [{"startOffset": 745, "endOffset": 937, "count": 0}], "isBlockCoverage": false}, {"functionName": "isReadable", "ranges": [{"startOffset": 1063, "endOffset": 1319, "count": 0}], "isBlockCoverage": false}, {"functionName": "isWritable", "ranges": [{"startOffset": 1449, "endOffset": 1607, "count": 0}], "isBlockCoverage": false}, {"functionName": "defer", "ranges": [{"startOffset": 2884, "endOffset": 2918, "count": 0}], "isBlockCoverage": false}, {"functionName": "nodefer", "ranges": [{"startOffset": 2936, "endOffset": 2948, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2967, "endOffset": 3028, "count": 0}], "isBlockCoverage": false}, {"functionName": "isArrayBufferLike", "ranges": [{"startOffset": 3056, "endOffset": 3235, "count": 0}], "isBlockCoverage": false}, {"functionName": "isArrayBuffer<PERSON>iew", "ranges": [{"startOffset": 3263, "endOffset": 3314, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3403, "endOffset": 3951, "count": 0}], "isBlockCoverage": true}, {"functionName": "unpipe", "ranges": [{"startOffset": 4108, "endOffset": 4208, "count": 0}], "isBlockCoverage": false}, {"functionName": "PipeProxyErrors", "ranges": [{"startOffset": 4213, "endOffset": 4381, "count": 0}], "isBlockCoverage": false}, {"functionName": "isObjectModeOptions", "ranges": [{"startOffset": 4412, "endOffset": 4433, "count": 0}], "isBlockCoverage": false}, {"functionName": "isEncodingOptions", "ranges": [{"startOffset": 4461, "endOffset": 4524, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4879, "endOffset": 33733, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "184", "url": "file:///usr/lib/node_modules/npm/lib/utils/format.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1957, "count": 1}], "isBlockCoverage": true}, {"functionName": "STRIP_C01", "ranges": [{"startOffset": 571, "endOffset": 1368, "count": 32}, {"startOffset": 644, "endOffset": 1367, "count": 0}], "isBlockCoverage": true}, {"functionName": "formatWithOptions", "ranges": [{"startOffset": 1396, "endOffset": 1916, "count": 32}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1495, "endOffset": 1509, "count": 93}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1845, "endOffset": 1909, "count": 35}, {"startOffset": 1882, "endOffset": 1886, "count": 31}, {"startOffset": 1887, "endOffset": 1892, "count": 18}, {"startOffset": 1893, "endOffset": 1897, "count": 17}], "isBlockCoverage": true}]}, {"scriptId": "185", "url": "file:///usr/lib/node_modules/npm/lib/utils/timers.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2120, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 136, "endOffset": 2094, "count": 1}], "isBlockCoverage": true}, {"functionName": "Timers", "ranges": [{"startOffset": 227, "endOffset": 358, "count": 1}], "isBlockCoverage": true}, {"functionName": "on", "ranges": [{"startOffset": 362, "endOffset": 415, "count": 1}], "isBlockCoverage": true}, {"functionName": "off", "ranges": [{"startOffset": 419, "endOffset": 474, "count": 1}], "isBlockCoverage": true}, {"functionName": "load", "ranges": [{"startOffset": 478, "endOffset": 576, "count": 1}], "isBlockCoverage": true}, {"functionName": "finish", "ranges": [{"startOffset": 580, "endOffset": 1036, "count": 1}, {"startOffset": 679, "endOffset": 739, "count": 0}, {"startOffset": 837, "endOffset": 1032, "count": 0}], "isBlockCoverage": true}, {"functionName": "#writeFile", "ranges": [{"startOffset": 1040, "endOffset": 1549, "count": 0}], "isBlockCoverage": false}, {"functionName": "#timeHandler", "ranges": [{"startOffset": 1568, "endOffset": 2092, "count": 20}, {"startOffset": 1641, "endOffset": 1716, "count": 10}, {"startOffset": 1723, "endOffset": 2082, "count": 10}, {"startOffset": 1977, "endOffset": 2074, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "186", "url": "file:///usr/lib/node_modules/npm/lib/utils/display.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15729, "count": 1}], "isBlockCoverage": true}, {"functionName": "COLOR_PALETTE", "ranges": [{"startOffset": 564, "endOffset": 802, "count": 1}], "isBlockCoverage": true}, {"functionName": "show", "ranges": [{"startOffset": 1149, "endOffset": 1195, "count": 10}, {"startOffset": 1181, "endOffset": 1195, "count": 0}], "isBlockCoverage": true}, {"functionName": "setBlocking", "ranges": [{"startOffset": 1225, "endOffset": 1577, "count": 2}], "isBlockCoverage": true}, {"functionName": "isPlainObject", "ranges": [{"startOffset": 1867, "endOffset": 1921, "count": 0}], "isBlockCoverage": false}, {"functionName": "getArrayOrObject", "ranges": [{"startOffset": 1948, "endOffset": 2478, "count": 0}], "isBlockCoverage": false}, {"functionName": "get<PERSON>sonBuffer", "ranges": [{"startOffset": 2502, "endOffset": 3951, "count": 0}], "isBlockCoverage": false}, {"functionName": "with<PERSON>eta", "ranges": [{"startOffset": 3970, "endOffset": 4185, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3983, "endOffset": 4185, "count": 44}, {"startOffset": 4059, "endOffset": 4086, "count": 36}, {"startOffset": 4087, "endOffset": 4115, "count": 1}, {"startOffset": 4117, "endOffset": 4144, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4187, "endOffset": 12878, "count": 1}], "isBlockCoverage": true}, {"functionName": "Display", "ranges": [{"startOffset": 4573, "endOffset": 4940, "count": 1}], "isBlockCoverage": true}, {"functionName": "off", "ranges": [{"startOffset": 4944, "endOffset": 5191, "count": 1}], "isBlockCoverage": true}, {"functionName": "get chalk", "ranges": [{"startOffset": 5195, "endOffset": 5333, "count": 0}], "isBlockCoverage": false}, {"functionName": "load", "ranges": [{"startOffset": 5337, "endOffset": 6754, "count": 1}, {"startOffset": 6159, "endOffset": 6179, "count": 0}, {"startOffset": 6275, "endOffset": 6295, "count": 0}], "isBlockCoverage": true}, {"functionName": "#write", "ranges": [{"startOffset": 6837, "endOffset": 7080, "count": 7}, {"startOffset": 6916, "endOffset": 6935, "count": 1}, {"startOffset": 6936, "endOffset": 6955, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7050, "endOffset": 7075, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7251, "endOffset": 7799, "count": 38}, {"startOffset": 7306, "endOffset": 7499, "count": 2}, {"startOffset": 7507, "endOffset": 7581, "count": 1}, {"startOffset": 7589, "endOffset": 7789, "count": 35}, {"startOffset": 7636, "endOffset": 7708, "count": 16}, {"startOffset": 7708, "endOffset": 7775, "count": 19}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7407, "endOffset": 7443, "count": 16}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7959, "endOffset": 9843, "count": 4}, {"startOffset": 8035, "endOffset": 8046, "count": 1}, {"startOffset": 8047, "endOffset": 8059, "count": 3}, {"startOffset": 8087, "endOffset": 8548, "count": 3}, {"startOffset": 8181, "endOffset": 8384, "count": 0}, {"startOffset": 8556, "endOffset": 8671, "count": 0}, {"startOffset": 8679, "endOffset": 9833, "count": 1}, {"startOffset": 8729, "endOffset": 8804, "count": 0}, {"startOffset": 9138, "endOffset": 9260, "count": 0}, {"startOffset": 9282, "endOffset": 9747, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8435, "endOffset": 8471, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9873, "endOffset": 10732, "count": 2}, {"startOffset": 9928, "endOffset": 10056, "count": 1}, {"startOffset": 10064, "endOffset": 10174, "count": 1}, {"startOffset": 10182, "endOffset": 10722, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10434, "endOffset": 10713, "count": 0}], "isBlockCoverage": false}, {"functionName": "#writeOutput", "ranges": [{"startOffset": 10750, "endOffset": 11004, "count": 1}, {"startOffset": 10910, "endOffset": 10994, "count": 0}], "isBlockCoverage": true}, {"functionName": "#tryWriteLog", "ranges": [{"startOffset": 11019, "endOffset": 12251, "count": 35}, {"startOffset": 11505, "endOffset": 11530, "count": 6}, {"startOffset": 11531, "endOffset": 11538, "count": 0}, {"startOffset": 11539, "endOffset": 11566, "count": 0}, {"startOffset": 11568, "endOffset": 11723, "count": 0}, {"startOffset": 11773, "endOffset": 12247, "count": 0}], "isBlockCoverage": true}, {"functionName": "#writeLog", "ranges": [{"startOffset": 12255, "endOffset": 12876, "count": 35}, {"startOffset": 12365, "endOffset": 12409, "count": 25}, {"startOffset": 12439, "endOffset": 12455, "count": 0}, {"startOffset": 12531, "endOffset": 12872, "count": 6}, {"startOffset": 12798, "endOffset": 12804, "count": 0}], "isBlockCoverage": true}, {"functionName": "show", "ranges": [{"startOffset": 12369, "endOffset": 12408, "count": 25}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 12880, "endOffset": 15702, "count": 1}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 13155, "endOffset": 13218, "count": 1}], "isBlockCoverage": true}, {"functionName": "get #rendering", "ranges": [{"startOffset": 13416, "endOffset": 13482, "count": 1}], "isBlockCoverage": true}, {"functionName": "get #spinning", "ranges": [{"startOffset": 13569, "endOffset": 13634, "count": 14}, {"startOffset": 13613, "endOffset": 13630, "count": 2}], "isBlockCoverage": true}, {"functionName": "Progress", "ranges": [{"startOffset": 13638, "endOffset": 13694, "count": 1}], "isBlockCoverage": true}, {"functionName": "load", "ranges": [{"startOffset": 13698, "endOffset": 13893, "count": 1}, {"startOffset": 13800, "endOffset": 13816, "count": 0}], "isBlockCoverage": true}, {"functionName": "off", "ranges": [{"startOffset": 13897, "endOffset": 14146, "count": 2}, {"startOffset": 13930, "endOffset": 13950, "count": 0}], "isBlockCoverage": true}, {"functionName": "resume", "ranges": [{"startOffset": 14150, "endOffset": 14184, "count": 1}], "isBlockCoverage": true}, {"functionName": "write", "ranges": [{"startOffset": 14368, "endOffset": 14511, "count": 7}, {"startOffset": 14408, "endOffset": 14442, "count": 0}, {"startOffset": 14479, "endOffset": 14507, "count": 0}], "isBlockCoverage": true}, {"functionName": "#render", "ranges": [{"startOffset": 14515, "endOffset": 14798, "count": 2}, {"startOffset": 14542, "endOffset": 14794, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14577, "endOffset": 14651, "count": 0}], "isBlockCoverage": false}, {"functionName": "#renderSpinner", "ranges": [{"startOffset": 14802, "endOffset": 15249, "count": 1}, {"startOffset": 14848, "endOffset": 14868, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15191, "endOffset": 15220, "count": 0}], "isBlockCoverage": false}, {"functionName": "#renderFrame", "ranges": [{"startOffset": 15253, "endOffset": 15547, "count": 1}, {"startOffset": 15411, "endOffset": 15449, "count": 0}], "isBlockCoverage": true}, {"functionName": "#clearSpinner", "ranges": [{"startOffset": 15551, "endOffset": 15700, "count": 3}], "isBlockCoverage": true}]}, {"scriptId": "187", "url": "file:///usr/lib/node_modules/npm/lib/utils/explain-eresolve.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2617, "count": 1}], "isBlockCoverage": true}, {"functionName": "explain", "ranges": [{"startOffset": 600, "endOffset": 1849, "count": 0}], "isBlockCoverage": false}, {"functionName": "report", "ranges": [{"startOffset": 1932, "endOffset": 2573, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "188", "url": "file:///usr/lib/node_modules/npm/lib/utils/explain-dep.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3103, "count": 1}], "isBlockCoverage": true}, {"functionName": "explainNode", "ranges": [{"startOffset": 63, "endOffset": 192, "count": 0}], "isBlockCoverage": false}, {"functionName": "colorType", "ranges": [{"startOffset": 212, "endOffset": 597, "count": 0}], "isBlockCoverage": false}, {"functionName": "printNode", "ranges": [{"startOffset": 617, "endOffset": 1052, "count": 0}], "isBlockCoverage": false}, {"functionName": "explainLinksIn", "ranges": [{"startOffset": 1077, "endOffset": 1336, "count": 0}], "isBlockCoverage": false}, {"functionName": "explainDependents", "ranges": [{"startOffset": 1364, "endOffset": 2264, "count": 0}], "isBlockCoverage": false}, {"functionName": "explainEdge", "ranges": [{"startOffset": 2286, "endOffset": 2798, "count": 0}], "isBlockCoverage": false}, {"functionName": "explainFrom", "ranges": [{"startOffset": 2820, "endOffset": 3044, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "189", "url": "file:///usr/lib/node_modules/npm/lib/utils/output-error.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 767, "count": 1}], "isBlockCoverage": true}, {"functionName": "outputError", "ranges": [{"startOffset": 65, "endOffset": 401, "count": 0}], "isBlockCoverage": false}, {"functionName": "jsonError", "ranges": [{"startOffset": 421, "endOffset": 716, "count": 1}, {"startOffset": 451, "endOffset": 465, "count": 0}, {"startOffset": 466, "endOffset": 492, "count": 0}, {"startOffset": 494, "endOffset": 714, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 574, "endOffset": 599, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 659, "endOffset": 684, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "190", "url": "node:tty", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5045, "count": 1}], "isBlockCoverage": false}, {"functionName": "isatty", "ranges": [{"startOffset": 1526, "endOffset": 1632, "count": 4}], "isBlockCoverage": true}, {"functionName": "ReadStream", "ranges": [{"startOffset": 1634, "endOffset": 2103, "count": 1}, {"startOffset": 1710, "endOffset": 1745, "count": 0}, {"startOffset": 1782, "endOffset": 1811, "count": 0}, {"startOffset": 1893, "endOffset": 1938, "count": 0}], "isBlockCoverage": true}, {"functionName": "ReadStream.setRawMode", "ranges": [{"startOffset": 2252, "endOffset": 2473, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteStream", "ranges": [{"startOffset": 2476, "endOffset": 3442, "count": 2}, {"startOffset": 2545, "endOffset": 2572, "count": 0}, {"startOffset": 2609, "endOffset": 2638, "count": 0}, {"startOffset": 2720, "endOffset": 2765, "count": 0}], "isBlockCoverage": true}, {"functionName": "WriteStream._refreshSize", "ranges": [{"startOffset": 3733, "endOffset": 4167, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteStream.cursorTo", "ranges": [{"startOffset": 4223, "endOffset": 4363, "count": 3}, {"startOffset": 4280, "endOffset": 4311, "count": 1}], "isBlockCoverage": true}, {"functionName": "WriteStream.moveCursor", "ranges": [{"startOffset": 4400, "endOffset": 4546, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteStream.clearLine", "ranges": [{"startOffset": 4582, "endOffset": 4721, "count": 3}, {"startOffset": 4638, "endOffset": 4669, "count": 0}], "isBlockCoverage": true}, {"functionName": "WriteStream.clearScreenDown", "ranges": [{"startOffset": 4763, "endOffset": 4898, "count": 0}], "isBlockCoverage": false}, {"functionName": "WriteStream.getWindowSize", "ranges": [{"startOffset": 4938, "endOffset": 4988, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "191", "url": "node:net", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 68429, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1645, "endOffset": 1670, "count": 0}], "isBlockCoverage": false}, {"functionName": "noop", "ranges": [{"startOffset": 4172, "endOffset": 4180, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFlags", "ranges": [{"startOffset": 4653, "endOffset": 4747, "count": 0}], "isBlockCoverage": false}, {"functionName": "createHandle", "ranges": [{"startOffset": 4749, "endOffset": 5132, "count": 0}], "isBlockCoverage": false}, {"functionName": "getNewAsyncId", "ranges": [{"startOffset": 5135, "endOffset": 5273, "count": 6}, {"startOffset": 5230, "endOffset": 5248, "count": 0}], "isBlockCoverage": true}, {"functionName": "isPipeName", "ranges": [{"startOffset": 5276, "endOffset": 5359, "count": 0}], "isBlockCoverage": false}, {"functionName": "createServer", "ranges": [{"startOffset": 5560, "endOffset": 5664, "count": 0}], "isBlockCoverage": false}, {"functionName": "connect", "ranges": [{"startOffset": 5887, "endOffset": 6288, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDefaultAutoSelectFamily", "ranges": [{"startOffset": 6290, "endOffset": 6365, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDefaultAutoSelectFamily", "ranges": [{"startOffset": 6367, "endOffset": 6483, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDefaultAutoSelectFamilyAttemptTimeout", "ranges": [{"startOffset": 6485, "endOffset": 6588, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDefaultAutoSelectFamilyAttemptTimeout", "ranges": [{"startOffset": 6590, "endOffset": 6777, "count": 0}], "isBlockCoverage": false}, {"functionName": "normalizeArgs", "ranges": [{"startOffset": 7271, "endOffset": 7997, "count": 0}], "isBlockCoverage": false}, {"functionName": "initSocketHandle", "ranges": [{"startOffset": 8069, "endOffset": 8672, "count": 3}, {"startOffset": 8425, "endOffset": 8666, "count": 0}], "isBlockCoverage": true}, {"functionName": "closeSocketHandle", "ranges": [{"startOffset": 8674, "endOffset": 9014, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket", "ranges": [{"startOffset": 9266, "endOffset": 13893, "count": 3}, {"startOffset": 9326, "endOffset": 9353, "count": 0}, {"startOffset": 9381, "endOffset": 9510, "count": 0}, {"startOffset": 9580, "endOffset": 9828, "count": 0}, {"startOffset": 9890, "endOffset": 10086, "count": 0}, {"startOffset": 10648, "endOffset": 10674, "count": 0}, {"startOffset": 11188, "endOffset": 12436, "count": 0}, {"startOffset": 12523, "endOffset": 12600, "count": 0}, {"startOffset": 12601, "endOffset": 12647, "count": 0}, {"startOffset": 12649, "endOffset": 12864, "count": 0}, {"startOffset": 13432, "endOffset": 13601, "count": 0}, {"startOffset": 13633, "endOffset": 13660, "count": 0}, {"startOffset": 13688, "endOffset": 13740, "count": 0}], "isBlockCoverage": true}, {"functionName": "_unrefTimer", "ranges": [{"startOffset": 14066, "endOffset": 14196, "count": 16}, {"startOffset": 14168, "endOffset": 14190, "count": 0}], "isBlockCoverage": true}, {"functionName": "Socket._final", "ranges": [{"startOffset": 14316, "endOffset": 14936, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterShutdown", "ranges": [{"startOffset": 14939, "endOffset": 15085, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeAfterFIN", "ranges": [{"startOffset": 15292, "endOffset": 15800, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._onTimeout", "ranges": [{"startOffset": 15882, "endOffset": 16386, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setNoDelay", "ranges": [{"startOffset": 16420, "endOffset": 16797, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setKeepAlive", "ranges": [{"startOffset": 16833, "endOffset": 17439, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.address", "ranges": [{"startOffset": 17470, "endOffset": 17514, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 17600, "endOffset": 17644, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 17724, "endOffset": 17780, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 17891, "endOffset": 18206, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 18295, "endOffset": 18375, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 18463, "endOffset": 18508, "count": 0}], "isBlockCoverage": false}, {"functionName": "tryReadStart", "ranges": [{"startOffset": 18516, "endOffset": 18766, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._read", "ranges": [{"startOffset": 18858, "endOffset": 19177, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.end", "ranges": [{"startOffset": 19204, "endOffset": 19360, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.resetAndDestroy", "ranges": [{"startOffset": 19398, "endOffset": 19748, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.pause", "ranges": [{"startOffset": 19776, "endOffset": 20114, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.resume", "ranges": [{"startOffset": 20144, "endOffset": 20328, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.read", "ranges": [{"startOffset": 20356, "endOffset": 20542, "count": 0}], "isBlockCoverage": false}, {"functionName": "onReadableStreamEnd", "ranges": [{"startOffset": 20589, "endOffset": 20688, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.destroySoon", "ranges": [{"startOffset": 20722, "endOffset": 20869, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._destroy", "ranges": [{"startOffset": 20901, "endOffset": 22415, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._reset", "ranges": [{"startOffset": 22444, "endOffset": 22543, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._getpeername", "ranges": [{"startOffset": 22578, "endOffset": 22875, "count": 0}], "isBlockCoverage": false}, {"functionName": "protoGetter", "ranges": [{"startOffset": 22878, "endOffset": 23060, "count": 9}], "isBlockCoverage": true}, {"functionName": "bytesRead", "ranges": [{"startOffset": 23087, "endOffset": 23178, "count": 0}], "isBlockCoverage": false}, {"functionName": "remoteAddress", "ranges": [{"startOffset": 23211, "endOffset": 23277, "count": 0}], "isBlockCoverage": false}, {"functionName": "remoteFamily", "ranges": [{"startOffset": 23309, "endOffset": 23373, "count": 0}], "isBlockCoverage": false}, {"functionName": "remotePort", "ranges": [{"startOffset": 23403, "endOffset": 23463, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._getsockname", "ranges": [{"startOffset": 23500, "endOffset": 23776, "count": 0}], "isBlockCoverage": false}, {"functionName": "localAddress", "ranges": [{"startOffset": 23808, "endOffset": 23873, "count": 0}], "isBlockCoverage": false}, {"functionName": "localPort", "ranges": [{"startOffset": 23903, "endOffset": 23962, "count": 0}], "isBlockCoverage": false}, {"functionName": "localFamily", "ranges": [{"startOffset": 23993, "endOffset": 24056, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.<computed>", "ranges": [{"startOffset": 24097, "endOffset": 24144, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._writeGeneric", "ranges": [{"startOffset": 24180, "endOffset": 25092, "count": 16}, {"startOffset": 24402, "endOffset": 24746, "count": 0}, {"startOffset": 24827, "endOffset": 24883, "count": 0}, {"startOffset": 24937, "endOffset": 24973, "count": 0}, {"startOffset": 25052, "endOffset": 25090, "count": 0}], "isBlockCoverage": true}, {"functionName": "connect", "ranges": [{"startOffset": 24497, "endOffset": 24611, "count": 0}], "isBlockCoverage": false}, {"functionName": "onClose", "ranges": [{"startOffset": 24618, "endOffset": 24697, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._writev", "ranges": [{"startOffset": 25123, "endOffset": 25191, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._write", "ranges": [{"startOffset": 25221, "endOffset": 25302, "count": 16}], "isBlockCoverage": true}, {"functionName": "_bytesDispatched", "ranges": [{"startOffset": 25495, "endOffset": 25599, "count": 0}], "isBlockCoverage": false}, {"functionName": "bytes<PERSON>ritten", "ranges": [{"startOffset": 25631, "endOffset": 26560, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkBindError", "ranges": [{"startOffset": 26565, "endOffset": 27364, "count": 0}], "isBlockCoverage": false}, {"functionName": "internalConnect", "ranges": [{"startOffset": 27367, "endOffset": 29467, "count": 0}], "isBlockCoverage": false}, {"functionName": "internalConnectMultiple", "ranges": [{"startOffset": 29470, "endOffset": 32410, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.connect", "ranges": [{"startOffset": 32439, "endOffset": 34035, "count": 0}], "isBlockCoverage": false}, {"functionName": "reinitializeHandle", "ranges": [{"startOffset": 34078, "endOffset": 34232, "count": 0}], "isBlockCoverage": false}, {"functionName": "socketToDnsFamily", "ranges": [{"startOffset": 34235, "endOffset": 34381, "count": 0}], "isBlockCoverage": false}, {"functionName": "lookupAndConnect", "ranges": [{"startOffset": 34383, "endOffset": 38314, "count": 0}], "isBlockCoverage": false}, {"functionName": "lookupAndConnectMultiple", "ranges": [{"startOffset": 38316, "endOffset": 42415, "count": 0}], "isBlockCoverage": false}, {"functionName": "connectErrorNT", "ranges": [{"startOffset": 42417, "endOffset": 42476, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.ref", "ranges": [{"startOffset": 42502, "endOffset": 42689, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.unref", "ranges": [{"startOffset": 42718, "endOffset": 42911, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterConnect", "ranges": [{"startOffset": 42915, "endOffset": 44603, "count": 0}], "isBlockCoverage": false}, {"functionName": "addClientAbortSignalOption", "ranges": [{"startOffset": 44605, "endOffset": 45015, "count": 0}], "isBlockCoverage": false}, {"functionName": "createConnectionError", "ranges": [{"startOffset": 45017, "endOffset": 45538, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterConnectMultiple", "ranges": [{"startOffset": 45540, "endOffset": 46858, "count": 0}], "isBlockCoverage": false}, {"functionName": "internalConnectMultipleTimeout", "ranges": [{"startOffset": 46860, "endOffset": 47356, "count": 0}], "isBlockCoverage": false}, {"functionName": "addServerAbortSignalOption", "ranges": [{"startOffset": 47358, "endOffset": 47788, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server", "ranges": [{"startOffset": 47790, "endOffset": 49402, "count": 0}], "isBlockCoverage": false}, {"functionName": "toNumber", "ranges": [{"startOffset": 49513, "endOffset": 49578, "count": 0}], "isBlockCoverage": false}, {"functionName": "createServerHandle", "ranges": [{"startOffset": 49646, "endOffset": 51098, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupListenHandle", "ranges": [{"startOffset": 51100, "endOffset": 53794, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitErrorNT", "ranges": [{"startOffset": 53861, "endOffset": 53923, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitListeningNT", "ranges": [{"startOffset": 53926, "endOffset": 54041, "count": 0}], "isBlockCoverage": false}, {"functionName": "listenInCluster", "ranges": [{"startOffset": 54044, "endOffset": 55626, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.listen", "ranges": [{"startOffset": 55655, "endOffset": 60017, "count": 0}], "isBlockCoverage": false}, {"functionName": "lookupAndListen", "ranges": [{"startOffset": 60020, "endOffset": 60563, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 60645, "endOffset": 60688, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.address", "ranges": [{"startOffset": 60764, "endOffset": 61057, "count": 0}], "isBlockCoverage": false}, {"functionName": "onconnection", "ranges": [{"startOffset": 61060, "endOffset": 62963, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.getConnections", "ranges": [{"startOffset": 63112, "endOffset": 63865, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.close", "ranges": [{"startOffset": 63894, "endOffset": 64784, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.<computed>", "ranges": [{"startOffset": 64826, "endOffset": 64946, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server._emitCloseIfDrained", "ranges": [{"startOffset": 64988, "endOffset": 65374, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitCloseNT", "ranges": [{"startOffset": 65378, "endOffset": 65461, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.<computed>", "ranges": [{"startOffset": 65520, "endOffset": 65682, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 65904, "endOffset": 65940, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 65944, "endOffset": 65985, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 66065, "endOffset": 66096, "count": 48}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 66100, "endOffset": 66136, "count": 3}], "isBlockCoverage": true}, {"functionName": "Server._setupWorker", "ranges": [{"startOffset": 66175, "endOffset": 66416, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.ref", "ranges": [{"startOffset": 66442, "endOffset": 66540, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.unref", "ranges": [{"startOffset": 66568, "endOffset": 66667, "count": 0}], "isBlockCoverage": false}, {"functionName": "_setSimultaneousAccepts", "ranges": [{"startOffset": 66809, "endOffset": 67478, "count": 0}], "isBlockCoverage": false}, {"functionName": "_setSimultaneousAccepts", "ranges": [{"startOffset": 67517, "endOffset": 67758, "count": 0}], "isBlockCoverage": false}, {"functionName": "get BlockList", "ranges": [{"startOffset": 67887, "endOffset": 67989, "count": 0}], "isBlockCoverage": false}, {"functionName": "get <PERSON>cket<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 67993, "endOffset": 68115, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "192", "url": "node:internal/stream_base_commons", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7170, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1057, "endOffset": 1082, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleWriteReq", "ranges": [{"startOffset": 1201, "endOffset": 2056, "count": 16}, {"startOffset": 1301, "endOffset": 1468, "count": 0}, {"startOffset": 1473, "endOffset": 1487, "count": 0}, {"startOffset": 1492, "endOffset": 1556, "count": 0}, {"startOffset": 1644, "endOffset": 1706, "count": 0}, {"startOffset": 1711, "endOffset": 1723, "count": 0}, {"startOffset": 1728, "endOffset": 1741, "count": 0}, {"startOffset": 1746, "endOffset": 1761, "count": 0}, {"startOffset": 1766, "endOffset": 1830, "count": 0}, {"startOffset": 1835, "endOffset": 2050, "count": 0}], "isBlockCoverage": true}, {"functionName": "onWriteComplete", "ranges": [{"startOffset": 2058, "endOffset": 2747, "count": 0}], "isBlockCoverage": false}, {"functionName": "createWriteWrap", "ranges": [{"startOffset": 2749, "endOffset": 2987, "count": 16}], "isBlockCoverage": true}, {"functionName": "writevGeneric", "ranges": [{"startOffset": 2989, "endOffset": 3628, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeGeneric", "ranges": [{"startOffset": 3630, "endOffset": 3835, "count": 16}], "isBlockCoverage": true}, {"functionName": "afterWriteDispatched", "ranges": [{"startOffset": 3837, "endOffset": 4144, "count": 16}, {"startOffset": 4004, "endOffset": 4059, "count": 0}], "isBlockCoverage": true}, {"functionName": "onStreamRead", "ranges": [{"startOffset": 4146, "endOffset": 6085, "count": 0}], "isBlockCoverage": false}, {"functionName": "setStreamTimeout", "ranges": [{"startOffset": 6087, "endOffset": 6963, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "193", "url": "node:diagnostics_channel", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10086, "count": 1}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 808, "endOffset": 1217, "count": 1}], "isBlockCoverage": false}, {"functionName": "#finalizers", "ranges": [{"startOffset": 888, "endOffset": 924, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 930, "endOffset": 1047, "count": 9}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1051, "endOffset": 1099, "count": 9}, {"startOffset": 1087, "endOffset": 1092, "count": 0}], "isBlockCoverage": true}, {"functionName": "incRef", "ranges": [{"startOffset": 1103, "endOffset": 1157, "count": 0}], "isBlockCoverage": false}, {"functionName": "decRef", "ranges": [{"startOffset": 1161, "endOffset": 1215, "count": 0}], "isBlockCoverage": false}, {"functionName": "markActive", "ranges": [{"startOffset": 1219, "endOffset": 1424, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeMarkInactive", "ranges": [{"startOffset": 1426, "endOffset": 1794, "count": 0}], "isBlockCoverage": false}, {"functionName": "defaultTransform", "ranges": [{"startOffset": 1796, "endOffset": 1846, "count": 0}], "isBlockCoverage": false}, {"functionName": "wrapStoreRun", "ranges": [{"startOffset": 1848, "endOffset": 2176, "count": 0}], "isBlockCoverage": false}, {"functionName": "subscribe", "ranges": [{"startOffset": 2258, "endOffset": 2492, "count": 0}], "isBlockCoverage": false}, {"functionName": "unsubscribe", "ranges": [{"startOffset": 2496, "endOffset": 2943, "count": 0}], "isBlockCoverage": false}, {"functionName": "bindStore", "ranges": [{"startOffset": 2947, "endOffset": 3115, "count": 0}], "isBlockCoverage": false}, {"functionName": "unbindStore", "ranges": [{"startOffset": 3119, "endOffset": 3318, "count": 0}], "isBlockCoverage": false}, {"functionName": "get hasSubscribers", "ranges": [{"startOffset": 3322, "endOffset": 3365, "count": 0}], "isBlockCoverage": false}, {"functionName": "publish", "ranges": [{"startOffset": 3369, "endOffset": 3709, "count": 0}], "isBlockCoverage": false}, {"functionName": "runStores", "ranges": [{"startOffset": 3713, "endOffset": 4052, "count": 0}], "isBlockCoverage": false}, {"functionName": "Channel", "ranges": [{"startOffset": 4074, "endOffset": 4215, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4226, "endOffset": 4411, "count": 0}], "isBlockCoverage": false}, {"functionName": "subscribe", "ranges": [{"startOffset": 4415, "endOffset": 4500, "count": 0}], "isBlockCoverage": false}, {"functionName": "unsubscribe", "ranges": [{"startOffset": 4504, "endOffset": 4541, "count": 0}], "isBlockCoverage": false}, {"functionName": "bindStore", "ranges": [{"startOffset": 4545, "endOffset": 4638, "count": 0}], "isBlockCoverage": false}, {"functionName": "unbindStore", "ranges": [{"startOffset": 4642, "endOffset": 4679, "count": 0}], "isBlockCoverage": false}, {"functionName": "get hasSubscribers", "ranges": [{"startOffset": 4683, "endOffset": 4727, "count": 1}], "isBlockCoverage": true}, {"functionName": "publish", "ranges": [{"startOffset": 4731, "endOffset": 4743, "count": 0}], "isBlockCoverage": false}, {"functionName": "runStores", "ranges": [{"startOffset": 4747, "endOffset": 4834, "count": 0}], "isBlockCoverage": false}, {"functionName": "channel", "ranges": [{"startOffset": 4874, "endOffset": 5140, "count": 9}, {"startOffset": 4952, "endOffset": 4967, "count": 0}, {"startOffset": 5000, "endOffset": 5027, "count": 0}, {"startOffset": 5029, "endOffset": 5109, "count": 0}], "isBlockCoverage": true}, {"functionName": "subscribe", "ranges": [{"startOffset": 5142, "endOffset": 5232, "count": 0}], "isBlockCoverage": false}, {"functionName": "unsubscribe", "ranges": [{"startOffset": 5234, "endOffset": 5328, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasSubscribers", "ranges": [{"startOffset": 5330, "endOffset": 5465, "count": 0}], "isBlockCoverage": false}, {"functionName": "assertChannel", "ranges": [{"startOffset": 5554, "endOffset": 5696, "count": 0}], "isBlockCoverage": false}, {"functionName": "tracingChannelFrom", "ranges": [{"startOffset": 5698, "endOffset": 6223, "count": 5}, {"startOffset": 5854, "endOffset": 5923, "count": 0}, {"startOffset": 5925, "endOffset": 6222, "count": 0}], "isBlockCoverage": true}, {"functionName": "TracingChannel", "ranges": [{"startOffset": 6250, "endOffset": 6475, "count": 1}, {"startOffset": 6321, "endOffset": 6471, "count": 5}], "isBlockCoverage": true}, {"functionName": "get hasSubscribers", "ranges": [{"startOffset": 6479, "endOffset": 6689, "count": 0}], "isBlockCoverage": false}, {"functionName": "subscribe", "ranges": [{"startOffset": 6693, "endOffset": 6845, "count": 0}], "isBlockCoverage": false}, {"functionName": "unsubscribe", "ranges": [{"startOffset": 6849, "endOffset": 7082, "count": 0}], "isBlockCoverage": false}, {"functionName": "traceSync", "ranges": [{"startOffset": 7086, "endOffset": 7597, "count": 0}], "isBlockCoverage": false}, {"functionName": "tracePromise", "ranges": [{"startOffset": 7601, "endOffset": 8787, "count": 0}], "isBlockCoverage": false}, {"functionName": "trace<PERSON><PERSON>back", "ranges": [{"startOffset": 8791, "endOffset": 9884, "count": 0}], "isBlockCoverage": false}, {"functionName": "tracingChannel", "ranges": [{"startOffset": 9888, "endOffset": 9976, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "194", "url": "node:internal/perf/observe", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16722, "count": 1}], "isBlockCoverage": false}, {"functionName": "queuePending", "ranges": [{"startOffset": 2600, "endOffset": 2861, "count": 0}], "isBlockCoverage": false}, {"functionName": "getObserverType", "ranges": [{"startOffset": 2863, "endOffset": 3203, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeDecrementObserverCounts", "ranges": [{"startOffset": 3205, "endOffset": 3626, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeIncrementObserverCount", "ranges": [{"startOffset": 3628, "endOffset": 3970, "count": 0}], "isBlockCoverage": false}, {"functionName": "performanceObserverSorter", "ranges": [{"startOffset": 4047, "endOffset": 4114, "count": 0}], "isBlockCoverage": false}, {"functionName": "PerformanceObserverEntryList", "ranges": [{"startOffset": 4156, "endOffset": 4384, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEntries", "ranges": [{"startOffset": 4388, "endOffset": 4527, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEntriesByType", "ranges": [{"startOffset": 4531, "endOffset": 4835, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEntriesByName", "ranges": [{"startOffset": 4839, "endOffset": 5332, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5336, "endOffset": 5584, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5936, "endOffset": 9533, "count": 0}], "isBlockCoverage": false}, {"functionName": "enqueue", "ranges": [{"startOffset": 10015, "endOffset": 10217, "count": 0}], "isBlockCoverage": false}, {"functionName": "bufferUserTiming", "ranges": [{"startOffset": 10278, "endOffset": 11304, "count": 0}], "isBlockCoverage": false}, {"functionName": "bufferResourceTiming", "ranges": [{"startOffset": 11592, "endOffset": 12856, "count": 0}], "isBlockCoverage": false}, {"functionName": "setResourceTimingBufferSize", "ranges": [{"startOffset": 12944, "endOffset": 13216, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDispatchBufferFull", "ranges": [{"startOffset": 13218, "endOffset": 13283, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearEntriesFromBuffer", "ranges": [{"startOffset": 13285, "endOffset": 13885, "count": 0}], "isBlockCoverage": false}, {"functionName": "filterBufferMapByNameAndType", "ranges": [{"startOffset": 13887, "endOffset": 14729, "count": 0}], "isBlockCoverage": false}, {"functionName": "observerCallback", "ranges": [{"startOffset": 14731, "endOffset": 15847, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasObserver", "ranges": [{"startOffset": 15884, "endOffset": 16003, "count": 0}], "isBlockCoverage": false}, {"functionName": "startPerf", "ranges": [{"startOffset": 16006, "endOffset": 16116, "count": 0}], "isBlockCoverage": false}, {"functionName": "stopPerf", "ranges": [{"startOffset": 16118, "endOffset": 16433, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "195", "url": "node:internal/perf/performance_entry", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3272, "count": 1}], "isBlockCoverage": false}, {"functionName": "isPerformanceEntry", "ranges": [{"startOffset": 675, "endOffset": 748, "count": 0}], "isBlockCoverage": false}, {"functionName": "PerformanceEntry", "ranges": [{"startOffset": 777, "endOffset": 1132, "count": 0}], "isBlockCoverage": false}, {"functionName": "get name", "ranges": [{"startOffset": 1136, "endOffset": 1236, "count": 0}], "isBlockCoverage": false}, {"functionName": "get entryType", "ranges": [{"startOffset": 1240, "endOffset": 1355, "count": 0}], "isBlockCoverage": false}, {"functionName": "get startTime", "ranges": [{"startOffset": 1359, "endOffset": 1474, "count": 0}], "isBlockCoverage": false}, {"functionName": "get duration", "ranges": [{"startOffset": 1478, "endOffset": 1590, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1594, "endOffset": 1838, "count": 0}], "isBlockCoverage": false}, {"functionName": "toJSON", "ranges": [{"startOffset": 1842, "endOffset": 2064, "count": 0}], "isBlockCoverage": false}, {"functionName": "createPerformanceEntry", "ranges": [{"startOffset": 2286, "endOffset": 2422, "count": 0}], "isBlockCoverage": false}, {"functionName": "get detail", "ranges": [{"startOffset": 2539, "endOffset": 2649, "count": 0}], "isBlockCoverage": false}, {"functionName": "toJSON", "ranges": [{"startOffset": 2653, "endOffset": 2904, "count": 0}], "isBlockCoverage": false}, {"functionName": "createPerformanceNodeEntry", "ranges": [{"startOffset": 2908, "endOffset": 3112, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "196", "url": "node:internal/tty", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6478, "count": 1}], "isBlockCoverage": false}, {"functionName": "warnOnDeactivatedColors", "ranges": [{"startOffset": 2542, "endOffset": 2995, "count": 0}], "isBlockCoverage": false}, {"functionName": "getColorDepth", "ranges": [{"startOffset": 3153, "endOffset": 6149, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasColors", "ranges": [{"startOffset": 6151, "endOffset": 6424, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "197", "url": "node:internal/modules/esm/module_map", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4028, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 281, "endOffset": 306, "count": 1}], "isBlockCoverage": true}, {"functionName": "ResolveCache", "ranges": [{"startOffset": 850, "endOffset": 878, "count": 1}], "isBlockCoverage": true}, {"functionName": "serialize<PERSON>ey", "ranges": [{"startOffset": 1224, "endOffset": 1836, "count": 12}, {"startOffset": 1633, "endOffset": 1835, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1752, "endOffset": 1818, "count": 0}], "isBlockCoverage": false}, {"functionName": "#getModuleCachedImports", "ranges": [{"startOffset": 1840, "endOffset": 2056, "count": 24}, {"startOffset": 1954, "endOffset": 2026, "count": 4}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2231, "endOffset": 2333, "count": 12}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 2473, "endOffset": 2602, "count": 12}], "isBlockCoverage": true}, {"functionName": "has", "ranges": [{"startOffset": 2606, "endOffset": 2710, "count": 0}], "isBlockCoverage": false}, {"functionName": "Load<PERSON>ache", "ranges": [{"startOffset": 2844, "endOffset": 2872, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2921, "endOffset": 3064, "count": 12}, {"startOffset": 3051, "endOffset": 3059, "count": 4}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 3067, "endOffset": 3674, "count": 8}, {"startOffset": 3298, "endOffset": 3334, "count": 0}, {"startOffset": 3336, "endOffset": 3406, "count": 0}, {"startOffset": 3490, "endOffset": 3496, "count": 0}], "isBlockCoverage": true}, {"functionName": "has", "ranges": [{"startOffset": 3677, "endOffset": 3834, "count": 0}], "isBlockCoverage": false}, {"functionName": "delete", "ranges": [{"startOffset": 3837, "endOffset": 3973, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "198", "url": "node:internal/modules/esm/translators", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17488, "count": 1}], "isBlockCoverage": false}, {"functionName": "lazyTypes", "ranges": [{"startOffset": 464, "endOffset": 580, "count": 10}, {"startOffset": 510, "endOffset": 528, "count": 9}, {"startOffset": 528, "endOffset": 579, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1246, "endOffset": 1271, "count": 1}], "isBlockCoverage": true}, {"functionName": "getSource", "ranges": [{"startOffset": 1855, "endOffset": 1983, "count": 0}], "isBlockCoverage": false}, {"functionName": "initCJSParseSync", "ranges": [{"startOffset": 2284, "endOffset": 2421, "count": 0}], "isBlockCoverage": false}, {"functionName": "assertBufferSource", "ranges": [{"startOffset": 2994, "endOffset": 3415, "count": 10}, {"startOffset": 3071, "endOffset": 3098, "count": 5}, {"startOffset": 3100, "endOffset": 3117, "count": 0}, {"startOffset": 3211, "endOffset": 3236, "count": 0}, {"startOffset": 3255, "endOffset": 3334, "count": 0}, {"startOffset": 3335, "endOffset": 3339, "count": 0}], "isBlockCoverage": true}, {"functionName": "stringify", "ranges": [{"startOffset": 3639, "endOffset": 3906, "count": 5}, {"startOffset": 3698, "endOffset": 3714, "count": 0}, {"startOffset": 3843, "endOffset": 3862, "count": 1}, {"startOffset": 3863, "endOffset": 3872, "count": 4}], "isBlockCoverage": true}, {"functionName": "err<PERSON><PERSON>", "ranges": [{"startOffset": 4026, "endOffset": 4171, "count": 0}], "isBlockCoverage": false}, {"functionName": "moduleStrategy", "ranges": [{"startOffset": 4253, "endOffset": 4576, "count": 5}], "isBlockCoverage": true}, {"functionName": "loadCJSModule", "ranges": [{"startOffset": 5236, "endOffset": 7291, "count": 0}], "isBlockCoverage": false}, {"functionName": "createCJSModuleWrap", "ranges": [{"startOffset": 7775, "endOffset": 9217, "count": 0}], "isBlockCoverage": false}, {"functionName": "requireCommonJS", "ranges": [{"startOffset": 9252, "endOffset": 9521, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9683, "endOffset": 9797, "count": 0}], "isBlockCoverage": false}, {"functionName": "commonjsStrategy", "ranges": [{"startOffset": 9985, "endOffset": 10729, "count": 0}], "isBlockCoverage": false}, {"functionName": "cjsPreparseModuleExports", "ranges": [{"startOffset": 10918, "endOffset": 13463, "count": 0}], "isBlockCoverage": false}, {"functionName": "builtinStrategy", "ranges": [{"startOffset": 13587, "endOffset": 13993, "count": 3}, {"startOffset": 13866, "endOffset": 13918, "count": 0}], "isBlockCoverage": true}, {"functionName": "jsonStrategy", "ranges": [{"startOffset": 14105, "endOffset": 16326, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 16392, "endOffset": 17485, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "199", "url": "node:internal/modules/esm/load", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7611, "count": 1}], "isBlockCoverage": false}, {"functionName": "getSource", "ranges": [{"startOffset": 1239, "endOffset": 2466, "count": 5}, {"startOffset": 1507, "endOffset": 2332, "count": 0}, {"startOffset": 2345, "endOffset": 2355, "count": 0}, {"startOffset": 2357, "endOffset": 2413, "count": 0}], "isBlockCoverage": true}, {"functionName": "getSourceSync", "ranges": [{"startOffset": 2649, "endOffset": 3388, "count": 0}], "isBlockCoverage": false}, {"functionName": "defaultLoad", "ranges": [{"startOffset": 3509, "endOffset": 4917, "count": 8}, {"startOffset": 3693, "endOffset": 3728, "count": 0}, {"startOffset": 3729, "endOffset": 3761, "count": 0}, {"startOffset": 3763, "endOffset": 3970, "count": 0}, {"startOffset": 4122, "endOffset": 4172, "count": 3}, {"startOffset": 4172, "endOffset": 4782, "count": 5}, {"startOffset": 4204, "endOffset": 4231, "count": 0}, {"startOffset": 4413, "endOffset": 4778, "count": 0}], "isBlockCoverage": true}, {"functionName": "defaultLoadSync", "ranges": [{"startOffset": 5430, "endOffset": 6070, "count": 0}], "isBlockCoverage": false}, {"functionName": "throwIfUnsupportedURLScheme", "ranges": [{"startOffset": 6258, "endOffset": 6890, "count": 8}, {"startOffset": 6488, "endOffset": 6515, "count": 3}, {"startOffset": 6516, "endOffset": 6543, "count": 3}, {"startOffset": 6544, "endOffset": 6673, "count": 0}, {"startOffset": 6678, "endOffset": 6888, "count": 0}], "isBlockCoverage": true}, {"functionName": "throwUnknownModuleFormat", "ranges": [{"startOffset": 7269, "endOffset": 7507, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "200", "url": "node:internal/modules/esm/module_job", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15200, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 449, "endOffset": 474, "count": 1}], "isBlockCoverage": true}, {"functionName": "isCommonJSGlobalLikeNotDefinedError", "ranges": [{"startOffset": 1203, "endOffset": 1334, "count": 0}], "isBlockCoverage": false}, {"functionName": "ModuleJobBase", "ranges": [{"startOffset": 1361, "endOffset": 1548, "count": 8}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1663, "endOffset": 10702, "count": 8}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2410, "endOffset": 3365, "count": 8}, {"startOffset": 2758, "endOffset": 2913, "count": 0}], "isBlockCoverage": true}, {"functionName": "_link", "ranges": [{"startOffset": 3503, "endOffset": 5128, "count": 8}, {"startOffset": 4486, "endOffset": 5053, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4742, "endOffset": 4893, "count": 10}], "isBlockCoverage": true}, {"functionName": "instantiate", "ranges": [{"startOffset": 5132, "endOffset": 5277, "count": 2}], "isBlockCoverage": true}, {"functionName": "_instantiate", "ranges": [{"startOffset": 5281, "endOffset": 9010, "count": 2}, {"startOffset": 5814, "endOffset": 5991, "count": 0}, {"startOffset": 6048, "endOffset": 8763, "count": 0}, {"startOffset": 8810, "endOffset": 9006, "count": 11}], "isBlockCoverage": true}, {"functionName": "addJobsToDependencyGraph", "ranges": [{"startOffset": 5380, "endOffset": 5713, "count": 12}, {"startOffset": 5514, "endOffset": 5539, "count": 1}, {"startOffset": 5539, "endOffset": 5712, "count": 11}], "isBlockCoverage": true}, {"functionName": "runSync", "ranges": [{"startOffset": 9014, "endOffset": 9451, "count": 0}], "isBlockCoverage": false}, {"functionName": "run", "ranges": [{"startOffset": 9455, "endOffset": 10700, "count": 2}, {"startOffset": 9667, "endOffset": 10643, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 11277, "endOffset": 15080, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "201", "url": "file:///usr/lib/node_modules/npm/node_modules/chalk/source/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5900, "count": 1}, {"startOffset": 1472, "endOffset": 1706, "count": 45}, {"startOffset": 2476, "endOffset": 3149, "count": 3}, {"startOffset": 5468, "endOffset": 5471, "count": 0}], "isBlockCoverage": true}, {"functionName": "applyOptions", "ranges": [{"startOffset": 571, "endOffset": 950, "count": 5}, {"startOffset": 618, "endOffset": 699, "count": 3}, {"startOffset": 701, "endOffset": 779, "count": 0}, {"startOffset": 870, "endOffset": 873, "count": 0}, {"startOffset": 919, "endOffset": 931, "count": 1}, {"startOffset": 932, "endOffset": 947, "count": 4}], "isBlockCoverage": true}, {"functionName": "Chalk", "ranges": [{"startOffset": 975, "endOffset": 1084, "count": 3}], "isBlockCoverage": true}, {"functionName": "chalkFactory", "ranges": [{"startOffset": 1109, "endOffset": 1275, "count": 5}], "isBlockCoverage": true}, {"functionName": "chalk", "ranges": [{"startOffset": 1137, "endOffset": 1170, "count": 0}], "isBlockCoverage": false}, {"functionName": "createChalk", "ranges": [{"startOffset": 1278, "endOffset": 1342, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1499, "endOffset": 1699, "count": 10}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1728, "endOffset": 1875, "count": 0}], "isBlockCoverage": false}, {"functionName": "getModelAnsi", "ranges": [{"startOffset": 1902, "endOffset": 2394, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2499, "endOffset": 2771, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2866, "endOffset": 3142, "count": 0}], "isBlockCoverage": false}, {"functionName": "Object.defineProperties", "ranges": [{"startOffset": 3189, "endOffset": 3197, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3245, "endOffset": 3289, "count": 18}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 3293, "endOffset": 3343, "count": 0}], "isBlockCoverage": false}, {"functionName": "createStyler", "ranges": [{"startOffset": 3375, "endOffset": 3650, "count": 10}, {"startOffset": 3458, "endOffset": 3500, "count": 9}, {"startOffset": 3500, "endOffset": 3583, "count": 1}], "isBlockCoverage": true}, {"functionName": "createBuilder", "ranges": [{"startOffset": 3675, "endOffset": 4243, "count": 10}], "isBlockCoverage": true}, {"functionName": "builder", "ranges": [{"startOffset": 3848, "endOffset": 3959, "count": 18}, {"startOffset": 3936, "endOffset": 3958, "count": 0}], "isBlockCoverage": true}, {"functionName": "applyStyle", "ranges": [{"startOffset": 4265, "endOffset": 5300, "count": 18}, {"startOffset": 4318, "endOffset": 4361, "count": 0}, {"startOffset": 4419, "endOffset": 4440, "count": 0}, {"startOffset": 4511, "endOffset": 4840, "count": 0}, {"startOffset": 5177, "endOffset": 5260, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "202", "url": "file:///usr/lib/node_modules/npm/node_modules/supports-color/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3965, "count": 1}, {"startOffset": 738, "endOffset": 762, "count": 0}, {"startOffset": 869, "endOffset": 893, "count": 0}], "isBlockCoverage": true}, {"functionName": "hasFlag", "ranges": [{"startOffset": 235, "endOffset": 585, "count": 20}, {"startOffset": 281, "endOffset": 303, "count": 0}, {"startOffset": 359, "endOffset": 363, "count": 0}, {"startOffset": 385, "endOffset": 390, "count": 0}, {"startOffset": 519, "endOffset": 582, "count": 0}], "isBlockCoverage": true}, {"functionName": "envForceColor", "ranges": [{"startOffset": 895, "endOffset": 1259, "count": 3}, {"startOffset": 966, "endOffset": 1258, "count": 0}], "isBlockCoverage": true}, {"functionName": "translateLevel", "ranges": [{"startOffset": 1261, "endOffset": 1420, "count": 3}, {"startOffset": 1312, "endOffset": 1332, "count": 0}], "isBlockCoverage": true}, {"functionName": "_supportsColor", "ranges": [{"startOffset": 1422, "endOffset": 3604, "count": 3}, {"startOffset": 1579, "endOffset": 1620, "count": 0}, {"startOffset": 1670, "endOffset": 1688, "count": 0}, {"startOffset": 1714, "endOffset": 1730, "count": 0}, {"startOffset": 1840, "endOffset": 1858, "count": 0}, {"startOffset": 1888, "endOffset": 1906, "count": 0}, {"startOffset": 2018, "endOffset": 2040, "count": 0}, {"startOffset": 2042, "endOffset": 2058, "count": 0}, {"startOffset": 2076, "endOffset": 2091, "count": 2}, {"startOffset": 2092, "endOffset": 2119, "count": 0}, {"startOffset": 2121, "endOffset": 2137, "count": 0}, {"startOffset": 2196, "endOffset": 2214, "count": 0}, {"startOffset": 2251, "endOffset": 2608, "count": 0}, {"startOffset": 2628, "endOffset": 2886, "count": 0}, {"startOffset": 2920, "endOffset": 3001, "count": 0}, {"startOffset": 3039, "endOffset": 3055, "count": 0}, {"startOffset": 3090, "endOffset": 3106, "count": 0}, {"startOffset": 3136, "endOffset": 3388, "count": 0}, {"startOffset": 3444, "endOffset": 3603, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2689, "endOffset": 2706, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2798, "endOffset": 2817, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSupportsColor", "ranges": [{"startOffset": 3613, "endOffset": 3797, "count": 3}, {"startOffset": 3728, "endOffset": 3743, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "203", "url": "node:process", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 83, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "204", "url": "node:internal/process/report", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2811, "count": 1}], "isBlockCoverage": false}, {"functionName": "writeReport", "ranges": [{"startOffset": 352, "endOffset": 771, "count": 0}], "isBlockCoverage": false}, {"functionName": "getReport", "ranges": [{"startOffset": 775, "endOffset": 940, "count": 0}], "isBlockCoverage": false}, {"functionName": "get directory", "ranges": [{"startOffset": 944, "endOffset": 995, "count": 0}], "isBlockCoverage": false}, {"functionName": "set directory", "ranges": [{"startOffset": 999, "endOffset": 1087, "count": 0}], "isBlockCoverage": false}, {"functionName": "get filename", "ranges": [{"startOffset": 1091, "endOffset": 1140, "count": 0}], "isBlockCoverage": false}, {"functionName": "set filename", "ranges": [{"startOffset": 1144, "endOffset": 1232, "count": 0}], "isBlockCoverage": false}, {"functionName": "get compact", "ranges": [{"startOffset": 1236, "endOffset": 1283, "count": 0}], "isBlockCoverage": false}, {"functionName": "set compact", "ranges": [{"startOffset": 1287, "endOffset": 1364, "count": 0}], "isBlockCoverage": false}, {"functionName": "get excludeNetwork", "ranges": [{"startOffset": 1368, "endOffset": 1429, "count": 0}], "isBlockCoverage": false}, {"functionName": "set excludeNetwork", "ranges": [{"startOffset": 1433, "endOffset": 1531, "count": 0}], "isBlockCoverage": false}, {"functionName": "get signal", "ranges": [{"startOffset": 1535, "endOffset": 1580, "count": 0}], "isBlockCoverage": false}, {"functionName": "set signal", "ranges": [{"startOffset": 1584, "endOffset": 1721, "count": 0}], "isBlockCoverage": false}, {"functionName": "get reportOnFatalError", "ranges": [{"startOffset": 1725, "endOffset": 1797, "count": 0}], "isBlockCoverage": false}, {"functionName": "set reportOnFatalError", "ranges": [{"startOffset": 1801, "endOffset": 1919, "count": 0}], "isBlockCoverage": false}, {"functionName": "get reportOnSignal", "ranges": [{"startOffset": 1923, "endOffset": 1987, "count": 0}], "isBlockCoverage": false}, {"functionName": "set reportOnSignal", "ranges": [{"startOffset": 1991, "endOffset": 2152, "count": 0}], "isBlockCoverage": false}, {"functionName": "get reportOnUncaughtException", "ranges": [{"startOffset": 2156, "endOffset": 2242, "count": 0}], "isBlockCoverage": false}, {"functionName": "set reportOnUncaughtException", "ranges": [{"startOffset": 2246, "endOffset": 2378, "count": 0}], "isBlockCoverage": false}, {"functionName": "addSignalHandler", "ranges": [{"startOffset": 2384, "endOffset": 2555, "count": 0}], "isBlockCoverage": false}, {"functionName": "remove<PERSON><PERSON>al<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2557, "endOffset": 2681, "count": 0}], "isBlockCoverage": false}, {"functionName": "signal<PERSON>andler", "ranges": [{"startOffset": 2683, "endOffset": 2757, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "205", "url": "file:///usr/lib/node_modules/npm/node_modules/chalk/source/vendor/ansi-styles/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5256, "count": 1}], "isBlockCoverage": true}, {"functionName": "wrapAnsi16", "ranges": [{"startOffset": 55, "endOffset": 105, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 71, "endOffset": 105, "count": 0}], "isBlockCoverage": false}, {"functionName": "wrapAnsi256", "ranges": [{"startOffset": 128, "endOffset": 186, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 144, "endOffset": 186, "count": 0}], "isBlockCoverage": false}, {"functionName": "wrapAnsi16m", "ranges": [{"startOffset": 209, "endOffset": 297, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 225, "endOffset": 297, "count": 0}], "isBlockCoverage": false}, {"functionName": "assembleStyles", "ranges": [{"startOffset": 1834, "endOffset": 5189, "count": 1}, {"startOffset": 1947, "endOffset": 2281, "count": 3}, {"startOffset": 2007, "endOffset": 2187, "count": 45}], "isBlockCoverage": true}, {"functionName": "value", "ranges": [{"startOffset": 2906, "endOffset": 3404, "count": 0}], "isBlockCoverage": false}, {"functionName": "value", "ranges": [{"startOffset": 3450, "endOffset": 3975, "count": 0}], "isBlockCoverage": false}, {"functionName": "value", "ranges": [{"startOffset": 4032, "endOffset": 4083, "count": 0}], "isBlockCoverage": false}, {"functionName": "value", "ranges": [{"startOffset": 4134, "endOffset": 4895, "count": 0}], "isBlockCoverage": false}, {"functionName": "value", "ranges": [{"startOffset": 4949, "endOffset": 5030, "count": 0}], "isBlockCoverage": false}, {"functionName": "value", "ranges": [{"startOffset": 5084, "endOffset": 5137, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "206", "url": "file:///usr/lib/node_modules/npm/node_modules/chalk/source/vendor/supports-color/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3865, "count": 1}, {"startOffset": 738, "endOffset": 762, "count": 0}, {"startOffset": 869, "endOffset": 893, "count": 0}], "isBlockCoverage": true}, {"functionName": "hasFlag", "ranges": [{"startOffset": 235, "endOffset": 585, "count": 16}, {"startOffset": 281, "endOffset": 303, "count": 0}, {"startOffset": 359, "endOffset": 363, "count": 0}, {"startOffset": 385, "endOffset": 390, "count": 0}, {"startOffset": 519, "endOffset": 582, "count": 0}], "isBlockCoverage": true}, {"functionName": "envForceColor", "ranges": [{"startOffset": 895, "endOffset": 1159, "count": 2}, {"startOffset": 949, "endOffset": 1157, "count": 0}], "isBlockCoverage": true}, {"functionName": "translateLevel", "ranges": [{"startOffset": 1161, "endOffset": 1320, "count": 2}, {"startOffset": 1212, "endOffset": 1232, "count": 0}], "isBlockCoverage": true}, {"functionName": "_supportsColor", "ranges": [{"startOffset": 1322, "endOffset": 3504, "count": 2}, {"startOffset": 1479, "endOffset": 1520, "count": 0}, {"startOffset": 1570, "endOffset": 1588, "count": 0}, {"startOffset": 1614, "endOffset": 1630, "count": 0}, {"startOffset": 1740, "endOffset": 1758, "count": 0}, {"startOffset": 1788, "endOffset": 1806, "count": 0}, {"startOffset": 1918, "endOffset": 1940, "count": 0}, {"startOffset": 1942, "endOffset": 1958, "count": 0}, {"startOffset": 1992, "endOffset": 2019, "count": 0}, {"startOffset": 2021, "endOffset": 2037, "count": 0}, {"startOffset": 2096, "endOffset": 2114, "count": 0}, {"startOffset": 2151, "endOffset": 2508, "count": 0}, {"startOffset": 2528, "endOffset": 2786, "count": 0}, {"startOffset": 2820, "endOffset": 2901, "count": 0}, {"startOffset": 2939, "endOffset": 2955, "count": 0}, {"startOffset": 2990, "endOffset": 3006, "count": 0}, {"startOffset": 3036, "endOffset": 3288, "count": 0}, {"startOffset": 3344, "endOffset": 3503, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2589, "endOffset": 2606, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2698, "endOffset": 2717, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSupportsColor", "ranges": [{"startOffset": 3513, "endOffset": 3697, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "207", "url": "file:///usr/lib/node_modules/npm/node_modules/chalk/source/utilities.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 997, "count": 1}], "isBlockCoverage": true}, {"functionName": "stringReplaceAll", "ranges": [{"startOffset": 78, "endOffset": 552, "count": 0}], "isBlockCoverage": false}, {"functionName": "stringEncaseCRLFWithFirstIndex", "ranges": [{"startOffset": 561, "endOffset": 996, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "208", "url": "file:///usr/lib/node_modules/npm/lib/commands/test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 288, "count": 1}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 199, "endOffset": 262, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "209", "url": "file:///usr/lib/node_modules/npm/lib/lifecycle-cmd.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 537, "count": 1}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 269, "endOffset": 307, "count": 1}], "isBlockCoverage": true}, {"functionName": "exec", "ranges": [{"startOffset": 311, "endOffset": 400, "count": 1}], "isBlockCoverage": true}, {"functionName": "execWorkspaces", "ranges": [{"startOffset": 404, "endOffset": 503, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "210", "url": "file:///usr/lib/node_modules/npm/lib/base-cmd.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6221, "count": 1}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 333, "endOffset": 353, "count": 1}], "isBlockCoverage": true}, {"functionName": "get describeUsage", "ranges": [{"startOffset": 490, "endOffset": 2325, "count": 0}], "isBlockCoverage": false}, {"functionName": "BaseCommand", "ranges": [{"startOffset": 2329, "endOffset": 2658, "count": 2}, {"startOffset": 2526, "endOffset": 2559, "count": 0}, {"startOffset": 2561, "endOffset": 2654, "count": 0}], "isBlockCoverage": true}, {"functionName": "get name", "ranges": [{"startOffset": 2662, "endOffset": 2712, "count": 9}], "isBlockCoverage": true}, {"functionName": "get description", "ranges": [{"startOffset": 2716, "endOffset": 2780, "count": 0}], "isBlockCoverage": false}, {"functionName": "get params", "ranges": [{"startOffset": 2784, "endOffset": 2838, "count": 0}], "isBlockCoverage": false}, {"functionName": "get usage", "ranges": [{"startOffset": 2842, "endOffset": 2902, "count": 0}], "isBlockCoverage": false}, {"functionName": "usageError", "ranges": [{"startOffset": 2906, "endOffset": 3078, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkExpected", "ranges": [{"startOffset": 3140, "endOffset": 3763, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkDevEngines", "ranges": [{"startOffset": 3840, "endOffset": 5455, "count": 2}, {"startOffset": 4141, "endOffset": 4884, "count": 0}, {"startOffset": 4885, "endOffset": 4889, "count": 0}, {"startOffset": 5138, "endOffset": 5451, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4036, "endOffset": 4050, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4065, "endOffset": 4075, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4441, "endOffset": 4454, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4491, "endOffset": 4505, "count": 0}], "isBlockCoverage": false}, {"functionName": "genMsg", "ranges": [{"startOffset": 4527, "endOffset": 4846, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4900, "endOffset": 5120, "count": 0}], "isBlockCoverage": false}, {"functionName": "setWorkspaces", "ranges": [{"startOffset": 5459, "endOffset": 6188, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "211", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/package-json/lib/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6498, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 819, "endOffset": 6467, "count": 3}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 1314, "endOffset": 1648, "count": 1}], "isBlockCoverage": true}, {"functionName": "create", "ranges": [{"startOffset": 1782, "endOffset": 1948, "count": 0}], "isBlockCoverage": false}, {"functionName": "load", "ranges": [{"startOffset": 2015, "endOffset": 2386, "count": 0}], "isBlockCoverage": false}, {"functionName": "fix", "ranges": [{"startOffset": 2414, "endOffset": 2526, "count": 0}], "isBlockCoverage": false}, {"functionName": "prepare", "ranges": [{"startOffset": 2580, "endOffset": 2700, "count": 0}], "isBlockCoverage": false}, {"functionName": "normalize", "ranges": [{"startOffset": 2759, "endOffset": 2877, "count": 3}], "isBlockCoverage": true}, {"functionName": "load", "ranges": [{"startOffset": 2978, "endOffset": 3708, "count": 3}, {"startOffset": 3121, "endOffset": 3212, "count": 0}, {"startOffset": 3232, "endOffset": 3655, "count": 0}], "isBlockCoverage": true}, {"functionName": "fromJSON", "ranges": [{"startOffset": 3753, "endOffset": 3823, "count": 3}], "isBlockCoverage": true}, {"functionName": "fromContent", "ranges": [{"startOffset": 3827, "endOffset": 3919, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromComment", "ranges": [{"startOffset": 4016, "endOffset": 4437, "count": 0}], "isBlockCoverage": false}, {"functionName": "get content", "ranges": [{"startOffset": 4441, "endOffset": 4487, "count": 9}], "isBlockCoverage": true}, {"functionName": "get path", "ranges": [{"startOffset": 4491, "endOffset": 4530, "count": 6}], "isBlockCoverage": true}, {"functionName": "get filename", "ranges": [{"startOffset": 4534, "endOffset": 4651, "count": 3}, {"startOffset": 4626, "endOffset": 4650, "count": 0}], "isBlockCoverage": true}, {"functionName": "create", "ranges": [{"startOffset": 4655, "endOffset": 4736, "count": 0}], "isBlockCoverage": false}, {"functionName": "update", "ranges": [{"startOffset": 4804, "endOffset": 5264, "count": 0}], "isBlockCoverage": false}, {"functionName": "save", "ranges": [{"startOffset": 5268, "endOffset": 5982, "count": 0}], "isBlockCoverage": false}, {"functionName": "normalize", "ranges": [{"startOffset": 5986, "endOffset": 6147, "count": 3}], "isBlockCoverage": true}, {"functionName": "prepare", "ranges": [{"startOffset": 6151, "endOffset": 6308, "count": 0}], "isBlockCoverage": false}, {"functionName": "fix", "ranges": [{"startOffset": 6312, "endOffset": 6465, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "212", "url": "file:///usr/lib/node_modules/npm/node_modules/json-parse-even-better-errors/lib/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4167, "count": 1}], "isBlockCoverage": true}, {"functionName": "hexify", "ranges": [{"startOffset": 697, "endOffset": 812, "count": 0}], "isBlockCoverage": false}, {"functionName": "stripBOM", "ranges": [{"startOffset": 1006, "endOffset": 1043, "count": 3}], "isBlockCoverage": true}, {"functionName": "makeParsedError", "ranges": [{"startOffset": 1069, "endOffset": 1164, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseError", "ranges": [{"startOffset": 1185, "endOffset": 2247, "count": 0}], "isBlockCoverage": false}, {"functionName": "JSONParseError", "ranges": [{"startOffset": 2294, "endOffset": 2567, "count": 0}], "isBlockCoverage": false}, {"functionName": "get name", "ranges": [{"startOffset": 2571, "endOffset": 2621, "count": 0}], "isBlockCoverage": false}, {"functionName": "set name", "ranges": [{"startOffset": 2625, "endOffset": 2640, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2644, "endOffset": 2710, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseJson", "ranges": [{"startOffset": 2732, "endOffset": 3452, "count": 3}, {"startOffset": 3315, "endOffset": 3332, "count": 0}, {"startOffset": 3364, "endOffset": 3382, "count": 0}, {"startOffset": 3413, "endOffset": 3430, "count": 0}], "isBlockCoverage": true}, {"functionName": "parseJsonError", "ranges": [{"startOffset": 3477, "endOffset": 3944, "count": 3}, {"startOffset": 3581, "endOffset": 3942, "count": 0}], "isBlockCoverage": true}, {"functionName": "parseJsonError.noExceptions", "ranges": [{"startOffset": 4055, "endOffset": 4166, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "213", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/package-json/lib/update-dependencies.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2026, "count": 1}], "isBlockCoverage": true}, {"functionName": "orderDeps", "ranges": [{"startOffset": 197, "endOffset": 516, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateDependencies", "ranges": [{"startOffset": 545, "endOffset": 1947, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "214", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/package-json/lib/update-scripts.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 664, "count": 1}], "isBlockCoverage": true}, {"functionName": "updateScripts", "ranges": [{"startOffset": 22, "endOffset": 631, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "215", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/package-json/lib/update-workspaces.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 626, "count": 1}], "isBlockCoverage": true}, {"functionName": "updateWorkspaces", "ranges": [{"startOffset": 25, "endOffset": 590, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "216", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/package-json/lib/normalize.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19679, "count": 1}], "isBlockCoverage": true}, {"functionName": "lazyHostedGitInfo", "ranges": [{"startOffset": 315, "endOffset": 449, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyLoadGlob", "ranges": [{"startOffset": 500, "endOffset": 596, "count": 0}], "isBlockCoverage": false}, {"functionName": "normalizePackageBin", "ranges": [{"startOffset": 638, "endOffset": 2220, "count": 3}, {"startOffset": 699, "endOffset": 2201, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 976, "endOffset": 1050, "count": 0}], "isBlockCoverage": false}, {"functionName": "normalizePackageMan", "ranges": [{"startOffset": 2222, "endOffset": 2695, "count": 0}], "isBlockCoverage": false}, {"functionName": "isCorrectlyEncodedName", "ranges": [{"startOffset": 2697, "endOffset": 2814, "count": 0}], "isBlockCoverage": false}, {"functionName": "isValidScopedPackageName", "ranges": [{"startOffset": 2816, "endOffset": 3125, "count": 0}], "isBlockCoverage": false}, {"functionName": "unixifyPath", "ranges": [{"startOffset": 3127, "endOffset": 3192, "count": 0}], "isBlockCoverage": false}, {"functionName": "secureAndUnixifyPath", "ranges": [{"startOffset": 3194, "endOffset": 3362, "count": 0}], "isBlockCoverage": false}, {"functionName": "normalize", "ranges": [{"startOffset": 3576, "endOffset": 19650, "count": 3}, {"startOffset": 3664, "endOffset": 3726, "count": 0}, {"startOffset": 3785, "endOffset": 3790, "count": 0}, {"startOffset": 3820, "endOffset": 3825, "count": 0}, {"startOffset": 3842, "endOffset": 3847, "count": 0}, {"startOffset": 4026, "endOffset": 4877, "count": 0}, {"startOffset": 4912, "endOffset": 5146, "count": 0}, {"startOffset": 5224, "endOffset": 5750, "count": 0}, {"startOffset": 5861, "endOffset": 5987, "count": 39}, {"startOffset": 5894, "endOffset": 5981, "count": 0}, {"startOffset": 6105, "endOffset": 6111, "count": 0}, {"startOffset": 6379, "endOffset": 6443, "count": 0}, {"startOffset": 6455, "endOffset": 6461, "count": 0}, {"startOffset": 6715, "endOffset": 6766, "count": 0}, {"startOffset": 6768, "endOffset": 6887, "count": 0}, {"startOffset": 6910, "endOffset": 7069, "count": 0}, {"startOffset": 7082, "endOffset": 7107, "count": 0}, {"startOffset": 7109, "endOffset": 7291, "count": 0}, {"startOffset": 7331, "endOffset": 7431, "count": 0}, {"startOffset": 7825, "endOffset": 7873, "count": 0}, {"startOffset": 7875, "endOffset": 8210, "count": 0}, {"startOffset": 8305, "endOffset": 8726, "count": 0}, {"startOffset": 8810, "endOffset": 8827, "count": 0}, {"startOffset": 8829, "endOffset": 9075, "count": 0}, {"startOffset": 9212, "endOffset": 9243, "count": 0}, {"startOffset": 9418, "endOffset": 9843, "count": 99}, {"startOffset": 9472, "endOffset": 9584, "count": 0}, {"startOffset": 9623, "endOffset": 9655, "count": 0}, {"startOffset": 9657, "endOffset": 9835, "count": 0}, {"startOffset": 9849, "endOffset": 9937, "count": 0}, {"startOffset": 9999, "endOffset": 10034, "count": 0}, {"startOffset": 10036, "endOffset": 10165, "count": 0}, {"startOffset": 10237, "endOffset": 10258, "count": 0}, {"startOffset": 10260, "endOffset": 10666, "count": 0}, {"startOffset": 10732, "endOffset": 10747, "count": 0}, {"startOffset": 10749, "endOffset": 11624, "count": 0}, {"startOffset": 11684, "endOffset": 12078, "count": 0}, {"startOffset": 12108, "endOffset": 12135, "count": 0}, {"startOffset": 12136, "endOffset": 12164, "count": 0}, {"startOffset": 12273, "endOffset": 12297, "count": 0}, {"startOffset": 12298, "endOffset": 12310, "count": 0}, {"startOffset": 12312, "endOffset": 12762, "count": 0}, {"startOffset": 12830, "endOffset": 12846, "count": 0}, {"startOffset": 12848, "endOffset": 14218, "count": 0}, {"startOffset": 14287, "endOffset": 15429, "count": 0}, {"startOffset": 15913, "endOffset": 16843, "count": 0}, {"startOffset": 16921, "endOffset": 18839, "count": 0}, {"startOffset": 19022, "endOffset": 19121, "count": 0}, {"startOffset": 19300, "endOffset": 19329, "count": 0}, {"startOffset": 19331, "endOffset": 19648, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10417, "endOffset": 10460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10478, "endOffset": 10490, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11936, "endOffset": 12021, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12490, "endOffset": 12700, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "217", "url": "file:///usr/lib/node_modules/semver/functions/clean.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 205, "count": 1}], "isBlockCoverage": true}, {"functionName": "clean", "ranges": [{"startOffset": 61, "endOffset": 181, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "218", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/package-json/lib/read-package.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1048, "count": 1}], "isBlockCoverage": true}, {"functionName": "read", "ranges": [{"startOffset": 396, "endOffset": 596, "count": 3}, {"startOffset": 507, "endOffset": 594, "count": 0}], "isBlockCoverage": true}, {"functionName": "parse", "ranges": [{"startOffset": 598, "endOffset": 771, "count": 3}, {"startOffset": 689, "endOffset": 769, "count": 0}], "isBlockCoverage": true}, {"functionName": "readPackage", "ranges": [{"startOffset": 869, "endOffset": 993, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "219", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/package-json/lib/sort.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3589, "count": 1}], "isBlockCoverage": true}, {"functionName": "packageSort", "ranges": [{"startOffset": 295, "endOffset": 3551, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "220", "url": "file:///usr/lib/node_modules/npm/lib/cli/update-notifier.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4033, "count": 1}], "isBlockCoverage": true}, {"functionName": "lastCheckedFile", "ranges": [{"startOffset": 600, "endOffset": 675, "count": 1}], "isBlockCoverage": true}, {"functionName": "updateCheck", "ranges": [{"startOffset": 817, "endOffset": 2560, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateNotifier", "ranges": [{"startOffset": 2585, "endOffset": 3602, "count": 1}, {"startOffset": 2881, "endOffset": 2911, "count": 0}, {"startOffset": 3004, "endOffset": 3011, "count": 0}, {"startOffset": 3324, "endOffset": 3601, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3174, "endOffset": 3198, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3540, "endOffset": 3548, "count": 0}], "isBlockCoverage": false}, {"functionName": "module.exports", "ranges": [{"startOffset": 3694, "endOffset": 4032, "count": 1}, {"startOffset": 3822, "endOffset": 3874, "count": 0}, {"startOffset": 3875, "endOffset": 3927, "count": 0}, {"startOffset": 3962, "endOffset": 4000, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3898, "endOffset": 3926, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "221", "url": "node:internal/fs/rimraf", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7517, "count": 1}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1175, "endOffset": 1635, "count": 1}], "isBlockCoverage": true}, {"functionName": "CB", "ranges": [{"startOffset": 1263, "endOffset": 1631, "count": 1}, {"startOffset": 1295, "endOffset": 1607, "count": 0}], "isBlockCoverage": true}, {"functionName": "_rimraf", "ranges": [{"startOffset": 1638, "endOffset": 2496, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1801, "endOffset": 2492, "count": 1}, {"startOffset": 1832, "endOffset": 2043, "count": 0}, {"startOffset": 2074, "endOffset": 2132, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2151, "endOffset": 2486, "count": 1}, {"startOffset": 2177, "endOffset": 2451, "count": 0}], "isBlockCoverage": true}, {"functionName": "fixWinEPERM", "ranges": [{"startOffset": 2499, "endOffset": 2934, "count": 0}], "isBlockCoverage": false}, {"functionName": "_rmdir", "ranges": [{"startOffset": 2937, "endOffset": 3235, "count": 0}], "isBlockCoverage": false}, {"functionName": "_rmchildren", "ranges": [{"startOffset": 3238, "endOffset": 3988, "count": 0}], "isBlockCoverage": false}, {"functionName": "rimrafPromises", "ranges": [{"startOffset": 3991, "endOffset": 4189, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4053, "endOffset": 4185, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4102, "endOffset": 4179, "count": 1}, {"startOffset": 4136, "endOffset": 4155, "count": 0}], "isBlockCoverage": true}, {"functionName": "rimrafSync", "ranges": [{"startOffset": 4192, "endOffset": 4875, "count": 0}], "isBlockCoverage": false}, {"functionName": "_unlinkSync", "ranges": [{"startOffset": 4878, "endOffset": 5507, "count": 0}], "isBlockCoverage": false}, {"functionName": "_rmdirSync", "ranges": [{"startOffset": 5510, "endOffset": 7007, "count": 0}], "isBlockCoverage": false}, {"functionName": "fixWinEPERMSync", "ranges": [{"startOffset": 7010, "endOffset": 7457, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "222", "url": "file:///usr/lib/node_modules/npm/lib/commands/run.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6416, "count": 1}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 683, "endOffset": 712, "count": 1}], "isBlockCoverage": true}, {"functionName": "completion", "ranges": [{"startOffset": 723, "endOffset": 1279, "count": 0}], "isBlockCoverage": false}, {"functionName": "exec", "ranges": [{"startOffset": 1283, "endOffset": 1453, "count": 1}, {"startOffset": 1391, "endOffset": 1449, "count": 0}], "isBlockCoverage": true}, {"functionName": "execWorkspaces", "ranges": [{"startOffset": 1457, "endOffset": 2434, "count": 0}], "isBlockCoverage": false}, {"functionName": "#run", "ranges": [{"startOffset": 2438, "endOffset": 4277, "count": 1}, {"startOffset": 2675, "endOffset": 2694, "count": 0}, {"startOffset": 2696, "endOffset": 2764, "count": 0}, {"startOffset": 2790, "endOffset": 2805, "count": 0}, {"startOffset": 2807, "endOffset": 2932, "count": 0}, {"startOffset": 3030, "endOffset": 3102, "count": 0}, {"startOffset": 3109, "endOffset": 3554, "count": 0}, {"startOffset": 3741, "endOffset": 3794, "count": 0}, {"startOffset": 3831, "endOffset": 3882, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2596, "endOffset": 2610, "count": 1}], "isBlockCoverage": true}, {"functionName": "#list", "ranges": [{"startOffset": 4281, "endOffset": 6385, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "223", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/run-script.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 520, "count": 1}], "isBlockCoverage": true}, {"functionName": "runScript", "ranges": [{"startOffset": 238, "endOffset": 455, "count": 1}, {"startOffset": 341, "endOffset": 454, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "224", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/run-script-pkg.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2977, "count": 1}], "isBlockCoverage": true}, {"functionName": "runScriptPkg", "ranges": [{"startOffset": 372, "endOffset": 2945, "count": 1}, {"startOffset": 786, "endOffset": 813, "count": 0}, {"startOffset": 892, "endOffset": 1267, "count": 0}, {"startOffset": 1281, "endOffset": 1323, "count": 0}, {"startOffset": 1462, "endOffset": 1507, "count": 0}, {"startOffset": 1584, "endOffset": 1628, "count": 0}, {"startOffset": 2188, "endOffset": 2211, "count": 0}], "isBlockCoverage": true}, {"functionName": "inputEnd", "ranges": [{"startOffset": 1342, "endOffset": 1350, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2230, "endOffset": 2924, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "225", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/make-spawn-args.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1130, "count": 1}], "isBlockCoverage": true}, {"functionName": "makeSpawnArgs", "ranges": [{"startOffset": 155, "endOffset": 1097, "count": 1}, {"startOffset": 443, "endOffset": 668, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "226", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/set-path.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1501, "count": 1}], "isBlockCoverage": true}, {"functionName": "setPATH", "ranges": [{"startOffset": 444, "endOffset": 1474, "count": 1}, {"startOffset": 738, "endOffset": 773, "count": 0}, {"startOffset": 976, "endOffset": 1064, "count": 11}, {"startOffset": 1391, "endOffset": 1458, "count": 111}, {"startOffset": 1422, "endOffset": 1454, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 517, "endOffset": 549, "count": 111}, {"startOffset": 540, "endOffset": 549, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 560, "endOffset": 588, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 602, "endOffset": 673, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 634, "endOffset": 671, "count": 63}], "isBlockCoverage": true}]}, {"scriptId": "227", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/promise-spawn/lib/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6177, "count": 1}], "isBlockCoverage": true}, {"functionName": "promiseSpawn", "ranges": [{"startOffset": 231, "endOffset": 1469, "count": 2}, {"startOffset": 289, "endOffset": 1050, "count": 1}, {"startOffset": 1050, "endOffset": 1147, "count": 0}, {"startOffset": 1147, "endOffset": 1168, "count": 1}, {"startOffset": 1168, "endOffset": 1265, "count": 0}, {"startOffset": 1265, "endOffset": 1468, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 398, "endOffset": 470, "count": 1}], "isBlockCoverage": true}, {"functionName": "getResult", "ranges": [{"startOffset": 659, "endOffset": 768, "count": 1}], "isBlockCoverage": true}, {"functionName": "rejectWithOpts", "ranges": [{"startOffset": 794, "endOffset": 900, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1079, "endOffset": 1098, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1197, "endOffset": 1216, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1286, "endOffset": 1448, "count": 1}, {"startOffset": 1330, "endOffset": 1388, "count": 0}], "isBlockCoverage": true}, {"functionName": "spawnWithShell", "ranges": [{"startOffset": 1494, "endOffset": 3437, "count": 1}, {"startOffset": 1899, "endOffset": 1920, "count": 0}, {"startOffset": 2250, "endOffset": 3261, "count": 0}, {"startOffset": 3297, "endOffset": 3341, "count": 0}], "isBlockCoverage": true}, {"functionName": "open", "ranges": [{"startOffset": 3524, "endOffset": 5195, "count": 0}], "isBlockCoverage": false}, {"functionName": "isPipe", "ranges": [{"startOffset": 5237, "endOffset": 5412, "count": 2}, {"startOffset": 5305, "endOffset": 5326, "count": 0}, {"startOffset": 5356, "endOffset": 5394, "count": 0}], "isBlockCoverage": true}, {"functionName": "stdioResult", "ranges": [{"startOffset": 5434, "endOffset": 5914, "count": 1}, {"startOffset": 5609, "endOffset": 5739, "count": 0}, {"startOffset": 5765, "endOffset": 5895, "count": 0}], "isBlockCoverage": true}, {"functionName": "findInObject", "ranges": [{"startOffset": 5977, "endOffset": 6145, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "228", "url": "node:child_process", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27267, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1936, "endOffset": 1965, "count": 1}], "isBlockCoverage": true}, {"functionName": "fork", "ranges": [{"startOffset": 3430, "endOffset": 5105, "count": 0}], "isBlockCoverage": false}, {"functionName": "_fork<PERSON><PERSON>d", "ranges": [{"startOffset": 5107, "endOffset": 5596, "count": 0}], "isBlockCoverage": false}, {"functionName": "normalizeExecArgs", "ranges": [{"startOffset": 5598, "endOffset": 6112, "count": 0}], "isBlockCoverage": false}, {"functionName": "exec", "ranges": [{"startOffset": 6649, "endOffset": 6896, "count": 0}], "isBlockCoverage": false}, {"functionName": "customPromiseExecFunction", "ranges": [{"startOffset": 6932, "endOffset": 7296, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6953, "endOffset": 7293, "count": 0}], "isBlockCoverage": false}, {"functionName": "normalizeExecFileArgs", "ranges": [{"startOffset": 7433, "endOffset": 8331, "count": 0}], "isBlockCoverage": false}, {"functionName": "execFile", "ranges": [{"startOffset": 8944, "endOffset": 13708, "count": 0}], "isBlockCoverage": false}, {"functionName": "copyProcessEnvToEnv", "ranges": [{"startOffset": 13852, "endOffset": 14052, "count": 1}, {"startOffset": 14010, "endOffset": 14050, "count": 0}], "isBlockCoverage": true}, {"functionName": "normalizeSpawnArguments", "ranges": [{"startOffset": 14054, "endOffset": 19366, "count": 1}, {"startOffset": 14215, "endOffset": 14280, "count": 0}, {"startOffset": 14351, "endOffset": 14542, "count": 0}, {"startOffset": 14622, "endOffset": 14645, "count": 0}, {"startOffset": 14942, "endOffset": 15006, "count": 0}, {"startOffset": 15069, "endOffset": 15093, "count": 0}, {"startOffset": 15095, "endOffset": 15173, "count": 0}, {"startOffset": 15236, "endOffset": 15260, "count": 0}, {"startOffset": 15262, "endOffset": 15340, "count": 0}, {"startOffset": 15451, "endOffset": 15493, "count": 0}, {"startOffset": 15495, "endOffset": 15626, "count": 0}, {"startOffset": 15690, "endOffset": 15810, "count": 0}, {"startOffset": 15886, "endOffset": 15956, "count": 0}, {"startOffset": 16096, "endOffset": 16204, "count": 0}, {"startOffset": 16227, "endOffset": 17089, "count": 0}, {"startOffset": 17132, "endOffset": 17185, "count": 0}, {"startOffset": 17263, "endOffset": 17277, "count": 0}, {"startOffset": 17530, "endOffset": 18134, "count": 0}, {"startOffset": 18231, "endOffset": 18274, "count": 111}, {"startOffset": 18312, "endOffset": 18777, "count": 0}, {"startOffset": 18808, "endOffset": 19061, "count": 111}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18556, "endOffset": 18765, "count": 0}], "isBlockCoverage": false}, {"functionName": "abortChildProcess", "ranges": [{"startOffset": 19368, "endOffset": 19624, "count": 0}], "isBlockCoverage": false}, {"functionName": "spawn", "ranges": [{"startOffset": 20179, "endOffset": 21403, "count": 1}, {"startOffset": 20545, "endOffset": 20919, "count": 0}, {"startOffset": 20943, "endOffset": 21384, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20578, "endOffset": 20766, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20810, "endOffset": 20913, "count": 0}], "isBlockCoverage": false}, {"functionName": "onAbortListener", "ranges": [{"startOffset": 21279, "endOffset": 21380, "count": 0}], "isBlockCoverage": false}, {"functionName": "spawnSync", "ranges": [{"startOffset": 22163, "endOffset": 23671, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkExecSyncError", "ranges": [{"startOffset": 23674, "endOffset": 24060, "count": 0}], "isBlockCoverage": false}, {"functionName": "execFileSync", "ranges": [{"startOffset": 24573, "endOffset": 25037, "count": 0}], "isBlockCoverage": false}, {"functionName": "execSync", "ranges": [{"startOffset": 25535, "endOffset": 25911, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateArgument<PERSON>ull<PERSON>heck", "ranges": [{"startOffset": 25914, "endOffset": 26137, "count": 225}, {"startOffset": 26039, "endOffset": 26135, "count": 0}], "isBlockCoverage": true}, {"functionName": "validateArgumentsNullCheck", "ranges": [{"startOffset": 26140, "endOffset": 26302, "count": 1}, {"startOffset": 26234, "endOffset": 26300, "count": 2}], "isBlockCoverage": true}, {"functionName": "validateTimeout", "ranges": [{"startOffset": 26305, "endOffset": 26493, "count": 1}, {"startOffset": 26363, "endOffset": 26409, "count": 0}, {"startOffset": 26411, "endOffset": 26491, "count": 0}], "isBlockCoverage": true}, {"functionName": "validateMax<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 26496, "endOffset": 26769, "count": 0}], "isBlockCoverage": false}, {"functionName": "sanitizeKillSignal", "ranges": [{"startOffset": 26772, "endOffset": 27135, "count": 1}, {"startOffset": 26886, "endOffset": 26936, "count": 0}, {"startOffset": 26966, "endOffset": 27133, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "229", "url": "node:internal/child_process", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 31799, "count": 1}], "isBlockCoverage": false}, {"functionName": "send", "ranges": [{"startOffset": 2498, "endOffset": 2557, "count": 0}], "isBlockCoverage": false}, {"functionName": "got", "ranges": [{"startOffset": 2564, "endOffset": 2618, "count": 0}], "isBlockCoverage": false}, {"functionName": "send", "ranges": [{"startOffset": 2680, "endOffset": 2747, "count": 0}], "isBlockCoverage": false}, {"functionName": "got", "ranges": [{"startOffset": 2754, "endOffset": 2895, "count": 0}], "isBlockCoverage": false}, {"functionName": "send", "ranges": [{"startOffset": 2925, "endOffset": 4479, "count": 0}], "isBlockCoverage": false}, {"functionName": "postSend", "ranges": [{"startOffset": 4486, "endOffset": 5232, "count": 0}], "isBlockCoverage": false}, {"functionName": "got", "ranges": [{"startOffset": 5239, "endOffset": 5696, "count": 0}], "isBlockCoverage": false}, {"functionName": "send", "ranges": [{"startOffset": 5761, "endOffset": 5820, "count": 0}], "isBlockCoverage": false}, {"functionName": "got", "ranges": [{"startOffset": 5827, "endOffset": 5881, "count": 0}], "isBlockCoverage": false}, {"functionName": "send", "ranges": [{"startOffset": 5946, "endOffset": 6066, "count": 0}], "isBlockCoverage": false}, {"functionName": "got", "ranges": [{"startOffset": 6073, "endOffset": 6232, "count": 0}], "isBlockCoverage": false}, {"functionName": "stdioStringToArray", "ranges": [{"startOffset": 6243, "endOffset": 6658, "count": 1}, {"startOffset": 6335, "endOffset": 6349, "count": 0}, {"startOffset": 6354, "endOffset": 6372, "count": 0}, {"startOffset": 6377, "endOffset": 6446, "count": 0}, {"startOffset": 6516, "endOffset": 6579, "count": 0}, {"startOffset": 6600, "endOffset": 6637, "count": 0}], "isBlockCoverage": true}, {"functionName": "ChildProcess", "ranges": [{"startOffset": 6660, "endOffset": 8153, "count": 1}, {"startOffset": 8083, "endOffset": 8151, "count": 0}], "isBlockCoverage": true}, {"functionName": "ChildProcess._handle.onexit", "ranges": [{"startOffset": 7001, "endOffset": 8039, "count": 1}, {"startOffset": 7049, "endOffset": 7092, "count": 0}, {"startOffset": 7159, "endOffset": 7194, "count": 0}, {"startOffset": 7270, "endOffset": 7566, "count": 0}], "isBlockCoverage": true}, {"functionName": "flushStdio", "ranges": [{"startOffset": 8276, "endOffset": 8754, "count": 1}, {"startOffset": 8366, "endOffset": 8373, "count": 0}, {"startOffset": 8416, "endOffset": 8752, "count": 3}, {"startOffset": 8657, "endOffset": 8676, "count": 0}, {"startOffset": 8677, "endOffset": 8702, "count": 0}, {"startOffset": 8727, "endOffset": 8752, "count": 0}], "isBlockCoverage": true}, {"functionName": "createSocket", "ranges": [{"startOffset": 8757, "endOffset": 8847, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHandleWrapType", "ranges": [{"startOffset": 8850, "endOffset": 9079, "count": 0}], "isBlockCoverage": false}, {"functionName": "closePending<PERSON><PERSON>le", "ranges": [{"startOffset": 9081, "endOffset": 9194, "count": 0}], "isBlockCoverage": false}, {"functionName": "ChildProcess.spawn", "ranges": [{"startOffset": 9228, "endOffset": 12799, "count": 1}, {"startOffset": 9379, "endOffset": 9388, "count": 0}, {"startOffset": 9723, "endOffset": 10108, "count": 0}, {"startOffset": 10226, "endOffset": 10256, "count": 0}, {"startOffset": 10597, "endOffset": 10854, "count": 0}, {"startOffset": 10869, "endOffset": 11160, "count": 0}, {"startOffset": 11281, "endOffset": 12195, "count": 3}, {"startOffset": 11346, "endOffset": 11355, "count": 0}, {"startOffset": 11377, "endOffset": 11428, "count": 0}, {"startOffset": 11649, "endOffset": 11911, "count": 0}, {"startOffset": 11936, "endOffset": 12191, "count": 0}, {"startOffset": 12263, "endOffset": 12284, "count": 0}, {"startOffset": 12360, "endOffset": 12381, "count": 0}, {"startOffset": 12457, "endOffset": 12478, "count": 0}, {"startOffset": 12549, "endOffset": 12659, "count": 3}, {"startOffset": 12640, "endOffset": 12657, "count": 0}, {"startOffset": 12743, "endOffset": 12782, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12130, "endOffset": 12175, "count": 0}], "isBlockCoverage": false}, {"functionName": "onErrorNT", "ranges": [{"startOffset": 12803, "endOffset": 12864, "count": 0}], "isBlockCoverage": false}, {"functionName": "onSpawnNT", "ranges": [{"startOffset": 12867, "endOffset": 12917, "count": 1}], "isBlockCoverage": true}, {"functionName": "ChildProcess.kill", "ranges": [{"startOffset": 12950, "endOffset": 13623, "count": 0}], "isBlockCoverage": false}, {"functionName": "ChildProcess.<computed>", "ranges": [{"startOffset": 13666, "endOffset": 13723, "count": 0}], "isBlockCoverage": false}, {"functionName": "ChildProcess.ref", "ranges": [{"startOffset": 13756, "endOffset": 13810, "count": 0}], "isBlockCoverage": false}, {"functionName": "ChildProcess.unref", "ranges": [{"startOffset": 13845, "endOffset": 13901, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 13904, "endOffset": 14901, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupChannel", "ranges": [{"startOffset": 15049, "endOffset": 27173, "count": 0}], "isBlockCoverage": false}, {"functionName": "isInternal", "ranges": [{"startOffset": 27208, "endOffset": 27518, "count": 0}], "isBlockCoverage": false}, {"functionName": "getValidStdio", "ranges": [{"startOffset": 27552, "endOffset": 30477, "count": 1}, {"startOffset": 27728, "endOffset": 27818, "count": 0}, {"startOffset": 28062, "endOffset": 28099, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28212, "endOffset": 30436, "count": 3}, {"startOffset": 28470, "endOffset": 28518, "count": 0}, {"startOffset": 28548, "endOffset": 28606, "count": 0}, {"startOffset": 28719, "endOffset": 28973, "count": 0}, {"startOffset": 29000, "endOffset": 29394, "count": 0}, {"startOffset": 29425, "endOffset": 29514, "count": 0}, {"startOffset": 29550, "endOffset": 29581, "count": 0}, {"startOffset": 29683, "endOffset": 29693, "count": 0}, {"startOffset": 29710, "endOffset": 30415, "count": 0}], "isBlockCoverage": true}, {"functionName": "cleanup", "ranges": [{"startOffset": 28237, "endOffset": 28429, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSocketList", "ranges": [{"startOffset": 30480, "endOffset": 30796, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeClose", "ranges": [{"startOffset": 30799, "endOffset": 31000, "count": 1}], "isBlockCoverage": true}, {"functionName": "spawnSync", "ranges": [{"startOffset": 31002, "endOffset": 31673, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "230", "url": "node:dgram", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28668, "count": 1}], "isBlockCoverage": false}, {"functionName": "lazyLoadCluster", "ranges": [{"startOffset": 2910, "endOffset": 3007, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket", "ranges": [{"startOffset": 3073, "endOffset": 4712, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSocket", "ranges": [{"startOffset": 4823, "endOffset": 4901, "count": 0}], "isBlockCoverage": false}, {"functionName": "startListening", "ranges": [{"startOffset": 4904, "endOffset": 5347, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 5349, "endOffset": 5914, "count": 0}], "isBlockCoverage": false}, {"functionName": "bufferSize", "ranges": [{"startOffset": 5916, "endOffset": 6208, "count": 0}], "isBlockCoverage": false}, {"functionName": "bindServerHandle", "ranges": [{"startOffset": 6276, "endOffset": 6772, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.bind", "ranges": [{"startOffset": 6798, "endOffset": 10253, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.connect", "ranges": [{"startOffset": 10283, "endOffset": 11079, "count": 0}], "isBlockCoverage": false}, {"functionName": "_connect", "ranges": [{"startOffset": 11083, "endOffset": 11429, "count": 0}], "isBlockCoverage": false}, {"functionName": "doConnect", "ranges": [{"startOffset": 11432, "endOffset": 12078, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.disconnect", "ranges": [{"startOffset": 12111, "endOffset": 12422, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.sendto", "ranges": [{"startOffset": 12527, "endOffset": 12968, "count": 0}], "isBlockCoverage": false}, {"functionName": "sliceBuffer", "ranges": [{"startOffset": 12972, "endOffset": 13732, "count": 0}], "isBlockCoverage": false}, {"functionName": "fixBufferList", "ranges": [{"startOffset": 13735, "endOffset": 14119, "count": 0}], "isBlockCoverage": false}, {"functionName": "enqueue", "ranges": [{"startOffset": 14122, "endOffset": 14549, "count": 0}], "isBlockCoverage": false}, {"functionName": "onListenSuccess", "ranges": [{"startOffset": 14552, "endOffset": 14690, "count": 0}], "isBlockCoverage": false}, {"functionName": "onListenError", "ranges": [{"startOffset": 14693, "endOffset": 14817, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearQueue", "ranges": [{"startOffset": 14820, "endOffset": 15017, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.send", "ranges": [{"startOffset": 15588, "endOffset": 18481, "count": 0}], "isBlockCoverage": false}, {"functionName": "doSend", "ranges": [{"startOffset": 18484, "endOffset": 19627, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterSend", "ranges": [{"startOffset": 19629, "endOffset": 19810, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.close", "ranges": [{"startOffset": 19837, "endOffset": 20405, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.<computed>", "ranges": [{"startOffset": 20447, "endOffset": 20580, "count": 0}], "isBlockCoverage": false}, {"functionName": "socketCloseNT", "ranges": [{"startOffset": 20584, "endOffset": 20638, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.address", "ranges": [{"startOffset": 20668, "endOffset": 20862, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.remoteAddress", "ranges": [{"startOffset": 20898, "endOffset": 21212, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setBroadcast", "ranges": [{"startOffset": 21248, "endOffset": 21400, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setTTL", "ranges": [{"startOffset": 21430, "endOffset": 21608, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setMulticastTTL", "ranges": [{"startOffset": 21647, "endOffset": 21843, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setMulticastLoopback", "ranges": [{"startOffset": 21887, "endOffset": 22091, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setMulticastInterface", "ranges": [{"startOffset": 22136, "endOffset": 22402, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.addMembership", "ranges": [{"startOffset": 22438, "endOffset": 22816, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.dropMembership", "ranges": [{"startOffset": 22854, "endOffset": 23235, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.addSourceSpecificMembership", "ranges": [{"startOffset": 23285, "endOffset": 23896, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.dropSourceSpecificMembership", "ranges": [{"startOffset": 23948, "endOffset": 24565, "count": 0}], "isBlockCoverage": false}, {"functionName": "healthCheck", "ranges": [{"startOffset": 24569, "endOffset": 24732, "count": 0}], "isBlockCoverage": false}, {"functionName": "stopReceiving", "ranges": [{"startOffset": 24735, "endOffset": 24899, "count": 0}], "isBlockCoverage": false}, {"functionName": "onMessage", "ranges": [{"startOffset": 24902, "endOffset": 25160, "count": 0}], "isBlockCoverage": false}, {"functionName": "onError", "ranges": [{"startOffset": 25163, "endOffset": 25278, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.ref", "ranges": [{"startOffset": 25304, "endOffset": 25411, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.unref", "ranges": [{"startOffset": 25440, "endOffset": 25549, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setRecvBufferSize", "ranges": [{"startOffset": 25590, "endOffset": 25647, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setSendBufferSize", "ranges": [{"startOffset": 25688, "endOffset": 25745, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.getRecvBufferSize", "ranges": [{"startOffset": 25786, "endOffset": 25843, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.getSendBufferSize", "ranges": [{"startOffset": 25884, "endOffset": 25941, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.getSendQueueSize", "ranges": [{"startOffset": 25980, "endOffset": 26049, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.getSendQueueCount", "ranges": [{"startOffset": 26089, "endOffset": 26159, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26278, "endOffset": 26332, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26405, "endOffset": 26461, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26614, "endOffset": 26671, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26747, "endOffset": 26806, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26962, "endOffset": 27019, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27095, "endOffset": 27154, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27306, "endOffset": 27359, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27431, "endOffset": 27486, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27638, "endOffset": 27695, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27771, "endOffset": 27830, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27937, "endOffset": 27972, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 28081, "endOffset": 28118, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 28403, "endOffset": 28439, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 28443, "endOffset": 28484, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "231", "url": "node:internal/dgram", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1851, "count": 1}], "isBlockCoverage": false}, {"functionName": "lookup4", "ranges": [{"startOffset": 478, "endOffset": 579, "count": 0}], "isBlockCoverage": false}, {"functionName": "lookup6", "ranges": [{"startOffset": 582, "endOffset": 677, "count": 0}], "isBlockCoverage": false}, {"functionName": "newHandle", "ranges": [{"startOffset": 679, "endOffset": 1323, "count": 0}], "isBlockCoverage": false}, {"functionName": "_createSocketHandle", "ranges": [{"startOffset": 1326, "endOffset": 1774, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "232", "url": "node:internal/socket_list", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2709, "count": 1}], "isBlockCoverage": false}, {"functionName": "SocketListSend", "ranges": [{"startOffset": 235, "endOffset": 376, "count": 0}], "isBlockCoverage": false}, {"functionName": "_request", "ranges": [{"startOffset": 380, "endOffset": 1035, "count": 0}], "isBlockCoverage": false}, {"functionName": "close", "ranges": [{"startOffset": 1039, "endOffset": 1190, "count": 0}], "isBlockCoverage": false}, {"functionName": "getConnections", "ranges": [{"startOffset": 1194, "endOffset": 1430, "count": 0}], "isBlockCoverage": false}, {"functionName": "SocketListReceive", "ranges": [{"startOffset": 1544, "endOffset": 2404, "count": 0}], "isBlockCoverage": false}, {"functionName": "add", "ranges": [{"startOffset": 2408, "endOffset": 2649, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "233", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/promise-spawn/lib/escape.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1538, "count": 1}], "isBlockCoverage": true}, {"functionName": "cmd", "ranges": [{"startOffset": 216, "endOffset": 1057, "count": 0}], "isBlockCoverage": false}, {"functionName": "sh", "ranges": [{"startOffset": 1070, "endOffset": 1502, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "234", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/package-envs.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 826, "count": 1}], "isBlockCoverage": true}, {"functionName": "packageEnvs", "ranges": [{"startOffset": 20, "endOffset": 549, "count": 3}, {"startOffset": 99, "endOffset": 534, "count": 12}, {"startOffset": 128, "endOffset": 150, "count": 1}, {"startOffset": 150, "endOffset": 530, "count": 11}, {"startOffset": 191, "endOffset": 232, "count": 0}, {"startOffset": 262, "endOffset": 384, "count": 0}, {"startOffset": 419, "endOffset": 474, "count": 2}, {"startOffset": 474, "endOffset": 530, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 282, "endOffset": 377, "count": 0}], "isBlockCoverage": false}, {"functionName": "module.exports", "ranges": [{"startOffset": 657, "endOffset": 825, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "235", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/node-gyp/lib/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 349, "count": 1}, {"startOffset": 83, "endOffset": 119, "count": 0}], "isBlockCoverage": true}, {"functionName": "isNodeGypPackage", "ranges": [{"startOffset": 121, "endOffset": 259, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "236", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/signal-manager.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1051, "count": 1}], "isBlockCoverage": true}, {"functionName": "handleSignal", "ranges": [{"startOffset": 369, "endOffset": 445, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupListeners", "ranges": [{"startOffset": 470, "endOffset": 590, "count": 1}, {"startOffset": 519, "endOffset": 561, "count": 2}], "isBlockCoverage": true}, {"functionName": "cleanupListeners", "ranges": [{"startOffset": 617, "endOffset": 795, "count": 1}, {"startOffset": 701, "endOffset": 759, "count": 2}], "isBlockCoverage": true}, {"functionName": "add", "ranges": [{"startOffset": 809, "endOffset": 985, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 918, "endOffset": 982, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "237", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/is-server-package.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 245, "count": 1}], "isBlockCoverage": true}, {"functionName": "module.exports", "ranges": [{"startOffset": 104, "endOffset": 244, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "238", "url": "file:///usr/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/validate-options.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1212, "count": 1}], "isBlockCoverage": true}, {"functionName": "validateOptions", "ranges": [{"startOffset": 24, "endOffset": 1177, "count": 1}, {"startOffset": 84, "endOffset": 161, "count": 0}, {"startOffset": 327, "endOffset": 397, "count": 0}, {"startOffset": 439, "endOffset": 508, "count": 0}, {"startOffset": 541, "endOffset": 575, "count": 0}, {"startOffset": 577, "endOffset": 658, "count": 0}, {"startOffset": 698, "endOffset": 771, "count": 0}, {"startOffset": 804, "endOffset": 828, "count": 0}, {"startOffset": 830, "endOffset": 905, "count": 0}, {"startOffset": 975, "endOffset": 1049, "count": 0}, {"startOffset": 1074, "endOffset": 1100, "count": 0}, {"startOffset": 1102, "endOffset": 1175, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 946, "endOffset": 972, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "239", "url": "node:readline", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12824, "count": 1}], "isBlockCoverage": false}, {"functionName": "Interface", "ranges": [{"startOffset": 2591, "endOffset": 3336, "count": 0}], "isBlockCoverage": false}, {"functionName": "question", "ranges": [{"startOffset": 3655, "endOffset": 4449, "count": 0}], "isBlockCoverage": false}, {"functionName": "question", "ranges": [{"startOffset": 4500, "endOffset": 5288, "count": 0}], "isBlockCoverage": false}, {"functionName": "createInterface", "ranges": [{"startOffset": 5822, "endOffset": 5946, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6139, "endOffset": 6183, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6230, "endOffset": 6270, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6324, "endOffset": 6371, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6422, "endOffset": 6466, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6518, "endOffset": 6563, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6615, "endOffset": 6660, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6713, "endOffset": 6759, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6811, "endOffset": 6856, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6905, "endOffset": 6947, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6997, "endOffset": 7040, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7091, "endOffset": 7135, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7187, "endOffset": 7232, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7287, "endOffset": 7335, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7391, "endOffset": 7440, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7495, "endOffset": 7543, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7599, "endOffset": 7648, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7693, "endOffset": 7731, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7783, "endOffset": 7828, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7880, "endOffset": 7925, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7979, "endOffset": 8026, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8077, "endOffset": 8121, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8170, "endOffset": 8212, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8351, "endOffset": 8393, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 8399, "endOffset": 8447, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8497, "endOffset": 8543, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 8549, "endOffset": 8601, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8649, "endOffset": 8693, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 8699, "endOffset": 8749, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8799, "endOffset": 8845, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 8851, "endOffset": 8903, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8948, "endOffset": 8989, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 8995, "endOffset": 9042, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9097, "endOffset": 9148, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 9154, "endOffset": 9211, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9261, "endOffset": 9307, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 9313, "endOffset": 9365, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9415, "endOffset": 9461, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 9467, "endOffset": 9519, "count": 0}], "isBlockCoverage": false}, {"functionName": "Interface._tabComplete", "ranges": [{"startOffset": 10115, "endOffset": 10596, "count": 0}], "isBlockCoverage": false}, {"functionName": "_ttyWriteDumb", "ranges": [{"startOffset": 11662, "endOffset": 12676, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "240", "url": "node:internal/readline/emitKeypressEvents", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2663, "count": 1}], "isBlockCoverage": false}, {"functionName": "emitKeypressEvents", "ranges": [{"startOffset": 644, "endOffset": 2624, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "241", "url": "node:readline/promises", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1658, "count": 1}], "isBlockCoverage": false}, {"functionName": "Interface", "ranges": [{"startOffset": 531, "endOffset": 631, "count": 0}], "isBlockCoverage": false}, {"functionName": "question", "ranges": [{"startOffset": 634, "endOffset": 1462, "count": 0}], "isBlockCoverage": false}, {"functionName": "createInterface", "ranges": [{"startOffset": 1466, "endOffset": 1590, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "242", "url": "node:internal/readline/promises", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3509, "count": 1}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 452, "endOffset": 3473, "count": 0}], "isBlockCoverage": false}]}], "timestamp": 20083.856473, "source-map-cache": {"file:///usr/lib/node_modules/npm/node_modules/walk-up-path/dist/commonjs/index.js": {"lineLengths": [13, 62, 24, 31, 33, 51, 19, 45, 26, 18, 9, 14, 22, 9, 5, 2, 24, 33], "data": null, "url": "index.js.map"}, "file:///usr/lib/node_modules/npm/node_modules/which/node_modules/isexe/dist/cjs/index.js": {"lineLengths": [13, 97, 33, 53, 88, 68, 5, 39, 29, 33, 17, 4, 96, 72, 21, 21, 3, 66, 42, 20, 141, 36, 18, 2, 72, 126, 2, 62, 70, 50, 22, 50, 22, 47, 71, 50, 3, 66, 3, 27, 3, 62, 20, 3, 25, 33], "data": null, "url": "index.js.map"}, "file:///usr/lib/node_modules/npm/node_modules/which/node_modules/isexe/dist/cjs/posix.js": {"lineLengths": [13, 3, 65, 27, 2, 10, 3, 62, 38, 27, 42, 3, 67, 45, 3, 45, 45, 9, 68, 5, 15, 21, 49, 25, 17, 5, 2, 22, 3, 68, 58, 3, 38, 45, 9, 60, 5, 15, 21, 49, 25, 17, 5, 2, 20, 79, 38, 52, 67, 67, 53, 49, 5, 49, 26, 25, 25, 33, 33, 33, 21, 24, 39, 37, 35, 2, 33], "data": null, "url": "posix.js.map"}, "file:///usr/lib/node_modules/npm/node_modules/which/node_modules/isexe/dist/cjs/win32.js": {"lineLengths": [13, 3, 67, 33, 2, 10, 3, 62, 38, 27, 42, 3, 69, 65, 3, 45, 45, 9, 74, 5, 15, 21, 49, 25, 17, 5, 2, 22, 3, 73, 75, 3, 38, 45, 9, 66, 5, 15, 21, 49, 25, 17, 5, 2, 20, 41, 60, 39, 37, 20, 5, 46, 43, 73, 29, 24, 9, 5, 17, 2, 88, 33], "data": null, "url": "win32.js.map"}, "file:///usr/lib/node_modules/npm/node_modules/which/node_modules/isexe/dist/cjs/options.js": {"lineLengths": [13, 62, 35], "data": null, "url": "options.js.map"}, "file:///usr/lib/node_modules/npm/node_modules/minipass/dist/commonjs/index.js": {"lineLengths": [13, 72, 62, 2, 62, 87, 51, 13, 7, 21, 21, 6, 45, 62, 61, 3, 78, 40, 3, 30, 28, 29, 45, 37, 36, 28, 3, 67, 3, 32, 28, 46, 35, 69, 61, 32, 3, 67, 3, 32, 28, 46, 36, 32, 32, 26, 46, 41, 43, 45, 32, 28, 30, 40, 36, 34, 34, 32, 32, 32, 30, 44, 40, 42, 40, 42, 38, 42, 30, 36, 34, 36, 30, 30, 34, 32, 46, 38, 49, 29, 79, 60, 11, 32, 24, 47, 27, 78, 3, 62, 2, 12, 3, 12, 8, 9, 9, 12, 34, 23, 25, 25, 43, 44, 5, 14, 56, 5, 34, 25, 24, 24, 11, 22, 26, 28, 5, 1, 3, 67, 22, 2, 12, 3, 36, 14, 59, 23, 5, 34, 31, 56, 42, 5, 1, 50, 90, 3, 34, 2, 58, 2, 75, 55, 2, 67, 41, 3, 51, 22, 21, 17, 18, 17, 15, 12, 14, 18, 26, 27, 21, 27, 23, 24, 13, 22, 24, 24, 7, 40, 7, 20, 7, 37, 7, 20, 7, 69, 70, 51, 63, 7, 26, 35, 16, 16, 73, 84, 9, 43, 36, 34, 9, 46, 46, 37, 9, 14, 37, 34, 9, 38, 38, 69, 19, 63, 60, 79, 9, 63, 59, 77, 9, 35, 21, 34, 33, 30, 13, 18, 70, 13, 9, 5, 7, 66, 6, 62, 77, 55, 73, 15, 7, 24, 34, 5, 7, 55, 7, 20, 30, 5, 7, 49, 7, 24, 70, 5, 7, 67, 7, 23, 70, 5, 7, 43, 7, 22, 32, 5, 7, 49, 7, 25, 72, 5, 7, 38, 7, 21, 27, 5, 7, 45, 6, 78, 74, 34, 7, 22, 41, 5, 57, 15, 29, 49, 43, 5, 7, 43, 7, 19, 29, 5, 7, 78, 34, 7, 22, 32, 26, 25, 22, 47, 30, 141, 24, 9, 45, 26, 30, 9, 22, 30, 49, 67, 71, 42, 63, 59, 43, 58, 86, 13, 48, 58, 43, 13, 49, 88, 13, 9, 58, 62, 31, 32, 33, 58, 34, 32, 30, 41, 16, 40, 41, 38, 19, 23, 33, 9, 56, 55, 28, 41, 38, 19, 23, 33, 9, 70, 61, 40, 64, 73, 52, 49, 9, 55, 52, 47, 9, 73, 54, 30, 26, 37, 12, 36, 37, 34, 15, 19, 29, 5, 7, 38, 6, 74, 17, 6, 70, 77, 66, 6, 72, 31, 7, 13, 28, 24, 32, 39, 22, 44, 35, 24, 9, 29, 21, 59, 79, 40, 28, 31, 43, 71, 14, 9, 59, 31, 19, 5, 22, 29, 32, 14, 28, 45, 36, 45, 45, 38, 40, 13, 18, 48, 41, 40, 13, 9, 33, 47, 31, 21, 5, 30, 42, 23, 30, 9, 45, 26, 30, 9, 32, 40, 15, 33, 25, 30, 66, 37, 65, 63, 43, 35, 20, 5, 51, 16, 28, 19, 58, 35, 9, 29, 29, 28, 32, 26, 27, 35, 12, 31, 5, 7, 61, 6, 79, 77, 78, 76, 41, 7, 14, 30, 5, 7, 23, 7, 13, 30, 28, 32, 5, 7, 53, 7, 21, 31, 5, 7, 71, 46, 7, 19, 29, 5, 7, 56, 7, 18, 28, 5, 25, 29, 36, 12, 47, 33, 5, 21, 29, 36, 12, 57, 36, 5, 30, 62, 33, 59, 31, 5, 25, 33, 29, 5, 7, 74, 6, 33, 7, 22, 28, 24, 32, 40, 26, 57, 29, 12, 42, 46, 50, 20, 25, 27, 9, 14, 78, 76, 46, 44, 57, 28, 44, 16, 31, 9, 20, 5, 7, 47, 6, 74, 73, 73, 52, 7, 18, 57, 16, 43, 65, 42, 17, 33, 13, 16, 62, 23, 9, 5, 7, 36, 7, 30, 36, 5, 7, 64, 71, 6, 68, 6, 80, 57, 6, 75, 78, 26, 6, 77, 79, 16, 7, 21, 42, 28, 36, 34, 56, 31, 13, 9, 65, 35, 9, 53, 27, 40, 9, 57, 30, 28, 63, 16, 50, 9, 19, 5, 7, 37, 7, 33, 37, 5, 7, 45, 6, 73, 76, 73, 52, 7, 22, 43, 73, 74, 64, 28, 64, 44, 35, 38, 38, 13, 9, 19, 5, 7, 60, 6, 80, 76, 79, 14, 7, 28, 49, 48, 36, 58, 38, 13, 9, 19, 5, 7, 47, 7, 22, 33, 5, 24, 34, 33, 31, 40, 24, 38, 29, 35, 32, 29, 35, 39, 9, 5, 7, 66, 71, 6, 73, 72, 18, 6, 74, 74, 75, 71, 19, 6, 72, 63, 6, 79, 74, 71, 6, 74, 66, 7, 23, 29, 74, 29, 29, 31, 30, 25, 9, 33, 45, 23, 29, 63, 43, 9, 32, 35, 9, 34, 32, 57, 55, 29, 44, 45, 23, 9, 34, 39, 36, 71, 43, 24, 35, 23, 9, 35, 45, 35, 23, 9, 57, 39, 40, 23, 9, 35, 44, 31, 19, 5, 22, 38, 45, 29, 9, 71, 31, 19, 5, 17, 30, 25, 33, 30, 26, 51, 31, 5, 18, 28, 45, 23, 46, 39, 17, 37, 45, 13, 9, 38, 20, 9, 38, 39, 19, 5, 7, 74, 23, 7, 21, 39, 26, 11, 30, 31, 60, 39, 33, 30, 24, 34, 43, 11, 16, 19, 5, 7, 78, 28, 6, 41, 7, 20, 31, 59, 9, 41, 30, 26, 50, 5, 7, 64, 7, 21, 49, 76, 47, 44, 11, 5, 7, 45, 6, 74, 7, 30, 70, 22, 32, 28, 34, 25, 27, 52, 10, 28, 24, 30, 36, 29, 68, 26, 30, 24, 23, 35, 41, 39, 47, 23, 27, 14, 39, 41, 39, 47, 29, 54, 14, 33, 41, 41, 47, 23, 58, 14, 73, 46, 29, 30, 48, 42, 40, 42, 15, 10, 16, 17, 24, 25, 38, 28, 14, 10, 5, 7, 38, 6, 75, 44, 7, 25, 70, 22, 32, 28, 28, 25, 34, 38, 34, 27, 52, 10, 28, 24, 30, 38, 68, 10, 31, 31, 35, 16, 17, 24, 25, 33, 28, 14, 10, 5, 7, 79, 6, 70, 19, 6, 76, 30, 6, 70, 21, 7, 17, 30, 19, 39, 16, 37, 24, 9, 31, 31, 62, 32, 31, 24, 60, 23, 15, 35, 61, 12, 33, 20, 5, 7, 33, 6, 70, 6, 18, 7, 27, 32, 5, 1, 28, 33], "data": null, "url": "index.js.map"}}}