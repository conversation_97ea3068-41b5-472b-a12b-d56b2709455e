{"result": [{"scriptId": "6", "url": "node:internal/per_context/primordials", "functions": [{"functionName": "SafeIterator", "ranges": [{"startOffset": 9149, "endOffset": 9220, "count": 7}], "isBlockCoverage": false}, {"functionName": "next", "ranges": [{"startOffset": 9225, "endOffset": 9274, "count": 287}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9279, "endOffset": 9324, "count": 4}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10889, "endOffset": 10956, "count": 5}], "isBlockCoverage": false}, {"functionName": "SafeMap", "ranges": [{"startOffset": 11614, "endOffset": 11642, "count": 13}], "isBlockCoverage": false}, {"functionName": "SafeSet", "ranges": [{"startOffset": 11943, "endOffset": 11971, "count": 13}], "isBlockCoverage": false}, {"functionName": "SafeFinalizationRegistry", "ranges": [{"startOffset": 12395, "endOffset": 12451, "count": 3}], "isBlockCoverage": false}, {"functionName": "SafeWeakRef", "ranges": [{"startOffset": 12604, "endOffset": 12642, "count": 10}], "isBlockCoverage": false}]}, {"scriptId": "9", "url": "node:internal/bootstrap/realm", "functions": [{"functionName": "internalBinding", "ranges": [{"startOffset": 5981, "endOffset": 6243, "count": 61}], "isBlockCoverage": false}, {"functionName": "canBeRequiredByUsers", "ranges": [{"startOffset": 9476, "endOffset": 9551, "count": 22}], "isBlockCoverage": true}, {"functionName": "canBeRequiredWithoutScheme", "ranges": [{"startOffset": 9562, "endOffset": 9656, "count": 45}], "isBlockCoverage": true}, {"functionName": "normalizeRequirableId", "ranges": [{"startOffset": 9667, "endOffset": 10012, "count": 27}, {"startOffset": 9743, "endOffset": 9905, "count": 0}, {"startOffset": 9961, "endOffset": 9985, "count": 11}, {"startOffset": 9985, "endOffset": 10011, "count": 16}], "isBlockCoverage": true}, {"functionName": "getCanBeRequiredByUsersWithoutSchemeList", "ranges": [{"startOffset": 10275, "endOffset": 10384, "count": 1}], "isBlockCoverage": true}, {"functionName": "compileForPublicLoader", "ranges": [{"startOffset": 10542, "endOffset": 11205, "count": 11}, {"startOffset": 10623, "endOffset": 10815, "count": 0}, {"startOffset": 10879, "endOffset": 11176, "count": 6}, {"startOffset": 11138, "endOffset": 11142, "count": 0}], "isBlockCoverage": true}, {"functionName": "compileForInternalLoader", "ranges": [{"startOffset": 12487, "endOffset": 13309, "count": 340}], "isBlockCoverage": false}, {"functionName": "requireBuiltin", "ranges": [{"startOffset": 13504, "endOffset": 13869, "count": 329}], "isBlockCoverage": false}]}, {"scriptId": "10", "url": "node:internal/errors", "functions": [{"functionName": "setInternalPrepareStackTrace", "ranges": [{"startOffset": 2771, "endOffset": 2862, "count": 1}], "isBlockCoverage": true}, {"functionName": "hideStackFrames", "ranges": [{"startOffset": 14657, "endOffset": 14954, "count": 7}], "isBlockCoverage": false}, {"functionName": "wrappedFn", "ranges": [{"startOffset": 14690, "endOffset": 14896, "count": 258}], "isBlockCoverage": false}]}, {"scriptId": "11", "url": "node:internal/assert", "functions": [{"functionName": "assert", "ranges": [{"startOffset": 157, "endOffset": 307, "count": 15}, {"startOffset": 205, "endOffset": 305, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "12", "url": "node:internal/bootstrap/node", "functions": [{"functionName": "get", "ranges": [{"startOffset": 3830, "endOffset": 3880, "count": 3}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 3886, "endOffset": 3944, "count": 1}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 4128, "endOffset": 4547, "count": 2}, {"startOffset": 4187, "endOffset": 4469, "count": 1}, {"startOffset": 4252, "endOffset": 4266, "count": 0}, {"startOffset": 4267, "endOffset": 4315, "count": 0}, {"startOffset": 4317, "endOffset": 4352, "count": 0}, {"startOffset": 4469, "endOffset": 4518, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 8066, "endOffset": 8228, "count": 1}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 8330, "endOffset": 8526, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 13230, "endOffset": 13266, "count": 15}], "isBlockCoverage": true}]}, {"scriptId": "14", "url": "node:internal/async_hooks", "functions": [{"functionName": "newAsyncId", "ranges": [{"startOffset": 14549, "endOffset": 14619, "count": 3}], "isBlockCoverage": true}, {"functionName": "getDefaultTriggerAsyncId", "ranges": [{"startOffset": 14994, "endOffset": 15284, "count": 3}, {"startOffset": 15250, "endOffset": 15283, "count": 0}], "isBlockCoverage": true}, {"functionName": "hasHooks", "ranges": [{"startOffset": 16096, "endOffset": 16159, "count": 15}], "isBlockCoverage": true}, {"functionName": "enabledHooksExist", "ranges": [{"startOffset": 16161, "endOffset": 16220, "count": 3}], "isBlockCoverage": true}, {"functionName": "initHooksExist", "ranges": [{"startOffset": 16222, "endOffset": 16277, "count": 3}], "isBlockCoverage": true}, {"functionName": "destroyHooksExist", "ranges": [{"startOffset": 16338, "endOffset": 16399, "count": 3}], "isBlockCoverage": true}, {"functionName": "emitBeforeScript", "ranges": [{"startOffset": 16890, "endOffset": 17066, "count": 3}, {"startOffset": 17038, "endOffset": 17064, "count": 0}], "isBlockCoverage": true}, {"functionName": "emitAfterScript", "ranges": [{"startOffset": 17069, "endOffset": 17189, "count": 3}, {"startOffset": 17133, "endOffset": 17158, "count": 0}], "isBlockCoverage": true}, {"functionName": "pushAsyncContext", "ranges": [{"startOffset": 17534, "endOffset": 18104, "count": 3}, {"startOffset": 17754, "endOffset": 17804, "count": 0}], "isBlockCoverage": true}, {"functionName": "popAsyncContext", "ranges": [{"startOffset": 18169, "endOffset": 18780, "count": 3}, {"startOffset": 18285, "endOffset": 18298, "count": 0}, {"startOffset": 18377, "endOffset": 18483, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "15", "url": "node:internal/validators", "functions": [{"functionName": "isInt32", "ranges": [{"startOffset": 1087, "endOffset": 1146, "count": 8}], "isBlockCoverage": true}, {"functionName": "parseFileMode", "ranges": [{"startOffset": 1881, "endOffset": 2192, "count": 1}, {"startOffset": 1974, "endOffset": 2142, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2438, "endOffset": 2819, "count": 1}, {"startOffset": 2558, "endOffset": 2612, "count": 0}, {"startOffset": 2652, "endOffset": 2706, "count": 0}, {"startOffset": 2749, "endOffset": 2815, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3757, "endOffset": 4189, "count": 1}, {"startOffset": 3827, "endOffset": 3891, "count": 0}, {"startOffset": 3923, "endOffset": 3987, "count": 0}, {"startOffset": 4011, "endOffset": 4014, "count": 0}, {"startOffset": 4111, "endOffset": 4187, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4381, "endOffset": 4493, "count": 210}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5369, "endOffset": 5702, "count": 1}, {"startOffset": 5440, "endOffset": 5700, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5513, "endOffset": 5574, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5898, "endOffset": 6012, "count": 2}, {"startOffset": 5955, "endOffset": 6010, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6594, "endOffset": 7613, "count": 2}, {"startOffset": 6736, "endOffset": 6808, "count": 0}, {"startOffset": 6847, "endOffset": 6919, "count": 0}, {"startOffset": 6925, "endOffset": 7609, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8567, "endOffset": 8845, "count": 1}, {"startOffset": 8629, "endOffset": 8692, "count": 0}, {"startOffset": 8725, "endOffset": 8843, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11548, "endOffset": 11860, "count": 1}, {"startOffset": 11694, "endOffset": 11713, "count": 0}, {"startOffset": 11715, "endOffset": 11858, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12692, "endOffset": 12909, "count": 1}, {"startOffset": 12739, "endOffset": 12835, "count": 0}, {"startOffset": 12837, "endOffset": 12907, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13109, "endOffset": 13225, "count": 11}], "isBlockCoverage": false}]}, {"scriptId": "16", "url": "node:internal/util", "functions": [{"functionName": "getDeprecationWarningEmitter", "ranges": [{"startOffset": 2398, "endOffset": 3047, "count": 16}], "isBlockCoverage": false}, {"functionName": "shouldEmitW<PERSON>ning", "ranges": [{"startOffset": 2497, "endOffset": 2507, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2544, "endOffset": 3044, "count": 0}], "isBlockCoverage": false}, {"functionName": "deprecate", "ranges": [{"startOffset": 3796, "endOffset": 4850, "count": 16}], "isBlockCoverage": false}, {"functionName": "deprecated", "ranges": [{"startOffset": 4160, "endOffset": 4462, "count": 0}], "isBlockCoverage": false}, {"functionName": "assertCrypto", "ranges": [{"startOffset": 5120, "endOffset": 5194, "count": 1}, {"startOffset": 5166, "endOffset": 5192, "count": 0}], "isBlockCoverage": true}, {"functionName": "normalizeEncoding", "ranges": [{"startOffset": 5373, "endOffset": 5504, "count": 9}, {"startOffset": 5425, "endOffset": 5442, "count": 8}, {"startOffset": 5443, "endOffset": 5461, "count": 0}, {"startOffset": 5477, "endOffset": 5503, "count": 0}], "isBlockCoverage": true}, {"functionName": "cachedResult", "ranges": [{"startOffset": 8281, "endOffset": 8437, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8332, "endOffset": 8434, "count": 0}], "isBlockCoverage": false}, {"functionName": "promisify", "ranges": [{"startOffset": 11280, "endOffset": 13141, "count": 2}, {"startOffset": 11400, "endOffset": 11456, "count": 1}, {"startOffset": 11543, "endOffset": 11820, "count": 0}, {"startOffset": 12927, "endOffset": 13089, "count": 6}], "isBlockCoverage": true}, {"functionName": "fn", "ranges": [{"startOffset": 12020, "endOffset": 12539, "count": 0}], "isBlockCoverage": false}, {"functionName": "exposeInterface", "ranges": [{"startOffset": 16032, "endOffset": 16252, "count": 1}], "isBlockCoverage": false}, {"functionName": "defineLazyProperties", "ranges": [{"startOffset": 16751, "endOffset": 17684, "count": 1}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 16970, "endOffset": 17113, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 17216, "endOffset": 17412, "count": 0}], "isBlockCoverage": false}, {"functionName": "defineReplaceableLazyAttribute", "ranges": [{"startOffset": 17686, "endOffset": 18586, "count": 1}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 17898, "endOffset": 18134, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 18239, "endOffset": 18310, "count": 0}], "isBlockCoverage": false}, {"functionName": "exposeLazyInterfaces", "ranges": [{"startOffset": 18588, "endOffset": 18688, "count": 1}], "isBlockCoverage": false}, {"functionName": "setOwnProperty", "ranges": [{"startOffset": 19883, "endOffset": 20073, "count": 16}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22175, "endOffset": 22303, "count": 43}, {"startOffset": 22219, "endOffset": 22281, "count": 3}], "isBlockCoverage": true}, {"functionName": "setupCoverageHooks", "ranges": [{"startOffset": 22431, "endOffset": 23108, "count": 1}, {"startOffset": 22901, "endOffset": 23078, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 24597, "endOffset": 25141, "count": 9}], "isBlockCoverage": false}, {"functionName": "WeakReference", "ranges": [{"startOffset": 24672, "endOffset": 24739, "count": 9}], "isBlockCoverage": true}, {"functionName": "incRef", "ranges": [{"startOffset": 24743, "endOffset": 24961, "count": 0}], "isBlockCoverage": false}, {"functionName": "decRef", "ranges": [{"startOffset": 24965, "endOffset": 25093, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 25097, "endOffset": 25139, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "17", "url": "node:internal/options", "functions": [{"functionName": "getCLIOptionsFromBinding", "ranges": [{"startOffset": 495, "endOffset": 623, "count": 73}, {"startOffset": 553, "endOffset": 599, "count": 1}], "isBlockCoverage": true}, {"functionName": "getCLIOptionsInfoFromBinding", "ranges": [{"startOffset": 625, "endOffset": 743, "count": 1}], "isBlockCoverage": true}, {"functionName": "getEmbedderOptions", "ranges": [{"startOffset": 745, "endOffset": 889, "count": 5}, {"startOffset": 801, "endOffset": 861, "count": 1}], "isBlockCoverage": true}, {"functionName": "refreshOptions", "ranges": [{"startOffset": 891, "endOffset": 947, "count": 1}], "isBlockCoverage": true}, {"functionName": "getOptionValue", "ranges": [{"startOffset": 949, "endOffset": 1072, "count": 73}], "isBlockCoverage": true}]}, {"scriptId": "18", "url": "node:internal/util/types", "functions": [{"functionName": "isUint8Array", "ranges": [{"startOffset": 236, "endOffset": 342, "count": 17}], "isBlockCoverage": true}]}, {"scriptId": "22", "url": "node:internal/util/debuglog", "functions": [{"functionName": "initializeDebugEnv", "ranges": [{"startOffset": 503, "endOffset": 976, "count": 1}, {"startOffset": 595, "endOffset": 932, "count": 0}], "isBlockCoverage": true}, {"functionName": "testEnabled", "ranges": [{"startOffset": 870, "endOffset": 927, "count": 0}], "isBlockCoverage": false}, {"functionName": "testEnabled", "ranges": [{"startOffset": 958, "endOffset": 969, "count": 3}], "isBlockCoverage": true}, {"functionName": "noop", "ranges": [{"startOffset": 1404, "endOffset": 1412, "count": 48}], "isBlockCoverage": true}, {"functionName": "debuglogImpl", "ranges": [{"startOffset": 1533, "endOffset": 2091, "count": 3}, {"startOffset": 1608, "endOffset": 2063, "count": 2}, {"startOffset": 1627, "endOffset": 2016, "count": 0}], "isBlockCoverage": true}, {"functionName": "debug", "ranges": [{"startOffset": 1716, "endOffset": 2009, "count": 0}], "isBlockCoverage": false}, {"functionName": "debuglog", "ranges": [{"startOffset": 2311, "endOffset": 3337, "count": 4}], "isBlockCoverage": false}, {"functionName": "init", "ranges": [{"startOffset": 2342, "endOffset": 2438, "count": 3}], "isBlockCoverage": true}, {"functionName": "debug", "ranges": [{"startOffset": 2453, "endOffset": 2847, "count": 3}, {"startOffset": 2700, "endOffset": 2730, "count": 0}, {"startOffset": 2737, "endOffset": 2776, "count": 2}, {"startOffset": 2783, "endOffset": 2837, "count": 1}], "isBlockCoverage": true}, {"functionName": "test", "ranges": [{"startOffset": 2877, "endOffset": 2946, "count": 0}], "isBlockCoverage": false}, {"functionName": "logger", "ranges": [{"startOffset": 2965, "endOffset": 3160, "count": 3}, {"startOffset": 3013, "endOffset": 3043, "count": 0}, {"startOffset": 3050, "endOffset": 3089, "count": 2}, {"startOffset": 3096, "endOffset": 3150, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3231, "endOffset": 3265, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "23", "url": "node:events", "functions": [{"functionName": "EventEmitter", "ranges": [{"startOffset": 6575, "endOffset": 6644, "count": 1}], "isBlockCoverage": false}, {"functionName": "checkListener", "ranges": [{"startOffset": 8095, "endOffset": 8173, "count": 5}], "isBlockCoverage": false}, {"functionName": "EventEmitter.init", "ranges": [{"startOffset": 10152, "endOffset": 10820, "count": 1}], "isBlockCoverage": false}, {"functionName": "emit", "ranges": [{"startOffset": 13617, "endOffset": 15920, "count": 9}], "isBlockCoverage": false}, {"functionName": "_addListener", "ranges": [{"startOffset": 15923, "endOffset": 17839, "count": 5}], "isBlockCoverage": false}, {"functionName": "addListener", "ranges": [{"startOffset": 18017, "endOffset": 18109, "count": 5}], "isBlockCoverage": false}, {"functionName": "arrayClone", "ranges": [{"startOffset": 25508, "endOffset": 25981, "count": 4}], "isBlockCoverage": false}]}, {"scriptId": "25", "url": "node:buffer", "functions": [{"functionName": "get", "ranges": [{"startOffset": 8395, "endOffset": 8423, "count": 1}], "isBlockCoverage": true}, {"functionName": "isEncoding", "ranges": [{"startOffset": 16155, "endOffset": 16306, "count": 8}], "isBlockCoverage": true}]}, {"scriptId": "26", "url": "node:internal/buffer", "functions": [{"functionName": "FastBuffer", "ranges": [{"startOffset": 26365, "endOffset": 26465, "count": 1}], "isBlockCoverage": false}, {"functionName": "reconnectZeroFillToggle", "ranges": [{"startOffset": 30682, "endOffset": 30754, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "28", "url": "node:internal/process/per_thread", "functions": [{"functionName": "buildAllowedFlags", "ranges": [{"startOffset": 7998, "endOffset": 12145, "count": 1}, {"startOffset": 8339, "endOffset": 8630, "count": 182}, {"startOffset": 8391, "endOffset": 8626, "count": 144}, {"startOffset": 8488, "endOffset": 8620, "count": 74}, {"startOffset": 9090, "endOffset": 9512, "count": 24}, {"startOffset": 9144, "endOffset": 9508, "count": 12}, {"startOffset": 9233, "endOffset": 9302, "count": 3}, {"startOffset": 9367, "endOffset": 9436, "count": 0}], "isBlockCoverage": true}, {"functionName": "isAccepted", "ranges": [{"startOffset": 8634, "endOffset": 9038, "count": 31}, {"startOffset": 8696, "endOffset": 8710, "count": 30}, {"startOffset": 8712, "endOffset": 8724, "count": 2}, {"startOffset": 8724, "endOffset": 8801, "count": 29}, {"startOffset": 8801, "endOffset": 8970, "count": 2}, {"startOffset": 8851, "endOffset": 8898, "count": 1}, {"startOffset": 8970, "endOffset": 9037, "count": 27}], "isBlockCoverage": true}, {"functionName": "trimLeadingDashes", "ranges": [{"startOffset": 9546, "endOffset": 9608, "count": 230}], "isBlockCoverage": true}, {"functionName": "NodeEnvironmentFlagsSet", "ranges": [{"startOffset": 9918, "endOffset": 9994, "count": 1}], "isBlockCoverage": true}, {"functionName": "add", "ranges": [{"startOffset": 10000, "endOffset": 10069, "count": 0}], "isBlockCoverage": false}, {"functionName": "delete", "ranges": [{"startOffset": 10075, "endOffset": 10148, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 10154, "endOffset": 10206, "count": 0}], "isBlockCoverage": false}, {"functionName": "has", "ranges": [{"startOffset": 10212, "endOffset": 11027, "count": 0}], "isBlockCoverage": false}, {"functionName": "entries", "ranges": [{"startOffset": 11033, "endOffset": 11198, "count": 0}], "isBlockCoverage": false}, {"functionName": "for<PERSON>ach", "ranges": [{"startOffset": 11204, "endOffset": 11381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11313, "endOffset": 11365, "count": 230}], "isBlockCoverage": true}, {"functionName": "get size", "ranges": [{"startOffset": 11387, "endOffset": 11448, "count": 0}], "isBlockCoverage": false}, {"functionName": "values", "ranges": [{"startOffset": 11454, "endOffset": 11617, "count": 0}], "isBlockCoverage": false}, {"functionName": "toggleTraceCategoryState", "ranges": [{"startOffset": 12335, "endOffset": 12665, "count": 1}, {"startOffset": 12415, "endOffset": 12590, "count": 0}, {"startOffset": 12622, "endOffset": 12663, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "29", "url": "node:internal/fs/utils", "functions": [{"functionName": "assertEncoding", "ranges": [{"startOffset": 3557, "endOffset": 3757, "count": 16}, {"startOffset": 3608, "endOffset": 3639, "count": 8}, {"startOffset": 3641, "endOffset": 3755, "count": 0}], "isBlockCoverage": true}, {"functionName": "getOptions", "ranges": [{"startOffset": 7391, "endOffset": 8017, "count": 16}, {"startOffset": 7509, "endOffset": 7541, "count": 0}, {"startOffset": 7578, "endOffset": 7818, "count": 8}, {"startOffset": 7735, "endOffset": 7818, "count": 0}, {"startOffset": 7932, "endOffset": 7996, "count": 0}], "isBlockCoverage": true}, {"functionName": "stringToFlags", "ranges": [{"startOffset": 15001, "endOffset": 16257, "count": 8}, {"startOffset": 15082, "endOffset": 15137, "count": 0}, {"startOffset": 15160, "endOffset": 15186, "count": 0}, {"startOffset": 15243, "endOffset": 15254, "count": 0}, {"startOffset": 15276, "endOffset": 15313, "count": 0}, {"startOffset": 15318, "endOffset": 15344, "count": 0}, {"startOffset": 15349, "endOffset": 15361, "count": 0}, {"startOffset": 15383, "endOffset": 15419, "count": 0}, {"startOffset": 15425, "endOffset": 15472, "count": 0}, {"startOffset": 15477, "endOffset": 15488, "count": 0}, {"startOffset": 15510, "endOffset": 15567, "count": 0}, {"startOffset": 15573, "endOffset": 15619, "count": 0}, {"startOffset": 15624, "endOffset": 15635, "count": 0}, {"startOffset": 15657, "endOffset": 15712, "count": 0}, {"startOffset": 15718, "endOffset": 15766, "count": 0}, {"startOffset": 15771, "endOffset": 15782, "count": 0}, {"startOffset": 15804, "endOffset": 15862, "count": 0}, {"startOffset": 15867, "endOffset": 15878, "count": 0}, {"startOffset": 15900, "endOffset": 15958, "count": 0}, {"startOffset": 15964, "endOffset": 16011, "count": 0}, {"startOffset": 16016, "endOffset": 16027, "count": 0}, {"startOffset": 16049, "endOffset": 16105, "count": 0}, {"startOffset": 16110, "endOffset": 16121, "count": 0}, {"startOffset": 16143, "endOffset": 16199, "count": 0}, {"startOffset": 16203, "endOffset": 16256, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18284, "endOffset": 18996, "count": 17}, {"startOffset": 18346, "endOffset": 18368, "count": 0}, {"startOffset": 18370, "endOffset": 18477, "count": 0}, {"startOffset": 18668, "endOffset": 18688, "count": 0}, {"startOffset": 18758, "endOffset": 18826, "count": 0}, {"startOffset": 18845, "endOffset": 18995, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19041, "endOffset": 19176, "count": 9}], "isBlockCoverage": true}]}, {"scriptId": "30", "url": "node:internal/url", "functions": [{"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3494, "endOffset": 4283, "count": 5}], "isBlockCoverage": false}, {"functionName": "get hasPort", "ranges": [{"startOffset": 4062, "endOffset": 4127, "count": 0}], "isBlockCoverage": false}, {"functionName": "get hasSearch", "ranges": [{"startOffset": 4131, "endOffset": 4206, "count": 1}], "isBlockCoverage": true}, {"functionName": "get hasHash", "ranges": [{"startOffset": 4210, "endOffset": 4281, "count": 1}], "isBlockCoverage": true}, {"functionName": "isURL", "ranges": [{"startOffset": 21737, "endOffset": 21862, "count": 17}, {"startOffset": 21788, "endOffset": 21804, "count": 0}, {"startOffset": 21805, "endOffset": 21831, "count": 0}, {"startOffset": 21832, "endOffset": 21858, "count": 0}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 22189, "endOffset": 33222, "count": 5}], "isBlockCoverage": false}, {"functionName": "setURLSearchParamsModified", "ranges": [{"startOffset": 22319, "endOffset": 22807, "count": 0}], "isBlockCoverage": false}, {"functionName": "URL", "ranges": [{"startOffset": 22816, "endOffset": 23280, "count": 5}, {"startOffset": 22912, "endOffset": 22960, "count": 0}, {"startOffset": 23065, "endOffset": 23096, "count": 2}, {"startOffset": 23236, "endOffset": 23276, "count": 4}], "isBlockCoverage": true}, {"functionName": "parse", "ranges": [{"startOffset": 23291, "endOffset": 23533, "count": 1}, {"startOffset": 23356, "endOffset": 23404, "count": 0}, {"startOffset": 23504, "endOffset": 23521, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23537, "endOffset": 24257, "count": 0}], "isBlockCoverage": false}, {"functionName": "#getSearchFromContext", "ranges": [{"startOffset": 24261, "endOffset": 24595, "count": 0}], "isBlockCoverage": false}, {"functionName": "#getSearchFromParams", "ranges": [{"startOffset": 24599, "endOffset": 24710, "count": 0}], "isBlockCoverage": false}, {"functionName": "#ensureSearchParamsUpdated", "ranges": [{"startOffset": 24714, "endOffset": 25153, "count": 4}, {"startOffset": 24982, "endOffset": 25149, "count": 0}], "isBlockCoverage": true}, {"functionName": "#updateContext", "ranges": [{"startOffset": 25401, "endOffset": 27046, "count": 4}, {"startOffset": 25510, "endOffset": 25531, "count": 0}, {"startOffset": 25532, "endOffset": 25630, "count": 0}, {"startOffset": 26326, "endOffset": 27042, "count": 0}], "isBlockCoverage": true}, {"functionName": "toString", "ranges": [{"startOffset": 27050, "endOffset": 27235, "count": 0}], "isBlockCoverage": false}, {"functionName": "get href", "ranges": [{"startOffset": 27239, "endOffset": 27424, "count": 4}], "isBlockCoverage": true}, {"functionName": "set href", "ranges": [{"startOffset": 27428, "endOffset": 27647, "count": 0}], "isBlockCoverage": false}, {"functionName": "get origin", "ranges": [{"startOffset": 27665, "endOffset": 28534, "count": 0}], "isBlockCoverage": false}, {"functionName": "get protocol", "ranges": [{"startOffset": 28538, "endOffset": 28642, "count": 1}], "isBlockCoverage": true}, {"functionName": "set protocol", "ranges": [{"startOffset": 28646, "endOffset": 28819, "count": 0}], "isBlockCoverage": false}, {"functionName": "get username", "ranges": [{"startOffset": 28823, "endOffset": 29050, "count": 0}], "isBlockCoverage": false}, {"functionName": "set username", "ranges": [{"startOffset": 29054, "endOffset": 29227, "count": 0}], "isBlockCoverage": false}, {"functionName": "get password", "ranges": [{"startOffset": 29231, "endOffset": 29454, "count": 0}], "isBlockCoverage": false}, {"functionName": "set password", "ranges": [{"startOffset": 29458, "endOffset": 29631, "count": 0}], "isBlockCoverage": false}, {"functionName": "get host", "ranges": [{"startOffset": 29635, "endOffset": 30066, "count": 0}], "isBlockCoverage": false}, {"functionName": "set host", "ranges": [{"startOffset": 30070, "endOffset": 30235, "count": 0}], "isBlockCoverage": false}, {"functionName": "get hostname", "ranges": [{"startOffset": 30239, "endOffset": 30521, "count": 1}, {"startOffset": 30405, "endOffset": 30430, "count": 0}], "isBlockCoverage": true}, {"functionName": "set hostname", "ranges": [{"startOffset": 30525, "endOffset": 30698, "count": 0}], "isBlockCoverage": false}, {"functionName": "get port", "ranges": [{"startOffset": 30702, "endOffset": 30810, "count": 0}], "isBlockCoverage": false}, {"functionName": "set port", "ranges": [{"startOffset": 30814, "endOffset": 30979, "count": 0}], "isBlockCoverage": false}, {"functionName": "get pathname", "ranges": [{"startOffset": 30983, "endOffset": 31275, "count": 1}, {"startOffset": 31049, "endOffset": 31099, "count": 0}, {"startOffset": 31132, "endOffset": 31180, "count": 0}], "isBlockCoverage": true}, {"functionName": "set pathname", "ranges": [{"startOffset": 31279, "endOffset": 31452, "count": 0}], "isBlockCoverage": false}, {"functionName": "get search", "ranges": [{"startOffset": 31456, "endOffset": 31653, "count": 0}], "isBlockCoverage": false}, {"functionName": "set search", "ranges": [{"startOffset": 31657, "endOffset": 31861, "count": 0}], "isBlockCoverage": false}, {"functionName": "get searchParams", "ranges": [{"startOffset": 31879, "endOffset": 32237, "count": 0}], "isBlockCoverage": false}, {"functionName": "get hash", "ranges": [{"startOffset": 32241, "endOffset": 32456, "count": 0}], "isBlockCoverage": false}, {"functionName": "set hash", "ranges": [{"startOffset": 32460, "endOffset": 32625, "count": 0}], "isBlockCoverage": false}, {"functionName": "toJSON", "ranges": [{"startOffset": 32629, "endOffset": 32812, "count": 0}], "isBlockCoverage": false}, {"functionName": "canParse", "ranges": [{"startOffset": 32823, "endOffset": 33220, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPathFromURLPosix", "ranges": [{"startOffset": 42504, "endOffset": 43025, "count": 1}, {"startOffset": 42567, "endOffset": 42623, "count": 0}, {"startOffset": 42701, "endOffset": 42984, "count": 135}, {"startOffset": 42732, "endOffset": 42980, "count": 0}], "isBlockCoverage": true}, {"functionName": "fileURLToPath", "ranges": [{"startOffset": 43027, "endOffset": 43439, "count": 1}, {"startOffset": 43175, "endOffset": 43269, "count": 0}, {"startOffset": 43307, "endOffset": 43348, "count": 0}, {"startOffset": 43381, "endOffset": 43408, "count": 0}], "isBlockCoverage": true}, {"functionName": "encodePathChars", "ranges": [{"startOffset": 43992, "endOffset": 46009, "count": 1}, {"startOffset": 44099, "endOffset": 44180, "count": 0}, {"startOffset": 44229, "endOffset": 44306, "count": 0}, {"startOffset": 44354, "endOffset": 44435, "count": 0}, {"startOffset": 44483, "endOffset": 44571, "count": 0}, {"startOffset": 44618, "endOffset": 44697, "count": 0}, {"startOffset": 44744, "endOffset": 44823, "count": 0}, {"startOffset": 44870, "endOffset": 44948, "count": 0}, {"startOffset": 44995, "endOffset": 45081, "count": 0}, {"startOffset": 45128, "endOffset": 45219, "count": 0}, {"startOffset": 45381, "endOffset": 45464, "count": 0}, {"startOffset": 45511, "endOffset": 45603, "count": 0}, {"startOffset": 45650, "endOffset": 45729, "count": 0}, {"startOffset": 45776, "endOffset": 45861, "count": 0}, {"startOffset": 45908, "endOffset": 45987, "count": 0}], "isBlockCoverage": true}, {"functionName": "pathToFileURL", "ranges": [{"startOffset": 46011, "endOffset": 47870, "count": 1}, {"startOffset": 46133, "endOffset": 46179, "count": 0}, {"startOffset": 46181, "endOffset": 47319, "count": 0}, {"startOffset": 47345, "endOffset": 47375, "count": 0}, {"startOffset": 47668, "endOffset": 47680, "count": 0}, {"startOffset": 47682, "endOffset": 47721, "count": 0}, {"startOffset": 47724, "endOffset": 47775, "count": 0}, {"startOffset": 47781, "endOffset": 47797, "count": 0}], "isBlockCoverage": true}, {"functionName": "toPathIfFileURL", "ranges": [{"startOffset": 47872, "endOffset": 48009, "count": 17}, {"startOffset": 47968, "endOffset": 48008, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "33", "url": "node:path", "functions": [{"functionName": "isPosixPathSeparator", "ranges": [{"startOffset": 2158, "endOffset": 2235, "count": 8343}], "isBlockCoverage": true}, {"functionName": "normalizeString", "ranges": [{"startOffset": 2470, "endOffset": 4418, "count": 75}, {"startOffset": 2687, "endOffset": 4402, "count": 8336}, {"startOffset": 2720, "endOffset": 2762, "count": 8261}, {"startOffset": 2762, "endOffset": 2853, "count": 75}, {"startOffset": 2805, "endOffset": 2811, "count": 68}, {"startOffset": 2811, "endOffset": 2853, "count": 7}, {"startOffset": 2853, "endOffset": 2886, "count": 8268}, {"startOffset": 2886, "endOffset": 4303, "count": 987}, {"startOffset": 2918, "endOffset": 2931, "count": 895}, {"startOffset": 2933, "endOffset": 2958, "count": 96}, {"startOffset": 2958, "endOffset": 4260, "count": 891}, {"startOffset": 2980, "endOffset": 4015, "count": 5}, {"startOffset": 3009, "endOffset": 3035, "count": 4}, {"startOffset": 3036, "endOffset": 3110, "count": 0}, {"startOffset": 3111, "endOffset": 3185, "count": 0}, {"startOffset": 3219, "endOffset": 3696, "count": 4}, {"startOffset": 3339, "endOffset": 3415, "count": 0}, {"startOffset": 3696, "endOffset": 3865, "count": 1}, {"startOffset": 3724, "endOffset": 3865, "count": 0}, {"startOffset": 3875, "endOffset": 4007, "count": 1}, {"startOffset": 3938, "endOffset": 3956, "count": 0}, {"startOffset": 4015, "endOffset": 4260, "count": 886}, {"startOffset": 4061, "endOffset": 4130, "count": 812}, {"startOffset": 4130, "endOffset": 4205, "count": 74}, {"startOffset": 4260, "endOffset": 4303, "count": 983}, {"startOffset": 4303, "endOffset": 4398, "count": 7281}, {"startOffset": 4331, "endOffset": 4345, "count": 214}, {"startOffset": 4347, "endOffset": 4368, "count": 31}, {"startOffset": 4368, "endOffset": 4398, "count": 7250}], "isBlockCoverage": true}, {"functionName": "resolve", "ranges": [{"startOffset": 36750, "endOffset": 37854, "count": 68}, {"startOffset": 36872, "endOffset": 36892, "count": 120}, {"startOffset": 36899, "endOffset": 37204, "count": 117}, {"startOffset": 37030, "endOffset": 37057, "count": 1}, {"startOffset": 37057, "endOffset": 37204, "count": 116}, {"startOffset": 37233, "endOffset": 37408, "count": 0}, {"startOffset": 37793, "endOffset": 37843, "count": 0}, {"startOffset": 37844, "endOffset": 37849, "count": 0}], "isBlockCoverage": true}, {"functionName": "normalize", "ranges": [{"startOffset": 37920, "endOffset": 38533, "count": 7}, {"startOffset": 38006, "endOffset": 38017, "count": 0}, {"startOffset": 38345, "endOffset": 38439, "count": 0}, {"startOffset": 38473, "endOffset": 38485, "count": 0}, {"startOffset": 38509, "endOffset": 38521, "count": 4}, {"startOffset": 38522, "endOffset": 38528, "count": 3}], "isBlockCoverage": true}, {"functionName": "isAbsolute", "ranges": [{"startOffset": 38600, "endOffset": 38756, "count": 11}], "isBlockCoverage": true}, {"functionName": "join", "ranges": [{"startOffset": 38825, "endOffset": 39189, "count": 4}, {"startOffset": 38874, "endOffset": 38885, "count": 0}, {"startOffset": 38950, "endOffset": 39079, "count": 8}, {"startOffset": 39114, "endOffset": 39125, "count": 0}], "isBlockCoverage": true}, {"functionName": "toNamespacedPath", "ranges": [{"startOffset": 41705, "endOffset": 41781, "count": 98}], "isBlockCoverage": true}, {"functionName": "dirname", "ranges": [{"startOffset": 41847, "endOffset": 42523, "count": 23}, {"startOffset": 41930, "endOffset": 41941, "count": 0}, {"startOffset": 42115, "endOffset": 42368, "count": 391}, {"startOffset": 42186, "endOffset": 42270, "count": 23}, {"startOffset": 42270, "endOffset": 42362, "count": 368}, {"startOffset": 42396, "endOffset": 42423, "count": 0}, {"startOffset": 42460, "endOffset": 42472, "count": 0}], "isBlockCoverage": true}, {"functionName": "basename", "ranges": [{"startOffset": 42619, "endOffset": 45049, "count": 8}, {"startOffset": 42680, "endOffset": 42713, "count": 0}, {"startOffset": 42845, "endOffset": 42865, "count": 0}, {"startOffset": 42866, "endOffset": 42897, "count": 0}, {"startOffset": 42899, "endOffset": 44423, "count": 0}, {"startOffset": 44471, "endOffset": 44956, "count": 148}, {"startOffset": 44542, "endOffset": 44766, "count": 8}, {"startOffset": 44766, "endOffset": 44950, "count": 140}, {"startOffset": 44788, "endOffset": 44950, "count": 8}, {"startOffset": 44984, "endOffset": 44994, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "35", "url": "node:internal/process/task_queues", "functions": [{"functionName": "setHasTickScheduled", "ranges": [{"startOffset": 1059, "endOffset": 1145, "count": 5}, {"startOffset": 1135, "endOffset": 1138, "count": 3}, {"startOffset": 1139, "endOffset": 1142, "count": 2}], "isBlockCoverage": true}, {"functionName": "processTicksAndRejections", "ranges": [{"startOffset": 1445, "endOffset": 2424, "count": 2}, {"startOffset": 1501, "endOffset": 2304, "count": 3}, {"startOffset": 1748, "endOffset": 1781, "count": 1}, {"startOffset": 1781, "endOffset": 2155, "count": 2}, {"startOffset": 1914, "endOffset": 1956, "count": 0}, {"startOffset": 1969, "endOffset": 2020, "count": 0}, {"startOffset": 2033, "endOffset": 2093, "count": 0}, {"startOffset": 2106, "endOffset": 2133, "count": 0}, {"startOffset": 2217, "endOffset": 2238, "count": 0}], "isBlockCoverage": true}, {"functionName": "nextTick", "ranges": [{"startOffset": 2568, "endOffset": 3442, "count": 3}, {"startOffset": 2669, "endOffset": 2676, "count": 0}, {"startOffset": 2724, "endOffset": 2738, "count": 1}, {"startOffset": 2743, "endOffset": 2780, "count": 2}, {"startOffset": 2785, "endOffset": 2836, "count": 0}, {"startOffset": 2841, "endOffset": 2906, "count": 0}, {"startOffset": 2911, "endOffset": 3054, "count": 0}, {"startOffset": 3354, "endOffset": 3414, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "36", "url": "node:internal/process/promises", "functions": [{"functionName": "", "ranges": [{"startOffset": 1267, "endOffset": 1379, "count": 4}], "isBlockCoverage": false}, {"functionName": "setHasRejectionToWarn", "ranges": [{"startOffset": 4097, "endOffset": 4187, "count": 2}, {"startOffset": 4177, "endOffset": 4180, "count": 0}], "isBlockCoverage": true}, {"functionName": "processPromiseRejections", "ranges": [{"startOffset": 12037, "endOffset": 13386, "count": 2}, {"startOffset": 12197, "endOffset": 12376, "count": 0}, {"startOffset": 12582, "endOffset": 13292, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "37", "url": "node:internal/fixed_queue", "functions": [{"functionName": "isEmpty", "ranges": [{"startOffset": 3130, "endOffset": 3182, "count": 12}], "isBlockCoverage": true}, {"functionName": "isFull", "ranges": [{"startOffset": 3186, "endOffset": 3253, "count": 3}], "isBlockCoverage": true}, {"functionName": "push", "ranges": [{"startOffset": 3257, "endOffset": 3344, "count": 3}], "isBlockCoverage": true}, {"functionName": "shift", "ranges": [{"startOffset": 3348, "endOffset": 3563, "count": 6}, {"startOffset": 3441, "endOffset": 3562, "count": 3}], "isBlockCoverage": true}, {"functionName": "isEmpty", "ranges": [{"startOffset": 3683, "endOffset": 3730, "count": 6}], "isBlockCoverage": true}, {"functionName": "push", "ranges": [{"startOffset": 3734, "endOffset": 3998, "count": 3}, {"startOffset": 3775, "endOffset": 3968, "count": 0}], "isBlockCoverage": true}, {"functionName": "shift", "ranges": [{"startOffset": 4002, "endOffset": 4257, "count": 6}, {"startOffset": 4117, "endOffset": 4236, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "40", "url": "node:internal/process/execution", "functions": [{"functionName": "tryGetCwd", "ranges": [{"startOffset": 945, "endOffset": 1275, "count": 1}, {"startOffset": 1006, "endOffset": 1273, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "43", "url": "node:internal/source_map/source_map_cache", "functions": [{"functionName": "", "ranges": [{"startOffset": 676, "endOffset": 804, "count": 1}], "isBlockCoverage": true}, {"functionName": "getSourceMapsEnabled", "ranges": [{"startOffset": 1579, "endOffset": 1642, "count": 1}], "isBlockCoverage": true}, {"functionName": "setSourceMapsEnabled", "ranges": [{"startOffset": 1729, "endOffset": 2313, "count": 1}, {"startOffset": 1837, "endOffset": 2020, "count": 0}], "isBlockCoverage": true}, {"functionName": "extractSourceURLMagicComment", "ranges": [{"startOffset": 2631, "endOffset": 3231, "count": 1}, {"startOffset": 2933, "endOffset": 2966, "count": 0}, {"startOffset": 3019, "endOffset": 3155, "count": 0}, {"startOffset": 3157, "endOffset": 3230, "count": 0}], "isBlockCoverage": true}, {"functionName": "maybeCacheSourceMap", "ranges": [{"startOffset": 4659, "endOffset": 6205, "count": 1}, {"startOffset": 4858, "endOffset": 4878, "count": 0}, {"startOffset": 4881, "endOffset": 4888, "count": 0}, {"startOffset": 5036, "endOffset": 5132, "count": 0}, {"startOffset": 5168, "endOffset": 5234, "count": 0}, {"startOffset": 5323, "endOffset": 5340, "count": 0}, {"startOffset": 5785, "endOffset": 5799, "count": 0}, {"startOffset": 5847, "endOffset": 5994, "count": 0}, {"startOffset": 6104, "endOffset": 6127, "count": 0}], "isBlockCoverage": true}, {"functionName": "dataFromUrl", "ranges": [{"startOffset": 7259, "endOffset": 7680, "count": 1}, {"startOffset": 7373, "endOffset": 7581, "count": 0}], "isBlockCoverage": true}, {"functionName": "lineLengths", "ranges": [{"startOffset": 7870, "endOffset": 8666, "count": 1}, {"startOffset": 8041, "endOffset": 8605, "count": 4364}, {"startOffset": 8417, "endOffset": 8440, "count": 4278}, {"startOffset": 8441, "endOffset": 8464, "count": 4278}, {"startOffset": 8466, "endOffset": 8601, "count": 86}], "isBlockCoverage": true}, {"functionName": "sourceMapFromFile", "ranges": [{"startOffset": 8818, "endOffset": 9098, "count": 1}, {"startOffset": 9046, "endOffset": 9096, "count": 0}], "isBlockCoverage": true}, {"functionName": "sourcesToAbsolute", "ranges": [{"startOffset": 10135, "endOffset": 10539, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10213, "endOffset": 10396, "count": 1}, {"startOffset": 10302, "endOffset": 10350, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "44", "url": "node:internal/modules/helpers", "functions": [{"functionName": "", "ranges": [{"startOffset": 1182, "endOffset": 1207, "count": 1}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1676, "endOffset": 1808, "count": 8}], "isBlockCoverage": true}, {"functionName": "initializeCjsConditions", "ranges": [{"startOffset": 1924, "endOffset": 2415, "count": 1}, {"startOffset": 2103, "endOffset": 2107, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadBuiltinModule", "ranges": [{"startOffset": 2836, "endOffset": 3216, "count": 11}, {"startOffset": 2925, "endOffset": 2942, "count": 0}], "isBlockCoverage": true}, {"functionName": "lazyModule", "ranges": [{"startOffset": 3309, "endOffset": 3422, "count": 5}, {"startOffset": 3353, "endOffset": 3401, "count": 1}], "isBlockCoverage": true}, {"functionName": "makeRequireFunction", "ranges": [{"startOffset": 4011, "endOffset": 6543, "count": 5}, {"startOffset": 4150, "endOffset": 4213, "count": 0}, {"startOffset": 4280, "endOffset": 5551, "count": 0}], "isBlockCoverage": true}, {"functionName": "require", "ranges": [{"startOffset": 4423, "endOffset": 5546, "count": 0}], "isBlockCoverage": false}, {"functionName": "require", "ranges": [{"startOffset": 5573, "endOffset": 5713, "count": 18}], "isBlockCoverage": true}, {"functionName": "resolve", "ranges": [{"startOffset": 5899, "endOffset": 6045, "count": 0}], "isBlockCoverage": false}, {"functionName": "paths", "ranges": [{"startOffset": 6190, "endOffset": 6312, "count": 0}], "isBlockCoverage": false}, {"functionName": "stripBOM", "ranges": [{"startOffset": 6755, "endOffset": 6910, "count": 2}, {"startOffset": 6837, "endOffset": 6890, "count": 0}], "isBlockCoverage": true}, {"functionName": "normalizeReferrerURL", "ranges": [{"startOffset": 9391, "endOffset": 9894, "count": 1}, {"startOffset": 9496, "endOffset": 9523, "count": 0}, {"startOffset": 9660, "endOffset": 9757, "count": 0}, {"startOffset": 9759, "endOffset": 9893, "count": 0}], "isBlockCoverage": true}, {"functionName": "hasStartedUserCJSExecution", "ranges": [{"startOffset": 11438, "endOffset": 11512, "count": 1}], "isBlockCoverage": true}, {"functionName": "setHasStartedUserCJSExecution", "ranges": [{"startOffset": 11516, "endOffset": 11593, "count": 5}], "isBlockCoverage": true}, {"functionName": "hasStartedUserESMExecution", "ranges": [{"startOffset": 11597, "endOffset": 11671, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "45", "url": "node:fs", "functions": [{"functionName": "makeCallback", "ranges": [{"startOffset": 4712, "endOffset": 4824, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4780, "endOffset": 4821, "count": 1}], "isBlockCoverage": true}, {"functionName": "isFileType", "ranges": [{"startOffset": 5197, "endOffset": 5457, "count": 172}, {"startOffset": 5396, "endOffset": 5416, "count": 28}], "isBlockCoverage": true}, {"functionName": "readFileSync", "ranges": [{"startOffset": 11296, "endOffset": 12935, "count": 8}, {"startOffset": 11418, "endOffset": 11449, "count": 0}, {"startOffset": 11622, "endOffset": 11712, "count": 0}, {"startOffset": 11713, "endOffset": 11753, "count": 0}, {"startOffset": 11841, "endOffset": 11851, "count": 0}, {"startOffset": 11852, "endOffset": 11855, "count": 0}, {"startOffset": 11985, "endOffset": 12934, "count": 0}], "isBlockCoverage": true}, {"functionName": "mkdir", "ranges": [{"startOffset": 33635, "endOffset": 34350, "count": 1}, {"startOffset": 33788, "endOffset": 34048, "count": 0}], "isBlockCoverage": true}, {"functionName": "splitRoot", "ranges": [{"startOffset": 69496, "endOffset": 69706, "count": 8}, {"startOffset": 69563, "endOffset": 69686, "count": 16}, {"startOffset": 69641, "endOffset": 69680, "count": 8}, {"startOffset": 69686, "endOffset": 69705, "count": 0}], "isBlockCoverage": true}, {"functionName": "encodeRealpathResult", "ranges": [{"startOffset": 69711, "endOffset": 69998, "count": 7}, {"startOffset": 69796, "endOffset": 69826, "count": 0}, {"startOffset": 69846, "endOffset": 69997, "count": 0}], "isBlockCoverage": true}, {"functionName": "nextPart", "ranges": [{"startOffset": 70404, "endOffset": 70479, "count": 100}], "isBlockCoverage": true}, {"functionName": "realpathSync", "ranges": [{"startOffset": 70653, "endOffset": 74472, "count": 8}, {"startOffset": 70777, "endOffset": 70795, "count": 0}, {"startOffset": 70956, "endOffset": 70991, "count": 1}, {"startOffset": 70991, "endOffset": 71553, "count": 7}, {"startOffset": 71553, "endOffset": 71743, "count": 0}, {"startOffset": 71743, "endOffset": 71880, "count": 7}, {"startOffset": 71880, "endOffset": 74399, "count": 100}, {"startOffset": 71992, "endOffset": 72123, "count": 8}, {"startOffset": 72123, "endOffset": 72281, "count": 92}, {"startOffset": 72368, "endOffset": 72396, "count": 89}, {"startOffset": 72398, "endOffset": 72535, "count": 72}, {"startOffset": 72489, "endOffset": 72513, "count": 0}, {"startOffset": 72535, "endOffset": 72601, "count": 28}, {"startOffset": 72638, "endOffset": 72687, "count": 0}, {"startOffset": 72687, "endOffset": 73876, "count": 28}, {"startOffset": 72983, "endOffset": 73008, "count": 0}, {"startOffset": 73049, "endOffset": 73137, "count": 27}, {"startOffset": 73137, "endOffset": 73558, "count": 1}, {"startOffset": 73497, "endOffset": 73550, "count": 0}, {"startOffset": 73558, "endOffset": 73796, "count": 1}, {"startOffset": 73840, "endOffset": 73870, "count": 1}, {"startOffset": 73876, "endOffset": 74168, "count": 1}, {"startOffset": 74168, "endOffset": 74191, "count": 0}, {"startOffset": 74193, "endOffset": 74395, "count": 0}, {"startOffset": 74399, "endOffset": 74413, "count": 7}], "isBlockCoverage": true}]}, {"scriptId": "52", "url": "node:internal/process/permission", "functions": [{"functionName": "isEnabled", "ranges": [{"startOffset": 332, "endOffset": 576, "count": 38}, {"startOffset": 392, "endOffset": 537, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "55", "url": "node:internal/console/constructor", "functions": [{"functionName": "value", "ranges": [{"startOffset": 5763, "endOffset": 6408, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 5972, "endOffset": 6067, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6079, "endOffset": 6109, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6239, "endOffset": 6338, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6350, "endOffset": 6380, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeGlobalConsole", "ranges": [{"startOffset": 20356, "endOffset": 21346, "count": 1}, {"startOffset": 20675, "endOffset": 21345, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21241, "endOffset": 21342, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "57", "url": "node:internal/event_target", "functions": [{"functionName": "defineEventHandler", "ranges": [{"startOffset": 29893, "endOffset": 31359, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 30053, "endOffset": 30189, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 30288, "endOffset": 31126, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "62", "url": "node:internal/v8/startup_snapshot", "functions": [{"functionName": "isBuildingSnapshot", "ranges": [{"startOffset": 433, "endOffset": 504, "count": 5}], "isBlockCoverage": false}, {"functionName": "runDeserializeCallbacks", "ranges": [{"startOffset": 831, "endOffset": 1004, "count": 1}, {"startOffset": 910, "endOffset": 1002, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "63", "url": "node:internal/process/signal", "functions": [{"functionName": "isSignal", "ranges": [{"startOffset": 238, "endOffset": 334, "count": 4}], "isBlockCoverage": false}, {"functionName": "startListeningIfSignal", "ranges": [{"startOffset": 398, "endOffset": 908, "count": 4}], "isBlockCoverage": false}]}, {"scriptId": "65", "url": "node:internal/modules/cjs/loader", "functions": [{"functionName": "", "ranges": [{"startOffset": 4692, "endOffset": 4783, "count": 1}, {"startOffset": 4739, "endOffset": 4775, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4832, "endOffset": 4875, "count": 1}], "isBlockCoverage": true}, {"functionName": "stat", "ranges": [{"startOffset": 6287, "endOffset": 6701, "count": 53}, {"startOffset": 6385, "endOffset": 6483, "count": 26}, {"startOffset": 6461, "endOffset": 6479, "count": 3}, {"startOffset": 6483, "endOffset": 6556, "count": 50}, {"startOffset": 6556, "endOffset": 6570, "count": 23}, {"startOffset": 6572, "endOffset": 6682, "count": 9}, {"startOffset": 6682, "endOffset": 6700, "count": 50}], "isBlockCoverage": true}, {"functionName": "update<PERSON><PERSON><PERSON>n", "ranges": [{"startOffset": 7192, "endOffset": 7394, "count": 7}, {"startOffset": 7264, "endOffset": 7274, "count": 6}, {"startOffset": 7291, "endOffset": 7344, "count": 6}, {"startOffset": 7301, "endOffset": 7343, "count": 0}, {"startOffset": 7346, "endOffset": 7392, "count": 6}], "isBlockCoverage": true}, {"functionName": "reportModuleToWatchMode", "ranges": [{"startOffset": 7511, "endOffset": 7667, "count": 25}, {"startOffset": 7592, "endOffset": 7607, "count": 0}, {"startOffset": 7609, "endOffset": 7665, "count": 0}], "isBlockCoverage": true}, {"functionName": "reportModuleNotFoundToWatchMode", "ranges": [{"startOffset": 7851, "endOffset": 8090, "count": 3}, {"startOffset": 7952, "endOffset": 7967, "count": 0}, {"startOffset": 7969, "endOffset": 8088, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8037, "endOffset": 8079, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 8181, "endOffset": 8931, "count": 7}, {"startOffset": 8487, "endOffset": 8497, "count": 0}, {"startOffset": 8515, "endOffset": 8879, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10914, "endOffset": 10939, "count": 1}], "isBlockCoverage": true}, {"functionName": "initializeCJS", "ranges": [{"startOffset": 11659, "endOffset": 12270, "count": 1}], "isBlockCoverage": true}, {"functionName": "tryPackage", "ranges": [{"startOffset": 13154, "endOffset": 14433, "count": 2}, {"startOffset": 13276, "endOffset": 13357, "count": 0}, {"startOffset": 13451, "endOffset": 13495, "count": 0}, {"startOffset": 13496, "endOffset": 13563, "count": 0}, {"startOffset": 13589, "endOffset": 14414, "count": 0}], "isBlockCoverage": true}, {"functionName": "tryFile", "ranges": [{"startOffset": 14794, "endOffset": 15057, "count": 18}, {"startOffset": 14883, "endOffset": 14894, "count": 15}, {"startOffset": 14894, "endOffset": 14923, "count": 3}, {"startOffset": 14923, "endOffset": 14951, "count": 0}, {"startOffset": 14952, "endOffset": 14975, "count": 3}, {"startOffset": 14978, "endOffset": 15021, "count": 0}, {"startOffset": 15021, "endOffset": 15056, "count": 3}], "isBlockCoverage": true}, {"functionName": "tryExtensions", "ranges": [{"startOffset": 15322, "endOffset": 15542, "count": 6}, {"startOffset": 15411, "endOffset": 15524, "count": 16}, {"startOffset": 15490, "endOffset": 15520, "count": 1}, {"startOffset": 15524, "endOffset": 15541, "count": 5}], "isBlockCoverage": true}, {"functionName": "findLongestRegisteredExtension", "ranges": [{"startOffset": 15726, "endOffset": 16202, "count": 7}, {"startOffset": 15950, "endOffset": 16184, "count": 13}, {"startOffset": 16001, "endOffset": 16014, "count": 1}, {"startOffset": 16014, "endOffset": 16152, "count": 12}, {"startOffset": 16152, "endOffset": 16180, "count": 7}, {"startOffset": 16184, "endOffset": 16201, "count": 0}], "isBlockCoverage": true}, {"functionName": "trySelf<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 16325, "endOffset": 16618, "count": 8}, {"startOffset": 16377, "endOffset": 16394, "count": 1}, {"startOffset": 16394, "endOffset": 16452, "count": 7}, {"startOffset": 16452, "endOffset": 16616, "count": 0}], "isBlockCoverage": true}, {"functionName": "trySelf", "ranges": [{"startOffset": 16826, "endOffset": 17754, "count": 8}, {"startOffset": 16885, "endOffset": 16902, "count": 1}, {"startOffset": 16902, "endOffset": 17024, "count": 7}, {"startOffset": 17025, "endOffset": 17050, "count": 0}, {"startOffset": 17052, "endOffset": 17075, "count": 7}, {"startOffset": 17075, "endOffset": 17752, "count": 0}], "isBlockCoverage": true}, {"functionName": "resolveExports", "ranges": [{"startOffset": 18102, "endOffset": 18920, "count": 8}, {"startOffset": 18309, "endOffset": 18324, "count": 3}, {"startOffset": 18339, "endOffset": 18350, "count": 3}, {"startOffset": 18350, "endOffset": 18451, "count": 5}, {"startOffset": 18451, "endOffset": 18473, "count": 2}, {"startOffset": 18475, "endOffset": 18918, "count": 0}], "isBlockCoverage": true}, {"functionName": "getDefaultExtensions", "ranges": [{"startOffset": 18982, "endOffset": 19477, "count": 4}, {"startOffset": 19125, "endOffset": 19153, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19401, "endOffset": 19473, "count": 16}, {"startOffset": 19425, "endOffset": 19473, "count": 4}], "isBlockCoverage": true}, {"functionName": "Module._findPath", "ranges": [{"startOffset": 19770, "endOffset": 23329, "count": 9}, {"startOffset": 19880, "endOffset": 19903, "count": 3}, {"startOffset": 19903, "endOffset": 19966, "count": 6}, {"startOffset": 19943, "endOffset": 19966, "count": 0}, {"startOffset": 20099, "endOffset": 20122, "count": 1}, {"startOffset": 20122, "endOffset": 20705, "count": 8}, {"startOffset": 20344, "endOffset": 20698, "count": 0}, {"startOffset": 20760, "endOffset": 20914, "count": 3}, {"startOffset": 20877, "endOffset": 20910, "count": 1}, {"startOffset": 20914, "endOffset": 20976, "count": 8}, {"startOffset": 20976, "endOffset": 23310, "count": 28}, {"startOffset": 21117, "endOffset": 21127, "count": 27}, {"startOffset": 21128, "endOffset": 21224, "count": 25}, {"startOffset": 21162, "endOffset": 21200, "count": 0}, {"startOffset": 21231, "endOffset": 21254, "count": 18}, {"startOffset": 21254, "endOffset": 21282, "count": 10}, {"startOffset": 21282, "endOffset": 21422, "count": 8}, {"startOffset": 21375, "endOffset": 21416, "count": 0}, {"startOffset": 21422, "endOffset": 22785, "count": 10}, {"startOffset": 21574, "endOffset": 22558, "count": 4}, {"startOffset": 21607, "endOffset": 21796, "count": 3}, {"startOffset": 21662, "endOffset": 21722, "count": 0}, {"startOffset": 21796, "endOffset": 22550, "count": 1}, {"startOffset": 21850, "endOffset": 22490, "count": 0}, {"startOffset": 22581, "endOffset": 22779, "count": 6}, {"startOffset": 22661, "endOffset": 22713, "count": 4}, {"startOffset": 22785, "endOffset": 22805, "count": 10}, {"startOffset": 22805, "endOffset": 22816, "count": 5}, {"startOffset": 22818, "endOffset": 23036, "count": 2}, {"startOffset": 22920, "endOffset": 22968, "count": 0}, {"startOffset": 23036, "endOffset": 23056, "count": 10}, {"startOffset": 23056, "endOffset": 23132, "count": 7}, {"startOffset": 23132, "endOffset": 23310, "count": 3}, {"startOffset": 23310, "endOffset": 23328, "count": 1}], "isBlockCoverage": true}, {"functionName": "Module._nodeModulePaths", "ranges": [{"startOffset": 25406, "endOffset": 26594, "count": 7}, {"startOffset": 25678, "endOffset": 25717, "count": 0}, {"startOffset": 26038, "endOffset": 26472, "count": 708}, {"startOffset": 26134, "endOffset": 26342, "count": 80}, {"startOffset": 26161, "endOffset": 26301, "count": 74}, {"startOffset": 26342, "endOffset": 26466, "count": 628}, {"startOffset": 26362, "endOffset": 26466, "count": 174}, {"startOffset": 26397, "endOffset": 26423, "count": 100}, {"startOffset": 26423, "endOffset": 26458, "count": 74}], "isBlockCoverage": true}, {"functionName": "Module._resolveLookupPaths", "ranges": [{"startOffset": 26729, "endOffset": 28016, "count": 8}, {"startOffset": 26809, "endOffset": 26875, "count": 0}, {"startOffset": 26960, "endOffset": 27159, "count": 3}, {"startOffset": 27040, "endOffset": 27090, "count": 2}, {"startOffset": 27091, "endOffset": 27158, "count": 0}, {"startOffset": 27161, "endOffset": 27488, "count": 5}, {"startOffset": 27221, "endOffset": 27236, "count": 4}, {"startOffset": 27238, "endOffset": 27348, "count": 4}, {"startOffset": 27348, "endOffset": 27388, "count": 1}, {"startOffset": 27477, "endOffset": 27483, "count": 0}, {"startOffset": 27488, "endOffset": 27556, "count": 3}, {"startOffset": 27557, "endOffset": 27576, "count": 3}, {"startOffset": 27578, "endOffset": 27827, "count": 0}, {"startOffset": 27827, "endOffset": 28015, "count": 3}], "isBlockCoverage": true}, {"functionName": "Module._load", "ranges": [{"startOffset": 31138, "endOffset": 35016, "count": 19}, {"startOffset": 31221, "endOffset": 32049, "count": 18}, {"startOffset": 31680, "endOffset": 32045, "count": 0}, {"startOffset": 32102, "endOffset": 32374, "count": 0}, {"startOffset": 32527, "endOffset": 33453, "count": 0}, {"startOffset": 33453, "endOffset": 33513, "count": 18}, {"startOffset": 33513, "endOffset": 33596, "count": 11}, {"startOffset": 33596, "endOffset": 33726, "count": 7}, {"startOffset": 33750, "endOffset": 34127, "count": 7}, {"startOffset": 33768, "endOffset": 33953, "count": 1}, {"startOffset": 33953, "endOffset": 34003, "count": 6}, {"startOffset": 34127, "endOffset": 34988, "count": 7}, {"startOffset": 34331, "endOffset": 34734, "count": 0}, {"startOffset": 34916, "endOffset": 34984, "count": 0}, {"startOffset": 34988, "endOffset": 35015, "count": 7}], "isBlockCoverage": true}, {"functionName": "Module._resolveFilename", "ranges": [{"startOffset": 35478, "endOffset": 38364, "count": 19}, {"startOffset": 35575, "endOffset": 35600, "count": 11}, {"startOffset": 35600, "endOffset": 35650, "count": 8}, {"startOffset": 35650, "endOffset": 35669, "count": 0}, {"startOffset": 35671, "endOffset": 36522, "count": 0}, {"startOffset": 36522, "endOffset": 36617, "count": 8}, {"startOffset": 36617, "endOffset": 36665, "count": 0}, {"startOffset": 36667, "endOffset": 37309, "count": 0}, {"startOffset": 37309, "endOffset": 37470, "count": 8}, {"startOffset": 37470, "endOffset": 37666, "count": 0}, {"startOffset": 37666, "endOffset": 37806, "count": 8}, {"startOffset": 37806, "endOffset": 37826, "count": 7}, {"startOffset": 37826, "endOffset": 37930, "count": 1}, {"startOffset": 37930, "endOffset": 38003, "count": 2}, {"startOffset": 37985, "endOffset": 37997, "count": 0}, {"startOffset": 38003, "endOffset": 38363, "count": 1}], "isBlockCoverage": true}, {"functionName": "Module.load", "ranges": [{"startOffset": 40021, "endOffset": 40688, "count": 7}, {"startOffset": 40356, "endOffset": 40386, "count": 0}, {"startOffset": 40388, "endOffset": 40440, "count": 0}], "isBlockCoverage": true}, {"functionName": "Module.require", "ranges": [{"startOffset": 40975, "endOffset": 41274, "count": 18}, {"startOffset": 41035, "endOffset": 41153, "count": 0}], "isBlockCoverage": true}, {"functionName": "wrapSafe", "ranges": [{"startOffset": 46694, "endOffset": 48701, "count": 5}, {"startOffset": 46987, "endOffset": 47859, "count": 0}, {"startOffset": 47924, "endOffset": 48382, "count": 3}, {"startOffset": 47970, "endOffset": 48132, "count": 0}, {"startOffset": 48574, "endOffset": 48681, "count": 1}], "isBlockCoverage": true}, {"functionName": "Module._compile", "ranges": [{"startOffset": 49102, "endOffset": 50633, "count": 5}, {"startOffset": 49201, "endOffset": 49211, "count": 0}, {"startOffset": 49229, "endOffset": 49382, "count": 0}, {"startOffset": 49567, "endOffset": 49599, "count": 0}, {"startOffset": 49632, "endOffset": 49805, "count": 0}, {"startOffset": 50029, "endOffset": 50059, "count": 1}, {"startOffset": 50150, "endOffset": 50184, "count": 1}, {"startOffset": 50186, "endOffset": 50397, "count": 0}, {"startOffset": 50593, "endOffset": 50614, "count": 1}], "isBlockCoverage": true}, {"functionName": "getMaybeCachedSource", "ranges": [{"startOffset": 50887, "endOffset": 51292, "count": 5}, {"startOffset": 51050, "endOffset": 51125, "count": 0}], "isBlockCoverage": true}, {"functionName": "Module._extensions..js", "ranges": [{"startOffset": 51471, "endOffset": 53565, "count": 5}, {"startOffset": 51745, "endOffset": 51767, "count": 0}, {"startOffset": 51863, "endOffset": 53354, "count": 0}, {"startOffset": 53395, "endOffset": 53429, "count": 2}, {"startOffset": 53433, "endOffset": 53516, "count": 0}], "isBlockCoverage": true}, {"functionName": "Module._extensions..json", "ranges": [{"startOffset": 53747, "endOffset": 54153, "count": 2}, {"startOffset": 53857, "endOffset": 53867, "count": 0}, {"startOffset": 53885, "endOffset": 53987, "count": 0}, {"startOffset": 54070, "endOffset": 54151, "count": 0}], "isBlockCoverage": true}, {"functionName": "isRelative", "ranges": [{"startOffset": 56411, "endOffset": 56752, "count": 8}, {"startOffset": 56494, "endOffset": 56511, "count": 5}, {"startOffset": 56511, "endOffset": 56556, "count": 3}, {"startOffset": 56557, "endOffset": 56599, "count": 3}, {"startOffset": 56600, "endOffset": 56643, "count": 1}, {"startOffset": 56644, "endOffset": 56749, "count": 0}], "isBlockCoverage": true}, {"functionName": "Module._initPaths", "ranges": [{"startOffset": 56872, "endOffset": 57837, "count": 1}, {"startOffset": 56913, "endOffset": 56938, "count": 0}, {"startOffset": 56990, "endOffset": 57013, "count": 0}, {"startOffset": 57221, "endOffset": 57263, "count": 0}, {"startOffset": 57562, "endOffset": 57704, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "66", "url": "node:internal/modules/package_json_reader", "functions": [{"functionName": "read", "ranges": [{"startOffset": 1112, "endOffset": 3862, "count": 26}, {"startOffset": 1208, "endOffset": 1245, "count": 18}, {"startOffset": 1245, "endOffset": 1922, "count": 8}, {"startOffset": 1922, "endOffset": 1931, "count": 0}, {"startOffset": 1932, "endOffset": 1946, "count": 0}, {"startOffset": 1947, "endOffset": 1969, "count": 8}, {"startOffset": 1993, "endOffset": 3812, "count": 3}, {"startOffset": 2061, "endOffset": 2657, "count": 0}, {"startOffset": 2947, "endOffset": 2981, "count": 2}, {"startOffset": 2983, "endOffset": 3023, "count": 2}, {"startOffset": 3083, "endOffset": 3129, "count": 0}, {"startOffset": 3189, "endOffset": 3235, "count": 0}, {"startOffset": 3346, "endOffset": 3405, "count": 1}, {"startOffset": 3377, "endOffset": 3404, "count": 0}, {"startOffset": 3407, "endOffset": 3447, "count": 1}, {"startOffset": 3481, "endOffset": 3677, "count": 1}, {"startOffset": 3602, "endOffset": 3655, "count": 0}, {"startOffset": 3705, "endOffset": 3808, "count": 0}, {"startOffset": 3812, "endOffset": 3861, "count": 8}], "isBlockCoverage": true}, {"functionName": "readPackage", "ranges": [{"startOffset": 3930, "endOffset": 4020, "count": 26}], "isBlockCoverage": true}, {"functionName": "readPackageScope", "ranges": [{"startOffset": 4237, "endOffset": 5057, "count": 13}, {"startOffset": 4424, "endOffset": 4994, "count": 19}, {"startOffset": 4673, "endOffset": 4719, "count": 0}, {"startOffset": 4721, "endOffset": 4748, "count": 0}, {"startOffset": 4815, "endOffset": 4842, "count": 0}, {"startOffset": 4913, "endOffset": 4990, "count": 13}, {"startOffset": 4994, "endOffset": 5056, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "67", "url": "node:internal/modules/esm/utils", "functions": [{"functionName": "initializeDefaultConditions", "ranges": [{"startOffset": 1785, "endOffset": 2290, "count": 1}, {"startOffset": 1968, "endOffset": 1972, "count": 0}, {"startOffset": 2085, "endOffset": 2089, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeESM", "ranges": [{"startOffset": 10706, "endOffset": 11092, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "68", "url": "node:internal/process/pre_execution", "functions": [{"functionName": "prepareMainThreadExecution", "ranges": [{"startOffset": 1060, "endOffset": 1244, "count": 1}], "isBlockCoverage": true}, {"functionName": "prepareExecution", "ranges": [{"startOffset": 2474, "endOffset": 4718, "count": 1}, {"startOffset": 3485, "endOffset": 3589, "count": 0}, {"startOffset": 4477, "endOffset": 4639, "count": 0}], "isBlockCoverage": true}, {"functionName": "setupSymbolDisposePolyfill", "ranges": [{"startOffset": 4720, "endOffset": 5455, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupUserModules", "ranges": [{"startOffset": 5457, "endOffset": 6205, "count": 1}, {"startOffset": 5827, "endOffset": 5858, "count": 0}], "isBlockCoverage": true}, {"functionName": "refreshRuntimeOptions", "ranges": [{"startOffset": 6207, "endOffset": 6263, "count": 1}], "isBlockCoverage": true}, {"functionName": "patchProcessObject", "ranges": [{"startOffset": 6690, "endOffset": 9027, "count": 1}, {"startOffset": 7675, "endOffset": 7727, "count": 0}], "isBlockCoverage": true}, {"functionName": "addReadOnlyProcessAlias", "ranges": [{"startOffset": 9029, "endOffset": 9310, "count": 13}, {"startOffset": 9150, "endOffset": 9308, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupWarningHandler", "ranges": [{"startOffset": 9312, "endOffset": 9832, "count": 1}, {"startOffset": 9687, "endOffset": 9826, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9716, "endOffset": 9818, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 9907, "endOffset": 10326, "count": 1}, {"startOffset": 9981, "endOffset": 10141, "count": 0}, {"startOffset": 10235, "endOffset": 10324, "count": 0}], "isBlockCoverage": true}, {"functionName": "setupEventsource", "ranges": [{"startOffset": 10394, "endOffset": 10519, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupWebCrypto", "ranges": [{"startOffset": 10623, "endOffset": 11575, "count": 1}, {"startOffset": 10758, "endOffset": 10775, "count": 0}, {"startOffset": 11264, "endOffset": 11573, "count": 0}], "isBlockCoverage": true}, {"functionName": "cryptoThisCheck", "ranges": [{"startOffset": 10949, "endOffset": 11125, "count": 0}], "isBlockCoverage": false}, {"functionName": "get crypto", "ranges": [{"startOffset": 11426, "endOffset": 11525, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupCodeCoverage", "ranges": [{"startOffset": 11577, "endOffset": 12098, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupCustomEvent", "ranges": [{"startOffset": 12204, "endOffset": 12481, "count": 1}, {"startOffset": 12343, "endOffset": 12360, "count": 0}], "isBlockCoverage": true}, {"functionName": "setupStacktracePrinterOnSigint", "ranges": [{"startOffset": 12483, "endOffset": 12708, "count": 1}, {"startOffset": 12585, "endOffset": 12707, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeReport", "ranges": [{"startOffset": 12710, "endOffset": 12959, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 12855, "endOffset": 12950, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupDebugEnv", "ranges": [{"startOffset": 12961, "endOffset": 13192, "count": 1}, {"startOffset": 13112, "endOffset": 13190, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeReportSignalHandlers", "ranges": [{"startOffset": 13254, "endOffset": 13442, "count": 1}, {"startOffset": 13342, "endOffset": 13440, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeHeapSnapshotSignalHandlers", "ranges": [{"startOffset": 13444, "endOffset": 14178, "count": 1}, {"startOffset": 13639, "endOffset": 14176, "count": 0}], "isBlockCoverage": true}, {"functionName": "doWriteHeapSnapshot", "ranges": [{"startOffset": 13752, "endOffset": 13906, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 14098, "endOffset": 14170, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupTraceCategoryState", "ranges": [{"startOffset": 14180, "endOffset": 14439, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupInspectorHooks", "ranges": [{"startOffset": 14441, "endOffset": 15023, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupNetworkInspection", "ranges": [{"startOffset": 15025, "endOffset": 15338, "count": 1}, {"startOffset": 15162, "endOffset": 15336, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeDeprecations", "ranges": [{"startOffset": 15533, "endOffset": 17485, "count": 1}, {"startOffset": 16245, "endOffset": 16547, "count": 16}, {"startOffset": 16290, "endOffset": 16522, "count": 0}, {"startOffset": 16864, "endOffset": 17061, "count": 0}, {"startOffset": 17089, "endOffset": 17483, "count": 0}], "isBlockCoverage": true}, {"functionName": "setupChildProcessIpcChannel", "ranges": [{"startOffset": 17487, "endOffset": 18052, "count": 1}, {"startOffset": 17563, "endOffset": 18050, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeClusterIPC", "ranges": [{"startOffset": 18054, "endOffset": 18324, "count": 1}, {"startOffset": 18141, "endOffset": 18322, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializePermission", "ranges": [{"startOffset": 18326, "endOffset": 20323, "count": 1}, {"startOffset": 18468, "endOffset": 19897, "count": 0}], "isBlockCoverage": true}, {"functionName": "binding", "ranges": [{"startOffset": 18492, "endOffset": 18579, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20155, "endOffset": 20315, "count": 6}, {"startOffset": 20233, "endOffset": 20309, "count": 0}], "isBlockCoverage": true}, {"functionName": "readPolicyFromDisk", "ranges": [{"startOffset": 20325, "endOffset": 22144, "count": 1}, {"startOffset": 20453, "endOffset": 22142, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeCJSLoader", "ranges": [{"startOffset": 22146, "endOffset": 22267, "count": 1}], "isBlockCoverage": true}, {"functionName": "initializeESMLoader", "ranges": [{"startOffset": 22269, "endOffset": 22852, "count": 1}, {"startOffset": 22605, "endOffset": 22850, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeSourceMapsHandlers", "ranges": [{"startOffset": 22854, "endOffset": 23052, "count": 1}], "isBlockCoverage": true}, {"functionName": "initializeFrozenIntrinsics", "ranges": [{"startOffset": 23054, "endOffset": 23241, "count": 1}, {"startOffset": 23139, "endOffset": 23239, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadPreloadModules", "ranges": [{"startOffset": 23348, "endOffset": 23696, "count": 1}, {"startOffset": 23542, "endOffset": 23694, "count": 0}], "isBlockCoverage": true}, {"functionName": "markBootstrapComplete", "ranges": [{"startOffset": 23698, "endOffset": 23792, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "70", "url": "node:internal/modules/run_main", "functions": [{"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 569, "endOffset": 1672, "count": 1}, {"startOffset": 743, "endOffset": 847, "count": 0}, {"startOffset": 1140, "endOffset": 1151, "count": 0}, {"startOffset": 1378, "endOffset": 1646, "count": 0}], "isBlockCoverage": true}, {"functionName": "shouldUseESMLoader", "ranges": [{"startOffset": 1833, "endOffset": 3041, "count": 1}, {"startOffset": 1939, "endOffset": 1955, "count": 0}, {"startOffset": 2429, "endOffset": 2445, "count": 0}, {"startOffset": 2561, "endOffset": 2577, "count": 0}, {"startOffset": 2640, "endOffset": 2657, "count": 0}, {"startOffset": 2881, "endOffset": 2914, "count": 0}, {"startOffset": 2960, "endOffset": 3035, "count": 0}], "isBlockCoverage": true}, {"functionName": "executeUserEntryPoint", "ranges": [{"startOffset": 5520, "endOffset": 6505, "count": 1}, {"startOffset": 6089, "endOffset": 6503, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6257, "endOffset": 6497, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "71", "url": "node:internal/dns/utils", "functions": [{"functionName": "initializeDns", "ranges": [{"startOffset": 5468, "endOffset": 5949, "count": 1}, {"startOffset": 5611, "endOffset": 5816, "count": 0}, {"startOffset": 5864, "endOffset": 5948, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5889, "endOffset": 5945, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "73", "url": "node:internal/bootstrap/switches/does_own_process_state", "functions": [{"functionName": "wrappedCwd", "ranges": [{"startOffset": 3781, "endOffset": 3884, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "80", "url": "node:internal/main/run_main_module", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1182, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "81", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/@babel/node/bin/babel-node.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 51, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "82", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/@babel/node/lib/babel-node.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4364, "count": 1}], "isBlockCoverage": true}, {"functionName": "_getRequireWildcardCache", "ranges": [{"startOffset": 153, "endOffset": 464, "count": 0}], "isBlockCoverage": false}, {"functionName": "_interopRequireWildcard", "ranges": [{"startOffset": 465, "endOffset": 1269, "count": 0}], "isBlockCoverage": false}, {"functionName": "asyncGeneratorStep", "ranges": [{"startOffset": 1270, "endOffset": 1535, "count": 2}, {"startOffset": 1405, "endOffset": 1445, "count": 0}, {"startOffset": 1461, "endOffset": 1533, "count": 1}], "isBlockCoverage": true}, {"functionName": "_asyncToGenerator", "ranges": [{"startOffset": 1536, "endOffset": 1925, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1576, "endOffset": 1922, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1644, "endOffset": 1918, "count": 1}], "isBlockCoverage": true}, {"functionName": "_next", "ranges": [{"startOffset": 1705, "endOffset": 1802, "count": 1}], "isBlockCoverage": true}, {"functionName": "_throw", "ranges": [{"startOffset": 1803, "endOffset": 1898, "count": 1}], "isBlockCoverage": true}, {"functionName": "getNormalizedV8Flag", "ranges": [{"startOffset": 2213, "endOffset": 2380, "count": 1060}, {"startOffset": 2310, "endOffset": 2364, "count": 1055}, {"startOffset": 2364, "endOffset": 2379, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2408, "endOffset": 4320, "count": 1}, {"startOffset": 2528, "endOffset": 2564, "count": 0}, {"startOffset": 2712, "endOffset": 3244, "count": 4}, {"startOffset": 2811, "endOffset": 2871, "count": 0}, {"startOffset": 2897, "endOffset": 2961, "count": 0}, {"startOffset": 3015, "endOffset": 3078, "count": 0}, {"startOffset": 3173, "endOffset": 3205, "count": 0}, {"startOffset": 3431, "endOffset": 3470, "count": 0}, {"startOffset": 3563, "endOffset": 3602, "count": 0}, {"startOffset": 3604, "endOffset": 3628, "count": 0}, {"startOffset": 3787, "endOffset": 3829, "count": 0}, {"startOffset": 4139, "endOffset": 4206, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2574, "endOffset": 2621, "count": 230}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3382, "endOffset": 3429, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3870, "endOffset": 4106, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3921, "endOffset": 4098, "count": 1}, {"startOffset": 3955, "endOffset": 4011, "count": 0}, {"startOffset": 4068, "endOffset": 4079, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4166, "endOffset": 4198, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4232, "endOffset": 4257, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4286, "endOffset": 4312, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "83", "url": "node:internal/source_map/source_map_cache_map", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3653, "count": 1}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 915, "endOffset": 3564, "count": 1}], "isBlockCoverage": false}, {"functionName": "#cleanup", "ranges": [{"startOffset": 1366, "endOffset": 1740, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 2181, "endOffset": 2496, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2309, "endOffset": 2355, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2598, "endOffset": 2826, "count": 0}], "isBlockCoverage": false}, {"functionName": "get size", "ranges": [{"startOffset": 2979, "endOffset": 3032, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3036, "endOffset": 3562, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "84", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/v8flags/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5893, "count": 1}, {"startOffset": 483, "endOffset": 494, "count": 0}, {"startOffset": 495, "endOffset": 507, "count": 0}, {"startOffset": 508, "endOffset": 523, "count": 0}, {"startOffset": 524, "endOffset": 529, "count": 0}], "isBlockCoverage": true}, {"functionName": "fail", "ranges": [{"startOffset": 1240, "endOffset": 1318, "count": 0}], "isBlockCoverage": false}, {"functionName": "openConfig", "ranges": [{"startOffset": 1320, "endOffset": 1592, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1369, "endOffset": 1588, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1435, "endOffset": 1582, "count": 1}, {"startOffset": 1470, "endOffset": 1549, "count": 0}], "isBlockCoverage": true}, {"functionName": "tryOpenConfig", "ranges": [{"startOffset": 1594, "endOffset": 2314, "count": 1}, {"startOffset": 1914, "endOffset": 2312, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1864, "endOffset": 1907, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2205, "endOffset": 2306, "count": 0}], "isBlockCoverage": false}, {"functionName": "normalizeFlagName", "ranges": [{"startOffset": 2513, "endOffset": 2599, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFlags", "ranges": [{"startOffset": 2831, "endOffset": 3734, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeConfig", "ranges": [{"startOffset": 3868, "endOffset": 4631, "count": 0}], "isBlockCoverage": false}, {"functionName": "module.exports", "ranges": [{"startOffset": 4650, "endOffset": 5810, "count": 1}, {"startOffset": 4785, "endOffset": 4859, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4815, "endOffset": 4853, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4911, "endOffset": 5806, "count": 1}, {"startOffset": 5025, "endOffset": 5805, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5151, "endOffset": 5800, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "85", "url": "node:os", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8453, "count": 1}], "isBlockCoverage": false}, {"functionName": "getCheckedFunction", "ranges": [{"startOffset": 2054, "endOffset": 2300, "count": 4}], "isBlockCoverage": true}, {"functionName": "checkError", "ranges": [{"startOffset": 2113, "endOffset": 2296, "count": 1}, {"startOffset": 2209, "endOffset": 2276, "count": 0}], "isBlockCoverage": true}, {"functionName": "getOSRelease", "ranges": [{"startOffset": 2682, "endOffset": 2695, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOSType", "ranges": [{"startOffset": 2744, "endOffset": 2754, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOSVersion", "ranges": [{"startOffset": 2806, "endOffset": 2819, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMachine", "ranges": [{"startOffset": 2869, "endOffset": 2882, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAvailableParallelism.<computed>", "ranges": [{"startOffset": 2930, "endOffset": 2961, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFreeMem.<computed>", "ranges": [{"startOffset": 2995, "endOffset": 3013, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHostname.<computed>", "ranges": [{"startOffset": 3048, "endOffset": 3067, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOSVersion.<computed>", "ranges": [{"startOffset": 3103, "endOffset": 3123, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOSType.<computed>", "ranges": [{"startOffset": 3156, "endOffset": 3173, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOSRelease.<computed>", "ranges": [{"startOffset": 3209, "endOffset": 3229, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMachine.<computed>", "ranges": [{"startOffset": 3263, "endOffset": 3281, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHomeDirectory.<computed>", "ranges": [{"startOffset": 3321, "endOffset": 3345, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTotalMem.<computed>", "ranges": [{"startOffset": 3380, "endOffset": 3399, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUptime.<computed>", "ranges": [{"startOffset": 3432, "endOffset": 3449, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadavg", "ranges": [{"startOffset": 3587, "endOffset": 3687, "count": 0}], "isBlockCoverage": false}, {"functionName": "cpus", "ranges": [{"startOffset": 3967, "endOffset": 4396, "count": 0}], "isBlockCoverage": false}, {"functionName": "arch", "ranges": [{"startOffset": 4427, "endOffset": 4469, "count": 0}], "isBlockCoverage": false}, {"functionName": "arch.<computed>", "ranges": [{"startOffset": 4496, "endOffset": 4514, "count": 0}], "isBlockCoverage": false}, {"functionName": "platform", "ranges": [{"startOffset": 4546, "endOffset": 4596, "count": 0}], "isBlockCoverage": false}, {"functionName": "platform.<computed>", "ranges": [{"startOffset": 4627, "endOffset": 4649, "count": 0}], "isBlockCoverage": false}, {"functionName": "tmpdir", "ranges": [{"startOffset": 4681, "endOffset": 5286, "count": 0}], "isBlockCoverage": false}, {"functionName": "tmpdir.<computed>", "ranges": [{"startOffset": 5315, "endOffset": 5329, "count": 0}], "isBlockCoverage": false}, {"functionName": "endianness", "ranges": [{"startOffset": 5366, "endOffset": 5413, "count": 0}], "isBlockCoverage": false}, {"functionName": "endianness.<computed>", "ranges": [{"startOffset": 5446, "endOffset": 5463, "count": 0}], "isBlockCoverage": false}, {"functionName": "networkInterfaces", "ranges": [{"startOffset": 5675, "endOffset": 6368, "count": 0}], "isBlockCoverage": false}, {"functionName": "setPriority", "ranges": [{"startOffset": 6452, "endOffset": 6741, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPriority", "ranges": [{"startOffset": 6799, "endOffset": 7054, "count": 0}], "isBlockCoverage": false}, {"functionName": "userInfo", "ranges": [{"startOffset": 7358, "endOffset": 7584, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "86", "url": "node:crypto", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8841, "count": 1}], "isBlockCoverage": false}, {"functionName": "lazyWebCrypto", "ranges": [{"startOffset": 3092, "endOffset": 3194, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyOwnsProcessState", "ranges": [{"startOffset": 3218, "endOffset": 3348, "count": 0}], "isBlockCoverage": false}, {"functionName": "createHash", "ranges": [{"startOffset": 3490, "endOffset": 3572, "count": 1}], "isBlockCoverage": true}, {"functionName": "createCipher", "ranges": [{"startOffset": 3574, "endOffset": 3674, "count": 0}], "isBlockCoverage": false}, {"functionName": "createCipheriv", "ranges": [{"startOffset": 3676, "endOffset": 3778, "count": 0}], "isBlockCoverage": false}, {"functionName": "createDecipher", "ranges": [{"startOffset": 3780, "endOffset": 3884, "count": 0}], "isBlockCoverage": false}, {"functionName": "createDecipheriv", "ranges": [{"startOffset": 3886, "endOffset": 3992, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 3994, "endOffset": 4150, "count": 0}], "isBlockCoverage": false}, {"functionName": "createDiffieHellmanGroup", "ranges": [{"startOffset": 4152, "endOffset": 4234, "count": 0}], "isBlockCoverage": false}, {"functionName": "createECDH", "ranges": [{"startOffset": 4236, "endOffset": 4292, "count": 0}], "isBlockCoverage": false}, {"functionName": "createHmac", "ranges": [{"startOffset": 4294, "endOffset": 4376, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSign", "ranges": [{"startOffset": 4378, "endOffset": 4460, "count": 0}], "isBlockCoverage": false}, {"functionName": "createVerify", "ranges": [{"startOffset": 4462, "endOffset": 4548, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFips", "ranges": [{"startOffset": 5599, "endOffset": 5684, "count": 0}], "isBlockCoverage": false}, {"functionName": "setFips", "ranges": [{"startOffset": 5686, "endOffset": 5970, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRandomValues", "ranges": [{"startOffset": 5972, "endOffset": 6063, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6141, "endOffset": 6395, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6399, "endOffset": 6588, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRandomBytesAlias", "ranges": [{"startOffset": 6637, "endOffset": 7470, "count": 3}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 6735, "endOffset": 7225, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7231, "endOffset": 7462, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8214, "endOffset": 8254, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8366, "endOffset": 8413, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8539, "endOffset": 8560, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "87", "url": "node:internal/crypto/random", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16246, "count": 1}], "isBlockCoverage": false}, {"functionName": "assertOffset", "ranges": [{"startOffset": 1260, "endOffset": 1626, "count": 0}], "isBlockCoverage": false}, {"functionName": "assertSize", "ranges": [{"startOffset": 1628, "endOffset": 2089, "count": 0}], "isBlockCoverage": false}, {"functionName": "randomBytes", "ranges": [{"startOffset": 2091, "endOffset": 2634, "count": 0}], "isBlockCoverage": false}, {"functionName": "randomFillSync", "ranges": [{"startOffset": 2636, "endOffset": 3304, "count": 0}], "isBlockCoverage": false}, {"functionName": "randomFill", "ranges": [{"startOffset": 3306, "endOffset": 4352, "count": 0}], "isBlockCoverage": false}, {"functionName": "randomInt", "ranges": [{"startOffset": 4902, "endOffset": 7142, "count": 0}], "isBlockCoverage": false}, {"functionName": "asyncRefillRandomIntCache", "ranges": [{"startOffset": 7144, "endOffset": 8020, "count": 0}], "isBlockCoverage": false}, {"functionName": "onJobDone", "ranges": [{"startOffset": 8023, "endOffset": 8185, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRandomValues", "ranges": [{"startOffset": 8417, "endOffset": 9033, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHexBytes", "ranges": [{"startOffset": 9398, "endOffset": 9697, "count": 0}], "isBlockCoverage": false}, {"functionName": "serializeUUID", "ranges": [{"startOffset": 9699, "endOffset": 10425, "count": 0}], "isBlockCoverage": false}, {"functionName": "getBufferedUUID", "ranges": [{"startOffset": 10427, "endOffset": 10730, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUnbufferedUUID", "ranges": [{"startOffset": 10732, "endOffset": 10970, "count": 0}], "isBlockCoverage": false}, {"functionName": "randomUUID", "ranges": [{"startOffset": 10972, "endOffset": 11292, "count": 0}], "isBlockCoverage": false}, {"functionName": "createRandomPrimeJob", "ranges": [{"startOffset": 11294, "endOffset": 12527, "count": 0}], "isBlockCoverage": false}, {"functionName": "generatePrime", "ranges": [{"startOffset": 12529, "endOffset": 12985, "count": 0}], "isBlockCoverage": false}, {"functionName": "generatePrimeSync", "ranges": [{"startOffset": 12987, "endOffset": 13245, "count": 0}], "isBlockCoverage": false}, {"functionName": "numberToHexCharCode", "ranges": [{"startOffset": 13519, "endOffset": 13563, "count": 0}], "isBlockCoverage": false}, {"functionName": "arrayBufferToUnsignedBigInt", "ranges": [{"startOffset": 13638, "endOffset": 14065, "count": 0}], "isBlockCoverage": false}, {"functionName": "unsignedBigIntToBuffer", "ranges": [{"startOffset": 14067, "endOffset": 14364, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkPrime", "ranges": [{"startOffset": 14366, "endOffset": 15259, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkPrimeSync", "ranges": [{"startOffset": 15261, "endOffset": 16059, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "88", "url": "node:internal/crypto/pbkdf2", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2973, "count": 1}], "isBlockCoverage": false}, {"functionName": "pbkdf2", "ranges": [{"startOffset": 487, "endOffset": 1151, "count": 0}], "isBlockCoverage": false}, {"functionName": "pbkdf2Sync", "ranges": [{"startOffset": 1153, "endOffset": 1558, "count": 0}], "isBlockCoverage": false}, {"functionName": "check", "ranges": [{"startOffset": 1560, "endOffset": 2031, "count": 0}], "isBlockCoverage": false}, {"functionName": "pbkdf2DeriveBits", "ranges": [{"startOffset": 2074, "endOffset": 2905, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "89", "url": "node:internal/crypto/util", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16325, "count": 1}], "isBlockCoverage": false}, {"functionName": "to<PERSON>uf", "ranges": [{"startOffset": 1942, "endOffset": 2121, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHashCache", "ranges": [{"startOffset": 2139, "endOffset": 2408, "count": 2}, {"startOffset": 2197, "endOffset": 2385, "count": 1}, {"startOffset": 2266, "endOffset": 2381, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2340, "endOffset": 2373, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCachedHashId", "ranges": [{"startOffset": 2410, "endOffset": 2538, "count": 1}, {"startOffset": 2527, "endOffset": 2535, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2572, "endOffset": 2615, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2649, "endOffset": 2691, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2725, "endOffset": 2767, "count": 0}], "isBlockCoverage": false}, {"functionName": "setEngine", "ranges": [{"startOffset": 2771, "endOffset": 3178, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3225, "endOffset": 3710, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6657, "endOffset": 6766, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateMax<PERSON><PERSON>er<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 7574, "endOffset": 7787, "count": 0}], "isBlockCoverage": false}, {"functionName": "normalizeAlgorithm", "ranges": [{"startOffset": 8013, "endOffset": 10448, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDataViewOrTypedArrayBuffer", "ranges": [{"startOffset": 10450, "endOffset": 10589, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDataViewOrTypedArrayByteOffset", "ranges": [{"startOffset": 10591, "endOffset": 10742, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDataViewOrTypedArrayByteLength", "ranges": [{"startOffset": 10744, "endOffset": 10895, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasAnyNotIn", "ranges": [{"startOffset": 10897, "endOffset": 11036, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateBitLength", "ranges": [{"startOffset": 11038, "endOffset": 11380, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateByteLength", "ranges": [{"startOffset": 11382, "endOffset": 11578, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11623, "endOffset": 11913, "count": 0}], "isBlockCoverage": false}, {"functionName": "onDone", "ranges": [{"startOffset": 11917, "endOffset": 12149, "count": 0}], "isBlockCoverage": false}, {"functionName": "jobPromise", "ranges": [{"startOffset": 12151, "endOffset": 12421, "count": 0}], "isBlockCoverage": false}, {"functionName": "bigIntArrayToUnsignedInt", "ranges": [{"startOffset": 12957, "endOffset": 13237, "count": 0}], "isBlockCoverage": false}, {"functionName": "bigIntArrayToUnsignedBigInt", "ranges": [{"startOffset": 13239, "endOffset": 13475, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStringOption", "ranges": [{"startOffset": 13477, "endOffset": 13642, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUsagesUnion", "ranges": [{"startOffset": 13644, "endOffset": 13856, "count": 0}], "isBlockCoverage": false}, {"functionName": "getBlockSize", "ranges": [{"startOffset": 13858, "endOffset": 14070, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDigestSizeInBytes", "ranges": [{"startOffset": 14072, "endOffset": 14255, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateKeyOps", "ranges": [{"startOffset": 14394, "endOffset": 15360, "count": 0}], "isBlockCoverage": false}, {"functionName": "secureHeapUsed", "ranges": [{"startOffset": 15362, "endOffset": 15745, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "90", "url": "node:internal/crypto/hashnames", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2520, "count": 1}], "isBlockCoverage": false}, {"functionName": "normalizeHashName", "ranges": [{"startOffset": 1890, "endOffset": 2133, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "91", "url": "node:internal/crypto/scrypt", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3331, "count": 1}], "isBlockCoverage": false}, {"functionName": "scrypt", "ranges": [{"startOffset": 626, "endOffset": 1292, "count": 0}], "isBlockCoverage": false}, {"functionName": "scryptSync", "ranges": [{"startOffset": 1294, "endOffset": 1700, "count": 0}], "isBlockCoverage": false}, {"functionName": "check", "ranges": [{"startOffset": 1702, "endOffset": 3283, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "92", "url": "node:internal/crypto/hkdf", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3376, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 831, "endOffset": 1382, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1386, "endOffset": 1814, "count": 0}], "isBlockCoverage": false}, {"functionName": "hkdf", "ranges": [{"startOffset": 1816, "endOffset": 2282, "count": 0}], "isBlockCoverage": false}, {"functionName": "hkdfSync", "ranges": [{"startOffset": 2284, "endOffset": 2622, "count": 0}], "isBlockCoverage": false}, {"functionName": "hkdfDeriveBits", "ranges": [{"startOffset": 2661, "endOffset": 3314, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "93", "url": "node:internal/crypto/keys", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 22339, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2318, "endOffset": 6439, "count": 1}], "isBlockCoverage": true}, {"functionName": "KeyObject", "ranges": [{"startOffset": 2428, "endOffset": 2970, "count": 0}], "isBlockCoverage": false}, {"functionName": "get type", "ranges": [{"startOffset": 2976, "endOffset": 3023, "count": 0}], "isBlockCoverage": false}, {"functionName": "from", "ranges": [{"startOffset": 3036, "endOffset": 3177, "count": 0}], "isBlockCoverage": false}, {"functionName": "equals", "ranges": [{"startOffset": 3183, "endOffset": 3467, "count": 0}], "isBlockCoverage": false}, {"functionName": "SecretKeyObject", "ranges": [{"startOffset": 3685, "endOffset": 3743, "count": 0}], "isBlockCoverage": false}, {"functionName": "get symmetricKeySize", "ranges": [{"startOffset": 3749, "endOffset": 3829, "count": 0}], "isBlockCoverage": false}, {"functionName": "export", "ranges": [{"startOffset": 3835, "endOffset": 4183, "count": 0}], "isBlockCoverage": false}, {"functionName": "normalizeKeyDetails", "ranges": [{"startOffset": 4316, "endOffset": 4585, "count": 0}], "isBlockCoverage": false}, {"functionName": "AsymmetricKeyObject", "ranges": [{"startOffset": 4694, "endOffset": 4754, "count": 0}], "isBlockCoverage": false}, {"functionName": "get asymmetricKeyType", "ranges": [{"startOffset": 4760, "endOffset": 4912, "count": 0}], "isBlockCoverage": false}, {"functionName": "get asymmetricKeyDetails", "ranges": [{"startOffset": 4918, "endOffset": 5295, "count": 0}], "isBlockCoverage": false}, {"functionName": "PublicKeyObject", "ranges": [{"startOffset": 5359, "endOffset": 5417, "count": 0}], "isBlockCoverage": false}, {"functionName": "export", "ranges": [{"startOffset": 5423, "endOffset": 5714, "count": 0}], "isBlockCoverage": false}, {"functionName": "PrivateKeyObject", "ranges": [{"startOffset": 5779, "endOffset": 5838, "count": 0}], "isBlockCoverage": false}, {"functionName": "export", "ranges": [{"startOffset": 5844, "endOffset": 6358, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseKeyFormat", "ranges": [{"startOffset": 6443, "endOffset": 6828, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseKeyType", "ranges": [{"startOffset": 6830, "endOffset": 7682, "count": 0}], "isBlockCoverage": false}, {"functionName": "option", "ranges": [{"startOffset": 7684, "endOffset": 7805, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseKeyFormatAndType", "ranges": [{"startOffset": 7807, "endOffset": 8507, "count": 0}], "isBlockCoverage": false}, {"functionName": "isStringOrBuffer", "ranges": [{"startOffset": 8509, "endOffset": 8646, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseKeyEncoding", "ranges": [{"startOffset": 8648, "endOffset": 10006, "count": 0}], "isBlockCoverage": false}, {"functionName": "parsePublicKeyEncoding", "ranges": [{"startOffset": 10205, "endOffset": 10341, "count": 0}], "isBlockCoverage": false}, {"functionName": "parsePrivateKeyEncoding", "ranges": [{"startOffset": 10541, "endOffset": 10657, "count": 0}], "isBlockCoverage": false}, {"functionName": "getKeyObjectHandle", "ranges": [{"startOffset": 10659, "endOffset": 11237, "count": 0}], "isBlockCoverage": false}, {"functionName": "getKeyTypes", "ranges": [{"startOffset": 11239, "endOffset": 11744, "count": 0}], "isBlockCoverage": false}, {"functionName": "getKeyObjectHandleFromJwk", "ranges": [{"startOffset": 11746, "endOffset": 14295, "count": 0}], "isBlockCoverage": false}, {"functionName": "prepareAsymmetricKey", "ranges": [{"startOffset": 14297, "endOffset": 15840, "count": 0}], "isBlockCoverage": false}, {"functionName": "preparePrivateKey", "ranges": [{"startOffset": 15842, "endOffset": 15930, "count": 0}], "isBlockCoverage": false}, {"functionName": "preparePublicOrPrivateKey", "ranges": [{"startOffset": 15932, "endOffset": 16027, "count": 0}], "isBlockCoverage": false}, {"functionName": "prepareSecret<PERSON>ey", "ranges": [{"startOffset": 16029, "endOffset": 16727, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSecretKey", "ranges": [{"startOffset": 16729, "endOffset": 16933, "count": 0}], "isBlockCoverage": false}, {"functionName": "createPublicKey", "ranges": [{"startOffset": 16935, "endOffset": 17272, "count": 0}], "isBlockCoverage": false}, {"functionName": "createPrivateKey", "ranges": [{"startOffset": 17274, "endOffset": 17615, "count": 0}], "isBlockCoverage": false}, {"functionName": "isKeyObject", "ranges": [{"startOffset": 17617, "endOffset": 17699, "count": 0}], "isBlockCoverage": false}, {"functionName": "CryptoKey", "ranges": [{"startOffset": 18122, "endOffset": 18182, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 18186, "endOffset": 18535, "count": 0}], "isBlockCoverage": false}, {"functionName": "get type", "ranges": [{"startOffset": 18539, "endOffset": 18674, "count": 0}], "isBlockCoverage": false}, {"functionName": "get extractable", "ranges": [{"startOffset": 18678, "endOffset": 18817, "count": 0}], "isBlockCoverage": false}, {"functionName": "get algorithm", "ranges": [{"startOffset": 18821, "endOffset": 18956, "count": 0}], "isBlockCoverage": false}, {"functionName": "get usages", "ranges": [{"startOffset": 18960, "endOffset": 19092, "count": 0}], "isBlockCoverage": false}, {"functionName": "defineCryptoKeyProperties", "ranges": [{"startOffset": 19545, "endOffset": 20448, "count": 0}], "isBlockCoverage": false}, {"functionName": "InternalCryptoKey", "ranges": [{"startOffset": 20744, "endOffset": 21159, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 21163, "endOffset": 21524, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 21528, "endOffset": 21675, "count": 0}], "isBlockCoverage": false}, {"functionName": "isCryptoKey", "ranges": [{"startOffset": 21804, "endOffset": 21888, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "94", "url": "node:internal/crypto/keygen", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10712, "count": 1}], "isBlockCoverage": false}, {"functionName": "isJwk", "ranges": [{"startOffset": 1183, "endOffset": 1253, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1255, "endOffset": 1409, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateKeyPair", "ranges": [{"startOffset": 1411, "endOffset": 2047, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateKeyPairSync", "ranges": [{"startOffset": 2193, "endOffset": 2310, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleError", "ranges": [{"startOffset": 2312, "endOffset": 2690, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseKeyEncoding", "ranges": [{"startOffset": 2692, "endOffset": 3942, "count": 0}], "isBlockCoverage": false}, {"functionName": "createJob", "ranges": [{"startOffset": 3944, "endOffset": 9232, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>ob", "ranges": [{"startOffset": 9263, "endOffset": 9800, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleGenerateKeyError", "ranges": [{"startOffset": 9802, "endOffset": 10004, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON>ey", "ranges": [{"startOffset": 10006, "endOffset": 10476, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateKeySync", "ranges": [{"startOffset": 10478, "endOffset": 10612, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "95", "url": "node:internal/crypto/diffie<PERSON>man", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10195, "count": 1}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1246, "endOffset": 3050, "count": 0}], "isBlockCoverage": false}, {"functionName": "DiffieHellmanGroup", "ranges": [{"startOffset": 3053, "endOffset": 3379, "count": 0}], "isBlockCoverage": false}, {"functionName": "dhGenerateKeys", "ranges": [{"startOffset": 3490, "endOffset": 3605, "count": 0}], "isBlockCoverage": false}, {"functionName": "dhComputeSecret", "ranges": [{"startOffset": 3719, "endOffset": 3977, "count": 0}], "isBlockCoverage": false}, {"functionName": "dhGetPrime", "ranges": [{"startOffset": 4076, "endOffset": 4185, "count": 0}], "isBlockCoverage": false}, {"functionName": "dhGetGenerator", "ranges": [{"startOffset": 4296, "endOffset": 4421, "count": 0}], "isBlockCoverage": false}, {"functionName": "dhGetPublicKey", "ranges": [{"startOffset": 4532, "endOffset": 4645, "count": 0}], "isBlockCoverage": false}, {"functionName": "dhGetPrivateKey", "ranges": [{"startOffset": 4759, "endOffset": 4874, "count": 0}], "isBlockCoverage": false}, {"functionName": "setPublicKey", "ranges": [{"startOffset": 4916, "endOffset": 5058, "count": 0}], "isBlockCoverage": false}, {"functionName": "setPrivateKey", "ranges": [{"startOffset": 5102, "endOffset": 5246, "count": 0}], "isBlockCoverage": false}, {"functionName": "ECDH", "ranges": [{"startOffset": 5250, "endOffset": 5404, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateKeys", "ranges": [{"startOffset": 5715, "endOffset": 5837, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPublicKey", "ranges": [{"startOffset": 5870, "endOffset": 6021, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 6042, "endOffset": 6308, "count": 0}], "isBlockCoverage": false}, {"functionName": "encode", "ranges": [{"startOffset": 6311, "endOffset": 6446, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFormat", "ranges": [{"startOffset": 6448, "endOffset": 6771, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 6845, "endOffset": 7913, "count": 0}], "isBlockCoverage": false}, {"functionName": "ecdhDeriveBits", "ranges": [{"startOffset": 8044, "endOffset": 10089, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "96", "url": "node:internal/crypto/cipher", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10199, "count": 1}], "isBlockCoverage": false}, {"functionName": "rsaFunctionFor", "ranges": [{"startOffset": 1233, "endOffset": 1981, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1301, "endOffset": 1978, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDecoder", "ranges": [{"startOffset": 2482, "endOffset": 2848, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUIntOption", "ranges": [{"startOffset": 2850, "endOffset": 3084, "count": 0}], "isBlockCoverage": false}, {"functionName": "createCipherBase", "ranges": [{"startOffset": 3086, "endOffset": 3505, "count": 0}], "isBlockCoverage": false}, {"functionName": "createCipher", "ranges": [{"startOffset": 3507, "endOffset": 3742, "count": 0}], "isBlockCoverage": false}, {"functionName": "createCipherWithIV", "ranges": [{"startOffset": 3744, "endOffset": 4083, "count": 0}], "isBlockCoverage": false}, {"functionName": "Cipher", "ranges": [{"startOffset": 4326, "endOffset": 4527, "count": 0}], "isBlockCoverage": false}, {"functionName": "_transform", "ranges": [{"startOffset": 4670, "endOffset": 4786, "count": 0}], "isBlockCoverage": false}, {"functionName": "_flush", "ranges": [{"startOffset": 4815, "endOffset": 4953, "count": 0}], "isBlockCoverage": false}, {"functionName": "update", "ranges": [{"startOffset": 4982, "endOffset": 5498, "count": 0}], "isBlockCoverage": false}, {"functionName": "final", "ranges": [{"startOffset": 5527, "endOffset": 5771, "count": 0}], "isBlockCoverage": false}, {"functionName": "setAutoPadding", "ranges": [{"startOffset": 5809, "endOffset": 5956, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAuthTag", "ranges": [{"startOffset": 5989, "endOffset": 6149, "count": 0}], "isBlockCoverage": false}, {"functionName": "setAuthTag", "ranges": [{"startOffset": 6153, "endOffset": 6365, "count": 0}], "isBlockCoverage": false}, {"functionName": "setAAD", "ranges": [{"startOffset": 6393, "endOffset": 6735, "count": 0}], "isBlockCoverage": false}, {"functionName": "Cipheriv", "ranges": [{"startOffset": 6983, "endOffset": 7193, "count": 0}], "isBlockCoverage": false}, {"functionName": "addCipherPrototypeFunctions", "ranges": [{"startOffset": 7195, "endOffset": 7786, "count": 3}, {"startOffset": 7591, "endOffset": 7664, "count": 1}, {"startOffset": 7664, "endOffset": 7726, "count": 2}], "isBlockCoverage": true}, {"functionName": "Decipher", "ranges": [{"startOffset": 8187, "endOffset": 8395, "count": 0}], "isBlockCoverage": false}, {"functionName": "Decipheriv", "ranges": [{"startOffset": 8800, "endOffset": 9017, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCipherInfo", "ranges": [{"startOffset": 9179, "endOffset": 10040, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "97", "url": "node:internal/streams/lazy_transform", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1535, "count": 1}], "isBlockCoverage": false}, {"functionName": "LazyTransform", "ranges": [{"startOffset": 401, "endOffset": 463, "count": 1}], "isBlockCoverage": true}, {"functionName": "makeGetter", "ranges": [{"startOffset": 595, "endOffset": 918, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 632, "endOffset": 915, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeSetter", "ranges": [{"startOffset": 920, "endOffset": 1139, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 957, "endOffset": 1136, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "98", "url": "node:stream", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5095, "count": 1}], "isBlockCoverage": false}, {"functionName": "fn", "ranges": [{"startOffset": 2552, "endOffset": 2712, "count": 0}], "isBlockCoverage": false}, {"functionName": "fn", "ranges": [{"startOffset": 3214, "endOffset": 3352, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4400, "endOffset": 4432, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4530, "endOffset": 4571, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4664, "endOffset": 4705, "count": 0}], "isBlockCoverage": false}, {"functionName": "_uint8ArrayToBuffer", "ranges": [{"startOffset": 4886, "endOffset": 5093, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "99", "url": "node:internal/streams/operators", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10772, "count": 1}], "isBlockCoverage": false}, {"functionName": "compose", "ranges": [{"startOffset": 1019, "endOffset": 1599, "count": 0}], "isBlockCoverage": false}, {"functionName": "map", "ranges": [{"startOffset": 1601, "endOffset": 4704, "count": 0}], "isBlockCoverage": false}, {"functionName": "asIndexedPairs", "ranges": [{"startOffset": 4706, "endOffset": 5179, "count": 0}], "isBlockCoverage": false}, {"functionName": "some", "ranges": [{"startOffset": 5181, "endOffset": 5329, "count": 0}], "isBlockCoverage": false}, {"functionName": "every", "ranges": [{"startOffset": 5331, "endOffset": 5659, "count": 0}], "isBlockCoverage": false}, {"functionName": "find", "ranges": [{"startOffset": 5661, "endOffset": 5803, "count": 0}], "isBlockCoverage": false}, {"functionName": "for<PERSON>ach", "ranges": [{"startOffset": 5805, "endOffset": 6175, "count": 0}], "isBlockCoverage": false}, {"functionName": "filter", "ranges": [{"startOffset": 6177, "endOffset": 6504, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReduceAwareErrMissingArgs", "ranges": [{"startOffset": 6720, "endOffset": 6834, "count": 0}], "isBlockCoverage": false}, {"functionName": "reduce", "ranges": [{"startOffset": 6838, "endOffset": 8280, "count": 0}], "isBlockCoverage": false}, {"functionName": "toArray", "ranges": [{"startOffset": 8282, "endOffset": 8711, "count": 0}], "isBlockCoverage": false}, {"functionName": "flatMap", "ranges": [{"startOffset": 8713, "endOffset": 8907, "count": 0}], "isBlockCoverage": false}, {"functionName": "toIntegerOrInfinity", "ranges": [{"startOffset": 8909, "endOffset": 9231, "count": 0}], "isBlockCoverage": false}, {"functionName": "drop", "ranges": [{"startOffset": 9233, "endOffset": 9781, "count": 0}], "isBlockCoverage": false}, {"functionName": "take", "ranges": [{"startOffset": 9783, "endOffset": 10453, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "100", "url": "node:internal/abort_controller", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12429, "count": 1}], "isBlockCoverage": false}, {"functionName": "lazyMessageChannel", "ranges": [{"startOffset": 1609, "endOffset": 1742, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyMakeTransferable", "ranges": [{"startOffset": 1744, "endOffset": 1902, "count": 0}], "isBlockCoverage": false}, {"functionName": "customInspect", "ranges": [{"startOffset": 2372, "endOffset": 2631, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateThisAbortSignal", "ranges": [{"startOffset": 2633, "endOffset": 2758, "count": 0}], "isBlockCoverage": false}, {"functionName": "setWeakAbortSignalTimeout", "ranges": [{"startOffset": 3340, "endOffset": 3740, "count": 0}], "isBlockCoverage": false}, {"functionName": "AbortSignal", "ranges": [{"startOffset": 3784, "endOffset": 3844, "count": 0}], "isBlockCoverage": false}, {"functionName": "get aborted", "ranges": [{"startOffset": 3881, "endOffset": 3964, "count": 0}], "isBlockCoverage": false}, {"functionName": "get reason", "ranges": [{"startOffset": 3997, "endOffset": 4076, "count": 0}], "isBlockCoverage": false}, {"functionName": "throwIfAborted", "ranges": [{"startOffset": 4080, "endOffset": 4196, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4200, "endOffset": 4329, "count": 0}], "isBlockCoverage": false}, {"functionName": "abort", "ranges": [{"startOffset": 4407, "endOffset": 4551, "count": 0}], "isBlockCoverage": false}, {"functionName": "timeout", "ranges": [{"startOffset": 4629, "endOffset": 4895, "count": 0}], "isBlockCoverage": false}, {"functionName": "any", "ranges": [{"startOffset": 4982, "endOffset": 6325, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6329, "endOffset": 7097, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7101, "endOffset": 7455, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7459, "endOffset": 8066, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8070, "endOffset": 8322, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8326, "endOffset": 8774, "count": 0}], "isBlockCoverage": false}, {"functionName": "ClonedAbortSignal", "ranges": [{"startOffset": 8778, "endOffset": 8862, "count": 0}], "isBlockCoverage": false}, {"functionName": "ClonedAbortSignal.<computed>", "ranges": [{"startOffset": 8907, "endOffset": 8915, "count": 0}], "isBlockCoverage": false}, {"functionName": "createAbortSignal", "ranges": [{"startOffset": 9393, "endOffset": 9806, "count": 0}], "isBlockCoverage": false}, {"functionName": "abortSignal", "ranges": [{"startOffset": 9808, "endOffset": 10170, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 10172, "endOffset": 10828, "count": 0}], "isBlockCoverage": false}, {"functionName": "transferableAbortSignal", "ranges": [{"startOffset": 10976, "endOffset": 11168, "count": 0}], "isBlockCoverage": false}, {"functionName": "transferableAbortController", "ranges": [{"startOffset": 11240, "endOffset": 11329, "count": 0}], "isBlockCoverage": false}, {"functionName": "aborted", "ranges": [{"startOffset": 11423, "endOffset": 11980, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "101", "url": "node:internal/streams/end-of-stream", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8492, "count": 1}], "isBlockCoverage": false}, {"functionName": "isRequest", "ranges": [{"startOffset": 891, "endOffset": 986, "count": 0}], "isBlockCoverage": false}, {"functionName": "nop", "ranges": [{"startOffset": 1000, "endOffset": 1008, "count": 0}], "isBlockCoverage": false}, {"functionName": "eos", "ranges": [{"startOffset": 1011, "endOffset": 7061, "count": 0}], "isBlockCoverage": false}, {"functionName": "eosWeb", "ranges": [{"startOffset": 7063, "endOffset": 7972, "count": 0}], "isBlockCoverage": false}, {"functionName": "finished", "ranges": [{"startOffset": 7974, "endOffset": 8432, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "102", "url": "node:internal/streams/compose", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5511, "count": 1}], "isBlockCoverage": false}, {"functionName": "compose", "ranges": [{"startOffset": 592, "endOffset": 5509, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "103", "url": "node:internal/streams/pipeline", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12389, "count": 1}], "isBlockCoverage": false}, {"functionName": "destroyer", "ranges": [{"startOffset": 1056, "endOffset": 1485, "count": 0}], "isBlockCoverage": false}, {"functionName": "popCallback", "ranges": [{"startOffset": 1487, "endOffset": 1815, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeAsyncIterable", "ranges": [{"startOffset": 1817, "endOffset": 2103, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromReadable", "ranges": [{"startOffset": 2105, "endOffset": 2279, "count": 0}], "isBlockCoverage": false}, {"functionName": "pumpToNode", "ranges": [{"startOffset": 2281, "endOffset": 3325, "count": 0}], "isBlockCoverage": false}, {"functionName": "pumpToWeb", "ranges": [{"startOffset": 3327, "endOffset": 3925, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipeline", "ranges": [{"startOffset": 3927, "endOffset": 4020, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipelineImpl", "ranges": [{"startOffset": 4022, "endOffset": 10539, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipe", "ranges": [{"startOffset": 10541, "endOffset": 12342, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "104", "url": "node:internal/streams/destroy", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7354, "count": 1}], "isBlockCoverage": false}, {"functionName": "checkError", "ranges": [{"startOffset": 487, "endOffset": 798, "count": 0}], "isBlockCoverage": false}, {"functionName": "destroy", "ranges": [{"startOffset": 908, "endOffset": 1785, "count": 0}], "isBlockCoverage": false}, {"functionName": "_destroy", "ranges": [{"startOffset": 1787, "endOffset": 2411, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitErrorCloseNT", "ranges": [{"startOffset": 2413, "endOffset": 2500, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitCloseNT", "ranges": [{"startOffset": 2502, "endOffset": 2822, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitErrorNT", "ranges": [{"startOffset": 2824, "endOffset": 3171, "count": 0}], "isBlockCoverage": false}, {"functionName": "undestroy", "ranges": [{"startOffset": 3173, "endOffset": 3863, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 3865, "endOffset": 5004, "count": 0}], "isBlockCoverage": false}, {"functionName": "construct", "ranges": [{"startOffset": 5006, "endOffset": 5422, "count": 0}], "isBlockCoverage": false}, {"functionName": "constructNT", "ranges": [{"startOffset": 5424, "endOffset": 6163, "count": 0}], "isBlockCoverage": false}, {"functionName": "isRequest", "ranges": [{"startOffset": 6165, "endOffset": 6261, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitCloseLegacy", "ranges": [{"startOffset": 6263, "endOffset": 6323, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitErrorCloseLegacy", "ranges": [{"startOffset": 6325, "endOffset": 6445, "count": 0}], "isBlockCoverage": false}, {"functionName": "destroyer", "ranges": [{"startOffset": 6480, "endOffset": 7262, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "105", "url": "node:internal/streams/duplex", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6588, "count": 1}], "isBlockCoverage": false}, {"functionName": "Duplex", "ranges": [{"startOffset": 2350, "endOffset": 4313, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 5461, "endOffset": 5673, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 5679, "endOffset": 5940, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyWebStreams", "ranges": [{"startOffset": 6014, "endOffset": 6178, "count": 0}], "isBlockCoverage": false}, {"functionName": "Duplex.fromWeb", "ranges": [{"startOffset": 6197, "endOffset": 6315, "count": 0}], "isBlockCoverage": false}, {"functionName": "Duplex.toWeb", "ranges": [{"startOffset": 6333, "endOffset": 6422, "count": 0}], "isBlockCoverage": false}, {"functionName": "Duplex.from", "ranges": [{"startOffset": 6455, "endOffset": 6586, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "106", "url": "node:internal/streams/legacy", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3251, "count": 1}], "isBlockCoverage": false}, {"functionName": "Stream", "ranges": [{"startOffset": 130, "endOffset": 178, "count": 0}], "isBlockCoverage": true}, {"functionName": "Stream.pipe", "ranges": [{"startOffset": 292, "endOffset": 2094, "count": 0}], "isBlockCoverage": false}, {"functionName": "eventNames", "ranges": [{"startOffset": 2127, "endOffset": 2393, "count": 0}], "isBlockCoverage": false}, {"functionName": "prependListener", "ranges": [{"startOffset": 2396, "endOffset": 3203, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "107", "url": "node:internal/streams/readable", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 51364, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1880, "endOffset": 1905, "count": 0}], "isBlockCoverage": false}, {"functionName": "nop", "ranges": [{"startOffset": 2867, "endOffset": 2875, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeBitMapDescriptor", "ranges": [{"startOffset": 3771, "endOffset": 3993, "count": 19}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3846, "endOffset": 3890, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 3896, "endOffset": 3985, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6167, "endOffset": 6255, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6261, "endOffset": 6427, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6504, "endOffset": 6604, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6610, "endOffset": 6840, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6909, "endOffset": 6997, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7003, "endOffset": 7169, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7239, "endOffset": 7329, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7335, "endOffset": 7504, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7573, "endOffset": 7676, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7682, "endOffset": 7949, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReadableState", "ranges": [{"startOffset": 7962, "endOffset": 9958, "count": 0}], "isBlockCoverage": false}, {"functionName": "onConstructed", "ranges": [{"startOffset": 10002, "endOffset": 10119, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable", "ranges": [{"startOffset": 10122, "endOffset": 11156, "count": 0}], "isBlockCoverage": true}, {"functionName": "Readable._destroy", "ranges": [{"startOffset": 11293, "endOffset": 11325, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.<computed>", "ranges": [{"startOffset": 11376, "endOffset": 11414, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.<computed>", "ranges": [{"startOffset": 11458, "endOffset": 11714, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.push", "ranges": [{"startOffset": 11943, "endOffset": 12212, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.unshift", "ranges": [{"startOffset": 12308, "endOffset": 12575, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkUnshiftByteMode", "ranges": [{"startOffset": 12579, "endOffset": 13645, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkUnshiftObjectMode", "ranges": [{"startOffset": 13647, "endOffset": 13887, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkUnshiftValue", "ranges": [{"startOffset": 13889, "endOffset": 14222, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkPushByteMode", "ranges": [{"startOffset": 14224, "endOffset": 15616, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkPushObjectMode", "ranges": [{"startOffset": 15618, "endOffset": 16215, "count": 0}], "isBlockCoverage": false}, {"functionName": "canPushMore", "ranges": [{"startOffset": 16217, "endOffset": 16547, "count": 0}], "isBlockCoverage": false}, {"functionName": "addChunk", "ranges": [{"startOffset": 16549, "endOffset": 17493, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.isPaused", "ranges": [{"startOffset": 17525, "endOffset": 17680, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.setEncoding", "ranges": [{"startOffset": 17744, "endOffset": 18314, "count": 0}], "isBlockCoverage": false}, {"functionName": "computeNewHighWaterMark", "ranges": [{"startOffset": 18375, "endOffset": 18726, "count": 0}], "isBlockCoverage": false}, {"functionName": "howMuchToRead", "ranges": [{"startOffset": 18839, "endOffset": 19320, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.read", "ranges": [{"startOffset": 19417, "endOffset": 24092, "count": 0}], "isBlockCoverage": false}, {"functionName": "onEofChunk", "ranges": [{"startOffset": 24095, "endOffset": 25057, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitReadable", "ranges": [{"startOffset": 25258, "endOffset": 25589, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitReadable_", "ranges": [{"startOffset": 25591, "endOffset": 26263, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeReadMore", "ranges": [{"startOffset": 26613, "endOffset": 26820, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeReadMore_", "ranges": [{"startOffset": 26822, "endOffset": 28651, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable._read", "ranges": [{"startOffset": 28922, "endOffset": 28988, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.pipe", "ranges": [{"startOffset": 29017, "endOffset": 33527, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipeOnDrain", "ranges": [{"startOffset": 33530, "endOffset": 34207, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.unpipe", "ranges": [{"startOffset": 34238, "endOffset": 34939, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.on", "ranges": [{"startOffset": 35065, "endOffset": 36074, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.removeListener", "ranges": [{"startOffset": 36169, "endOffset": 36876, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.removeAllListeners", "ranges": [{"startOffset": 36979, "endOffset": 37573, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateReadableListening", "ranges": [{"startOffset": 37576, "endOffset": 38278, "count": 0}], "isBlockCoverage": false}, {"functionName": "nReadingNextTick", "ranges": [{"startOffset": 38280, "endOffset": 38368, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.resume", "ranges": [{"startOffset": 38522, "endOffset": 39027, "count": 0}], "isBlockCoverage": false}, {"functionName": "resume", "ranges": [{"startOffset": 39030, "endOffset": 39203, "count": 0}], "isBlockCoverage": false}, {"functionName": "resume_", "ranges": [{"startOffset": 39205, "endOffset": 39519, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.pause", "ranges": [{"startOffset": 39548, "endOffset": 39860, "count": 0}], "isBlockCoverage": false}, {"functionName": "flow", "ranges": [{"startOffset": 39863, "endOffset": 40014, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.wrap", "ranges": [{"startOffset": 40198, "endOffset": 41219, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.<computed>", "ranges": [{"startOffset": 41264, "endOffset": 41316, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.iterator", "ranges": [{"startOffset": 41349, "endOffset": 41492, "count": 0}], "isBlockCoverage": false}, {"functionName": "streamToAsyncIterator", "ranges": [{"startOffset": 41495, "endOffset": 41744, "count": 0}], "isBlockCoverage": false}, {"functionName": "createAsyncIterator", "ranges": [{"startOffset": 41746, "endOffset": 42822, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 43041, "endOffset": 43430, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 43436, "endOffset": 43566, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 43648, "endOffset": 43712, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 43794, "endOffset": 44002, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44090, "endOffset": 44156, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44237, "endOffset": 44319, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44401, "endOffset": 44461, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 44472, "endOffset": 44581, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44657, "endOffset": 44711, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44791, "endOffset": 44879, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44957, "endOffset": 45042, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 45111, "endOffset": 45195, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 45240, "endOffset": 45324, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 45395, "endOffset": 45482, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 45488, "endOffset": 45777, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 45852, "endOffset": 45940, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 46081, "endOffset": 46126, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 46206, "endOffset": 46264, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 46270, "endOffset": 46431, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromList", "ranges": [{"startOffset": 46737, "endOffset": 49189, "count": 0}], "isBlockCoverage": false}, {"functionName": "endReadable", "ranges": [{"startOffset": 49191, "endOffset": 49417, "count": 0}], "isBlockCoverage": false}, {"functionName": "endReadableNT", "ranges": [{"startOffset": 49419, "endOffset": 50312, "count": 0}], "isBlockCoverage": false}, {"functionName": "endWritableNT", "ranges": [{"startOffset": 50314, "endOffset": 50474, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.from", "ranges": [{"startOffset": 50492, "endOffset": 50561, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyWebStreams", "ranges": [{"startOffset": 50626, "endOffset": 50790, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.fromWeb", "ranges": [{"startOffset": 50811, "endOffset": 50945, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.toWeb", "ranges": [{"startOffset": 50965, "endOffset": 51099, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.wrap", "ranges": [{"startOffset": 51118, "endOffset": 51362, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "108", "url": "node:internal/streams/add-abort-signal", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1741, "count": 1}], "isBlockCoverage": false}, {"functionName": "validateAbortSignal", "ranges": [{"startOffset": 570, "endOffset": 729, "count": 0}], "isBlockCoverage": false}, {"functionName": "addAbortSignal", "ranges": [{"startOffset": 764, "endOffset": 1075, "count": 0}], "isBlockCoverage": false}, {"functionName": "module.exports.addAbortSignalNoValidate", "ranges": [{"startOffset": 1120, "endOffset": 1739, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "109", "url": "node:internal/streams/state", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1332, "count": 1}], "isBlockCoverage": false}, {"functionName": "highWaterMarkFrom", "ranges": [{"startOffset": 287, "endOffset": 454, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDefaultHighWaterMark", "ranges": [{"startOffset": 456, "endOffset": 586, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDefaultHighWaterMark", "ranges": [{"startOffset": 588, "endOffset": 799, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHighWaterMark", "ranges": [{"startOffset": 801, "endOffset": 1234, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "110", "url": "node:string_decoder", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5141, "count": 1}], "isBlockCoverage": false}, {"functionName": "normalizeEncoding", "ranges": [{"startOffset": 2172, "endOffset": 2422, "count": 0}], "isBlockCoverage": false}, {"functionName": "StringDecoder", "ranges": [{"startOffset": 2633, "endOffset": 2832, "count": 0}], "isBlockCoverage": false}, {"functionName": "write", "ranges": [{"startOffset": 3155, "endOffset": 3540, "count": 0}], "isBlockCoverage": false}, {"functionName": "end", "ranges": [{"startOffset": 3817, "endOffset": 4008, "count": 0}], "isBlockCoverage": false}, {"functionName": "text", "ranges": [{"startOffset": 4219, "endOffset": 4376, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4514, "endOffset": 4727, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4819, "endOffset": 4882, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4975, "endOffset": 5090, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "111", "url": "node:internal/streams/from", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4341, "count": 1}], "isBlockCoverage": false}, {"functionName": "from", "ranges": [{"startOffset": 241, "endOffset": 4316, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "112", "url": "node:internal/streams/writable", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33628, "count": 1}], "isBlockCoverage": false}, {"functionName": "nop", "ranges": [{"startOffset": 2779, "endOffset": 2796, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeBitMapDescriptor", "ranges": [{"startOffset": 3852, "endOffset": 4074, "count": 20}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3927, "endOffset": 3971, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 3977, "endOffset": 4066, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7038, "endOffset": 7116, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7122, "endOffset": 7288, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7358, "endOffset": 7458, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7464, "endOffset": 7737, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7814, "endOffset": 7914, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7920, "endOffset": 8150, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8291, "endOffset": 8368, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 8374, "endOffset": 8538, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8725, "endOffset": 8825, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 8831, "endOffset": 9028, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9098, "endOffset": 9176, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 9182, "endOffset": 9349, "count": 0}], "isBlockCoverage": false}, {"functionName": "WritableState", "ranges": [{"startOffset": 9362, "endOffset": 11664, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetBuffer", "ranges": [{"startOffset": 11666, "endOffset": 11830, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 11868, "endOffset": 11997, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 12093, "endOffset": 12204, "count": 0}], "isBlockCoverage": false}, {"functionName": "onConstructed", "ranges": [{"startOffset": 12253, "endOffset": 12439, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable", "ranges": [{"startOffset": 12442, "endOffset": 13548, "count": 0}], "isBlockCoverage": false}, {"functionName": "value", "ranges": [{"startOffset": 13630, "endOffset": 13834, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.pipe", "ranges": [{"startOffset": 13935, "endOffset": 14003, "count": 0}], "isBlockCoverage": false}, {"functionName": "_write", "ranges": [{"startOffset": 14006, "endOffset": 15404, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.write", "ranges": [{"startOffset": 15433, "endOffset": 15624, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.cork", "ranges": [{"startOffset": 15653, "endOffset": 15751, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.uncork", "ranges": [{"startOffset": 15782, "endOffset": 16020, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDefaultEncoding", "ranges": [{"startOffset": 16063, "endOffset": 16388, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeOr<PERSON>uffer", "ranges": [{"startOffset": 16578, "endOffset": 17782, "count": 0}], "isBlockCoverage": false}, {"functionName": "doWrite", "ranges": [{"startOffset": 17784, "endOffset": 18221, "count": 0}], "isBlockCoverage": false}, {"functionName": "onwriteError", "ranges": [{"startOffset": 18223, "endOffset": 18608, "count": 0}], "isBlockCoverage": false}, {"functionName": "onwrite", "ranges": [{"startOffset": 18610, "endOffset": 21216, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterWriteTick", "ranges": [{"startOffset": 21218, "endOffset": 21405, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterWrite", "ranges": [{"startOffset": 21407, "endOffset": 21915, "count": 0}], "isBlockCoverage": false}, {"functionName": "errorBuffer", "ranges": [{"startOffset": 21987, "endOffset": 22538, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 22604, "endOffset": 24274, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable._write", "ranges": [{"startOffset": 24304, "endOffset": 24473, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.end", "ranges": [{"startOffset": 24537, "endOffset": 26177, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON>sh", "ranges": [{"startOffset": 26180, "endOffset": 26609, "count": 0}], "isBlockCoverage": false}, {"functionName": "onFinish", "ranges": [{"startOffset": 26611, "endOffset": 27243, "count": 0}], "isBlockCoverage": false}, {"functionName": "prefinish", "ranges": [{"startOffset": 27245, "endOffset": 27769, "count": 0}], "isBlockCoverage": false}, {"functionName": "finishMaybe", "ranges": [{"startOffset": 27771, "endOffset": 28270, "count": 0}], "isBlockCoverage": false}, {"functionName": "finish", "ranges": [{"startOffset": 28272, "endOffset": 28915, "count": 0}], "isBlockCoverage": false}, {"functionName": "callFinishedCallbacks", "ranges": [{"startOffset": 28917, "endOffset": 29235, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 29319, "endOffset": 29422, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 29470, "endOffset": 29576, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 29582, "endOffset": 29832, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 29879, "endOffset": 30282, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 30288, "endOffset": 30422, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 30477, "endOffset": 30595, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 30652, "endOffset": 30772, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 30825, "endOffset": 30920, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 30972, "endOffset": 31088, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31144, "endOffset": 31297, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31357, "endOffset": 31454, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31507, "endOffset": 31600, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31653, "endOffset": 31743, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31812, "endOffset": 31909, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31968, "endOffset": 32225, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.destroy", "ranges": [{"startOffset": 32303, "endOffset": 32583, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable._destroy", "ranges": [{"startOffset": 32671, "endOffset": 32703, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.<computed>", "ranges": [{"startOffset": 32754, "endOffset": 32792, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyWebStreams", "ranges": [{"startOffset": 32857, "endOffset": 33021, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.fromWeb", "ranges": [{"startOffset": 33042, "endOffset": 33176, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.toWeb", "ranges": [{"startOffset": 33196, "endOffset": 33303, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.<computed>", "ranges": [{"startOffset": 33347, "endOffset": 33626, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "113", "url": "node:stream/promises", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 917, "count": 1}], "isBlockCoverage": false}, {"functionName": "pipeline", "ranges": [{"startOffset": 318, "endOffset": 869, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "114", "url": "node:internal/streams/transform", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7123, "count": 1}], "isBlockCoverage": false}, {"functionName": "Transform", "ranges": [{"startOffset": 3920, "endOffset": 5500, "count": 0}], "isBlockCoverage": true}, {"functionName": "final", "ranges": [{"startOffset": 5502, "endOffset": 5946, "count": 0}], "isBlockCoverage": false}, {"functionName": "prefinish", "ranges": [{"startOffset": 5948, "endOffset": 6029, "count": 0}], "isBlockCoverage": false}, {"functionName": "Transform._transform", "ranges": [{"startOffset": 6101, "endOffset": 6196, "count": 0}], "isBlockCoverage": false}, {"functionName": "Transform._write", "ranges": [{"startOffset": 6228, "endOffset": 6965, "count": 0}], "isBlockCoverage": false}, {"functionName": "Transform._read", "ranges": [{"startOffset": 6996, "endOffset": 7121, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "115", "url": "node:internal/streams/passthrough", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1762, "count": 1}], "isBlockCoverage": false}, {"functionName": "PassThrough", "ranges": [{"startOffset": 1529, "endOffset": 1671, "count": 0}], "isBlockCoverage": false}, {"functionName": "PassThrough._transform", "ranges": [{"startOffset": 1708, "endOffset": 1760, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "116", "url": "node:internal/streams/duplexpair", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1373, "count": 1}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 219, "endOffset": 1136, "count": 0}], "isBlockCoverage": false}, {"functionName": "duplexPair", "ranges": [{"startOffset": 1138, "endOffset": 1343, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "117", "url": "node:internal/crypto/sig", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6942, "count": 1}], "isBlockCoverage": false}, {"functionName": "Sign", "ranges": [{"startOffset": 870, "endOffset": 1129, "count": 0}], "isBlockCoverage": false}, {"functionName": "_write", "ranges": [{"startOffset": 1252, "endOffset": 1344, "count": 0}], "isBlockCoverage": false}, {"functionName": "update", "ranges": [{"startOffset": 1371, "endOffset": 1684, "count": 0}], "isBlockCoverage": false}, {"functionName": "getPadding", "ranges": [{"startOffset": 1687, "endOffset": 1762, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSaltLength", "ranges": [{"startOffset": 1764, "endOffset": 1845, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDSASignatureEncoding", "ranges": [{"startOffset": 1847, "endOffset": 2199, "count": 0}], "isBlockCoverage": false}, {"functionName": "getIntOption", "ranges": [{"startOffset": 2201, "endOffset": 2446, "count": 0}], "isBlockCoverage": false}, {"functionName": "sign", "ranges": [{"startOffset": 2470, "endOffset": 3086, "count": 0}], "isBlockCoverage": false}, {"functionName": "signOneShot", "ranges": [{"startOffset": 3089, "endOffset": 4307, "count": 0}], "isBlockCoverage": false}, {"functionName": "Verify", "ranges": [{"startOffset": 4309, "endOffset": 4576, "count": 0}], "isBlockCoverage": false}, {"functionName": "verify", "ranges": [{"startOffset": 4804, "endOffset": 5387, "count": 0}], "isBlockCoverage": false}, {"functionName": "verifyOneShot", "ranges": [{"startOffset": 5390, "endOffset": 6868, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "118", "url": "node:internal/crypto/hash", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5947, "count": 1}], "isBlockCoverage": false}, {"functionName": "Hash", "ranges": [{"startOffset": 1100, "endOffset": 1826, "count": 1}, {"startOffset": 1159, "endOffset": 1195, "count": 0}, {"startOffset": 1345, "endOffset": 1364, "count": 0}, {"startOffset": 1365, "endOffset": 1391, "count": 0}, {"startOffset": 1437, "endOffset": 1484, "count": 0}, {"startOffset": 1616, "endOffset": 1620, "count": 0}], "isBlockCoverage": true}, {"functionName": "copy", "ranges": [{"startOffset": 1957, "endOffset": 2125, "count": 0}], "isBlockCoverage": false}, {"functionName": "_transform", "ranges": [{"startOffset": 2156, "endOffset": 2261, "count": 0}], "isBlockCoverage": false}, {"functionName": "_flush", "ranges": [{"startOffset": 2288, "endOffset": 2368, "count": 0}], "isBlockCoverage": false}, {"functionName": "update", "ranges": [{"startOffset": 2395, "endOffset": 2859, "count": 1}, {"startOffset": 2488, "endOffset": 2526, "count": 0}, {"startOffset": 2603, "endOffset": 2749, "count": 0}, {"startOffset": 2800, "endOffset": 2842, "count": 0}], "isBlockCoverage": true}, {"functionName": "digest", "ranges": [{"startOffset": 2887, "endOffset": 3208, "count": 1}, {"startOffset": 2980, "endOffset": 3018, "count": 0}], "isBlockCoverage": true}, {"functionName": "Hmac", "ranges": [{"startOffset": 3211, "endOffset": 3611, "count": 0}], "isBlockCoverage": false}, {"functionName": "digest", "ranges": [{"startOffset": 3792, "endOffset": 4224, "count": 0}], "isBlockCoverage": false}, {"functionName": "asyncDigest", "ranges": [{"startOffset": 4379, "endOffset": 4850, "count": 0}], "isBlockCoverage": false}, {"functionName": "hash", "ranges": [{"startOffset": 4852, "endOffset": 5884, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "119", "url": "node:internal/crypto/x509", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9909, "count": 1}], "isBlockCoverage": false}, {"functionName": "isX509Certificate", "ranges": [{"startOffset": 1155, "endOffset": 1238, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFlags", "ranges": [{"startOffset": 1240, "endOffset": 2485, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2487, "endOffset": 2649, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2651, "endOffset": 9663, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "120", "url": "node:internal/crypto/certificate", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1546, "count": 1}], "isBlockCoverage": false}, {"functionName": "verifySpkac", "ranges": [{"startOffset": 385, "endOffset": 505, "count": 0}], "isBlockCoverage": false}, {"functionName": "exportPublicKey", "ranges": [{"startOffset": 507, "endOffset": 635, "count": 0}], "isBlockCoverage": false}, {"functionName": "exportChallenge", "ranges": [{"startOffset": 637, "endOffset": 765, "count": 0}], "isBlockCoverage": false}, {"functionName": "Certificate", "ranges": [{"startOffset": 1122, "endOffset": 1216, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "121", "url": "node:child_process", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27267, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1936, "endOffset": 1965, "count": 1}], "isBlockCoverage": true}, {"functionName": "fork", "ranges": [{"startOffset": 3430, "endOffset": 5105, "count": 0}], "isBlockCoverage": false}, {"functionName": "_fork<PERSON><PERSON>d", "ranges": [{"startOffset": 5107, "endOffset": 5596, "count": 0}], "isBlockCoverage": false}, {"functionName": "normalizeExecArgs", "ranges": [{"startOffset": 5598, "endOffset": 6112, "count": 0}], "isBlockCoverage": false}, {"functionName": "exec", "ranges": [{"startOffset": 6649, "endOffset": 6896, "count": 0}], "isBlockCoverage": false}, {"functionName": "customPromiseExecFunction", "ranges": [{"startOffset": 6932, "endOffset": 7296, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6953, "endOffset": 7293, "count": 0}], "isBlockCoverage": false}, {"functionName": "normalizeExecFileArgs", "ranges": [{"startOffset": 7433, "endOffset": 8331, "count": 0}], "isBlockCoverage": false}, {"functionName": "execFile", "ranges": [{"startOffset": 8944, "endOffset": 13708, "count": 0}], "isBlockCoverage": false}, {"functionName": "copyProcessEnvToEnv", "ranges": [{"startOffset": 13852, "endOffset": 14052, "count": 1}, {"startOffset": 13950, "endOffset": 14007, "count": 0}], "isBlockCoverage": true}, {"functionName": "normalizeSpawnArguments", "ranges": [{"startOffset": 14054, "endOffset": 19366, "count": 1}, {"startOffset": 14215, "endOffset": 14280, "count": 0}, {"startOffset": 14351, "endOffset": 14542, "count": 0}, {"startOffset": 14622, "endOffset": 14645, "count": 0}, {"startOffset": 14819, "endOffset": 14872, "count": 0}, {"startOffset": 14942, "endOffset": 15006, "count": 0}, {"startOffset": 15069, "endOffset": 15093, "count": 0}, {"startOffset": 15095, "endOffset": 15173, "count": 0}, {"startOffset": 15236, "endOffset": 15260, "count": 0}, {"startOffset": 15262, "endOffset": 15340, "count": 0}, {"startOffset": 15407, "endOffset": 15450, "count": 0}, {"startOffset": 15451, "endOffset": 15493, "count": 0}, {"startOffset": 15495, "endOffset": 15626, "count": 0}, {"startOffset": 15690, "endOffset": 15810, "count": 0}, {"startOffset": 15886, "endOffset": 15956, "count": 0}, {"startOffset": 16096, "endOffset": 16204, "count": 0}, {"startOffset": 16227, "endOffset": 17089, "count": 0}, {"startOffset": 17132, "endOffset": 17185, "count": 0}, {"startOffset": 17530, "endOffset": 18134, "count": 0}, {"startOffset": 18231, "endOffset": 18274, "count": 111}, {"startOffset": 18312, "endOffset": 18777, "count": 0}, {"startOffset": 18808, "endOffset": 19061, "count": 111}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18556, "endOffset": 18765, "count": 0}], "isBlockCoverage": false}, {"functionName": "abortChildProcess", "ranges": [{"startOffset": 19368, "endOffset": 19624, "count": 0}], "isBlockCoverage": false}, {"functionName": "spawn", "ranges": [{"startOffset": 20179, "endOffset": 21403, "count": 1}, {"startOffset": 20545, "endOffset": 20919, "count": 0}, {"startOffset": 20943, "endOffset": 21384, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20578, "endOffset": 20766, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20810, "endOffset": 20913, "count": 0}], "isBlockCoverage": false}, {"functionName": "onAbortListener", "ranges": [{"startOffset": 21279, "endOffset": 21380, "count": 0}], "isBlockCoverage": false}, {"functionName": "spawnSync", "ranges": [{"startOffset": 22163, "endOffset": 23671, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkExecSyncError", "ranges": [{"startOffset": 23674, "endOffset": 24060, "count": 0}], "isBlockCoverage": false}, {"functionName": "execFileSync", "ranges": [{"startOffset": 24573, "endOffset": 25037, "count": 0}], "isBlockCoverage": false}, {"functionName": "execSync", "ranges": [{"startOffset": 25535, "endOffset": 25911, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateArgument<PERSON>ull<PERSON>heck", "ranges": [{"startOffset": 25914, "endOffset": 26137, "count": 230}, {"startOffset": 26039, "endOffset": 26135, "count": 0}], "isBlockCoverage": true}, {"functionName": "validateArgumentsNullCheck", "ranges": [{"startOffset": 26140, "endOffset": 26302, "count": 1}, {"startOffset": 26234, "endOffset": 26300, "count": 7}], "isBlockCoverage": true}, {"functionName": "validateTimeout", "ranges": [{"startOffset": 26305, "endOffset": 26493, "count": 1}, {"startOffset": 26363, "endOffset": 26409, "count": 0}, {"startOffset": 26411, "endOffset": 26491, "count": 0}], "isBlockCoverage": true}, {"functionName": "validateMax<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 26496, "endOffset": 26769, "count": 0}], "isBlockCoverage": false}, {"functionName": "sanitizeKillSignal", "ranges": [{"startOffset": 26772, "endOffset": 27135, "count": 1}, {"startOffset": 26886, "endOffset": 26936, "count": 0}, {"startOffset": 26966, "endOffset": 27133, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "122", "url": "node:internal/child_process", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 31799, "count": 1}], "isBlockCoverage": false}, {"functionName": "send", "ranges": [{"startOffset": 2498, "endOffset": 2557, "count": 0}], "isBlockCoverage": false}, {"functionName": "got", "ranges": [{"startOffset": 2564, "endOffset": 2618, "count": 0}], "isBlockCoverage": false}, {"functionName": "send", "ranges": [{"startOffset": 2680, "endOffset": 2747, "count": 0}], "isBlockCoverage": false}, {"functionName": "got", "ranges": [{"startOffset": 2754, "endOffset": 2895, "count": 0}], "isBlockCoverage": false}, {"functionName": "send", "ranges": [{"startOffset": 2925, "endOffset": 4479, "count": 0}], "isBlockCoverage": false}, {"functionName": "postSend", "ranges": [{"startOffset": 4486, "endOffset": 5232, "count": 0}], "isBlockCoverage": false}, {"functionName": "got", "ranges": [{"startOffset": 5239, "endOffset": 5696, "count": 0}], "isBlockCoverage": false}, {"functionName": "send", "ranges": [{"startOffset": 5761, "endOffset": 5820, "count": 0}], "isBlockCoverage": false}, {"functionName": "got", "ranges": [{"startOffset": 5827, "endOffset": 5881, "count": 0}], "isBlockCoverage": false}, {"functionName": "send", "ranges": [{"startOffset": 5946, "endOffset": 6066, "count": 0}], "isBlockCoverage": false}, {"functionName": "got", "ranges": [{"startOffset": 6073, "endOffset": 6232, "count": 0}], "isBlockCoverage": false}, {"functionName": "stdioStringToArray", "ranges": [{"startOffset": 6243, "endOffset": 6658, "count": 1}, {"startOffset": 6335, "endOffset": 6349, "count": 0}, {"startOffset": 6354, "endOffset": 6372, "count": 0}, {"startOffset": 6377, "endOffset": 6446, "count": 0}, {"startOffset": 6516, "endOffset": 6579, "count": 0}, {"startOffset": 6600, "endOffset": 6637, "count": 0}], "isBlockCoverage": true}, {"functionName": "ChildProcess", "ranges": [{"startOffset": 6660, "endOffset": 8153, "count": 1}, {"startOffset": 8083, "endOffset": 8151, "count": 0}], "isBlockCoverage": true}, {"functionName": "ChildProcess._handle.onexit", "ranges": [{"startOffset": 7001, "endOffset": 8039, "count": 1}, {"startOffset": 7049, "endOffset": 7092, "count": 0}, {"startOffset": 7159, "endOffset": 7194, "count": 0}, {"startOffset": 7270, "endOffset": 7566, "count": 0}], "isBlockCoverage": true}, {"functionName": "flushStdio", "ranges": [{"startOffset": 8276, "endOffset": 8754, "count": 1}, {"startOffset": 8366, "endOffset": 8373, "count": 0}, {"startOffset": 8416, "endOffset": 8752, "count": 3}, {"startOffset": 8657, "endOffset": 8676, "count": 0}, {"startOffset": 8677, "endOffset": 8702, "count": 0}, {"startOffset": 8727, "endOffset": 8752, "count": 0}], "isBlockCoverage": true}, {"functionName": "createSocket", "ranges": [{"startOffset": 8757, "endOffset": 8847, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHandleWrapType", "ranges": [{"startOffset": 8850, "endOffset": 9079, "count": 0}], "isBlockCoverage": false}, {"functionName": "closePending<PERSON><PERSON>le", "ranges": [{"startOffset": 9081, "endOffset": 9194, "count": 0}], "isBlockCoverage": false}, {"functionName": "ChildProcess.spawn", "ranges": [{"startOffset": 9228, "endOffset": 12799, "count": 1}, {"startOffset": 9379, "endOffset": 9388, "count": 0}, {"startOffset": 9723, "endOffset": 10108, "count": 0}, {"startOffset": 10226, "endOffset": 10256, "count": 0}, {"startOffset": 10597, "endOffset": 10854, "count": 0}, {"startOffset": 10869, "endOffset": 11160, "count": 0}, {"startOffset": 11281, "endOffset": 12195, "count": 3}, {"startOffset": 11346, "endOffset": 11355, "count": 0}, {"startOffset": 11377, "endOffset": 11428, "count": 0}, {"startOffset": 11649, "endOffset": 11911, "count": 0}, {"startOffset": 11936, "endOffset": 12191, "count": 0}, {"startOffset": 12263, "endOffset": 12284, "count": 0}, {"startOffset": 12360, "endOffset": 12381, "count": 0}, {"startOffset": 12457, "endOffset": 12478, "count": 0}, {"startOffset": 12549, "endOffset": 12659, "count": 3}, {"startOffset": 12640, "endOffset": 12657, "count": 0}, {"startOffset": 12743, "endOffset": 12782, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12130, "endOffset": 12175, "count": 0}], "isBlockCoverage": false}, {"functionName": "onErrorNT", "ranges": [{"startOffset": 12803, "endOffset": 12864, "count": 0}], "isBlockCoverage": false}, {"functionName": "onSpawnNT", "ranges": [{"startOffset": 12867, "endOffset": 12917, "count": 1}], "isBlockCoverage": true}, {"functionName": "ChildProcess.kill", "ranges": [{"startOffset": 12950, "endOffset": 13623, "count": 0}], "isBlockCoverage": false}, {"functionName": "ChildProcess.<computed>", "ranges": [{"startOffset": 13666, "endOffset": 13723, "count": 0}], "isBlockCoverage": false}, {"functionName": "ChildProcess.ref", "ranges": [{"startOffset": 13756, "endOffset": 13810, "count": 0}], "isBlockCoverage": false}, {"functionName": "ChildProcess.unref", "ranges": [{"startOffset": 13845, "endOffset": 13901, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 13904, "endOffset": 14901, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupChannel", "ranges": [{"startOffset": 15049, "endOffset": 27173, "count": 0}], "isBlockCoverage": false}, {"functionName": "isInternal", "ranges": [{"startOffset": 27208, "endOffset": 27518, "count": 0}], "isBlockCoverage": false}, {"functionName": "getValidStdio", "ranges": [{"startOffset": 27552, "endOffset": 30477, "count": 1}, {"startOffset": 27728, "endOffset": 27818, "count": 0}, {"startOffset": 28062, "endOffset": 28099, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28212, "endOffset": 30436, "count": 3}, {"startOffset": 28470, "endOffset": 28518, "count": 0}, {"startOffset": 28548, "endOffset": 28606, "count": 0}, {"startOffset": 28719, "endOffset": 28973, "count": 0}, {"startOffset": 29000, "endOffset": 29394, "count": 0}, {"startOffset": 29425, "endOffset": 29514, "count": 0}, {"startOffset": 29550, "endOffset": 29581, "count": 0}, {"startOffset": 29683, "endOffset": 29693, "count": 0}, {"startOffset": 29710, "endOffset": 30415, "count": 0}], "isBlockCoverage": true}, {"functionName": "cleanup", "ranges": [{"startOffset": 28237, "endOffset": 28429, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSocketList", "ranges": [{"startOffset": 30480, "endOffset": 30796, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeClose", "ranges": [{"startOffset": 30799, "endOffset": 31000, "count": 1}], "isBlockCoverage": true}, {"functionName": "spawnSync", "ranges": [{"startOffset": 31002, "endOffset": 31673, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "123", "url": "node:net", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 68429, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1645, "endOffset": 1670, "count": 0}], "isBlockCoverage": false}, {"functionName": "noop", "ranges": [{"startOffset": 4172, "endOffset": 4180, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFlags", "ranges": [{"startOffset": 4653, "endOffset": 4747, "count": 0}], "isBlockCoverage": false}, {"functionName": "createHandle", "ranges": [{"startOffset": 4749, "endOffset": 5132, "count": 0}], "isBlockCoverage": false}, {"functionName": "getNewAsyncId", "ranges": [{"startOffset": 5135, "endOffset": 5273, "count": 0}], "isBlockCoverage": false}, {"functionName": "isPipeName", "ranges": [{"startOffset": 5276, "endOffset": 5359, "count": 0}], "isBlockCoverage": false}, {"functionName": "createServer", "ranges": [{"startOffset": 5560, "endOffset": 5664, "count": 0}], "isBlockCoverage": false}, {"functionName": "connect", "ranges": [{"startOffset": 5887, "endOffset": 6288, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDefaultAutoSelectFamily", "ranges": [{"startOffset": 6290, "endOffset": 6365, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDefaultAutoSelectFamily", "ranges": [{"startOffset": 6367, "endOffset": 6483, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDefaultAutoSelectFamilyAttemptTimeout", "ranges": [{"startOffset": 6485, "endOffset": 6588, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDefaultAutoSelectFamilyAttemptTimeout", "ranges": [{"startOffset": 6590, "endOffset": 6777, "count": 0}], "isBlockCoverage": false}, {"functionName": "normalizeArgs", "ranges": [{"startOffset": 7271, "endOffset": 7997, "count": 0}], "isBlockCoverage": false}, {"functionName": "initSocketHandle", "ranges": [{"startOffset": 8069, "endOffset": 8672, "count": 0}], "isBlockCoverage": false}, {"functionName": "closeSocketHandle", "ranges": [{"startOffset": 8674, "endOffset": 9014, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket", "ranges": [{"startOffset": 9266, "endOffset": 13893, "count": 0}], "isBlockCoverage": false}, {"functionName": "_unrefTimer", "ranges": [{"startOffset": 14066, "endOffset": 14196, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._final", "ranges": [{"startOffset": 14316, "endOffset": 14936, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterShutdown", "ranges": [{"startOffset": 14939, "endOffset": 15085, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeAfterFIN", "ranges": [{"startOffset": 15292, "endOffset": 15800, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._onTimeout", "ranges": [{"startOffset": 15882, "endOffset": 16386, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setNoDelay", "ranges": [{"startOffset": 16420, "endOffset": 16797, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setKeepAlive", "ranges": [{"startOffset": 16833, "endOffset": 17439, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.address", "ranges": [{"startOffset": 17470, "endOffset": 17514, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 17600, "endOffset": 17644, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 17724, "endOffset": 17780, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 17891, "endOffset": 18206, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 18295, "endOffset": 18375, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 18463, "endOffset": 18508, "count": 0}], "isBlockCoverage": false}, {"functionName": "tryReadStart", "ranges": [{"startOffset": 18516, "endOffset": 18766, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._read", "ranges": [{"startOffset": 18858, "endOffset": 19177, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.end", "ranges": [{"startOffset": 19204, "endOffset": 19360, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.resetAndDestroy", "ranges": [{"startOffset": 19398, "endOffset": 19748, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.pause", "ranges": [{"startOffset": 19776, "endOffset": 20114, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.resume", "ranges": [{"startOffset": 20144, "endOffset": 20328, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.read", "ranges": [{"startOffset": 20356, "endOffset": 20542, "count": 0}], "isBlockCoverage": false}, {"functionName": "onReadableStreamEnd", "ranges": [{"startOffset": 20589, "endOffset": 20688, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.destroySoon", "ranges": [{"startOffset": 20722, "endOffset": 20869, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._destroy", "ranges": [{"startOffset": 20901, "endOffset": 22415, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._reset", "ranges": [{"startOffset": 22444, "endOffset": 22543, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._getpeername", "ranges": [{"startOffset": 22578, "endOffset": 22875, "count": 0}], "isBlockCoverage": false}, {"functionName": "protoGetter", "ranges": [{"startOffset": 22878, "endOffset": 23060, "count": 9}], "isBlockCoverage": true}, {"functionName": "bytesRead", "ranges": [{"startOffset": 23087, "endOffset": 23178, "count": 0}], "isBlockCoverage": false}, {"functionName": "remoteAddress", "ranges": [{"startOffset": 23211, "endOffset": 23277, "count": 0}], "isBlockCoverage": false}, {"functionName": "remoteFamily", "ranges": [{"startOffset": 23309, "endOffset": 23373, "count": 0}], "isBlockCoverage": false}, {"functionName": "remotePort", "ranges": [{"startOffset": 23403, "endOffset": 23463, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._getsockname", "ranges": [{"startOffset": 23500, "endOffset": 23776, "count": 0}], "isBlockCoverage": false}, {"functionName": "localAddress", "ranges": [{"startOffset": 23808, "endOffset": 23873, "count": 0}], "isBlockCoverage": false}, {"functionName": "localPort", "ranges": [{"startOffset": 23903, "endOffset": 23962, "count": 0}], "isBlockCoverage": false}, {"functionName": "localFamily", "ranges": [{"startOffset": 23993, "endOffset": 24056, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.<computed>", "ranges": [{"startOffset": 24097, "endOffset": 24144, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._writeGeneric", "ranges": [{"startOffset": 24180, "endOffset": 25092, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._writev", "ranges": [{"startOffset": 25123, "endOffset": 25191, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket._write", "ranges": [{"startOffset": 25221, "endOffset": 25302, "count": 0}], "isBlockCoverage": false}, {"functionName": "_bytesDispatched", "ranges": [{"startOffset": 25495, "endOffset": 25599, "count": 0}], "isBlockCoverage": false}, {"functionName": "bytes<PERSON>ritten", "ranges": [{"startOffset": 25631, "endOffset": 26560, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkBindError", "ranges": [{"startOffset": 26565, "endOffset": 27364, "count": 0}], "isBlockCoverage": false}, {"functionName": "internalConnect", "ranges": [{"startOffset": 27367, "endOffset": 29467, "count": 0}], "isBlockCoverage": false}, {"functionName": "internalConnectMultiple", "ranges": [{"startOffset": 29470, "endOffset": 32410, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.connect", "ranges": [{"startOffset": 32439, "endOffset": 34035, "count": 0}], "isBlockCoverage": false}, {"functionName": "reinitializeHandle", "ranges": [{"startOffset": 34078, "endOffset": 34232, "count": 0}], "isBlockCoverage": false}, {"functionName": "socketToDnsFamily", "ranges": [{"startOffset": 34235, "endOffset": 34381, "count": 0}], "isBlockCoverage": false}, {"functionName": "lookupAndConnect", "ranges": [{"startOffset": 34383, "endOffset": 38314, "count": 0}], "isBlockCoverage": false}, {"functionName": "lookupAndConnectMultiple", "ranges": [{"startOffset": 38316, "endOffset": 42415, "count": 0}], "isBlockCoverage": false}, {"functionName": "connectErrorNT", "ranges": [{"startOffset": 42417, "endOffset": 42476, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.ref", "ranges": [{"startOffset": 42502, "endOffset": 42689, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.unref", "ranges": [{"startOffset": 42718, "endOffset": 42911, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterConnect", "ranges": [{"startOffset": 42915, "endOffset": 44603, "count": 0}], "isBlockCoverage": false}, {"functionName": "addClientAbortSignalOption", "ranges": [{"startOffset": 44605, "endOffset": 45015, "count": 0}], "isBlockCoverage": false}, {"functionName": "createConnectionError", "ranges": [{"startOffset": 45017, "endOffset": 45538, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterConnectMultiple", "ranges": [{"startOffset": 45540, "endOffset": 46858, "count": 0}], "isBlockCoverage": false}, {"functionName": "internalConnectMultipleTimeout", "ranges": [{"startOffset": 46860, "endOffset": 47356, "count": 0}], "isBlockCoverage": false}, {"functionName": "addServerAbortSignalOption", "ranges": [{"startOffset": 47358, "endOffset": 47788, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server", "ranges": [{"startOffset": 47790, "endOffset": 49402, "count": 0}], "isBlockCoverage": false}, {"functionName": "toNumber", "ranges": [{"startOffset": 49513, "endOffset": 49578, "count": 0}], "isBlockCoverage": false}, {"functionName": "createServerHandle", "ranges": [{"startOffset": 49646, "endOffset": 51098, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupListenHandle", "ranges": [{"startOffset": 51100, "endOffset": 53794, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitErrorNT", "ranges": [{"startOffset": 53861, "endOffset": 53923, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitListeningNT", "ranges": [{"startOffset": 53926, "endOffset": 54041, "count": 0}], "isBlockCoverage": false}, {"functionName": "listenInCluster", "ranges": [{"startOffset": 54044, "endOffset": 55626, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.listen", "ranges": [{"startOffset": 55655, "endOffset": 60017, "count": 0}], "isBlockCoverage": false}, {"functionName": "lookupAndListen", "ranges": [{"startOffset": 60020, "endOffset": 60563, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 60645, "endOffset": 60688, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.address", "ranges": [{"startOffset": 60764, "endOffset": 61057, "count": 0}], "isBlockCoverage": false}, {"functionName": "onconnection", "ranges": [{"startOffset": 61060, "endOffset": 62963, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.getConnections", "ranges": [{"startOffset": 63112, "endOffset": 63865, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.close", "ranges": [{"startOffset": 63894, "endOffset": 64784, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.<computed>", "ranges": [{"startOffset": 64826, "endOffset": 64946, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server._emitCloseIfDrained", "ranges": [{"startOffset": 64988, "endOffset": 65374, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitCloseNT", "ranges": [{"startOffset": 65378, "endOffset": 65461, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.<computed>", "ranges": [{"startOffset": 65520, "endOffset": 65682, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 65904, "endOffset": 65940, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 65944, "endOffset": 65985, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 66065, "endOffset": 66096, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 66100, "endOffset": 66136, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server._setupWorker", "ranges": [{"startOffset": 66175, "endOffset": 66416, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.ref", "ranges": [{"startOffset": 66442, "endOffset": 66540, "count": 0}], "isBlockCoverage": false}, {"functionName": "Server.unref", "ranges": [{"startOffset": 66568, "endOffset": 66667, "count": 0}], "isBlockCoverage": false}, {"functionName": "_setSimultaneousAccepts", "ranges": [{"startOffset": 66809, "endOffset": 67478, "count": 0}], "isBlockCoverage": false}, {"functionName": "_setSimultaneousAccepts", "ranges": [{"startOffset": 67517, "endOffset": 67758, "count": 0}], "isBlockCoverage": false}, {"functionName": "get BlockList", "ranges": [{"startOffset": 67887, "endOffset": 67989, "count": 0}], "isBlockCoverage": false}, {"functionName": "get <PERSON>cket<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 67993, "endOffset": 68115, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "124", "url": "node:internal/stream_base_commons", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7170, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1057, "endOffset": 1082, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleWriteReq", "ranges": [{"startOffset": 1201, "endOffset": 2056, "count": 0}], "isBlockCoverage": false}, {"functionName": "onWriteComplete", "ranges": [{"startOffset": 2058, "endOffset": 2747, "count": 0}], "isBlockCoverage": false}, {"functionName": "createWriteWrap", "ranges": [{"startOffset": 2749, "endOffset": 2987, "count": 0}], "isBlockCoverage": false}, {"functionName": "writevGeneric", "ranges": [{"startOffset": 2989, "endOffset": 3628, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeGeneric", "ranges": [{"startOffset": 3630, "endOffset": 3835, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterWriteDispatched", "ranges": [{"startOffset": 3837, "endOffset": 4144, "count": 0}], "isBlockCoverage": false}, {"functionName": "onStreamRead", "ranges": [{"startOffset": 4146, "endOffset": 6085, "count": 0}], "isBlockCoverage": false}, {"functionName": "setStreamTimeout", "ranges": [{"startOffset": 6087, "endOffset": 6963, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "125", "url": "node:diagnostics_channel", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10086, "count": 1}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 808, "endOffset": 1217, "count": 1}], "isBlockCoverage": false}, {"functionName": "#finalizers", "ranges": [{"startOffset": 888, "endOffset": 924, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 930, "endOffset": 1047, "count": 9}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1051, "endOffset": 1099, "count": 9}, {"startOffset": 1087, "endOffset": 1092, "count": 0}], "isBlockCoverage": true}, {"functionName": "incRef", "ranges": [{"startOffset": 1103, "endOffset": 1157, "count": 0}], "isBlockCoverage": false}, {"functionName": "decRef", "ranges": [{"startOffset": 1161, "endOffset": 1215, "count": 0}], "isBlockCoverage": false}, {"functionName": "markActive", "ranges": [{"startOffset": 1219, "endOffset": 1424, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeMarkInactive", "ranges": [{"startOffset": 1426, "endOffset": 1794, "count": 0}], "isBlockCoverage": false}, {"functionName": "defaultTransform", "ranges": [{"startOffset": 1796, "endOffset": 1846, "count": 0}], "isBlockCoverage": false}, {"functionName": "wrapStoreRun", "ranges": [{"startOffset": 1848, "endOffset": 2176, "count": 0}], "isBlockCoverage": false}, {"functionName": "subscribe", "ranges": [{"startOffset": 2258, "endOffset": 2492, "count": 0}], "isBlockCoverage": false}, {"functionName": "unsubscribe", "ranges": [{"startOffset": 2496, "endOffset": 2943, "count": 0}], "isBlockCoverage": false}, {"functionName": "bindStore", "ranges": [{"startOffset": 2947, "endOffset": 3115, "count": 0}], "isBlockCoverage": false}, {"functionName": "unbindStore", "ranges": [{"startOffset": 3119, "endOffset": 3318, "count": 0}], "isBlockCoverage": false}, {"functionName": "get hasSubscribers", "ranges": [{"startOffset": 3322, "endOffset": 3365, "count": 0}], "isBlockCoverage": false}, {"functionName": "publish", "ranges": [{"startOffset": 3369, "endOffset": 3709, "count": 0}], "isBlockCoverage": false}, {"functionName": "runStores", "ranges": [{"startOffset": 3713, "endOffset": 4052, "count": 0}], "isBlockCoverage": false}, {"functionName": "Channel", "ranges": [{"startOffset": 4074, "endOffset": 4215, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4226, "endOffset": 4411, "count": 0}], "isBlockCoverage": false}, {"functionName": "subscribe", "ranges": [{"startOffset": 4415, "endOffset": 4500, "count": 0}], "isBlockCoverage": false}, {"functionName": "unsubscribe", "ranges": [{"startOffset": 4504, "endOffset": 4541, "count": 0}], "isBlockCoverage": false}, {"functionName": "bindStore", "ranges": [{"startOffset": 4545, "endOffset": 4638, "count": 0}], "isBlockCoverage": false}, {"functionName": "unbindStore", "ranges": [{"startOffset": 4642, "endOffset": 4679, "count": 0}], "isBlockCoverage": false}, {"functionName": "get hasSubscribers", "ranges": [{"startOffset": 4683, "endOffset": 4727, "count": 1}], "isBlockCoverage": true}, {"functionName": "publish", "ranges": [{"startOffset": 4731, "endOffset": 4743, "count": 0}], "isBlockCoverage": false}, {"functionName": "runStores", "ranges": [{"startOffset": 4747, "endOffset": 4834, "count": 0}], "isBlockCoverage": false}, {"functionName": "channel", "ranges": [{"startOffset": 4874, "endOffset": 5140, "count": 9}, {"startOffset": 4952, "endOffset": 4967, "count": 0}, {"startOffset": 5000, "endOffset": 5027, "count": 0}, {"startOffset": 5029, "endOffset": 5109, "count": 0}], "isBlockCoverage": true}, {"functionName": "subscribe", "ranges": [{"startOffset": 5142, "endOffset": 5232, "count": 0}], "isBlockCoverage": false}, {"functionName": "unsubscribe", "ranges": [{"startOffset": 5234, "endOffset": 5328, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasSubscribers", "ranges": [{"startOffset": 5330, "endOffset": 5465, "count": 0}], "isBlockCoverage": false}, {"functionName": "assertChannel", "ranges": [{"startOffset": 5554, "endOffset": 5696, "count": 0}], "isBlockCoverage": false}, {"functionName": "tracingChannelFrom", "ranges": [{"startOffset": 5698, "endOffset": 6223, "count": 5}, {"startOffset": 5854, "endOffset": 5923, "count": 0}, {"startOffset": 5925, "endOffset": 6222, "count": 0}], "isBlockCoverage": true}, {"functionName": "TracingChannel", "ranges": [{"startOffset": 6250, "endOffset": 6475, "count": 1}, {"startOffset": 6321, "endOffset": 6471, "count": 5}], "isBlockCoverage": true}, {"functionName": "get hasSubscribers", "ranges": [{"startOffset": 6479, "endOffset": 6689, "count": 0}], "isBlockCoverage": false}, {"functionName": "subscribe", "ranges": [{"startOffset": 6693, "endOffset": 6845, "count": 0}], "isBlockCoverage": false}, {"functionName": "unsubscribe", "ranges": [{"startOffset": 6849, "endOffset": 7082, "count": 0}], "isBlockCoverage": false}, {"functionName": "traceSync", "ranges": [{"startOffset": 7086, "endOffset": 7597, "count": 0}], "isBlockCoverage": false}, {"functionName": "tracePromise", "ranges": [{"startOffset": 7601, "endOffset": 8787, "count": 0}], "isBlockCoverage": false}, {"functionName": "trace<PERSON><PERSON>back", "ranges": [{"startOffset": 8791, "endOffset": 9884, "count": 0}], "isBlockCoverage": false}, {"functionName": "tracingChannel", "ranges": [{"startOffset": 9888, "endOffset": 9976, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "126", "url": "node:internal/perf/observe", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16722, "count": 1}], "isBlockCoverage": false}, {"functionName": "queuePending", "ranges": [{"startOffset": 2600, "endOffset": 2861, "count": 0}], "isBlockCoverage": false}, {"functionName": "getObserverType", "ranges": [{"startOffset": 2863, "endOffset": 3203, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeDecrementObserverCounts", "ranges": [{"startOffset": 3205, "endOffset": 3626, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeIncrementObserverCount", "ranges": [{"startOffset": 3628, "endOffset": 3970, "count": 0}], "isBlockCoverage": false}, {"functionName": "performanceObserverSorter", "ranges": [{"startOffset": 4047, "endOffset": 4114, "count": 0}], "isBlockCoverage": false}, {"functionName": "PerformanceObserverEntryList", "ranges": [{"startOffset": 4156, "endOffset": 4384, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEntries", "ranges": [{"startOffset": 4388, "endOffset": 4527, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEntriesByType", "ranges": [{"startOffset": 4531, "endOffset": 4835, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEntriesByName", "ranges": [{"startOffset": 4839, "endOffset": 5332, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5336, "endOffset": 5584, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 5936, "endOffset": 9533, "count": 0}], "isBlockCoverage": false}, {"functionName": "enqueue", "ranges": [{"startOffset": 10015, "endOffset": 10217, "count": 0}], "isBlockCoverage": false}, {"functionName": "bufferUserTiming", "ranges": [{"startOffset": 10278, "endOffset": 11304, "count": 0}], "isBlockCoverage": false}, {"functionName": "bufferResourceTiming", "ranges": [{"startOffset": 11592, "endOffset": 12856, "count": 0}], "isBlockCoverage": false}, {"functionName": "setResourceTimingBufferSize", "ranges": [{"startOffset": 12944, "endOffset": 13216, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDispatchBufferFull", "ranges": [{"startOffset": 13218, "endOffset": 13283, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearEntriesFromBuffer", "ranges": [{"startOffset": 13285, "endOffset": 13885, "count": 0}], "isBlockCoverage": false}, {"functionName": "filterBufferMapByNameAndType", "ranges": [{"startOffset": 13887, "endOffset": 14729, "count": 0}], "isBlockCoverage": false}, {"functionName": "observerCallback", "ranges": [{"startOffset": 14731, "endOffset": 15847, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasObserver", "ranges": [{"startOffset": 15884, "endOffset": 16003, "count": 0}], "isBlockCoverage": false}, {"functionName": "startPerf", "ranges": [{"startOffset": 16006, "endOffset": 16116, "count": 0}], "isBlockCoverage": false}, {"functionName": "stopPerf", "ranges": [{"startOffset": 16118, "endOffset": 16433, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "127", "url": "node:internal/perf/performance_entry", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3272, "count": 1}], "isBlockCoverage": false}, {"functionName": "isPerformanceEntry", "ranges": [{"startOffset": 675, "endOffset": 748, "count": 0}], "isBlockCoverage": false}, {"functionName": "PerformanceEntry", "ranges": [{"startOffset": 777, "endOffset": 1132, "count": 0}], "isBlockCoverage": false}, {"functionName": "get name", "ranges": [{"startOffset": 1136, "endOffset": 1236, "count": 0}], "isBlockCoverage": false}, {"functionName": "get entryType", "ranges": [{"startOffset": 1240, "endOffset": 1355, "count": 0}], "isBlockCoverage": false}, {"functionName": "get startTime", "ranges": [{"startOffset": 1359, "endOffset": 1474, "count": 0}], "isBlockCoverage": false}, {"functionName": "get duration", "ranges": [{"startOffset": 1478, "endOffset": 1590, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1594, "endOffset": 1838, "count": 0}], "isBlockCoverage": false}, {"functionName": "toJSON", "ranges": [{"startOffset": 1842, "endOffset": 2064, "count": 0}], "isBlockCoverage": false}, {"functionName": "createPerformanceEntry", "ranges": [{"startOffset": 2286, "endOffset": 2422, "count": 0}], "isBlockCoverage": false}, {"functionName": "get detail", "ranges": [{"startOffset": 2539, "endOffset": 2649, "count": 0}], "isBlockCoverage": false}, {"functionName": "toJSON", "ranges": [{"startOffset": 2653, "endOffset": 2904, "count": 0}], "isBlockCoverage": false}, {"functionName": "createPerformanceNodeEntry", "ranges": [{"startOffset": 2908, "endOffset": 3112, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "128", "url": "node:dgram", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28668, "count": 1}], "isBlockCoverage": false}, {"functionName": "lazyLoadCluster", "ranges": [{"startOffset": 2910, "endOffset": 3007, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket", "ranges": [{"startOffset": 3073, "endOffset": 4712, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSocket", "ranges": [{"startOffset": 4823, "endOffset": 4901, "count": 0}], "isBlockCoverage": false}, {"functionName": "startListening", "ranges": [{"startOffset": 4904, "endOffset": 5347, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 5349, "endOffset": 5914, "count": 0}], "isBlockCoverage": false}, {"functionName": "bufferSize", "ranges": [{"startOffset": 5916, "endOffset": 6208, "count": 0}], "isBlockCoverage": false}, {"functionName": "bindServerHandle", "ranges": [{"startOffset": 6276, "endOffset": 6772, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.bind", "ranges": [{"startOffset": 6798, "endOffset": 10253, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.connect", "ranges": [{"startOffset": 10283, "endOffset": 11079, "count": 0}], "isBlockCoverage": false}, {"functionName": "_connect", "ranges": [{"startOffset": 11083, "endOffset": 11429, "count": 0}], "isBlockCoverage": false}, {"functionName": "doConnect", "ranges": [{"startOffset": 11432, "endOffset": 12078, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.disconnect", "ranges": [{"startOffset": 12111, "endOffset": 12422, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.sendto", "ranges": [{"startOffset": 12527, "endOffset": 12968, "count": 0}], "isBlockCoverage": false}, {"functionName": "sliceBuffer", "ranges": [{"startOffset": 12972, "endOffset": 13732, "count": 0}], "isBlockCoverage": false}, {"functionName": "fixBufferList", "ranges": [{"startOffset": 13735, "endOffset": 14119, "count": 0}], "isBlockCoverage": false}, {"functionName": "enqueue", "ranges": [{"startOffset": 14122, "endOffset": 14549, "count": 0}], "isBlockCoverage": false}, {"functionName": "onListenSuccess", "ranges": [{"startOffset": 14552, "endOffset": 14690, "count": 0}], "isBlockCoverage": false}, {"functionName": "onListenError", "ranges": [{"startOffset": 14693, "endOffset": 14817, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearQueue", "ranges": [{"startOffset": 14820, "endOffset": 15017, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.send", "ranges": [{"startOffset": 15588, "endOffset": 18481, "count": 0}], "isBlockCoverage": false}, {"functionName": "doSend", "ranges": [{"startOffset": 18484, "endOffset": 19627, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterSend", "ranges": [{"startOffset": 19629, "endOffset": 19810, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.close", "ranges": [{"startOffset": 19837, "endOffset": 20405, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.<computed>", "ranges": [{"startOffset": 20447, "endOffset": 20580, "count": 0}], "isBlockCoverage": false}, {"functionName": "socketCloseNT", "ranges": [{"startOffset": 20584, "endOffset": 20638, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.address", "ranges": [{"startOffset": 20668, "endOffset": 20862, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.remoteAddress", "ranges": [{"startOffset": 20898, "endOffset": 21212, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setBroadcast", "ranges": [{"startOffset": 21248, "endOffset": 21400, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setTTL", "ranges": [{"startOffset": 21430, "endOffset": 21608, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setMulticastTTL", "ranges": [{"startOffset": 21647, "endOffset": 21843, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setMulticastLoopback", "ranges": [{"startOffset": 21887, "endOffset": 22091, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setMulticastInterface", "ranges": [{"startOffset": 22136, "endOffset": 22402, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.addMembership", "ranges": [{"startOffset": 22438, "endOffset": 22816, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.dropMembership", "ranges": [{"startOffset": 22854, "endOffset": 23235, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.addSourceSpecificMembership", "ranges": [{"startOffset": 23285, "endOffset": 23896, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.dropSourceSpecificMembership", "ranges": [{"startOffset": 23948, "endOffset": 24565, "count": 0}], "isBlockCoverage": false}, {"functionName": "healthCheck", "ranges": [{"startOffset": 24569, "endOffset": 24732, "count": 0}], "isBlockCoverage": false}, {"functionName": "stopReceiving", "ranges": [{"startOffset": 24735, "endOffset": 24899, "count": 0}], "isBlockCoverage": false}, {"functionName": "onMessage", "ranges": [{"startOffset": 24902, "endOffset": 25160, "count": 0}], "isBlockCoverage": false}, {"functionName": "onError", "ranges": [{"startOffset": 25163, "endOffset": 25278, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.ref", "ranges": [{"startOffset": 25304, "endOffset": 25411, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.unref", "ranges": [{"startOffset": 25440, "endOffset": 25549, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setRecvBufferSize", "ranges": [{"startOffset": 25590, "endOffset": 25647, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.setSendBufferSize", "ranges": [{"startOffset": 25688, "endOffset": 25745, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.getRecvBufferSize", "ranges": [{"startOffset": 25786, "endOffset": 25843, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.getSendBufferSize", "ranges": [{"startOffset": 25884, "endOffset": 25941, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.getSendQueueSize", "ranges": [{"startOffset": 25980, "endOffset": 26049, "count": 0}], "isBlockCoverage": false}, {"functionName": "Socket.getSendQueueCount", "ranges": [{"startOffset": 26089, "endOffset": 26159, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26278, "endOffset": 26332, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26405, "endOffset": 26461, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26614, "endOffset": 26671, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26747, "endOffset": 26806, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26962, "endOffset": 27019, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27095, "endOffset": 27154, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27306, "endOffset": 27359, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27431, "endOffset": 27486, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27638, "endOffset": 27695, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27771, "endOffset": 27830, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 27937, "endOffset": 27972, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 28081, "endOffset": 28118, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 28403, "endOffset": 28439, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 28443, "endOffset": 28484, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "129", "url": "node:internal/dgram", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1851, "count": 1}], "isBlockCoverage": false}, {"functionName": "lookup4", "ranges": [{"startOffset": 478, "endOffset": 579, "count": 0}], "isBlockCoverage": false}, {"functionName": "lookup6", "ranges": [{"startOffset": 582, "endOffset": 677, "count": 0}], "isBlockCoverage": false}, {"functionName": "newHandle", "ranges": [{"startOffset": 679, "endOffset": 1323, "count": 0}], "isBlockCoverage": false}, {"functionName": "_createSocketHandle", "ranges": [{"startOffset": 1326, "endOffset": 1774, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "130", "url": "node:internal/socket_list", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2709, "count": 1}], "isBlockCoverage": false}, {"functionName": "SocketListSend", "ranges": [{"startOffset": 235, "endOffset": 376, "count": 0}], "isBlockCoverage": false}, {"functionName": "_request", "ranges": [{"startOffset": 380, "endOffset": 1035, "count": 0}], "isBlockCoverage": false}, {"functionName": "close", "ranges": [{"startOffset": 1039, "endOffset": 1190, "count": 0}], "isBlockCoverage": false}, {"functionName": "getConnections", "ranges": [{"startOffset": 1194, "endOffset": 1430, "count": 0}], "isBlockCoverage": false}, {"functionName": "SocketListReceive", "ranges": [{"startOffset": 1544, "endOffset": 2404, "count": 0}], "isBlockCoverage": false}, {"functionName": "add", "ranges": [{"startOffset": 2408, "endOffset": 2649, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "131", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/v8flags/config-path.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 842, "count": 1}], "isBlockCoverage": true}, {"functionName": "macos", "ranges": [{"startOffset": 148, "endOffset": 261, "count": 0}], "isBlockCoverage": false}, {"functionName": "windows", "ranges": [{"startOffset": 263, "endOffset": 397, "count": 0}], "isBlockCoverage": false}, {"functionName": "linux", "ranges": [{"startOffset": 479, "endOffset": 620, "count": 1}], "isBlockCoverage": true}, {"functionName": "module.exports", "ranges": [{"startOffset": 639, "endOffset": 840, "count": 1}, {"startOffset": 677, "endOffset": 706, "count": 0}, {"startOffset": 737, "endOffset": 762, "count": 0}, {"startOffset": 792, "endOffset": 819, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "132", "url": "file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/homedir-polyfill/index.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 168, "count": 1}, {"startOffset": 112, "endOffset": 166, "count": 0}], "isBlockCoverage": true}]}], "timestamp": 20083.840721, "source-map-cache": {"file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/@babel/node/lib/babel-node.js": {"lineLengths": [13, 0, 34, 28, 46, 26, 311, 804, 265, 389, 68, 38, 13, 45, 24, 43, 47, 1, 35, 45, 16, 48, 3, 13, 1, 53, 3, 47, 136, 3, 38, 46, 29, 35, 5, 26, 32, 17, 34, 36, 17, 7, 5, 48, 22, 32, 101, 24, 12, 21, 5, 3, 26, 27, 3, 7, 11, 20, 86, 33, 17, 121, 16, 5, 60, 62, 88, 7, 45, 38, 21, 44, 16, 61, 9, 9, 7, 31, 59, 5, 52, 54, 3, 4, 0, 38, 0], "data": {"version": 3, "names": ["_v8flags", "require", "_path", "_child_process", "_url", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "_interopRequireWildcard", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "asyncGeneratorStep", "gen", "resolve", "reject", "_next", "_throw", "arg", "info", "value", "error", "done", "Promise", "then", "_asyncToGenerator", "fn", "self", "args", "arguments", "apply", "err", "undefined", "path", "join", "dirname", "__filename", "babelA<PERSON><PERSON>", "process", "argv", "slice", "userArgs", "argSeparator", "indexOf", "getNormalizedV8Flag", "matches", "match", "replace", "getV8Flags", "v8Flags", "map", "allowedNodeEnvironmentFlags", "for<PERSON>ach", "flag", "push", "v8FlagsSet", "Set", "i", "length", "split", "unshift", "kexec", "code", "shouldPassthroughIPC", "send", "proc", "child_process", "spawn", "stdio", "on", "signal", "kill", "pid", "exitCode", "message"], "sources": ["file:///home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/web/node_modules/@babel/node/src/babel-node.ts"], "sourcesContent": ["/**\n * This tiny wrapper file checks for known node flags and appends them\n * when found, before invoking the \"real\" _babel-node(1) executable.\n */\n\nimport getV8Flags from \"v8flags\";\nimport path from \"path\";\nimport child_process from \"child_process\";\nimport { fileURLToPath } from \"url\";\n\nconst args = [\n  path.join(path.dirname(fileURLToPath(import.meta.url)), \"_babel-node\"),\n];\n\nlet babelArgs = process.argv.slice(2);\nlet userArgs: string[];\n\n// separate node arguments from script arguments\nconst argSeparator = babelArgs.indexOf(\"--\");\nif (argSeparator > -1) {\n  userArgs = babelArgs.slice(argSeparator); // including the  --\n  babelArgs = babelArgs.slice(0, argSeparator);\n}\n\n/**\n * Replace underscores with dashes in the v8Flag name\n * Also ensure that if the arg contains a value (e.g. --arg=true)\n * that only the flag is returned.\n */\nfunction getNormalizedV8Flag(arg: string) {\n  // v8 uses the \"no\" prefix to negate boolean flags (e.g. --nolazy),\n  // but they are not listed by v8flags\n  const matches = arg.match(/--(?:no)?(.+)/);\n\n  if (matches) {\n    return `--${matches[1].replace(/_/g, \"-\")}`;\n  }\n\n  return arg;\n}\n\ngetV8Flags(async function (err, v8Flags) {\n  if (!process.env.BABEL_8_BREAKING) {\n    // The version of v8flags used by Babel 7 uses _, while the one used\n    // by Babel 8 used -. Normalize the flags accordingly.\n    v8Flags = v8Flags.map(getNormalizedV8Flag);\n    process.allowedNodeEnvironmentFlags.forEach(flag =>\n      v8Flags.push(getNormalizedV8Flag(flag)),\n    );\n  }\n\n  const v8FlagsSet = new Set(v8Flags);\n\n  for (let i = 0; i < babelArgs.length; i++) {\n    const arg = babelArgs[i];\n    const flag = arg.split(\"=\")[0];\n\n    if (!process.env.BABEL_8_BREAKING) {\n      if (flag === \"-d\") {\n        args.unshift(\"--debug\");\n        continue;\n      } else if (flag === \"-gc\") {\n        args.unshift(\"--expose-gc\");\n        continue;\n      }\n    }\n    if (flag === \"-r\" || flag === \"--require\") {\n      args.push(flag);\n      args.push(babelArgs[++i]);\n    } else if (\n      flag === \"debug\" || // node debug foo.js\n      flag === \"inspect\" ||\n      v8FlagsSet.has(getNormalizedV8Flag(flag))\n    ) {\n      args.unshift(arg);\n    } else {\n      args.push(arg);\n    }\n  }\n\n  // append arguments passed after --\n  if (argSeparator > -1) {\n    args.push(...userArgs);\n  }\n\n  try {\n    // eslint-disable-next-line import/no-unresolved\n    const { default: kexec } = await import(\"kexec\");\n    kexec(process.argv[0], args);\n  } catch (err) {\n    if (\n      err.code !== \"ERR_MODULE_NOT_FOUND\" &&\n      err.code !== \"MODULE_NOT_FOUND\" &&\n      err.code !== \"UNDECLARED_DEPENDENCY\"\n    ) {\n      throw err;\n    }\n\n    // passthrough IPC only if babel-node itself has an IPC channel\n    const shouldPassthroughIPC = process.send !== undefined;\n    const proc = child_process.spawn(process.argv[0], args, {\n      stdio: shouldPassthroughIPC\n        ? [\"inherit\", \"inherit\", \"inherit\", \"ipc\"]\n        : \"inherit\",\n    });\n    proc.on(\"exit\", function (code, signal) {\n      process.on(\"exit\", function () {\n        if (signal) {\n          process.kill(process.pid, signal);\n        } else {\n          process.exitCode = code ?? undefined;\n        }\n      });\n    });\n    if (shouldPassthroughIPC) {\n      proc.on(\"message\", message => process.send(message));\n    }\n    process.on(\"SIGINT\", () => proc.kill(\"SIGINT\"));\n    process.on(\"SIGTERM\", () => proc.kill(\"SIGTERM\"));\n  }\n});\n"], "mappings": ";;AAKA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AACA,IAAAG,IAAA,GAAAH,OAAA;AAAoC,SAAAI,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAI,wBAAAC,GAAA,EAAAL,WAAA,SAAAA,WAAA,IAAAK,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAT,wBAAA,CAAAC,WAAA,OAAAQ,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAAA,SAAAW,mBAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,EAAAX,GAAA,EAAAY,GAAA,cAAAC,IAAA,GAAAN,GAAA,CAAAP,GAAA,EAAAY,GAAA,OAAAE,KAAA,GAAAD,IAAA,CAAAC,KAAA,WAAAC,KAAA,IAAAN,MAAA,CAAAM,KAAA,iBAAAF,IAAA,CAAAG,IAAA,IAAAR,OAAA,CAAAM,KAAA,YAAAG,OAAA,CAAAT,OAAA,CAAAM,KAAA,EAAAI,IAAA,CAAAR,KAAA,EAAAC,MAAA;AAAA,SAAAQ,kBAAAC,EAAA,6BAAAC,IAAA,SAAAC,IAAA,GAAAC,SAAA,aAAAN,OAAA,WAAAT,OAAA,EAAAC,MAAA,QAAAF,GAAA,GAAAa,EAAA,CAAAI,KAAA,CAAAH,IAAA,EAAAC,IAAA,YAAAZ,MAAAI,KAAA,IAAAR,kBAAA,CAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,UAAAG,KAAA,cAAAH,OAAAc,GAAA,IAAAnB,kBAAA,CAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,WAAAc,GAAA,KAAAf,KAAA,CAAAgB,SAAA;AAEpC,MAAMJ,IAAI,GAAG,CACXK,KAAI,CAACC,IAAI,CAACD,KAAI,CAACE,OAAO,CAAAC,UAA+B,CAAC,EAAE,aAAa,CAAC,CACvE;AAED,IAAIC,SAAS,GAAGC,OAAO,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC;AACrC,IAAIC,QAAkB;AAGtB,MAAMC,YAAY,GAAGL,SAAS,CAACM,OAAO,CAAC,IAAI,CAAC;AAC5C,IAAID,YAAY,GAAG,CAAC,CAAC,EAAE;EACrBD,QAAQ,GAAGJ,SAAS,CAACG,KAAK,CAACE,YAAY,CAAC;EACxCL,SAAS,GAAGA,SAAS,CAACG,KAAK,CAAC,CAAC,EAAEE,YAAY,CAAC;AAC9C;AAOA,SAASE,mBAAmBA,CAAC1B,GAAW,EAAE;EAGxC,MAAM2B,OAAO,GAAG3B,GAAG,CAAC4B,KAAK,CAAC,eAAe,CAAC;EAE1C,IAAID,OAAO,EAAE;IACX,OAAQ,KAAIA,OAAO,CAAC,CAAC,CAAC,CAACE,OAAO,CAAC,IAAI,EAAE,GAAG,CAAE,EAAC;EAC7C;EAEA,OAAO7B,GAAG;AACZ;AAEA8B,QAAU,CAAAvB,iBAAA,CAAC,WAAgBM,GAAG,EAAEkB,OAAO,EAAE;EACJ;IAGjCA,OAAO,GAAGA,OAAO,CAACC,GAAG,CAACN,mBAAmB,CAAC;IAC1C,CAAAN,OAAA,CAAAa,2BAAA,IAAAlE,OAAA,4BAAoCmE,OAAO,CAACC,IAAI,IAC9CJ,OAAO,CAACK,IAAI,CAACV,mBAAmB,CAACS,IAAI,CAAC,CACxC,CAAC;EACH;EAEA,MAAME,UAAU,GAAG,IAAIC,GAAG,CAACP,OAAO,CAAC;EAEnC,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,SAAS,CAACqB,MAAM,EAAED,CAAC,EAAE,EAAE;IACzC,MAAMvC,GAAG,GAAGmB,SAAS,CAACoB,CAAC,CAAC;IACxB,MAAMJ,IAAI,GAAGnC,GAAG,CAACyC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEK;MACjC,IAAIN,IAAI,KAAK,IAAI,EAAE;QACjBzB,IAAI,CAACgC,OAAO,CAAC,SAAS,CAAC;QACvB;MACF,CAAC,MAAM,IAAIP,IAAI,KAAK,KAAK,EAAE;QACzBzB,IAAI,CAACgC,OAAO,CAAC,aAAa,CAAC;QAC3B;MACF;IACF;IACA,IAAIP,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,WAAW,EAAE;MACzCzB,IAAI,CAAC0B,IAAI,CAACD,IAAI,CAAC;MACfzB,IAAI,CAAC0B,IAAI,CAACjB,SAAS,CAAC,EAAEoB,CAAC,CAAC,CAAC;IAC3B,CAAC,MAAM,IACLJ,IAAI,KAAK,OAAO,IAChBA,IAAI,KAAK,SAAS,IAClBE,UAAU,CAACxD,GAAG,CAAC6C,mBAAmB,CAACS,IAAI,CAAC,CAAC,EACzC;MACAzB,IAAI,CAACgC,OAAO,CAAC1C,GAAG,CAAC;IACnB,CAAC,MAAM;MACLU,IAAI,CAAC0B,IAAI,CAACpC,GAAG,CAAC;IAChB;EACF;EAGA,IAAIwB,YAAY,GAAG,CAAC,CAAC,EAAE;IACrBd,IAAI,CAAC0B,IAAI,CAAC,GAAGb,QAAQ,CAAC;EACxB;EAEA,IAAI;IAEF,MAAM;MAAE5C,OAAO,EAAEgE;IAAM,CAAC,SAAAtC,OAAA,CAAAT,OAAA,GAAAU,IAAA,OAAA9B,uBAAA,CAAAT,OAAA,CAAgB,OAAO,GAAC;IAChD4E,KAAK,CAACvB,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEX,IAAI,CAAC;EAC9B,CAAC,CAAC,OAAOG,GAAG,EAAE;IACZ,IACEA,GAAG,CAAC+B,IAAI,KAAK,sBAAsB,IACnC/B,GAAG,CAAC+B,IAAI,KAAK,kBAAkB,IAC/B/B,GAAG,CAAC+B,IAAI,KAAK,uBAAuB,EACpC;MACA,MAAM/B,GAAG;IACX;IAGA,MAAMgC,oBAAoB,GAAGzB,OAAO,CAAC0B,IAAI,KAAKhC,SAAS;IACvD,MAAMiC,IAAI,GAAGC,cAAa,CAACC,KAAK,CAAC7B,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEX,IAAI,EAAE;MACtDwC,KAAK,EAAEL,oBAAoB,GACvB,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,GACxC;IACN,CAAC,CAAC;IACFE,IAAI,CAACI,EAAE,CAAC,MAAM,EAAE,UAAUP,IAAI,EAAEQ,MAAM,EAAE;MACtChC,OAAO,CAAC+B,EAAE,CAAC,MAAM,EAAE,YAAY;QAC7B,IAAIC,MAAM,EAAE;UACVhC,OAAO,CAACiC,IAAI,CAACjC,OAAO,CAACkC,GAAG,EAAEF,MAAM,CAAC;QACnC,CAAC,MAAM;UACLhC,OAAO,CAACmC,QAAQ,GAAGX,IAAI,WAAJA,IAAI,GAAI9B,SAAS;QACtC;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI+B,oBAAoB,EAAE;MACxBE,IAAI,CAACI,EAAE,CAAC,SAAS,EAAEK,OAAO,IAAIpC,OAAO,CAAC0B,IAAI,CAACU,OAAO,CAAC,CAAC;IACtD;IACApC,OAAO,CAAC+B,EAAE,CAAC,QAAQ,EAAE,MAAMJ,IAAI,CAACM,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC/CjC,OAAO,CAAC+B,EAAE,CAAC,SAAS,EAAE,MAAMJ,IAAI,CAACM,IAAI,CAAC,SAAS,CAAC,CAAC;EACnD;AACF,CAAC,EAAC", "sourceRoot": ""}, "url": null}}}