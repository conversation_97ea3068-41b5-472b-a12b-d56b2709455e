{"name": "portal-fuxi-assets", "version": "1.0.0", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "portal-fuxi-assets", "version": "1.0.0", "license": "ISC", "dependencies": {"basepack": "^1.14.1", "cors": "^2.8.5", "express": "^4.17.1", "express-http-proxy": "^1.6.0", "fuxi": "^2.15.2", "fuxi-nossis": "^3.0.0", "http-server": "^0.12.3"}}, "node_modules/@fortawesome/fontawesome-free": {"version": "5.15.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/@fortawesome/fontawesome-free/-/fontawesome-free-5.15.4.tgz", "integrity": "sha512-eYm8vijH/hpzr/6/1CJ/V/Eb1xQFW2nnUKArb3z+yUWv7HTwj6M7SP957oMjfZjAHU6qpoNc2wQvIxBLWYa/Jg==", "hasInstallScript": true, "license": "(CC-BY-4.0 AND OFL-1.1 AND MIT)", "engines": {"node": ">=6"}}, "node_modules/abbrev": {"version": "1.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/abbrev/-/abbrev-1.1.1.tgz", "integrity": "sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==", "license": "ISC"}, "node_modules/accepts": {"version": "1.3.7", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/accepts/-/accepts-1.3.7.tgz", "integrity": "sha1-UxvHJlF6OytB+FACHGzBXqq1B80=", "license": "MIT", "dependencies": {"mime-types": "~2.1.24", "negotiator": "0.6.2"}, "engines": {"node": ">= 0.6"}}, "node_modules/array-each": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/array-each/-/array-each-1.0.1.tgz", "integrity": "sha512-zHjL5SZa68hkKHBFBK6DJCTtr9sfTCPCaph/L7tMSLcTFgy+zX7E+6q5UArbtOtMBCtxdICpfTCspRse+ywyXA==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/array-flatten": {"version": "1.1.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/array-flatten/-/array-flatten-1.1.1.tgz", "integrity": "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=", "license": "MIT"}, "node_modules/array-slice": {"version": "1.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/array-slice/-/array-slice-1.1.0.tgz", "integrity": "sha512-B1qMD3RBP7O8o0H2KbrXDyB0IccejMF15+87Lvlor12ONPRHP6gTjXMNkt/d3ZuOGbAe66hFmaCfECI24Ufp6w==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/async": {"version": "2.6.3", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/async/-/async-2.6.3.tgz", "integrity": "sha1-1yYl4jRKNlbjo61Pp0n6gymdgv8=", "license": "MIT", "dependencies": {"lodash": "^4.17.14"}}, "node_modules/basepack": {"version": "1.14.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/basepack/-/basepack-1.14.1.tgz", "integrity": "sha512-26MpRYEYx9tej+YYZSzJEOyEHMwRLfXPpTvca7/NYDjjo61l7kwwRk6xabadAyvXNA0QtKrl0cmGDbDLv7Zd2Q==", "license": "Altice Labs License Terms and Conditions (see http://www.alticelabs.com/licenses/)"}, "node_modules/basic-auth": {"version": "1.1.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/basic-auth/-/basic-auth-1.1.0.tgz", "integrity": "sha1-RSIe5Cn37h5QNb4/UVM/HN/SmIQ=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/body-parser": {"version": "1.19.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/body-parser/-/body-parser-1.19.0.tgz", "integrity": "sha1-lrJwnlfJxOCab9Zqj9l5hE9p8Io=", "license": "MIT", "dependencies": {"bytes": "3.1.0", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "~1.1.2", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "on-finished": "~2.3.0", "qs": "6.7.0", "raw-body": "2.4.0", "type-is": "~1.6.17"}, "engines": {"node": ">= 0.8"}}, "node_modules/braces": {"version": "3.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/braces/-/braces-3.0.2.tgz", "integrity": "sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==", "license": "MIT", "dependencies": {"fill-range": "^7.0.1"}, "engines": {"node": ">=8"}}, "node_modules/bytes": {"version": "3.1.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/bytes/-/bytes-3.1.0.tgz", "integrity": "sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/colors": {"version": "1.4.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/colors/-/colors-1.4.0.tgz", "integrity": "sha1-xQSRR51MG9rtLJztMs98fcI2D3g=", "license": "MIT", "engines": {"node": ">=0.1.90"}}, "node_modules/content-disposition": {"version": "0.5.3", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/content-disposition/-/content-disposition-0.5.3.tgz", "integrity": "sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70=", "license": "MIT", "dependencies": {"safe-buffer": "5.1.2"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.4", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/content-type/-/content-type-1.0.4.tgz", "integrity": "sha1-4TjMdeBAxyexlm/l5fjJruJW/js=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie": {"version": "0.4.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/cookie/-/cookie-0.4.0.tgz", "integrity": "sha1-vrQ35wIrO21JAZ0IhmUwPr6cFLo=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.0.6", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw=", "license": "MIT"}, "node_modules/cors": {"version": "2.8.5", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/cors/-/cors-2.8.5.tgz", "integrity": "sha1-6sEdpRWS3Ya58G9uesKTs9+HXSk=", "license": "MIT", "dependencies": {"object-assign": "^4", "vary": "^1"}, "engines": {"node": ">= 0.10"}}, "node_modules/corser": {"version": "2.0.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/corser/-/corser-2.0.1.tgz", "integrity": "sha1-jtolLsqrWEDc2XXOuQ2TcMgZ/4c=", "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/debug": {"version": "2.6.9", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/depd": {"version": "1.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/depd/-/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/destroy": {"version": "1.0.4", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/destroy/-/destroy-1.0.4.tgz", "integrity": "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=", "license": "MIT"}, "node_modules/detect-file": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/detect-file/-/detect-file-1.0.0.tgz", "integrity": "sha512-DtCOLG98P007x7wiiOmfI0fi3eIKyWiLTGJ2MDnVi/E04lWGbf+JzrRHMm0rgIIZJGtHpKpbVgLWHrv8xXpc3Q==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/ecstatic": {"version": "3.3.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ecstatic/-/ecstatic-3.3.2.tgz", "integrity": "sha1-bR3UmBTQBZRoLGUq22YHamnUbEg=", "deprecated": "This package is unmaintained and deprecated. See the GH Issue 259.", "license": "MIT", "dependencies": {"he": "^1.1.1", "mime": "^1.6.0", "minimist": "^1.1.0", "url-join": "^2.0.5"}, "bin": {"ecstatic": "lib/ecstatic.js"}}, "node_modules/ee-first": {"version": "1.1.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=", "license": "MIT"}, "node_modules/encodeurl": {"version": "1.0.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/encodeurl/-/encodeurl-1.0.2.tgz", "integrity": "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/es6-promise": {"version": "4.2.8", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/es6-promise/-/es6-promise-4.2.8.tgz", "integrity": "sha1-TrIVlMlyvEBVPSduUQU5FD21Pgo=", "license": "MIT"}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=", "license": "MIT"}, "node_modules/etag": {"version": "1.8.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/etag/-/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/eventemitter3": {"version": "4.0.4", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/eventemitter3/-/eventemitter3-4.0.4.tgz", "integrity": "sha1-tUY6zmNaCD0Bi9x8kXtMXxCoU4Q=", "license": "MIT"}, "node_modules/expand-tilde": {"version": "2.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/expand-tilde/-/expand-tilde-2.0.2.tgz", "integrity": "sha512-A5EmesHW6rfnZ9ysHQjPdJRni0SRar0tjtG5MNtm9n5TUvsYU8oozprtRD4AqHxcZWWlVuAmQo2nWKfN9oyjTw==", "license": "MIT", "dependencies": {"homedir-polyfill": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/express": {"version": "4.17.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/express/-/express-4.17.1.tgz", "integrity": "sha1-RJH8OGBc9R+GKdOcK10Cb5ikwTQ=", "license": "MIT", "dependencies": {"accepts": "~1.3.7", "array-flatten": "1.1.1", "body-parser": "1.19.0", "content-disposition": "0.5.3", "content-type": "~1.0.4", "cookie": "0.4.0", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "~1.1.2", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "~1.1.2", "fresh": "0.5.2", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.5", "qs": "6.7.0", "range-parser": "~1.2.1", "safe-buffer": "5.1.2", "send": "0.17.1", "serve-static": "1.14.1", "setprototypeof": "1.1.1", "statuses": "~1.5.0", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/express-http-proxy": {"version": "1.6.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/express-http-proxy/-/express-http-proxy-1.6.0.tgz", "integrity": "sha1-hnKxCTzJa4qT6OPalI3REaZo7yI=", "license": "MIT", "dependencies": {"debug": "^3.0.1", "es6-promise": "^4.1.1", "raw-body": "^2.3.0"}, "engines": {"node": ">=6.0.0"}}, "node_modules/express-http-proxy/node_modules/debug": {"version": "3.2.6", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/debug/-/debug-3.2.6.tgz", "integrity": "sha1-6D0X3hbYp++3cX7b5fsQE17uYps=", "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/express-http-proxy/node_modules/ms": {"version": "2.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=", "license": "MIT"}, "node_modules/extend": {"version": "3.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/extend/-/extend-3.0.2.tgz", "integrity": "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==", "license": "MIT"}, "node_modules/fill-range": {"version": "7.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fill-range/-/fill-range-7.0.1.tgz", "integrity": "sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==", "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/finalhandler": {"version": "1.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/finalhandler/-/finalhandler-1.1.2.tgz", "integrity": "sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0=", "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "statuses": "~1.5.0", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/findup-sync": {"version": "4.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/findup-sync/-/findup-sync-4.0.0.tgz", "integrity": "sha512-6jvvn/12IC4quLBL1KNokxC7wWTvYncaVUYSoxWw7YykPLuRrnv4qdHcSOywOI5RpkOVGeQRtWM8/q+G6W6qfQ==", "license": "MIT", "dependencies": {"detect-file": "^1.0.0", "is-glob": "^4.0.0", "micromatch": "^4.0.2", "resolve-dir": "^1.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/fined": {"version": "1.2.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fined/-/fined-1.2.0.tgz", "integrity": "sha512-ZYDqPLGxDkDhDZBjZBb+oD1+j0rA4E0pXY50eplAAOPg2N/gUBSSk5IM1/QhPfyVo19lJ+CvXpqfvk+b2p/8Ng==", "license": "MIT", "dependencies": {"expand-tilde": "^2.0.2", "is-plain-object": "^2.0.3", "object.defaults": "^1.1.0", "object.pick": "^1.2.0", "parse-filepath": "^1.0.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/flagged-respawn": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/flagged-respawn/-/flagged-respawn-1.0.1.tgz", "integrity": "sha512-lNaHNVymajmk0OJMBn8fVUAU1BtDeKIqKoVhk4xAALB57aALg6b4W0MfJ/cUE0g9YBXy5XhSlPIpYIJ7HaY/3Q==", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/follow-redirects": {"version": "1.12.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/follow-redirects/-/follow-redirects-1.12.1.tgz", "integrity": "sha1-3lSmIFMRuT1gOY68Ac9wFWgjErY=", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}}, "node_modules/font-awesome": {"version": "4.7.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/font-awesome/-/font-awesome-4.7.0.tgz", "integrity": "sha512-U6kGnykA/6bFmg1M/oT9EkFeIYv7JlX3bozwQJWiiLz6L0w3F5vBVPxHlwyX/vtNq1ckcpRKOB9f2Qal/VtFpg==", "license": "(OFL-1.1 AND MIT)", "engines": {"node": ">=0.10.3"}}, "node_modules/for-in": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/for-in/-/for-in-1.0.2.tgz", "integrity": "sha512-7EwmXrOjyL+ChxMhmG5lnW9MPt1aIeZEwKhQzoBUdTV0N3zuwWDZYVJatDvZ2OyzPUvdIAZDsCetk3coyMfcnQ==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/for-own": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/for-own/-/for-own-1.0.0.tgz", "integrity": "sha512-0OABksIGrxKK8K4kynWkQ7y1zounQxP+CWnyclVwj81KW3vlLlGUx57DKGcP/LH216GzqnstnPocF16Nxs0Ycg==", "license": "MIT", "dependencies": {"for-in": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/forwarded": {"version": "0.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/forwarded/-/forwarded-0.1.2.tgz", "integrity": "sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fresh": {"version": "0.5.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/fresh/-/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/fuxi": {"version": "2.15.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fuxi/-/fuxi-2.15.2.tgz", "integrity": "sha512-2/65BARFRxhMl3FkVmlnb6q5tz4TPDS1N+7I9wXmO4Sgx/BkMstZnk7EySbdYhGzOxQ+zIHCy23fqc2PMhn/Aw==", "license": "SEE LICENSE IN LICENSE", "dependencies": {"@fortawesome/fontawesome-free": "^5.13.0", "font-awesome": "^4.7.0", "fuxicons": "^1.0.1", "grunt-cli": "^1.4.3"}}, "node_modules/fuxi-nossis": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fuxi-nossis/-/fuxi-nossis-3.0.0.tgz", "integrity": "sha512-I8dTFpYuL+pZHyaCtepVfBjWR5Mb09M2F7uiP0Qm5cRBlSrsHSTKpzinMBdV9Av4zxi09zyP6boNOFvCXOHzLg==", "license": "Altice Labs License Terms and Conditions (see http://www.alticelabs.com/licenses/)"}, "node_modules/fuxicons": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fuxicons/-/fuxicons-1.0.1.tgz", "integrity": "sha1-fzH+J+9cxGOzr0lwQpIkEUCIQgc=", "license": "ISC"}, "node_modules/global-modules": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/global-modules/-/global-modules-1.0.0.tgz", "integrity": "sha512-sKzpEkf11GpOFuw0Zzjzmt4B4UZwjOcG757PPvrfhxcLFbq0wpsgpOqxpxtxFiCG4DtG93M6XRVbF2oGdev7bg==", "license": "MIT", "dependencies": {"global-prefix": "^1.0.1", "is-windows": "^1.0.1", "resolve-dir": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/global-prefix": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/global-prefix/-/global-prefix-1.0.2.tgz", "integrity": "sha512-5lsx1NUDHtSjfg0eHlmYvZKv8/nVqX4ckFbM+FrGcQ+04KWcWFo9P5MxPZYSzUvyzmdTbI7Eix8Q4IbELDqzKg==", "license": "MIT", "dependencies": {"expand-tilde": "^2.0.2", "homedir-polyfill": "^1.0.1", "ini": "^1.3.4", "is-windows": "^1.0.1", "which": "^1.2.14"}, "engines": {"node": ">=0.10.0"}}, "node_modules/global-prefix/node_modules/which": {"version": "1.3.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/which/-/which-1.3.1.tgz", "integrity": "sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/grunt-cli": {"version": "1.4.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/grunt-cli/-/grunt-cli-1.4.3.tgz", "integrity": "sha512-9Dtx/AhVeB4LYzsViCjUQkd0Kw0McN2gYpdmGYKtE2a5Yt7v1Q+HYZVWhqXc/kGnxlMtqKDxSwotiGeFmkrCoQ==", "license": "MIT", "dependencies": {"grunt-known-options": "~2.0.0", "interpret": "~1.1.0", "liftup": "~3.0.1", "nopt": "~4.0.1", "v8flags": "~3.2.0"}, "bin": {"grunt": "bin/grunt"}, "engines": {"node": ">=10"}}, "node_modules/grunt-known-options": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/grunt-known-options/-/grunt-known-options-2.0.0.tgz", "integrity": "sha512-GD7cTz0I4SAede1/+pAbmJRG44zFLPipVtdL9o3vqx9IEyb7b4/Y3s7r6ofI3CchR5GvYJ+8buCSioDv5dQLiA==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/hasown/-/hasown-2.0.2.tgz", "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/he": {"version": "1.2.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/he/-/he-1.2.0.tgz", "integrity": "sha1-hK5l+n6vsWX922FWauFLrwVmTw8=", "license": "MIT", "bin": {"he": "bin/he"}}, "node_modules/homedir-polyfill": {"version": "1.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/homedir-polyfill/-/homedir-polyfill-1.0.3.tgz", "integrity": "sha512-eSmmWE5bZTK2Nou4g0AI3zZ9rswp7GRKoKXS1BLUkvPviOqs4YTN1djQIqrXy9k5gEtdLPy86JjRwsNM9tnDcA==", "license": "MIT", "dependencies": {"parse-passwd": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/http-errors": {"version": "1.7.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/http-errors/-/http-errors-1.7.2.tgz", "integrity": "sha1-T1ApzxMjnzEDblsuVSkrz7zIXI8=", "license": "MIT", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.1", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/http-proxy": {"version": "1.18.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/http-proxy/-/http-proxy-1.18.1.tgz", "integrity": "sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=", "license": "MIT", "dependencies": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/http-server": {"version": "0.12.3", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/http-server/-/http-server-0.12.3.tgz", "integrity": "sha1-ugRx0OzEJYhmFss1xPryeRQKDTc=", "license": "MIT", "dependencies": {"basic-auth": "^1.0.3", "colors": "^1.4.0", "corser": "^2.0.1", "ecstatic": "^3.3.2", "http-proxy": "^1.18.0", "minimist": "^1.2.5", "opener": "^1.5.1", "portfinder": "^1.0.25", "secure-compare": "3.0.1", "union": "~0.5.0"}, "bin": {"hs": "bin/http-server", "http-server": "bin/http-server"}, "engines": {"node": ">=6"}}, "node_modules/iconv-lite": {"version": "0.4.24", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/inherits": {"version": "2.0.3", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=", "license": "ISC"}, "node_modules/ini": {"version": "1.3.8", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ini/-/ini-1.3.8.tgz", "integrity": "sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==", "license": "ISC"}, "node_modules/interpret": {"version": "1.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/interpret/-/interpret-1.1.0.tgz", "integrity": "sha512-CLM8SNMDu7C5psFCn6Wg/tgpj/bKAg7hc2gWqcuR9OD5Ft9PhBpIu8PLicPeis+xDd6YX2ncI8MCA64I9tftIA==", "license": "MIT"}, "node_modules/ipaddr.js": {"version": "1.9.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "integrity": "sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/is-absolute": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-absolute/-/is-absolute-1.0.0.tgz", "integrity": "sha512-dOWoqflvcydARa360Gvv18DZ/gRuHKi2NU/wU5X1ZFzdYfH29nkiNZsF3mp4OJ3H4yo9Mx8A/uAGNzpzPN3yBA==", "license": "MIT", "dependencies": {"is-relative": "^1.0.0", "is-windows": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-core-module": {"version": "2.13.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-core-module/-/is-core-module-2.13.1.tgz", "integrity": "sha512-hHrIjvZsftOsvKSn2TRYl63zvxsgE0K+0mYMoH6gD4omR5IWB2KynivBQczo3+wF1cCkjzvptnI9Q0sPU66ilw==", "license": "MIT", "dependencies": {"hasown": "^2.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-number": {"version": "7.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-number/-/is-number-7.0.0.tgz", "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==", "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-plain-object": {"version": "2.0.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-plain-object/-/is-plain-object-2.0.4.tgz", "integrity": "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==", "license": "MIT", "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-relative": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-relative/-/is-relative-1.0.0.tgz", "integrity": "sha512-Kw/ReK0iqwKeu0MITLFuj0jbPAmEiOsIwyIXvvbfa6QfmN9pkD1M+8pdk7Rl/dTKbH34/XBFMbgD4iMJhLQbGA==", "license": "MIT", "dependencies": {"is-unc-path": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-unc-path": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-unc-path/-/is-unc-path-1.0.0.tgz", "integrity": "sha512-mrGpVd0fs7WWLfVsStvgF6iEJnbjDFZh9/emhRDcGWTduTfNHd9CHeUwH3gYIjdbwo4On6hunkztwOaAw0yllQ==", "license": "MIT", "dependencies": {"unc-path-regex": "^0.1.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-windows": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-windows/-/is-windows-1.0.2.tgz", "integrity": "sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/isexe": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/isexe/-/isexe-2.0.0.tgz", "integrity": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==", "license": "ISC"}, "node_modules/isobject": {"version": "3.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/isobject/-/isobject-3.0.1.tgz", "integrity": "sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/kind-of": {"version": "6.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/kind-of/-/kind-of-6.0.3.tgz", "integrity": "sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/liftup": {"version": "3.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/liftup/-/liftup-3.0.1.tgz", "integrity": "sha512-yR<PERSON>aiQDizWSzoXk3APcA71eOI/UuhEkNN9DiW2Tt44mhYzX4joFoCZlxsSOF7RyeLlfqzFLQI1ngFq3ggMPhOw==", "license": "MIT", "dependencies": {"extend": "^3.0.2", "findup-sync": "^4.0.0", "fined": "^1.2.0", "flagged-respawn": "^1.0.1", "is-plain-object": "^2.0.4", "object.map": "^1.0.1", "rechoir": "^0.7.0", "resolve": "^1.19.0"}, "engines": {"node": ">=10"}}, "node_modules/lodash": {"version": "4.17.17", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/lodash/-/lodash-4.17.17.tgz", "integrity": "sha1-2QGLOsxXqVydz0pFxrY7h3tsLUU=", "license": "MIT"}, "node_modules/make-iterator": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/make-iterator/-/make-iterator-1.0.1.tgz", "integrity": "sha512-pxiuXh0iVEq7VM7KMIhs5gxsfxCux2URptUQaXo4iZZJxBAzTPOLE2BumO5dbfVYq/hBJFBR/a1mFDmOx5AGmw==", "license": "MIT", "dependencies": {"kind-of": "^6.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/map-cache": {"version": "0.2.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/map-cache/-/map-cache-0.2.2.tgz", "integrity": "sha512-8y/eV9QQZCiyn1SprXSrCmqJN0yNRATe+PO8ztwqrvrbdRLA3eYJF0yaR0YayLWkMbsQSKWS9N2gPcGEc4UsZg==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/media-typer": {"version": "0.3.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/merge-descriptors": {"version": "1.0.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/merge-descriptors/-/merge-descriptors-1.0.1.tgz", "integrity": "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=", "license": "MIT"}, "node_modules/methods": {"version": "1.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/methods/-/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/micromatch": {"version": "4.0.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/micromatch/-/micromatch-4.0.5.tgz", "integrity": "sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==", "license": "MIT", "dependencies": {"braces": "^3.0.2", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime": {"version": "1.6.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/mime/-/mime-1.6.0.tgz", "integrity": "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=", "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.44.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/mime-db/-/mime-db-1.44.0.tgz", "integrity": "sha1-+hHF6wrKEzS0Izy01S8QxaYnL5I=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.27", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/mime-types/-/mime-types-2.1.27.tgz", "integrity": "sha1-R5SfmOJ56lMRn1ci4PNOUpvsAJ8=", "license": "MIT", "dependencies": {"mime-db": "1.44.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/minimist": {"version": "1.2.5", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/minimist/-/minimist-1.2.5.tgz", "integrity": "sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI=", "license": "MIT"}, "node_modules/mkdirp": {"version": "0.5.5", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/mkdirp/-/mkdirp-0.5.5.tgz", "integrity": "sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=", "license": "MIT", "dependencies": {"minimist": "^1.2.5"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/ms": {"version": "2.0.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "license": "MIT"}, "node_modules/negotiator": {"version": "0.6.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/negotiator/-/negotiator-0.6.2.tgz", "integrity": "sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/nopt": {"version": "4.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/nopt/-/nopt-4.0.3.tgz", "integrity": "sha512-CvaGwVMztSMJLOeXPrez7fyfObdZqNUK1cPAEzLHrTybIua9pMdmmPR5YwtfNftIOMv3DPUhFaxsZMNTQO20Kg==", "license": "ISC", "dependencies": {"abbrev": "1", "osenv": "^0.1.4"}, "bin": {"nopt": "bin/nopt.js"}}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object.defaults": {"version": "1.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/object.defaults/-/object.defaults-1.1.0.tgz", "integrity": "sha512-c/K0mw/F11k4dEUBMW8naXUuBuhxRCfG7W+yFy8EcijU/rSmazOUd1XAEEe6bC0OuXY4HUKjTJv7xbxIMqdxrA==", "license": "MIT", "dependencies": {"array-each": "^1.0.1", "array-slice": "^1.0.0", "for-own": "^1.0.0", "isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.map": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/object.map/-/object.map-1.0.1.tgz", "integrity": "sha512-3+mAJu2PLfnSVGHwIWubpOFLscJANBKuB/6A4CxBstc4aqwQY0FWcsppuy4jU5GSB95yES5JHSI+33AWuS4k6w==", "license": "MIT", "dependencies": {"for-own": "^1.0.0", "make-iterator": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.pick": {"version": "1.3.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/object.pick/-/object.pick-1.3.0.tgz", "integrity": "sha512-tqa/UMy/CCoYmj+H5qc07qvSL9dqcs/WZENZ1JbtWBlATP+iVOe778gE6MSijnyCnORzDuX6hU+LA4SZ09YjFQ==", "license": "MIT", "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/on-finished": {"version": "2.3.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/on-finished/-/on-finished-2.3.0.tgz", "integrity": "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/opener": {"version": "1.5.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/opener/-/opener-1.5.1.tgz", "integrity": "sha1-bS8Od/GgrwAyrKcWwsH7uOfoq+0=", "license": "(WTFPL OR MIT)", "bin": {"opener": "bin/opener-bin.js"}}, "node_modules/os-homedir": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/os-homedir/-/os-homedir-1.0.2.tgz", "integrity": "sha512-B5JU3cabzk8c67mRRd3ECmROafjYMXbuzlwtqdM8IbS8ktlTix8aFGb2bAGKrSRIlnfKwovGUUr72JUPyOb6kQ==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/os-tmpdir": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "integrity": "sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/osenv": {"version": "0.1.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/osenv/-/osenv-0.1.5.tgz", "integrity": "sha512-0CWcCECdMVc2Rw3U5w9ZjqX6ga6ubk1xDVKxtBQPK7wis/0F2r9T6k4ydGYhecl7YUBxBVxhL5oisPsNxAPe2g==", "license": "ISC", "dependencies": {"os-homedir": "^1.0.0", "os-tmpdir": "^1.0.0"}}, "node_modules/parse-filepath": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/parse-filepath/-/parse-filepath-1.0.2.tgz", "integrity": "sha512-FwdRXKCohSVeXqwtYonZTXtbGJKrn+HNyWDYVcp5yuJlesTwNH4rsmRZ+GrKAPJ5bLpRxESMeS+Rl0VCHRvB2Q==", "license": "MIT", "dependencies": {"is-absolute": "^1.0.0", "map-cache": "^0.2.0", "path-root": "^0.1.1"}, "engines": {"node": ">=0.8"}}, "node_modules/parse-passwd": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/parse-passwd/-/parse-passwd-1.0.0.tgz", "integrity": "sha512-1Y1A//QUXEZK7YKz+rD9WydcE1+EuPr6ZBgKecAB8tmoW6UFv0NREVJe1p+jRxtThkcbbKkfwIbWJe/IeE6m2Q==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/parseurl": {"version": "1.3.3", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/parseurl/-/parseurl-1.3.3.tgz", "integrity": "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/path-parse": {"version": "1.0.7", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==", "license": "MIT"}, "node_modules/path-root": {"version": "0.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/path-root/-/path-root-0.1.1.tgz", "integrity": "sha512-QLcPegTHF11axjfojBIoDygmS2E3Lf+8+jI6wOVmNVenrKSo3mFdSGiIgdSHenczw3wPtlVMQaFVwGmM7BJdtg==", "license": "MIT", "dependencies": {"path-root-regex": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/path-root-regex": {"version": "0.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/path-root-regex/-/path-root-regex-0.1.2.tgz", "integrity": "sha512-4GlJ6rZDhQZFE0DPVKh0e9jmZ5egZfxTkp7bcRDuPlJXbAwhxcl2dINPUAsjLdejqaLsCeg8axcLjIbvBjN4pQ==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-to-regexp": {"version": "0.1.7", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/path-to-regexp/-/path-to-regexp-0.1.7.tgz", "integrity": "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=", "license": "MIT"}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/portfinder": {"version": "1.0.26", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/portfinder/-/portfinder-1.0.26.tgz", "integrity": "sha1-R1ZY1WyjC+1yrH8TeO01C9G2TnA=", "license": "MIT", "dependencies": {"async": "^2.6.2", "debug": "^3.1.1", "mkdirp": "^0.5.1"}, "engines": {"node": ">= 0.12.0"}}, "node_modules/portfinder/node_modules/debug": {"version": "3.2.6", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/debug/-/debug-3.2.6.tgz", "integrity": "sha1-6D0X3hbYp++3cX7b5fsQE17uYps=", "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/portfinder/node_modules/ms": {"version": "2.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=", "license": "MIT"}, "node_modules/proxy-addr": {"version": "2.0.6", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/proxy-addr/-/proxy-addr-2.0.6.tgz", "integrity": "sha1-/cIzZQVEfT8vLGOO0nLK9hS7sr8=", "license": "MIT", "dependencies": {"forwarded": "~0.1.2", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/qs": {"version": "6.7.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/qs/-/qs-6.7.0.tgz", "integrity": "sha1-QdwaAV49WB8WIXdr4xr7KHapsbw=", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.6"}}, "node_modules/range-parser": {"version": "1.2.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/range-parser/-/range-parser-1.2.1.tgz", "integrity": "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "2.4.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/raw-body/-/raw-body-2.4.0.tgz", "integrity": "sha1-oc5vucm8NWylLoklarWQWeE9AzI=", "license": "MIT", "dependencies": {"bytes": "3.1.0", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/rechoir": {"version": "0.7.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/rechoir/-/rechoir-0.7.1.tgz", "integrity": "sha512-/njmZ8s1wVeR6pjTZ+0nCnv8SpZNRMT2D1RLOJQESlYFDBvwpTA4KWJpZ+sBJ4+vhjILRcK7JIFdGCdxEAAitg==", "license": "MIT", "dependencies": {"resolve": "^1.9.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/requires-port": {"version": "1.0.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/requires-port/-/requires-port-1.0.0.tgz", "integrity": "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=", "license": "MIT"}, "node_modules/resolve": {"version": "1.22.8", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/resolve/-/resolve-1.22.8.tgz", "integrity": "sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==", "license": "MIT", "dependencies": {"is-core-module": "^2.13.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-dir": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/resolve-dir/-/resolve-dir-1.0.1.tgz", "integrity": "sha512-R7uiTjECzvOsWSfdM0QKFNBVFcK27aHOUwdvK53BcW8zqnGdYp0Fbj82cy54+2A4P2tFM22J5kRfe1R+lM/1yg==", "license": "MIT", "dependencies": {"expand-tilde": "^2.0.0", "global-modules": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/safe-buffer": {"version": "5.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0=", "license": "MIT"}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=", "license": "MIT"}, "node_modules/secure-compare": {"version": "3.0.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/secure-compare/-/secure-compare-3.0.1.tgz", "integrity": "sha1-8aAymzCLIh+uN7mXTz1XjQypmeM=", "license": "MIT"}, "node_modules/send": {"version": "0.17.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/send/-/send-0.17.1.tgz", "integrity": "sha1-wdiwWfeQD3Rm3Uk4vcROEd2zdsg=", "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "~1.1.2", "destroy": "~1.0.4", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "~1.7.2", "mime": "1.6.0", "ms": "2.1.1", "on-finished": "~2.3.0", "range-parser": "~1.2.1", "statuses": "~1.5.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/ms": {"version": "2.1.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.1.1.tgz", "integrity": "sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo=", "license": "MIT"}, "node_modules/serve-static": {"version": "1.14.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/serve-static/-/serve-static-1.14.1.tgz", "integrity": "sha1-Zm5jbcTwEPfvKZcKiKZ0MgiYsvk=", "license": "MIT", "dependencies": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.17.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/setprototypeof": {"version": "1.1.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/setprototypeof/-/setprototypeof-1.1.1.tgz", "integrity": "sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM=", "license": "ISC"}, "node_modules/statuses": {"version": "1.5.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/statuses/-/statuses-1.5.0.tgz", "integrity": "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==", "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toidentifier": {"version": "1.0.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/toidentifier/-/toidentifier-1.0.0.tgz", "integrity": "sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM=", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/type-is": {"version": "1.6.18", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/type-is/-/type-is-1.6.18.tgz", "integrity": "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=", "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/unc-path-regex": {"version": "0.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/unc-path-regex/-/unc-path-regex-0.1.2.tgz", "integrity": "sha512-eXL4nmJT7oCpkZsHZUOJo8hcX3GbsiDOa0Qu9F646fi8dT3XuSVopVqAcEiVzSKKH7UoDti23wNX3qGFxcW5Qg==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/union": {"version": "0.5.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/union/-/union-0.5.0.tgz", "integrity": "sha1-ssEb6E9gU4U3uEbtuboma6AJAHU=", "dependencies": {"qs": "^6.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/unpipe": {"version": "1.0.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/url-join": {"version": "2.0.5", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/url-join/-/url-join-2.0.5.tgz", "integrity": "sha1-WvIvGMBSoACkjXuCxenC4v7tpyg=", "license": "MIT"}, "node_modules/utils-merge": {"version": "1.0.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/utils-merge/-/utils-merge-1.0.1.tgz", "integrity": "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=", "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/v8flags": {"version": "3.2.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/v8flags/-/v8flags-3.2.0.tgz", "integrity": "sha512-mH8etigqMfiGWdeXpaaqGfs6BndypxusHHcv2qSHyZkGEznCd/qAXCWWRzeowtL54147cktFOC4P5y+kl8d8Jg==", "license": "MIT", "dependencies": {"homedir-polyfill": "^1.0.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/vary": {"version": "1.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/vary/-/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=", "license": "MIT", "engines": {"node": ">= 0.8"}}}, "dependencies": {"@fortawesome/fontawesome-free": {"version": "5.15.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/@fortawesome/fontawesome-free/-/fontawesome-free-5.15.4.tgz", "integrity": "sha512-eYm8vijH/hpzr/6/1CJ/V/Eb1xQFW2nnUKArb3z+yUWv7HTwj6M7SP957oMjfZjAHU6qpoNc2wQvIxBLWYa/Jg=="}, "abbrev": {"version": "1.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/abbrev/-/abbrev-1.1.1.tgz", "integrity": "sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q=="}, "accepts": {"version": "1.3.7", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/accepts/-/accepts-1.3.7.tgz", "integrity": "sha1-UxvHJlF6OytB+FACHGzBXqq1B80=", "requires": {"mime-types": "~2.1.24", "negotiator": "0.6.2"}}, "array-each": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/array-each/-/array-each-1.0.1.tgz", "integrity": "sha512-zHjL5SZa68hkKHBFBK6DJCTtr9sfTCPCaph/L7tMSLcTFgy+zX7E+6q5UArbtOtMBCtxdICpfTCspRse+ywyXA=="}, "array-flatten": {"version": "1.1.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/array-flatten/-/array-flatten-1.1.1.tgz", "integrity": "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI="}, "array-slice": {"version": "1.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/array-slice/-/array-slice-1.1.0.tgz", "integrity": "sha512-B1qMD3RBP7O8o0H2KbrXDyB0IccejMF15+87Lvlor12ONPRHP6gTjXMNkt/d3ZuOGbAe66hFmaCfECI24Ufp6w=="}, "async": {"version": "2.6.3", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/async/-/async-2.6.3.tgz", "integrity": "sha1-1yYl4jRKNlbjo61Pp0n6gymdgv8=", "requires": {"lodash": "^4.17.14"}}, "basepack": {"version": "1.14.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/basepack/-/basepack-1.14.1.tgz", "integrity": "sha512-26MpRYEYx9tej+YYZSzJEOyEHMwRLfXPpTvca7/NYDjjo61l7kwwRk6xabadAyvXNA0QtKrl0cmGDbDLv7Zd2Q=="}, "basic-auth": {"version": "1.1.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/basic-auth/-/basic-auth-1.1.0.tgz", "integrity": "sha1-RSIe5Cn37h5QNb4/UVM/HN/SmIQ="}, "body-parser": {"version": "1.19.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/body-parser/-/body-parser-1.19.0.tgz", "integrity": "sha1-lrJwnlfJxOCab9Zqj9l5hE9p8Io=", "requires": {"bytes": "3.1.0", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "~1.1.2", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "on-finished": "~2.3.0", "qs": "6.7.0", "raw-body": "2.4.0", "type-is": "~1.6.17"}}, "braces": {"version": "3.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/braces/-/braces-3.0.2.tgz", "integrity": "sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==", "requires": {"fill-range": "^7.0.1"}}, "bytes": {"version": "3.1.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/bytes/-/bytes-3.1.0.tgz", "integrity": "sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY="}, "colors": {"version": "1.4.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/colors/-/colors-1.4.0.tgz", "integrity": "sha1-xQSRR51MG9rtLJztMs98fcI2D3g="}, "content-disposition": {"version": "0.5.3", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/content-disposition/-/content-disposition-0.5.3.tgz", "integrity": "sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70=", "requires": {"safe-buffer": "5.1.2"}}, "content-type": {"version": "1.0.4", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/content-type/-/content-type-1.0.4.tgz", "integrity": "sha1-4TjMdeBAxyexlm/l5fjJruJW/js="}, "cookie": {"version": "0.4.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/cookie/-/cookie-0.4.0.tgz", "integrity": "sha1-vrQ35wIrO21JAZ0IhmUwPr6cFLo="}, "cookie-signature": {"version": "1.0.6", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw="}, "cors": {"version": "2.8.5", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/cors/-/cors-2.8.5.tgz", "integrity": "sha1-6sEdpRWS3Ya58G9uesKTs9+HXSk=", "requires": {"object-assign": "^4", "vary": "^1"}}, "corser": {"version": "2.0.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/corser/-/corser-2.0.1.tgz", "integrity": "sha1-jtolLsqrWEDc2XXOuQ2TcMgZ/4c="}, "debug": {"version": "2.6.9", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}}, "depd": {"version": "1.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/depd/-/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak="}, "destroy": {"version": "1.0.4", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/destroy/-/destroy-1.0.4.tgz", "integrity": "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA="}, "detect-file": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/detect-file/-/detect-file-1.0.0.tgz", "integrity": "sha512-DtCOLG98P007x7wiiOmfI0fi3eIKyWiLTGJ2MDnVi/E04lWGbf+JzrRHMm0rgIIZJGtHpKpbVgLWHrv8xXpc3Q=="}, "ecstatic": {"version": "3.3.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ecstatic/-/ecstatic-3.3.2.tgz", "integrity": "sha1-bR3UmBTQBZRoLGUq22YHamnUbEg=", "requires": {"he": "^1.1.1", "mime": "^1.6.0", "minimist": "^1.1.0", "url-join": "^2.0.5"}}, "ee-first": {"version": "1.1.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="}, "encodeurl": {"version": "1.0.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/encodeurl/-/encodeurl-1.0.2.tgz", "integrity": "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k="}, "es6-promise": {"version": "4.2.8", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/es6-promise/-/es6-promise-4.2.8.tgz", "integrity": "sha1-TrIVlMlyvEBVPSduUQU5FD21Pgo="}, "escape-html": {"version": "1.0.3", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="}, "etag": {"version": "1.8.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/etag/-/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc="}, "eventemitter3": {"version": "4.0.4", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/eventemitter3/-/eventemitter3-4.0.4.tgz", "integrity": "sha1-tUY6zmNaCD0Bi9x8kXtMXxCoU4Q="}, "expand-tilde": {"version": "2.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/expand-tilde/-/expand-tilde-2.0.2.tgz", "integrity": "sha512-A5EmesHW6rfnZ9ysHQjPdJRni0SRar0tjtG5MNtm9n5TUvsYU8oozprtRD4AqHxcZWWlVuAmQo2nWKfN9oyjTw==", "requires": {"homedir-polyfill": "^1.0.1"}}, "express": {"version": "4.17.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/express/-/express-4.17.1.tgz", "integrity": "sha1-RJH8OGBc9R+GKdOcK10Cb5ikwTQ=", "requires": {"accepts": "~1.3.7", "array-flatten": "1.1.1", "body-parser": "1.19.0", "content-disposition": "0.5.3", "content-type": "~1.0.4", "cookie": "0.4.0", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "~1.1.2", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "~1.1.2", "fresh": "0.5.2", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.5", "qs": "6.7.0", "range-parser": "~1.2.1", "safe-buffer": "5.1.2", "send": "0.17.1", "serve-static": "1.14.1", "setprototypeof": "1.1.1", "statuses": "~1.5.0", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}}, "express-http-proxy": {"version": "1.6.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/express-http-proxy/-/express-http-proxy-1.6.0.tgz", "integrity": "sha1-hnKxCTzJa4qT6OPalI3REaZo7yI=", "requires": {"debug": "^3.0.1", "es6-promise": "^4.1.1", "raw-body": "^2.3.0"}, "dependencies": {"debug": {"version": "3.2.6", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/debug/-/debug-3.2.6.tgz", "integrity": "sha1-6D0X3hbYp++3cX7b5fsQE17uYps=", "requires": {"ms": "^2.1.1"}}, "ms": {"version": "2.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="}}}, "extend": {"version": "3.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/extend/-/extend-3.0.2.tgz", "integrity": "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g=="}, "fill-range": {"version": "7.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fill-range/-/fill-range-7.0.1.tgz", "integrity": "sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==", "requires": {"to-regex-range": "^5.0.1"}}, "finalhandler": {"version": "1.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/finalhandler/-/finalhandler-1.1.2.tgz", "integrity": "sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0=", "requires": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "statuses": "~1.5.0", "unpipe": "~1.0.0"}}, "findup-sync": {"version": "4.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/findup-sync/-/findup-sync-4.0.0.tgz", "integrity": "sha512-6jvvn/12IC4quLBL1KNokxC7wWTvYncaVUYSoxWw7YykPLuRrnv4qdHcSOywOI5RpkOVGeQRtWM8/q+G6W6qfQ==", "requires": {"detect-file": "^1.0.0", "is-glob": "^4.0.0", "micromatch": "^4.0.2", "resolve-dir": "^1.0.1"}}, "fined": {"version": "1.2.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fined/-/fined-1.2.0.tgz", "integrity": "sha512-ZYDqPLGxDkDhDZBjZBb+oD1+j0rA4E0pXY50eplAAOPg2N/gUBSSk5IM1/QhPfyVo19lJ+CvXpqfvk+b2p/8Ng==", "requires": {"expand-tilde": "^2.0.2", "is-plain-object": "^2.0.3", "object.defaults": "^1.1.0", "object.pick": "^1.2.0", "parse-filepath": "^1.0.1"}}, "flagged-respawn": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/flagged-respawn/-/flagged-respawn-1.0.1.tgz", "integrity": "sha512-lNaHNVymajmk0OJMBn8fVUAU1BtDeKIqKoVhk4xAALB57aALg6b4W0MfJ/cUE0g9YBXy5XhSlPIpYIJ7HaY/3Q=="}, "follow-redirects": {"version": "1.12.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/follow-redirects/-/follow-redirects-1.12.1.tgz", "integrity": "sha1-3lSmIFMRuT1gOY68Ac9wFWgjErY="}, "font-awesome": {"version": "4.7.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/font-awesome/-/font-awesome-4.7.0.tgz", "integrity": "sha512-U6kGnykA/6bFmg1M/oT9EkFeIYv7JlX3bozwQJWiiLz6L0w3F5vBVPxHlwyX/vtNq1ckcpRKOB9f2Qal/VtFpg=="}, "for-in": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/for-in/-/for-in-1.0.2.tgz", "integrity": "sha512-7EwmXrOjyL+ChxMhmG5lnW9MPt1aIeZEwKhQzoBUdTV0N3zuwWDZYVJatDvZ2OyzPUvdIAZDsCetk3coyMfcnQ=="}, "for-own": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/for-own/-/for-own-1.0.0.tgz", "integrity": "sha512-0OABksIGrxKK8K4kynWkQ7y1zounQxP+CWnyclVwj81KW3vlLlGUx57DKGcP/LH216GzqnstnPocF16Nxs0Ycg==", "requires": {"for-in": "^1.0.1"}}, "forwarded": {"version": "0.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/forwarded/-/forwarded-0.1.2.tgz", "integrity": "sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ="}, "fresh": {"version": "0.5.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/fresh/-/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="}, "function-bind": {"version": "1.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA=="}, "fuxi": {"version": "2.15.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fuxi/-/fuxi-2.15.2.tgz", "integrity": "sha512-2/65BARFRxhMl3FkVmlnb6q5tz4TPDS1N+7I9wXmO4Sgx/BkMstZnk7EySbdYhGzOxQ+zIHCy23fqc2PMhn/Aw==", "requires": {"@fortawesome/fontawesome-free": "^5.13.0", "font-awesome": "^4.7.0", "fuxicons": "^1.0.1", "grunt-cli": "^1.4.3"}}, "fuxi-nossis": {"version": "3.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fuxi-nossis/-/fuxi-nossis-3.0.0.tgz", "integrity": "sha512-I8dTFpYuL+pZHyaCtepVfBjWR5Mb09M2F7uiP0Qm5cRBlSrsHSTKpzinMBdV9Av4zxi09zyP6boNOFvCXOHzLg=="}, "fuxicons": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/fuxicons/-/fuxicons-1.0.1.tgz", "integrity": "sha1-fzH+J+9cxGOzr0lwQpIkEUCIQgc="}, "global-modules": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/global-modules/-/global-modules-1.0.0.tgz", "integrity": "sha512-sKzpEkf11GpOFuw0Zzjzmt4B4UZwjOcG757PPvrfhxcLFbq0wpsgpOqxpxtxFiCG4DtG93M6XRVbF2oGdev7bg==", "requires": {"global-prefix": "^1.0.1", "is-windows": "^1.0.1", "resolve-dir": "^1.0.0"}}, "global-prefix": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/global-prefix/-/global-prefix-1.0.2.tgz", "integrity": "sha512-5lsx1NUDHtSjfg0eHlmYvZKv8/nVqX4ckFbM+FrGcQ+04KWcWFo9P5MxPZYSzUvyzmdTbI7Eix8Q4IbELDqzKg==", "requires": {"expand-tilde": "^2.0.2", "homedir-polyfill": "^1.0.1", "ini": "^1.3.4", "is-windows": "^1.0.1", "which": "^1.2.14"}, "dependencies": {"which": {"version": "1.3.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/which/-/which-1.3.1.tgz", "integrity": "sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==", "requires": {"isexe": "^2.0.0"}}}}, "grunt-cli": {"version": "1.4.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/grunt-cli/-/grunt-cli-1.4.3.tgz", "integrity": "sha512-9Dtx/AhVeB4LYzsViCjUQkd0Kw0McN2gYpdmGYKtE2a5Yt7v1Q+HYZVWhqXc/kGnxlMtqKDxSwotiGeFmkrCoQ==", "requires": {"grunt-known-options": "~2.0.0", "interpret": "~1.1.0", "liftup": "~3.0.1", "nopt": "~4.0.1", "v8flags": "~3.2.0"}}, "grunt-known-options": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/grunt-known-options/-/grunt-known-options-2.0.0.tgz", "integrity": "sha512-GD7cTz0I4SAede1/+pAbmJRG44zFLPipVtdL9o3vqx9IEyb7b4/Y3s7r6ofI3CchR5GvYJ+8buCSioDv5dQLiA=="}, "hasown": {"version": "2.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/hasown/-/hasown-2.0.2.tgz", "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "requires": {"function-bind": "^1.1.2"}}, "he": {"version": "1.2.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/he/-/he-1.2.0.tgz", "integrity": "sha1-hK5l+n6vsWX922FWauFLrwVmTw8="}, "homedir-polyfill": {"version": "1.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/homedir-polyfill/-/homedir-polyfill-1.0.3.tgz", "integrity": "sha512-eSmmWE5bZTK2Nou4g0AI3zZ9rswp7GRKoKXS1BLUkvPviOqs4YTN1djQIqrXy9k5gEtdLPy86JjRwsNM9tnDcA==", "requires": {"parse-passwd": "^1.0.0"}}, "http-errors": {"version": "1.7.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/http-errors/-/http-errors-1.7.2.tgz", "integrity": "sha1-T1ApzxMjnzEDblsuVSkrz7zIXI8=", "requires": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.1", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}}, "http-proxy": {"version": "1.18.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/http-proxy/-/http-proxy-1.18.1.tgz", "integrity": "sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=", "requires": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}}, "http-server": {"version": "0.12.3", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/http-server/-/http-server-0.12.3.tgz", "integrity": "sha1-ugRx0OzEJYhmFss1xPryeRQKDTc=", "requires": {"basic-auth": "^1.0.3", "colors": "^1.4.0", "corser": "^2.0.1", "ecstatic": "^3.3.2", "http-proxy": "^1.18.0", "minimist": "^1.2.5", "opener": "^1.5.1", "portfinder": "^1.0.25", "secure-compare": "3.0.1", "union": "~0.5.0"}}, "iconv-lite": {"version": "0.4.24", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=", "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "inherits": {"version": "2.0.3", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="}, "ini": {"version": "1.3.8", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/ini/-/ini-1.3.8.tgz", "integrity": "sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew=="}, "interpret": {"version": "1.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/interpret/-/interpret-1.1.0.tgz", "integrity": "sha512-CLM8SNMDu7C5psFCn6Wg/tgpj/bKAg7hc2gWqcuR9OD5Ft9PhBpIu8PLicPeis+xDd6YX2ncI8MCA64I9tftIA=="}, "ipaddr.js": {"version": "1.9.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "integrity": "sha1-v/OFQ+64mEglB5/zoqjmy9RngbM="}, "is-absolute": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-absolute/-/is-absolute-1.0.0.tgz", "integrity": "sha512-dOWoqflvcydARa360Gvv18DZ/gRuHKi2NU/wU5X1ZFzdYfH29nkiNZsF3mp4OJ3H4yo9Mx8A/uAGNzpzPN3yBA==", "requires": {"is-relative": "^1.0.0", "is-windows": "^1.0.1"}}, "is-core-module": {"version": "2.13.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-core-module/-/is-core-module-2.13.1.tgz", "integrity": "sha512-hHrIjvZsftOsvKSn2TRYl63zvxsgE0K+0mYMoH6gD4omR5IWB2KynivBQczo3+wF1cCkjzvptnI9Q0sPU66ilw==", "requires": {"hasown": "^2.0.0"}}, "is-extglob": {"version": "2.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ=="}, "is-glob": {"version": "4.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==", "requires": {"is-extglob": "^2.1.1"}}, "is-number": {"version": "7.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-number/-/is-number-7.0.0.tgz", "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng=="}, "is-plain-object": {"version": "2.0.4", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-plain-object/-/is-plain-object-2.0.4.tgz", "integrity": "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==", "requires": {"isobject": "^3.0.1"}}, "is-relative": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-relative/-/is-relative-1.0.0.tgz", "integrity": "sha512-Kw/ReK0iqwKeu0MITLFuj0jbPAmEiOsIwyIXvvbfa6QfmN9pkD1M+8pdk7Rl/dTKbH34/XBFMbgD4iMJhLQbGA==", "requires": {"is-unc-path": "^1.0.0"}}, "is-unc-path": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-unc-path/-/is-unc-path-1.0.0.tgz", "integrity": "sha512-mrGpVd0fs7WWLfVsStvgF6iEJnbjDFZh9/emhRDcGWTduTfNHd9CHeUwH3gYIjdbwo4On6hunkztwOaAw0yllQ==", "requires": {"unc-path-regex": "^0.1.2"}}, "is-windows": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/is-windows/-/is-windows-1.0.2.tgz", "integrity": "sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA=="}, "isexe": {"version": "2.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/isexe/-/isexe-2.0.0.tgz", "integrity": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw=="}, "isobject": {"version": "3.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/isobject/-/isobject-3.0.1.tgz", "integrity": "sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg=="}, "kind-of": {"version": "6.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/kind-of/-/kind-of-6.0.3.tgz", "integrity": "sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw=="}, "liftup": {"version": "3.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/liftup/-/liftup-3.0.1.tgz", "integrity": "sha512-yR<PERSON>aiQDizWSzoXk3APcA71eOI/UuhEkNN9DiW2Tt44mhYzX4joFoCZlxsSOF7RyeLlfqzFLQI1ngFq3ggMPhOw==", "requires": {"extend": "^3.0.2", "findup-sync": "^4.0.0", "fined": "^1.2.0", "flagged-respawn": "^1.0.1", "is-plain-object": "^2.0.4", "object.map": "^1.0.1", "rechoir": "^0.7.0", "resolve": "^1.19.0"}}, "lodash": {"version": "4.17.17", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/lodash/-/lodash-4.17.17.tgz", "integrity": "sha1-2QGLOsxXqVydz0pFxrY7h3tsLUU="}, "make-iterator": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/make-iterator/-/make-iterator-1.0.1.tgz", "integrity": "sha512-pxiuXh0iVEq7VM7KMIhs5gxsfxCux2URptUQaXo4iZZJxBAzTPOLE2BumO5dbfVYq/hBJFBR/a1mFDmOx5AGmw==", "requires": {"kind-of": "^6.0.2"}}, "map-cache": {"version": "0.2.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/map-cache/-/map-cache-0.2.2.tgz", "integrity": "sha512-8y/eV9QQZCiyn1SprXSrCmqJN0yNRATe+PO8ztwqrvrbdRLA3eYJF0yaR0YayLWkMbsQSKWS9N2gPcGEc4UsZg=="}, "media-typer": {"version": "0.3.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g="}, "merge-descriptors": {"version": "1.0.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/merge-descriptors/-/merge-descriptors-1.0.1.tgz", "integrity": "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E="}, "methods": {"version": "1.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/methods/-/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4="}, "micromatch": {"version": "4.0.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/micromatch/-/micromatch-4.0.5.tgz", "integrity": "sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==", "requires": {"braces": "^3.0.2", "picomatch": "^2.3.1"}}, "mime": {"version": "1.6.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/mime/-/mime-1.6.0.tgz", "integrity": "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE="}, "mime-db": {"version": "1.44.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/mime-db/-/mime-db-1.44.0.tgz", "integrity": "sha1-+hHF6wrKEzS0Izy01S8QxaYnL5I="}, "mime-types": {"version": "2.1.27", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/mime-types/-/mime-types-2.1.27.tgz", "integrity": "sha1-R5SfmOJ56lMRn1ci4PNOUpvsAJ8=", "requires": {"mime-db": "1.44.0"}}, "minimist": {"version": "1.2.5", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/minimist/-/minimist-1.2.5.tgz", "integrity": "sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI="}, "mkdirp": {"version": "0.5.5", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/mkdirp/-/mkdirp-0.5.5.tgz", "integrity": "sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=", "requires": {"minimist": "^1.2.5"}}, "ms": {"version": "2.0.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "negotiator": {"version": "0.6.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/negotiator/-/negotiator-0.6.2.tgz", "integrity": "sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs="}, "nopt": {"version": "4.0.3", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/nopt/-/nopt-4.0.3.tgz", "integrity": "sha512-CvaGwVMztSMJLOeXPrez7fyfObdZqNUK1cPAEzLHrTybIua9pMdmmPR5YwtfNftIOMv3DPUhFaxsZMNTQO20Kg==", "requires": {"abbrev": "1", "osenv": "^0.1.4"}}, "object-assign": {"version": "4.1.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="}, "object.defaults": {"version": "1.1.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/object.defaults/-/object.defaults-1.1.0.tgz", "integrity": "sha512-c/K0mw/F11k4dEUBMW8naXUuBuhxRCfG7W+yFy8EcijU/rSmazOUd1XAEEe6bC0OuXY4HUKjTJv7xbxIMqdxrA==", "requires": {"array-each": "^1.0.1", "array-slice": "^1.0.0", "for-own": "^1.0.0", "isobject": "^3.0.0"}}, "object.map": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/object.map/-/object.map-1.0.1.tgz", "integrity": "sha512-3+mAJu2PLfnSVGHwIWubpOFLscJANBKuB/6A4CxBstc4aqwQY0FWcsppuy4jU5GSB95yES5JHSI+33AWuS4k6w==", "requires": {"for-own": "^1.0.0", "make-iterator": "^1.0.0"}}, "object.pick": {"version": "1.3.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/object.pick/-/object.pick-1.3.0.tgz", "integrity": "sha512-tqa/UMy/CCoYmj+H5qc07qvSL9dqcs/WZENZ1JbtWBlATP+iVOe778gE6MSijnyCnORzDuX6hU+LA4SZ09YjFQ==", "requires": {"isobject": "^3.0.1"}}, "on-finished": {"version": "2.3.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/on-finished/-/on-finished-2.3.0.tgz", "integrity": "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=", "requires": {"ee-first": "1.1.1"}}, "opener": {"version": "1.5.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/opener/-/opener-1.5.1.tgz", "integrity": "sha1-bS8Od/GgrwAyrKcWwsH7uOfoq+0="}, "os-homedir": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/os-homedir/-/os-homedir-1.0.2.tgz", "integrity": "sha512-B5JU3cabzk8c67mRRd3ECmROafjYMXbuzlwtqdM8IbS8ktlTix8aFGb2bAGKrSRIlnfKwovGUUr72JUPyOb6kQ=="}, "os-tmpdir": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "integrity": "sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g=="}, "osenv": {"version": "0.1.5", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/osenv/-/osenv-0.1.5.tgz", "integrity": "sha512-0CWcCECdMVc2Rw3U5w9ZjqX6ga6ubk1xDVKxtBQPK7wis/0F2r9T6k4ydGYhecl7YUBxBVxhL5oisPsNxAPe2g==", "requires": {"os-homedir": "^1.0.0", "os-tmpdir": "^1.0.0"}}, "parse-filepath": {"version": "1.0.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/parse-filepath/-/parse-filepath-1.0.2.tgz", "integrity": "sha512-FwdRXKCohSVeXqwtYonZTXtbGJKrn+HNyWDYVcp5yuJlesTwNH4rsmRZ+GrKAPJ5bLpRxESMeS+Rl0VCHRvB2Q==", "requires": {"is-absolute": "^1.0.0", "map-cache": "^0.2.0", "path-root": "^0.1.1"}}, "parse-passwd": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/parse-passwd/-/parse-passwd-1.0.0.tgz", "integrity": "sha512-1Y1A//QUXEZK7YKz+rD9WydcE1+EuPr6ZBgKecAB8tmoW6UFv0NREVJe1p+jRxtThkcbbKkfwIbWJe/IeE6m2Q=="}, "parseurl": {"version": "1.3.3", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/parseurl/-/parseurl-1.3.3.tgz", "integrity": "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ="}, "path-parse": {"version": "1.0.7", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw=="}, "path-root": {"version": "0.1.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/path-root/-/path-root-0.1.1.tgz", "integrity": "sha512-QLcPegTHF11axjfojBIoDygmS2E3Lf+8+jI6wOVmNVenrKSo3mFdSGiIgdSHenczw3wPtlVMQaFVwGmM7BJdtg==", "requires": {"path-root-regex": "^0.1.0"}}, "path-root-regex": {"version": "0.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/path-root-regex/-/path-root-regex-0.1.2.tgz", "integrity": "sha512-4GlJ6rZDhQZFE0DPVKh0e9jmZ5egZfxTkp7bcRDuPlJXbAwhxcl2dINPUAsjLdejqaLsCeg8axcLjIbvBjN4pQ=="}, "path-to-regexp": {"version": "0.1.7", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/path-to-regexp/-/path-to-regexp-0.1.7.tgz", "integrity": "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w="}, "picomatch": {"version": "2.3.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="}, "portfinder": {"version": "1.0.26", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/portfinder/-/portfinder-1.0.26.tgz", "integrity": "sha1-R1ZY1WyjC+1yrH8TeO01C9G2TnA=", "requires": {"async": "^2.6.2", "debug": "^3.1.1", "mkdirp": "^0.5.1"}, "dependencies": {"debug": {"version": "3.2.6", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/debug/-/debug-3.2.6.tgz", "integrity": "sha1-6D0X3hbYp++3cX7b5fsQE17uYps=", "requires": {"ms": "^2.1.1"}}, "ms": {"version": "2.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="}}}, "proxy-addr": {"version": "2.0.6", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/proxy-addr/-/proxy-addr-2.0.6.tgz", "integrity": "sha1-/cIzZQVEfT8vLGOO0nLK9hS7sr8=", "requires": {"forwarded": "~0.1.2", "ipaddr.js": "1.9.1"}}, "qs": {"version": "6.7.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/qs/-/qs-6.7.0.tgz", "integrity": "sha1-QdwaAV49WB8WIXdr4xr7KHapsbw="}, "range-parser": {"version": "1.2.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/range-parser/-/range-parser-1.2.1.tgz", "integrity": "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE="}, "raw-body": {"version": "2.4.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/raw-body/-/raw-body-2.4.0.tgz", "integrity": "sha1-oc5vucm8NWylLoklarWQWeE9AzI=", "requires": {"bytes": "3.1.0", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}}, "rechoir": {"version": "0.7.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/rechoir/-/rechoir-0.7.1.tgz", "integrity": "sha512-/njmZ8s1wVeR6pjTZ+0nCnv8SpZNRMT2D1RLOJQESlYFDBvwpTA4KWJpZ+sBJ4+vhjILRcK7JIFdGCdxEAAitg==", "requires": {"resolve": "^1.9.0"}}, "requires-port": {"version": "1.0.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/requires-port/-/requires-port-1.0.0.tgz", "integrity": "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8="}, "resolve": {"version": "1.22.8", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/resolve/-/resolve-1.22.8.tgz", "integrity": "sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==", "requires": {"is-core-module": "^2.13.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}}, "resolve-dir": {"version": "1.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/resolve-dir/-/resolve-dir-1.0.1.tgz", "integrity": "sha512-R7uiTjECzvOsWSfdM0QKFNBVFcK27aHOUwdvK53BcW8zqnGdYp0Fbj82cy54+2A4P2tFM22J5kRfe1R+lM/1yg==", "requires": {"expand-tilde": "^2.0.0", "global-modules": "^1.0.0"}}, "safe-buffer": {"version": "5.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="}, "safer-buffer": {"version": "2.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo="}, "secure-compare": {"version": "3.0.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/secure-compare/-/secure-compare-3.0.1.tgz", "integrity": "sha1-8aAymzCLIh+uN7mXTz1XjQypmeM="}, "send": {"version": "0.17.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/send/-/send-0.17.1.tgz", "integrity": "sha1-wdiwWfeQD3Rm3Uk4vcROEd2zdsg=", "requires": {"debug": "2.6.9", "depd": "~1.1.2", "destroy": "~1.0.4", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "~1.7.2", "mime": "1.6.0", "ms": "2.1.1", "on-finished": "~2.3.0", "range-parser": "~1.2.1", "statuses": "~1.5.0"}, "dependencies": {"ms": {"version": "2.1.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/ms/-/ms-2.1.1.tgz", "integrity": "sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo="}}}, "serve-static": {"version": "1.14.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/serve-static/-/serve-static-1.14.1.tgz", "integrity": "sha1-Zm5jbcTwEPfvKZcKiKZ0MgiYsvk=", "requires": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.17.1"}}, "setprototypeof": {"version": "1.1.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/setprototypeof/-/setprototypeof-1.1.1.tgz", "integrity": "sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM="}, "statuses": {"version": "1.5.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/statuses/-/statuses-1.5.0.tgz", "integrity": "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow="}, "supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w=="}, "to-regex-range": {"version": "5.0.1", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==", "requires": {"is-number": "^7.0.0"}}, "toidentifier": {"version": "1.0.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/toidentifier/-/toidentifier-1.0.0.tgz", "integrity": "sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM="}, "type-is": {"version": "1.6.18", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/type-is/-/type-is-1.6.18.tgz", "integrity": "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=", "requires": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}}, "unc-path-regex": {"version": "0.1.2", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/unc-path-regex/-/unc-path-regex-0.1.2.tgz", "integrity": "sha512-eXL4nmJT7oCpkZsHZUOJo8hcX3GbsiDOa0Qu9F646fi8dT3XuSVopVqAcEiVzSKKH7UoDti23wNX3qGFxcW5Qg=="}, "union": {"version": "0.5.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/union/-/union-0.5.0.tgz", "integrity": "sha1-ssEb6E9gU4U3uEbtuboma6AJAHU=", "requires": {"qs": "^6.4.0"}}, "unpipe": {"version": "1.0.0", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="}, "url-join": {"version": "2.0.5", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/url-join/-/url-join-2.0.5.tgz", "integrity": "sha1-WvIvGMBSoACkjXuCxenC4v7tpyg="}, "utils-merge": {"version": "1.0.1", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/utils-merge/-/utils-merge-1.0.1.tgz", "integrity": "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM="}, "v8flags": {"version": "3.2.0", "resolved": "http://maven.ptin.corppt.com:80/api/npm/npm-releases/v8flags/-/v8flags-3.2.0.tgz", "integrity": "sha512-mH8etigqMfiGWdeXpaaqGfs6BndypxusHHcv2qSHyZkGEznCd/qAXCWWRzeowtL54147cktFOC4P5y+kl8d8Jg==", "requires": {"homedir-polyfill": "^1.0.1"}}, "vary": {"version": "1.1.2", "resolved": "http://npm.ptin.corppt.com:80/api/npm/npm-releases/vary/-/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="}}}