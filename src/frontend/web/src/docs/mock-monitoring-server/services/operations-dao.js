import operations from '../data/operations'
import versions from '../data/versions'
import operationStates from '../data/operation-states'

/**
 *
 * @param q
 * @param page
 * @returns {*[]}
 */
export function getOperationsByVersion ({ versionName, q } = {}) {
  let filteredData = [...operations]

  if (versionName) {
    filteredData = filteredData.filter(operation => operation.version === versionName)
  }

  if (q) {
    filteredData = filteredData.filter(operation => operation.name.startsWith(q))
  }

  filteredData = filteredData.map(({ id, name, entityName, type, valid }) => ({ id, name, entityName, type, valid }))

  return { entries: filteredData, count: filteredData.length }
}

export function getOperationById ({ id } = {}) {
  if (!id) {
    return undefined
  }
  const found = operations.find(operation => operation.id === id)
  if (found) {
    return expandOperationVersion(found)
  }
}

export function getOperationByVersionAndName ({ version, name } = {}) {
  if (!version || !name) {
    return undefined
  }

  const found = operations.find(operation => operation.version === version && operation.name === name)
  if (found) {
    return expandOperationVersion(found)
  }
}

function expandOperationVersion(operation) {
  const versionName = operation.version
  const version = versions[versionName]
  return {
    ...operation,
    version,
    tags: operation.tags?.map(name => ({ name })),
    stateName: operationStates[operation.state]?.name,
    stateI18n: operationStates[operation.state]?.stateI18n
  }
}
