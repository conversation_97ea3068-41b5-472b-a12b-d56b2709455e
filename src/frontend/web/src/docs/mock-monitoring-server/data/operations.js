export default [
  {
    id: 270773,
    name: 'automatic-retry-new-request',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270802,
    name: 'bigger-workflow-na-portal',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270814,
    name: 'bundle',
    entityName: 'CFS.TO.BUNDLE',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270809,
    name: 'decision-workflow-na-portal',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270796,
    name: 'decision-workflow-na-portal-numerous-activities',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270768,
    name: 'external-system',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270805,
    name: 'fork-failed-workflow-na-portal',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270804,
    name: 'fork-workflow-na-portal',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270769,
    name: 'http-system',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270806,
    name: 'in-progress-workflow-na-portal',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270771,
    name: 'invalid-manual-action',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270775,
    name: 'manual-abort-order',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270770,
    name: 'manual-action',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270774,
    name: 'manual-retry-new-request',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270766,
    name: 'notification',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270767,
    name: 'notification-custom-message',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270803,
    name: 'notification-workflow-na-portal',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270772,
    name: 'order-manual-action',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270763,
    name: 'sum',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    versionState: 'PRODUCTION',
    description: 'ic scenario',
    tags: [
      'selfnet',
    ],
    operationAttributes: [{
      id: 270764,
      valid: true,
      name: 'N_SUMS',
      description: null,
      entityName: null,
      path: '{"type":"CFS","name":"CFS.SUM","child":{"type":"ATTRIBUTE","name":"N_SUMS","child":null}}',
      direction: 'IN',
      type: 'string',
      idCatEntity: 10030,
      minMultiplicity: 1,
      maxMultiplicity: 1,
      code: 'Attribute_270764',
      externalCode: null,
      constraints: [],
      entityConstraints: []
    }],
  },
  {
    id: 270807,
    name: 'sum-enrich-input',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270800,
    name: 'sum-failure',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270801,
    name: 'sum-held',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270797,
    name: 'sum-json-path',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270812,
    name: 'sum-simulated',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270765,
    name: 'sum-subworkflow',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270799,
    name: 'sum-success',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270776,
    name: 'sumToCancel-subworkflow',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270795,
    name: 'workflow-decision',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270787,
    name: 'workflow-event-activity',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270781,
    name: 'workflow-http',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270783,
    name: 'workflow-http-callback',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270789,
    name: 'workflow-http-callback-timeout',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [
      {
        id: 270790,
        valid: true,
        name: 'SWE_RESPONSE_URL',
        description: null,
        entityName: null,
        path: '{"type":"CFS","name":"CFS.SUM","child":{"type":"ATTRIBUTE","name":"SWE_RESPONSE_URL","child":null}}',
        direction: 'IN',
        type: 'string',
        idCatEntity: 10030,
        minMultiplicity: 1,
        maxMultiplicity: 1,
        code: 'Attribute_270790',
        externalCode: null,
        constraints: [],
        entityConstraints: []
      }
    ]
  },
  {
    id: 270785,
    name: 'workflow-http-gateway-callback',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [{
      id: 270786,
      valid: true,
      name: 'SWE_RESPONSE_URL',
      description: null,
      entityName: null,
      path: '{"type":"CFS","name":"CFS.SUM","child":{"type":"ATTRIBUTE","name":"SWE_RESPONSE_URL","child":null}}',
      direction: 'IN',
      type: 'string',
      idCatEntity: 10030,
      minMultiplicity: 1,
      maxMultiplicity: 1,
      code: 'Attribute_270786',
      externalCode: null,
      constraints: [],
      entityConstraints: []

    }],
  },
  {
    id: 270810,
    name: 'workflow-http-get',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [
      {
        id: 270811,
        valid: true,
        name: 'SWE_RESPONSE_URL',
        description: null,
        entityName: null,
        path: '{"type":"CFS","name":"CFS.SUM","child":{"type":"ATTRIBUTE","name":"SWE_RESPONSE_URL","child":null}}',
        direction: 'IN',
        type: 'string',
        idCatEntity: 10030,
        minMultiplicity: 1,
        maxMultiplicity: 1,
        code: 'Attribute_270811',
        externalCode: null,
        constraints: [],
        entityConstraints: []
      },
    ]
  },
  {
    id: 270780,
    name: 'workflow-ldap',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270791,
    name: 'workflow-output-entities',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    state: 10101,
    workflow: 'generic',
    tags: ['selfnet', 'hakuna', 'matata'],
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [
      {
        id: 270792,
        valid: true,
        name: 'outAttr',
        description: null,
        entityName: null,
        path: '{"type":"CFS","name":"CFS.SUM","child":{"type":"ATTRIBUTE","name":"outAttr","child":null}}',
        direction: 'OUT',
        type: 'number',
        idCatEntity: 10030,
        minMultiplicity: 1,
        maxMultiplicity: 1,
        code: 'Attribute_270792',
        externalCode: null,
        constraints: [],
        entityConstraints: []
      },
      {
        id: 270793,
        valid: true,
        name: 'outAttr2',
        description: null,
        entityName: null,
        path: '{"type":"CFS","name":"CFS.SUM","child":{"type":"ATTRIBUTE","name":"outAttr2","child":null}}',
        direction: 'OUT',
        type: 'number',
        idCatEntity: 10030,
        minMultiplicity: 1,
        maxMultiplicity: 1,
        code: 'Attribute_270793',
        externalCode: null,
        constraints: [],
        entityConstraints: []
      },
      {
        id: 270794,
        valid: true,
        name: 'inAttr',
        description: null,
        entityName: null,
        path: '{"type":"CFS","name":"CFS.SUM","child":{"type":"ATTRIBUTE","name":"inAttr","child":null}}',
        direction: 'IN',
        type: 'number',
        idCatEntity: 10030,
        minMultiplicity: 1,
        maxMultiplicity: 1,
        code: 'Attribute_270794',
        externalCode: null,
        constraints: [],
        entityConstraints: []
      }
    ]
  },
  {
    id: 270777,
    name: 'workflow-snmp',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270778,
    name: 'workflow-ssh',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
    operationAttributes: [],
  },
  {
    id: 270779,
    name: 'workflow-telnet',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    version: 'SWE_OC_WORKFLOW_ENGINE',
  },
  {
    id: 270782,
    name: 'add',
    entityName: 'RFS.GPONACCESS',
    type: 'RFS',
    valid: true,
    operationAttributes: [
      { id: 277047, valid: true, name: 'vendor', description: null, entityName: null, path: '{"type":"RFS","name":"RFS.GPONACCESS","child":{"type":"ATTRIBUTE","name":"vendor","child":null}}', direction: 'IN', type: 'string', idCatEntity: 10030, minMultiplicity: 1, maxMultiplicity: 1, code: 'Attribute_277047', externalCode: null, constraints: [], entityConstraints: [] },
      { id: 277048, valid: true, name: 'oltName', description: null, entityName: null, path: '{"type":"RFS","name":"RFS.GPONACCESS","child":{"type":"ATTRIBUTE","name":"oltName","child":null}}', direction: 'IN', type: 'string', idCatEntity: 10030, minMultiplicity: 1, maxMultiplicity: 1, code: 'Attribute_277048', externalCode: null, constraints: [], entityConstraints: [] },
      { id: 277049, valid: true, name: 'ponPort', description: null, entityName: null, path: '{"type":"RFS","name":"RFS.GPONACCESS","child":{"type":"ATTRIBUTE","name":"ponPort","child":null}}', direction: 'IN', type: 'string', idCatEntity: 10030, minMultiplicity: 1, maxMultiplicity: 1, code: 'Attribute_277049', externalCode: null, constraints: [], entityConstraints: [] },
      { id: 277050, valid: true, name: 'onuId', description: null, entityName: null, path: '{"type":"RFS","name":"RFS.GPONACCESS","child":{"type":"ATTRIBUTE","name":"onuId","child":null}}', direction: 'IN', type: 'string', idCatEntity: 10030, minMultiplicity: 1, maxMultiplicity: 1, code: 'Attribute_277050', externalCode: null, constraints: [], entityConstraints: [] },
      { id: 277051, valid: true, name: 'ontSerialNumber', description: null, entityName: null, path: '{"type":"RFS","name":"RFS.GPONACCESS","child":{"type":"ATTRIBUTE","name":"ontSerialNumber","child":null}}', direction: 'IN', type: 'string', idCatEntity: 10030, minMultiplicity: 1, maxMultiplicity: 1, code: 'Attribute_277051', externalCode: null, constraints: [], entityConstraints: [] },
      { id: 277052, valid: true, name: 'cpeMacAddress', description: null, entityName: null, path: '{"type":"RFS","name":"RFS.GPONACCESS","child":{"type":"ATTRIBUTE","name":"cpeMacAddress","child":null}}', direction: 'IN', type: 'string', idCatEntity: 10030, minMultiplicity: 1, maxMultiplicity: 1, code: 'Attribute_277052', externalCode: null, constraints: [], entityConstraints: [] },
      { id: 277053, valid: true, name: 'lineProfile', description: null, entityName: null, path: '{"type":"RFS","name":"RFS.GPONACCESS","child":{"type":"ATTRIBUTE","name":"lineProfile","child":null}}', direction: 'IN', type: 'string', idCatEntity: 10030, minMultiplicity: 1, maxMultiplicity: 1, code: 'Attribute_277053', externalCode: null, constraints: [], entityConstraints: [] },
      { id: 277054, valid: true, name: 'serviceProfile', description: null, entityName: null, path: '{"type":"RFS","name":"RFS.GPONACCESS","child":{"type":"ATTRIBUTE","name":"serviceProfile","child":null}}', direction: 'IN', type: 'string', idCatEntity: 10030, minMultiplicity: 1, maxMultiplicity: 1, code: 'Attribute_277054', externalCode: null, constraints: [], entityConstraints: [] },
      { id: 277055, valid: true, name: 'hsiProfileUp', description: null, entityName: null, path: '{"type":"RFS","name":"RFS.GPONACCESS","child":{"type":"ATTRIBUTE","name":"hsiProfileUp","child":null}}', direction: 'IN', type: 'string', idCatEntity: 10030, minMultiplicity: 0, maxMultiplicity: 1, code: 'Attribute_277055', externalCode: null, constraints: [], entityConstraints: [] },
      { id: 277056, valid: true, name: 'hsiProfileDown', description: null, entityName: null, path: '{"type":"RFS","name":"RFS.GPONACCESS","child":{"type":"ATTRIBUTE","name":"hsiProfileDown","child":null}}', direction: 'IN', type: 'string', idCatEntity: 10030, minMultiplicity: 0, maxMultiplicity: 1, code: 'Attribute_277056', externalCode: null, constraints: [], entityConstraints: [] },
      { id: 277057, valid: true, name: 'hsiSvlan', description: null, entityName: null, path: '{"type":"RFS","name":"RFS.GPONACCESS","child":{"type":"ATTRIBUTE","name":"hsiSvlan","child":null}}', direction: 'IN', type: 'string', idCatEntity: 10030, minMultiplicity: 0, maxMultiplicity: 1, code: 'Attribute_277057', externalCode: null, constraints: [], entityConstraints: [] },
      { id: 277058, valid: true, name: 'hsiCvlan', description: null, entityName: null, path: '{"type":"RFS","name":"RFS.GPONACCESS","child":{"type":"ATTRIBUTE","name":"hsiCvlan","child":null}}', direction: 'IN', type: 'string', idCatEntity: 10030, minMultiplicity: 0, maxMultiplicity: 1, code: 'Attribute_277058', externalCode: null, constraints: [], entityConstraints: [] },
      { id: 277059, valid: true, name: 'iptvSvlan', description: null, entityName: null, path: '{"type":"RFS","name":"RFS.GPONACCESS","child":{"type":"ATTRIBUTE","name":"iptvSvlan","child":null}}', direction: 'IN', type: 'string', idCatEntity: 10030, minMultiplicity: 0, maxMultiplicity: 1, code: 'Attribute_277059', externalCode: null, constraints: [], entityConstraints: [] },
      { id: 277060, valid: true, name: 'iptvCvlan', description: null, entityName: null, path: '{"type":"RFS","name":"RFS.GPONACCESS","child":{"type":"ATTRIBUTE","name":"iptvCvlan","child":null}}', direction: 'IN', type: 'string', idCatEntity: 10030, minMultiplicity: 0, maxMultiplicity: 1, code: 'Attribute_277060', externalCode: null, constraints: [], entityConstraints: [] },
      { id: 277061, valid: true, name: 'voipSvlan', description: null, entityName: null, path: '{"type":"RFS","name":"RFS.GPONACCESS","child":{"type":"ATTRIBUTE","name":"voipSvlan","child":null}}', direction: 'IN', type: 'string', idCatEntity: 10030, minMultiplicity: 0, maxMultiplicity: 1, code: 'Attribute_277061', externalCode: null, constraints: [], entityConstraints: [] },
      { id: 277062, valid: true, name: 'voipCvlan', description: null, entityName: null, path: '{"type":"RFS","name":"RFS.GPONACCESS","child":{"type":"ATTRIBUTE","name":"voipCvlan","child":null}}', direction: 'IN', type: 'string', idCatEntity: 10030, minMultiplicity: 0, maxMultiplicity: 1, code: 'Attribute_277062', externalCode: null, constraints: [], entityConstraints: [] },
      { id: 277063, valid: true, name: 'onuId', description: null, entityName: null, path: '{"type":"RFS","name":"RFS.GPONACCESS","child":{"type":"ATTRIBUTE","name":"onuId","child":null}}', direction: 'OUT', type: 'string', idCatEntity: 10030, minMultiplicity: 1, maxMultiplicity: 1, code: 'Attribute_277063', externalCode: null, constraints: [], entityConstraints: [] }
    ],
    tags: ['ftth'],
    state: 10101,
    stateName: 'IN SERVICE',
    stateI18n: 'in service',
    version: 'FULFILLMENT_OPERATIONS_V1.0.0'
  },
  {
    id: 270784,
    name: 'add',
    entityName: 'CFS.SUM',
    type: 'CFS',
    valid: true,
    operationAttributes: [],
    tags: ['ftth'],
    state: 10101,
    stateName: 'IN SERVICE',
    stateI18n: 'in service',
    version: 'FULFILLMENT_OPERATIONS_V1.0.0'
  }
]
