import './demo-common'
import text from '../../README.adoc'
import mermaid from 'mermaid'

document.querySelector('.container').insertAdjacentHTML('beforeend', text)

function addZoomBehaviourForGraph(mermaidElement) {
  mermaidElement.addEventListener('click', (event) => {
    if (window.getSelection().toString().length) {
      return // ignore class toggle on text selections
    }
    if (event.target.matches('.mermaid .label div, .mermaid text tspan')) {
      return // ignore text clicks
    }

    mermaidElement.classList.toggle('fullscreen')
  })

  mermaidElement.addEventListener('wheel', function(event) {
    if (!mermaidElement.matches('.mermaid.fullscreen')) {
      return
    }

    event.preventDefault()
    event.stopImmediatePropagation()

    let zoomLevel = +(mermaidElement.style.getPropertyValue('--zoom-level') || '1.0')
    if (event.deltaY < 0) {
      zoomLevel *= 1.1
    } else if (event.deltaY > 0) {
      zoomLevel = Math.max(1.0, zoomLevel / 1.1)
    }
    mermaidElement.style.setProperty('--zoom-level', zoomLevel)
  })
}

document.querySelectorAll('.mermaid').forEach(addZoomBehaviourForGraph)

queueMicrotask(() => {
  mermaid.initialize({
    startOnLoad: true
  })
})
