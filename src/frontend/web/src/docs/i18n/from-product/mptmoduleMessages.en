################ MPT MODULE ###############
na.portal.mpt.module.title = MPT
na.portal.mpt.module.description = The Mass Provision Tool is a module that helps operators define and execute bulk operations over the network.<br>The operator uploads a file containing the list of operations to perform and MPT is responsible for the management of the bulk execution. It allows scheduling, either one shot or recursive, and it allows the operator to control the bulk execution.
na.portal.mpt.mega.menu.title = Operations
na.portal.mpt.mega.menu.navinitial = Go to Homepage
na.portal.mpt.mega.menu.mptadmin = MPT Administrations

na.portal.mpt.home.label.title = MPT
na.portal.mpt.home.label.subtitle = Massive provisions
na.portal.mpt.home.mainnav.mptadmin = MPT Administration
na.portal.mpt.home.mainnav.goadmin = GO Administration

###########GENERAL###########
na.portal.mpt.selects.states.empty.option = Without state
na.portal.mpt.selects.states.option.allstates = All states
na.portal.mpt.selects.option.allfamilies = All Families
na.portal.mpt.messages.informative.mandatoryfields = Mandatory fields
na.portal.mpt.weekdays.monday = MON
na.portal.mpt.weekdays.tuesday = TUE
na.portal.mpt.weekdays.wednesday = WED
na.portal.mpt.weekdays.thursday = THU
na.portal.mpt.weekdays.friday = FRI
na.portal.mpt.weekdays.saturday = SAT
na.portal.mpt.weekdays.sunday = SUN
na.portal.mpt.weekdays.all = ALL

###########BUTTONS###########
na.portal.mpt.buttons.createoperation = Create Operation
na.portal.mpt.buttons.suspend = Suspend
na.portal.mpt.buttons.cancel = Cancel
na.portal.mpt.buttons.addfile = Add File

###########FIELDS###########
na.portal.mpt.field.modificationdate.label = Modified in
na.portal.mpt.field.startdate.label = Start Date/Hour
na.portal.mpt.field.enddate.label = End Date/Hour
na.portal.mpt.field.user.label = User
na.portal.mpt.field.user.placeholder = User
na.portal.mpt.field.family.label = Family
na.portal.mpt.field.family.placeholder = Family
na.portal.mpt.field.operation.label = Operation
na.portal.mpt.field.operation.placeholder = Operation
na.portal.mpt.field.file.label = File
na.portal.mpt.field.file.placeholder = File
na.portal.mpt.field.state.label = State
na.portal.mpt.field.filtername.label = Filter Name
na.portal.mpt.field.filtername.placeholder = Filter Name

###########TABLES###########
na.portal.mpt.datatables.column.label.insertionDate = Date/Hour
na.portal.mpt.datatables.column.label.user = User
na.portal.mpt.datatables.column.label.family = Family
na.portal.mpt.datatables.column.label.operation = Operation
na.portal.mpt.datatables.column.label.file = File
na.portal.mpt.datatables.column.label.state = State
na.portal.mpt.datatables.column.label.executionstate = Execution
na.portal.mpt.datatables.column.label.progress = % Execution
na.portal.mpt.datatables.column.label.status = % OK
na.portal.mpt.datatables.column.label.actions = Actions
na.portal.mpt.datatables.column.label.actions.play = Start
na.portal.mpt.datatables.column.label.actions.pause = Pause
na.portal.mpt.datatables.column.label.actions.stop = Stop
na.portal.mpt.datatables.column.label.actions.edit = Edit
na.portal.mpt.datatables.column.label.actions.delete = Delete
na.portal.mpt.datatables.column.label.actions.details = Details

###########ADMIN SUSPEND##############
na.portal.mpt.suspend.admin.title = Confirmation
na.portal.mpt.suspend.admin.question = Do you really want to administrative suspend MPT?
na.portal.mpt.suspend.admin.info = Continue with suspension may result in missing the operation run in a permanent way.

###########CREATE OPERATION###########
na.portal.mpt.createoperation.label.sending = Sending
na.portal.mpt.createoperation.label.validating = Validating
na.portal.mpt.createoperation.label.waiting.server = Waiting for server confirmation
na.portal.mpt.createoperation.label.title = Create Massive Operation
na.portal.mpt.createoperation.tab.label = Create Operation
na.portal.mpt.createoperation.breadcrumb.label = Create operation
na.portal.mpt.createoperation.label.characteristics = Characteristics
na.portal.mpt.createoperation.label.files = Files
na.portal.mpt.createoperation.label.schedule = Schedule
na.portal.mpt.createoperation.label.provisiontype = Provision Type
na.portal.mpt.createoperation.label.operation.identification = Data that allows to identity the operation.
na.portal.mpt.createoperation.label.family = Family
na.portal.mpt.createoperation.label.operation = Operation
na.portal.mpt.createoperation.label.description = Description
na.portal.mpt.createoperation.label.selectfiles = Select Files
na.portal.mpt.createoperation.label.files.limit = Can be inserted until {0} files, in a maximum of {1}MB per file.
na.portal.mpt.createoperation.label.files.information = Files will be exported by priority in independent way and will result in distinctive operations.
na.portal.mpt.createoperation.label.schedule.information = Scheduling Identification
na.portal.mpt.createoperation.label.schedule.immediatly = Immediatly
na.portal.mpt.createoperation.label.schedule.later = Later
na.portal.mpt.createoperation.label.schedule.scheduling = Appointment
na.portal.mpt.createoperation.messages.operationsubmitted = Operation successfully created!
na.portal.mpt.createoperation.messages.operationerror = An error has occurred during the operation creation!

############### DATATABLES LABELS ####################
datatables.action.label.addschedule = add schedule
na.portal.mpt.datatables.column.label.startDate = Begin Date
na.portal.mpt.datatables.column.label.origin = Origin
na.portal.mpt.datatables.column.label.description = Description
na.portal.mpt.datatables.column.label.endDate = End Date
na.portal.mpt.datatables.column.label.startHour = Start Hour
na.portal.mpt.datatables.column.label.endHour = End Hour
na.portal.mpt.datatables.column.label.duration = Duration
na.portal.mpt.datatables.column.label.weekdays = Periodicity
na.portal.mpt.datatables.column.label.username = User
na.portal.mpt.datatables.column.label.state = State
na.portal.mpt.datatables.column.label.executionstate = Execution State
na.portal.mpt.datatables.column.label.opk = OPK
na.portal.mpt.datatables.column.label.key = Key
na.portal.mpt.datatables.column.label.parameter = Parameter
na.portal.mpt.datatables.column.label.value = value

###########EDIT BULK###########
na.portal.mpt.editbulk.label.title = Edit Massive Operation
na.portal.mpt.editbulk.tab.label = Edit Operation
na.portal.mpt.editbulk.breadcrumb.label = Edit operation
na.portal.mpt.editbulk.label.characteristics = Characteristics
na.portal.mpt.editbulk.label.files = Files
na.portal.mpt.editbulk.label.schedule = Schedule
na.portal.mpt.editbulk.label.provisiontype = Massive Provision Type
na.portal.mpt.editbulk.label.operation.identification = Data that allows Bulk identification
na.portal.mpt.editbulk.label.family = Family
na.portal.mpt.editbulk.label.operation = Operation
na.portal.mpt.editbulk.label.description = Description
na.portal.mpt.editbulk.label.existingfiles = Existing Files
na.portal.mpt.editbulk.label.files = Files

###########MPT ERROR MESSAGES###########
na.portal.mpt.createoperation.errors.invalidfield.title = Invalid fields
na.portal.mpt.createoperation.errors.invalidfield.message = Correct the marked fields
na.portal.mpt.createoperation.errors.title = Error Creating Operation
na.portal.mpt.createoperation.errors.missingfields.title = Mandatory Fields
na.portal.mpt.createoperation.errors.missingfields.message = Please fill all mandatory fields.
na.portal.mpt.createoperation.errors.baddates.title = Incorrect Dates.
na.portal.mpt.createoperation.errors.baddates.intervals.title = Incorrect intervals.
na.portal.mpt.createoperation.errors.baddates.startDate.past = Start date can not be in past.
na.portal.mpt.createoperation.errors.baddates.startDate.superior = Start date can not be higher than the End Date.
#mpt.createoperation.errors.baddates.schedules.overlapping = The selected schedule dates are overlapping with previous schedules.
na.portal.mpt.createoperation.errors.baddates.schedules.overlapping = The selected schedule dates are overlapping with previous schedules.
na.portal.mpt.createoperation.errors.invalidfiles.title = Invalid selected files.
na.portal.mpt.createoperation.errors.invalidfiles.message = The size of the file {0} ({1} MB) is superior to the suported. Please select another file or correct the content.
na.portal.mpt.createoperation.errors.missingfiles.title = Missing Files.
na.portal.mpt.createoperation.errors.missingfiles.message = Need to submit at least one file in order to create one operation.
na.portal.mpt.createoperation.errors.missingfields.startDate = Start Date to schedule is mandatory.
na.portal.mpt.createoperation.errors.missingfields.endDate = End Date to schedule is mandatory.
na.portal.mpt.createoperation.errors.missingfields.intervals = A least one interval is required.
na.portal.mpt.createoperation.errors.missingfields.intervals.badsyntax = Intervals sintax error.
na.portal.mpt.createoperation.errors.missingfields.intervals.missing.startDate = Start Date is mandatory in the interval.
na.portal.mpt.createoperation.errors.missingfields.intervals.missing.endDate = End Date is mandatory in the interval.
na.portal.mpt.createoperation.errors.missingfields.intervals.missing.weekdays = Periodicity of schedule is mandatory.
na.portal.mpt.createoperation.errors.intervals.overlapping.intervals = Overlapping Intervals.
na.portal.mpt.createoperation.errors.intervals.expired = Expired intervals.
na.portal.mpt.createoperation.errors.schedule.expired = Schedule expired.
na.portal.mpt.createoperation.errors.intervals.repeated.intervals = Repeated Intervals.
na.portal.mpt.createoperation.errors.globalintervals.repeated.intervals = Overlapping intervals for the specified date. Please correct them.
na.portal.mpt.createoperation.errors.invalidfiles.wrongtype = Invalid files. Only CSV are supported.
na.portal.mpt.createoperation.errors.intervals.exceeds.limit = A schedule already exists to the specified date. Intervals limit exceeded. Please delete some intervals in order to advance.
na.portal.mpt.createopeartion.errors.invalidafiles.exceeds.linelimit = File {0} exceeds line limit.
na.portal.mpt.createoperation.errors.schedule.missing.message = Scheduling is mandatory.
na.portal.mpt.createoperation.errors.files.sending.failed.title = Error while sending files.
na.portal.mpt.createoperation.errors.files.sending.failed.message = A problem has occurred while sending the following requests {0}. Due to this problem the its operations were not created. Please contact the administrator and try later.
na.portal.mpt.loaddetails.tab.content.error.title = Operation error
na.portal.mpt.loaddetails.tab.content.error.description = Error obtaining the required details. Please try again later.

na.portal.mpt.bulks.form.invalid.fields.title = Form errors.
na.portal.mpt.bulks.form.startdate.invalid.syntax = Start Date/Hour with invalid syntax.
na.portal.mpt.bulks.form.enddate.invalid.syntax = End Date/Hour with invalid syntax         
na.portal.mpt.bulks.form.invalid.dates.startdate.after.enddate = Start Date/Hour can not be higher than End Date/Hour.
na.portal.mpt.bulks.form.operation.invalid.syntax = Operation field with invalid syntax. 
na.portal.mpt.bulks.form.username.invalid.syntax = User field with invalid syntax.
na.portal.mpt.bulks.form.filename.invalid.syntax = File field with invalid syntax.
na.portal.mpt.bulks.filter.form.missing.filtername = Filter name is mandatory.
na.portal.mpt.bulks.filter.form.empty = Need to fill minimum fields in order to save the filter.

na.portal.mpt.editbulk.errors.missingfields.title = Mandatory Fields
na.portal.mpt.editbulk.errors.title.missingfields.message = Fill all mandatory fields.
na.portal.mpt.editbulk.errors.notupdatable = Selected Bulk cannot be modified due to its current state.
################ MODALS ################
na.portal.mpt.createoperation.modals.scheduling.title = Schedule
na.portal.mpt.createoperation.modals.scheduling.subtitle = Configure the schedule to associate to the massive provision. It is defined in your timezone ({0})
na.portal.mpt.createoperation.modals.scheduling.startDate = Begin
na.portal.mpt.createoperation.modals.scheduling.endDate = End date
na.portal.mpt.createoperation.modals.scheduling.noLimit = No end date
na.portal.mpt.createoperation.modals.scheduling.endBy = End by
na.portal.mpt.createoperation.modals.scheduling.noLimit.starthourinfo = Choose the start hour for the schedule.
na.portal.mpt.createoperation.modals.scheduling.startHour = Begin
na.portal.mpt.createoperation.modals.scheduling.period = Choose the schedule period
na.portal.mpt.createoperation.modals.scheduling.interval.startHour = Begin
na.portal.mpt.createoperation.modals.scheduling.interval.duration = Duration
na.portal.mpt.createoperation.modals.scheduling.interval.hours = hours
na.portal.mpt.createoperation.modals.scheduling.interval.minutes = minutes
na.portal.mpt.createoperation.modals.addSchedule = Add Schedule
na.portal.mpt.createoperation.modals.chooseStartTimeAndDuration = Choose the start time of your schedule and the duration:
na.portal.mpt.editoperation.label.schedule = Schedule
na.portal.mpt.editoperation.label.schedule.information = Schedule Information

################ BULKS ################
na.portal.mpt.bulks.states.validating = Validating
na.portal.mpt.bulks.states.pending = Pending
na.portal.mpt.bulks.states.waiting = Waiting
na.portal.mpt.bulks.states.executing = Executing
na.portal.mpt.bulks.states.suspending = Suspending
na.portal.mpt.bulks.states.suspended = Suspended
na.portal.mpt.bulks.states.aborting = Aborting
na.portal.mpt.bulks.states.rejected = Rejected
na.portal.mpt.bulks.states.terminated = Terminated
na.portal.mpt.bulks.states.scheduled = Scheduled
na.portal.mpt.bulks.states.expired = Expired
na.portal.mpt.bulks.states.suspended_administratively = Administratively Suspended
na.portal.mpt.bulks.states.aborted = Aborted
na.portal.mpt.bulks.states.undefined = Undefined
na.portal.mpt.bulks.states.error = Error

na.portal.mpt.bulks.order.status.ok = OK
na.portal.mpt.bulks.order.status.nok = NOK
na.portal.mpt.bulks.order.status.rejected = Rejected

na.portal.mpt.bulks.execution.states.not_started = Not Started
na.portal.mpt.bulks.execution.states.started = Started
na.portal.mpt.bulks.execution.states.terminated = Terminated
na.portal.mpt.bulks.execution.states.error = Error

na.portal.mpt.bulks.actions.play.loading = Starting
na.portal.mpt.bulks.actions.pause.loading = Suspending
na.portal.mpt.bulks.actions.stop.loading = Stopping

################ BULK DETAILS ################
na.portal.mpt.bulk.details.characteristics = Characteristics
na.portal.mpt.bulk.details.results = Results
na.portal.mpt.bulk.details.characteristics.description = Description
na.portal.mpt.bulk.details.characteristics.schedule = Scheduling
na.portal.mpt.bulk.details.characteristics.transitions =  State Transition
na.portal.mpt.bulk.details.characteristics.description.filename = File Name
na.portal.mpt.bulk.details.characteristics.description.familyname = Family
na.portal.mpt.bulk.details.characteristics.description.operationname = Operation
na.portal.mpt.bulk.details.characteristics.description.description = Description
na.portal.mpt.bulks.details.inputfile=Input File
na.portal.mpt.bulks.details.outputfile=Output File


############### RESULTS ###############
na.portal.mpt.results.fields.label.startDate = Date / Start Hour
na.portal.mpt.results.fields.label.endDate = Date / End Hour
na.portal.mpt.results.fields.label.opk = OPK
na.portal.mpt.results.fields.label.key = Key
na.portal.mpt.results.fields.label.state = State
na.portal.mpt.results.fields.label.execution.state = Execution

na.portal.mpt.results.form.invalid.fields.title = Form errors.
na.portal.mpt.results.form.starttime.invalid.syntax = Start Time Invalid Syntax
na.portal.mpt.results.form.endtime.invalid.syntax = End Time Invalid Syntax
na.portal.mpt.results.form.invalid.dates.starttime.after.endtime = Start Date/Hour can not be higher than End Date/Hour.
na.portal.mpt.results.filter.form.missing.filtername = Filter Name is mandatory.
na.portal.mpt.results.filter.form.empty = Need to fill minimum fields in order to save the filter.
 
############## RESULT DETAILS ################
na.portal.mpt.result.details.title.opk = OPK
na.portal.mpt.result.details.subtitle.operation = Operation:
na.portal.mpt.result.details.subtitle.rfs = RFS:
na.portal.mpt.result.details.characteristics = Characteristics
na.portal.mpt.result.details.characteristics.description = Description
na.portal.mpt.result.details.characteristics.description.filename = Filename
na.portal.mpt.result.details.characteristics.description.familyname = Family
na.portal.mpt.result.details.characteristics.description.operationname = Operation
na.portal.mpt.result.details.characteristics.description.requestdate = Request Date
na.portal.mpt.result.details.characteristics.description.testduration = Test Duration
na.portal.mpt.result.details.characteristics.description.username = Username
na.portal.mpt.result.details.characteristics.state = State
na.portal.mpt.result.details.characteristics.state.starttime = Start Time
na.portal.mpt.result.details.characteristics.state.endtime = End Time
na.portal.mpt.result.details.characteristics.state.status = Status
na.portal.mpt.result.details.characteristics.state.description = Description
na.portal.mpt.result.details.characteristics.payload = Payload
na.portal.mpt.result.details.label.request = Request
na.portal.mpt.result.details.label.response = Response
na.portal.mpt.result.details.characteristics.description.lineNum = Line Number

############# ERROR TITLES #############
exception.NA_MPT_00001.title = Searching error
exception.NA_MPT_00002.title = Searching error
exception.NA_MPT_00003.title = Download error
exception.NA_MPT_00004.title = Searching error
exception.NA_MPT_00005.title = Searching error
exception.NA_MPT_00006.title = Creating error
exception.NA_MPT_00007.title = Removing error
exception.NA_MPT_00008.title = Invalid state
exception.NA_MPT_00009.title = Start error
exception.NA_MPT_00010.title = Pause error
exception.NA_MPT_00011.title = Stop error
exception.NA_MPT_00012.title = Suspend error