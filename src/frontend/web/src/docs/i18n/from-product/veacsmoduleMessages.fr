veacs.serviceData.voiceProfile = Voice Profile
na.portal.veacs.module.title = VEACS
na.portal.veacs.module.description = Le VEACS, Voice Équipement ACS (Configuration Automatique du service) est la solution de Device Management pour téléphone SIP.<br>Le VEACS permet à l''opérateur de gérer l''inventaire des périphériques ainsi que les associations des clients et des services. Expose les interfaces de configuration, la mise à jour du firmware et d''autres paramètres.

###### Operations #######

na.portal.veacs.operation.removeSipDevice.title = Effacer l''équipement SIP
na.portal.veacs.operation.removeSipDevice.subtitle = Dans cette zone il est possible de effacer l''équipement SIP
na.portal.veacs.operation.removeSipDevice.name = effacer l''équipement SIP

na.portal.veacs.operation.changeSipDeviceId.title = Changer SIP de l''équipement
na.portal.veacs.operation.changeSipDeviceId.subtitle = Dans cette zone il est possible de changer le SIP de l''équipement
na.portal.veacs.operation.changeSipDeviceId.name = Changer SIP de l''équipement

na.portal.veacs.operation.addCustomKeyModule.title = Ajouter module des touches
na.portal.veacs.operation.addCustomKeyModule.subtitle = Dans cette zone il est possible d''ajouter le module de touches
na.portal.veacs.operation.addCustomKeyModule.name = ajouter module de touches

na.portal.veacs.operation.removeCustomKeyModule.title = Effacer module de touches
na.portal.veacs.operation.removeCustomKeyModule.subtitle = Dans cette zone il est possible d''éffacer le module de touches
na.portal.veacs.operation.removeCustomKeyModule.name = effacer module de touches

na.portal.veacs.operation.createTemplateWithNewSoftwareVersion.title = Créer un modèle
na.portal.veacs.operation.createTemplateWithNewSoftwareVersion.subtitle = Dans cette zone il est possible de créer un modèle avec une nouvelle version
na.portal.veacs.operation.createTemplateWithNewSoftwareVersion.name = créer un modèle avec une nouvelle version

na.portal.veacs.operation.migrateConfigurationBySoftwareVersion.title = Transférer configuration
na.portal.veacs.operation.migrateConfigurationBySoftwareVersion.subtitle = Dans cette zone il est possible de transférer la configuration par la version
na.portal.veacs.operation.migrateConfigurationBySoftwareVersion.name = transférer la configuration para la version

na.portal.veacs.operation.diagnostics.diagnostic.title = Diagnostic de l'équipement
na.portal.veacs.operation.diagnostics.diagnostic.subtitle = Dans cette zone, vous pouvez générer un diagnostic d'équipement
na.portal.veacs.operation.diagnostics.diagnostic.name = diagnostic de l'équipement

na.portal.veacs.customKey.keyId.1stKeyOnTheLeftColumnOfThe1stmodule = 1er du 1er module colonne de gauche
na.portal.veacs.customKey.keyId.1stKeyOnTheRightColumnOfThe1stModule = 1er du 1er module colonne de droite
na.portal.veacs.customKey.keyId.1stKeyOnTheLeftColumnOfThe2ndModule = 1er du 2e module colonne de gauche
na.portal.veacs.customKey.keyId.1stKeyOnTheRightColumnOfThe2ndModule = 1er du 2e module colonne de droite
na.portal.veacs.customKey.keyId.1stKeyOnTheLeftColumnOfThe3rdModule = 1er du 3ème module colonne de gauche
na.portal.veacs.customKey.keyId.1stKeyOnTheRightColumnOfThe3rdModule = 1er du 3ème module colonne de droite
na.portal.veacs.customKey.keyId.1stKeyOfThe1stPageOfThe1stModule = 1er de la 1ère page du 1er module
na.portal.veacs.customKey.keyId.1stKeyOfThe2ndPageOfThe1stModule = 1er de la 2ème page du 1er module
na.portal.veacs.customKey.keyId.1stKeyOfThe1stPageOfThe2ndModule = 1ère de la 1ère page du 2ème module
na.portal.veacs.customKey.keyId.1stKeyOfThe2ndPageOfThe2ndModule = 1ère de la 2ème page du 2ème module
na.portal.veacs.customKey.keyId.1stKeyOfThe1stPageOfThe3rdModule = 1ère de la 1ère page du 3ème module
na.portal.veacs.customKey.keyId.1stKeyOfThe2ndPageOfThe3rdModule = 1ère de la 2ème page du 3ème module
na.portal.veacs.customKey.keyId.1stKeyOfThe1stPageOfThe4thModule = 1ère de la 1ère page du 4ème module
na.portal.veacs.customKey.keyId.1stKeyOfThe2ndPageOfThe4thModule = 1ère de la 2ème page du 4ème module
na.portal.veacs.customKey.keyId.1stKey2ndPageOfTheScreen = 1ère de la 2ème page d'écran
na.portal.veacs.customKey.keyId.1stKey3rdPageOfTheScreen = 1er de la 3ème page d'écran
na.portal.veacs.customKey.keyId.1stKeyOfThe1stModule = 1er du 1er module
na.portal.veacs.customKey.keyId.1stKeyOfThe2ndModule = 1er du 2ème module
na.portal.veacs.customKey.keyId.1stSidekey = 1er key latéral