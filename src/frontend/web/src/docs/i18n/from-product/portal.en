# Security

security.authentication.error.credentials           = Wrong set of username and password.
security.authentication.error.lost.session          = Your session has expired. To perform the operation please login again.

# ERROR

error.401.title                                     = Error 401 Access not authorized
error.401.body                                      = Error 401 Access not authorized
error.403.title                                     = Error 403 Access forbidden
error.403.body                                      = Error 403 You are not allowed to access the page.
error.404.title                                     = Error 404 Page not found
error.404.body                                      = Error 404 Page not found
error.captcha.not.satisfied                         = Wrong captcha
error.captcha.expired                               = Captcha expired. Please, try again.

# NOSSIS

# LOGIN

nossis.login.title.key.context.portal               = Nossis
nossis.login.button.ok.text                         = Enter
nossis.login.box.user.label                         = Username
nossis.login.box.user.placeholder                   = Insert username
nossis.login.box.missingvalues                      = Missing username or password. Please check the inserted data.
nossis.login.box.locked                             = The account {0} is blocked. Contact support.
nossis.login.box.password.label                     = Password
nossis.login.box.password.placeholder               = Insert password
nossis.login.box.captcha.title                      = Digit the following text
nossis.login.box.remainingLoginAttempts             = Remaining login attempts
nossis.login.title.key.main.label                   = Suite NOSSIS
nossis.login.title.key.product.label                = Product(*)
nossis.login.title.key.slogan.label                 = Product slogan(*)
nossis.login.label.title.box                        = Login with your user account

nossis.footer.label                                 = Nossis © Altice Labs, SA. All rights reserved
nossis.footer.about.title                           = About
nossis.back.to.nossis                               = Back
nossis.othermodules                                 = Other modules
nossis.dashboard                                    = Home
nossis.aggregator                                   = NETWIN
nossis.wait                                         = Wait a moment, please...
nossis.resume                                       = NOSSIS resume

nossis.login.captcha.placeholder                    = Insert word
nossis.login.validate.credentials                   = Validating credentials... please wait.
nossis.login.captcha.newwords                       = Get new image
nossis.login.password.update.required               = Error, please change the password.
nossis.login.box.password.label.current             = Current password
nossis.login.box.password.label.new                 = New password
nossis.login.box.password.label.new.confirm         = Confirm new password
nossis.login.box.password.change.failed             = An error occurred while changing the password
nossis.login.box.password.change.success            = Password changed successfully
nossis.login.header.user.config.password.title      = Change password
nossis.login.mandatory.fields                       = Mandatory fields

nossis.go.to.home                                   = Start

# HEADER

nossis.header.help                                  = Help
nossis.header.logout                                = Logout
nossis.header.logout.started                        = Started {0}
nossis.header.logout.lastAccess                     = Last access {0}
nossis.header.sca                                   = Access management
nossis.header.brand                                 = NOSSIS
nossis.header.backto.dashboard                      = Back to portal
nossis.header.businessView                          = Business view
nossis.header.documentation                         = Documentation

# OPERATIONAL VIEW

nossis.operationalview                              = NOSSIS
nossis.operationalview.accessmodule                 = Open module
nossis.operationalview.hidemodulemenu               = Hide

# MODAL

nossis.modal.changepage.body                        = Data will be lost. Do you wish to proceed?
nossis.modal.changepage.header                      = Change page
nossis.modal.navigate.button.confirm                = Confirm
nossis.modal.navigate.button.cancel                 = Cancel
nossis.modal.navigate.button.close                  = Close

# ERRORS PAGES

nossis.error.serverTimePrefix                       = Server time
nossis.error.clientTimePrefix                       = Client time

nossis.error.sessionExpired.titleInfo               = Expired session
nossis.error.sessionExpired.description             = Your session expired. Data was not saved.
nossis.error.sessionExpired.action                  = Click to <a data-action-context="login">login</a> again.

nossis.error.notFound.titleInfo                     = Page not found - 404
nossis.error.notFound.description                   = We are sorry, but we could not find the page you& are looking for.
nossis.error.notFound.action                        = Go back to <a data-action-context="portal">portal</a>.

nossis.error.notAuthorized.titleInfo                = Access not authorized - 401
nossis.error.notAuthorized.description              = You have no permission to access this page with the given credentials.
nossis.error.notAuthorized.action                   = Contact the <a data-action-context="mailto:<EMAIL>">administrator</a> of the system or the <a data-action-context="mailto:<EMAIL>">support</a>.

nossis.error.badRequest.titleInfo                   = Invalid request - 400
nossis.error.badRequest.description                 = We are sorry, but we could not comply with your request.
nossis.error.badRequest.action                      = Contact the <a data-action-context="mailto:<EMAIL>">support</a>.

nossis.error.maintenance.titleInfo                  = In maintenance - 503
nossis.error.maintenance.description                = We are sorry, but the application is on maintenance. We promise to be brief.
nossis.error.maintenance.action                     = Contact the <a data-action-context="mailto:<EMAIL>">support</a>.

nossis.error.internalServerError.titleInfo          = Internal server error - 500
nossis.error.internalServerError.description        = We are sorry, but something gone wrong.
nossis.error.internalServerError.action             = Contact the <a data-action-context="mailto:<EMAIL>">support</a>.

nossis.error.serverError.errorInfoTitle             = Error details
nossis.error.serverError.errorSend                  = Copy the given error code and send it to the support.
nossis.error.serverError.errorCode                  = Error code

nossis.error.generic.title                          = An error occurred with this request.
nossis.error.generic.description                    = An error occurred with this request. Try again or contact administrators about this problem.

# RESUME

nossis.information.title                            = Information
nossis.information.nossis.title                     = NOSSIS
nossis.information.nossis.description               = Entry point for the products and solutions
nossis.information.mainfunctionalities.title        = Main Functionalities
nossis.information.mainfunctionalities.description  = Project of physical and logical network infrastructure, and  network construction and resource allocation workflows; Inventory of infrastructures and physical network resources (indoor and outdoor), with georeferenced outdoor infrastructures; Logical inventory of networks and services, including management of multiservice, multi-technology and multivendor resources and catalogues; Reservation and resource allocation facilities (supporting automated provisioning), with open APIs for OSS/BSS integration; Capacity management for physical and logical resources; Network reconciliation (via a mediation system).
nossis.information.customerbenefits.title           = Customer Benefits
nossis.information.customerbenefits.description     = Master reference inventory for the whole company;Easy navigation through all the information, from the physical infrastructure component to the service logic;E2E view of service composition, and compatibility with a wide range of technologies;Enables accurate project design and construction, including inputs from physical and logical capacity management and site surveys;Ease the integration with external systems for supporting other processes (including ful­llment and assurance processes, and others);Guarantee accurate inventory information, using reconciliation processes for maintaining up-to-date information.

# DOCUMENTATION

nossis.documentation                                = Documentation
nossis.documentation.userManual                     = User manual
nossis.documentation.operationalModules             = Operational modules
nossis.documentation.businessView                   = Business views
nossis.documentation.otherDocumentation             = Other manuals
nossis.documentation.dropdown.version               = Version

# LOAD PAGE MODAL
nossis.password.update.cancel.changes               = Cancel