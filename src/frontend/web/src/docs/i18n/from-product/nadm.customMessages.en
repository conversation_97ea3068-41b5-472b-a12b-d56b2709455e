customKey.keyId.1stSidekey = 1st sidekey
customKey.keyId.1stKeyOnTheLeftColumnOfThe1stmodule = 1st key on the left column of the 1st module
customKey.keyId.1stKeyOnTheRightColumnOfThe1stModule = 1st key on the right column of the 1st module
customKey.keyId.1stKeyOnTheLeftColumnOfThe2ndModule = 1st key on the left column of the 2nd module
customKey.keyId.1stKeyOnTheRightColumnOfThe2ndModule = 1st key on the right column of the 2nd module
customKey.keyId.1stKeyOnTheLeftColumnOfThe3rdModule = 1st key on the left column of the 3rd module
customKey.keyId.1stKeyOnTheRightColumnOfThe3rdModule = 1st key on the right column of the 3rd module
customKey.keyId.1stKeyOnTheLeftColumnOfThe4thModule = 1st key on the left column of the 4th module
customKey.keyId.1stKeyOnTheRightColumnOfThe4thModule = 1st key on the right column of the 4th module
customKey.keyId.1stKeyOnTheLeftColumnOfThe5thModule = 1st key on the left column of the 5th module
customKey.keyId.1stKeyOnTheRightColumnOfThe5thModule = 1st key on the right column of the 5th module
customKey.keyId.1stKeyOnTheLeftColumnOfThe6thModule = 1st key on the left column of the 6th module
customKey.keyId.1stKeyOnTheRightColumnOfThe6thModule = 1st key on the right column of the 6th module
customKey.keyId.1stKeyOfThe1stPageOfThe1stModule = 1st key of the 1st page of the 1st module
customKey.keyId.1stKeyOfThe2ndPageOfThe1stModule = 1st key of the 2nd page of the 1st module
customKey.keyId.1stKeyOfThe1stPageOfThe2ndModule = 1st key of the 1st page of the 2nd module
customKey.keyId.1stKeyOfThe2ndPageOfThe2ndModule = 1st key of the 2nd page of the 2nd module
customKey.keyId.1stKeyOfThe1stPageOfThe3rdModule = 1st key of the 1st page of the 3rd module
customKey.keyId.1stKeyOfThe2ndPageOfThe3rdModule = 1st key of the 2nd page of the 3rd module
customKey.keyId.1stKeyOfThe1stPageOfThe4thModule = 1st key of the 1st page of the 4th module
customKey.keyId.1stKeyOfThe2ndPageOfThe4thModule = 1st key of the 2nd page of the 4th module
customKey.keyId.1stKeyOfThe1stPageOfThe5thModule = 1st key of the 1st page of the 5th module
customKey.keyId.1stKeyOfThe2ndPageOfThe5thModule = 1st key of the 2nd page of the 5th module
customKey.keyId.1stKeyOfThe1stPageOfThe6thModule = 1st key of the 1st page of the 6th module
customKey.keyId.1stKeyOfThe2ndPageOfThe6thModule = 1st key of the 2nd page of the 6th module
customKey.keyId.1stKey2ndPageOfTheScreen = 1st key 2nd page of the screen
customKey.keyId.1stKey3rdPageOfTheScreen = 1st key 3rd page of the screen
customKey.keyId.1stKeyOfThe1stModule = 1st key of the 1st module
customKey.keyId.1stKeyOfThe2ndModule = 1st key of the 2nd module
customKey.type.opt.SpeedDial = Speed Dial
customKey.type.opt.CallPickup = Call Pickup
customKey.type.opt.blfWithSpeedDial = BLF with speedDial
customKey.type.opt.blfWithCallPickup = BLF with callPickup
customKey.type.opt.blfWithCallPickupAndSpeedDial = BLF with Call Pickup and Speed Dial
customKey.type.opt.blf = BLF
customKey.type.opt.aggregateLine = Aggregate Line
customKey.type.opt.intercomPaging = Intercom Paging
customKey.type.opt.multicastPaging = Multicast Paging
customKey.type.opt.callPark = Call Park
customKey.type.opt.monitoredCallPark = Monitored Call Park

NADM.genericPhone.callPark = Call Park Management
NADM.genericPhone.callPark.monitoring = Call Park Monitoring

NADM.genericPhone.customKeys.customKey.type.intercomPaging = Intercom Paging
NADM.genericPhone.customKeys.customKey.type.multicastPaging = Multicast Paging

NADM.genericPhone.multicastPaging = Multicast Paging
NADM.genericPhone.multicastPaging.address = Multicast address
NADM.genericPhone.multicastPaging.senderId = Sender Id
NADM.genericPhone.multicastPaging.codecs = Multicast Paging Codec
NADM.genericPhone.multicastPaging.priorityActive = Paging Priority Active
NADM.genericPhone.multicastPaging.pagingBarge = Paging Barge
NADM.genericPhone.multicastPaging.pagingBargeDefault = Paging Barge Default
NADM.genericPhone.multicastPaging.senderChannel = Canal du Multicast d''envoi
NADM.genericPhone.multicastPaging.nameDefault = Nom du canal Default
NADM.genericPhone.multicastPaging.channelDefault = Canal Default
NADM.genericPhone.multicastPaging.namePriority = Nom du canal Priority
NADM.genericPhone.multicastPaging.channelPriority = Canal Priority
NADM.genericPhone.multicastPaging.nameEmergency = Nom du canal Emergency
NADM.genericPhone.multicastPaging.channelEmergency = Canal Emergency
NADM.genericPhone.multicastPaging.listeningAddress1 = Multicast Listenging Address Priority 1
NADM.genericPhone.multicastPaging.listeningName1 = Multicast Listenging Name Priority 1
NADM.genericPhone.multicastPaging.listeningAddress2 = Multicast Listenging Address Priority 2
NADM.genericPhone.multicastPaging.listeningName2 = Multicast Listenging Address Priority 2
NADM.genericPhone.multicastPaging.listeningAddress3 = Multicast Listenging Address Priority 3
NADM.genericPhone.multicastPaging.listeningName3 = Multicast Listenging Name Priority 3
NADM.genericPhone.multicastPaging.listeningAddress4 = Multicast Listenging Address Priority 4
NADM.genericPhone.multicastPaging.listeningName4 = Multicast Listenging Name Priority 4
NADM.genericPhone.multicastPaging.listeningAddress5 = Multicast Listenging Address Priority 5
NADM.genericPhone.multicastPaging.listeningName5 = Multicast Listenging Name Priority 5
NADM.genericPhone.multicastPaging.listeningAddress6 = Multicast Listenging Address Priority 6
NADM.genericPhone.multicastPaging.listeningName6 = Multicast Listenging Name Priority 6
NADM.genericPhone.multicastPaging.listeningAddress7 = Multicast Listenging Address Priority 7
NADM.genericPhone.multicastPaging.listeningName7 = Multicast Listenging Name Priority 7
NADM.genericPhone.multicastPaging.listeningAddress8 = Multicast Listenging Address Priority 8
NADM.genericPhone.multicastPaging.listeningName8 = Multicast Listenging Name Priority 8
NADM.genericPhone.multicastPaging.listeningAddress9 = Multicast Listenging Address Priority 9
NADM.genericPhone.multicastPaging.listeningName9 = Multicast Listenging Name Priority 9
NADM.genericPhone.multicastPaging.listeningAddress10 = Multicast Listenging Address Priority 10
NADM.genericPhone.multicastPaging.listeningName10 = Multicast Listenging Name Priority 10

NADM.Equipment = Equipment
NADM.name = MAC Address
NADM.deviceInfo.time = Time
NADM.deviceInfo.time.dateformat = Date format
NADM.deviceInfo.time.timeformat = Time format
NADM.deviceInfo.time.timezone = Timezone
NADM.deviceInfo.daylightSavingTime = Daylight Saving Time
NADM.deviceInfo.daylightSavingTime.enable = Enable
NADM.deviceInfo.daylightSavingTime.type = Type
NADM.deviceInfo.daylightSavingTime.startWeekDay = Start Week Day
NADM.deviceInfo.daylightSavingTime.startWeekDayOcurrence = Start Week Day Ocurrence
NADM.deviceInfo.daylightSavingTime.startMonth = Start Month
NADM.deviceInfo.daylightSavingTime.startDay = Start Day
NADM.deviceInfo.daylightSavingTime.startHour = Start Hour
NADM.deviceInfo.daylightSavingTime.endWeekDay = End Week Day
NADM.deviceInfo.daylightSavingTime.endWeekDayOcurrence = End Week Day Ocurrence
NADM.deviceInfo.daylightSavingTime.endMonth = End Month
NADM.deviceInfo.daylightSavingTime.endDay = End Day
NADM.deviceInfo.daylightSavingTime.endHour = End Hour
NADM.deviceInfo.daylightSavingTime.offset = Offset
NADM.deviceInfo.daylightSavingTime.options = Options
NADM.deviceInfo.DNS = DNS
NADM.deviceInfo.DNS.primary = Primary DNS
NADM.deviceInfo.DNS.secondary = Secondary DNS
NADM.deviceInfo.NTP = NTP
NADM.deviceInfo.NTP.primary = Primary NTP
NADM.deviceInfo.NTP.secondary = Secondary NTP
NADM.deviceInfo.lan = Lan
NADM.deviceInfo.lan.addressingType = Addressing Type
NADM.deviceInfo.lan.ipAddress = IP Address
NADM.deviceInfo.lan.subnetMask = Subnet Mask
NADM.deviceInfo.lan.defaultGateway = Default Gateway
NADM.deviceInfo.ldap = LDAP
NADM.deviceInfo.ldap.enable = Enable
NADM.deviceInfo.ldap.host = Host
NADM.deviceInfo.ldap.port = Port
NADM.deviceInfo.ldap.username = Username
NADM.deviceInfo.ldap.password = Password
NADM.deviceInfo.ldap.base = Base
NADM.deviceInfo.ldap.sort = Sort
NADM.deviceInfo.ldap.nameFilter = Name Filter
NADM.deviceInfo.ldap.numberFilter = Number Filter
NADM.deviceInfo.ldap.displayNameAttributes = Display Name Attributes
NADM.deviceInfo.ldap.displayNumberAttributes = Display Number Attributes
NADM.deviceInfo.ldap.displayName = Display Name
NADM.deviceInfo.ldap.lookupDisplayName = Lookup Display Name
NADM.deviceInfo.ldap.lookup = Lookup
NADM.deviceInfo.ldap.clientDn = Client Dn
NADM.deviceInfo.ldap.firstNameFilter = First Name Filter
NADM.deviceInfo.ldap.lastNameFilter = Last Name Filter
NADM.deviceInfo.ldap.searchItem3 = searchItem3
NADM.deviceInfo.ldap.searchFilter3 = Search Filter 3
NADM.deviceInfo.ldap.searchItem4 = Search Item 4
NADM.deviceInfo.ldap.searchFilter4 = Search Filter 4
NADM.deviceInfo.ldap.displayAttributes = Display Attributes
NADM.deviceInfo.ldap.mode = Mode
NADM.deviceInfo.ldap.version = Version
NADM.deviceInfo.ldap.maxHits = Max Hits
NADM.deviceInfo.ldap.doInitialQuery = Do Initial Query
NADM.deviceInfo.ldap.predictText = Predict Text
NADM.deviceInfo.ldap.authMethod = Auth Method
NADM.deviceInfo.ldap.numberMapping = Number Mapping
NADM.deviceInfo.ldap.corpDirName = Corp Dir Name

NADM.genericPhone = Generic Phone
NADM.genericPhone.language = Language
NADM.genericPhone.voiceVlanId = Voice Vlan Id
NADM.genericPhone.dataVlanId = Data Vlan Id
NADM.genericPhone.speakerGain = Speaker Gain
NADM.genericPhone.handsetGain = Handset Gain
NADM.genericPhone.headsetGain = Headset Gain
NADM.genericPhone.userPassword = User Password
NADM.genericPhone.adminPassword = Admin Password
NADM.genericPhone.screensaverEnable = Screensaver Enable
NADM.genericPhone.screensaverWaitTime = Screensaver WaitTime
NADM.genericPhone.screensaverImageSelect = Screensaver ImageSelect
NADM.genericPhone.backgroundImageURL = Background Image URL
NADM.genericPhone.backgroundImageText = Background Text
NADM.genericPhone.backgroundImageSelect = Background Image Select
NADM.genericPhone.callPickupCode = CallPickup Code
NADM.genericPhone.callPickupAudioNotification = Call Pickup Audio Notification
NADM.genericPhone.callPickupTone = CallPickup Tone
NADM.genericPhone.blfSubscriptionTimeout = BLF Subscription Time Out
NADM.genericPhone.customKeyPrimaryLine = CustomKey Primary Line
NADM.genericPhone.customKeys = Custom Keys
NADM.genericPhone.customKeys.customKey = Custom Key
NADM.genericPhone.customKeys.customKey.keyId = Key ID
NADM.genericPhone.customKeys.customKey.line = Line
NADM.genericPhone.customKeys.customKey.name = Name
NADM.genericPhone.customKeys.customKey.monitor = Monitorf
NADM.genericPhone.customKeys.customKey.type = Custom key type
NADM.genericPhone.stationName = Station Name
NADM.genericPhone.dialplan = Dial Plan
NADM.genericPhone.dialplan.rule = Rule
NADM.genericPhone.dialplan.areaCode = Area Code
NADM.genericPhone.dialplan.areaCode.code = Code
NADM.genericPhone.dialplan.areaCode.minLength = Min Length
NADM.genericPhone.dialplan.areaCode.maxLength = Max Length
NADM.genericPhone.dialplan.blockOut = Block Out
NADM.genericPhone.dialplan.dialNow = Dial Now

NADM.firmwareUpgrade = Firmware Upgrade
NADM.firmwareUpgrade.enable = Enable
NADM.firmwareUpgrade.expectedVersion = Expected Version
NADM.firmwareUpgrade.migrateConfig = Migrate Config
NADM.firmwareUpgrade.scheduleEnabled = Schedule Enabled
NADM.firmwareUpgrade.scheduleStartTime = Schedule Start Time
NADM.firmwareUpgrade.scheduleEndTime = Schedule End Time
NADM.firmwareUpgrade.file = Firmware file
NADM.firmwareUpgrade.customUrl = Custum URL
NADM.firmwareUpgrade.associatedEquipmentEnabled = Associated Equipment Enabled
NADM.firmwareUpgrade.associatedEquipmentFile = Associated Equipment File
NADM.firmwareUpgrade.associatedEquipmentCustomUrl = Associated Equipment CustomUrl

NADM.services = Services
NADM.services.service = Service
NADM.services.service.name = Name
NADM.services.service.serviceData = Service Data
NADM.services.service.voiceProfile = Voice Profile
NADM.services.service.serviceData.voiceProfile = Voice Profile
NADM.services.service.voiceProfile.line = Line
NADM.services.service.voiceProfile.password = Password
NADM.services.service.voiceProfile.codecs = Codecs
NADM.service.service.voiceProfile.dialplan = Dial Plan
NADM.services.service.voiceProfile.proxy = Proxy
NADM.services.service.voiceProfile.outboundproxy = OutboundProxy
NADM.services.service.voiceProfile.displayName = Display Name
NADM.advancedData = Advanced Data

NADM.regex.mac = Error: invalid MAC address
NADM.regex.ip = Error: invalid IP address

NADM.genericduorSystem = Generic port system
NADM.genericduorSystem.guestSettings = Guest settings
NADM.genericduorSystem.enableDTMF = Enable DTMF
NADM.genericduorSystem.remotePin = Pin Remote
NADM.genericduorSystem.duorBellMode = Bell mode
NADM.genericduorSystem.callMode = Call mode