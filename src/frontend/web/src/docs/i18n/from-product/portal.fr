#Security

security.authentication.error.credentials           = Mauvais ensemble de nom d''utilisateur et de mot de passe.
security.authentication.error.lost.session          = Votre session expirée, connectez-vous à nouveau à Pelase.

#ERROR

error.401.title                                     = Erreur 401 Accès non autorisé
error.401.body                                      = Erreur 401 Accès non autorisé
error.403.title                                     = Erreur 403 Accès interdit
error.403.body                                      = Erreur 403 Accès interdit
error.404.title                                     = Erreur 404 - Page non trouvée
error.404.body                                      = Erreur 404 - Page non trouvée
error.captcha.not.satisfied                         = Captcha incorrect
error.captcha.expired                               = La captcha est expirée. Veuillez réessayer

# NOSSIS

# LOGIN

nossis.login.title.key.context.portal               = Nossis
nossis.login.button.ok.text                         = Entrer
nossis.login.box.user.label                         = Nom d''utilisateur
nossis.login.box.user.placeholder                   = Insérez le nom d''utilisateur
nossis.login.box.missingvalues                      = Nom d''utilisateur ou mot de passe manquant
nossis.login.box.locked                             = Le compte {0} est bloqué. Contactez le support.
nossis.login.box.password.label                     = Mot de passe
nossis.login.box.password.placeholder               = Insérez le mot de passe
nossis.login.box.captcha.title                      = Chiffrez le texte suivant
nossis.login.box.remainingLoginAttempts             = Tentatives de connexion restantes
nossis.login.title.key.main.label                   = Suite NOSSIS
nossis.login.title.key.product.label                = Produit(*)
nossis.login.title.key.slogan.label                 = Slogan du produit (*)
nossis.login.label.title.box                        = Connectez-vous avec votre compte utilisateur

nossis.footer.label                                 = Nossis © Altice Labs, SA. Tous les droits sont réservés
nossis.footer.about.title                           = Sur
nossis.back.to.nossis                               = Retour
nossis.othermodules                                 = Autres modules
nossis.dashboard                                    = Accueil
nossis.aggregator                                   = NOSSIS
nossis.wait                                         = Attendez un moment s''il vous plaît...
nossis.resume                                       = NOSSIS Résumé

nossis.login.captcha.placeholder                    = Insérer un mot
nossis.login.validate.credentials                   = Validation des informations d''identification ... veuillez patienter.
nossis.login.captcha.newwords                       = Obtenez une nouvelle image
nossis.login.password.update.required               = Erreur, veuillez changer le mot de passe.
nossis.login.box.password.label.current             = Mot de passe actuel
nossis.login.box.password.label.new                 = Nouveau mot de passe
nossis.login.box.password.label.new.confirm         = Confirmer le nouveau mot de passe
nossis.login.box.password.change.failed             = Une erreur s''est produite lors de la modification du mot de passe
nossis.login.box.password.change.success            = Le mot de passe a été changé avec succès
nossis.login.header.user.config.password.title      = Changer le mot de passe
nossis.login.mandatory.fields                       = Champs obligatoires

nossis.go.to.home                                   = Début

#HEADER

nossis.header.help                                  = Aidez-moi
nossis.header.logout                                = Se déconnecter
nossis.header.logout.started                        = Commencé {0}
nossis.header.logout.lastAccess                     = Dernier accès {0}
nossis.header.sca                                   = Gestion des accès
nossis.header.brand                                 = NOSSIS
nossis.header.backto.dashboard                      = Retour au portail
nossis.header.businessView                          = Vue d''entreprise
nossis.header.documentation                         = Documentation

#OPERATIONAL VIEW

nossis.operationalview                              = NOSSIS
nossis.operationalview.accessmodule                 = Module ouvert
nossis.operationalview.hidemodulemenu               = Cacher

#MODAL

nossis.modal.changepage.body                        = Les données seront perdues. Voulez-vous procéder?
nossis.modal.changepage.header                      = Changer de page
nossis.modal.navigate.button.confirm                = Confirmer
nossis.modal.navigate.button.cancel                 = Annuler
nossis.modal.navigate.button.close                  = Fermer

# ERRORS PAGES

nossis.error.serverTimePrefix                       = Heure du serveur
nossis.error.clientTimePrefix                       = Temps client

nossis.error.sessionExpired.titleInfo               = Session expirée
nossis.error.sessionExpired.description             =  Votre session a expiré. Les données n''ont pas été enregistrées.
nossis.error.sessionExpired.action                  = Cliquez pour <a data-action-context="login">s''identifier</a> encore.

nossis.error.notFound.titleInfo                     = Page non trouvée - 404
nossis.error.notFound.description                   = Nous sommes désolés, mais nous n''avons pas pu trouver la page que vous& sont en train de chercher.
nossis.error.notFound.action                        = Revenir à <a data-action-context="portal">portail</a>.

nossis.error.notAuthorized.titleInfo                = Accès non autorisé - 401
nossis.error.notAuthorized.description              = Vous n''êtes pas autorisé à accéder à cette page avec les informations d''identification fournies.
nossis.error.notAuthorized.action                   = Contacter le <a data-action-context="mailto:<EMAIL>">administratrice</a> du système ou de la <a data-action-context="mailto:<EMAIL>">soutien</a>.

nossis.error.badRequest.titleInfo                   = Demande non valide - 400
nossis.error.badRequest.description                 = Nous sommes désolés, mais nous n''avons pas pu répondre à votre demande.
nossis.error.badRequest.action                      = Contacter le <a data-action-context="mailto:<EMAIL>">soutien</a>.

nossis.error.maintenance.titleInfo                  = En maintenance - 503
nossis.error.maintenance.description                = Nous sommes désolés, mais l''application est en maintenance. Nous promettons d''être brefs.
nossis.error.maintenance.action                     = Contacter le <a data-action-context="mailto:<EMAIL>">soutien</a>.

nossis.error.internalServerError.titleInfo          = Erreur de serveur interne - 500
nossis.error.internalServerError.description        = Nous sommes désolés, mais quelque chose a mal tourné.
nossis.error.internalServerError.action             = Contacter le <a data-action-context="mailto:<EMAIL>">soutien</a>.

nossis.error.serverError.errorInfoTitle             = Détails de l''erreur
nossis.error.serverError.errorSend                  = Copiez le code d''erreur donné et envoyez-le au support.
nossis.error.serverError.errorCode                  = Code d''erreur

nossis.error.generic.title                          = Une erreur s''est produite avec cette demande.
nossis.error.generic.description                    = Une erreur s''est produite avec cette demande. Réessayez ou contactez les administrateurs à propos de ce problème.

#RESUME

nossis.information.title                            = Information
nossis.information.nossis.title                     = NOSSIS
nossis.information.nossis.description               = Point d''entrée pour les produits et solutions
nossis.information.mainfunctionalities.title        = Fonctionnalités principales
nossis.information.mainfunctionalities.description  = Projet d''infrastructure de réseau physique et logique, et construction de réseaux et workflows d''allocation des ressources; Inventaire des infrastructures et des ressources du réseau physique (intérieur et extérieur), avec des infrastructures extérieures géoréférencées; Inventaire logique des réseaux et des services, y compris la gestion des ressources et des catalogues multiservices, multi-technologies et multifournisseurs; Installations de réservation et d''allocation de ressources (prenant en charge l''approvisionnement automatisé), avec des API ouvertes pour l''intégration OSS / BSS; Gestion des capacités pour les ressources physiques et logiques; Réconciliation du réseau (via un système de médiation).
nossis.information.customerbenefits.title           = Avantages clients
nossis.information.customerbenefits.description     = Maîtriser l''inventaire de référence pour l''ensemble de l''entreprise; Navigation facile à travers toutes les informations, de la composante d''infrastructure physique à la logique de service; Vue E2E de la composition du service et compatibilité avec un large éventail de technologies; Permet une conception et une construction de projet précises, y compris les entrées de gestion des capacités physiques et logiques et études de site; Facilitez l''intégration avec des systèmes externes pour prendre en charge d''autres processus (y compris les processus d''exécution et d''assurance, et autres); Garantissez des informations d''inventaire précises, en utilisant des processus de rapprochement pour maintenir des informations à jour.

# DOCUMENTATION

nossis.documentation                                = Documentation
nossis.documentation.userManual                     = Manuel de l''Utilisateur
nossis.documentation.operationalModules             = Modules opérationnels
nossis.documentation.businessView                   = Vues d''entreprise
nossis.documentation.otherDocumentation             = Autres manuels
nossis.documentation.dropdown.version               = Version

# LOAD PAGE MODAL
nossis.password.update.cancel.changes               = Annuler