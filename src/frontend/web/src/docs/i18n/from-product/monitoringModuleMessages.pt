na.portal.monitoring.module.title                                               = Monitoria
na.portal.monitoring.module.description                                         = O módulo de monitoria fornece um conjunto de ferramentas ao utilizador para acompanhar a execução de Service Orders recebidas pelo sistema. Permite pesquisá-las por um conjunto de critérios, realizar ações sobre as que estejam num estado não final e reenviar notificações associadas a transições de estado ou previstas no fluxo. Também permite a criação de novas Service Orders com base nas operações e entidades criadas no módulo Catálogo.

na.portal.monitoring.nav.context                                                = Monitoria

na.portal.monitoring.home.title                                                 = Monitoria
na.portal.monitoring.home.description                                           = Módulo de Monitoria

# filters

na.portal.monitoring.home.filters.search                                        = Pesquisar
na.portal.monitoring.home.filters.search.by.id                                  = pesquisa por ID
na.portal.monitoring.home.filters.search.with.multiple.fields                   = pesquisa com múltiplos campos
na.portal.monitoring.home.filters.id.search.orderId                             = ID da ordem
na.portal.monitoring.home.filters.id.search.serviceId                           = ID do serviço
na.portal.monitoring.home.filters.id.search.input.placeholder.orderId           = Escreva o ID da ordem que deseja verificar
na.portal.monitoring.home.filters.id.search.input.placeholder.serviceId         = Escreva o ID do serviço que deseja verificar

na.portal.monitoring.home.filters.add.fields                                    = adicionar campos
na.portal.monitoring.home.filters.need.to.refresh                               = precisa de recarregar para atualizar as últimas 24 horas
na.portal.monitoring.home.filters.more                                          = mais
na.portal.monitoring.home.filters.less                                          = menos
na.portal.monitoring.home.filters.all                                           = todos

na.portal.monitoring.home.filters.items.state                                   = Estado
na.portal.monitoring.home.filters.items.category                                = Categoria
na.portal.monitoring.home.filters.items.system                                  = Sistema
na.portal.monitoring.home.filters.items.operation                               = Operação
na.portal.monitoring.home.filters.items.service.type                            = Tipo de serviço
na.portal.monitoring.home.filters.items.response.code                           = Código de resposta
na.portal.monitoring.home.filters.items.response.message                        = Mensagem de resposta

na.portal.monitoring.home.filters.state.validating                              = validating
na.portal.monitoring.home.filters.state.acknowledged                            = acknowledged
na.portal.monitoring.home.filters.state.canceled                                = cancelled
na.portal.monitoring.home.filters.state.held                                    = held
na.portal.monitoring.home.filters.state.in.progress                             = in progress
na.portal.monitoring.home.filters.state.partial                                 = partial
na.portal.monitoring.home.filters.state.pending                                 = pending
na.portal.monitoring.home.filters.state.rejected                                = rejected
na.portal.monitoring.home.filters.state.completed                               = completed
na.portal.monitoring.home.filters.state.failed                                  = failed

na.portal.monitoring.home.filters.system.na                                     = Network Activator
na.portal.monitoring.home.filters.system.om                                     = Order Manager

# date range picker

na.portal.monitoring.datarangepicker.message.refresh                            = precisa de recarregar para atualizar {0}
na.portal.monitoring.datarangepicker.message.LAST_30_MINUTES                    = os últimos 30 minutos
na.portal.monitoring.datarangepicker.message.LAST_HOUR                          = a última hora
na.portal.monitoring.datarangepicker.message.LAST_12_HOURS                      = as últimas 12 horas
na.portal.monitoring.datarangepicker.message.LAST_24_HOURS                      = as últimas 24 horas
na.portal.monitoring.datarangepicker.message.LAST_WEEK                          = a última semana
na.portal.monitoring.datarangepicker.message.LAST_MONTH                         = o último mês

na.portal.monitoring.datarangepicker.LAST_30_MINUTES                            = Últimos 30 minutos
na.portal.monitoring.datarangepicker.LAST_HOUR                                  = Última hora
na.portal.monitoring.datarangepicker.LAST_12_HOURS                              = Últimas 12 horas
na.portal.monitoring.datarangepicker.LAST_24_HOURS                              = Últimas 24 horas
na.portal.monitoring.datarangepicker.LAST_WEEK                                  = Última semana
na.portal.monitoring.datarangepicker.LAST_MONTH                                 = Último mês
na.portal.monitoring.datarangepicker.CUSTOM_RANGE                               = Outro intervalo

na.portal.monitoring.datarangepicker.dateformat                                 = DD-MM-YYYY HH:mm:ss
na.portal.monitoring.datarangepicker.applyButton                                = aplicar
na.portal.monitoring.datarangepicker.cancelButton                               = cancelar

na.portal.monitoring.datarangepicker.sunday                                     = Dom
na.portal.monitoring.datarangepicker.monday                                     = Seg
na.portal.monitoring.datarangepicker.tuesday                                    = Ter
na.portal.monitoring.datarangepicker.wednesday                                  = Qua
na.portal.monitoring.datarangepicker.thursday                                   = Qui
na.portal.monitoring.datarangepicker.friday                                     = Sex
na.portal.monitoring.datarangepicker.saturday                                   = Sáb

na.portal.monitoring.datarangepicker.january                                    = Janeiro
na.portal.monitoring.datarangepicker.february                                   = Fevereiro
na.portal.monitoring.datarangepicker.march                                      = Março
na.portal.monitoring.datarangepicker.april                                      = Abril
na.portal.monitoring.datarangepicker.may                                        = Maio
na.portal.monitoring.datarangepicker.june                                       = Junho
na.portal.monitoring.datarangepicker.july                                       = Julho
na.portal.monitoring.datarangepicker.august                                     = Agosto
na.portal.monitoring.datarangepicker.september                                  = Setembro
na.portal.monitoring.datarangepicker.october                                    = Outubro
na.portal.monitoring.datarangepicker.november                                   = Novembro
na.portal.monitoring.datarangepicker.december                                   = Dezembro

# orders table

na.portal.monitoring.orders.table.order.id                                      = ID da ordem
na.portal.monitoring.orders.table.category                                      = Categoria
na.portal.monitoring.orders.table.description                                   = Descrição
na.portal.monitoring.orders.table.state                                         = Estado
na.portal.monitoring.orders.table.customer                                      = Cliente
na.portal.monitoring.orders.table.technician                                    = Técnico
na.portal.monitoring.orders.table.system                                        = Sistema
na.portal.monitoring.orders.table.system.entry                                  = Entrada no sistema
na.portal.monitoring.orders.table.update.last                                  = Última atualização
na.portal.monitoring.orders.table.actions                                       = Ações
na.portal.monitoring.orders.table.actions.options                               = opções
na.portal.monitoring.orders.table.error.message.single.item                     = {0} no item da ordem {1} de {2}
na.portal.monitoring.orders.table.error.message.multiple.items                  = <b> Há {0} items da ordem em falha</b>. Expanda a linha para ver o que aconteceu.
na.portal.monitoring.orders.table.info.message.single.item                      = {0} no item da ordem {1} de {2}
na.portal.monitoring.orders.table.info.message.multiple.items                   = <b> {0} items da ordem tem mensagens</b>. Expanda a linha para ver o que aconteceu.
na.portal.monitoring.orders.table.info.message.validating.order                 = Ordem está a ser validada

# orders item table

na.portal.monitoring.order.items.table.operation.service                        = Operação / Serviço
na.portal.monitoring.order.items.table.description                              = Descrição
na.portal.monitoring.order.items.table.state                                    = Estado
na.portal.monitoring.order.items.table.has.process                              = Tem processo?
na.portal.monitoring.order.items.table.service.id                               = ID do serviço
na.portal.monitoring.order.items.table.actions                                  = Ações

# order item resume

na.portal.monitoring.order.item.resume.table.operation.category                 = Operação / Categoria
na.portal.monitoring.order.item.resume.table.service.id                         = ID do Serviço
na.portal.monitoring.order.item.resume.table.state                              = Estado
na.portal.monitoring.order.item.resume.table.duration                           = Duração
na.portal.monitoring.order.item.resume.table.end.date                           = Última atualização
na.portal.monitoring.order.item.resume.table.has.process                        = Tem processo
na.portal.monitoring.order.item.resume.table.actions                            = Ações

# orders state

na.portal.monitoring.order.state.sent                                           = enviado
na.portal.monitoring.order.state.failed                                         = falhado

# orders response retry table

na.portal.monitoring.order.response.retry.table.retry.number                    = #
na.portal.monitoring.order.response.retry.table.date.time                       = Data / hora
na.portal.monitoring.order.response.retry.table.state                           = Estado
na.portal.monitoring.order.response.retry.table.state.sent                      = enviado
na.portal.monitoring.order.response.retry.table.state.failed                    = falhado
na.portal.monitoring.order.response.retry.table.message                         = Mensagem

# orders response page

na.portal.monitoring.order.response.label.response                              = Resposta
na.portal.monitoring.order.response.label.destination                           = Para
na.portal.monitoring.order.response.label.orderHistoryCheck                     = Pode verificar o <b>histórico</b>.
na.portal.monitoring.order.response.label.responseSuccess                       = Resposta enviada com sucesso.
na.portal.monitoring.order.response.label.delivering                            = Enviando resposta.
na.portal.monitoring.order.response.label.responseFailed                        = Resposta falhada. Erro {0}.
na.portal.monitoring.order.response.tooltip.seeTable                            = Ver em tabela
na.portal.monitoring.order.response.tooltip.seeCode                             = Ver em código
na.portal.monitoring.order.activity.response.label.responseFailed               = Resposta falhada. Erro <b>{0}</b>.
na.portal.monitoring.order.activity.response.label.inProgress                   = Resposta em progresso.
na.portal.monitoring.order.activity.response.label.success                      = Resposta enviada com sucesso.
na.portal.monitoring.order.button.summary                                       = Sumarizar
na.portal.monitoring.order.summary.modal.title                                  = Sumário da ordem

# orders notification details table

na.portal.monitoring.order.notification.details.table.errorIssues               = {0} de {1} com erro.
na.portal.monitoring.order.notification.details.table.system                    = Sistema
na.portal.monitoring.order.notification.details.table.systems                   = Sistemas
na.portal.monitoring.order.notification.details.table.attempt                   = Tentativa
na.portal.monitoring.order.notification.details.table.attempts                  = Tentativas
na.portal.monitoring.order.notification.details.table.dateTime                  = Data
na.portal.monitoring.order.notification.details.table.type                      = Tipo
na.portal.monitoring.order.notification.details.table.summary                   = Resumo
na.portal.monitoring.order.notification.details.table.state                     = Estado
na.portal.monitoring.order.notification.details.table.logs_history              = Histórico de Logs
na.portal.monitoring.order.notification.details.table.state.success             = Sucesso
na.portal.monitoring.order.notification.details.table.state.error               = Erro

# orders validation page

na.portal.monitoring.order.validation.response.label.response                   = Modelo de informação
na.portal.monitoring.order.validation.messages.table.code                       = Código
na.portal.monitoring.order.validation.messages.table.message                    = Mensagem
na.portal.monitoring.order.validation.messages.table.description                = Descrição
na.portal.monitoring.order.validation.messages.global                           = <b>{0} ({1} - {2}).</b> Veja abaixo para mais detalhes.

# orders

na.portal.monitoring.order.from.system                                          = por
na.portal.monitoring.order.status.validation                                    = Validação
na.portal.monitoring.order.status.execution                                     = Execução
na.portal.monitoring.order.status.response                                      = Resposta
na.portal.monitoring.order.status.notifications                                 = Notificações

na.portal.monitoring.order.date.time.second                                     = segundo
na.portal.monitoring.order.date.time.seconds                                    = segundos
na.portal.monitoring.order.date.time.minute                                     = minuto
na.portal.monitoring.order.date.time.minutes                                    = minutos
na.portal.monitoring.order.date.time.hour                                       = hora
na.portal.monitoring.order.date.time.hours                                      = horas
na.portal.monitoring.order.date.time.day                                        = dia
na.portal.monitoring.order.date.time.days                                       = dias
na.portal.monitoring.order.date.time.week                                       = semana
na.portal.monitoring.order.date.time.weeks                                      = semanas
na.portal.monitoring.order.date.time.month                                      = há um mês
na.portal.monitoring.order.date.time.months                                     = meses

na.portal.monitoring.order.date.day.today                                       = hoje
na.portal.monitoring.order.date.day.yesterday                                   = ontem
na.portal.monitoring.order.date.weekday.0                                       = domingo
na.portal.monitoring.order.date.weekday.1                                       = segunda-feira
na.portal.monitoring.order.date.weekday.2                                       = terça-feira
na.portal.monitoring.order.date.weekday.3                                       = quarta-feira
na.portal.monitoring.order.date.weekday.4                                       = quinta-feira
na.portal.monitoring.order.date.weekday.5                                       = sexta-feira
na.portal.monitoring.order.date.weekday.6                                       = sábado

na.portal.monitoring.order.date.format.full                                     = {0} às {1}h
na.portal.monitoring.order.date.format.short                                    = há {0} {1}
na.portal.monitoring.order.date.format.duration                                 = {0} {1}
na.portal.monitoring.order.date.format.relativeTime                             = {0}, em {1} de {2}

# Order activity

na.portal.monitoring.order.tooltip.skip                                         = avançar
na.portal.monitoring.order.tooltip.rollback                                     = reverter
na.portal.monitoring.order.tooltip.retry                                        = retentar
na.portal.monitoring.order.tooltip.retry_new_request                            = retentar
na.portal.monitoring.order.tooltip.restart                                      = recomeçar
na.portal.monitoring.order.tooltip.report                                       = relatório
na.portal.monitoring.order.tooltip.complete                                     = concluir

# Order activity external System interaction
na.portal.monitoring.order.ext.sys.interaction.origin                         = De
na.portal.monitoring.order.ext.sys.interaction.destination                    = Para
na.portal.monitoring.order.ext.sys.interaction.date                           = Data
na.portal.monitoring.order.ext.sys.interaction.details                        = Detalhes
na.portal.monitoring.order.ext.sys.interaction.request                        = Pedido
na.portal.monitoring.order.ext.sys.interaction.sync.response                  = Resposta síncrona
na.portal.monitoring.order.ext.sys.interaction.async.response                 = Resposta assíncrona

# modal

na.portal.monitoring.order.modal.button.complete                                = Concluir
na.portal.monitoring.order.modal.button.retry                                   = Retentar
na.portal.monitoring.order.modal.button.skip                                    = Avançar
na.portal.monitoring.order.modal.button.rollback                                = Reverter
na.portal.monitoring.order.modal.button.cancel                                  = Cancelar
na.portal.monitoring.order.modal.button.confirm                                 = Confirmar
na.portal.monitoring.order.modal.button.restart                                 = Reinicar
na.portal.monitoring.order.modal.label.skip                                     = Diga-nos porque quer saltar esta atividade
na.portal.monitoring.order.modal.label.rollback                                 = Diga-nos porque quer reverter esta atividade
na.portal.monitoring.order.modal.label.retry                                    = Diga-nos porque fez o retry
na.portal.monitoring.order.modal.label.complete                                 = Diga-nos porque concluiu a atividade
na.portal.monitoring.order.modal.label.restart                                  = Diga-nos porque fez o restart
na.portal.monitoring.order.modal.label.finish                                   = Diga-nos porque você terminou o pedido
na.portal.monitoring.order.modal.label.cancel                                   = Diga-nos porque cancelou o pedido
na.portal.monitoring.order.modal.label.cancelWithRollback                       = Diga-nos porque cancelou o pedido
na.portal.monitoring.order.modal.label.remove                                   = Diga-nos porque removeu o pedido
na.portal.monitoring.order.modal.title.skip                                     = Avançar {0}
na.portal.monitoring.order.modal.title.rollback                                 = Reverter {0}
na.portal.monitoring.order.modal.title.retry                                    = Retentar {0}
na.portal.monitoring.order.modal.title.complete                                 = Concluir {0}
na.portal.monitoring.order.modal.title.restart                                  = Reiniciar {0}
na.portal.monitoring.order.modal.title.skip.individual							= Avançar {0}
na.portal.monitoring.order.modal.title.skip.massive								= Avançar ordens
na.portal.monitoring.order.modal.title.rollback.individual					    = Reverter {0}
na.portal.monitoring.order.modal.title.rollback.massive						    = Reverter ordens
na.portal.monitoring.order.modal.title.retry.individual							= Retentar {0}
na.portal.monitoring.order.modal.title.retry.massive							= Retentar ordens
na.portal.monitoring.order.modal.title.cancel.individual                        = Cancelar {0}
na.portal.monitoring.order.modal.title.cancel.massive                           = Cancelar ordens
na.portal.monitoring.order.modal.title.cancelWithRollback.individual            = Cancelar e reverter {0}
na.portal.monitoring.order.modal.title.cancelWithRollback.massive               = Cancelar e reverter ordens
na.portal.monitoring.order.modal.title.remove.individual                        = Remover {0}
na.portal.monitoring.order.modal.title.remove.massive                           = Remover ordens
na.portal.monitoring.order.modal.title.report                                   = Notas e ações sobre {0}
na.portal.monitoring.order.modal.emptyNotes                                     = (sem notas)
na.portal.monitoring.order.modal.tab.responseDetail                             = Detalhe da resposta
na.portal.monitoring.order.modal.tab.response                                   = Resposta
na.portal.monitoring.order.modal.tab.request                                    = Pedido
na.portal.monitoring.order.modal.tab.history                                    = Histórico

na.portal.monitoring.orders.modal.alert.partial.action.title                   = Esta ação será parcialmente aplicada a estas ordens
na.portal.monitoring.orders.modal.alert.partial.action.text                    = Algumas ordens não serão processadas, pois o seu estado atual é incompatível com esta ação.

na.portal.monitoring.notifications.modals.alert.confirm.retry.system.title         = Reenviar notificação
na.portal.monitoring.notifications.modals.alert.confirm.retry.system.content       = Tem a certeza que pretende notificar este sistema novamente?
na.portal.monitoring.notifications.modals.alert.confirm.retry.all.systems.title    = Reenviar notificações
na.portal.monitoring.notifications.modals.alert.confirm.retry.all.systems.content  = Tem a certeza que pretende notificar todos os sistemas novamente?
na.portal.monitoring.notifications.modals.details.error                            = Resposta Falhada.
na.portal.monitoring.notifications.modals.details.success                          = Resposta enviada com sucesso.

# external system interactions
na.portal.monitoring.ext.sys.interactions.table.datetime                        = Data / hora
na.portal.monitoring.ext.sys.interactions.table.description                     = Descrição
na.portal.monitoring.ext.sys.interactions.table.author                          = Feito por
na.portal.monitoring.ext.sys.interactions.table.actions                         = Ações
na.portal.monitoring.ext.sys.interactions.table.raw.data                        = Dados brutos
na.portal.monitoring.ext.sys.interactions.details                               = Detalhes

na.portal.monitoring.ext.sys.interactions.filter.title                          = Pode filtrar dados por
na.portal.monitoring.ext.sys.interactions.filter.task.manual                    = Tarefas manuais
na.portal.monitoring.ext.sys.interactions.filter.task.automatic                 = Tarefas automáticas
na.portal.monitoring.ext.sys.interactions.filter.notifications                  = Notificações

na.portal.monitoring.ext.sys.tab.execution                                      = Execução
na.portal.monitoring.ext.sys.tab.history                                        = Histórico

# splitter

na.portal.monitoring.splitter.tip.close                                         = Fechar
na.portal.monitoring.splitter.tip.open                                          = Abrir
na.portal.monitoring.splitter.tip.resize                                        = Redimensionar
na.portal.monitoring.splitter.tip.slide                                         = Slide Aberto

# notification

na.portal.monitoring.action.complete.success.title                              = Atividade concluída com sucesso
na.portal.monitoring.action.complete.success.content                            = Atividade "{0}" concluída com sucesso
na.portal.monitoring.action.success.title                                       = {0} com sucesso
na.portal.monitoring.action.success.content                                     = Ação de {0} executada com sucesso sobre atividade "{1}"
na.portal.monitoring.order.action.skip.success.title                            = Ordem avançada com sucesso
na.portal.monitoring.order.action.skip.success.content                          = Ação de avançar ordem {0} executada com sucesso
na.portal.monitoring.order.action.rollback.success.title                        = Reversão de ordem com sucesso
na.portal.monitoring.order.action.rollback.success.content                      = Ação de reverter ordem {0} executada com sucesso
na.portal.monitoring.order.action.remove.success.title                          = Remoção de ordem com sucesso
na.portal.monitoring.order.action.remove.success.content                        = Ação de remover ordem {0} executada com sucesso
na.portal.monitoring.order.massive.action.skip.success.title                    = Ordens avançadas com sucesso
na.portal.monitoring.order.massive.action.skip.success.content                  = Ação de avançar ordens executada com sucesso
na.portal.monitoring.order.massive.action.skip.partial.title                    = Ordens parcialmente ignoradas
na.portal.monitoring.order.massive.action.skip.partial.content                  = Ação de avançar ordens executada parcialmente
na.portal.monitoring.order.massive.action.rollback.success.title                = Reversão de ordens com sucesso
na.portal.monitoring.order.massive.action.rollback.success.content              = Ação de reverter ordens executada com sucesso
na.portal.monitoring.order.massive.action.rollback.partial.title                = Ordens parcialmente revertidas
na.portal.monitoring.order.massive.action.rollback.partial.content              = Ação de reversão de ordem realizada parcialmente
na.portal.monitoring.order.action.retry.success.title                           = Ordem de retentar com sucesso
na.portal.monitoring.order.action.retry.success.content                         = Ação de retentar ordem {0} executada com sucesso
na.portal.monitoring.order.massive.action.retry.success.title                   = Ordens retentadas com sucesso
na.portal.monitoring.order.massive.action.retry.success.content                 = Ação de retentar ordens executada com sucesso
na.portal.monitoring.order.massive.action.retry.partial.title                   = Ordens parcialmente retentadas
na.portal.monitoring.order.massive.action.retry.partial.content                 = Ação de retentar ordens realizada parcialmente
na.portal.monitoring.order.action.cancel.success.title                          = Ordem cancelada com sucesso
na.portal.monitoring.order.action.cancel.success.content                        = Ação de cancelar ordem {0} executada com sucesso
na.portal.monitoring.order.massive.action.cancel.success.title                  = Ordens canceladas com sucesso
na.portal.monitoring.order.massive.action.cancel.success.content                = Ação de cancelar ordens executada com sucesso
na.portal.monitoring.order.massive.action.cancel.partial.title                  = Ordens parcialmente canceladas
na.portal.monitoring.order.massive.action.cancel.partial.content                = Ação de cancelamento de ordem realizada parcialmente
na.portal.monitoring.order.massive.action.remove.success.title                  = Ordens removidas com sucesso
na.portal.monitoring.order.massive.action.remove.success.content                = Ação de remover ordens executada com sucesso
na.portal.monitoring.order.massive.action.remove.partial.title                  = Ordens parcialmente removidas
na.portal.monitoring.order.massive.action.remove.partial.content                = Ação de remover ordens realizada parcialmente
na.portal.monitoring.order.massive.action.invalid.states.title                  = Ação em Massa: Estados Inválidos
na.portal.monitoring.order.massive.action.failed.orders                         = Ação não aplicada a {0} ordem(ens)
na.portal.monitoring.order.massive.action.selected.invalid                      = Esta ordem está num estado inválido para esta ação
na.portal.monitoring.order.massive.action.all.selected.invalid                  = Todas as ordens selecionadas estão num estado inválido para esta ação
na.portal.monitoring.order.select.all.notification.title                        = Ordens selecionadas
na.portal.monitoring.order.select.all.notification.content                      = {0} / {1} orders selecionadas
na.portal.monitoring.order.select.all.notification.select.all                   = selecionar todas
na.portal.monitoring.order.select.all.notification.unselect.all                 = desselecionar todas
na.portal.monitoring.order.view.timeline.graph.not.found.title                  = Grafo e linha do tempo não encontrados
na.portal.monitoring.order.view.timeline.graph.not.found.message                = O grafo e linha do tempo ainda não foram criados, por favor tente mais tarde
na.portal.monitoring.order.view.timeline.graph.unexpected.error.title           = Ocorreu um erro inesperado!
na.portal.monitoring.order.view.timeline.graph.unexpected.error.message         = Não foi possível carregar o grafo e linha do tempo
na.portal.monitoring.order.systems.notification.retry.success.title             = Sistemas notificados com sucesso.
na.portal.monitoring.order.systems.notification.retry.success.content           = Todos os sistemas da notificação "{0}" notificados com sucesso.
na.portal.monitoring.order.system.notification.retry.success.title              = Sistema notificado com sucesso.
na.portal.monitoring.order.system.notification.retry.success.content            = Sistema "{0}" notificado com sucesso.

# timeline notification

na.portal.monitoring.timeline.activity.notification.system.retry            = Reenviar
na.portal.monitoring.timeline.activity.notification.system.retry.title      = Pedido de nova tentativa enviado
na.portal.monitoring.timeline.activity.notification.system.retry.content    = Pedido de nova tentativa da notificação {0} enviado para o sistema {1}.
na.portal.monitoring.timeline.activity.notification.resume                  = 1 sistema
na.portal.monitoring.timeline.activity.notification.resume.plural           = {0} sistemas
na.portal.monitoring.timeline.activity.notification.resume.missing          = Não há informações disponiveis sobre notificações.
na.portal.monitoring.timeline.activity.notification.resume.issues           = {0} de {1} com erros.

# search date filter by

na.portal.monitoring.search.date.filterby                                       = filtrado por
na.portal.monitoring.search.date.filterby.systemEntry                           = entrada no sistema
na.portal.monitoring.search.date.filterby.lastUpdate                            = última atualização

# operations

na.portal.monitoring.button.bulkoperations.group                  = Selecionar a ação pretendida
na.portal.monitoring.bulkoperations.button                        = operações massivas
na.portal.monitoring.order.possible.actions                       = ações possiveis
na.portal.monitoring.order.action.cancel                          = cancelar
na.portal.monitoring.order.action.complete                        = completar
na.portal.monitoring.order.action.retry                           = tentar novamente
na.portal.monitoring.order.action.skip                            = avançar
na.portal.monitoring.order.action.rollback                        = reverter
na.portal.monitoring.order.action.pause                           = pausar
na.portal.monitoring.order.action.cancelWithRollback              = cancelar e reverter
na.portal.monitoring.order.action.remove                          = remover
na.portal.monitoring.bulkoperations.tags.button                   = gerir tags
na.portal.monitoring.bulkoperations.delete.button                 = eliminar

# order operation attribute view
# this section comes from operations catalog ( na.portal.operationscatalog.* = na.portal.monitoring.operation.view.* )

na.portal.monitoring.operation.view.tab.characteristics                                         = Características
na.portal.monitoring.operation.view.tab.section.general.characteristics                         = Caracterização base
na.portal.monitoring.operation.view.tab.section.input.attributes                                = Atributos de entrada
na.portal.monitoring.operation.view.tab.section.execution.attributes                            = Atributos de execução
na.portal.monitoring.operation.view.tab.section.output.attributes                               = Atributos de saída

# this part comes from opercat dynamic form DB
na.portal.monitoring.operation.view.version  = Versão
na.portal.monitoring.operation.view.tags  = Tags
na.portal.monitoring.operation.view.workflow  = Workflow

na.portal.monitoring.operation.view.table.column.label.attributes_entities = Atributos / Entidades
na.portal.monitoring.operation.view.table.column.label.attributes = Atributos
na.portal.monitoring.operation.view.table.column.label.cardinality = Cardinalidade
na.portal.monitoring.operation.view.table.column.label.validationRules = Regras de validação
na.portal.monitoring.operation.view.table.column.label.enrichmentRule = Regras de enriquecimento
na.portal.monitoring.operation.view.table.column.label.value = Valor

# order create

na.portal.monitoring.order.create.definition                                = Definição
na.portal.monitoring.order.create.label                                     = Criar
na.portal.monitoring.order.create.button.label.execute                      = executar
na.portal.monitoring.order.create.sidebar.title                             = Definição
na.portal.monitoring.order.create.sidebar.system                            = Sistema
na.portal.monitoring.order.create.sidebar.user                              = Utilizador
na.portal.monitoring.order.create.sidebar.operations                        = Operações
na.portal.monitoring.order.create.sidebar.version                           = Versão
na.portal.monitoring.order.create.sidebar.placeholder.missing.system        = Deve selecionar um sistema
na.portal.monitoring.order.create.sidebar.placeholder.select.system         = Selecione um sistema
na.portal.monitoring.order.create.sidebar.placeholder.select.user           = Selecione um utilizador
na.portal.monitoring.order.create.sidebar.placeholder.select.operations     = Selecione as operações pretendidas
na.portal.monitoring.order.create.notification.failure.message              = Por favor verifique os campos assinalados
na.portal.monitoring.order.create.notification.failure.title                = Não é possível guardar o formulário
na.portal.monitoring.order.create.button.optional.parameters.label          = adicionar parâmetros opcionais

na.portal.monitoring.order.create.attribute.form.order.parameters.order.header.title                          = Ordem
na.portal.monitoring.order.create.attribute.form.operation.parameters.base.characteristics.header.title       = Características base
na.portal.monitoring.order.create.attribute.form.operation.parameters.specific.characteristics.header.title   = Características específicas

na.portal.monitoring.order.create.attribute.form.related-party.attributes.header.title                  = Atributos
na.portal.monitoring.order.create.attribute.form.related-party.properties.input.key                     = Chave
na.portal.monitoring.order.create.attribute.form.related-party.properties.button.add                    = Adicionar
na.portal.monitoring.order.create.attribute.form.related-party.properties.input.key.error.missing       = O campo "chave" é obrigatório
na.portal.monitoring.order.create.attribute.form.related-party.properties.input.key.error.duplicated    = A chave não pode ser duplicada
na.portal.monitoring.order.create.attribute.form.related-party.properties.button.remove                 = Remover

na.portal.monitoring.order.create.attribute.form.notification.order.required.title                                   = Deve selecionar um sistema e uma operação
na.portal.monitoring.order.create.attribute.form.notification.order.required.description                             = Por favor valide se os campos estão selecionados.
na.portal.monitoring.order.create.attribute.form.notification.order.invalid.version.state.title                      = Estado da versão inválido
na.portal.monitoring.order.create.attribute.form.notification.order.invalid.version.state.description                = Por favor selecione uma versão com um estado válido.
na.portal.monitoring.order.create.attribute.form.notification.order.no.attributes.description                        = Esta operação não tem atributos associados

na.portal.monitoring.order.create.attribute.form.parameter.tab.order     = parâmetros da ordem
na.portal.monitoring.order.create.attribute.form.parameter.tab.operation = parâmetros da operação

na.portal.monitoring.order.create.attribute.form.button.submit.message   = Ordem submetida com sucesso
