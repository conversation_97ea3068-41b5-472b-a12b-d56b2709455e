na.portal.go.module.title = GO
na.portal.go.module.description = The Generic Operations module allows an operator to invoke any operation that is available on the NBI.<br>A system administrator can manage what operations are available for each group of users, and he can also define more derived operations with more limited options to a more specific group of users.
na.portal.go.mega.menu.title = Operations
na.portal.go.mega.menu.navinitial = Homepage

na.portal.go.admin.label.title = GO Administration
na.portal.go.admin.label.subtitle = Generic Operations Management

na.portal.go.home.label.title = GO
na.portal.go.home.label.subtitle = Generic Operations
na.portal.go.home.mainnav.go = GO
na.portal.go.home.mainnav.admin = GO Administration
na.portal.go.home.menu.admin = Administration
na.portal.go.mainnav.admin.title = Administration

###########FIELDS###########
na.portal.go.field.beginDate.label = Begin Date/Time
na.portal.go.field.endDate.label = End Date/Time
na.portal.go.family.field.label = Family
na.portal.go.family.selects.empty.option = All Families
na.portal.go.field.orderKey.label = OPK
na.portal.go.field.orderKey.placeholder = OPK
na.portal.go.rfs.field.label = RFS
na.portal.go.rfs.selects.empty.option = All RFS
na.portal.go.state.field.label = State
na.portal.go.state.selects.empty.option = All States
na.portal.go.operation.field.label = Operation
na.portal.go.operation.selects.empty.option = All Operations
na.portal.go.field.operation.label = Operation
na.portal.go.field.operation.placeholder = Operation Name
na.portal.go.field.operationName.label = Operation Name
na.portal.go.field.user.label = Username
na.portal.go.field.user.placeholder = Username
na.portal.go.field.field.label = Family
na.portal.go.field.operationName.placeholder =
na.portal.go.field.modificationBeginDate.label = Modified since
na.portal.go.field.modificationEndDate.label = to

#Datatables
na.portal.go.datatables.column.label.lastModification = Last modification
na.portal.go.datatables.column.label.stateAction = State/Action
na.portal.go.datatables.column.label.insertionDate = Date/Time
na.portal.go.datatables.column.label.orderKey = OPK
na.portal.go.datatables.column.label.username = Username
na.portal.go.datatables.column.label.family = Family
na.portal.go.datatables.column.label.operation = Operation
na.portal.go.datatables.column.label.rfs = RFS
na.portal.go.datatables.column.label.state = State
na.portal.go.datatables.column.label.name = Name
na.portal.go.datatables.column.label.type = Type
na.portal.go.datatables.column.label.value = Value
na.portal.go.datatables.column.label.messageType = Parameter
na.portal.go.datatables.column.label.status = State
na.portal.go.datatables.column.label.errorMessage = Message
na.portal.go.datatables.column.label.actions = Actions
na.portal.go.datatables.column.label.request = Request
na.portal.go.datatables.column.label.response = Response
na.portal.go.datatables.column.label.description = Description
na.portal.go.datatables.column.label.lrName = LR Name
na.portal.go.datatables.action.label.edit = Edit
na.portal.go.datatables.action.label.add = Add
na.portal.go.datatables.action.label.remove = Remove


#Execution state

na.portal.go.operation.state.PENDING.label = Pending
na.portal.go.operation.state.EXECUTING.label = Executing
na.portal.go.operation.state.TERMINATED.label = Terminated
na.portal.go.operation.state.STARTED.label = Started
na.portal.go.operation.state.NOT_STARTED.label = Not Started
na.portal.go.operation.state.ERROR.label = Error

#Admin Execution state
na.portal.go.adminoperation.state.ACTIVE.label = ON
na.portal.go.adminoperation.state.INACTIVE.label = OFF

na.portal.go.operation.details.characteristics = Characteristics
na.portal.go.operation.details.label.operation = Operation
na.portal.go.operation.details.label.family = Family
na.portal.go.operation.details.label.rfs = RFS
na.portal.go.operation.details.label.responseurl = Response URL
na.portal.go.operation.details.label.username = Username
na.portal.go.operation.details.label.description = Description
na.portal.go.operation.details.label.state = State
na.portal.go.operation.details.label.payload = Payload
na.portal.go.operation.details.label.request = Request
na.portal.go.operation.details.label.response = Response
na.portal.go.operation.details.label.nooperation = No Operation
na.portal.go.operation.details.payload.empty = No response
na.portal.go.operation.details.breadcrumb.order = Order
na.portal.go.operation.details.label.lrname = Resource
na.portal.go.operation.details.label.lrid = Resource Id
na.portal.go.operation.details.label.status = Status
na.portal.go.operation.details.label.responsecode = Resp. Code
na.portal.go.operation.details.label.responsevalue = Resp. Message
na.portal.go.operation.details.label.message = Message
na.portal.go.operation.details.label.descriptiontime = Date/Time
na.portal.go.operation.details.title.detailsmodal = Detail Log/Data
na.portal.go.operation.details.log = Log
na.portal.go.operation.details.data = Data
na.portal.go.operation.details.label.empty = empty
na.portal.go.operation.details.state.error = Error
na.portal.go.operation.details.state.not_executed = Not Executed
na.portal.go.operation.details.state.skip = Skip
na.portal.go.operation.details.state.executed = Executed
na.portal.go.operation.details.see.code = See code
na.portal.go.operation.details.see.table = See table
na.portal.go.operation.create.title = Execute generic operation
na.portal.go.operation.create.label = Execute generic operation
na.portal.go.operation.create.legend.requiredfields.label = Mandatory fields
na.portal.go.operation.create.tab.characteristics.label = Characteristics
na.portal.go.operation.create.tab.characteristics.description.title = Operation type
na.portal.go.operation.create.tab.characteristics.description.text = Data that allow the identification of Operation.
na.portal.go.operation.create.tab.operation.label = Operation
na.portal.go.operation.create.confirm.cancel = Are you sure to cancel the operation? Data will be lost.
na.portal.go.operation.create.error.requiredfield.title = Error trying to create the operation
na.portal.go.operation.create.error.nextstep.title = Error step forward
na.portal.go.operation.create.error.requiredfield.text = Field {0} is mandatory.
na.portal.go.operation.create.label.sending = Sending
na.portal.go.operation.create.label.waiting.server = Waiting for server confirmation
na.portal.go.operation.create.sending.failed.title = Error uploading the firmware file
na.portal.go.operation.create.sending.failed.message = A problem has occurred while uploading the chosen file. Due to this problem the operations were not executed. Please contact the administrator and try later.

na.portal.go.admin.operation.create.title = Create operation
na.portal.go.admin.operation.create.label = Create operation
na.portal.go.admin.template.edit.title = Edit operation
na.portal.go.admin.template.edit.label = Edit operation
na.portal.go.admin.template.add.title = Add operation
na.portal.go.admin.template.add.label = Add operation

na.portal.go.adminoperation.create.tab.operation.label = Operating Parameters
na.portal.go.adminoperation.create.tab.operation.title = Operating Parameters
na.portal.go.adminoperation.create.tab.operation.description.title = Operation identification
na.portal.go.adminoperation.create.tab.operation.description.text =
na.portal.go.adminoperation.create.tab.operation.fieldDefinition.title = Definition of operations fields
na.portal.go.adminoperation.create.tab.operation.fieldDefinition.text =

########## FIELD TYPES ##########
na.portal.go.template.field.type.textfield.title = text field
na.portal.go.template.field.type.textarea.title = text area
na.portal.go.template.field.type.selectbox.title = selection box
na.portal.go.template.field.type.selectbox.option.selected.label = (definition)
na.portal.go.template.field.type.selectbox.noOptions = no visible options
na.portal.go.template.field.type.checkboxes.title = verification boxes
na.portal.go.template.field.type.checkboxes.option.selected.label = (definition)
na.portal.go.template.field.type.radiobuttons.title = radio buttons
na.portal.go.template.field.type.radiobuttons.option.selected.label = (definition)
na.portal.go.template.field.changedefault = Change definition
na.portal.go.template.field.editable = Editable
na.portal.go.template.field.errors.editfield.title = Field error
na.portal.go.template.field.errors.atleastoneoption.title = Field error
na.portal.go.template.field.errors.atleastoneoption.message = It must be visible at least one option
na.portal.go.template.field.errors.editingfields.title = Error finalizing operation
na.portal.go.template.field.errors.editingfields.message = There are fields to be edited, please confirm or cancel your changes.
na.portal.go.template.field.errors.requirednoteditablefield.message = Required field, needs a default value or be editable.

na.portal.go.field.type.textfield.title = text field
na.portal.go.field.type.textarea.title = text area
na.portal.go.field.type.selectbox.title = selection box
na.portal.go.field.type.checkboxes.title = verification boxes
na.portal.go.field.type.radiobuttons.title = radio buttons

###########BUTTONS###########
na.portal.go.buttons.clone = Clone
na.portal.go.buttons.createoperation = Create Operation
na.portal.go.buttons.executeoperation = Execute Operation
na.portal.go.buttons.suspend = Suspend
na.portal.go.buttons.cancel = Cancel
na.portal.go.buttons.configureOptionalFields = configure optional fields

########### GO ERROR MESSAGES ###########
na.portal.go.createoperation.errors.missingfields.title = Mandatory fields
na.portal.go.createoperation.errors.missingfields.message = Fill in all mandatory fields.
na.portal.go.createoperation.errors.baddata.title = Error in the fields
na.portal.go.createoperation.errors.baddata.message = Correct all notified data.
na.portal.go.template.errors.createoperationmodel.title = Error creating operation model
na.portal.go.template.errors.createoperationmodel.message = The operating model name can not be the same as the basic operation.

# Tooltips
na.portal.go.template.attention.message = Parent template was modified, this template needs to be validated.
na.portal.go.field.all                = select all