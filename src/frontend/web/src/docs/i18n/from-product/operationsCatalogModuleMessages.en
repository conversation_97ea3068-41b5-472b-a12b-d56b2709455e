na.portal.operationscatalog.button.create                                                       = Create
na.portal.operationscatalog.button.createAttribute                                              = Create attribute

na.portal.operationscatalog.button.operations                                                   = operations
na.portal.operationscatalog.button.operations.title                                             = View operations

na.portal.operationscatalog.button.responseCodes                                                = response codes
na.portal.operationscatalog.button.responseCodes.title                                          = View response codes

na.portal.operationscatalog.button.edit.title                                                   = edit
na.portal.operationscatalog.button.delete.title                                                 = delete
na.portal.operationscatalog.button.save.title                                                   = save
na.portal.operationscatalog.button.cancel.title                                                 = cancel
na.portal.operationscatalog.button.multiactions.title                                           = more actions

na.portal.operationscatalog.search.name.placeholder                                             = Search by name
na.portal.operationscatalog.search.code.placeholder                                             = Search by type / code

na.portal.operationscatalog.valid.option.label                                                  = Valid
na.portal.operationscatalog.invalid.option.label                                                = Invalid

# Version labels/placeholders

na.portal.operationscatalog.entityLateralVersion.name.label                                     = Name
na.portal.operationscatalog.entityLateralVersion.name.placeholder                               = Version name
na.portal.operationscatalog.entityLateralVersion.state.label                                    = State
na.portal.operationscatalog.entityLateralVersion.state.placeholder                              = Version state
na.portal.operationscatalog.entityLateralVersion.catEntityVersion.label                         = Entities version
na.portal.operationscatalog.entityLateralVersion.catEntityVersion.placeholder                   = Version entities version
na.portal.operationscatalog.entityLateralVersion.valid.label                                    = Status
na.portal.operationscatalog.entityLateralVersion.valid.placeholder                              = Version status

na.portal.operationscatalog.entityVersion.name.label                                            = Version
na.portal.operationscatalog.entityVersion.name.placeholder                                      = Version name
na.portal.operationscatalog.entityVersion.description.placeholder                               = Version description
na.portal.operationscatalog.entityVersion.catEntityVersion.label                                = Version of the entities catalog
na.portal.operationscatalog.entityVersion.catEntityVersion.placeholder                          = Version of the entities catalog
na.portal.operationscatalog.entityVersion.catEntityVersion.withoutVersion.placeholder           = Without version

na.portal.operationscatalog.entityVersion.operationVersion.label                                = Version of the operations catalog
na.portal.operationscatalog.entityVersion.operationVersion.placeholder                          = Version of the operations catalog

na.portal.operationscatalog.version.create.label                                                = Create version
na.portal.operationscatalog.version.fromEntitiesVersion.label                                   = from entities version
na.portal.operationscatalog.version.fromOperationsVersion.label                                 = from operations version

na.portal.operationscatalog.version.create.noEntitiesVersions                                   = There are no created or in a valid state entities versions
na.portal.operationscatalog.version.create.noOperationsVersions                                 = There are no created or valid operations versions

# CFS labels/placeholders

na.portal.operationscatalog.entityLateralCfs.name.label                                         = Name
na.portal.operationscatalog.entityLateralCfs.name.placeholder                                   = Operation name

na.portal.operationscatalog.entityLateralCfs.description.label                                  = Description
na.portal.operationscatalog.entityLateralCfs.description.placeholder                            = Operation description

na.portal.operationscatalog.entityLateralCfs.entityName.label                                   = Entity
na.portal.operationscatalog.entityLateralCfs.entityName.placeholder                             = Operation entity

na.portal.operationscatalog.entityLateralCfs.state.label                                        = State
na.portal.operationscatalog.entityLateralCfs.state.placeholder                                  = Operation state

na.portal.operationscatalog.entityLateralCfs.tags.label                                         = Tags
na.portal.operationscatalog.entityLateralCfs.tags.placeholder                                   = Operation tags

na.portal.operationscatalog.entityLateralCfs.valid.label                                        = Status
na.portal.operationscatalog.entityLateralCfs.valid.placeholder                                  = Operation status

na.portal.operationscatalog.entityCfs.name.label                                                = Name
na.portal.operationscatalog.entityCfs.name.placeholder                                          = Operation name

na.portal.operationscatalog.entityCfs.description.label                                         = Description
na.portal.operationscatalog.entityCfs.description.placeholder                                   = Operation description

na.portal.operationscatalog.entityCfs.entityName.label                                          = Entity
na.portal.operationscatalog.entityCfs.entityName.placeholder                                    = Operation entity

na.portal.operationscatalog.entityCfs.OPERATION_VERSION_version.label                           = Version
na.portal.operationscatalog.entityCfs.OPERATION_VERSION_version.placeholder                     = Operation entity version

na.portal.operationscatalog.entityCfs.OPERATION_TAGS_tagsAsString.label                         = Tags
na.portal.operationscatalog.entityCfs.OPERATION_TAGS_tagsAsString.placeholder                   = Operation tags

na.portal.operationscatalog.entityCfs.workflow.label                                            = Workflow
na.portal.operationscatalog.entityCfs.workflow.placeholder                                      = Operation workflow


# RFS labels/placeholders

na.portal.operationscatalog.entityLateralRfs.name.label                                         = Name
na.portal.operationscatalog.entityLateralRfs.name.placeholder                                   = Operation name

na.portal.operationscatalog.entityLateralRfs.description.label                                  = Description
na.portal.operationscatalog.entityLateralRfs.description.placeholder                            = Operation description

na.portal.operationscatalog.entityLateralRfs.entityName.label                                   = Entity
na.portal.operationscatalog.entityLateralRfs.entityName.placeholder                             = Operation entity

na.portal.operationscatalog.entityLateralRfs.state.label                                        = State
na.portal.operationscatalog.entityLateralRfs.state.placeholder                                  = Operation state

na.portal.operationscatalog.entityLateralRfs.tags.label                                         = Tags
na.portal.operationscatalog.entityLateralRfs.tags.placeholder                                   = Operation tags

na.portal.operationscatalog.entityLateralRfs.valid.label                                        = Status
na.portal.operationscatalog.entityLateralRfs.valid.placeholder                                  = Operation status

na.portal.operationscatalog.entityRfs.name.label                                                = Name
na.portal.operationscatalog.entityRfs.name.placeholder                                          = Operation name

na.portal.operationscatalog.entityRfs.description.label                                         = Description
na.portal.operationscatalog.entityRfs.description.placeholder                                   = Operation description

na.portal.operationscatalog.entityRfs.entityName.label                                          = Entity
na.portal.operationscatalog.entityRfs.entityName.placeholder                                    = Operation entity

na.portal.operationscatalog.entityRfs.OPERATION_VERSION_version.label                           = Version
na.portal.operationscatalog.entityRfs.OPERATION_VERSION_version.placeholder                     = Operation entity version

na.portal.operationscatalog.entityRfs.OPERATION_TAGS_tagsAsString.label                         = Tags
na.portal.operationscatalog.entityRfs.OPERATION_TAGS_tagsAsString.placeholder                   = Operation tags

na.portal.operationscatalog.entityRfs.workflow.label                                            = Workflow
na.portal.operationscatalog.entityRfs.workflow.placeholder                                      = Operation workflow


# Resource labels/placeholders

na.portal.operationscatalog.entityLateralResource.name.label                                    = Name
na.portal.operationscatalog.entityLateralResource.name.placeholder                              = Operation name

na.portal.operationscatalog.entityLateralResource.description.label                             = Description
na.portal.operationscatalog.entityLateralResource.description.placeholder                       = Operation description

na.portal.operationscatalog.entityLateralResource.entityName.label                              = Entity
na.portal.operationscatalog.entityLateralResource.entityName.placeholder                        = Operation entity

na.portal.operationscatalog.entityLateralResource.state.label                                   = State
na.portal.operationscatalog.entityLateralResource.state.placeholder                             = Operation state

na.portal.operationscatalog.entityLateralResource.tags.label                                    = Tags
na.portal.operationscatalog.entityLateralResource.tags.placeholder                              = Operation tags

na.portal.operationscatalog.entityLateralResource.valid.label                                   = Status
na.portal.operationscatalog.entityLateralResource.valid.placeholder                             = Operation status

na.portal.operationscatalog.entityResource.name.label                                           = Name
na.portal.operationscatalog.entityResource.name.placeholder                                     = Operation name

na.portal.operationscatalog.entityResource.description.label                                    = Description
na.portal.operationscatalog.entityResource.description.placeholder                              = Operation description

na.portal.operationscatalog.entityResource.entityName.label                                     = Entity
na.portal.operationscatalog.entityResource.entityName.placeholder                               = Operation entity

na.portal.operationscatalog.entityResource.OPERATION_VERSION_version.label                      = Version
na.portal.operationscatalog.entityResource.OPERATION_VERSION_version.placeholder                = Operation entity version

na.portal.operationscatalog.entityResource.OPERATION_TAGS_tagsAsString.label                    = Tags
na.portal.operationscatalog.entityResource.OPERATION_TAGS_tagsAsString.placeholder              = Operation tags

na.portal.operationscatalog.entityResource.workflow.label                                       = Workflow
na.portal.operationscatalog.entityResource.workflow.placeholder                                 = Operation workflow


# Response Code labels/placeholders

na.portal.operationscatalog.entityLateralResponseCode.type.label                                = Type
na.portal.operationscatalog.entityLateralResponseCode.type.placeholder                          = Response code type

na.portal.operationscatalog.entityLateralResponseCode.code.label                                = Code
na.portal.operationscatalog.entityLateralResponseCode.code.placeholder                          = Response code code

na.portal.operationscatalog.entityLateralResponseCode.tags.label                                = Tags
na.portal.operationscatalog.entityLateralResponseCode.tags.placeholder                          = Response code tags


# Datatable labels

na.portal.operationscatalog.datatables.column.label.actions                                     = Actions
na.portal.operationscatalog.datatables.column.label.catEntityName                               = Entity name
na.portal.operationscatalog.datatables.column.label.name                                        = Name
na.portal.operationscatalog.datatables.column.label.description                                 = Description
na.portal.operationscatalog.datatables.column.label.state                                       = State
na.portal.operationscatalog.datatables.column.label.version                                     = Version
na.portal.operationscatalog.datatables.column.label.operation                                   = Operation
na.portal.operationscatalog.datatables.column.label.typeCode                                    = Type / Code
na.portal.operationscatalog.datatables.column.label.tags                                        = Tags
na.portal.operationscatalog.datatables.column.label.typeId                                      = Type
na.portal.operationscatalog.datatables.column.label.value                                       = Value
na.portal.operationscatalog.datatables.column.label.position                                    = Position
na.portal.operationscatalog.datatables.column.label.attribute                                   = Attribute
na.portal.operationscatalog.datatables.column.label.attributeName                               = Attribute name
na.portal.operationscatalog.datatables.column.label.attributeType                               = Attribute type
na.portal.operationscatalog.datatables.column.label.restriction                                 = Restriction
na.portal.operationscatalog.datatables.column.label.entityName                                  = Entity
na.portal.operationscatalog.datatables.column.label.entityVersion                               = Entity version
na.portal.operationscatalog.datatables.column.label.attributes_entities                         = Attributes / Entities
na.portal.operationscatalog.datatables.column.label.enrichmentRule                              = Enrichment Rule
na.portal.operationscatalog.datatables.column.label.cardinality                                 = Cardinality
na.portal.operationscatalog.datatables.column.label.validationRule                              = Validation Rule
na.portal.operationscatalog.datatables.column.label.entities                                    = Entities
na.portal.operationscatalog.datatables.column.label.attributes                                  = Attributes

na.portal.operationscatalog.datatables.column.label.outputEntities                              = Output entities
na.portal.operationscatalog.datatables.column.label.outputAttributes                            = Output attributes

na.portal.operationscatalog.datatable.actions.options                                           = options

# State Labels

na.portal.operationscatalog.entities.state.DEVELOPMENT                                          = Development
na.portal.operationscatalog.entities.state.TESTING                                              = Testing
na.portal.operationscatalog.entities.state.ABANDONED                                            = Abandoned
na.portal.operationscatalog.entities.state.PRODUCTION                                           = Production
na.portal.operationscatalog.entities.state.BLOCKED                                              = Blocked
na.portal.operationscatalog.entities.state.DEPRECATED                                           = Deprecated

na.portal.operationscatalog.datatable.actions.state.changeStateTo                               = change state to
na.portal.operationscatalog.datatable.actions.state.noFurtherStates                             = no further states

# Multi-select

na.portal.operationscatalog.selectbox.placeholder                                               = Select
na.portal.operationscatalog.input.nomatches                                                     = No results found
na.portal.operationscatalog.version.noVersions                                                  = No versions found
na.portal.operationscatalog.tag.newTag                                                          = (new tag)

na.portal.operationscatalog.tab.characteristics                                                 = Characteristics
na.portal.operationscatalog.tab.section.general.characteristics                                 = General characteristics
na.portal.operationscatalog.tab.section.input.attributes                                        = Input attributes
na.portal.operationscatalog.tab.section.execution.attributes                                    = Execution attributes
na.portal.operationscatalog.tab.section.output.attributes                                       = Output attributes

# Input attributes modal

na.portal.operationscatalog.modal.title.add                                                     = Create attribute
na.portal.operationscatalog.modal.title.edit                                                    = Edit attribute
na.portal.operationscatalog.modal.label.name                                                    = Name
na.portal.operationscatalog.modal.label.type                                                    = Type
na.portal.operationscatalog.modal.label.regex                                                   = Regular expression
na.portal.operationscatalog.modal.label.logicCondition                                          = Logic condition
na.portal.operationscatalog.modal.label.logicalcondition.minSize                                = Minimum number of characters
na.portal.operationscatalog.modal.label.logicalcondition.maxSize                                = Maximum number of characters
na.portal.operationscatalog.modal.label.logicalcondition.maxRange                               = Upper bound
na.portal.operationscatalog.modal.label.logicalcondition.minRange                               = Lower bound
na.portal.operationscatalog.modal.label.entityRule                                              = Entity rule
na.portal.operationscatalog.modal.label.cardinality                                             = Cardinality
na.portal.operationscatalog.modal.label.cardinality.min                                         = Min:
na.portal.operationscatalog.modal.label.cardinality.max                                         = Max:
na.portal.operationscatalog.modal.label.restrictions                                            = Restrictions
na.portal.operationscatalog.modal.label.restrictions.doNotApply                                 = Don''t apply
na.portal.operationscatalog.modal.helper.regex                                                  = Regular expression helper information
na.portal.operationscatalog.modal.validation.numeric                                            = Values must be integers.
na.portal.operationscatalog.modal.validation.minLowerThanMax                                    = The upper limit must be greater or equal than the lower limit.
na.portal.operationscatalog.modal.validation.required                                           = The fields are required.
na.portal.operationscatalog.modal.validation.notNegative                                        = Values can not be negative.
na.portal.operationscatalog.modal.validation.atLeastOneFieldRequired                            = Please fill in at least one of the fields marked in red.
na.portal.operationscatalog.modal.error.notIntegerNumber                                        = Values must be integers.
na.portal.operationscatalog.modal.error.regularExpression.required                              = Set the regular expression.
na.portal.operationscatalog.modal.error.regularExpression.invalidSyntax                         = Invalid regular expression.
na.portal.operationscatalog.modal.error.enumeration.notEmpty                                    = Must have at least one selected value.
na.portal.operationscatalog.modal.error.enumeration.sameAsEntity                                = You have applied the same values as of the entity, change them or select "Don''t apply" to continue.
na.portal.operationscatalog.modal.error.outOfRangeOfEntityRule                                  = Values outside the entity rule range ({0}, {1})

na.portal.operationscatalog.form.notification.failure.title                                     = The form could not be saved
na.portal.operationscatalog.form.notification.failure.message                                   = Please check all the highlighted field

# Attribute constraint types

na.portal.operationscatalog.attribute.constraint.type.SIZE                                      = Size
na.portal.operationscatalog.attribute.constraint.type.REGEXP                                    = Regular expression
na.portal.operationscatalog.attribute.constraint.type.RANGE                                     = Range
na.portal.operationscatalog.attribute.constraint.type.ENUMC                                     = Enumerate
na.portal.operationscatalog.attribute.constraint.type.FLOAT_RANGE                               = Float

# Confirmation modals

na.portal.operationscatalog.modals.version.confirm.state.change.title                           = Transition version state
na.portal.operationscatalog.modals.version.confirm.state.change.content                         = Are you sure you want to transition the version "{0}" to state "{1}"?

na.portal.operationscatalog.modals.operation.confirm.state.change.title                         = Transition operation state
na.portal.operationscatalog.modals.operation.confirm.state.change.content                       = Are you sure you want to transition the operation "{0}" to state "{1}"?

na.portal.operationscatalog.modals.version.confirm.delete.title                                 = Delete version
na.portal.operationscatalog.modals.version.confirm.delete.content                               = Are you sure you want to delete the version "{0}"?

na.portal.operationscatalog.modals.operation.confirm.delete.title                               = Delete operation
na.portal.operationscatalog.modals.operation.confirm.delete.content                             = Are you sure you want to delete the operation "{0}"?

na.portal.operationscatalog.modals.version.confirm.clone.title                                  = Clone version
na.portal.operationscatalog.modals.version.confirm.clone.content                                = Are you sure you want to clone the version "{0}"?

na.portal.operationscatalog.modals.operation.confirm.clone.title                                = Clone operation
na.portal.operationscatalog.modals.operation.confirm.clone.content                              = Are you sure you want to clone the operation "{0}"?

na.portal.operationscatalog.modals.operation.confirm.entity.name.change.title                   = Change entity name
na.portal.operationscatalog.modals.operation.confirm.entity.name.change.content                 = Are you sure you want to change the entity name? All attributes will be removed

# Notifications

na.portal.operationscatalog.notification.state.update.success.title                             = Updated successfully
na.portal.operationscatalog.notification.state.update.success.content                           = Version state updated successfully!

na.portal.operationscatalog.notification.operation.state.update.success.title                   = Updated successfully
na.portal.operationscatalog.notification.operation.state.update.success.content                 = Operation state updated successfully!

na.portal.operationscatalog.notification.version.delete.success.title                           = Deleted successfully
na.portal.operationscatalog.notification.version.delete.success.content                         = Version deleted successfully!

na.portal.operationscatalog.notification.operation.delete.success.title                         = Deleted successfully
na.portal.operationscatalog.notification.operation.delete.success.content                       = Operation deleted successfully!

na.portal.operationscatalog.notification.failure.title                                          = The form could not be saved
na.portal.operationscatalog.notification.failure.message                                        = Please check all the highlighted fields

na.portal.operationscatalog.notification.this.version.is.invalid                                = This version is invalid
na.portal.operationscatalog.notification.this.operation.is.invalid                              = This operation is invalid
na.portal.operationscatalog.notification.this.attribute.is.invalid                              = This attribute is invalid

# Validations

na.portal.operationscatalog.validations.mandatory                                               = mandatory field