#### ABOUT ####

nossis.portal.about=A propos de
nossis.portal.about.title=A propos de
nossis.portal.about.orientations=Le produit suit les directives de la politique de confidentialité de Altice Labs.

nossis.portal.aditional.info = Informations Complémentaires
nossis.portal.terms.and.conditions = Termes et Conditions
nossis.portal.main.licenced.to=Ce produit est concédé sous licence à Altice. Tous droits réservés.
nossis.portal.main.licence.conditions=Pour consulter les termes et conditions de licence, rendez-vous sur
nossis.portal.modules.licence.conditions=Conditions, termes et licences -
nossis.portal.versions.and.licences=Versions et licences
nossis.portal.supported.browsers=Navigateurs
nossis.portal.minimum.hardware.requirements=Configuration minimale

na.button.label.save = Garder
na.button.label.cancel = Annuler
na.button.label.export = Exportation
na.button.label.edit = Editer
na.button.label.passwordShow = Montrer les caractères
na.button.label.passwordHide = Cacher les caractères
na.buttons.search = Recherche
na.buttons.clear = Effacer
na.buttons.viewall = Voir tous
na.buttons.caret.moreoptions = Plus d''options
na.button.apply = Appliquer

na.module.mpt = MPT
na.module.go = GO

na.general.minimize = Réduire
na.general.maximize = Agrandir
na.general.filters = Filtres
na.general.search.results = Résultat de la recherche
na.general.results = Résultats

na.selectbox.placeholder = Choisisser
na.selectbox.filters.noresults = Sans filtres
na.selectbox.filters.results = {0} filtres

na.loading = En chargement
loading.label = Veuillez patienter un instant, s''il vous plaît...

na.basemodule.lateralSearch.title = Recherche

#DATATABLES
datatables.navigation.label.first =
datatables.navigation.label.last =
datatables.navigation.label.next = prochain
datatables.navigation.label.previous = précédent
datatables.pagination.info = Page _INPUT_ de _TOTAL_
datatables.info.label.emptytable = Aucun enregistrement à afficher
# next key is for nossis inv, when angular is no longer used, remove this
datatables.info.label.entriestoshow = Total des enregistrements <b> _TOTAL_ </b>
datatables.info.label.totalresults = Total des enregistrements _TOTAL_
datatables.info.label.infofiltered = - filtrer de _MAX_ résultats.
datatables.info.label.infopostfix =
datatables.info.label.infothousands = .
datatables.info.label.lengthmenu = enregistrement par page
datatables.info.label.loadingrecords = En chargement...
datatables.info.label.processing = En traitement
datatables.info.label.search = Filtrer les résultats
datatables.info.label.zerorecords = Il n''y a pas d''enregistrements
datatables.info.export.label.currentpage = Page atuel
datatables.info.export.label.allpages = Toutes les pages
datatables.info.export.label.info = Information pour effectuer l''exportation
datatables.info.export.label.types = Format d''exportation
datatables.info.export.bymail = Le nombre des résultats gérés est supérieur au total permit pour l''information on-line, donc tous les résultats seront envoyé dans un fichier a votre addresse email.
datatables.action.label.seemore = Voir détails
datatables.action.label.edit = Éditer
datatables.action.label.remove = Retirer
datatables.action.label.research = Nouvelle recherche
datatables.action.label.clone = Clone
datatables.action.label.logdata = Voir Log/Date
datatables.action.label.add = Ajouter
datatables.action.label.confirm = Confirmer
datatables.action.label.cancel = Annuler
datatables.action.label.columnfilter = Gérer les colonnes
datatables.action.label.export = Exportation
datatables.action.label.search = Recherche...
datatables.action.label.advancedsearch = Recherche avancée
datatables.action.label.exportPDF = PDF
datatables.action.label.exportXLS = XLS
datatables.action.label.exportCSV = CSV
datatables.columnFilter.search.placeholder = Recherche

na.datatables.action.label.view                         = consulter
na.datatables.action.label.add                          = ajouter
na.datatables.action.label.clone                        = clone
na.datatables.action.label.columnfilter                 = gérer colonnes
na.datatables.action.label.edit                         = éditer
na.datatables.action.label.cancel                       = annuler
na.datatables.action.label.complete                     = compléter
na.datatables.action.label.retry                        = retenter
na.datatables.action.label.skip                         = sauter
na.datatables.action.label.pause                        = pause
na.datatables.action.label.pin                          = coincer
na.datatables.action.label.remove                       = effacer
na.datatables.action.label.moreoptions                  = options
na.datatables.columnFilter.placeholder                  = recherche
na.datatables.columnFilter.search.placeholder           = recherche...

na.details.title.clone = clone
na.details.title.edit = éditer
na.details.title.delete = effacer

#TODO: Tornar isto configuravel
datatables.search.advancedsearch.minimum.length = Vous devez utiliser, au minimum, un caractère.

#Notifications
na.notifications.input.freetext = Texte libre
na.notifications.baddate = Le format de la date est incorrect.

#Errors
na.searchform.error.badsyntax = Erreur de syntaxe
na.searchform.error.badsyntax.initialDate = Le format du champ Date/Heure début n''est pas correct.
na.searchform.error.badsyntax.endDate = Le format du champ Date/Heure fin n''est pas correct.
na.searchform.error.baddates = Les dates saisies sont incorrectes
na.searchform.error.baddates.negativeinterval = La date du début ne peut pas être supérieur à la date de fin.
na.searchform.error.baddates.endDate.biggertoday = La date de fin ne peut pas être supérieur à la date actuelle.
na.searchform.error.baddates.startDate.biggertoday = La date du début ne peut pas être supérieur à la date actuelle.
na.searchform.error.baddates.initialDate = La date saisie sur le champ Date/Heure début n''est pas correcte.
na.searchform.error.baddates.endDate = La date saisie sur le champ Date/Heure fin n''est pas correcte.
na.searchform.error.missingdate.initialDate = Le champ Date/Heure début est obligatoire.
na.searchform.error.missingdate.endDate = Le champ Date/Heure Fin est obligatoire.
na.searchform.error.baddates.temporalwindow = L''intervalle entre les dates ne peut pas être supérieur à {0} jours.

na.input.nomatches=Sans résultats

#MODALS
na.modal.button.savefilter = Guarder Filtre
na.modal.button.ok = OK
na.modal.button.confirm = Confirmer
na.modal.button.cancel = Annuler
na.modal.button.close = Fermer
na.modal.button.deletefilter = Supprimer filtre
na.modal.button.submit = Soumettre
na.modal.button.yes = Oui
na.modal.button.no = Non
na.modal.changepage.dataloss.title = Changer de page
na.modal.changepage.dataloss.body = Les données seront perdues. Voulez-vous continuer?
na.modal.filters.title.savefilter = Guarder Filtre
na.modal.filters.info.savefilter = Confirmer les données pour la création du nouveau filtre personnel
na.modal.filters.title.editfilter = Éditer Filtre
na.modal.filters.title.deletefilter = Effacer Filtre
na.modal.filters.info.deletefilter = Voulez-vous effacer le filtre sélectionné?
na.modals.fields.name.label = Nom du Filtre
na.modals.fields.name.placeholder = Nom du Filtre
na.modals.filters.error.save = Erreur pendent l''enregistrement du filtre
na.modals.filters.error.edit = Erreur pendant l''édition du filtre
na.modals.filters.error.delete = Erreur pendant l''élimination du filtre
na.modals.filters.error.missingstartdate = Le champ Date/Heure début est obligatoire.
na.modals.filters.error.missingenddate = Le champ Date/Heure fin est obligatoire.
na.modals.filters.error.missingfiltername = Le champ Nom du Filtre est obligatoire.
na.modals.filters.error.missingselectedfilter = Il faut choisir un filtre pour éditer.
na.modals.filters.error.missing.selectfilter.delete = Il faut choisir un filtre pour effacer.
na.modals.filters.success.save = Filtre sauvegarder avec succès
na.modals.filters.success.save.message = Votre filtre {0} a été sauvegarder avec succès.
na.modals.filters.success.edit = Filtre éditer avec succès
na.modals.filters.success.edit.message = Votre filtre {0} a été édité avec succès.
na.modals.filters.success.delete = Filtre effacer avec succès
na.modals.filters.success.delete.message = Votre filtre {0} a été éffacé avec succès.
na.modals.filters.options = Option du filtre
na.modals.filters.select.label = Choisir filtre:

################### Module Names
na.portal.module.mpt = MPT
na.portal.module.go = GO
na.portal.module.mpt.admin = Administration MPT

##################DOUBLE BOX
doublebox.addAll = Déplacer tous >>
doublebox.addSelected = Déplacer selection >
doublebox.removeAll = Déplacer tous <<
doublebox.removeSelected = Déplacer selection <

##################DATE FORMATS
na.date.time.format.utc = yyyy-MM-dd'T'HH:mm:ss.SSSX
na.date.time.format.short = dd-MM-yyyy HH:mm
na.date.time.format.seconds = dd-MM-yyyy HH:mm:ss
na.date.time.format.milliseconds = dd-MM-yyyy HH:mm:ss.SSS
na.date.format.short = dd-MM-yyyy
na.time.format.short = HH:mm

na.wizard.buttons.before = Précédant
na.wizard.buttons.next = Suivant
na.wizard.buttons.finalize = Terminer
na.wizard.buttons.cancel = Annuler
na.wizard.step = Pas {0}

na.type = Type
na.values = Valeurs

################# RESUMES
na.information.title = Information
na.operationalview.accessmodule = Accéder au module

################## VALIDATION ERRORS
na.field.x.required = Champ {0} est obligatoire.
na.fill.all.required.fields = Remplir tous les champs obligatoires.
na.basemodule.validations.mandatory          = Champs requis
na.basemodule.validations.invalid.pattern    = Format invalide
na.basemodule.validations.numeric            = Champ numérique
na.basemodule.validations.integer            = Champ entier
na.basemodule.validations.max.characters     = Limite maximale de {0} caractères
na.basemodule.validations.min.characters     = Limite minimale de {0} caractères

################## MultiSelect

input.nomatches = Sans résultats

################## UPLOAD FILE FIELD
na.upload.field.no.file.selected = Aucun fichier sélectionné
na.upload.field.browse = Parcourir