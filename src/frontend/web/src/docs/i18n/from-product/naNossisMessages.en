########################## NOSSIS ##########################

#NA

nossis.upload = Sending

# ERRORS PAGES

nossis.401.title = 401 - Access denied!
nossis.401.subtitle = Access denied!
nossis.401.description = Does not have privileges to access this page or page requires you to be logged.
nossis.401.resolve.label =  To solve the problem, try one of the following:
nossis.401.tryagain.button = Try again
nossis.401.tryagain.button.description = Try again to make the page request
nossis.401.report.button = Report error
nossis.401.report.button.description = Inform system administrators about this problem
nossis.401.backtologin = Back to login page

nossis.404.title = 404 - Page not found!
nossis.404.subtitle = Page not found!
nossis.404.description = Page not found, probably you have made a mistake entering the address, or the page you are looking for no longer exists
nossis.404.resolve.label =  To solve the problem, try one of the following:
nossis.404.tryagain.button = Try again
nossis.404.tryagain.button.description = Try again to make the page request
nossis.404.report.button = Report error
nossis.404.report.button.description = Inform system administrators about this problem
nossis.404.backtologin = Back to login page

nossis.500.title = 500 - An unexpected error has occurred!
nossis.500.subtitle = An unexpected error has occurred!
nossis.500.description = Error in the page request
nossis.500.resolve.label =  To solve the problem, try one of the following:
nossis.500.tryagain.button = Try again
nossis.500.tryagain.button.description = Try again to make the page request
nossis.500.report.button = Report error
nossis.500.report.button.description = Inform system administrators about this problem
nossis.500.backtologin = Back to login page




security.authentication.error.locked = Blocked Account. Please contact the administrators.

#Login
na.login.button.ok.text=login
na.login.box.user.label=Login
na.login.box.user.placeholder=Insert Login
na.login.box.password.label=Password
na.login.box.password.placeholder=Insert Password
na.login.title.key.slogan.label=
na.login.label.title.box=Login with your customer account
na.login.validate.credentials = Validating credentials ... Please wait.
na.login.captcha.placeholder = Insert captcha
na.login.captcha.newwords = New captcha
na.login.box.captcha.title = Type the following text


naportal.notification.fuximissing.title =  Fuxi Missing
naportal.notification.fuximissing.message = Failed to load essential files to the graphical representation
