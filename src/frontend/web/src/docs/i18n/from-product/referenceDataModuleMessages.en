# datatables
datatables.column.label.actions                                 = Actions
datatables.column.label.catalog.catEntityName                   = Entity Name
datatables.column.label.catalog.name                            = Name

na.reference.data.results.label.title                           = Data Management

na.reference.data.clone.title                                   = Cloning a catalog instance
na.reference.data.clone.message                                 = You are creating a new catalog instance based on the info of an already existing one

na.reference.data.form.notification.failure.message             = Please check all the highlighted fields
na.reference.data.form.notification.failure.title               = The form could not be saved

na.reference.data.modal.entity.changesubtype.title              = Alter
na.reference.data.modal.entity.changesubtype.content            = Discard instance?
na.reference.data.modal.entity.delete.title                     = Remove
na.reference.data.modal.entity.deleteMultiple                   = {0} instance(s) will be removed
na.reference.data.modal.navigate.button.confirm                 = Confirm
na.reference.data.modal.navigate.button.cancel                  = Cancel
na.reference.data.modal.header.delete                           = Delete
na.reference.data.modal.body.delete                             = Delete catalog instance?

na.reference.data.details.title.edit                            = edit
na.reference.data.details.title.clone                           = clone
na.reference.data.details.title.delete                          = delete

na.reference.data.notification.preconditionfailed.idcatentity   = entity id

na.reference.data.persistence.error.delete.title                = Could not be deleted.

na.reference.data.tab.characteristics                           = Characteristics
na.reference.data.tab.attributes                                = Attributes
na.reference.data.tab.specific.attributes                       = Specific Attributes

# ----------------------
# --- web components ---
# ----------------------

na.portal.reference.data.idCatEntity.label                      = Type
na.portal.reference.data.idCatEntity.placeholder                = Select

na.portal.reference.data.state.label                            = State
na.portal.reference.data.state.placeholder                      = Select

na.portal.reference.data.entity.clone.idCatEntity.label         = Type
na.portal.reference.data.entity.clone.idCatEntity.placeholder   = Select

na.portal.reference.data.entity.create.idCatEntity.label        = Type
na.portal.reference.data.entity.create.idCatEntity.placeholder  = Select

na.portal.reference.data.entity.create.state.label              = State
na.portal.reference.data.entity.create.state.placeholder        = Select

na.portal.reference.data.entity.edit.state.label                = State
na.portal.reference.data.entity.edit.state.placeholder          = Select

na.portal.reference.data.entity.externalCode.label              = External code
na.portal.reference.data.entity.externalCode.placeholder        = Catalog external code

na.portal.reference.data.entity.idCatEntity.label               = Type
na.portal.reference.data.entity.idCatEntity.placeholder         = Select

na.portal.reference.data.entity.name.label                      = Name
na.portal.reference.data.entity.name.placeholder                = Catalog name
na.portal.reference.data.entity.name.error.msg.pattern          = Alphanumeric and can't start with number

na.portal.reference.data.entity.search.idCatEntity.label        = Type
na.portal.reference.data.entity.search.idCatEntity.placeholder  = Select

na.portal.reference.data.entity.search.state.label              = State
na.portal.reference.data.entity.search.state.placeholder        = Select

na.portal.reference.data.entity.state.label                     = State
na.portal.reference.data.entity.state.placeholder               = Select

na.portal.reference.data.entityLateral.idCatEntity.label        = Type
na.portal.reference.data.entityLateral.idCatEntity.placeholder  = Select

na.portal.reference.data.entityLateral.state.label              = State
na.portal.reference.data.entityLateral.state.placeholder        = Select

# ---------------------------------
# --- reference data navigation ---
# ---------------------------------

na.reference.data.dashboard.button.create                       = Create
na.reference.data.breadcrumb.action.create                      = Create
na.reference.data.breadcrumb.home                               = Data Management
na.reference.data.nav.search                                    = Search

# ------------------------------------
# --- reference data portal resume ---
# ------------------------------------

na.reference.data.module.title                                  = Data Management
na.reference.data.module.description                            = Using the Data Management module an operator can manage the system's base information that supports activation processes, like, equipment access information, data mappings between OSS and the network, equipment profiles, etc.
