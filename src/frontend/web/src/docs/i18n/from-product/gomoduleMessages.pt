na.portal.go.module.title = GO
na.portal.go.module.description = O módulo de Operações Genéricas permite ao operador invocar qualquer operação disponível na NBI.<br>O administrador do sistema consegue gerir as operações disponibilizadas a cada grupo de utilizadores, bem como derivar operações de modo a facilitar o uso em casos específicos.
na.portal.go.mega.menu.title = Operações
na.portal.go.mega.menu.navinitial = Navegar para página inicial

na.portal.go.admin.label.title = Administração GO
na.portal.go.admin.label.subtitle = Gestão de Operações Genéricas

na.portal.go.home.label.title = GO
na.portal.go.home.label.subtitle = Operações Genéricas
na.portal.go.home.mainnav.go = GO
na.portal.go.home.mainnav.admin = Administração GO
na.portal.go.home.menu.admin = Administração
na.portal.go.mainnav.admin.title = Administração

###########FIELDS###########
na.portal.go.field.beginDate.label = Data/Hora Início
na.portal.go.field.endDate.label = Data/Hora Fim
na.portal.go.family.field.label = Família
na.portal.go.family.selects.empty.option = Todas as famílias
na.portal.go.field.orderKey.label = OPK
na.portal.go.field.orderKey.placeholder = OPK
na.portal.go.rfs.field.label = RFS
na.portal.go.rfs.selects.empty.option = Todos os RFS
na.portal.go.state.field.label = Estado
na.portal.go.state.selects.empty.option = Todos os estados
na.portal.go.operation.field.label = Operação
na.portal.go.operation.selects.empty.option = Todas as operações
na.portal.go.field.operation.label = Operação
na.portal.go.field.operation.placeholder = Nome da operação
na.portal.go.field.operationName.label = Nome da operação
na.portal.go.field.user.label = Utilizador
na.portal.go.field.user.placeholder = Utilizador
na.portal.go.field.field.label = Família
na.portal.go.field.operationName.placeholder =
na.portal.go.field.modificationBeginDate.label = Modificado de
na.portal.go.field.modificationEndDate.label = a

#Datatables
na.portal.go.datatables.column.label.lastModification = Última modificação
na.portal.go.datatables.column.label.stateAction = Estado/Ação
na.portal.go.datatables.column.label.insertionDate = Data/Hora
na.portal.go.datatables.column.label.orderKey = OPK
na.portal.go.datatables.column.label.username = Utilizador
na.portal.go.datatables.column.label.family = Família
na.portal.go.datatables.column.label.operation = Operação
na.portal.go.datatables.column.label.rfs = RFS
na.portal.go.datatables.column.label.state = Estado
na.portal.go.datatables.column.label.name = Nome
na.portal.go.datatables.column.label.type = Tipo
na.portal.go.datatables.column.label.value = Valor
na.portal.go.datatables.column.label.messageType = Parâmetro
na.portal.go.datatables.column.label.status = Estado
na.portal.go.datatables.column.label.errorMessage = Mensagem
na.portal.go.datatables.column.label.actions = Ações
na.portal.go.datatables.column.label.request = Pedido
na.portal.go.datatables.column.label.response = Resposta
na.portal.go.datatables.column.label.description = Descrição
na.portal.go.datatables.column.label.lrName = LR Name
na.portal.go.datatables.action.label.edit = Editar
na.portal.go.datatables.action.label.add = Adicionar
na.portal.go.datatables.action.label.remove = Remover


#Execution state

na.portal.go.operation.state.PENDING.label = Pendente
na.portal.go.operation.state.EXECUTING.label = Em execução
na.portal.go.operation.state.TERMINATED.label = Terminado
na.portal.go.operation.state.STARTED.label = Iniciado
na.portal.go.operation.state.NOT_STARTED.label = Não Iniciado
na.portal.go.operation.state.ERROR.label = Erro

#Admin Execution state
na.portal.go.adminoperation.state.ACTIVE.label = ON
na.portal.go.adminoperation.state.INACTIVE.label = OFF

na.portal.go.operation.details.characteristics = Características
na.portal.go.operation.details.label.operation = Operação
na.portal.go.operation.details.label.family = Família
na.portal.go.operation.details.label.rfs = RFS
na.portal.go.operation.details.label.responseurl = URL de Resposta
na.portal.go.operation.details.label.username = Utilizador
na.portal.go.operation.details.label.description = Descrição
na.portal.go.operation.details.label.state = Estado
na.portal.go.operation.details.label.payload = Payload
na.portal.go.operation.details.label.request = Pedido
na.portal.go.operation.details.label.response = Resposta
na.portal.go.operation.details.label.nooperation = Sem operação
na.portal.go.operation.details.payload.empty = Sem resposta
na.portal.go.operation.details.breadcrumb.order = Ordem
na.portal.go.operation.details.label.lrname = Recurso
na.portal.go.operation.details.label.lrid = Id Recurso
na.portal.go.operation.details.label.status = Estado
na.portal.go.operation.details.label.responsecode = Cód. Resposta
na.portal.go.operation.details.label.responsevalue = Mensagem Resposta
na.portal.go.operation.details.label.message = Mensagem
na.portal.go.operation.details.label.descriptiontime = Data/Hora
na.portal.go.operation.details.title.detailsmodal = Detalhe Log/Data
na.portal.go.operation.details.log = Log
na.portal.go.operation.details.data = Data
na.portal.go.operation.details.label.empty = vazio
na.portal.go.operation.details.state.error = Erro
na.portal.go.operation.details.state.not_executed = Não executado
na.portal.go.operation.details.state.skip = Skip
na.portal.go.operation.details.state.executed = Executado
na.portal.go.operation.details.see.code = Ver em código
na.portal.go.operation.details.see.table = Ver em tabela
na.portal.go.operation.create.title = Executar operação genérica
na.portal.go.operation.create.label = Executar operação genérica
na.portal.go.operation.create.legend.requiredfields.label = Campos de preenchimento obrigatório
na.portal.go.operation.create.tab.characteristics.label = Características
na.portal.go.operation.create.tab.characteristics.description.title = Tipo de operação
na.portal.go.operation.create.tab.characteristics.description.text = Dados que permitem a identificação da Operação.
na.portal.go.operation.create.tab.operation.label = Operação
na.portal.go.operation.create.confirm.cancel = Tem a certeza que quer cancelar a operação? Os dados serão perdidos.
na.portal.go.operation.create.error.requiredfield.title = Erro ao criar operação
na.portal.go.operation.create.error.nextstep.title = Erro ao avançar passo
na.portal.go.operation.create.error.requiredfield.text = Campo {0} é obrigatório
na.portal.go.operation.create.label.sending = A enviar
na.portal.go.operation.create.label.waiting.server = A esperar confirmação do servidor
na.portal.go.operation.create.sending.failed.title = Erro no upload do ficheiro de firmware
na.portal.go.operation.create.sending.failed.message = Ocorreu um problema no envio do ficheiro escolhido. Devido a este problema não foram executadas as respetivas operações. Contacte a administração e tente mais tarde.

na.portal.go.admin.operation.create.title = Criar modelo de operação
na.portal.go.admin.operation.create.label = Criar modelo de operação
na.portal.go.admin.template.edit.title = Editar operação
na.portal.go.admin.template.edit.label = Editar operação
na.portal.go.admin.template.add.title = Adicionar modelo de operação
na.portal.go.admin.template.add.label = Adicionar modelo de operação

na.portal.go.adminoperation.create.tab.operation.label = Parâmetros de Operação
na.portal.go.adminoperation.create.tab.operation.title = Parâmetros de Operação
na.portal.go.adminoperation.create.tab.operation.description.title = Identificação da operação
na.portal.go.adminoperation.create.tab.operation.description.text =
na.portal.go.adminoperation.create.tab.operation.fieldDefinition.title = Definição dos campos da operação
na.portal.go.adminoperation.create.tab.operation.fieldDefinition.text =

########## FIELD TYPES ##########
na.portal.go.template.field.type.textfield.title = campo de texto
na.portal.go.template.field.type.textarea.title = area de texto
na.portal.go.template.field.type.selectbox.title = caixa de selecção
na.portal.go.template.field.type.selectbox.option.selected.label = (definição)
na.portal.go.template.field.type.selectbox.noOptions = Sem opções visíveis
na.portal.go.template.field.type.checkboxes.title = caixas de verificação
na.portal.go.template.field.type.checkboxes.option.selected.label = (definição)
na.portal.go.template.field.type.radiobuttons.title = botão de radio
na.portal.go.template.field.type.radiobuttons.option.selected.label = (definição)
na.portal.go.template.field.changedefault = Alterar definição
na.portal.go.template.field.editable = Editável
na.portal.go.template.field.errors.editfield.title = Erro no Campo
na.portal.go.template.field.errors.atleastoneoption.title = Erro no Campo
na.portal.go.template.field.errors.atleastoneoption.message = Deverá ser visivel pelo menos uma opção
na.portal.go.template.field.errors.editingfields.title = Erro a finalizar operação
na.portal.go.template.field.errors.editingfields.message = Existem campos a serem editados, por favor confirme ou cancele as suas alterações.
na.portal.go.template.field.errors.requirednoteditablefield.message = Campo é obrigatório, necessário definir valor ou tornar editável.

na.portal.go.field.type.textfield.title = campo de texto
na.portal.go.field.type.textarea.title = area de texto
na.portal.go.field.type.selectbox.title = caixa de selecção
na.portal.go.field.type.checkboxes.title = caixas de verificação
na.portal.go.field.type.radiobuttons.title = botão de radio

###########BUTTONS###########
na.portal.go.buttons.clone = Clonar
na.portal.go.buttons.createoperation = criar operação
na.portal.go.buttons.executeoperation = executar operação
na.portal.go.buttons.suspend = Suspender
na.portal.go.buttons.cancel = Cancelar
na.portal.go.buttons.configureOptionalFields = configurar campos opcionais

########### GO ERROR MESSAGES ###########
na.portal.go.createoperation.errors.missingfields.title = Campos Obrigatórios
na.portal.go.createoperation.errors.missingfields.message = Preencha todos os campos obrigatórios.
na.portal.go.createoperation.errors.baddata.title = Erro nos Campos
na.portal.go.createoperation.errors.baddata.message = Corrija os dados conforme respetiva notificação.
na.portal.go.template.errors.createoperationmodel.title = Erro a criar modelo de operação
na.portal.go.template.errors.createoperationmodel.message = O nome do modelo de operação não pode ser o mesmo da operação base.

# Tooltips
na.portal.go.template.attention.message = O template pai foi modificado, é necessário validar este template.
na.portal.go.field.all                  = selecionar todos