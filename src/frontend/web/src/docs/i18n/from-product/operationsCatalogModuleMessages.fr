na.portal.operationscatalog.button.create                                                       = C<PERSON>er
na.portal.operationscatalog.button.createAttribute                                              = Créer un attribut

na.portal.operationscatalog.button.operations                                                   = opérations
na.portal.operationscatalog.button.operations.title                                             = Voir les opérations

na.portal.operationscatalog.button.responseCodes                                                = codes de réponse
na.portal.operationscatalog.button.responseCodes.title                                          = Voir les codes de réponse

na.portal.operationscatalog.button.edit.title                                                   = editer
na.portal.operationscatalog.button.delete.title                                                 = effacer
na.portal.operationscatalog.button.save.title                                                   = garder
na.portal.operationscatalog.button.cancel.title                                                 = annuler
na.portal.operationscatalog.button.multiactions.title                                           = plus d''actions

na.portal.operationscatalog.search.name.placeholder                                             = Chercher par le nom
na.portal.operationscatalog.search.code.placeholder                                             = Chercher par type / code

na.portal.operationscatalog.valid.option.label                                                  = Valide
na.portal.operationscatalog.invalid.option.label                                                = Invalide

# Version labels/placeholders

na.portal.operationscatalog.entityLateralVersion.name.label                                     = Nom
na.portal.operationscatalog.entityLateralVersion.name.placeholder                               = Nom de la version
na.portal.operationscatalog.entityLateralVersion.state.label                                    = État
na.portal.operationscatalog.entityLateralVersion.state.placeholder                              = État de la version
na.portal.operationscatalog.entityLateralVersion.catEntityVersion.label                         = Version de l''identité
na.portal.operationscatalog.entityLateralVersion.catEntityVersion.placeholder                   = Version de l''identité de la version
na.portal.operationscatalog.entityLateralVersion.valid.label                                    = État
na.portal.operationscatalog.entityLateralVersion.valid.placeholder                              = État des versions

na.portal.operationscatalog.entityVersion.name.label                                            = Version
na.portal.operationscatalog.entityVersion.name.placeholder                                      = Nom de la version
na.portal.operationscatalog.entityVersion.description.placeholder                               = Description de la version
na.portal.operationscatalog.entityVersion.catEntityVersion.label                                = Version du catalogue d''entités
na.portal.operationscatalog.entityVersion.catEntityVersion.placeholder                          = Version du catalogue d''entités
na.portal.operationscatalog.entityVersion.catEntityVersion.withoutVersion.placeholder           = Sans version

na.portal.operationscatalog.entityVersion.operationVersion.label                                = Version du catalogue d''opérations
na.portal.operationscatalog.entityVersion.operationVersion.placeholder                          = Version du catalogue d''opérations

na.portal.operationscatalog.version.create.label                                                = Créer version
na.portal.operationscatalog.version.fromEntitiesVersion.label                                   = a partir de la version d''entités
na.portal.operationscatalog.version.fromOperationsVersion.label                                 = a partir de la version d''opérations

na.portal.operationscatalog.version.create.noEntitiesVersions                                   = Il n''y a pas de versions d''entités crées ou dans un état valide
na.portal.operationscatalog.version.create.noOperationsVersions                                 = Il n''y a pas de versions d''opérations crées ou dans un état valide

# CFS labels/placeholders

na.portal.operationscatalog.entityLateralCfs.name.label                                         = Nom
na.portal.operationscatalog.entityLateralCfs.name.placeholder                                   = Nom de l''opération

na.portal.operationscatalog.entityLateralCfs.description.label                                  = Description
na.portal.operationscatalog.entityLateralCfs.description.placeholder                            = Description de l''opération

na.portal.operationscatalog.entityLateralCfs.entityName.label                                   = entité
na.portal.operationscatalog.entityLateralCfs.entityName.placeholder                             = Nom de l''entité de l''opération

na.portal.operationscatalog.entityLateralCfs.state.label                                        = État
na.portal.operationscatalog.entityLateralCfs.state.placeholder                                  = État de l''opération

na.portal.operationscatalog.entityLateralCfs.tags.label                                         = Étiquette
na.portal.operationscatalog.entityLateralCfs.tags.placeholder                                   = Étiquettes de l''opération

na.portal.operationscatalog.entityLateralCfs.valid.label                                        = État
na.portal.operationscatalog.entityLateralCfs.valid.placeholder                                  = États des opérations

na.portal.operationscatalog.entityCfs.name.label                                                = Nom
na.portal.operationscatalog.entityCfs.name.placeholder                                          = Nom de l''opération

na.portal.operationscatalog.entityCfs.description.label                                         = Description
na.portal.operationscatalog.entityCfs.description.placeholder                                   = Description de l''opération

na.portal.operationscatalog.entityCfs.entityName.label                                          = entité
na.portal.operationscatalog.entityCfs.entityName.placeholder                                    = Nom de l''entité de l''opération

na.portal.operationscatalog.entityCfs.OPERATION_VERSION_version.label                           = Version
na.portal.operationscatalog.entityCfs.OPERATION_VERSION_version.placeholder                     = Version de l''entité de l''opération

na.portal.operationscatalog.entityCfs.OPERATION_TAGS_tagsAsString.label                         = Étiquette
na.portal.operationscatalog.entityCfs.OPERATION_TAGS_tagsAsString.placeholder                   = Étiquettes de l''opération

na.portal.operationscatalog.entityCfs.workflow.label                                            = Workflow
na.portal.operationscatalog.entityCfs.workflow.placeholder                                      = Workflow de l''opération


# RFS labels/placeholders

na.portal.operationscatalog.entityLateralRfs.name.label                                         = Nom
na.portal.operationscatalog.entityLateralRfs.name.placeholder                                   = Nom de l''opération

na.portal.operationscatalog.entityLateralRfs.description.label                                  = Description
na.portal.operationscatalog.entityLateralRfs.description.placeholder                            = Description de l''opération

na.portal.operationscatalog.entityLateralRfs.entityName.label                                   = entité
na.portal.operationscatalog.entityLateralRfs.entityName.placeholder                             = Nom de l''entité de l''opération

na.portal.operationscatalog.entityLateralRfs.state.label                                        = État
na.portal.operationscatalog.entityLateralRfs.state.placeholder                                  = État de l''opération

na.portal.operationscatalog.entityLateralRfs.tags.label                                         = Étiquettes
na.portal.operationscatalog.entityLateralRfs.tags.placeholder                                   = Étiquettes de l''opération

na.portal.operationscatalog.entityLateralRfs.valid.label                                        = État
na.portal.operationscatalog.entityLateralRfs.valid.placeholder                                  = État des opérations

na.portal.operationscatalog.entityRfs.name.label                                                = Nom
na.portal.operationscatalog.entityRfs.name.placeholder                                          = Nom de l''opération

na.portal.operationscatalog.entityRfs.description.label                                         = Description
na.portal.operationscatalog.entityRfs.description.placeholder                                   = Description de l''opération

na.portal.operationscatalog.entityRfs.entityName.label                                          = entité
na.portal.operationscatalog.entityRfs.entityName.placeholder                                    = Nom de l''entité de l''opération

na.portal.operationscatalog.entityRfs.OPERATION_VERSION_version.label                           = Version
na.portal.operationscatalog.entityRfs.OPERATION_VERSION_version.placeholder                     = Version de l''entité de l''opération

na.portal.operationscatalog.entityRfs.OPERATION_TAGS_tagsAsString.label                         = Étiquettes
na.portal.operationscatalog.entityRfs.OPERATION_TAGS_tagsAsString.placeholder                   = Étiquettes de l''opération

na.portal.operationscatalog.entityRfs.workflow.label                                            = Workflow
na.portal.operationscatalog.entityRfs.workflow.placeholder                                      = Workflow de l''opération


# Resource labels/placeholders

na.portal.operationscatalog.entityLateralResource.name.label                                    = Nom
na.portal.operationscatalog.entityLateralResource.name.placeholder                              = Nom de l''opération

na.portal.operationscatalog.entityLateralResource.description.label                             = Description
na.portal.operationscatalog.entityLateralResource.description.placeholder                       = Description de l''opération

na.portal.operationscatalog.entityLateralResource.entityName.label                              = Entité
na.portal.operationscatalog.entityLateralResource.entityName.placeholder                        = Nom de l''entité de l''opération

na.portal.operationscatalog.entityLateralResource.state.label                                   = État
na.portal.operationscatalog.entityLateralResource.state.placeholder                             = État de l''opération

na.portal.operationscatalog.entityLateralResource.tags.label                                    = Étiquettes
na.portal.operationscatalog.entityLateralResource.tags.placeholder                              = Étiquettes de l''opération

na.portal.operationscatalog.entityLateralResource.valid.label                                   = État
na.portal.operationscatalog.entityLateralResource.valid.placeholder                             = État des opérations

na.portal.operationscatalog.entityResource.name.label                                           = Nom
na.portal.operationscatalog.entityResource.name.placeholder                                     = Nom de l''opération

na.portal.operationscatalog.entityResource.description.label                                    = Description
na.portal.operationscatalog.entityResource.description.placeholder                              = Description de l''opération

na.portal.operationscatalog.entityResource.entityName.label                                     = entité
na.portal.operationscatalog.entityResource.entityName.placeholder                               = Nome da entité de l''opération

na.portal.operationscatalog.entityResource.OPERATION_VERSION_version.label                      = Version
na.portal.operationscatalog.entityResource.OPERATION_VERSION_version.placeholder                = Version de l''entité de l''opération

na.portal.operationscatalog.entityResource.OPERATION_TAGS_tagsAsString.label                    = Étiquettes
na.portal.operationscatalog.entityResource.OPERATION_TAGS_tagsAsString.placeholder              = Étiquettes de l''opération

na.portal.operationscatalog.entityResource.workflow.label                                       = Workflow
na.portal.operationscatalog.entityResource.workflow.placeholder                                 = Workflow de l''opération


# Response Code labels/placeholders

na.portal.operationscatalog.entityLateralResponseCode.type.label                                = Type
na.portal.operationscatalog.entityLateralResponseCode.type.placeholder                          = Type du code de réponse

na.portal.operationscatalog.entityLateralResponseCode.code.label                                = Code
na.portal.operationscatalog.entityLateralResponseCode.code.placeholder                          = Code du code de réponse

na.portal.operationscatalog.entityLateralResponseCode.tags.label                                = Étiquettes
na.portal.operationscatalog.entityLateralResponseCode.tags.placeholder                          = Étiquettes du code de réponse


# Datatable labels

na.portal.operationscatalog.datatables.column.label.actions                                     = Actions
na.portal.operationscatalog.datatables.column.label.catEntityName                               = Nom de l''entité
na.portal.operationscatalog.datatables.column.label.name                                        = Nom
na.portal.operationscatalog.datatables.column.label.description                                 = Description
na.portal.operationscatalog.datatables.column.label.state                                       = État
na.portal.operationscatalog.datatables.column.label.version                                     = Version
na.portal.operationscatalog.datatables.column.label.operation                                   = Opération
na.portal.operationscatalog.datatables.column.label.typeCode                                    = Type / Code
na.portal.operationscatalog.datatables.column.label.tags                                        = Étiquettes
na.portal.operationscatalog.datatables.column.label.typeId                                      = Valeur
na.portal.operationscatalog.datatables.column.label.value                                       = Position
na.portal.operationscatalog.datatables.column.label.position                                    = Type
na.portal.operationscatalog.datatables.column.label.attribute                                   = attribut
na.portal.operationscatalog.datatables.column.label.attributeName                               = Nom d''attribut
na.portal.operationscatalog.datatables.column.label.attributeType                               = Type de l''attribut
na.portal.operationscatalog.datatables.column.label.restriction                                 = Restriction
na.portal.operationscatalog.datatables.column.label.entityName                                  = entité
na.portal.operationscatalog.datatables.column.label.entityVersion                               = Version de l''entité
na.portal.operationscatalog.datatables.column.label.attributes_entities                         = attributs / entités
na.portal.operationscatalog.datatables.column.label.enrichmentRule                              = Règles d''enrichissement
na.portal.operationscatalog.datatables.column.label.cardinality                                 = Cardinalité
na.portal.operationscatalog.datatables.column.label.validationRule                              = Règles de validation
na.portal.operationscatalog.datatables.column.label.entities                                    = entités
na.portal.operationscatalog.datatables.column.label.attributes                                  = attributs

na.portal.operationscatalog.datatables.column.label.outputEntities                              = entités de sortie
na.portal.operationscatalog.datatables.column.label.outputAttributes                            = attributs de sortie

na.portal.operationscatalog.datatable.actions.options                                           = options

# State Labels

na.portal.operationscatalog.entities.state.DEVELOPMENT                                          = Développement
na.portal.operationscatalog.entities.state.TESTING                                              = Tests
na.portal.operationscatalog.entities.state.ABANDONED                                            = Abandonné
na.portal.operationscatalog.entities.state.PRODUCTION                                           = Production
na.portal.operationscatalog.entities.state.BLOCKED                                              = Bloqué
na.portal.operationscatalog.entities.state.DEPRECATED                                           = Obsolète

na.portal.operationscatalog.datatable.actions.state.changeStateTo                               = changer l''état pour
na.portal.operationscatalog.datatable.actions.state.noFurtherStates                             = sans états suivant

# Multi-select

na.portal.operationscatalog.selectbox.placeholder                                               = Selectioner
na.portal.operationscatalog.input.nomatches                                                     = Aucun résultat trouvé
na.portal.operationscatalog.version.noVersions                                                  = Aucune version trouvée
na.portal.operationscatalog.tag.newTag                                                          = (nova tag)

na.portal.operationscatalog.tab.characteristics                                                 = Caractéristiques
na.portal.operationscatalog.tab.section.general.characteristics                                 = Caractérisation de base
na.portal.operationscatalog.tab.section.input.attributes                                        = attributs d''entrée
na.portal.operationscatalog.tab.section.execution.attributes                                    = attributs d''éxécution
na.portal.operationscatalog.tab.section.output.attributes                                       = attributs de sortie

# Input attributes modal

na.portal.operationscatalog.modal.title.add                                                     = Créer attribut
na.portal.operationscatalog.modal.title.edit                                                    = Edité attribut
na.portal.operationscatalog.modal.label.name                                                    = Nom
na.portal.operationscatalog.modal.label.type                                                    = Type
na.portal.operationscatalog.modal.label.regex                                                   = Expression régulière
na.portal.operationscatalog.modal.label.logicCondition                                          = Condition logique
na.portal.operationscatalog.modal.label.logicalcondition.minSize                                = Numéro minimum de caractères
na.portal.operationscatalog.modal.label.logicalcondition.maxSize                                = Numéro maximum de caractères
na.portal.operationscatalog.modal.label.logicalcondition.maxRange                               = Limite supérieur
na.portal.operationscatalog.modal.label.logicalcondition.minRange                               = Limite inférieur
na.portal.operationscatalog.modal.label.entityRule                                              = Règle d''entité
na.portal.operationscatalog.modal.label.cardinality                                             = Cardinalité
na.portal.operationscatalog.modal.label.cardinality.min                                         = Min:
na.portal.operationscatalog.modal.label.cardinality.max                                         = Max:
na.portal.operationscatalog.modal.label.restrictions                                            = Restrictions
na.portal.operationscatalog.modal.label.restrictions.doNotApply                                 = Ne pas appliquer
na.portal.operationscatalog.modal.helper.regex                                                  = Information d''aide pour RegEx
na.portal.operationscatalog.modal.validation.numeric                                            = La Valeur doit être en numérique
na.portal.operationscatalog.modal.validation.minLowerThanMax                                    = La limite supérieur doit être supérieur ou égal à la limite inférieur.
na.portal.operationscatalog.modal.validation.required                                           = Champs obligatoires.
na.portal.operationscatalog.modal.validation.notNegative                                        = Les valeurs ne peuvent pas être negatives.
na.portal.operationscatalog.modal.validation.atLeastOneFieldRequired                            = Remplissez au moins un des champs marqués en rouge.
na.portal.operationscatalog.modal.error.notIntegerNumber                                        = Les valeurs doivent être entières.
na.portal.operationscatalog.modal.error.regularExpression.required                              = Définir l''expression régulière.
na.portal.operationscatalog.modal.error.regularExpression.invalidSyntax                         = Expression régulière invalide
na.portal.operationscatalog.modal.error.enumeration.notEmpty                                    = Vous devez choisir, au minimum, une valeur.
na.portal.operationscatalog.modal.error.enumeration.sameAsEntity                                = Les mêmes valeurs que celles de l''entité ont été appliquées, modifiez-les ou sélectionnez "Ne pas appliquer" pour continuer.
na.portal.operationscatalog.modal.error.outOfRangeOfEntityRule                                  = Valeurs hors limite ({0}, {1}) de la règle d''entité

na.portal.operationscatalog.form.notification.failure.title                                     = Il n''est pas possible de garder le formulaire
na.portal.operationscatalog.form.notification.failure.message                                   = Veuillez vérifier les champs signalés

# Attribute constraint types

na.portal.operationscatalog.attribute.constraint.type.SIZE                                      = Dimension
na.portal.operationscatalog.attribute.constraint.type.REGEXP                                    = Expression régulière
na.portal.operationscatalog.attribute.constraint.type.RANGE                                     = Intervalle
na.portal.operationscatalog.attribute.constraint.type.ENUMC                                     = Enumérateur
na.portal.operationscatalog.attribute.constraint.type.FLOAT_RANGE                               = Float

# Confirmation modals

na.portal.operationscatalog.modals.version.confirm.state.change.title                           = Transition de l''état de la version
na.portal.operationscatalog.modals.version.confirm.state.change.content                         = Voulez-vous vraiment passer de la version "{0}" pour l''état "{1}"?

na.portal.operationscatalog.modals.operation.confirm.state.change.title                         = Transition de l''état de l''opération
na.portal.operationscatalog.modals.operation.confirm.state.change.content                       = Voulez-vous vraiment passer de l''operation "{0}" pour l''état "{1}"?

na.portal.operationscatalog.modals.version.confirm.delete.title                                 = Effacer version
na.portal.operationscatalog.modals.version.confirm.delete.content                               = Voulez-vous vraiment effacer la version "{0}"?

na.portal.operationscatalog.modals.operation.confirm.delete.title                               = Effacer opération
na.portal.operationscatalog.modals.operation.confirm.delete.content                             = Voulez-vous vraiment effacer l''opération "{0}"?

na.portal.operationscatalog.modals.version.confirm.clone.title                                  = Clonar versão
na.portal.operationscatalog.modals.version.confirm.clone.content                                = Voulez-vous vraiment cloner la version "{0}"?

na.portal.operationscatalog.modals.operation.confirm.clone.title                                = Cloner opération
na.portal.operationscatalog.modals.operation.confirm.clone.content                              = Voulez-vous vraiment cloner l''opération "{0}"?

na.portal.operationscatalog.modals.operation.confirm.entity.name.change.title                   = Changement du nom de l''identité
na.portal.operationscatalog.modals.operation.confirm.entity.name.change.content                 = Voulez-vous vraiment changer le nom de l''identité? Tous les attributs seront effacés

# Notifications

na.portal.operationscatalog.notification.state.update.success.title                             = Changer avec succès
na.portal.operationscatalog.notification.state.update.success.content                           = État de la version changée avec succès!

na.portal.operationscatalog.notification.operation.state.update.success.title                   = Changer avec succès
na.portal.operationscatalog.notification.operation.state.update.success.content                 = État de l''opération changée avec succès!

na.portal.operationscatalog.notification.version.delete.success.title                           = Effacer avec succès
na.portal.operationscatalog.notification.version.delete.success.content                         = Version effacée avec succès!

na.portal.operationscatalog.notification.operation.delete.success.title                         = Effacer avec succès
na.portal.operationscatalog.notification.operation.delete.success.content                       = Opération effacée avec succès!

na.portal.operationscatalog.notification.failure.title                                          = Il n''est pas possible de garder le formulaire
na.portal.operationscatalog.notification.failure.message                                        = Veuillez vérifier les champs signalés

na.portal.operationscatalog.notification.this.version.is.invalid                                = Cette opération n''est pas valide
na.portal.operationscatalog.notification.this.operation.is.invalid                              = Cette opération est invalide
na.portal.operationscatalog.notification.this.attribute.is.invalid                              = Cet attribut est invalide

# Validations

na.portal.operationscatalog.validations.mandatory                                               = Champs obligatoires