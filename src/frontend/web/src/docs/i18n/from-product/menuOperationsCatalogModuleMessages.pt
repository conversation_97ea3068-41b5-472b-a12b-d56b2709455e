na.portal.operationscatalog.module.title                                                        = Catálogo
na.portal.operationscatalog.module.description                                                  = O módulo Catálogo está dividido em duas secções: Entidades e Operações. A secção Entidades permite definir Customer Facing Service (CFS), Resource Facing Services (RFS) e Recursos. Os tipos de atributos também podem ser definidos e associados como atributos às entidades com restrições definidas associadas ao seu tipo base. Os tipos base usados para criar os tipos de atributos são numérico, string e enumerador, que são todos tipos simples, mas também o tipo complexo componente que pode usar tipos de atributos criados anteriormente para criar estruturas complexas. O catálogo também permite o desenho de relacionamentos entre CFSs, RFSs e Recursos. O catálogo possui capacidades de versionamento para que a definição das entidades, seus atributos e seu relacionamento estejam sempre associados a uma versão específica. A secção Operações permite a definição de operações em Customer Facing Service (CFS), Resource Facing Services (RFS) e Recursos. Cada operação deve ter um nome, uma entidade base associada (CFS, RFS ou LR), o nome do fluxo a ser executado e também os atributos de entrada e saída associados à entidade base ou às entidades a ela relacionadas. Para os atributos de entrada podem ser definidas restrições adicionais, caso o atributo original também possua restrições será utilizada a união dessas restrições. O catálogo possui recursos de versionamento para que a definição das operações esteja sempre associada a uma versão específica.
na.portal.operationscatalog.submodule.title                                                     = Operações

na.portal.operationscatalog.home.label.title                                                    = Versões
na.portal.operationscatalog.home.label.subtitle                                                 = Modulo do catálogo de operações

na.portal.operationscatalog.nav.context                                                         = Catálogo de operações
na.portal.operationscatalog.nav.version                                                         = Versões
na.portal.operationscatalog.nav.cfs                                                             = Serviços de cliente
na.portal.operationscatalog.nav.rfs                                                             = Serviços de rede
na.portal.operationscatalog.nav.resource                                                        = Recursos
na.portal.operationscatalog.nav.responseCode                                                    = Código de resposta
na.portal.operationscatalog.nav.create                                                          = Criar
