
exception.00001.developer.message      = Execeção possivelmente lançada por BadGateway,GatewayTimeout ou Timeout
exception.00001.title                  = Serviço REST API temporariamente indisponível
exception.00001.user.message           = Serviço temporariamente indisponível
exception.00002.developer.message      = Não foi possivel concluir a operação
exception.00002.title                  = Erro não definido
exception.00002.user.message           = Não foi possivel concluir a operação
exception.00003.developer.message      = Erro de configuração
exception.00003.title                  = Erro de configuração
exception.00003.user.message           = Erro de configuração
exception.00007.title                  = Erro no pedido
exception.404.title                    = Entidade não encontrada
exception.EXCEPTIONLOCATION00002.title = Pré-condição não respeitada
exception.FMWKCOMMON00011.title        = Pré-condição não respeitada
exception.FMWKCOMMON00013.title        = Pré-condição não respeitada
exception.FMWKCOMMON00016.title        = Pré-condição não respeitada
exception.FMWKCOMMON00030.title        = Pré-condição não respeitada