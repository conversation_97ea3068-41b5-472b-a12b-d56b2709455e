# datatables
datatables.column.label.actions                                 = Actions
datatables.column.label.catalog.catEntityName                   = Nom de l''identitée
datatables.column.label.catalog.name                            = Nom

na.reference.data.results.label.title                           = Gestion de donnés

na.reference.data.clone.title                                   = À cloner une instance du catalogue
na.reference.data.clone.message                                 = Vous êtes en train de créer une nouvelle instance du catalogue ayant pour base des informations d''une instance déjà existente

na.reference.data.form.notification.failure.message             = Veuillez vérifier les champs signalés, s''il vous plaît
na.reference.data.form.notification.failure.title               = Sauvegarde du formulaire impossible

na.reference.data.modal.entity.changesubtype.title              = Changement du catalogue
na.reference.data.modal.entity.changesubtype.content            = Voulez-vous supprimer les changements?
na.reference.data.modal.entity.delete.title                     = Éffacer
na.reference.data.modal.entity.deleteMultiple                   = Éffacer {0} instance(s) du catalogue
na.reference.data.modal.navigate.button.confirm                 = Confirmer
na.reference.data.modal.navigate.button.cancel                  = Annuler
na.reference.data.modal.header.delete                           = Éffacer
na.reference.data.modal.body.delete                             = Voulez-vous supprimer cette instance?

na.reference.data.details.title.edit                            = éditer
na.reference.data.details.title.clone                           = cloner
na.reference.data.details.title.delete                          = éffacer

na.reference.data.notification.preconditionfailed.idcatentity   = id d''identitée

na.reference.data.persistence.error.delete.title                = Élimination impossible.

na.reference.data.tab.characteristics                           = Caractéristiques
na.reference.data.tab.attributes                                = Attributs
na.reference.data.tab.specific.attributes                       = Attributs spécifiques

# ----------------------
# --- web components ---
# ----------------------

na.portal.reference.data.idCatEntity.label                      = Type
na.portal.reference.data.idCatEntity.placeholder                = Sélectionner

na.portal.reference.data.state.label                            = Etat
na.portal.reference.data.state.placeholder                      = Sélectionner

na.portal.reference.data.entity.clone.idCatEntity.label         = Type
na.portal.reference.data.entity.clone.idCatEntity.placeholder   = Sélectionner

na.portal.reference.data.entity.create.idCatEntity.label        = Type
na.portal.reference.data.entity.create.idCatEntity.placeholder  = Sélectionner

na.portal.reference.data.entity.create.state.label              = Etat
na.portal.reference.data.entity.create.state.placeholder        = Sélectionner

na.portal.reference.data.entity.edit.state.label                = Etat
na.portal.reference.data.entity.edit.state.placeholder          = Sélectionner

na.portal.reference.data.entity.externalCode.label              = Code externe
na.portal.reference.data.entity.externalCode.placeholder        = Catalogue Code externe

na.portal.reference.data.entity.idCatEntity.label               = Type
na.portal.reference.data.entity.idCatEntity.placeholder         = Sélectionner

na.portal.reference.data.entity.name.label                      = Nom
na.portal.reference.data.entity.name.placeholder                = Nom du catalogue
na.portal.reference.data.entity.name.error.msg.pattern          = Alphanumérique et qui ne peut pas commencer para un numéro.

na.portal.reference.data.entity.search.idCatEntity.label        = Type
na.portal.reference.data.entity.search.idCatEntity.placeholder  = Sélectionner

na.portal.reference.data.entity.search.state.label              = Etat
na.portal.reference.data.entity.search.state.placeholder        = Sélectionner

na.portal.reference.data.entity.state.label                     = Etat
na.portal.reference.data.entity.state.placeholder               = Sélectionner

na.portal.reference.data.entityLateral.idCatEntity.label        = Type
na.portal.reference.data.entityLateral.idCatEntity.placeholder  = Sélectionner

na.portal.reference.data.entityLateral.state.label              = Etat
na.portal.reference.data.entityLateral.state.placeholder        = Sélectionner

# ---------------------------------
# --- reference data navigation ---
# ---------------------------------

na.reference.data.dashboard.button.create                       = Créer
na.reference.data.breadcrumb.action.create                      = Créer
na.reference.data.breadcrumb.home                               = Gestion de donnés
na.reference.data.nav.search                                    = Rechercher

# ------------------------------------
# --- reference data portal resume ---
# ------------------------------------

na.reference.data.module.title                                  = Gestion de donnés
na.reference.data.module.description                            = Le module de gestion des données permet la gestion des informations de base du système qui prennent en charge les processus d''activation tels que les informations d''accès à l''équipement, les mappages de données entre les systèmes et le réseau, les profils d''équipement, etc.
