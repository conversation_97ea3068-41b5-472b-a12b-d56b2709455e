na.portal.operationscatalog.button.create                                                       = <PERSON><PERSON>r
na.portal.operationscatalog.button.createAttribute                                              = Criar atributo

na.portal.operationscatalog.button.operations                                                   = operações
na.portal.operationscatalog.button.operations.title                                             = Consultar operações

na.portal.operationscatalog.button.responseCodes                                                = códigos de resposta
na.portal.operationscatalog.button.responseCodes.title                                          = Consultar códigos de resposta

na.portal.operationscatalog.button.edit.title                                                   = editar
na.portal.operationscatalog.button.delete.title                                                 = eliminar
na.portal.operationscatalog.button.save.title                                                   = guardar
na.portal.operationscatalog.button.cancel.title                                                 = cancelar
na.portal.operationscatalog.button.multiactions.title                                           = mais ações

na.portal.operationscatalog.search.name.placeholder                                             = Procurar por nome
na.portal.operationscatalog.search.code.placeholder                                             = Procurar por tipo / código

na.portal.operationscatalog.valid.option.label                                                  = Válido
na.portal.operationscatalog.invalid.option.label                                                = Inválido

# Version labels/placeholders

na.portal.operationscatalog.entityLateralVersion.name.label                                     = Nome
na.portal.operationscatalog.entityLateralVersion.name.placeholder                               = Nome da versão
na.portal.operationscatalog.entityLateralVersion.state.label                                    = Estado
na.portal.operationscatalog.entityLateralVersion.state.placeholder                              = Estado da versão
na.portal.operationscatalog.entityLateralVersion.catEntityVersion.label                         = Versões de entidade
na.portal.operationscatalog.entityLateralVersion.catEntityVersion.placeholder                   = Versão de entidade da versão
na.portal.operationscatalog.entityLateralVersion.valid.label                                    = Status
na.portal.operationscatalog.entityLateralVersion.valid.placeholder                              = Status das versões

na.portal.operationscatalog.entityVersion.name.label                                            = Versão
na.portal.operationscatalog.entityVersion.name.placeholder                                      = Nome da versão
na.portal.operationscatalog.entityVersion.description.placeholder                               = Descrição da versão
na.portal.operationscatalog.entityVersion.catEntityVersion.label                                = Versão do catálogo de entidades
na.portal.operationscatalog.entityVersion.catEntityVersion.placeholder                          = Versão do catálogo de entidades
na.portal.operationscatalog.entityVersion.catEntityVersion.withoutVersion.placeholder           = Sem versão

na.portal.operationscatalog.entityVersion.operationVersion.label                                = Versão do catálogo de operações
na.portal.operationscatalog.entityVersion.operationVersion.placeholder                          = Versão do catálogo de operações

na.portal.operationscatalog.version.create.label                                                = Criar versão
na.portal.operationscatalog.version.fromEntitiesVersion.label                                   = a partir da versão de entidades
na.portal.operationscatalog.version.fromOperationsVersion.label                                 = a partir da versão de operações

na.portal.operationscatalog.version.create.noEntitiesVersions                                   = Não existem versões de entidades criadas ou num estado válido
na.portal.operationscatalog.version.create.noOperationsVersions                                 = Não existem versões de operações criadas ou válidas

# CFS labels/placeholders

na.portal.operationscatalog.entityLateralCfs.name.label                                         = Nome
na.portal.operationscatalog.entityLateralCfs.name.placeholder                                   = Nome da operação

na.portal.operationscatalog.entityLateralCfs.description.label                                  = Descrição
na.portal.operationscatalog.entityLateralCfs.description.placeholder                            = Descrição da operação

na.portal.operationscatalog.entityLateralCfs.entityName.label                                   = Entidade
na.portal.operationscatalog.entityLateralCfs.entityName.placeholder                             = Nome da entidade da operação

na.portal.operationscatalog.entityLateralCfs.state.label                                        = Estado
na.portal.operationscatalog.entityLateralCfs.state.placeholder                                  = Estado da operação

na.portal.operationscatalog.entityLateralCfs.tags.label                                         = Tags
na.portal.operationscatalog.entityLateralCfs.tags.placeholder                                   = Tags da operação

na.portal.operationscatalog.entityLateralCfs.valid.label                                        = Status
na.portal.operationscatalog.entityLateralCfs.valid.placeholder                                  = Status das operações

na.portal.operationscatalog.entityCfs.name.label                                                = Nome
na.portal.operationscatalog.entityCfs.name.placeholder                                          = Nome da operação

na.portal.operationscatalog.entityCfs.description.label                                         = Descrição
na.portal.operationscatalog.entityCfs.description.placeholder                                   = Descrição da operação

na.portal.operationscatalog.entityCfs.entityName.label                                          = Entidade
na.portal.operationscatalog.entityCfs.entityName.placeholder                                    = Nome da entidade da operação

na.portal.operationscatalog.entityCfs.OPERATION_VERSION_version.label                           = Versão
na.portal.operationscatalog.entityCfs.OPERATION_VERSION_version.placeholder                     = Versão de entidade da operação

na.portal.operationscatalog.entityCfs.OPERATION_TAGS_tagsAsString.label                         = Tags
na.portal.operationscatalog.entityCfs.OPERATION_TAGS_tagsAsString.placeholder                   = Tags da operação

na.portal.operationscatalog.entityCfs.workflow.label                                            = Workflow
na.portal.operationscatalog.entityCfs.workflow.placeholder                                      = Workflow da operação


# RFS labels/placeholders

na.portal.operationscatalog.entityLateralRfs.name.label                                         = Nome
na.portal.operationscatalog.entityLateralRfs.name.placeholder                                   = Nome da operação

na.portal.operationscatalog.entityLateralRfs.description.label                                  = Descrição
na.portal.operationscatalog.entityLateralRfs.description.placeholder                            = Descrição da operação

na.portal.operationscatalog.entityLateralRfs.entityName.label                                   = Entidade
na.portal.operationscatalog.entityLateralRfs.entityName.placeholder                             = Nome da entidade da operação

na.portal.operationscatalog.entityLateralRfs.state.label                                        = Estado
na.portal.operationscatalog.entityLateralRfs.state.placeholder                                  = Estado da operação

na.portal.operationscatalog.entityLateralRfs.tags.label                                         = Tags
na.portal.operationscatalog.entityLateralRfs.tags.placeholder                                   = Tags da operação

na.portal.operationscatalog.entityLateralRfs.valid.label                                        = Status
na.portal.operationscatalog.entityLateralRfs.valid.placeholder                                  = Status das operações

na.portal.operationscatalog.entityRfs.name.label                                                = Nome
na.portal.operationscatalog.entityRfs.name.placeholder                                          = Nome da operação

na.portal.operationscatalog.entityRfs.description.label                                         = Descrição
na.portal.operationscatalog.entityRfs.description.placeholder                                   = Descrição da operação

na.portal.operationscatalog.entityRfs.entityName.label                                          = Entidade
na.portal.operationscatalog.entityRfs.entityName.placeholder                                    = Nome da entidade da operação

na.portal.operationscatalog.entityRfs.OPERATION_VERSION_version.label                           = Versão
na.portal.operationscatalog.entityRfs.OPERATION_VERSION_version.placeholder                     = Versão de entidade da operação

na.portal.operationscatalog.entityRfs.OPERATION_TAGS_tagsAsString.label                         = Tags
na.portal.operationscatalog.entityRfs.OPERATION_TAGS_tagsAsString.placeholder                   = Tags da operação

na.portal.operationscatalog.entityRfs.workflow.label                                            = Workflow
na.portal.operationscatalog.entityRfs.workflow.placeholder                                      = Workflow da operação


# Resource labels/placeholders

na.portal.operationscatalog.entityLateralResource.name.label                                    = Nome
na.portal.operationscatalog.entityLateralResource.name.placeholder                              = Nome da operação

na.portal.operationscatalog.entityLateralResource.description.label                             = Descrição
na.portal.operationscatalog.entityLateralResource.description.placeholder                       = Descrição da operação

na.portal.operationscatalog.entityLateralResource.entityName.label                              = Entidade
na.portal.operationscatalog.entityLateralResource.entityName.placeholder                        = Nome da entidade da operação

na.portal.operationscatalog.entityLateralResource.state.label                                   = Estado
na.portal.operationscatalog.entityLateralResource.state.placeholder                             = Estado da operação

na.portal.operationscatalog.entityLateralResource.tags.label                                    = Tags
na.portal.operationscatalog.entityLateralResource.tags.placeholder                              = Tags da operação

na.portal.operationscatalog.entityLateralResource.valid.label                                   = Status
na.portal.operationscatalog.entityLateralResource.valid.placeholder                             = Status das operações

na.portal.operationscatalog.entityResource.name.label                                           = Nome
na.portal.operationscatalog.entityResource.name.placeholder                                     = Nome da operação

na.portal.operationscatalog.entityResource.description.label                                    = Descrição
na.portal.operationscatalog.entityResource.description.placeholder                              = Descrição da operação

na.portal.operationscatalog.entityResource.entityName.label                                     = Entidade
na.portal.operationscatalog.entityResource.entityName.placeholder                               = Nome da entidade da operação

na.portal.operationscatalog.entityResource.OPERATION_VERSION_version.label                      = Versão
na.portal.operationscatalog.entityResource.OPERATION_VERSION_version.placeholder                = Versão de entidade da operação

na.portal.operationscatalog.entityResource.OPERATION_TAGS_tagsAsString.label                    = Tags
na.portal.operationscatalog.entityResource.OPERATION_TAGS_tagsAsString.placeholder              = Tags da operação

na.portal.operationscatalog.entityResource.workflow.label                                       = Workflow
na.portal.operationscatalog.entityResource.workflow.placeholder                                 = Workflow da operação


# Response Code labels/placeholders

na.portal.operationscatalog.entityLateralResponseCode.type.label                                = Tipo
na.portal.operationscatalog.entityLateralResponseCode.type.placeholder                          = Tipo do código de resposta

na.portal.operationscatalog.entityLateralResponseCode.code.label                                = Código
na.portal.operationscatalog.entityLateralResponseCode.code.placeholder                          = Código do código de resposta

na.portal.operationscatalog.entityLateralResponseCode.tags.label                                = Tags
na.portal.operationscatalog.entityLateralResponseCode.tags.placeholder                          = Tags do código de resposta


# Datatable labels

na.portal.operationscatalog.datatables.column.label.actions                                     = Ações
na.portal.operationscatalog.datatables.column.label.catEntityName                               = Nome da entidade
na.portal.operationscatalog.datatables.column.label.name                                        = Nome
na.portal.operationscatalog.datatables.column.label.description                                 = Descrição
na.portal.operationscatalog.datatables.column.label.state                                       = Estado
na.portal.operationscatalog.datatables.column.label.version                                     = Versão
na.portal.operationscatalog.datatables.column.label.operation                                   = Operação
na.portal.operationscatalog.datatables.column.label.typeCode                                    = Tipo / Código
na.portal.operationscatalog.datatables.column.label.tags                                        = Tags
na.portal.operationscatalog.datatables.column.label.typeId                                      = Valor
na.portal.operationscatalog.datatables.column.label.value                                       = Posição
na.portal.operationscatalog.datatables.column.label.position                                    = Tipo
na.portal.operationscatalog.datatables.column.label.attribute                                   = Atributo
na.portal.operationscatalog.datatables.column.label.attributeName                               = Nome do atributo
na.portal.operationscatalog.datatables.column.label.attributeType                               = Tipo do atributo
na.portal.operationscatalog.datatables.column.label.restriction                                 = Restrição
na.portal.operationscatalog.datatables.column.label.entityName                                  = Entidade
na.portal.operationscatalog.datatables.column.label.entityVersion                               = Versão de entidade
na.portal.operationscatalog.datatables.column.label.attributes_entities                         = Atributos / Entidades
na.portal.operationscatalog.datatables.column.label.enrichmentRule                              = Regras de enriquecimento
na.portal.operationscatalog.datatables.column.label.cardinality                                 = Cardinalidade
na.portal.operationscatalog.datatables.column.label.validationRule                              = Regras de validação
na.portal.operationscatalog.datatables.column.label.entities                                    = Entidades
na.portal.operationscatalog.datatables.column.label.attributes                                  = Atributos

na.portal.operationscatalog.datatables.column.label.outputEntities                              = Entidades de saída
na.portal.operationscatalog.datatables.column.label.outputAttributes                            = Atributos de saída

na.portal.operationscatalog.datatable.actions.options                                           = opções

# State Labels

na.portal.operationscatalog.entities.state.DEVELOPMENT                                          = Desenvolvimento
na.portal.operationscatalog.entities.state.TESTING                                              = Testes
na.portal.operationscatalog.entities.state.ABANDONED                                            = Abandonada
na.portal.operationscatalog.entities.state.PRODUCTION                                           = Produção
na.portal.operationscatalog.entities.state.BLOCKED                                              = Bloqueada
na.portal.operationscatalog.entities.state.DEPRECATED                                           = Obsoleta

na.portal.operationscatalog.datatable.actions.state.changeStateTo                               = alterar o estado para
na.portal.operationscatalog.datatable.actions.state.noFurtherStates                             = sem estados seguintes

# Multi-select

na.portal.operationscatalog.selectbox.placeholder                                               = Selecione
na.portal.operationscatalog.input.nomatches                                                     = Nenhum resultado encontrado
na.portal.operationscatalog.version.noVersions                                                  = Nenhuma versão encontrada
na.portal.operationscatalog.tag.newTag                                                          = (nova tag)

na.portal.operationscatalog.tab.characteristics                                                 = Características
na.portal.operationscatalog.tab.section.general.characteristics                                 = Caracterização base
na.portal.operationscatalog.tab.section.input.attributes                                        = Atributos de entrada
na.portal.operationscatalog.tab.section.execution.attributes                                    = Atributos de execução
na.portal.operationscatalog.tab.section.output.attributes                                       = Atributos de saída

# Input attributes modal

na.portal.operationscatalog.modal.title.add                                                     = Criar atributo
na.portal.operationscatalog.modal.title.edit                                                    = Editar atributo
na.portal.operationscatalog.modal.label.name                                                    = Nome
na.portal.operationscatalog.modal.label.type                                                    = Tipo
na.portal.operationscatalog.modal.label.regex                                                   = Expressão regular
na.portal.operationscatalog.modal.label.logicCondition                                          = Condição lógica
na.portal.operationscatalog.modal.label.logicalcondition.minSize                                = Número mínimo de caracteres
na.portal.operationscatalog.modal.label.logicalcondition.maxSize                                = Número máximo de caracteres
na.portal.operationscatalog.modal.label.logicalcondition.maxRange                               = Limite superior
na.portal.operationscatalog.modal.label.logicalcondition.minRange                               = Limite inferior
na.portal.operationscatalog.modal.label.entityRule                                              = Regra entidade
na.portal.operationscatalog.modal.label.cardinality                                             = Cardinalidade
na.portal.operationscatalog.modal.label.cardinality.min                                         = Mín:
na.portal.operationscatalog.modal.label.cardinality.max                                         = Máx:
na.portal.operationscatalog.modal.label.restrictions                                            = Restrições
na.portal.operationscatalog.modal.label.restrictions.doNotApply                                 = Não aplicar
na.portal.operationscatalog.modal.helper.regex                                                  = Informação de ajuda para RegEx
na.portal.operationscatalog.modal.validation.numeric                                            = Valor tem que ser numérico
na.portal.operationscatalog.modal.validation.minLowerThanMax                                    = O limite superior tem que ser maior ou igual ao limite inferior.
na.portal.operationscatalog.modal.validation.required                                           = Os campos são obrigatórios.
na.portal.operationscatalog.modal.validation.notNegative                                        = Os valores não podem ser negativos.
na.portal.operationscatalog.modal.validation.atLeastOneFieldRequired                            = Preencha pelo menos um dos campos assinalados a vermelho.
na.portal.operationscatalog.modal.error.notIntegerNumber                                        = Os valores têm que ser inteiros.
na.portal.operationscatalog.modal.error.regularExpression.required                              = Defina a expressão regular.
na.portal.operationscatalog.modal.error.regularExpression.invalidSyntax                         = Expressão regular inválida.
na.portal.operationscatalog.modal.error.enumeration.notEmpty                                    = Deve ter pelo menos um valor selecionado.
na.portal.operationscatalog.modal.error.enumeration.sameAsEntity                                = Foram aplicados os mesmos valores dos da entidade, altere-os ou selecione "Não aplicar" para continuar.
na.portal.operationscatalog.modal.error.outOfRangeOfEntityRule                                  = Valores fora do intervalo ({0}, {1}) da regra de entidade

na.portal.operationscatalog.form.notification.failure.title                                     = Não é possível guardar o formulário
na.portal.operationscatalog.form.notification.failure.message                                   = Por favor verifique os campos assinalados

# Attribute constraint types

na.portal.operationscatalog.attribute.constraint.type.SIZE                                      = Dimensão
na.portal.operationscatalog.attribute.constraint.type.REGEXP                                    = Expressão regular
na.portal.operationscatalog.attribute.constraint.type.RANGE                                     = Intervalo
na.portal.operationscatalog.attribute.constraint.type.ENUMC                                     = Enumerado
na.portal.operationscatalog.attribute.constraint.type.FLOAT_RANGE                               = Float

# Confirmation modals

na.portal.operationscatalog.modals.version.confirm.state.change.title                           = Transição do estado da versão
na.portal.operationscatalog.modals.version.confirm.state.change.content                         = Tem a certeza que quer transitar a versão "{0}" para o estado "{1}"?

na.portal.operationscatalog.modals.operation.confirm.state.change.title                         = Transição do estado da operação
na.portal.operationscatalog.modals.operation.confirm.state.change.content                       = Tem a certeza que quer transitar a operação "{0}" para o estado "{1}"?

na.portal.operationscatalog.modals.version.confirm.delete.title                                 = Apagar versão
na.portal.operationscatalog.modals.version.confirm.delete.content                               = Tem a certeza que pretende apagar a versão "{0}"?

na.portal.operationscatalog.modals.operation.confirm.delete.title                               = Apagar operação
na.portal.operationscatalog.modals.operation.confirm.delete.content                             = Tem a certeza que pretende apagar a operação "{0}"?

na.portal.operationscatalog.modals.version.confirm.clone.title                                  = Clonar versão
na.portal.operationscatalog.modals.version.confirm.clone.content                                = Tem a certeza que pretende clonar a versão "{0}"?

na.portal.operationscatalog.modals.operation.confirm.clone.title                                = Clonar operação
na.portal.operationscatalog.modals.operation.confirm.clone.content                              = Tem a certeza que pretende clonar a operação "{0}"?

na.portal.operationscatalog.modals.operation.confirm.entity.name.change.title                   = Alteração do nome da entidade
na.portal.operationscatalog.modals.operation.confirm.entity.name.change.content                 = Tem a certeza que quer alterar o nome da entidade? Todos os atributos serão removidos

# Notifications

na.portal.operationscatalog.notification.state.update.success.title                             = Alterado com sucesso
na.portal.operationscatalog.notification.state.update.success.content                           = Estado da versão alterado com sucesso!

na.portal.operationscatalog.notification.operation.state.update.success.title                   = Alterado com sucesso
na.portal.operationscatalog.notification.operation.state.update.success.content                 = Estado da operação alterado com sucesso!

na.portal.operationscatalog.notification.version.delete.success.title                           = Apagada com sucesso
na.portal.operationscatalog.notification.version.delete.success.content                         = Versão apagada com sucesso!

na.portal.operationscatalog.notification.operation.delete.success.title                         = Apagada com sucesso
na.portal.operationscatalog.notification.operation.delete.success.content                       = Operação apagada com sucesso!

na.portal.operationscatalog.notification.failure.title                                          = Não é possível guardar o formulário
na.portal.operationscatalog.notification.failure.message                                        = Por favor verifique os campos assinalados

na.portal.operationscatalog.notification.this.version.is.invalid                                = Esta versão está inválida
na.portal.operationscatalog.notification.this.operation.is.invalid                              = Esta operação está inválida
na.portal.operationscatalog.notification.this.attribute.is.invalid                              = Este atributo está inválido

# Validations

na.portal.operationscatalog.validations.mandatory                                               = campo de preenchimento obrigatório