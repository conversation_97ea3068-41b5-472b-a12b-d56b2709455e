na.portal.catalog.basemodule.valid.option.label             = Valid
na.portal.catalog.basemodule.invalid.option.label           = Invalid

# Validations

na.portal.catalog.basemodule.validations.mandatory          = mandatory field
na.portal.catalog.basemodule.validations.invalid.pattern    = Invalid format
na.portal.catalog.basemodule.validations.numeric            = Numeric field
na.portal.catalog.basemodule.validations.integer            = Integer field
na.portal.catalog.basemodule.validations.max.characters     = Maximum {0} characters
na.portal.catalog.basemodule.validations.min.characters     = Minimum {0} characters