########################## NOSSIS ##########################

nossis.upload = Envoyer


# ERRORS PAGES
nossis.401.title = 401 - Accès non autorisé!
nossis.401.subtitle = Accès Non autorisé!
nossis.401.description = Vous n''avez pas les privilèges pour accedé a cette page ou la page à besoin que vous soyer connecter.
nossis.401.resolve.label =  Pour résoudre le problème, veuillez essayer une des suivantes options:
nossis.401.tryagain.button = Essayer a nouveau
nossis.401.tryagain.button.description = Essayer de faire a nouveau la demande de page
nossis.401.report.button = Signaler une erreur
nossis.401.report.button.description = Informer les administrateurs du système de ce problème
nossis.401.backtologin = Retourner a la page de login

nossis.404.title = 404 - Page non trouvée!
nossis.404.subtitle = La page n''a pas été trouvée!
nossis.404.description = La page est introuvable, vous vous êtes peut-être trompé a saisir l''adresse ou la page que vous recherchez n''existe plus.
nossis.404.resolve.label =  Pour résoudre le problème, essayer une des suivantes options:
nossis.404.tryagain.button = Essayer a nouveau
nossis.404.tryagain.button.description = Essayer a nouveau la demande de page
nossis.404.report.button = Signaler une erreur
nossis.404.report.button.description = Informer les administrateurs du système de ce problème
nossis.404.backtologin = Retourner a la page de login

nossis.500.title = 500 - Une erreure s''est produite!
nossis.500.subtitle = Une erreure s''est produite!
nossis.500.description = Une erreure s''est produite pendant la demande de la page
nossis.500.resolve.label =  Pour résoudre le problème, essayer une des suivantes options:
nossis.500.tryagain.button = Essayer a nouveau
nossis.500.tryagain.button.description = Essayer a nouveau la demande de page
nossis.500.report.button = Signaler une erreur
nossis.500.report.button.description = Informer les administrateurs du système de ce problème
nossis.500.backtologin = Retourner a la page de login

security.authentication.error.locked = Compte verrouillé, contactez les administrateurs du système.

#Login
na.login.button.ok.text=entrer
na.login.box.user.label=Login
na.login.box.user.placeholder=Introduir Login
na.login.box.password.label=Mot de passe
na.login.box.password.placeholder=Introduir Mot de passe
na.login.title.key.slogan.label=
na.login.label.title.box=Connectez-vous avec votre compte client
na.login.validate.credentials = Valider les informations d''identification... Veuillez patienter.
na.login.captcha.placeholder = Introduir captcha
na.login.captcha.newwords = Obtenir un nouveau captcha
na.login.box.captcha.title = Entrez le texte suivant


naportal.notification.fuximissing.title = Fuxi en manque
naportal.notification.fuximissing.message = Faille de chargement de fichiers essentiels pour la représentation graphique
