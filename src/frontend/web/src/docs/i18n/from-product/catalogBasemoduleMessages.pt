na.portal.catalog.basemodule.valid.option.label             = Válido
na.portal.catalog.basemodule.invalid.option.label           = Inválido

# Validations

na.portal.catalog.basemodule.validations.mandatory          = campo de preenchimento obrigatório
na.portal.catalog.basemodule.validations.invalid.pattern    = Formato inválido
na.portal.catalog.basemodule.validations.numeric            = Campo numérico
na.portal.catalog.basemodule.validations.integer            = Campo de número inteiro
na.portal.catalog.basemodule.validations.max.characters     = Limite máximo de {0} caracteres
na.portal.catalog.basemodule.validations.min.characters     = Limite mínimo de {0} caracteres