#### ABOUT ####

nossis.portal.about=Sobre
nossis.portal.about.title=Sobre
nossis.portal.about.orientations=O produto segue as orientações da política de privacidade da Altice Labs.

nossis.portal.aditional.info=Informação adicional
nossis.portal.terms.and.conditions=Termos e Condições
nossis.portal.main.licenced.to=Este produto está licenciado para Altice. Todos os direitos reservados.
nossis.portal.main.licence.conditions=Para consultar os termos e condições da licença, aceda a
nossis.portal.modules.licence.conditions=Condições, termos e licenciamento -
nossis.portal.versions.and.licences=Versões & Licenças
nossis.portal.supported.browsers=Navegadores suportados
nossis.portal.minimum.hardware.requirements=Requisitos mínimos de hardware

na.button.label.save = Guardar
na.button.label.cancel = Cancelar
na.button.label.export = Exportar
na.button.label.edit = Editar
na.button.label.passwordShow = Mostrar caracteres
na.button.label.passwordHide = Esconder caracteres
na.buttons.search = Pesquisar
na.buttons.clear = Limpar
na.buttons.viewall = Ver todos
na.buttons.caret.moreoptions = Mais opções
na.button.apply = Aplicar

na.module.mpt = MPT
na.module.go = GO

na.general.minimize = Minimizar
na.general.maximize = Maximizar
na.general.filters = Filtros
na.general.search.results = Resultados da pesquisa
na.general.results = Resultados

na.selectbox.placeholder = Selecione
na.selectbox.filters.noresults = Sem filtros
na.selectbox.filters.results = {0} filtros

na.loading = A carregar
loading.label = Aguarde um momento, por favor...

na.basemodule.lateralSearch.title = Pesquisa

#DATATABLES
datatables.navigation.label.first =
datatables.navigation.label.last =
datatables.navigation.label.next = próximo
datatables.navigation.label.previous = anterior
datatables.pagination.info = Página _INPUT_ de _TOTAL_
datatables.info.label.emptytable = Não existem registos a apresentar
# next key is for nossis inv, when angular is no longer used, remove this
datatables.info.label.entriestoshow = Total de registos <b> _TOTAL_ </b>
datatables.info.label.totalresults = Total de registos _TOTAL_
datatables.info.label.infofiltered = - a filtrar de _MAX_ resultados.
datatables.info.label.infopostfix =
datatables.info.label.infothousands = .
datatables.info.label.lengthmenu = registos por página
datatables.info.label.loadingrecords = A carregar...
datatables.info.label.processing = A processar
datatables.info.label.search = Filtrar resultados
datatables.info.label.zerorecords = Não existem registos a apresentar
datatables.info.export.label.currentpage = Página atual
datatables.info.export.label.allpages = Todas as páginas
datatables.info.export.label.info = Informação a exportar
datatables.info.export.label.types = Formato de exportação
datatables.info.export.bymail = O número de resultados a gerar no ficheiro é superior ao total permitido para a geração online da informação. O ficheiro será enviado para o seu endereço de email.
datatables.action.label.seemore = Ver detalhes
datatables.action.label.edit = Editar
datatables.action.label.remove = Remover
datatables.action.label.research = Pesquisar Novamente
datatables.action.label.clone = Clonar
datatables.action.label.logdata = Ver Log/Data
datatables.action.label.add = Adicionar
datatables.action.label.confirm = Confirmar
datatables.action.label.cancel = Cancelar
datatables.action.label.columnfilter = Gerir colunas
datatables.action.label.export = Exportar
datatables.action.label.search = pesquisar...
datatables.action.label.advancedsearch = Pesquisa Avançada
datatables.action.label.exportPDF = PDF
datatables.action.label.exportXLS = XLS
datatables.action.label.exportCSV = CSV
datatables.columnFilter.search.placeholder = Pesquisar

na.datatables.action.label.view                          = consultar
na.datatables.action.label.add                           = adicionar
na.datatables.action.label.clone                         = clonar
na.datatables.action.label.columnfilter                  = gerir colunas
na.datatables.action.label.edit                          = editar
na.datatables.action.label.cancel                        = cancelar
na.datatables.action.label.complete                      = completar
na.datatables.action.label.retry                         = tentar novamente
na.datatables.action.label.skip                          = avançar
na.datatables.action.label.pause                         = pausar
na.datatables.action.label.pin                           = fixar
na.datatables.action.label.remove                        = eliminar
na.datatables.action.label.moreoptions                   = opções
na.datatables.columnFilter.placeholder                   = pesquisar
na.datatables.columnFilter.search.placeholder            = pesquisar...

na.details.title.clone = clonar
na.details.title.edit = editar
na.details.title.delete = eliminar

#TODO: Tornar isto configuravel
datatables.search.advancedsearch.minimum.length = Devem ser utilizados, no mínimo, um carater.

#Notifications
na.notifications.input.freetext = Texto livre
na.notifications.baddate = O formato da data está incorrecto.

#Errors
na.searchform.error.badsyntax = Erro de sintaxe
na.searchform.error.badsyntax.initialDate = O formato do campo Data/Hora Início não é válido.
na.searchform.error.badsyntax.endDate = O formato do campo Data/Hora Fim não é válido.
na.searchform.error.baddates = Erro nas datas inseridas
na.searchform.error.baddates.negativeinterval = A data inicial não pode ser superior à data final.
na.searchform.error.baddates.endDate.biggertoday = A data final não pode ser superior à data atual.
na.searchform.error.baddates.startDate.biggertoday = A data inicial não pode ser superior à data atual.
na.searchform.error.baddates.initialDate = A data inserida no camo Data/Hora Início não é válida.
na.searchform.error.baddates.endDate = A data inserida no camo Data/Hora Fim não é válida.
na.searchform.error.missingdate.initialDate = O campo Data/Hora Início é obrigatório.
na.searchform.error.missingdate.endDate = O campo Data/Hora Fim é obrigatório.
na.searchform.error.baddates.temporalwindow = O intervalo entre as datas não pode ser superior a {0} dias.

na.input.nomatches=Sem resultados

#MODALS
na.modal.button.savefilter = Guardar Filtro
na.modal.button.ok = OK
na.modal.button.confirm = Confirmar
na.modal.button.cancel = Cancelar
na.modal.button.close = Fechar
na.modal.button.deletefilter = Eliminar Filtro
na.modal.button.submit = Submeter
na.modal.button.yes = Sim
na.modal.button.no = Não
na.modal.changepage.dataloss.title = Mudar de página
na.modal.changepage.dataloss.body = Ao sair desta página pode perder dados. Pretende, mesmo assim, mudar de página?
na.modal.filters.title.savefilter = Guardar Filtro
na.modal.filters.info.savefilter = Confirme os dados para a criação do novo filtro pessoal
na.modal.filters.title.editfilter = Editar Filtro
na.modal.filters.title.deletefilter = Eliminar Filtro
na.modal.filters.info.deletefilter = Deseja eliminar o filtro selecionado?
na.modals.fields.name.label = Nome do Filtro
na.modals.fields.name.placeholder = Nome do Filtro
na.modals.filters.error.save = Erro ao guardar filtro
na.modals.filters.error.edit = Erro ao editar filtro
na.modals.filters.error.delete = Erro ao eliminar filtro
na.modals.filters.error.missingstartdate = O campo Data\Hora início é obrigatório.
na.modals.filters.error.missingenddate = O campo Data\Hora fim é obrigatório.
na.modals.filters.error.missingfiltername = O campo Nome do Filtro é obrigatório.
na.modals.filters.error.missingselectedfilter = É necessário escolher um filtro para editar.
na.modals.filters.error.missing.selectfilter.delete = É necessário escolher um filtro para eliminar.
na.modals.filters.success.save = Filtro guardado com sucesso
na.modals.filters.success.save.message = O seu filtro {0} foi guardado com sucesso.
na.modals.filters.success.edit = Filtro editado com sucesso
na.modals.filters.success.edit.message = O seu filtro {0} foi editado com sucesso.
na.modals.filters.success.delete = Filtro eliminado com sucesso
na.modals.filters.success.delete.message = O seu filtro {0} foi eliminado com sucesso.
na.modals.filters.options = Opções do filtro
na.modals.filters.select.label = Selecionar filtro:

################### Module Names
na.portal.module.mpt = MPT
na.portal.module.go = GO
na.portal.module.mpt.admin = Administração MPT

##################DOUBLE BOX
doublebox.addAll = Mover todos >>
doublebox.addSelected = Mover seleção >
doublebox.removeAll = Mover todos <<
doublebox.removeSelected = Mover seleção <

##################DATE FORMATS
na.date.time.format.utc = yyyy-MM-dd'T'HH:mm:ss.SSSX
na.date.time.format.short = dd-MM-yyyy HH:mm
na.date.time.format.seconds = dd-MM-yyyy HH:mm:ss
na.date.time.format.milliseconds = dd-MM-yyyy HH:mm:ss.SSS
na.date.format.short = dd-MM-yyyy
na.time.format.short = HH:mm

na.wizard.buttons.before = Anterior
na.wizard.buttons.next = Seguinte
na.wizard.buttons.finalize = Finalizar
na.wizard.buttons.cancel = Cancelar
na.wizard.step = Passo {0}

na.type = Tipo
na.values = Valores

################# RESUMES
na.information.title = Informação
na.operationalview.accessmodule = Aceder ao módulo

################## VALIDATION ERRORS
na.field.x.required = Campo {0} é obrigatório.
na.fill.all.required.fields = Preencha todos os campos obrigatórios.
na.basemodule.validations.mandatory          = Campo de preenchimento obrigatório
na.basemodule.validations.invalid.pattern    = Formato inválido
na.basemodule.validations.numeric            = Campo numérico
na.basemodule.validations.integer            = Campo de número inteiro
na.basemodule.validations.max.characters     = Limite máximo de {0} caracteres
na.basemodule.validations.min.characters     = Limite mínimo de {0} caracteres

################## MultiSelect

input.nomatches = Nenhum resultado encontrado

################## UPLOAD FILE FIELD
na.upload.field.no.file.selected = Nenhum ficheiro seleccionado
na.upload.field.browse = Procurar