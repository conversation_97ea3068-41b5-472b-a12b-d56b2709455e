veacs.serviceData.voiceProfile = Voice Profile
na.portal.veacs.module.title = VEACS
na.portal.veacs.module.description = O VEACS, Voice Equipment ACS (Configuração Automática de Serviço) é a solução de Device Management para telefone SIP.<br>O VEACS possibilita ao operador gerir o inventário de dispositivos bem como as associações a clientes e serviços. Expõe interfaces de configuração, atualização de firmware e outras configurações.

###### Operations #######

na.portal.veacs.operation.removeSipDevice.title = Remover equipamento SIP
na.portal.veacs.operation.removeSipDevice.subtitle = Nesta área é possível remover o equipamento SIP
na.portal.veacs.operation.removeSipDevice.name = remover equipamento SIP

na.portal.veacs.operation.changeSipDeviceId.title = Alterar SIP do equipamento
na.portal.veacs.operation.changeSipDeviceId.subtitle = Nesta área é possível alterar o SIP do equipamento
na.portal.veacs.operation.changeSipDeviceId.name = alterar SIP do equipamento

na.portal.veacs.operation.addCustomKeyModule.title = Adicionar módulo de teclas
na.portal.veacs.operation.addCustomKeyModule.subtitle = Nesta área é possível adicionar módulo de teclas
na.portal.veacs.operation.addCustomKeyModule.name = adicionar módulo de teclas

na.portal.veacs.operation.removeCustomKeyModule.title = Eliminar módulo de teclas
na.portal.veacs.operation.removeCustomKeyModule.subtitle = Nesta área é possível eliminar o módulo de teclas
na.portal.veacs.operation.removeCustomKeyModule.name = eliminar módulo de teclas

na.portal.veacs.operation.createTemplateWithNewSoftwareVersion.title = Criar template
na.portal.veacs.operation.createTemplateWithNewSoftwareVersion.subtitle = Nesta área é possível criar template com nova versão
na.portal.veacs.operation.createTemplateWithNewSoftwareVersion.name = criar template com nova versão

na.portal.veacs.operation.migrateConfigurationBySoftwareVersion.title = Migrar configuração
na.portal.veacs.operation.migrateConfigurationBySoftwareVersion.subtitle = Nesta área é possível migrar configuração através da versão
na.portal.veacs.operation.migrateConfigurationBySoftwareVersion.name = migrar configuração através da versão

na.portal.veacs.operation.diagnostics.diagnostic.title = Diagnóstico de equipamento
na.portal.veacs.operation.diagnostics.diagnostic.subtitle = Nesta área é possível gerar um diagnóstico de equipamento
na.portal.veacs.operation.diagnostics.diagnostic.name = diagnóstico de equipamento

na.portal.veacs.customKey.keyId.1stKeyOnTheLeftColumnOfThe1stmodule = 1ª do 1º módulo coluna esquerda
na.portal.veacs.customKey.keyId.1stKeyOnTheRightColumnOfThe1stModule = 1ª do 1º módulo coluna direita
na.portal.veacs.customKey.keyId.1stKeyOnTheLeftColumnOfThe2ndModule = 1ª do 2º módulo coluna esquerda
na.portal.veacs.customKey.keyId.1stKeyOnTheRightColumnOfThe2ndModule = 1ª do 2º módulo coluna direita
na.portal.veacs.customKey.keyId.1stKeyOnTheLeftColumnOfThe3rdModule = 1ª do 3º módulo coluna esquerda
na.portal.veacs.customKey.keyId.1stKeyOnTheRightColumnOfThe3rdModule = 1ª do 3º módulo coluna direita
na.portal.veacs.customKey.keyId.1stKeyOfThe1stPageOfThe1stModule = 1ª da 1ª página do 1ª módulo
na.portal.veacs.customKey.keyId.1stKeyOfThe2ndPageOfThe1stModule = 1ª da 2ª página do 1ª módulo
na.portal.veacs.customKey.keyId.1stKeyOfThe1stPageOfThe2ndModule = 1ª da 1ª página do 2ª módulo
na.portal.veacs.customKey.keyId.1stKeyOfThe2ndPageOfThe2ndModule = 1ª da 2ª página do 2ª módulo
na.portal.veacs.customKey.keyId.1stKeyOfThe1stPageOfThe3rdModule = 1ª da 1ª página do 3ª módulo
na.portal.veacs.customKey.keyId.1stKeyOfThe2ndPageOfThe3rdModule = 1ª da 2ª página do 3ª módulo
na.portal.veacs.customKey.keyId.1stKeyOfThe1stPageOfThe4thModule = 1ª da 1ª página do 4ª módulo
na.portal.veacs.customKey.keyId.1stKeyOfThe2ndPageOfThe4thModule = 1ª da 2ª página do 4ª módulo
na.portal.veacs.customKey.keyId.1stKey2ndPageOfTheScreen = 1ª da 2ª página de ecrã
na.portal.veacs.customKey.keyId.1stKey3rdPageOfTheScreen = 1ª da 3ª página de ecrã
na.portal.veacs.customKey.keyId.1stKeyOfThe1stModule = 1ª do 1º módulo
na.portal.veacs.customKey.keyId.1stKeyOfThe2ndModule = 1ª do 2º módulo
na.portal.veacs.customKey.keyId.1stSidekey = 1ª chave lateral