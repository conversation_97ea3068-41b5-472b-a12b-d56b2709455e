# datatables
datatables.column.label.actions                                 = Ações
datatables.column.label.catalog.catEntityName                   = Nome Entidade
datatables.column.label.catalog.name                            = Nome

na.reference.data.results.label.title                           = Gestão de Dados

na.reference.data.clone.title                                   = A clonar uma instância de catálogo
na.reference.data.clone.message                                 = Está a criar uma nova instância de catálogo baseada em informação de uma já existente

na.reference.data.form.notification.failure.message             = Por favor verifique os campos assinalados
na.reference.data.form.notification.failure.title               = Não é possível guardar o formulário

na.reference.data.modal.entity.changesubtype.title              = Alteração de catálogo
na.reference.data.modal.entity.changesubtype.content            = Deseja descartar as alterações?
na.reference.data.modal.entity.delete.title                     = Remover
na.reference.data.modal.entity.deleteMultiple                   = Apagar {0} instância(s) de catálogo
na.reference.data.modal.navigate.button.confirm                 = Confirmar
na.reference.data.modal.navigate.button.cancel                  = Cancelar
na.reference.data.modal.header.delete                           = Apagar
na.reference.data.modal.body.delete                             = Deseja apagar esta instância?

na.reference.data.details.title.edit                            = editar
na.reference.data.details.title.clone                           = clonar
na.reference.data.details.title.delete                          = apagar

na.reference.data.notification.preconditionfailed.idcatentity   = id da entidade

na.reference.data.persistence.error.delete.title                = Não foi possível eliminar.

na.reference.data.tab.characteristics                           = Características
na.reference.data.tab.attributes                                = Atributos
na.reference.data.tab.specific.attributes                       = Atributos Específicos

# ----------------------
# --- web components ---
# ----------------------

na.portal.reference.data.idCatEntity.label                      = Tipo
na.portal.reference.data.idCatEntity.placeholder                = Selecione

na.portal.reference.data.state.label                            = Estado
na.portal.reference.data.state.placeholder                      = Selecione

na.portal.reference.data.entity.clone.idCatEntity.label         = Tipo
na.portal.reference.data.entity.clone.idCatEntity.placeholder   = Selecione

na.portal.reference.data.entity.create.idCatEntity.label        = Tipo
na.portal.reference.data.entity.create.idCatEntity.placeholder  = Selecione

na.portal.reference.data.entity.create.state.label              = Estado
na.portal.reference.data.entity.create.state.placeholder        = Selecione

na.portal.reference.data.entity.edit.state.label                = Estado
na.portal.reference.data.entity.edit.state.placeholder          = Selecione

na.portal.reference.data.entity.externalCode.label              = Código externo
na.portal.reference.data.entity.externalCode.placeholder        = Código externo do catálogo

na.portal.reference.data.entity.idCatEntity.label               = Tipo
na.portal.reference.data.entity.idCatEntity.placeholder         = Selecione

na.portal.reference.data.entity.name.label                      = Nome
na.portal.reference.data.entity.name.placeholder                = Nome do catálogo
na.portal.reference.data.entity.name.error.msg.pattern          = Alfanumérico e não pode começar por número

na.portal.reference.data.entity.search.idCatEntity.label        = Tipo
na.portal.reference.data.entity.search.idCatEntity.placeholder  = Selecione

na.portal.reference.data.entity.search.state.label              = Estado
na.portal.reference.data.entity.search.state.placeholder        = Selecione

na.portal.reference.data.entity.state.label                     = Estado
na.portal.reference.data.entity.state.placeholder               = Selecione

na.portal.reference.data.entityLateral.idCatEntity.label        = Tipo
na.portal.reference.data.entityLateral.idCatEntity.placeholder  = Selecione

na.portal.reference.data.entityLateral.state.label              = Estado
na.portal.reference.data.entityLateral.state.placeholder        = Selecione

# ---------------------------------
# --- reference data navigation ---
# ---------------------------------

na.reference.data.dashboard.button.create                       = Criar
na.reference.data.breadcrumb.action.create                      = Criar
na.reference.data.breadcrumb.home                               = Gestão de Dados
na.reference.data.nav.search                                    = Pesquisar

# ------------------------------------
# --- reference data portal resume ---
# ------------------------------------

na.reference.data.module.title                                  = Gestão de Dados
na.reference.data.module.description                            = O módulo de Gestão de Dados permite a gestão da informação base do sistema que suporta os processos de ativação, tal como, informação de acesso a equipamentos, mapeamentos de dados entre sistemas e rede, perfis de equipamentos, etc.
