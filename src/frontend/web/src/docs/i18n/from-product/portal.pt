# Security

security.authentication.error.credentials           = Nome de utilizador ou palavra-passe inválidos. Por favor, verifique os dados inseridos.
security.authentication.error.lost.session          = A sua sessão expirou. De forma a realizar a operação efetue novamente o login.

# ERROR

error.401.title                                     = Erro 401 Acesso não autorizado
error.401.body                                      = Erro 401 Acesso não autorizado
error.403.title                                     = Erro 403 Acesso não autorizado.
error.403.body                                      = Erro 403 Não tem permissões para aceder à página requisitada.
error.404.title                                     = Erro 404 Pagina não encontrada
error.404.body                                      = Erro 404 Pagina não encontrada
error.captcha.not.satisfied                         = Captcha inválido. Por favor, verifique os dados inseridos.
error.captcha.expired                               = Captcha expirou. Por favor, tente novamente.

# NOSSIS

# LOGIN

nossis.login.title.key.context.portal               = Nossis
nossis.login.button.ok.text                         = Entrar
nossis.login.box.user.label                         = Nome de utilizador
nossis.login.box.user.placeholder                   = Inserir nome de utilizador
nossis.login.box.missingvalues                      = Nome de utilizador ou palavra-passe em falta. Por favor, verifique os dados inseridos.
nossis.login.box.locked                             = A conta {0} está bloqueada, contacte o suporte.
nossis.login.box.password.label                     = Palavra-passe
nossis.login.box.password.placeholder               = Inserir palavra-passe
nossis.login.box.captcha.title                      = Digite o seguinte texto
nossis.login.box.remainingLoginAttempts             = N.º de tentativas restantes
nossis.login.title.key.main.label                   = Suite NOSSIS
nossis.login.title.key.product.label                = Produto(*)
nossis.login.title.key.slogan.label                 = Slogan do produto(*)
nossis.login.label.title.box                        = Faça login com a sua conta de cliente

nossis.footer.label                                 = Nossis © Altice Labs, SA. Todos os direitos reservados.
nossis.footer.about.title                           = Sobre
nossis.back.to.nossis                               = Voltar
nossis.othermodules                                 = Outros Módulos
nossis.dashboard                                    = Início
nossis.aggregator                                   = NETWIN
nossis.wait                                         = Aguarde um momento, por favor...
nossis.resume                                       = NOSSIS RESUMO

nossis.login.captcha.placeholder                    = Inserir texto
nossis.login.validate.credentials                   = A validar credenciais... por favor aguarde.
nossis.login.captcha.newwords                       = Tente uma nova imagem
nossis.login.password.update.required               = Erro, p.f. altere a palavra-passe.
nossis.login.box.password.label.current             = Palavra-passe atual
nossis.login.box.password.label.new                 = Nova palavra-passe
nossis.login.box.password.label.new.confirm         = Confirme nova palavra-passe
nossis.login.box.password.change.failed             = Ocorreu um erro ao alterar a palavra-passe
nossis.login.box.password.change.success            = Palavra-passe alterada com sucesso
nossis.login.header.user.config.password.title      = Alterar a palavra-passe
nossis.login.mandatory.fields                       = Campos de preenchimento obrigatório

nossis.go.to.home                                   = Início

# HEADER

nossis.header.help                                  = Ajuda
nossis.header.logout                                = Terminar sessão
nossis.header.logout.started                        = Iniciada {0}
nossis.header.logout.lastAccess                     = Último acesso {0}
nossis.header.sca                                   = Gestão de acessos
nossis.header.brand                                 = NOSSIS
nossis.header.backto.dashboard                      = Voltar ao Portal
nossis.header.businessView                          = Vistas de negócio
nossis.header.documentation                         = Documentação

# OPERATIONAL VIEW

nossis.operationalview                              = NOSSIS
nossis.operationalview.accessmodule                 = Aceder ao módulo
nossis.operationalview.hidemodulemenu               = Esconder

# MODAL

nossis.modal.changepage.body                        = Ao sair desta página pode perder dados. Pretende, mesmo assim, mudar de página?
nossis.modal.changepage.header                      = Mudar de página
nossis.modal.navigate.button.confirm                = Confirmar
nossis.modal.navigate.button.cancel                 = Cancelar
nossis.modal.navigate.button.close                  = Fechar

# ERRORS PAGES

nossis.error.serverTimePrefix                       = Hora servidor
nossis.error.clientTimePrefix                       = Hora cliente

nossis.error.sessionExpired.titleInfo               = Sessao expirada
nossis.error.sessionExpired.description             = A sua sessão expirou. Os dados não foram guardados/submetidos
nossis.error.sessionExpired.action                  = Clique para <a data-action-context="login">iniciar sessão</a> novamente.

nossis.error.notFound.titleInfo                     = Página não encontrada - 404
nossis.error.notFound.description                   = Pedimos desculpa, mas não encontramos a página que procura.
nossis.error.notFound.action                        = Pode voltar para o <a data-action-context="portal">portal</a>.

nossis.error.notAuthorized.titleInfo                = Acesso não autorizado - 401
nossis.error.notAuthorized.description              = Não tem permissões para aceder à área ou à página com as credenciais que lhe foram fornecidas.
nossis.error.notAuthorized.action                   = Contacte o <a data-action-context="mailto:<EMAIL>">administrador</a> do sistema ou o <a data-action-context="mailto:<EMAIL>">suporte</a>.

nossis.error.badRequest.titleInfo                   = Pedido inválido - 400
nossis.error.badRequest.description                 = Pedimos desculpa, mas não foi possível completar o pedido.
nossis.error.badRequest.action                      = Contacte o <a data-action-context="mailto:<EMAIL>">suporte</a>.

nossis.error.maintenance.titleInfo                  = Em manutenção - 503
nossis.error.maintenance.description                = Pedimos desculpa, mas a aplicação está em manutenção. Prometemos ser breves.
nossis.error.maintenance.action                     = Contacte o <a data-action-context="mailto:<EMAIL>">suporte</a>.

nossis.error.internalServerError.titleInfo          = Erro interno do servidor - 500
nossis.error.internalServerError.description        = Pedimos desculpa, mas não é possível apresentar o conteúdo solicitado.
nossis.error.internalServerError.action             = Contacte o <a data-action-context="mailto:<EMAIL>">suporte</a>.

nossis.error.serverError.errorInfoTitle             = Detalhes do erro
nossis.error.serverError.errorSend                  = Copie o código de erro abaixo e envie ao suporte.
nossis.error.serverError.errorCode                  = Código do erro

nossis.error.generic.title                          = Ocorreu um erro no pedido da página
nossis.error.generic.description                    = Ocorreu um erro no pedido da página, tente novamente ou informe os administradores do sistema sobre este problema

nossis.error.unknown.description                    = Pedimos desculpa, mas ocorreu um erro inesperado.

# RESUME

nossis.information.title                            = Informação
nossis.information.nossis.title                     = NOSSIS
nossis.information.nossis.description               = Ponto de entrada principal dos produtos/soluções
nossis.information.mainfunctionalities.title        = Principais funcionalidades
nossis.information.mainfunctionalities.description  = Projeto de infra-estrutura de rede física e lógica, e construção de rede e fluxos de trabalho de alocação de recursos; Inventário das infra-estruturas e recursos de rede física (interior e exterior), com infra-estruturas exteriores georreferenciados; Lógico inventário de redes e serviços, incluindo a gestão de multisserviços, multi-tecnologia e de vários fornecedores de recursos e catálogos; Reserva e alocação de recursos (instalações de apoio de provisionamento automatizado), com APIs abertas para a integração OSS / BSS; O gerenciamento de capacidade de recursos físicos e lógicos; Reconciliação de rede (através de um sistema de mediação).
nossis.information.customerbenefits.title           = Benefícios para o Cliente
nossis.information.customerbenefits.description     = Inventário de referência principal para toda a empresa; navegação fácil através de todas as informações, a partir do componente de infra-estrutura física para a lógica de serviço; vista E2E de composição de serviços, e compatibilidade com uma ampla gama de tecnologias; Permite concepção do projeto precisa e construção, incluindo as entradas de pesquisas de gestão de capacidade e sites físicos e lógicos; facilitar a integração com sistemas externos para suportar outros processos (incluindo processos fulfillment e garantia, entre outros); Garantir informações precisas de estoque, utilizando processos de reconciliação para manter up-to-date informações

# DOCUMENTATION

nossis.documentation                                = Documentação
nossis.documentation.userManual                     = Manual de utilizador
nossis.documentation.operationalModules             = Módulos operacionais
nossis.documentation.businessView                   = Vistas de negócio
nossis.documentation.otherDocumentation             = Outros manuais
nossis.documentation.dropdown.version               = Versão

# LOAD PAGE MODAL
nossis.password.update.cancel.changes               = Cancelar