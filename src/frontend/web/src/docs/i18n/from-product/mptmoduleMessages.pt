################ MPT MODULE ###############
na.portal.mpt.module.title = MPT
na.portal.mpt.module.description = O módulo de Provisões Massivas possibilita a definição e execução de operações massivas sobre a rede.<br>O operador envia uma lista de ficheiros com as operações a serem efetuadas, o MPT é responsável pela gestão da sua execução.<br>São permitidas execuções agendadas bem como o controlo de cada execução.
na.portal.mpt.mega.menu.title = Operações
na.portal.mpt.mega.menu.navinitial = Navegar para página inicial
na.portal.mpt.mega.menu.mptadmin = Administração MPT

na.portal.mpt.home.label.title = MPT
na.portal.mpt.home.label.subtitle = Provisões massivas
na.portal.mpt.home.mainnav.mptadmin = Administração MPT
na.portal.mpt.home.mainnav.goadmin = Administração GO

###########GENERAL###########
na.portal.mpt.selects.states.empty.option = Sem Estado
na.portal.mpt.selects.states.option.allstates = Todos os Estados
na.portal.mpt.selects.option.allfamilies = Todas as Famílias
na.portal.mpt.messages.informative.mandatoryfields = Campos de preenchimento obrigatório
na.portal.mpt.weekdays.monday = SEG
na.portal.mpt.weekdays.tuesday = TER
na.portal.mpt.weekdays.wednesday = QUA
na.portal.mpt.weekdays.thursday = QUI
na.portal.mpt.weekdays.friday = SEX
na.portal.mpt.weekdays.saturday = SÁB
na.portal.mpt.weekdays.sunday = DOM
na.portal.mpt.weekdays.all = TODOS

###########BUTTONS###########
na.portal.mpt.buttons.createoperation = Criar operação
na.portal.mpt.buttons.suspend = Suspender
na.portal.mpt.buttons.cancel = Cancelar
na.portal.mpt.buttons.addfile = Adicionar Ficheiro

###########FIELDS###########
na.portal.mpt.field.modificationdate.label = Modificado em
na.portal.mpt.field.startdate.label = Data/Hora Início
na.portal.mpt.field.enddate.label = Data/Hora Fim
na.portal.mpt.field.user.label = Utilizador
na.portal.mpt.field.user.placeholder = Utilizador
na.portal.mpt.field.family.label = Família
na.portal.mpt.field.family.placeholder = Família
na.portal.mpt.field.operation.label = Operação
na.portal.mpt.field.operation.placeholder = Operação
na.portal.mpt.field.file.label = Ficheiro
na.portal.mpt.field.file.placeholder = Ficheiro
na.portal.mpt.field.state.label = Estado
na.portal.mpt.field.filtername.label = Nome do Filtro
na.portal.mpt.field.filtername.placeholder = Nome do Filtro

###########TABLES###########
na.portal.mpt.datatables.column.label.insertionDate = Data/Hora
na.portal.mpt.datatables.column.label.user = Utilizador
na.portal.mpt.datatables.column.label.family = Família
na.portal.mpt.datatables.column.label.operation = Operação
na.portal.mpt.datatables.column.label.file = Ficheiro
na.portal.mpt.datatables.column.label.state = Estado
na.portal.mpt.datatables.column.label.executionstate = Execução
na.portal.mpt.datatables.column.label.progress = % Execução
na.portal.mpt.datatables.column.label.status = % OK
na.portal.mpt.datatables.column.label.actions = Ações
na.portal.mpt.datatables.column.label.actions.play = Iniciar
na.portal.mpt.datatables.column.label.actions.pause = Pausar
na.portal.mpt.datatables.column.label.actions.stop = Parar
na.portal.mpt.datatables.column.label.actions.edit = Editar
na.portal.mpt.datatables.column.label.actions.delete = Apagar
na.portal.mpt.datatables.column.label.actions.details = Ver detalhes

###########ADMIN SUSPEND##############
na.portal.mpt.suspend.admin.title = Confirmação
na.portal.mpt.suspend.admin.question = Deseja suspender administrativamente o MPT?
na.portal.mpt.suspend.admin.info = Continuar com a suspensão poderá implicar na não realização da operação de forma definitiva.

###########CREATE OPERATION###########
na.portal.mpt.createoperation.label.sending = A enviar
na.portal.mpt.createoperation.label.validating = A validar
na.portal.mpt.createoperation.label.waiting.server = A esperar confirmação do servidor.
na.portal.mpt.createoperation.label.title = Criar operação massiva
na.portal.mpt.createoperation.tab.label = Criar Operação
na.portal.mpt.createoperation.breadcrumb.label = Criar operação
na.portal.mpt.createoperation.label.characteristics = Características
na.portal.mpt.createoperation.label.files = Ficheiros
na.portal.mpt.createoperation.label.schedule = Agendamento
na.portal.mpt.createoperation.label.provisiontype = Tipo da Provisão Massiva
na.portal.mpt.createoperation.label.operation.identification = Dados que permitem a identificação da Operação
na.portal.mpt.createoperation.label.family = Família
na.portal.mpt.createoperation.label.operation = Operação
na.portal.mpt.createoperation.label.description = Descrição
na.portal.mpt.createoperation.label.selectfiles = Selecione os Ficheiros
na.portal.mpt.createoperation.label.files.limit = Pode inserir até {0} ficheiros, num máximo de {1}MB por ficheiro.
na.portal.mpt.createoperation.label.files.information = Os ficheiros serão executados por prioridade de forma independente e criarão uma operação distinta.
na.portal.mpt.createoperation.label.schedule.information = Identificação do Agendamento
na.portal.mpt.createoperation.label.schedule.immediatly = Imediatamente
na.portal.mpt.createoperation.label.schedule.later = Mais tarde
na.portal.mpt.createoperation.label.schedule.scheduling = Mediante agendamento
na.portal.mpt.createoperation.messages.operationsubmitted = Operação criada com sucesso!
na.portal.mpt.createoperation.messages.operationerror = Ocorreu um erro durante a criação da operação!

############### DATATABLES LABELS ####################
datatables.action.label.addschedule = adicionar agendamento
na.portal.mpt.datatables.column.label.startDate = Data Início
na.portal.mpt.datatables.column.label.origin = Origem
na.portal.mpt.datatables.column.label.description = Descrição
na.portal.mpt.datatables.column.label.endDate = Data Fim
na.portal.mpt.datatables.column.label.startHour = Hora Início
na.portal.mpt.datatables.column.label.endHour = Hora Término
na.portal.mpt.datatables.column.label.duration = Duração
na.portal.mpt.datatables.column.label.weekdays = Periodicidade
na.portal.mpt.datatables.column.label.username = Utilizador
na.portal.mpt.datatables.column.label.state = Estado
na.portal.mpt.datatables.column.label.executionstate = Execução
na.portal.mpt.datatables.column.label.opk = OPK
na.portal.mpt.datatables.column.label.key = Chave
na.portal.mpt.datatables.column.label.parameter = Parâmetro
na.portal.mpt.datatables.column.label.value = Valor

###########EDIT BULK###########
na.portal.mpt.editbulk.label.title = Editar operação massiva
na.portal.mpt.editbulk.tab.label = Editar Operação
na.portal.mpt.editbulk.breadcrumb.label = Editar operação
na.portal.mpt.editbulk.label.characteristics = Características
na.portal.mpt.editbulk.label.files = Ficheiros
na.portal.mpt.editbulk.label.schedule = Agendamento
na.portal.mpt.editbulk.label.provisiontype = Tipo da Provisão Massiva
na.portal.mpt.editbulk.label.operation.identification = Dados que permitem a identificação do Bulk
na.portal.mpt.editbulk.label.family = Família
na.portal.mpt.editbulk.label.operation = Operação
na.portal.mpt.editbulk.label.description = Descrição
na.portal.mpt.editbulk.label.existingfiles = Ficheiros existentes
na.portal.mpt.editbulk.label.files = Ficheiros

###########MPT ERROR MESSAGES###########
na.portal.mpt.createoperation.errors.invalidfield.title = Erro nos campos inseridos
na.portal.mpt.createoperation.errors.invalidfield.message = Por favor corriga os campos assinalados
na.portal.mpt.createoperation.errors.title = Erro ao criar operação.
na.portal.mpt.createoperation.errors.missingfields.title = Campos Obrigatórios
na.portal.mpt.createoperation.errors.missingfields.message = Preencha todos os campos obrigatórios.
na.portal.mpt.createoperation.errors.baddates.title = Erro nas datas inseridas.
na.portal.mpt.createoperation.errors.baddates.intervals.title = Erro nos intervalos definidos.
na.portal.mpt.createoperation.errors.baddates.startDate.past = A data de início não pode estar no passado.
na.portal.mpt.createoperation.errors.baddates.startDate.superior = A data de início não pode ser superior à data de fim.
#mpt.createoperation.errors.baddates.schedules.overlapping = As datas pretendidas no agendamento entram em conflicto com datas já definidas em agendamentos anteriores.
na.portal.mpt.createoperation.errors.baddates.schedules.overlapping = As datas pretendidas no agendamento estão em conflicto com datas já definidas em agendamentos anteriores.
na.portal.mpt.createoperation.errors.invalidfiles.title = Erro no ficheiro seleccionado.
na.portal.mpt.createoperation.errors.invalidfiles.message = O tamanho do ficheiro {0} ({1} MB) é superior ao suportado. Por favor selecione outro ficheiro ou corrija o seu conteúdo.
na.portal.mpt.createoperation.errors.missingfiles.title = Ficheiros em falta.
na.portal.mpt.createoperation.errors.missingfiles.message = É necessário submeter pelo menos um ficheiro para criar uma operação.
na.portal.mpt.createoperation.errors.missingfields.startDate = A data de início para o agendamento é obrigatória.
na.portal.mpt.createoperation.errors.missingfields.endDate = A data de fim para o agendamento é obrigatório.
na.portal.mpt.createoperation.errors.missingfields.intervals = É necessário definir no mínimo um intervalo.
na.portal.mpt.createoperation.errors.missingfields.intervals.badsyntax = Encontram-se presentes intervalos com o formato errado.
na.portal.mpt.createoperation.errors.missingfields.intervals.missing.startDate = O campo Início é obrigatório nos intervalos.
na.portal.mpt.createoperation.errors.missingfields.intervals.missing.endDate = O campo Término é obrigatório nos intervalos.
na.portal.mpt.createoperation.errors.missingfields.intervals.missing.weekdays = A periocidade do agendamento é obrigatória.
na.portal.mpt.createoperation.errors.intervals.overlapping.intervals = Contém intervalos que se sobrepõem, corrija a informação.
na.portal.mpt.createoperation.errors.intervals.expired = Contém intervalos expirados, corrija a informação.
na.portal.mpt.createoperation.errors.schedule.expired = O agendamento está expirado.
na.portal.mpt.createoperation.errors.intervals.repeated.intervals = Contém intervalos repetidos, corrija a informação.
na.portal.mpt.createoperation.errors.globalintervals.repeated.intervals = Tem intervalo(s) que se sobrepõem a intervalos previamente definidos para a mesma data. Corriga a informação.
na.portal.mpt.createoperation.errors.invalidfiles.wrongtype = Atualmente só é suportado ficheiros CSV.
na.portal.mpt.createoperation.errors.intervals.exceeds.limit = Nas datas pretendidas já existe um agendamento definido. O número de intervalos definidos mais os existentes ultrapassa o limite definido. Apague alguns intervalos de forma a não ultrapassar o limite.
na.portal.mpt.createopeartion.errors.invalidafiles.exceeds.linelimit = O ficheiro {0} contém mais linhas que o permitido.
na.portal.mpt.createoperation.errors.schedule.missing.message = O agendamento é obrigatório.
na.portal.mpt.createoperation.errors.files.sending.failed.title = Erro no envio dos ficheiros
na.portal.mpt.createoperation.errors.files.sending.failed.message = Ocorreu um problema no envio dos seguintes ficheiros {0}. Devido a este problema não foram criadas as respetivas operações. Contacte a administração e tente mais tarde.
na.portal.mpt.loaddetails.tab.content.error.title = Erro na operação
na.portal.mpt.loaddetails.tab.content.error.description = Ocorreu um erro a obter os detalhes pretendidos. Tente mais tarde.

na.portal.mpt.bulks.form.invalid.fields.title = Erros no formulário.
na.portal.mpt.bulks.form.startdate.invalid.syntax = O formato do campo Data / Hora de Início está incorreto.
na.portal.mpt.bulks.form.enddate.invalid.syntax = O formato do campo Data / Hora de Fim está incorreto.
na.portal.mpt.bulks.form.invalid.dates.startdate.after.enddate = A Data / Hora de Início não pode ser superior à Data / Hora de Fim.
na.portal.mpt.bulks.form.operation.invalid.syntax = O formato do campo Operação está incorreto.
na.portal.mpt.bulks.form.username.invalid.syntax = O formato do campo Utilizador está incorreto.
na.portal.mpt.bulks.form.filename.invalid.syntax = O formato do campo Ficheiro está incorreto.
na.portal.mpt.bulks.filter.form.missing.filtername = O campo Nome do Filtro é obrigatório.
na.portal.mpt.bulks.filter.form.empty = É necessário preencher no mínimo um campo para guardar o filtro.

na.portal.mpt.editbulk.errors.missingfields.title = Campos Obrigatórios
na.portal.mpt.editbulk.errors.title.missingfields.message = Preencha todos os campos obrigatórios.
na.portal.mpt.editbulk.errors.notupdatable = O Bulk seleccionado não se encontra num estado onde seja possível a sua modificação. Contate o administrador.
################ MODALS ################
na.portal.mpt.createoperation.modals.scheduling.title = Agendamento
na.portal.mpt.createoperation.modals.scheduling.subtitle = Configure o agendamento que pretende associar à provisão massiva. Está definido na sua timezone ({0})
na.portal.mpt.createoperation.modals.scheduling.startDate = Início
na.portal.mpt.createoperation.modals.scheduling.endDate = Data fim
na.portal.mpt.createoperation.modals.scheduling.noLimit = Sem data de fim
na.portal.mpt.createoperation.modals.scheduling.endBy = Termina em
na.portal.mpt.createoperation.modals.scheduling.noLimit.starthourinfo = Escolha a hora de início do agendamento.
na.portal.mpt.createoperation.modals.scheduling.startHour = Início
na.portal.mpt.createoperation.modals.scheduling.period = Escolha a periodicidade
na.portal.mpt.createoperation.modals.scheduling.interval.startHour = Início
na.portal.mpt.createoperation.modals.scheduling.interval.duration = Duração
na.portal.mpt.createoperation.modals.scheduling.interval.hours = horas
na.portal.mpt.createoperation.modals.scheduling.interval.minutes = minutos
na.portal.mpt.createoperation.modals.addSchedule = Adicionar Agendamento
na.portal.mpt.createoperation.modals.chooseStartTimeAndDuration = Escolha a hora de início e duração do seu agendamento:
na.portal.mpt.editoperation.label.schedule = Agendamento
na.portal.mpt.editoperation.label.schedule.information = Identificação do Agendamento

################ BULKS ################
na.portal.mpt.bulks.states.validating = A Validar
na.portal.mpt.bulks.states.pending = Pendente
na.portal.mpt.bulks.states.waiting = Em Espera
na.portal.mpt.bulks.states.executing = A Executar
na.portal.mpt.bulks.states.suspending = A Suspender
na.portal.mpt.bulks.states.suspended = Suspenso
na.portal.mpt.bulks.states.aborting = A Abortar
na.portal.mpt.bulks.states.rejected = Rejeitado
na.portal.mpt.bulks.states.terminated = Terminado
na.portal.mpt.bulks.states.scheduled = Agendado
na.portal.mpt.bulks.states.expired = Expirado
na.portal.mpt.bulks.states.suspended_administratively = Suspenso Administrativamente
na.portal.mpt.bulks.states.aborted = Abortado
na.portal.mpt.bulks.states.undefined = Desconhecido
na.portal.mpt.bulks.states.error = Erro

na.portal.mpt.bulks.order.status.ok = OK
na.portal.mpt.bulks.order.status.nok = NOK
na.portal.mpt.bulks.order.status.rejected = Rejeitado

na.portal.mpt.bulks.execution.states.not_started = Não iniciado
na.portal.mpt.bulks.execution.states.started = Iniciado
na.portal.mpt.bulks.execution.states.terminated = Terminado
na.portal.mpt.bulks.execution.states.error = Erro

na.portal.mpt.bulks.actions.play.loading = A Iniciar
na.portal.mpt.bulks.actions.pause.loading = A Suspender
na.portal.mpt.bulks.actions.stop.loading = A Parar

################ BULK DETAILS ################
na.portal.mpt.bulk.details.characteristics = Características
na.portal.mpt.bulk.details.results = Resultados
na.portal.mpt.bulk.details.characteristics.description = Descrição
na.portal.mpt.bulk.details.characteristics.schedule = Agendamento
na.portal.mpt.bulk.details.characteristics.transitions = Transição de Estado
na.portal.mpt.bulk.details.characteristics.description.filename = Nome do Ficheiro
na.portal.mpt.bulk.details.characteristics.description.familyname = Família
na.portal.mpt.bulk.details.characteristics.description.operationname = Operação
na.portal.mpt.bulk.details.characteristics.description.description = Descrição
na.portal.mpt.bulks.details.inputfile=ficheiro de entrada
na.portal.mpt.bulks.details.outputfile=ficheiro de saída


############### RESULTS ###############
na.portal.mpt.results.fields.label.startDate = Data / Hora de Início
na.portal.mpt.results.fields.label.endDate = Data / Hora de Fim
na.portal.mpt.results.fields.label.opk = OPK
na.portal.mpt.results.fields.label.key = Chave
na.portal.mpt.results.fields.label.state = Estado
na.portal.mpt.results.fields.label.execution.state = Execução

na.portal.mpt.results.form.invalid.fields.title = Erros no formulário.
na.portal.mpt.results.form.starttime.invalid.syntax = O formato do campo Data / Hora de Início está incorreto.
na.portal.mpt.results.form.endtime.invalid.syntax = O formato do campo Data / Hora de Fim está incorreto.
na.portal.mpt.results.form.invalid.dates.starttime.after.endtime = A Data / Hora de Início não pode ser superior à Data / Hora de Fim.
na.portal.mpt.results.filter.form.missing.filtername = O campo Nome do Filtro é obrigatório.
na.portal.mpt.results.filter.form.empty = É necessário preencher no mínimo um campo para guardar o filtro.

############## RESULT DETAILS ################
na.portal.mpt.result.details.title.opk = OPK
na.portal.mpt.result.details.subtitle.operation = Operação:
na.portal.mpt.result.details.subtitle.rfs = RFS:
na.portal.mpt.result.details.characteristics = Características
na.portal.mpt.result.details.characteristics.description = Descrição
na.portal.mpt.result.details.characteristics.description.filename = Nome do Ficheiro
na.portal.mpt.result.details.characteristics.description.familyname = Família
na.portal.mpt.result.details.characteristics.description.operationname = Operação
na.portal.mpt.result.details.characteristics.description.requestdate = Data do Pedido
na.portal.mpt.result.details.characteristics.description.testduration = Duração do Teste
na.portal.mpt.result.details.characteristics.description.username = Utilizador
na.portal.mpt.result.details.characteristics.state = Estado
na.portal.mpt.result.details.characteristics.state.starttime = Data de Início
na.portal.mpt.result.details.characteristics.state.endtime = Data de Fim
na.portal.mpt.result.details.characteristics.state.status = Estado
na.portal.mpt.result.details.characteristics.state.description = Descrição
na.portal.mpt.result.details.characteristics.payload = Payload
na.portal.mpt.result.details.label.request = Pedido
na.portal.mpt.result.details.label.response = Resposta
na.portal.mpt.result.details.characteristics.description.lineNum = Nº da linha

############# ERROR TITLES #############
exception.NA_MPT_00001.title = Erro a pesquisar
exception.NA_MPT_00002.title = Erro a pesquisar
exception.NA_MPT_00003.title = Erro no download
exception.NA_MPT_00004.title = Erro a pesquisar
exception.NA_MPT_00005.title = Erro a pesquisar
exception.NA_MPT_00006.title = Erro a criar
exception.NA_MPT_00007.title = Erro a apagar
exception.NA_MPT_00008.title = Estado inválido
exception.NA_MPT_00009.title = Erro a iniciar
exception.NA_MPT_00010.title = Erro a pausar
exception.NA_MPT_00011.title = Erro a parar
exception.NA_MPT_00012.title = Erro a supender