na.portal.go.module.title = GO
na.portal.go.module.description = Le module Opérations génériques permet à l''opérateur d''appeler n''importe quelle opération disponible à partir de la NBI. <br> L''administrateur du système peut gérer les opérations disponibles pour chaque groupe d''utilisateurs et en dériver des opérations afin de faciliter leur utilisation dans des cas spécifiques.
na.portal.go.mega.menu.title = Opérations
na.portal.go.mega.menu.navinitial = Aller sur la page principale

na.portal.go.admin.label.title = Administration GO
na.portal.go.admin.label.subtitle = Gestion des Opérations Génériques

na.portal.go.home.label.title = GO
na.portal.go.home.label.subtitle = Operations Génériques
na.portal.go.home.mainnav.go = GO
na.portal.go.home.mainnav.admin = Administration GO
na.portal.go.home.menu.admin = Administration
na.portal.go.mainnav.admin.title = Administration

###########FIELDS###########
na.portal.go.field.beginDate.label = Date/Heure Début
na.portal.go.field.endDate.label = Date/Heure Fin
na.portal.go.family.field.label = Famille
na.portal.go.family.selects.empty.option = Toutes les familles
na.portal.go.field.orderKey.label = OPK
na.portal.go.field.orderKey.placeholder = OPK
na.portal.go.rfs.field.label = RFS
na.portal.go.rfs.selects.empty.option = Tous les RFS
na.portal.go.state.field.label = État
na.portal.go.state.selects.empty.option = Tous les états
na.portal.go.operation.field.label = Opération
na.portal.go.operation.selects.empty.option = Toutes les opérations
na.portal.go.field.operation.label = Opération
na.portal.go.field.operation.placeholder = Nom de l''opération
na.portal.go.field.operationName.label = Nom de l''opération
na.portal.go.field.user.label = Utilisateur
na.portal.go.field.user.placeholder = Utilisateur
na.portal.go.field.field.label = Famille
na.portal.go.field.operationName.placeholder =
na.portal.go.field.modificationBeginDate.label = Modifié le
na.portal.go.field.modificationEndDate.label = a

#Datatables
na.portal.go.datatables.column.label.lastModification = Dernier changement
na.portal.go.datatables.column.label.stateAction = État/Action
na.portal.go.datatables.column.label.insertionDate = Date/Heure
na.portal.go.datatables.column.label.orderKey = OPK
na.portal.go.datatables.column.label.username = Utilisateur
na.portal.go.datatables.column.label.family = Famille
na.portal.go.datatables.column.label.operation = Opération
na.portal.go.datatables.column.label.rfs = RFS
na.portal.go.datatables.column.label.state = État
na.portal.go.datatables.column.label.name = Nom
na.portal.go.datatables.column.label.type = Type
na.portal.go.datatables.column.label.value = Valeur
na.portal.go.datatables.column.label.messageType = Paramètre
na.portal.go.datatables.column.label.status = État
na.portal.go.datatables.column.label.errorMessage = Message
na.portal.go.datatables.column.label.actions = Actions
na.portal.go.datatables.column.label.request = Demande
na.portal.go.datatables.column.label.response = Réponse
na.portal.go.datatables.column.label.description = Description
na.portal.go.datatables.column.label.lrName = LR Name
na.portal.go.datatables.action.label.edit = Éditer
na.portal.go.datatables.action.label.add = Ajouter
na.portal.go.datatables.action.label.remove = Retirer


#Execution state

na.portal.go.operation.state.PENDING.label = En attente
na.portal.go.operation.state.EXECUTING.label = En exécution
na.portal.go.operation.state.TERMINATED.label = Terminé
na.portal.go.operation.state.STARTED.label = Commencé
na.portal.go.operation.state.NOT_STARTED.label = Pas commencé
na.portal.go.operation.state.ERROR.label = Erreur

#Admin Execution state
na.portal.go.adminoperation.state.ACTIVE.label = ON
na.portal.go.adminoperation.state.INACTIVE.label = OFF

na.portal.go.operation.details.characteristics = Caractéristiques
na.portal.go.operation.details.label.operation = Opération
na.portal.go.operation.details.label.family = Famille
na.portal.go.operation.details.label.rfs = RFS
na.portal.go.operation.details.label.responseurl = URL de Réponse
na.portal.go.operation.details.label.username = Utilisateur
na.portal.go.operation.details.label.description = Description
na.portal.go.operation.details.label.state = État
na.portal.go.operation.details.label.payload = Charge utile
na.portal.go.operation.details.label.request = Demande
na.portal.go.operation.details.label.response = Réponse
na.portal.go.operation.details.label.nooperation = Sans opération
na.portal.go.operation.details.payload.empty = Sans Réponse
na.portal.go.operation.details.breadcrumb.order = Ordre
na.portal.go.operation.details.label.lrname = Ressource
na.portal.go.operation.details.label.lrid = Id Ressource
na.portal.go.operation.details.label.status = État
na.portal.go.operation.details.label.responsecode = Cod. Réponse
na.portal.go.operation.details.label.responsevalue = Message Réponse
na.portal.go.operation.details.label.message = Message
na.portal.go.operation.details.label.descriptiontime = Date/Heure
na.portal.go.operation.details.title.detailsmodal = Détail Log/Date
na.portal.go.operation.details.log = Log
na.portal.go.operation.details.data = Date
na.portal.go.operation.details.label.empty = vide
na.portal.go.operation.details.state.error = Erreur
na.portal.go.operation.details.state.not_executed = Non exécuté
na.portal.go.operation.details.state.skip = Sauter
na.portal.go.operation.details.state.executed = Exécuter
na.portal.go.operation.details.see.code = Voir en code
na.portal.go.operation.details.see.table = Voir en tableau
na.portal.go.operation.create.title = Exécuté opération générique
na.portal.go.operation.create.label = Exécuté opération générique
na.portal.go.operation.create.legend.requiredfields.label = Champs obligatoires
na.portal.go.operation.create.tab.characteristics.label = Caractéristiques
na.portal.go.operation.create.tab.characteristics.description.title = Type d''opération
na.portal.go.operation.create.tab.characteristics.description.text = Données qui permettent l''identification de l''opération.
na.portal.go.operation.create.tab.operation.label = Opération
na.portal.go.operation.create.confirm.cancel = Êtes-vous sure de vouloir terminer l''opération? Les données seront perdues.
na.portal.go.operation.create.error.requiredfield.title = Erreur pendant la création de l''opération
na.portal.go.operation.create.error.nextstep.title = Erreur pendant progression
na.portal.go.operation.create.error.requiredfield.text = Champ {0} obrigatoire
na.portal.go.operation.create.label.sending = En envoyer
na.portal.go.operation.create.label.waiting.server = En attente de la confirmation du serveur.
na.portal.go.operation.create.sending.failed.title = Erreur lors du téléchargement du fichier du firmware
na.portal.go.operation.create.sending.failed.message = Une erreur s''est produite lors de l''envoi du fichier choisi. Suite à ce problème les respectives opérations n''ont pas été réalisé. Veuillez contacter l''administrateur du système et essayer plus tard.

na.portal.go.admin.operation.create.title = Créer modèle d''opération
na.portal.go.admin.operation.create.label = Créer modèle d''opération
na.portal.go.admin.template.edit.title = Édition de l''opération
na.portal.go.admin.template.edit.label = Édition de l''opération
na.portal.go.admin.template.add.title = Ajouter modèle d''opération
na.portal.go.admin.template.add.label = Ajouter modèle d''opération

na.portal.go.adminoperation.create.tab.operation.label = Paramètres d''Opération
na.portal.go.adminoperation.create.tab.operation.title = Paramètres d''Opération
na.portal.go.adminoperation.create.tab.operation.description.title = Identification de l''opération
na.portal.go.adminoperation.create.tab.operation.description.text =
na.portal.go.adminoperation.create.tab.operation.fieldDefinition.title = Définir les champs de l''opération
na.portal.go.adminoperation.create.tab.operation.fieldDefinition.text =

########## FIELD TYPES ##########
na.portal.go.template.field.type.textfield.title = champ de texte
na.portal.go.template.field.type.textarea.title = espace de texte
na.portal.go.template.field.type.selectbox.title = boîte de sélection
na.portal.go.template.field.type.selectbox.option.selected.label = (définition)
na.portal.go.template.field.type.selectbox.noOptions = Sans options visibles
na.portal.go.template.field.type.checkboxes.title = boîtes de verification
na.portal.go.template.field.type.checkboxes.option.selected.label = (définition)
na.portal.go.template.field.type.radiobuttons.title = bouton radio
na.portal.go.template.field.type.radiobuttons.option.selected.label = (définition)
na.portal.go.template.field.changedefault = Changer définition
na.portal.go.template.field.editable = Editable
na.portal.go.template.field.errors.editfield.title = Erreur dans le champ
na.portal.go.template.field.errors.atleastoneoption.title = Erreur dans le champ
na.portal.go.template.field.errors.atleastoneoption.message = Au moins une option devra être visible
na.portal.go.template.field.errors.editingfields.title = Erreur de fin d''opération
na.portal.go.template.field.errors.editingfields.message = I y a des champs en modification, veuillez confirmer ou annuler vos modifications.
na.portal.go.template.field.errors.requirednoteditablefield.message = Champ obligatoire, il est nécessaire de définir la valeur ou bien la rendre éditable.

na.portal.go.field.type.textfield.title = champ de texte
na.portal.go.field.type.textarea.title = espace du texte
na.portal.go.field.type.selectbox.title = boîte de sélection
na.portal.go.field.type.checkboxes.title = boîtes de vérification
na.portal.go.field.type.radiobuttons.title = bouton radio

###########BUTTONS###########
na.portal.go.buttons.clone = Cloner
na.portal.go.buttons.createoperation = créer opération
na.portal.go.buttons.executeoperation = exécution de l''opération
na.portal.go.buttons.suspend = Suspendre
na.portal.go.buttons.cancel = Annuler
na.portal.go.buttons.configureOptionalFields = configurer champs en options

########### GO ERROR MESSAGES ###########
na.portal.go.createoperation.errors.missingfields.title = Champs obligatoires
na.portal.go.createoperation.errors.missingfields.message = Remplir tous les champs obligatoires.
na.portal.go.createoperation.errors.baddata.title = Erreur dans les champs
na.portal.go.createoperation.errors.baddata.message = Veuillez corriger les données comme indique le message.
na.portal.go.template.errors.createoperationmodel.title = Erreur lors de la création du modèle d''opération
na.portal.go.template.errors.createoperationmodel.message = Le nom du modèle de l''opération doit être différent de l''opération base.

# Tooltips
na.portal.go.template.attention.message = Le modèle original a été modifié, ce modèle doit être validé.
na.portal.go.field.all                = sélectionner tout