#### ABOUT ####

nossis.portal.about=About
nossis.portal.about.title=About
nossis.portal.about.orientations=This product follows the Altice Labs orientations and policies regarding privacy.

nossis.portal.aditional.info=Additional Information
nossis.portal.terms.and.conditions=Terms and Conditions
nossis.portal.main.licenced.to=This product is licenced to Altice. All rights reserved.
nossis.portal.main.licence.conditions=To consult licensing terms and conditions go to
nossis.portal.modules.licence.conditions=Conditions, terms and licensing -
nossis.portal.versions.and.licences=Versions & Licenses
nossis.portal.supported.browsers=Supported browsers
nossis.portal.minimum.hardware.requirements=Minimum hardware requirements

na.button.label.save = Save
na.button.label.cancel = Cancel
na.button.label.export = Export
na.button.label.edit = Edit
na.button.label.passwordShow = Show password
na.button.label.passwordHide = Hide password
na.buttons.search = Search
na.buttons.clear = Clear
na.buttons.viewall = View all
na.buttons.caret.moreoptions = More Options
na.button.apply = Apply

na.module.mpt = MPT
na.module.go = GO

na.general.minimize = Minimize
na.general.maximize = Maximize
na.general.filters = Filters
na.general.search.results = Search Results
na.general.results = Results

na.selectbox.placeholder = Select
na.selectbox.filters.noresults = No Filters
na.selectbox.filters.results = {0} filters

na.loading = Loading
loading.label = Wait a moment, please...

na.basemodule.lateralSearch.title = Search

#DATATABLES
datatables.navigation.label.first =
datatables.navigation.label.last =
datatables.navigation.label.next = next
datatables.navigation.label.previous = previous
datatables.pagination.info = Page _INPUT_ of _TOTAL_
datatables.info.label.emptytable = No records to display
# next key is for nossis inv, when angular is no longer used, remove this
datatables.info.label.entriestoshow = Total records <b> _TOTAL_ </b>
datatables.info.label.totalresults = Total records _TOTAL_
datatables.info.label.infofiltered = - filtering from _MAX_ results.
datatables.info.label.infopostfix =
datatables.info.label.infothousands = ,
datatables.info.label.lengthmenu = results per page
datatables.info.label.loadingrecords = Loading...
datatables.info.label.processing = Processing
datatables.info.label.search = Filter Results
datatables.info.label.zerorecords = No records to display
datatables.info.export.label.currentpage = Current Page
datatables.info.export.label.allpages = All Pages
datatables.info.export.label.info = Info to export
datatables.info.export.label.types = Exporting format
datatables.info.export.bymail = The number of results to generate in the file is greater than the total allowed for the online generation of information. The file will be sent to your email address.
datatables.action.label.seemore = More Info
datatables.action.label.edit = Edit
datatables.action.label.remove = Remove
datatables.action.label.research = Search Again
datatables.action.label.clone = Clone
datatables.action.label.logdata = See Log/Data
datatables.action.label.add = Add
datatables.action.label.confirm = Confirm
datatables.action.label.cancel = Cancel
datatables.action.label.columnfilter = Manage Columns
datatables.action.label.export = Export
datatables.action.label.search = Searching...
datatables.action.label.advancedsearch = Advanced Search
datatables.action.label.exportPDF = PDF
datatables.action.label.exportXLS = XLS
datatables.action.label.exportCSV = CSV
datatables.columnFilter.search.placeholder = Search

na.datatables.action.label.view                   = view
na.datatables.action.label.add                    = create
na.datatables.action.label.clone                  = clone
na.datatables.action.label.columnfilter           = column filter
na.datatables.action.label.edit                   = edit
na.datatables.action.label.cancel                 = cancel
na.datatables.action.label.complete               = complete
na.datatables.action.label.retry                  = retry
na.datatables.action.label.skip                   = skip
na.datatables.action.label.pause                  = pause
na.datatables.action.label.pin                    = pin
na.datatables.action.label.remove                 = remove
na.datatables.action.label.moreoptions            = options
na.datatables.columnFilter.placeholder            = search
na.datatables.columnFilter.search.placeholder     = search...

na.details.title.clone = clone
na.details.title.edit = edit
na.details.title.delete = delete

#TODO: Tornar isto configuravel
datatables.search.advancedsearch.minimum.length = Should be used, at least one character.

#Notifications
na.notifications.input.freetext = Free text
na.notifications.baddate = The date format is incorrect.

#Errors
na.searchform.error.badsyntax = Syntax Error
na.searchform.error.badsyntax.initialDate = Initial Date / Time field format is not valid.
na.searchform.error.badsyntax.endDate = End Date / Time field format is not valid.
na.searchform.error.baddates = Error in the inserted dates
na.searchform.error.baddates.negativeinterval = The start date can not exceed the final date.
na.searchform.error.baddates.endDate.biggertoday = The end date can not exceed the current date.
na.searchform.error.baddates.startDate.biggertoday = The start date can not exceed the current date.
na.searchform.error.baddates.initialDate = The date entered in the Initial Date/Time field is not valid.
na.searchform.error.baddates.endDate = The date entered in the End Date/Time field is not valid. 
na.searchform.error.missingdate.initialDate = The Initial Date / Time field is required.
na.searchform.error.missingdate.endDate = The End Date / Time field is required.
na.searchform.error.baddates.temporalwindow = The interval between the dates can not exceed {0} days.

na.input.nomatches=No results

#MODALS
na.modal.button.savefilter = Save Filter
na.modal.button.ok = OK
na.modal.button.confirm = Confirm
na.modal.button.cancel = Cancel
na.modal.button.close = Close
na.modal.button.deletefilter = Delete Filter
na.modal.button.submit = Submit
na.modal.button.yes = Yes
na.modal.button.no = No
na.modal.changepage.dataloss.title = Change page
na.modal.changepage.dataloss.body = Data will be lost. Do you wish to proceed?
na.modal.filters.title.savefilter = Save Filter
na.modal.filters.info.savefilter = Confirm the data for the creation of new personal filter
na.modal.filters.title.editfilter = Edit Filter
na.modal.filters.title.deletefilter = Delete Filter
na.modal.filters.info.deletefilter = Do you want to remove the selected filter?
na.modals.fields.name.label = Filter Name
na.modals.fields.name.placeholder = Filter Name
na.modals.filters.error.save = Error when saving the filter
na.modals.filters.error.edit = Error when editing the filter
na.modals.filters.error.delete = Error when deleting the filter
na.modals.filters.error.missingstartdate = The Initial Date / Time field is required.
na.modals.filters.error.missingenddate = The End Date / Time field is required.
na.modals.filters.error.missingfiltername = The Filter Name field is required.
na.modals.filters.error.missingselectedfilter = You must choose a filter to edit.
na.modals.filters.error.missing.selectfilter.delete = You must choose a filter to delete.
na.modals.filters.success.save = Filter successfully saved
na.modals.filters.success.save.message = Your filter {0} has been successfully saved.
na.modals.filters.success.edit = Filter successfully edited
na.modals.filters.success.edit.message = Your filter {0} has been successfully edited.
na.modals.filters.success.delete = Filter successfully deleted
na.modals.filters.success.delete.message = Your filter {0} has been successfully deleted.
na.modals.filters.options = Filter options
na.modals.filters.select.label = Select filter:

################### Module Names
na.portal.module.mpt = MPT
na.portal.module.go = GO
na.portal.module.mpt.admin = MPT administration

##################DOUBLE BOX
doublebox.addAll = Move all >>
doublebox.addSelected = Move selected >
doublebox.removeAll = Move all <<
doublebox.removeSelected = Move selected <

##################DATE FORMATS
na.date.time.format.utc = yyyy-MM-dd'T'HH:mm:ss.SSSX
na.date.time.format.short = MM/dd/yyyy HH:mm
na.date.time.format.seconds = MM/dd/yyyy HH:mm:ss
na.date.time.format.milliseconds = MM/dd/yyyy HH:mm:ss.SSS
na.date.format.short = MM/dd/yyyy
na.time.format.short = HH:mm

na.wizard.buttons.before = Before
na.wizard.buttons.next = Next
na.wizard.buttons.finalize = Finish
na.wizard.buttons.cancel = Cancel
na.wizard.step = Step {0}

na.type = Type
na.values = Values

################# RESUMES
na.information.title = Information
na.operationalview.accessmodule = Access module

################## VALIDATION ERRORS
na.field.x.required = Field {0} is mandatory.
na.fill.all.required.fields = Fill in all mandatory fields.
na.basemodule.validations.mandatory          = Mandatory field
na.basemodule.validations.invalid.pattern    = Invalid format
na.basemodule.validations.numeric            = Numeric field
na.basemodule.validations.integer            = Integer field
na.basemodule.validations.max.characters     = Maximum {0} characters
na.basemodule.validations.min.characters     = Minimum {0} characters

################## MultiSelect

input.nomatches = No results found

################## UPLOAD FILE FIELD
na.upload.field.no.file.selected = No files selected
na.upload.field.browse = Browse