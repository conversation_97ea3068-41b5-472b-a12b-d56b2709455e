################ MPT MODULE ###############
na.portal.mpt.module.title = MPT
na.portal.mpt.module.description = Le module de Mass Provisions permet la définition et l''execution des mass opérations sur le réseau.<br>L''opérateur envoi une liste de fichiers avec les opérations à exécuter, le MPT est responsable de la gestion et de son exécution.<br>Les exécutions prévues sont autorisées ainsi comme le contrôle de chaque exécution.
na.portal.mpt.mega.menu.title = Opérations
na.portal.mpt.mega.menu.navinitial = Aller sur la page initiale
na.portal.mpt.mega.menu.mptadmin = Administration MPT

na.portal.mpt.home.label.title = MPT
na.portal.mpt.home.label.subtitle = Mass Provisions
na.portal.mpt.home.mainnav.mptadmin = Administration MPT
na.portal.mpt.home.mainnav.goadmin = Administration GO

###########GENERAL###########
na.portal.mpt.selects.states.empty.option = Sans État
na.portal.mpt.selects.states.option.allstates = Tout les États
na.portal.mpt.selects.option.allfamilies = Toutes les Familles
na.portal.mpt.messages.informative.mandatoryfields = Champs obligatoires
na.portal.mpt.weekdays.monday = LUN
na.portal.mpt.weekdays.tuesday = MAR
na.portal.mpt.weekdays.wednesday = MER
na.portal.mpt.weekdays.thursday = JEU
na.portal.mpt.weekdays.friday = VEN
na.portal.mpt.weekdays.saturday = SAM
na.portal.mpt.weekdays.sunday = DIM
na.portal.mpt.weekdays.all = TOUS

###########BUTTONS###########
na.portal.mpt.buttons.createoperation = Crée opération
na.portal.mpt.buttons.suspend = Suspendre
na.portal.mpt.buttons.cancel = Annuler
na.portal.mpt.buttons.addfile = Ajouter Fichier

###########FIELDS###########
na.portal.mpt.field.modificationdate.label = Changé le
na.portal.mpt.field.startdate.label = Date/Heure Début
na.portal.mpt.field.enddate.label = Date/Heure Fin
na.portal.mpt.field.user.label = Utilisateur
na.portal.mpt.field.user.placeholder = Utilisateur
na.portal.mpt.field.family.label = Famille
na.portal.mpt.field.family.placeholder = Famille
na.portal.mpt.field.operation.label = Opération
na.portal.mpt.field.operation.placeholder = Opération
na.portal.mpt.field.file.label = Fichier
na.portal.mpt.field.file.placeholder = Fichier
na.portal.mpt.field.state.label = État
na.portal.mpt.field.filtername.label = Nom du Filtre
na.portal.mpt.field.filtername.placeholder = Nom du Filtre

###########TABLES###########
na.portal.mpt.datatables.column.label.insertionDate = Date/Heure
na.portal.mpt.datatables.column.label.user = Utilisateur
na.portal.mpt.datatables.column.label.family = Famille
na.portal.mpt.datatables.column.label.operation = Opération
na.portal.mpt.datatables.column.label.file = Fichier
na.portal.mpt.datatables.column.label.state = État
na.portal.mpt.datatables.column.label.executionstate = Exécution
na.portal.mpt.datatables.column.label.progress = % Exécution
na.portal.mpt.datatables.column.label.status = % OK
na.portal.mpt.datatables.column.label.actions = Actions
na.portal.mpt.datatables.column.label.actions.play = Commencer
na.portal.mpt.datatables.column.label.actions.pause = Pause
na.portal.mpt.datatables.column.label.actions.stop = Arrêter
na.portal.mpt.datatables.column.label.actions.edit = Éditer
na.portal.mpt.datatables.column.label.actions.delete = Effacer
na.portal.mpt.datatables.column.label.actions.details = Voir détails

###########ADMIN SUSPEND##############
na.portal.mpt.suspend.admin.title = Confirmation
na.portal.mpt.suspend.admin.question = Voulez-vous suspendre administrativement le MPT?
na.portal.mpt.suspend.admin.info = Continuer avec la suspension peut entraîner le non-achèvement de l''opération en permanence.

###########CREATE OPERATION###########
na.portal.mpt.createoperation.label.sending = En envoyer
na.portal.mpt.createoperation.label.validating = En validation
na.portal.mpt.createoperation.label.waiting.server = En attente de la confirmation du serveur.
na.portal.mpt.createoperation.label.title = Créer Opération massive
na.portal.mpt.createoperation.tab.label = Créer Opération
na.portal.mpt.createoperation.breadcrumb.label = Créer Opération
na.portal.mpt.createoperation.label.characteristics = Caractéristiques
na.portal.mpt.createoperation.label.files = Fichiers
na.portal.mpt.createoperation.label.schedule = Planning
na.portal.mpt.createoperation.label.provisiontype = Type de Provision Massive
na.portal.mpt.createoperation.label.operation.identification = Données qui permettent l''identification de l''opération
na.portal.mpt.createoperation.label.family = Famille
na.portal.mpt.createoperation.label.operation = Opération
na.portal.mpt.createoperation.label.description = Description
na.portal.mpt.createoperation.label.selectfiles = Sélectionnez les Fichiers
na.portal.mpt.createoperation.label.files.limit = Vous pouvez ajouté jusqu''à {0} Fichiers, juqu''au maximum de {1}MB par Fichier.
na.portal.mpt.createoperation.label.files.information = Les fichiers seront indépendamment gérés par priorité et créeront une opération distincte.
na.portal.mpt.createoperation.label.schedule.information = Identifiction du Planning
na.portal.mpt.createoperation.label.schedule.immediatly = Immédiatement
na.portal.mpt.createoperation.label.schedule.later = Plus tard
na.portal.mpt.createoperation.label.schedule.scheduling = Sur planning
na.portal.mpt.createoperation.messages.operationsubmitted = Opération créer avec succès!
na.portal.mpt.createoperation.messages.operationerror = Une erreur s''est produite pendant la création de l''opération!

############### DATATABLES LABELS ####################
datatables.action.label.addschedule = Ajouter planning
na.portal.mpt.datatables.column.label.startDate = Date Début
na.portal.mpt.datatables.column.label.origin = Origine
na.portal.mpt.datatables.column.label.description = Description
na.portal.mpt.datatables.column.label.endDate = Date de Fin
na.portal.mpt.datatables.column.label.startHour = Heure Début
na.portal.mpt.datatables.column.label.endHour = Heure Fin
na.portal.mpt.datatables.column.label.duration = Durée
na.portal.mpt.datatables.column.label.weekdays = Fréquence
na.portal.mpt.datatables.column.label.username = Utilisateur
na.portal.mpt.datatables.column.label.state = État
na.portal.mpt.datatables.column.label.executionstate = Exécution
na.portal.mpt.datatables.column.label.opk = OPK
na.portal.mpt.datatables.column.label.key = Clé
na.portal.mpt.datatables.column.label.parameter = Paramètre
na.portal.mpt.datatables.column.label.value = Valeur

###########EDIT BULK###########
na.portal.mpt.editbulk.label.title = Editer Opération massive
na.portal.mpt.editbulk.tab.label = Editer Opération
na.portal.mpt.editbulk.breadcrumb.label = Éditer Opération
na.portal.mpt.editbulk.label.characteristics = Caractéristiques
na.portal.mpt.editbulk.label.files = Fichiers
na.portal.mpt.editbulk.label.schedule = Planning
na.portal.mpt.editbulk.label.provisiontype = Type de Provision Massive
na.portal.mpt.editbulk.label.operation.identification = Données qui permettent l''identification du Bulk
na.portal.mpt.editbulk.label.family = Famille
na.portal.mpt.editbulk.label.operation = Opération
na.portal.mpt.editbulk.label.description = Description
na.portal.mpt.editbulk.label.existingfiles = Fichiers existants
na.portal.mpt.editbulk.label.files = Fichiers

###########MPT ERROR MESSAGES###########
na.portal.mpt.createoperation.errors.title = Erreur pendant la création de l''Opération.
na.portal.mpt.createoperation.errors.missingfields.title = Champs obligatoire
na.portal.mpt.createoperation.errors.missingfields.message = Remplir tous les champs obligatoire.
na.portal.mpt.createoperation.errors.invalidfield.title = Champs non valides
na.portal.mpt.createoperation.errors.invalidfield.message = Corriger les champs marqués
na.portal.mpt.createoperation.errors.baddates.title = Erreur sur les dates saisies.
na.portal.mpt.createoperation.errors.baddates.intervals.title = Erreur aux intervalles définis.
na.portal.mpt.createoperation.errors.baddates.startDate.past = La date de début ne peut pas être dans le passé.
na.portal.mpt.createoperation.errors.baddates.startDate.superior = La date de Début ne peut pas être supérieur à celle de fin.
na.portal.mpt.createoperation.errors.baddates.schedules.overlapping = Les dates prévues sur le planning sont en conflit avec les dates déjà définies dans les plannings précédents.
na.portal.mpt.createoperation.errors.invalidfiles.title = Erreur dans le Fichier sélectionné.
na.portal.mpt.createoperation.errors.invalidfiles.message = La taille du Fichier {0} ({1} MB) est trop élevée. Veuillez sélectionner un autre fichier ou corriger son contenu.
na.portal.mpt.createoperation.errors.missingfiles.title = Fichiers manquants.
na.portal.mpt.createoperation.errors.missingfiles.message = Vous devez soumettre au moins un fichier pour créer une Opération.
na.portal.mpt.createoperation.errors.missingfields.startDate = La date de début de la planification est obligatoire.
na.portal.mpt.createoperation.errors.missingfields.endDate = La date de fin de la planification est obligatoire.
na.portal.mpt.createoperation.errors.missingfields.intervals = Vous devez définir  au minimum un intervalle.
na.portal.mpt.createoperation.errors.missingfields.intervals.badsyntax = Il y a des intervalles avec le mauvais format.
na.portal.mpt.createoperation.errors.missingfields.intervals.missing.startDate = Le champ Début est obligatoire dans les intervalles.
na.portal.mpt.createoperation.errors.missingfields.intervals.missing.endDate = Le champ Fin est obligatoire dans les intervalles.
na.portal.mpt.createoperation.errors.missingfields.intervals.missing.weekdays = La période de programmation est obligatoire.
na.portal.mpt.createoperation.errors.intervals.overlapping.intervals = Contient des intervalles qui se chevauchent, veuillez corriger cette information.
na.portal.mpt.createoperation.errors.intervals.expired = Contient des intervalles qui sont terminés, veuillez corriger cette information
na.portal.mpt.createoperation.errors.schedule.expired = L''horaire est terminé.
na.portal.mpt.createoperation.errors.intervals.repeated.intervals = Contient des intervalles répétés, veuillez corriger l''information.
na.portal.mpt.createoperation.errors.globalintervals.repeated.intervals = Contient des intervalles qui se répetent avec d''autre déjà définis pour la même date, veuillez corriger cette information.
na.portal.mpt.createoperation.errors.invalidfiles.wrongtype = Actuellement l''application supporte uniquement des Fichiers CSV.
na.portal.mpt.createoperation.errors.intervals.exceeds.limit = Aux dates souhaitées il existe déjà un horaire défini. Le numéro d''intervalle définis plus ceux qui existent déjà dépasse la limite définie. Effacé quelques intervalles afin de ne pas depasser la limite.
na.portal.mpt.createopeartion.errors.invalidafiles.exceeds.linelimit = Le Fichier {0} contient plus de lignes que ce qui est permis.
na.portal.mpt.createoperation.errors.schedule.missing.message = Le planning est obligatoire.
na.portal.mpt.createoperation.errors.files.sending.failed.title = Erreur pendant l''envoi des Fichiers
na.portal.mpt.createoperation.errors.files.sending.failed.message = Une erreur s''est produite lors de l''envoi des Fichiers suivants {0}. Suite à ce problème les respectives opérations n''ont pas été crées. Veuillez contacter l''administrateur du système et essayer plus tard.
na.portal.mpt.loaddetails.tab.content.error.title = Erreur dans l''Opération
na.portal.mpt.loaddetails.tab.content.error.description = Une erreur s''est produite lors de l''obtention des détails prétendus. Veuillez essayer plus tard.

na.portal.mpt.bulks.form.invalid.fields.title = Erreurs dans le formulaire.
na.portal.mpt.bulks.form.startdate.invalid.syntax = Le format du champ Date / Heure du Début n''est pas correcte.
na.portal.mpt.bulks.form.enddate.invalid.syntax = Le format du champ Date / Heure du Fin n''est pas correcte.
na.portal.mpt.bulks.form.invalid.dates.startdate.after.enddate = La Date / Heure du Début ne peut pas être supérieur à la Date / Heure de Fin.
na.portal.mpt.bulks.form.operation.invalid.syntax = Le format du champ d''Opération n''est pas correcte.
na.portal.mpt.bulks.form.username.invalid.syntax = Le format du champ Utilisateur n''est pas correct.
na.portal.mpt.bulks.form.filename.invalid.syntax = Le format du champ du Fichier n''est pas correct.
na.portal.mpt.bulks.filter.form.missing.filtername = Le champ Nom du Filtre est obligatoire.
na.portal.mpt.bulks.filter.form.empty = Il est nécessaire de remplir au minimum um champ pour pouvoir garder le filtre.

na.portal.mpt.editbulk.errors.missingfields.title = Champs obligatoires
na.portal.mpt.editbulk.errors.title.missingfields.message = Veuillez remplir tout les champs obligatoire.
na.portal.mpt.editbulk.errors.notupdatable = Le volume sélectionné n''est pas dans un État où la modification est possible. Veuillez contacter l''administrateur du système.
################ MODALS ################
na.portal.mpt.createoperation.modals.scheduling.title = Planning
na.portal.mpt.createoperation.modals.scheduling.subtitle = Configurez la plannification que vous souhaitez associer à un approvisionnement massif.
na.portal.mpt.createoperation.modals.scheduling.startDate = Début
na.portal.mpt.createoperation.modals.scheduling.endDate = Fin
na.portal.mpt.createoperation.modals.scheduling.endBy = Terminer par
na.portal.mpt.createoperation.modals.scheduling.noLimit = Sans limite
na.portal.mpt.createoperation.modals.scheduling.noLimit.starthourinfo = Choisissez l''heure de début et la durée de votre planning
na.portal.mpt.createoperation.modals.scheduling.startHour = Début
na.portal.mpt.createoperation.modals.scheduling.period = Choisissez la périodicité
na.portal.mpt.createoperation.modals.scheduling.interval.startHour = Début
na.portal.mpt.createoperation.modals.scheduling.interval.duration = Durée
na.portal.mpt.createoperation.modals.scheduling.interval.hours = heures
na.portal.mpt.createoperation.modals.scheduling.interval.minutes = minutes
na.portal.mpt.createoperation.modals.addSchedule = Ajouter Planning
na.portal.mpt.createoperation.modals.chooseStartTimeAndDuration = Choisissez l''heure de début de votre horaire et la durée:
na.portal.mpt.editoperation.label.schedule.information = Identification du planning

################ BULKS ################
na.portal.mpt.bulks.states.validating = En validation
na.portal.mpt.bulks.states.pending = En attente
na.portal.mpt.bulks.states.waiting = En attente
na.portal.mpt.bulks.states.executing = En cours d''éxécution
na.portal.mpt.bulks.states.suspending = En suspension
na.portal.mpt.bulks.states.suspended = Suspendu
na.portal.mpt.bulks.states.aborting = En interruption
na.portal.mpt.bulks.states.rejected = Rejeté
na.portal.mpt.bulks.states.terminated = Terminé
na.portal.mpt.bulks.states.scheduled = Prévu pour
na.portal.mpt.bulks.states.expired = Expiré
na.portal.mpt.bulks.states.suspended_administratively =  Administrativement suspendu
na.portal.mpt.bulks.states.aborted = interrompu
na.portal.mpt.bulks.states.undefined = Inconnu
na.portal.mpt.bulks.states.error = Erreur

na.portal.mpt.bulks.order.status.ok = OK
na.portal.mpt.bulks.order.status.nok = NOK
na.portal.mpt.bulks.order.status.rejected = Rejeté

na.portal.mpt.bulks.execution.states.not_started = N''a pas été commencé
na.portal.mpt.bulks.execution.states.started = Commencé
na.portal.mpt.bulks.execution.states.terminated = Terminé
na.portal.mpt.bulks.execution.states.error = Erreur

na.portal.mpt.bulks.actions.play.loading = A Commencé
na.portal.mpt.bulks.actions.pause.loading = En suspension
na.portal.mpt.bulks.actions.stop.loading = En Arrêt

################ BULK DETAILS ################
na.portal.mpt.bulk.details.characteristics = Caractéristiques
na.portal.mpt.bulk.details.results = Résultats
na.portal.mpt.bulk.details.characteristics.description = Description
na.portal.mpt.bulk.details.characteristics.schedule = Planning
na.portal.mpt.bulk.details.characteristics.transitions = Transition de l''État
na.portal.mpt.bulk.details.characteristics.description.filename = Nom du Fichier
na.portal.mpt.bulk.details.characteristics.description.familyname = Famille
na.portal.mpt.bulk.details.characteristics.description.operationname = Opération
na.portal.mpt.bulk.details.characteristics.description.description = Description
na.portal.mpt.bulks.details.inputfile=Fichier d''entrée
na.portal.mpt.bulks.details.outputfile=Fichier de sortie


############### RESULTS ###############
na.portal.mpt.results.fields.label.startDate = Date / Heure de Début
na.portal.mpt.results.fields.label.endDate = Date / Heure de Fin
na.portal.mpt.results.fields.label.opk = OPK
na.portal.mpt.results.fields.label.key = Clé
na.portal.mpt.results.fields.label.state = État
na.portal.mpt.results.fields.label.execution.state = Exécution

na.portal.mpt.results.form.invalid.fields.title = Erreur dans le formulaire.
na.portal.mpt.results.form.starttime.invalid.syntax = Le format du champ Date / Heure de Début n''est pas correct.
na.portal.mpt.results.form.endtime.invalid.syntax = Le format du champ Date / Heure de Fin n''est pas correct
na.portal.mpt.results.form.invalid.dates.starttime.after.endtime = La Date / Heure de Début ne peut pas être supérieur à la Date / Heure de Fin.
na.portal.mpt.results.filter.form.missing.filtername = Le champ Nom du Filtre est obligatoire.
na.portal.mpt.results.filter.form.empty = Il est nécéssaire de remplir au minimum um champ pour garder le filtre.

############## RESULT DETAILS ################
na.portal.mpt.result.details.title.opk = OPK
na.portal.mpt.result.details.subtitle.operation = Opération:
na.portal.mpt.result.details.subtitle.rfs = RFS:
na.portal.mpt.result.details.characteristics = Caractéristiques
na.portal.mpt.result.details.characteristics.description = Description
na.portal.mpt.result.details.characteristics.description.filename = Nom du Fichier
na.portal.mpt.result.details.characteristics.description.familyname = Famille
na.portal.mpt.result.details.characteristics.description.operationname = Opération
na.portal.mpt.result.details.characteristics.description.requestdate = Date de la demande
na.portal.mpt.result.details.characteristics.description.testduration = Durée du Test
na.portal.mpt.result.details.characteristics.description.username = Utilisateur
na.portal.mpt.result.details.characteristics.state = État
na.portal.mpt.result.details.characteristics.state.starttime = Date de Début
na.portal.mpt.result.details.characteristics.state.endtime = Date de Fin
na.portal.mpt.result.details.characteristics.state.status = État
na.portal.mpt.result.details.characteristics.state.description = Description
na.portal.mpt.result.details.characteristics.payload = Contenu
na.portal.mpt.result.details.label.request = Demande
na.portal.mpt.result.details.label.response = Réponse
na.portal.mpt.result.details.characteristics.description.lineNum = Nº de la ligne

############# ERROR TITLES #############
exception.NA_MPT_00001.title = Erreur pendant la recherche
exception.NA_MPT_00002.title = Erreur pendant la recherche
exception.NA_MPT_00003.title = Erreur pendant le téléchargement
exception.NA_MPT_00004.title = Erreur pendant la recherche
exception.NA_MPT_00005.title = Erreur pendant la recherche
exception.NA_MPT_00006.title = Erreur pendant la création
exception.NA_MPT_00007.title = Erreur pendant l'effacement
exception.NA_MPT_00008.title = État invalide
exception.NA_MPT_00009.title = Erreur pendant l'initialisation
exception.NA_MPT_00010.title = Erreur pendant la pause
exception.NA_MPT_00011.title = Erreur pendant l'arrêt
exception.NA_MPT_00012.title = Erreur pendant la suspension