na.portal.monitoring.module.title                                               = Surveillance
na.portal.monitoring.module.description                                         = Le module de surveillance fournit un ensemble d'outils permettant à l'utilisateur de surveiller l'exécution des Service Orders reçus par le système. Il permet la recherche basée sur un ensemble de critères prédéfinis, d'exécuter des actions sur les Service Orders qui ne sont pas dans un état final et également de renvoyer des notifications associées aux transitions d'état ou qui existent dans les workflows. Il permet également la création de nouveaux Service Orders basés sur les opérations et entités créées sur le module Catalogue.

na.portal.monitoring.nav.context                                                = Surveillance

na.portal.monitoring.home.title                                                 = Surveillance
na.portal.monitoring.home.description                                           = Module de Surveillance

# filters

na.portal.monitoring.home.filters.search                                        = Recherche
na.portal.monitoring.home.filters.search.by.id                                  = recherche par ID
na.portal.monitoring.home.filters.search.with.multiple.fields                   = recherche avec plusieurs champs
na.portal.monitoring.home.filters.id.search.orderId                             = ID de l''ordre
na.portal.monitoring.home.filters.id.search.serviceId                           = ID du service
na.portal.monitoring.home.filters.id.search.input.placeholder.orderId           = Écrivez l''ID de l''ordre que vous voulez vérifier
na.portal.monitoring.home.filters.id.search.input.placeholder.serviceId         = Écrivez l''ID du service que vous voulez vérifier

na.portal.monitoring.home.filters.add.fields                                    = ajouter champs
na.portal.monitoring.home.filters.need.to.refresh                               = a besoin de recharger pour mettre à jour les dernières 24 heures
na.portal.monitoring.home.filters.more                                          = plus
na.portal.monitoring.home.filters.less                                          = moins
na.portal.monitoring.home.filters.all                                           = tous

na.portal.monitoring.home.filters.items.state                                   = État
na.portal.monitoring.home.filters.items.category                                = Catégorie
na.portal.monitoring.home.filters.items.system                                  = Système
na.portal.monitoring.home.filters.items.operation                               = Opération
na.portal.monitoring.home.filters.items.service.type                            = Type de service
na.portal.monitoring.home.filters.items.response.code                           = Code de réponse
na.portal.monitoring.home.filters.items.response.message                        = Message de réponse

na.portal.monitoring.home.filters.state.validating                              = validating
na.portal.monitoring.home.filters.state.acknowledged                            = acknowledged
na.portal.monitoring.home.filters.state.canceled                                = cancelled
na.portal.monitoring.home.filters.state.held                                    = held
na.portal.monitoring.home.filters.state.in.progress                             = in progress
na.portal.monitoring.home.filters.state.partial                                 = partial
na.portal.monitoring.home.filters.state.pending                                 = pending
na.portal.monitoring.home.filters.state.rejected                                = rejected
na.portal.monitoring.home.filters.state.completed                               = completed
na.portal.monitoring.home.filters.state.failed                                  = failed

na.portal.monitoring.home.filters.system.na                                     = Network Activator
na.portal.monitoring.home.filters.system.om                                     = Order Manager

# date range picker

na.portal.monitoring.datarangepicker.message.refresh                            = A besoin de recharger pour mettre à jour {0}
na.portal.monitoring.datarangepicker.message.LAST_30_MINUTES                    = les dernières 30 minutes
na.portal.monitoring.datarangepicker.message.LAST_HOUR                          = la dernière heure
na.portal.monitoring.datarangepicker.message.LAST_12_HOURS                      = les dernières 12 heures
na.portal.monitoring.datarangepicker.message.LAST_24_HOURS                      = les dernières 24 heures
na.portal.monitoring.datarangepicker.message.LAST_WEEK                          = la dernière semaine
na.portal.monitoring.datarangepicker.message.LAST_MONTH                         = le dernier mois

na.portal.monitoring.datarangepicker.LAST_30_MINUTES                            = Dernières 30 minutes
na.portal.monitoring.datarangepicker.LAST_HOUR                                  = Dernière heure
na.portal.monitoring.datarangepicker.LAST_12_HOURS                              = Dernières 12 heures
na.portal.monitoring.datarangepicker.LAST_24_HOURS                              = Dernières 24 heures
na.portal.monitoring.datarangepicker.LAST_WEEK                                  = Dernière semaine
na.portal.monitoring.datarangepicker.LAST_MONTH                                 = Dernier mois
na.portal.monitoring.datarangepicker.CUSTOM_RANGE                               = Autre intervalle

na.portal.monitoring.datarangepicker.dateformat                                 = DD-MM-YYYY HH:mm:ss
na.portal.monitoring.datarangepicker.applyButton                                = appliquer
na.portal.monitoring.datarangepicker.cancelButton                               = annuler

na.portal.monitoring.datarangepicker.sunday                                     = Dim
na.portal.monitoring.datarangepicker.monday                                     = Lun
na.portal.monitoring.datarangepicker.tuesday                                    = Mar
na.portal.monitoring.datarangepicker.wednesday                                  = Mer
na.portal.monitoring.datarangepicker.thursday                                   = Jeu
na.portal.monitoring.datarangepicker.friday                                     = Ven
na.portal.monitoring.datarangepicker.saturday                                   = Sam

na.portal.monitoring.datarangepicker.january                                    = Janvier
na.portal.monitoring.datarangepicker.february                                   = Février
na.portal.monitoring.datarangepicker.march                                      = Mars
na.portal.monitoring.datarangepicker.april                                      = Avril
na.portal.monitoring.datarangepicker.may                                        = Mai
na.portal.monitoring.datarangepicker.june                                       = Juin
na.portal.monitoring.datarangepicker.july                                       = Juillet
na.portal.monitoring.datarangepicker.august                                     = Août
na.portal.monitoring.datarangepicker.september                                  = Septembre
na.portal.monitoring.datarangepicker.october                                    = Octobre
na.portal.monitoring.datarangepicker.november                                   = Novembre
na.portal.monitoring.datarangepicker.december                                   = Décembre

# orders table

na.portal.monitoring.orders.table.order.id                                      = ID de l''ordre
na.portal.monitoring.orders.table.category                                      = Catégories
na.portal.monitoring.orders.table.description                                   = Description
na.portal.monitoring.orders.table.state                                         = État
na.portal.monitoring.orders.table.customer                                      = Client
na.portal.monitoring.orders.table.technician                                    = Technicien
na.portal.monitoring.orders.table.system                                        = Système
na.portal.monitoring.orders.table.system.entry                                  = Entrée dans le système
na.portal.monitoring.orders.table.update.last                                   = Dernière mise à jour
na.portal.monitoring.orders.table.actions                                       = Actions
na.portal.monitoring.orders.table.actions.options                               = options
na.portal.monitoring.orders.table.error.message.single.item                     = {0} dans l''item de l''ordre {1} de {2}
na.portal.monitoring.orders.table.error.message.multiple.items                  = <b> Il y a {0} items de l''ordre qui ont échoué</b>. Ouvrez la ligne pour voir ce qui s''est passé.
na.portal.monitoring.orders.table.info.message.single.item                      = {0} dans l''item de l''ordre {1} de {2}
na.portal.monitoring.orders.table.info.message.multiple.items                   = <b> Il y a {0} items de l''ordre qui ont des messages</b>. Ouvrez la ligne pour voir ce qui s''est passé.
na.portal.monitoring.orders.table.info.message.validating.order                 = Ordre est en cours de validation

# orders item table

na.portal.monitoring.order.items.table.operation.service                        = Opération / Service
na.portal.monitoring.order.items.table.description                              = Description
na.portal.monitoring.order.items.table.state                                    = État
na.portal.monitoring.order.items.table.has.process                              = Avec procès?
na.portal.monitoring.order.items.table.service.id                               = ID du service
na.portal.monitoring.order.items.table.actions                                  = Actions

# order item resume

na.portal.monitoring.order.item.resume.table.operation.category                 = Opération / Catégorie
na.portal.monitoring.order.item.resume.table.service.id                         = ID du Service
na.portal.monitoring.order.item.resume.table.state                              = État
na.portal.monitoring.order.item.resume.table.duration                           = Durée
na.portal.monitoring.order.item.resume.table.end.date                           = Dernière mise à jour
na.portal.monitoring.order.item.resume.table.has.process                        = Avec procès
na.portal.monitoring.order.item.resume.table.actions                            = Actions

# orders state

na.portal.monitoring.order.state.sent                                           = envoyé
na.portal.monitoring.order.state.failed                                         = échoué

# orders response retry table

na.portal.monitoring.order.response.retry.table.retry.number                    = #
na.portal.monitoring.order.response.retry.table.date.time                       = Date / heure
na.portal.monitoring.order.response.retry.table.state                           = État
na.portal.monitoring.order.response.retry.table.state.sent                      = envoyé
na.portal.monitoring.order.response.retry.table.state.failed                    = échoué
na.portal.monitoring.order.response.retry.table.message                         = Message

# orders response page

na.portal.monitoring.order.response.label.response                              = Réponse
na.portal.monitoring.order.response.label.destination                           = Pour
na.portal.monitoring.order.response.label.orderHistoryCheck                     = Vous pouvez vérifier l''historique <b>histórico</b>.
na.portal.monitoring.order.response.label.responseSuccess                       = Réponse envoyé avec succès.
na.portal.monitoring.order.response.label.delivering                            = En train d''envoyer la réponse.
na.portal.monitoring.order.response.label.responseFailed                        = Réponse échoué. Erreur {0}.
na.portal.monitoring.order.response.tooltip.seeTable                            = Voir dans le tableau
na.portal.monitoring.order.response.tooltip.seeCode                             = Voir em code
na.portal.monitoring.order.activity.response.label.responseFailed               = Réponse échoué. Erreur <b>{0}</b>.
na.portal.monitoring.order.activity.response.label.inProgress                   = Réponse en cours.
na.portal.monitoring.order.activity.response.label.success                      = Réponse envoyé avec succès.

# orders notification details table

na.portal.monitoring.order.notification.details.table.errorIssues               = {0} sur {1} avait des problèmes d''erreur.
na.portal.monitoring.order.notification.details.table.system                    = Système
na.portal.monitoring.order.notification.details.table.systems                   = Systèmes
na.portal.monitoring.order.notification.details.table.attempt                   = Attempt
na.portal.monitoring.order.notification.details.table.attempts                  = Attempts
na.portal.monitoring.order.notification.details.table.dateTime                  = Date
na.portal.monitoring.order.notification.details.table.type                      = Taper
na.portal.monitoring.order.notification.details.table.summary                   = Sommaire
na.portal.monitoring.order.notification.details.table.state                     = Etat
na.portal.monitoring.order.notification.details.table.logs_history              = Historique des Journaux
na.portal.monitoring.order.notification.details.table.state.success             = Succès
na.portal.monitoring.order.notification.details.table.state.error               = Erreur

# orders validation page

na.portal.monitoring.order.validation.response.label.response                   = Modèle d''information
na.portal.monitoring.order.validation.messages.table.code                       = Code
na.portal.monitoring.order.validation.messages.table.message                    = Message
na.portal.monitoring.order.validation.messages.table.description                = Desciption
na.portal.monitoring.order.validation.messages.global                           = <b>{0} ({1} - {2}).</b> Pour plus de détails voir ci-dessous.

# orders

na.portal.monitoring.order.from.system                                          = par
na.portal.monitoring.order.status.validation                                    = Validation
na.portal.monitoring.order.status.execution                                     = Exécution
na.portal.monitoring.order.status.response                                      = Réponse
na.portal.monitoring.order.status.notifications                                 = Notifications

na.portal.monitoring.order.date.time.second                                     = seconde
na.portal.monitoring.order.date.time.seconds                                    = secondes
na.portal.monitoring.order.date.time.minute                                     = minute
na.portal.monitoring.order.date.time.minutes                                    = minutes
na.portal.monitoring.order.date.time.hour                                       = heure
na.portal.monitoring.order.date.time.hours                                      = heures
na.portal.monitoring.order.date.time.day                                        = jour
na.portal.monitoring.order.date.time.days                                       = jours
na.portal.monitoring.order.date.time.week                                       = semaine
na.portal.monitoring.order.date.time.weeks                                      = semaines
na.portal.monitoring.order.date.time.month                                      = il y a un mois
na.portal.monitoring.order.date.time.months                                     = mois

na.portal.monitoring.order.date.day.today                                       = aujourd''hui
na.portal.monitoring.order.date.day.yesterday                                   = hier
na.portal.monitoring.order.date.weekday.0                                       = dimanche
na.portal.monitoring.order.date.weekday.1                                       = lundi
na.portal.monitoring.order.date.weekday.2                                       = mardi
na.portal.monitoring.order.date.weekday.3                                       = mercredi
na.portal.monitoring.order.date.weekday.4                                       = jeudi
na.portal.monitoring.order.date.weekday.5                                       = vendredi
na.portal.monitoring.order.date.weekday.6                                       = samedi

na.portal.monitoring.order.date.format.full                                     = {0} a {1}h
na.portal.monitoring.order.date.format.short                                    = il y a {0} {1}
na.portal.monitoring.order.date.format.duration                                 = {0} {1}
na.portal.monitoring.order.date.format.relativeTime                             = {0}, en {1} de {2}

# Order activity

na.portal.monitoring.order.tooltip.skip                                         = sauter
na.portal.monitoring.order.tooltip.rollback                                     = Retour en arriere
na.portal.monitoring.order.tooltip.retry                                        = répéter
na.portal.monitoring.order.tooltip.retry_new_request                            = répéter
na.portal.monitoring.order.tooltip.restart                                      = recommencer
na.portal.monitoring.order.tooltip.report                                       = rapport
na.portal.monitoring.order.tooltip.complete                                     = conclure

# Order activity external System interaction
na.portal.monitoring.order.ext.sys.interaction.origin                         = De
na.portal.monitoring.order.ext.sys.interaction.destination                    = Pour
na.portal.monitoring.order.ext.sys.interaction.date                           = Date
na.portal.monitoring.order.ext.sys.interaction.details                        = Détails
na.portal.monitoring.order.ext.sys.interaction.request                        = Demande
na.portal.monitoring.order.ext.sys.interaction.sync.response                  = Réponse synchrone
na.portal.monitoring.order.ext.sys.interaction.async.response                 = Réponse asynchrone

# modal

na.portal.monitoring.order.modal.button.complete                                = conclure
na.portal.monitoring.order.modal.button.retry                                   = Répéter
na.portal.monitoring.order.modal.button.skip                                    = Sauter
na.portal.monitoring.order.modal.button.rollback                                = Retour en arriere
na.portal.monitoring.order.modal.button.cancel                                  = Annuler
na.portal.monitoring.order.modal.button.confirm                                 = Confirmer
na.portal.monitoring.order.modal.button.restart                                 = Redémarrer
na.portal.monitoring.order.modal.label.skip                                     = Dites-nous pourquoi voulez-vous sauter cette activité
na.portal.monitoring.order.modal.label.rollback                                 = Dites-nous pourquoi voulez-vous retour en arriere cette activité
na.portal.monitoring.order.modal.label.retry                                    = Dites-nous pourquoi avez-vous répéter l''activité
na.portal.monitoring.order.modal.label.complete                                 = Dites-nous pourquoi avez-vous conclu l''activité
na.portal.monitoring.order.modal.label.restart                                  = Dites-nous pourquoi avez-vous recommencé
na.portal.monitoring.order.modal.label.finish                                   = Dites-nous pourquoi avez-vous terminé votre demande
na.portal.monitoring.order.modal.label.cancel                                   = Dites-nous pourquoi avez-vous annuler votre demande
na.portal.monitoring.order.modal.label.cancelWithRollback                       = Dites-nous pourquoi avez-vous annuler votre demande
na.portal.monitoring.order.modal.label.remove                                   = Dites-nous pourquoi avez-vous retirer votre demande
na.portal.monitoring.order.modal.title.skip                                     = Sauter {0}
na.portal.monitoring.order.modal.title.rollback                                 = Retour en arriere {0}
na.portal.monitoring.order.modal.title.retry                                    = Répéter {0}
na.portal.monitoring.order.modal.title.complete                                 = conclure {0}
na.portal.monitoring.order.modal.title.restart                                  = Recommencer {0}
na.portal.monitoring.order.modal.title.skip.individual							= Sauter {0}
na.portal.monitoring.order.modal.title.skip.massive								= Sauter ordrers
na.portal.monitoring.order.modal.title.rollback.individual					    = Retour en arriere {0}
na.portal.monitoring.order.modal.title.rollback.massive						    = Retour en arriere ordrers
na.portal.monitoring.order.modal.title.retry.individual							= Répéter {0}
na.portal.monitoring.order.modal.title.retry.massive							= Répéter ordrers
na.portal.monitoring.order.modal.title.cancel.individual                        = Annuler {0}
na.portal.monitoring.order.modal.title.cancel.massive                           = Annuler ordrers
na.portal.monitoring.order.modal.title.cancelWithRollback.individual            = Annuler et restaurer {0}
na.portal.monitoring.order.modal.title.cancelWithRollback.massive               = Annulation et restauration des commandes
na.portal.monitoring.order.modal.title.report                                   = Notes et actions sur {0}
na.portal.monitoring.order.modal.title.remove.individual                        = retirer {0}
na.portal.monitoring.order.modal.title.remove.massive                           = supprimer ordrers
na.portal.monitoring.order.modal.emptyNotes                                     = (sans notes)
na.portal.monitoring.order.modal.tab.responseDetail                             = Détail de la réponse
na.portal.monitoring.order.modal.tab.response                                   = Réponse
na.portal.monitoring.order.modal.tab.request                                    = Demande
na.portal.monitoring.order.modal.tab.history                                    = Historique

na.portal.monitoring.orders.modal.alert.partial.action.title                    = L''action sera partiellement appliquée à ces ordres
na.portal.monitoring.orders.modal.alert.partial.action.text                     = Certaines ordres ne seront pas traitées car leur état actuel est incompatible avec cette action.

na.portal.monitoring.notifications.modals.alert.confirm.retry.system.title            = Nouvelle notification
na.portal.monitoring.notifications.modals.alert.confirm.retry.system.content          = Voulez-vous vraiment notifier à nouveau ce système?
na.portal.monitoring.notifications.modals.alert.confirm.retry.all.systems.title       = Nouvelles notifications
na.portal.monitoring.notifications.modals.alert.confirm.retry.all.systems.content     = Voulez-vous vraiment notifier à nouveau tous les systèmes?
na.portal.monitoring.notifications.modals.details.error                               = Échec de la réponse.
na.portal.monitoring.notifications.modals.details.success                             = Réponse envoyée avec succès.

# external system interactions
na.portal.monitoring.ext.sys.interactions.table.datetime                        = Date / heure
na.portal.monitoring.ext.sys.interactions.table.description                     = Description
na.portal.monitoring.ext.sys.interactions.table.author                          = Faite par
na.portal.monitoring.ext.sys.interactions.table.actions                         = Actions
na.portal.monitoring.ext.sys.interactions.table.raw.data                        = Données brutes
na.portal.monitoring.ext.sys.interactions.details                               = Détails

na.portal.monitoring.ext.sys.interactions.filter.title                          = Vous pouvez filtrer les données par
na.portal.monitoring.ext.sys.interactions.filter.task.manual                    = Tâches manuelles
na.portal.monitoring.ext.sys.interactions.filter.task.automatic                 = Tâches automatiques
na.portal.monitoring.ext.sys.interactions.filter.notifications                  = Avis

na.portal.monitoring.ext.sys.tab.execution                                      = Exécution
na.portal.monitoring.ext.sys.tab.history                                        = Histoire

# splitter

na.portal.monitoring.splitter.tip.close                                         = Fermer
na.portal.monitoring.splitter.tip.open                                          = Ouvrir
na.portal.monitoring.splitter.tip.resize                                        = Redimensionner
na.portal.monitoring.splitter.tip.slide                                         = Diapositive ouvert

# notification

na.portal.monitoring.action.complete.success.title                              = Activité conclu avec succès
na.portal.monitoring.action.complete.success.content                            = Activité "{0}" conclu avec succès
na.portal.monitoring.action.success.title                                       = {0} avec succès
na.portal.monitoring.action.success.content                                     = Action de {0} éxecuté avec succès sur l''activité "{1}"
na.portal.monitoring.order.action.skip.success.title                            = Ordre avancée avec succès
na.portal.monitoring.order.action.skip.success.content                          = Action de sauter l''ordre {0} exécuté avec succès
na.portal.monitoring.order.action.rollback.success.title                        = Ordre retour en arriere avec succès
na.portal.monitoring.order.action.rollback.success.content                      = Action de retour en arriere l''ordre {0} exécuté avec succès
na.portal.monitoring.order.action.remove.success.title                          = Ordre supprimée avec succès
na.portal.monitoring.order.action.remove.success.content                        = Action de suppriméer l''ordre {0} exécuté avec succès
na.portal.monitoring.order.massive.action.skip.success.title                    = Ordres avancée avec succès
na.portal.monitoring.order.massive.action.skip.success.content                  = Action de sauter l''ordres exécuté avec succès
na.portal.monitoring.order.massive.action.skip.partial.title                    = Ordres partiellement ignorés
na.portal.monitoring.order.massive.action.skip.partial.content                  = Action de saut d''ordre effectuée partiellement
na.portal.monitoring.order.massive.action.rollback.success.title                = Ordres retour en arriere avec succès
na.portal.monitoring.order.massive.action.rollback.success.content              = Action de retour en arriere l''ordres exécuté avec succès
na.portal.monitoring.order.massive.action.rollback.partial.title                = Ordres partiellement annulés
na.portal.monitoring.order.massive.action.rollback.partial.content              = Action d''annulation d''ordre effectuée partiellement
na.portal.monitoring.order.action.retry.success.title                       	= Ordre retenté avec succès
na.portal.monitoring.order.action.retry.success.content                         = Action de retenter l''ordre {0} exécuté avec succès
na.portal.monitoring.order.massive.action.retry.success.title                   = Ordres retenté avec succès
na.portal.monitoring.order.massive.action.retry.success.content                 = Action de retenter l''ordres exécuté avec succès
na.portal.monitoring.order.massive.action.retry.partial.title                   = Ordres partiellement réessayés
na.portal.monitoring.order.massive.action.retry.partial.content                 = Action de réessai d''ordre effectuée partiellement
na.portal.monitoring.order.action.cancel.success.title                          = Ordre annulée avec succès
na.portal.monitoring.order.action.cancel.success.content                        = Action d''annulation de l''ordre {0} exécuté avec succès
na.portal.monitoring.order.massive.action.cancel.success.title                  = Ordres annulée avec succès
na.portal.monitoring.order.massive.action.cancel.success.content                = Action d''annulation de ordres exécuté avec succès
na.portal.monitoring.order.massive.action.cancel.partial.title                  = Ordres partiellement annulés
na.portal.monitoring.order.massive.action.cancel.partial.content                = Action d''annulation d''ordre effectuée partiellement
na.portal.monitoring.order.massive.action.remove.success.title                  = Ordres supprimées avec succès
na.portal.monitoring.order.massive.action.remove.success.content                = Action pour supprimer les ordres exécutés avec succès
na.portal.monitoring.order.massive.action.remove.partial.title                  = Ordres partiellement supprimés
na.portal.monitoring.order.massive.action.remove.partial.content                = Action de suppression d''ordre effectuée partiellement
na.portal.monitoring.order.massive.action.invalid.states.title                  = Action de Masse : États Invalides
na.portal.monitoring.order.massive.action.selected.invalid                      = La ordre sélectionnée est dans un état non valide pour cette action
na.portal.monitoring.order.massive.action.all.selected.invalid                  = Toutes les commandes sélectionnées sont dans un état non valide pour cette action
na.portal.monitoring.order.select.all.notification.title                        = Ordrers sélectionnées
na.portal.monitoring.order.massive.action.failed.orders                         = Action non appliquée à {0} ordre(s)
na.portal.monitoring.order.select.all.notification.content                      = {0} / {1} ordrers sélectionnées
na.portal.monitoring.order.select.all.notification.select.all                   = tout sélectionner
na.portal.monitoring.order.select.all.notification.unselect.all                 = désélectionner tout
na.portal.monitoring.order.view.timeline.graph.not.found.title                  = Graphe et chronologie introuvables
na.portal.monitoring.order.view.timeline.graph.not.found.message                = Le graphe et la chronologie ne sont pas encore créés, réessayez plus tard
na.portal.monitoring.order.view.timeline.graph.unexpected.error.title           = Une erreure s''est produite!
na.portal.monitoring.order.view.timeline.graph.unexpected.error.message         = Impossible de charger le graphe et la chronologie
na.portal.monitoring.order.systems.notification.retry.success.title             = Systèmes notifiés avec succès.
na.portal.monitoring.order.systems.notification.retry.success.content           = Tous les systèmes de notification "{0}" ont été notifiés avec succès.
na.portal.monitoring.order.system.notification.retry.success.title              = Système notifié avec succès.
na.portal.monitoring.order.system.notification.retry.success.content            = Système "{0}" notifié avec succès.

# timeline notification

na.portal.monitoring.timeline.activity.notification.system.retry               = Réessayer
na.portal.monitoring.timeline.activity.notification.system.retry.title         = Réessayer envoyé
na.portal.monitoring.timeline.activity.notification.system.retry.content       = Nouvelle tentative de notification {0} envoyée au système {1}.
na.portal.monitoring.timeline.activity.notification.resume                     = 1 système
na.portal.monitoring.timeline.activity.notification.resume.plural              = {0} systèmes
na.portal.monitoring.timeline.activity.notification.resume.missing              = Aucune information de notification disponible.
na.portal.monitoring.timeline.activity.notification.resume.issues              = {0} sur {1} ont rencontré des problèmes d''erreur.

# search date filter by

na.portal.monitoring.search.date.filterby                                       = filtrer par
na.portal.monitoring.search.date.filterby.systemEntry                           = entrée dans le système
na.portal.monitoring.search.date.filterby.lastUpdate                            = dernière mise à jour

# operations

na.portal.monitoring.button.bulkoperations.group                  = Sélectionnez l''action souhaitée
na.portal.monitoring.bulkoperations.button                        = opérations en vrac
na.portal.monitoring.order.possible.actions                       = actions possibles
na.portal.monitoring.order.action.cancel                          = annuler
na.portal.monitoring.order.action.complete                        = compléter
na.portal.monitoring.order.action.retry                           = retenter
na.portal.monitoring.order.action.skip                            = sauter
na.portal.monitoring.order.action.rollback                        = retour en arriere
na.portal.monitoring.order.action.pause                           = pause
na.portal.monitoring.order.action.cancelWithRollback              = annuler et retouer en arriere
na.portal.monitoring.order.action.remove                          = retirer
na.portal.monitoring.bulkoperations.tags.button                   = gérer les tags
na.portal.monitoring.bulkoperations.delete.button                 = retirer

# order operation attribute view
# this section comes from operations catalog ( na.portal.operationscatalog.* = na.portal.monitoring.operation.view.* )

na.portal.monitoring.operation.view.tab.characteristics                                         = Caractéristiques
na.portal.monitoring.operation.view.tab.section.general.characteristics                         = Caractérisation de base
na.portal.monitoring.operation.view.tab.section.input.attributes                                = Attributs d''entrée
na.portal.monitoring.operation.view.tab.section.execution.attributes                            = Attributs d''éxécution
na.portal.monitoring.operation.view.tab.section.output.attributes                               = Attributs de sortie

# this part comes from opercat dynamic form DB
na.portal.monitoring.operation.view.version  = Version
na.portal.monitoring.operation.view.tags  = Tags
na.portal.monitoring.operation.view.workflow  = Workflow

na.portal.monitoring.operation.view.table.column.label.attributes_entities = Attributs / Entités
na.portal.monitoring.operation.view.table.column.label.attributes = Attributs
na.portal.monitoring.operation.view.table.column.label.cardinality = Cardinalité
na.portal.monitoring.operation.view.table.column.label.validationRules = Règles de validation
na.portal.monitoring.operation.view.table.column.label.enrichmentRule = Règles d''enrichissement
na.portal.monitoring.operation.view.table.column.label.value = Valeur

# order create

na.portal.monitoring.order.create.definition                                = Définition
na.portal.monitoring.order.create.label                                     = Créer
na.portal.monitoring.order.create.button.label.execute                      = exécuter
na.portal.monitoring.order.create.sidebar.title                             = Définition
na.portal.monitoring.order.create.sidebar.system                            = Système
na.portal.monitoring.order.create.sidebar.user                              = Utilisateur
na.portal.monitoring.order.create.sidebar.operations                        = Opérations
na.portal.monitoring.order.create.sidebar.version                           = Version
na.portal.monitoring.order.create.sidebar.placeholder.missing.system        = Doit sélectionner un système
na.portal.monitoring.order.create.sidebar.placeholder.select.system         = Sélectionner un système
na.portal.monitoring.order.create.sidebar.placeholder.select.user           = Sélectionner un utilisateur
na.portal.monitoring.order.create.sidebar.placeholder.select.operations     = Sélectionnez les opérations souhaitées
na.portal.monitoring.order.create.notification.failure.message              = Veuillez vérifier les champs signalés
na.portal.monitoring.order.create.notification.failure.title                = Il n''est pas possible de garder le formulaire
na.portal.monitoring.order.create.button.optional.parameters.label          = ajouter des paramètres facultatifs

na.portal.monitoring.order.create.attribute.form.order.parameters.order.header.title                          = Ordre
na.portal.monitoring.order.create.attribute.form.operation.parameters.base.characteristics.header.title       = Caractéristiques de base
na.portal.monitoring.order.create.attribute.form.operation.parameters.specific.characteristics.header.title   = Caractéristiques particulières

na.portal.monitoring.order.create.attribute.form.related-party.attributes.header.title                  = Attributes
na.portal.monitoring.order.create.attribute.form.related-party.properties.input.key                     = Clé
na.portal.monitoring.order.create.attribute.form.related-party.properties.button.add                    = Ajouter
na.portal.monitoring.order.create.attribute.form.related-party.properties.input.key.error.missing       = Le champ "clé" est obligatoire
na.portal.monitoring.order.create.attribute.form.related-party.properties.input.key.error.duplicated    = La clé ne peut pas être dupliquée
na.portal.monitoring.order.create.attribute.form.related-party.properties.button.remove                 = Retirer

na.portal.monitoring.order.create.attribute.form.notification.order.required.title                                   = Doit sélectionner un système et une opération
na.portal.monitoring.order.create.attribute.form.notification.order.required.description                             = Veuillez confirmer que les champs sont remplis.
na.portal.monitoring.order.create.attribute.form.notification.order.invalid.version.state.title                      = État de version non valide
na.portal.monitoring.order.create.attribute.form.notification.order.invalid.version.state.description                = Veuillez sélectionner une version avec un État valide.
na.portal.monitoring.order.create.attribute.form.notification.order.no.attributes.description                        = Cette opération n'a pas d'attributs associés

na.portal.monitoring.order.create.attribute.form.parameter.tab.order     = paramètres de commande
na.portal.monitoring.order.create.attribute.form.parameter.tab.operation = paramètres de opération

na.portal.monitoring.order.create.attribute.form.button.submit.message   = Ordre soumise avec succès