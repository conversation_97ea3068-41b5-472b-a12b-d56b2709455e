veacs.serviceData.voiceProfile = Voice Profile
na.portal.veacs.module.title = VEACS
na.portal.veacs.module.description = VEACS stands for Voice Equipment ACS (Automatic Configuration Service) and it is a Device Management solution for SIP Phones.<br>VEACS allows an operator to manage its device inventory and associate devices with clients and services. It exposes interfaces for device configuration, firmware upgrade and other configuration options.

###### Operations #######

na.portal.veacs.operation.removeSipDevice.title = Remove SIP equipment
na.portal.veacs.operation.removeSipDevice.subtitle = In this area you can remove SIP equipment
na.portal.veacs.operation.removeSipDevice.name = remove SIP equipment

na.portal.veacs.operation.changeSipDeviceId.title = Change SIP equipment
na.portal.veacs.operation.changeSipDeviceId.subtitle = In this area you can change SIP equipment
na.portal.veacs.operation.changeSipDeviceId.name = change SIP equipment

na.portal.veacs.operation.addCustomKeyModule.title = Add custom key module
na.portal.veacs.operation.addCustomKeyModule.subtitle = In this area you can add custom key module
na.portal.veacs.operation.addCustomKeyModule.name = add custom key module

na.portal.veacs.operation.removeCustomKeyModule.title = Remove custom key module
na.portal.veacs.operation.removeCustomKeyModule.subtitle = In this area you can remove custom key module
na.portal.veacs.operation.removeCustomKeyModule.name = remove custom key module

na.portal.veacs.operation.createTemplateWithNewSoftwareVersion.title = Create template
na.portal.veacs.operation.createTemplateWithNewSoftwareVersion.subtitle = In this area you can create template with new software version
na.portal.veacs.operation.createTemplateWithNewSoftwareVersion.name = create template with new software version

na.portal.veacs.operation.migrateConfigurationBySoftwareVersion.title = Migrate configuration
na.portal.veacs.operation.migrateConfigurationBySoftwareVersion.subtitle = In this area you can migrate configuration by software version
na.portal.veacs.operation.migrateConfigurationBySoftwareVersion.name = migrate configuration by software version

na.portal.veacs.operation.diagnostics.diagnostic.title = Equipment Diagnostic
na.portal.veacs.operation.diagnostics.diagnostic.subtitle = In this area you can generate a equipment diagnostic
na.portal.veacs.operation.diagnostics.diagnostic.name = equipment diagnostic

na.portal.veacs.customKey.keyId.1stKeyOnTheLeftColumnOfThe1stmodule = 1st key on the left column of the 1st module
na.portal.veacs.customKey.keyId.1stKeyOnTheRightColumnOfThe1stModule = 1st key on the right column of the 1st module
na.portal.veacs.customKey.keyId.1stKeyOnTheLeftColumnOfThe2ndModule = 1st key on the left column of the 2nd module
na.portal.veacs.customKey.keyId.1stKeyOnTheRightColumnOfThe2ndModule = 1st key on the right column of the 2nd module
na.portal.veacs.customKey.keyId.1stKeyOnTheLeftColumnOfThe3rdModule = 1st key on the left column of the 3rd module
na.portal.veacs.customKey.keyId.1stKeyOnTheRightColumnOfThe3rdModule = 1st key on the right column of the 3rd module
na.portal.veacs.customKey.keyId.1stKeyOfThe1stPageOfThe1stModule = 1st key of the 1st page of the 1st module
na.portal.veacs.customKey.keyId.1stKeyOfThe2ndPageOfThe1stModule = 1st key of the 2nd page of the 1st module
na.portal.veacs.customKey.keyId.1stKeyOfThe1stPageOfThe2ndModule = 1st key of the 1st page of the 2nd module
na.portal.veacs.customKey.keyId.1stKeyOfThe2ndPageOfThe2ndModule = 1st key of the 2nd page of the 2nd module
na.portal.veacs.customKey.keyId.1stKeyOfThe1stPageOfThe3rdModule = 1st key of the 1st page of the 3rd module
na.portal.veacs.customKey.keyId.1stKeyOfThe2ndPageOfThe3rdModule = 1st key of the 2nd page of the 3rd module
na.portal.veacs.customKey.keyId.1stKeyOfThe1stPageOfThe4thModule = 1st key of the 1st page of the 4th module
na.portal.veacs.customKey.keyId.1stKeyOfThe2ndPageOfThe4thModule = 1st key of the 2nd page of the 4th module
na.portal.veacs.customKey.keyId.1stKey2ndPageOfTheScreen = 1st key 2nd page of the screen
na.portal.veacs.customKey.keyId.1stKey3rdPageOfTheScreen = 1st key 3rd page of the screen
na.portal.veacs.customKey.keyId.1stKeyOfThe1stModule = 1st key of the 1st module
na.portal.veacs.customKey.keyId.1stKeyOfThe2ndModule = 1st key of the 2nd module
na.portal.veacs.customKey.keyId.1stSidekey = 1st side key