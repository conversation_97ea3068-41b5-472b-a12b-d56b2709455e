########################################## EXCEPTIONS ##########################################
#exception.<code>.title = EXCEPTION TITLE
#exception.<code>.user.message = EXCEPTION USER MESSAGE

exception.NA_00001.title = Service Unavailable
exception.NA_00001.user.message = The service is temporarily unavailable. Try again later.
exception.NA_00002.title = Operation not concluded.
exception.NA_00002.user.message = Due to a problem it has not been possible to complete the operation. Contact System Administrators.
exception.NA_00003.title = Configuration Error
exception.NA_00003.user.message = Due to a configuration error, you can not use this page.
exception.NA_00004.title = Request Error
exception.NA_00004.user.message = An error occurred while processing response.
exception.NA_00005.title = Non Existent resource
exception.NA_00005.user.message = The resource you want does not exist.
exception.NA_00006.title = Existing resource
exception.NA_00006.user.message = The resource you want to save already exists.
exception.NA_00007.title = Bad Request
exception.NA_00007.user.message = Wrong parameters on request.
exception.NA_00008.title = Error
exception.NA_00008.user.message = An error occurred during execution.

# FILTER EXCEPTIONS
exception.NA_FILTER_00005.title = Non Existent filter
exception.NA_FILTER_00005.user.message = The filter you want does not exist.
exception.NA_FILTER_00006.title = Existing filter
exception.NA_FILTER_00006.user.message = The filter you want to save already exists.
exception.NA_FILTER_00007.title = Bad Request
exception.NA_FILTER_00007.user.message = A problem has occurred with the executed request.
exception.NA_FILTER_00008.title = Error
exception.NA_FILTER_00008.user.message = An error occurred during execution.
exception.NA_FILTER_00009.title = Bad Request
exception.NA_FILTER_00009.user.message = Missing required fields on the submitted request.
exception.NA_FILTER_00010.title = Invalid filter
exception.NA_FILTER_00010.user.message = Can not save empty filter.