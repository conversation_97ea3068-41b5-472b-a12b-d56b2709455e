# ----------------------------------------------------------------------------------------------------------------------
# -- This is repeated because it's "nailed" in the template from nossis-inv frontend base
# ----------------------------------------------------------------------------------------------------------------------

netwin.basemodule.results.label.title               = Data Management

netwin.input.nomatches                              = no matches

netwin.dateTimePicker.dateFormat                    = YYYY-DD-MM
netwin.dateTimePicker.minDate                       = 2014/01/01
netwin.dateTimePicker.maxDate                       = 2099/12/31
netwin.dateTimePicker.separator                     = -

netwin.selectbox.placeholder                        = Select

netwin.project.modal.header.delete                  = Delete
netwin.project.modal.body.delete                    = Do you really wish to delete this instance?
netwin.modal.navigate.button.confirm                = confirm
netwin.modal.navigate.button.cancel                 = cancel

netwin.attribute.boolean.no                         = No
netwin.attribute.boolean.yes                        = Yes

netwin.splitter.hide                                = Minimize
netwin.splitter.show                                = Maximize

netwin.button.label.cancel                          = Cancel
netwin.button.label.create                          = Create
netwin.button.label.save                            = Save

netwin.basemodule.lateralFilter.title               = Filters
netwin.basemodule.lateralSearch.title               = search
netwin.basemodule.search.search                     = Search
netwin.basemodule.search.clean                      = Clean
netwin.basemodule.requiredFields                    = Mandatory fields

netwin.datatables.action.label.view                 = view
netwin.datatables.action.label.add                  = create
netwin.datatables.action.label.clone                = clone
netwin.datatables.action.label.columnfilter         = column filter
netwin.datatables.action.label.delete               = remove
netwin.datatables.action.label.edit                 = edit
netwin.datatables.action.label.remove               = remove
netwin.datatables.columnFilter.placeholder          = search
netwin.datatables.columnFilter.search.placeholder   = search...
netwin.datatables.action.label.moreoptions          = options

netwin.notification.fuximissing.message             = Failed while loading the styling scripts
netwin.notification.fuximissing.title               = Fuxi is missing
netwin.notification.required                        = required field
netwin.notification.regex                           = Invalid format or value
netwin.notification.numeric                         = Insert a numeric value
netwin.notification.double                          = Insert a numeric value
netwin.notification.range.prefix                    = Value must be
netwin.notification.range.lessThan                  = less or equal than {0}
netwin.notification.range.greaterThan               = greater or equal than {0}
netwin.notification.range.and                       = and
netwin.notification.length.prefix                   = Value must have at
netwin.notification.length.min                      = least {0}
netwin.notification.length.and                      = and at
netwin.notification.length.max                      = most {0}
netwin.notification.length.character                = character
netwin.notification.length.characters               = characters