########################## NOSSIS ##########################

#NA
nossis.upload = A enviar


# ERRORS PAGES
nossis.401.title = 401 - Acesso Não autorizado!
nossis.401.subtitle = Acesso Não autorizado!
nossis.401.description = Não possui privilégios para aceder a esta página ou a página necessita que você esteja logado.
nossis.401.resolve.label =  Para resolver o problema, tente uma das seguintes opções:
nossis.401.tryagain.button = Tentar novamente
nossis.401.tryagain.button.description = Tentar efectuar novamente o pedido de página
nossis.401.report.button = Reportar erro
nossis.401.report.button.description = Informar os administradores do sistema sobre este problema
nossis.401.backtologin = Voltar à página de login

nossis.404.title = 404 - P<PERSON>gina não encontrada!
nossis.404.subtitle = A página não foi encontrada!
nossis.404.description = A página não foi encontrada, provavelmente terá se enganado a digitar o endereço, ou a página que procura já não existe
nossis.404.resolve.label =  Para resolver o problema, tente uma das seguintes opções:
nossis.404.tryagain.button = Tentar novamente
nossis.404.tryagain.button.description = Tentar efectuar novamente o pedido de página
nossis.404.report.button = Reportar erro
nossis.404.report.button.description = Informar os administradores do sistema sobre este problema
nossis.404.backtologin = Voltar à página de login

nossis.500.title = 500 - Ocorreu um erro inesperado!
nossis.500.subtitle = Ocorreu um erro inesperado!
nossis.500.description = Ocorreu um erro no pedido da página
nossis.500.resolve.label =  Para resolver o problema, tente uma das seguintes opções:
nossis.500.tryagain.button = Tentar novamente
nossis.500.tryagain.button.description = Tentar efectuar novamente o pedido de página
nossis.500.report.button = Reportar erro
nossis.500.report.button.description = Informar os administradores do sistema sobre este problema
nossis.500.backtologin = Voltar à página de login

security.authentication.error.locked = Conta bloqueada, contate os administradores.

#Login
na.login.button.ok.text=entrar
na.login.box.user.label=Login
na.login.box.user.placeholder=Inserir Login
na.login.box.password.label=Password
na.login.box.password.placeholder=Inserir Password
na.login.title.key.slogan.label=
na.login.label.title.box=Faça login com a sua conta de cliente
na.login.validate.credentials = A validar credenciais... Aguarde.
na.login.captcha.placeholder = Inserir o captcha
na.login.captcha.newwords = Obter um novo captcha
na.login.box.captcha.title = Digite o seguinte texto

naportal.notification.fuximissing.title = Fuxi em falta
naportal.notification.fuximissing.message = Falha no carregamento de ficheiros essenciais na representação gráfica
