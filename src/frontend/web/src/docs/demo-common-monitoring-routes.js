import { arrayEquals } from '~utils/array-utils.util'

const { GET, POST } = (() => {
  const route = (url, method) => {
    const pathSections = url.split('/')
    const isSimpleUrl = pathSections.every(pathSection => !pathSection.startsWith(':'))

    if (isSimpleUrl) {
      return {
        url: () => url,
        matchesUrl: (urlToMatch) => url === urlToMatch,
        getParametersFromUrl: (urlToMatch) => url === urlToMatch ? {} : null,
        toPlayRoute: () => () => ({ url, method }),
        method: () => method
      }
    }

    const urlParams = pathSections
      .filter(pathSection => pathSection.startsWith(':'))
      .reduce((acc, pathSection) => ({ ...acc, [pathSection]: pathSection.slice(1) }), {})

    const generateUrl = (params = {}) => {
      return pathSections.map(pathSection => {
        if (pathSection.startsWith(':')) {
          const key = urlParams[pathSection]
          return params[key] != null ? params[key].toString() : ''
        }
        return pathSection
      }).join('/')
    }

    const matchesUrl = (urlToMatch) => {
      const urlToMatchSections = urlToMatch.split('/')
      return arrayEquals(pathSections, urlToMatchSections, (pathSection, pathSectionToMatch) => {
        return pathSection.startsWith(':') || pathSection === pathSectionToMatch
      })
    }

    const getParametersFromUrl = (url) => {
      if (!matchesUrl(url)) {
        return {}
      }
      const urlSections = url.split('/')

      return urlSections.reduce((acc, val, index) => {
        const pathSection = pathSections[index]
        if (pathSection.startsWith(':')) {
          return { ...acc, [urlParams[pathSection]]: val }
        }
        return acc
      })
    }

    const pathSectionsPlayPosition = pathSections.reduce((acc, section) => {
      if (section.startsWith(':') && !acc.includes(urlParams[section])) {
        return [...acc, urlParams[section]]
      }
      return acc
    }, [])

    const toPlayRoute = () => (...data) => ({
      url: generateUrl(Object.fromEntries(pathSectionsPlayPosition.map((param, index) => [param, data[index]]))),
      method
    })

    return {
      url: generateUrl,
      matchesUrl,
      getParametersFromUrl,
      toPlayRoute,
      method: () => method
    }
  }

  return {
    GET: (url) => ({ ...route(url, 'GET') }),
    POST: (url) => ({ ...route(url, 'POST') }),
    PUT: (url) => ({ ...route(url, 'PUT') }),
    DELETE: (url) => ({ ...route(url, 'DELETE') })
  }
})()

/*
@property {PlayRoute0} na.monitoring.controllers.Application.resume
@property {PlayRoute1} na.monitoring.controllers.OrderController.view
@property {PlayRoute} na.monitoring.controllers.OrderController.getOrderById
@property {PlayRoute} na.monitoring.controllers.OrderController.getStates
@property {PlayRoute} na.monitoring.controllers.OrderController.getSettings
@property {PlayRoute} na.monitoring.controllers.OrderController.getFiltersAsJson
@property {PlayRoute1} na.monitoring.controllers.OrderController.getGraph
@property {PlayRoute} na.monitoring.controllers.OrderController.dataTableOperationServiceMonitoring
@property {PlayRoute} na.monitoring.controllers.OrderController.dataTableResponseRetries
@property {PlayRoute} na.monitoring.controllers.OrderController.dataTableDeliveryAttempts
@property {PlayRoute} na.monitoring.controllers.OrderController.cancelOrders
@property {PlayRoute} na.monitoring.controllers.OrderController.skipOrders
@property {PlayRoute} na.monitoring.controllers.OrderController.rollBackOrders
@property {PlayRoute} na.monitoring.controllers.OrderController.retryOrders
@property {PlayRoute} na.monitoring.controllers.ActivityController.restart
@property {PlayRoute} na.monitoring.controllers.ActivityController.skip
@property {PlayRoute} na.monitoring.controllers.ActivityController.rollback
@property {PlayRoute1} na.monitoring.controllers.ActivityController.retry
@property {PlayRoute3} na.monitoring.controllers.ActivityController.complete
@property {PlayRoute} na.monitoring.controllers.NotificationController.getNotificationsByOrderId
@property {PlayRoute2} na.monitoring.controllers.NotificationController.retryNotificationBySubscriber
@property {PlayRoute} na.monitoring.controllers.NotificationController.retryNotification
@property {PlayRoute} na.monitoring.controllers.TableController.dataTableDeliveryAttempts
@property {PlayRoute1} na.monitoring.controllers.Assets.versioned

@property {PlayRoute0} na.monitoring.controllers.OrderController.createOrder
@property {PlayRoute0} na.monitoring.controllers.OrderController.getSystems
@property {PlayRoute0} na.monitoring.controllers.OrderController.getSystemsWithUsers
@property {PlayRoute0} na.monitoring.controllers.OrderController.createPage
@property {PlayRoute0} na.monitoring.controllers.OrderController.clonePage

@property {PlayRoute0} na.monitoring.controllers.OrderController.getOperations
@property {PlayRoute1} na.monitoring.controllers.OrderController.getOperationById
@property {PlayRoute1} na.monitoring.controllers.OrderController.getOperationsByVersion

*/
export const routes = {
  na: {
    monitoring: {
      controllers: {
        OrderController: {
          home: GET('/monitoring/orders'),
          view: GET('/monitoring/orders/view/:id'),
          getOrderById: GET('/monitoring/orders/view/:id'),
          getStates: GET('/monitoring/orders/states'),
          getSystems: GET('/monitoring/orders/systemsUsers'),
          getSystemsWithUsers: GET('/monitoring/orders/states'),
          createOrder: POST('/monitoring/orders/create'),
          createPage: GET('/monitoring/orders/create'),
          getSettings: GET('/monitoring/orders/settings'),
          getOperationsByVersion: GET('/monitoring/orders/operationsByVersion'),
          getOperationById: GET('/monitoring/orders/operation-info/:id'),
          getOperationByVersionAndName: GET('/monitoring/orders/operation-info/:version/:name'),
        }
      }
    }
  }
}

const toPlayRoutes = (routes) => {
  if (routes == null) return routes
  if (typeof routes.toPlayRoute === 'function') {
    return routes.toPlayRoute()
  }
  return Object.fromEntries(Object.entries(routes).map(([key, route]) => [key, toPlayRoutes(route)]))
}

// VERSION PAGES
global.monitoringJsRoutes = toPlayRoutes(routes)
