import '../demo-common.js'
import mermaid from 'mermaid'
import * as d3 from 'd3'
import elkLayouts from '@mermaid-js/layout-elk'
// load and register the engine
mermaid.registerLayoutLoaders(elkLayouts)

// apply code caption to bottom for consistency
document.querySelectorAll('.listingblock').forEach(block => {
  const title = block.querySelector(':scope > .title')
  if (!title || ["Figure", "Code"].every((prefix) => !title.textContent.startsWith(prefix))) {return}
  block.appendChild(title)
})

/**
 * Adds Zoom Behaviour For Graph Elements generated by mermaid
 * @param {HTMLElement} mermaidElement
 */
function addZoomBehaviourForGraph(mermaidElement) {
  function shouldIgnoreClick(event) {
    if (window.getSelection().toString().length) {
      return true // ignore class toggle on text selections
    }
    if (event.target.matches('.mermaid .label div, .mermaid text tspan')) {
      return true // ignore text clicks
    }
    return false
  }

  mermaidElement.addEventListener('click', (event) => {
    if (shouldIgnoreClick(event)) {
      return
    }

    const dupNode = mermaidElement.cloneNode(true)
    dupNode.classList.toggle('fullscreen')
    document.body.appendChild(dupNode)
    const svgElement = /** @type {HTMLElement} */(dupNode.querySelector('svg'))

    const svg = d3.select(svgElement)
    svg.html('<g>' + svg.html() + '</g>')
    const inner = svg.select('g')
    const zoom = d3.zoom().on('zoom', function(event) {
      inner.attr('transform', event.transform)
    })
    svg.call(zoom)
    svgElement.style.maxWidth = null
    dupNode.addEventListener('click', (event) => {
      if (shouldIgnoreClick(event)) {
        return
      }
      dupNode.remove()
    })
  })
}

document.querySelectorAll('.mermaid').forEach(addZoomBehaviourForGraph)

queueMicrotask(() => {
  mermaid.initialize({
    startOnLoad: true,
    look: 'handDrawn',
    themeVariables: {
      fontFamily: 'Architects Daughter'
    },
    securityLevel: 'strict',
    theme: 'neutral',
  })
})
