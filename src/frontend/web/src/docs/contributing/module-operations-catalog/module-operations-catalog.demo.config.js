// This file is used by webpack-demos.config.js

const path = require('node:path')
module.exports = ({ menus }) => ({
  entry: path.resolve(__dirname, 'module-operations-catalog.demo.js'),
  chunk: 'contributing/module-operations-catalog',
  htmlTemplate: path.resolve(__dirname, 'module-operations-catalog.demo.html'),
  htmlOutput: 'contributing-module-operations-catalog.html',
  title: 'Operations Catalog Module',
  navBarLabel: 'Module: Operations Catalog',
  menu: menus.contributingArchitecture,
  dependencies: {}
})
