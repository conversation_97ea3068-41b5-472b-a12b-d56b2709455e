= NA Portal Frontend Web
:source-highlighter: rouge
:figure-caption:
:imagesdir: src/docs/contributing/development
:data-uri:
:sectnums:
:toc:

== Overview

This document is focused on aiding the development of the application by introducing the similarities and differences between the production and development environments. It provides more details about the architecture to help understand each piece of software involved in development.

The next section explains how the project is structured, giving an idea of how to navigate the project. The Frontend and API layers have different file structures because they use different technologies.

We also describe how the code is written. By following a style guide, we ensure consistent coding practices, making it easier to develop, debug, and maintain the project.

Additionally, we provide advice that not only explains the reasoning behind some of the code patterns visible in the project but also gives context to help maintain code consistency.

The following section explains the process of bundling the application. The bundles are then used to start the development server or to be published in a repository.

Finally, the last section provides details on how to integrate our modules into other products. Each module is designed to be executed in a standalone way or integrated inside an iframe.

== Getting Started

The home page has a link:/[Getting Started] section to help you start the project.

== Development Architecture

The development architecture closely mirrors the production architecture, with differences designed to facilitate development and maintenance.

- *Web Balancer*: The web balancer is replaced with a development server. The production setup uses Nginx, which operates as a daemon and requires installation on the machine. Instead, a simple Express application serves as a proxy to the API layer, while assets are managed using Node Package Manager (NPM). Since asset bundling is handled by Webpack, which already depends on Node.js, no additional dependencies are introduced for development. An added benefit is that the entire server setup remains self-contained within the project, eliminating the need for system-wide configurations. To start the development server, run `npm run dev` as explained in the link:/[Getting Started] section.

- *API*: Each module has a configuration file entry point for development, located in `src/frontend/play/{{module name}}/conf-dev/application.conf`, where `{{module name}}` corresponds to a product module. Only common modules do not have a config file, as they are meant to be used by other modules. All module servers start with `./gradlew run`, as explained in the link:/[Getting Started] section.

- *Backend*: The backends used for development are in CI due to the lack of resources on local machines. Opening all frontend servers with dev tools requires a large amount of resources. Adding the entire infrastructure to the local machine (Backends, DB, etc.), as well as developing with IntelliJ *and* a browser open, would significantly slow down development. Therefore, for development, the machine is configured to access the Backend in CI.

- *IAM*: IAM (Identity and Access Management) is in CI because there is no easy way to install it locally.

- *Log Management*: All logs go to `/tmp` as they are only needed during development. Afterward, they are unnecessary and are automatically removed when the system shuts down.

In summary, the following figure shows the architecture in the frontend:

[mermaid,caption="Figure {counter:figure}."]
.Interaction between modules in the Frontend and API layers
....
%%{init: {"flowchart": {"defaultRenderer": "elk"}} }%%
flowchart-elk TD

Browser --> DevServer("Development server (npm run dev)")

DevServer --> NADMMod
DevServer --> MonitMod
DevServer --> OtherMod
DevServer --> Portal

MonitMod --> OrderFollowup
MonitMod --> SWE
OtherMod --> otherBackend
NADMMod --> NADM

subgraph "Frontend"
Browser
end

subgraph API["API Layer (./gradlew run) "]
NADMMod["NADM Module"]
MonitMod["Monitoring Module"]
OtherMod["Other Module"]
Portal
end

Portal -->|Login / Logout| IAM("IAM in CI machine")
API -->|validate user permission| IAM

subgraph "Backend in CI machine"
NADM
OrderFollowup("Order Followup")
SWE
otherBackend("Other Backend")
end

subgraph Logs["Logs in /tmp"]
LogNADM["/tmp/nadm/"]
LogMonit["/tmp/monitoring/"]
LogOtherMod["/tmp/{moduleName}/"]
LogPortal["/tmp/portal/"]
end

NADMMod --> LogNADM
MonitMod --> LogMonit
OtherMod --> LogOtherMod
Portal --> LogPortal

....

=== API

When developing in the API layer, we have two approaches: Server-side rendering (SSR) and Client-side rendering (CSR). Old modules use SSR, while new modules use a mix of both because we are migrating to CSR. Both architectures use the Model-View-Controller (MVC) pattern, albeit with different views.

In SSR, the view is HTML content, whether it is an entire page or a part of it (e.g., modal or tab content). In CSR, the view is purely JSON content. Some pages return JSON or HTML regardless of the architecture. For example, select box and table data require JSON, while all page routes should return HTML.

The API is developed using an MVC pattern while leveraging features of the Play framework. Each module is divided into the following sections:

- *Model*: Manages the application data. It contains object models used in the application module, as well as adapters to adapt data received from other layers (e.g., backend) to the object model. Some models are _data transfer objects_ (DTOs) sent to the frontend layer.
- *View*: Represents the presentation of the model in a particular format. It contains the HTML template engine to convert input data to HTML output. The controller is responsible for sending the result to the frontend.
- *Controller*: Handles user input and interacts with data models. It receives requests, optionally validates them, and then passes the data to the *Model* for processing.
- *Service*: Contains reusable parts of code used by the controllers. They have minimal dependencies to make it easier to migrate to other components. Generally, they are used to access other layers and return adapted results using data adapters in the *Model* section.
- *Action*: In the Play framework, actions are used as filters before accessing the controller. Each controller filter is accessed by looking at the annotations in the controller function.

.Code example of an action used in a controller as method annotations
[source,java,caption="Code {counter:code}. "]
----
@Authenticated // <-- action 1
@Authorized({AAAPIResources.MONITORING + AAAPIAccess.RU}) // <-- action 2
public CompletionStage<Result> retry(boolean isNewRequest) {
    JsonNode jsonParams = request().body().asJson();
    return service.retry(buildActionPayload(jsonParams), isNewRequest)
            .thenApply(Json::toJson)
            .thenApply(Results::ok);
}
----
- *Router*: The Play framework provides a configuration file for routing, a pattern-matching system responsible for mapping incoming browser requests to a specified controller. If a request does not match any defined route, the router returns a *`404 Not Found`* response.
- *Error Handler*: Notifies the user in case an error occurs, whether it is a business error or a server fault.

The next figure shows the interaction of each part when a request is made to the server:

[mermaid,caption="Figure {counter:figure}."]
.The workflow of a request in a module in the API layer, other layers are ignored.
....
flowchart LR

Browser --> |makes request| router
router --> |404 if route not found| Browser
router --> |route found| Action
Action --> |"action blocked request (e.g., 401 for forbidden access to user)"| Browser
Action --> |action ok| Controller
Controller --> |sends response| Browser

subgraph Common["Common workflow"]
router
Action
Controller
View
Model
service
end

Controller --> Model
Model --> Controller
Controller --> View
View --> Controller
Controller --> service
service --> Model
Model --> service
service --> Controller
service --> External("External service <br>(Backend, IAM, DB, etc...)")
External --> service

subgraph ExceptionWorkflow
errHand["Error Handler"]
end

errHand --> |responds to browser with a 4xx or 5xx HTTP code| Browser
Common --> |unhandled exception thrown| errHand
....

=== Frontend, Flux

When developing in the frontend layer, we use the link:https://facebookarchive.github.io/flux/docs/in-depth-overview/[Flux] architecture. It has three major parts: the dispatcher, the stores, and the views.

[mermaid,caption="Figure {counter:figure}."]
.Flux architecture data flow
....
flowchart LR

Action1("Action") --> Dispatcher

Dispatcher --> Store
Store --> View
Action --> Dispatcher
View --> Action

....

While the implementation differs from the core Flux architecture, the underlying concept remains the same:

- The view communicates with the store by calling its setters. This triggers an action, which is then dispatched to the store.
- The view consists of components that react to store changes by registering observers within the store.

==== Store

To manage data, we have a modular store, where each store functions as an independent module.

[mermaid,caption="Figure {counter:figure}."]
.Interaction between store and view
....
flowchart LR

subgraph Store
SN1("Store name 1")
SN2("Store name 2")
SN3("Store name 3")
SN4("Store name 4")
end

subgraph View
CP1("Component 1")
CP2("Component 2")
CP3("Component 3")
CP4("Component 4")
end

SN1 -->|reflects| CP1
SN1 -->|reflects| CP2
SN3 -->|reflects| CP4
SN4 -->|reflects| CP2
SN2 -->|reflects| CP3

CP1 -->|dispatch action| SN1
CP3 -->|dispatch action| SN2

....

Each store has 3 main components:

- state: Represents the current state. The state tree is a frozen object structure, so trying to make mutations in the
object structure will have no effect;

- setters: they work as actions and dispatcher. When a setter is called on the store, it generates an action and
dispatches it to the store

- getters: they are computed values derived from the store state. It helps to keep the state object structure
simple by acting as additional properties of the state structure. It uses memoization techniques to simplify state
comparison with getters by comparing through object equality without needing to compare the object structure tree.

===== Defining the state

While a store has the 3 main components defined previously, you need to define its state and how it changes.

====== Initial state

To define a state, you must first define the initial state of the store when it is initialized. The initial state is
just a supplier function, simply a function without parameters that returns an object structure.

[source,js]
```
export const initialState = () => {a: 1, b: 2, c: 3}
```

====== Getter

A getter is defined as a function that receives the current state and returns a computed result.

[source,js]
```
const sum = (state) => state.a + state.b + state.c

export const getters = {
  sum
}
```

There is an issue when you create getters that returns objects. The store compares changes through object sameness, as
well as our listeners. So you can create a getter that returns an array for example:

[source,js]
```
const sum = (state) => state.a + state.b + state.c
const abList = (state) => [state.a, state.b]

export const getters = {
  sum,
  abList
}
```

Every time a getter property is read, it will call the function and memoize to the state, meaning if you call the
getter again, it will return the same object until the store state changes.

But there is an issue, when the store state changes, and you compare the previous and next state, and a
getter returns a plain object or array, you would need to compare the getter result object structure
recursively instead of just checking if the object is the same.
If you have a lot of observers to check, for example, the `list` getter in the previous example, a lot
of resources will be used to compare the object, degrading app performance.

To solve that, we have selectors, it provides a memoized getter with a small cache, just the previous result actually,
that when it is called, it will check if the input getters return the same value, if it happens, returns the
saved result, making the getter return the same object.

[source,js]
```
import { selector } from '~utils/selector.util'

// ...

const a = (state) => state.a
const b = (state) => state.b
const sum = (state) => state.a + state.b + state.c
const abList = selector([a, b], (a, b) => [a, b])

export const getters = {
  sum,
  abList
}
```

With this, if you change the state and the difference is only in the `c` property, `abList` result will be the
same object when comparing the previous and next state, because the input getters returns the same results.

====== Setter

A setter is defined as a function that receives the current state and returns another state, the state should have
schema structure as the previous.

Key Considerations When Creating a Setter:

- The input state is an immutable object structure.
- The store detects state changes by checking the root object reference.

So, to change the state, you must create a new object structure with only the changes applied.

The next figure shows how to change the state, given the current state has the value
`{a: { c: 1, d: {} },b: { e: {}, f: 9 }}`
and want to change the value of the property with path `.a.c`, from `1` to `5`:

[mermaid,caption="Figure {counter:figure}."]
.Process of changing a store state
....
flowchart TB

subgraph next state
direction TB
N1(("R'"))
N11(("o1'"))
N111(("5"))
N1 ---|a| N11
N11 ---|c| N111

end

subgraph previous State
direction TB

P1(("R"))
P11(("o1"))
P12(("o2"))
P111(("1"))
P112(("o3"))
P1121(("..."))
P121(("o4"))
P1211(("..."))
P122(("9"))

P1 ---|a| P11
P1 ---|b| P12
P11 ---|c| P111
P11 ---|d| P112
P12 ---|e| P121
P12 ---|f| P122
P112 --- P1121
P121 --- P1211
end

N1 ---|b| P12
N11 ---|d| P112
....

In short, you create a new state, reusing some internal object structures from
the previous state. This makes verifying if a part of the state has changed with
a simple equality check (===) in the root object without needing to compare the
entire structure of a part of the state. The next example is a code example
of how to define a setter for the previous figure:

[source,js]
```
const setC = (state, cVal) => {
  if(typeof cVal !== "number" || state.a.c === cVal){
    return state;
  }
  return {
   ...state,
    a: {
     ...state.a,
      c: cVal
    }
  }
}

export const setters = {
    setC
}
```

Note that we change the state tree only if the new state will respect the existing state schema, and if the new state
has some difference in the structure.
Otherwise, we return the previous state, notifying the store that calling the setter did not change the state.

==== Observing state changes

The normal way to observe the store is to run `.listenStateChange((oldSnapshot, newSnapshot) => void)`. It requires
a function as input, which will be the function to be executed each time the store state changes. The handler receives 2
parameters, each one of them is an immutable snapshot of the store, the snapshots only have the state and getter
properties of the store, so you can get the information of the snapshot the same way you get from the original store.

One note: when handling a state change, store setters are disabled, so calling a store setter will throw an error.
It prevents possible infinite cycles, as well as blocking the UI, with multiple setters called. To call a store setter
you must run in an asynchronous method, that will run when the setters are enabled.

There are also 2 helper functions to reuse common usage of the observers: `reflect()` and `react()`

With `reflect()` you receive 2 parameters: the first parameter is a function to get the data from both snapshots where it compares
the result, if it is different, it calls the second parameter which is the handler that receives the old and new values
to handle it.

`react()` method is meant to be used for asynchronous operations where a specific action was executed, without
affecting the view, e.g. making a request to the server to get search results based on information on the store.
As for now, it is an alias for `reflect()`.

===== Debugging the store

There may be situations when an error happens in the product, causing it to have an inconsistent state, you can see
the changes in the browser console by enabling the debug mode. To enable debug mode you set the console
evaluation context to the iframe then run `na.Store.enableDebug()` it will enable the logs until you refresh the page.
To persist the debug mode you either add `{persist: true}` as argument on `enableDebug()` to enable debug or run `.persist()`
on the API that is returned from `enableDebug()`.

image::enabling-store-debug.gif[caption="Figure {counter:figure}: ", title="Enabling debug mode fill the console with store logs", align="center"]

When debug mode is enabled, any action related to store modifications, be it adding listeners or changing state,
is logged to the browser dev tools console. It helps understand the application state, helping to identify the
cause of a problem in the product when it happens. The most common use case is state change logs, every time an action
is sent to the store, it is logged to the browser. It has a specific color that helps to quickly identify if the state
has changed without needing to expand to check: if it is blue, it means the state has changed, if is orange, the action
did not modify the store.

[cols="a,a", grid=none]
|===
| image::changed-state-store-setter.png[,100%,caption="Figure {counter:figure}: ", title="Log of setter call that changed store state", align="center"]
| image::unchanged-state-store-setter.png[,100%,caption="Figure {counter:figure}: ", title="Log of setter call that did not change store state", align="center"]
|===

=== Internationalization (i18n)

This application uses internationalization (i18n), the process of designing a software application
so that it can be adapted to various languages and regions without engineering changes.

I18n is done by adding translation files, called i18n files, to the product, so it translates
content for each user.

==== I18n files location

All i18n file locations are defined in `nossis.messages.resources.path`, configuration
key in application.conf. Each field in the configuration value list represents a folder
to import i18n files, each one with the locale name as the file extension.

==== Conflict resolution

I18n keys can be duplicated in different files and are expected to be if you want to
customize them. To handle the conflict there is a conflict resolution rule: *the last defined
wins*, this rule helps understand the order of folders in the i18n location configuration:
Product locations before customization.

==== Usage in API

The translations are applied by two methods, the static method `pt.alticelabs.nossis.messages.api.Messages.get(String)`,
used on Java, and Twirl view tag `na.naportalbase.views.tags.i18n(String)` on `*.scala.html` files

==== Usage in Frontend

On Frontend, the i18n data are downloaded when loading the page, so you have access to the same keys as in the API.
There are 3 ways to use the i18n keys:

1. JavaScript module - import i18n function with `import { i18n } from '~utils/i18n'` then call the function to
apply the i18n

NOTE: `i18n` is an alias of `translate` on the same module, so you can import it using
`import { translate } from '~utils/i18n'`

2. `<x-i18n>` component - translates `key` attribute and puts the result in the component content. The result
is HTML escaped. More info on link:./i18n.html[i18n element documentation]

3. `<x-i18n-attr-container>` component translates content with `data-i18n-` prefix and applies the changes on
the result on the attribute without the prefix on the same element. More info on
link:./i18n-attr-container.html[I18n on attributes element documentation]

==== Applied language on API

The language applied is user-dependent, each user has a language defined on IAM, and for each request made to the server, it
will check the user logged and get the defined language. With the user language information, all usages of the API
automatically use the user language as the locale to translate the content.

==== Applied language on Frontend

On Frontend, the i18n respects the `link:https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/lang[lang]`
attribute on the page root `<html>`, each time the value of `lang` attribute changes, all i18n components update the
translated content to the new locale automatically. The JavaScript i18n module API locale also reflects the `lang`
attribute in the `<html>`

Some components find the closest element with the `lang` attribute. This is the best approach to get
the locale, it is recommended for all components to use the locale from the closest element with `lang` attribute,
while the JS API gets the locale from the root element: the `<html>`.

The loaded HTML in a module always has the `lang` defined in the `<html>` element. Its value is the same as
the one applied on the API layer. In summary, the `lang` value is defined by the user in IAM.

== File structure

=== Play

```
{project-root}/src/frontend/play/ # play project root
├── 🖿 basemodule/ # base module
│   ├── 🖿 app/ # module source root
│   │   └── 🖿 na/naportalbase # contributing section pages
│   │       ├── 🖿 controllers/ # contains controllers
│   │       ├── 🖿 exceptions/ # exception catalog
│   │       ├── 🖿 models/ # model files
│   │       ├── 🖿 services/ # services
│   │       ├── 🖿 settings/ # contains constants and access configurations
│   │       ├── 🖿 utils/ # contains common utilities for the application
│   │       └── 🖿 views/ # HTML rendering code, mostly used in SSR
│   ├── 🖿 conf/ # config files
│   ├── 🖿 conf-dev/ # development specific config files
│   ├── 🗎 build.gradle # gradle specific configuration for module
│   ├── 🗎 settings.gradle # gradle specific configuration for module
│   └── 🗎 pom.xml # module maven project file
├── 🖿 catalog-basemodule/ #  base module that contains adaptations for modules that uses NOSSIS-INV
├── 🖿 go/ # GO module
├── 🖿 mpt/ # MPT module
├── 🖿 .../ # other modules
│
├── 🗎 build.gradle # gradle specific configuration for module
└── 🗎 pom.xml      # maven project file
```

Each application module contains the following folder structure

```
{project-root}/src/frontend/play/{app-module-name} # play project root
├── 🖿 app/ # module source root
│   └── 🖿 na/naportalbase # contributing section pages
│       ├── 🖿 controllers/ # contains controllers
│       ├── 🖿 exceptions/ # exception catalog
│       ├── 🖿 models/ # model files
│       ├── 🖿 services/ # services
│       ├── 🖿 settings/ # contains constants and access configurations
│       ├── 🖿 utils/ # contains common utilities for the application
│       └── 🖿 views/ # HTML rendering code, mostly used in SSR
├── 🖿 conf/ # config files
│   ├── 🖿 i18n/ # i18n files
│   ├── 🗎 {app-module-name}.routes # router configuration
│   ├── 🗎 routes.properties # router configuration
│   └── 🗎 {app-module-name}-app.conf # router configuration
├── 🖿 conf-dev/ # development specific config files
│   ├── 🖿 i18n/ # optional, will contain customization i18n files
│   ├── 🗎 application.conf # configuration entry point
│   ├── 🗎 {app-module-name}-app.conf # module business logic specific configuration
│   └── 🗎 nossisaaapi.conf # NOSSISAAAPI configuration file
├── 🖿 test/ # unit tests located here
├── 🗎 build.gradle # gradle specific configuration for module
└── 🗎 pom.xml # module source root
```

NOTE: NOSSISAAAPI is short of *NOSSIS Security Authentication and authorization Adapter API*, it allows user
authorization and authentication access control with different providers (IAM, SCA, etc...) without needing
to change the code.
More info in link:https://alabs.atlassian.net/wiki/spaces/NOSSISFRMWRK/pages/100670594/NOSSIS+Security+AAAPI[NOSSIS Security AAAPI Documentation]

=== Frontend

In the frontend we have a different file structure from Play, since the way the code is bundled is by import tree.

```
{project-root}/src/frontend/web  # web project root
├── 🖿 src/ # source code root
│   ├── 🖿 base-style/ # base stylesheets to apply on Web Components
│   ├── 🖿 docs/ # documentation/demo specific code
│   │   ├── 🖿 contributing/ # contributing section pages
│   │   ├── 🖿 i18n/ # i18n config files
│   │   └── 🖿 mock-{module_name}-server/ # contains logic to simulate a server in demos
│   ├── 🖿 lib/ # libraries that are not in NPM goes here (NOTE: they are deprecated)
│   ├── 🖿 modules/ # module specific code
│   │   ├── 🖿 basemodule/ # basemodule (NOTE: it uses AngularJS only, deprecated in favour of
│   │   │                 # web/src/utils and web/src/web-components)
│   │   ├── 🖿 catalog-basemodule/ # NOSSIS INV code adaptation (NOTE: it uses AngularJS only,
│   │   │                          # deprecated in favour of web/src/utils and web/src/web-components)
│   │   ├── 🖿 go/ # GO module
│   │   │   ├── 🖿 src / # GO src file
│   │   │   ├── 🖿 stylesheets / # GO base stylesheets
│   │   ├── 🖿 monitoring/ # Monitoring module
│   │   └── 🖿 ...
│   ├── 🖿 test-utils/ # Test utilities be it unit tests or UI tests
│   ├── 🖿 utils/ # Utilities folder
│   └── 🖿 web-components/ # common components here
│       ├── 🖿 breadcrumb/
│       │   ├── 🖿 demo # breadcrumb demo and documentation folder
│       │   ├── 🖿 test # breadcrumb unit testing folder
│       │   └── breadcrumb.element.js # breadcrumb logic
│       ├── 🖿 datetimepicker/
│       └── 🖿 ...
│
├── 🗎 .babelrc          # babel configuration file
├── 🗎 .eslintignore     # files to ignore on ESLint
├── 🗎 .eslintrc.js      # ESLint configuration file
├── 🗎 .npmrc            # NPM project specific configuration
├── 🗎 .jsconfig.json    # config file for IDE Intellisense (adds import alias, etc...)
├── 🗎 package.json      # NPM project file
├── 🗎 package-lock.json # guarantees consistent versions between machines
├── 🗎 README.adoc       # README file
├── 🗎 specs.js          # script to execute unit tests
├── 🗎 specs-ui.js       # script to execute UI tests
├── 🗎 webpack.config.parts.js             # reusable webpack config parts
├── 🗎 webpack-adoc-loader.js              # adoc loader for webpack
├── 🗎 webpack-demos.config.js             # webpack bundle config for demo and documentation
├── 🗎 webpack-gradle-modules.config.js    # webpack bundle config for all modules except Portal
├── 🗎 webpack-gradle-nossis-ui.config.js  # webpack bundle config for Portal module
└── 🗎 webpack-ui-tests.config.js          # webpack bundle config for UI tests playground
```

Because we are migrating away from AngularJS, and due to evolutions in development, we do not yet have a consistent
file structure, this is due to the way ES6 bundling tools work and AngularJS work too.

The way ES6 bundling works is by entrypoint. When bundling, you get the import tree from the entrypoint file
and bundle it in single or multiple files depending on the bundle configuration, files not in the import tree
are ignored. They are not going to be downloaded to the client.

AngularJS, on the other hand, has a dependency injection mechanism, meaning it requires all the dependencies declared
before initializing the application, making it difficult to get the full benefits of ES6 import in a way that is
easy to maintain.

==== ESM Module Type

In _Ecmascript modules_ (ESM), a file is a module. To help identify which kind of module we are dealing with,
be it Angular specific code, test code, etc..., we group them by adding suffix in the file name that is similar
to a file extension. At the time of writing we have the following extensions.

- `*.angular-module.js` file includes an AngularJS module
- `*.directive.js` file includes an AngularJS directive
- `*.controller.js` file includes an AngularJS controller
- `*.service.js` file, **If not yet migrated from AngularJS** includes an Angular service, it should be migrated to
an ES6 module if that is the case
- `*.state.js` includes a state definition of a Store
- `*.store.js` includes a Store, the state definition, in normal cases, has the same name and is in the same directory.
If the state is to be reused in multiple stores, naturally it may have a different name and/or in a different location.
- `*.element.js` includes the definition and logic of a custom element, using Web components
- `*.element.html` includes the layout of a custom element
- `*.element.css` includes specific styles of a custom element
- `*.util.js` provides common algorithms to be used in the project
- `*.spec.js` includes unit test code
- `*.spec-ui.js` includes UI test code
- `*.demo.js` includes demo page logic
- `*.demo.config.js` includes a page configuration, imported only by `webpack-demos.config.js`
- `*.nossis-ui.js` code that will be imported and used in Portal module, imported only by `web/src/nossis-ui-contribution.js`
- `*.datatable-renderer.js` includes a datatable renderer that transforms cell specific data to html code
- `\*.na.js` only found in `implementation.*.js` files in `web/src/utils`, NA portal specific implementation, imported by each module entrypoint, excluding Portal module
- `\*.play.js` only found in `implementation.*.js` files in `web/src/utils`, Play framework specific implementation, only on module entrypoint
- `\*.csr.js` only found in `implementation.*.js` files in `web/src/utils`, Client side rendering specific implementation, imported only on module entrypoint
- `\*.ssr.js` only found in `implementation.*.js` files in `web/src/utils`, Server side rendering specific implementation, imported only on module entrypoint

There are some files that have both types (e.g. `implementation.na.nossis-ui.js`), you apply both descriptions
when it makes sense, for example, a file with `*.csr.ssr.js` extension does not make sense, so it should not exist.

==== Planned Module structure

Because we have AngularJS, it is hard to define a file structure that is specific to a module, but since there are plans
to remove it, it is a good idea to have a planned file structure that simplifies maintenance.

```
└── 🖿 web/src/modules/
    └── 🖿 {module}/
        ├── 🖿 app/ # Module app component, an isolated piece of code that has the logic, layout and styles isolated
        ├── 🖿 common-element-mixins/ # optional folder, if there is logic you want to reuse in multiple components, you export it and move here
        ├── 🖿 common-styles/ # optional folder, if there are styles you want to reuse in multiple components, you export it and move here
        ├── 🖿 common-stores/ # application-wide stores are here, (e.g. maintain search state when going back)
        ├── 🖿 component/ # module specific components here, if it is useful for multiple modules you can move it to web/src/web-components, or to NOWC
        │   ├── 🖿 component1-example/
        │   └── 🖿 component2-example/
        ├── 🖿 pages/ # Module page components here
        │   ├── 🖿 search-page-example/ # represents a page in the application
        │   └── 🖿 view-page-example/ # represents a page in the application
        └── 🖿 utils/ # Module specific utils
```

As you can see, the file structure is similar to the `web/src` folder but contextualized to a module, which makes it easier to navigate in the project, this structure is used in NOSSISTSC since AngularJS is removed there.

== Code style

NA Portal, being a project with a large codebase, has a style guide: a set of conventions about how to write code for a
specific project. It is much easier to understand a large codebase when all the code in it is in a consistent style.

On Java side, we use Nossis Code Style, an in-house style.

On JavaScript side, we use link:https://standardjs.com/[StandardJS] for a consistent style, the code is automatically
analyzed when `dev` mode is enabled in NPM, because it runs ESLint in the background, making static analysis of the code
automatically.

== Good development practices

There are good code practices that are common to many languages, such as giving descriptive names, favoring immutable
variables (in JS is using `const`), limiting usage of global variables, etc... This section will be focused on
general development practices instead of specific code practices.

=== CSS

==== Block Element Modifier (BEM)

Use link:https://getbem.com/introduction/[Block Element Modifier (BEM)] methodology, it provides a way to organize
the CSS rules in a modular way. It uses the 3 concepts to name the elements.

- Block: Standalone entity that is meaningful on its own.

An example of a block class is `.block`

- Element: Parts of a block and have no standalone meaning. Any element is semantically tied to its block.

An example of an element class that is a descendant of `.block` is `.block__element`

- Modifier: Flags on blocks or elements. It can be used to change appearance, behavior, or state, as well as specifying
a block or element.

An example of a modifier applied to block class `.block` is `.block\--disabled`.
Another modifier example is `.block\--title` to apply additional styles to blocks that
are meant to be the title of something.

There are times when you want to specify an element of multiple levels of the descendant tree,
e.g. `.block\__element__subelement__subsubelement`, it becomes easier to apply a block with a modifier and
apply styles there, e.g. `.block.block\--order-validation-section-details` and continue from there.

Also, applying BEM to Web Component `part` attributes is also a good idea to maintain the CSS code consistent. The following example
shows an example of using BEM in `part`:

[source, html]
```
<web-component class="component">
  #Shadow DOM
    <div>
      <button part="button button--ok">OK</button>
      <button part="button button--cancel">Cancel</button>
    </div>
</web-component>
```

[source, css]
```
.component::part(button--ok) {
    background-color: "green";
}

.component::part(button--cancel) {
    background-color: "red";
}
```
In conclusion, BEM methodology allows us to have a consistent coding style when applying styles to our components

=== JavaScript

==== Have a consistent code style

Using a coding style guide is especially important in teamwork, where multiple developers are working on the same project.
This allows a consistent coding style, making it easier to read, write, debug, and maintain.
As written previously, we are using link:https://standardjs.com/[StandardJS] as a coding style. We are also using
link:https://eslint.org/[ESLint]
not only to find linting errors but also to enforce the coding style.

Running dev environment with `npm run dev` will automatically run ESLint every time a JavaScript file is modified, so
make sure you pay attention to the logs.

==== Comment `utils/`

Generally, it is a good idea to document the code. But in JavaScript there is a benefit of applying JSDoc comments to
functions: it can provide type info for the type-checker to run static analysis on the code, as well as improve
IntelliSense in an IDE.

In the `utils/` folder, it is increasingly important to have the methods commented, as it is used through all modules,
so, listing supported values and what they represent helps immensely in code debugging and maintenance.

For other methods that are part of an API (e.g. Custom Element `connectedCallback()`), there is little benefit in adding
them if the code is self-explanatory.

==== In iframe communication, include `targetOrigin` in `postMessage` [[iframe-communication-best-practice]]

While `link:https://developer.mozilla.org/en-US/docs/Web/API/Window/postMessage#targetorigin[targetOrigin]` is optional,
in `link:https://developer.mozilla.org/en-US/docs/Web/API/Window/postMessage[postMessage]`, this product is made in mind
that each module *can* be in a different host or port. So, to avoid any CORS issue, it is recommended that every
communication between Portal and the other modules, that is done using `postMessage`, have `targetOrigin` defined.

=== Web components

link:https://developer.mozilla.org/en-US/docs/Web/API/Web_components[Web components] are a set of different technologies
allowing you to create reusable custom elements. While JS is used to create those components, the way they are developed
is different from conventional JavaScript, which is why this section is separate from the previous section.

==== Isolate the component

Use link:https://developer.mozilla.org/en-US/docs/Web/API/Web_components/Using_shadow_DOM[Shadow DOM] to isolate the
HTML and CSS of a component. Having them in the Shadow DOM prevents other components, as well as libraries from accidentally
making changes to the component DOM tree, as well as the component appearance with CSS.
Another advantage is that it reduces name pollution, meaning there is no need to specify the CSS class names to prevent
naming collision. If it is in a shadow DOM, it applies only to that specific DOM.

==== Separation of concerns

All web components defined in the projects are located in an `\*.element.js` file. In the same folder where resides
an `*.element.js` you may find an `\*.element.html` and/or an `*.element.css` file. If a component requires a specific
DOM structure, the HTML must be in the `\*.element.html` which will be used to apply the component HTML structure in the
Shadow DOM, also, if there are specific style rules for that component, they are added to the respective `*.element.css`
to be applied on the component's shadow DOM `adoptedStylesheets`.

==== Adopted stylesheets

Since we are applying shadow DOM to the components, we need to isolate the styles, one way to do this is to add `<style>`
to the shadow DOM, but it is a problem if you use a CSS framework with many CSS rules: Each component gets a copy of the
stylesheet, meaning there is a big cost in memory to add one component into the DOM. How to solve this: by applying
`adoptedStylesheets` on the Shadow Root.

The `adoptedStylesheets` is a new feature in the browsers that allows reusing the same CSS object to apply on a DOM,
be it light DOM or Shadow DOM, since the CSS is the same, there is no longer a memory penalty in applying CSS on
the component.

==== State above configuration

When developing a component, you can configure it using attributes, but there is a problem. When an error happens in
production, it is not easy to check if an element attribute has changed before initialization. There is
a solution for that: eliminating the problem by making the component reactive. Each time an attribute of the
element changes, update the component accordingly. Effectively transforming attributes meant for configuration to state
attributes.

==== Reflect properties in attributes

It is recommended for a component definition to be as stateless as possible.
By stateless, it means that the state is outside the Web component definition
class. Only private properties exclusively managed by the component can be saved in the class.

.Content-Security-Policy examples using frame-ancestors directive
[source,js,caption="Code {counter:code}. "]
```
class CustomElement extends HTMLElement {

  // in case of string properties string we use this way
  get format () {
    return this.getAttribute('format') ?? '<default-format>'
  }

  set format (val) {
    if(val == null){
      this.removeAttribute('format')
    }
    if(typeof val !== "string"){
      return
    }
    this.setAttribute('format', val)
  }

  // in case of boolean properties string we use this way
  get disabled () {
    return this.hasAttribute('disabled')
  }

  set disabled (val) {
    return this.toggleAttribute('disabled', !!val)
  }
}


```

==== *Use Popover API for popups*

When creating popups, the normal way is to create a DOM element at the end of `<body>` element
as to be able to be on top of each element, however, it means accessing outside the web component.
If the element is inside a shadow DOM, you will need to navigate each shadowDOM until getting into
the light DOM. With Popover API you can put elements above every other component because it will be in
a separate layer, for modal and popups.

== Bundling deliverables

=== Webpack

To build our assets, we use Webpack to bundle the Frontend code. Webpack is also used
to bundle the documentation as well as provide configurations for the API to provide the assets, e.g. the filenames of
files to load, because the assets filenames generated have a content hash suffix.
The next figure shows the folders generated by webpack based on the configuration used to bundle the code.

[listing,,caption="Figure {counter:figure}. Webpack generated assets folder structure"]
```
{project-root}/src/frontend/web  # web project root
└── 🖿 build/ # all generated deliverables here
    └── 🖿 webpack/
        ├── 🖿 demo/ # demo bundle for development
        ├── 🖿 demo-prod/ # demo bundle to publish the documentation
        ├── 🖿 gradle-modules/ # assets for base and product modules, except Portal
        ├── 🖿 gradle-nossis-ui/ # assets for Portal module only
        └── 🖿 ui-test/ # assets for UI tests playground
```

When working on the `web` project, starting the development environment generates or updates the `build` folder with the following bundles:

* `demo`:
** Contains all static assets for the NA Portal documentation.
** Used as the documentation server's assets.
** Includes a soft link to the NOWC documentation, which the documentation server redirects successfully, the decision of using the soft
link is to not waste disk read/write every time the documentation is updated

* `demo-prod`:
** Similar to `demo`, but includes a copy of the NOWC documentation structure instead of a soft link.
** The assets are used for publishing the documentation Docker image. Built manually using `npm run build:demo-prod`
as it is the only build folder that is not automatically built with `npm run dev`.

* `gradle-modules`:
** Contains assets for all modules except Portal.
** Contains configuration and generated code
for Play to compile, for example `.../gradle-modules/monitoring/app/na/monitoring/views/imports/scripts.scala.html` which
contains the assets with the correct content hash on the file name suffix

* `gradle-nossis-ui`:
** Similar to gradle-modules, but specifically for the Portal module.
** The Portal module is isolated and does not depend on any base module.
** Keeping it separate improves maintainability by reducing complexity within gradle-modules.

* `ui-test`:
** Contains static assets for the NA Portal UI test playground.
** Used as assets for the UI test server, which initializes during UI test execution.

=== Gradle

The `{project-root}/src/frontend/play` folder builds the assets for API and Frontend. Since the repository is mainly
a maven project, and Play Framework build is supported on SBT and Gradle only, building the project with maven will
internally run the process to build the Play Framework using Gradle.

For assets bundling, Gradle will build the `{project-root}/src/frontend/web` module to get the generated assets as well
as the catalog of asset names generated. The catalog is used to identify the asset file name to load, since they have
content hash in the name, as to download the assets when there is a change, instead of getting the old version of the
assets from the cache.

The next figure shows the process of building the `{project-root}/src/frontend/play` project.

[mermaid,caption="Figure {counter:figure}."]
.Deliverable generation workflow on NA Portal Frontend and API
....
flowchart TB
    Start(( ))
    --> A[mvn install]
    --> B(./gradlew build)
    --> |before compilation| C[npm ci]
    --> D[npm run gradle]
    --> |generates| E[frontend assets]
    --> |contains assets to compile and generate *-assets.jar| B
    --> |after frontend assets generated| H[Compile]
    --> |generates| I["{module}.jar and {module}-assets.jar "]
    --> End((( )))

subgraph npm
direction TB
C
D
E
end

subgraph gradle
direction TB
B
H
I
end
....

The generated `{module}.jar` and `{module}-assets.jar`, where `{module}` is replaced by the module name
(e.g. `na-portal-frontend-go`)

==== Deliverable location

The final `.jar` files are located in `{project-root}/src/frontend/play/{module}/target/playBinary/lib`
folder, other files in `target` folder are intermediary files generated during the building of the project.

== Integrating with other products

Each module is in an iframe, meaning it can be integrated with other products if necessary.

=== Cross-origin issues

Before integrating with other modules, you need to be mindful of issues that may arise when integrating with other
modules, especially if it is in another origin.

NOTE: An origin is the protocol, host, and port part of the URL used to access it. Two URLs have the same origin if the
protocol, port (if specified), and host are the same for both. More info on
link:https://developer.mozilla.org/en-US/docs/Glossary/Origin[MDN]

If the other product is meant to be on a different origin, you will most likely need to configure the browser
link:https://developer.mozilla.org/en-US/docs/Web/Security/Same-origin_policy[Same-origin policies], such as
Cross-origin security features in the browser, such
as link:https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS[Cross-Origin Resource Sharing (CORS)] and/or
link:https://developer.mozilla.org/en-US/docs/Web/HTTP/Cross-Origin_Resource_Policy[Cross-Origin Resource Policy (CORP)]

There are other features that are needed to pass data between the product, such as authentication token, that are
generally saved in link:https://developer.mozilla.org/en-US/docs/Web/HTTP/Cookies[cookies 🍪].

Additionally, if the product is inside an iframe with a different origin, you also need to take account of possible
CORS issues when communicating with it. You need to ensure you are using the
`link:https://developer.mozilla.org/en-US/docs/Web/API/Window/postMessage#targetorigin[targetOrigin]` parameter when
calling `link:https://developer.mozilla.org/en-US/docs/Web/API/Window/postMessage[postMessage]` to an iframe.

=== Web balancer

If to connect to another server the request passes by the same web balancer, meaning that for the browser, the origin is
the same, there is no need to deal with cross-origin issues, as most security features are targeted to cross-origin
communication.

[mermaid,caption="Figure {counter:figure}."]
.Same web balancer in the same origin means no cross-origin security feature are triggered
....
flowchart TB

Browser <--> WB["Web Balancer"]

WB <--> FF["Fulfillment One"]
WB <--> NetQ["NetQ"]

....

So, if the assets in the web balancer in one product are the same as the other, configuring the web balancer
to connect to both is a good idea. You configure additional routes to move the request to the other product. In
the case of NGINX, which is the one being used in this version, you add new locations and upstream configurations
to NGINX, to point new URL paths to the other product.

If there are differences in assets in each other product, (e.g. different FUXI version), you will have to
deal with the cross-origin issue.

NOTE: Web balancer assets are used exclusively for Portal module, if an external product wants to integrate with one of
our modules, except Portal and base modules, this is a non-issue because all assets are bundled and are in the same
server as the module, meaning, each module provides all of its own assets.

=== Debugging and configuring cross-origin policies

If it is unfeasible to guarantee all requests go to the same origin, we will need to configure the cross-origin
security policies of the pages to allow integration between the product.

==== Cookies

link:https://developer.mozilla.org/en-US/docs/Web/HTTP/Cookies[Cookies] are used to pass data between multiple servers,
the most common data used is authentication data. To share information data the recommended way is to apply the
`link:https://developer.mozilla.org/en-US/docs/Web/HTTP/Cookies#define_where_cookies_are_sent[Domain]`
attribute of the cookie to the domain and use subdomains in the product, for example, setting cookie `Domain` attribute
to `corppt.com`, the cookies will be sent to `fulfillment-one.corppt.com` and `netq.corppt.com`.

Unfortunately there is no configuration for it in Portal because the session is in a separate configuration from Play
specific cookie configuration, it would be necessary to add support of `play.http.session.domain` which is already
included in `application.conf`

.Code used to create a session in `nossis-ui` repo, branch `tag/3.21.0`, file `src/play/security-adapter/app/pt/alticelabs/nossis/security/Security.java`
[source,java,caption="Code {counter:code}. "]
----
    // ...

    public static void createSession(Http.Context context, Session session) {
        context.session().put(SESSION_ID, jwtEncryption ? EncryptionManager.encrypt(session.getId()) : session.getId());
        if(PlaySecurityConfig.handleSessionIdCookie()) {
            Object providerSessionId = Attributes.get(Attribute.PROVIDER_SESSION_ID, session.getId());

            if(providerSessionId!=null) {
                boolean cookieSecure = Optional.ofNullable(ConfigFactory.load().getBoolean("play.http.session.secure")).orElse(false);
                boolean cookieHttpOnly = Optional.ofNullable(ConfigFactory.load().getBoolean("play.http.session.httpOnly")).orElse(false);
                Http.Cookie playLangCookie = Http.Cookie.builder(PlaySecurityConfig.NOSSISAAAPI_PROVIDER_SESSIONID_PARAMETER_NAME, providerSessionId.toString()).withSecure(cookieSecure).withHttpOnly(cookieHttpOnly).build();
                Http.Context.current().response().setCookie(playLangCookie);
            }
        }
        SecurityUtils.setLanguageContext(context, session);
    }

    // ...
----

NOTE: TODO: Update this section once it is supported, telling that you can set the `Domain` attribute by applying
the `play.http.session.domain` config key in
`application.conf`

==== CORS configuration

CORS is configured by enabling the Play CORS Filter. To do it, add `play.filters.cors.CORSFilter` to `application.conf`:

```
play.filters.enabled += "play.filters.cors.CORSFilter"
```

You can configure CORS with the following config variables:

- `play.filters.cors.pathPrefixes` - filter paths by a whitelist of path prefixes
- `play.filters.cors.allowedOrigins` - allow only requests with origins from a whitelist (by default all origins are allowed)
- `play.filters.cors.allowedHttpMethods` - allow only HTTP methods from a whitelist for preflight requests (by default all methods are allowed)
- `play.filters.cors.allowedHttpHeaders` - allow only HTTP headers from a whitelist for preflight requests (by default all headers are allowed)
- `play.filters.cors.exposedHeaders` - set custom HTTP headers to be exposed in the response (by default no headers are exposed)
- `play.filters.cors.supportsCredentials` - disable/enable support for credentials (by default credentials support is enabled)
- `play.filters.cors.preflightMaxAge` - set how long the results of a preflight request can be cached in a preflight result cache (by default 1 hour)
- `play.filters.cors.serveForbiddenOrigins` - enable/disable serving requests with origins not in whitelist as non-CORS requests (by default they are forbidden)


More info on
link:https://www.playframework.com/documentation/2.6.x/CorsFilter[Play CORS filter documentation]

==== Iframe security HTTP headers

There are 2 HTTP headers used for application security in iframes. The old
`link:https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Frame-Options[X-Frame-Options]`, and the new
`link:https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Security-Policy[Content-Security-Policy]`.
If the iframe and Portal are in a different origin, the X-Frame-Options must not be used because it only supports
two directives:

- `DENY`: The page cannot be displayed in a frame, regardless of the site attempting to do so.
- `SAMEORIGIN`: The page can only be displayed if all ancestor frames are same origin as the page itself.

There is another directive that is deprecated, and modern browsers will ignore the `X-Frame-Options` header completely
if it is used. That is the `ALLOW-FROM` directive.
More info on link:https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Frame-Options#allow-from_origin[MDN]

You should use instead the `link:https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Security-Policy/frame-ancestors[frame-ancestors]`
directive of link:https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Security-Policy[Content-Security-Policy].

Here are some examples of `link:https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Security-Policy[Content-Security-Policy]` headers

.Content-Security-Policy examples using frame-ancestors directive
[source,http,caption="Figure {counter:figure}. "]
```
Content-Security-Policy: frame-ancestors 'none';
Content-Security-Policy: frame-ancestors 'self';
Content-Security-Policy: frame-ancestors 'self' https://example.org https://example.com https://store.example.com;
```

The first example has the same result as `X-Frame-Options: DENY` and the second `X-Frame-Options: SAMEORIGIN`. The third
one is the same result as the deprecated `X-Frame-Options: ALLOW-FROM <origin-list>`.

Now, in this project, we have a funny interaction:
All API modules have a wrongly set `DENY` in `X-Frame-Options`, as seen on `play.filters.headers.frameOptions`
config variable on `application.conf`, however, the header is being overridden by the web balancer, as defined
on `base-ssl.conf.SAMPLE`


.NGINX part of code that sets X-Frame-Options on `base-ssl.conf.SAMPLE`
[source,,caption="Figure {counter:figure}. "]
```
    # Other security headers
        add_header X-Frame-Options "SAMEORIGIN";
        add_header X-Content-Type-Options "nosniff";
    } # ANSIBLE DO NOT CHANGE: END location
```

A good solution is to set `play.filters.headers.frameOptions` to null, as to avoid confusion, as well
to replace the `X-Frame-Options` configuration in NGINX to use `Content-Security-Policy` header to allow
the modules to be rendered in an iframe.

If a product module is to be integrated with an external product in a different origin, it needs to be ensured
that the `frame-ancestors` directive points to both the self and the product origin.

In conclusion, make sure X-Frame-Options header is not used for different origins, as it will block the iframe, and
use `frame-ancestors` directive of `Content-Security-Policy` instead.


==== Iframe communication

As written in the <<Cross-origin issues>> section, if the product is inside an iframe with a different origin,
there are possible CORS issues when in the `page <-> iframe` communication. To avoid this issue it is necessary to
ensure that `link:https://developer.mozilla.org/en-US/docs/Web/API/Window/postMessage#targetorigin[targetOrigin]`
parameter is being used when calling `link:https://developer.mozilla.org/en-US/docs/Web/API/Window/postMessage[postMessage]` to an iframe.

NOTE: There is no configuration needed for iframe communication. All modules of Fulfillment `postMessage` calls have
the `targetOrigin` parameter applied. That is, if the best practices are being followed, especially the
<<iframe-communication-best-practice, "In iframe communication, include targetOrigin in postMessage">> recommendation