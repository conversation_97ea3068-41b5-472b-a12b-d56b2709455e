.root {
  position: relative;
  border: 1px solid rgb(221, 221, 221);
  height: 675px;
}

.root[data-current-page="login"]{
  border: none;
}

#menu-nav {
  position: relative;
  z-index: 2;
}

.navbar-brand {
  cursor: pointer;
}

.operview-content {
  flex: 1;
}

.portal-page-wrap {
  height: 600px;
}

.demo-iframe {
  height: 580px;
  margin: 10px;
}

.root:not([data-current-page="overview"]) .portal-page-wrap,
.root:not([data-current-page="login"]) .demo-login,
.root:not([data-current-page="iframe"]) :is(.demo-iframe, .breadcrumb__label--module-name),
.root:not(:has(input[value="data-management"]:checked)) [data-permission="data-management"],
.root:not(:has(input[value="entities-catalog"]:checked)) [data-permission="entities-catalog"],
.root:not(:has(input[value="operations-catalog"]:checked)) [data-permission="operations-catalog"],
.root:not(:has(input[value="go"]:checked)) [data-permission="go"],
.root:not(:has(input[value="mpt"]:checked)) [data-permission="mpt"],
.root:not(:has(input[value="veacs"]:checked)) [data-permission="veacs"],
.root:not(:has(input[value="monitoring"]:checked)) [data-permission="monitoring"],
.root:not(:has(input[value="diagnostics"]:checked)) [data-permission="diagnostics"],
.root:not(:has(input[value="entities-catalog"]:checked)):not(:has(input[value="operations-catalog"]:checked)) [data-permission="entities-catalog|operations-catalog"]
{
  display: none;
}



.root[data-current-page="login"] > :is(nav, footer){
  display: none;
}

.demo-iframe {
  font-size: 4em;
  font-family: Architects Daughter, serif;
}

.demo-login {
  position: relative;
  font-family: Architects Daughter, serif;
  font-size: 2em;
  height: 100%;
  & > h1 {
    font-size: 2em;
    line-height: 300px;
    font-family: inherit;
  }
  & label {
    font-family: inherit;
    font-size: 2rem;
    display: block;
    text-align: left;
    cursor: pointer;
    border: 2px solid transparent;
    padding: 2px;
    &:has(input[type="checkbox"])::before{
      width: 1em;
      display: inline-block;
      text-align: center;
      vertical-align: center;
      font-weight: bolder;
    }
    &:has(input[type="checkbox"]:checked)::before {
      content: '🗹';
    }
    &:has(input[type="checkbox"]:not(:checked))::before {
      content: '☐';
    }
    & input[type="checkbox"] {
      display: none;
    }
  }

  & :is(.user-permissions, .login-button, label:hover){
    border: 2px solid #919090;
    border-radius: 255px 15px 225px 15px/15px 225px 15px 255px;
  }
  & .login-button {
    padding: 50px 100px;
  }
  & .user-permissions {
    display: block;
    position: absolute;
    top: 15px;
    right: 15px;
    width: 25%;
    padding: 10px;
  }
}

.demo-iframe-content {
  margin: 10px;
  height: 540px;
  line-height: 540px;
  background-size: cover;
}

.demo-login, .demo-iframe-content, .about-modal__bg, .about-modal__content {
  text-align: center;
  outline: none;
  border: 2px solid #919090;
  border-radius: 255px 15px 225px 15px/15px 225px 15px 255px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1400' height='1000' viewBox='-45 -20 90 40'%3E%3Cpath stroke-dasharray='0 0' fill='none' stroke-width='4' stroke='%23eee' d='M-52 -23 C-52.3 -23, -52.35 -23, -52.35 -23.98 M-52.35 -23 C-52.35 -23, -52.35 -23, -52.35 -23 M-52.238 -16.003 C-50.326 -18.321, -47.913 -19.4156, -45.663 -23.835 M-51.528 -16.1060 C-50.3396 -17.73, -48.709 -20.027, -45.497 -23.774 M-51.9239 -7.3672 C-48.812 -13.7365, -44.898 -17.1494, -39.334 -24.600 M-52.4993 -8.4384 C-47.217 -13.099, -42.834 -18.286, -38.236 -23.7680 M-52.859 -1.51294 C-46.258 -6.984, -39.8834 -14.0691, -32.2537 -22.77154 M-51.995 -0.40419 C-47.387 -5.8817, -41.466 -12.47846, -32.315 -23.225 M-53.422 7.344 C-41.267 -5.693, -30.460 -16.631, -25.650 -23.344 M-52.862 7.572 C-46.748 0.815, -39.961 -5.819, -25.554 -23.993 M-52.585 13.872 C-45.060 6.579, -39.040 -2.631, -19.906 -22.045 M-52.527 14.584 C-41.673 3.434, -32.056 -7.550, -19.181 -22.659 M-50.938 23.620 C-37.849 5.805, -24.983 -10.757, -11.584 -23.333 M-51.344 22.436 C-35.736 4.612, -20.758 -14.099, -13.074 -22.442 M-46.022 22.346 C-34.228 8.543, -20.615 -6.043, -5.719 -23.184 M-45.864 23.917 C-31.810 6.782, -16.755 -9.720, -5.019 -23.719 M-41.110 22.354 C-26.011 8.065, -9.501 -10.963, 0.971 -22.671 M-39.561 23.463 C-28.809 10.632, -17.019 -3.024, 1.380 -22.835 M-32.889 24.376 C-19.854 8.357, -7.352 -6.813, 8.149 -23.037 M-33.094 24.149 C-17.945 6.640, -3.861 -10.059, 7.256 -23.508 M-26.928 24.257 C-12.004 6.529, 2.858 -12.078, 13.787 -24.698 M-26.336 23.851 C-10.693 5.703, 4.882 -12.418, 14.882 -22.994 M-20.181 22.529 C-4.269 6.358, 7.776 -8.258, 21.842 -22.886 M-20.402 23.215 C-4.658 6.070, 10.169 -11.798, 20.657 -22.698 M-13.708 22.928 C-4.700 13.196, 3.055 2.953, 27.854 -23.482 M-13.935 23.874 C-5.663 14.469, 3.399 4.980, 27.409 -24.295 M-6.772 22.629 C4.479 12.156, 14.387 0.409, 33.775 -22.797 M-5.927 22.949 C9.890 4.818, 25.833 -14.181, 34.104 -23.796 M-0.969 24.769 C15.549 5.370, 31.995 -14.043, 39.416 -23.985 M0.393 23.044 C15.675 4.626, 32.066 -13.804, 40.894 -23.772 M6.232 23.954 C18.803 9.494, 32.194 -5.409, 48.088 -24.809 M6.844 23.717 C18.323 10.660, 28.649 -2.893, 47.510 -22.929 M12.777 23.742 C23.051 12.062, 34.457 -2.145, 51.671 -22.782 M13.487 23.004 C22.072 13.315, 31.711 2.897, 52.290 -22.268 M19.403 23.199 C31.761 9.815, 44.220 -3.385, 51.888 -14.417 M20.346 23.441 C28.810 14.458, 37.283 4.083, 52.030 -13.418 M26.716 21.956 C31.338 17.356, 38.169 10.998, 53.032 -5.456 M26.381 23.251 C36.816 12.595, 45.827 1.215, 52.557 -6.217 M31.820 24.422 C37.344 16.669, 42.996 10.295, 52.614 2.715 M33.534 23.066 C40.278 14.974, 47.273 6.846, 52.761 2.083 M40.369 23.177 C44.046 17.940, 49.818 13.244, 51.581 8.153 M39.285 23.264 C43.988 19.109, 47.540 14.426, 53.270 8.638 M46.248 23.499 C48.127 21.093, 49.509 19.883, 52.418 16.487 M46.861 23.416 C48.712 20.559, 51.083 18.137, 52.306 16.607'%3E%3C/path%3E%3C/svg%3E");
  background-position: center;
}



li[data-overview-tab]{
  width: 250px;
  &:not(.active) {
    cursor: pointer;
  }
}

.portal-page-wrap {
  display: flex;
  flex-direction: column;

  & > .fx-bs-row-container {
    flex: 1;
    display: flex;
  }
}

.root.about-open .about-modal {
  display: block;
}

.about-modal, .about-modal__bg {
  position: absolute;
  inset: 0
}

.about-modal__content {
  z-index: 4;
  background-color: #ddd;
  background-size: cover;
  position: absolute;
  top: 25%;
  left: calc(50% - 300px);
  width: 50%;
  height: 300px;
  line-height: 300px;
  font-size: 3em;
  font-family: Architects Daughter, serif;
}

.about-modal {
  display: none;
  z-index: 3;
}

.about-modal__bg {
  border: none;
  opacity: 50%;
  filter: brightness(0.2);
  border-radius: 0;
}