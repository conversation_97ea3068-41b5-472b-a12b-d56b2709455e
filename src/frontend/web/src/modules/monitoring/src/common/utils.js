const buttonClassesByAction = {
  show_ext_sys_interactions: 'activity-action-external-system-interaction',
  report: 'activity-action-report',
  restart: 'activity-action-restart',
  skip: 'operation-action-skip',
  rollback: 'operation-action-rollback',
  retry_new_request: 'operation-action-retry_new_request',
  retry: 'operation-action-retry',
  complete: 'operation-action-complete',
}

export function getButtonClassByAction(actionName) {
  const buttonClassByAction = actionName != null ? buttonClassesByAction[actionName] : null
  return buttonClassByAction || ''
}

const iconClassesByAction = {
  show_ext_sys_interactions: 'fuxicons fuxicons-highlighter',
  report: 'fuxicons fuxicons-planning',
  restart: 'fuxicons fuxicons-restart',
  skip: 'glyphicon glyphicon-forward',
  rollback: 'fa fa-step-backward',
  retry_new_request: 'fa fa-undo',
  retry: 'fa fa-undo',
  complete: 'far fa-check-square',
}

export function getIconClassByAction(actionName) {
  const iconClassByAction = actionName != null ? iconClassesByAction[actionName] : null
  return iconClassByAction || ''
}

const iconClassesByStatus = {
  success: 'timeline-status-success fuxicons fuxicons-success-alt fx-status-success fx-form-validator-success',
  waiting: 'timeline-status-waiting fuxicons fuxicons-in-progress fx-status-info',
  info: 'timeline-status-info fuxicons fuxicons fuxicons-in-progress fx-status-info',
  error: 'timeline-status-error fuxicons fuxicons-error text-danger',
  pending: 'timeline-status-pending fa-stack',
  none: null,
}

export function getIconClassByStatus(status) {
  const iconClassByStatus = status != null ? iconClassesByStatus[status] : null
  return iconClassByStatus || null
}
