/**
 * @typedef {object} PlayRouteResult
 * @property {string} method - route http request method
 * @property {string} url - route http request url
 *
 */

/**
 @callback PlayRoute0
 @function
 @return {PlayRouteResult}
 */

/**
 @callback PlayRoute1
 @function
 @param arg1
 @return {PlayRouteResult}
 */

/**
 @callback PlayRoute2
 @function
 @param arg1
 @param arg2
 @return {PlayRouteResult}
 */

/**
 @callback PlayRoute3
 @function
 @param arg1
 @param arg2
 @param arg3
 @return {PlayRouteResult}
 */

/**
 @name monitoringJsRoutes

 @property {PlayRoute0} na.monitoring.controllers.Application.resume
 @property {PlayRoute0} na.monitoring.controllers.Application.goToHome
 @property {PlayRoute1} na.monitoring.controllers.OrderController.view
 @property {PlayRoute1} na.monitoring.controllers.OrderController.clonePage
 @property {PlayRoute} na.monitoring.controllers.OrderController.getOrderById
 @property {PlayRoute} na.monitoring.controllers.OrderController.getOrderSummary
 @property {PlayRoute} na.monitoring.controllers.OrderController.getStates
 @property {PlayRoute} na.monitoring.controllers.OrderController.getSettings
 @property {PlayRoute} na.monitoring.controllers.OrderController.getFiltersAsJson
 @property {PlayRoute1} na.monitoring.controllers.OrderController.getGraph
 @property {PlayRoute} na.monitoring.controllers.OrderController.dataTableOperationServiceMonitoring
 @property {PlayRoute} na.monitoring.controllers.OrderController.dataTableResponseRetries
 @property {PlayRoute} na.monitoring.controllers.OrderController.dataTableDeliveryAttempts
 @property {PlayRoute} na.monitoring.controllers.OrderController.cancelOrders
 @property {PlayRoute} na.monitoring.controllers.OrderController.skipOrders
 @property {PlayRoute} na.monitoring.controllers.OrderController.rollBackOrders
 @property {PlayRoute} na.monitoring.controllers.OrderController.cancelOrdersWithRollback
 @property {PlayRoute} na.monitoring.controllers.OrderController.retryOrders
 @property {PlayRoute} na.monitoring.controllers.OrderController.removeOrders
 @property {PlayRoute} na.monitoring.controllers.OrderController.getAllowedOrderActions
 @property {PlayRoute} na.monitoring.controllers.OrderController.getOperationsByVersion
 @property {PlayRoute} na.monitoring.controllers.OrderController.getOperationById
 @property {PlayRoute} na.monitoring.controllers.OrderController.getOperationByVersionAndName
 @property {PlayRoute} na.monitoring.controllers.ActivityController.restart
 @property {PlayRoute} na.monitoring.controllers.ActivityController.skip
 @property {PlayRoute} na.monitoring.controllers.ActivityController.rollback
 @property {PlayRoute1} na.monitoring.controllers.ActivityController.retry
 @property {PlayRoute3} na.monitoring.controllers.ActivityController.complete
 @property {PlayRoute} na.monitoring.controllers.NotificationController.getNotificationsByOrderId
 @property {PlayRoute2} na.monitoring.controllers.NotificationController.retryNotificationBySubscriber
 @property {PlayRoute} na.monitoring.controllers.NotificationController.retryNotification
 @property {PlayRoute0} na.monitoring.controllers.TableController.dataTableExternalSystemInteractionsHistory
 @property {PlayRoute1} na.monitoring.controllers.Assets.versioned
 */

/** @type monitoringJsRoutes */
export const routes = global.monitoringJsRoutes

export default routes
