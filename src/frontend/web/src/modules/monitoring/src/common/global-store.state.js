export const initialState = function() {
  return {
    sessionId: '',
    allowedOrderActions: [],
    orderListing: {
      filterData: [], // fields shown in the bar
      searchDateRange: {
        startDate: null,
        endDate: null
      },
      searchDateReference: null,
      idSearchData: {
        currentContext: null,
        fieldValue: ''
      },
      headerData: {
        visibleView: null,
        formMode: true
      },
      tableData: {
        pageLength: 0,
        pageNumber: 0,
        sortInfo: null
      }
    }
  }
}

export const getters = {
  // get all filter values as a Map of ( property -> value )
  orderListingData: function (state) {
    return state.orderListing
  },
  saveData: function (state) {
    return JSON.stringify(state)
  },
  allowedOrderActions: function (state) {
    return state.allowedOrderActions
  }
}

function setOrderListingData(state, data) {
  if (data == null) {
    return state
  }
  const filters = data.filterData || state.orderListing.filterData
  const searchDateRange = data.searchDateRange || state.orderListing.searchDateRange
  const idSearchData = data.idSearchData || state.orderListing.searchDateRange
  const headerData = data.headerData || state.orderListing.headerData
  const tableData = data.tableData || state.orderListing.tableData
  const searchDateReference = data.searchDateReference || state.orderListing.searchDateReference
  return Object.assign({}, state, {
    orderListing: Object.assign({}, state.orderListing, {
      filterData: filters,
      searchDateRange,
      idSearchData,
      headerData,
      tableData,
      searchDateReference
    })
  })
}

export const setters = {
  loadData: function(state, saveData) {
    const saveDataAsObj = (typeof saveData === 'string' && saveData !== '') ? JSON.parse(saveData) : {}
    const firstState = initialState()
    const state1 = setOrderListingData(firstState, saveDataAsObj.orderListing)
    return Object.assign({}, state1, {
      sessionId: saveDataAsObj.sessionId
    })
  },
  setOrderListingData,
  setOrderListingTableData: function (state, tableData) {
    if (state.orderListing.tableData.pageLength === tableData.pageLength &&
        state.orderListing.tableData.pageNumber === tableData.pageNumber &&
        state.orderListing.tableData.sortInfo === tableData.sortInfo) {
      return state
    }

    return Object.assign({}, state, {
      orderListing: Object.assign({}, state.orderListing, {
        tableData: Object.assign({}, state.orderListing.tableData, {
          pageLength: tableData.pageLength,
          pageNumber: tableData.pageNumber,
          sortInfo: tableData.sortInfo
        })
      })
    })
  },

  setOrderListingHeaderFormMode: function (state, formMode) {
    const formModeAsBoolean = !!formMode

    if (state.orderListing.headerData.formMode === formModeAsBoolean) {
      return state
    }

    return Object.assign({}, state, {
      orderListing: Object.assign({}, state.orderListing, {
        headerData: Object.assign({}, state.orderListing.headerData, {
          formMode: formModeAsBoolean
        })
      })
    })
  },

  setAllowedActions: function (state, allowedOrderActions) {
    if (!Array.isArray(allowedOrderActions) || !allowedOrderActions.every(item => typeof item === 'string')) {
      return state
    }
    return Object.assign({}, state, {
      allowedOrderActions
    })
  },

  resetIfNewSession: function (state, sessionId) {
    if (state.sessionId === sessionId) {
      return state
    }

    return Object.assign({}, initialState(), {
      sessionId
    })
  }
}
