<x-i18n-attr-container class="page page--monitoring-order-create">
    <div class="page__header">
        <div class="fx-constraint two-rows">
            <div class="pull-left">
                <span class="fx-info-icon">
                    <i class="fuxicons fuxicons-monitoring"></i>
                </span>
                <h1 class="page__title">
                    <x-i18n key="na.portal.monitoring.order.create.label"></x-i18n>
                </h1>
            </div>
        </div>
        <div class="pull-right">
            <a class="btn btn-primary" data-page-action="submit-create-order" id="forms_button_save">
                <x-i18n key="na.portal.monitoring.order.create.button.label.execute"></x-i18n>
            </a>
            <a class="btn btn-default " data-page-action="cancel-create-order" id="forms_button_cancel">
                <x-i18n key="na.button.label.cancel"></x-i18n>
            </a>
        </div>
    </div>
        <x-tab-container class="page__body">
            <x-tab data-i18n-label="na.portal.monitoring.order.create.definition" class="tab tab--monitoring-order-create-definition">
                <x-splitter>
                    <x-na-monitoring-order-create-sidebar slot="left" class="page__sidebar"></x-na-monitoring-order-create-sidebar>
                    <x-na-monitoring-order-create-dynamic-form class="page__operation-form" slot="right"></x-na-monitoring-order-create-dynamic-form>
                </x-splitter>
            </x-tab>
        </x-tab-container>
</x-i18n-attr-container>
