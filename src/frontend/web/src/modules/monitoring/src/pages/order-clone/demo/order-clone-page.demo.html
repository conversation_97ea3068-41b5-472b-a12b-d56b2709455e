<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title><%= title %></title>
<%= css %></head>
<body>
    <%= navbar %>
    <div class='app'>
        <h1><%= title %></h1>

        <div class="demo-container demo-container--demo-1">
            <x-breadcrumb></x-breadcrumb>
            <div class="element-container">
                <x-na-monitoring-order-clone-page data-from-order="ed0bace0-45a4-479d-8c74-f586ba0188c713"></x-na-monitoring-order-clone-page>
            </div>
        </div>

        <pre><code class="code--page xml">&lt;x-na-monitoring-order-clone-page data-from-order="<span class="order-id-text--demo-1">ed0bace0-45a4-479d-8c74-f586ba0188c713</span>"&gt;&lt;/x-na-monitoring-order-clone-page&gt;</code></pre>

        <div style="font-size: 1.2rem; text-align: center">
            Order to test: <select class="select select--demo-1">
            <option value="ed0bace0-45a4-479d-8c74-f586ba0188c713" selected> old style order from demo environment ( id: ed0bace0-45a4-479d-8c74-f586ba0188c713 )</option>
            <option value="ed0bace0-45a4-479d-8c74-f586ba0188c714"> new style order from demo environment ( id: ed0bace0-45a4-479d-8c74-f586ba0188c714 )</option>
            <option value="2b2fa783-dd8b-404e-8dec-4bc41b82a7da21"> order from CI ( id: 2b2fa783-dd8b-404e-8dec-4bc41b82a7da21 )</option>
            <option value="2b2fa783-dd8b-404e-8dff-4bc41b82a7da21"> order from CI with deleted operation, the operation name is "deletoper" ( id: 2b2fa783-dd8b-404e-8dff-4bc41b82a7da21 )</option>
            <option value="non-existent-order"> non existent order ( id: non-existent-order )</option>
        </select>

            <p> Changing order will change the attribute on &lt;x-na-monitoring-order-clone-page&gt; </p>
        </div>

        <div class="container">
            <h3>Imports</h3>
            <pre><code class="code--import lang-js"></code></pre>
        </div>



    </div>
<%= js %></body>
</html>