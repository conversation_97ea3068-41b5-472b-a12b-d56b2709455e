import { isPlainObject, areEqualObjects } from '~utils/object-utils.util'
import { arrayEquals } from '~utils/array-utils.util'
import { selector } from '~utils/selector.util'
import { deepEquals } from '~utils/deep-equals.util'

const hasOwn = (obj, key) => Object.prototype.hasOwnProperty.call(obj, key)
const isStringArray = (obj) => Array.isArray(obj) && obj.every(field => typeof field === 'string')
const emptyArray = Object.freeze([])
const emptyObject = Object.freeze({})

const initialStateObj = {
  selectedSystem: '',
  selectedUser: '',
  systemInfo: emptyObject,
  /* "system name": {
                 "versionName": opername
                 "versionState": operstate // only if we need to print the version state
                 "versionStateLabel": operstateLabel // only if we need to print the version state
                 "userList": [..."usernames"]
             } */

  selectedOperations: emptyArray, /* array of operationId */
  selectedOperationForm: ''/* operationId */,
  operationInfo: emptyObject,
  /* {{ operation id }}: {
                name: "string",
                type: "CFS" | "RFS" | "RESOURCE"
                entityName: string
                valid: boolean
                id: string
            } */

  operationFormInfo: emptyObject,
  /* {{ operation id }}: {
                 loadStatus: "unloaded" | "loading" | "loaded"
                 formFields: []
             } */

  orderFormValues: {
    externalId: '',
    category: '',
    relatedParty: emptyArray
  },
  orderFormOptionalFields: {
    relatedParty: true
  },
  operationFormOptionalFields: emptyObject,
  /*
    {{ operation id }}: {
        relatedParty: true,
        place: true
      }
*/
  operationFormValues: emptyObject,
  /* {{ operation id }}: {
                specificCharacteristics: {[name]: value}
                baseCharacteristics: {
                    id: string
                }
            } */
  allowedVersionStates: emptyArray
}

export const operationFormDefaultOptionalFields = {
  relatedParty: true,
  place: true
}

export const initialState = function() {
  return initialStateObj
}

const getSelectedOperations = state => state.selectedOperations
const getSelectedOperationForm = state => state.selectedOperationForm
const getOperationFormValues = state => state.operationFormValues
const getOperationInfo = state => state.operationInfo
const getOperationFormInfo = state => state.operationFormInfo

const getOrderFormValues = state => state.orderFormValues
const orderFormOptionalFields = state => state.orderFormOptionalFields

const operationFormOptionalFields = state => state.operationFormOptionalFields

const selectedOperationFormOptionalFields = selector(
  [getSelectedOperationForm, operationFormOptionalFields],
  (selectedOperationForm, operationFormOptionalFields) => {
    if (selectedOperationForm === '') {
      return emptyObject
    }
    return operationFormOptionalFields[selectedOperationForm] || operationFormDefaultOptionalFields
  }
)

const getSelectedSystem = state => state.selectedSystem

const getAllowedVersionStates = state => state.allowedVersionStates

const selectedOperationFormInfo = selector(
  [getSelectedOperationForm, getOperationFormInfo],
  (selectedOperationForm, operationFormInfo) => {
    if (selectedOperationForm === '' || !hasOwn(operationFormInfo, selectedOperationForm)) {
      return emptyObject
    }
    return operationFormInfo[selectedOperationForm]
  }
)

const getOperationFormStatus = selector(
  [getSelectedOperations, getOperationFormInfo, getOperationFormValues],
  (selectedOperations, operationFormInfo, operationFormValues) => {
    return Object.fromEntries(
      selectedOperations.map(operation => {
        const result = {
          ok: false
        }

        if (
          !hasOwn(operationFormValues, operation) ||
                  !hasOwn(operationFormInfo, operation)
        ) {
          return [operation, result]
        }
        const { specificCharacteristics, baseCharacteristics } = operationFormValues[operation]
        if (!baseCharacteristics.id) {
          return [operation, result]
        }

        const { formFields } = operationFormInfo[operation]

        for (const field of formFields) {
          const { minMultiplicity, fieldName } = field
          if (minMultiplicity > 0 && !specificCharacteristics[fieldName]) {
            return [operation, result]
          }
        }

        result.ok = true
        return [operation, result]
      })
    )
  }
)

const selectedOperationFormFields = selector(
  [getSelectedOperationForm, getOperationFormInfo],
  (selectedOperationForm, operationFormInfo) => {
    if (selectedOperationForm === '' || !hasOwn(operationFormInfo, selectedOperationForm)) {
      return emptyArray
    }
    const { formFields } = operationFormInfo[selectedOperationForm]
    if (formFields.length <= 0) {
      return emptyArray
    }

    return formFields
  }
)

const selectedOperationFormFieldValues = selector(
  [getSelectedOperationForm, getOperationFormValues],
  (selectedOperationForm, operationFormValues) => {
    if (selectedOperationForm === '' || !hasOwn(operationFormValues, selectedOperationForm)) {
      return emptyObject
    }

    return operationFormValues[selectedOperationForm]
  }
)

const selectedOperationInfo = selector(
  [getSelectedOperations, getOperationInfo],
  (selectedOperations, operationInfo) => selectedOperations
    .filter(id => hasOwn(operationInfo, id))
    .map(id => operationInfo[id])
)

const getSystemInfo = state => state.systemInfo
const systemNames = selector([getSystemInfo], (systemInfo) => Object.keys(systemInfo))

const currentVersionStateAllowed = selector(
  [getSelectedSystem, getAllowedVersionStates, getSystemInfo],
  (selectedSystem, allowedVersionStates, systemInfo) => selectedSystem === '' ||
            allowedVersionStates.length <= 0 ||
            allowedVersionStates.includes(systemInfo[selectedSystem]?.versionState)
)

const dataToSubmit = (state) => {
  const { orderFormValues, selectedOperations, operationFormValues, operationInfo, selectedSystem, selectedUser, orderFormOptionalFields, operationFormOptionalFields } = state
  const { externalId, category, relatedParty } = orderFormValues

  const toOrderItem = (operationId) => {
    const info = operationInfo[operationId]
    const { specificCharacteristics, baseCharacteristics, place, relatedParty } = operationFormValues[operationId]
    const serviceCharacteristic = Object
      .entries(specificCharacteristics)
      .map(([name, value]) => ({ name, value }))
    let placeData = []
    const operationOptionalFields = operationFormOptionalFields[operationId] ?? operationFormDefaultOptionalFields

    if (operationOptionalFields.place) {
      place.forEach((val) => {
        const { placeRole, properties } = val
        if (placeRole || properties.length > 0) {
          placeData = [...placeData, {
            ...(placeRole ? { role: placeRole } : {}),
            property: Object.entries(properties).map(([key, value]) => ({ name: key, value }))
          }]
        }
      })
    }

    let relatedPartyData = []

    if (operationOptionalFields.relatedParty) {
      relatedParty.forEach((val) => {
        const { relatedPartyId, relatedPartyRole, properties } = val
        if (relatedPartyId || relatedPartyRole || properties.length > 0) {
          relatedPartyData = [...relatedPartyData, {
            ...(relatedPartyId ? { id: relatedPartyId } : {}),
            ...(relatedPartyRole ? { role: relatedPartyRole } : {}),
            property: Object.entries(properties).map(([key, value]) => ({ name: key, value }))
          }]
        }
      })
    }

    return {
      action: info.name, // operation name
      serviceSpecification: {
        id: info.entityName // operation entity
      },
      service: {
        id: baseCharacteristics.id,
        category: info.type,
        place: placeData,
        relatedParty: relatedPartyData,
        serviceCharacteristic
      }
    }
  }

  const orderData = {
    externalId,
    category,
    relatedParty: emptyArray,
    orderItem: selectedOperations.map(toOrderItem)
  }

  if (orderFormOptionalFields.relatedParty) {
    relatedParty.forEach((val) => {
      const { relatedPartyId, relatedPartyRole, properties } = val
      if (relatedPartyId || relatedPartyRole || properties.length > 0) {
        orderData.relatedParty = [...orderData.relatedParty, {
          ...(relatedPartyId ? { id: relatedPartyId } : {}),
          ...(relatedPartyRole ? { role: relatedPartyRole } : {}),
          property: Object.entries(properties).map(([key, value]) => ({ name: key, value }))
        }]
      }
    })
  }

  return {
    system: selectedSystem,
    user: selectedUser,
    orderData
  }
}

export const getters = {
  selectedOperationForm: getSelectedOperationForm,
  selectedOperationFormInfo,
  selectedOperationFormFields,
  selectedOperationFormFieldValues,
  selectedOperations: getSelectedOperations,
  operationInfo: getOperationInfo,
  operationFormInfo: getOperationFormInfo,
  operationFormStatus: getOperationFormStatus,
  orderFormValues: getOrderFormValues,
  orderFormOptionalFields,
  operationFormOptionalFields,
  selectedOperationFormOptionalFields,
  selectedOperationInfo,
  systemInfo: getSystemInfo,
  systemNames,
  dataToSubmit,
  selectedSystem: getSelectedSystem,
  allowedVersionStates: getAllowedVersionStates,
  currentVersionStateAllowed,
}

function selectOperations(state, selectedOperations) {
  if (!isStringArray(selectedOperations) || arrayEquals(state.selectedOperations, selectedOperations)) {
    return state
  }
  if (selectedOperations.length <= 0) {
    return {
      ...state,
      selectedOperationForm: '',
      selectedOperations: emptyArray
    }
  }
  const selectedOperationForm = selectedOperations.includes(state.selectedOperationForm)
    ? state.selectedOperationForm : selectedOperations[0]

  return {
    ...state,
    selectedOperationForm,
    selectedOperations
  }
}

function selectOperationForm(state, selectedOperationForm) {
  if (typeof selectedOperationForm !== 'string' ||
    selectedOperationForm === state.selectedOperationForm ||
    !state.selectedOperations.includes(selectedOperationForm)
  ) {
    return state
  }
  return {
    ...state,
    selectedOperationForm,
  }
}

function updateCurrentOperationFormGroupValue (state, inputData) {
  const { selectedOperationForm } = state
  if (selectedOperationForm === '' || !isPlainObject(inputData)) {
    return state
  }

  const { fieldName, fieldValue, groupName } = inputData
  if (typeof fieldName !== 'string' || fieldName === '') {
    return state
  }
  if (groupName !== 'places' && groupName !== 'relatedParties') {
    return state
  }

  const currentOperationFormValues = state.operationFormValues[selectedOperationForm] || {
    specificCharacteristics: {},
    baseCharacteristics: { id: '' },
    place: [],
    relatedParty: []
  }

  return {
    ...state,
    operationFormValues: {
      ...state.operationFormValues,
      [selectedOperationForm]: {
        ...currentOperationFormValues,
        [fieldName]: fieldValue
      }
    }
  }
}

function updateCurrentOperationFormValue (state, inputData) {
  const { selectedOperationForm } = state
  if (selectedOperationForm === '' || !isPlainObject(inputData)) {
    return state
  }

  const { fieldName, fieldValue, groupName } = inputData
  if (typeof fieldName !== 'string' || fieldName === '') {
    return state
  }
  if (!(['specificCharacteristics', 'baseCharacteristics'].includes(groupName))) {
    return state
  }
  const currentOperationFormValues = state.operationFormValues[selectedOperationForm] || {
    specificCharacteristics: {},
    baseCharacteristics: { id: '' },
    place: [],
    relatedParty: []
  }

  if (currentOperationFormValues[groupName][fieldName] === fieldValue) {
    return state
  }

  return {
    ...state,
    operationFormValues: {
      ...state.operationFormValues,
      [selectedOperationForm]: {
        ...currentOperationFormValues,
        [groupName]: {
          ...currentOperationFormValues[groupName],
          [fieldName]: fieldValue
        }
      }
    }
  }
}

export const setters = {
  resetWithSystemInfo: (state, systemInfo) => ({
    ...initialStateObj,
    systemInfo
  }),
  selectOperations,
  selectOperationForm,
  updateOperationInfo: (state, operationInfo) => {
    if (!isPlainObject(operationInfo)) {
      return state
    }
    const changes = {}
    for (const [key, value] of Object.entries(operationInfo)) {
      if (!deepEquals(state.operationInfo[key], operationInfo[key])) {
        changes[key] = value
      }
    }
    return Object.keys(changes).length <= 0 ? state : {
      ...state,
      operationInfo: {
        ...state.operationInfo,
        ...changes
      }
    }
  },
  updateOperationFormInfo: (state, operationFormUpdate) => {
    if (!isPlainObject(operationFormUpdate)) {
      return state
    }

    const { id, formInfo } = operationFormUpdate

    if (typeof id !== 'string' || id === '') {
      return state
    }

    const currentOperationFromInfo = state.operationFormInfo[id] || {}
    if (deepEquals(currentOperationFromInfo, formInfo)) {
      return state
    }

    return {
      ...state,
      operationFormInfo: {
        ...state.operationFormInfo,
        [id]: formInfo
      }
    }
  },
  updateOrderFormValue: (state, inputData) => {
    const orderFormValues = { ...state.orderFormValues, [inputData.name]: inputData.value }

    return {
      ...state,
      orderFormValues
    }
  },
  updateOrderFormOptionalFields: (state, optionalFields) => {
    if (!isPlainObject(optionalFields)) {
      return state
    }

    const updatedOptionalFields = Object.fromEntries(
      Object.entries(state.orderFormOptionalFields).map(([name, enabled]) => {
        const newStatus = optionalFields[name]
        const newField = typeof newStatus === 'boolean' ? newStatus : enabled
        return [name, newField]
      })
    )

    if (areEqualObjects(state.orderFormOptionalFields, updatedOptionalFields)) {
      return state
    }

    return {
      ...state,
      orderFormOptionalFields: updatedOptionalFields
    }
  },
  updateSelectedOperationFormOptionalFields: (state, optionalFields) => {
    const { selectedOperationForm } = state

    if (!isPlainObject(optionalFields)) {
      return state
    }

    const selectedOptionalFields = state.operationFormOptionalFields[selectedOperationForm]

    const updatedOptionalFields = Object.fromEntries(
      Object.entries(selectedOptionalFields || operationFormDefaultOptionalFields)
        .map(([name, enabled]) => {
          const newStatus = optionalFields[name]
          const newField = typeof newStatus === 'boolean' ? newStatus : enabled
          return [name, newField]
        })
    )

    if (selectedOptionalFields && areEqualObjects(selectedOptionalFields, updatedOptionalFields)) {
      return state
    }

    return {
      ...state,
      operationFormOptionalFields: {
        ...state.operationFormOptionalFields,
        [selectedOperationForm]: updatedOptionalFields
      }
    }
  },
  updateCurrentOperationFormGroupValue,
  setOperationsWithFormValues: (state, operationsWithFormValues) => {
    if (!isPlainObject(operationsWithFormValues)) {
      return state
    }
    const operationsToUpdate = Object.keys(operationsWithFormValues)

    let currentState = state
    currentState = selectOperations(currentState, operationsToUpdate)

    for (const operation of operationsToUpdate) {
      const formValues = operationsWithFormValues[operation]
      currentState = selectOperationForm(currentState, operation)
      currentState = updateCurrentOperationFormGroupValue(currentState, { fieldName: 'relatedParty', groupName: 'relatedParties', fieldValue: formValues.relatedParty })
      currentState = updateCurrentOperationFormGroupValue(currentState, { fieldName: 'place', groupName: 'places', fieldValue: formValues.place })
      for (const [key, value] of Object.entries(formValues.baseCharacteristics ?? {})) {
        currentState = updateCurrentOperationFormValue(currentState, { fieldName: key, groupName: 'baseCharacteristics', fieldValue: value })
      }
      for (const [key, value] of Object.entries(formValues.specificCharacteristics ?? {})) {
        currentState = updateCurrentOperationFormValue(currentState, { fieldName: key, groupName: 'specificCharacteristics', fieldValue: value })
      }
    }
    currentState = selectOperationForm(currentState, getSelectedOperationForm(state))
    return currentState
  },

  updateCurrentOperationFormValue,
  selectSystem: (state, newSystem) => {
    if (typeof newSystem !== 'string' ||
                newSystem === state.selectedSystem ||
                !hasOwn(state.systemInfo, newSystem)
    ) {
      return state
    }

    const userList = state.systemInfo[newSystem].userList
    const selectedUser = userList.length === 1 ? userList[0] : ''
    return {
      ...state,
      selectedUser,
      selectedSystem: newSystem,
      selectedOperations: emptyArray,
      operationInfo: emptyObject,
      operationFormInfo: emptyObject,
      operationFormValues: emptyObject
    }
  },
  selectedUser: (state, newUser) => {
    if (typeof newUser !== 'string' ||
                newUser === state.selectedUser
    ) {
      return state
    }
    return { ...state, selectedUser: newUser }
  },
  allowedVersionStates: (state, allowedVersionStates) => {
    return { ...state, allowedVersionStates }
  },
  reset: () => initialStateObj
}
