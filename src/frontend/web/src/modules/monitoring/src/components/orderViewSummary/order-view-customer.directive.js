import { monitoringModule } from '~monitoring/src/monitoring.angular-module'
import { providers } from '~monitoring/src/monitoring-providers'
import { store as orderStore } from '~monitoring/src/components/orderView/order-view.store'
import { iconCustomerType } from '~monitoring/src/components/orderSearchTable/order-list-table-customer.datatable-renderer'

monitoringModule.directive(providers.components.orderView.directives.customer, [
  function () {
    return {
      restrict: 'A',
      priority: 0,
      terminal: false,
      link: function (scope, element) {
        const order = orderStore.getters.orderData

        element.addClass(function () {
          return iconCustomerType(order.customerType)
        })
      }
    }
  }])
