import { monitoringModule } from '~monitoring/src/monitoring.angular-module'
import { providers } from '~monitoring/src/monitoring-providers'
import { i18n } from '~utils/i18n'
import { i18n as monitoringI18n } from '~monitoring/src/common/i18n.service'
import { formatFuller, formatShorterTimeDelta } from '~monitoring/src/common/date-formatter'
import { store as orderStore } from '~monitoring/src/components/orderView/order-view.store'
import { getIconClassByStatus, getIconClassByAction, getButtonClassByAction } from '~monitoring/src/common/utils'
import { htmlEscape } from '~utils/string.util'

monitoringModule.directive(providers.components.orderViewExecutionPage.directives.executionTimeline, [
  function () {
    return {
      restrict: 'A',
      terminal: false,
      link: function (scope, element) {
        let currentOrderItem = scope.executionStore.getters.currentOrderItem
        if (currentOrderItem.id != null && scope.executionStore.getters.execution.timeline != null) {
          feedTimeline(element, scope.executionStore.getters.execution.timeline)
        }

        scope.executionStore.listenStateChange(function (newStore, oldStore) {
          if (
            scope.executionStore.getters.currentOrderItem.id != null &&
                            newStore.getters.execution.timeline !== oldStore.getters.execution.timeline
          ) {
            currentOrderItem = scope.executionStore.getters.currentOrderItem
            feedTimeline(element, newStore.getters.execution.timeline)
          }
        })

        function feedTimeline(element, nodes) {
          element.empty()
          if (!jQuery.isEmptyObject(nodes)) {
            const activities = nodes.map(nodeToActivity)

            activities.forEach(function (activity) {
              element.append(renderActivity(activity))
            })
          }

          function nodeToActivity(node) {
            function NodeActivity(timelineActivity) {
              const MILLIS_IN_SECOND = 1000
              const activity = Object.assign(timelineActivity)

              function hasError() {
                return activity.status && activity.status === 'error'
              }

              return {
                hasError,
                getId: function () {
                  return activity.id
                },
                getName: function () {
                  return activity.name
                },
                getErrorMessage: function () {
                  return activity.errorMessage
                },
                getIcon: function () {
                  return activity.icon
                },
                getFulltime: function () {
                  return activity.startDate !== null ? formatFuller(activity.startDate, i18n, moment) : ''
                },
                hasToggler: function () {
                  return activity.operations.length > 0
                },
                getDuration: function () {
                  if (activity.startDate === null) {
                    return null
                  }
                  const endDate = activity.endDate || Date.now()
                  return formatShorterTimeDelta((endDate - activity.startDate) / MILLIS_IN_SECOND)
                },
                getStatusDescriptionClass: function () {
                  let statusClass = ''
                  if (hasError() && activity.leaf) {
                    statusClass = 'status_error'
                  } else if (activity.status && activity.status === 'pending' && activity.leaf) {
                    statusClass = 'status_pending'
                  }
                  return statusClass
                },
                getStartDate: function () {
                  return activity.startDate
                },
                getActions: function () {
                  const actions = []
                  activity.actions.filter(action => action !== 'report').forEach(function (action) {
                    actions.push({
                      name: action,
                      buttonAttributesMap: {
                        classes: getButtonClassByAction(action),
                        title: monitoringI18n('order.tooltip.' + action.toLowerCase()),
                        attributes: 'data-activity-id="' + activity.id + '" data-action="' + action + '"'
                      },
                      iconAttributesMap: {
                        classes: getIconClassByAction(action),
                        iconTitle: monitoringI18n('order.tooltip.' + action.toLowerCase())
                      }
                    })
                  })
                  return actions
                },
                getReports: function () {
                  const reports = []
                  activity.actions.filter(action => action === 'report').forEach(function (action) {
                    reports.push({
                      name: action,
                      buttonAttributesMap: {
                        classes: getButtonClassByAction(action),
                        title: monitoringI18n('order.tooltip.' + action.toLowerCase()),
                        attributes: 'data-activity-id="' + activity.id + '" data-action="' + action + '"'
                      },
                      iconAttributesMap: {
                        classes: getIconClassByAction(action),
                        iconTitle: monitoringI18n('order.tooltip.' + action.toLowerCase())
                      }
                    })
                  })
                  return reports
                },
                getOperations: function () {
                  const operations = []
                  activity.operations.forEach(function (operation) {
                    const iconAttributesMap = {}
                    const buttonAttributesMap = {}
                    operation.actions.forEach(function (action) {
                      buttonAttributesMap[action] = {
                        classes: getButtonClassByAction(action),
                        title: monitoringI18n('order.tooltip.' + action.toLowerCase())
                      }
                      iconAttributesMap[action] = {
                        classes: getIconClassByAction(action),
                        iconTitle: monitoringI18n('order.tooltip.' + action.toLowerCase()),
                      }
                    })
                    operations.push({
                      iconAttributesMap,
                      buttonAttributesMap,
                      ...operation
                    })
                  })
                  return operations
                },
                hasOperations: function () {
                  return activity.operations.length > 0
                },
                hasActions: function () {
                  const orderInfo = orderStore.getters.orderData
                  return activity.actions.length > 0 && orderInfo.actionsPermission
                },
                hasReports: function () {
                  return activity.actions.filter(action => action === 'report').length > 0
                },
                getDescription: function () {
                  return activity.description
                },
                getType: function () {
                  return activity.type.toLowerCase()
                },
                hasFinished: function () {
                  return activity.startDate !== null && activity.endDate !== null
                },
                getStatusIconClass: function () {
                  return getIconClassByStatus(activity.status)
                },
                isNotification: function () {
                  return activity.type === 'NOTIFICATION'
                },
                isInProgress: function () {
                  return activity.status === 'waiting'
                },
                getNotificationDetails: function () {
                  if (!this.isNotification()) {
                    return {}
                  }
                  return activity.detail
                }
              }
            }

            return new NodeActivity(node)
          }
        }

        /**
                     * Render timeline activity (icon, line, description, operation nodes)
                     *
                     * @param activity
                     * @returns {string|html}
                     */
        function renderActivity(activity) {
          function renderIconActivity(iconClass) {
            if (iconClass != null) {
              if (iconClass.indexOf('pending') !== -1) {
                return '<span class="' + iconClass + '">' +
                        '  <i class="fas fa-circle fa-stack-2x fx-form-validator-warning"></i>' +
                        '  <i class="fas fa-hourglass-half text-white fa-stack-1x"></i>' +
                        '</span>'
              }
              return renderIcon(iconClass)
            }
            return ''
          }

          function renderIcon(iconClass, title, attributes) {
            title = title || ''
            attributes = attributes || ''
            return `<i class="${iconClass}" title="${htmlEscape(title)}" ${attributes}></i>`
          }

          function renderOperationActionButton(iconClass, title, buttonClasses, activityId, operationId) {
            const icon = renderIcon(iconClass)
            const activityIdHtml = htmlEscape(activityId)
            const operationIdHtml = htmlEscape(operationId)
            const titleHtml = htmlEscape(title)
            return `<button class="${buttonClasses}" title="${titleHtml}" data-activity-id="${activityIdHtml}" data-operation-id="${operationIdHtml}">${icon}</button>&nbsp;`
          }

          function renderOperations(operations) {
            function renderActions(operation) {
              const actionsHtml = ['<span class="activity-node__actions">']
              operation.actions.forEach(function (action) {
                const icon = operation.iconAttributesMap[action]
                const button = operation.buttonAttributesMap[action]
                actionsHtml.push(renderOperationActionButton(icon.classes, button.title, button.classes, activity.getId(), operation.id))
              })
              actionsHtml.push('</span>')
              return actionsHtml.join('')
            }

            const html = ['<div class="activity-nodes toggle">']
            operations.forEach(function (operation) {
              html.push('' +
                    '<div class="activity-node-container">' +
                    '   <span class="activity-node activity-node__status-error">' +
                    '       <span>' + operation.message + '&nbsp;</span>' +
                    '   </span>' + renderActions(operation) +
                    '</div>'
              )
            })
            html.push('</div>')
            return html.join('')
          }

          function renderActivityActionButton(action) {
            const { classes: iconClass, iconTitle } = action.iconAttributesMap
            const { classes: buttonClass, attributes, title } = action.buttonAttributesMap
            const icon = renderIcon(iconClass, iconTitle)
            const titleHtml = htmlEscape(title)
            return `<button class="${buttonClass}" title="${titleHtml}" ${attributes}>${icon}</button>&nbsp;`
          }

          function renderActivityActionsAndReports() {
            const activityHtml = []
            if (activity.hasReports() || activity.hasActions()) {
              activityHtml.push('<div class="node__actions actions">')
              if (activity.hasReports()) {
                activityHtml.push(...activity.getReports().map(renderActivityActionButton))
              }
              if (activity.hasActions()) {
                activityHtml.push(...activity.getActions().map(renderActivityActionButton))
              }
              activityHtml.push('</div>')
            }
            return activityHtml.join('')
          }

          function renderActivityDescription() {
            function resolverActivityTag(activityType) {
              switch (activityType.toUpperCase()) {
                case 'EXTERNAL_SYSTEM_INTERACTION':
                case 'EVENT':
                  return 'a'
                default:
                  return 'span'
              }
            }

            const activityHtml = ['<div class="description ' + activity.getStatusDescriptionClass() + '">']
            const icon = activity.getIcon()
            if (icon != null && icon.trim() !== '') {
              activityHtml.push('<i class="' + icon + '"></i>')
            }
            const tag = resolverActivityTag(activity.getType())
            activityHtml.push('<' + tag + ' data-activity-id="' + activity.getId() + '" class="' + activity.getType() + '">' + activity.getDescription() + '</' + tag + '>')

            if (activity.isInProgress()) {
              activityHtml.push(`<div class="timestamp"><span class="duration">${activity.getDuration()}</span>${activity.getFulltime()}</div>`)
            } else if (activity.hasError()) {
              activityHtml.push(`<div class="error-message">${activity.getErrorMessage()}</div>`)
              activityHtml.push(`<div class="timestamp">${activity.getFulltime()}</div>`)
            } else if (activity.hasFinished()) {
              activityHtml.push(`<div class="timestamp"><span class="duration">${activity.getDuration()}</span>${activity.getFulltime()}</div>`)
            }

            activityHtml.push('</div>')
            return activityHtml.join('')
          }

          function renderActivityDetails() {
            function renderStateIcon(failed) {
              return failed ? '<i class="fuxicons-error fx-status-error text-danger"></i>'
                : '<i class="fuxicons fuxicons-success-alt fx-status-success fx-form-validator-success"></i>'
            }

            function renderDetailResume(numberOfSystems, numberOfIssues) {
              const systemsResume = numberOfSystems > 1 ? monitoringI18n('na.portal.monitoring.timeline.activity.notification.resume.plural', numberOfSystems)
                : monitoringI18n('na.portal.monitoring.timeline.activity.notification.resume')

              const resumeHtml = ["<summary class='notification-detail__detail-resume'>"]

              resumeHtml.push('<i class="caret"></i>')
              resumeHtml.push('<i class="caret collapsed"></i>')
              resumeHtml.push(`<span class="detail-resume__systems-count">${systemsResume}</span>`)

              if (numberOfIssues > 0) {
                resumeHtml.push(`
                        <div class="detail-resume__errors">
                            <i class="fuxicons-error"></i>
                            <span>${monitoringI18n('na.portal.monitoring.timeline.activity.notification.resume.issues', numberOfIssues, numberOfSystems)}</span>
                        </div>
                        `)
              }
              resumeHtml.push('</summary>')
              return resumeHtml.join('')
            }

            const numberOfSystems = activity.getNotificationDetails().systemSubscribers.length

            if (numberOfSystems === 0) {
              return ''
            }

            let numberOfIssues = 0
            const details = activity.getNotificationDetails()

            const comparator = new Intl.Collator(undefined, { numeric: true })

            const subscribers = [...details.systemSubscribers]
              .sort((a, b) => comparator.compare(a, b))
              .map(function (subscriber) {
                if (subscriber.failed) {
                  numberOfIssues++
                }
                return ` 
                      <li class="notification-subscriber"
                         data-notification-id="${details.notificationId}" 
                         data-system-name="${subscriber.name}"
                      >
                         <div class="notification-subscriber__state">
                            ${renderStateIcon(subscriber.failed)}
                         </div>
                         <a class="notification-subscriber__system" title="${subscriber.name}">
                            ${subscriber.name}
                         </a>
                         <button class="notification-subscriber__control" 
                  
                         title="${monitoringI18n('na.portal.monitoring.timeline.activity.notification.system.retry')}">
                         <i class="fa fa-undo"></i>
                         </button>
                      </li>
                      `
              })

            if (numberOfSystems < 1) {
              return `
                    <div class="notification-detail notification-detail--warning">
                        <div class='alert alert-warning'>
                            <i class="fuxicons fuxicons-warning"></i>
                            <span>${monitoringI18n('na.portal.monitoring.timeline.activity.notification.resume.missing')}</span>
                        </div>
                    </div>`
            }

            return [
              '<details class="notification-detail">',
              renderDetailResume(numberOfSystems, numberOfIssues),
              '<ul class="notification-detail__subscriber-list">',
              ...subscribers,
              '</ul>',
              '</details>'].join('')
          }

          function render() {
            const activityHtml = ['<div class="activity">']

            if (activity.hasToggler()) {
              activityHtml.push(renderIcon('toggle fa fa-caret-down'))
              activityHtml.push(renderIcon('toggle fa fa-caret-right hide'))
            }
            activityHtml.push("<div class='activity__header'>")
            activityHtml.push(renderActivityDescription())
            activityHtml.push(renderActivityActionsAndReports())
            if (activity.hasOperations()) {
              activityHtml.push(renderOperations(activity.getOperations()))
            }
            activityHtml.push('</div>')
            if (activity.isNotification()) {
              activityHtml.push(renderActivityDetails())
            }

            activityHtml.push('</div>')
            return activityHtml.join('')
          }

          return '<div class="item activity-item" data-node-id="' + activity.getId() + '" data-node-name="' + activity.getName() + '">' +
                '       <div class="timeline-status">' +
                '           ' + renderIconActivity(activity.getStatusIconClass()) +
                '       </div>' +
                '           ' + render() +
                '   </div>'
        }
      }
    }
  }])
