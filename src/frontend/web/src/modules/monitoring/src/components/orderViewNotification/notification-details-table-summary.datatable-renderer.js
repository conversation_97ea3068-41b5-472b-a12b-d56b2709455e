import { saveRenderer } from '~components/naDatatables-v2/datatable-render-store'

const parseErrors = (context) => {
  if (context.rowData.systems.every(system => system.state === 'SUCCESS')) {
    return ''
  }
  const { systemsWithErrorCount, systemsCount } = context.rowData
  const key = `na.portal.monitoring.order.notification.details.table.errorIssues;${systemsWithErrorCount};${systemsCount}`
  return `<i class="icon fuxicons fuxicons-error"></i> <x-i18n key="${key}"></x-i18n>`
}

export const summary = (context) => {
  const systemsCount = context.rowData.systemsCount
  const key = parseInt(systemsCount) !== 1
    ? 'na.portal.monitoring.order.notification.details.table.systems'
    : 'na.portal.monitoring.order.notification.details.table.system'
  const errorHtml = parseErrors(context)
  return `<div><span>${systemsCount} <x-i18n key="${key}"></x-i18n></span>${errorHtml}</div>`
}

saveRenderer({
  key: 'na.datatableTemplates.monitoring.notificationDetails.summary',
  renderer: summary
})
