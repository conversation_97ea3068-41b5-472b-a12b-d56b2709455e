import './demo-providers'
import * as $ from 'jquery'
import '../dynamic-properties-form.element'
import './dynamic-properties-form.demo.css'
import '~utils/modal/implementation'
import { showModal } from '~utils/modal'

$('.code--import').text("import '~monitoring/src/components/dynamic-properties-form/dynamic-properties-form.element'")

document.addEventListener('click', async ev => {
  if (ev.target.matches('[data-action="reset"]')) {
    resetSandbox()
  }

  if (ev.target.matches('[data-action="value"]')) {
    await displaySandboxValue()
  }

  if (ev.target.matches('[data-action="setValue"]')) {
    showUpdateValueModal()
  }
})

function resetSandbox() {
  const sandboxArea = document.querySelector('.sandbox_area')
  sandboxArea.innerHTML = `
<x-dynamic-properties-form data-field="sandbox"></x-dynamic-properties-form>
  `
}

async function displaySandboxValue() {
  const value = document.querySelector('[data-field="sandbox"]').valueAsObject
  const jsonValue = JSON.stringify(value, null, 2)
  await copyToClipboard(jsonValue)
    .then(() => alert(jsonValue + '\n This value was added to your clipboard!'))
    .catch(() => alert(jsonValue))
}

function updateSandboxModalValue() {
  const element = document.querySelector('[data-field="sandbox"]')
  const valueToAdd = document.getElementById('sandbox-update-form-modal-textarea').value
  element.valueAsObject = JSON.parse(valueToAdd)
}

function showUpdateValueModal() {
  showModal({
    title: 'Update Form',
    body: updateFormModalBody(),
    buttonOk: {
      label: 'Update',
      callback: updateSandboxModalValue,
      dismiss: true
    },
    buttonCancel: {
      label: 'Cancel',
      dismiss: true
    }
  })
}

function updateFormModalBody() {
  return `
    <form>
        <textarea id="sandbox-update-form-modal-textarea"></textarea>
    </form>
  `
}
function copyToClipboard(value) {
  return navigator?.clipboard?.writeText(value) || Promise.reject(new Error('Clipboard is not Supported'))
}
