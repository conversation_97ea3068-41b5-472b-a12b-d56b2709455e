import { monitoringModule } from '~monitoring/src/monitoring.angular-module'
import { providers } from '~monitoring/src/monitoring-providers'
import { PayloadRender } from '~monitoring/src/common/payload-render'
import { store } from '~monitoring/src/components/externalSystemsInteractions/ext-sys-interactions.store'
import { dateTimeFormats, dateTimeService } from '~utils/time-deprecated'
import { htmlEscape } from '~utils/string.util'
import * as moment from 'moment'

monitoringModule.directive(providers.components.orderViewExecutionPage.directives.activityExternalSystemInteractionsModal, [
  '$compile',
  activityExternalSystemInteractionsModalDirective
])

function activityExternalSystemInteractionsModalDirective($compile) {
  return {
    restrict: 'A',
    terminal: false,
    link: function (scope, element) {
      element.on('click', 'i.toggle', function () {
        $(this).parent().find('.toggle').toggleClass('hide')
      })

      store.setters.init({
        actionHistory: scope.data.actionHistory,
        interactions: scope.data.externalSystemInteractions
      })
      const { externalSystemInteractions } = scope.data
      const sortedTimeDescExtSysInteractions = [...externalSystemInteractions].sort((a, b) => b.time - a.time)
      const latestInteraction = sortedTimeDescExtSysInteractions[0]
      render(scope.data)
      const { requestPayload, syncResponse, asyncResponse } = latestInteraction
      setTimeout(() => {
        renderPayload($('.monitoring-order-view__ext-sys-interaction-request-payload', element)[0], requestPayload)
        renderPayload($('.monitoring-order-view__ext-sys-interaction-sync-response-payload', element)[0], syncResponse)
        renderPayload($('.monitoring-order-view__ext-sys-interaction-async-response-payload', element)[0], asyncResponse)

        const $navigationTabsBtn = element.find('.tabs--top-ext-sys-interaction .fx-tab')
        $navigationTabsBtn.on('click', function () {
          const sectionId = $(this).attr('href')
          if (sectionId.includes('history')) {
            const $historyTabContent = element.find('.tab-content--ext-sys-history')
            if ($historyTabContent.html() === '') {
              requestAnimationFrame(() => {
                const tabHistory = $(historyTabHtml())
                $historyTabContent.append(tabHistory)
                $compile(tabHistory)(scope)
              })
            }
          }
        })
      }, 250 /* just enough to finish modal show animation */)

      function renderNavTabs() {
        $(`<ul class="nav nav-tabs fx-main-tabs tabs--top-ext-sys-interaction">
              <li class="active">
                  <a href="#tab-ext-sys-execution" class="fx-tab tab--execution" data-toggle="tab">
                      <span class="fx-tab-title">Execution</span>
                  </a>
              </li>
              <li>
                  <a href="#tab-ext-sys-history" class="fx-tab tab--history" data-toggle="tab">
                      <span class="fx-tab-title">History</span>
                  </a>
              </li>
          </ul>`).appendTo(element)
      }

      function executionTabHtml(data) {
        const {
          requestTabLabel,
          syncResponseTabLabel,
          asyncResponseTabLabel,
          destinationLabel,
          dateLabel
        } = data

        const latestInteraction = sortedTimeDescExtSysInteractions[0]
        const { syncResponse, asyncResponse, time, endpoint } = latestInteraction

        let timestamp = Number(time).valueOf()
        if (Number.isInteger(timestamp)) {
          const momentDate = moment(timestamp)
          if (momentDate.isValid() && momentDate.isAfter('1970-01-01', 'year')) {
            timestamp = momentDate.format(dateTimeService.getDateFormat(dateTimeFormats.DATETIME_MILLI_SECONDS))
          }
        }

        let navTabsHtml = ''
        let contentTabsHtml = ''

        navTabsHtml += ` <li class="navigation-tab active">
                              <a href="#tab-ext-sys-request-detail" class="tab-validation-request-detail tab-detail fx-tab" data-toggle="tab">${requestTabLabel}</a>
                            </li>`
        contentTabsHtml += `
<section id="tab-ext-sys-request-detail" class="tab-ext-sys-request-detail tab-pane active fx-full-height">
  <div class="clearfix">
    <div class="validation-page__request">
      <div class="monitoring-order-view__page_header">
        <div class="pull-left">
          <label class="response-page__destination-label"><b>${destinationLabel}</b></label>
          <a href="${htmlEscape(endpoint) || ''}" target="_blank" rel="noopener noreferrer" class="response-page__destination">${htmlEscape(endpoint) || ''}</a>
        </div>
        <div class="pull-right">
          <label class="response-page__destination-label">${dateLabel}</label>
          <span class="response-page__destination">${timestamp}</span>
        </div>
        <br/>
        <div class="clearfix">
          <div class="monitoring-order-view__page_status"></div>
        </div>
      </div>
      <div class="monitoring-order-view__ext-sys-interaction-request-payload"></div>
    </div>
  </div>
</section>`

        if (syncResponse) {
          navTabsHtml += ` <li class="navigation-tab">
                        <a href="#tab-ext-sys-sync-response-detail" class="tab-ext-sys-sync-response-detail tab-detail fx-tab" data-toggle="tab">${syncResponseTabLabel}</a>
                        </li>`
          contentTabsHtml += `<section id="tab-ext-sys-sync-response-detail" class="tab-validation-response-detail tab-pane fx-full-height">
                        <div class="clearfix monitoring-order-view__ext-sys-interaction-sync-response-payload"></div>
                        </section>`
        }

        if (asyncResponse) {
          navTabsHtml += ` <li class="navigation-tab">
                        <a href="#tab-ext-sys-async-response-detail" class="tab-ext-sys-async-response-detail tab-detail fx-tab" data-toggle="tab">${asyncResponseTabLabel}</a>
                        </li>`
          contentTabsHtml += `<section id="tab-ext-sys-async-response-detail" class="tab-validation-response-detail tab-pane fx-full-height">
                        <div class="clearfix monitoring-order-view__ext-sys-interaction-async-response-payload"></div>
                        </section>`
        }

        return `<div id="monitoring-order-view__ext-sys-execution-view">
  <ul class="nav nav-tabs nav-stacked nav-tabs-stacked-left pull-left" style="width: 200px;">
    ${navTabsHtml}
  </ul>
  <div class="tab-content nav-tabs-stacked-left-content validation-content fx-full-height" style="min-height: 20em">
    ${contentTabsHtml}
  </div>
</div>`
      }

      function historyTabHtml() {
        return '<div data-na-portal-monitoring-order-external-system-interactions-history-tab></div>'
      }

      function renderPayload(element, payloadResponse) {
        if (!payloadResponse || payloadResponse.trim() === '') {
          return
        }

        let prettyPayload
        try {
          prettyPayload = JSON.stringify(JSON.parse(payloadResponse), null, 2)
        } catch (e) {
          prettyPayload = payloadResponse
        }

        PayloadRender(element, prettyPayload)
      }

      function render(data) {
        renderNavTabs()

        const operationsListHtml = $(
          '<div class="tab-content fx-main-tab-content with-padding">\n' +
                        '    <div class="tab-pane active" id="tab-ext-sys-execution">\n' +
                        "        <div class=\"fx-tab-pane-inner\" style='padding: 0'>\n" +
                        executionTabHtml(data) +
                        '        </div>\n' +
                        '    </div>\n' +
                        '    <div class="tab-pane" id="tab-ext-sys-history">\n' +
                        '        <div class="tab-content--ext-sys-history fx-tab-pane-inner"></div>\n' +
                        '    </div>\n' +
                        '</div>\n')
        element.append(operationsListHtml)
      }
    }
  }
}
