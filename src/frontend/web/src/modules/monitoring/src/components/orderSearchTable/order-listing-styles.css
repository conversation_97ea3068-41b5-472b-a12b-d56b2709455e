

table.dataTable.dataTable--theme-newfuxi {
  border-collapse: separate;
  border-spacing: 0;
  border-top: none;
  width: 100%;
}

table.dataTable.dataTable--theme-newfuxi > thead th {
    border: none;
    background-color: transparent;
}

table.dataTable.dataTable--theme-newfuxi > tbody > tr.child .details,
table.dataTable.dataTable--theme-newfuxi > tbody > tr:not(.child) > td:first-child{
    border-left: 3px solid #0099ab;
}

table.dataTable.dataTable--theme-newfuxi > tbody > tr.child .details,
table.dataTable.dataTable--theme-newfuxi > tbody > tr:not(.child) > td:last-child{
    border-right: 1px solid #d2d2d2;
}

table.dataTable.dataTable--theme-newfuxi > tbody > tr.child .details,
table.dataTable.dataTable--theme-newfuxi > tbody > tr:not(.child) > td {
    background-color: white;
}


table.dataTable.dataTable--theme-newfuxi > tbody > tr:not(.child) > td {
    padding: 15px 5px;
    border-top: 1px solid #d2d2d2;
}

table.dataTable.dataTable--theme-newfuxi > tbody > tr.child, table.dataTable.dataTable--theme-newfuxi > tbody > tr.child > td {
    border-left-width: 0;
    margin-left: 0;
    padding: 0;
}

table.dataTable.dataTable--theme-newfuxi > tbody .details,
table.dataTable.dataTable--theme-newfuxi > tbody .details__separator {
    margin-left: 0;
    margin-top: 0;
    border-bottom: 1px solid #d2d2d2;
}

table.dataTable.dataTable--theme-newfuxi > tbody .fx-table-actions {
    display: flex;
    align-items: center;
}

table.dataTable.dataTable--theme-newfuxi > tbody td.orderID.expandable-column-cell a {
    display: inline;
}

table.dataTable.dataTable--theme-newfuxi > tbody .details {
    margin-bottom: 12px;
}


table.dataTable.dataTable--theme-newfuxi > tbody > tr.row--details-hidden .visible-on-details,
table.dataTable.dataTable--theme-newfuxi > tbody > tr.row--details-visible .hidden-on-details{
    display: none;
}

table.dataTable.dataTable--theme-newfuxi .details__table{
    padding: 1em 0 1em 1em;
    display: none;
}

table.dataTable.dataTable--theme-newfuxi > tbody > tr.row--details-visible .details__table{
    display: block;
}

table.dataTable.dataTable--theme-newfuxi > tbody .details > .alert {
    margin-left: 10px;
    padding: 8px;
    margin-bottom: 8px;
}

table.dataTable.dataTable--theme-newfuxi > tbody .details  .fx-alert-icon {
    font-size: 16px;
    top: -2px;
}

table.dataTable.dataTable--theme-newfuxi .dataTable-table-info {
    margin-top: 10px;
    height: 0;
}

table.dataTable.dataTable--theme-newfuxi .dataTable-table-info .dataTables_length select {
    background-color: white;
    color: #666666;
    padding: 8px 1px;
    border: 1px solid #C8C8C8;
    border-radius: 0;
    font-size: 13px;
    font-weight: normal;
    font-style: normal;
    text-align: right;
    text-align-last: right;
}

table.dataTable.dataTable--theme-newfuxi ~ .dataTable-pagination-wrapper {
    top: 0;
}

table.dataTable.dataTable--theme-newfuxi > tbody .fx-table-actions {
    display: flex;
    align-items: center;
}

table.dataTable.dataTable--theme-newfuxi ~ .dataTable-pagination-wrapper .dataTables_info {
    padding: 7px 7px 0 0;
}

table.dataTable.dataTable--theme-newfuxi ~ .dataTable-pagination-wrapper .paginate_button {
    background-color: white;
    color: rgb(102, 102, 102);
    border: 1px solid #C8C8C8;
    border-radius: 0;
    font-size: 13px;
    font-weight: normal;
    font-style: normal;
    text-align: right;
    text-align-last: right;
    display: inline-block;
}

table.dataTable.dataTable--theme-newfuxi ~ .dataTable-pagination-wrapper .paginate_input {
    text-align: center;
    line-height: 1.8;
}

table.dataTable.dataTable--theme-newfuxi .dataTable__expandable_view_cell {
    display: flex;
    flex-flow: row;
}

table.dataTable.dataTable--theme-newfuxi .expandable-column-cell span,
table.dataTable.dataTable--theme-newfuxi .expandable-column-cell a {
    overflow: hidden;
    text-overflow: ellipsis;
    margin-left: 0.3em;
}

table.dataTable.dataTable--theme-newfuxi .dataTable__expandable_view_cell a {
    flex-grow: 1;
}

.table {
    background: rgb(240, 240, 240)
}

.table > tbody > tr > td {
    border-top: 0;
}

div#order-listing-table_wrapper div.dataTable-pagination-wrapper div.dataTables_paginate {
    margin-top: -33px;
}

table.dataTable.dataTable--theme-newfuxi tr.even:hover td, table.dataTable--theme-newfuxi.dataTable tr.odd:hover td {
    background-color: #e4f8f6;
}

table.dataTable.dataTable--theme-newfuxi a {
    display: block;
}

.order-selection_container {
    border-radius: 0;
    background-clip: padding-box;
    border: none;
    margin-bottom: 8px;
    padding: 8px;
    position: relative;
}

.order-selection_container > .fx-alert-icon {
    font-size: 16px;
    left: 10px;
    top: 7px;
}

.order-selection_description {
    margin: 0;
}

.details > .alert {
    border-radius: 4px 0 0 4px;
}