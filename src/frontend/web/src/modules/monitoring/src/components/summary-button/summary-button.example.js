// Example of how to use the summary.svg image in a button component
import summaryIcon from '../../assets/images/summary.svg'

// Method 1: Using import (webpack will handle the path)
export function createSummaryButtonWithImport() {
  const button = document.createElement('button')
  button.className = 'btn btn-primary btn-sm'
  button.innerHTML = `
    <img src="${summaryIcon}" alt="Summary" style="width: 16px; height: 16px; margin-right: 4px;">
    Summary
  `
  return button
}

// Method 2: Using direct path (works in both dev and production)
export function createSummaryButtonWithPath() {
  const button = document.createElement('button')
  button.className = 'btn btn-primary btn-sm'
  button.innerHTML = `
    <img src="/na-ext/images/summary.svg" alt="Summary" style="width: 16px; height: 16px; margin-right: 4px;">
    Summary
  `
  return button
}

// Method 3: Using CSS background image
export function createSummaryButtonWithCSS() {
  const button = document.createElement('button')
  button.className = 'btn btn-primary btn-sm summary-button'
  button.innerHTML = `
    <span class="summary-icon"></span>
    Summary
  `
  
  // Add CSS styles
  const style = document.createElement('style')
  style.textContent = `
    .summary-button .summary-icon {
      display: inline-block;
      width: 16px;
      height: 16px;
      margin-right: 4px;
      background-image: url('/na-ext/images/summary.svg');
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      vertical-align: middle;
    }
  `
  document.head.appendChild(style)
  
  return button
}

// Method 4: Angular directive usage example
export const summaryButtonDirectiveExample = `
<!-- In your Angular template -->
<button class="btn btn-primary btn-sm" ng-click="showSummary()">
  <img src="/na-ext/images/summary.svg" alt="Summary" style="width: 16px; height: 16px; margin-right: 4px;">
  {{ 'monitoring.summary.button' | translate }}
</button>
`

// Method 5: Scala template usage example
export const summaryButtonScalaExample = `
@* In your Scala template *@
<button class="btn btn-primary btn-sm" onclick="showSummary()">
  <img src="/na-ext/images/summary.svg" alt="Summary" style="width: 16px; height: 16px; margin-right: 4px;">
  @Messages("monitoring.summary.button")
</button>
`
