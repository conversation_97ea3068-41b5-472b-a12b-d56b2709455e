.dynamic-properties-form__field-container {
    display: grid;
    grid-template-columns: [label] minmax(100px, max-content) [input] max-content [button] max-content auto;
    grid-auto-flow: row;
    gap: 1em .5em;
}

.dynamic-properties-form__field-container:not(:empty) + .dynamic-properties-form__control {
    margin-top: 1em;
}

.dynamic-properties-form__control {
    display: flex;
    gap: 0.5em;
}

.field--dynamic-properties-form {
    display: contents;
}

.field__label {
    text-align: right;
    word-wrap: break-word;
    max-width: 250px;
}

.label--margin {
    width: 100px;
}

.field-add-property {
    display: flex;
    gap: .5em;
}

.dynamic-properties-form__new-attribute-key {
    min-width: 280px;
}