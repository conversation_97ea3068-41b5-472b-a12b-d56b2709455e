import { deepClone } from '~utils/deep-clone.util'
import { isPlainObject } from '~utils/object-utils.util'

/**
 * execution state management
 */

export const initialState = function () {
  return {
    currentOrderItem: {
      id: null
    },
    execution: {},
    activityModal: {
      message: ''
    }
  }
}

export const getters = {
  currentOrderItem: function (data) {
    return data.currentOrderItem
  },
  execution: function (data) {
    return data.execution
  },
  activityModal: function (data) {
    return data.activityModal
  }
}

function setExecution(state, data) {
  if (!isPlainObject(data)) {
    return state
  }
  const { graph, timeline, workflowId, workflowName } = data
  if (
    !isPlainObject(graph) ||
          !Array.isArray(timeline) ||
          typeof workflowId !== 'string' ||
          typeof workflowName !== 'string'
  ) {
    return state
  }
  const newExecution = deepClone(data)

  return Object.assign({}, state, {
    execution: newExecution
  })
}

export const setters = {
  setCurrentOrderItem: function (state, data) {
    if (state.currentOrderItem.id === data.id && state.currentOrderItem.state === data.state) {
      return state
    }
    return Object.assign({}, state, {
      currentOrderItem: Object.assign({}, state.currentOrderItem, data)
    })
  },
  setExecution,
  setActivityModal: function (state, data) {
    return Object.assign({}, state, {
      activityModal: Object.assign({}, state.activityModal, data)
    })
  }
}
