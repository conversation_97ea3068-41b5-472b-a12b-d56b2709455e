import { monitoringModule } from '~monitoring/src/monitoring.angular-module'
import { providers } from '~monitoring/src/monitoring-providers'
import graphStore from './order-view-execution-graph.store'
import '~nowc/components/graph/graph.element'

monitoringModule.directive(providers.components.orderViewExecutionPage.directives.executionGraph, [
  function () {
    return {
      restrict: 'A',
      terminal: false,
      link: function (scope, element) {
        let currentOrderItem = scope.executionStore.getters.currentOrderItem
        const graph = scope.executionStore.getters.execution.graph
        if (currentOrderItem != null && graph != null) {
          feedGraph(graph)
        }

        scope.executionStore.listenStateChange(function (newStore, oldStore) {
          const newOrderItem = newStore.getters.currentOrderItem
          const graph = newStore.getters.execution.graph
          if (newOrderItem != null && graph !== oldStore.getters.execution.graph) {
            currentOrderItem = newOrderItem
            feedGraph(graph)
          }
        })

        const edgeIsInExecutedPath = (nodesPath, edge) =>
          (nodesPath.includes(edge.source) && nodesPath.includes(edge.target) &&
            (nodesPath.indexOf(edge.target) - 1 === nodesPath.indexOf(edge.source) ||
            nodesPath.indexOf(edge.target) === nodesPath.indexOf(edge.source)))

        const updateEdgesWhenInTraveledPath = (nodesPath, edges) => {
          return edges
            .map(edge => {
              if (!edgeIsInExecutedPath(nodesPath, edge)) {
                return edge
              }

              return {
                ...edge,
                modifierClass: 'graph__edge-area--traveled'
              }
            })
        }

        function feedGraph(graph) {
          if (currentOrderItem.hasProcess && graph != null) {
            const edges = updateEdgesWhenInTraveledPath(graph.nodesPath, graph.edges)
            updateGraph({
              ...graph,
              edges
            })
          } else {
            updateGraph({
              layout: null,
              nodes: null,
              edges: null,
              nodeTypes: null,
              nodesPath: null
            })
          }
          element.removeClass('graph--loading')
        }

        const updateGraph = (updatedGraph) => {
          graphStore.setters.setGraph(updatedGraph);
        }
      }
    }
  }
])
