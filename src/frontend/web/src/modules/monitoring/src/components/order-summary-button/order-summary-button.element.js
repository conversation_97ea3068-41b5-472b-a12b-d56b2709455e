import { store as orderStore } from '~monitoring/src/components/orderView/order-view.store'
import { showModal, modalSizes } from '~utils/modal'
import { request } from '~utils/proxy'
import { routes as monitoringJsRoutes } from '~monitoring/src/common/routes'
import { notifyError } from '~utils/toast-notification'
import { i18n as monitoringI18n } from '~monitoring/src/common/i18n.service'

export class OrderSummaryButtonElement extends HTMLElement {
  constructor () {
    super()
    this.addEventListener('click', this.handleClick.bind(this))
  }

  static get observedAttributes () {
    return ['order-id']
  }

  connectedCallback () {
    this.render()
  }

  attributeChangedCallback (name, oldValue, newValue) {
    if (name === 'order-id') {
      this.render()
    }
  }

  render () {
    const buttonText = monitoringI18n('order.button.summary')

    this.innerHTML = `
      <button type="button" class="btn btn-default summary-button">
        <img class="summary-icon" alt="Summary" src="/na-ext/images/summary.svg" style="margin-right: 4px;">
        <i class="fa fa-spinner fa-spin" style="display: none;"></i>
        <span class="summary-text">${buttonText}</span>
      </button>
    `
  }

  async handleClick (event) {
    event.preventDefault()
    event.stopPropagation()

    const orderId = this.getAttribute('order-id')
    if (!orderId) {
      this.showError('Order ID not found')
      return
    }
    const orderData = orderStore.getters.orderData
    const cachedSummary = orderStore.getters.orderSummary
    if (cachedSummary) {
      this.showSummaryModal(cachedSummary, orderData.id)
      return
    }

    const button = this.querySelector('button')
    const image = this.querySelector('img')
    const icon = this.querySelector('i')

    button.disabled = true
    image.style.display = 'none'
    icon.style.display = 'block'

    try {
      await this.getSummaryFromAPIWithData(orderData)

    } catch (error) {
      console.error('Order summary error:', error)
      this.showError('Failed to generate order summary')
    } finally {
      // Reset button state
      button.disabled = false
      icon.style.display = 'none'
      image.style.display = 'block'
    }
  }

  async getSummaryFromAPIWithData() {
    const orderData = orderStore.getters.orderData
    const orderFollowupData = {
      orderId: orderData.id,
      externalId: orderData.externalOrderId,
      category: orderData.category,
      description: orderData.description,
      state: orderData.state,
      system: orderData.system,
      customer: orderData.customer,
      technician: orderData.technician,
      systemEntry: orderData.systemEntry,
      lastUpdate: orderData.lastUpdate,
      items: orderData.items || []
    }

    return new Promise((resolve, reject) => {
      request({
        method: 'POST',
        route: monitoringJsRoutes.na.monitoring.controllers.OrderController.getOrderSummary(),
        data: orderFollowupData,
        onSuccess: (data) => {
          if (data.success) {
            // Cache the summary in the store for future use
            orderStore.setters.setOrderSummary(data.summary)
            this.showSummaryModal(data.summary, orderData.id)
            resolve(data)
          } else {
            reject(new Error(data.error || 'Failed to generate summary'))
          }
        },
        onError: () => {
          reject(new Error('Failed to generate order summary'))
        }
      })
    })
  }

  showSummaryModal (summary, orderId) {
    const title = monitoringI18n('order.summary.modal.title') || 'Order Summary'
    
    const bodyContent = `
      <div class="order-summary-content">
        ${summary}
      </div>
    `

    showModa({
      title: `<img class="summary-icon" alt="Summary" src="/na-ext/images/summary.svg" style="margin-right: 4px;"> ${title} - ${orderId}`,
      body: bodyContent,
      modalSize: modalSizes.large,
    })
  }

  showError (message) {
    const errorTitle = monitoringI18n('order.summary.error.title') || 'Error'
    
    notifyError({
      title: errorTitle,
      message: message,
      timeOut: 8000
    })
  }
}

export const elementTagName = 'x-order-summary-button'
if (!customElements.get(elementTagName)) {
  customElements.define(elementTagName, OrderSummaryButtonElement)
}
