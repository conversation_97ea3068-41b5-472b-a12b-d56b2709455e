export const initialState = function () {
  return {
    startDate: null,
    endDate: null,
    filterBy: null,
    options: [],
    selectedOptionKey: null,
    defaultOptionKey: null
  }
}

function setFilterBy(state, newFilterBy) {
  return (state.filterBy === newFilterBy) ? state : Object.assign({}, state, { filterBy: newFilterBy })
}

function setOptions(state, options) {
  return Object.assign({}, state, { options: options })
}

function setValue(state, newDateRange) {
  if (state.startDate === newDateRange.startDate && state.endDate === newDateRange.endDate) {
    return state
  }
  return Object.assign({}, state, {
    startDate: newDateRange.startDate,
    endDate: newDateRange.endDate
  })
}

function setRange(state, option) {
  return (state.selectedOptionKey === option) ? state : Object.assign({}, state, { selectedOptionKey: option })
}

function setDefaultRange(state, option) {
  return (state.defaultOptionKey === option) ? state : Object.assign({}, state, { defaultOptionKey: option })
}

function setIntervalAndOption(state, optionWithRange) {
  return setRange(setValue(state, optionWithRange), optionWithRange.option)
}

export const setters = {
  setOptions: setOptions,
  setValue: setValue,
  setRange: setRange,
  setFilterBy: setFilterBy,
  setDefaultRange: setDefaultRange,
  setIntervalAndOption: setIntervalAndOption
}

export const getters = {
  isCustomRangeSelected: function(state) {
    return state.selectedOptionKey == null
  },
  startDate: function (state) {
    return state.startDate
  },
  endDate: function (state) {
    return state.endDate
  },
  value: function(state) {
    return {
      startDate: state.startDate,
      endDate: state.endDate,
      filterBy: state.filterBy
    }
  },
  range: function(state) {
    return state.selectedOptionKey
  },
  defaultRange: function(state) {
    return state.defaultOptionKey
  },
  filterBy: function(state) {
    return state.filterBy
  },
  options: function(state) {
    return state.options
  },
  optionsMap: function(state) {
    return state.options.reduce(function(acc, option) {
      acc[option.key] = option
      return acc
    }, {})
  }
}
