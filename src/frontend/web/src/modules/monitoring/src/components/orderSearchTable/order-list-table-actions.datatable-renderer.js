import { saveRenderer } from '~components/naDatatables-v2/datatable-render-store'
import { link, dataAttributes, defaultIconMap } from '~components/naDatatables-v2/datatable-render-utils'
import { htmlEscape } from '~utils/string.util'

const actionIconMap = {
  pin: 'fuxicons fuxicons-thumb-tack-o',
  cancel: 'glyphicon glyphicon-ban-circle',
  pause: 'glyphicon glyphicon-pause',
  skip: 'glyphicon glyphicon-forward',
  retry: 'fa fa-undo',
  rollback: 'fa fa-step-backward',
  complete: 'glyphicon glyphicon-check',
  cancelWithRollback: 'glyphicon glyphicon-ban-circle',
  remove: 'glyphicon glyphicon-remove'

}

function menuButton(parameters, dropdown) {
  const iconClass = parameters.icon
  const title = parameters.title

  const dropdownHtml = Array.isArray(dropdown) ? dropdown.join('') : dropdown
  if (dropdownHtml === '') {
    return ''
  }

  return '<div class="btn-group">' +
            '<button data-toggle="dropdown" class="btn btn-link btn-group btn-link-in-table dropdown-toggle" data-toggle="dropdown" title="' + title + '" aria-haspopup="true" aria-expanded="false">' +
            '    <i class="' + iconClass + '"></i>' +
            '</button>' +
            '<div class="dropdown-menu fx-dropdown-reverse" role="menu">' +
            dropdownHtml +
            '</div>' +
            '</div>'
}

function menuLink(parameters, content) {
  const icon = parameters.icon
  const action = parameters.action
  const title = parameters.title
  const context = parameters.context
  const isEnabled = parameters.enabled
  let classes = parameters.classes || ''
  if (parameters.visible === false) {
    return ''
  }
  const iconHtml = icon ? `<i class="${htmlEscape(icon)}"></i> ` : ''
  if (isEnabled === false) {
    classes += ' disabled '
  }
  if (isEnabled === false) {
    return `<li class="${htmlEscape(classes)}">` +
      `<button ${dataAttributes(parameters)} class="dropdown-menu__link" disabled data-action="${action}" title="${title}">` +
      iconHtml + content +
      '</button>' +
      '</li>'
  } else {
    return `<li class="${htmlEscape(classes)}">` +
      `<button ${dataAttributes(parameters)} class="dropdown-menu__link" data-type="action" data-row="${context.rowNumber}" data-action="${action}" title="${title}">` +
      iconHtml + content +
      '</button>' +
      '</li>'
  }
}

function dropdown(context) {
  const rowData = context.rowData
  const i18n = context.i18n
  function dropdownBody() {
    const possibleActions = rowData.possibleActions || {}
    return Object.keys(possibleActions).map(function (possibleAction) {
      return menuLink({
        icon: actionIconMap[possibleAction],
        visible: rowData.actionsPermission,
        action: possibleAction,
        'data-id': rowData.orderId,
        title: i18n('na.portal.monitoring.order.action.' + possibleAction),
        enabled: possibleActions[possibleAction] === 'enabled',
        context
      }, `<x-i18n key="na.portal.monitoring.order.action.${possibleAction}"></x-i18n>`)
    })
  }
  return dropdownBody().filter(function (html) { return html.trim().length > 0 })
}

export function orderListingActions(context) {
  return '<span class="fx-table-actions">' +
            link({
              icon: actionIconMap.pin,
              action: 'pin',
              title: context.i18n('na.datatables.action.label.pin'),
              context
            }) +
            link({
              icon: defaultIconMap.clone,
              action: 'clone',
              title: context.i18n('na.datatables.action.label.clone'),
              visible: context?.rowData?.cloneable != null,
              enabled: context?.rowData?.cloneable === true,
              'data-order-id': context?.rowData?.orderId,
              context
            }) +
            menuButton({
              icon: defaultIconMap.moreOptions,
              title: context.i18n('na.datatables.action.label.moreoptions')
            }, dropdown(context)) +
            '</span>'
}

saveRenderer({
  key: 'na.datatableTemplates.monitoring.orderListing.actions',
  renderer: orderListingActions
})
