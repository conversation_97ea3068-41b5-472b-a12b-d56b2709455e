import { monitoringModule } from '~monitoring/src/monitoring.angular-module'
import { providers } from '~monitoring/src/monitoring-providers'
import { i18n } from '~monitoring/src/common/i18n.service'
import { getSessionLang } from '~utils/time-deprecated/session-lang'

/**
 * Directive: data-na-portal-monitoring-daterangepicker
 */
monitoringModule.directive(providers.common.directives.daterangepicker, [
  function () {
    /**
     * @type {{momentDate}} Date
     * @typedef {Array.<Date>} RangeArray
     * @typedef {{startDate: Date, endDate: Date}} Range
     */

    /**
     * Helper to build range maps, adapt data, manage range keys and respective i18n
     *
     * @param options
     * @returns {{getRanges, getRangeKey}}
     * @constructor
     */
    function RangeDatesHelper(options) {
      const optionsMap = options.reduce(function(acc, option) {
        acc[option.key] = option
        return acc
      }, {})

      const optionsMapByInterval = options.reduce(function(acc, option) {
        acc[option.interval] = option
        return acc
      }, {})

      /**
         *
         * @param {Range} range
         * @returns {string} rangeKey
         */
      function getRangeKeyFromRange(range) {
        const interval = moment(range.endDate).millisecond(0).unix() - moment(range.startDate).unix()
        return optionsMapByInterval[interval] && optionsMapByInterval[interval].key
      }

      /**
         *
         * @param interval
         * @returns {Range} range
         */
      function getRange(interval) {
        return {
          startDate: moment().subtract(interval, 'seconds'),
          endDate: moment()
        }
      }

      /**
         *
         * @type {Object.<string, Range>}
         * @private
         */
      function getRangesMap() {
        return options.reduce(function(acc, option) {
          const range = getRange(option.interval)
          acc[option.label] = [range.startDate, range.endDate]
          return acc
        }, {})
      }

      /**
         *
         * @param {string} rangeKey
         * @returns {{ranges: Object<string, RangeArray>, range: Range}}
         */
      function getRanges(rangeKey) {
        const data = {
          ranges: getRangesMap(),
          range: {
            startDate: null,
            endDate: null
          }
        }

        const range = rangeKey != null ? getRange(optionsMap[rangeKey].interval) : null
        if (range != null) {
          data.range = range
        }

        return data
      }

      return {
        getRanges: getRanges,
        getRangeKey: getRangeKeyFromRange
      }
    }

    /**
     * Component responsible to refresh active date range.
     *
     * @param container
     * @returns {{update: updateMessage, onClick: onClick}}
     * @constructor
     */
    function RefreshComponent(container) {
      const onClickCallbacks = []
      const $datePickerRefreshMessage = $('#daterangepicker-message', container)
      const $datePickerRefreshButton = $('.glyphicon-repeat', container)

      function hideMessage() {
        $datePickerRefreshButton.hide()
        $datePickerRefreshMessage.hide()
      }

      function updateMessage(message) {
        if (message !== null) {
          $datePickerRefreshButton.show()
          $datePickerRefreshMessage.show()
            .html(i18n('datarangepicker.message.refresh', message))
        } else {
          hideMessage()
        }
      }

      $datePickerRefreshButton.click(function () {
        onClickCallbacks.forEach(function(clickCallback) {
          clickCallback()
        })
      })

      function onClick(callback) {
        onClickCallbacks.push(callback)
      }

      return {
        update: updateMessage,
        onClick: onClick
      }
    }

    /**
     * Daterangepicker wrapper
     *
     * @param element
     * @returns {{init: init, setRanges: setRanges, onChange: onChange, open: open}}
     * @constructor
     */
    function Daterangepicker(element) {
      let onChangeCallbacks = []

      /**
                 * @type {daterangepicker}
                 */
      let daterangepickerApi = null

      /**
         * Update/refresh date intervals
         *
         * @param {Object<string, RangeArray>} [data.ranges]
         * @param {Range} data.range
         */
      function setRanges(data) {
        daterangepickerApi.ranges = data.ranges
        setDates(data.range)
      }

      /** update start date and end date of the date time picker, the start and end must be valid moment date */
      function setDates(dates) {
        if (
          dates == null ||
                        !moment.isMoment(dates.startDate) ||
                        !moment.isMoment(dates.endDate) ||
                        (daterangepickerApi.startDate.isSame(dates.startDate) && daterangepickerApi.endDate.isSame(dates.endDate))
        ) { return }

        const oldChangeCallback = onChangeCallbacks
        onChangeCallbacks = []
        daterangepickerApi.setStartDate(dates.startDate)
        daterangepickerApi.setEndDate(dates.endDate)
        onChangeCallbacks = oldChangeCallback
        triggerChangeCallbacks()
      }

      function init(data) {
        element.daterangepicker({
          timePicker: true,
          timePicker24Hour: true,
          timePickerSeconds: true,
          startDate: data.range.startDate,
          endDate: data.range.endDate,
          opens: 'right',
          drops: 'down',
          alwaysShowCalendars: true,
          showCustomRangeLabel: true,
          autoUpdateInput: true,
          linkedCalendars: false,
          locale: {
            format: i18n('na.portal.monitoring.datarangepicker.dateformat'),
            separator: '  -  ',
            customRangeLabel: i18n('datarangepicker.CUSTOM_RANGE'),
            applyLabel: i18n('datarangepicker.applyButton'),
            cancelLabel: i18n('datarangepicker.cancelButton'),
            daysOfWeek: [
              i18n('datarangepicker.sunday'),
              i18n('datarangepicker.monday'),
              i18n('datarangepicker.tuesday'),
              i18n('datarangepicker.wednesday'),
              i18n('datarangepicker.thursday'),
              i18n('datarangepicker.friday'),
              i18n('datarangepicker.saturday')
            ],
            monthNames: [
              i18n('datarangepicker.january'),
              i18n('datarangepicker.february'),
              i18n('datarangepicker.march'),
              i18n('datarangepicker.april'),
              i18n('datarangepicker.may'),
              i18n('datarangepicker.june'),
              i18n('datarangepicker.july'),
              i18n('datarangepicker.august'),
              i18n('datarangepicker.september'),
              i18n('datarangepicker.october'),
              i18n('datarangepicker.november'),
              i18n('datarangepicker.december')
            ]
          },
          ranges: data.ranges
        })

        daterangepickerApi = element.data('daterangepicker')
        element.on('change', triggerChangeCallbacks).trigger('change')
      }

      function onChange(callback) {
        onChangeCallbacks.push(callback)
      }

      function triggerChangeCallbacks() {
        onChangeCallbacks.forEach(function(changeCallback) {
          changeCallback({
            startDate: daterangepickerApi.startDate.millisecond(0).toISOString(),
            endDate: daterangepickerApi.endDate.millisecond(999).toISOString()
          })
        })
      }

      function open() {
        element.trigger('click.daterangepicker')
      }

      return {
        init: init,
        setRanges: setRanges,
        setDates: setDates,
        onChange: onChange,
        open: open
      }
    }

    return {
      restrict: 'A',
      priority: 0,
      terminal: false,
      link: {
        pre: function () {
          // Configure language globally
          moment.locale(getSessionLang())
        },
        post: function (scope, element) {
          const store = scope.filterDateStore
          const rangeDatesHelper = new RangeDatesHelper(store.getters.options)
          const refreshComponent = new RefreshComponent(element)
          const daterangepicker = new Daterangepicker($(':input', element))

          function updateDaterangepicker() {
            const ranges = rangeDatesHelper.getRanges(store.getters.range)
            daterangepicker.setRanges(ranges)
          }

          daterangepicker.onChange(function(range) {
            const rangeKey = rangeDatesHelper.getRangeKey(range)

            // notify changes to search component
            store.setters.setIntervalAndOption({
              option: rangeKey,
              startDate: range.startDate,
              endDate: range.endDate
            })
          })

          refreshComponent.onClick(function() {
            updateDaterangepicker()
            scope.submitAction()
          })

          const listener = store.listenStateChange(function(newStateStore, oldStateStore) {
            if (newStateStore.getters.isCustomRangeSelected) {
              refreshComponent.update(null)
            } else if (newStateStore.getters.range !== oldStateStore.getters.range) {
              const rangeKey = newStateStore.getters.range
              refreshComponent.update(i18n('datarangepicker.message.' + rangeKey))
            }
            daterangepicker.setDates({
              startDate: moment(newStateStore.getters.startDate),
              endDate: moment(newStateStore.getters.endDate)
            })
          })

          scope.$on('$destroy', listener.unregister)

          $('#search-date-range', element).click(function() {
            daterangepicker.open()
          })

          function init() {
            let data
            let rangeKey = store.getters.range
            const initialRange = store.getters.value

            if (initialRange && initialRange.startDate && initialRange.endDate) {
              data = rangeDatesHelper.getRanges(rangeKey)
              data.range = {
                startDate: moment(initialRange.startDate),
                endDate: moment(initialRange.endDate)
              }
            } else {
              rangeKey = rangeKey || store.getters.defaultRange
              data = rangeDatesHelper.getRanges(rangeKey)
            }

            daterangepicker.init(data)
            store.setters.setRange(rangeKey)
          }

          init()
        }
      }
    }
  }])
