import { saveRenderer } from '~components/naDatatables-v2/datatable-render-store'
import { link } from '~nowc/components/table/table-render-utils'
import { i18n } from '~utils/i18n'

const actionIconMap = {
  pin: 'fuxicons fuxicons-thumb-tack-o'
}

export function serviceActions(context) {
  return '<span class="fx-table-actions">' +
            link({
              icon: actionIconMap.pin,
              action: 'pin',
              title: i18n('na.datatables.action.label.edit'),
              context
            }) +
            '</span>'
}

saveRenderer({
  key: 'na.datatableTemplates.monitoring.orderListing.serviceActions',
  renderer: serviceActions
})
