<x-i18n-attr-container class="monitoring-order-create-sidebar">
    <div class="header header--monitoring-order-create-sidebar">
        <label><x-i18n key="na.portal.monitoring.order.create.sidebar.title" class="header__title"></x-i18n></label>
    </div>
    <form id="details-form">
        <div class="form-group form-group--system-name">
            <label class="fx-required"><x-i18n key="na.portal.monitoring.order.create.sidebar.system" class="header__title"></x-i18n><abbr data-i18n-title="na.basemodule.validations.mandatory"></abbr></label>
            <div class="select-box-container--system-name"></div>
        </div>
        <div class="form-group form-group--system-user">
            <label class="fx-required"><x-i18n key="na.portal.monitoring.order.create.sidebar.user" class="header__title"></x-i18n><abbr data-i18n-title="na.basemodule.validations.mandatory"></abbr></label>
            <div class="select-box-container--system-user"></div>
        </div>
        <div class="form-group form-group--version">
            <label><x-i18n key="na.portal.monitoring.order.create.sidebar.version" class="header__title"></label>
            <div id="version" data-i18n-placeholder="na.portal.monitoring.order.create.sidebar.placeholder.missing.system" class="form-control form-control--version" disabled></div>
        </div>
        <div class="form-group form-group--operations">
            <label class="fx-required"><x-i18n key="na.portal.monitoring.order.create.sidebar.operations" class="header__title"></x-i18n><abbr data-i18n-title="na.basemodule.validations.mandatory"></abbr></label>
            <div class="multi-select-box-container--operations"></div>
        </div>
    </form>
</x-i18n-attr-container>
