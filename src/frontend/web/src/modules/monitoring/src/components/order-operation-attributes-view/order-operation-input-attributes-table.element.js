import '~monitoring/src/common/tables-i18n-fallbacks.js'
import { TableWithAllMixins } from '~nowc/components/table/table.element'
import '~operations-catalog/src/operation/operation-details-validation-rule.datatable-renderer.js'
import '~operations-catalog/src/operation/inputAttributes/operation-details-input-attribute-entity.datatable-renderer.js'
import { flatPath } from '~operations-catalog/src/operation/operation-details-datatable.helper'

const operationData = new WeakMap()

class OrderOperationInputAttributesTableElement extends TableWithAllMixins {
  usesI18nHeaders () {
    return true
  }

  usesAjaxLoader () {
    return true
  }

  usesDatatable () {
    return true
  }

  reloadCallback(data, callback) {
    const { operationData } = this
    if (!operationData?.operation) {
      return callback({
        data: [],
        version: data.version,
        count: 0
      })
    }

    const { operation, operationValues } = operationData
    const attributes = operation.operationAttributes
    const inputAttributes = attributes.filter(attribute => attribute.direction === 'IN')
    const inputValues = operationValues.filter(value => value.direction === 'IN')
    callback({
      data: getTableData(inputAttributes, inputValues, data),
      version: data.version,
      count: inputAttributes.length
    })
  }

  get operationData() {
    return operationData.get(this)
  }

  set operationData(data) {
    operationData.set(this, data)
    this.reloadTable()
  }

  loadConfig () {
    return new Promise((resolve, reject) => {
      resolve({
        columns: [
          {
            name: 'name',
            title: 'na.portal.monitoring.operation.view.table.column.label.attributes_entities',
            property: 'attribute',
            sortable: true,
            visible: true,
            celltemplate: 'na.datatableTemplates.operationscatalog.operationInputAttribute'
          },
          {
            name: 'cardinality',
            title: 'na.portal.monitoring.operation.view.table.column.label.cardinality',
            property: 'cardinality',
            sortable: true,
            visible: true
          },
          {
            name: 'validationRules',
            title: 'na.portal.monitoring.operation.view.table.column.label.validationRules',
            property: 'validationRule',
            sortable: true,
            visible: true,
            cellTemplate: 'na.datatableTemplates.operationscatalog.validationRule'
          },
          {
            name: 'Value',
            title: 'na.portal.monitoring.operation.view.table.column.label.value',
            property: 'value',
            sortable: false,
            visible: true,
            visibilityToggle: true
          }
        ],
        pagination: {
          enabled: false,
          pageLengthMenu: {
            enabled: true,
            options: [
              15,
              30,
              45
            ]
          },
          pageLength: 15
        },
        autoWidth: false
      })
      /* const url = this.getTableConfigUrl()
      if (url) {
        proxy.get({
          url,
          onSuccess: (serverData) => {
            resolve(serverData)
          }
        })
      } */
    })
  }
}

function getTableData(attributes, attributeValues, tableInfo) {
  const stringComparator = new Intl.Collator()

  const tableData = []
  if (attributes.length > 0) {
    tableData.push(...attributes.map(function (attribute) {
      const { name } = attribute
      const value = attributeValues.find(value => value.property === name)?.value || ''
      return {
        inputAttribute: attribute,
        name,
        attribute: name,
        pathString: flatPath(attribute.path),
        path: attribute.path,
        cardinality: attribute.minMultiplicity + '..' + attribute.maxMultiplicity,
        validationRule: [].concat(attribute.entityConstraints, attribute.constraints),
        valid: attribute.valid,
        value,
      }
    }))

    const sortAlgorithmByAttribute = {
      attribute: function (rowData1, rowData2) {
        const name1 = rowData1.attribute
        const name2 = rowData2.attribute
        const comparisonValue = stringComparator.compare(name1, name2)
        if (comparisonValue === 0) {
          const path1 = rowData1.pathString
          const path2 = rowData2.pathString
          return stringComparator.compare(path1, path2)
        }
        return comparisonValue
      }
    }

    const comparisonMultiplier = {
      ascendant: 1,
      descendant: -1
    }

    if (Object.keys(tableInfo.sorts).length > 0) {
      tableData.sort(function (a, b) {
        return Object.keys(tableInfo.sorts).reduce(function (accumulator, attributeToSort) {
          if (accumulator === 0 && sortAlgorithmByAttribute[attributeToSort] != null) {
            accumulator = sortAlgorithmByAttribute[attributeToSort](a, b) * comparisonMultiplier[tableInfo.sorts[attributeToSort]]
          }
          return accumulator
        }, 0)
      })
    }
  }
  return tableData
}

export const elementTagName = 'x-na-monitoring-order-input-attributes-table'
customElements.define(elementTagName, OrderOperationInputAttributesTableElement)
