import html from './templates/order-create-related-party-form.template.html'
import { createLazyTemplate } from '~utils/lazy-load-template.util'
import { FieldSetList } from '../order-create-field-set-list.element'

const fieldTemplate = createLazyTemplate({ html })

const SELECTORS = {
  FIELD_SET_FORM: 'x-na-monitoring-order-create-field-set-form'
}

class OrderCreateRelatedPartyList extends FieldSetList {
  getTemplate() {
    const baseTemplate = super.getTemplate()
    const customFieldsTemplate = fieldTemplate.content.querySelector('template')
    baseTemplate.querySelector(SELECTORS.FIELD_SET_FORM).innerHTML = customFieldsTemplate.innerHTML

    return baseTemplate
  }
}

export const elementTagName = 'x-na-monitoring-order-create-field-set-list-related-parties'
customElements.define(elementTagName, OrderCreateRelatedPartyList)
