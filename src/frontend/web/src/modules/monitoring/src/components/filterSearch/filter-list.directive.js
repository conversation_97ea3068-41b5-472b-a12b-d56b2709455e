import { monitoringModule } from '~monitoring/src/monitoring.angular-module'
import { providers } from '~monitoring/src/monitoring-providers'

monitoringModule.directive(providers.components.orderListing.directives.filterList, ['$compile', filterListDirective])

function filterListDirective($compile) {
  return {
    restrict: 'A',
    terminal: false,
    scope: {
      store: '=',
      parentContainerSelector: '@'
    },
    link: function (scope, element) {
      scope.store.listenStateChange(function (newStateStore, oldStateStore) {
        if (newStateStore.state.visibleFields !== oldStateStore.state.visibleFields) {
          renderFilters()
        }
      })

      if (scope.parentContainerSelector != null && scope.parentContainerSelector !== '') {
        const parentContainer = element.closest(scope.parentContainerSelector)
        scope.containerHeightGetter = function () {
          return parseInt(parentContainer.css('min-height'), 10)
        }
      }

      let childScopes = []

      function renderFilters() {
        childScopes.forEach(function (childScope) {
          childScope.$destroy()
        })
        element.empty()
        childScopes = scope.store.getters.visibleFieldsWithInfo.map(function (filter) {
          const childScope = scope.$new()
          const component = filter.component
          const directives = (function (type) {
            switch (type) {
              case 'text': return 'data-na-portal-monitoring-filter-item-' + type
              default: return 'data-na-portal-monitoring-filter-item data-na-portal-monitoring-filter-item-' + type
            }
          })(component.type)
          const elementClasses = (function (component) {
            const result = ['filter', 'filter--monitoring-order-search']
            if (component.pagination) {
              result.push('filter--with-pagination')
            }
            if (component.errorMessage != null) {
              result.push('filter--with-error')
            }
            return result.join(' ')
          })(component)
          const generatedTemplate = '' +
                  '<div class="' + elementClasses + '"' + directives +
                  ' id="' + filter.id + '"' +
                  ' data-field-id="' + filter.id + '"' +
                  ' data-store="store"' +
                  ' data-container-height-getter="containerHeightGetter"' +
                  '></div>'

          // compile the template after it is placed in the page
          const template = $(generatedTemplate)
          element.append(template)
          $compile(template)(childScope)

          return childScope
        })
      }

      renderFilters()
    }
  }
}
