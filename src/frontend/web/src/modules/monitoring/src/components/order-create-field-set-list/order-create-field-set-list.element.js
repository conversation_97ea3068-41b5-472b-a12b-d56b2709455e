import html from './order-create-field-set-list.element.html'
import itemHtml from './order-create-field-set-list-item.template.html'
import { createLazyTemplate } from '~utils/lazy-load-template.util'
import { elementTagName as fileSetFormTagName } from '../order-create-field-set-form/order-create-field-set-form.element'
import fuxiStylesheet from '~base-styles/fuxi.stylesheet'
import bootstrapStyleSheet from '~base-styles/bootstrap.stylesheet'
import { CssStyleSheetLoader } from '~utils/stylesheet-utils.util'

const template = createLazyTemplate({ html })
const itemTemplate = createLazyTemplate({ html: itemHtml })

const SELECTORS = {
  FIELD_SET_ITEM: '.field-set-item',
  FIELD_SET_LIST: '.order-create__field-set-list',
  FIELD_SET_FORM: 'x-na-monitoring-order-create-field-set-form'
}
const cssPromise = CssStyleSheetLoader(() => import(/* webpackChunkName: "monitoring/components-styles" */ './order-create-field-set-list.element.inline.css'))

export class FieldSetList extends HTMLElement {
  constructor() {
    super()

    this.attachShadow({ mode: 'open' })
    const { shadowRoot } = this
    cssPromise.then(stylesheet => {
      shadowRoot.adoptedStyleSheets = [bootstrapStyleSheet, fuxiStylesheet, stylesheet]
    })
    shadowRoot.append(document.importNode(template.content, true))

    shadowRoot.addEventListener('click', (event) => {
      const changeEvent = new CustomEvent('change', { bubbles: true, composed: true })

      if (event.target.closest('[data-action="create"]')) {
        this.insertNewRelatedPartyForm(event.target.closest(SELECTORS.FIELD_SET_ITEM))
        this.dispatchEvent(changeEvent)
      }
      if (event.target.closest('[data-action="clone"]')) {
        this.cloneForm(event.target.closest(SELECTORS.FIELD_SET_ITEM))
        this.dispatchEvent(changeEvent)
      }
      if (event.target.closest('[data-action="delete"]')) {
        this.removeRelatedPartyForm(event.target.closest(SELECTORS.FIELD_SET_ITEM))
        this.dispatchEvent(changeEvent)
      }
    })
  }

  connectedCallback() {
    this.insertNewRelatedPartyForm()
  }

  insertNewRelatedPartyForm(element, form) {
    const fieldTemplateNode = this.getTemplate()
    if (!element) {
      return this.shadowRoot
        .querySelector(SELECTORS.FIELD_SET_LIST)
        .append(form || fieldTemplateNode)
    }
    return element.insertAdjacentElement('afterend', form || fieldTemplateNode)
  }

  cloneForm(element) {
    const formComponent = element.querySelector(SELECTORS.FIELD_SET_FORM)
    const clonedForm = this.getTemplate()
    const appendedClonedForm = this.insertNewRelatedPartyForm(element, clonedForm)
    appendedClonedForm.querySelector(SELECTORS.FIELD_SET_FORM).valueAsObject = formComponent.valueAsObject
  }

  getTemplate() {
    return document.importNode(itemTemplate.content, true).querySelector(SELECTORS.FIELD_SET_ITEM)
  }

  removeRelatedPartyForm(element) {
    element.remove()
    if (this.shadowRoot.querySelector(SELECTORS.FIELD_SET_LIST).childElementCount === 0) {
      this.insertNewRelatedPartyForm()
    }
  }

  get value() {
    return JSON.stringify(this.valueAsObject)
  }

  get valueAsObject() {
    return [...this.shadowRoot.querySelectorAll(SELECTORS.FIELD_SET_FORM)].map(form => form.valueAsObject)
  }

  set valueAsObject(valueObject) {
    if (!Array.isArray(valueObject)) {
      return
    }
    const formComponent = this.shadowRoot.querySelector(SELECTORS.FIELD_SET_LIST)
    if (valueObject.length === 0) {
      formComponent.replaceChildren(this.getTemplate())
      return
    }
    const fields = [...this.shadowRoot.querySelectorAll(SELECTORS.FIELD_SET_FORM)]
    const minLength = Math.min(fields.length, valueObject.length)
    fields.slice(0, minLength).forEach((field, index) => {
      field.querySelectorAll(fileSetFormTagName).forEach(form => { form.valueAsObject = valueObject[index] })
    })
    fields.slice(minLength).forEach(field => field.remove())
    const newElements = valueObject.slice(minLength).map(values => {
      const clonedForm = this.getTemplate()
      clonedForm.querySelectorAll(fileSetFormTagName).forEach(form => { form.valueAsObject = values })
      return clonedForm
    })
    formComponent.append(...newElements)
  }

  get validity() {
    return {
      valid: true
    }
  }
}
