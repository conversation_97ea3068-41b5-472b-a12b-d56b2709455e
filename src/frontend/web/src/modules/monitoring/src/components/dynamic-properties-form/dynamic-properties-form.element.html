<x-i18n-attr-container>
    <div class="dynamic-properties-form">
        <div class="dynamic-properties-form__field-container">
            <!-- where generated fields will be added -->
        </div>
        <div class="form-group dynamic-properties-form__control">
            <label class="control-label label--margin"></label>
            <x-input-container>
                <div class="field-add-property">
                    <input class="form-control input-sm dynamic-properties-form__new-attribute-key" type="text">
                    <button class="btn btn-link btn-link-in-table" data-action="add-attribute" type="button">
                        <i class="glyphicon glyphicon-plus"></i>
                        <x-i18n key="na.portal.monitoring.order.create.attribute.form.related-party.properties.button.add"></x-i18n>
                    </button>

                </div>
            </x-input-container>

        </div>
    </div>
</x-i18n-attr-container>
