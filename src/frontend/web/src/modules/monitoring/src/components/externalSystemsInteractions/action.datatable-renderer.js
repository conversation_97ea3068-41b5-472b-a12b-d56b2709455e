import { saveRenderer } from '~components/naDatatables-v2/datatable-render-store'
import { link } from '~nowc/components/table/table-render-utils'

export function actionRenderer(context) {
  const i18n = context.i18n
  return '<span class="fx-table-actions">' +
          link({
            icon: 'fuxicons fuxicons-eye',
            action: 'view',
            title: i18n('na.datatables.action.label.view'),
            context
          }) +
          '</span>'
}

saveRenderer({
  key: 'na.datatableTemplates.monitoring.extSysInteraction.actions',
  renderer: actionRenderer
})
