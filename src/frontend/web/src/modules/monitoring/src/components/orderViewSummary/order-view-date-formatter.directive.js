import { monitoringModule } from '~monitoring/src/monitoring.angular-module'
import { providers } from '~monitoring/src/monitoring-providers'
import { i18n } from '~utils/i18n'
import { formatShort, formatFuller } from '~monitoring/src/common/date-formatter'
import { store as orderStore } from '~monitoring/src/components/orderView/order-view.store'
import { getSessionLang } from '~utils/time-deprecated/session-lang'
import * as moment from 'moment'

monitoringModule.directive(providers.components.orderView.directives.dateFormatter, [
  function () {
    return {
      restrict: 'A',
      priority: 1,
      terminal: false,
      link: function (scope, element) {
        // Configure language globally
        moment.locale(getSessionLang())

        const order = orderStore.getters.orderData

        orderStore.listenStateChange((newState, oldState) => {
          if (newState.getters.orderData.systemEntry !== oldState.getters.orderData.systemEntry) {
            updateDateField(newState.getters.orderData.systemEntry)
          }
        })

        const updateDateField = (dateTime) => {
          const dateFormat = element.attr('data-date-format')
          if (dateFormat === 'full') {
            element.html(formatFuller(dateTime, i18n, moment))
          } else if (dateFormat === 'short') {
            element.html(formatShort(dateTime, i18n, moment))
          }
        }
        updateDateField(order.systemEntry)
      }
    }
  }])
