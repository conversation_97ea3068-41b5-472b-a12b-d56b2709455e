import { saveRenderer } from '~components/naDatatables-v2/datatable-render-store'
import { htmlEscape } from '~utils/string.util'
import { i18n } from '~utils/i18n'

const stateIcon = {
  SUCCESS: 'fx-alert-icon fx-alert-icon-success fx-icon',
  ERROR: 'fx-alert-icon fx-alert-icon-danger fx-icon'
}

const stateI18nKey = {
  SUCCESS: 'na.portal.monitoring.order.notification.details.table.state.success',
  ERROR: 'na.portal.monitoring.order.notification.details.table.state.error'
}

const renderRetryButton = () => {
  return `<button title="${htmlEscape(i18n('na.portal.monitoring.order.tooltip.retry'))}" type="button" class="btn btn-secundary fx-btn-action retry-notification">` +
        '<i class="icon fa fa-undo"></i>' +
        '</button>'
}

export const state = (context) => {
  if (context.cellData == null || context.cellData === '') {
    return '--'
  }
  return '<div>' +
      `<span><i class="icon ${stateIcon[context.cellData] || ''}"></i> <x-i18n key="${htmlEscape(stateI18nKey[context.cellData] || '')}"></x-i18n></span>` +
      renderRetryButton() +
    '</div>'
}

saveRenderer({
  key: 'na.datatableTemplates.monitoring.notificationDetails.state',
  renderer: state
})
