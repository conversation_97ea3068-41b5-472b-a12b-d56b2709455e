import { monitoringModule } from '~monitoring/src/monitoring.angular-module'
import { providers } from '~monitoring/src/monitoring-providers'
import { i18n } from '~monitoring/src/common/i18n.service'
import { PayloadRender } from '~monitoring/src/common/payload-render'
import { getIconClassByStatus } from '~monitoring/src/common/utils'

monitoringModule.directive(providers.components.orderViewValidationPage.directives.page, [
  OrderViewValidationPageDirective
])

function OrderViewValidationPageDirective() {
  function createStatusMessage(alertClass, alertIcon, desc) {
    return '' +
                '<div class=" alert ' + alertClass + '">' +
                '   <div class="fx-alert-icon"></div>' +
                '   <div class="fx-alert-message">' +
                '       <p>' + desc + '</p>' +
                '   </div>' +
                '</div>'
  }

  function loadStatusMessageIfAny($element, gobalErrorMessage) {
    if (gobalErrorMessage) {
      const code = gobalErrorMessage.code
      const description = gobalErrorMessage.description
      const message = gobalErrorMessage.message
      const statusMessage = i18n('order.validation.messages.global', description, code, message)

      $element.html(createStatusMessage('alert-danger', 'fuxicons fuxicons-error', statusMessage))
    }
  }

  /*
     * Render payload response code
     */
  function renderPayload(element, payloadResponse) {
    const prettyPayload = payloadResponse ? JSON.stringify(JSON.parse(payloadResponse), null, 2) : ''
    return PayloadRender(element, prettyPayload)
  }

  return {
    restrict: 'A',
    terminal: false,
    scope: {
      store: '='
    },
    link: function (scope, element) {
      const store = scope.store

      renderStatusDiagram(store.getters.structureValidationStatus, store.getters.sematicsValidationStatus)

      function renderStatusDiagram(structureValidationStatus, sematicsValidationStatus) {
        const Status = store.getters.EnumStatus
        $('.status-diagram', element).append(
          buildItem(structureValidationStatus, 'far fa-file-alt', 'structure') +
                        buildItem(sematicsValidationStatus, 'fa fa-sitemap', 'semantics')
        )

        function buildItem(status, diagramIconClass, type) {
          const isDisabled = status === Status.NONE
          const disabledClass = (isDisabled) ? ' disabled' : ''
          return '<div id="' + type + '" class="item clearfix' + disabledClass + '">' +
                            '   <div class="pull-left">' +
                            '       ' + renderIcon(status, isDisabled) +
                            '   </div>' +
                            '   <div class="status-diagram-icon pull-left">' +
                            '       <i class="' + diagramIconClass + disabledClass + '"></i>' +
                            '   </div>' +
                            '</div>'
        }

        function renderIcon(status) {
          let iconHtml = ''
          if (status != null) {
            switch (status) {
              case Status.SUCCESS:
              case Status.INFO:
              case Status.ERROR: {
                const iconClass = getIconClassByStatus(status)
                iconHtml = '<i class="' + iconClass + '"></i>'
              }
                break
              case Status.NONE:
                iconHtml = '<div class="bullet"></div>'
                break
            }
          }
          return iconHtml
        }
      }

      /* Content */

      const ViewMode = {
        TABLE: 'table',
        CODE: 'code'
      }

      const $content = $(element, '.validation-content')
      const $toolbarButtons = $content.find('.btn')
      const $toolbarContainers = $content.find('div[data-container]')
      const $navigationTabs = $(element, '.nav-tabs')
      const $navigationTabsBtn = $navigationTabs.find('.navigation-tab')
      const $navigationTabsContent = $content.find('.tab-pane')
      let defaultView = ViewMode.TABLE
      const codeMirrorInstanceResponse = renderPayload($('.validation-page__response', $content)[0], store.getters.orderValidationResponse ?? '')
      const codeMirrorInstanceRequest = renderPayload($('.validation-page__request', $content)[0], store.getters.orderValidationRequest ?? '')
      const responseObserver = store.reflect(
        ({ getters }) => getters.orderValidationResponse,
        (responseText) => codeMirrorInstanceResponse.setPayload(responseText)
      )
      const requestObserver = store.reflect(
        ({ getters }) => getters.orderValidationRequest,
        (requestText) => codeMirrorInstanceRequest.setPayload(requestText)
      )

      scope.$on('$destroy', function() {
        requestObserver.unregister()
        responseObserver.unregister()
      })

      $navigationTabsBtn.on('click', function () {
        $navigationTabsContent.removeClass('active')
        const sectionId = $(this).find('.tab-detail').attr('href')
        $(sectionId).addClass('active')
      })

      if (store.getters.isValidationSuccess) {
        $content.find('.btn-group').hide()
        const header = $(element, '.monitoring-order-view-validation__page_header')
        header.find('.validation-page__status').hide()
        defaultView = ViewMode.CODE
      } else {
        loadStatusMessageIfAny($('.validation-page__status', element), store.getters.globalErrorMessage)
      }

      $toolbarButtons.on('click', function () {
        // hide & deselect
        $toolbarButtons.removeClass('active')
        $toolbarContainers.hide()

        // select and show target container
        const targetContainer = $(this).attr('data-target')
        $(this).addClass('active')
        $toolbarContainers.filter('[data-container="' + targetContainer + '"]').show()

        if (targetContainer === ViewMode.CODE) {
          codeMirrorInstanceResponse.refresh()
        }
      }).filter('[data-target="' + defaultView + '"]').click()
    }
  }
}
