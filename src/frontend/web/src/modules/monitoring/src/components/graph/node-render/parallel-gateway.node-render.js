import { saveRenderer } from '~nowc/components/graph/graph.renders-store'
import cssContent from './gateway.node-render.css'

const styles = () => cssContent

function parallelGateway() {
  return "<div class='node node--render-parallel-gateway'>" +
                '<svg class="node__diamond-border">' +
                    '<path d="M 0 15 L 15 0 L 30 15 L 15 30 Z"></path>' +
                '</svg>' +
                '<i class="node__icon fa fa-plus"></i>' +
            '</div>'
}

saveRenderer({
  key: 'na.components.monitoring.graph.node.render.parallelGateway',
  renderer: parallelGateway,
  styles
})
