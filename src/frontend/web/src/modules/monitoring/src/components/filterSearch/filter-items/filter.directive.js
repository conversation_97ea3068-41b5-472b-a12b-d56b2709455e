import { monitoringModule } from '~monitoring/src/monitoring.angular-module'
import { providers } from '~monitoring/src/monitoring-providers'
import { i18n } from '~utils/i18n'
import { request } from '~utils/proxy'
import { FilterItemController } from './filter.controller'
import '~components/i18n/i18n.element'
import { htmlEscape } from '~utils/string.util'

monitoringModule.directive(providers.components.orderListing.directives.filterItem, [
  filterItemDirective
])

function filterItemDirective() {
  return {
    restrict: 'A',
    terminal: false,
    controller: FilterItemController,
    scope: {
      fieldId: '@',
      containerHeightGetter: '=',
      store: '='
    },
    link: function (scope, element, attrs, ctrl) {
      const store = scope.store
      const fieldId = scope.fieldId
      let value = store.getters.valueOfField(fieldId)
      const listener = store.listenStateChange(onStateChange)
      let expand = false
      let query = ''
      const pageSize = 15
      let currentPage = 0
      let isLastPage = false
      let queryTimeout

      scope.$on('$destroy', listener.unregister)

      element.on('input change', 'input.search-field', function () {
        const input = $(this)
        clearTimeout(queryTimeout)
        queryTimeout = setTimeout(function () {
          currentPage = 0
          query = input.val()
          filterDropdown()
        }, 300)
      })

      function dropdownListHtmlFromData(arrayData) {
        const storeGet = store.getters
        const field = storeGet.fieldInfoOfId(fieldId)
        const value = [].concat(storeGet.valueOfField(fieldId))

        return arrayData.reduce(function (html, option) {
          const isChecked = value.filter(function (val) {
            return val === option.value
          }).length > 0
          const inputType = htmlEscape(field.component.inputType.toLowerCase())
          return html +
                    '<li class="' + inputType + ' searchable">' +
                    '   <label>' + ctrl.renderDropdownInput(field, option, isChecked) + '</label>' +
                    '</li>'
        }, '')
      }

      function updateComponentData(arrayData) {
        const componentData = [].concat(store.getters.fieldInfoOfId(fieldId).component.data)
        const componentDataVals = new Set(componentData.map(data => data.value))
        const newComponentData = arrayData.reduce(function (acc, record) {
          return !componentDataVals.has(record.value) ? acc.concat(record) : acc
        }, componentData)

        if (newComponentData !== componentData) {
          store.setters.setFieldComponentData({ fieldId, data: newComponentData })
        }
      }

      function i18nArrayData(data) {
        return data.map(data => ({ ...data, text: i18n(data.text) }))
      }

      function getArrayData(response) {
        if (Array.isArray(response)) {
          return i18nArrayData(response)
        }
        if (response && Array.isArray(response.entries)) {
          return i18nArrayData(response.entries)
        }
        return []
      }

      function getNextPage() {
        currentPage += 1
        const field = store.getters.fieldInfoOfId(fieldId)

        if (field.component.dropDownDataUrl) {
          request({
            route: {
              url: field.component.dropDownDataUrl,
              method: field.component.dropDownDataMethod || 'GET',
            },
            data: { q: query, top: pageSize, skip: currentPage * pageSize },
            onSuccess: function (response) {
              const arrayData = getArrayData(response)
              isLastPage = arrayData.length < pageSize
              updateComponentData(arrayData)
              const html = dropdownListHtmlFromData(arrayData)
              const $dropdownList = $('.dropdown ul.list-unstyled', element)
              $dropdownList.append(html)
            }
          })
        }
      }

      element.on('change', ':checkbox', function () {
        const checkbox = $(this)
        const allSelected = checkbox.prop('name') === 'all' && checkbox.is(':checked')
        let value = null

        if (!allSelected) {
          value = scope.store.getters.valueOfField(scope.fieldId) || []
          if (checkbox.is(':checked')) {
            value = value.concat(checkbox.attr('value'))
          } else {
            value = value.filter(function (optionValue) {
              return optionValue !== checkbox.attr('value')
            })
          }
        }

        scope.store.setters.setFieldValue({ id: scope.fieldId, value })
      })

      element.on('change', ':radio', function () {
        let value = null
        $(':radio:checked', element).each(function () {
          value = $(this).attr('value')
        })
        scope.store.setters.setFieldValue({ id: scope.fieldId, value })
      })

      element.on('click', '.filter-footer button', function () {
        toggleFilterView()
      })

      ctrl.setRender(render)

      // functions

      function computeMaxElements(element, maxHeight) {
        let count = 0
        let height = 0
        element.find('.filter-content p').each(function (index, elem) {
          if ((height += $(elem).outerHeight(true)) <= maxHeight) {
            count++
          }
        })
        return count
      }

      function toggleFilterView() {
        expand = !expand
        toggleShowMore(!expand)
        element.find('.filter-content').empty().html(renderContent())
        collapseFilters()
      }

      function toggleShowMore(show) {
        const filterFooter = $(element).find('.filter-footer')
        filterFooter.find('.title').html(function () {
          if (show) {
            return '<x-i18n key="na.portal.monitoring.home.filters.more"></x-i18n>&nbsp;<span class="how-many-more"></span>'
          }
          return '<x-i18n key="na.portal.monitoring.home.filters.less"></x-i18n>'
        })

        const button = filterFooter.find('.button button i')
        button.toggleClass('glyphicon-chevron-up')
        button.toggleClass('glyphicon-chevron-down')
      }

      function collapseFilters() {
        // if containerHeightGetter is not defined, disable collapse behaviour entirely
        if (typeof scope.containerHeightGetter !== 'function') {
          return
        }

        const howManyMore = $(element).find('.how-many-more')
        const filterFooter = $(element).find('.filter-footer')
        const filterElements = $(element).find('.filter-item').length
        const containerHeight = scope.containerHeightGetter()
        const maxHeight = containerHeight - element.find('.filter-header').outerHeight() - filterFooter.outerHeight()
        const maxElements = computeMaxElements(element, maxHeight)
        const hidingFilterElements = filterElements - maxElements

        // update how many more value
        if (hidingFilterElements > 0) {
          filterFooter.show()
          howManyMore.html('(' + hidingFilterElements + ')')
        } else {
          filterFooter.hide()
        }

        $(element).find('.filter-item').each(function (index, elem) {
          if (!expand && index >= maxElements) {
            $(elem).hide()
          }
        })
      }

      function onStateChange() {
        if (value !== scope.store.getters.valueOfField(scope.fieldId)) {
          value = scope.store.getters.valueOfField(scope.fieldId)
          // The ctrl.renderParam may not be set yet and since the filter item is rendered
          // right after the renderParam is set, there's no need to wait for it to be set.
          if (typeof ctrl.renderParam === 'function') {
            element.find('.filter-content').empty().html(renderContent())
          }

          if (element.find('[name="all"]').length > 0) {
            const noValueChecked = value == null || value.length === 0
            if (noValueChecked) {
              element.find(':input:checked').prop('checked', false)
            }
            element.find('[name="all"]').prop('checked', noValueChecked).prop('disabled', noValueChecked)
          }

          collapseFilters()
        }
      }

      function paramsOfValue(value) {
        return scope.store.getters.labels[scope.fieldId].filter(function (param) {
          return param.value === value
        })[0]
      }

      function filterDropdown() {
        const field = scope.store.getters.fieldInfoOfId(scope.fieldId)

        if (field.component.dropDownDataUrl) {
          request({
            route: {
              url: field.component.dropDownDataUrl,
              method: field.component.dropDownDataMethod || 'GET',
            },
            data: { q: query, top: pageSize, skip: currentPage * pageSize },
            onSuccess: function (response) {
              const arrayData = getArrayData(response)
              isLastPage = arrayData.length < pageSize
              updateComponentData(arrayData)
              const html = dropdownListHtmlFromData(arrayData)
              const $dropdownList = $('.dropdown ul.list-unstyled', element)
              $dropdownList.find('.searchable').remove()
              $dropdownList.append(html)
            }
          })
        }
      }

      function renderContent() {
        function toArray(value) {
          return value && value.length === 0 ? [null] : [].concat(value)
        }
        return toArray(value)
          .map(val => `<p class="filter-item">${ctrl.renderParam(paramsOfValue(val))}</p>`)
          .join('')
      }

      function isSelectedItem(currentVal, item) {
        return [].concat(currentVal).some(function (value) {
          return String(value) === String(item.value)
        })
      }

      function renderDropdown() {
        const field = scope.store.getters.fieldInfoOfId(scope.fieldId)
        const component = field.component
        if (component.dropDownDataUrl) {
          const pageData = component.pageData
          if (pageData != null) {
            const value = scope.store.getters.valueOfField(scope.fieldId)
            const hasValue = value != null && value.length > 0

            const valueListHtml = pageData.reduce(function (html, item) {
              const inputType = htmlEscape(component.inputType.toLowerCase())
              const isSelected = isSelectedItem(value, item)
              return html +
                        '<li class="' + inputType + ' searchable">' +
                        '   <label>' + ctrl.renderDropdownInput(field, item, isSelected) + '</label>' +
                        '</li>'
            }, '')

            let html = ''
            if (component.inputType === 'CHECKBOX') {
              html = '<li class="checkbox" value="">' +
                     '    <label>' +
                     `        <input name="all" type="checkbox"${!hasValue ? ' checked disabled' : ''}><x-i18n key="na.portal.monitoring.home.filters.all"></x-i18n>` +
                     '    </label>' +
                     '</li>' +
                     '<hr/>' +
                     valueListHtml
            } else if (component.inputType === 'RADIO') {
              html = '<li class="radio" value="">' +
                     '    <label>' +
                     `        <input name="${field.property}" type="radio"${!hasValue ? ' checked disabled' : ''}><x-i18n key="na.portal.monitoring.home.filters.all"></x-i18n>` +
                     '    </label>' +
                     '</li>' +
                     '<hr/>' +
                     valueListHtml
            }

            return '<ul class="list-unstyled">' + html + '</ul>'
          }
          return '<ul class="list-unstyled loading"></ul>'
        }
        return '<ul class="list-unstyled">' + component.dropDownMenu + '</ul>'
      }

      function render() {
        const field = scope.store.getters.fieldInfoOfId(scope.fieldId)
        const errorMessage = field.component.errorMessage

        const htmlDropdownMenu = (() => {
          if (errorMessage) {
            return '<i class="dropdown-icon glyphicon glyphicon-plus"></i> ' +
                   `<i class="glyphicon glyphicon-exclamation-sign fx-status-error" title="${htmlEscape(errorMessage)}"><span class="sr-only">${htmlEscape(errorMessage)}</span></i>`
          }

          const searchableHtml = !field.component.searchable ? '' : '' +
            '<div class="fx-dropdown-header">' +
            '   <div class="form-group fx-search-input">' +
            '       <div class="fx-pos-rel">' +
            `           <input type="text" class="form-control input-sm search-field" placeholder="${htmlEscape(i18n('na.portal.monitoring.home.filters.search'))}">` +
            '           <button type="button" class="glyphicon glyphicon-search"></button>' +
            '       </div>' +
            '   </div>' +
            '</div>'
          return '<button data-toggle="dropdown" class="dropdown-toggle btn btn-link btn-link-in-table">' +
                   '<i class="glyphicon glyphicon-plus dropdown-icon"></i>' +
                 '</button>' +
                 '<div class="dropdown dropdown-menu">' +
                    searchableHtml +
                    '<div class="dropdown--filter fx-dropdown-body">' +
                        renderDropdown() +
                    '</div>' +
                  '</div>'
        })()

        const html = '' +
                  '<div class="filter-header">' +
                  `   <div class="filter-title"><p><strong>${htmlEscape(field.title)}</strong></p></div>` +
                  '       <div class="filter-action btn-group fx-dropdown">' +
                            htmlDropdownMenu +
                  '       </div>' +
                  '   </div>' +
                  '</div>' +
                  `<div class="filter-content">${renderContent()}</div>` +
                  '<div class="filter-footer" hidden>' +
                  '   <div class="title">' +
                  '       <x-i8n key="na.portal.monitoring.home.filters.more"></x-i8n>&nbsp;<span class="how-many-more"></span>' +
                  '   </div>' +
                  '   <div class="button">' +
                  '       <button data-toggle="dropdown" class="dropdown-toggle btn btn-link btn-link-in-table">' +
                  '           <i class="glyphicon glyphicon-chevron-down"></i>' +
                  '       </button>' +
                  '   </div>' +
                  '</div>'

        element.empty().html(html)
        element.find('.dropdown--filter').on('scroll', function() {
          const element = this
          if (element.scrollHeight - element.scrollTop === element.clientHeight) {
            const field = store.getters.fieldInfoOfId(fieldId)
            if (!isLastPage && field.component.pagination === true) {
              getNextPage()
            }
          }
        })
        collapseFilters()
      }
    }
  }
}
