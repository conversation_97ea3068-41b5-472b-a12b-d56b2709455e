/**
 * order state management
 */

const Status = {
  NONE: 'none',
  INFO: 'info',
  SUCCESS: 'success',
  ERROR: 'error'
}

export const initialState = function () {
  return {

    status: {
      validation: Status.NONE,
      execution: Status.NONE,
      response: Status.NONE,
      notification: Status.NONE
    },

    order: {
      id: 0,
      externalOrderId: '',
      category: '',
      description: '',
      state: '',
      stateIcon: '',
      stateI18n: '',
      customer: '',
      customerType: '',
      customerTypeIcon: '',
      technician: '',
      system: '',
      systemEntry: 0,
      items: [],
      orderValidation: null,
      orderResponse: null,
      actionsPermission: true,
      possibleActions: {},
      summary: null
    }
  }
}

export const getters = {
  orderData: function (state) {
    return state.order
  },
  orderSummary: function (state) {
    return state.order.summary
  },
  status: function (state) {
    return state.status
  },
  constantStatus: function () {
    return Status
  }

}

export const setters = {
  setStatus: function (state, data) {
    return {
      ...state,
      status: copyStatus(data, state.status)
    }
  },
  setOrder: function (state, data) {
    return {
      ...state,
      order: {
        ...state.order,
        id: data.orderId,
        category: data.category,
        externalOrderId: data.externalOrderId,
        description: data.description,
        state: data.state,
        stateIcon: data.stateIcon,
        stateI18n: data.stateI18n,
        customer: data.customer,
        customerType: data.customerType,
        technician: data.technician,
        system: data.system,
        systemEntry: data.systemEntry,
        items: data.items,
        orderValidation: data.orderValidation,
        orderResponse: data.orderResponse,
        actionsPermission: data.actionsPermission,
        possibleActions: data.possibleActions
      }
    }
  },
  setOrderSummary: function (state, summary) {
    return {
      ...state,
      order: {
        ...state.order,
        summary: summary
      }
    }
  }
}

function copyStatus(status, oldStatus) {
  const valueSet = Object.keys(Status).reduce(function (acc, key) {
    acc[Status[key]] = Status[key]
    return acc
  }, {})
  return {
    validation: valueSet[status.validation] || oldStatus.validation,
    execution: valueSet[status.execution] || oldStatus.execution,
    response: valueSet[status.response] || oldStatus.response,
    notification: valueSet[status.notification] || oldStatus.notification
  }
}
