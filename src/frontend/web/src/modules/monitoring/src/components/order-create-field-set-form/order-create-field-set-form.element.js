import '~nowc/components/collapsible/collapsible.element'
import '~components/i18n/i18n.element'
import '~nowc/components/input-container/input-container.element'
import '../dynamic-properties-form/dynamic-properties-form.element'
import html from './order-create-field-set-form.element.html'
import { createLazyTemplate } from '~utils/lazy-load-template.util'
import { CssStyleSheetLoader } from '~utils/stylesheet-utils.util'
const template = createLazyTemplate({ html })

const cssLoad = CssStyleSheetLoader(() => import(/* webpackChunkName: "monitoring/components-styles" */'./order-create-field-set-form.element.inline.css'))
class OrderCreateFieldSetForm extends HTMLElement {
  constructor() {
    super()
    this.attachShadow({ mode: 'open' })
    const { shadowRoot } = this
    cssLoad.then(stylesheet => { shadowRoot.adoptedStyleSheets = [stylesheet] })
    shadowRoot.append(document.importNode(template.content, true))
  }

  get value() {
    return JSON.stringify(this.valueAsObject)
  }

  get valueAsObject() {
    const inputElements = [...this.querySelectorAll('input[data-field]')]
    const values = inputElements.reduce((acc, inputElement) => {
      acc[inputElement.getAttribute('data-field')] = inputElement.value
      return acc
    }, {})
    const properties = this.shadowRoot.querySelector('[data-field="properties"]').valueAsObject
    return {
      ...values,
      properties
    }
  }

  set value(value) {
    this.valueAsObject = JSON.parse(value)
  }

  set valueAsObject(value) {
    const inputElements = [...this.querySelectorAll('input[data-field]')]
    inputElements.forEach(inputElement => {
      inputElement.value = value[inputElement.getAttribute('data-field')] || ''
    })
    this.shadowRoot.querySelector('[data-field="properties"]').valueAsObject = value?.properties
  }
}

export const elementTagName = 'x-na-monitoring-order-create-field-set-form'
customElements.define(elementTagName, OrderCreateFieldSetForm)
