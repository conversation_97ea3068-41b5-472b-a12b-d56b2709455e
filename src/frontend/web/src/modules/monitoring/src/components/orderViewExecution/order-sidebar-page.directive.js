import { monitoringModule } from '~monitoring/src/monitoring.angular-module'
import { providers } from '~monitoring/src/monitoring-providers'
import { i18n } from '~utils/i18n'
import { getStatesPriority, getStateByValueMap } from '~monitoring/src/common/settings.service'
import { store } from '~monitoring/src/components/orderView/order-view.store'
import { getInitialSelectedItem, getItemById } from '~monitoring/src/components/orderViewExecution/choose-initial-order-item'
import '../sidebar/order-sidebar.plugin'

monitoringModule.directive(providers.pages.order.directives.sidebar, [
  function () {
    async function adaptData(order) {
      const stateByValueMap = await getStateByValueMap()
      function adaptOrderItem(data) {
        const extendedOrder = { id: data.id, orderId: order.id, ...data }
        if (data.state) {
          const state = stateByValueMap[data.state]
          if (state != null) {
            extendedOrder.stateI18n = state.text
            extendedOrder.stateIcon = state.icon
          }
        }
        extendedOrder.items = data.items.map(adaptOrderItem)
        return extendedOrder
      }

      return order.items.map(adaptOrderItem)
    }

    function sidebarSettings(data, selectedId, serviceIconClass, store) {
      return {
        data,
        initialSelectedId: selectedId,
        onSelect: function (item, data) {
          store.setters.setCurrentOrderItem(data)
        },
        renderItem: function (item, data) {
          const $item = $(item)
          $item.append('<p>' + data.name + '</p>')
          $item.append('<i class="' + serviceIconClass[data.serviceType] + '"></i>')
          $item.append(data.service + '<br/>')

          if (data.state) {
            $item.append('<span class="badge ' + data.stateIcon + '">' + data.stateI18n + '</span>')
          }
        }
      }
    }

    const serviceIconClass = {
      CFS: 'fuxicons fuxicons-client-services',
      RFS: 'fuxicons fuxicons-network-configuration',
      RESOURCE: 'fuxicons fuxicons-resource'
    }

    async function updateSidebarData(scope, initialSelectedItemId) {
      const sidebarData = await adaptData(store.getters.orderData)
      let item
      if (initialSelectedItemId !== '') {
        item = getItemById(sidebarData, initialSelectedItemId)
      } else {
        item = getInitialSelectedItem(await getStatesPriority(), sidebarData)
        initialSelectedItemId = item.id
      }
      return {
        sidebarSettings: sidebarSettings(sidebarData, initialSelectedItemId, serviceIconClass, scope.executionStore),
        currentOrderItem: item
      }
    }

    return {
      restrict: 'A',
      terminal: false,
      link: {
        pre: function (scope, element, attrs) {
          updateSidebarData(scope, attrs.orderItemId).then(({ currentOrderItem, sidebarSettings }) => {
            scope.executionStore.setters.setCurrentOrderItem(currentOrderItem)
            $(element).orderSidebar(sidebarSettings)
          })
        },
        post: function(scope, element, attrs) {
          const executionStore = scope.executionStore
          const storeListener = store.listenStateChange(function (newSnapshot, oldSnapshot) {
            if (newSnapshot.getters.orderData.state !== oldSnapshot.getters.orderData.state) {
              updateSidebarData(scope, attrs.orderItemId).then(({ currentOrderItem, sidebarSettings }) => {
                $(element).empty().orderSidebar(sidebarSettings)
                executionStore.setters.setCurrentOrderItem(currentOrderItem)
              })
            }
          })

          scope.$on('$destroy', function() {
            storeListener.unregister()
          })
        }
      }
    }
  }])
