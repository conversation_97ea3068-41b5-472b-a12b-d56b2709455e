<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title><%= title %></title>
<%= css %></head>
<body>
    <%= navbar %>
    <div class='container app'>
        <h1><%= title %></h1>

        <h3>Dependencies</h3>
        <%= dependencyTable %>

        <h3>Imports</h3>
        <pre><code class="code--import lang-js"></code></pre>

        <h3>Demo</h3>

        <h4>Related Parties field-set List</h4>
        <x-na-monitoring-order-create-field-set-form>
            <div class="form-group form-group--related-party__id">
                <label class="control-label" for="order-attribute-form-input--related-party-id">Related party ID</label>
                <x-input-container>
                    <input data-field="relatedPartyId" id="order-attribute-form-input--related-party-id" class="form-control input-sm input--monitoring-create-attribute-form"/>
                </x-input-container>
            </div>
            <div class="form-group form-group--related-party__role">
                <label class="control-label" for="order-attribute-form-input--related-party-role">Related party role</label>
                <x-input-container>
                    <input data-field="relatedPartyRole" id="order-attribute-form-input--related-party-role" class="form-control input-sm input--monitoring-create-attribute-form"/>
                </x-input-container>
            </div>
        </x-na-monitoring-order-create-field-set-form>

        <pre><code class="xml">
&lt;x-na-monitoring-order-create-field-set-form&gt;
    &lt;div class=&quot;form-group form-group--related-party__id&quot;&gt;
        &lt;label class=&quot;control-label&quot; for=&quot;order-attribute-form-input--related-party-id&quot;&gt;Related party ID&lt;/label&gt;
        &lt;x-input-container&gt;
            &lt;input data-field=&quot;relatedPartyId&quot; id=&quot;order-attribute-form-input--related-party-id&quot; class=&quot;form-control input-sm input--monitoring-create-attribute-form&quot;/&gt;
        &lt;/x-input-container&gt;
    &lt;/div&gt;
    &lt;div class=&quot;form-group form-group--related-party__role&quot;&gt;
        &lt;label class=&quot;control-label&quot; for=&quot;order-attribute-form-input--related-party-role&quot;&gt;Related party role&lt;/label&gt;
        &lt;x-input-container&gt;
            &lt;input data-field=&quot;relatedPartyRole&quot; id=&quot;order-attribute-form-input--related-party-role&quot; class=&quot;form-control input-sm input--monitoring-create-attribute-form&quot;/&gt;
        &lt;/x-input-container&gt;
    &lt;/div&gt;
&lt;/x-na-monitoring-order-create-field-set-form&gt;
        </code></pre>

        <h4>Get value programmatically</h4>

        The field-set-form element respects the html and internal common form conventions, the
        has-basic-form-features.mixin.js is able to automatically fetch its data as long as it
        has the attribute data-field defined.

        To fetch the string value from the element just get it's value property.
        To fetch the value as a JS Object, use the property valueAsObject

        <pre><code class="js">
            const fieldSetElement = document.querySelectorAll([data-field="related-party-form"])
            // String value
            const value = fieldSetElement.value
            // JS Object value
            const valueAsObject = fieldSetElement.valueAsObject
        </code></pre>

        <h4>Sandbox</h4>

        <div class="sandbox__controls">
            <button class="btn" data-action="reset">reset</button>
            <button class="btn" data-action="value">Get value</button>
            <button class="btn" data-action="setValue">Set value</button>
        </div>
        <div class="sandbox_area">
            <x-na-monitoring-order-create-field-set-form data-field="sandbox">
                <div class="form-group form-group--related-party__id">
                    <label class="control-label" for="order-attribute-form-input--sandbox-related-party-id">Sandbox ID</label>
                    <x-input-container>
                        <input data-field="sandbox-id" id="order-attribute-form-input--sandbox-related-party-id" class="form-control input-sm input--monitoring-create-attribute-form"/>
                    </x-input-container>
                </div>
                <div class="form-group form-group--related-party__role">
                    <label class="control-label" for="order-attribute-form-input--sandbox-related-party-role">Sandbox role</label>
                    <x-input-container>
                        <input data-field="sandbox-role" id="order-attribute-form-input--sandbox-related-party-role" class="form-control input-sm input--monitoring-create-attribute-form"/>
                    </x-input-container>
                </div>
            </x-na-monitoring-order-create-field-set-form>
        </div>

    </div>
<%= js %></body>
</html>