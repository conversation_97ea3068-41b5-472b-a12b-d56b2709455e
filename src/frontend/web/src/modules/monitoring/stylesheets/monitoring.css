
/* tables */

#monitoring table td.expandable-column-cell {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    max-width: 1px;
}

#monitoring td.expandable-column-cell.focus {
    overflow: visible;
    white-space: normal;
    word-break: break-all;
    max-width: unset;
}

#monitoring table td.expandable-column-cell a{
    display: inline-block;
    max-width: 100%;
}


/* header */

#entity-header {
    background: #fff;
    border: 1px solid #d2d2d2;
}

#header-info {
    height: 55px;
    line-height: 55px;
}

#header-search {
    padding: 8px 15px;
    background-color: white;
}

.monitoring-header h1 {
    display: inline-block;
    font-size: 25px;
    margin: 0;
    padding: 10px 0 15px 30px;
}

.button--monitoring-order-create {
    float: right;
    margin: 10px 10px 15px 30px;
}


/*****************************
 *    Horizontal Splitter    *
 *****************************/

#horizontal-splitter .toggler {
    height: 10px;
    line-height: 10px;
    margin: 0 auto;
    text-align: center;
}

#horizontal-splitter .toggler a {
    height: 10px;
    width: 10px;
    line-height: 10px;
    margin: 0 auto;
    text-align: center;
}

#horizontal-splitter .toggler span {
    height: 10px;
    width: 50px;
    display: inline-block;
}

#entity-content,
#horizontal-splitter {
    background: #f0f0f0;
}

#entity-content {
    padding: 30px;
}

/* table */

#entity-content table .badge {
    padding: 5px 20px;
}

/********************
 *    ORDER SEARCH    *
 ********************/

.page--monitoring-order-search {
    height: 100%;
    overflow-y: auto;
}

/********************
 *    ORDER VIEW    *
 ********************/
.page--order-view {
    overflow-y: scroll;
    display: flex;
    flex-direction: column;
}

.page--order-view .page__header{
    flex-grow: 0;
    background: #f0f0f0;
    display: flex;
}

.page--order-view  .page__content{
    flex-grow: 1;
    background-color: white;
}

.page--order-view .graph-container {
    overflow: visible;
}


.page--order-view .header .filter-badge {
    margin-left: 10px;
}

.page--order-view .header .order-details {
    margin-top: 20px;
}

.page--order-view .right {
    background-color: white;
    overflow: hidden;
    padding: 35px 20px 0;
    vertical-align: top;
}

.summary.summary--monitoring-order-view {
    padding: 20px 30px 0 20px;
    display: flex;
    flex-basis: 75%;
    flex-grow: 1;
}

.summary__info {
    display: flex;
    flex-direction: column;
}

.summary__info-title {
    display: flex;
}

.summary__name {
    margin-top: 0;
}

.summary--monitoring-order-view .summary__description:after {
    content:"";
    display:inline-block;
    width:0;
}

.summary--monitoring-order-view.fx-constraint.two-rows .fx-info-icon {
    background: transparent;
    color: #666;
    margin: 0 10px 0 0;
}

.summary--monitoring-order-view .summary__icon {
    font-size: 32px;
    padding: 0;
    margin: 0;
}

.monitoring-order-view__execution-info {
    background-color: white;
    vertical-align: top;
    position: relative;
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 100%;
}

.monitoring-order-view__execution-details-table-container {
    padding: 35px 20px 0;
}

.monitoring-order-view__execution-details-table {
    margin-bottom: 0;
}

.monitoring-order-view__execution-details-table > thead > tr > th {
    border: none;
}

.monitoring-order-view__execution-details-table > thead > tr > th,
.monitoring-order-view__execution-details-table > tbody > tr > td {
    border-top: none;
    vertical-align: middle;
}

table.monitoring-order-view__execution-details-table > tbody > tr:last-child > td {
    border-bottom: 0;
}

.timeline .fa-stack {
    width: 2em;
}

.monitoring-order-view__execution-details-table > tbody > tr.data {
    border: 1px solid #333;
}

.monitoring-order-view__execution-item-splitter {
    flex-grow: 1;
}

.tab-content--order-view-response-page > .tab-pane {
    height: 100%;
}


/****************************
 *  Activity Action Modal   *
 ****************************/

.monitoring-order-view__execution-action-modal .modal-header {
    font-weight: normal;
}

.monitoring-order-view__execution-action-modal .modal-body form {
    margin: 20px 20px 40px 0;
}

.monitoring-order-view__execution-action-modal .modal-body form textarea {
    width: 100%;
    height: 100px;
    border: 1px solid var(--color-border-input);
}

.monitoring-order-view__execution-action-modal .modal-body .actions-list {
    padding-bottom: 40px;
}

.monitoring-order-view__execution-action-modal .modal-body .actions-list .action-item {
    padding: 20px 0 0 5px;
    vertical-align: top;
}

.monitoring-order-view__execution-action-modal .modal-body .actions-list .action-item .action-title {
    font-weight: bold;
}

.monitoring-order-view__execution-action-modal .modal-body .actions-list .action-item i {
    padding-right: 5px;
}

.monitoring-order-view__execution-action-modal .modal-body .actions-list .action-item .action-notes {
    padding: 5px 0 0 12px;
}

#delivery-attempts-table tbody > tr:not(.child) > td {
    padding: 9px 5px;
}

#delivery-attempts-table tbody > tr.child .details {
    margin-bottom: 4px;
}

/**********************
 *  Execution View    *
 **********************/

.page--monitoring-order-execution {
    display: flex;
}

.pane--monitoring-timeline-pane {
    padding-left: 20px;
    min-width: 300px;
    width: 50%
}

.graph--monitoring-order-view {
    display: block;
    min-height: 500px;
    position: relative;
    width: 100%;
    height: 100%;
}


.graph--monitoring-order-view.graph--loading {
    display: none;
}

.graph__edge-area--traveled {
    stroke: black;
    fill: black;
    stroke-width: 1;
    color: black;
}

.monitoring-order-view__execution-item-splitter::part(separator) {
    background-color: #e5e5e5;
}

/**********************
*  Response View   *
 **********************/

#response-retries-table,
#validationErrorMessages {
    background-color: white;
}

#response-retries-table > tbody > tr:last-child > td,
#validationErrorMessages > tbody > tr:last-child > td {
    border-bottom: 0;
}

/**********************
 *  Validation View   *
 **********************/

.validation-page {
    background-color: white;
    display: flex;
    flex-direction: column;
}

.validation-page__content {
    flex: 1;
    overflow-y: hidden;
}

.validation-page__request,
.validation-page__response {
    height: 100%;
}


.validation-page .tab-validation-response-detail.active {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.content--order-view-validation-response-detail {
    flex: 1;
    overflow-y: hidden;
    margin: -20px -30px -10px -30px;
    padding: 20px 30px 10px 30px;
    z-index: 1;
}


.validation-page .toolbar {
    align-self: flex-end;
    z-index: 2;
}

.validation-page .toolbar .btn {
    font-size: 18px;
}

.validation-page .status-diagram {
    margin-left: 20px;
}

.validation-page .validation-page__status {
    padding: 10px 0;
}

.monitoring-order-view-validation__page_header {
    display: flex;
    padding-top: 20px;
}

.monitoring-order-view-validation__page_header .model-info {
    flex: 1;
}



/***********************
 *  Common             *
 ***********************/

.page--monitoring .CodeMirror-scroll {
    height: auto;
    overflow: visible !important;
    margin-bottom: 0;
    margin-top: 0;
}


.monitoring-order-view__response-page {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.monitoring-order-view__page_header {
    padding: 10px;
}

.monitoring-order-view__page_status {
    padding-top: 15px;
    padding-bottom: 10px;
}

.dropdown-list--monitoring-order-view-actions {
    position: relative; 
    margin-left: 50px; 
    align-self: flex-start;
}

.dropdown-list--monitoring-order-view-actions #bulkOperationsDropdown {
    width: max-content;
}


.dropdown-menu__link {
    color: #666;
    padding: 3px 20px;
    width: 100%;
    text-align: left;
    text-decoration: none;
    background: none;
    box-shadow: none;
    border: none;
}

.dropdown-menu__link:hover {
    background: #fff9de;
}

.dropdown-menu__link.disabled,
.dropdown-menu__link[disabled]{
    color: #999;
    background: none;
    pointer-events: none;
}

/********************
 *    ORDER VIEW    *
 ********************/

.container--monitoring-ext-sys-interaction-history-left-pane {
    padding: 0.5em;
}

#external-system-interactions-history-table td.description {
    text-align: center;
    vertical-align: middle;
}

.filter__input-container {
    padding-right: 15px;
    padding-left: 15px;
}

.filter__input-container > .fx-search-input {
    position: relative
}


.filter--monitoring-ext-sys-interaction-history .filter__link {
    font-weight: bold;
    padding: 0.2em;
    color: #999;
    border-radius: 0.2em;
    border: solid 1px transparent;
}

.filter--monitoring-ext-sys-interaction-history .filter__link--active {
    border: solid 1px #666;
    color: #666
}
.filter--monitoring-ext-sys-interaction-history .filter__link:hover {
    cursor: pointer;
    background: #eee;
}

.filter--monitoring-ext-sys-interaction-history .filter__group {
    flex: 1;
}

.filter--monitoring-ext-sys-interaction-history .filter__group-link:not(:last-child)::after {
    content: " | ";
    pointer-events: none;
}

.filter.filter--monitoring-ext-sys-interaction-history {
    display: flex;
    flex-flow: row;
    margin-bottom: 1em;
}

.modal--operation-attributes {
    & .modal-dialog {
        overflow: hidden;
    }

    & .modal-body {
        overflow: auto;
    }
}

.tab-pane.active {
    padding: 0;
}

a {
    cursor: pointer;
}

body {
    overflow: hidden;
}

.order-selection_container .notification__message {
    display: inline-block;
}


.summary-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    max-width: 160px;
    padding: 6px 12px;
    border: 1px solid #ccc;
    border-radius: 6px;
    background-color: #f8f9fa;
    overflow: hidden;
}

.summary-icon {
    flex-shrink: 0;
    flex-grow: 0;
    width: 24px;
    height: 24px;
    display: inline;
}

.summary-text {
    flex-shrink: 1;
    min-width: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}