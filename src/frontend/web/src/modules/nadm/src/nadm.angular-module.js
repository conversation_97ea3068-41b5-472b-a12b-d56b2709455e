import { moduleName as baseModule } from '~basemodule/app/moduleDeclarations/basemodule.angular-module'
import { moduleName as TableModule } from '~components/naDatatables-v2/datatable.angular-module'
import { moduleName as validationModule } from '~components/validation/validation.angular-module'
import { moduleName as datetimepickerModule } from '~components/datetimepicker/datetimepicker.angular-module'
import { moduleName as inputsModule } from '~components/inputs/inputs.angular-module'
import { moduleName as goModule } from '~go/src/go.angular-module'
import { modules as nadmModules } from './nadm-providers'
import { module } from 'angular'

export const moduleName = nadmModules.nadm

module(nadmModules.nadm, [
  baseModule,
  TableModule,
  validationModule,
  datetimepickerModule,
  goModule,
  inputsModule
])

export const nadmModule = module(moduleName)

export default nadmModule
