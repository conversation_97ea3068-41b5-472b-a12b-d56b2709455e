import tagsManagementFormStore from './tags-management-form.store'
import { htmlEscape } from '~utils/string.util'
import nadmModule from '../../nadm.angular-module'
import routes from '../../common/routes'
import { providers } from '../../nadm-providers'
import i18n from '../../common/i18n.service'

const template = () => `<div id="manage-tags-form" class="tags-form tags-form--modal"><p>
    <div class="btn-group tags-form__amounts">
        <div class="btn btn-default btn-s" title="${i18n('manage.tags.button.selected.equipments')}" type="button"><span
                class="tags-form_equipments-amount"></span>&nbsp;${i18n('manage.tags.button.selected.equipments')}
        </div>
        <div class="btn btn-default btn-s tags-form_associated-tags-button" title="${i18n('manage.tags.button.associated.tags.title')}" type="button">
            <span class="tags-form_associated-tags-amount"></span>&nbsp;${i18n('manage.tags.button.associated.tags')}
            <div class="overlay tags-form_associated-tags-overlay" style="opacity: 1;">
                <div class="progress progress-striped active"
                     style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                    <div class="progress-bar wait-progress" role="progressbar" aria-valuenow="100" aria-valuemin="0"
                         aria-valuemax="100" style="width: 100%;padding:0 15px;">${i18n('manage.tags.button.calculating')}
                    </div>
                </div>
            </div>
        </div>
        <div class="btn btn-default btn-s" title="${i18n('manage.tags.button.common.tags.title')}" type="button"><span
                class="tags-form_common-tags-amount"></span>&nbsp;${i18n('manage.tags.button.common.tags')}
        </div>
    </div>
    </p><p>&nbsp;</p>
    <div class="tags-form__input">
        <label for="tags" class="col-sm-2 control-label">${i18n('manage.tags.select.label')}</label>
        <x-multiselect
          id="tags"
          data-field="tags"
          full-width
          hide-selected-values
          has-clear-button
          adapter="nadm-tags"
          has-tags-with-label="${i18n('manage.tags.select.newTag')}"
          url="${routes.na.nadm.controllers.Tag.tags().url}"
          placeholder="${i18n('manage.tags.select.placeholder')}">
        </x-multiselect>
    </div>
</div>`

nadmModule.directive(providers.equipment.directives.tagsManagementForm, [
  function() {
    return {
      restrict: 'A',
      priority: 1,
      template: template(),
      link: {
        pre: function(scope, element) {
          const store = tagsManagementFormStore
          const $tagsSelect = element.find('[data-field="tags"]')
          $tagsSelect.empty()
          store.getters.commonTags.forEach(function (tag) {
            $(`<option value="${htmlEscape(tag)}" selected>${htmlEscape(tag)}</option>`).appendTo($tagsSelect)
          })
        },
        post: function(scope, element) {
          const store = tagsManagementFormStore

          const $equipmentsAmount = element.find('.tags-form_equipments-amount')
          const $associatedTagsAmount = element.find('.tags-form_associated-tags-amount')
          const $associatedTagsOverlay = element.find('.tags-form_associated-tags-overlay')

          const $commonTagsAmount = element.find('.tags-form_common-tags-amount')
          const $tagsSelect = element.find('[data-field="tags"]')
          const $tagsAmounts = element.find('.tags-form__amounts')

          function updateAmountsImmediately(params) {
            const animate = params != null && params.animate === true

            $equipmentsAmount.text(store.getters.equipmentsAmount)
            if (store.getters.areAssociationTagsCalculated) {
              if (animate) {
                $associatedTagsOverlay.fadeOut()
              } else {
                $associatedTagsOverlay.hide()
              }
              $associatedTagsAmount.text(store.getters.associationTagsAmount)
            } else {
              $associatedTagsOverlay.fadeIn()
            }

            $commonTagsAmount.text(store.getters.commonTagsAmount)
          }

          $tagsSelect.on('change', function () {
            const value = $(this).val() || []
            store.setters.setCommonTags(value)
          })

          function updateAmounts(snapshot) {
            requestAnimationFrame(function () {
              if (snapshot.state.isMultiple) {
                updateAmountsImmediately({ animate: true })
              }
            })
          }

          $tagsAmounts.toggle(store.state.isMultiple)
          updateAmountsImmediately({ animate: false })

          const listener = store.listenStateChange(updateAmounts)

          scope.$on('$destroy', listener.unregister)
        }
      }
    }
  }
])
