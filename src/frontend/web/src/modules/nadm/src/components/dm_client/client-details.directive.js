import nadmModule from '../../nadm.angular-module'
import { providers } from '../../nadm-providers'
import { labels, updateBreadcrumb } from '../../common/breadcrumb.service'

nadmModule.directive(providers.client.directives.detail, [
  clientDetailsPageDirective
])

function clientDetailsPageDirective() {
  return {
    restrict: 'A',
    priority: 1,
    link: {
      pre: function (scope, elem, attr) {
        scope.clientId = attr.clientId
      },
      post: function (scope) {
        updateBreadcrumb([
          labels.client.search(),
          labels.client.view(scope.clientId)
        ])
      }
    }
  }
}
