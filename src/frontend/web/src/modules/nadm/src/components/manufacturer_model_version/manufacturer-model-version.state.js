import { selector } from '~utils/selector.util'
import { deepClone } from '~utils/deep-clone.util'
import deepEquals from '~utils/deep-equals.util'
import { isPlainObject } from '~utils/object-utils.util'

const emptyArray = Object.freeze([])

const initialStateObj = Object.freeze({
  manufacturer: '',
  model: '',
  version: '',
  manufacturerOptions: emptyArray,
  modelOptions: emptyArray,
  versionOptions: emptyArray
})

export const initialState = () => initialStateObj

const getManufacturer = state => state.manufacturer
const getModel = state => state.model
const getVersion = state => state.version
const getManufacturerOptions = state => state.manufacturerOptions
const getModelOptions = state => state.modelOptions
const getVersionOptions = state => state.versionOptions

const getSelectedValues = selector(
  [getManufacturer, getModel, getVersion],
  (manufacturer, model, version) => ({ manufacturer, model, version })
)

const getIsSelectedValid = selector(
  [getManufacturer, getModel, getVersion],
  (manufacturer, model, version) => {
    return manufacturer && manufacturer.trim() && model && model.trim() && version && version.trim()
  }
)

const getManufacturerQueryParams = selector(
  [getModel, getVersion],
  (model, version) => {
    const query = {}

    if (model) {
      query.model = model
    }

    if (version) {
      query.version = version
    }

    return query
  }
)

const getModelQueryParams = selector(
  [getManufacturer, getVersion],
  (manufacturer, version) => {
    const query = {}

    if (manufacturer) {
      query.manufacturer = manufacturer
    }

    if (version) {
      query.version = version
    }

    return query
  }
)

const getVersionQueryParams = selector(
  [getManufacturer, getModel],
  (manufacturer, model) => {
    const query = {}

    if (manufacturer) {
      query.manufacturer = manufacturer
    }

    if (model) {
      query.model = model
    }

    return query
  }
)

export const getters = {
  manufacturer: getManufacturer,
  model: getModel,
  version: getVersion,
  manufacturerOptions: getManufacturerOptions,
  modelOptions: getModelOptions,
  versionOptions: getVersionOptions,
  selectedValues: getSelectedValues,
  isSelectedValid: getIsSelectedValid,
  manufacturerQueryParams: getManufacturerQueryParams,
  modelQueryParams: getModelQueryParams,
  versionQueryParams: getVersionQueryParams
}

const resetState = () => initialStateObj

const setManufacturer = (state, manufacturer) => {
  if (manufacturer == null) {
    return state
  }

  if (typeof manufacturer !== 'string') {
    return state
  }

  return { ...state, manufacturer }
}

const setModel = (state, model) => {
  if (model == null) {
    return state
  }

  if (typeof model !== 'string') {
    return state
  }

  return { ...state, model }
}

const setVersion = (state, version) => {
  if (version == null) {
    return state
  }

  if (typeof version !== 'string') {
    return state
  }

  return { ...state, version }
}

const setValues = (state, values) => {
  if (!isPlainObject(values)) {
    return state
  }

  const state1 = setManufacturer(state, values.manufacturer)
  const state2 = setModel(state1, values.model)
  const state3 = setVersion(state2, values.version)

  return state3
}

const setManufacturerOptions = (state, manufacturerOptions) => {
  if (manufacturerOptions == null) {
    return state
  }

  if (!Array.isArray(manufacturerOptions)) {
    return state
  }

  if (manufacturerOptions.some(option => !option.id || !option.text)) {
    return state
  }

  if (deepEquals(state.manufacturerOptions, manufacturerOptions)) {
    return state
  }

  return { ...state, manufacturerOptions: deepClone(manufacturerOptions) }
}

const setModelOptions = (state, modelOptions) => {
  if (modelOptions == null) {
    return state
  }

  if (!Array.isArray(modelOptions)) {
    return state
  }

  if (modelOptions.some(option => !option.id || !option.text)) {
    return state
  }

  if (deepEquals(state.modelOptions, modelOptions)) {
    return state
  }

  return { ...state, modelOptions: deepClone(modelOptions) }
}

const setVersionOptions = (state, versionOptions) => {
  if (versionOptions == null) {
    return state
  }

  if (!Array.isArray(versionOptions)) {
    return state
  }

  if (versionOptions.some(option => !option.id || !option.text)) {
    return state
  }

  if (deepEquals(state.versionOptions, versionOptions)) {
    return state
  }

  return { ...state, versionOptions: deepClone(versionOptions) }
}

export const setters = {
  resetState,
  setManufacturer,
  setModel,
  setVersion,
  setValues,
  setManufacturerOptions,
  setModelOptions,
  setVersionOptions
}
