import { provide as provideI18n } from '~utils/i18n/provider'
import { provide as provideToast } from '~utils/toast-notification/provider'

const i18nMap = {
  'na.portal.nadm.modal.clientName.field.label': 'Nome do cliente',
  'na.portal.nadm.modal.clientName.field.description': 'Des<PERSON>ri<PERSON>',
  'na.basemodule.validations.mandatory': 'campo de preenchimento obrigatório',
  'na.portal.nadm.invalid.client.name': 'Nome de cliente inválido',
}

provideI18n({
  translate: (key) => i18nMap[key] || key,
  tokenize: (key) => [{ type: 'text', text: i18nMap[key] || key }]
})

provideToast({
  hideToast: () => console.log('hide toast notification called')
})
