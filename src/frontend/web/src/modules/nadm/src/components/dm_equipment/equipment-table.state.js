import { selector } from '~utils/selector.util'
import { deepClone } from '~utils/deep-clone.util'
import { isPlainObject } from '~utils/object-utils.util'

const emptyArray = Object.freeze([])
const emptyObject = Object.freeze({})

const allExceptNumbersAndLettersRegex = /[^a-zA-Z1-9]+/g

const initialStateObj = Object.freeze({
  operations: emptyObject,
  diagnosticsOperations: emptyObject,
  cloneOperationName: emptyObject,
  tableRowsData: emptyArray,
  rowsIdFilter: emptyArray,
  hasSelectionColumn: true,
  columns: emptyArray,
  sortInfo: emptyArray,
  totalRecords: 0,
  errorRowsId: emptyArray,
  totalExecutedRows: 0,
  errorExecParams: emptyObject,
  allRecordsSelected: false,
  isTableDataInErrorMode: false,
  // necessary due to the backend having some limitations regarding the length of a query
  maxRecordsCharsSelectable: 8048
})

export const initialState = () => initialStateObj

const isDataColumn = column => column.property != null

const tableRowsData = state => state.tableRowsData
const rowsIdFilter = state => state.rowsIdFilter
const allRecordsSelected = state => state.allRecordsSelected
const hasSelectionColumn = state => state.hasSelectionColumn
const operations = state => state.operations
const diagnosticsOperations = state => state.diagnosticsOperations
const totalRecords = state => state.totalRecords
const cloneOperationName = state => state.cloneOperationName
const columns = state => state.columns
const errorRowsId = state => state.errorRowsId
const totalExecutedRows = state => state.totalExecutedRows
const errorExecParams = state => state.errorExecParams
const isTableDataInErrorMode = state => state.isTableDataInErrorMode
const sortInfo = state => state.sortInfo
const maxRecordsCharsSelectable = state => state.maxRecordsCharsSelectable

const pageLength = selector(
  [tableRowsData],
  tableRowsData => tableRowsData.length
)

const isTableEmpty = selector(
  [pageLength],
  pageLength => pageLength <= 0
)

const tableRowsDataIds = selector(
  [tableRowsData],
  tableRowsData => {
    const rowsIds = []
    tableRowsData.forEach(row => { rowsIds.push(row.identifier) })
    return rowsIds
  }
)

const selectedTableRows = selector(
  [rowsIdFilter, tableRowsData, allRecordsSelected],
  (rowsIdFilter, tableRowsData, allRecordsSelected) => tableRowsData.reduce((acc, row, index) => {
    const inFilter = rowsIdFilter.includes(row.identifier)
    acc[index] = allRecordsSelected ? !inFilter : inFilter
    return acc
  }, {})
)

const areAllTableRowsSelected = selector(
  [isTableEmpty, selectedTableRows],
  (isTableEmpty, selectedTableRows) =>
    !isTableEmpty && Object.keys(selectedTableRows).every(key => selectedTableRows[key] === true)
)

const areNoTableRowsSelected = selector(
  [isTableEmpty, selectedTableRows],
  (isTableEmpty, selectedTableRows) =>
    isTableEmpty || Object.keys(selectedTableRows).every(key => selectedTableRows[key] === false)
)

const selectedRowsData = selector(
  [selectedTableRows, tableRowsData],
  (selectedTableRows, tableRowsData) =>
    Object.keys(selectedTableRows).filter(key => selectedTableRows[key]).map(key => tableRowsData[+key])
)

const recordsCharsSelected = selector(
  [rowsIdFilter],
  rowsIdFilter => rowsIdFilter.reduce((acc, rowId) => {
    const extendedRowId = rowId.replace(allExceptNumbersAndLettersRegex,
      val => '%' + val.charCodeAt(0).toString(16))
    const lastElementId = rowsIdFilter[rowsIdFilter.length - 1]
    // "'" + ID + "'" + "," -> 'ID'
    acc += '%27'.length + extendedRowId.length + '%27'.length + (lastElementId === rowId ? 0 : '%3A'.length)
    return acc
  }, 0)
)

const closeToMaxRecordsCharsAllowed = selector(
  [maxRecordsCharsSelectable, recordsCharsSelected],
  (maxRecordsCharsSelectable, recordsCharsSelected) =>
    maxRecordsCharsSelectable - recordsCharsSelected <= 50
)

const maxRecordsCharsExceeded = selector(
  [maxRecordsCharsSelectable, recordsCharsSelected],
  (maxRecordsCharsSelectable, recordsCharsSelected) =>
    maxRecordsCharsSelectable - recordsCharsSelected <= 0
)

const areAllRecordsSelected = selector(
  [allRecordsSelected, rowsIdFilter],
  (allRecordsSelected, rowsIdFilter) => allRecordsSelected && rowsIdFilter.length <= 0
)

const totalSelectedRecords = selector(
  [allRecordsSelected, rowsIdFilter, totalRecords],
  (allRecordsSelected, rowsIdFilter, totalRecords) =>
    allRecordsSelected ? totalRecords - rowsIdFilter.length : rowsIdFilter.length
)

const areNoRecordsSelected = selector(
  [totalSelectedRecords],
  totalSelectedRecords => totalSelectedRecords <= 0
)

const cloneOperation = selector(
  [operations, cloneOperationName],
  (operations, cloneOperationName) => {
    const id = Object.keys(operations).find(id => cloneOperationName === operations[id].name)
    return id == null ? { name: cloneOperationName } : { id, ...operations[id] }
  }
)

const isCloneOperationValid = selector(
  [cloneOperation],
  cloneOperation => cloneOperation.id != null
)

const visibleColumnMap = selector(
  [columns],
  columns => columns.reduce((acc, column) => {
    acc[column.name] = column.visible
    return acc
  }, {})
)

const dataColumns = selector(
  [columns],
  columns => columns.filter(isDataColumn)
)

const visibleDataColumns = selector(
  [dataColumns],
  dataColumns => dataColumns.filter(column => column.visible)
)

const areAllDataColumnsHidden = selector(
  [visibleDataColumns],
  visibleDataColumns => visibleDataColumns.length <= 0
)

const visibleDataColumnProperties = selector(
  [visibleDataColumns],
  visibleDataColumns => visibleDataColumns.map(column => column.property)
)

const hasErrorRows = selector(
  [errorRowsId],
  errorRowsId => errorRowsId.length > 0
)

const isInErrorMode = selector(
  [totalExecutedRows],
  totalExecutedRows => totalExecutedRows > 0
)

const totalRowsInError = selector(
  [errorRowsId],
  errorRowsId => errorRowsId.length
)

const totalFailedRows = selector(
  [errorRowsId, totalRecords],
  (errorRowsId, totalRecords) => errorRowsId.length > 0 ? errorRowsId.length : totalRecords
)

const visibleRowsData = selector(
  [tableRowsData, errorRowsId],
  (tableRowsData, errorRowsId) => {
    if (errorRowsId.length > 0) {
      const errorRowsIdSet = new Set(errorRowsId)
      return tableRowsData.filter(rowData => errorRowsIdSet.has(rowData.identifier))
    } else {
      return tableRowsData
    }
  }
)

export const getters = {
  operations,
  diagnosticsOperations,
  tableRowsData,
  selectedTableRows,
  allRecordsSelected,
  rowsIdFilter,
  totalRecords,
  columns,
  sortInfo,
  hasSelectionColumn,
  cloneOperationName,
  errorRowsId,
  totalExecutedRows,
  isTableDataInErrorMode,
  errorExecParams,
  maxRecordsCharsSelectable,
  isTableEmpty,
  pageLength,
  selectedRowsData,
  areNoTableRowsSelected,
  areNoRecordsSelected,
  areAllRecordsSelected,
  tableRowsDataIds,
  areAllTableRowsSelected,
  totalSelectedRecords,
  cloneOperation,
  isCloneOperationValid,
  visibleColumnMap,
  dataColumns,
  areAllDataColumnsHidden,
  visibleDataColumns,
  visibleDataColumnProperties,
  hasErrorRows,
  isInErrorMode,
  visibleRowsData,
  totalRowsInError,
  totalFailedRows,
  closeToMaxRecordsCharsAllowed,
  maxRecordsCharsExceeded
}

const resetState = () => initialStateObj

const setOperations = (state, params) => {
  if (params == null) {
    return state
  }

  if (params.operations == null) {
    return state
  }

  const cloneOperationName = typeof (params.cloneOperationName) === 'string' ? params.cloneOperationName : ''

  return {
    ...state,
    cloneOperationName,
    operations: deepClone(params.operations)
  }
}

const setDiagnosticsOperations = (state, params) => {
  if (params == null) {
    return state
  }

  if (params.diagnosticsOperations == null) {
    return state
  }

  return {
    ...state,
    diagnosticsOperations: deepClone(params.diagnosticsOperations)
  }
}

const setTableRows = (state, params) => {
  if (params == null) {
    return state
  }

  if (!Array.isArray(params.rowsData)) {
    return state
  }

  if (!Array.isArray(params.sortInfo)) {
    return state
  }

  if (typeof params.totalRecords !== 'number' && params.totalRecords <= 0) {
    return state
  }

  return {
    ...state,
    tableRowsData: deepClone(params.rowsData),
    totalRecords: params.totalRecords,
    sortInfo: params.sortInfo.map(columnSort => ({ ...columnSort })),
    isTableDataInErrorMode: isInErrorMode(state)

  }
}

function resetSelectionOnEmptySelection(state) {
  if (!state.allRecordsSelected || totalSelectedRecords(state) > 0) {
    return state
  }

  return {
    ...state,
    allRecordsSelected: false,
    rowsIdFilter: emptyArray
  }
}

const toggleSelectAllTableRows = (state, params) => {
  if (params == null) {
    return state
  }

  if (typeof params.selected !== 'boolean') {
    return state
  }

  if (params.selected && areAllTableRowsSelected(state)) {
    return state
  }

  if (!params.selected && areNoTableRowsSelected(state)) {
    return state
  }

  let newSelectedRows

  const ids = state.tableRowsData.map(row => row.identifier)

  if (state.allRecordsSelected ? !params.selected : params.selected) {
    newSelectedRows = Array.from(new Set(state.rowsIdFilter.concat(ids)))
  } else {
    const set = new Set(state.rowsIdFilter)
    ids.forEach(id => set.delete(id))
    newSelectedRows = Array.from(set)
  }

  const result = { ...state, rowsIdFilter: newSelectedRows }
  return resetSelectionOnEmptySelection(result)
}

const toggleTableRowSelectionV2 = (state, params) => {
  if (params == null) {
    return state
  }

  if (typeof params.rowNumber !== 'number') {
    return state
  }

  if (typeof params.selected !== 'boolean') {
    return state
  }

  const areAllSelected = state.allRecordsSelected

  const identifier = state.tableRowsData[params.rowNumber].identifier
  const identifierInFilter = state.rowsIdFilter.includes(identifier)
  const isAlreadySelected = areAllSelected ? !identifierInFilter : identifierInFilter

  if (params.selected && isAlreadySelected) {
    return state
  }
  if (!params.selected && !isAlreadySelected) {
    return state
  }

  let newSelectedRows

  if (areAllSelected ? !params.selected : params.selected) {
    newSelectedRows = state.rowsIdFilter.concat(identifier)
  } else {
    newSelectedRows = state.rowsIdFilter.filter(id => id !== identifier)
  }

  const result = { ...state, rowsIdFilter: newSelectedRows }

  return resetSelectionOnEmptySelection(result)
}

const clearSelection = state => ({
  ...state,
  rowsIdFilter: emptyArray,
  allRecordsSelected: false
})

const selectAllRecords = state => {
  if (state.allRecordsSelected === true && state.rowsIdFilter.length <= 0) {
    return state
  }
  return {
    ...state,
    allRecordsSelected: true,
    rowsIdFilter: emptyArray
  }
}

const unselectAllRecords = state => {
  if (state.allRecordsSelected === false && state.rowsIdFilter.length <= 0) {
    return state
  }

  return {
    ...state,
    allRecordsSelected: false,
    rowsIdFilter: emptyArray
  }
}

const resetRowsIdFilter = state => ({ ...state, rowsIdFilter: emptyArray })

const toggleAllRecordsSelection = state => ({
  ...state,
  allRecordsSelected: !state.allRecordsSelected,
  rowsIdFilter: emptyArray
})

const isEqualColumn = (column1, column2) => column1.name === column2.name &&
  column1.title === column2.title &&
  column1.property === column2.property &&
  column1.visible === column2.visible

const setColumns = (state, columns) => {
  if (!Array.isArray(columns)) {
    return state
  }

  if (state.columns.length === columns.length) {
    let areEqual = true
    for (let i = 0, e = columns.length; i < e; i++) {
      if (!isEqualColumn(state.columns[i], columns[i])) {
        areEqual = false
        break
      }
    }
    if (areEqual) {
      return state
    }
  }

  return {
    ...state,
    columns: columns.map(column => ({
      name: column.name,
      title: column.title,
      property: column.property,
      visible: column.visible
    }))
  }
}

const hideColumn = (state, columnName) => {
  if (typeof columnName !== 'string' || columnName === '') {
    return state
  }

  let changed = false

  const result = state.columns.map(column => {
    if (column.visible && column.name === columnName) {
      changed = true
      return { ...column, visible: false }
    }
    return column
  })
  if (changed) {
    return { ...state, columns: result }
  } else {
    return state
  }
}

const showColumn = (state, columnName) => {
  if (typeof columnName !== 'string' || columnName === '') {
    return state
  }
  let changed = false
  const result = state.columns.map(column => {
    if (!column.visible && column.name === columnName) {
      changed = true
      return { ...column, visible: true }
    }
    return column
  })
  return changed ? ({ ...state, columns: result }) : state
}

const setVisibleDataColumns = (state, newVisibleDataColumnNames) => {
  if (!Array.isArray(newVisibleDataColumnNames)) {
    return state
  }
  const newVisibleDataColumnNameSet = new Set(newVisibleDataColumnNames)
  const oldDataColumns = visibleDataColumns(state)
  const oldDataColumnNames = oldDataColumns.map(column => column.name)
  if (oldDataColumnNames.length === newVisibleDataColumnNameSet.size &&
    oldDataColumnNames.every(name => newVisibleDataColumnNameSet.has(name))) {
    return state
  }

  const result = state.columns.map(column => {
    if (isDataColumn(column) && column.visible !== newVisibleDataColumnNameSet.has(column.name)) {
      return { ...column, visible: newVisibleDataColumnNameSet.has(column.name) }
    }
    return column
  })

  return { ...state, columns: result }
}

const updateErrorRowsIds = (state, newErrorRowsId) => {
  if (!Array.isArray(newErrorRowsId)) {
    return state
  }

  if (newErrorRowsId.some(rowId => typeof rowId !== 'string')) {
    return state
  }

  const errorRowsId = newErrorRowsId.length > 0 ? newErrorRowsId.slice() : emptyArray

  return { ...state, errorRowsId }
}

const setTotalExecutedRows = (state, totalExecutedRows) => {
  if (isNaN(totalExecutedRows) || totalExecutedRows < 0) {
    return state
  }

  if (state.totalExecutedRows === totalExecutedRows) {
    return state
  }

  return { ...state, totalExecutedRows }
}

const setErrorExecParams = (state, execParams) => {
  if (!isPlainObject(execParams)) {
    return state
  }
  if (typeof execParams.name !== 'string') {
    return state
  }

  if (typeof execParams.action !== 'string') {
    return state
  }

  const newExecParams = { name: execParams.name, action: execParams.action }

  switch (execParams.action) {
    case 'manage-tags':
      if (Array.isArray(execParams.tagsToAdd) && Array.isArray(execParams.tagsToRemove)) {
        Object.assign(newExecParams, {
          tagsToAdd: execParams.tagsToAdd,
          tagsToRemove: execParams.tagsToRemove
        })
      } else {
        return state
      }
      break
    case 'delete':
      break
    default:
      return state
  }

  return { ...state, errorExecParams: newExecParams }
}

const enterErrorMode = (state, params) => {
  if (!isPlainObject(params)) {
    return state
  }

  const state1 = updateErrorRowsIds(state, params.errorRowsIds)
  const state2 = setErrorExecParams(state1, params.errorExecParams)
  const state3 = setTotalExecutedRows(state2, params.totalExecutedRows)
  return { ...state3, allRecordsSelected: false }
}

const exitErrorMode = state => {
  if (!isInErrorMode(state)) {
    return state
  }
  return {
    ...state,
    errorRowsId: emptyArray,
    totalExecutedRows: 0
  }
}

export const setters = {
  resetState,
  setOperations,
  setDiagnosticsOperations,
  setTableRows,
  toggleSelectAllTableRows,
  selectAllRecords,
  unselectAllRecords,
  resetRowsIdFilter,
  toggleAllRecordsSelection,
  toggleTableRowSelectionV2,
  clearSelection,
  setColumns,
  hideColumn,
  showColumn,
  setVisibleDataColumns,
  enterErrorMode,
  exitErrorMode
}
