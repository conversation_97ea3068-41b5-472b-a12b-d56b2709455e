/**
 * @typedef {Object} AssociatedResource
 * @property {string} type      - associated resource type
 * @property {Object} values    - associated resource data
 * */

/** @typedef {Object} Resource
 * @property {number} resourceId                        - index key
 * @property {Object} resourceDescriptionI18n           - description, non-null, when not defined is {}
 * @property {string} [resourceDescriptionI18n.key]     - description i18n key
 * @property {string} [resourceDescriptionI18n.label]   - description i18n key fallback
 * @property {string[]} canBecome                       - list of types that the resource can be associated
 * @property {AssociatedResource} associatedResource    - associated resource, non-null, empty resource has type "EmptyResource"
 * */

/** @typedef {Object} Template
 * @property {Object} template      - form information
 * @property {string} resourceKey   - resource key, maps to resource id
 * @property {string} resourceName  - resource name
 * @property {boolean} service      - flag to identity if it is a service
 * */

/** @typedef {Object.<string, Resource>} Resources */
/** @typedef {Object.<string, Template>} Templates */
/** @typedef {Object.<string, string>} HtmlTemplates */

/** @typedef Associations
 * @property {Resources} resources
 * @property {Templates} templates
 * */

/** @typedef {Associations} AssociationsConfig
 * @property {HtmlTemplates} htmlTemplate
 * @property {string} wizardTemplate
 * */

/** @typedef State
 * @property {Resources} originalResources
 * @property {Resources} resources
 * @property {Templates} templates
 * @property {HtmlTemplates} htmlTemplate
 * @property {string} equipmentId
 * @property {string} wizardTemplate
 * */

/**
 * @callback StateGetter
 * @template T
 * @param {State} state
 * @return T
 * */

/**
 * @callback StateSetter
 * @template T
 * @param {State} state
 * @param {T} params
 * @return State
 * */

import { selector } from '~utils/selector.util'
import { isPlainObject } from '~utils/object-utils.util'
import { deepClone } from '~utils/deep-clone.util'

const emptyObject = Object.freeze({})
const emptyResource = Object.freeze({ type: 'EmptyResource', values: {} })

/** @type {Readonly<State>} */
const initialStateObj = Object.freeze({
  resources: emptyObject,
  originalResources: emptyObject,
  templates: emptyObject,
  htmlTemplate: '',
  wizardTemplate: '',
  equipmentId: '',
})

export const initialState = () => initialStateObj

/** @type {StateGetter<Resources>} */
const equipmentId = (state) => state.equipmentId
/** @type {StateGetter<Resources>} */
const resources = (state) => state.resources
/** @type {StateGetter<Associations>} */
const associations = (state) => ({
  resources: state.resources,
  templates: state.templates
})
/** @type {StateGetter<Templates>} */
const templates = (state) => state.templates
/** @type {StateGetter<Templates>} */
const serviceTemplates = selector([templates], (templates) => Object.fromEntries(Object.entries(templates)
  .filter(([_, value]) => value.service)))

/** @type {StateGetter<HtmlTemplates>} */
const htmlTemplate = (state) => state.htmlTemplate
/** @type {StateGetter<string>} */
const wizardTemplate = (state) => state.wizardTemplate
/** @type {StateGetter<Resources>} */
const originalResources = (state) => state.originalResources
/** @type {StateGetter<Associations>} */
const originalAssociations = (state) => ({
  resources: state.originalResources,
  templates: state.templates
})
/** @type {StateGetter<boolean>} */
const resourcesChanged = (state) => state.resources !== state.originalResources
/** @type {StateGetter<string[]>} */
const serviceTypes = (state) => Object.keys(state.templates)

const resourcePredicates = {
  /** @type {function(Resource): boolean} */
  isEmpty: resource => resource.associatedResource.type === emptyResource.type,
  /** @type {function(Resource): boolean} */
  canBecomeMultiple: resource => resource.canBecome.length > 1,
  /** @type {function(string): function(Resource): boolean} */
  canBecomeServiceType: serviceType => resource => resource.canBecome.includes(serviceType),
  /** @type {function(string): function(Resource): boolean} */
  isFilledBy: serviceType => resource => resource.associatedResource.type === serviceType
}

/** @type {StateGetter<Object.<string, Resource[]>>} */
const resourcesPerTemplate = selector([serviceTypes, resources], (servicesTypes, resources) => {
  /** @type {Resource[]} */
  const resourceList = Object.values(resources)
  return servicesTypes.reduce((acc, serviceType) => {
    acc[serviceType] = resourceList.filter(resourcePredicates.canBecomeServiceType(serviceType))
    return acc
  }, {})
})

const firstKeyOfEachModule = selector([resources], resources => {
  const resourceList = Object.values(resources)
  return resourceList.filter(resource => Object.keys(resource.resourceDescriptionI18n).length > 0)
    .map(resource => {
      return {
        resourceDescriptionI18n: resource.resourceDescriptionI18n,
        resourceId: resource.resourceId
      }
    })
})

/** @type {StateGetter<Object.<string, number[]>>} */
const resourcesIdsPerTemplate = selector([resourcesPerTemplate], (resourcesPerTemplate) => {
  return Object.fromEntries(Object.entries(resourcesPerTemplate).map(([key, value]) => {
    return [key, value.map(resource => resource.resourceId)]
  }))
})

/** @type {StateGetter<Object.<string, Resource[]>>} */
const availableResourcesPerTemplate = selector([resourcesPerTemplate], (resourcesPerTemplate) => {
  return Object.fromEntries(Object.entries(resourcesPerTemplate).map(([key, value]) => {
    return [key, value.filter(resourcePredicates.isEmpty)]
  }))
})

/** @type {StateGetter<Object.<string, number[]>>} */
const availableResourcesIdsPerTemplate = selector([availableResourcesPerTemplate], (availableResourcesPerTemplate) => {
  return Object.fromEntries(Object.entries(availableResourcesPerTemplate).map(([key, value]) => {
    return [key, value.map(resource => resource.resourceId)]
  }))
})

/** @type {StateGetter<Object.<string, number[]>>} */
const availableResources = selector([availableResourcesPerTemplate], (availableResourcesPerTemplate) => {
  return [...new Set(Object.keys(availableResourcesPerTemplate).reduce((acc, key) => {
    return acc.concat(availableResourcesPerTemplate[key])
  }, []))]
})

/** @type {StateGetter<Object.<string, Resource[]>>} */
const sharedResourcesPerTemplate = selector([resourcesPerTemplate], (resourcesPerService) => {
  return Object.fromEntries(Object.entries(resourcesPerService).map(([key, value]) => {
    return [key, value.filter(resourcePredicates.canBecomeMultiple)]
  }))
})

/** @type {StateGetter<Object.<string, number[]>>} */
const sharedResourcesIdsPerTemplate = selector([sharedResourcesPerTemplate], (sharedResourcesPerTemplate) => {
  return Object.fromEntries(Object.entries(sharedResourcesPerTemplate).map(([key, value]) => {
    return [key, value.map(resource => resource.resourceId)]
  }))
})

/** @type {StateGetter<Object.<string, Resource[]>>} */
const filledResourcesByTemplate = selector([resourcesPerTemplate], (resourcesPerService) => {
  return Object.fromEntries(Object.entries(resourcesPerService).map(([key, value]) => {
    return [key, value.filter(resourcePredicates.isFilledBy(key))]
  }))
})

/** @type {StateGetter<Object.<string, number[]>>} */
const filledResourcesIdsByTemplate = selector([filledResourcesByTemplate], (filledResourcesByTemplate) => {
  return Object.fromEntries(Object.entries(filledResourcesByTemplate).map(([key, value]) => {
    return [key, value.map(resource => resource.resourceId)]
  }))
})

/** @type {StateGetter<Object.<string, Resource[]>>} */
const filledResourcesByOtherTemplate = selector([sharedResourcesPerTemplate], (resourcesPerService) => {
  return Object.fromEntries(Object.entries(resourcesPerService).map(([key, value]) => {
    const isFilledBy = resourcePredicates.isFilledBy(key)
    const isEmpty = resourcePredicates.isEmpty
    return [key, value.filter(resource => !isEmpty(resource) && isFilledBy(resource))]
  }))
})

/** @type {StateGetter<Object.<string, Resources>>} */
const emptyResourceMap = selector([resources], (resources) => {
  return Object.fromEntries(Object.entries(resources).filter(([_, resource]) => resourcePredicates.isEmpty(resource)))
})

export const getters = {
  equipmentId,
  resources,
  templates,
  serviceTemplates,
  htmlTemplate,
  wizardTemplate,
  associations,
  originalResources,
  originalAssociations,
  availableResources,
  resourcesChanged,
  serviceTypes,
  firstKeyOfEachModule,
  resourcesPerTemplate,
  resourcesIdsPerTemplate,
  availableResourcesPerTemplate,
  availableResourcesIdsPerTemplate,
  sharedResourcesPerTemplate,
  sharedResourcesIdsPerTemplate,
  filledResourcesByTemplate,
  filledResourcesIdsByTemplate,
  filledResourcesByOtherTemplate,
  emptyResourceMap
}

/** @type {StateSetter<void>} */
const resetAssociation = () => initialStateObj

/** @type {StateSetter<void>} */
const revertChanges = (state) => {
  if (state.resources === state.originalResources) {
    return state
  }
  return { ...state, resources: state.originalResources }
}

/** @type {StateSetter<{equipmentId: string, associations: AssociationsConfig}>} */
const initAssociationsOfEquipment = (state, params) => {
  if (!isPlainObject(params)) {
    return state
  }
  const equipmentId = params.equipmentId
  const associations = params.associations
  if (typeof equipmentId !== 'string' || equipmentId === '' || !isPlainObject(associations)) {
    return state
  }
  const resources = associations.resources
  const templates = associations.templates
  const htmlTemplate = associations.htmlTemplate
  const wizardTemplate = associations.wizardTemplate
  if (!isPlainObject(resources) || !isPlainObject(templates) || !isPlainObject(htmlTemplate) ||
      typeof wizardTemplate !== 'string') {
    return state
  }

  const cleanResources = deepClone(resources)
  Object.values(cleanResources).forEach(cleanResource => {
    if (resourcePredicates.isEmpty(cleanResource)) {
      cleanResource.associatedResource = emptyResource
    }
  })

  return {
    resources: cleanResources,
    originalResources: cleanResources,
    templates,
    htmlTemplate,
    wizardTemplate,
    equipmentId
  }
}

/** @type {StateSetter<{resourceId: number}>} */
const revertResource = (state, params) => {
  if (!isPlainObject(params)) {
    return state
  }
  const resourceId = params.resourceId
  if (typeof resourceId !== 'string') {
    return state
  }
  if (state.resources[resourceId] === state.originalResources[resourceId]) {
    return state
  }

  const newResources = { ...state.resources }
  newResources[resourceId] = state.originalResources[resourceId]

  return { ...state, resources: newResources }
}

/** @type {StateSetter<{resourceId: number}>} */
const clearResource = (state, params) => {
  if (!isPlainObject(params)) {
    return state
  }
  const resourceId = params.resourceId
  if (typeof resourceId !== 'number') {
    return state
  }
  if (resourcePredicates.isEmpty(state.resources[resourceId])) {
    return state
  }

  const newResources = { ...state.resources }
  newResources[resourceId] = { ...newResources[resourceId], associatedResource: emptyResource }

  return { ...state, resources: newResources }
}

/** @type {StateSetter<{fromResourceId: number, toResourceId: number, associatedResource: object}>} */
const moveResource = (state, params) => {
  if (!isPlainObject(params)) {
    return state
  }
  const fromResourceId = params.fromResourceId
  const toResourceId = params.toResourceId
  const associatedResource = params.associatedResource
  if (typeof fromResourceId !== 'number' || typeof toResourceId !== 'number' || typeof associatedResource !== 'object') {
    return state
  }
  const srcResource = state.resources[fromResourceId]
  const targetResource = state.resources[toResourceId]

  if (!resourcePredicates.isEmpty(targetResource)) {
    return state
  }
  if (!resourcePredicates.canBecomeServiceType(srcResource.associatedResource.type)(targetResource)) {
    return state
  }

  const newResources = { ...state.resources }
  newResources[toResourceId] = { ...newResources[toResourceId], associatedResource }
  newResources[fromResourceId] = { ...newResources[fromResourceId], associatedResource: emptyResource }

  return { ...state, resources: newResources }
}

/** @type {StateSetter<{resourceId: number, associatedResource: object}>} */
const updateResource = (state, params) => {
  if (!isPlainObject(params)) {
    return state
  }
  const resourceId = params.resourceId
  const associatedResource = params.associatedResource
  if (typeof resourceId !== 'number' || typeof associatedResource !== 'object') {
    return state
  }
  const resource = state.resources[resourceId]

  if (resourcePredicates.isEmpty(resource)) {
    return state
  }

  const newResources = { ...state.resources }
  newResources[resourceId] = { ...newResources[resourceId], associatedResource }

  return { ...state, resources: newResources }
}

/** @type {StateSetter<{resourceId: number , toResourceLine: string}>} */
const moveToLine = (state, params) => {
  if (!isPlainObject(params)) {
    return state
  }
  const toResourceLine = params.toResourceLine
  const resourceId = params.resourceId
  if (typeof toResourceLine !== 'string') {
    return state
  }
  const resource = state.resources[resourceId]

  if (resourcePredicates.isEmpty(resource)) {
    return state
  }

  const newResources = { ...state.resources }
  const { associatedResource } = newResources[resourceId]
  newResources[resourceId] = {
    ...newResources[resourceId],
    associatedResource: {
      ...associatedResource,
      values: {
        ...associatedResource.values,
        'customKey.line': toResourceLine
      }
    }
  }

  return { ...state, resources: newResources }
}

/** @type {StateSetter<{associatedResource: AssociatedResource}>} */
const createResource = (state, params) => {
  if (!isPlainObject(params) || !isPlainObject(params.associatedResource)) {
    return state
  }
  const associatedResource = params.associatedResource

  const template = state.templates[associatedResource.type]
  const resourceId = parseInt(associatedResource.values[template.resourceKey], 10)

  const targetResource = state.resources[resourceId]
  if (!resourcePredicates.isEmpty(targetResource)) {
    return state
  }
  if (!resourcePredicates.canBecomeServiceType(associatedResource.type)(targetResource)) {
    return state
  }

  const newResources = { ...state.resources }
  newResources[resourceId] = { ...newResources[resourceId], associatedResource: deepClone(associatedResource) }

  return { ...state, resources: newResources }
}

export const setters = {
  resetAssociation,
  revertChanges,
  initAssociationsOfEquipment,
  createResource,
  revertResource,
  moveResource,
  updateResource,
  moveToLine,
  clearResource
}
