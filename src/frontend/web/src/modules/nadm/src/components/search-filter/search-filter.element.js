import { CssStyleSheetLoader } from '~utils/stylesheet-utils.util'
import { createLazyTemplate } from '~utils/lazy-load-template.util'
import fuxiStylesheet from '~base-styles/fuxi.stylesheet'
import bootstrapStylesheet from '~base-styles/bootstrap.stylesheet'
import html from './search-filter.element.html'
import routes from '~nadm/src/common/routes'
import { del, get, post, put } from '~utils/proxy'
import { showNotificationOn } from '~utils/element-notification'
import { notifyError, notifySuccess } from '~utils/toast-notification'
import baseI18n, { i18n } from '~utils/i18n'
import '~components/i18n-container/i18n-container.element'
import * as modal from '~utils/modal'
import { htmlEscape } from '~utils/string.util'
import { getStoreForContext } from '~nadm/src/common/search/base-search-form.store'

const cssLoad = CssStyleSheetLoader(() => import(/* webpackChunkName: "web-components/components-styles" */ './search-filter.element.inline.css'))
const template = createLazyTemplate({ html })

const FILTER_CONTEXT_ATTRIBUTE = 'data-filter-context'

const storeContextMapping = {
  NA_EQUIPMENTS: 'equipment',
  NA_GLOBALEQUIPMENTS: 'globalequip',
  NA_SERVICE: 'service'
}

class SearchFilterElement extends HTMLElement {
  constructor() {
    super()

    this.store = getStoreForContext(storeContextMapping[this.context])

    if (!this.store) {
      throw new Error(`Store for context ${this.context} not found`)
    }

    this.attachShadow({ mode: 'open' })
    const { shadowRoot } = this

    cssLoad.then((stylesheet) => {
      shadowRoot.adoptedStyleSheets = [bootstrapStylesheet, fuxiStylesheet, stylesheet]
    })

    shadowRoot.append(document.importNode(template.content, true))
    setUpDropdown(shadowRoot.querySelector('[popover]'), shadowRoot.querySelector('[popovertarget]'))

    const saveFilterButton = shadowRoot.getElementById('nadm-search-filter__save')
    const filterNameInput = shadowRoot.getElementById('nadm-search-filter__name')
    const filterSelect = this.renderFilterSelect()
    const deleteButton = shadowRoot.getElementById('nadm-search-filter__delete')

    shadowRoot.querySelector('[popover]').addEventListener('toggle', (ev) => {
      if (ev.newState !== 'open') return

      const filterContext = filterSelect.value
      if (filterContext) filterNameInput.value = filterContext

      const updateSaveButton = () => {
        const saveText = i18n('nadm.filter.button.edit.title')
        const saveAsText = i18n('nadm.filter.button.save.title')
        saveFilterButton.textContent = filterContext && filterNameInput.value === filterContext ? saveText : saveAsText
        saveFilterButton.setAttribute('data-action', filterContext && filterNameInput.value === filterContext ? 'edit' : 'create')
        saveFilterButton.toggleAttribute('disabled', !filterNameInput.value || filterNameInput.value.trim() === '')
      }

      updateSaveButton()
      filterNameInput.addEventListener('input', updateSaveButton)
    })

    saveFilterButton.addEventListener('click', () => {
      const action = saveFilterButton.getAttribute('data-action')
      const filterName = filterNameInput.value?.trim()

      if (action === 'create') this.create(filterName)
      if (action === 'edit') this.editFilter(filterName)
    })

    filterSelect.addEventListener('change', () => {
      const { value } = filterSelect
      if (value !== this.store.getters.selectedFilter) {
        this.store.setters.setFilter(filterSelect.value)
      }
    })

    deleteButton.addEventListener('click', () => {
      const filterName = filterSelect.value
      if (filterName) this.deleteFilter(filterName)
    })
  }

  create(filterName) {
    const saveFilterDropdown = this.shadowRoot.getElementById('nadm-search-filter__save-dropdown')
    const filterSelect = this.shadowRoot.getElementById('nadm-search-filter__select')
    const filterNameInput = this.shadowRoot.getElementById('nadm-search-filter__name')

    const data = {
      form: { ...this.store.getters.searchFields, filterName }
    }

    const notification = showNotificationOn(saveFilterDropdown)
    post({
      url: routes.na.nadm.controllers.Filters.saveFilter(this.context).url,
      data,
      onSuccess: () => {
        notifySuccess({
          title: i18n('na.modals.filters.success.save'),
          message: i18n('na.modals.filters.success.save.message', htmlEscape(filterName)),
          timeOut: 5000
        })
        filterSelect.value = filterName
        notification.hide()
        saveFilterDropdown.hidePopover()
        filterNameInput.value = ''
      },
      onError: (error) => {
        notification.hide()
        notifyError({
          title: baseI18n(error.title || i18n('exception.NA_00008.title')),
          message: baseI18n(error.message || i18n('exception.NA_00008.user.message')),
          timeOut: 5000
        })
      }
    })
  }

  editFilter(filterName) {
    const saveFilterDropdown = this.shadowRoot.getElementById('nadm-search-filter__save-dropdown')
    const filterNameInput = this.shadowRoot.getElementById('nadm-search-filter__name')

    const data = {
      form: { ...this.store.getters.searchFields }
    }

    const notification = showNotificationOn(saveFilterDropdown)
    put({
      url: routes.na.nadm.controllers.Filters.editFilter(this.context, filterName).url,
      data,
      onSuccess: () => {
        notifySuccess({
          title: i18n('na.modals.filters.success.edit'),
          message: i18n('na.modals.filters.success.edit.message', htmlEscape(filterName)),
          timeOut: 5000
        })
        notification.hide()
        saveFilterDropdown.hidePopover()
        filterNameInput.value = ''
      },
      onError: (error) => {
        notifyError({
          title: baseI18n(error.title || i18n('exception.NA_00008.title')),
          message: baseI18n(error.message || i18n('exception.NA_00008.user.message')),
          timeOut: 5000
        })
        notification.hide()
      }
    })
  }

  applyFilter(filterName) {
    get({
      url: routes.na.nadm.controllers.Filters.getSelectedFilter(this.context, filterName).url,
      onSuccess: (data) => {
        const formValues = Object.fromEntries(data.map(({ name, value }) => ([name, value])))
        this.store.setters.searchWithFields(formValues)
      },
      onError: (error) => {
        notifyError({
          title: baseI18n(error.title || i18n('exception.NA_00008.title')),
          message: baseI18n(error.message || i18n('exception.NA_00008.user.message')),
          timeOut: 5000
        })
      }
    })
  }

  deleteFilter(filterName) {
    const removeFilter = () => {
      del({
        url: routes.na.nadm.controllers.Filters.deleteFilter(this.context, filterName).url,
        onSuccess: () => {
          notifySuccess({
            title: i18n('na.modals.filters.success.delete'),
            message: i18n('na.modals.filters.success.delete.message', htmlEscape(filterName)),
            timeOut: 5000
          })
          this.store.setters.clearFieldValues()
        },
        onError: (error) => {
          notifyError({
            title: baseI18n(error.title || i18n('exception.NA_00008.title')),
            message: baseI18n(error.message || i18n('exception.NA_00008.user.message')),
            timeOut: 5000
          })
        }
      })
    }

    modal.showModal({
      title: i18n('na.modal.filters.title.deletefilter'),
      body: i18n('na.modal.filters.info.deletefilter'),
      buttonOk: {
        callback: removeFilter
      },
    })
  }

  get context() {
    return this.getAttribute(FILTER_CONTEXT_ATTRIBUTE)
  }

  set context(value) {
    this.setAttribute(FILTER_CONTEXT_ATTRIBUTE, value)
  }

  connectedCallback() {
    const filterSelect = this.shadowRoot.getElementById('nadm-search-filter__select')
    const deleteButton = this.shadowRoot.getElementById('nadm-search-filter__delete')

    this.storeListener = this.store.reflect(({ getters }) => getters.selectedFilter, (newFilter) => {
      deleteButton.toggleAttribute('disabled', !newFilter)

      if (newFilter !== filterSelect.value) {
        filterSelect.value = newFilter
      }

      if (newFilter) {
        this.applyFilter(newFilter)
      }
    })
  }

  disconnectedCallback() {
    this.storeListener.unregister()
  }

  renderFilterSelect() {
    const filterHeader = this.shadowRoot.querySelector('.nadm-search-filter__header')
    const deleteButton = this.shadowRoot.getElementById('nadm-search-filter__delete')

    filterHeader.querySelector('#nadm-search-filter__select')?.remove()

    const selectedFilter = htmlEscape(this.store.getters.selectedFilter)
    deleteButton.toggleAttribute('disabled', !selectedFilter)

    const selectHtml = `<x-select id="nadm-search-filter__select" 
        placeholder="${i18n('nadm.filter.select.placeholder')}" 
        has-clear-button 
        full-width
        url="${routes.na.nadm.controllers.Filters.getAllFilters(this.context).url}" 
        class="nadm-search-filter__select">
            ${selectedFilter ? `<option value="${selectedFilter}" selected>${selectedFilter}</option>` : ''}
        </x-select>`

    filterHeader.prepend($(selectHtml)[0])

    return filterHeader.querySelector('#nadm-search-filter__select')
  }
}

export const elementTagName = 'x-nadm-filter'
customElements.define(elementTagName, SearchFilterElement)

function setUpDropdown(dropdownContent, dropdownToggle) {
  const positionDropdown = () => {
    const { height, width } = dropdownContent.getBoundingClientRect()
    const { bottom, top, left, right } = dropdownToggle.getBoundingClientRect()
    dropdownContent.style.top = height + bottom > document.documentElement.clientHeight ? `${top - height}px` : `${bottom}px`
    dropdownContent.style.left = width + left > document.documentElement.clientWidth ? `${right - width}px` : `${left}px`
    dropdownContent.style.removeProperty('visibility')
  }

  dropdownContent.addEventListener('beforetoggle', () => {
    dropdownContent.style.visibility = 'hidden'
  })

  dropdownContent.addEventListener('toggle', (ev) => {
    if (ev.newState !== 'open') {
      window.removeEventListener('scroll', positionDropdown, true)
      window.removeEventListener('resize', positionDropdown, true)
      dropdownToggle.removeAttribute('open')
      return
    }
    window.addEventListener('scroll', positionDropdown, true)
    window.addEventListener('resize', positionDropdown, true)
    positionDropdown()
    dropdownToggle.setAttribute('open', '')
  })
}
