import SelectBox from '~nowc/components/selectbox/selectbox.element'
import manufacturerModelVersionStore from './manufacturer-model-version.store'
import { get } from '~utils/proxy'
import routes from '../../common/routes'
import { showNotificationOn } from '~utils/element-notification'

export class Model<PERSON>ield extends SelectBox {
  constructor () {
    super()
    this.setAttribute('has-clear-button', '')

    this.addEventListener('change', () => {
      manufacturerModelVersionStore.setters.setModel(this.value)
    })

    this.updateOptions({})
  }

  connectedCallback() {
    super.connectedCallback()
    this.modelValueListener = manufacturerModelVersionStore.reflect(
      ({ getters }) => getters.model,
      (newValue) => newValue !== this.value && (this.value = newValue)
    )

    this.queryParamListener = manufacturerModelVersionStore.reflect(
      ({ getters }) => getters.modelQueryParams,
      (newValue) => this.updateOptions(newValue)
    )
  }

  disconnectedCallback() {
    super.disconnectedCallback()
    this.modelValueListener.unregister()
    this.queryParamListener.unregister()
  }

  updateOptions(query) {
    const notification = !this.getValueSafe() ? showNotificationOn(this) : { hide: () => {} }
    get({
      route: routes.na.nadm.controllers.Search.catalogModels(false),
      data: query,
      onSuccess: (data) => {
        const adaptedData = data.entries.map(({ selectItemId, text }) => ({ id: selectItemId, text }))
        manufacturerModelVersionStore.setters.setModelOptions(adaptedData)
        notification.hide()
      }
    })
  }

  getValueSafe() {
    try {
      return this.value
    } catch (ex) {
      return null
    }
  }

  get isDataLoadedDynamically() {
    return true
  }

  queryTransportRequest(params, success, failure) {
    const { q } = params.data

    const optionsToDisplay = manufacturerModelVersionStore.getters.modelOptions
      .filter(({ text }) => !q ? true : text.includes(q))

    success(optionsToDisplay)
  }
}

customElements.define('x-model-field', ModelField)
