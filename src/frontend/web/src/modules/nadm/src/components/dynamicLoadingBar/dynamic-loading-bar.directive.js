import nadmModule from '../../nadm.angular-module'
import { providers } from '../../nadm-providers'
import dynamicLoadingBarStore from './dynamic-loading-bar.store'
import templateHtml from './dynamic-loading-bar.template.html'

nadmModule.directive(providers.dynamicLoadingBar.directives.dynamicLoadingBar, [dynamicLoadingBarDirective])

function dynamicLoadingBarDirective() {
  return {
    restrict: 'A',
    template: templateHtml,
    link: function(scope, element) {
      const $loadingBar = element.find('#dynamicLoading .wait-progress')
      const $loading = element.find('#dynamicLoading')

      const listener = dynamicLoadingBarStore.listenStateChange((newSnapshot, oldSnapshot) => {
        if (newSnapshot.getters.isUploading !== oldSnapshot.getters.isUploading) {
          if (dynamicLoadingBarStore.getters.isUploading) {
            $loadingBar.text('Uploading')
            $loading.css('margin-top', '20%')
            $loading.css('outline', 'none')
            $loading.modal({ backdrop: 'static', keyboard: false })
            $loading.modal('show')
          } else {
            $loading.modal('hide')
          }
        }
        if (newSnapshot.getters.isUpdatingBarStatus && Object.keys(newSnapshot.getters.updateBarParams).length !== 0) {
          const text = newSnapshot.getters.updateBarParams.text
          const barWidth = newSnapshot.getters.updateBarParams.uploadPercentage

          $loadingBar.text(text)
          $loadingBar.css('width', barWidth + '%')
        }
      })

      scope.$on('$destroy', () => {
        listener.unregister()
      })
    }
  }
}
