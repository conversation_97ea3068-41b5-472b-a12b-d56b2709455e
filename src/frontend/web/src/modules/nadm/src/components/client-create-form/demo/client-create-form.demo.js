import './demo-providers'
import * as $ from 'jquery'
import '~utils/modal/implementation'
import * as modal from '~utils/modal'
import { elementTagName } from '~nadm/src/components/client-create-form/client-create-form.element'

const appElem = $('.app')

$('.code--import').text('import \'~nadm/src/components/client-create-form/client-create-form.element\';')

const clientCreateFormElem = document.createElement(elementTagName)
clientCreateFormElem.setAttribute('client-name-pattern', '^[a-zA-Z0-9\u00C0-\u017F&@s.]+$')
appElem.on('click', '.button--show-modal', () => {
  const createClientModal = modal.showModal({

    title: 'Client Create Form',
    body: clientCreateFormElem,
    buttonOk: {
      callback: () => alert(JSON.stringify(clientCreateFormElem.value, null, 2))
    },
    buttonCancel: {
      callback: () => alert('cancel button clicked')
    },
    modalSize: 'large'
  })

  const modalContent = createClientModal.querySelector('.modal-content')
  if (modalContent) {
    const modalOkButton = modalContent.querySelector('#modal_button_ok')
    modalOkButton.toggleAttribute('disabled', !clientCreateFormElem.checkValidity())
  }

  createClientModal.addEventListener('input', () => {
    const modalOkButton = modalContent.querySelector('#modal_button_ok')
    modalOkButton.toggleAttribute('disabled', !clientCreateFormElem.checkValidity())
  })
})

$(document.body).css('overflow', 'auto')
