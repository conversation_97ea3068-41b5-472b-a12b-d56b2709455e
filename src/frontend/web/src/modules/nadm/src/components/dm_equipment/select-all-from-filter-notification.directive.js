import equipmentTableStore from './equipment-table.store'
import nadmModule from '../../nadm.angular-module'
import { providers } from '../../nadm-providers'
import { htmlEscape } from '~utils/string.util'
import i18n from '../../common/i18n.service'

const template = `<div class="alert alert-info hidden equip-selection_container">
             <div class="equip-selection_container__equipment-count-message">
                 <div class="fx-alert-icon"></div> 
                 <div class="fx-alert-message"> 
                      <span class="equip-selection_title"></span> 
                      <p class="equip-selection_description"></p> 
                 </div>
             </div> 
             <div class="fx-alert-message"> 
                 <button class="btn btn-default equip-selection--select-all-records-button"></button> 
             </div> 
         </div>`

nadmModule.directive(providers.equipment.directives.selectAllFromFilterNotification, [
  function() {
    return {
      restrict: 'A',
      priority: 1,
      template,
      link: function(scope, element) {
        const notificationButtonSelector = '.equip-selection--select-all-records-button'

        const notificationContainer = element.find('.equip-selection_container')
        const notificationTitle = element.find('.equip-selection_title')
        const notificationDescription = element.find('.equip-selection_description')
        const notificationButton = element.find(notificationButtonSelector)

        function applyI18n(store) {
          notificationTitle.text(i18n('operation.all.equipments.notification.title'))
          applyI18nOnDescription(store)
        }

        function applyI18nOnDescription(store) {
          const htmlContent = htmlEscape(i18n('operation.all.equipments.notification.content'))
            .replace(/\{0}/g, function() { return "<span class='equip-selection_selected'>" + store.getters.totalSelectedRecords + '</span>' })
            .replace(/\{1}/g, function() { return "<span class='equip-selection_total'>" + store.getters.totalRecords + '</span>' })

          notificationDescription.html(htmlContent)
          notificationButton.text(i18n(
            (!store.getters.allRecordsSelected && store.getters.totalRecords === store.getters.rowsIdFilter.length) ||
                            (store.getters.allRecordsSelected && store.getters.rowsIdFilter.length === 0) ||
                            (store.getters.totalRowsInError === store.getters.rowsIdFilter.length &&
                            store.getters.isInErrorMode)
              ? 'operation.all.equipments.notification.unselect.all'
              : 'operation.all.equipments.notification.select.all'
          ))
        }

        element.on('click', notificationButtonSelector, function () {
          if ((!equipmentTableStore.getters.allRecordsSelected && equipmentTableStore.getters.totalRecords === equipmentTableStore.getters.rowsIdFilter.length) ||
                            equipmentTableStore.getters.allRecordsSelected && equipmentTableStore.getters.rowsIdFilter.length > 0 ||
                            (equipmentTableStore.getters.totalRowsInError === equipmentTableStore.getters.rowsIdFilter.length &&
                            equipmentTableStore.getters.isInErrorMode)) {
            equipmentTableStore.setters.resetRowsIdFilter()
          } else {
            equipmentTableStore.setters.toggleAllRecordsSelection()
          }
        })

        const listener = equipmentTableStore.listenStateChange(function (newSnapshot) {
          const getters = newSnapshot.getters
          const isNotificationVisible = getters.allRecordsSelected ? getters.totalRecords !== getters.rowsIdFilter.length
            : !getters.areNoRecordsSelected

          notificationContainer.toggleClass('hidden', !isNotificationVisible)

          if (isNotificationVisible) {
            applyI18nOnDescription(newSnapshot)
          }
        })

        scope.$on('$destroy', function () {
          listener.unregister()
        })

        applyI18n(equipmentTableStore)
      }
    }
  }
])
