import clientTableStore from './client-table.store'
import nadmModule from '../../nadm.angular-module'
import { providers } from '../../nadm-providers'
import routes from '../../common/routes'
import { htmlEscape } from '~utils/string.util'
import { modalSizes, showModal } from '~utils/modal'
import { navigateTo } from '~utils/navigation'
import { request } from '~utils/proxy'
import baseI18n from '~utils/i18n'
import { ClientEquipmentModel, Client } from '../../common/search/nadm-search.constants'
import { providerName as tableDirectiveName } from '~components/naDatatables-v2/datatable.directive'
import { notifySuccess } from '~utils/toast-notification'
import { getStoreForContext } from '~nadm/src/common/search/base-search-form.store'

const clientSearchStore = getStoreForContext(Client)
const clientEquipmentModelSearchStore = getStoreForContext(ClientEquipmentModel)

nadmModule.directive(providers.client.directives.table, [
  searchTableDirective
])

function searchTableDirective() {
  return {
    require: tableDirectiveName,
    restrict: 'A',
    priority: 10,
    link: function (scope, element, attrs, tableCtrl) {
      clientTableStore.setters.resetState()

      element.on('click', '[data-action="view"]', function () {
        const $el = $(this)
        const rowNumber = $el.data('row')
        const data = clientTableStore.getters.tableRowsData[rowNumber]

        // lets clientEquipmentModel search clear the search state when changing clients.
        clientEquipmentModelSearchStore.setters.setClient(data.clientId)

        const url = routes.na.nadm.controllers.Navigation.clientDetails(data.clientId).url
        navigateTo({ url })
      })

      $(element).on('click', '[data-action="delete"]', function() {
        const $el = $(this)
        const rowNumber = $el.data('row')
        openRemoveModal(clientTableStore.getters.tableRowsData[rowNumber])
      })

      function openRemoveModal(data) {
        const buttonOk = {
          label: baseI18n('nossis.modal.navigate.button.confirm'),
          callback: deleteClientCallback(data.clientId),
          isPrimary: true,
          dismiss: true
        }
        const buttonCancel = {
          label: baseI18n('nossis.modal.navigate.button.cancel'),
          callback: function () {},
          dismiss: true
        }
        const content = '<div class="alert alert-warning" style="white-space: normal; background-color: white">' +
                            '    <div class="fx-alert-icon"></div>' +
                            '    <div class="fx-alert-message">' +
                            '        <p>' + baseI18n('na.portal.nadm.modal.client.remove', '<strong>' + htmlEscape(data.clientId) + '</strong>') +
                            '        </p>' +
                            '    </div>' +
                            '</div>'
        showModal({
          title: baseI18n('na.portal.nadm.modal.entity.delete.title'),
          body: content,
          modalSize: modalSizes.medium,
          buttonOk,
          buttonCancel
        })
      }

      function deleteClientCallback(clientId) {
        return function () {
          request({
            route: routes.na.nadm.controllers.Detail.removeClient(clientId),
            data: {},
            onSuccess: function() {
              tableCtrl.reloadTable({ resetPagination: false })
              const notificationOptions = {
                title: baseI18n('na.portal.nadm.modal.success.remove.client'),
                message: baseI18n('na.portal.nadm.modal.success.remove.client.message', '<strong>' +
                                        htmlEscape(clientId) + '</strong>'),
                allowHtml: true
              }
              notifySuccess(notificationOptions)
            }
          })
        }
      }

      const searchStoreListener = clientSearchStore.listenStateChange(function (newSnapshot, oldSnapshot) {
        if (newSnapshot.getters.formFieldValues !== oldSnapshot.getters.formFieldValues) {
          tableCtrl.reloadTable({ resetPagination: true })
        }
      })

      scope.$on('$destroy', function () {
        searchStoreListener.unregister()
      })

      tableCtrl.paramsPost = function() {
        return clientSearchStore.getters.searchParams
      }

      tableCtrl.addConfigReducer(function (config) {
        const savedPageLength = clientSearchStore.getters.tablePageLength
        const savedPageNumber = clientSearchStore.getters.tablePageNumber
        const pagination = Object.assign({}, config.pagination)

        const adaptedColumns = clientSearchStore.getters.isSortDefined ? config.columns.map(function(column) {
          // ver sort info e sortable
          if (!column.sortable) {
            return column
          }

          const tableSortedColumns = clientSearchStore.getters.tableSortedColumns
          if (tableSortedColumns[column.name] === column.initialSortDirection) {
            return column
          }

          const result = Object.assign({}, column, { initialSortDirection: tableSortedColumns[column.name] })

          if (tableSortedColumns[column.name] == null) {
            delete result.initialSortDirection
          }

          return result
        }) : config.columns

        if (savedPageLength > 0 &&
                        config.pagination.enabled &&
                        config.pagination.pageLengthMenu.enabled &&
                        config.pagination.pageLengthMenu.options.indexOf(savedPageLength) >= 0
        ) {
          pagination.pageLength = savedPageLength
        }

        if (savedPageNumber > 0 &&
                        config.pagination.enabled &&
                        config.pagination.pageLength > 0
        ) {
          pagination.pageNumber = savedPageNumber
        }
        return Object.assign({}, config, {
          columns: adaptedColumns,
          pagination
        })
      })

      tableCtrl.addDrawHandler(function () {
        tableCtrl.getDatatableApi().then(function (api) {
          const apiData = api.data()
          const dataArray = []
          for (let i = 0, e = apiData.length; i < e; i++) {
            dataArray.push(apiData[i])
          }
          tableCtrl.getSortInfo().then(function (sortInfo) {
            clientTableStore.setters.setTableRows({
              rowsData: dataArray,
              totalRecords: api.page.info().recordsTotal,
              sortInfo
            })

            const sortedColumns = sortInfo.reduce(function (acc, sortColumn) {
              acc[sortColumn.name] = sortColumn.order
              return acc
            }, {})
            clientSearchStore.setters.updateTableState({
              pageLength: api.page.info().length,
              pageNumber: api.page.info().page + 1,
              sortedColumns
            })
          })
        })
      })
    }
  }
}
