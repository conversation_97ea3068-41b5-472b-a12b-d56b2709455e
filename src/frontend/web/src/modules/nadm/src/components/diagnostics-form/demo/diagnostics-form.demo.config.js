// This file is used by webpack-demos.config.js

const path = require('path')

module.exports = ({ menus }) => ({
    entry: path.resolve(__dirname, 'diagnostics-form.demo.js'),
    chunk: 'diagnostics-form/diagnostics-form',
    htmlTemplate: path.resolve(__dirname, 'diagnostics-form.demo.html'),
    htmlOutput: 'diagnostics-form.html',
    title: 'Diagnostics form component Demo',
    navBarLabel: 'Diagnostics form',
    menu: menus.nadmComponents,
    dependencies: {
    }
})
