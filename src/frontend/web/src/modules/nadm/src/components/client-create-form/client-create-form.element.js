import html from './client-create-form.element.html'
import '~components/i18n/i18n.element'
import '~nowc/components/input-container/input-container.element'
import { hasFormBasicFeatures } from '~utils/common-mixins/has-basic-form-features.mixin'

class ClientCreateForm extends hasFormBasicFeatures(HTMLElement) {
  constructor() {
    super()
    this.addEventListener('input', () => {
      this.validateForm()
    })
  }

  connectedCallback() {
    this.innerHTML = html
    updateClientNameValidation(this)
  }

  checkValidity() {
    return this.querySelector('form').checkValidity()
  }

  get value() {
    const inputs = this.querySelectorAll('[data-field]')
    const values = {}
    inputs.forEach(input => {
      const field = input.getAttribute('data-field')
      values[field] = input.value
    })
    return values
  }

  static get observedAttributes () { return ['client-name-pattern'] }

  attributeChangedCallback(attrName) {
    if (attrName === 'client-name-pattern') {
      updateClientNameValidation(this)
    }
  }
  getFieldElementValidationInfo(fieldElement) {
    const field = fieldElement.getAttribute('data-field')
    const value = fieldElement.value

    if (fieldElement.validity.patternMismatch) {
      const i18Message = ['na.portal.nadm.invalid.client.name']
      return { isValid: false, error: { field, value, i18Message } }
    }
    return super.getFieldElementValidationInfo(fieldElement)
  }
}

/**
 * @param {ClientCreateForm} createForm - create form component
 */
function updateClientNameValidation(createForm) {
  const regex = createForm.getAttribute('client-name-pattern')
  const inputElement = createForm.querySelector('[data-field="clientName"]')
  if (inputElement) {
    if(regex && regex.trim() !== ""){
      inputElement.setAttribute('pattern', regex)
    } else {
      inputElement.removeAttribute('pattern')
    }
  }
}

export const elementTagName = 'x-na-nadm-client-create-form'
customElements.define(elementTagName, ClientCreateForm)
