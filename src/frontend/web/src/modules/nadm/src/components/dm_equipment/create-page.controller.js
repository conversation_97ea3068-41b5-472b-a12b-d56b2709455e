import nadmModule from '../../nadm.angular-module'
import { providers } from '../../nadm-providers'
import routes from '../../common/routes'
import { showConfirmModal } from '~utils/modal'
import tableFormStore from '../../common/tableform/table-form.store'
import { navigateTo } from '~utils/navigation'
import { cancel, loadCharacteristics, saveDetail, saveDevice } from '../../common/nadm-detail.service'
import nadmI18n from '../../common/i18n.service'
import manufacturerModelVersionStore from '../manufacturer_model_version/manufacturer-model-version.store'

nadmModule.controller(providers.equipment.controllers.create, [
    '$element',
    '$scope',
    '$compile',
    '$timeout',
    providers.equipment.service.equipmentTab,
    CreatePageController
])

function CreatePageController($element, $scope, $compile, $timeout, equipmentTab) {
  manufacturerModelVersionStore.setters.resetState()

  const detailCtrl = this
  detailCtrl.charactericsLoaded = false
  detailCtrl.searchFormItems = {}

  let searchFormItemsBackup
  let changedDueToRestore = false

  const initialHtml = $('#characteristics').html()

  const url = {
    create: function () {
      const { manufacturer, model, version } = manufacturerModelVersionStore.getters.selectedValues
      return routes.na.nadm.controllers.Detail.createEquipment(manufacturer, model, version, detailCtrl.searchFormItems.clientId).url
    },
    search: function () {
      return routes.na.nadm.controllers.Navigation.equipment().url
    },
    view: function (equipmentName) {
      const { manufacturer, model, version } = manufacturerModelVersionStore.getters.selectedValues
      const clientId = detailCtrl.searchFormItems.clientId
      return routes.na.nadm.controllers.Navigation.equipmentDetail(manufacturer, model, version, clientId, equipmentName, equipmentTab.getDefaultTab()).url
    },
    edit: function () {
      return routes.na.nadm.controllers.Navigation.equipmentEdit($scope.manufacturer, $scope.model, $scope.version, $scope.clientId, $scope.equipmentId, equipmentTab.getDefaultTab()).url
    },
    createTemplate: function () {
      const { manufacturer, model, version } = manufacturerModelVersionStore.getters.selectedValues
      return routes.na.nadm.controllers.Navigation.equipmentCreateTemplate(manufacturer, model, version, detailCtrl.searchFormItems.clientId).url
    }
  }

  const tableListener = tableFormStore.reflect(({ getters }) => getters.isDirty, (isDirty) => {
    $scope.$broadcast(isDirty ? 'NADM::SaveButton::Enable' : 'NADM::SaveButton::Disable')
  })

  $scope.$on('$destroy', () => tableListener.unregister())

  function backupSearchFormItems() {
    searchFormItemsBackup = manufacturerModelVersionStore.getters.selectedValues
  }

  function restoreSearchFormItems() {
    manufacturerModelVersionStore.setters.setValues(searchFormItemsBackup)
  }

  detailCtrl.isCharacterizeFormValid = function () {
    return manufacturerModelVersionStore.getters.isSelectedValid && detailCtrl.searchFormItems.clientId && detailCtrl.searchFormItems.clientId.trim()
  }

  detailCtrl.isCharacteristicsLoaded = function () {
    return detailCtrl.charactericsLoaded
  }

  detailCtrl.unloadCharacteristics = function () {
    $scope.$emit('Common::Controller::Broadcast', { event: 'NADM::SaveButton::Disable' })
    $('#characteristics').html(initialHtml)
    detailCtrl.charactericsLoaded = false
    tableFormStore.setters.setDirty(false)
  }

  function disableButton() {
    $scope.$broadcast('NADM::Characterize::Button::Disable')
  }

  function enableButton() {
    $scope.$broadcast('NADM::Characterize::Button::Enable')
  }

  const selectedValuesListener = manufacturerModelVersionStore.reflect(
    ({ getters }) => getters.selectedValues,
    () => {
      updateView()
    })

  $scope.$on('$destroy', () => selectedValuesListener.unregister())

  $element.on('change', '[data-field=client]', (ev) => {
    const value = ev.target.value
    if (value === detailCtrl.searchFormItems.clientId) {
      return
    }

    detailCtrl.searchFormItems.clientId = ev.target.value

    updateView()
  })

  function updateView() {
    if (changedDueToRestore === true) {
      changedDueToRestore = false
      return
    }

    if (detailCtrl.isCharacteristicsLoaded()) {
      displayDatalossModal()
    }

    if (detailCtrl.isCharacterizeFormValid()) {
      enableButton()
    } else {
      disableButton()
    }
  }

  function displayDatalossModal() {
    const bindKeysToButtons = function (keyEvent) {
      const keyEnter = 13
      const keyEsc = 27
      switch (keyEvent.which) {
        case keyEnter:
          $('#modal_button_ok').trigger('click')
          break
        case keyEsc:
          $('#modal_button_cancel').trigger('click')
          break
      }
    }

    const content = nadmI18n('modal.modifications.formReset')

    const activeElement = $(document.activeElement)
    if (activeElement.is('input') || activeElement.is('textarea')) {
      activeElement.trigger('blur')
    }

    showConfirmModal({
      title: nadmI18n('modal.modifications.header'),
      body: content,
      onConfirm: function () {
        $(document).off('keydown', bindKeysToButtons)
        detailCtrl.unloadCharacteristics()
      },
      onCancel: function () {
        restoreSearchFormItems()
        changedDueToRestore = true
        $(document).off('keydown', bindKeysToButtons)
        $timeout(function () {
          $scope.$digest()
        }) // ensure the view is updated outside digest loop
      }
    })

    $(document).on('keydown', bindKeysToButtons)
  }

  $scope.cancelView = function() {
    navigateTo({ url: url.search() })
  }

  $scope.editForm = function() {
    navigateTo({ url: url.edit() })
  }

  $scope.characterizeEquipment = function($container) {
    backupSearchFormItems()
    loadCharacteristics(url.createTemplate(), function(html) {
      // Calling `$compile(html)` returns a function that, when called with
      // a context object, links the compiled HTML to the given context (e.g.
      // binds scope-based expressions in the view to the passed in scope).
      const linkingFunction = $compile(html)
      const elem = linkingFunction($scope)

      $container = $('#characteristics')
      $container.empty().html(elem)

      detailCtrl.charactericsLoaded = true
    })
  }

  $scope.create = function () {
    const equipmentData = dataToSubmit()
    const equipmentName = equipmentData['equipments.equipment.name']

    saveDetail(url.create(), url.view(equipmentName), equipmentData)
  }

  $scope.cancelCreate = function() {
    cancel(url.search(), url.create(), dataToSubmit())
  }

  $scope.saveEquipment = function($container) {
    saveDevice(url.update(), url.characteristics(), dataToSubmit(), function(html) {
      // Calling `$compile(html)` returns a function that, when called with
      // a context object, links the compiled HTML to the given context (e.g.
      // binds scope-based expressions in the view to the passed in scope).
      const linkingFunction = $compile(html)
      const elem = linkingFunction($scope)
      $container.empty().html(elem)
    })
  }

  function dataToSubmit() {
    const configurations = tableFormStore.getters.getValues
    const mapToSubmit = {}

    for (const key in configurations) {
      if (Object.hasOwn(configurations, key)) {
        const value = configurations[key]
        if (value.equipment != null) {
          mapToSubmit[key] = value.equipment
        }
      }
    }

    return mapToSubmit
  }
}
