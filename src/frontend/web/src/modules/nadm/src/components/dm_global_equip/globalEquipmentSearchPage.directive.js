import nadmModule from '../../nadm.angular-module'
import { providers } from '../../nadm-providers'
import { labels, updateBreadcrumb } from '../../common/breadcrumb.service'

nadmModule.directive(providers.globalEquip.directives.page, [globalEquipPageDirective])

function globalEquipPageDirective() {
  return {
    restrict: 'A',
    priority: 1,
    link: function() {
      updateBreadcrumb([
        labels.globalEquipment.search()
      ])
    }
  }
}
