import equipmentTableStore from './equipment-table.store'
import nadmModule from '../../nadm.angular-module'
import { providers } from '../../nadm-providers'
import i18n from '../../common/i18n.service'

const template = '<div class="alert alert-warning hidden max-selectable-equip--container">' +
        '<div class="fx-alert-icon"><i class="fuxicons fuxicons-warning"></i></div>' +
        '<div class="fx-alert-message">' +
        ' <span class="max-selectable-equip__title"></span>' +
        ' <p class="max-selectable-equip__description"></p>' +
        '</div>' +
        '</div>'

nadmModule.directive(providers.equipment.directives.warningAboutMaxSelectableEquipments, [
  function() {
    return {
      restrict: 'A',
      priority: 1,
      template,
      link: function(scope, element) {
        const notificationContainer = element.find('.max-selectable-equip--container')
        const notificationTitle = element.find('.max-selectable-equip__title')
        const notificationDescription = element.find('.max-selectable-equip__description')

        function applyI18n(closeToMaxRecordsCharsAllowed, maxRecordsCharsExceeded, allRecordsSelected) {
          let htmlContent = ''
          let htmlTitle = ''
          const selectionType = allRecordsSelected ? 'deselectable' : 'selectable'
          if (maxRecordsCharsExceeded) {
            htmlTitle = i18n(`operation.exceeded.max.${selectionType}.equipments.notification.title`)
            htmlContent = i18n(`operation.exceeded.max.${selectionType}.equipments.notification.content`)
          } else if (closeToMaxRecordsCharsAllowed) {
            htmlTitle = i18n(`operation.close.max.${selectionType}.equipments.notification.title`)
            htmlContent = i18n(`operation.close.max.${selectionType}.equipments.notification.content`)
          }
          notificationTitle.text(htmlTitle)
          notificationDescription.html(htmlContent)
        }

        const listener = equipmentTableStore.listenStateChange(function (newSnapshot, oldSnapshot) {
          const { closeToMaxRecordsCharsAllowed, maxRecordsCharsExceeded, allRecordsSelected } = newSnapshot.getters
          if (newSnapshot.getters.rowsIdFilter !== oldSnapshot.getters.rowsIdFilter) {
            notificationContainer.toggleClass('hidden', !closeToMaxRecordsCharsAllowed && !maxRecordsCharsExceeded)
          }

          if (closeToMaxRecordsCharsAllowed || maxRecordsCharsExceeded) {
            applyI18n(closeToMaxRecordsCharsAllowed, maxRecordsCharsExceeded, allRecordsSelected)
          }
        })

        scope.$on('$destroy', function () {
          listener.unregister()
        })

        applyI18n(equipmentTableStore)
      }
    }
  }
])
