import nadmModule from '../../nadm.angular-module'
import { providers } from '../../nadm-providers'

nadmModule.directive(providers.clientEquipmentModel.directives.page, [
  clientEquipDetailsPageDirective
])

function clientEquipDetailsPageDirective() {
  return {
    restrict: 'A',
    priority: 1,
    controller: providers.clientEquipmentModel.controllers.detail,
    link: {
      pre: function (scope, elem, attr) {
        scope.clientId = attr.clientId
        scope.manufacturer = attr.manufacturer
        scope.model = attr.model
        scope.version = attr.version
      }
    }
  }
}
