import nadmModule from '../../nadm.angular-module'
import { providers } from '../../nadm-providers'
import associationTypeServiceTableStore from './association-type-services-table.store'
import { providerName as tableDirectiveName } from '~components/naDatatables-v2/datatable.directive'

nadmModule.directive(providers.equipmentAssociations.directives.servicesTable, [servicesTableDirective])

function servicesTableDirective() {
  return {
    require: tableDirectiveName,
    restrict: 'A',
    priority: 10,
    link: (scope, element, attrs, tableCtrl) => {
      tableCtrl.getStoreInfo().then(({ table, store }) => {
        element.on('click', '[data-action="select-radio-row"]', function () {
          const $el = $(this)
          const rowNumber = $el.data('row')
          const data = store.getters.rowsData[table][rowNumber]
          scope.onSelectedService(data)
        })
      })

      tableCtrl.paramsPost = () => associationTypeServiceTableStore.getters.paramsPost

      const tableStoreListener = associationTypeServiceTableStore.listenStateChange(() => {
        tableCtrl.reloadTable({ resetPagination: false })
      })

      scope.$on('$destroy', () => tableStoreListener.unregister())
    }
  }
}
