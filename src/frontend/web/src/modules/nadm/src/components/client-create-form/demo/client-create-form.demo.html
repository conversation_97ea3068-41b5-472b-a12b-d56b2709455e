<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title><%= title %></title>
    <%= css %>
</head>
<body>
<%= navbar %>
<div class='container app'>
    <h1><%= title %></h1>

    <h3>Dependencies</h3>
    <%= dependencyTable %>

    <h3>Imports</h3>
    <pre><code class="code--import lang-js"></code></pre>

    <h3>Basic Example</h3>
    <x-na-nadm-client-create-form></x-na-nadm-client-create-form>
    <pre><code class="code--table-1 xml">&lt;x-na-nadm-client-create-form&gt;&lt;/x-na-nadm-client-create-form&gt;</code></pre>

    <h3>Basic Example with client pattern</h3>
    <x-na-nadm-client-create-form client-name-pattern="^[a-zA-Z0-9\u00C0-\u017F&@\s\.]+$"></x-na-nadm-client-create-form>
    <pre><code class="code--table-1 xml">&lt;x-na-nadm-client-create-form client-name-pattern=&quot;^[a-zA-Z0-9\u00C0-\u017F&@\s\.]+$&quot;&gt;&lt;/x-na-nadm-client-create-form&gt;</code></pre>

    <h3>Get form value</h3>
    <section>
        <br>
        You can access the form values by calling this components value property
        <br>
    </section>
    <pre><code class="code--table-1 js">import { elementTagName } from '~nadm/src/components/client-create-form/client-create-form.element'

const clientCreateFormElem = document.createElement(elementTagName)

clientCreateFormElem.value</code></pre>

    <section>
    Example of a <code>clientCreateFormElem.value</code> result
    <pre><code>{
    "clientName": "Big Whale",
    "clientDescription": "She likes to swim and eat a lot of fish"
}</code></pre>
    </section>

    <h3>Example with modal</h3>
    <button class="btn btn-sm btn-primary button--show-modal">Show Modal</button>
    <pre><code class="code--table-1 js">
import { showModal } from '~utils/modal'
import { elementTagName } from '~nadm/src/components/client-create-form/client-create-form.element'

const clientCreateFormElem = document.createElement(elementTagName)
clientCreateFormElem.setAttribute('client-name-pattern', '^[a-zA-Z0-9\u00C0-\u017F&@s.]+$')

const createClientModal = showModal({
  title: 'Client Create Form',
  body: clientCreateFormElem,
  buttonOk: {
    callback: () =&gt; alert(JSON.stringify(clientCreateFormElem.value, null, 2))
  },
  buttonCancel: {
    callback: () =&gt; alert('cancel button clicked')
  },
  modalSize: 'large'
})

const modalContent = createClientModal.querySelector('.modal-content')
if (modalContent) {
  const modalOkButton = modalContent.querySelector('#modal_button_ok')
  modalOkButton.toggleAttribute('disabled', !clientCreateFormElem.checkValidity())
}

createClientModal.addEventListener('input', () =&gt; {
  const modalOkButton = modalContent.querySelector('#modal_button_ok')
  modalOkButton.toggleAttribute('disabled', !clientCreateFormElem.checkValidity())
})
        </code></pre>



</div>
<%= js %></body>
</html>