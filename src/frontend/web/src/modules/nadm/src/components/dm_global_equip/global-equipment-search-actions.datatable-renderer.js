import { htmlEscape } from '~utils/string.util'
import { save<PERSON><PERSON>er } from '~components/naDatatables-v2/datatable-render-store'
import globalEquipmentTableStore from './globalEquipment-table.store'

function dataAttributes(parameters) {
  const dataAttr = Object.keys(parameters).filter(key => key.startsWith('data-') && parameters[key] !== '')
  if (dataAttr.length <= 0) {
    return ''
  }
  return dataAttr.map(key => key + '="' + htmlEscape(parameters[key]) + '"').join(' ')
}

function link(parameters) {
  const { icon, action, title, context, enabled: isEnabled, visible } = parameters
  if (visible === false) {
    return ''
  }
  const disabledAttribute = (isEnabled === false) ? 'disabled' : ''
  return `<button ${dataAttributes(parameters)}
             data-row="${context.rowNumber}"
             title="${htmlEscape(title)}"
             data-action="${htmlEscape(action)}"
             class="btn fx-btn-action"
             ${disabledAttribute}>
                <i class="${htmlEscape(icon)}"></i>
        </button>`
}

function menuButton(parameters, dropdown) {
  const iconClass = parameters.icon
  const title = parameters.title

  const dropdownHtml = Array.isArray(dropdown) ? dropdown.join('') : dropdown
  if (dropdownHtml === '') {
    return `<div class="btn-group">
        <button class="btn fx-btn-action disabled" 
                disabled
                data-toggle="dropdown" 
                title="${htmlEscape(title)}"
                aria-haspopup="false" 
                aria-expanded="false">
                    <i class="${htmlEscape(iconClass)}"></i>
        </button>`
  }

  return `<div class="btn-group">
        <button class="btn fx-btn-action dropdown-toggle" 
                data-toggle="dropdown"
                type="button"
                title="${htmlEscape(title)}"
                aria-haspopup="true" 
                aria-expanded="false">
                    <i class="${htmlEscape(iconClass)}"></i>
         </button>
        <div class="dropdown-menu fx-dropdown-open-to-left" role="menu">${dropdownHtml}</div>
    </div>`
}

function menuLink(parameters, content) {
  const { icon, action, title, context, enabled: isEnabled, visible } = parameters
  const input = parameters.input || ''
  const idValue = parameters.idValue ? `id ="${htmlEscape(parameters.idValue)}"` : ''
  const href = parameters.isHref ? 'href=""' : ''
  const link = parameters.isHref ? '-link' : ''
  let classes = parameters.classes || ''

  if (visible === false) {
    return ''
  }
  const iconHtml = icon ? `<i class="${htmlEscape(icon)}"></i> ` : ''
  if (isEnabled === false) {
    classes += ' disabled '
  }
  const itemClassHtml = classes ? `class="${htmlEscape(classes.trim())}"` : ''
  if (isEnabled === false) {
    return `<li ${itemClassHtml}>
                ${input}
                <a ${dataAttributes(parameters)}
                   ${idValue}
                   ${href} 
                   class="disabled" 
                   disabled
                   data-action="${htmlEscape(action + link)}"
                   title="${htmlEscape(title)}" 
                   style="pointer-events: none">
                        ${iconHtml}${content}
                </a>
            </li>`
  } else {
    return `<li ${itemClassHtml}>
                ${input}
                <a ${dataAttributes(parameters)}
                   ${idValue}${href} 
                   data-row="${htmlEscape(context.rowNumber)}" 
                   data-action="${htmlEscape(action + link)}"
                   title="${htmlEscape(title)}">
                        ${iconHtml}${content}
                 </a>
            </li>`
  }
}

function menuLinkToUploadEntry(parameters, content) {
  const { action } = parameters

  const input = `<input id="na-nadm-upload-file" 
                               data-row="${parameters.context.rowNumber}" 
                               data-action="${htmlEscape(action)}" 
                               type="file"/>`
  const isHref = true
  const idValue = 'na-nadm-upload-link'
  return menuLink(
    { ...parameters, input, isHref, idValue }, content
  )
}

function menuHeader(iconClass, content) {
  if (iconClass) {
    return `<li class="dropdown-header"><b><i class="${htmlEscape(iconClass)} fx-icon"></i> ${htmlEscape(content)}</b></li>`
  }
  return `<li class="dropdown-header"><b>${htmlEscape(content)}</b></li>`
}

function menuDivider() {
  return '<li role="separator" class="divider"></li>'
}

function dropdown(context, operations) {
  const { rowData, i18n } = context

  function dropdownHeader() {
    let operationsLinks = ''
    Object.keys(operations).forEach(operationId => {
      operationsLinks += menuLink({
        icon: '',
        visible: true,
        action: 'operation-execution',
        'data-id': operationId,
        'data-name': operations[operationId].name,
        title: operations[operationId].title,
        context,
        classes: 'dropdown-menu__item--nadm-individual-operation'
      }, operations[operationId].label)
    })

    if (operationsLinks === '') {
      return ''
    } else {
      return menuHeader('', i18n('na.portal.nadm.button.bulkoperations.group')) +
              operationsLinks
    }
  }

  function dropdownBody() {
    return menuLink({
      icon: 'glyphicon glyphicon-remove fx-icon',
      visible: rowData.deletePermission !== false,
      enabled: rowData.equipments <= 0,
      action: 'delete',
      title: i18n('na.portal.nadm.bulkoperations.delete.button'),
      context
    }, '<span>' + i18n('na.portal.nadm.bulkoperations.delete.button') + '</span>') +
          menuLinkToUploadEntry({
            icon: 'glyphicon glyphicon-import fx-icon',
            visible: rowData.editPermission !== false && rowData.uploadEnabled !== false,
            enabled: true,
            action: 'upload',
            title: i18n('na.portal.nadm.operation.upload.button'),
            context
          }, '<span>' + i18n('na.portal.nadm.operation.upload.button') + '</span>')
  }

  return [dropdownHeader(), dropdownBody()].filter(function (html) { return html.trim().length > 0 }).join(menuDivider())
}

function actionRenderer(context) {
  const rowData = context.rowData
  const operations = globalEquipmentTableStore.getters.operations
  return '<span class="fx-table-actions">' +
        link({
          icon: 'fuxicons fuxicons-eye',
          action: 'view',
          title: context.i18n('na.datatables.action.label.view'),
          context
        }) +
        link({
          icon: 'glyphicon glyphicon-pencil',
          action: 'edit',
          title: context.i18n('na.datatables.action.label.edit'),
          context,
          enabled: rowData.editPermission
        }) +
      menuButton({
        icon: 'fa fa-ellipsis-h',
        title: context.i18n('na.datatables.action.label.moreoptions')
      }, dropdown(context, operations)) +
      '</span>'
}

saveRenderer({ key: 'na.datatableTemplates.nadm.globalEquipmentSearchActions', renderer: actionRenderer })
