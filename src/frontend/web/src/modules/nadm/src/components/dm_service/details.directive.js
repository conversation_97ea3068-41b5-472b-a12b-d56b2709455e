import nadmModule from '../../nadm.angular-module'
import { providers } from '../../nadm-providers'
import { labels, updateBreadcrumb } from '../../common/breadcrumb.service'

nadmModule.directive(providers.service.directives.details, [serviceDetailsPageDirective])

function serviceDetailsPageDirective() {
  return {
    restrict: 'A',
    priority: 1,
    controller: providers.service.controllers.detail,
    link: {
      pre: function (scope, elem, attr) {
        scope.serviceName = attr.serviceName
        scope.serviceType = attr.serviceType
        scope.clientName = attr.clientName
      },
      post: function (scope) {
        updateBreadcrumb([
          labels.service.search(),
          labels.service.view(scope.serviceName, scope.clientName, scope.serviceType)
        ])
      }
    }
  }
}
