import { selector } from '~utils/selector.util'
import { deepClone } from '~utils/deep-clone.util'

const emptyArray = Object.freeze([])
const emptyObject = Object.freeze({})

const initialStateObj = Object.freeze({
  operations: emptyObject,
  tableRowsData: emptyArray,
  columns: emptyArray,
  sortInfo: emptyArray,
  totalRowRecords: 0,
})

export const initialState = function () {
  return initialStateObj
}

function isDataColumn(column) {
  return column.property != null
}

const operations = function (state) { return state.operations }
const tableRowsData = function (state) { return state.tableRowsData }
const columns = function (state) { return state.columns }
const sortInfo = function (state) { return state.sortInfo }
const totalRowRecords = function (state) { return state.totalRowRecords }

const pageLength = selector(
  [tableRowsData],
  function (tableRowsData) {
    return tableRowsData.length
  }
)

const isTableEmpty = selector(
  [pageLength],
  function (pageLength) {
    return pageLength <= 0
  }
)

const tableRowsDataIds = selector(
  [tableRowsData],
  function (tableRowsData) {
    const rowsIds = []
    tableRowsData.forEach(function (row) {
      rowsIds.push(row.identifier)
    })
    return rowsIds
  }
)

const visibleColumnMap = selector(
  [columns],
  function (columns) {
    return columns.reduce(function (acc, column) {
      acc[column.name] = column.visible
      return acc
    }, {})
  }
)

const dataColumns = selector(
  [columns],
  function (columns) {
    return columns.filter(isDataColumn)
  }
)

const visibleDataColumns = selector(
  [dataColumns],
  function (dataColumns) {
    return dataColumns.filter(function (column) {
      return column.visible
    })
  }
)

const areAllDataColumnsHidden = selector(
  [visibleDataColumns],
  function (visibleDataColumns) {
    return visibleDataColumns.length <= 0
  }
)

const visibleDataColumnProperties = selector(
  [visibleDataColumns],
  function (visibleDataColumns) {
    return visibleDataColumns.map(function (column) {
      return column.property
    })
  }
)

export const getters = {
  operations,
  tableRowsData,
  totalRowRecords,
  columns,
  sortInfo,
  isTableEmpty,
  tableRowsDataIds,
  visibleColumnMap,
  dataColumns,
  areAllDataColumnsHidden,
  visibleDataColumns,
  visibleDataColumnProperties
}

const resetState = function () {
  return initialStateObj
}

const setOperations = function(state, params) {
  if (params == null) {
    return state
  }

  if (params.operations == null) {
    return state
  }

  return Object.assign({}, state, {
    operations: deepClone(params.operations)
  })
}

const setTableRows = function (state, params) {
  if (params == null) {
    return state
  }

  if (!Array.isArray(params.rowsData)) {
    return state
  }

  if (!Array.isArray(params.sortInfo)) {
    return state
  }

  if (typeof params.totalRowRecords !== 'number' && params.totalRowRecords <= 0) {
    return state
  }

  return Object.assign({}, state, {
    tableRowsData: deepClone(params.rowsData),
    totalRowRecords: params.totalRowRecords,
    sortInfo: params.sortInfo.map(function (columnSort) { return Object.assign({}, columnSort) })
  })
}

const updateTableRows = function (state, params) {
  if (params == null) {
    return state
  }

  if (!Array.isArray(params.rowsData)) {
    return state
  }

  return Object.assign({}, state, {
    tableRowsData: deepClone(params.rowsData)
  })
}

function isEqualColumn(column1, column2) {
  return column1.name === column2.name &&
            column1.title === column2.title &&
            column1.property === column2.property &&
            column1.visible === column2.visible
}

const setColumns = function (state, columns) {
  if (!Array.isArray(columns)) {
    return state
  }

  if (state.columns.length === columns.length) {
    let areEqual = true
    for (let i = 0, e = columns.length; i < e; i++) {
      if (!isEqualColumn(state.columns[i], columns[i])) {
        areEqual = false
        break
      }
    }
    if (areEqual) {
      return state
    }
  }

  return Object.assign({}, state, {
    columns: columns.map(function (column) {
      return {
        name: column.name,
        title: column.title,
        property: column.property,
        visible: column.visible
      }
    })
  })
}

const hideColumn = function (state, columnName) {
  if (typeof columnName !== 'string' || columnName === '') {
    return state
  }
  let changed = false
  const result = state.columns.map(function (column) {
    if (column.visible && column.name === columnName) {
      changed = true
      return Object.assign({}, column, { visible: false })
    }
    return column
  })
  if (changed) {
    return Object.assign({}, state, {
      columns: result
    })
  } else {
    return state
  }
}

const showColumn = function (state, columnName) {
  if (typeof columnName !== 'string' || columnName === '') {
    return state
  }
  let changed = false
  const result = state.columns.map(function (column) {
    if (!column.visible && column.name === columnName) {
      changed = true
      return Object.assign({}, column, { visible: true })
    }
    return column
  })
  return changed ? Object.assign({}, state, { columns: result }) : state
}

const setVisibleDataColumns = function (state, newVisibleDataColumnNames) {
  if (!Array.isArray(newVisibleDataColumnNames)) {
    return state
  }
  const newVisibleDataColumnNameSet = new Set(newVisibleDataColumnNames)
  const oldDataColumns = visibleDataColumns(state)
  const oldDataColumnNames = oldDataColumns.map(function (column) { return column.name })
  if (oldDataColumnNames.length === newVisibleDataColumnNameSet.size && oldDataColumnNames.every(function (name) {
    return newVisibleDataColumnNameSet.has(name)
  })) {
    return state
  }

  const result = state.columns.map(function (column) {
    if (isDataColumn(column) && column.visible !== newVisibleDataColumnNameSet.has(column.name)) {
      return Object.assign({}, column, { visible: newVisibleDataColumnNameSet.has(column.name) })
    }
    return column
  })

  return Object.assign({}, state, {
    columns: result
  })
}

export const setters = {
  resetState,
  setOperations,
  setTableRows,
  updateTableRows,
  setColumns,
  hideColumn,
  showColumn,
  setVisibleDataColumns,
}
