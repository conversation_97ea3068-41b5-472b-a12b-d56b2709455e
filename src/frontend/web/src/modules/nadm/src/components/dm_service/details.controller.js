import nadmModule from '../../nadm.angular-module'
import { providers } from '../../nadm-providers'
import tableFormStore from '../../common/tableform/table-form.store'
import routes from '../../common/routes'
import { navigateTo } from '~utils/navigation'
import { cancel, saveDetail } from '../../common/nadm-detail.service'

nadmModule.controller(providers.service.controllers.detail, [
  '$scope',
  serviceDetailController
])

function serviceDetailController($scope) {
  const ctrl = this
  $scope.formData = {}
  let initialConfiguration = {}

  ctrl.initServiceForm = () => {
    initialConfiguration = angular.copy($scope.formData)
    $scope.$watch('formData', newValue => {
      if (!compareMaps(newValue, initialConfiguration)) {
        tableFormStore.setters.setValues(newValue)
        $scope.$emit('Common::Controller::Broadcast', { event: 'Service::Form::Changed' })
      } else {
        $scope.$emit('Common::Controller::Broadcast', { event: 'Service::Form::Unchanged' })
      }
    }, true)
  }

  function compareMaps(map1, map2) {
    let testVal
    if (map1.size !== map2.size) {
      return false
    }

    for (const key in map1) {
      if (Object.hasOwn(map1, key)) {
        testVal = map2[key]
        // in cases of an undefined value, make sure the key
        // actually exists on the object so there are no false positives
        if (testVal !== map1[key] || (testVal === undefined && !map2.has(key))) {
          return false
        }
      }
    }

    return true
  }

  const url = {
    update: () => routes.na.nadm.controllers.Detail.updateService($scope.clientName, $scope.serviceName).url,
    search: () => routes.na.nadm.controllers.Navigation.service().url,
    view: () => routes.na.nadm.controllers.Navigation.serviceDetails($scope.clientName, $scope.serviceName, $scope.serviceType).url,
    edit: () => routes.na.nadm.controllers.Navigation.serviceEdit($scope.clientName, $scope.serviceName, $scope.serviceType).url
  }

  /**
   * Navigate to the search page.
   *
   * @when Method called on cancel button in detail page on view mode.
   */
  $scope.cancelView = () => {
    navigateTo({ url: url.search() })
  }

  /**
   * Navigate to the detail page in edit mode.
   *
   * @when Method called on edit button in detail page.
   */
  $scope.editForm = () => {
    navigateTo({ url: url.edit() })
  }

  /**
   * Update service on backend and on success show details page in view mode
   */
  $scope.update = () => {
    saveDetail(url.update(), url.view(), dataToSubmit())
  }

  /**
   * Cancel edit mode and navigates to search page
   *
   * @when Method called on cancel button in detail page on edit mode.
   */
  $scope.cancelEdit = () => {
    cancel(url.search(), url.update(), dataToSubmit())
  }

  /**
   * Get form data to submit.
   *
   * @return data
   */
  function dataToSubmit() {
    return tableFormStore.getters.getValues
  }
}
