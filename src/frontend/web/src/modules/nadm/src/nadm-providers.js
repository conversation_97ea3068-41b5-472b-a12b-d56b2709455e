import { module<PERSON>ame, providerNameGenerator } from '~basemodule/app/angular-provider-name-generator'

const namespaceProviderNameGenerator = providerNameGenerator.withNamespace('NaPortalNadm')
const directive = namespaceProviderNameGenerator.directive
const service = namespaceProviderNameGenerator.service
const controller = namespaceProviderNameGenerator.controller

export const modules = {
  nadm: moduleName('nadm'),
}

export const providers = {
  commons: {
    controllers: {
      common: controller('common'),
    },
    services: {
    },
    directives: {
      sideBarSearch: directive('data-na-portal-nadm-side-bar-search'),
      SaveButton: directive('data-na-portal-nadm-save-button'),
      InitialBreadcrumb: directive('data-na-portal-nadm-initial-breadcrumb'),
      NavigationSidebar: directive('data-na-portal-nadm-navigation-sidebar'),
      characterizeButton: directive('data-na-portal-nadm-commons-characterize-button'),
      Tags: directive('data-na-portal-nadm-tags')
    }
  },
  tableForm: {
    directives: {
      selectBox: directive('data-na-portal-nadm-table-select-box'),
      datatable: directive('data-na-portal-nadm-table-form'),
      row: directive('data-na-portal-nadm-table-row'),
      valueLabel: directive('data-na-portal-nadm-value-label'),
      rowGroup: directive('data-na-portal-tr-group', { requirePrefix: null })
    },
    controllers: {
      row: controller('tableFormRow')
    },
  },
  client: {
    controllers: {
      create: controller('ClientCreate'),
    },
    directives: {
      detail: directive('data-na-portal-nadm-client-details-page'),
      table: directive('data-na-portal-nadm-client-table'),
      create: directive('data-na-portal-nadm-client-create'),
      page: directive('data-na-portal-nadm-client-page')
    }
  },
  clientEquipmentModel: {
    controllers: {
      detail: controller('ClientEquipmentDetail'),
      createModal: controller('ClientEquipmentModelcreateModal')
    },
    directives: {
      page: directive('data-na-portal-nadm-client-equip-details-page'),
      table: directive('data-na-portal-nadm-client-equip-model-table'),
      addButton: directive('data-na-portal-nadm-client-equip-model-add-button'),
      characterizeButton: directive('data-na-portal-nadm-client-equip-characterize-button'),
      createModal: directive('data-na-portal-nadm-client-equip-model-create-modal')

    },
  },
  equipment: {
    controllers: {
      create: controller('EquipmentCreate'),
      detail: controller('EquipmentDetail'),
    },
    service: {
      associations: service('associations'),
      equipmentTab: service('equipmentTab'),
    },
    directives: {
      tagsManagementForm: directive('data-na-portal-nadm-tags-management-form'),
      tabSelector: directive('data-na-portal-nadm-equipment-details-tab-selector'),
      detail: directive('data-na-portal-nadm-equipment-details-page'),
      equipmentExportAllToCSV: directive('data-na-portal-nadm-equipment-export-all-to-csv'),
      equipmentSelectVisibleColumns: directive('data-na-portal-nadm-equipment-select-visible-columns'),
      page: directive('data-na-portal-nadm-equipment-page'),
      searchTable: directive('data-na-portal-nadm-equip-search-table'),
      characteristicsTab: directive('data-na-portal-nadm-equip-characteristics-tab'),
      selectAllFromFilterNotification: directive('data-na-portal-nadm-equipment-select-all-from-filter-notification'),
      warningAboutMaxSelectableEquipments: directive('data-na-portal-nadm-warning-about-max-selectable-equipments'),
      tableErrorModeNotification: directive('data-na-portal-nadm-equipment-table-error-mode-notification'),
      create: directive('data-na-portal-nadm-equipment-create-page')
    },
  },
  equipmentAssociations: {
    controllers: {
      wizard: controller('EquipmentAssociationsWizard')
    },
    directives: {
      associationsTab: directive('data-na-portal-nadm-equip-associations-tab'),
      selectableField: directive('data-na-portal-nadm-equip-associations-selectable-field'),
      table: directive('data-na-portal-nadm-equipment-associations-table'),
      wizard: directive('data-na-portal-nadm-equipment-associations-wizard'),
      mainPageForm: directive('data-na-portal-nadm-equipment-associations-search-form'),
      servicesTable: directive('data-na-portal-nadm-equipment-associations-services-table'),
      associationTypesDropdown: directive('data-na-portal-nadm-association-types-dropdown'),
      selectLineForm: directive('data-na-portal-nadm-equipment-associations-select-line-form'),
      toolbarAdvancedSearch: directive('data-na-portal-nadm-equipment-associations-toolbar-advanced-search')
    }
  },
  globalEquip: {
    controllers: {
      detail: controller('GlobalEquipmentDetail'),
    },
    directives: {
      detail: directive('data-na-portal-nadm-global-equip-details-page'),
      page: directive('data-na-portal-nadm-global-equip-page'),
      searchTable: directive('data-na-portal-nadm-global-equip-search-table')
    },
  },
  service: {
    controllers: {
      detail: controller('ServiceDetail'),
      create: controller('ServiceCreate')
    },
    directives: {
      DetailsForm: directive('data-na-portal-nadm-service-details-form'),
      CreateForm: directive('data-na-portal-nadm-service-create-form'),
      details: directive('data-na-portal-nadm-service-details-page'),
      autoCharacterize: directive('data-na-portal-nadm-service-auto-characterize'),
      table: directive('data-na-portal-nadm-service-table'),
      page: directive('data-na-portal-nadm-service-page'),
      create: directive('data-na-portal-nadm-service-create-page')
    }
  },
  dynamicLoadingBar: {
    directives: {
      dynamicLoadingBar: directive('data-na-portal-nadm-dynamic-loading-bar')
    },
    controllers: {
      dynamicLoadingBar: controller('dynamicLoadingBar')
    }
  },
  operation: {
    directives: {
      bulkOperationsDropdown: directive('data-na-portal-nadm-bulk-operations-dropdown')
    },
    services: {
      operationModal: service('operationModal')
    }
  },
}
