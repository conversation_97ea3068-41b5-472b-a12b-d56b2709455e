import { setPath } from '~utils/breadcrumb-api'
import { customI18n, i18n } from './i18n.service'
import baseI18n from '~utils/i18n'
import { routes } from './routes'

const { Detail, Navigation } = routes.na.nadm.controllers

export const labels = {
  globalEquipment: {
    search: () => ({
      label: i18n('nav.globalEquip'),
      path: Navigation.globalEquipment().url,
      urlType: 'ajax'
    }),
    view: (manufacturer, model, version) => ({
      label: manufacturer + ' - ' + model + ' - ' + version,
      path: Navigation.globalEquipmentDetails(manufacturer, model, version).url,
      urlType: 'ajax'
    })
  },
  client: {
    search: () => ({
      label: i18n('nav.client'),
      path: Navigation.client().url,
      urlType: 'ajax'
    }),
    view: (clientId) => ({
      label: clientId,
      path: Navigation.clientDetails(clientId).url,
      urlType: 'ajax'
    }),
  },
  equipment: {
    search: () => ({
      label: i18n('nav.equipment'),
      path: Navigation.equipment().url,
      urlType: 'ajax'
    }),
    view: (manufacturer,
      model,
      version,
      clientId,
      equipmentId) => ({
      label: equipmentId,
      path: Navigation.equipmentDetail(
        manufacturer,
        model,
        version,
        clientId,
        equipmentId,
        'characteristics'
      ).url,
      urlType: 'ajax'
    }),
    edit: (equipment) => ({
      label: equipment,
      urlType: 'ajax'
    }),
    create: () => ({
      label: i18n('create.equipment'),
      path: Navigation.equipmentCreate().url,
      urlType: 'ajax'
    }),
  },
  service: {
    search: () => ({
      label: i18n('nav.services'),
      path: Navigation.service().url,
      urlType: 'ajax'
    }),
    create: () => ({
      label: i18n('create.service'),
      path: Navigation.serviceCreate().url,
      urlType: 'ajax'
    }),
    view: (serviceName, clientName, serviceType) => ({
      label: serviceName + ' - ' + clientName + ' - ' + baseI18n('NADM.services.service.' + serviceType),
      path: Navigation.serviceDetails(clientName, serviceName, serviceType).url,
      urlType: 'ajax'
    }),
  },
  clientEquipmentModel: {
    update: (clientId, manufacturer, model, version) => ({
      label: clientId + ' - ' + manufacturer + ' - ' + model + ' - ' + version,
      path: Detail.updateClientEquipment(clientId, manufacturer, model, version).url,
      urlType: 'ajax'
    }),
    search: (clientId) => ({
      label: clientId,
      path: Navigation.clientDetails(clientId).url,
      urlType: 'ajax'
    }),
  },
}

export function updateBreadcrumb(...breadcrumbArray) {
  setPath(...breadcrumbArray)
}
