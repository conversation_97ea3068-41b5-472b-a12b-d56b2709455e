import nadmModule from '../nadm.angular-module'
import { updateBreadcrumb } from './breadcrumb.service'
import { providers } from '../nadm-providers'

/**
 * This directive is used when the user enters the front page of Module.
 * Its purpose is to set the initial breadcrumb in order to contextualize the user.
 * Note: If the user enters the module via nossis "links" the breadcrumb it's ok, however if it navigates through the custom menu or hits F5/Ctrl+F5 the breadcrumb context disappears.
 */
nadmModule.directive(providers.commons.directives.InitialBreadcrumb, [initialBreadcrumbDirective])
function initialBreadcrumbDirective() {
  return {
    restrict: 'A',
    priority: 1,
    terminal: true,
    link: () => updateBreadcrumb()
  }
}
