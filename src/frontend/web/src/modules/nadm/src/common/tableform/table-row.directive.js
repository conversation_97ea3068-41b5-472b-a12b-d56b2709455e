import nadmModule from '../../nadm.angular-module'
import { providers } from '../../nadm-providers'
import { elementTagName } from '~nowc/components/date-time-picker/date-time-picker.element'

nadmModule.directive(providers.tableForm.directives.row, [
  '$timeout',
  tableRowDirective
])

function tableRowDirective($timeout) {
  return {
    scope: true,
    controller: providers.tableForm.controllers.row,
    controllerAs: 'TableRowController',
    link: function($scope, element, attributes, controller) {
      element.on('click', 'a.editable-field-link', () => {
        controller.editMode()

        requestAnimationFrame(() => {
          element[0].querySelectorAll(elementTagName).forEach((element) => {
            element.valueAsFormattedInUTC = $scope.model.currentValue
          })
        })

        return false
      })

      const validate = (newValue, oldValue) => {
        requestAnimationFrame(() => {
          if (controller.isValueChanged()) {
            element.attr('changed', 'true')
          } else {
            element.attr('changed', 'false')
          }

          if (element.find('.has-error, ng-invalid').length <= 0) {
            element.attr('valid', 'true')
          } else {
            element.attr('valid', 'false')
          }

          if (oldValue && newValue) {
            controller.updateValues(newValue, oldValue)
          }
        })
      }

      $scope.$watch('model', (newValue, oldValue) => validate(newValue, oldValue), true)

      $timeout(() => {
        controller.initialize(
          attributes.id,
          $scope.pageContext,
          attributes.selectable === 'true',
          attributes.required === 'true'
        )

        // Do validation. Some required inputs may be invalid at start
        validate()
      })

      $scope.$on('NADM::TableRow::Form::RevertChanges', () => controller.resetForm())

      element.on('click', 'button.btn.revert-param', event => {
        const target = $(event.target)
        if (target.is(':button') || target.is('button > i')) {
          controller.revertValue()
        }
      })

      element.on('change input', elementTagName, event => {
        const target = $(event.target)

        $scope.model.currentValue = target[0].valueAsFormattedInUTC
        $scope.$applyAsync()
      })
    }
  }
}
