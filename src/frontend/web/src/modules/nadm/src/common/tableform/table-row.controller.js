import store from './table-form.store'
import nadmI18n from '../../common/i18n.service'
import nadmModule from '../../nadm.angular-module'
import { providers } from '../../nadm-providers'

nadmModule.controller(providers.tableForm.controllers.row, [
  '$scope',
  tableFormController
])

function tableFormController($scope) {
  const ctrl = this
  ctrl.getCurrentValue = getCurrentValue

  $scope.icon = { classes: '', title: '' }
  $scope.confirmMode = false
  $scope.viewMode = true
  $scope.edited = false
  $scope.model = {}

  $scope.initialModel = {}

  /**
     * Gives the next context in the ECG hierarchy
     * @type {{equipment: string, clientEquipment: string, globalEquipment: string}}
     */
  const nextContext = ctrl.nextContext = {
    equipment: 'clientEquipment',
    clientEquipment: 'globalEquipment',
    globalEquipment: 'default'
  }

  const contextMapping = ctrl.contextMapping = {
    equipment: 'equipment',
    clientEquipment: 'client',
    globalEquipment: 'global',
    default: 'default'
  }

  ctrl.contextMapping = contextMapping

  /**
     * css classes that correspond to the ECG icons
     * @type {{client: object, global: object, default: object}}
     */
  const iconClasses = ctrl.iconClasses = {
    client: { iconClass: 'glyphicon glyphicon-user', title: 'icon.client' },
    global: { iconClass: 'fuxicons fuxicons-equipment-configuration', title: 'icon.global' },
    default: { iconClass: 'fuxicons fuxicons-lcd-external-in-alt', title: 'icon.external' }
  }

  ctrl.initialize = (id, context, isSelectable, isRequired) => {
    const values = getValues(id)
    const valContextPair = getCurrentValue(values, context)
    $scope.model = {
      required: isRequired,
      elementId: id,
      selectable: isSelectable,
      initialValues: values,
      context,
      currentValue: valContextPair.value,
      isInherit: isCurrentValueFromUpperContext(values, context),
      inheritingContext: valContextPair.context // for icon
    }
    updateIcons()
    $scope.initialModel = angular.copy($scope.model)
    $scope.$apply()

    // Required inputs not selectable, are by default in edit mode,
    // to make known to the user which are required
    if (isRequired === true && isSelectable !== true && $scope.viewContext === 'CREATE') {
      ctrl.editMode()
    }
  }

  ctrl.updateValues = (newValue, oldValue) => {
    if (newValue.currentValue !== oldValue.currentValue || newValue.isInherit !== oldValue.isInherit) {
      if (!$scope.model.isInherit) {
        store.setters.setValue({
          key: $scope.model.elementId,
          context: contextMapping[$scope.model.context],
          value: newValue.currentValue
        })
      } else {
        store.setters.setValue({
          key: $scope.model.elementId,
          context: contextMapping[$scope.model.context],
          value: null
        })
      }
      $scope.$emit('TableRow::Form::ValueChanged')
    }
  }

  function getValues(id) {
    return store.getters.getValues[id]
  }

  function updateIcons() {
    if ($scope.model.isInherit) {
      $scope.icon.classes = iconClasses[contextMapping[$scope.model.inheritingContext]].iconClass
      $scope.icon.title = nadmI18n(iconClasses[contextMapping[$scope.model.inheritingContext]].title)
    } else {
      $scope.icon.classes = ''
      $scope.icon.title = ''
    }
  }

  function getCurrentValue(values, context) {
    if (context == null || values == null) {
      return { value: '', context: 'default' }
    }
    const result = values[contextMapping[context]]
    if (result != null) {
      return {
        value: result,
        context
      }
    } else {
      return getCurrentValue(values, nextContext[context])
    }
  }

  function isCurrentValueFromUpperContext(values, context) {
    if (context == null || values == null) {
      return true
    }
    return values[contextMapping[context]] == null
  }

  ctrl.addNextLevelConfirmation = () => {
    $scope.confirmMode = true
  }

  ctrl.confirmInherit = () => {
    const model = $scope.model
    const valContextPair = getCurrentValue(model.initialValues, nextContext[model.context])
    model.currentValue = valContextPair.value
    model.isInherit = true
    model.inheritingContext = valContextPair.context
    $scope.confirmMode = false
    updateIcons()
    if ($scope.initialModel.isInherit !== model.isInherit) {
      $scope.edited = true
    }
  }

  $scope.isValueEdited = ctrl.isValueEdited = () => {
    const model = $scope.model
    const initialModel = $scope.initialModel
    return model.currentValue !== initialModel.currentValue ||
          model.isInherit !== initialModel.isInherit
  }

  ctrl.cancelInherit = () => {
    $scope.confirmMode = false
  }

  ctrl.isValueChanged = () => $scope.edited

  ctrl.editMode = () => {
    $scope.viewMode = false
    $scope.edited = true
    $scope.previousInheritValue = $scope.model.isInherit
    if (!$scope.model.selectable) {
      $scope.model.isInherit = false
    }
    $scope.$digest()
  }

  ctrl.revertValue = () => {
    $scope.edited = false
    $scope.viewMode = true
    $scope.model = angular.copy($scope.initialModel)
    $scope.$digest()
  }

  ctrl.resetForm = () => {
    $scope.edited = false
    $scope.viewMode = true
    $scope.model = angular.copy($scope.initialModel)
  }
}
