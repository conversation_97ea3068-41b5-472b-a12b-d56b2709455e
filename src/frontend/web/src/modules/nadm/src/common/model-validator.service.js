/**
 * @typedef {Object} componentType
 * @property {string} name
 * @property {fieldType} validationType
 */

/**
 * @typedef  {{name: string}} fieldType
 */

/**
     * @constant
     * @property {fieldType} string
     * @property {fieldType} number
     * @property {fieldType} enumeration
     */
const fieldTypes = {
  string: {
    name: 'string'
  },
  number: {
    name: 'number'
  },
  enumeration: {
    name: 'enumeration'
  }
}

export const errorTypes = {
  nullValue: 'nullValue',
  invalidEnumValue: 'invalid enum value',
  regexpNotMatched: 'nullValue'
}

/**
     * @type {{textField: componentType, selectBox: componentType, passwordField: componentType}}
     */
const componentTypes = {
  textField: {
    name: 'textField',
    validationType: fieldTypes.string
  },
  selectBox: {
    name: 'selectBox',
    validationType: fieldTypes.enumeration
  },
  passwordField: {
    name: 'passwordField',
    validationType: fieldTypes.string
  }
}

export function createValidatorFromTemplate(template) {
  const validator = {}
  const templateComponents = template.template.componentList

  templateComponents.forEach(component => {
    const fieldName = component.elementId
    const fieldValidations = {}

    fieldValidations.isNullable = !component.required
    fieldValidations.type = componentTypes[component.type].validationType

    switch (fieldValidations.type.name) {
      case fieldTypes.enumeration.name: {
        fieldValidations.validValues = component.options.map(option => option.value)
        break
      }
      case fieldTypes.number.name: {
        break
      }
      case fieldTypes.string.name: {
        if (component.regex != null) {
          fieldValidations.matchesRegexp = component.regex
        }
        break
      }
    }
    validator[fieldName] = fieldValidations
  })
  return validator
}

function validateEnumeration(field, fieldValidations, errors, fieldName) {
  let enumField = field

  if (fieldValidations.validValues.every(value => typeof value === 'string')) {
    enumField = enumField.toString()
  }

  if (!fieldValidations.validValues.includes(enumField)) {
    errors.push({
      fieldName,
      type: errorTypes.invalidEnumValue,
      validValues: fieldValidations.validValues,
      value: field
    })
  }
}

function validateString(fieldValidations, field, errors, fieldName) {
  if (fieldValidations.matchesRegexp != null && !field.match(new RegExp(fieldValidations.matchesRegexp))) {
    errors.push({
      fieldName,
      type: errorTypes.regexpNotMatched,
      regexp: fieldValidations.matchesRegexp,
      value: field
    })
  }
}

export function validate(model, validator) {
  const errors = []

  for (const fieldName in validator) {
    if (!Object.hasOwn(validator, fieldName)) {
      continue
    }

    const field = model[fieldName]
    const fieldValidations = validator[fieldName]

    if (fieldValidations.isNullable && field == null) {
      continue
    }

    if (!fieldValidations.isNullable && field == null) {
      errors.push({ fieldName, type: errorTypes.nullValue })
      continue
    }

    switch (fieldValidations.type.name) {
      case fieldTypes.enumeration.name: validateEnumeration(field, fieldValidations, errors, fieldName); break
      case fieldTypes.string.name: validateString(fieldValidations, field, errors, fieldName); break
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}
