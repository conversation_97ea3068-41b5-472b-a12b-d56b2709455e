import nadmModule from '../nadm.angular-module'
import { providers } from '../nadm-providers'
import { navigateTo } from '~utils/navigation'
import * as SearchContext from './search/nadm-search.constants'
import routes from './routes'
import { getStoreForContext } from './search/base-search-form.store'

nadmModule.directive(providers.commons.directives.NavigationSidebar, [
  searchSidebarDirective
])

function searchSidebarDirective() {
  return {
    scope: true,
    priority: 1,
    link: (scope, element) => {
      const { Navigation } = routes.na.nadm.controllers

      const sidebarNavigateTo = (route, context, element) => {
        const $listItem = $(element).parent()
        if (!$listItem.is('.fx-active')) {
          getStoreForContext(context).setters.resetState()
        }
        if (!$listItem.is('.disabled')) {
          navigateTo({ route })
        }
      }

      element.on('click', '[data-nav-action="searchGlobalEquip"]', function() {
        sidebarNavigateTo(Navigation.globalEquipment(), SearchContext.GlobalEquip, this)
      })

      element.on('click', '[data-nav-action="searchEquipment"]', function() {
        sidebarNavigateTo(Navigation.equipment(), SearchContext.Equipment, this)
      })

      element.on('click', '[data-nav-action="searchClient"]', function() {
        sidebarNavigateTo(Navigation.client(), SearchContext.Client, this)
      })

      element.on('click', '[data-nav-action="searchService"]', function() {
        sidebarNavigateTo(Navigation.service(), SearchContext.Service, this)
      })
    }
  }
}
