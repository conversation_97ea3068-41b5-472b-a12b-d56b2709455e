/**
 * @typedef {Object} PlayRouteResult
 * @property {string} method - route http request method
 * @property {string} url - route http request url
 *
 */

/**
 @callback PlayRoute0
 @function
 @return {PlayRouteResult}
 */

/**
 @callback PlayRoute1
 @function
 @param arg1
 @return {PlayRouteResult}
 */

/**
 @callback PlayRoute2
 @function
 @param arg1
 @param arg2
 @return {PlayRouteResult}
 */

/**
 @callback PlayRoute3
 @function
 @param arg1
 @param arg2
 @param arg3
 @return {PlayRouteResult}
 */

/**
 @callback PlayRoute4
 @function
 @param arg1
 @param arg2
 @param arg3
 @param arg4
 @return {PlayRouteResult}
 */

/**
 @callback PlayRoute6
 @function
 @param arg1
 @param arg2
 @param arg3
 @param arg4
 @param arg5
 @param arg6
 @return {PlayRouteResult}
 */

/**
 @typedef {Object} nadmjsRoutes

 @property {Object} na
 @property {Object} na.nadm
 @property {Object} na.nadm.controllers
 @property {Object} na.nadm.controllers.Application
 @property {PlayRoute0} na.nadm.controllers.Application.index
 @property {PlayRoute0} na.nadm.controllers.Application.resume
 @property {PlayRoute0} na.nadm.controllers.Application.jsSettings
 @property {Object} na.nadm.controllers.Export
 @property {PlayRoute1} na.nadm.controllers.Export.getEquipmentsCsv
 @property {Object} na.nadm.controllers.Navigation
 @property {PlayRoute0} na.nadm.controllers.Navigation.globalEquipment
 @property {PlayRoute3} na.nadm.controllers.Navigation.globalEquipmentDetails
 @property {PlayRoute3} na.nadm.controllers.Navigation.globalEquipmentEdit
 @property {PlayRoute0} na.nadm.controllers.Navigation.client
 @property {PlayRoute1} na.nadm.controllers.Navigation.clientDetails
 @property {PlayRoute4} na.nadm.controllers.Navigation.clientEquipmentDetail
 @property {PlayRoute4} na.nadm.controllers.Navigation.clientEquipmentDetailModal
 @property {PlayRoute1} na.nadm.controllers.Navigation.clientEquipmentCreateModal
 @property {PlayRoute4} na.nadm.controllers.Navigation.clientEquipmentEditModal
 @property {PlayRoute2} na.nadm.controllers.Navigation.equipmentCharacteristics
 @property {PlayRoute0} na.nadm.controllers.Navigation.equipment
 @property {PlayRoute6} na.nadm.controllers.Navigation.equipmentDetail
 @property {PlayRoute3} na.nadm.controllers.Navigation.equipmentEdit
 @property {PlayRoute0} na.nadm.controllers.Navigation.equipmentCreate
 @property {PlayRoute0} na.nadm.controllers.Navigation.service
 @property {PlayRoute0} na.nadm.controllers.Navigation.serviceCreate
 @property {PlayRoute1} na.nadm.controllers.Navigation.serviceCreateTemplate
 @property {PlayRoute3} na.nadm.controllers.Navigation.serviceDetails
 @property {PlayRoute3} na.nadm.controllers.Navigation.serviceEdit
 @property {PlayRoute1} na.nadm.controllers.Navigation.serviceValues
 @property {Object} na.nadm.controllers.Search
 @property {PlayRoute0} na.nadm.controllers.Search.searchGlobalEquipment
 @property {PlayRoute0} na.nadm.controllers.Search.searchClient
 @property {PlayRoute0} na.nadm.controllers.Search.searchEquipment
 @property {PlayRoute1} na.nadm.controllers.Search.searchEquipmentAssociations
 @property {PlayRoute1} na.nadm.controllers.Search.searchClientEquipmentModel
 @property {PlayRoute0} na.nadm.controllers.Search.searchService
 @property {PlayRoute1} na.nadm.controllers.Search.catalogManufacturers
 @property {PlayRoute1} na.nadm.controllers.Search.catalogModels
 @property {PlayRoute1} na.nadm.controllers.Search.catalogVersions
 @property {Object} na.nadm.controllers.Detail
 @property {PlayRoute3} na.nadm.controllers.Detail.updateGlobalEquipment
 @property {PlayRoute4} na.nadm.controllers.Detail.updateGlobalEquipmentAndTemplate
 @property {PlayRoute3} na.nadm.controllers.Detail.removeGlobalEquipment
 @property {PlayRoute2} na.nadm.controllers.Detail.updateEquipment
 @property {PlayRoute2} na.nadm.controllers.Detail.updateService
 @property {PlayRoute1} na.nadm.controllers.Detail.updateEquipmentAssociation
 @property {PlayRoute1} na.nadm.controllers.Detail.createService
 @property {PlayRoute4} na.nadm.controllers.Detail.createEquipment
 @property {PlayRoute1} na.nadm.controllers.Detail.removeEquipment
 @property {PlayRoute4} na.nadm.controllers.Detail.removeClientEquipment
 @property {PlayRoute4} na.nadm.controllers.Detail.updateClientEquipment
 @property {PlayRoute1} na.nadm.controllers.Detail.createClient
 @property {PlayRoute3} na.nadm.controllers.Detail.getGlobalEquipmentDetails
 @property {PlayRoute1} na.nadm.controllers.Detail.removeClient
 @property {PlayRoute2} na.nadm.controllers.Detail.removeService
 @property {Object} na.nadm.controllers.Operation
 @property {PlayRoute0} na.nadm.controllers.Operation.uploadFirmwareFile
 @property {PlayRoute3} na.nadm.controllers.Operation.deleteFirmwareFile
 @property {PlayRoute1} na.nadm.controllers.Operation.getOperationForm
 @property {PlayRoute0} na.nadm.controllers.Operation.getGlobalEquipmentOperations
 @property {PlayRoute1} na.nadm.controllers.Operation.getMassiveOperationForm
 @property {PlayRoute0} na.nadm.controllers.Operation.executeOperation
 @property {PlayRoute0} na.nadm.controllers.Operation.getMappingModel
 @property {PlayRoute0} na.nadm.controllers.Operation.executeBulkOperation
 @property {PlayRoute0} na.nadm.controllers.Operation.executeBulkDelete
 @property {Object} na.nadm.controllers.Tag
 @property {PlayRoute0} na.nadm.controllers.Tag.tags
 @property {PlayRoute0} na.nadm.controllers.Tag.equipmentTagSearch
 @property {PlayRoute1} na.nadm.controllers.Tag.equipmentTagSearchAssociatedTagsContinuation
 @property {PlayRoute0} na.nadm.controllers.Tag.manageEquipmentsTags
 @property {Object} na.nadm.controllers.Filters
 @property {PlayRoute1} na.nadm.controllers.Filters.saveFilter
 @property {PlayRoute2} na.nadm.controllers.Filters.editFilter
 @property {PlayRoute2} na.nadm.controllers.Filters.deleteFilter
 @property {PlayRoute1} na.nadm.controllers.Filters.getAllFilters
 @property {PlayRoute2} na.nadm.controllers.Filters.getSelectedFilter
 */

import { getContext } from './context.service'

export const routes = /** @type nadmjsRoutes */ contextualizeRouteMap(global.nadmjsRoutes)

function contextualizeRouteMap(routeMap) {
  return Object.fromEntries(applyRoutes(Object.entries(routeMap)))
}

function applyRoutes(entrySet) {
  return entrySet.map(([key, val]) => {
    switch (typeof val) {
      case 'function':
        return [key, insertContext(val)]
      case 'object':
        return [key, Object.fromEntries(applyRoutes(Object.entries(val)))]
      default:
        return [key, val]
    }
  })
}

function insertContext(reverseRouteFunction) {
  return (...args) => {
    return reverseRouteFunction(getContext(), ...args)
  }
}

export default routes
