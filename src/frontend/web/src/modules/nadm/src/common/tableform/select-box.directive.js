import nadmModule from '../../nadm.angular-module'
import { providers } from '../../nadm-providers'

nadmModule.directive(providers.tableForm.directives.selectBox, [
  '$timeout',
  selectBoxDirective
])

function selectBoxDirective($timeout) {
  return {
    scope: true,
    priority: 1,
    link: {
      pre: (scope, element, attributes) => {
        const rowCtrl = scope.TableRowController
        scope.model.isInherit = scope.previousInheritValue
        const valContextPair = rowCtrl.getCurrentValue(
          scope.initialModel.initialValues,
          rowCtrl.nextContext[scope.initialModel.context]
        )
        const inheritOptionValue = $('option.inherit-option').val()

        scope.selectableUiModel = scope.model.isInherit ? inheritOptionValue : scope.model.currentValue

        element.find('option.inherit-option')
          .attr('data-icon', rowCtrl.iconClasses[rowCtrl.contextMapping[valContextPair.context]].iconClass)
          .html(valContextPair.value)

        scope.$on('SelectBox::SelectedOption', (event, params) => {
          const value = params.value
          if (value === inheritOptionValue) {
            scope.model.isInherit = true
            scope.model.currentValue = valContextPair.value
            scope.model.inheritingContext = valContextPair.context
          } else {
            scope.model.currentValue = value
            scope.model.isInherit = false
          }
          $timeout(() => { scope.$apply() })
        })

        scope.$watch('model.isInherit', () => {
          if (scope.model.isInherit) {
            scope.$broadcast('SelectBox::SetValue', { id: attributes.id, value: inheritOptionValue })
          }
        })
      }
    }
  }
}
