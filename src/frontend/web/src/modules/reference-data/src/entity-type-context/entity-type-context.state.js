import { selector } from '~utils/selector.util'
import { isPlainObject } from '~utils/object-utils.util'

const emptyObj = Object.freeze({})

// Initial State

const initialStateObj = Object.freeze({
  entityType: Object.freeze({
    id: '',
    name: ''
  }),
  searchValues: emptyObj
})

export const initialState = () => initialStateObj

// getters

const entityType = (state) => state.entityType
const id = (state) => state.entityType.id
const name = (state) => state.entityType.name
const isDefined = (state) => state.entityType.id !== ''
const stateSearchValues = (state) => state.searchValues
const searchValues = selector(
  [stateSearchValues, id, isDefined],
  (searchValues, id, isDefined) => isDefined ? { ...searchValues, idCatEntity: id } : searchValues
)

export const getters = {
  id,
  name,
  entityType,
  isDefined,
  searchValues
}

// setters

function updateEntityType(state, entityType) {
  if (
    !isPlainObject(entityType) ||
        (entityType.id !== '' && isNaN(entityType.id)) ||
        typeof entityType.name !== 'string' ||
        (state.entityType.id === String(entityType.id) && state.entityType.name === entityType.name)
  ) {
    return state
  }

  if (entityType.id === '' || +entityType.id < 0) {
    return initialStateObj
  }

  return {
    ...state,
    entityType: {
      id: String(entityType.id),
      name: entityType.name
    },
    searchValues: emptyObj
  }
}

function replaceSearchValues(state, searchValues) {
  if (!isPlainObject(searchValues)) {
    return state
  }
  return {
    ...state,
    searchValues: { ...searchValues }
  }
}

export const setters = {
  updateEntityType,
  replaceSearchValues
}
