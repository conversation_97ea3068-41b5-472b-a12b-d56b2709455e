import '@webcomponents/custom-elements'
import { navigateTo } from '~utils/navigation/index'
import { routes } from '../common/routes'
import { labels, updateBreadcrumb } from '../common/breadcrumb.service'
import { store } from '../entity-type-context/entity-type-context.store'
import { cloneCatalogInstance, deleteSingleCatalogInstance, editCatalogInstance } from '../entity-actions'

const clickHandler = Symbol('clickHandler')

const clickHandlerForId = (catalogInstanceId) => (event) => {
  const pageActionElement = event.target.closest('[data-page-action]')
  if (pageActionElement == null) {
    return
  }

  switch (pageActionElement.getAttribute('data-page-action').toLowerCase()) {
    case 'edit':
      editCatalogInstance(catalogInstanceId)
      break
    case 'clone':
      cloneCatalogInstance(catalogInstanceId)
      break
    case 'delete':
      deleteSingleCatalogInstance(catalogInstanceId).then(() => {
        navigateTo({
          route: routes.na.reference.data.controllers.SearchController.getSearchPage()
        })
      })
      break
  }
}

export class ViewPage extends HTMLElement {
  constructor(...args) {
    super(...args)
  }

  connectedCallback() {
    const getAttributeOrNull = (attr) => this.hasAttribute(attr) ? this.getAttribute(attr) : null
    const catalogInstanceId = getAttributeOrNull('instance-id')
    const catalogInstanceName = getAttributeOrNull('instance-name')
    const catalogTypeId = getAttributeOrNull('type-id')
    const catalogTypeName = getAttributeOrNull('type-name')

    store.setters.updateEntityType({
      id: catalogTypeId,
      name: catalogTypeName
    })

    updateBreadcrumb(
      labels.referenceData.search(catalogTypeName),
      labels.referenceData.detail(catalogInstanceId, catalogInstanceName)
    )

    this[clickHandler] = clickHandlerForId(catalogInstanceId)

    this.addEventListener('click', this[clickHandler])
  }

  disconnectedCallback() {
    this.removeEventListener('click', this[clickHandler])
  }
}

customElements.define('x-refdata-view-page', ViewPage)
