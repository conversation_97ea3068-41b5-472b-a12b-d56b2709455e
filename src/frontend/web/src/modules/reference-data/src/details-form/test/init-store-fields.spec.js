import fs from 'fs'
import path from 'path'
import tape from 'tape'
import { JSD<PERSON> } from 'jsdom'
import { getStoreInitializationParams } from '../init-store-fields'
import { constants } from '../details-form.state'
import { promisify } from 'util'

const {
  IS_INTEGER,
  NUMERIC_RANGE,
  IS_NUMBER,
  MIN_LENGTH,
  MAX_LENGTH,
  REGEX,
  REQUIRED,
  DECIMAL_SEPARATOR,
  MAX,
  MIN
} = constants.validations

const readFile = promisify(fs.readFile)
const htmlPromise = readFile(path.resolve(__dirname, 'init-store-fields.jsdom.html'), 'utf8')

const windowPromise = htmlPromise.then(html => new JSDOM(html).window)

async function setup() {
  return await windowPromise
}

async function setupTestTarget(elementId) {
  const { document } = await setup()
  const testTarget = document.getElementById(elementId)
  if (testTarget == null) {
    throw Error(`element "#${elementId}" not found on ${path.resolve(__dirname, 'init-store-fields.jsdom.html')}`)
  }
  return testTarget
}

tape.test('Reference Data - Details form store - store fields initialization for inputs', async (t) => {
  const testTarget = await setupTestTarget('test-target-inputs')
  const expected = {
    initialValues: {
      'pattern-test-input': 'olt_list_instance',
      'maxlength-minlength-test-input': 'Default input value',
      'numeric-integer-input': '10112325',
      'numeric-integer-min-input': '200',
      'numeric-integer-max-input': '-200',
      'numeric-integer-ranged-input': '200',
      'numeric-floating-point-input': '10.125',
      'numeric-comma-separated-floating-point-input': '10,125',
      'numeric-comma-separated-floating-point-in-range-input': '100,125',
    },
    validators: [
      {
        field: 'pattern-test-input',
        validation: REQUIRED
      },
      {
        field: 'pattern-test-input',
        validation: REGEX,
        pattern: '^(?!xml)[A-Za-z._:][A-Za-z0-9._:]+$',
        errorMessage: 'Alfanumérico e não pode começar por número'
      },
      {
        field: 'pattern-test-input',
        validation: MAX_LENGTH,
        value: 100
      },
      {
        field: 'maxlength-minlength-test-input',
        validation: REQUIRED
      },
      {
        field: 'maxlength-minlength-test-input',
        validation: MIN_LENGTH,
        value: 5
      },
      {
        field: 'maxlength-minlength-test-input',
        validation: MAX_LENGTH,
        value: 200
      },
      {
        field: 'numeric-integer-input',
        validation: REQUIRED
      },
      {
        field: 'numeric-integer-input',
        validation: IS_INTEGER
      },
      {
        field: 'numeric-integer-min-input',
        validation: REQUIRED
      },
      {
        field: 'numeric-integer-min-input',
        validation: IS_INTEGER
      },
      {
        field: 'numeric-integer-min-input',
        validation: MIN,
        value: 1,
        errorMessage: 'Número inteiro positivo'
      },
      {
        field: 'numeric-integer-max-input',
        validation: REQUIRED
      },
      {
        field: 'numeric-integer-max-input',
        validation: IS_INTEGER
      },
      {
        field: 'numeric-integer-max-input',
        validation: MAX,
        value: -1,
        errorMessage: 'Número inteiro negativo'
      },
      {
        field: 'numeric-integer-ranged-input',
        validation: REQUIRED
      },
      {
        field: 'numeric-integer-ranged-input',
        validation: IS_INTEGER
      },
      {
        field: 'numeric-integer-ranged-input',
        validation: NUMERIC_RANGE,
        min: 1,
        max: 999,
        errorMessage: 'Número inteiro positivo menor que 1000'
      },
      {
        field: 'numeric-floating-point-input',
        validation: REQUIRED
      },
      {
        field: 'numeric-floating-point-input',
        validation: IS_NUMBER
      },
      {
        field: 'numeric-floating-point-input',
        validation: DECIMAL_SEPARATOR,
        separator: '.'
      },
      {
        field: 'numeric-comma-separated-floating-point-input',
        validation: REQUIRED
      },
      {
        field: 'numeric-comma-separated-floating-point-input',
        validation: IS_NUMBER
      },
      {
        field: 'numeric-comma-separated-floating-point-input',
        validation: DECIMAL_SEPARATOR,
        separator: ','
      },
      {
        field: 'numeric-comma-separated-floating-point-in-range-input',
        validation: REQUIRED
      },
      {
        field: 'numeric-comma-separated-floating-point-in-range-input',
        validation: IS_NUMBER
      },
      {
        field: 'numeric-comma-separated-floating-point-in-range-input',
        validation: DECIMAL_SEPARATOR,
        separator: ','
      },
      {
        field: 'numeric-comma-separated-floating-point-in-range-input',
        validation: NUMERIC_RANGE,
        min: 2.25,
        max: 200.75
      }
    ]
  }
  t.deepEqual(getStoreInitializationParams(testTarget), expected)
  t.end()
})

tape.test('Reference Data - Details form store - store fields initialization for text areas', async (t) => {
  const testTarget = await setupTestTarget('test-target-textareas')
  const expected = {
    initialValues: {
      'test-textarea': '',
      'length-range-textarea': 'Default text area value',
    },
    validators: [
      {
        field: 'test-textarea',
        validation: REQUIRED
      },
      {
        field: 'length-range-textarea',
        validation: REQUIRED
      },
      {
        field: 'length-range-textarea',
        validation: MIN_LENGTH,
        value: 5
      },
      {
        field: 'length-range-textarea',
        validation: MAX_LENGTH,
        value: 200
      },
    ]
  }
  t.deepEqual(getStoreInitializationParams(testTarget), expected)
  t.end()
})

tape.test('Reference Data - Details form store - store fields initialization for select boxes', async (t) => {
  const testTarget = await setupTestTarget('test-target-selctboxes')
  const expected = {
    initialValues: {
      'test-select': '1',
      'test-select-required': '2',
    },
    validators: [
      {
        field: 'test-select-required',
        validation: REQUIRED
      },
    ]
  }
  t.deepEqual(getStoreInitializationParams(testTarget), expected)
  t.end()
})

tape.test('Reference Data - Details form store - store fields initialization', async (t) => {
  const testTarget = await setupTestTarget('test-target-all')
  const expected = {
    initialValues: {
      name: 'olt_list_instance',
      state: '1',
      externalCode: 'codigo externo',
      ip: '10112325'
    },
    validators: [
      {
        field: 'name',
        validation: REQUIRED
      },
      {
        field: 'name',
        validation: REGEX,
        pattern: '^(?!xml)[A-Za-z._:][A-Za-z0-9._:]+$',
        errorMessage: 'Alfanumérico e não pode começar por número'
      },
      {
        field: 'name',
        validation: MAX_LENGTH,
        value: 100
      },
      {
        field: 'state',
        validation: 'REQUIRED'
      },
      {
        field: 'externalCode',
        validation: REQUIRED
      },
      {
        field: 'externalCode',
        validation: MAX_LENGTH,
        value: 100
      },
      {
        field: 'ip',
        validation: REQUIRED
      },
      {
        field: 'ip',
        validation: IS_INTEGER
      }
    ]
  }
  t.deepEqual(getStoreInitializationParams(testTarget), expected)
  t.end()
})
