import { moduleName, providerNameGenerator } from '~basemodule/app/angular-provider-name-generator'

const namespaceProviderNameGenerator = providerNameGenerator.withNamespace('NaPortalReferenceData')
const directive = namespaceProviderNameGenerator.directive

export const modules = {
  referenceData: moduleName('reference-data')
}

export const providers = {
  directives: {
    CatalogHome: directive('data-na-portal-reference-data-home'),
    ClonePage: directive('data-na-portal-reference-data-clone-page'),
    CreateButton: directive('data-na-portal-reference-data-create-button'),
    CreatePage: directive('data-na-portal-reference-data-create-page'),
    EditPage: directive('data-na-portal-reference-data-edit-page'),
    EntitySearchForm: directive('data-na-portal-reference-data-entity-search-form'),
    EntityTypeSelectBox: directive('data-na-portal-reference-data-entity-type-select-box'),
    NavigationBar: directive('data-na-portal-reference-data-navigation-bar'),
    SearchPage: directive('data-na-portal-reference-data-search-page'),
    SearchTable: directive('data-na-portal-reference-data-search-table'),
    SearchTableActions: directive('data-na-portal-reference-data-search-table-actions'),
    SearchTableConfig: directive('data-na-portal-reference-data-search-table-config'),
    SearchTableToolbar: directive('data-na-portal-reference-data-table-toolbar'),
    ViewPage: directive('data-na-portal-reference-data-view-page')
  }
}
