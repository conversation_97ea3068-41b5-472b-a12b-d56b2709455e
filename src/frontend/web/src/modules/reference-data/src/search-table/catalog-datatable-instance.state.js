import { selector } from '~utils/selector.util'
import { areEqualObjects, isPlainObject } from '~utils/object-utils.util'
import { arrayEquals } from '~utils/array-utils.util'

const emptyObject = Object.freeze({})
const emptyArray = Object.freeze([])
const initialAjaxStateObj = Object.freeze({
  enabled: false,
  method: 'GET',
  url: ''
})
const initialSelectionColumnObj = Object.freeze({
  name: 'checkbox-col',
  title: "<input data-action='select-all-rows' type='checkbox' class='check_box checkbox_results'>",
  visible: true,
  sortable: false,
  expandable: false,
  columnWidth: '75px',
  cellTemplate: 'na.datatableTemplates.catalog.checkboxRenderer'
})
const iconColumnObj = Object.freeze({
  name: 'icon',
  title: '',
  visible: true,
  sortable: false,
  expandable: false,
  columnWidth: '25px',
  cellTemplate: 'na.datatableTemplates.catalog.iconRenderer'
})
const actionColumnObj = Object.freeze({
  name: 'action',
  title: 'Actions',
  visible: true,
  sortable: false,
  expandable: false,
  columnWidth: '125px',
  cellTemplate: 'na.datatableTemplates.catalog.actionRenderer'
})

const currentPageInfo = Object.freeze({
  totalRecords: 0,
  rowsData: emptyArray,
})
const initialStateObj = {
  selectedRowNumbers: emptyObject,
  ajax: initialAjaxStateObj,
  selectionColumn: initialSelectionColumnObj,
  iconColumn: iconColumnObj,
  actionColumn: actionColumnObj,
  dynamicColumns: [],
  currentPageInfo: currentPageInfo,
  reloadTable: false
}

export const initialState = () => initialStateObj

// getters

const getDynamicColumns = (state) => state.dynamicColumns
const getSelectionColumn = (state) => state.selectionColumn
const isSelectionColumnVisible = (state) => state.selectionColumn.visible
const getIconColumn = (state) => state.iconColumn
const getActionColumn = (state) => state.actionColumn
const getAjax = (state) => state.ajax
const getSelectedRowNumbers = (state) => state.selectedRowNumbers
const getSelectedRowNumberList = (state) => Object.keys(state.selectedRowNumbers).filter((key) => state.selectedRowNumbers[key] === true)
const getCurrentPageLength = (state) => state.currentPageInfo.rowsData.length
const rowsData = (state) => state.currentPageInfo.rowsData
const selectedRowsData = (state) => state.currentPageInfo.rowsData.filter((row, index) => state.selectedRowNumbers[index] === true)
const areAllRowsSelected = (state) => getCurrentPageLength(state) > 0 && getCurrentPageLength(state) === getSelectedRowNumberList(state).length
const isAnyRowSelected = (state) => getSelectedRowNumberList(state).length > 0
const isMultipleRowsSelected = (state) => getSelectedRowNumberList(state).length > 1
const reloadTable = (state) => state.reloadTable
const isTableColumnsReset = (state) => arrayEquals(state.dynamicColumns, [])

const getColumns = selector(
  [getSelectionColumn, getIconColumn, getDynamicColumns, getActionColumn],
  function (selectionColumn, iconColumn, dynamicColumns, actionColumn) {
    return [selectionColumn, iconColumn, ...dynamicColumns, actionColumn]
  }
)

const getTableConfig = selector(
  [getColumns, getAjax],
  function (columns, ajax) {
    const result = {
      columns: columns,
      autoWidth: false
    }
    if (ajax.enabled) {
      result.ajax = {
        url: ajax.url,
        method: ajax.method
      }
    }
    return result
  }
)

export const getters = {
  reloadTable: reloadTable,
  selectedRowNumbers: getSelectedRowNumbers,
  areAllRowsSelected: areAllRowsSelected,
  isAnyRowSelected: isAnyRowSelected,
  isMultipleRowsSelected: isMultipleRowsSelected,
  isSelectionColumnVisible: isSelectionColumnVisible,
  selectedRowsData: selectedRowsData,
  rowsData: rowsData,
  isTableColumnsReset,
  tableConfig: getTableConfig,
  columns: getColumns
}

// setters

function setShowSelectionColumn(state, show) {
  if (typeof show !== 'boolean' || state.selectionColumn.visible === show) {
    return state
  }
  return {
    ...state,
    selectionColumn: {
      ...state.selectionColumn,
      visible: show
    }
  }
}

function setAjax(state, ajax) {
  if (!isPlainObject(ajax)) {
    return state
  }
  if (!ajax.enabled && !state.ajax.enabled) {
    return state
  }
  if (areEqualObjects(ajax, state.ajax)) {
    return state
  }
  return {
    ...state,
    ajax: {
      enabled: !!ajax.enabled,
      url: ajax.url,
      method: ajax.method
    }
  }
}

function replaceDynamicColumns(state, columns) {
  if (!Array.isArray(columns)) {
    return state
  }
  if (columns.length <= 0) {
    return {
      ...state,
      dynamicColumns: []
    }
  }
  return {
    ...state,
    dynamicColumns: columns.map((column) => ({ ...column }))
  }
}

function reload(state) {
  if (state.reloadTable) {
    return state
  }
  return {
    ...state,
    reloadTable: true
  }
}

function reloadDone(state) {
  if (!state.reloadTable) {
    return state
  }
  return {
    ...state,
    reloadTable: false
  }
}

function selectRowNumber(state, rowNumber) {
  if (state.selectedRowNumbers[rowNumber] === true) {
    return state
  } else {
    return {
      ...state,
      selectedRowNumbers: {
        ...state.selectedRowNumbers,
        [rowNumber]: true
      }
    }
  }
}

function deselectRowNumber(state, rowNumber) {
  if (state.selectedRowNumbers[rowNumber] !== true) {
    return state
  } else {
    return {
      ...state,
      selectedRowNumbers: {
        ...state.selectedRowNumbers,
        [rowNumber]: false
      }
    }
  }
}

function selectAllRows(state) {
  if (areAllRowsSelected(state)) {
    return state
  }
  const selectedRowNumbers = {}
  for (let i = 0, e = getCurrentPageLength(state); i < e; ++i) {
    selectedRowNumbers[i] = true
  }
  return {
    ...state,
    selectedRowNumbers: selectedRowNumbers
  }
}

function clearSelection(state) {
  if (!isAnyRowSelected(state)) {
    return state
  } else {
    return {
      ...state,
      selectedRowNumbers: emptyObject
    }
  }
}

function setCurrentPageInfo(state, newPageInfo) {
  if (!isPlainObject(newPageInfo)) {
    return state
  }

  const { currentPageInfo } = state
  let { totalRecords, rowsData } = newPageInfo

  totalRecords = typeof totalRecords === 'number' && totalRecords >= 0 ? totalRecords : currentPageInfo.totalRecords
  rowsData = Array.isArray(rowsData) ? rowsData.map(rowData => ({ ...rowData })) : currentPageInfo.rowsData

  if (totalRecords === currentPageInfo.totalRecords &&
        arrayEquals(rowsData, currentPageInfo.rowsData, areEqualObjects) &&
        !isAnyRowSelected(state)) {
    return state
  }

  return clearSelection({
    ...state,
    currentPageInfo: {
      ...currentPageInfo,
      totalRecords,
      rowsData
    }
  })
}

function setActionAndNameDefaultColumnTitles(state = initialStateObj, titles) {
  if (!isPlainObject(titles)) {
    return state
  }

  let action = titles

  action = typeof action === 'string' && action !== '' ? action : state.actionColumn.title

  if (action === state.actionColumn.title) {
    return state
  }

  return {
    ...state,
    actionColumn: {
      ...state.actionColumn,
      title: action,
    },
    dynamicColumns: state.dynamicColumns
  }
}

export const setters = {
  setActionAndNameDefaultColumnTitles,
  replaceDynamicColumns,
  reload,
  reloadDone,
  setCurrentPageInfo,
  setAjax,
  selectRowNumber,
  deselectRowNumber,
  clearSelection,
  selectAllRows,
  setShowSelectionColumn
}
