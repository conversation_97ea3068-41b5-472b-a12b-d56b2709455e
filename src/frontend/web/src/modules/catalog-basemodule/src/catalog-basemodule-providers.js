import { module<PERSON><PERSON>, providerNameGenerator } from '~basemodule/app/angular-provider-name-generator'

/**
 @name catalogBaseModuleJsRoutes
 */

const namespaceProviderNameGenerator = providerNameGenerator.withNamespace('NaPortalCatalogBasemodule')
const controller = namespaceProviderNameGenerator.controller
const directive = namespaceProviderNameGenerator.directive
const service = namespaceProviderNameGenerator.service

export const modules = {
  catalogbasemodule: moduleName('catalogBaseModule'),
  catalogbasemodulecommon: moduleName('catalogBaseModuleCommon')
}

export const providers = {
  common: {
    directives: {
      multiSelect: directive('data-na-portal-catalog-basemodule-multi-select'),
      requiredField: directive('data-na-portal-catalog-basemodule-required')
    }
  }
}
