import { navigateTo } from '~utils/navigation'
import routes from '../common/routes'

import mptModule from '../mpt.angular-module'
import { providers } from '../mpt-providers'

mptModule.controller(providers.general.controllers.Navigation, [
  '$scope',
  function($scope) {
    $scope.navigateToMptAdminHome = function() {
      navigateTo(routes.na.mpt.controllers.AdminHome.administrationHome())
    }

    $scope.navigateToMptGOHome = function() {
      console.info('navigate to GO ADMIN')
    }
  }])
