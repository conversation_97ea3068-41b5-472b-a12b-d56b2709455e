import { setBulkEditData } from '../createoperation/create-operation.services'
import mptModule from '../mpt.angular-module'
import { NormalOperation } from '../createoperation/normal-operation.model'
import * as mptWizardModes from '../constants/wizard-modes.constant'
import { providers } from '../mpt-providers'

mptModule.directive(providers.bulkEdit.directives.setEditBulkContext, [
  mptSetEditBulkContextDirective
])

function mptSetEditBulkContextDirective() {
  return {
    restrict: 'A',
    compile: function () {
      return {
        pre: function (scope) {
          scope.wizardMode = mptWizardModes.EDIT
        },
        post: function (scope, element, attrs) {
          const jsonData = JSON.parse(attrs.editData)
          const bulkEditModel = NormalOperation.build(jsonData)
          setBulkEditData(bulkEditModel)
          scope.$broadcast('MPT::Controller::SetEditData')
        }
      }
    }
  }
}
