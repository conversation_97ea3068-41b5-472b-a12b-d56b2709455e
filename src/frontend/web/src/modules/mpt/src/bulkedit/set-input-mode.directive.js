import mptModule from '../mpt.angular-module'
import { providers } from '../mpt-providers'
import * as mptWizardModes from '../constants/wizard-modes.constant'

mptModule.directive(providers.bulkEdit.directives.setInputMode, [
  mptSetInputModeDirective])
function mptSetInputModeDirective() {
  return {
    restrict: 'A',
    link: function (scope, element) {
      if (scope.wizardMode === mptWizardModes.EDIT) {
        if (element.is('[data-na-portal-mpt-file-uploader]')) {
          element.parent().addClass('disabled')
        }
        element.attr('disabled', 'true')
      }
    }
  }
}
