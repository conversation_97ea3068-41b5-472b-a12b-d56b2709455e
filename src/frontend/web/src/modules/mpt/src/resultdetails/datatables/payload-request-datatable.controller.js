import i18n from '../../common/i18n.service'
import { DatatableCatalog } from '~components/naDatatables/datatable-catalog.constant'

import mptModule from '../../mpt.angular-module'
import { providers } from '../../mpt-providers'

mptModule.controller(providers.resultDetails.controllers.requestDatatableController, [
  '$scope',
  requestDatatableController
])

function requestDatatableController($scope) {
  const requestDatatable = this // jshint ignore: line

  requestDatatable.moduleMessages = window.getMptmoduleMessage
  $scope.datatable = {}
  $scope.paramsPost = {}
  $scope.datatableFeatures = defineTableFeatures()
  $scope.datatableColumns = defineDatatableColumns()

  // Datatable Events
  $scope.$on('CustomDatatables::Controller::SetFormToSubmit', function(event, params) {
    $scope.paramsPost = params
    $scope.$broadcast('Datatables::Controller::SetDataToSend', { paramsPost: params })
  })

  $scope.$on('CustomDatatables::Controller::SetNewDatatable', function(event, params) {
    $scope.datatable = params
    $scope.$broadcast('ToolBar::Directive::GenToolBar', { tableId: $scope.datatable.id })
  })

  $scope.$on('CustomDatatables::Controller::InitDatatable', function(/* event,params */) {
    if ($scope.requestTableData.length !== 0) {
      $scope.$broadcast('Datatables::Controller::SetInitialData', $scope.requestTableData)
    }
  })

  $scope.$on('CustomDatatables::Controller::LoadingNotification', function(event, params) {
    $scope.$broadcast('Datatables::Controller::SetCleanLoadingNotification', params)
  })

  // TABLE DEFINITION FUNCTIONS
  function defineTableFeatures() {
    const tableFeatures = {}
    tableFeatures[DatatableCatalog.features.autoWidth] = false
    tableFeatures[DatatableCatalog.features.ordering] = true
    tableFeatures[DatatableCatalog.features.order] = [[0, 'asc']]
    tableFeatures[DatatableCatalog.features.paging] = false
    tableFeatures[DatatableCatalog.features.info] = false
    tableFeatures[DatatableCatalog.features.serverSide] = false
    tableFeatures[DatatableCatalog.features.withColReorder] = true
    tableFeatures[DatatableCatalog.features.pageLength] = 100
    tableFeatures[DatatableCatalog.features.processing] = false

    return tableFeatures
  }

  function defineDatatableColumns() {
    const column1 = {}; const column2 = {}

    column1[DatatableCatalog.columns.data] = 'parameter'
    column1[DatatableCatalog.columns.title] = i18n('datatables.column.label.parameter')
    column1[DatatableCatalog.columns.type] = 'text'
    column1[DatatableCatalog.columns.className] = 'parameter-col'
    column1[DatatableCatalog.columns.defaultContent] = '--'
    column2[DatatableCatalog.columns.width] = '50%'

    column2[DatatableCatalog.columns.data] = 'value'
    column2[DatatableCatalog.columns.title] = i18n('datatables.column.label.value')
    column2[DatatableCatalog.columns.type] = 'text'
    column2[DatatableCatalog.columns.className] = 'value-col'
    column2[DatatableCatalog.columns.defaultContent] = '--'
    column2[DatatableCatalog.columns.width] = '50%'

    return [column1, column2]
  }
}
