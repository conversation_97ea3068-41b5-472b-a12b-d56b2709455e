import mptModule from '../mpt.angular-module'
import { providers } from '../mpt-providers'

mptModule.directive(providers.createOperation.directives.HighlightMandatory, [
  function() {
    function addRemoveHighlight(element, add) {
      if (element.is('input') && typeof element.attr('data-na-portal-select-box') !== 'undefined') {
        if (add) { element.parent().find('.select2-container').css('border', '1px solid red') } else { element.parent().find('.select2-container').css('border', '') }
      }

      if (element.is('select')) {
        if (add) { element.parent().find('.select2-container').css('border', '1px solid red') } else { element.parent().find('.select2-container').css('border', '') }
      }

      if (element.is('input') || element.is('textarea')) {
        if (add) { element.css('border-color', 'red') } else { element.css('border-color', '') }
      }
    }

    return {
      restrict: 'A',
      require: 'ngModel',
      link: function(scope, element, attrs, ngModelCtrl) {
        ngModelCtrl.$parsers.unshift(function(viewValue) {
          if (viewValue) {
            addRemoveHighlight(element, false)
            ngModelCtrl.$setValidity(attrs.ngModel, true)
            return viewValue
          } else {
            ngModelCtrl.$setValidity(attrs.ngModel, false)
            return undefined
          }
        })

        scope.$on('MPT::Directive::HighlightMandatory', function() {
          if (typeof ngModelCtrl.$modelValue === 'undefined' || ngModelCtrl.$modelValue === '') {
            addRemoveHighlight(element, true)
          }
        })
      }
    }
  }])
