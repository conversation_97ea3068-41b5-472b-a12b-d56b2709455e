import { getMaxFiles } from '~mpt/src/common/settings.service'

import mptModule from '../mpt.angular-module'
import { providers } from '../mpt-providers'

mptModule.directive(providers.createOperation.directives.FileUploader, [
  function () {
    return {
      restrict: 'A',
      link: function(scope, element, attrs) {
        const numberOfFiles = attrs.numberOfFiles
        const $buttonDiv = element.parent()

        if (numberOfFiles >= getMaxFiles()) {
          element.prop('disabled', 'disabled')
          $buttonDiv.addClass('disabled')
        }

        scope.$on('MPT::CreateOperation::DisableFileButton', function(event, params) {
          if (params === true) {
            element.prop('disabled', 'disabled')
            $buttonDiv.addClass('disabled')
          } else {
            element.prop('disabled', '')
            $buttonDiv.removeClass('disabled')
          }
        })

        element.on('change', function() {
          if (element.val() !== '') {
            const file = element[0].files[0]
            scope.$emit('MPT::Controller::AddFileToQueue', file)
            element.val('')
          }
        })
      }
    }
  }])
