import { getSearchForm, validateForm } from './bulk-details-results-search.service'
import { elementTagName } from '~nowc/components/date-time-picker/date-time-picker.element'

import mptModule from '../../../mpt.angular-module'
import { providers } from '../../../mpt-providers'

mptModule.controller(providers.bulkDetails.controllers.ResultsSearch, [
  '$scope',
  '$element',
  mptBulkDetailsResultsSearchController
])

function mptBulkDetailsResultsSearchController($scope, $element) {
  const ctrl = this // jshint ignore:line

  ctrl.submitResultsSearchForm = function() {
    const formToSubmit = {}

    // Avoid properties with null values, this happens when an "unselect" is triggered
    // TODO: refactor as in other searches, where this is not happening
    $.each(ctrl.searchFormItems, function(key, value) {
      if (value != null) {
        formToSubmit[key] = value
      }
    })

    const formValid = validateForm(formToSubmit)

    if (formToSubmit.filterName) {
      delete formToSubmit.filterName
    }

    if (formValid) {
      $scope.$emit('Common::Controller::Broadcast', { event: 'CustomDatatables::Controller::ReloadTable', payload: formToSubmit })
    }
  }

  ctrl.clearSearchFormItems = function() {
    ctrl.searchFormItems = {}
    ctrl.searchFormItems.startTime = ''
    ctrl.searchFormItems.endTime = ''
    $scope.$broadcast('SelectBox::cleanAll')
  }

  ctrl.searchFormItems = getSearchForm()
  if (!ctrl.searchFormItems) {
    ctrl.clearSearchFormItems()
  }

  $scope.$watch(() => ctrl.searchFormItems.startTime, (newValue) => {
    $element.find('[data-field="startTime"]').val(newValue)
  })

  $scope.$watch(() => ctrl.searchFormItems.endTime, (newValue) => {
    $element.find('[data-field="endTime"]').val(newValue)
  })

  $element.on('change input', elementTagName, event => {
    const target = $(event.target)
    const dataField = target[0].getAttribute('data-field')

    ctrl.searchFormItems[dataField] = target[0].value
  })
}
