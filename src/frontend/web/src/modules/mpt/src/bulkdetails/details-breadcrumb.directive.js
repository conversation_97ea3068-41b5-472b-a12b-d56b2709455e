import { mptModule } from '~mpt/src/mpt.angular-module'
import { providers } from '~mpt/src/mpt-providers'
import { updateBreadcrumb, labels } from '~mpt/src/common/breadcrumb.service'
import { getResultDetails } from '~mpt/src/resultdetails/result-details.services'
import { getSelectedTab } from './details.service'

mptModule.directive(providers.bulkDetails.directive.breadcrumb, [
  function () {
    return {
      restrict: 'A',
      link: function (scope, element, attrs) {
        const details = getResultDetails()
        const latestTab = getSelectedTab()
        updateBreadcrumb([
          labels.bulkDetails(attrs.bulkFilename, details.bulkId, latestTab, details.operationId)
        ])
      }
    }
  }
])
