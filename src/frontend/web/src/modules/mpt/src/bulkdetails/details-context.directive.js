import { BulkDetails } from '~mpt/src/bulkdetails/bulk-details.model'
import { setBulkDetails } from '~mpt/src/bulkdetails/details.service'

import { providers } from '../mpt-providers'
import mptModule from '../mpt.angular-module'

mptModule.directive(providers.bulkDetails.directive.BulkDetailsContext, [
  function () {
    return {
      restrict: 'A',
      priority: 100,
      compile: function (element, attrs) {
        return {
          pre: function preLink(scope) {
            const parsedJson = JSON.parse(attrs.formattedDetailsJson)
            parsedJson.dynamicFields = JSON.parse(attrs.dynamicFields)
            const bulkDetails = BulkDetails.build(parsedJson)
            setBulkDetails(bulkDetails)
            scope.$broadcast('MptBulkDetails::Controller::SetDetails')
          }
        }
      }
    }
  }])
