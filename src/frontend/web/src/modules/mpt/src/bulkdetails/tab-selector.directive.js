import mptModule from '../mpt.angular-module'
import { providers } from '../mpt-providers'
import routes from '~mpt/src/common/routes'
import { TabContexts } from '~mpt/src/bulkdetails/tab-contexts.constant'
import { notifyPageLoaded } from '~utils/navigation'

mptModule.directive(providers.bulkDetails.directive.TabSelector, [
  mptDetailsTabSelector
])

function mptDetailsTabSelector() {
  return {
    restrict: 'A',
    terminal: false,
    link: function (scope, element) {
      const tabContainer = element[0].shadowRoot.querySelector('.tab-item-list')

      tabContainer.addEventListener('click', (ev) => {
        const tabButton = ev.originalTarget.closest('.tab-item')
        if (!tabButton) return

        const isPreviouslyActive = tabButton.classList.contains('tab-item--active')

        window.requestAnimationFrame(() => {
          const isCurrentlyActive = tabButton.classList.contains('tab-item--active')

          if (isCurrentlyActive && !isPreviouslyActive) {
            let tabContext = TabContexts.resultsTab

            if (tabButton.id === `tab-${TabContexts.detailsTab}-button`) {
              tabContext = TabContexts.detailsTab
            }

            const route = routes.na.mpt.controllers.BulkDetails.getBulkDetails
            const url = route(scope.detailsCtrl.bulkDetails.id, tabContext).url
            notifyPageLoaded({ url })
          }
        })
      }, { capture: true })
    }
  }
}
