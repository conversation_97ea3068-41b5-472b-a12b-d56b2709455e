import { htmlEscape } from '~utils/string.util'
import { save<PERSON><PERSON>er } from '~components/naDatatables-v2/datatable-render-store'

function dataAttributes(parameters) {
  const dataAttr = Object.keys(parameters).reduce(function (acc, key) {
    const val = parameters[key]
    if (key.indexOf('data-') === 0 && val !== '' && val != null) {
      acc[key] = val.toString()
    }
    return acc
  }, {})
  if (dataAttr.length <= 0) {
    return ''
  }
  return Object.keys(dataAttr).map(function(key) {
    return htmlEscape(key) + '="' + htmlEscape(dataAttr[key].replace('"', '\"')) + '"'
  }).join(' ')
}

function link(parameters) {
  const icon = parameters.icon
  const action = parameters.action
  const title = parameters.title
  const context = parameters.context
  const isEnabled = parameters.enabled

  if (parameters.visible === false) {
    return ''
  }

  const disabledAttribute = (isEnabled === false) ? 'disabled' : ''
  return `<button ${dataAttributes(parameters)} 
            data-row="${htmlEscape(context.rowNumber)}" 
            title="${htmlEscape(title)}" 
            data-action="${htmlEscape(action)}" 
            class="btn fx-btn-action" 
            ${disabledAttribute}>
            <i class="${htmlEscape(icon)}"></i>
          </button>`
}

function actionRenderer(context) {
  const i18n = context.i18n
  const rowData = context.rowData
  const rowNumber = context.rowNumber
  return '<span class="fx-table-actions">' +
            link({
              icon: 'glyphicon glyphicon-play' + (rowData.playPermission ? ' green' : ''),
              action: 'startOperation',
              title: i18n('na.portal.mpt.datatables.column.label.actions.play'),
              enabled: rowData.playPermission,
              'data-id': rowData.id,
              'data-row': rowNumber,
              context
            }) +
            link({
              icon: 'glyphicon glyphicon-pause' + (rowData.pausePermission ? ' yellow' : ''),
              action: 'pauseOperation',
              title: i18n('na.portal.mpt.datatables.column.label.actions.pause'),
              enabled: rowData.pausePermission,
              'data-id': rowData.id,
              'data-row': rowNumber,
              context
            }) +
            link({
              icon: 'glyphicon glyphicon-stop' + (rowData.stopPermission ? ' red' : ''),
              action: 'stopOperation',
              title: i18n('na.portal.mpt.datatables.column.label.actions.stop'),
              enabled: rowData.stopPermission,
              'data-id': rowData.id,
              'data-row': rowNumber,
              context
            }) +
            link({
              icon: 'glyphicon glyphicon-pencil',
              action: 'editOperation',
              title: i18n('na.portal.mpt.datatables.column.label.actions.edit'),
              enabled: rowData.editPermission,
              'data-id': rowData.id,
              'data-row': rowNumber,
              context
            }) +
            link({
              icon: 'fuxicons fuxicons-eye',
              action: 'viewOperation',
              title: i18n('na.datatables.action.label.view'),
              enabled: rowData.viewPermission,
              'data-id': rowData.id,
              'data-row': rowNumber,
              context
            }) +
            '</span>'
}

saveRenderer({ key: 'na.datatableTemplates.mpt.bulkSearch.actionRenderer', renderer: actionRenderer })
