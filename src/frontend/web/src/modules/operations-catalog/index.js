import './style-load'
import '~utils/i18n/implementation.play'
import '~utils/modal/implementation.na'
import '~utils/toast-notification/implementation'
import '~utils/nowc/i18n.configuration'
import { moduleName } from './src/operations-catalog.angular-module'
import { moduleName as catalogBaseModuleName } from '~catalog-basemodule'
import * as $ from 'jquery'
import { bootstrap } from 'angular'
import '~basemodule/fuxi'
import '~basemodule/app/src/common/enable-tooltip.directive'
import '~nowc/components/splitter/splitter.element'
import '~nowc/components/shadow-scroll/shadow-scroll.element'
import '~nowc/components/tabs/tab-container.element'
import '~nowc/components/label/label.element'
import '~nowc/components/collapsible/collapsible.element'
import '~components/i18n-container/i18n-container.element'
import '~components/i18n/i18n.element'

const importAll = (requires) => requires.keys().forEach(requires)
importAll(require.context('./src', true, /\.datatable-renderer\.js$/))
importAll(require.context('./src', true, /\.directive\.js$/))
importAll(require.context('./src', true, /\.adapter\.js$/))
importAll(require.context('./src', true, /\.element\.js$/))
importAll(require.context('../../utils', true, /\/implementation\.js$/))
importAll(require.context('../../utils', true, /\/implementation\.ssr\.js$/))
importAll(require.context('../../utils', true, /\/implementation\.play\.(js|ts)$/))
importAll(require.context('~components/naDatatables-v2', true, /\.directive\.js$/))
importAll(require.context('~components/naDatatables-v2', true, /\.datatable-renderer\.js$/))
importAll(require.context('~components/pagecontainer', true, /\.directive\.js$/))

global.na = global.na || {}
global.na.$ = $
bootstrap(document.body, [moduleName, catalogBaseModuleName])
