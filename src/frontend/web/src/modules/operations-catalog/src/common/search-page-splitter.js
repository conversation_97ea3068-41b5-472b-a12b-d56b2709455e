export function initializeSearchPageSplitter(element, formStore) {
  const splitterElement = element.find('x-splitter')

  formStore.getters.isFormCollapsed
    ? splitterElement.attr('collapsed', '')
    : splitterElement.removeAttr('collapsed')

  splitterElement.on('collapse', () => {
    formStore.setters.updateFormCollapse(true)
  })

  splitterElement.on('expand', () => {
    formStore.setters.updateFormCollapse(false)
  })
}
