import { selector } from '~utils/selector.util'

function isArrayEquals(arr1, arr2) {
  return arr1.length === arr2.length && arr1.every(function (val, index) {
    return val === arr2[index]
  })
}

export function createBaseSearchStoreDefinition() {
  function initialState() {
    return {
      tablePageNumber: 0,
      tablePageLength: 0,
      tableColumnsVisibility: {},
      tableColumns: [],
      formCollapsed: false
    }
  }

  // getters

  function getTablePageNumber(state) {
    return state.tablePageNumber
  }

  function getTablePageLength(state) {
    return state.tablePageLength
  }

  function getTableColumnsVisibility(state) {
    return state.tableColumnsVisibility
  }

  function getFormCollapsed(state) {
    return state.formCollapsed
  }

  const isColumnVisible = selector(
    getTableColumnsVisibility,
    function (tableColumnsVisibility) {
      return function(id) {
        return tableColumnsVisibility[id] === true
      }
    }
  )

  // setters
  function resetState() {
    return initialState()
  }

  function updateTablePageNumber(state, tablePageNumber) {
    if (state.tablePageNumber === tablePageNumber) {
      return state
    }
    return Object.assign({}, state, { tablePageNumber: tablePageNumber })
  }

  function updateTablePageLength(state, tablePageLength) {
    if (state.tablePageLength === tablePageLength) {
      return state
    }
    return Object.assign({}, state, { tablePageLength: tablePageLength })
  }

  function updatetableColumns(state, tableColumns) {
    if (!Array.isArray(tableColumns)) {
      console.warn('tableColumns must be an array')
      return state
    }
    if (isArrayEquals(state.tableColumns, tableColumns)) {
      return state
    }
    return Object.assign({}, state, {
      tableColumns: tableColumns.slice(),
      tableColumnsVisibility: tableColumns.reduce(function (acc, column) {
        acc[column] = true
        return acc
      }, {})
    })
  }

  function updateColumnVisibility(state, newVisibilityStateObj) {
    const cleanedVisibilityObj = Object.keys(state.tableColumnsVisibility).reduce(function (acc, id) {
      acc[id] = typeof newVisibilityStateObj[id] === 'boolean' ? newVisibilityStateObj[id] : state.tableColumnsVisibility[id]
      return acc
    }, {})

    const isEqual = Object.keys(state.tableColumnsVisibility).every(function (column) {
      return state.tableColumnsVisibility[column] === cleanedVisibilityObj[column]
    })

    if (isEqual) {
      return state
    }

    return Object.assign({}, state, {
      tableColumnsVisibility: cleanedVisibilityObj
    })
  }

  function updateFormCollapse(state, formCollapsed) {
    if (state.formCollapsed === formCollapsed) {
      return state
    }
    return Object.assign({}, state, { formCollapsed: formCollapsed })
  }

  function updateTableState(state, tableState) {
    let newState = state
    if (typeof tableState.pageLength === 'number') {
      newState = updateTablePageLength(newState, tableState.pageLength)
    }
    if (typeof tableState.pageNumber === 'number') {
      newState = updateTablePageNumber(newState, tableState.pageNumber)
    }
    if (typeof tableState.columnVisibility === 'object' && tableState.columnVisibility != null) {
      newState = updateColumnVisibility(newState, tableState.columnVisibility)
    }
    return newState
  }

  const getters = {
    tablePageNumber: getTablePageNumber,
    tablePageLength: getTablePageLength,
    tableColumnsVisibility: getTableColumnsVisibility,
    isFormCollapsed: getFormCollapsed,
    isColumnVisible: isColumnVisible
  }

  const setters = {
    tablePageLength: updateTablePageLength,
    tablePageNumber: updateTablePageNumber,
    tableColumns: updatetableColumns,
    updateFormCollapse: updateFormCollapse,
    updateColumnVisibility: updateColumnVisibility,
    resetState: resetState,
    updateTableState: updateTableState
  }

  return {
    initialState: initialState,
    getters: getters,
    setters: setters
  }
}
