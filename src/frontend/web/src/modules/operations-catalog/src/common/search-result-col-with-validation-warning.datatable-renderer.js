import { saveRenderer } from '~components/naDatatables-v2/datatable-render-store'
import { htmlEscape } from '~utils/string.util'
import { i18n } from '~utils/i18n'

function columnWithValidationWarning(context, tooltipLabel) {
  const rowData = context.rowData
  const dataHtml = htmlEscape(context.cellData)
  if (rowData.valid === false) {
    return `<i class="fx-alert-icon fx-alert-icon-warning fx-icon" title="${tooltipLabel}"></i> ${dataHtml}`
  }
  return dataHtml
}

function versionColumnWithValidationWarning(context) {
  return columnWithValidationWarning(context, i18n('na.portal.operationscatalog.notification.this.version.is.invalid'))
}

function operationColumnWithValidationWarning(context) {
  return columnWithValidationWarning(context, i18n('na.portal.operationscatalog.notification.this.operation.is.invalid'))
}

saveRenderer({ key: 'na.datatableTemplates.operationscatalog.columnWithValidationWarning.version', renderer: versionColumnWithValidationWarning })
saveRenderer({ key: 'na.datatableTemplates.operationscatalog.columnWithValidationWarning.operation', renderer: operationColumnWithValidationWarning })
