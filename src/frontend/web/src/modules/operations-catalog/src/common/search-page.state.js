import { deepClone } from '~utils/deep-clone.util'

const emptyObj = Object.freeze({})

const initialStateObj = {
  requestPayload: emptyObj,
  sidebarSearch: emptyObj,
  submitNumber: 0
}

export const initialState = () => initialStateObj

const requestPayload = (state) => state.requestPayload
const sidebarSearchValues = (state) => state.sidebarSearchValues
const submitNumber = (state) => state.submitNumber

export const getters = {
  requestPayload,
  sidebarSearchValues,
  submitNumber
}

function clearRequestPayload(state = initialStateObj) {
  if (state.requestPayload === emptyObj) {
    return state
  }

  return {
    ...state,
    requestPayload: emptyObj
  }
}

function updateRequestPayload(state = initialStateObj, newPayload) {
  if (state.requestPayload === newPayload) {
    return state
  }

  return {
    ...state,
    requestPayload: deepClone(newPayload)
  }
}

function clearSearchField (state = initialStateObj, fieldName) {
  if (state.sidebarSearch[fieldName] == null) {
    return state
  }

  const { [fieldName]: _, ...newSidebarSearch } = state.sidebarSearch
  return {
    ...state,
    sidebarSearch: newSidebarSearch
  }
}

function clearSearchForm (state = initialStateObj) {
  return {
    ...state,
    sidebarSearch: emptyObj
  }
}

function setSearchValue (state = initialStateObj, { fieldName, fieldValue }) {
  if (state.sidebarSearch[fieldName] === fieldValue) {
    return state
  }
  if (fieldValue == null || fieldValue === '') {
    return clearSearchField(state, fieldName)
  }

  return {
    ...state,
    sidebarSearch: {
      ...state.sidebarSearch,
      [fieldName]: fieldValue
    }
  }
}

function setSearchValues (state = initialStateObj, fields = {}) {
  const { sidebarSearch } = state
  if (Object.keys(fields).every(key => sidebarSearch[key] === fields[key])) {
    return state
  }

  return {
    ...state,
    sidebarSearch: {
      ...state.sidebarSearch,
      ...fields
    }
  }
}

function submitSearch(state = initialStateObj) {
  return {
    ...state,
    requestPayload: state.sidebarSearch,
    submitNumber: state.submitNumber + 1
  }
}

function submitSearchIfValuesChanged(state = initialStateObj) {
  if (state.requestPayload === state.sidebarSearch) {
    return state
  }

  return {
    ...state,
    requestPayload: state.sidebarSearch,
    submitNumber: state.submitNumber + 1
  }
}

export const setters = {
  clearRequestPayload,
  updateRequestPayload,
  clearSearchField,
  clearSearchForm,
  setSearchValue,
  setSearchValues,
  submitSearchIfValuesChanged,
  submitSearch
}
