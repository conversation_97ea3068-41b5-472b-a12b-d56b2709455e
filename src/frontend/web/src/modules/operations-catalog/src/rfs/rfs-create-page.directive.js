import operationsCatalogModule from '../operations-catalog.angular-module'
import { providers } from '../operations-catalog-providers'
import { updateBreadcrumb, labels } from '../common/breadcrumb.service'

operationsCatalogModule.directive(providers.rfs.directives.rfsCreatePage, [rfsCreatePageDirective])

function rfsCreatePageDirective() {
  return {
    restrict: 'A',
    priority: 0,
    terminal: false,
    link: function () {
      updateBreadcrumb(
        labels.rfs.search(),
        labels.rfs.create()
      )
    }
  }
}
