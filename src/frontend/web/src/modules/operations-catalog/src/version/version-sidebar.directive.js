import operationsCatalogModule from '../operations-catalog.angular-module'
import { providers } from '../operations-catalog-providers'
import { store as versionResultStore } from './version-result.store'

operationsCatalogModule.directive(providers.versions.directives.sidebar, [sidebarDirective])

function sidebarDirective() {
  return {
    restrict: 'A',
    priority: 1,
    terminal: false,
    link: function (scope, element) {
      versionResultStore.setters.submitSearchIfValuesChanged()
      const initialFormValues = versionResultStore.getters.requestPayload
      element.find('[data-field]').each(function() {
        const elem = $(this)
        const field = elem.attr('data-field')
        if (initialFormValues[field] != null && initialFormValues[field] !== '') {
          elem.val(initialFormValues[field]).trigger('change', initialFormValues[field])
        }
      })

      element.on('change input', '[data-field]', function () {
        versionResultStore.setters.setSearchValue({
          fieldName: $(this).attr('data-field'),
          fieldValue: $(this).val()
        })
      })

      element.on('click', '[data-action="search"]', function () {
        versionResultStore.setters.submitSearch()
      })

      element.on('click', '[data-action="clear"]', function () {
        const sideBarFormFields = $('[data-field]')
        sideBarFormFields.each(function() {
          $(this).val('')
        })
        versionResultStore.setters.clearSearchForm()
      })
    }
  }
}
