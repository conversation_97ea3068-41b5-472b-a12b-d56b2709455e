import { providerName as table } from '~components/naDatatables-v2/datatable.directive'
import operationsCatalogModule from '../operations-catalog.angular-module'
import { providers } from '../operations-catalog-providers'
import { store } from './version-characteristics-tab.store'

operationsCatalogModule.directive(providers.versions.directives.versionOperationsDataTable, [versionOperationsDataTableDirective])

function versionOperationsDataTableDirective() {
  return {
    restrict: 'A',
    require: table,
    priority: 2,
    link: function (scope, element, attr, ctrl) {
      const $searchCount = $('#operationsCount')
      const $searchInput = $('#operationsSearchInput')
      const $searchButton = $('#operationsSearchSubmit')

      ctrl.paramsPost = function () {
        const filter = store.getters.operationsFilter
        return filter ? { name: filter } : {}
      }

      ctrl.addDrawHandler(() => ctrl.getPageInfo().then(pageInfo => store.setters.operationsCount(pageInfo.recordsTotal)))

      function refreshSearch() {
        store.setters.operationsFilter($searchInput.val() || '')
        ctrl.reloadTable({ resetPagination: true })
      }

      $searchButton.click(function () { refreshSearch() })
      $searchInput.keyup((e) => { if (e.which === 13) { refreshSearch() } })

      const listener = store.listenStateChange((newStateStore, oldStateStore) => {
        if (newStateStore.getters.operationsCount !== oldStateStore.getters.operationsCount) {
          $searchCount.text(newStateStore.getters.operationsCount)
        }
      })

      scope.$on('$destroy', listener.unregister)
    }
  }
}
