import operationsCatalogModule from '../operations-catalog.angular-module'
import { providers } from '../operations-catalog-providers'
import { getVersion } from '../version-context/version-context.service'
import { navigateTo } from '~utils/navigation'
import { updateBreadcrumb, labels } from '../common/breadcrumb.service'
import resourceSearchFormStore from './resource-search-form.store'
import { initializeSearchPageSplitter } from '~operations-catalog/src/common/search-page-splitter'

operationsCatalogModule.directive(providers.resource.directives.SearchPage, [SearchPageDirective])

function SearchPageDirective() {
  return {
    restrict: 'A',
    priority: 0,
    terminal: false,
    link: function(scope, element) {
      updateBreadcrumb(labels.resource.search())

      initializeSearchPageSplitter(element, resourceSearchFormStore)

      element.on('click', '[data-page-action="create"]', () => getVersion().then(version => navigateTo({
        route: operationsCatalogJsRoutes.na.operationscatalog.controllers.ResourceController.getCreatePage(version.id)
      })))
    }
  }
}
