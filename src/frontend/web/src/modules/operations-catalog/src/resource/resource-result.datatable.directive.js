import operationsCatalogModule from '../operations-catalog.angular-module'
import { providerName as datatableDirective } from '~components/naDatatables-v2/datatable.directive'
import { providers } from '../operations-catalog-providers'
import { navigateTo } from '~utils/navigation'
import { store as resourceResultStore } from './resource-result.store'
import { cloneOperation, deleteOperation } from '../operation/operation-actions.functions'
import resourceSearchStore from './resource-search-form.store'

export const providerName = providers.resource.directives.resultsDataTable
operationsCatalogModule.directive(providerName, [resourceResultsDataTableDirective])

function reflectPaginationFromStore(store) {
  return function (config) {
    const savedPageLength = store.getters.tablePageLength
    const savedPageNumber = store.getters.tablePageNumber
    const pagination = Object.assign({}, config.pagination)

    if (savedPageLength > 0 &&
            config.pagination.enabled &&
            config.pagination.pageLengthMenu.enabled &&
            config.pagination.pageLengthMenu.options.indexOf(savedPageLength) >= 0
    ) {
      pagination.pageLength = savedPageLength
    }

    if (savedPageNumber > 0 &&
            config.pagination.enabled &&
            config.pagination.pageLength > 0
    ) {
      pagination.pageNumber = savedPageNumber
    }
    return Object.assign({}, config, { pagination: pagination })
  }
}

function reflectPaginationToStore(store) {
  return function (pageInfo) {
    store.setters.updateTableState({
      pageLength: pageInfo.pageLength,
      pageNumber: pageInfo.pageNumber
    })
  }
}

function resourceResultsDataTableDirective() {
  return {
    restrict: 'A',
    priority: 2,
    require: datatableDirective,
    terminal: false,
    link: function (scope, element, attrs, ctrl) {
      ctrl.paramsPost = () => resourceResultStore.getters.requestPayload

      ctrl.addConfigReducer(reflectPaginationFromStore(resourceSearchStore))
      ctrl.addDrawHandler(() => ctrl.getPageInfo().then(reflectPaginationToStore(resourceSearchStore)))

      ctrl.addReloadMiddleware({
        priority: Number.MAX_SAFE_INTEGER,
        callback: function(data, next) {
          // prevent making a request when current version is not loaded yet, happens when opening
          // the operation search page for the first time in a session
          if (resourceResultStore.getters.requestPayload?.version) {
            next(data)
          }
        }
      })

      const observer = resourceResultStore.listenStateChange((newSnapshot, oldSnapshot) => {
        if (newSnapshot.getters.submitNumber > oldSnapshot.getters.submitNumber && ctrl.isTableInitialized) {
          ctrl.reloadTable({ resetPagination: true })
        }
      })

      scope.$on('$destroy', observer.unregister)

      function rowDataOfAction(actionElement) {
        const rowNumber = actionElement.attr('data-row')
        return element.dataTable().api().data()[rowNumber]
      }

      element.on('click', 'a[data-action="view"]', function () {
        const rowData = rowDataOfAction($(this))
        navigateTo({ route: operationsCatalogJsRoutes.na.operationscatalog.controllers.ResourceController.view(rowData.id) })
      })

      element.on('click', 'a[data-action="edit"]', function () {
        const rowData = rowDataOfAction($(this))
        navigateTo({ route: operationsCatalogJsRoutes.na.operationscatalog.controllers.ResourceController.getEditPage(rowData.id) })
      })

      element.on('click', 'a[data-action="clone"]', function () {
        const data = rowDataOfAction($(this))
        cloneOperation(data)
      })

      element.on('click', 'a[data-action="delete"]', function () {
        const data = rowDataOfAction($(this))
        deleteOperation(data).then(() => ctrl.reloadTable({ resetPagination: true }))
      })
    }
  }
}
