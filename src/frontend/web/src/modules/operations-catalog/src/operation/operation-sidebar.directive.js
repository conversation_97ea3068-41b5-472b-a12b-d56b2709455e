import operationsCatalogModule from '../operations-catalog.angular-module'
import * as versionContext from '../version-context/version-context.service'
import { providers } from '../operations-catalog-providers'
import { store as cfsResultStore } from '../cfs/cfs-result.store'
import { store as rfsResultStore } from '../rfs/rfs-result.store'
import { store as resourceResultStore } from '../resource/resource-result.store'

export const providerName = providers.operation.directives.sidebar
operationsCatalogModule.directive(providerName, [operationSidebarDirective])

const storesByOperationType = {
  cfs: cfsResultStore,
  rfs: rfsResultStore,
  resource: resourceResultStore
}

function operationSidebarDirective() {
  return {
    restrict: 'A',
    priority: 1,
    terminal: false,
    link: function (scope, element, attrs) {
      const store = storesByOperationType[attrs.operationType.toLowerCase()]

      function updateVersionOnStore(version) {
        store.setters.setSearchValues({
          version: version.name,
          catEntityVersion: version.catEntityVersion
        })
      }

      const observer = versionContext.observeVersionChange(function(newVersion) {
        updateVersionOnStore(newVersion)
        store.setters.submitSearchIfValuesChanged()
      })

      scope.$on('$destroy', observer.unregister)

      versionContext.getVersion().then((version) => {
        updateVersionOnStore(version)
        store.setters.submitSearchIfValuesChanged()
      })

      const initialFormValues = store.getters.requestPayload
      element.find('[data-field]').each(function() {
        const elem = $(this)
        const field = elem.attr('data-field')
        if (initialFormValues[field] != null && initialFormValues[field] !== '') {
          elem.val(initialFormValues[field])
        }
      })

      element.on('change input', '[data-field]', function () {
        store.setters.setSearchValue({
          fieldName: $(this).attr('data-field'),
          fieldValue: $(this).val()
        })
      })

      element.on('click', 'input[data-action="search"]', function () {
        versionContext.getVersion().then(function (version) {
          updateVersionOnStore(version)
          store.setters.submitSearch()
        })
      })
    }
  }
}
