import operationsCatalogModule from '../../operations-catalog.angular-module'
import { providers } from '../../operations-catalog-providers'
import { providerName as datatable } from '~components/naDatatables-v2/datatable.directive'
import { flatPath, getEntityNameFromPath } from '../operation-details-datatable.helper'
import { getEntity } from '../stores/operation-details-store.service'

operationsCatalogModule.directive(providers.operation.directives.outputAttributesDataTable, [
  outputAttributesDataTableDirective
])

function outputAttributesDataTableDirective() {
  return {
    restrict: 'A',
    priority: 2,
    require: datatable,
    terminal: false,
    link: function (scope, element, attrs, ctrl) {
      const stringComparator = new Intl.Collator()
      ctrl.addReloadMiddleware(function(tableInfo, next) {
        getEntity().then((operation) => {
          const entityMap = operation.operationAttributes
            .filter(attribute => attribute && attribute.direction.toUpperCase() === 'OUT')
            .reduce(function (entityMap, attribute) {
              const entityName = getEntityNameFromPath(attribute.path)
              entityMap[entityName] = entityMap[entityName] || []
              entityMap[entityName].push({ ...attribute })
              return entityMap
            }, {})

          const tableData = []
          Object.keys(entityMap).forEach((key) => {
            entityMap[key].forEach((attribute) => {
              attribute.pathString = flatPath(attribute.path)
              tableData.push({ entityName: key, attribute: attribute })
            })
          })

          const sortAlgorithmByAttribute = {
            attribute(rowData1, rowData2) {
              const name1 = rowData1.attribute.name
              const name2 = rowData2.attribute.name
              const comparisonValue = stringComparator.compare(name1, name2)
              if (comparisonValue === 0) {
                const path1 = rowData1.attribute.pathString
                const path2 = rowData2.attribute.pathString
                return stringComparator.compare(path1, path2)
              }
              return comparisonValue
            }
          }

          const comparisonMultiplier = {
            ascendant: 1,
            descendant: -1
          }

          if (Object.keys(tableInfo.sorts).length > 0) {
            tableData.sort(function (a, b) {
              return Object.keys(tableInfo.sorts).reduce(function (accumulator, attributeToSort) {
                if (accumulator === 0 && sortAlgorithmByAttribute[attributeToSort] != null) {
                  accumulator = sortAlgorithmByAttribute[attributeToSort](a, b) * comparisonMultiplier[tableInfo.sorts[attributeToSort]]
                }
                return accumulator
              }, 0)
            })
          }

          return next({
            data: tableData,
            recordsTotal: tableData.length,
            recordsFiltered: tableData.length
          })
        })
      })
    }
  }
}
