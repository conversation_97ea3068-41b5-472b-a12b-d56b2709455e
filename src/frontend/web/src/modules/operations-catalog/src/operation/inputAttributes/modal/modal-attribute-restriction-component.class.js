import '~components/i18n/i18n.element'

function i18nHtml(key, ...params) {
  return `<x-i18n key="${[key, ...params].join(';')}"></x-i18n>`
}

export function RestrictionComponent(content, updateCallback, i18n) {
  const Constraint = {
    REGEXP: 'REGEXP',
    SIZE: 'SIZE',
    RANGE: 'RANGE',
    ENUMC: 'ENUMC',
    FLOAT_RANGE: 'FLOAT_RANGE',
    NONE: ''
  }

  let entityConstraint = null
  let constraint = null
  const $regex = $('#attribute-regexInput', content)
  const $minSize = $('#attribute-inputMinSize', content)
  const $maxSize = $('#attribute-inputMaxSize', content)
  const $minRange = $('#attribute-inputMinRange', content)
  const $maxRange = $('#attribute-inputMaxRange', content)
  const $minFloatRange = $('#attribute-inputMinFloatRange', content)
  const $maxFloatRange = $('#attribute-inputMaxFloatRange', content)
  const $radioButtons = $(':input[name="restriction"]', content)
  const $enumRestrictionField = $('#attribute-enumRestriction select', content)

  const $errorConditionMessage = $('#attribute-conditionValidation', content)
  const $regexErrorMessage = $('#attribute-regexValidation', content)
  const $enumerationErrorMessage = $('#attribute-enumerationValidation', content)

  function hideErrorMessages() {
    $errorConditionMessage.hide()
    $regexErrorMessage.hide()
    $enumerationErrorMessage.hide()
    $('.has-error', content).removeClass('has-error')
  }

  function isInteger(value) {
    return (value === null || value % 1 === 0)
  }

  function hasValidRegExSyntax(regexp) {
    try {
      RegExp(regexp)
      return true
    } catch (e) {
      return false
    }
  }

  function setFieldError(...args) {
    for (const $input of args.filter(input => input != null)) {
      $input.closest('.form-group').toggleClass('has-error', true)
    }
  }

  function validate(save) {
    hideErrorMessages()

    if (constraint == null) {
      return true
    }

    switch (constraint.type) {
      case Constraint.SIZE:
      {
        let minSize = $minSize.val()
        let maxSize = $maxSize.val()

        // ignore validation when field are null, while interacting with the form
        if (!save && !minSize && !maxSize) {
          return true
        }

        // required min size
        if (!minSize) {
          $errorConditionMessage.show().find('.errorMessage').html(i18nHtml('na.portal.operationscatalog.modal.validation.required'))
          setFieldError($minSize)
          return false
        }

        // required max size
        if (!maxSize) {
          $errorConditionMessage.show().find('.errorMessage').html(i18nHtml('na.portal.operationscatalog.modal.validation.required'))
          setFieldError($maxSize)
          return false
        }

        // Values must be integer
        if (!isInteger(minSize) || !isInteger(maxSize)) {
          $errorConditionMessage.show().find('.errorMessage').html(i18nHtml('na.portal.operationscatalog.modal.error.notIntegerNumber'))

          if (!isInteger(minSize)) {
            setFieldError($minSize)
          }

          if (!isInteger(maxSize)) {
            setFieldError($maxSize)
          }

          return false
        }

        minSize = parseInt(minSize)
        maxSize = parseInt(maxSize)

        // Not negative numbers
        if (minSize < 0 || maxSize < 0) {
          $errorConditionMessage.show().find('.errorMessage').html(i18nHtml('na.portal.operationscatalog.modal.validation.notNegative'))

          if (minSize < 0) {
            setFieldError($minSize)
          }

          if (maxSize < 0) {
            setFieldError($maxSize)
          }

          return false
        }

        // Lower limit bigger than upper limit
        if (maxSize < minSize) {
          $errorConditionMessage.show().find('.errorMessage').html(i18nHtml('na.portal.operationscatalog.modal.validation.minLowerThanMax'))
          return false
        }

        // Operation constraint must respect entity constraint
        if (entityConstraint != null && entityConstraint.type === Constraint.SIZE) {
          if (minSize < entityConstraint.min || maxSize > entityConstraint.max) {
            setFieldError($minRange, $maxRange)
            $errorConditionMessage.show().find('.errorMessage').html(i18nHtml('na.portal.operationscatalog.modal.error.outOfRangeOfEntityRule', entityConstraint.min, entityConstraint.max))
            return false
          }
        }

        break
      }
      case Constraint.RANGE:
      {
        let min = $minRange.val()
        let max = $maxRange.val()

        // ignore validation when field are null, while interacting with the form
        if (!save && !min && !max) {
          return true
        }

        // At least one field is required
        if (!min && !max) {
          $errorConditionMessage.show().find('.errorMessage').html(i18nHtml('na.portal.operationscatalog.modal.validation.atLeastOneFieldRequired'))
          setFieldError($minRange, $maxRange)
          return false
        }

        // Values must be integer
        if (!isInteger(min) || !isInteger(max)) {
          $errorConditionMessage.show().find('.errorMessage').html(i18nHtml('na.portal.operationscatalog.modal.error.notIntegerNumber'))

          if (!isInteger(min)) {
            setFieldError($minRange)
          }

          if (!isInteger(max)) {
            setFieldError($maxRange)
          }

          return false
        }

        min = parseInt(min)
        max = parseInt(max)

        // Not negative numbers
        if (min < 0 || max < 0) {
          $errorConditionMessage.show().find('.errorMessage').html(i18nHtml('na.portal.operationscatalog.modal.validation.notNegative'))

          if (min < 0) {
            setFieldError($minRange)
          }

          if (max < 0) {
            setFieldError($maxRange)
          }

          return false
        }

        // Lower limit bigger than upper limit
        if (min != null && max != null && max < min) {
          $errorConditionMessage.show().find('.errorMessage').html(i18nHtml('na.portal.operationscatalog.modal.validation.minLowerThanMax'))
          return false
        }

        // Operation constraint must respect entity constraint
        if (entityConstraint != null && entityConstraint.type === Constraint.RANGE) {
          if (min < entityConstraint.minRange || max > entityConstraint.maxRange) {
            setFieldError($minRange, $maxRange)
            $errorConditionMessage.show().find('.errorMessage').html(i18nHtml('na.portal.operationscatalog.modal.error.outOfRangeOfEntityRule', entityConstraint.minRange, entityConstraint.maxRange))
            return false
          }
        }

        break
      }
      case Constraint.FLOAT_RANGE:
      {
        let minFloat = $minFloatRange.val()
        let maxFloat = $maxFloatRange.val()

        // ignore validation when field are null, while interacting with the form
        if (!save && !minFloat && !maxFloat) {
          return true
        }

        // At least one field is required
        if (!minFloat && !maxFloat) {
          $errorConditionMessage.show().find('.errorMessage').html(i18nHtml('na.portal.operationscatalog.modal.validation.atLeastOneFieldRequired'))
          setFieldError($minFloatRange, $maxFloatRange)
          return false
        }

        minFloat = Number(minFloat)
        maxFloat = Number(maxFloat)

        // Not negative numbers
        if (minFloat < 0 || maxFloat < 0) {
          $errorConditionMessage.show().find('.errorMessage').html(i18nHtml('na.portal.operationscatalog.modal.validation.notNegative'))

          if (minFloat < 0) {
            setFieldError($minFloatRange)
          }

          if (maxFloat < 0) {
            setFieldError($maxFloatRange)
          }

          return false
        }

        // Lower limit bigger than upper limit
        if (minFloat != null && maxFloat != null && maxFloat < minFloat) {
          $errorConditionMessage.show().find('.errorMessage').html(i18nHtml('na.portal.operationscatalog.modal.validation.minLowerThanMax'))
          return false
        }

        // Operation constraint must respect entity constraint
        if (entityConstraint != null && entityConstraint.type === Constraint.FLOAT_RANGE) {
          if (minFloat < entityConstraint.minRange || maxFloat > entityConstraint.maxRange) {
            setFieldError($minRange, $maxRange)
            $errorConditionMessage.show().find('.errorMessage').html(i18nHtml('na.portal.operationscatalog.modal.error.outOfRangeOfEntityRule', entityConstraint.minRange, entityConstraint.maxRange))
            return false
          }
        }

        break
      }
      case Constraint.REGEXP:
      {
        let regexp = $regex.val()

        // ignore validation when field is null, while interacting with the form
        if (!save && !regexp) {
          return true
        }

        // Is required
        if (!regexp) {
          $regexErrorMessage.show().find('.errorMessage').html(i18nHtml('na.portal.operationscatalog.modal.error.regularExpression.required'))
          setFieldError($regex)
          return false
        }

        regexp = regexp.trim()

        // Valid regex syntax
        if (!hasValidRegExSyntax(regexp)) {
          $regexErrorMessage.show().find('.errorMessage').html(i18nHtml('na.portal.operationscatalog.modal.error.regularExpression.invalidSyntax'))
          setFieldError($regex)
          return false
        }

        /* Operation constraint must respect entity constraint
                if (entityConstraint != null
                    && (entityConstraint.type === Constraint.SIZE || entityConstraint.type === Constraint.RANGE || entityConstraint.type === Constraint.FLOAT_RANGE)) {
                    var regexExp = new RegExp(regexp);
                    var smallerThanMin = -1, biggerThanMax = -1;
                    var min = null, max = null;

                    if (entityConstraint.type === Constraint.SIZE) {
                        min = entityConstraint.min;
                        max = entityConstraint.max;
                        smallerThanMin = min - 1;
                        biggerThanMax = max + 1;
                    } else if (entityConstraint.type === Constraint.RANGE) {
                        min = entityConstraint.minRange;
                        max = entityConstraint.maxRange;
                        smallerThanMin = min - 1;
                        biggerThanMax = max + 1;
                    }

                    if (regexExp.test(biggerThanMax.toString())){
                        setFieldError($minRange, $maxRange);
                        $regexErrorMessage.show().find('.errorMessage').html(i18n('modal.error.outOfRangeOfEntityRule', min, max));
                        return false;
                    }

                    if (smallerThanMin >= 0 && regexExp.test(smallerThanMin.toString())) {
                        setFieldError($minRange, $maxRange);
                        $regexErrorMessage.show().find('.errorMessage').html(i18n('modal.error.outOfRangeOfEntityRule', min, max));
                        return false;
                    }
                } */

        break
      }

      case Constraint.ENUMC:
      {
        // Must not have all options selected
        if ($enumRestrictionField.find('option').length === constraint.valueList.length) {
          $enumerationErrorMessage.show().find('.errorMessage').html(i18nHtml('modal.error.enumeration.sameAsEntity'))
          setFieldError($enumRestrictionField)
          return false
        }

        // Is required
        if (constraint.valueList.length === 0) {
          $enumerationErrorMessage.show().find('.errorMessage').html(i18nHtml('modal.error.enumeration.notEmpty'))
          setFieldError($enumRestrictionField)
          return false
        }
        break
      }
    }
    return true
  }

  function changeRestrictionsByAttributeType(typeName) {
    $('#attribute-restrictions div[data-restriction]', content).hide() // hide all restrictions

    switch (typeName) {
      case 'string':
        $('#attribute-restrictions, #attribute-notApplyRestriction, #attribute-logicConditionSize, #attribute-regexRestriction', content).show()
        break
      case 'float':
        $('#attribute-restrictions, #attribute-notApplyRestriction, #attribute-logicConditionFloatRange, #attribute-regexRestriction', content).show()
        break
      case 'number':
        $('#attribute-restrictions, #attribute-notApplyRestriction, #attribute-logicConditionRange, #attribute-regexRestriction', content).show()
        break
      case 'enumeration':
        $('#attribute-restrictions, #attribute-notApplyRestriction, #attribute-enumRestriction', content).show()
        break
      default:
        $('#attribute-restrictions', content).hide()
        return
    }

    checkDefaultRestriction()
    clearForm()
  }

  function clearForm() {
    if (constraint != null) {
      hideErrorMessages()
      $('#attribute-restrictions :input:not([type="radio"])', content).val('')
    }
    validate()
  }

  function checkedRestriction() {
    return $radioButtons.filter(':checked').val()
  }

  function checkDefaultRestriction() {
    checkRestriction(Constraint.NONE)
  }

  function checkRestriction(type) {
    $radioButtons.filter('[value="' + type + '"]').prop('checked', true)
  }

  function updateRestriction(type, constraint) {
    if (checkedRestriction() !== type) {
      checkRestriction(type)
    }
    updateCallback(constraint)
  }

  function setRestrictionSize() {
    constraint = {
      type: Constraint.SIZE,
      typei18n: i18n('attribute.constraint.type.' + Constraint.SIZE),
      min: parseInt($minSize.val()),
      max: parseInt($maxSize.val())
    }
    updateRestriction(constraint.type, constraint)
  }

  function setRestrictionRange() {
    constraint = {
      type: Constraint.RANGE,
      typei18n: i18n('attribute.constraint.type.' + Constraint.RANGE),
      minRange: parseInt($minRange.val()),
      maxRange: parseInt($maxRange.val())
    }
    updateRestriction(constraint.type, constraint)
  }

  function setNoRestriction() {
    constraint = null
    updateRestriction('', constraint)
  }

  function setRestrictionFloatRange() {
    constraint = {
      type: Constraint.FLOAT_RANGE,
      typei18n: i18n('attribute.constraint.type.' + Constraint.FLOAT_RANGE),
      minRange: Number($minFloatRange.val()),
      maxRange: Number($maxFloatRange.val())
    }
    updateRestriction(constraint.type, constraint)
  }

  function setRestrictionRegex() {
    constraint = {
      type: Constraint.REGEXP,
      typei18n: i18n('attribute.constraint.type.' + Constraint.REGEXP),
      regexp: $regex.val()
    }
    updateRestriction(constraint.type, constraint)
  }

  function setRestrictionEnum() {
    constraint = {
      type: Constraint.ENUMC,
      typei18n: i18n('attribute.constraint.type.' + Constraint.ENUMC),
      valueList: $enumRestrictionField.select2('data').map(function (option) {
        return {
          name: option.name,
          position: option.position,
          value: option.value
        }
      })
    }
    updateRestriction(constraint.type, constraint)
  }

  $radioButtons.change(function () {
    switch (checkedRestriction()) {
      case Constraint.SIZE:
        setRestrictionSize()
        break
      case Constraint.RANGE:
        setRestrictionRange()
        break
      case Constraint.FLOAT_RANGE:
        setRestrictionFloatRange()
        break
      case Constraint.REGEXP:
        setRestrictionRegex()
        break
      case Constraint.ENUMC:
      {
        setRestrictionEnum()
        break
      }
      case Constraint.NONE:
        setNoRestriction()
        break
    }

    validate()
  })

  $regex.on('change input', function () {
    setRestrictionRegex()
    validate()
  })

  $minRange.on('change input', function () {
    setRestrictionRange()
    validate()
  })

  $maxRange.on('change input', function () {
    setRestrictionRange()
    validate()
  })

  $minFloatRange.on('change input', function () {
    setRestrictionFloatRange()
    validate()
  })

  $maxFloatRange.on('change input', function () {
    setRestrictionFloatRange()
    validate()
  })

  $minSize.on('change input', function () {
    setRestrictionSize()
    validate()
  })

  $maxSize.on('change input', function () {
    setRestrictionSize()
    validate()
  })

  $enumRestrictionField.on('change input', function () {
    setRestrictionEnum()
    validate()
  })

  return {
    setValue: function (operationConstraint) {
      const type = operationConstraint != null ? operationConstraint.type : Constraint.NONE
      switch (type) {
        case Constraint.SIZE:
          $minSize.val(parseInt(operationConstraint.min))
          $maxSize.val(parseInt(operationConstraint.max))
          setRestrictionSize()
          break
        case Constraint.RANGE:
          $minRange.val(parseInt(operationConstraint.minRange))
          $maxRange.val(parseInt(operationConstraint.maxRange))
          setRestrictionRange()
          break
        case Constraint.FLOAT_RANGE:
          $minFloatRange.val(Number(operationConstraint.minRange))
          $maxFloatRange.val(Number(operationConstraint.maxRange))
          setRestrictionFloatRange()
          break
        case Constraint.REGEXP:
          $regex.val(operationConstraint.regexp)
          setRestrictionRegex()
          break
        case Constraint.NONE:
          setNoRestriction()
          break
      }
    },
    getValue: function () {
      return constraint
    },
    isValid: validate,
    onEntityAttributeChange: function (typeName, entityAttribute) {
      // get entity attribute constraint
      if (entityAttribute != null && entityAttribute.attributeType.constraints.length > 0) {
        entityConstraint = entityAttribute.attributeType.constraints[0]
      }

      changeRestrictionsByAttributeType(typeName)
    }
  }
}
