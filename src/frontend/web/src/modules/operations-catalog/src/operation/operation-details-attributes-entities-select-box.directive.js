import operationsCatalogModule from '../operations-catalog.angular-module'
import { providers } from '../operations-catalog-providers'
import { entitiesAttributesSelectBoxController } from './operation-details-attributes-entities-select-box.controller'
import i18n from '~utils/i18n'
import { attributePathTemplate } from './operation-entity-attribute-path.renderer'

export const directiveName = providers.operation.directives.entitiesAttributesSelectBox
operationsCatalogModule.directive(directiveName, [entitiesAttributesSelectBoxDirective])

function entitiesAttributesSelectBoxDirective() {
  return {
    restrict: 'A',
    priority: 2,
    controller: entitiesAttributesSelectBoxController,
    terminal: false,
    link: function (scope, $select2, attr, ctrl) {
      // create an empty disabled select
      $select2.select2({
        placeholder: i18n('na.portal.operationscatalog.selectbox.placeholder'),
        width: '100%',
        disabled: true,
        language: {
          noResults: noResults
        },
        templateResult: templateResult,
        templateSelection: templateResult,
        escapeMarkup: function (res) {
          return res
        },
        data: []
      })

      scope.detailsEntitiesStore.getSortedAttributeList().then((initialAttributeList) => {
        let initialData = ctrl.reduceResult(initialAttributeList)
        initialData = processResults(initialData)
        if (ctrl.getInitialAttribute() != null) {
          const initialAttribute = ctrl.getInitialAttribute()
          initialData.forEach(function(option) {
            if (option.data.attribute.name === initialAttribute.name &&
                            option.data.attribute.path === initialAttribute.path) {
              option.selected = true
            }
          })
        }

        // when the entity name is selected, reinitialize with query and initial value behaviours
        $select2.select2({
          placeholder: i18n('na.portal.operationscatalog.selectbox.placeholder'),
          width: '100%',
          disabled: attr.disabled != null,
          language: {
            noResults: noResults
          },
          templateResult: templateResult,
          templateSelection: templateResult,
          escapeMarkup: function (res) {
            return res
          },
          data: initialData.filter(function(option) { return option.selected === true }),
          ajax: {
            transport: function (params, success) {
              scope.detailsEntitiesStore.getSortedAttributeList().then((attributeList) => {
                let result = ctrl.reduceResult(attributeList)
                if (params.data && params.data.q && params.data.q.trim() !== '') {
                  const query = params.data.q.trim()
                  result = result.filter(function (option) {
                    return option.name.trim().indexOf(query) >= 0
                  })
                }
                success({
                  results: processResults(result)
                })
              })
            }
          }
        })
      })

      function noResults() {
        return i18n('na.portal.operationscatalog.input.nomatches')
      }

      function templateResult(data) {
        if (!data.data) {
          return data.text
        }
        return attributePathTemplate({
          attributeName: data.data.attribute.name,
          jsonPath: data.data.attribute.path,
          isValid: data.data.attribute.valid,
          invalidTooltipLabel: i18n('na.portal.operationscatalog.notification.this.attribute.is.invalid')

        })
      }

      function processResults(data) {
        const results = []
        if (data != null) {
          data.forEach(function (attr) {
            results.push({
              id: attr.name + attr.pathAsString,
              text: attr.name + attr.pathAsString,
              data: {
                path: attr.pathAsString,
                attribute: attr
              }
            })
          })
        }
        return results
      }
    }
  }
}
