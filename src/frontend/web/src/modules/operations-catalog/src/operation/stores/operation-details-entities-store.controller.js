import { request } from '~utils/proxy'
import { store } from './details-entities-relations.store'

export function ComponentDataStore() {
  const ctrl = this
  ctrl.dataStore = store
  const pendingPromises = []
  store.setters.resetTree()

  this.initData = function (initialEntityData) {
    store.setters.updateTree(initialEntityData)
    ctrl.pendingPromises.forEach(function (callback) {
      callback(initialEntityData)
    })
  }

  this.initDataFrom = function (route) {
    return request({
      route: route,
      onSuccess: function (entityData) {
        store.setters.updateTree(entityData)
        pendingPromises.forEach(function (callback) {
          callback(entityData)
        })
      }
    })
  }

  this.getStore = () => new Promise((resolve) => {
    if (!store.getters.isTreeReset) {
      resolve(store)
    } else {
      pendingPromises.push(resolve)
    }
  })

  this.getSortedAttributeList = () => this.getStore().then(store => store.getters.sortedAttributeList)
}
