import { notifyError } from '~utils/toast-notification'
import { request } from '~utils/proxy'
import { showModal, hideModal, modalSizes } from '~utils/modal'
import baseI18n from '~utils/i18n'
import i18n from '../../../common/i18n'
import { AttributeModel } from './modal-attribute.model'
import { RestrictionComponent } from './modal-attribute-restriction-component.class'
import { EntityAttributeComponent } from './modal-entity-attribute-component.class'
import { MultiplicityComponent } from './modal-multiplicity-component.class'
import { getEntity } from '../../stores/operation-details-store.service'
import instanceStore from '../../stores/operation-databinding.store'
import { store as entityRelationsStore } from '../../stores/details-entities-relations.store'

function formIsValid(...components) {
  return components.every(component => component.isValid(true))
}

function applyIdsToAttribute(attributeToSubmit, operationAttributes) {
  if (operationAttributes.length <= 0) { return }

  const equalOperationAttributes = operationAttributes.filter(function byOriginalInputAttribute(attribute) {
    return attribute &&
            typeof attribute.direction === 'string' &&
            attribute.direction.toUpperCase() === 'IN' &&
            attribute.path === attributeToSubmit.path &&
            attribute.name === attributeToSubmit.name
  })

  equalOperationAttributes
    .filter(attribute => attribute.id != null)
    .forEach(function applyAttrIdsFromOriginalAttribute(attribute) {
      attributeToSubmit.id = attribute.id
    })

  if (attributeToSubmit.constraints.length > 0) {
    attributeToSubmit.constraints.forEach(function (constraintToSubmit) {
      equalOperationAttributes.forEach(function (attribute) {
        // apply ID to constraints
        attribute.constraints
          .filter((constraint) => constraint.id != null && constraint.type === constraintToSubmit.type)
          .forEach((constraint) => { constraintToSubmit.id = constraint.id })

        // apply ID to enumValues
        attribute.constraints
          .filter((constraint) => constraint.id != null &&
                        constraint.type === constraintToSubmit.type &&
                        constraint.type === 'ENUMC')
          .forEach(function (constraint) {
            const valueIdMap = constraint.valueList.reduce(function(accumulator, value) {
              accumulator[value.name] = value.id
              return accumulator
            }, {})

            constraintToSubmit.valueList
              .filter(value => valueIdMap[value.name] != null)
              .forEach(value => { value.id = valueIdMap[value.name] })
          })
      })
    })
  }
}

export function openCreateAttributeModal() {
  const title = i18n('na.portal.operationscatalog.modal.title.add')

  request({
    route: operationsCatalogJsRoutes.na.operationscatalog.controllers.OperationController.inputAttributeModal('create'),
    onSuccess(content) {
      const model = AttributeModel()
      let restrictionComponent, multiplicityComponent, entityAttributeComponent

      showModal({
        title,
        body: content,
        modalSize: modalSizes.extraLarge,
        buttonOk: {
          callback() {
            if (formIsValid(restrictionComponent, multiplicityComponent, entityAttributeComponent)) {
              const attributeToSubmit = model.getAttributeToSubmit()
              getEntity().then(function(entity) {
                applyIdsToAttribute(attributeToSubmit, entity.operationAttributes)
                instanceStore.setters.addInputAttribute(attributeToSubmit)
                hideModal()
              })
            } else {
              notifyError({
                title: i18n('form.notification.failure.title'),
                message: i18n('form.notification.failure.message'),
              })
            }
          },
          dismiss: false,
          label: baseI18n('na.button.label.save')
        },
        onVisible: ({ modalBody }) => {
          // restriction component
          restrictionComponent = RestrictionComponent(modalBody, model.setConstraint, i18n)

          // multiplicity component
          multiplicityComponent = MultiplicityComponent(modalBody, model.setMultiplicity, i18n)
          multiplicityComponent.setValue(model.getMultiplicity())

          // entity attribute component
          entityAttributeComponent = EntityAttributeComponent(modalBody, model.setEntityAttribute)
          entityAttributeComponent.registerChangeCallback(restrictionComponent.onEntityAttributeChange)
          entityAttributeComponent.registerChangeCallback(multiplicityComponent.resetValue)

          entityAttributeComponent.init()
        }
      })
    }
  })
}

export function openEditAttributeModal(attribute) {
  request({
    route: operationsCatalogJsRoutes.na.operationscatalog.controllers.OperationController.inputAttributeModal('edit'),
    onSuccess(data) {
      const model = AttributeModel()
      model.setAttribute(attribute)
      let restrictionComponent, multiplicityComponent, entityAttributeComponent

      const selectEntry = entityRelationsStore.getters.sortedAttributeList.find(attr => {
        return attr.name === attribute.name && attr.path === attribute.path
      })

      const title = i18n('na.portal.operationscatalog.modal.title.edit')

      const content = $(data)
      content.find('#attribute-name').append($('<option selected></option>')
        .attr('value', selectEntry.name + selectEntry.pathAsString)
        .attr('data-of-option', JSON.stringify(selectEntry))
      )

      showModal({
        title,
        body: content,
        modalSize: modalSizes.extraLarge,
        buttonOk: {
          callback() {
            if (formIsValid(restrictionComponent, multiplicityComponent, entityAttributeComponent)) {
              const attributeToSubmit = model.getAttributeToSubmit()
              getEntity().then(function(entity) {
                applyIdsToAttribute(attributeToSubmit, entity.operationAttributes)
                instanceStore.setters.updateInputAttribute(attributeToSubmit)
                hideModal()
              })
            } else {
              notifyError({
                title: i18n('form.notification.failure.title'),
                message: i18n('form.notification.failure.message'),
              })
            }
          },
          dismiss: false,
          label: baseI18n('na.button.label.save')
        },
        onVisible: () => {
          // restriction component
          restrictionComponent = RestrictionComponent(content, model.setConstraint, i18n)

          // multiplicity component
          multiplicityComponent = MultiplicityComponent(content, model.setMultiplicity, i18n)

          // entity attribute component
          entityAttributeComponent = EntityAttributeComponent(content, model.setEntityAttribute)
          entityAttributeComponent.registerChangeCallback(restrictionComponent.onEntityAttributeChange)

          entityAttributeComponent.init()

          // set modal values only after contextualized with attribute type
          multiplicityComponent.setValue(model.getMultiplicity())
          restrictionComponent.setValue(model.getConstraint())
        }
      })
    }
  })
}
