/* eslint-disable no-useless-escape */
import * as store from '../../common/search-page-store'

const sessionStorageKey = 'dataTableStoreOperation::Storage'

function getSessionIdFromCookie() {
  return document.cookie.replace(/(?:(?:^|.*;\s*)sessionId\s*\=\s*([^;]*).*$)|^.*$/, '$1')
}

let watchers = []

const operationStates = loadSavedState() || {
  cfs: store.createInitialState(),
  rfs: store.createInitialState(),
  resource: store.createInitialState()
}

let state = operationStates.cfs
const mutators = store.mutators
const getters = store.getters

function loadSavedState() {
  const storageItem = sessionStorage.getItem(sessionStorageKey)
  if (!storageItem) {
    return null
  }
  const savedStorage = JSON.parse(storageItem)
  if (savedStorage.sessionId == null || savedStorage.sessionId !== getSessionIdFromCookie()) {
    return null
  }
  return savedStorage.state
}

function saveState() {
  sessionStorage.setItem(sessionStorageKey, JSON.stringify({
    sessionId: getSessionIdFromCookie(),
    state: operationStates
  }))
}

export function setRequestPayload(newPayload) {
  const previousRequestPayload = getters.requestPayload(state)
  mutators.updateRequestPayload(state, newPayload)
  broadcast(getters.requestPayload(state), previousRequestPayload)
}

export function resetRequestPayload() {
  mutators.clearRequestPayload(state)
}

export function getRequestPayload() {
  return getters.requestPayload(state)
}

function broadcast(requestPayload, previousRequestPayload) {
  watchers.forEach(function (callback) {
    callback(requestPayload, previousRequestPayload)
  })
}

export function watchPayloadChange(callback) {
  watchers.push(callback)
  return {
    unregister: function () {
      watchers = watchers.filter(function (cb) { return cb !== callback })
    }
  }
}

export function setSearchValue(fieldName, fieldValue) {
  if (fieldValue == null || fieldValue === '') {
    mutators.clearSearchField(state, fieldName)
  } else {
    mutators.setSearchField(state, fieldName, fieldValue)
  }
}

export function getSidebarSearchValues() {
  return getters.sidebarSearchValues(state)
}

export function setOperationTypeState(operationType) {
  state = operationStates[operationType]
}

export function copySearchToRequestPayload() {
  const previousRequestPayload = getters.requestPayload(state)
  mutators.copySearchToRequestPayload(state)
  saveState()
  broadcast(getters.requestPayload(state), previousRequestPayload)
}
