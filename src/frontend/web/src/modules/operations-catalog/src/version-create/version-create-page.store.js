import { Store } from '~utils/store.util'
import { initialState, getters, setters, constants } from './version-create-page.state'
import { i18n } from '~utils/i18n'
export const store = Store({ initialState, getters, setters, constants }, 'Operations Catalog - Version Create Page')

const { validations } = constants

store.setters.defineDefaultErrorMessage({
  [validations.REQUIRED]: i18n('na.portal.catalog.basemodule.validations.mandatory'),
})

export default store
