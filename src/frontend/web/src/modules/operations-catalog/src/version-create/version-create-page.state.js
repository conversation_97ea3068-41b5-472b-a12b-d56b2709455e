import { isPlainObject, areEqualObjects, hasSameKeysObjects } from '~utils/object-utils.util'
import { arrayEquals } from '~utils/array-utils.util'
import { selector } from '~utils/selector.util'

const fieldValues = Object.freeze({
  catEntityVersion: '',
  operationVersion: '',
  creationType: '',
  name: '',
  description: ''
})

const submissionStates = {
  NONE: 'NONE',
  VALIDATING: 'VALIDATING',
  SUBMITTING: 'SUBMITTING',
  DONE: 'DONE'
}

const submissionNextStates = {
  NONE: 'VALIDATING',
  VALIDATING: 'SUBMITTING',
  SUBMITTING: 'DONE',
  DONE: 'DONE'
}

const validations = {
  REQUIRED: 'REQUIRED',
}

const submissionInitialState = submissionStates.NONE

export const constants = {
  submissionStates,
  validations
}

const emptyObject = Object.freeze({})
const initialStateObj = Object.freeze({
  fieldValues: fieldValues,
  initialFieldValues: fieldValues,
  validators: emptyObject,
  submissionState: submissionInitialState,
  defaultErrorMessages: {
    REQUIRED: 'Required field',
  }
})

export const initialState = () => initialStateObj

const isDirty = (state) => state.fieldValues !== state.initialFieldValues
const isPristine = (state) => state.fieldValues === state.initialFieldValues
const getFieldValues = (state) => state.fieldValues
const fieldValidators = (state) => state.validators
const defaultErrorMessages = (state) => state.defaultErrorMessages
const dataToSubmit = (state) => state.fieldValues
const submissionState = (state) => state.submissionState
const hasEntityVersion = (state) => state.fieldValues.catEntityVersion !== ''
const hasOperationVersion = (state) => state.fieldValues.operationVersion !== ''
const isSubmissionDoneCorrectly = (state) => state.submissionState === 'DONE'

const concatUniqueError = (errors, validation) => {
  if (errors.findIndex(errorValidation => errorValidation.validation === validation.validation) < 0) {
    return errors.concat(validation)
  }
}

const hasValue = (value) => value != null && value !== ''

const validateRequired = (errors, value, errorMessage) => !hasValue(value)
  ? concatUniqueError(errors, { validation: validations.REQUIRED, errorMessage })
  : errors

const errorsByField = selector(
  [getFieldValues, fieldValidators, defaultErrorMessages],
  (formValues, fieldValidators, defaultErrorMessages) => {
    return Array.from(new Set([
      ...Object.keys(formValues),
      ...Object.keys(fieldValidators)
    ])).reduce((obj, key) => {
      const validators = fieldValidators[key]
      const value = formValues[key]
      obj[key] = validators.reduce((errors, validator) => {
        const errorMessage = validator.errorMessage || defaultErrorMessages[validator.validation]
        if (validator.validation === validations.REQUIRED) {
          errors = validateRequired(errors, value, errorMessage)
        }
        return errors
      }, [])
      return obj
    }, {})
  })

const isValid = selector(
  [errorsByField],
  (errorsByField) => {
    return Object.values(errorsByField).every(errors => errors.length <= 0)
  }
)

export const getters = {
  isDirty,
  isPristine,
  dataToSubmit,
  fieldValues: getFieldValues,
  hasEntityVersion,
  hasOperationVersion,
  isSubmissionDoneCorrectly,
  submissionState,
  errorsByField,
  isValid
}

const reset = () => initialStateObj

const setFields = (state, fields) => {
  if (!isPlainObject(fields)) {
    return state
  }
  const { fieldValues } = state

  const newValues = Object.keys(fieldValues).reduce((acc, key) => {
    acc[key] = typeof fields[key] === 'string' ? fields[key] : fieldValues[key]
    return acc
  }, {})

  if (areEqualObjects(newValues, fieldValues)) {
    return state
  }

  return {
    ...state,
    fieldValues: newValues,
  }
}

const setValidators = (state, validators) => {
  if (!Array.isArray(validators)) {
    return state
  }

  const fieldValidators = validators.reduce((obj, validator) => {
    const { field, validation } = validator
    obj[field] = obj[field] || []
    const newValidator = { validation }
    if (typeof validator.errorMessage === 'string') {
      newValidator.errorMessage = validator.errorMessage
    }
    if (validation === validations.REQUIRED) {
      obj[field].push(newValidator)
    }
    return obj
  }, Object.keys(fieldValues).reduce((obj, key) => Object.assign(obj, { [key]: [] }), {}))

  if (hasSameKeysObjects(fieldValidators, state.validators)) {
    const stateValidators = state.validators
    const areValidatorsEquals = Object.keys(fieldValidators).every(key => arrayEquals(stateValidators[key], fieldValidators[key], areEqualObjects))
    if (areValidatorsEquals) {
      return state
    }
  }

  return {
    ...state,
    validators: fieldValidators
  }
}

const initFields = (state, fields) => {
  if (!isPlainObject(fields)) {
    return state
  }
  const { initialValues, validators } = fields

  if (!isPlainObject(initialValues) || !Array.isArray(validators)) {
    return state
  }

  const state1 = setFields(state, initialValues)
  const state2 = setValidators(state1, validators)
  const newState = resetSubmissionState(state2)

  if (newState === state && isPristine(state)) {
    return state
  }
  return {
    ...newState,
    initialFieldValues: newState.fieldValues
  }
}

const advanceSubmissionStage = (state) => {
  const nexState = submissionNextStates[state.submissionState]
  if (nexState === state.submissionState) {
    return state
  }
  return {
    ...state,
    submissionState: nexState
  }
}

const resetSubmissionState = (state) => {
  if (state.submissionState === submissionInitialState) {
    return state
  }
  return {
    ...state,
    submissionState: submissionInitialState
  }
}

const setCreationType = (state, creationType) => setFields(state, { creationType })

const defineDefaultErrorMessage = (state, errorMessages) => {
  if (!isPlainObject(errorMessages)) {
    return state
  }
  const errorMessagesToReplace = Object.entries(state.defaultErrorMessages).reduce((acc, [key, value]) => {
    acc[key] = typeof errorMessages[key] === 'string' ? errorMessages[key] : value
    return acc
  }, {})

  if (areEqualObjects(state.defaultErrorMessages, errorMessagesToReplace)) {
    return state
  }
  return {
    ...state,
    defaultErrorMessages: errorMessagesToReplace
  }
}

export const setters = {
  reset,
  initFields,
  setFields,
  setCreationType,
  advanceSubmissionStage,
  resetSubmissionState,
  defineDefaultErrorMessage
}
