import operationsCatalogModule from '../operations-catalog.angular-module'
import { providers } from '../operations-catalog-providers'
import { labels, updateBreadcrumb } from '../common/breadcrumb.service'
import { getEntity } from '../operation/stores/operation-details-store.service'

operationsCatalogModule.directive(providers.cfs.directives.cfsEditPage, [cfsEditPageDirective])

function cfsEditPageDirective() {
  return {
    restrict: 'A',
    priority: 0,
    terminal: false,
    link() {
      getEntity().then(({ id, name }) => {
        updateBreadcrumb(
          labels.cfs.search(),
          labels.cfs.edit(id, name)
        )
      })
    }
  }
}
