import { SelectBox } from '~nowc/components/selectbox/selectbox.element'
import { entitySearchQuery } from '../operation/entities-search-select-box.service'
import { withEntitiesSelectBoxLogic } from '../operation/entities-select-box.element.mixin'

class CfsEntitiesSelectBox extends withEntitiesSelectBoxLogic(SelectBox) {
  constructor(...args) {
    super(...args)
    this.queryTransportRequest = entitySearchQuery(versionName => operationsCatalogJsRoutes.na.operationscatalog.controllers.CfsController.entitiesSearch(versionName))
  }
}

customElements.define('x-opercat-cfs-entities-select', CfsEntitiesSelectBox)
