import operationsCatalogModule from '../operations-catalog.angular-module'
import { providers } from '../operations-catalog-providers'

operationsCatalogModule.directive(providers.sidebar.directives.sidebar, [sidebarDirective])

function sidebarDirective() {
  return {
    restrict: 'A',
    link: function (scope, $element) {
      const element = $element[0]
      element.addEventListener('click', (event) => {
        if (event.target.matches('input[data-action="clear"]')) {
          event.preventDefault()
          element.querySelectorAll('[data-field]').forEach(field => {
            field.value = ''
            $(field).trigger('change')
          })
        }
      })
    }
  }
}
