import fuxiModule from './fuxi.angular-module'
import { providers } from '../app/basemodule-providers'
import i18n from '~utils/i18n'

fuxiModule.directive(providers.fuxi.directives.splitterInit, [
  '$timeout',
  splitterInitDirective
])

function splitterInitDirective($timeout) {
  return {
    restrict: 'A',
    terminal: false,
    priority: 0,
    scope: true,
    link: function (scope, element) {
      function SplitterView(sidebarEl, contentEl, position, collapsed) {
        this.$sidebarEl = $(sidebarEl)
        this.$contentEl = $(contentEl)
        this.position = position || 'left'
        this.collapsed = collapsed || false
        this.delta = undefined
        this.offset = 40
        this.basePos = 0
        this.CSSDefsExpanded = {}
        this.CSSDefsCollapsed = {}

        element.attr('data-original-title', (this.collapsed ? i18n('na.general.maximize') : i18n('na.general.minimize')))
        switch (this.position) {
          case 'top' :
            // TODO
            break
          case 'left' :
            this.delta = this.$sidebarEl.outerWidth()
            this.basePos = (this.$sidebarEl.css('left') === 'auto' ? 0 : parseInt(this.$sidebarEl.css('left'), 10))

            this.$contentEl.css('left', (this.collapsed ? this.basePos : this.basePos + this.delta + 9))

            this.CSSDefsExpanded.sidebarEl = { left: this.basePos }
            this.CSSDefsExpanded.contentEl = { left: this.basePos + this.delta + 9 }
            this.CSSDefsCollapsed.sidebarEl = { left: this.basePos + this.offset - this.delta }
            this.CSSDefsCollapsed.contentEl = { left: this.basePos + this.offset + 9 }

            $(element).tooltip({ placement: 'right' })
            break
          case 'bottom' :
            this.delta = this.$sidebarEl.outerHeight()

            this.$contentEl.css('bottom', (this.collapsed ? 0 : this.delta + 9))

            this.CSSDefsExpanded.sidebarEl = { bottom: 0 }
            this.CSSDefsExpanded.contentEl = { bottom: this.delta + 9 }
            this.CSSDefsCollapsed.sidebarEl = { bottom: this.offset - this.delta }
            this.CSSDefsCollapsed.contentEl = { bottom: this.offset + 9 }

            $(element).tooltip({ placement: 'top' })
            break
          case 'right' :
            this.delta = this.$sidebarEl.outerWidth()
            this.basePos = (this.$sidebarEl.css('right') === 'auto' ? 0 : parseInt(this.$sidebarEl.css('right'), 10))

            this.$contentEl.css('right', (this.collapsed ? this.basePos : this.basePos + this.delta + 9))

            this.CSSDefsExpanded.sidebarEl = { right: this.basePos }
            this.CSSDefsExpanded.contentEl = { right: this.basePos + this.delta + 9 }
            this.CSSDefsCollapsed.sidebarEl = { right: this.basePos + this.offset - this.delta }
            this.CSSDefsCollapsed.contentEl = { right: this.basePos + this.offset + 9 }

            $(element).tooltip({ placement: 'left' })
            break
        }
        element.on('click', $.proxy(this.toggle, this))
      }

      SplitterView.prototype.toggle = function () {
        if (this.collapsed) {
          this.expand()
        } else {
          this.collapse()
        }
      }

      SplitterView.prototype.expand = function () {
        this.$sidebarEl.animate(this.CSSDefsExpanded.sidebarEl, 180)
        this.$contentEl.animate(this.CSSDefsExpanded.contentEl, 180, $.proxy(this.emitEvent, this))

        $(element).tooltip('hide')
        element.attr('data-original-title', i18n('na.general.minimize'))

        this.$sidebarEl.removeClass('fx-splitter-collapsed')
        this.collapsed = false
      }

      SplitterView.prototype.collapse = function () {
        this.$sidebarEl.animate(this.CSSDefsCollapsed.sidebarEl, 180)
        this.$contentEl.animate(this.CSSDefsCollapsed.contentEl, 180, $.proxy(this.emitEvent, this))

        $(element).tooltip('hide')
        element.attr('data-original-title', i18n('na.general.maximize'))

        this.$sidebarEl.addClass('fx-splitter-collapsed')
        this.collapsed = true
      }

      SplitterView.prototype.emitEvent = function () {
        $(element).trigger({
          type: 'toggled.splitter.fuxi',
          state: (this.collapsed ? 'collapsed' : 'expanded')
        })
      }

      let splitterView = null
      let toCollapse = false

      $timeout(function () {
        splitterView = new SplitterView(element.attr('data-splitter-sidebar'), element.attr('data-splitter-content'), element.attr('data-splitter-position'), false)
        if (toCollapse) {
          splitterView.collapse()
        }
      }, 0)

      scope.$on('Splitter::Collapse', function () {
        if (!splitterView) {
          toCollapse = true
        } else if (!splitterView.collapsed) {
          splitterView.collapse()
        }
      })

      scope.$on('Splitter::Expand', function () {
        if (splitterView && splitterView.collapsed) {
          splitterView.expand()
        }
      })
    }
  }
}
