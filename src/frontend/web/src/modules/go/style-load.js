/** @file This file is in a separate module to run first. This way, it loads the styles while hydrating JS */

import bootstrapStylesheet from '~base-styles/bootstrap.stylesheet'
import fuxiStylesheet from '~base-styles/fuxi.stylesheet'
import goStyleSheet from './stylesheet'
import datatablesStylesheet from '~base-styles/datatables.stylesheet'
import select2Stylesheet from '~base-styles/select2.stylesheet'
import bootstrapSwitch from '~base-styles/bootstrap-switch.stylesheet'

import { setAssetsBaseUrl } from '~utils/stylesheet-utils.util'

setAssetsBaseUrl(naportalBasemodulejsRoutes.na.naportalbase.controllers.Assets.versioned('na-portal-assets-vendors').url)
document.adoptedStyleSheets = [
  bootstrapStylesheet,
  bootstrapSwitch,
  datatablesStylesheet,
  select2Stylesheet,
  fuxiStylesheet,
  goStyleSheet
]
