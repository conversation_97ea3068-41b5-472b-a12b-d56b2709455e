import goModule from '../go.angular-module'
import { providers } from '../go-providers'

/**
 * @ngdoc directive
 * @name module.directive:templateLoader
 * @restrict A
 * @description
 * Directive that replaces itself with new content.
 * @element Div
 * @example
 * <div data-template-loader></div>
**/

goModule.directive(providers.commons.directives.TemplateLoader, [
  '$compile',
  function($compile) {
    return {
      restrict: 'A',
      priority: 1,
      terminal: false,
      scope: true,
      link: (scope, element) => {
        scope.$on('Common::Directive::LoadTemplate', (event, params) => {
          const content = $compile($.trim(params))(scope)
          scope.$destroy()
          element.empty()
          element.append(content)
        })
      }
    }
  }
])
