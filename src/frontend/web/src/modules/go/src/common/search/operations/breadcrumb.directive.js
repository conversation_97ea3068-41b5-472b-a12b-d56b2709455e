import goModule from '../../../go.angular-module'
import { updateBreadcrumb } from '../../breadcrumb.service'
import { providers } from '../../../go-providers'

/**
     * This directive is used when the user enters the front page of GO Module.
     * Its purpose is to set the initial breadcrumb in order to contextualize the user.
     * Note: If the user enters the module via nossis "links" the breadcrumb its ok, however if it navigates through the custom menu or hits F5/Ctrl+F5 the breadcrumb context disappears.
     */

goModule
  .directive(providers.operation.directives.Breadcrumb, [
    initialBreadcrumbDirective
  ])

function initialBreadcrumbDirective() {
  return {
    restrict: 'A',
    priority: 0,
    terminal: true,
    link: () => {
      updateBreadcrumb()
    }
  }
}
