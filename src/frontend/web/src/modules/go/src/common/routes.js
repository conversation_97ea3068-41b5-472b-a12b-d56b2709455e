/**
 * @typedef {Object} PlayRouteResult
 * @property {string} method
 * @property {string} url
 *
 */

/**
 @callback PlayRoute0
 @function
 @return {PlayRouteResult}
 */

/**
 @callback PlayRoute1
 @function
 @param arg1
 @return {PlayRouteResult}
 */

/**
 @callback PlayRoute2
 @function
 @param arg1
 @param arg2
 @return {PlayRouteResult}
 */

/**
 @callback PlayRoute3
 @function
 @param arg1
 @param arg2
 @param arg3
 @return {PlayRouteResult}
 */

/**
 @callback PlayRoute4
 @function
 @param arg1
 @param arg2
 @param arg3
 @param arg4
 @return {PlayRouteResult}
 */

/**
 @name gojsRoutes

 @property {PlayRoute0} na.go.controllers.Application.index
 @property {PlayRoute0} na.go.controllers.OperationsTable.getTableDataforOperations
 @property {PlayRoute0} na.go.controllers.OperationsTable.getAdminTableDataforOperations
 @property {PlayRoute1} na.go.controllers.Search.getFieldsForFiltername
 @property {PlayRoute0} na.go.controllers.Modals.getOperationSaveFilterModal
 @property {PlayRoute0} na.go.controllers.Modals.getTemplateSaveFilterModal
 @property {PlayRoute1} na.go.controllers.Modals.getEditFilterModal
 @property {PlayRoute1} na.go.controllers.Modals.editFilter
 @property {PlayRoute0} na.go.controllers.Modals.saveFilter
 @property {PlayRoute1} na.go.controllers.Modals.getDeleteFilterModal
 @property {PlayRoute1} na.go.controllers.Modals.deleteFilter
 @property {PlayRoute0} na.go.controllers.Operation.createOperationPage
 @property {PlayRoute1} na.go.controllers.Operation.cloneOperationPage
 @property {PlayRoute2} na.go.controllers.OperationDetails.getOperationDetails
 @property {PlayRoute0} na.go.controllers.Operation.getCharacteristicsTab
 @property {PlayRoute0} na.go.controllers.Operation.getOperationTab
 @property {PlayRoute0} na.go.controllers.Operation.createOperation
 @property {PlayRoute0} na.go.controllers.Catalog.getOperationTemplatesOfRfs
 @property {PlayRoute0} na.go.controllers.Catalog.getDistinctOperationTemplates
 @property {PlayRoute0} na.go.controllers.Catalog.getRfsOfFamily
 @property {PlayRoute0} na.go.controllers.Catalog.getDistinctRfsOfFamily
 @property {PlayRoute0} na.go.controllers.AdminOperation.index
 @property {PlayRoute0} na.go.controllers.AdminOperation.createTemplatePage
 @property {PlayRoute1} na.go.controllers.AdminOperation.addTemplatePage
 @property {PlayRoute1} na.go.controllers.AdminOperation.editTemplatePage
 @property {PlayRoute1} na.go.controllers.AdminOperation.getCharacteristicsTab
 @property {PlayRoute1} na.go.controllers.AdminOperation.getOperationTab
 @property {PlayRoute1} na.go.controllers.AdminOperation.createTemplate
 @property {PlayRoute1} na.go.controllers.AdminOperation.updateTemplate
 @property {PlayRoute4} na.go.controllers.AdminOperation.changeTemplateState
 @property {PlayRoute2} na.go.controllers.OperationDetails.getOperationDetails
 */

export const routes = global.gojsRoutes

export default routes
