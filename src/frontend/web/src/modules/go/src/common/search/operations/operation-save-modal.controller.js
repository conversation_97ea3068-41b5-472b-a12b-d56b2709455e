import goModule from '../../../go.angular-module'
import routes from '../../routes'
import { getTempOperationForm, setTempOperationForm } from '../gosearchservice'
import { i18n as goI18n } from '../../i18n.service'
import { request } from '~utils/proxy'
import OperationForm from './operation.model'
import { providers } from '../../../go-providers'
import { elementTagName } from '~nowc/components/date-time-picker/date-time-picker.element'

goModule
  .controller(providers.operation.controllers.saveModal, [
    '$scope',
    '$element',
    goOperationSaveModalController
  ])

function goOperationSaveModalController($scope, $element) {
  const allRfs = goI18n('rfs.selects.empty.option')
  const allOper = goI18n('operation.selects.empty.option')

  $scope.tempSearchFormItems = {}
  $scope.$on('$destroy', () => {
    const tempFormModel = new OperationForm($scope.tempSearchFormItems)
    setTempOperationForm(tempFormModel)
  })
  const tempForm = getTempOperationForm()
  const goModelForm = angular.copy(tempForm)
  $scope.tempSearchFormItems = goModelForm

  function generateRfsList(selectedChoice) {
    const items = goModelForm
    const requestData = {
      context: 'operation'
    }
    if (items.family) { requestData.family = items.family }
    const route = routes.na.go.controllers.Catalog.getDistinctRfsOfFamily()
    request({
      route,
      data: requestData,
      onSuccess: data => {
        const selectData = data.map(rfs => ({ id: rfs.name, text: rfs.name }))

        $scope.$broadcast('SelectBox::LoadData', {
          id: 'modal-selectRfsNames',
          data: selectData,
          selectedChoice
        })
      }
    })
  }

  function generateOperationList(selectedChoice) {
    const items = goModelForm
    const requestData = {
      context: 'operation'
    }
    if (items.family) { requestData.family = items.family }
    if (items.rfs) { requestData.rfs = items.rfs }
    const route = routes.na.go.controllers.Catalog.getDistinctOperationTemplates()
    request({
      route,
      data: requestData,
      onSuccess: data => {
        const selectData = data.map(operationTemplate => ({ id: operationTemplate.name, text: operationTemplate.name }))

        $scope.$broadcast('SelectBox::LoadData', {
          id: 'modal-operation',
          data: selectData,
          selectedChoice
        })
      }
    })
  }

  generateRfsList(goModelForm.rfs)
  generateOperationList(goModelForm.operationName)

  $scope.$on('SelectBox::SelectedOption', (event, params) => {
    if (params.context === 'family') {
      goModelForm.family = (params.value && params.value !== '') ? params.value : undefined
      goModelForm.rfs = undefined
      goModelForm.operationName = undefined
      generateRfsList($scope.tempSearchFormItems.rfs)
      generateOperationList()
    } else if (params.context === 'rfs') {
      goModelForm.rfs = (params.value && params.value !== allRfs) ? params.value : undefined
      goModelForm.operationName = undefined
      generateOperationList()
    } else if (params.context === 'operation') {
      goModelForm.operationName = (params.value && params.value !== allOper) ? params.value : undefined
    }
  })

  $scope.$watch(() => $scope.tempSearchFormItems.startDate, (newValue) => {
    $element.find('[data-field="startDate"]').val(newValue)
  })

  $scope.$watch(() => $scope.tempSearchFormItems.endDate, (newValue) => {
    $element.find('[data-field="endDate"]').val(newValue)
  })

  $element.on('change input', elementTagName, event => {
    const target = $(event.target)
    const dataField = target[0].getAttribute('data-field')

    $scope.tempSearchFormItems[dataField] = target[0].value
  })
}
