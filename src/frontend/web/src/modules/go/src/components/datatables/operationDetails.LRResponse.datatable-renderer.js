import { save<PERSON><PERSON><PERSON> } from '~components/naDatatables-v2/datatable-render-store'

import { htmlEscape } from '~utils/string.util'
import { link } from '~nowc/components/table/table-render-utils'
import { i18n } from '~utils/i18n'

function columnStateRenderer(context) {
  const { rowData, i18n } = context
  function lrStateIcon(type, title) {
    let className = 'fuxicons '
    switch (type) {
      case 'success': className += 'fuxicons-success green'; break
      case 'error': className += 'fuxicons-error red'; break
      case 'warning': className += 'fuxicons-warning yellow'; break
    }
    return '<i class="' + htmlEscape(className) + '" data-original-title="" title="" data-toggle="popover" data-placement="right" data-enable-popover data-trigger="focus" data-content="' + htmlEscape(title) + '"></i>'
  }

  switch (rowData['description.configState']) {
    case 'EXECUTED': return lrStateIcon('success', i18n('operation.details.state.executed'))
    case 'NOT_EXECUTED': return lrStateIcon('warning', i18n('operation.details.state.not_executed'))
    case 'SKIP': return lrStateIcon('warning', i18n('operation.details.state.skip'))
    case 'ERROR': // jshint ignore:line
    default: return lrStateIcon('error', i18n('operation.details.state.error'))
  }
}

function truncatedColumn(textContent) {
  return textContent ? '<span class="fx-truncate" title="' + htmlEscape(textContent) + '">' + htmlEscape(textContent) + '</span>' : '--'
}

function columnResponseCodeRenderer(context) {
  const value = context.rowData['response.code']
  return value ? htmlEscape(value) : '--'
}

function columnResponseValueRenderer(context) {
  return truncatedColumn(context.rowData['response.value'])
}

function columnResponseMessageRenderer(context) {
  return truncatedColumn(context.cellData)
}

function columnDateHourRenderer(context) {
  const value = context.rowData['description.time']
  return value ? htmlEscape(value) : '--'
}

function operationDetailsActionsRenderer(context) {
  const { rowData } = context
  return '<span class="fx-table-actions">' +
    link({
      icon: 'fa fa-file-code',
      action: 'logdata',
      title: i18n('datatables.action.label.logdata'),
      visible: rowData !== false && (rowData.log != null || rowData.data != null),
      context
    }) +
    '</span>'
}

saveRenderer({ key: 'na.datatableTemplates.go.operationDetails.columnStateRenderer', renderer: columnStateRenderer })
saveRenderer({ key: 'na.datatableTemplates.go.operationDetails.columnResponseCodeRenderer', renderer: columnResponseCodeRenderer })
saveRenderer({ key: 'na.datatableTemplates.go.operationDetails.columnResponseValueRenderer', renderer: columnResponseValueRenderer })
saveRenderer({ key: 'na.datatableTemplates.go.operationDetails.columnResponseMessageRenderer', renderer: columnResponseMessageRenderer })
saveRenderer({ key: 'na.datatableTemplates.go.operationDetails.columnDateHourRenderer', renderer: columnDateHourRenderer })
saveRenderer({ key: 'na.datatableTemplates.go.operationDetails.operationDetailsActionsRenderer', renderer: operationDetailsActionsRenderer })
