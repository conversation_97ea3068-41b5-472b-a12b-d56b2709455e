import { store } from '../operationWizard/operation-store.store'
import goModule from '../../go.angular-module'
import { providers } from '../../go-providers'

goModule
  .directive(providers.operation.directives.CloneOperation, [
    cloneOperationDirective
  ])

function cloneOperationDirective() {
  return {
    restrict: 'A',
    controller: ['$scope', '$element', '$attrs',
      ($scope, $element, attrs) => {
        const ctrl = $scope.createCtrl
        const cloneinfo = JSON.parse(attrs.naPortalGoCloneOperation)

        ctrl.characteristics.data.family = cloneinfo.family || null
        ctrl.characteristics.data.rfs = cloneinfo.rfs || null
        ctrl.characteristics.data.operation = cloneinfo.operationName || null

        store.setters.updateCharacteristicsValues(ctrl.characteristics.data)

        const listener = store.reflect(({ getters }) => getters.fieldsType, (fieldsType) => {
          if (cloneinfo.attributes) {
            const attributes = cloneinfo.attributes.reduce((attributeMap, attribute) => {
              if (fieldsType[attribute.name] !== 'FileField') {
                attributeMap[attribute.name] = attribute.value
              }
              return attributeMap
            }, {})
            requestAnimationFrame(() => store.setters.setAttributes(attributes))
          }
          listener.unregister()
        })

        $scope.$on('SelectBox::updated', (_, params) => {
          if (params.element.attr('id') === 'operation') {
            $scope.$broadcast('SelectBox::Disable', { id: 'family-field' })
            $scope.$broadcast('SelectBox::Disable', { id: 'selectRfsNames' })
            $scope.$broadcast('SelectBox::Disable', { id: 'operation' })
            $element.children()[0].goToStepNumber(2)
          }
        })
      }]
  }
}
