class OperationModel {
  constructor({
    id,
    family,
    operationName,
    rfs,
    responseURL,
    request,
    response,
    orderKey,
    username,
    partitionKey,
    sInsertionDate,
    loginOrigin,
    description,
    status
  }) {
    this.id = id
    this.family = family
    this.operationName = operationName
    this.rfs = rfs
    this.responseURL = responseURL
    this.request = request
    this.response = response
    this.orderKey = orderKey
    this.username = username
    this.partitionKey = partitionKey
    this.sInsertionDate = sInsertionDate
    this.loginOrigin = loginOrigin
    this.description = description
    this.status = status
  }

  static buildEmptyResponse() {
    return {
      payload: '-',
      messageType: 1,
      retries: '-',
      sInsertionDate: '-',
      description: '-',
      status: '-',
    }
  }

  static buildEmptyRequest() {
    return {
      payload: '-',
      messageType: 0,
      retries: '-',
      sInsertionDate: '-',
      description: '-',
      status: '-',
    }
  }

  static buildDetails(details) {
    return {
      payload: details.payload,
      parsedPayload: details.parsedPayload,
      messageType: details.messageType,
      retries: details.retries,
      sInsertionDate: details.sInsertionDate,
      description: details.description,
      status: details.status,
    }
  }

  getRequestIdentifier() {
    return `${this.orderKey}_${this.request.partitionKeyDetails}`
  }

  getResponseIdentifier() {
    return `${this.orderKey}_${this.response.partitionKeyDetails}`
  }

  static build(data) {
    const { details } = data
    let requestObj = {}
    let responseObj = {}
    let request, response

    if (details.length === 0) {
      requestObj = this.buildEmptyRequest()
      responseObj = this.buildEmptyResponse()
    } else if (details.length === 1) {
      [request] = details
      requestObj = this.buildDetails(request)
      responseObj = this.buildEmptyResponse()
    } else if (details.length > 1) {
      if (details[0].messageType === 0) {
        [request, response] = details
      } else {
        [response, request] = details
      }

      requestObj = this.buildDetails(request)
      responseObj = this.buildDetails(response)
    }

    return new OperationModel({
      id: data.id ?? '',
      sInsertionDate: data.sInsertionDate ?? '',
      family: data.family ?? '',
      operationName: data.operationName ?? '',
      rfs: data.rfs ?? '',
      responseURL: data.responseURL ?? '',
      orderKey: data.orderKey ?? '',
      username: data.username ?? '',
      partitionKey: data.partitionKey ?? '',
      request: OperationModel.buildDetails(requestObj),
      response: OperationModel.buildDetails(responseObj),
      description: data.description ?? '',
      loginOrigin: data.loginOrigin ?? ''
    })
  }
}

export default OperationModel
