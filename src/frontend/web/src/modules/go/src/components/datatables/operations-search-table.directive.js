import goModule from '../../go.angular-module'
import { TabContexts } from '../../common/constants/goconstants'
import routes from '../../common/routes'
import OperationForm from '../../common/search/operations/operation.model'
import * as goSearchService from '../../common/search/gosearchservice'
import { navigateTo } from '~utils/navigation'
import operationSearchPageStore from '../../common/search/operations/operations-search-form.store'
import { providers } from '../../go-providers'
import { providerName } from '~components/naDatatables-v2/datatable.directive'

goModule
  .directive(providers.operation.directives.OperationsSearchTable, [
    OperationsSearchTableDirective
  ])

function OperationsSearchTableDirective() {
  return {
    restrict: 'A',
    priority: 9,
    require: providerName,
    terminal: false,
    link: async function (scope, element, attrs, ctrl) {
      ctrl.paramsPost = () => {
        return new OperationForm(goSearchService.getOperationForm()).dataToSubmit()
      }

      ctrl.addConfigReducer(config => {
        const savedPageLength = operationSearchPageStore.getters.tablePageLength
        const savedPageNumber = operationSearchPageStore.getters.tablePageNumber
        const pagination = { ...config.pagination }

        const adaptedColumns = config.columns.map((column) => {
          const changes = {}
          if (column.visibilityToggle) {
            const savedColumnVisibility = operationSearchPageStore.getters.tableColumnsVisibility[column.name]
            if (savedColumnVisibility != null && column.visible !== savedColumnVisibility) {
              changes.visible = savedColumnVisibility
            }
          }

          return { ...column, ...changes }
        })

        if (savedPageLength > 0 &&
                        config.pagination.enabled &&
                        config.pagination.pageLengthMenu.enabled &&
                        config.pagination.pageLengthMenu.options.indexOf(savedPageLength) >= 0
        ) {
          pagination.pageLength = savedPageLength
        }

        if (savedPageNumber > 0 &&
                        config.pagination.enabled &&
                        config.pagination.pageLength > 0
        ) {
          pagination.pageNumber = savedPageNumber
        }

        return {
          ...config,
          columns: adaptedColumns,
          pagination
        }
      })

      const { store, table } = await ctrl.getStoreInfo()

      function rowInfoOfAction(actionElement) {
        const rowNumber = parseInt(actionElement.attr('data-row'))
        const rowData = store.getters.rowsData[table][rowNumber]
        const operationID = rowData.id
        return { rowNumber, rowData, operationID }
      }

      element.on('click', '[data-action="clone-operation"]', function() {
        const { operationID } = rowInfoOfAction($(this))
        navigateTo({ url: routes.na.go.controllers.Operation.cloneOperationPage(operationID).url })
      })

      element.on('click', '[data-action="view-operation"]', function() {
        const { operationID } = rowInfoOfAction($(this))
        navigateTo({ url: routes.na.go.controllers.OperationDetails.getOperationDetails(operationID, TabContexts.characteristics).url })
      })

      ctrl.addDrawHandler(async () => {
        const { pageLength, pageNumber } = await ctrl.getPageInfo()
        operationSearchPageStore.setters.updateTableState({ pageLength, pageNumber })
      })

      store.reflect(
        snapshot => snapshot.getters.visibleColumnNames,
        (newValue, oldValue, snapshot) => {
          const columnVisibility = snapshot.getters.columns[table].reduce((acc, column) => {
            return Object.assign(acc, {
              [column.name]: column.visible
            })
          }, {})
          operationSearchPageStore.setters.updateColumnVisibility(columnVisibility)
        }
      ).until(({ getters }) => !getters.definedTableSet.has(table))

      const reloadListener = operationSearchPageStore.reflect(
        snapshot => snapshot.getters.formFields,
        () => ctrl.reloadTable({ resetPagination: true })
      )

      scope.$on('$destroy', reloadListener.unregister)
    }
  }
}
