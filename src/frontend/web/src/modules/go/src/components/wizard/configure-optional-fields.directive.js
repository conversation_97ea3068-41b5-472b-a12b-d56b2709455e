import { i18n } from '~utils/i18n'
import { store } from '../operationWizard/operation-store.store'
import { optionalFields } from './configure-optional-fields'
import goModule from '../../go.angular-module'
import { providers } from '../../go-providers'

goModule
  .directive(providers.operation.directives.ConfigureOptionalFields, [
    configureOptionalFields
  ])

function configureOptionalFields() {
  return {
    restrict: 'A',
    link: ($scope, element) => {
      optionalFields(element, store, i18n)
    }
  }
}
