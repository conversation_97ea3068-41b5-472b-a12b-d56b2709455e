import goModule from '~go/src/go.angular-module'
import { providers } from '~go/src/go-providers'
import { providerName as datatableDirective } from '~components/naDatatables-v2/datatable.directive'

goModule
  .directive(providers.operation.directives.LrDatatable, [
    goLrDatatable
  ])

function goLrDatatable () {
  return {
    restrict: 'A',
    require: datatableDirective,
    link: function (scope, element, attrs, ctrl) {
      const initialData = scope.operationDetailsLRTableData[0] || []
      ctrl.addReloadMiddleware({
        priority: 2000,
        callback (tableQuery, next) {
          next({
            data: initialData,
            recordsTotal: initialData.length,
            recordsFiltered: initialData.length
          })
        }
      })

      const collapsedGroups = {}

      function groupRowRenderer(rows, group, level) {
        const collapsed = !!collapsedGroups[group]
        rows
          .nodes()
          .toArray()
          .forEach(rowElement => {
            rowElement.classList.add('group-item', 'group-item-' + group.replace(/ /g, '-'))
            rowElement.setAttribute('data-group', group)
            rowElement.style.display = collapsed ? 'none' : ''
          })

        const caretClass = collapsed ? 'collapsed-group' : 'expanded-group'
        return $(`<tr class="dtrg-start dtrg-level-0"></tr><td class="group ${group} ${caretClass} group-item-expander" colSpan="8" data-group="${group}"
    data-group-level="${level}">${group}</td></tr>`)
      }

      ctrl.addConfigReducer(function (config) {
        return {
          ...config,
          rowGroup: {
            ...config.rowGroup,
            startRender: groupRowRenderer
          }
        }
      })

      element.on('click', '.group-item-expander', function() {
        const $el = $(this)
        const group = $el.data('group')
        collapsedGroups[group] = !collapsedGroups[group]
        ctrl.reloadTable({ resetPagination: false })
      })

      element.on('click', '[data-action="logdata"]', function() {
        const $el = $(this)
        const rowIndex = $el.data('row')
        const data = initialData[rowIndex]
        scope.showDetails(data)
      })
    }
  }
}
