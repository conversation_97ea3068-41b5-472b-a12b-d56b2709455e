import { store } from '../../operationWizard/operation-store.store'
import { htmlEscape } from '~utils/string.util'
import goModule from '../../../go.angular-module'
import { providers } from '../../../go-providers'
import { i18n } from '~utils/i18n'

goModule
  .directive(providers.commons.directives.DynamicForm, [
    '$compile',
    naDynamicFormDirective
  ])

function naDynamicFormDirective($compile) {
  return {
    restrict: 'A',
    priority: 1000,
    scope: true,
    compile: () => ({
      pre: (scope, formElement, iAttrs) => {
        const fieldsMap = {}
        const mappings = readMappings()

        const listener = store.listenStateChange((newStateStore, oldStateStore) => {
          if (newStateStore.getters.visibleFields !== oldStateStore.getters.visibleFields) {
            updateFieldsVisibility()
          }
        })

        scope.$on('$destroy', () => {
          listener.unregister()
        })

        if (mappings !== null) {
          store.setters.setMappings(mappings)

          appendFormComponents(formElement, mappings)

          formElement.find('.form-group').each(function () {
            const name = this.getAttribute('data-form-field')
            fieldsMap[name] = $(this)
          })

          updateFieldsVisibility()

          scope.$on('NA::Field::Validate', () => {
            validateForm(formElement)
          })
        }

        function updateFieldsVisibility() {
          const visibleFieldsMap = store.getters.visibleFields.reduce((acc, attr) => {
            acc[attr] = true
            return acc
          }, {})
          Object.keys(fieldsMap).forEach(mapping => {
            const isVisible = visibleFieldsMap[mapping] || false
            fieldsMap[mapping].toggleClass('hidden', !isVisible)
          })
        }

        function readMappings() {
          if (iAttrs.naPortalGoDynamicFormMappings) {
            const mappings = JSON.parse(iAttrs.naPortalGoDynamicFormMappings)
            mappings.forEach(mapping => {
              mapping.component.name = mapping.templateMapping
            })
            return mappings
          }
          return null
        }

        /**
         * Append components to form.
         */
        function appendFormComponents(formElement, mappings) {
          const requiredMappings = mappings.filter(mapping => mapping.component.required === true)

          requiredMappings.forEach(mapping => {
            formElement.append(buildComponent(mapping))
          })

          const optionalMappings = mappings.filter(mapping => mapping.component.required === false)

          if (optionalMappings.length > 0) {
            optionalMappings.forEach(mapping => {
              formElement.append(buildComponent(mapping))
            })
          }
        }

        /**
         * Build mapping component
         */
        function buildComponent(mapping) {
          const componentScope = buildComponentScope(mapping)
          return $compile(createMapping(mapping))(componentScope)
        }

        /**
         * Build a new scope with component data and model
         */
        function buildComponentScope(mapping) {
          const name = mapping.templateMapping
          const componentScope = scope.$new(true)
          componentScope.model = ComponentModel(name)
          componentScope.store = store
          componentScope.component = mapping.component
          return componentScope
        }

        /**
         * Model wrapper
         */
        function ComponentModel(name) {
          return {
            set: value => {
              if (store.getters.attributes[name] != null) {
                store.setters.setAttribute(name, value)
              }
            },
            get: () => store.getters.attributes[name],
            onChange: callback => {
              const listener = store.listenStateChange((newStateStore, oldStateStore) => {
                if (newStateStore.getters.attributes[name] !== oldStateStore.getters.attributes[name]) {
                  callback()
                }
              })
              scope.$on('$destroy', () => {
                listener.unregister()
              })
            }

          }
        }

        /**
         * Trigger validation on all input components
         */
        function validateForm(element) {
          element.find('.go-operation-mapping').trigger('NA::Field::Validate')
        }

        function setInitializedAttributeIfAllFieldsInitialized() {
          const areAllFieldsInitialized = formElement.find('.go-operation-mapping')
            .toArray()
            .every(fieldElement => $(fieldElement).find(':input').length > 0)

          if (areAllFieldsInitialized) {
            formElement.attr('initialized', '')
          } else {
            requestAnimationFrame(setInitializedAttributeIfAllFieldsInitialized)
          }
        }

        setInitializedAttributeIfAllFieldsInitialized()
      }
    })
  }
}

function createMapping(mapping) {
  let mappingField = '<div></div>'
  if (mapping.component['@class'] === 'pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.components.TextField') {
    mappingField = '<div class="col-sm-10 go-operation-mapping" data-na-portal-go-dynamic-form-text-field></div>'
  } else if (mapping.component['@class'] === 'pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.components.TextArea') {
    mappingField = '<div class="col-sm-10 go-operation-mapping" data-na-portal-go-dynamic-form-text-area>'
  } else if (mapping.component['@class'] === 'pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.components.SelectBox') {
    mappingField = '<div class="col-sm-10 go-operation-mapping" data-na-portal-go-dynamic-form-select-box></div>'
  } else if (mapping.component['@class'] === 'pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.components.CheckList') {
    mappingField = '<div class="col-sm-10 go-operation-mapping" data-na-portal-go-dynamic-form-check-list></div>'
  } else if (mapping.component['@class'] === 'pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.components.RadioButton') {
    mappingField = '<div class="col-sm-10 go-operation-mapping" data-na-portal-go-dynamic-form-radio-button></div>'
  } else if (mapping.component['@class'] === 'pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.components.FileField') {
    mappingField = '<div class="col-sm-10 go-operation-mapping" data-na-portal-go-dynamic-form-upload-button></div>'
  }

  const requiredClass = mapping.component.required === true ? 'fx-required' : ''
  const includeRequiredAbbr = (isRequired) => isRequired ? `<abbr title="${i18n('na.basemodule.validations.mandatory')}"></abbr>` : ''

  const html = []
  html.push('<div class="form-group" data-form-field="' + htmlEscape(mapping.templateMapping) + '">')
  html.push(`<label class="col-sm-2 control-label ${htmlEscape(requiredClass)}" title="${htmlEscape(mapping.label)}">`)
  html.push(`${htmlEscape(mapping.label)}${includeRequiredAbbr(mapping.component.required)}`)
  html.push('</label>')
  html.push(mappingField)
  html.push('</div>')
  return html.join('')
}
