import goModule from '../../go.angular-module'
import { providers } from '../../go-providers'
import { i18n } from '~utils/i18n'

/**
     * @ngdoc directive
     * @name required
     * <AUTHOR>
     *
     * @description
     * Handles required fields.
     *
     * @requires fieldNotification directive
     * @requires id attribute
     *
     * @attribute requiredNotAcceptedValue defines a new value that triggers the error
     *
     * @example
     <div data-na-portal-go-validation-required-field>
     <label>The label</label>
     <input id="theInput" required>
     </div>
     */

goModule.directive(providers.components.validation.directives.RequiredField, ['$timeout', function($timeout) {
  const validate = function(notificationCtr, viewValue, id, newValidationValue) {
    let invalid = false
    if (viewValue == null || viewValue === newValidationValue || viewValue.length === 0) {
      invalid = true
    }

    notificationCtr.setMessageVisible(false)
    notificationCtr.setMsg('')
    if (invalid) {
      notificationCtr.setState(true, 'error')
    } else {
      notificationCtr.setState(false)
    }
    return invalid
  }

  const link = function(scope, element, attrs, ctrls) {
    const controllers = {}
    controllers.notification = ctrls[0]

    $timeout(function() {
      const label = controllers.notification.getElement().find('label')
      label.addClass('fx-required')
      label.append('<abbr title="' + i18n('na.basemodule.validations.mandatory') + '"></abbr>')

      $(element).on('NA::Field::Validate::Required', function(event, params) {
        const isValid = validate(controllers.notification, params.value, params.id, element.attr('data-required-not-accepted-value'))
        $(element).toggleClass('required-field', isValid)
        scope.$applyAsync()
      })
    })
  }

  return {
    require: ['^' + providers.components.validation.directives.FieldNotification],
    restrict: 'A',
    priority: 100,
    link,
    scope: true
  }
}])
