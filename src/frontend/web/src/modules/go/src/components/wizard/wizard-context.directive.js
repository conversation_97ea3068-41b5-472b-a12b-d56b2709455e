import * as TemplateService from '../../admin/wizard/template.service'
import goModule from '../../go.angular-module'
import { providers } from '../../go-providers'

goModule
  .directive(providers.commons.directives.WizardContext, [
    wizardContextDirective
  ])

function wizardContextDirective() {
  return {
    restrict: 'A',
    compile: () => ({
      pre: (scope, element, attrs) => {
        TemplateService.setContextMode(attrs.naPortalGoWizardContext)
      }
    })
  }
}
