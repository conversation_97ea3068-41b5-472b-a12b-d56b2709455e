import goModule from '../../go.angular-module'
import { updateBreadcrumb, labels } from '../../common/breadcrumb.service'
import { providers } from '../../go-providers'

goModule
  .directive(providers.template.directives.CreateTemplateBreadcrumb, [
    adminCreateTemplateBreadcrumbDirective
  ])

function adminCreateTemplateBreadcrumbDirective() {
  return {
    restrict: 'A',
    priority: 0,
    terminal: true,
    link: () => {
      updateBreadcrumb([
        labels.template.home(),
        labels.template.create()
      ])
    }
  }
}
