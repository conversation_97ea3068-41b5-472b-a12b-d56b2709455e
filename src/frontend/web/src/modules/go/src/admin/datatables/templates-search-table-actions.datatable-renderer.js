import { htmlEscape } from '~utils/string.util'
import { save<PERSON><PERSON><PERSON> } from '~components/naDatatables-v2/datatable-render-store'

function dataAttributes(parameters) {
  const dataAttr = Object.keys(parameters).reduce((acc, key) => {
    const val = parameters[key]
    if (key.indexOf('data-') === 0 && val !== '' && val != null) {
      acc[key] = val.toString()
    }
    return acc
  }, {})
  if (dataAttr.length <= 0) {
    return ''
  }
  return Object.keys(dataAttr).map(key => key + '="' + (dataAttr[key].replace('"', '\"')) + '"').join(' ')
}

function link(parameters) {
  const { icon, action, title, context, enabled: isEnabled } = parameters
  if (parameters.visible === false) {
    return ''
  }
  const disabledAttribute = (isEnabled === false) ? 'disabled' : ''
  return `<button ${dataAttributes(parameters)}
          data-row="${htmlEscape(context.rowNumber)}"
          title="${htmlEscape(title)} "
          data-action="${htmlEscape(action)}" 
          class="btn fx-btn-action btn-link-in-table" 
          ${disabledAttribute}>
            <i class="${htmlEscape(icon)}"></i>
      </button>`
}

function actionRenderer(context) {
  const { i18n, rowData } = context
  return '<span class="fx-table-actions">' +
            link({
              icon: 'glyphicon glyphicon-pencil',
              action: 'edit-template',
              title: i18n('na.portal.go.datatables.action.label.edit'),
              enabled: rowData.editPermission,
              'data-id': rowData.id,
              context
            }) +
            link({
              icon: 'glyphicon glyphicon-plus',
              action: 'add-template',
              title: i18n('na.portal.go.datatables.action.label.add'),
              enabled: rowData.addPermission,
              'data-id': rowData.id,
              context
            }) +
            link({
              icon: 'glyphicon glyphicon-remove',
              action: 'remove-template',
              title: i18n('na.portal.go.datatables.action.label.remove'),
              enabled: rowData.removePermission,
              'data-id': rowData.id,
              context
            }) +
            '</span>'
}

saveRenderer({ key: 'na.datatableTemplates.go.templateSearch.actionRenderer', renderer: actionRenderer })
