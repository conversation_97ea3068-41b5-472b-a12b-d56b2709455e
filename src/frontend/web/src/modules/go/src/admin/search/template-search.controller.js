import goModule from '../../go.angular-module'
import { htmlEscape } from '~utils/string.util'
import { i18n as goI18n } from '../../common/i18n.service'
import { i18n } from '~utils/i18n'
import * as goSearchService from '../../common/search/gosearchservice'
import { routes } from '../../common/routes'
import OperationForm from '../../common/search/operations/operation.model'
import { filtersOperations as filterOperations } from '~components/searchform/search.service'
import { SearchContexts } from '../../common/constants/goconstants'
import { navigateTo } from '~utils/navigation'
import { request } from '~utils/proxy'
import templateSearchPageStore from './template-search-form.store'
import { showModal } from '~utils/modal'
import { notifyError, notifySuccess } from '~utils/toast-notification'
import { providers } from '../../go-providers'

goModule
  .controller(providers.template.controllers.Search, [
    '$scope',
    '$element',
    '$rootScope',
    '$compile',
    goSearchAdminOperationController
  ])

function goSearchAdminOperationController($scope, $element, $rootScope, $compile) {
  const ctrl = this // jshint ignore: line

  $scope.searchFormItems = goSearchService.getAdminTemplateForm()

  ctrl.regexDateValidation = ''
  ctrl.validDateSyntax = true
  ctrl.searchContext = goSearchService.getSearchContext()

  $scope.$watch(function() { return goSearchService.getSearchContext() }, function(newValue) {
    if (typeof newValue !== 'undefined') { ctrl.searchContext = newValue }
  })

  /** ********************************************** SEARCH *****************************************************************/

  $scope.$on('GO::OperationSearch::Controller::newOperationPage', function() {
    const operationDetailsUrl = routes.na.go.controllers.AdminOperation.createTemplatePage().url
    navigateTo({ url: operationDetailsUrl })
  })
  const allRfs = goI18n('rfs.selects.empty.option')

  function isRfsComboSelected() {
    return $scope.searchFormItems.rfs && $scope.searchFormItems.rfs != allRfs
  }

  function generateRfsList(selectedChoice) {
    const items = $scope.searchFormItems
    const requestData = {
      context: 'template'
    }
    if (items.family) { requestData.family = items.family }
    const route = routes.na.go.controllers.Catalog.getDistinctRfsOfFamily()
    request({
      route,
      data: requestData,
      onSuccess: data => {
        const selectData = data.map(rfs => ({ id: rfs.name, text: rfs.name }))

        $scope.$broadcast('SelectBox::LoadData', {
          id: 'selectRfsNames',
          data: selectData,
          selectedChoice
        })
      }
    })
  }

  function generateOperationList(selectedChoice) {
    const items = $scope.searchFormItems
    const requestData = {
      context: 'template'
    }
    if (items.family) {
      requestData.family = items.family
    }
    if (isRfsComboSelected()) {
      requestData.rfs = items.rfs
    }
    const route = routes.na.go.controllers.Catalog.getDistinctOperationTemplates()
    request({
      route,
      data: requestData,
      onSuccess: data => {
        const selectData = data.map(operationTemplate => ({ id: operationTemplate.name, text: operationTemplate.name }))

        $scope.$broadcast('SelectBox::LoadData', {
          id: 'operation',
          data: selectData,
          selectedChoice
        })
      }
    })
  }

  generateRfsList($scope.searchFormItems.rfs)
  generateOperationList($scope.searchFormItems.operationName)

  function updateSearchFormItems(newItems) {
    $scope.searchFormItems = new OperationForm(newItems)
    $scope.$broadcast('SelectBox::SetValue', { id: 'family-field', value: newItems.family })
    $scope.$broadcast('SelectBox::SetValue', { id: 'executionState', value: newItems.executionState })
    generateRfsList(newItems.rfs)
    generateOperationList(newItems.operationName)

    $element.find('[data-field="startDate"]').val(newItems.startDate)
    $element.find('[data-field="endDate"]').val(newItems.endDate)
  }

  $scope.$on('SelectBox::SelectedOption', (event, params) => {
    if (params.context === 'filters') {
      const selectedFilter = params.value
      const route = routes.na.go.controllers.Search.getFieldsForFiltername(selectedFilter)
      let tempFormData = {}

      request({
        route,
        data: { context: SearchContexts.template },
        onSuccess: filterData => {
          tempFormData = OperationForm.buildFromFilter(filterData, selectedFilter)
          updateSearchFormItems(tempFormData)
        }
      })
    } else if (params.context === 'family') {
      $scope.searchFormItems.family = params.value ? params.value : undefined
      $scope.searchFormItems.rfs = undefined
      $scope.searchFormItems.operationName = undefined
      generateOperationList()
      generateRfsList()
    } else if (params.context === 'rfs') {
      $scope.searchFormItems.rfs = params.value ? params.value : undefined
      $scope.searchFormItems.operationName = undefined
      generateOperationList()
    } else if (params.context === 'operation') {
      $scope.searchFormItems.operationName = params.value ? params.value : undefined
    }
  })

  $scope.$on('operationSearch::Controller::FillForm', function(event, params) {
    if (params instanceof OperationForm) {
      const operationsModelForm = angular.copy(params)
      $scope.$emit('Common::Controller::Broadcast', { event: 'CustomDatatables::Controller::SetFormToSubmit', payload: operationsModelForm })
      $scope.searchFormItems = params
    } else {
      updateSearchFormItems(params)
    }
  })

  ctrl.clearSearchFormItems = function () {
    $scope.searchFormItems = {}
    $scope.$emit('Common::Controller::Broadcast', { event: 'SelectBox::cleanAll', payload: {} })

    goSearchService.initializeAdminModelForm({})
    $scope.searchFormItems = goSearchService.getAdminTemplateForm()
  }

  $scope.$on('operationSearch::Controller::SubmitForm', function() {
    ctrl.submitSearchForm()
  })

  ctrl.submitSearchForm = function() {
    const form = $scope.searchFormItems
    const result = goSearchService.validateDates(form.startDate, form.endDate, {
      startDateRequired: false,
      endDateRequired: false
    })
    if (result) {
      goSearchService.setAdminTemplateForm(form)
      templateSearchPageStore.setters.updateFormFields(Object.keys(form).reduce((acc, key) => {
        return form[key] == null ? acc : Object.assign(acc, { [key]: form[key] })
      }, {}))

      $scope.$emit('Common::Controller::Broadcast', { event: 'CustomDatatables::Controller::ReloadTable', payload: form })
      // Note: delay the following code until reload callback has finished in order to avoid showing columns before data is ready
      const eventName = 'Datatables::Controller::RequestComplete'
      $scope.$$listeners[eventName] = null

      $scope.$on(eventName, () => {
        $scope.$emit('Common::Controller::Broadcast', { event: 'Toolbar::Directive::UpdateTree', payload: {} })
      })
    }
  }

  /** ****************************************** MODALS *********************************************************/
  /** ********* SAVE FILTER MODAL *********/
  ctrl.openSaveFilterModal = function() {
    const route = routes.na.go.controllers.Modals.getTemplateSaveFilterModal()

    request({
      route,
      data: {},
      onSuccess: data => {
        const modalScope = $rootScope.$new(true) // New isolated scope for modal. Its important to create it from the rootscope since its a fresh copy!

        const buttonOk = {
          label: i18n('na.modal.button.savefilter'),
          callback: () => ctrl.callbackconfirm(modalScope),
          dismiss: true
        }
        const buttonCancel = {
          label: i18n('na.modal.button.cancel'),
          callback: () => ctrl.callbackcancel(modalScope),
          dismiss: true
        }

        const tempForm = angular.copy($scope.searchFormItems)
        const tempFormModel = new OperationForm({...tempForm, filterName: ""})
        goSearchService.setTempOperationForm(tempFormModel)

        const content = $compile($.trim(data))(modalScope)

        showModal({
          title: i18n('na.modal.filters.title.savefilter'),
          body: content,
          modalSize: 'medium',
          buttonOk,
          buttonCancel
        })
      }
    })
  }

  ctrl.callbackconfirm = function(modalScope) {
    // destroy the modal scope, in there we have a listener for $destroy event that will save the tempForm on the service
    modalScope.$destroy()

    const rfsModalTempForm = goSearchService.getTempOperationForm()
    if (!rfsModalTempForm.filterName) {
      notifyError({
        title: i18n('na.modals.filters.error.save'),
        message: i18n('na.modals.filters.error.missingfiltername'),
        timeOut: 5000
      })
    } else {
      const result = goSearchService.validateDates(rfsModalTempForm.startDate, rfsModalTempForm.endDate, {
        context: filterOperations.save,
        startDateRequired: false,
        endDateRequired: false
      })
      if (result) {
        const route = routes.na.go.controllers.Modals.saveFilter()

        request({
          route,
          data: { form: rfsModalTempForm.dataToSubmit(), context: SearchContexts.template },
          onSuccess: () => {
            updateSearchFormItems(rfsModalTempForm)
            notifySuccess({
              title: i18n('na.modals.filters.success.save'),
              message: htmlEscape(i18n('na.modals.filters.success.save.message', rfsModalTempForm.filterName)),
              timeOut: 5000
            })
          }
        })
      }
    }
  }

  ctrl.callbackcancel = function(modalScope) {
    // destroy the modal scope, in there we have a listener on $destroy event that will save the tempForm to save
    modalScope.$destroy()
    // Override value set on $destroy event
    goSearchService.setTempOperationForm({})
  }

  /** *********************** MODAL EDIT FILTER *************************/
  ctrl.openEditFilterModal = function() {
    if ($scope.searchFormItems.filterName === '' || !$scope.searchFormItems.filterName) {
      notifyError({
        title: i18n('na.modals.filters.error.edit'),
        message: i18n('na.modals.filters.error.missingselectedfilter'),
        timeOut: 5000
      })
    } else {
      const route = routes.na.go.controllers.Modals.getEditFilterModal($scope.searchFormItems.filterName)

      request({
        route,
        data: { context: SearchContexts.template },
        onSuccess: data => {
          const modalScope = $rootScope.$new(true)

          const buttonOk = {
            label: i18n('na.modal.button.savefilter'),
            callback: () => ctrl.editcallbackconfirm(modalScope),
            dismiss: true
          }
          const buttonCancel = {
            label: i18n('na.modal.button.cancel'),
            callback: () => ctrl.editcallbackcancel(modalScope),
            dismiss: true
          }

          // Store the form in the service. Attention, this is considered a temporary form, so we store it in tempForm!
          const tempForm = angular.copy($scope.searchFormItems)
          const tempFormModel = new OperationForm(tempForm)
          goSearchService.setTempOperationForm(tempFormModel)

          const content = $compile($.trim(data))(modalScope)

          showModal({
            title: i18n('na.modal.filters.title.editfilter'),
            body: content,
            buttonOk,
            modalSize: 'medium',
            buttonCancel
          })
        }
      })
    }
  }

  ctrl.editcallbackcancel = modalScope => {
    // destroy the modal scope, in there we have a listener on $destroy event that will save the tempForm to save
    modalScope.$destroy()
    // Override value set on $destroy event
    goSearchService.setTempOperationForm({})
  }

  ctrl.editcallbackconfirm = modalScope => {
    // destroy the modal scope, in there we have a listener for $destroy event that will save the tempForm on the service
    modalScope.$destroy()
    const rfsModalTempForm = goSearchService.getTempOperationForm()
    const result = goSearchService.validateDates(rfsModalTempForm.startDate, rfsModalTempForm.endDate, {
      context: filterOperations.update,
      startDateRequired: false,
      endDateRequired: false
    })
    if (result) {
      if (rfsModalTempForm.filterName === '' || !rfsModalTempForm.filterName) {
        notifyError({
          title: i18n('na.modals.filters.error.edit'),
          message: i18n('na.modals.filters.error.missingselectedfilter'),
          timeOut: 5000
        })
      } else {
        const route = routes.na.go.controllers.Modals.editFilter(rfsModalTempForm.filterName)

        request({
          route,
          data: { form: rfsModalTempForm, context: SearchContexts.template },
          onSuccess: () => {
            updateSearchFormItems(rfsModalTempForm)
            notifySuccess({
              title: i18n('na.modals.filters.success.edit'),
              message: htmlEscape(i18n('na.modals.filters.success.edit.message', rfsModalTempForm.filterName)),
              timeOut: 5000
            })
          }
        })
      }
    }
  }

  /** *********************** MODAL DELETE FILTER *************************/
  ctrl.openDeleteFilterModal = function() {
    if ($scope.searchFormItems.filterName === '' || !$scope.searchFormItems.filterName) {
      notifyError({
        title: i18n('na.modals.filters.error.delete'),
        message: i18n('na.modals.filters.error.missing.selectfilter.delete'),
        timeOut: 5000
      })
    } else {
      const route = routes.na.go.controllers.Modals.getDeleteFilterModal($scope.searchFormItems.filterName)

      request({
        route,
        data: { context: SearchContexts.template },
        onSuccess: data => {
          const modalScope = $rootScope.$new(true)

          const buttonOk = {
            label: i18n('na.modal.button.deletefilter'),
            callback: () => ctrl.deletecallbackconfirm(modalScope),
            dismiss: true
          }
          const buttonCancel = {
            label: i18n('na.modal.button.cancel'),
            callback: () => ctrl.deletecallbackcancel(modalScope),
            dismiss: true
          }

          goSearchService.setTempSelectedFilter($scope.searchFormItems.filterName)

          const content = $compile($.trim(data))(modalScope)

          showModal({
            title: i18n('na.modal.filters.title.deletefilter'),
            body: content,
            buttonOk,
            modalSize: 'medium',
            buttonCancel
          })
        }
      })
    }
  }

  ctrl.deletecallbackconfirm = function(modalScope) {
    // destroy the modal scope, in there we have a listener on $destroy event that will save the tempForm to save
    modalScope.$destroy()
    const filterToDelete = goSearchService.getTempSelectedFilter()

    if (filterToDelete === '' || !filterToDelete) {
      notifyError({
        title: i18n('na.modals.filters.error.delete'),
        message: i18n('na.modals.filters.error.missing.selectfilter.delete'),
        timeOut: 5000
      })
    } else {
      const route = routes.na.go.controllers.Modals.deleteFilter(filterToDelete)
      request({
        route,
        data: { context: SearchContexts.template },
        onSuccess: () => {
          notifySuccess({
            title: i18n('na.modals.filters.success.delete'),
            message: htmlEscape(i18n('na.modals.filters.success.delete.message', filterToDelete)),
            timeOut: 5000
          })
        }
      })
    }
    $scope.searchFormItems = {}
    $scope.$broadcast('SelectBox::cleanAll')

    goSearchService.initializeAdminModelForm({})
    $scope.searchFormItems = goSearchService.getAdminTemplateForm()
  }

  ctrl.deletecallbackcancel = modalScope => {
    // destroy the modal scope, in there we have a listener on $destroy event that will save the tempForm to save
    modalScope.$destroy()
    goSearchService.setTempSelectedFilter('')
  }
}
