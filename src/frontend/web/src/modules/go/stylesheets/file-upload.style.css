.form-field.form-field--file-input {
    position: relative;
    display: inline-block;
    width: 100%;
    margin-bottom: 0;
    background-color: #fff;
    padding: 0;
    height: 30px;
}

.form-field--file-input .form-field__label {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1;
    cursor: pointer;
    line-height: 1.5;
    font-weight: normal;
    display: flex;
}

.form-field--file-input .form-field__placeholder {
    flex-grow: 1;
    padding: 5px 10px;
    text-overflow: ellipsis;
    overflow: hidden;
}


.form-field--file-input .form-field__label--empty {
    color: #aaa;
}

.form-field--file-input .form-field__close {
    padding: 5px 10px;
    font-weight: bold;
}

.form-field--file-input .form-field__label .form-field__button {
    display: block;
    height: 29px;
    padding: 5px 10px;
    line-height: 1.5;
    color: #495057;
    background-color: #e9ecef;
    border-left: 1px solid #ced4da;
    border-radius: 0 .25rem .25rem 0;
}

.form-field--file-input .form-field__input {
    position: relative;
    z-index: 2;
    width: 1px;
    height: calc(2.25rem + 2px);
    margin: 0;
    opacity: 0.01;
}

.form-field__label--empty .form-field__close {
    display: none;
}
