import './style-load'
import { moduleName } from './src/go.angular-module'
import { bootstrap } from 'angular'

import 'fuxi/app/js/vendor/bootstrap/bootstrap'
import '~basemodule/fuxi'
import '~basemodule/app/src/common/enable-tooltip.directive'
import '~utils/proxy/implementation'
import '~utils/time-deprecated/implementation'
import '~utils/nowc/i18n.configuration'

import $ from 'jquery'
import '~nowc/components/date-time-picker/date-time-picker.element'
import '~nowc/components/wizard/wizard.element'
import '~nowc/components/splitter/splitter.element'
import '~nowc/components/collapsible/collapsible.element'
import '~nowc/components/shadow-scroll/shadow-scroll.element'
import '~nowc/components/tabs/tab-container.element'
import '~nowc/components/dynamic-span/dynamic-span.element'
import '~components/i18n/i18n.element'

global.jQuery = global.$ = $

const importAll = (requires) => requires.keys().forEach(requires)
importAll(require.context('~utils', true, /\/implementation\.js$/))
importAll(require.context('~utils', true, /\/implementation\.ssr\.js$/))
importAll(require.context('~utils', true, /\/implementation\.play\.(js|ts)$/))
importAll(require.context('~utils', true, /\/implementation\.na\.js$/))
importAll(require.context('~components/validation', true, /\.directive\.js$/))
importAll(require.context('~components/naDatatables-v2', true, /\.datatable-renderer\.js$/))
importAll(require.context('~components/naDatatables-v2', true, /\.directive\.js$/))
importAll(require.context('~components/naDatatables', true, /\.directive\.js$/))
importAll(require.context('~components/naDatatables', true, /\.controller\.js$/))
importAll(require.context('~components/naDatatables', true, /\.service\.js$/))
importAll(require.context('~components/selectbox', true, /\.directive\.js$/))
importAll(require.context('./src', true, /\.datatable-renderer\.js$/))
importAll(require.context('./src', true, /\.directiv(e|es)\.js$/))
importAll(require.context('./src', true, /\.controller\.js$/))
importAll(require.context('./src', true, /\.service\.js$/))

bootstrap(document.body, [moduleName])
