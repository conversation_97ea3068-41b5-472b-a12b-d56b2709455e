export const NOSSIS_ONE_THEME = 'nossis-one'

/**
 * Mutation observer used to observe the app theme, for now we point to the <html> element as we do not have
 * any Application web component such as <x-tsc-app> of NOSSIS TSC project which at that point
 */
const mutationObserver = new MutationObserver((mutations) => {
  const targetsData = []
  for (const mutation of mutations) {
    const { target } = mutation
    // we can change multiple times on the same element, we only care about the attribute
    // value before and after those changes
    if (targetsData.find(({ element }) => element === target) == null) {
      targetsData.push({
        element: target,
        oldTheme: mutation.oldValue,
        newTheme: target.getAttribute('data-app-theme')
      })
    }
  }

  for (const { newTheme, oldTheme, element } of targetsData) {
    themeChangeObservers.forEach(observer => {
      observer.callback(newTheme, oldTheme, element)
    })
  }
})

/**
 * options used to observe theme changes in html
 * @type {MutationObserverInit}
 */
const mutationObserverOptions = {
  attributeFilter: ['data-app-theme'],
  attributes: true,
  attributeOldValue: true,
}

const themeChangeObservers = new Set()

/**
 * Observes applied theme changes on element, every time it changes
 * @param {HTMLElement} element
 * @param {(newTheme: string, oldTheme: string, element: Element) => void} callback
 */
function observeThemeChangeOnElement(element, callback) {
  const observer = Object.freeze({
    target: element,
    callback,
    unregister: () => {
      themeChangeObservers.delete(observer)
    }
  })
  if (themeChangeObservers.size <= 0) {
    mutationObserver.observe(element, mutationObserverOptions)
  }
  themeChangeObservers.add(observer)
}

export function observeThemeChange(callback) {
  observeThemeChangeOnElement(document.documentElement, callback)
}

let baseUrl = 'na-portal-assets-vendors' // default base url

function applyAssetUrl(url) {
  const separator = baseUrl.endsWith('/') ? '' : '/'
  return baseUrl + separator + url
}

export function setAssetsBaseUrl(router) {
  baseUrl = router
}
export const getCurrentThemeName = () => document.documentElement.getAttribute('data-app-theme')

/**
 * Applies baseURL to CSSStyleSheet manually
 *
 * Due to chromium bug https://bugs.chromium.org/p/chromium/issues/detail?id=1275639,
 * `baseURL` option does not work on CSSStyleSheet constructor
 *
 * @param {string} css - CSS text
 * @return {string} CSS text with updated URLS
 */
function applyBaseURLToAssets(css) {
  return css.replace(/url\(([a-zA-Z][^)]+)\)/g, (_, url) => `url(${applyAssetUrl(url)})`)
}

/**
 *
 * @param {CSSStyleSheet} styleSheet - target CSSStyleSheet to apply CSS rules
 * @param { function | *[] | string } css - CSS rules content, can be a module import call to load asynchronously, or
 *    the CSS content. Also handles Webpack css import returning a list of imported modules.
 */
export function applyStylesOnStyleSheet(styleSheet, css) {
  if (typeof css === 'function') {
    Promise.resolve(css()).then(moduleOrCss => {
      if (moduleOrCss?.default) {
        // moduleCss is an ES module, so we get the default module value
        applyStylesOnStyleSheet(styleSheet, moduleOrCss.default)
      } else {
        // we expect moduleOrCss to be the css content here
        applyStylesOnStyleSheet(styleSheet, moduleOrCss)
      }
    })
    return
  }
  if (Array.isArray(css)) {
    // is a css string array or a webpack imported css module content
    css = css.map(cssData => {
      if (Array.isArray(cssData)) {
        // is a webpack imported css module, import its contents
        return isDebugMode() ? withRulesLocation(cssData) : cssData[1]
      }
      // is a css string, use it
      return cssData
    }).join('\n')
  }
  styleSheet.replaceSync(applyBaseURLToAssets(css))
}

// Debug section - at this point, this code is to simplify debugging

const storageKey = 'Naportal::Stylesheet::Debug'
function getDebugStorage () {
  if (globalThis === window) {
    return window.sessionStorage.getItem(storageKey) ??
      window.localStorage.getItem(storageKey) ??
      'disabled'
  }
}

function saveDebugToStorage (isLocalStorage, isDebugModeEnabled) {
  if (globalThis === window) {
    const storage = isLocalStorage ? window.localStorage : window.sessionStorage
    return storage.setItem(storageKey, isDebugModeEnabled ? 'enabled' : 'disabled')
  }
}

function isDebugMode () {
  return globalThis.DEBUG != null ? globalThis.DEBUG === true : getDebugStorage() === 'enabled'
}

global.na = global.na || {}
global.na.StyleSheet = {
  enableDebug (option = {}) {
    saveDebugToStorage(false, true)
    const persist = () => saveDebugToStorage(true, true)
    if (option.persist === true) { persist() }
    return { persist }
  },

  disableDebug(option = {}) {
    saveDebugToStorage(false, false)
    const persist = () => saveDebugToStorage(true, false)
    if (option.persist === true) { persist() }
    return { persist }
  }
}

let registeredProperty = false
/**
 * Add the custom CSS property "--stylesheet" that puts the filename where the rules are located
 *
 * Due to usage of constructed Stylesheets, the link where a rule is located is "Contructed" (translated to the
 * browser language), this doesn't help identifying styling problems. This function purpose is to help migrating
 * FUXI, an UX framework. Using this utility helps identify the location of CSS rules causing problems.
 *
 * To enable debug mode, run "window.na.StyleSheet.enableDebug()" the refresh the page, it will persist on the session.
 * To persist in multiple sessions, run "window.na.StyleSheet.enableDebug().persist()"
 *
 * @param {string[]} webpackCSSData - css data generated by webpack
 */
function withRulesLocation(webpackCSSData) {
  const customPropertyName = '--na-stylesheet'
  if (!registeredProperty) {
    // register property to be non-inheritable as to not show the applied rules in dev-tools inherited with all parent
    // unrelated nodes, at the time of writing is not supported in Firefox
    try {
      window.CSS?.registerProperty?.({ name: customPropertyName, inherits: false })
    } catch (e) {
      // do nothing, ignore error because property is already registered
      console.groupCollapsed(`Error registering debug CSS property ${customPropertyName} because it is already registered. Ignoring error...`)
      console.warn(e)
      console.groupEnd()
    }
    registeredProperty = true
  }
  // webpackCSSData[0] contains the absolute path or an id number if it is compiled to production code
  const location = String(webpackCSSData[0]).split('/').at(-1)
  // is a webpack imported css module, import its contents
  return webpackCSSData[1].replaceAll(/;(\s+)}/g, (_, whitespaces) => `;${customPropertyName}: "${location}";${whitespaces}}`)
}

/**
 * Creates a CSSStyleSheet loader that only loads once and on demand.
 *
 * @param {() => Promise<{default: string}>} cssTextImportFn - css style import function to call when
 *      awaiting the created loader
 *
 * @returns {PromiseLike<CSSStyleSheet>} a promise like object that allows to use it as a promise
 */
export function CssStyleSheetLoader(cssTextImportFn) {
  let getStyleSheet = () => {
    const promise = cssTextImportFn().then(({ default: css }) => {
      const styleSheet = new CSSStyleSheet()
      applyStylesOnStyleSheet(styleSheet, css)
      return styleSheet
    })
    getStyleSheet = () => promise
    return promise
  }

  return {
    then: (styleSheetFn) => getStyleSheet().then(styleSheetFn)
  }
}
