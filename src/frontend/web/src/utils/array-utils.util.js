function defaultEqualityCheck (val1, val2) {
  // return true if equal or both NaN because NaN !== NaN
  return val1 === val2 || (Number.isNaN(val1) && Number.isNaN(val2))
}

export function arrayEquals (array1, array2, comparator) {
  if (!Array.isArray(array1) || !Array.isArray(array2) || array1.length !== array2.length) {
    return false
  }
  if (array1 === array2) {
    return true
  }

  const isEqual = typeof comparator === 'function' ? comparator : defaultEqualityCheck

  const len = array1.length
  for (let i = 0; i < len; ++i) {
    if (!isEqual(array1[i], array2[i])) {
      return false
    }
  }
  return true
}

export function isEmptyArray(array) {
  return Array.isArray(array) && array.length === 0
}
