import { receivedFromActiveIframe } from '~utils/nossis-ui'
import { i18n } from '~utils/i18n'

window.addEventListener('message', receiveMessage, false)

function receiveMessage({ data, origin, source }) {
  // is this message related to breadcrumb ? If not, ignore the message.
  if (!data || data.context !== 'breadcrumb') {
    return
  }

  // did we receive from the active iframe ? If not, do nothing for security reasons.
  if (!receivedFromActiveIframe(origin)) {
    return
  }

  function handleMenuItemClick(event) {
    if (event.currentTarget.hasAttribute('data-handle-by-portal')) {
      return
    }
    event.preventDefault()
    event.stopPropagation()
    source.postMessage({
      context: data.context,
      action: 'entry-click',
      entry: {
        url: event.currentTarget.getAttribute('href'),
        label: event.currentTarget.textContent,
        isLast: event.currentTarget.matches('li:last-child > a')
      },
      handleByOrigin: event.currentTarget.matches('li:nth-child(-n+1) > a')
    }, origin)
  }

  function getPathNameFromUrl(linkUrl) {
    const moduleUrl = new URL(linkUrl)
    return moduleUrl.pathname
  }

  function isActive(moduleUrlString) {
    const activeTargetUrl = new URL(moduleUrlString)
    const activeTargetPath = window.location.href.split('target=')[1].split('?')[0]
    return activeTargetPath === activeTargetUrl.pathname
  }

  function isElementWithSelectedLink(element, currentPath) {
    let link = element.href?.split('target=')[1]
    if (!link) {
      return false
    }
    if (currentPath.includes(link)) {
      return true
    }
    link = link.replace('home', '')
    if (currentPath.includes(link)) {
      return true
    }
    link = link.split('#')[0]
    return currentPath.includes(link)
  }

  switch (data.action) {
    case 'ping':
      source.postMessage({ context: data.context, action: 'pong' }, origin)
      break
    case 'set-breadcrumb-path': {
      const title = i18n('nossis.go.to.home')
      const prefix = [{ action: window.location.pathname, cssClass: 'glyphicon glyphicon-home fx-icon', title }]
      const { path } = data
      const activeTopLevelMenu = document.body.querySelector('.top-level-menu.active')

      if (activeTopLevelMenu != null) {
        let menuAction = null
        let subMenuTitle = null

        if (activeTopLevelMenu.classList.contains('dropdown')) {
          const currentPath = getHash(window.location)
          const selectedLink = Array.from(activeTopLevelMenu.querySelectorAll('a'))
            .find(element => isElementWithSelectedLink(element, currentPath))
          const subMenu = selectedLink.closest('ul')
          subMenuTitle = subMenu.querySelector('.fx-divider-title').textContent.trim()
          menuAction = getHash(subMenu.querySelector('[data-main-link-of-module]')) || getHash(selectedLink)
        }
        menuAction ??= getHash(activeTopLevelMenu.querySelector('[data-main-link-of-module]')) || getHash(window.location)

        const isEmptyPath = path?.length === 0

        prefix.push({
          ...(!isEmptyPath ? { action: menuAction } : {}),
          cssClass: '',
          title: activeTopLevelMenu.querySelector('a').textContent.trim()
        })

        if (subMenuTitle) {
          prefix.push({
            ...(!isEmptyPath ? { action: menuAction } : {}),
            cssClass: '',
            title: subMenuTitle
          })
        }
      }

      requestAnimationFrame(() => {
        const maxLength = 50
        const half = Math.floor(maxLength / 2)
        window.nossisportal.breadcrumb.set([
          ...prefix,
          ...path.map((entry, i, { length }) => {
            const { label, url, icon } = entry
            const title = (label.length > maxLength) ? `${label.slice(0, half - 3)}...${label.slice(-half)}` : label
            return {
              ...(isActive(url) || i === length - 1 ? {} : { action: getPathNameFromUrl(url) }),
              title,
              cssClass: icon
            }
          })
        ]);
        [...document.querySelectorAll('.breadcrumb.fx-breadcrumb > li > a')].forEach((link) => {
          link.addEventListener('click', handleMenuItemClick)
        })
        document.querySelectorAll('.breadcrumb.fx-breadcrumb > li').forEach((element, index) => {
          const pathIndex = index - prefix.length
          if (pathIndex < 0) {
            return
          }
          const { label } = path[pathIndex]
          if (label.length > maxLength) {
            element.setAttribute('title', label)
          }
        })
      })
      break
    }
    case 'handle-entry': {
      const { entry } = data;
      [...document.querySelectorAll(`.breadcrumb.fx-breadcrumb > li > a[href="${entry.url}"]`)].forEach((link) => {
        link.setAttribute('data-handle-by-portal', '')
        link.click()
        link.removeAttribute('data-handle-by-portal')
      })
    }
  }
}

function getHash(link) {
  if (!link) {
    return null
  }

  const isolatedHash = typeof link === 'string' ? link : link.href

  if (!isolatedHash && typeof isolatedHash !== 'string') {
    return null
  }

  return isolatedHash.split('target=')[1]
    ?.replace('#/', '#')
    .split(/#.*(\/)/)[0]
}
