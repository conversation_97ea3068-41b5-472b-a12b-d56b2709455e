function defaultEqualityCheck (val1, val2) {
  return val1 === val2
}

export function hasSameKeysObjects (...objects) {
  const objectKeysList = objects.map(Object.keys)
  const allKeys = objectKeysList.reduce(function (keys, objectKeys) { return keys.concat(objectKeys) })
  const union = new Set(allKeys)
  return objectKeysList.every(function (objectKeys) { return union.size === objectKeys.length })
}

export function areEqualObjects (...args) {
  const argsHasEqualityCheck = typeof args[args.length - 1] === 'function'
  const equalityCheck = argsHasEqualityCheck ? args[args.length - 1] : defaultEqualityCheck
  const objects = argsHasEqualityCheck ? args.slice(0, -1) : args
  const objectKeysList = objects.map(Object.keys)
  const allKeys = objectKeysList.reduce(function (keys, objectKeys) { return keys.concat(objectKeys) })
  const union = new Set(allKeys)
  if (!objectKeysList.every(objectKeys => union.size === objectKeys.length)) {
    return false
  }
  const unionArray = Array.from(union)
  const firstObject = objects[0]
  return objects.slice(1).every(function (object) {
    return unionArray.every(key => equalityCheck(object[key], firstObject[key]))
  })
}

export function omitKeys (obj, ...keys) {
  keys = keys.reduce((acc, key) => acc.concat(key), []).map(key => String(key))
  const target = {}
  for (const i in obj) {
    // eslint-disable-next-line no-prototype-builtins
    if (!obj.hasOwnProperty(i)) { continue }
    if (keys.indexOf(i) >= 0) { continue }
    target[i] = obj[i]
  }
  return target
}

export function isEmptyObject (obj) {
  return Object.keys(obj).length === 0
}

export function isPlainObject (obj) {
  return obj && obj.constructor === Object
}
