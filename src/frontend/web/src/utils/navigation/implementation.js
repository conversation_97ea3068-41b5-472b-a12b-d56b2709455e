import { showWaitNotification, hideWaitNotification } from '../page-notification'
import { get } from '~utils/proxy'
import { hideToast } from '~utils/toast-notification'
import { Store } from '../store.util'
import { provide } from './provider'
provide({ navigateTo, navigateToInNewTab, executeCustomNavigation, addLoadPageMiddleware, loadPage, addNavigationHandler, listenPageLoadEvent, captureNavigation })

let middlewares = []
let popstateListeners = []
const pageLoadListeners = []
let isListeningPopState = false
let navigationListeners = []

export function captureNavigation(callback) {
  navigationListeners.push(callback)
  return {
    remove: () => { navigationListeners = navigationListeners.filter(listener => listener !== callback) },
    callback: callback
  }
}

export function addLoadPageMiddleware(callback) {
  middlewares.push(callback)
  return {
    remove: () => removeMiddleware(callback),
    callback: callback
  }
}

function removeMiddleware(middleware) {
  middlewares = middlewares.filter((mid) => mid !== middleware)
}

function reduceMiddleWare(middlewares, data, done) {
  function applyReducer(reducers, index) {
    return (acc) => {
      if (index < reducers.length) {
        reducers[index](acc, applyReducer(reducers, index + 1))
      } else {
        done(acc)
      }
    }
  }
  applyReducer(middlewares, 0)(data)
}

export function loadPage({ url, html, isError }) {
  return new Promise((resolve) => reduceMiddleWare(middlewares, { url, html, isError }, data => resolve(data)))
}

function removeBackNavigationHandler(callback) {
  popstateListeners = popstateListeners.filter(listener => listener !== callback)
  popstateListeners.push(callback)
}

function broadcastToPopstateListeners(event) {
  popstateListeners.forEach(listener => listener(event))
}

const initialState = () => ({
  previousPage: { url: null, timestamp: -1 },
  currentPage: (history && history.state && history.state.timestamp) ? { url: history.state.url, timestamp: history.state.timestamp } : { url: null, timestamp: -1 },
  popStateActionQueue: []
})

const getters = {
  isForwardNavigation: (state) => state.currentPage.timestamp > state.previousPage.timestamp,
  currentPopStateAction: (state) => state.popStateActionQueue.length > 0 ? state.popStateActionQueue[0].action : 'default'
}

const setters = {
  currentPage: (state, { timestamp, url }) => {
    return state.currentPage.timestamp === timestamp ? state : {
      ...state,
      previousPage: state.currentPage,
      currentPage: { timestamp, url }
    }
  },
  ignoreNextPopState: (state) => ({
    ...state,
    popStateActionQueue: [...state.popStateActionQueue, { action: 'ignore handling' }]
  }),
  ignoreCaptureNextPopState: (state) => ({
    ...state,
    popStateActionQueue: [...state.popStateActionQueue, { action: 'ignore capture' }]
  }),
  shiftActionQueue: (state) => state.popStateActionQueue.length <= 0 ? state : {
    ...state,
    popStateActionQueue: state.popStateActionQueue.slice(1)
  }
}

const store = Store({ initialState, getters, setters }, 'Navigation Handler')

export function addNavigationHandler(callback) {
  if (!isListeningPopState && window && window.addEventListener) {
    window.addEventListener('popstate', e => {
      const { url, timestamp } = (e && e.state && e.state.timestamp) ? e.state : { url: null, timestamp: -1 }
      store.setters.currentPage({ url, timestamp })
      const isForwardNavigation = store.getters.isForwardNavigation
      const popAction = store.getters.currentPopStateAction
      store.setters.shiftActionQueue()
      switch (popAction) {
        case 'ignore handling':
          break
        case 'ignore capture':
          broadcastToPopstateListeners(e)
          break
        case 'default':
          if (navigationListeners.length > 0) {
            let doneSynchronously = true
            let isDoneCalledSynchronous = false
            reduceMiddleWare(navigationListeners, e.state, () => {
              if (doneSynchronously) {
                isDoneCalledSynchronous = true
              } else {
                store.setters.ignoreCaptureNextPopState()
                isForwardNavigation ? history.forward() : history.back()
              }
            })
            doneSynchronously = false
            if (!isDoneCalledSynchronous) {
              store.setters.ignoreNextPopState()
              isForwardNavigation ? history.back() : history.forward()
            } else {
              broadcastToPopstateListeners(e)
            }
          } else {
            broadcastToPopstateListeners(e)
          }
      }
    })
    isListeningPopState = true
  }

  popstateListeners.push(callback)
  return {
    remove: () => removeBackNavigationHandler(callback),
    callback: callback
  }
}

function navigateToPage(url, data, callbackOnSuccess, callbackOnError) {
  showWaitNotification()
  get({
    url,
    data,
    onSuccess(html) {
      hideToast()
      loadPage({ html, url }).then(function (data) {
        pageLoadListeners.forEach(listener => listener(data))
      })
      hideWaitNotification()
      if (typeof callbackOnSuccess === 'function') { callbackOnSuccess() }
    },
    onError: callbackOnError
  })
}

export function navigateTo({ route = {}, url, params, onSuccess, onError } = {}) {
  url = (() => {
    const uri = decodeURIComponent(route.url || url)
    return encodeURI === true ? window.encodeURI(uri) : uri
  })()
  executeCustomNavigation(() => navigateToPage(url, params, onSuccess, onError))
}

export function navigateToInNewTab ({ route = {}, url } = {}) {
  url = (() => {
    const uri = decodeURIComponent(route.url || url)
    return encodeURI === true ? window.encodeURI(uri) : uri
  })()

  window.open(url, '_blank')
}

export function executeCustomNavigation(callback) {
  if (navigationListeners.length > 0) {
    reduceMiddleWare(navigationListeners, null, (callback))
  } else {
    callback()
  }
}

export function listenPageLoadEvent(callback) {
  pageLoadListeners.push(callback)
  return {
    remove: () => pageLoadListeners.filter(listener => listener !== callback),
    callback: callback
  }
}
