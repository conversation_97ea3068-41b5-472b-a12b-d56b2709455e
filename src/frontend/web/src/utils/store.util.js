import { deepFreeze } from './deep-freeze.util'
import { deepClone } from './deep-clone.util'

function getDebugStorage () {
  if (globalThis === window) {
    const storageValue = window.localStorage.getItem('Naportal::Store::Debug')
    return storageValue == null ? storageValue : JSON.stringify(storageValue)
  }
}

function saveDebugToStorage (debugMode) {
  if (globalThis === window) {
    return window.localStorage.setItem('Naportal::Store::Debug', JSON.stringify(debugMode))
  }
}

function isDebugMode () {
  return globalThis.DEBUG != null ? globalThis.DEBUG === true : getDebugStorage() != null
}

const debugMode = {
  listeners: false,
  setters: false,
  applyState: false,
  init: false
}

const storeActionsStack = []

function debug (debugVariant, ...args) {
  if (!debugVariant) {
    return
  }
  if (typeof console.groupCollapsed === 'function') {
    console.groupCollapsed(...args[0])
    args.slice(1).forEach(arg => {
      console.log(...arg)
    })
    console.groupEnd()
  } else {
    args.forEach(arg => console.log(...arg))
  }
}

Store.enableDebug = function (option = {}) {
  debugMode.listeners = option.listeners !== false
  debugMode.setters = option.setters !== false
  debugMode.applyState = option.applyState !== false
  debugMode.init = option.init !== false

  if (option.persist === true) {
    saveDebugToStorage(debugMode)
  }
  return {
    persist () {
      saveDebugToStorage(debugMode)
    }
  }
}

Store.disableDebug = function (option = {}) {
  debugMode.listeners = option.listeners === true
  debugMode.setters = option.setters === true
  debugMode.applyState = option.applyState === true
  debugMode.init = option.init === true

  if (option.persist === true) {
    saveDebugToStorage(debugMode)
  }
  return {
    persist () {
      saveDebugToStorage(debugMode)
    }
  }
}

if (isDebugMode()) {
  Store.enableDebug(getDebugStorage())
}

function cloneIfPlainObject (val) {
  return (val && val.constructor === Object) ? Object.assign({}, val) : val
}

function printArgs (args) {
  return args.length === 1 ? cloneIfPlainObject(args[0]) : args.map(cloneIfPlainObject)
}

function debugStateChange (setterName, args, oldState, newState, store) {
  if (!debugMode.setters) return
  const params = deepClone(args.length === 1 ? args[0] : args)
  debug(debugMode.setters,
    ['%csetter called %c%s %c%s %c%s',
      'color:blue',
      'font-weight:bold', setterName,
      'color:blue', store.name ? 'on store' : '',
      'font-weight:bold', store.name ? '"' + store.name + '"' : ''],
    ['%cparams:', 'color:blue;font-weight:bold', params],
    ['%cold state:', 'color:blue;font-weight:bold', oldState],
    ['%cnew state:', 'color:blue;font-weight:bold', newState],
    ['%cstore:', 'color:blue;font-weight:bold', store])
  actions.push({
    action: 'set',
    store: store.name ? store.name : null,
    setter: setterName,
    params: params,
    stateChanged: true,
    newState: newState
  })
}

function debugSetterError (setterName, args, error, store) {
  if (!debugMode.setters) return
  debug(debugMode.setters,
    ['%csetter called %c%s %c%s %c%s',
      'color:red',
      'font-weight:bold', setterName,
      'color:red', store.name ? 'on store' : '',
      'font-weight:bold', store.name ? '"' + store.name + '"' : ''],
    ['%cparams:', 'color:red;font-weight:bold', printArgs(args)],
    ['%cstate:', 'color:red;font-weight:bold', store.state],
    ['%cerror:', 'color:red;font-weight:bold', error],
    ['%cstore:', 'color:red;font-weight:bold', store])
  actions.push({
    action: 'set',
    store: store.name ? store.name : null,
    setter: setterName,
    params: args,
    stateChanged: true
  })
}

function debugRecursiveStateChange (setterName, args, store) {
  debug(true,
    ['%csetter %c%s %c%s %c%s %cwhile it is changing state',
      'color:red',
      'font-weight:bold', setterName,
      'color:red', store.name ? 'called on store' : 'called',
      'font-weight:bold', store.name ? '"' + store.name + '"' : '',
      'color:red'],
    ['%cparams:', 'color:red;font-weight:bold', printArgs(args)],
    ['%cstate:', 'color:red;font-weight:bold', store.state],
    ['%cstack:', 'color:red;font-weight:bold', [...storeActionsStack]],
    ['%cerror:', 'color:red;font-weight:bold', new Error('recursive setter stacktrace')],
    ['%cstore:', 'color:red;font-weight:bold', store])
  if (!debugMode.setters) return
  actions.push({
    action: 'set',
    store: store.name ? store.name : null,
    setter: setterName,
    params: args,
    stateChanged: true
  })
}

function debugListenerUnregisterStateChange (listener, store) {
  if (!debugMode.listeners) return
  debug(debugMode.listeners,
    ['%cunregistering listener %c%s %c%s %cwhile changing state',
      'color:orange',
      'color:orange', store.name ? 'on store' : '',
      'font-weight:bold', store.name ? '"' + store.name + '"' : '',
      'color:orange'],
    ['%cerror:', 'color:orange;font-weight:bold', new Error('listener unregister')],
    ['%clistener:', 'color:orange;font-weight:bold', listener],
    ['%cstore:', 'color:orange;font-weight:bold', store])
}

function debugNoStateChange (setterName, args, store) {
  if (!debugMode.setters) return
  const params = deepClone(args.length === 1 ? args[0] : args)
  debug(debugMode.setters,
    ['%csetter called %c%s %c%s %c%s',
      'color:orange',
      'font-weight:bold', setterName,
      'color:orange', store.name ? 'on store' : '',
      'font-weight:bold', store.name ? '"' + store.name + '"' : ''],
    ['%cparams:', 'color:orange;font-weight:bold', params],
    ['%cstate:', 'color:orange;font-weight:bold', store.state],
    ['%cno state changed', 'color:orange;font-weight:bold'],
    ['%cstore:', 'color:orange;font-weight:bold', store]
  )
  actions.push({
    action: 'set',
    store: store.name ? store.name : null,
    setter: setterName,
    params: params,
    stateChanged: false
  })
}

function debugInitStore (store) {
  if (!debugMode.init) return
  debug(debugMode.init,
    ['%cstore %c%s %cinitialized',
      'color:darkgreen',
      'font-weight:bold', '"' + store.name + '"',
      'color:darkgreen'],
    ['%cstore:', 'color:darkgreen;font-weight:bold', store]
  )
  actions.push({ action: 'init', store: store.name })
}

function cacheGettersResults (getters) {
  if (getters.$$cached == null) {
    const cache = new WeakMap()
    getters.$$cached = Object.keys(getters).reduce(function (result, getterName) {
      result[getterName] = function (state) {
        let getterCache = cache.get(state)
        if (getterCache == null) {
          getterCache = {}
          cache.set(state, getterCache)
        }
        if (getterCache[getterName] === undefined) {
          getterCache[getterName] = deepFreeze(getters[getterName](state))
        }
        return getterCache[getterName]
      }
      return result
    }, {})
  }
  return getters.$$cached
}

function createImmutableStore (stateDefinition, frozenState) {
  return new Store(Object.assign({}, stateDefinition, {
    setters: {},
    initialState: function () {
      return frozenState
    },
    listenStateChange: function () {
      console.error('Immutable store: there will never be any changes')
    }
  }))
}

const stores = {}
const actions = []

const storesTriggeringStateChanges = []
export function Store (stateDefinition, storeName) {
  if (!(this instanceof Store)) {
    return new Store(stateDefinition, storeName)
  }
  const store = this
  if (typeof storeName === 'string' && storeName.trim() !== '') {
    store.name = storeName.trim()
    stores[store.name] = this
    debugInitStore(this)
  }
  if (stateDefinition.constants) {
    store.constants = deepFreeze(stateDefinition.constants)
  }
  store.state = deepFreeze(stateDefinition.initialState())
  const cachedGetters = cacheGettersResults(stateDefinition.getters)
  store.getters = {}
  Object.keys(cachedGetters).forEach(function (getterName) {
    Object.defineProperty(store.getters, getterName, {
      get: function () {
        return cachedGetters[getterName](store.state)
      }
    })
  })

  let stateChangeListeners = []

  function signalStateChange (newState, oldState) {
    storesTriggeringStateChanges.push(store)
    const newStateStore = createImmutableStore(stateDefinition, newState)
    const oldStateStore = createImmutableStore(stateDefinition, oldState)
    stateChangeListeners.forEach(function (listener) {
      listener.trigger(newStateStore, oldStateStore)
    })
    const index = storesTriggeringStateChanges.indexOf(store)
    storesTriggeringStateChanges.splice(index, 1)
  }

  function withStackTrace (fn, setterName) {
    return (...args) => {
      storeActionsStack.push({
        action: setterName,
        params: args.length === 1 ? args[0] : args,
        store: store.name
      })
      fn(...args)
      storeActionsStack.pop()
    }
  }

  store.setters = Object.keys(stateDefinition.setters).reduce(function (acc, setterName) {
    acc[setterName] = function (...args) {
      if (storesTriggeringStateChanges.includes(store)) {
        debugRecursiveStateChange(setterName, args, store)
        return
      }

      let newState
      try {
        newState = stateDefinition.setters[setterName].apply(this, [store.state].concat(args))
      } catch (e) {
        debugSetterError(setterName, args, e, store)
        return
      }
      if (newState !== store.state) {
        const frozenNewState = deepFreeze(newState)
        const oldState = store.state
        store.state = frozenNewState
        debugStateChange(setterName, args, oldState, frozenNewState, store)
        signalStateChange(frozenNewState, oldState)
        return
      }
      debugNoStateChange(setterName, args, store)
    }
    acc[setterName] = withStackTrace(acc[setterName], setterName)
    return acc
  }, {})

  const stateChangeListenerPrototype = {
    middlewareFunctions: Object.freeze([]),
    callback () {
      // when listener is cleaned, callback will be deleted and will run
      // this function that does nothing, it is possible to happen when
      // unregistering listener in the middle of a state change.
    },
    unregister  () {
      stateChangeListeners = stateChangeListeners.filter(listener => listener !== this)
      const listenerCopy = { ...this }
      if (storesTriggeringStateChanges.includes(store)) {
        debugListenerUnregisterStateChange(listenerCopy, store)
      }
      cleanupListener(this)
      debug(debugMode.listeners,
        ['removed store listener%s, current number of listeners: %d', store.name ? ' on "' + store.name + '"' : '', stateChangeListeners.length],
        ['removed listener: %o', listenerCopy],
        ['current listeners: %o', stateChangeListeners]
      )
    },
    until (condition) {
      const { middlewareFunctions } = this
      const untilConditionMiddleWare = (next, args) => {
        if (condition(...args) === true) {
          this.unregister()
        } else {
          next(args)
        }
      }
      this.middlewareFunctions = [untilConditionMiddleWare, ...middlewareFunctions]
      return this
    },
    trigger (newSnapshot, oldSnapshot) {
      const runCallback = (...args) => {
        try {
          this.callback(...args)
        } catch (e) {
          console.error('error on listener %o: %o', this, e)
        }
      }

      if (this.middlewareFunctions.length <= 0) {
        runCallback(newSnapshot, oldSnapshot)
        return
      }
      let args = [newSnapshot, oldSnapshot]
      const middlewareFunctions = this.middlewareFunctions.slice()
      let i = 0
      const runNext = newArgs => {
        i++
        args = newArgs
        if (i >= middlewareFunctions.length) {
          runCallback(...args)
        } else {
          middlewareFunctions[i](runNext, args)
        }
      }
      middlewareFunctions[0](runNext, args)
    }
  }

  /**
   * Cleans the listener, used when unregistering the listener to make the callbacks free for garbage collection even if
   * the listener is referenced to other objects.
   *
   * @param {StoreListener} listener - target listener
   */
  function cleanupListener (listener) {
    delete listener.middlewareFunctions
    delete listener.callback
  }

  function createStateChangeListener (callback) {
    const listener = Object.create(stateChangeListenerPrototype)
    listener.callback = callback
    listener.unregister = stateChangeListenerPrototype.unregister.bind(listener)
    listener.until = stateChangeListenerPrototype.until.bind(listener)
    return listener
  }

  /**
   * Creates an observer to react to state changes in the store
   *
   * @param callback - observer callback
   * @return {StoreListener} created observer
   */
  store.listenStateChange = function (callback) {
    const listener = createStateChangeListener(callback)
    stateChangeListeners.push(listener)
    debug(debugMode.listeners,
      ['added store listener%s, current number of listeners: %d', store.name ? ' on "' + store.name + '"' : '', stateChangeListeners.length],
      ['listener: %o', listener]
    )
    return listener
  }

  store.reflect = (stateChangeCheck, callback) => {
    const listener = store.listenStateChange(callback)
    const { middlewareFunctions } = listener

    if (Array.isArray(stateChangeCheck)) {
      /** @type {MiddlewareFunction} */
      const reflectMultipleMiddleWare = (next, [newSnapshot, oldSnapshot]) => {
        const newValue = stateChangeCheck.map(check => check(newSnapshot))
        const oldValue = stateChangeCheck.map(check => check(oldSnapshot))
        for (let i = 0, e = stateChangeCheck.length; i < e; i++) {
          if (newValue[i] !== oldValue[i]) {
            next([newValue, oldValue, newSnapshot, oldSnapshot])
            return
          }
        }
      }
      listener.middlewareFunctions = [...middlewareFunctions, reflectMultipleMiddleWare]
    } else {
      /** @type {MiddlewareFunction} */
      const reflectMiddleWare = (next, [newSnapshot, oldSnapshot]) => {
        const newValue = stateChangeCheck(newSnapshot)
        const oldValue = stateChangeCheck(oldSnapshot)
        if (newValue !== oldValue) {
          next([newValue, oldValue, newSnapshot, oldSnapshot])
        }
      }
      listener.middlewareFunctions = [...middlewareFunctions, reflectMiddleWare]
    }

    return listener
  }

  store.react = (stateChangeCheck, callback) => {
    return store.reflect(stateChangeCheck, callback)
  }

  function applyState (newState) {
    const frozenNewState = deepFreeze(newState)
    const oldState = store.state
    store.state = frozenNewState
    debugStateChange('[(DEBUG) applyState]', [frozenNewState], oldState, frozenNewState, store)
    stateChangeListeners.forEach(function (listener) {
      listener.callback(
        createImmutableStore(stateDefinition, frozenNewState),
        createImmutableStore(stateDefinition, oldState)
      )
    })
  }

  Object.defineProperty(store, 'applyState', {
    get: function () {
      return debugMode.applyState ? applyState : undefined
    }
  })

  Object.defineProperty(store, 'listeners', {
    get: function () {
      return stateChangeListeners
    }
  })

  function resetState () {
    const frozenNewState = deepFreeze(stateDefinition.initialState())
    const oldState = store.state
    store.state = frozenNewState
    debugStateChange('[(DEBUG) reset]', [frozenNewState], oldState, frozenNewState, store)
    stateChangeListeners.forEach(function (listener) {
      listener.callback(
        createImmutableStore(stateDefinition, frozenNewState),
        createImmutableStore(stateDefinition, oldState)
      )
    })
  }

  Object.defineProperty(store, 'reset', {
    get: function () { return resetState }
  })
}

Store.stores = Store.state = new Proxy(stores, { set: () => false })
Store.actionLog = actions
Store.actionLogAsString = actions
Store.getStoreOrElse = (name, doIfNotDefined) => {
  if (stores[name] != null) {
    return stores[name]
  } else if (typeof doIfNotDefined === 'function') {
    return doIfNotDefined()
  }
}

Object.defineProperties(Store, {
  state: { get: () => ({ ...stores }) },
  actionLog: { get: () => actions.slice() },
  actionLogAsString: { get: () => actions.map(value => JSON.stringify(value)).join('\n') },
  actionLogAsReadableString: { get: () => actions.map(value => JSON.stringify(value, null, 2)).join('\n') },
  actionLogAsJson: { get: () => JSON.stringify(actions, null, 2) }
})

global.na = global.na || {}
global.na.Store = Store

export default Store

/**
 * @typedef {object} StoreListener
 *
 * Represents a store state change observer
 *
 * @property {()=>void} callback - callback function of the object
 * @property {()=>void} unregister - stop listening
 * @property {MiddlewareFunction[]} middlewareFunctions - middlwerare functions called before executing the callback
 * @property {Predicate} until - predicate to stop listening the callback, validated before running the callback when
 * the listener is triggered. If the predicate fails, the listener unregisters immediately.
 * @property {(newStateStore: StoreSnapshot, oldStateStore: StoreSnapshot)=>void} trigger - triggers the callback
 */

/**
 * @typedef {object} StoreSnapshot
 *
 * An immutable store that contains the state of a store in a specific point in time.
 *
 * @property {object} getters - The original store getters
 * @property {Readonly<object>} state - The original store state
 */

/**
 * @typedef {()=>boolean} Predicate
 */

/**
 * @callback MiddlewareFunction
 * @param {(paramsAcc: any[]) => any} next - call the next middleware or `storeListener.trigger` with `paramsAcc` as
 *        callback parameters
 * @param {any[]} paramsAcc - callback params received from previous middleware or `storeListener.trigger`
 */
