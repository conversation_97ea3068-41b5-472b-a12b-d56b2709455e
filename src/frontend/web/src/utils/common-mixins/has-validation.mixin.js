/**
 *  This mixin ensures a component implements validity.
 *  @param {CustomElementConstructor} superClass - HTMLElement class or subclass
 *  @returns {CustomElementConstructor} - subclass with applied mixin
 */
export function hasValidation(superClass) {
  return class ValidationMixin extends superClass {
    /** If a component extends this mixin, this method can be overridden to implement custom validity */
    get validity() {
      /** If the mixin extends a component with validity already defined, the original validity will be prioritized */
      const superValidity = super.validity ?? {}
      const result = {
        badInput: false,
        patternMismatch: false,
        customError: false,
        rangeOverflow: false,
        rangeUnderflow: false,
        stepMismatch: false,
        tooLong: false,
        typeMismatch: false,
        valueMissing: false,
        invalidRange: false,
        valid: true,
        ...superValidity
      }

      if (this.hasAttribute('required')) {
        if (!this.value) {
          result.valid = false
          result.valueMissing = true
        }
      }

      return result
    }
  }
}
