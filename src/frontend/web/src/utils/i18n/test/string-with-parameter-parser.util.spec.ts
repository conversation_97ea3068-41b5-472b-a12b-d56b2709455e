import { test } from 'tape';
import { parse } from '../string-with-parameter-parser.util'

test(' i18n - string with parameters parser - split by semicolon ', (t) => {
    // prepare
    t.plan(1)
    const expected = {
        key: 'i18n.key.scope',
        parameters: ['param1', 'param2'],
        valid: true,
    }
    // act
    const result = parse('i18n.key.scope;param1;param2')
    // assert
    t.deepEquals(result, expected, 'valid result with correct key and parameters')
})

test(' i18n - string with parameters parser - key and parameters are trimmed', (t) => {
    // prepare
    t.plan(1)
    const expected = {
        key: 'i18n.key.scope',
        parameters: ['param1', 'param2'],
        valid: true,
    }
    // act
    const result = parse('  i18n.key.scope  ;  param1 ;  param2   ')
    // assert
    t.deepEquals(result, expected, 'valid result with key and parameters values trimmed')
})

test(' i18n - string with parameters parser - split by semicolon with quotes or double quotes', (t) => {
    // prepare
    t.plan(8)
    const expected = {
        key: 'i18n.key.scope',
        parameters: [
            'param1',
            'param2 with semicolon ; "ok"',
            ' param3; not trimmed  ',
            "param4; 'double quotes'",
            '  param5; double quotes not trimmed '
        ],
        valid: true,
    }
    // act
    const result = parse('i18n.key.scope;param1;\'param2 with semicolon ; "ok"\';\' param3; not trimmed  \';"param4; \'double quotes\'";"  param5; double quotes not trimmed "')
    // assert
    t.ok(result.valid, 'result is valid')
    t.equals(result.key, expected.key, 'result key is equal as expected')
    t.equals(result.parameters.length, expected.parameters.length, 'result has the expected number of parameters')
    t.equals(result.parameters[0], expected.parameters[0], 'first parameter, the normal one, works as expected')
    t.equals(result.parameters[1], expected.parameters[1], 'second parameter, quoted with semicolon, returns params with semicolon')
    t.equals(result.parameters[2], expected.parameters[2], 'third parameter, quoted with semicolon wrapped in spaces, are not trimmed')
    t.equals(result.parameters[3], expected.parameters[3], 'fourth parameter, double quoted with semicolon, returns params with semicolon')
    t.equals(result.parameters[4], expected.parameters[4], 'fifth parameter, double quoted with semicolon wrapped in spaces, are not trimmed')

})


test(' i18n - string with parameters parser - without semicolons parameters are empty ', (t) => {
    // prepare
    t.plan(1)
    const expected = {
        key: 'i18n.key.scope',
        parameters: [],
        valid: true,
    }
    // act
    const result = parse('i18n.key.scope')
    // assert
    t.deepEquals(result, expected, "valid result with empty parameters")
})

test(' i18n - string with parameters parser - spaced without semicolons parameters are empty ', (t) => {
    // prepare
    t.plan(1)
    const expected = {
        key: 'i18n key scope',
        parameters: ["22"],
        valid: true,
    }
    // act
    const result = parse('i18n key scope; 22')
    // assert
    t.deepEquals(result, expected, "valid result with empty parameters")
})

test(' i18n - string with parameters parser - empty string returns empty key with empty parameters', (t) => {
    // prepare
    t.plan(1)
    const expected = {
        key: '',
        parameters: [],
        valid: true,
    }
    // act
    const result = parse('')
    // assert
    t.deepEquals(result, expected, 'valid result with empty key and empty parameters')
})


test(' i18n - string with parameters parser - other type of values string returns empty key with empty parameters and invalid flag', (t) => {
    // prepare
    t.plan(4)
    const expected = {
        key: '',
        parameters: [],
        valid: false,
    }
    const warn = console.warn
    console.warn = () => {}
    // act
    const numericInputResult = parse(1 as any)
    const nullInputResult = parse(null)
    const objectInputResult = parse({} as any)
    const arrayInputResult = parse([] as any)
    // assert
    t.deepEquals(numericInputResult, expected, "numeric input returns invalid result")
    t.deepEquals(nullInputResult, expected, "null input returns invalid result")
    t.deepEquals(objectInputResult, expected, "numeric input returns invalid result")
    t.deepEquals(arrayInputResult, expected, "array input returns invalid result")

    // clean
    console.warn = warn
})
