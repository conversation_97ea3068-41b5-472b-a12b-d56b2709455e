import { instance } from './provider'

export const translate = (message: string, ...args:string[]): string  => instance.translate(message, ...args)
export const filterByPartialKey = (partialMessage: string): string | string[] => instance.filterByPartialKey(partialMessage)
export const tokenize = (message: string, ...args:string[]) => instance.tokenize(message, ...args)

export const i18nfilterByPartialKey = filterByPartialKey
export const i18n = translate


export default translate