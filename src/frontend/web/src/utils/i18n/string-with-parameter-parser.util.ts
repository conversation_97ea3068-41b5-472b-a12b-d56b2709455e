interface ParseResult {
    key: string,
    parameters: string[],
    valid: boolean
}

/**
  regex will match:

     (
        "[^"]*"       double quotes + anything but double quotes + double quotes
        |           OR
        '[^']*'       quote + anything but quote + quote
        |           OR
        [^'";]+    1 or more characters excl. quotes, double quotes or semicolon
    )
    (?=             FOLLOWED BY
        \s*;        0 or more empty spaces and a semicolon
        |           OR
        \s*$        0 or more empty spaces and nothing else (end of string)
    )

 It is used to split a string by unquoted semicolon
*/
const regex = /("[^"]*"|'[^']*'|[^'";]+)(?=\s*;|\s*$)/g

export const parse = (paramText: string) : ParseResult => {
    if(typeof paramText !== "string"){
        console.warn('i18n string with parameter parser: argument must be a string')
        return {
            key: '',
            parameters: [],
            valid: false
        }
    }
    const splitValue = (paramText).match(regex)
    if(splitValue == null){
        // on this block, paramText is empty or has only whitespace characters
        return {
            key: '',
            parameters: [],
            valid: true
        }
    }
    return {
        key: splitValue[0].trim(),
        parameters: splitValue.slice(1).map(str => {
            str = str.trim()
            if((str.startsWith("'") && str.endsWith("'")) || (str.startsWith('"') && str.endsWith('"'))){
                return str.slice(1, -1)
            }
            return str
        }),
        valid: true
    }
}