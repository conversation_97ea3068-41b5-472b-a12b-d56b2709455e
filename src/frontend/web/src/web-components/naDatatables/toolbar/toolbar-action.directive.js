import { modules, providers } from '~basemodule/app/basemodule-providers'

angular.module(modules.datatables).directive(providers.datatables.directives.ToolBarAction, [
  '$timeout',
  '$compile',
  ToolBarActionDirective
])

function ToolBarActionDirective($timeout, $compile) {
  return {
    restrict: 'A',
    scope: true,
    // terminal : true,
    controller: providers.datatables.controllers.ToolBarAction,
    controllerAs: 'toolBarActionCtrl',
    link: function (scope, element, attrs, ctrl) {
      ctrl.getIndexAttr = function () {
        return attrs.index
      }
      ctrl.getEventAttr = function () {
        return attrs.event
      }
      ctrl.getUrlTargetAttr = function () {
        return attrs.urlTarget
      }
      ctrl.getActionTypeAttr = function () {
        return attrs.actionType
      }

      if (attrs.extraAttributes) {
        const moarAttributes = attrs.extraAttributes.split(' ')
        for (let i = 0; i < moarAttributes.length; i++) {
          element.attr(moarAttributes[i], '')
        }
        $timeout(function() {
          element.removeAttr('data-extra-attributes')
          $compile(element)(scope)
        })
      }
    }
  }
}
