import { getDatatable, getApplicationExportLimit } from '~components/naDatatables/datatables.service'
import { modules, providers } from '~basemodule/app/basemodule-providers'

angular.module(modules.datatables).directive(providers.datatables.directives.ToolbarDetailedExport, [
  ToolbarDetailedExportDirective
])

function ToolbarDetailedExportDirective() {
  return {
    restrict: 'A',
    controller: providers.datatables.controllers.ToolbarDetailedExport,
    controllerAs: 'toolbarDetailedExportCtrl',
    link: function (scope, element, attrs, ctrl) {
      ctrl.tableId = attrs.tableId
      const datatable = getDatatable(ctrl.tableId)
      ctrl.datatableApi = datatable.datatableApi
      ctrl.jqueryDatatable = datatable.jQueryDatatable
      ctrl.totalRecords = 0
      ctrl.exportLimit = getApplicationExportLimit()
      ctrl.setTableTotalRecords = function() {
        ctrl.totalRecords = ctrl.datatableApi.page.info().recordsTotal
        if (ctrl.totalRecords > ctrl.exportLimit) { ctrl.sendExportByEmail = true }
      }

      ctrl.exportData = function() {
        console.info(ctrl.selectedFormat + '-' + ctrl.infoToExport)
        $('#' + ctrl.tableId + '_detailedExport_content').hide()
      }
      ctrl.cancel = function() {
        $('#' + ctrl.tableId + '_detailedExport_content').hide()
      }
    }
  }
}
