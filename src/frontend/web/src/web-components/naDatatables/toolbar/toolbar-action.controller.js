import { modules, providers } from '~basemodule/app/basemodule-providers'

angular.module(modules.datatables).controller(providers.datatables.controllers.ToolBarAction, [
  '$scope',
  '$parse',
  ToolBarActionController
])

function ToolBarActionController($scope, $parse) {
  const ctrl = this
  ctrl.disabled = true
  ctrl.getIndexAttr = function () {} // overwritten in directive
  ctrl.getEventAttr = function () {} // overwritten in directive
  ctrl.getUrlTargetAttr = function () {} // overwritten in directive
  ctrl.getActionTypeAttr = function () {} // overwritten in directive
  ctrl.getTableTotalRecords = function() {} // overwritten in directive

  ctrl.action = function () {
    $scope.$emit(ctrl.getEventAttr(), ctrl.getUrlTargetAttr(), ctrl.getActionTypeAttr())
  }

  ctrl.actionPredefined = function(functionName) {
    const parsedFunc = $parse(functionName)
    if (typeof parsedFunc !== 'undefined') {
      parsedFunc($scope)()
    }
  }
}
