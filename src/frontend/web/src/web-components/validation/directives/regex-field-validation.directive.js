import validationModule from '../validation.angular-module'
import { providers } from '~basemodule/app/basemodule-providers'
import { directiveName as fieldNotificationDirectiveName } from './field-notification.directive'

/**
     * @ngdoc directive
     * @name regexError
     * <AUTHOR>
     *
     * @description
     * Handles fields that need to match a specific regular expression.
     *
     * @requires fieldNotification directive
     * @requires "regex" attribute with the regular expression as value
     * @requires notificationMessage attribute with the message to be display in case of error as value
     * @requires ngModel directive
     * @requires id attribute
     *
     * @example
     <div na-portal-field-notification na-portal-input-notification>
     <label>The label</label>
     <input
     data-na-portal-regex-error regex="^(([1-9]?\d|1\d\d|2[0-4]\d|25[0-5]).){3}([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])$"
     notification-message="It should be an IPv4 address"
     data-ng-model="theInput"
     id="theInput">
     </div>
     <!--OR-->
     <div na-portal-field-notification data-na-portal-button-notification>
     <label>The label</label>
     <input data-na-portal-regex-error regex="^(([1-9]?\d|1\d\d|2[0-4]\d|25[0-5]).){3}([1-9]?\d|1\d\d|2[0-4]\d|25[0-5])$"
     notification-message="It should be an IPv4 address"
     data-ng-model="theInput" id="theInput">
     </div>
     */
export const directiveName = providers.validation.directives.RegexError
validationModule.directive(directiveName, ['$timeout', function ($timeout) {
  const validate = function (notification, viewValue, id, regTester, message) {
    if (viewValue.length > 0 && !regTester.test(viewValue)) {
      notification.setMsg(message)
      notification.setState(true, 'error')
      notification.setMessageVisible(true)
      return undefined
    } else {
      notification.setMessageVisible(false)
      notification.setState(false)
      return viewValue
    }
  }

  const link = function (scope, element, attrs, ctrls) {
    const controllers = {}
    controllers.notification = ctrls[0]
    controllers.notification.setMessageVisible(false)

    controllers.ngModel = ctrls[1]

    const regex = attrs.regex
    const regTester = new RegExp(regex)

    $timeout(function () {
      validate(controllers.notification, element.val(), element.attr('id'), regTester, attrs.notificationMessage)
    })

    controllers.ngModel.$parsers.unshift(function (viewValue) {
      if (typeof viewValue === 'undefined') {
        return undefined
      } else {
        const result = validate(controllers.notification, viewValue, element.attr('id'), regTester, attrs.notificationMessage)

        if (result || (result === '')) {
          controllers.ngModel.$setValidity(element.attr('name'), true)
        } else {
          controllers.ngModel.$setValidity(element.attr('name'), false)
        }
        return result
      }
    })
  }

  return {
    require: ['^' + fieldNotificationDirectiveName, 'ngModel'],
    restrict: 'A',
    priority: 50,
    link,
    scope: true
  }
}])
