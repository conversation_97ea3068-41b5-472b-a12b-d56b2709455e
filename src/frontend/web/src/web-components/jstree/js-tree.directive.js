import * as Tree from '~components/jstree/js-tree.service'
import TreeController from '~components/jstree/js-tree.controller'
import { providers } from '~basemodule/app/basemodule-providers'
import { request } from '~utils/proxy'
import module from './jstree.angular-module'
import 'jstree'
import 'fuxi/app/js/vendor/jstree/themes/fuxi/style.css'
export const providerName = providers.jstree.directives.Tree
module.directive(providerName, [
  '$timeout',
  jsTreeDirective
])

function jsTreeDirective ($timeout) {
  return {
    restrict: 'A',
    controller: TreeController,
    link: function (scope, element, attrs, ctrl) {
      ctrl.setInitializeJsTreeWithJSON(function (elem, data, additionalPlugins, additionalPluginConfigurations) {
        const defaultConf = Tree.prepareJSONData(data, additionalPlugins, additionalPluginConfigurations)

        const unselectNodes = function (nodes) {
          const jsTreeData = ctrl.tree.jstree(true)._model.data
          nodes.forEach(function (node) {
            if (node.state && node.state.selected === false) {
              const idOfTargetToDeselect = Object.keys(jsTreeData).find(function (id) {
                return jsTreeData[id].text === node.text
              })
              ctrl.tree.jstree(true).deselect_node(idOfTargetToDeselect)
            }
          })
        }

        ctrl.tree = elem.jstree(defaultConf).bind('select_node.jstree', function (ev, data) {
          ctrl.treeElementClicked(ev, data)
        }).on('loaded.jstree', function () {
          setTimeout(function () { unselectNodes(defaultConf.core.data) })
          ctrl.treeAfterLoad(ctrl.tree)
        })

        ctrl.tree.bind('click.jstree', function () {
          return true
        })

        return ctrl.tree
      })

      ctrl.setInitializeJsTreeWithRest(function (additionalPlugins, additionalPluginConfigurations) {
        if (typeof attrs.treeLoadUrl !== 'undefined') {
          request({
            url: attrs.treeLoadUrl,
            data: {},
            onSuccess: function (data) {
              const defaultConf = Tree.prepareJSONData(data.data, additionalPlugins, additionalPluginConfigurations)

              ctrl.tree = element.jstree(defaultConf, false)
                .on('loaded.jstree', function () {
                  ctrl.treeAfterLoad(element)
                })

              ctrl.tree.bind('click.jstree', function () {
                return false
              })

              $timeout(function () {
                $(ctrl.tree).find('a').each(function () {
                  const $this = $(this)
                  $this.click(function () {
                    ctrl.treeElementClicked(null, $(this))
                  })
                })
              })

              ctrl.onRequestSuccess(ctrl.tree)
            },
            onError: function (data, status) {
              ctrl.onRestRequestError(data, status)
            }
          })
        }
      })
    }
  }
}
