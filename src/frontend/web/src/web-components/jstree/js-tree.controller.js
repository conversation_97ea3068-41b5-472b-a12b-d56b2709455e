export function TreeController () {
  const ref = this

  // expected to be overridden
  ref.treeElementClicked = function (/* ev,data */) {}
  ref.treeAfterLoad = function (/* tree */) {}
  ref.onRequestError = function (/* data,status */) {}
  ref.onRequestSuccess = function (/* tree,data */) {}
  /// //

  ref.initializeJsTreeWithJSON = function (/* element, data, additionalPlugins, additionalPluginConfigurations */) {}
  ref.initializeJsTreeWithRest = function (/* additionalPlugins, additionalPluginConfigurations */) {}
  ref.setInitializeJsTreeWithJSON = function (fn) {
    ref.initializeJsTreeWithJSON = fn
  }
  ref.callInitializeJsTreeWithJSON = function (element, data, additionalPlugins, additionalPluginConfigurations) {
    return ref.initializeJsTreeWithJSON(element, data, additionalPlugins, additionalPluginConfigurations)
  }
  ref.setInitializeJsTreeWithRest = function (fn) {
    ref.initializeJsTreeWithRest = fn
  }
  ref.callInitializeJsTreeWithRest = function (additionalPlugins, additionalPluginConfigurations) {
    return ref.initializeJsTreeWithRest(additionalPlugins, additionalPluginConfigurations)
  }
}

export default TreeController
