import tape from 'tape'
import { elementTagName as tag } from '../breadcrumb.element'
import '~utils/breadcrumb-api/implementation'
import * as breadcrumb from '~utils/breadcrumb-api'

const prefix = 'BreadCrumb Component Container - '

tape.test(prefix + ' renders the path ', (t) => {
  // prepare
  t.plan(2)
  const { body } = window.document

  // act
  body.innerHTML = `
    <${tag}></${tag}>
  `

  breadcrumb.setPath(
    { label: 'Lorem' },
    { label: 'ipsum' },
    { label: 'dolor' },
    { label: 'sit' },
    { label: 'amet' }
  )

  const breadcrumbElements = body.querySelectorAll('li')
  const breadcrumbContent = Array.from(breadcrumbElements).map(element => element.textContent)

  t.isEqual(breadcrumbElements.length, 5)
  t.deepEquals(breadcrumbContent, ['Lorem', 'ipsum', 'dolor', 'sit', 'amet'])

  // clean
  body.innerHTML = ''
})

tape.test(prefix + ' path hyperlinks redirect to correct path', (t) => {
  // prepare
  t.plan(2)
  const { body } = window.document

  // act
  body.innerHTML = `
    <${tag}></${tag}>
  `

  breadcrumb.setPath({
    label: 'Page 1',
    url: 'page1.html'
  }, {
    label: 'Sub Page 1',
    url: 'subpage1-1.html'
  },
  {
    label: 'Sub sub Page 1',
    url: 'subsubpage1-1-1.html'
  },
  {
    label: 'current page',
    url: 'subsubsubpage1-1-1-2.html'

  })

  const breadcrumbElements = body.querySelectorAll('li')
  const breadcrumbHyperlinks = Array.from(breadcrumbElements)
    .map(element => element.querySelector('a')?.href)
    .filter(element => element != null)

  t.isEqual(breadcrumbElements.length, 4)
  t.deepEquals(breadcrumbHyperlinks, ['page1.html', 'subpage1-1.html', 'subsubpage1-1-1.html'])

  // clean
  body.innerHTML = ''
})
