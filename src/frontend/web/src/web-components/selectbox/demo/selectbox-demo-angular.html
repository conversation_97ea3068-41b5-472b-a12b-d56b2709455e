<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title><%= title %></title>
<%= css %></head>
</head>
<body>
    <%= navbar %>
    <div class='container app' id="main-wrapper">

        <h1><%= title %></h1>

        <h3>Dependencies</h3>
        <%= dependencyTable %>

        <h3>Imports</h3>
        <pre><code class="code--import"></code></pre>

        <h3>Basic</h3>


        <select data-na-portal-select-box id="select-box-basic" style="width: 200px">
            <option>Option1</option>
            <option>Option2</option>
        </select>
        <pre><code class="xml">&lt;select data-na-portal-select-box="" id=&quot;select-box-basic&quot;&gt;
    &lt;option&gt;Option1&lt;/option&gt;
    &lt;option&gt;Option2&lt;/option&gt;
&lt;/select&gt;</code></pre>


        <h3>Server side</h3>

        <select data-na-portal-select-box data-is-dynamic data-url="/mock/data/array" id="select-box-server-side-array" style="width: 200px"></select>
        <pre><code class="xml">&lt;select data-na-portal-select-box data-is-dynamic data-url=&quot;/mock/data/array&quot; id=&quot;select-box-server-side-array&quot;&gt;&lt;/select&gt;</code></pre>
        <h4>Example <code>/mock/data/array</code> response</h4>
        <pre><code>[
    {"id": "1", "text": "Option 1"}
    {"id": "2", "text": "Option 2"}
    {"id": "3", "text": "Option 3"}
    // ...
    {"id": "98", "text": "Option 98"}
    {"id": "99", "text": "Option 99"}
    {"id": "100", "text": "Option 100"}
]</code></pre>

        <select data-na-portal-select-box data-is-dynamic data-url="/mock/data/object" id="select-box-server-side-object" style="width: 200px"></select>
        <pre><code class="xml">&lt;select data-na-portal-select-box data-is-dynamic data-url=&quot;/mock/data/object&quot; id=&quot;select-box-server-side-object&quot;&gt;&lt;/select&gt;</code></pre>
        <h4>Example <code>/mock/data/object</code> response</h4>
        <pre><code>{
    "entries": [
        {"id": "1", "text": "Option 1"}
        {"id": "2", "text": "Option 2"}
        {"id": "3", "text": "Option 3"}
        // ...
        {"id": "98", "text": "Option 98"}
        {"id": "99", "text": "Option 99"}
        {"id": "100", "text": "Option 100"}
    ]
}</code></pre>

        <h3>Pagination</h3>

        <select data-na-portal-select-box
                data-placeholder="Pagination Example"
                data-is-dynamic data-url="/mock/data/pagination"
                id="select-box-pagination" style="width: 200px"></select>
        <pre><code class="xml">&lt;select data-na-portal-select-box
    data-placeholder=&quot;Pagination Example&quot;
    data-is-dynamic data-url=&quot;/mock/data/pagination&quot;&gt;&lt;/select&gt;</code></pre>
        <h4>Example <code>/mock/data/pagination</code> response</h4>
        <pre><code>{
    "count": 100,
    "entries": [
        {"id": "1", "text": "Option 1"}
        {"id": "2", "text": "Option 2"}
        {"id": "3", "text": "Option 3"}
        // ...
        {"id": "18", "text": "Option 18"}
        {"id": "19", "text": "Option 19"}
        {"id": "20", "text": "Option 20"}
    ]
}</code></pre>
    </div>
<%= js %></body>
</html>