import selectBoxModule from './selectbox.angular-module'
import { providers } from '~basemodule/app/basemodule-providers'
import { select2ApiFromElement } from './select2Api.service'
import { selectBoxController } from './selectbox.controller'
import i18n from '../../utils/i18n'
import { cacheSelect2Options, getTextFromValue } from './selectbox-options-cache'
import * as $ from 'jquery'
import 'select2/dist/js/select2.full'
import { htmlEscape } from '~utils/string.util'

/**
 * @ngdoc directive
 * @name naPortalSelectBox
 * @restrict A
 * @description
 * Directive that initializes a selectbox on the element.
 * @element Input
 * @example
 * <div data-na-portal-select-box></div>
 **/
export const directiveName = providers.selectbox.directives.SelectBox
selectBoxModule.directive(directiveName, [selectBoxDirective])

function selectBoxDirective () {
  return {
    restrict: 'A',
    priority: 0,
    terminal: false,
    controller: selectBoxController,
    controllerAs: 'selectboxCtrl',
    require: ['?ngModel', directiveName, 'select'],
    scope: true,
    compile: function (cElem, attrs) {
      /*
             * Init Settings
             */

      // defaults
      const settings = {
        id: null,
        ngModel: null,
        isDynamic: false,
        hasDefaultValue: false,
        unlocksSelect: false,
        context: null,
        defaultValue: null,
        disable: false,
        scopeTarget: null,

        // select2 options
        options: {
          width: getDefinedWidth(cElem[0]),
          allowClear: false,
          placeholder: i18n('na.selectbox.placeholder'),
          theme: 'bootstrap',
          language: {
            noResults: function () {
              return i18n('na.input.nomatches')
            }
          }
        }
      }

      if (attrs.allowClear != null) {
        settings.options.allowClear = true
      }

      if (attrs.selectWidth != null && attrs.selectWidth !== '') {
        settings.options.width = attrs.selectWidth
      }

      if (attrs.context != null && attrs.context !== '') {
        settings.context = attrs.context
      }

      if (attrs.scopeTarget != null && attrs.scopeTarget !== '') {
        settings.scopeTarget = attrs.scopeTarget
      }

      if (attrs.unlocksSelect != null && attrs.unlocksSelect !== '') {
        settings.unlocksSelect = attrs.unlocksSelect
      }

      if (attrs.disableSelect != null) {
        settings.disable = true
      }

      if (attrs.ngModel != null && attrs.ngModel !== '') {
        settings.ngModel = attrs.ngModel
      }

      if (attrs.value != null && attrs.value !== '') {
        settings.hasDefaultValue = true
        settings.defaultValue = attrs.value
      }

      if (attrs.allowClear != null && attrs.allowClear === 'true') {
        settings.options.allowClear = true
      }

      if (attrs.placeholder != null && attrs.placeholder !== '') {
        settings.options.placeholder = attrs.placeholder // already internationalized
      }

      if (attrs.id != null) {
        settings.id = attrs.id
      } else {
        throw new Error('Invalid arguments for selectbox: "id" is undefined')
      }

      /*
             Configure select tag with attribute "data-show-icons" to show icons in all options configured with
             attribute 'data-icon="icon class here"'
             (ex: <option data-icon="glyphicon glyphicon-user" value="">...<option> )
             */
      if (attrs.showIcons != null) {
        const format = function (state) {
          const optionText = state.text

          if (!state.id) {
            return optionText // optgroup
          }

          const iconClass = $(state.element).data('icon')
          if (iconClass != null) {
            return $('<span>').append($('<i>', {
              class: iconClass
            })).append(' ' + htmlEscape(optionText))
          }

          return optionText
        }

        Object.assign(settings.options, {
          templateResult: format,
          templateSelection: format
        })
      }

      cElem.one('change', function (event, data) {
        if (data == null) {
          return
        }
        if (cElem.find('option').filter(function () {
          return $(this).attr('value') !== ''
        }).length <= 0) {
          const item = data
          cElem.append("<option value='" + item + "' selected>" + htmlEscape(getTextFromValue(attrs.url, item)) + '</option>')
          cElem.trigger('change')
        }
      })

      return function (scope, element, attrs, [ngModelController, selectboxCtrl, buggedAngularSelectCtrl]) {
        const select2Element = select2ApiFromElement(element)
        /*
          In angular 1.8.x in nossis ui 3, different from nossis ui 2 (v1.4.y), this controller
        method has additional checks to verify if an option applying the value, if option does not
        exist, it sets the value to null.

          The problem is that the way it checks if the option exists, is if it's in a Javascript
        object saved in-memory instead of checking the DOM. They don't match, causing a lot of
        unexpected errors.

           This next change reverts the readValue() logic to the one defined in angular (1.4)
         */
        buggedAngularSelectCtrl.readValue = function readSingleValue() {
          return element.val()
        }

        buggedAngularSelectCtrl.writeValue = function writeSingleValue(value) {
          element.val(value)
        }

        if ((selectboxCtrl.isQuerySet() || attrs.isDynamic != null) && attrs.url != null) {
          settings.isDynamic = true

          // Define Ajax settings
          Object.assign(settings.options, {
            ajax: {
              url: attrs.url,
              dataType: 'json',
              data: function (params) {
                return selectboxCtrl.getAjaxData({
                  q: params.term,
                  page: params.page || 1
                })
              },
              transport: (params, success, failure) => selectboxCtrl.query(params, success, failure),
              processResults: function (data) {
                if (Array.isArray(data)) {
                  cacheSelect2Options(attrs.url, data)
                  return { results: data }
                } else if (typeof data.count !== 'number') {
                  cacheSelect2Options(attrs.url, data.entries)
                  return { results: data.entries }
                } else {
                  cacheSelect2Options(attrs.url, data.entries)
                  return {
                    results: data.entries,
                    pagination: {
                      more: data.entries.length > 0 && data.entries.length !== data.count
                    }
                  }
                }
              }
            }
          })
        }

        if (settings.ngModel != null) {
          const watchFunction = function () {
            const result = scope.$eval(settings.ngModel)
            // null and undefined should not trigger a change
            return result != null ? result : null
          }

          scope.$watch(watchFunction, function (newValue) {
            select2Element.setValue(newValue || null)
          })
        }

        if (settings.disable) {
          select2Element.disable()
        }

        scope.$on('SelectBox::SetValue', function (event, params) {
          if (params && Object.prototype.hasOwnProperty.call(params, 'id') && settings.id === params.id && element.data('select2')) {
            select2Element.setValue(params.value)
          }
        })

        element.on('select2:unselect', function () {
          const select2 = element.data('select2')

          // update title with placeholder
          $('.select2-selection__rendered', select2.$container).attr('title', settings.options.placeholder)

          scope.$emit('SelectBox::Unselect', {
            value: null,
            text: '',
            selectId: settings.id,
            context: settings.context,
            unlock: settings.unlocksSelect,
            scopeTarget: settings.scopeTarget
          })

          // hack: prevents select2 to open the dropdown on deselect
          select2.options.set('disabled', true)
          setTimeout(function () {
            select2.options.set('disabled', false)
          }, 0)
        })

        scope.$on('SelectBox::Disable', function (event, params) {
          if (params && Object.prototype.hasOwnProperty.call(params, 'id') && settings.id === params.id && element.data('select2')) {
            select2Element.disable()
          }
        })

        scope.$on('SelectBox::Clean', function (event, id) {
          select2Element.cleanIf(id === settings.id)
        })

        scope.$on('SelectBox::cleanAll', function () {
          select2Element.clean()
        })

        scope.$on('SelectBox::Unlock', function (event, id) {
          if (id === settings.id) {
            select2Element.enable()
          }
        })

        scope.$on('SelectBox::LoadData', function (event, params) {
          if (params.id === settings.id) {
            selectboxCtrl.getInitializedSelect2(function () {
              const selectedChoice = params.selectedChoice
              select2Element.setData(params.data)
              select2Element.setValue(selectedChoice != null ? selectedChoice : null)
            })
          }
        })

        element.on('change', function (event, newValueIfDefined) {
          if (!select2Element.isInitialized) {
            return
          }
          let choice = newValueIfDefined !== undefined ? newValueIfDefined : element.valueOf().val()
          if (choice === '') {
            choice = null
          }
          const choiceData = select2Element.getSingleDataOrElse(null)
          const text = choiceData != null && choiceData.id != null ? choiceData.text.trim() : null

          scope.$emit('SelectBox::SelectedOption', {
            value: choice,
            text,
            selectId: settings.id,
            context: settings.context,
            unlock: settings.unlocksSelect,
            scopeTarget: settings.scopeTarget
          })
        })

        if (settings.ngModel != null) {
          // format text going to user (model to view)
          ngModelController.$formatters.push(function (value) {
            return (value == null || value === '') ? null : value.toString()
          })

          // format text from the user (view to model)
          ngModelController.$parsers.push(function (value) {
            return value === '' ? null : value
          })
        }

        /*

                 In the previous version of select2 (version 3.4), this class is applied automatically on the button,
                 and the F.U.X.I. framework applies CSS rules to the element with the selector that supposedly is the
                 button, and the rules applied are related to the dimensions (set width of the button).

                 In the version 4.0 the class is the same, however, the width of the button applied is directly
                 based on width of the container (<select> element), so the rules applied to the button are being
                 overridden by the internal implementation of the Select2 v4.0, that, during initialization, applies
                 an inline CSS that defines the width of the select box.
                 In short, button width equals container width.

                 The solution is to apply the class in the container.

                 */

        element.addClass('select2-container')

        window.requestAnimationFrame(function () {
          select2Element.initialize(settings.options)
          const initSelect2Api = {
            select2Api: select2Element,
            settings,
            element
          }
          selectboxCtrl.notifySelect2Initialized(initSelect2Api)
          scope.$emit('SelectBox::initialized', initSelect2Api)
        })

        if (settings.hasDefaultValue && settings.ngModel) {
          scope.$eval(settings.ngModel + '= "' + settings.defaultValue + '"')
        }

        scope.$on('$destroy', () => {
          if (element.data('select2') || element.data('select2Id')) {
            element.select2('destroy')

            const utils = jQuery.fn.select2.amd.require('select2/utils')
            utils.RemoveData($(element)[0])
          }
        })
      }
    }
  }
}

const getDefinedWidth = (element) => {
  if (element.hasAttribute('full-width')) {
    return '100%'
  }
  if (element.hasAttribute('width')) {
    return element.getAttribute('width')
  }
  const { display } = element.style
  element.style.display = 'none'
  const { width } = getComputedStyle(element)
  element.style.display = display
  return width
}
