import inputsModule from './inputs.angular-module'
import { providers } from '~basemodule/app/basemodule-providers'
import i18n from '~utils/i18n'
inputsModule.directive(providers.inputs.directives.toggleShowPasswordButton, [
  showPasswordButtonDirective
])

function showPasswordButtonDirective() {
  return {
    link: function (scope, element, attributes) {
      const icon = element.children('i')
      element.click(function () {
        const inputTarget = $(document.getElementById(attributes.target))
        const targetType = inputTarget.attr('type')
        if (targetType === 'text') {
          inputTarget.attr('type', 'password')
          element.attr('title', i18n('na.button.label.passwordShow'))
          icon.removeClass('fuxicons-eye-slash')
          icon.addClass('fuxicons-eye')
        } else {
          inputTarget.attr('type', 'text')
          icon.removeClass('fuxicons-eye')
          icon.addClass('fuxicons-eye-slash')
          element.attr('title', i18n('na.button.label.passwordHide'))
        }
      })
    }
  }
}
