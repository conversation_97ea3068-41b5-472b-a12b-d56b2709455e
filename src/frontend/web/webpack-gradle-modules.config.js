const path = require('path')
const webpack = require('webpack')
const shell = require('shelljs')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const CompressionPlugin = require('compression-webpack-plugin')
const configParts = require('./webpack.config.parts.js')

module.exports = env => {
  const mode = getMode(env)
  const outputDir = typeof env.outputDir === 'string' ? env.outputDir : 'dist'

  return {
    mode,
    entry: {
      'basemodule/resume': './src/resume-scripts.js',
      'operations-catalog/operations-catalog': [
        './src/modules/operations-catalog/index.js',
        './src/modules/operations-catalog/styles.css'
      ],
      'reference-data/reference-data': [
        './src/modules/reference-data/index.js',
        './src/modules/reference-data/styles.css'
      ],
      'monitoring/monitoring': [
        './src/modules/monitoring/index.js'
      ],
      'go/go': [
        './src/modules/go/index.js',
        './src/modules/go/styles.css'
      ],
      'mpt/mpt': [
        './src/modules/mpt/index.js',
        './src/modules/mpt/styles.css'
      ],
      'nadm/nadm': [
        './src/modules/nadm/index.js',
        './src/modules/nadm/styles.css'
      ],
    },
    output: {
      path: path.resolve(__dirname, outputDir),
      filename: outputFilenameNameByMode[mode],
      chunkFilename: (pathData) => {
        const { chunk } = pathData
        /* web worker generated chunk does not have a name (they are undefined) */
        if (!chunk.name) {
          return 'web-components/worker-chunk-' + outputFilenameNameByMode[mode]
        }
        return outputFilenameNameByMode[mode]
      }
    },
    module: {
      rules: [
        ...configParts.module.rules,
        {
          test: { or: [/\.(element|template|directive)\.html$/, /\/implementation\.html$/] },
          include: function (resourcePath) {
            return resourcePath.includes('/node_modules/@alticelabsprojects/nossis-orchestration-web-components'.replace(/\//g, path.sep)) ||
                (!resourcePath.includes('/node_modules/') && !resourcePath.includes('/bower_components/'))
          },
          use: {
            loader: 'raw-loader',
          }
        }, {
          test: /\.css$/,
          exclude: /\.(raw|inline)\.css$/,
          use: [
            { loader: 'css-loader', options: { sourceMap: true } }
          ]
        }, {
          test: /\.(raw|inline)\.css$/,
          include: function (resourcePath) {
            return resourcePath.includes('/node_modules/@alticelabsprojects/nossis-orchestration-web-components'.replace(/\//g, path.sep)) ||
                (!resourcePath.includes('/node_modules/') && !resourcePath.includes('/bower_components/'))
          },
          use: {
            loader: 'css-loader',
          }
        }, {
          test: /\.s[ac]ss$/,
          exclude: /\.(raw|inline)\.s[ac]ss$/,
          use: [
            { loader: 'css-loader', options: { sourceMap: true, importLoaders: 1 } },
            'sass-loader'
          ]
        },
        {
          test: /\.(raw|inline)\.s[ac]ss$/,
          use: [
            { loader: 'css-loader', options: { sourceMap: true, importLoaders: 1 } },
            'sass-loader'
          ]
        }, {
          test: /\.(woff|woff2|eot|ttf|otf|wav|mp3|png|jpg|jpeg|gif|svg)(\?v=\d+\.\d+\.\d+)?$/,
          include: /node_modules/,
          loader: 'file-loader',
          options: {
            outputPath: 'na-portal-assets-vendors/vendors-assets',
            publicPath: 'vendors-assets'
          }
        }, {
          test: /\.(png|jpg|jpeg|gif|svg)$/,
          include: /src\/modules\/monitoring/,
          exclude: /node_modules/,
          loader: 'file-loader',
          options: {
            name: '[name].[ext]',
            outputPath: 'na-ext/images',
            publicPath: '/na-ext/images'
          }
        }
      ]
    },
    resolve: configParts.resolve,
    optimization: {
      ...configParts.optimization,
      splitChunks: {
        // include all types of chunks
        cacheGroups: {
          ...configParts.optimization.splitChunks.cacheGroups,
          // common chunk
          vendors: {
            test: /[\\/]node_modules[\\/]/,
            name: 'na-portal-assets-vendors/common',
            priority: 10,
            chunks: 'all',
            enforce: true // https://github.com/webpack-contrib/mini-css-extract-plugin/issues/257#issuecomment-432594711
          },

          'catalog-basemodule': {
            test: /[\\/]modules[\\/]catalog-basemodule[\\/]/,
            name: 'catalog-basemodule/catalog-basemodule',
            chunks: 'all',
            enforce: true // https://github.com/webpack-contrib/mini-css-extract-plugin/issues/257#issuecomment-432594711
          },

          // used specifically for resume scripts
          'utils-module-navigation': {
            test: /[\\/]utils[\\/]module-navigation[\\/]/,
            name: 'basemodule/utils-module-navigation',
            chunks: 'all',
            enforce: true // https://github.com/webpack-contrib/mini-css-extract-plugin/issues/257#issuecomment-432594711
          },

          'web-components': {
            test: /[\\/]src[\\/]web-components[\\/]/,
            name(module) {
              const moduleFileName = module.identifier().split('/').reduceRight((item) => item)
              const chunkName = moduleFileName.split('.')[0]
              return `web-components/async-${chunkName}`
            },
            chunks: 'async',
            priority: 2,
            enforce: true // https://github.com/webpack-contrib/mini-css-extract-plugin/issues/257#issuecomment-432594711
          },

          utils: {
            test: /[\\/]src[\\/]utils[\\/]/,
            name: 'utils/utils',
            chunks: 'all',
            priority: 2,
            enforce: true // https://github.com/webpack-contrib/mini-css-extract-plugin/issues/257#issuecomment-432594711
          },

          basemodule: {
            test: /[\\/]src[\\/]modules[\\/]basemodule[\\/]/,
            name: 'basemodule/basemodule',
            chunks: 'all',
            enforce: true // https://github.com/webpack-contrib/mini-css-extract-plugin/issues/257#issuecomment-432594711
          },

        }
      }
    },

    plugins: [
      // basemodule
      scriptsScalaHtmlGeneration({
        chunks: ['basemodule/resume'],
        template: '../play/basemodule/app/na/naportalbase/views/imports/resumescripts.scala.html',
        filename: 'play-resumescripts-basemodule.html'
      }),

      // operations-catalog
      scriptsScalaHtmlGeneration({
        chunks: ['operations-catalog/operations-catalog'],
        template: '../play/operations-catalog/app/na/operationscatalog/views/imports/scripts.scala.html',
        filename: 'play-scripts-operations-catalog.html'
      }),

      // reference-data
      scriptsScalaHtmlGeneration({
        chunks: ['reference-data/reference-data'],
        template: '../play/reference-data/app/na/reference/data/views/imports/scripts.scala.html',
        filename: 'play-scripts-reference-data.html'
      }),

      // monitoring
      scriptsScalaHtmlGeneration({
        chunks: ['monitoring/monitoring'],
        template: '../play/monitoring/app/na/monitoring/views/imports/scripts.scala.html',
        filename: 'play-scripts-monitoring.html'
      }),

      // go
      scriptsScalaHtmlGeneration({
        chunks: ['go/go'],
        template: '../play/go/app/na/go/views/imports/scripts.scala.html',
        filename: 'play-scripts-go.html'
      }),

      // mpt
      scriptsScalaHtmlGeneration({
        chunks: ['mpt/mpt'],
        template: '../play/mpt/app/na/mpt/views/imports/scripts.scala.html',
        filename: 'play-scripts-mpt.html'
      }),

      // nadm
      scriptsScalaHtmlGeneration({
        chunks: ['nadm/nadm'],
        template: '../play/nadm/app/na/nadm/views/imports/scripts.scala.html',
        filename: 'play-scripts-nadm.html'
      }),

      new webpack.ProvidePlugin({
        $: 'jquery',
        jQuery: 'jquery',
        'window.jQuery': 'jquery'
      }),
      webPackHooksPlugin({
        beforeRun: () => {
          shell.rm('-rf', outputDir)
        },
        AfterRun: () => {
          shell.mkdir('-p', `${outputDir}/basemodule/app/na/naportalbase/views/imports`)
          shell.mv(`${outputDir}/play-resumescripts-basemodule.html`, `${outputDir}/basemodule/app/na/naportalbase/views/imports/resumescripts.scala.html`)
          shell.mv(`${outputDir}/play-resumecss-basemodule.html`, `${outputDir}/basemodule/app/na/naportalbase/views/imports/resumecss.scala.html`)

          shell.mkdir('-p', `${outputDir}/portal/app/na/portal/views/imports`)
          shell.mv(`${outputDir}/play-resumecss-portal.html`, `${outputDir}/portal/app/na/portal/views/imports/resumecss.scala.html`)

          shell.mkdir('-p', `${outputDir}/operations-catalog/app/na/operationscatalog/views/imports`)
          shell.mv(`${outputDir}/play-scripts-operations-catalog.html`, `${outputDir}/operations-catalog/app/na/operationscatalog/views/imports/scripts.scala.html`)
          shell.mv(`${outputDir}/play-css-operations-catalog.html`, `${outputDir}/operations-catalog/app/na/operationscatalog/views/imports/css.scala.html`)

          shell.mkdir('-p', `${outputDir}/reference-data/app/na/reference/data/views/imports`)
          shell.mv(`${outputDir}/play-scripts-reference-data.html`, `${outputDir}/reference-data/app/na/reference/data/views/imports/scripts.scala.html`)
          shell.mv(`${outputDir}/play-css-reference-data.html`, `${outputDir}/reference-data/app/na/reference/data/views/imports/css.scala.html`)

          shell.mkdir('-p', `${outputDir}/monitoring/app/na/monitoring/views/imports`)
          shell.mv(`${outputDir}/play-scripts-monitoring.html`, `${outputDir}/monitoring/app/na/monitoring/views/imports/scripts.scala.html`)
          shell.mv(`${outputDir}/play-css-monitoring.html`, `${outputDir}/monitoring/app/na/monitoring/views/imports/css.scala.html`)

          shell.mkdir('-p', `${outputDir}/go/app/na/go/views/imports`)
          shell.mv(`${outputDir}/play-scripts-go.html`, `${outputDir}/go/app/na/go/views/imports/scripts.scala.html`)
          shell.mv(`${outputDir}/play-css-go.html`, `${outputDir}/go/app/na/go/views/imports/css.scala.html`)

          shell.mkdir('-p', `${outputDir}/mpt/app/na/mpt/views/imports`)
          shell.mv(`${outputDir}/play-scripts-mpt.html`, `${outputDir}/mpt/app/na/mpt/views/imports/scripts.scala.html`)
          shell.mv(`${outputDir}/play-css-mpt.html`, `${outputDir}/mpt/app/na/mpt/views/imports/css.scala.html`)

          shell.mkdir('-p', `${outputDir}/nadm/app/na/nadm/views/imports`)
          shell.mv(`${outputDir}/play-scripts-nadm.html`, `${outputDir}/nadm/app/na/nadm/views/imports/scripts.scala.html`)
          shell.mv(`${outputDir}/play-css-nadm.html`, `${outputDir}/nadm/app/na/nadm/views/imports/css.scala.html`)
        }
      }),
    ].concat(additionPluginsByMode[mode]),

    performance: {
      hints: mode === PRODUCTION ? 'warning' : false,
      maxEntrypointSize: 1024000
    }
  }
}

// constants
const DEVELOPMENT = 'development'
const PRODUCTION = 'production'

const outputFilenameNameByMode = {
  [DEVELOPMENT]: '[name].js',
  [PRODUCTION]: '[name].[contenthash].min.js'
}

const additionPluginsByMode = {
  [DEVELOPMENT]: [],
  // CompressionPlugin is most likely not needed
  [PRODUCTION]: [new CompressionPlugin({ test: /\.js(\?.*)?$/i })]
}

/*
 * Guarantees that the result is either "development" or "production"
 */
function getMode({ NODE_ENV }) {
  return typeof NODE_ENV === 'string' &&
    NODE_ENV.toLowerCase() === PRODUCTION
    ? PRODUCTION : DEVELOPMENT
}

/*
 * The HtmlWebpackPlugin is wrapped in this function to simplify the configuration
 * of the "imports/script.scala.html" file, the generated file is created on the
 * webpack output directory, which later is copied to its respective location
 */
function scriptsScalaHtmlGeneration({ chunks, template, filename }) {
  return new HtmlWebpackPlugin({
    inject: false,
    templateParameters: (compilation, assets, assetTags, options) => {
      if (!Array.isArray(assets.css) || assets.css.length <= 0) {
        return { assets }
      }

      const { css } = assets

      const cssOrder = [
        'na-portal-assets-vendors/bootstrap',
        'na-portal-assets-vendors/vendors-basepack'
      ]

      const orderedCss = [
        ...css.filter(cssFile => cssFile.includes(cssOrder[0])),
        ...css.filter(cssFile => cssFile.includes(cssOrder[1])),
        ...css.filter(cssFile => cssOrder.every(file => !cssFile.includes(file)))
      ]

      return {
        assets: {
          ...assets,
          css: orderedCss
        },
      }
    },
    template,
    chunks,
    filename
  })
}

/*
 * Small plugin to add hooks to webpack compilation process.
 * Used to execute the hooks every time the build is run during watch mode
 */
function webPackHooksPlugin({ beforeRun, AfterRun }) {
  return {
    apply: (compiler) => {
      compiler.hooks.beforeRun.tap('beforeRunPlugin', beforeRun)
      compiler.hooks.afterEmit.tap('AfterEmitPlugin', AfterRun)
    }
  }
}
