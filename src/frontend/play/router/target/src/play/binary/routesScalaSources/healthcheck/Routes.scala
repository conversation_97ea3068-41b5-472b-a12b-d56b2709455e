// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/router/conf/healthcheck.routes
// @DATE:Tue Jul 01 11:21:08 WEST 2025

package healthcheck

import play.core.routing._
import play.core.routing.HandlerInvokerFactory._

import play.api.mvc._

import _root_.controllers.Assets.Asset

class Routes(
  override val errorHandler: play.api.http.HttpErrorHandler, 
  // @LINE:5
  RouterController_0: na.router.controllers.RouterController,
  val prefix: String
) extends GeneratedRouter {

   @javax.inject.Inject()
   def this(errorHandler: play.api.http.HttpErrorHandler,
    // @LINE:5
    RouterController_0: na.router.controllers.RouterController
  ) = this(errorHand<PERSON>, RouterController_0, "/")

  def withPrefix(prefix: String): Routes = {
    healthcheck.RoutesPrefix.setPrefix(prefix)
    new Routes(errorHandler, RouterController_0, prefix)
  }

  private[this] val defaultPrefix: String = {
    if (this.prefix.endsWith("/")) "" else "/"
  }

  def documentation = List(
    ("""GET""", this.prefix, """na.router.controllers.RouterController.healthCheck()"""),
    Nil
  ).foldLeft(List.empty[(String,String,String)]) { (s,e) => e.asInstanceOf[Any] match {
    case r @ (_,_,_) => s :+ r.asInstanceOf[(String,String,String)]
    case l => s ++ l.asInstanceOf[List[(String,String,String)]]
  }}


  // @LINE:5
  private[this] lazy val na_router_controllers_RouterController_healthCheck0_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix)))
  )
  private[this] lazy val na_router_controllers_RouterController_healthCheck0_invoker = createInvoker(
    RouterController_0.healthCheck(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "healthcheck",
      "na.router.controllers.RouterController",
      "healthCheck",
      Nil,
      "GET",
      this.prefix + """""",
      """""",
      Seq()
    )
  )


  def routes: PartialFunction[RequestHeader, Handler] = {
  
    // @LINE:5
    case na_router_controllers_RouterController_healthCheck0_route(params@_) =>
      call { 
        na_router_controllers_RouterController_healthCheck0_invoker.call(RouterController_0.healthCheck())
      }
  }
}
