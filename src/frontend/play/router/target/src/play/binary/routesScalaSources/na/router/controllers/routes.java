// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/router/conf/healthcheck.routes
// @DATE:Tue Jul 01 18:11:33 WEST 2025

package na.router.controllers;

import healthcheck.RoutesPrefix;

public class routes {
  
  public static final na.router.controllers.ReverseRouterController RouterController = new na.router.controllers.ReverseRouterController(RoutesPrefix.byNamePrefix());

  public static class javascript {
    
    public static final na.router.controllers.javascript.ReverseRouterController RouterController = new na.router.controllers.javascript.ReverseRouterController(RoutesPrefix.byNamePrefix());
  }

}
