package na.nadm.services;

import com.fasterxml.jackson.databind.JsonNode;
import na.go.models.adapters.TemplateAdapter;
import na.nadm.models.TemplateWrapper;
import na.nadm.settings.NadmSettings;
import na.naportalbase.services.ServicesHelper;
import na.naportalbase.services.ws.RequestBuilderFactory;
import na.naportalbase.utils.ODataBuilder;
import na.naportalbase.utils.ODataQueryParametersBuilder;
import na.naportalbase.utils.SecurityUtils;
import play.libs.Json;
import pt.ptinovacao.na.portal.webui.restful.go.entities.CreateOperationContent;
import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping;
import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Operation;

import javax.inject.Inject;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class NadmOperationsService {

    private final ServicesHelper ws;

    private final NadmSettings settings;
    private final RequestBuilderFactory requestBuilderFactory;

    @Inject
    public NadmOperationsService(ServicesHelper ws,
                                 NadmSettings settings,
                                 RequestBuilderFactory requestBuilderFactory) {
        this.ws = ws;
        this.settings = settings;
        this.requestBuilderFactory = requestBuilderFactory;
    }


    public static List<Mapping> getOptionalTemplateMappings(List<Mapping> templateMappings) {
        return templateMappings.stream().filter(mapping -> !mapping.getComponent().getRequired()).collect(Collectors.toList());
    }

    /**
     * Get all active operations on GO.
     *
     * @return operations map (id -> name)
     */
    public CompletionStage<Map<String, String>> getOperations(String context) {
        return getOperations(context, settings.getFamily(context),settings.getRfs(context));
    }


    public CompletionStage<Map<String, String>> getOperations(String context, String family, String rfs){
        if(isUserForbidenToExecuteAnyExternalAction(context)){
            return CompletableFuture.completedFuture(new HashMap<>());
        }
        ODataBuilder oDataBuilder = new ODataBuilder();
        oDataBuilder.addTextField("familyName", family, na.naportalbase.utils.ODataBuilder.FilterOperation.EQUAL);
        oDataBuilder.addTextField("rfsName", rfs, na.naportalbase.utils.ODataBuilder.FilterOperation.EQUAL);
        return ws.sendGet(requestBuilderFactory.create()
                .setUrl(settings.getTemplatesUrl(context))
                .addQueryParameters(ODataQueryParametersBuilder.get().filter(oDataBuilder).select("definition").build())
                .responseAs(TemplateWrapper.class)
                .build()
        ).thenApply(templates -> {
            Map<String, String> operations = new HashMap<>();
            templates.getEntries().forEach(template -> {
                Operation definition = template.getDefinition();
                if (Boolean.FALSE.equals(definition.getAttention())) {
                    operations.put(definition.getId().toString(), definition.getName());
                }
            });
            return operations;
        });
    }


    public CompletionStage<Operation> getOperationTemplate(String context, String operationId) {
        String urlService = settings.getTemplateByIdUrl(context, operationId);
        return ws.sendGet(requestBuilderFactory.create()
                        .setUrl(urlService)
                        .addQueryParameter("template-id", operationId)
                        .responseAs(Operation.class)
                        .build())
                .thenApply(NadmOperationsService::cleanTemplateMappings);
    }

    public boolean isUserForbidenToExecuteAnyExternalAction(String context){
        return !settings.getIsActiveMptGoIntegration(context) ||
                !Authorization.isAuthorizedToExecuteAnyExternalAction(context);
    }

    private CompletionStage<JsonNode> executeOperation(String context, CreateOperationContent createOperation) {
        createOperation.setFamily(settings.getFamily(context));
        createOperation.setRfs(settings.getRfs(context));
        createOperation.setUsername(SecurityUtils.getUsername());
        return ws.sendPost(requestBuilderFactory.create()
                .setUrl(settings.getOperationUrl(context))
                .setPayloadAsJson(Json.toJson(createOperation))
                .build());
    }

    public CompletionStage<JsonNode> executeIndividualOperation(String context, CreateOperationContent createOperation) {
        return executeOperation(context, createOperation)
                .thenApply((jsonNode -> {
                    final String id = jsonNode.get("operationId").asText();
                    final List<String> stringList = Stream.of(settings.getIndividualOperationLink(context).split("/")).map(pathSection -> {
                        if (":id".equals(pathSection)) {
                            return id;
                        }
                        return pathSection;
                    }).collect(Collectors.toList());
                    final String operationLink = String.join("/", stringList);
                    return Json.newObject()
                            .put("status", "ok")
                            .put("url", operationLink);
                }));
    }

    private static Operation cleanTemplateMappings(Operation template){
        List<Mapping> mappings = new LinkedList<>();
        template.getMapping().stream()
                .sorted(Comparator.comparing(Mapping::getPosition))
                .forEach(mapping -> {
                    mapping.setComponent(TemplateAdapter.parseComponent(mapping.getComponent()));
                    TemplateAdapter.adaptDefaultValue(mapping);
                    mappings.add(mapping);
                });

        template.setMapping(mappings);
        return template;
    }
}
