package na.nadm.services;

import akka.japi.function.Creator;
import akka.stream.javadsl.Source;
import akka.util.ByteString;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Maps;
import na.nadm.models.FamilyWrapper;
import na.nadm.models.MassiveEquipmentSelectionData;
import na.nadm.models.MassiveOperationExecData;
import na.nadm.models.OperationHeadersWrapper;
import na.nadm.models.OperationWrapper;
import na.nadm.settings.NadmSettings;
import na.nadm.utils.AllEquipmentRecordsIterableFactory;
import na.nadm.utils.NadmBackendRestAPI;
import na.naportalbase.exceptions.ExceptionCodes;
import na.naportalbase.services.ServicesHelper;
import na.naportalbase.services.ws.RequestBuilderFactory;
import na.naportalbase.utils.ODataBuilder;
import na.naportalbase.utils.ODataQueryParametersBuilder;
import na.naportalbase.utils.SecurityUtils;
import play.libs.Json;
import play.libs.concurrent.HttpExecutionContext;
import play.mvc.Http;
import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.entities.Request;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.model.OperationEntry;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.model.OperationHeaderEntry;
import pt.ptinovacao.nossis.exceptions.BusinessException;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class NadmBulkOperationsService {

    private final NadmOperationsService operationService;

    private final NadmBackendRestAPI restAPI;

    private final ServicesHelper ws;

    private final NadmSettings settings;

    private final HttpExecutionContext ec;

    private final AllEquipmentRecordsIterableFactory allEquipmentIterableFactory;
    private final RequestBuilderFactory requestBuilderFactory;

    @Inject
    public NadmBulkOperationsService(NadmOperationsService operationService,
                                     NadmBackendRestAPI restAPI,
                                     ServicesHelper ws,
                                     NadmSettings settings,
                                     HttpExecutionContext ec,
                                     AllEquipmentRecordsIterableFactory allEquipmentIterableFactory,
                                     RequestBuilderFactory requestBuilderFactory) {
        this.operationService = operationService;
        this.restAPI = restAPI;
        this.ws = ws;
        this.settings = settings;
        this.ec = ec;
        this.allEquipmentIterableFactory = allEquipmentIterableFactory;
        this.requestBuilderFactory = requestBuilderFactory;
    }


    /**
     * Filter bulk operations against active operations on GO.
     *
     * @return operations map (id -> name)
     */
    public CompletionStage<Map<String, String>> getBulkOperationsWithTemplate(String context) {
        if (!canUserExecuteAnyMassiveAction(context)) {
            return CompletableFuture.completedFuture(new HashMap<>());
        }
        return operationService.getOperations(context).thenCombine(getBulkOperations(context), (operations, bulkOperations) -> {
            Set<String> bulkOperationsBlackList = new HashSet<>(settings.getEquipmentMassiveOperationsBlackList(context));
            Set<String> bulkOperationsSet = new HashSet<>(bulkOperations);
            if (settings.isEquipmentMassiveOperationsWhiteListDefined(context)) {
                Set<String> bulkOperationsWhiteList = new HashSet<>(settings.getEquipmentMassiveOperationsWhiteList(context));
                return Maps.filterValues(operations, value -> bulkOperationsSet.contains(value)
                        && bulkOperationsWhiteList.contains(value)
                        && !bulkOperationsBlackList.contains(value));
            } else {
                // backwards compatibility
                return Maps.filterValues(operations, value -> bulkOperationsSet.contains(value)
                        && !bulkOperationsBlackList.contains(value));
            }
        });
    }

    public CompletionStage<JsonNode> executeMassiveOperation(String context,
                                                             MassiveOperationExecData createOperation) {
        return getBulkSortedOperationHeaders(context, createOperation.getOperation())
                .thenComposeAsync(headersOptional -> {

                    if (!headersOptional.isPresent()) {
                        throw new BusinessException(ExceptionCodes.NOT_FOUND);
                    }

                    final List<String> headers = headersOptional.get();

                    Map<String, String> formAttributes = createOperation.getFormAttributes()
                            .stream()
                            .collect(Collectors.toMap(
                                    attr -> attr.getName().toLowerCase(),
                                    MassiveOperationExecData.Attribute::getValue
                            ));
                    Source<ByteString, ?> source =
                            Source.<ByteString>fromIterator((Creator<Iterator<ByteString>>) () -> iteratorSelectedRecords(
                                    context,
                                    headers,
                                    formAttributes,
                                    createOperation
                            ));
                    Http.MultipartFormData.FilePart<Source<ByteString, ?>> filePart = new Http.MultipartFormData.FilePart<>(
                            "data",
                            "file.csv",
                            "multipart/form-data; charset=UTF-8",
                            source
                    );


                    Request populatedRequest = new Request();
                    populatedRequest.setDate(new Date());
                    populatedRequest.setFamilyName(settings.getFamily(context));
                    populatedRequest.setOperationName(createOperation.getOperation());
                    populatedRequest.setUsername(SecurityUtils.getUsername());
                    populatedRequest.setInputDescription("From NADM");
                    populatedRequest.setLaunch(createOperation.isExecuteImmediately());
                    JsonNode populatedRequestAsJson = Json.toJson(populatedRequest);

                    Map<String, String> jsons = new HashMap<>();
                    jsons.put("request", populatedRequestAsJson.toString());
                    jsons.put("filename", filePart.getFilename());

                    return ws.postMultipartAsSource(
                            filePart,
                            jsons,
                            settings.getCreateBulkUrl(context),
                            JsonNode.class
                    ).thenApply(jsonNode -> {
                        final Long id = jsonNode.asLong();
                        final List<String> stringList = Stream.of(settings.getMassiveOperationLink(context).split("/")).map(
                                pathSection -> {
                                    if (":id".equals(pathSection)) {
                                        return String.valueOf(id);
                                    }
                                    return pathSection;
                                }).collect(Collectors.toList());
                        final String operationLink = String.join("/", stringList);
                        return Json.newObject()
                                .put("status", "ok")
                                .put("url", operationLink);

                    });
                }, ec.current());
    }

    public CompletionStage<List<Mapping>> getBulkOperationTemplateById(String context,
                                                                       String operationId) {
        return operationService.getOperationTemplate(context, operationId)
                .thenApplyAsync(template -> {
                    final String templateName = template.getName();
                    final List<String> massiveOperationsToUseConfirmationModal =
                            settings.getEquipmentMassiveOperationsToUseConfirmationModal(
                                    context);
                    if (massiveOperationsToUseConfirmationModal.contains(templateName)) {
                        return Collections.emptyList();
                    }
                    final List<Mapping> templateMappings = template.getMapping();
                    // Fill attributes for each execution line
                    Set<String> attributesSet = new HashSet<>(settings.getMappingModel(context).values());
                    return templateMappings.stream()
                            .filter(mapping -> !attributesSet.contains(mapping.getTemplateMapping()))
                            .collect(Collectors.toList());
                }, ec.current());
    }

    public boolean canUserExecuteAnyMassiveAction(String context) {
        return settings.getIsActiveMptGoIntegration(context) &&
                Authorization.isAuthorizedToExecuteAnyMassiveAction(context);
    }

    private Iterator<ByteString> iteratorSelectedRecords(String context,
                                                         List<String> headers,
                                                         Map<String, String> formAttributes,
                                                         MassiveEquipmentSelectionData selectionData) {
        return allEquipmentIterableFactory.forActionExecution(context, restAPI, selectionData).stream()
                .map(bulkEntryMapper(context, headers, formAttributes))
                .map(bulkEntry -> bulkEntry + System.lineSeparator())
                .map(ByteString::fromString).iterator();
    }

    private CompletionStage<Optional<List<String>>> getBulkSortedOperationHeaders(String context,
                                                                                  String operationName) {
        return getBulkOperationId(context, operationName).thenCompose(operationIdOptional -> operationIdOptional
                .map(operationId -> getBulkSortedOperationHeaders(context, operationId).thenApply(Optional::ofNullable))
                .orElse(CompletableFuture.completedFuture(Optional.empty())));
    }

    private CompletionStage<List<String>> getBulkSortedOperationHeaders(String context,
                                                                        Long operationId) {
        ODataBuilder oDataBuilder = new ODataBuilder();
        oDataBuilder.addNumericField("operationId", operationId, ODataBuilder.FilterOperation.EQUAL);
        return ws.sendGet(requestBuilderFactory.create()
                        .setUrl(settings.getOperationHeadersUrl(context))
                        .addQueryParameters(ODataQueryParametersBuilder.get().filter(oDataBuilder).build())
                        .responseAs(OperationHeadersWrapper.class).build())
                .thenApply(operationHeaders -> operationHeaders.getEntries().stream()
                        .sorted(Comparator.comparingLong(OperationHeaderEntry::getPosition))
                        .map(OperationHeaderEntry::getName)
                        .collect(Collectors.toList()));
    }

    private CompletionStage<Optional<Long>> getBulkOperationId(String context,
                                                               String operationName) {
        return getBulkOperations(context, operationName).thenApply(operations -> {
            if (!operations.getEntries().isEmpty()) {
                return Optional.of(operations.getEntries().get(0).getId());
            }
            return Optional.empty();
        });
    }

    private CompletionStage<List<String>> getBulkOperations(String context) {
        return getBulkOperations(context, null).thenApplyAsync(bulkOperations -> {
            List<String> bulkOperationsList = new ArrayList<>();
            for (OperationEntry bulkOperation : bulkOperations.getEntries()) {
                bulkOperationsList.add(bulkOperation.getName());
            }
            return bulkOperationsList;
        });
    }

    private CompletionStage<OperationWrapper> getBulkOperations(String context,
                                                                String operationName) {
        return getBulkFamilyId(context).thenCompose(familyId -> {
            if (!familyId.isPresent()) {
                return CompletableFuture.completedFuture(new OperationWrapper(Collections.emptyList()));
            }

            ODataBuilder oDataBuilder = new ODataBuilder();
            oDataBuilder.addNumericField("familyId", familyId.get(), ODataBuilder.FilterOperation.EQUAL);
            oDataBuilder.addNumericField("active", 1, ODataBuilder.FilterOperation.EQUAL);
            if (operationName != null && !operationName.isEmpty()) {
                oDataBuilder.addTextField("name", operationName, ODataBuilder.FilterOperation.EQUAL);
            }
            return ws.sendGet(requestBuilderFactory.create()
                    .setUrl(settings.getMptOperationsUrl(context))
                    .addQueryParameters(ODataQueryParametersBuilder.get().filter(oDataBuilder).build())
                    .responseAs(OperationWrapper.class)
                    .build());
        });
    }

    private CompletionStage<Optional<Long>> getBulkFamilyId(String context) {
        ODataBuilder oDataBuilder = new ODataBuilder();
        oDataBuilder.addTextField("name", settings.getFamily(context), ODataBuilder.FilterOperation.EQUAL);
        oDataBuilder.addNumericField("active", 1, ODataBuilder.FilterOperation.EQUAL);
        return ws.sendGet(requestBuilderFactory.create()
                .setUrl(settings.getMptFamilies(context))
                .addQueryParameters(ODataQueryParametersBuilder.get().filter(oDataBuilder).build())
                .responseAs(FamilyWrapper.class)
                .build()).thenApply(families -> {
            if (!families.getEntries().isEmpty()) {
                return Optional.of(families.getEntries().get(0).getId());
            }
            return Optional.empty();
        });
    }

    private static String createBulkEntry(List<String> sortedAttributesName,
                                          Map<String, String> formAttributes,
                                          Map<String, String> recordAttributes) {
        Map<String, String> allAttributes = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
        allAttributes.putAll(recordAttributes);
        allAttributes.putAll(formAttributes);
        StringBuilder sb = new StringBuilder();
        int index = sortedAttributesName.size();
        for (String attributeName : sortedAttributesName) {
            sb.append(allAttributes.getOrDefault(attributeName, ""));
            if (--index != 0) {
                sb.append(";");
            }
        }
        return sb.toString();
    }

    private Function<JsonNode, String> bulkEntryMapper(String context,
                                                       List<String> sortedAttributes,
                                                       Map<String, String> attributes) {
        Map<String, String> mappingModel = settings.getMappingModel(context);
        return (node -> {
            Map<String, String> attributesMap = new HashMap<>();
            for (Map.Entry<String, String> entry : mappingModel.entrySet()) {
                attributesMap.put(entry.getValue(), node.get(entry.getKey()).asText());
            }
            return createBulkEntry(sortedAttributes, attributes, attributesMap);
        });
    }


}
