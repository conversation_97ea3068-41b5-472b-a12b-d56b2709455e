package na.nadm.models.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import pt.ptinovacao.na.portal.webui.restful.commons.model.RawListContainer;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)

public class ManufacturerModelVersionEntityDto extends RawListContainer<ManufacturerModelVersionEntity> {
    public ManufacturerModelVersionEntityDto(){}

    public ManufacturerModelVersionEntityDto(List<ManufacturerModelVersionEntity> entries) { super(entries); }
}
