package na.nadm.models;

import pt.ptinovacao.na.portal.webui.restful.commons.model.RawListContainer;
import pt.ptinovacao.nadm.Equipment;

import java.util.List;

/**
 * Created by rsantos on 30/06/16.
 */
@SuppressWarnings("unused")
public class EquipmentWrapper extends RawListContainer<Equipment> {

    public EquipmentWrapper(){}

    public EquipmentWrapper(List<Equipment> entries) {
        super(entries);
    }
}
