package na.nadm.models.responses;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import pt.ptinovacao.na.portal.webui.restful.commons.model.RawListContainer;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public final class TagResponse extends RawListContainer<Tag> {

    public TagResponse(){}

    @JsonCreator
    public TagResponse(@JsonProperty("entries") List<Tag> entries) { super(entries); }

}
