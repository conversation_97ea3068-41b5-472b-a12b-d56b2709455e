package na.nadm.settings;

import na.naportalbase.models.security.ResourcePermission;

public class AAAPIResources {

    private final static String DEVICE = ".device";
    private final static String NETQ_DIAGNOSTIC = "SHOW";

    public static ResourcePermission device(String context) {
        return new ResourcePermission(context + DEVICE);
    }

    public static ResourcePermission diagnostic() {
        return new ResourcePermission(NETQ_DIAGNOSTIC);
    }
}
