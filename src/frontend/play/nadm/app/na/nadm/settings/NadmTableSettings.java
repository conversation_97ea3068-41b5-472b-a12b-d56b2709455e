package na.nadm.settings;

import com.google.common.collect.ImmutableList;
import com.typesafe.config.Config;
import na.naportalbase.exceptions.ExceptionCodes;
import na.naportalbase.models.tables.config.Pagination;
import na.naportalbase.models.tables.config.TableConfig;
import na.naportalbase.models.tables.config.TableConfigBuilder;
import na.naportalbase.settings.TableSettings;
import play.api.mvc.Call;
import pt.ptinovacao.nossis.exceptions.BusinessException;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Map;

@Singleton
public class NadmTableSettings extends TableSettings {

    public static final Pagination DEFAULT = new Pagination(true, 15, ImmutableList.of(15, 30, 60));

    @Inject
    public NadmTableSettings(Config config) {
        super(config);
    }


    public TableConfig loadTableConfig(String context, String tableName){
        return super.loadTableConfig(context,tableName);
    }

    @Override
    public TableConfigBuilder loadTableConfigBuilder(String context, String tableName){
        Map<String, Object> tableDefinitionConfig = loadTableDefinitionConfig(context, tableName);
        TableConfigBuilder builder = initTableConfigBuilder(context,tableDefinitionConfig);
        builder.pagination(DEFAULT);
        loadTableFeatures(tableDefinitionConfig, builder);
        loadPaginationAndAutoWidth(tableDefinitionConfig, builder);
        loadTableColumns(tableDefinitionConfig, builder);
        loadRowGrouping(tableDefinitionConfig, builder);
        return builder;
    }

    @Override
    protected TableConfig loadTableFromConfiguration(String context, String tableName){
        return loadTableConfigBuilder(context, tableName).build();
    }

    protected TableConfigBuilder initTableConfigBuilder(String context, final Map<String, Object> tableDefinitionConfig) {
        Object routeValue = tableDefinitionConfig.get("route");
        if(routeValue != null){
            return TableConfigBuilder.fromRoute(getRoute(context,routeValue.toString()));
        } else {
            return new TableConfigBuilder();
        }
    }

    protected Call getRoute(String context, final String reverseRoute) {

        final String route = String.valueOf(reverseRoute);
        final String routeIndexParam = ".routes.";

        int routesDot = route.lastIndexOf(routeIndexParam) + routeIndexParam.length() - 1;
        int lastDot = route.lastIndexOf(".");

        final String routesClass = route.substring(0, routesDot);
        final String controllerField = route.substring(routesDot + 1, lastDot);
        final String controllerMethodName = route.substring(lastDot + 1);

        try {
            final Field controller = Class.forName(routesClass).getField(controllerField);
            final Method declaredMethod = controller.getType().getDeclaredMethod(controllerMethodName, String.class);
            return (Call) declaredMethod.invoke(controller.get(null), context);
        } catch (ClassNotFoundException | NoSuchFieldException | NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
            throw new BusinessException(ExceptionCodes.CONFIGURATION_EXCEPTION, e, "route", route);
        }
    }
}
