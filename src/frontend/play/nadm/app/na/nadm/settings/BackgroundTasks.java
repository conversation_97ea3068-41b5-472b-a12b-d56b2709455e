package na.nadm.settings;

import play.api.inject.ApplicationLifecycle;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.util.concurrent.CompletableFuture;

@Singleton
public final class BackgroundTasks {

    private static final play.Logger.ALogger LOGGER = play.Logger.of(NadmConstants.LOGGER_NAME);

    @Inject
    private BackgroundTasks(final ApplicationLifecycle lifecycle) {

        // ---------------
        // -- ON START ---
        // ---------------

        LOGGER.info("STARTING NADM MODULE");

        lifecycle.addStopHook(() -> {
            LOGGER.info("NADM MODULE STOPPED");
            return CompletableFuture.completedFuture(null);
        });

        LOGGER.info("NADM MODULE STARTED");

    }
}