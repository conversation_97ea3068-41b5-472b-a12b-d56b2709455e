package na.nadm.controllers;

import com.fasterxml.jackson.databind.JsonNode;
import na.nadm.models.MassiveEquipmentSelectionData;
import na.nadm.models.MassiveOperationTagManagementData;
import na.nadm.services.TagServices;
import na.nadm.settings.AAAPIResources;
import na.nadm.settings.TagResultCache;
import na.naportalbase.exceptions.ExceptionCodes;
import na.naportalbase.utils.SecurityUtils;
import play.libs.Json;
import play.mvc.Controller;
import play.mvc.Result;
import play.mvc.Results;
import pt.alticelabs.nossis.security.actions.Authenticated;
import pt.ptinovacao.nossis.exceptions.BusinessException;

import javax.inject.Inject;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;


public class Tag extends Controller {

    private final TagServices tagServices;

    @Inject
    public Tag(TagServices tagServices) {
        this.tagServices = tagServices;
    }

    @Authenticated
    public CompletionStage<Result> tags(final String context) {
        if (!SecurityUtils.isAuthorized(AAAPIResources.device(context).read())) {
            return SecurityUtils.unauthorizedPromise();
        }

        response().setHeader(CACHE_CONTROL, "public, max-age=10");

        String searchTerm = Optional.ofNullable(request().queryString().get("q"))
                .filter(query -> query.length > 0)
                .map(query -> query[0])
                .map(query -> query + "*")
                .orElse("");

        int searchPage = Optional.ofNullable(request().queryString().get("page"))
                .filter(query -> query.length > 0)
                .map(query -> Integer.parseInt(query[0]))
                .map(page -> page - 1)
                .orElse(0);

        return tagServices.getTagsByNameAsJSON(context, searchTerm, searchPage).thenApply(Results::ok);
    }

    @Authenticated
    public CompletionStage<Result> equipmentTagSearch(final String context) {
        JsonNode requestJson = request().body().asJson();
        MassiveEquipmentSelectionData execData = Json.fromJson(requestJson, MassiveEquipmentSelectionData.class);
        // Prepare a chunked text stream

        return CompletableFuture.completedFuture(tagServices.getCommonAndAssociatedTags(context, execData))
                .thenApply(Json::toJson)
                .thenApply(Results::ok);
    }

    @SuppressWarnings("unused")
    @Authenticated
    public CompletionStage<Result> equipmentTagSearchAssociatedTagsContinuation(final String context, final String uuid) {

        final CompletionStage<TagResultCache.Entry> completionStage = tagServices.getCommonAndAssociatedTags(uuid);

        if (completionStage == null) {
            throw new BusinessException(ExceptionCodes.NOT_FOUND);
        }

        return completionStage
                .thenApply(Json::toJson)
                .thenApply(Results::ok);
    }

    @Authenticated
    public CompletionStage<Result> manageEquipmentsTags(final String context) {
        if (!SecurityUtils.isAuthorized(AAAPIResources.device(context).read())) {
            return SecurityUtils.unauthorizedPromise();
        }
        JsonNode requestJson = request().body().asJson();
        MassiveOperationTagManagementData execData = Json.fromJson(requestJson, MassiveOperationTagManagementData.class);


        return tagServices.manageEquipmentTags(context, execData)
                .thenApply(Json::toJson)
                .thenApply(Results::ok);
    }
}