package na.nadm.controllers;

import com.fasterxml.jackson.databind.JsonNode;
import na.nadm.settings.AAAPIResources;
import na.nadm.utils.FormBuilderUtil;
import na.nadm.utils.NadmBackendRestAPI;
import na.naportalbase.utils.SecurityUtils;
import play.Logger;
import play.libs.Json;
import play.libs.concurrent.HttpExecutionContext;
import play.mvc.Controller;
import play.mvc.Result;
import play.mvc.Results;
import pt.alticelabs.nossis.security.actions.Authenticated;

import javax.inject.Inject;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

public class Detail extends Controller {

    private static final Logger.ALogger nadmLogger = Logger.of("nadm");

    private final NadmBackendRestAPI restAPI;

    private final HttpExecutionContext ec;

    @Inject
    public Detail(NadmBackendRestAPI restAPI,
                  HttpExecutionContext ec) {
        this.restAPI = restAPI;
        this.ec = ec;
    }

    @Authenticated
    public CompletionStage<Result> getGlobalEquipmentDetails(final String context,
                                                             final String manufacturer,
                                                             final String model,
                                                             final String version) {
        response().setHeader(CACHE_CONTROL, "no-cache, no-store, must-revalidate");
        if (!SecurityUtils.isAuthorized(AAAPIResources.device(context).read())) {
            return SecurityUtils.unauthorizedPromise();
        }
        return restAPI.getGlobalEquimentConfigurations(context, manufacturer, model, version)
                .thenApply(Json::toJson)
                .thenApply(Results::ok);
    }

    @Authenticated
    public CompletionStage<Result> updateGlobalEquipment(final String context,
                                                         final String manufacturer,
                                                         final String model,
                                                         final String version) {

        Map<String, String> attributesMap = requestParamsToMap();
        String firmwareFilename = attributesMap.get("equipments.equipment.equipmentData.deviceInfo.firmwareFilename");
        if (!SecurityUtils.isAuthorized(AAAPIResources.device(context).update())) {
            nadmLogger.debug("No authorization");
            return CompletableFuture.completedFuture(SecurityUtils.unauthorizedResult());
        }

        return restAPI.updateGlobalEquipmentConfiguration(
                        context,
                        manufacturer,
                        model,
                        version,
                        firmwareFilename,
                        requestParamsToMap()
                )
                .thenApplyAsync(configurations -> ok(), ec.current());
    }

    @Authenticated
    public CompletionStage<Result> updateGlobalEquipmentAndTemplate(final String context,
                                                                    final String manufacturer,
                                                                    final String model,
                                                                    final String version,
                                                                    final String firmwareFilename) {

        if (!SecurityUtils.isAuthorized(AAAPIResources.device(context).update())) {
            nadmLogger.debug("No authorization");
            return CompletableFuture.completedFuture(SecurityUtils.unauthorizedResult());
        }

        CompletableFuture<Boolean> updateGlobalEquipmentConfiguration = restAPI.updateGlobalEquipmentConfiguration(
                context,
                manufacturer,
                model,
                version,
                firmwareFilename,
                requestParamsToMap()
        ).toCompletableFuture();
        CompletableFuture<Boolean> updateTemplateConfiguration = restAPI.updateTemplateConfiguration(
                context,
                manufacturer,
                model,
                version,
                firmwareFilename,
                requestParamsToMap()
        ).toCompletableFuture();

        return CompletableFuture.allOf(updateGlobalEquipmentConfiguration, updateTemplateConfiguration)
                .thenApplyAsync(configurations -> ok(), ec.current());
    }

    @Authenticated
    public CompletionStage<Result> removeGlobalEquipment(final String context,
                                                         final String manufacturer,
                                                         final String model,
                                                         final String version) {

        if (!SecurityUtils.isAuthorized(AAAPIResources.device(context).update())) {
            nadmLogger.debug("No authorization");
            return CompletableFuture.completedFuture(SecurityUtils.unauthorizedResult());
        }

        CompletableFuture<Boolean> removeGlobalEquipmentConfiguration = restAPI.removeGlobalEquipmentConfiguration(
                context,
                manufacturer,
                model,
                version
        ).toCompletableFuture();
        CompletableFuture<Boolean> removeTemplateConfiguration = restAPI.removeTemplateConfiguration(
                context,
                manufacturer,
                model,
                version
        ).toCompletableFuture();

        return CompletableFuture.allOf(removeGlobalEquipmentConfiguration, removeTemplateConfiguration)
                .thenApplyAsync(configurations -> ok(), ec.current());
    }

    @Authenticated
    public CompletionStage<Result> updateClientEquipment(final String context,
                                                         final String clientId,
                                                         final String manufacturer,
                                                         final String model,
                                                         final String version) {

        if (!SecurityUtils.isAuthorized(AAAPIResources.device(context).update())) {
            nadmLogger.debug("No authorization");
            return CompletableFuture.completedFuture(SecurityUtils.unauthorizedResult());
        }

        return restAPI.updateClientEquipmentConfiguration(context, clientId, manufacturer, model, version, requestParamsToMap())
                .thenApplyAsync(configurations -> ok(), ec.current());
    }

    @Authenticated
    public CompletionStage<Result> removeClientEquipment(final String context,
                                                         final String clientId,
                                                         final String manufacturer,
                                                         final String model,
                                                         final String version) {

        if (!SecurityUtils.isAuthorized(AAAPIResources.device(context).delete())) {
            nadmLogger.debug("No authorization");
            return CompletableFuture.completedFuture(SecurityUtils.unauthorizedResult());
        }

        return restAPI.removeClientEquipmentConfiguration(context, clientId, manufacturer, model, version)
                .thenApplyAsync(configurations -> ok(), ec.current());
    }

    @Authenticated
    public CompletionStage<Result> createClientEquipment(final String context,
                                                         final String manufacturer,
                                                         final String model,
                                                         final String version,
                                                         final String clientId) {

        if (!SecurityUtils.isAuthorized(AAAPIResources.device(context).create())) {
            nadmLogger.debug("No authorization");
            return CompletableFuture.completedFuture(SecurityUtils.unauthorizedResult());
        }

        return restAPI.createClientEquipmentConfiguration(context, manufacturer, model, version, clientId, requestParamsToMap())
                .thenApplyAsync(configurations -> ok(), ec.current());
    }

    @Authenticated
    public CompletionStage<Result> createEquipment(final String context,
                                                   final String manufacturer,
                                                   final String model,
                                                   final String version,
                                                   final String clientId) {

        if (!SecurityUtils.isAuthorized(AAAPIResources.device(context).create())) {
            nadmLogger.debug("No authorization");
            return CompletableFuture.completedFuture(SecurityUtils.unauthorizedResult());
        }

        return restAPI.createEquipmentConfiguration(context, manufacturer, model, version, clientId, null, requestParamsToMap())
                .thenApplyAsync(configurations -> ok(), ec.current());
    }

    @Authenticated
    public CompletionStage<Result> createClient(final String context,
                                                final String clientId) {

        if (!SecurityUtils.isAuthorized(AAAPIResources.device(context).create())) {
            nadmLogger.debug("No authorization");
            return CompletableFuture.completedFuture(SecurityUtils.unauthorizedResult());
        }

        return restAPI.createClient(context, clientId, requestParamsToMap().get("description"))
                .thenApplyAsync(configurations -> ok(), ec.current());
    }

    @Authenticated
    public CompletionStage<Result> updateEquipment(final String context,
                                                   final String clientId,
                                                   final String equipmentId) {

        if (!SecurityUtils.isAuthorized(AAAPIResources.device(context).update())) {
            nadmLogger.debug("No authorization");
            return CompletableFuture.completedFuture(SecurityUtils.unauthorizedResult());
        }

        return restAPI.updateEquipmentConfiguration(context, clientId, equipmentId, requestParamsToMap())
                .thenApplyAsync(configurations -> ok(), ec.current());
    }

    @Authenticated
    public CompletionStage<Result> updateEquipmentAssociation(final String context,
                                                              final String equipmentId) {

        if (!SecurityUtils.isAuthorized(AAAPIResources.device(context).update())) {
            nadmLogger.debug("No authorization");
            return CompletableFuture.completedFuture(SecurityUtils.unauthorizedResult());
        }

        return restAPI.updateEquipmentAssociations(context, equipmentId, request().body().asJson())
                .thenApplyAsync(configurations -> ok(), ec.current());
    }

    @Authenticated
    public CompletionStage<Result> removeEquipment(final String context,
                                                   final String equipmentId) {
        if (!SecurityUtils.isAuthorized(AAAPIResources.device(context).delete())) {
            nadmLogger.debug("No authorization");
            return CompletableFuture.completedFuture(SecurityUtils.unauthorizedResult());
        }

        return restAPI.removeEquipmentConfiguration(context, equipmentId)
                .thenApplyAsync(configurations -> ok(), ec.current());
    }

    @Authenticated
    public CompletionStage<Result> removeClient(final String context,
                                                final String clientId) {
        if (!SecurityUtils.isAuthorized(AAAPIResources.device(context).delete())) {
            nadmLogger.debug("No authorization");
            return CompletableFuture.completedFuture(SecurityUtils.unauthorizedResult());
        }

        return restAPI.removeClient(context, clientId)
                .thenApplyAsync(configurations -> ok(), ec.current());
    }

    @Authenticated
    public CompletionStage<Result> removeService(final String context,
                                                 final String clientName,
                                                 final String serviceName) {
        if (!SecurityUtils.isAuthorized(AAAPIResources.device(context).delete())) {
            nadmLogger.debug("No authorization");
            return CompletableFuture.completedFuture(SecurityUtils.unauthorizedResult());
        }

        return restAPI.removeService(context, clientName, serviceName)
                .thenApplyAsync(configurations -> ok(), ec.current());
    }

    @Authenticated
    public CompletionStage<Result> updateService(final String context,
                                                 final String clientName,
                                                 final String serviceName) {

        if (!SecurityUtils.isAuthorized(AAAPIResources.device(context).update())) {
            nadmLogger.debug("No authorization");
            return CompletableFuture.completedFuture(SecurityUtils.unauthorizedResult());
        }

        return restAPI.updateServiceConfiguration(context, clientName, serviceName, requestParamsToMap())
                .thenApplyAsync(configurations -> ok(), ec.current());
    }

    @Authenticated
    public CompletionStage<Result> createService(final String context,
                                                 final String serviceType) {

        if (!SecurityUtils.isAuthorized(AAAPIResources.device(context).create())) {
            nadmLogger.debug("No authorization");
            return CompletableFuture.completedFuture(SecurityUtils.unauthorizedResult());
        }

        return restAPI.createServiceConfiguration(context, serviceType, requestParamsToMap())
                .thenApplyAsync(configurations -> ok(), ec.current());
    }

    private Map<String, String> requestParamsToMap() {
        JsonNode jsonRequest = request().body().asJson();
        return FormBuilderUtil.jsonToMap(jsonRequest);
    }

}
