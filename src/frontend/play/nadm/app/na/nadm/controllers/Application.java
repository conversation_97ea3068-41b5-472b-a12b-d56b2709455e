package na.nadm.controllers;

import na.nadm.settings.NadmSettings;
import na.nadm.views.html.imports.scripts;
import na.nadm.views.html.roots.resume;
import play.libs.Json;
import play.mvc.Controller;
import play.mvc.Http;
import play.mvc.Result;
import pt.alticelabs.nossis.security.actions.Authenticated;

import javax.inject.Inject;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

public class Application extends Controller {

    private final Navigation navigationController;

    private final NadmSettings settings;

    @Inject
    public Application(Navigation navigationController,
                       NadmSettings settings) {

        this.navigationController = navigationController;
        this.settings = settings;
    }

    /**
     * Method renders template associate to index.
     *
     * @return home template
     */
    @Authenticated
    public CompletionStage<Result> index(String context) {
        response().setHeader(CACHE_CONTROL, "no-cache, no-store, must-revalidate");
        switch (settings.getHomepage(context)) {
            case EQUIPMENT:
                return navigationController.equipment(context);
            case CLIENT:
                return navigationController.client(context);
            case SERVICES:
                return navigationController.service(context);
            default:
                return navigationController.globalEquipment(context);
        }
    }

    /**
     * Method renders template associate to resume.
     */
    @Authenticated
    public CompletionStage<Result> resume(String context) {
        return CompletableFuture.completedFuture(ok(resume.render(context)));
    }

    /**
     * Method renders template scripts with all imports of angular files.
     *
     * @return home scripts
     */
    @Authenticated
    public CompletionStage<Result> getModuleAngularScripts() {
        return CompletableFuture.completedFuture(ok(scripts.render()));
    }


    /**
     * Method that instantiates the javascript object containing the module's routes.
     * <p>
     * WARNING: all javascript routes must accept a first context argument.
     *
     * @return javascript object with routes
     */
    public CompletionStage<Result> javascriptRoutes() {
        return CompletableFuture.completedFuture(ok(play.routing.JavaScriptReverseRouter.create(
                "nadmjsRoutes",
                routes.javascript.Application.index(),
                routes.javascript.Application.resume(),
                routes.javascript.Application.jsSettings(),
                routes.javascript.Export.getEquipmentsCsv(),
                routes.javascript.Navigation.globalEquipment(),
                routes.javascript.Navigation.globalEquipmentDetails(),
                routes.javascript.Navigation.globalEquipmentEdit(),
                routes.javascript.Navigation.client(),
                routes.javascript.Navigation.clientDetails(),
                routes.javascript.Navigation.clientEquipmentDetail(),
                routes.javascript.Navigation.clientEquipmentDetailModal(),
                routes.javascript.Navigation.clientEquipmentCreateModal(),
                routes.javascript.Navigation.clientEquipmentCreateTemplate(),
                routes.javascript.Navigation.clientEquipmentEditModal(),
                routes.javascript.Navigation.equipmentCharacteristics(),
                routes.javascript.Navigation.equipment(),
                routes.javascript.Navigation.equipmentDetail(),
                routes.javascript.Navigation.equipmentEdit(),
                routes.javascript.Navigation.equipmentCreate(),
                routes.javascript.Navigation.equipmentCreateTemplate(),
                routes.javascript.Navigation.service(),
                routes.javascript.Navigation.serviceCreate(),
                routes.javascript.Navigation.serviceCreateTemplate(),
                routes.javascript.Navigation.serviceDetails(),
                routes.javascript.Navigation.serviceEdit(),
                routes.javascript.Navigation.serviceValues(),
                routes.javascript.Search.searchGlobalEquipment(),
                routes.javascript.Search.searchClient(),
                routes.javascript.Search.searchEquipment(),
                routes.javascript.Search.searchEquipmentAssociations(),
                routes.javascript.Search.searchClientEquipmentModel(),
                routes.javascript.Search.clientNames(),
                routes.javascript.Search.searchService(),
                routes.javascript.Search.catalogManufacturers(),
                routes.javascript.Search.catalogModels(),
                routes.javascript.Tag.equipmentTagSearch(),
                routes.javascript.Tag.equipmentTagSearchAssociatedTagsContinuation(),
                routes.javascript.Tag.manageEquipmentsTags(),
                routes.javascript.Tag.tags(),
                routes.javascript.Search.catalogVersions(),
                routes.javascript.Detail.updateGlobalEquipment(),
                routes.javascript.Detail.updateGlobalEquipmentAndTemplate(),
                routes.javascript.Detail.removeGlobalEquipment(),
                routes.javascript.Detail.updateClientEquipment(),
                routes.javascript.Detail.updateEquipment(),
                routes.javascript.Detail.updateEquipmentAssociation(),
                routes.javascript.Detail.createEquipment(),
                routes.javascript.Detail.updateService(),
                routes.javascript.Detail.createService(),
                routes.javascript.Detail.removeEquipment(),
                routes.javascript.Detail.createClientEquipment(),
                routes.javascript.Detail.removeClientEquipment(),
                routes.javascript.Detail.removeClient(),
                routes.javascript.Detail.removeService(),
                routes.javascript.Detail.createClient(),
                routes.javascript.Detail.getGlobalEquipmentDetails(),
                routes.javascript.Operation.getOperationForm(),
                routes.javascript.Operation.getMassiveOperationForm(),
                routes.javascript.Operation.uploadFirmwareFile(),
                routes.javascript.Operation.deleteFirmwareFile(),
                routes.javascript.Operation.executeOperation(),
                routes.javascript.Operation.executeBulkDelete(),
                routes.javascript.Operation.getMappingModel(),
                routes.javascript.Operation.getEquipmentOperations(),
                routes.javascript.Operation.getGlobalEquipmentOperations(),
                routes.javascript.Operation.executeBulkOperation(),
                routes.javascript.Filters.saveFilter(),
                routes.javascript.Filters.editFilter(),
                routes.javascript.Filters.deleteFilter(),
                routes.javascript.Filters.getAllFilters(),
                routes.javascript.Filters.getSelectedFilter()
        )).as(Http.MimeTypes.JAVASCRIPT));
    }

    public Result jsSettings(String context) {

        Map<String, Object> settingsMap = new HashMap<>();
        settingsMap.put("exportFileName", settings.getEquipmentExportFileName(context));
        settingsMap.put("CSVColumnSeparator", settings.getCsvColumnSeparator(context));
        settingsMap.put("CSVListSeparator", settings.getCsvListSeparator(context));

        return ok(Json.toJson(settingsMap));
    }
}