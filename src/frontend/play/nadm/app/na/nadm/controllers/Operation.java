package na.nadm.controllers;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import na.nadm.models.MassiveEquipmentSelectionData;
import na.nadm.models.MassiveOperationExecData;
import na.nadm.services.Authorization;
import na.nadm.services.NadmBulkDeleteService;
import na.nadm.services.NadmBulkOperationsService;
import na.nadm.services.NadmOperationsService;
import na.nadm.settings.NadmSettings;
import na.nadm.utils.FormBuilderUtil;
import na.nadm.views.html.components.operation.operationForm;
import na.naportalbase.utils.SecurityUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.filefilter.WildcardFileFilter;
import play.Logger;
import play.libs.Json;
import play.libs.concurrent.HttpExecutionContext;
import play.mvc.Controller;
import play.mvc.Http;
import play.mvc.Result;
import play.mvc.Results;
import pt.alticelabs.nossis.security.actions.Authenticated;
import pt.ptinovacao.na.portal.webui.restful.go.entities.CreateOperationContent;
import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping;
import pt.ptinovacao.naportal.nadm.exceptions.NadmConfigurationExceptionCode;
import pt.ptinovacao.nossis.exceptions.FaultException;

import javax.inject.Inject;
import java.io.File;
import java.io.FileFilter;
import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.PosixFilePermission;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

import static na.nadm.settings.NadmConstants.FORM_DATA_DEFAULT_MANUFACTURER_FIELD_NAME;
import static na.nadm.settings.NadmConstants.FORM_DATA_DEFAULT_MODEL_FIELD_NAME;
import static na.nadm.settings.NadmConstants.FORM_DATA_DEFAULT_VERSION_FIELD_NAME;
import static pt.ptinovacao.nossis.exceptions.ErrorCode.CONFIGURATION_ERROR;

public class Operation extends Controller {

    private static final Logger.ALogger nadmLogger = Logger.of("nadm");

    private final HttpExecutionContext ec;
    private final NadmOperationsService operationService;
    private final NadmBulkOperationsService bulkOperationService;
    private final NadmBulkDeleteService bulkDeleteService;
    private final NadmSettings settings;

    @Inject
    public Operation(HttpExecutionContext ec,
                     NadmOperationsService operationService,
                     NadmBulkOperationsService bulkOperationService,
                     NadmBulkDeleteService bulkDeleteService,
                     NadmSettings settings) {
        this.ec = ec;
        this.operationService = operationService;
        this.bulkOperationService = bulkOperationService;
        this.bulkDeleteService = bulkDeleteService;
        this.settings = settings;
    }

    private static boolean isDirEmpty(final Path directory) {
        try (DirectoryStream<Path> dirStream = Files.newDirectoryStream(directory)) {
            return !dirStream.iterator().hasNext();
        } catch (IOException e) {
            nadmLogger.error("Error checking if directory is empty", e);
        }
        return false;
    }

    @Authenticated
    public CompletionStage<Result> getOperationForm(final String context,
                                                    final String operationId) {
        if (operationService.isUserForbidenToExecuteAnyExternalAction(context)) {
            return SecurityUtils.unauthorizedPromise();
        }
        JsonNode attributes = request().body().asJson();
        Map<String, String> attributesMap = FormBuilderUtil.jsonToMap(attributes);

        return operationService.getOperationTemplate(context, operationId)
                .thenApplyAsync(template -> {
                    final List<Mapping> templateMappings = template.getMapping();

                    // set context data and change template inputs to read only
                    if (!attributesMap.isEmpty()) {
                        templateMappings.forEach(mapping -> {
                            if (attributesMap.containsKey(mapping.getTemplateMapping())) {
                                mapping.getComponent().setDefaultValue(attributesMap.get(mapping.getTemplateMapping()));
                                if (Boolean.TRUE.equals(mapping.getComponent().getRequired())) {
                                    mapping.getComponent().setEditable(false);
                                }
                            }
                        });
                    }

                    return ok(operationForm.render(
                            templateMappings,
                            NadmOperationsService.getOptionalTemplateMappings(templateMappings)
                    ));
                }, ec.current());
    }

    @Authenticated
    public CompletionStage<Result> getMassiveOperationForm(final String context,
                                                           final String operationId) {
        if (operationService.isUserForbidenToExecuteAnyExternalAction(context)) {
            return SecurityUtils.unauthorizedPromise();
        }
        return bulkOperationService.getBulkOperationTemplateById(context, operationId)
                .thenApplyAsync(templateMappings -> ok(operationForm.render(
                        templateMappings,
                        NadmOperationsService.getOptionalTemplateMappings(templateMappings)
                )), ec.current());
    }

    @Authenticated
    public Result uploadFirmwareFile(final String context) {
        if (operationService.isUserForbidenToExecuteAnyExternalAction(context)) {
            return SecurityUtils.unauthorizedResult();
        }
        Http.MultipartFormData<File> body = request().body().asMultipartFormData();
        Map<String, String> urlEncoded = body.asFormUrlEncoded().entrySet().stream().collect(
                Collectors.toMap(Map.Entry::getKey, e -> e.getValue()[0]));

        Http.MultipartFormData.FilePart<File> firmware = body.getFiles().get(0);

        String firmwareFileName = firmware.getFilename();
        Path filepath = Paths.get(
                getDirsPath(context, urlEncoded),
                firmwareFileName
        );

        try {
            FileUtils.deleteDirectory(new File(filepath.getParent().toString()));
            Files.createDirectories(filepath.getParent());
            Files.move(firmware.getFile().toPath(), filepath);

            /*Fix permissions (644)*/
            Set<PosixFilePermission> filePermissions = new HashSet<>();
            filePermissions.add(PosixFilePermission.OWNER_READ);
            filePermissions.add(PosixFilePermission.OWNER_WRITE);
            filePermissions.add(PosixFilePermission.GROUP_READ);
            filePermissions.add(PosixFilePermission.OTHERS_READ);

            Files.setPosixFilePermissions(filepath, filePermissions);
        } catch (IOException e) {
            throw new FaultException(new NadmConfigurationExceptionCode("NADM_AP_00040", CONFIGURATION_ERROR));
        }

        return ok();
    }

    @Authenticated
    public Result deleteFirmwareFile(final String context,
                                     final String manufacturer,
                                     final String model,
                                     final String version) {
        if (operationService.isUserForbidenToExecuteAnyExternalAction(context)) {
            return SecurityUtils.unauthorizedResult();
        }

        StringJoiner joiner = new StringJoiner("/").add(manufacturer).add(model).add(version);
        String baseDirs = settings.getBaseUploadFilesDir(context);
        String filePath = baseDirs + joiner;
        FileFilter fileFilter = new WildcardFileFilter("*");
        File[] files = new File(filePath).listFiles(fileFilter);
        if (files != null) {
            for (File file : files) {
                try {
                    Files.delete(file.toPath());
                } catch (IOException e) {
                    throw new FaultException(new NadmConfigurationExceptionCode("NADM_AP_00041", CONFIGURATION_ERROR), e);
                }
            }
            try {
                if (isDirEmpty(Paths.get(filePath))) {
                    FileUtils.deleteDirectory(new File(filePath));
                }
            } catch (IOException e) {
                throw new FaultException(new NadmConfigurationExceptionCode("NADM_AP_00041", CONFIGURATION_ERROR));
            }
        }
        return ok();
    }

    @Authenticated
    public CompletionStage<Result> executeOperation(final String context) {
        if (operationService.isUserForbidenToExecuteAnyExternalAction(context)) {
            return SecurityUtils.unauthorizedPromise();
        }
        JsonNode requestJson = request().body().asJson();
        CreateOperationContent createOperation = Json.fromJson(requestJson, CreateOperationContent.class);
        return operationService.executeIndividualOperation(context, createOperation).thenApply(Results::ok);
    }

    @Authenticated
    public CompletionStage<Result> executeBulkOperation(final String context) {
        if (operationService.isUserForbidenToExecuteAnyExternalAction(context)) {
            return SecurityUtils.unauthorizedPromise();
        }
        JsonNode requestJson = request().body().asJson();
        MassiveOperationExecData execData = Json.fromJson(requestJson, MassiveOperationExecData.class);
        return bulkOperationService.executeMassiveOperation(context, execData)
                .thenApply(Results::ok);
    }

    @Authenticated
    public CompletionStage<Result> executeBulkDelete(final String context) {
        if (!Authorization.isAuthorizedToMassivelyDeleteEquipments(context)) {
            return SecurityUtils.unauthorizedPromise();
        }
        JsonNode requestJson = request().body().asJson();
        MassiveEquipmentSelectionData selectionData = Json.fromJson(requestJson, MassiveEquipmentSelectionData.class);
        return bulkDeleteService.deleteEquipments(context, selectionData)
                .thenApply(Json::toJson)
                .thenApply(Results::ok);
    }

    @Authenticated
    public CompletionStage<Result> getMappingModel(String context) {
        response().setHeader(CACHE_CONTROL, "no-cache, no-store, must-revalidate");
        return CompletableFuture.completedFuture(ok(Json.toJson(settings.getMappingModel(context))));
    }


    @Authenticated
    public CompletionStage<Result> getEquipmentOperations(final String context) {
        response().setHeader(CACHE_CONTROL, "no-cache, no-store, must-revalidate");
        final String cloneEquipmentOperation = settings.getCloneEquipmentOperation(context);
        Set<String> whiteList = new HashSet<>(settings.getEquipmentIndividualOperationsWhitelist(context));

        Map<String, Object> diagnosticsOperations = getDiagnosticOperations(context);

        return operationService.getOperations(context)
                .thenApplyAsync(operations -> {
                    ObjectNode activeOperations = getActiveOperations(operations, whiteList);
                    if (cloneEquipmentOperation != null && operations.containsValue(cloneEquipmentOperation)) {
                        activeOperations.put("cloneEquipmentOperationName", cloneEquipmentOperation);
                    }

                    activeOperations.putPOJO("diagnosticsOperations", diagnosticsOperations);

                    return activeOperations;
                })
                .thenApply(Results::ok);

    }

    @Authenticated
    public CompletionStage<Result> getGlobalEquipmentOperations(final String context) {
        response().setHeader(CACHE_CONTROL, "no-cache, no-store, must-revalidate");
        Set<String> whiteList = new HashSet<>(settings.getGlobalEquipmentIndividualOperationsWhitelist(context));
        return operationService.getOperations(
                        context,
                        settings.getGlobalEquipmentOperationsFamily(context),
                        settings.getGlobalEquipmentOperationsRfs(context)
                )
                .thenApplyAsync(operations -> getActiveOperations(operations, whiteList))
                .thenApply(Results::ok);
    }

    public Map<String, Object> getDiagnosticOperations(String context) {
        if(!settings.getIsActiveDiagnosticIntegration(context) ||
                !Authorization.isAuthorizedToExecuteDiagnosticOperations()){
            return Collections.emptyMap();
        }

        return settings.getDiagnosticsOperations(context);
    }

    @SuppressWarnings("unchecked")
    private Map<String, String> getMappingForOperationWithUpload(final String context,
                                                                 final String operationName) {
        Map<String, Object> mappingForOperationsWithUpload = settings.getMappingForOperationsWithUpload(context);
        if (!mappingForOperationsWithUpload.isEmpty() && mappingForOperationsWithUpload.containsKey(operationName)) {
                return (Map<String, String>) mappingForOperationsWithUpload.get(operationName);
        }
        return new HashMap<>();
    }

    private String getDirsPath(String context, Map<String, String> urlEncoded) {
        String mappedManufacturer = null;
        String mappedModel = null;
        String mappedVersion = null;
        String operationName = "";

        if (urlEncoded.containsKey("operationName")) {
            operationName = urlEncoded.get("operationName");
            final Map<String, String> mappingForOperationWithUpload = getMappingForOperationWithUpload(context, operationName);
            if (!mappingForOperationWithUpload.isEmpty()) {
                mappedManufacturer = mappingForOperationWithUpload.get(FORM_DATA_DEFAULT_MANUFACTURER_FIELD_NAME);
                mappedModel = mappingForOperationWithUpload.get(FORM_DATA_DEFAULT_MODEL_FIELD_NAME);
                mappedVersion = mappingForOperationWithUpload.get(FORM_DATA_DEFAULT_VERSION_FIELD_NAME);
            }
        }

        String manufacturer = urlEncoded.containsKey(mappedManufacturer)
                ? urlEncoded.get(mappedManufacturer)
                : urlEncoded.get(FORM_DATA_DEFAULT_MANUFACTURER_FIELD_NAME);

        String model = urlEncoded.containsKey(mappedModel)
                ? urlEncoded.get(mappedModel)
                : urlEncoded.get(FORM_DATA_DEFAULT_MODEL_FIELD_NAME);

        String version = urlEncoded.containsKey(mappedVersion)
                ? urlEncoded.get(mappedVersion)
                : urlEncoded.get(FORM_DATA_DEFAULT_VERSION_FIELD_NAME);

        if (model == null || manufacturer == null || version == null)
            throw new NullPointerException(
                    String.format(
                            "Incorrect mapping of form fields { manufacturer: %s, model: %s, newVersion: %s } for operation %s",
                            mappedManufacturer,
                            mappedModel,
                            mappedVersion,
                            operationName
                    )
            );

        StringJoiner joiner = new StringJoiner("/").add(manufacturer).add(model).add(version);
        return settings.getBaseUploadFilesDir(context) + joiner;
    }

    private ObjectNode getActiveOperations(Map<String, String> operations,
                                           Set<String> whitelist) {
        Map<String, String> activeOperations = new HashMap<>();
        operations.forEach((key, value) -> {
            if (whitelist.contains(value)) {
                activeOperations.put(key, value);
            }
        });

        final ObjectNode objectNode = Json.newObject();
        objectNode.putPOJO("operations", activeOperations);
        return objectNode;
    }


}
