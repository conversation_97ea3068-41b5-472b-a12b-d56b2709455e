package na.nadm.utils;

import com.fasterxml.jackson.databind.JsonNode;
import pt.alticelabs.nossis.messages.api.Messages;
import pt.ptinovacao.naportal.components.I18n;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * Created by ruig on 26-07-2016.
 */
public class FormBuilderUtil {

    private static String getI18nOrLabel(I18n i18n){
        if(i18n.getI18nKey() == null || "".equals(i18n.getI18nKey())){
            return i18n.getLabel();
        }
        else if (Messages.get(i18n.getI18nKey()).equals(i18n.getI18nKey())){
            return i18n.getLabel();
        } else {
            return Messages.get(i18n.getI18nKey());
        }
    }


    public static String parseI18n(I18n i18n){
        if(i18n != null) {
            return getI18nOrLabel(i18n);
        }
        else return "";
    }


    public enum ViewType {
        VIEW,
        EDIT,
        CREATE,
    }

    public static Map<String, String> jsonToMap(JsonNode form) {
        Map<String, String> params = new HashMap<>();
        Iterator<String> ite = form.fieldNames();
        while (ite.hasNext()) {
            String name = ite.next();
            String value = form.get(name).asText();
            if (value != null) {
                params.put(name, value);
            }
        }
        return params;
    }

}
