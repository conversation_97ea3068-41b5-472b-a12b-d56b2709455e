package na.nadm.utils;

import com.fasterxml.jackson.databind.JsonNode;

import java.util.Iterator;
import java.util.List;

public class DataConverter {

    private DataConverter() {
        throw new IllegalStateException("Utility class");
    }

    public static String jsonObjectToCsvLine(JsonNode object, List<String> fieldsList, String columnSeparator, String listSeparator) {
        String csvLine = "";
        boolean isFirstField = true;
        for(String field : fieldsList) {
            if(!isFirstField) csvLine = csvLine.concat(columnSeparator);
            isFirstField = false;

            JsonNode node = object.get(field);
            if(node == null){
                csvLine = csvLine.concat("");
            } else if(node.isTextual()) {
                csvLine = csvLine.concat(node.asText());
            } else if (node.isArray()) {
                Iterator<JsonNode> it = node.iterator();
                while(it.hasNext()) {
                    String entry = it.next().asText();
                    if(it.hasNext()) entry = entry.concat(listSeparator);
                    csvLine = csvLine.concat(entry);
                }
            } else {
                csvLine = csvLine.concat("");
            }
        }
        return csvLine.concat("\n");
    }
}
