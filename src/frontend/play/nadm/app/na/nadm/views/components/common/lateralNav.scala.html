@import na.nadm.settings.NadmConstants.NavContext
@import na.nadm.settings.NadmSettings
@import na.naportalbase.views.tags.i18n

@(context:String, activeNavigationContext: NavContext, nadmSettings: NadmSettings)

@navElements = @{

    class NavElementAttribute(
            val action: String, val icon: String, val aClass: String,
            val activeNavContext: NavContext
    ){
        val linkClasses = this.aClass + (if(this.activeNavContext.equals(activeNavigationContext)) " fx-active" else "")
    }

    var map = scala.collection.mutable.LinkedHashMap[String, NavElementAttribute](
        "na.portal.nadm.nav.globalEquip" ->  new NavElementAttribute(
            action = "searchGlobalEquip",
            icon = "fuxicons fuxicons-equipment-configuration",
            aClass = "nav-model",
            activeNavContext = NavContext.GLOBAL_EQUIP
        ),
        "na.portal.nadm.nav.equipment" ->  new NavElementAttribute(
            action = "searchEquipment",
            icon = "glyphicon glyphicon-hdd",
            aClass = "nav-equipment",
            activeNavContext = NavContext.EQUIPMENT
        ),
        "na.portal.nadm.nav.client" ->  new NavElementAttribute(
            action = "searchClient",
            icon = "glyphicon glyphicon-user",
            aClass = "nav-client",
            activeNavContext = NavContext.CLIENT
        ),
        "na.portal.nadm.nav.services" -> new NavElementAttribute(
            action = "searchService",
            icon = "fa fa-cogs",
            aClass = "nav-service" + (if(nadmSettings.hasServiceTypes(context)) "" else " disabled"),
            activeNavContext = NavContext.SERVICE
        )
    )
    map
}

<!-- data-enable-tooltip -->
<nav id="fx-page-main-nav" class="fx-ico-nav" data-na-portal-nadm-navigation-sidebar>
    <ul>
    @for((key,value) <- navElements) {
        <li class="@value.linkClasses" data-placement="right" data-toggle="tooltip" data-enable-tooltip data-original-title="@i18n(key)">
            <a data-nav-action="@value.action">
                <i class="@value.icon"></i>
            </a>
        </li>
    }
    </ul>
</nav>
