@import na.nadm.settings.NadmConstants.NavContext
@import na.nadm.settings.NadmSettings
@import na.nadm.utils.FormBuilderUtil.ViewType
@import na.nadm.views.html.components.common.detail.headerButtons
@import na.nadm.views.html.components.common.lateralNav
@import na.nadm.views.html.components.common.tableform.tableForm
@import na.nadm.views.html.skeletons.mainSkel
@import na.naportalbase.views.html.components.tabs.contentTabComp
@import na.naportalbase.views.html.skeletons.tabs.contentTabSkel
@import na.naportalbase.views.tags.i18n
@import pt.ptinovacao.naportal.nadm.Configurations

@import scala.collection.immutable

@(
		context: String,
		clientId: String,
		manufacturer: String,
		model:String,
		version:String,
		configurations: Configurations,
		viewContext : ViewType,
		nadmSettings: NadmSettings
)


@navBar = {
	@lateralNav.render(context, NavContext.CLIENT, nadmSettings)
}


@header = {
    <div class="pull-left">
        <h1>
            <span class="fx-info-icon">
                <i class="fuxicons fuxicons-equipment-configuration"></i>
            </span>
            @manufacturer - @model - @version
        </h1>
    </div>
}

@headerAction = {
    @headerButtons.render(context, viewContext)
}

@rightSide = {
	<div id="fx-splitter-content" class="fx-splitter-content">
		<div class="fx-splitter-content-inner">
		</div>
	</div>
}

@characteristicsTabAttributes = @{

	var charTab = immutable.Map[String, String](
		"id" -> "global-equip-edit-tab",
		"context" -> "characteristics"
	)
	charTab
}

@tabs = {
	@contentTabComp.render(true,i18n("na.portal.nadm.tabname.characteristics"),characteristicsTabAttributes)
}

@AdditionalHeaderContent = {
	@contentTabSkel.render(tabs)
}


@mainSkel(context) {
    <div data-fuxi-header></div>
    @navBar
	<div class="fx-main-content-wrapper fx-with-main-nav" data-ng-controller="NaPortalNadmCommonController" data-na-portal-nadm-client-equip-details-page data-client-id="@clientId" data-manufacturer="@manufacturer" data-model="@model" data-version="@version" >
		@na.naportalbase.views.html.skeletons.pageHeaderSkel.render(header, headerAction, null, AdditionalHeaderContent)
		<div id="overLay">
			<div class="tab-content fx-main-tab-content" data-template-loader data-context="equipmentModel">
				<div class="tab-pane active">
					<div class="fx-tab-pane-inner">
						@tableForm.render(configurations, viewContext, context, nadmSettings)
					</div>
				</div>
			</div>
		</div>
	</div>
}