@import na.nadm.views.html.components.common.form.{associationsNameField, associationsTypeField}
@import na.naportalbase.views.html.components.buttonsComp.leftSideFormInputButtonsComp
@import na.naportalbase.views.tags.i18n

@import scala.collection.mutable

@(context: String)

@clearButtonAttributes = @{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-click", "searchCtrl.clearForm($event)")
    map.put("id", "clearSearchButton")
    map.put("type", "reset")
    map.put("value", i18n("na.buttons.clear"))
    map
}

<header class="fx-sidebar-header search-sidebar__header">
    <div class="fx-sidebar-header-inner">
        <p>
            <span class="fx-entity-header-icon">
                <i class="fa fa-search"></i>
            </span>
            <x-i18n key="na.basemodule.lateralSearch.title"></x-i18n>
        </p>
    </div>
</header>
<div class="fx-sidebar-content-wrapper search-sidebar__field-list">
    <x-shadow-scroll vertical>
        @associationsTypeField.render()
        @associationsNameField.render()
    </x-shadow-scroll>
</div>
<div class="form-group search-sidebar__button-list">
    @leftSideFormInputButtonsComp.render(null, clearButtonAttributes)
</div>
