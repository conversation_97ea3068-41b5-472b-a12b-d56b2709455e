@import na.nadm.utils.FormBuilderUtil
@import na.naportalbase.views.html.components.selectsComp.selectComp
@import pt.ptinovacao.naportal.components.{Option, SelectableUiComponent}

@import java.lang.Boolean.{FALSE, TRUE}
@import scala.collection.{immutable, mutable}

@(uiComponent: SelectableUiComponent, angularModel: String)

@elementAttr = @{
    var map = mutable.HashMap[String, String] (
        "id" -> uiComponent.getElementId,
        "name" -> uiComponent.getElementId,
	    "data-ng-model" -> angularModel,
        "data-na-portal-select-box" -> null,
        "class" -> "form-control input-sm",
        "full-width" -> null,
    )

    if(TRUE.equals(uiComponent.getRequired)){
        map.put("data-na-portal-required", null)
    } else {
        map.put("allow-clear", null)
    }

    if(FALSE.equals(uiComponent.getEditable)){
        map.put("disabled","")
    }

    immutable.HashMap(map.toSeq:_*)
}

@selectOptions = {
    @for(option: Option <- uiComponent.getOptions) {
        <option value="@option.getValue"><i class="fa fa-undo"></i>
        @{
            val i18nText = FormBuilderUtil.parseI18n(option.getI18n)
            if (i18nText != "") {
                i18nText
            } else {
                option.getValue
            }
        }
        </option>
    }
}

<div data-na-portal-field-notification data-na-portal-input-notification-hr>
    @uiComponent.getType match {
        case SelectableUiComponent.Type.selectBox => {
            @selectComp.render(elementAttr, null, null, selectOptions)
        }
        case SelectableUiComponent.Type.checkBox => {}

        case SelectableUiComponent.Type.radioButton => {}

        case _ => {}
    }
</div>

