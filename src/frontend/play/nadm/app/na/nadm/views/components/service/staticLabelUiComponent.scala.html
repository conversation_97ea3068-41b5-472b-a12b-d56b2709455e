@import pt.ptinovacao.naportal.components.{TextUiComponent, UiComponent}

@(uiComponent: UiComponent)

@isPassword = @{
	uiComponent match {
		case textComponent : TextUiComponent if textComponent.getType == TextUiComponent.Type.passwordField => true
		case _ => false
	}
}

@if(isPassword){
    <p class="form-control-static" id="@uiComponent.getElementId">••••••••••••••••••••</p>
}else{
    <p class="form-control-static" id="@uiComponent.getElementId" data-ng-bind="formData['@uiComponent.getElementId']"></p>
}