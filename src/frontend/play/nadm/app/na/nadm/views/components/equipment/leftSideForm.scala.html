@import na.nadm.views.html.components.common.form.{EquipIdField, clientField, contactIPField, dateField, firstContactTimeIntervalField, lastContactTimeIntervalField, manufacturerField, modelField, provisionStateField, tagsField, versionField}
@import na.naportalbase.views.html.components.buttonsComp.leftSideFormInputButtonsComp
@import na.naportalbase.views.tags.i18n
@import scala.collection.mutable
@import pt.ptinovacao.na.portal.web.ui.naem.schemas.Context

@(context: String)

@searchButtonAttributes = @{
    var map = mutable.HashMap[String, String]()
    map.put("id", "searchButton")
    map.put("type","submit")
    map.put("value",i18n("na.buttons.search"))
    map
}

@clearButtonAttributes = @{
    var map = mutable.HashMap[String, String]()
    map.put("id", "clearSearchButton")
    map.put("type", "reset")
    map.put("value", i18n("na.buttons.clear"))
    map
}

<header class="fx-sidebar-header search-sidebar__header">
    <div class="fx-sidebar-header-inner">
        <p>
            <span class="fx-entity-header-icon">
                <i class="fa fa-search"></i>
            </span>
            <x-i18n key="na.basemodule.lateralSearch.title"></x-i18n>
        </p>
    </div>
</header>
<div class="fx-sidebar-content-wrapper search-sidebar__field-list">
    <x-nadm-filter data-filter-context="@Context.NA_EQUIPMENTS">
        <x-shadow-scroll>
            @EquipIdField.render()
            @manufacturerField.render(context)
            @modelField.render(context)
            @versionField.render(context)
            @clientField.render()
            @contactIPField.render()
            @tagsField.render(context)
            @provisionStateField.render()
            @firstContactTimeIntervalField.render()
            @lastContactTimeIntervalField.render()
            @dateField.render()
        </x-shadow-scroll>
    </x-nadm-filter>
</div>
<div class="form-group search-sidebar__button-list">
    <div class="fx-splitter-sidebar-buttons">
        <div class="fx-splitter-sidebar-buttons-inner">
        @leftSideFormInputButtonsComp.render(searchButtonAttributes, clearButtonAttributes)
        </div>
    </div>
</div>
