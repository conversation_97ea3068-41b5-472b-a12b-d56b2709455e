@import na.go.views.html.components.operationcreate
@import play.libs.Json
@import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping

@(requiredMappings: List[Mapping], optionalMappings: List[Mapping])

<div class="optional-fields-form">
    @operationcreate.configureOptionalFields(optionalMappings)
    <form class="form-horizontal" data-na-portal-go-dynamic-form data-na-portal-go-dynamic-form-mappings="@Json.toJson(requiredMappings)">
    </form>
</div>
