@import na.nadm.settings.NadmConstants.NavContext
@import na.nadm.settings.NadmSettings
@import na.nadm.views.html.components.common.lateralNav
@import na.nadm.views.html.components.equipment.leftSideForm
@import na.nadm.views.html.components.operation
@import na.nadm.views.html.skeletons.mainSkel
@import na.naportalbase.views.tags.i18n
@import pt.alticelabs.nossis.security.views.html.authorized
@import na.nadm.settings.AAAPIResources

@(context: String, operations: java.util.Map[String, String], nadmSettings: NadmSettings)

@bulkOperationsButton = {
	<div class="table-header-actions clearfix">

	</div>
}

@rightSide = {
		<div class="fx-splitter-content-inner">
			<div class="fx-entity-header">
				<div class="fx-entity-header-info">
					<span class="fx-entity-header-icon">
						<i class="glyphicon glyphicon-hdd"></i>
					</span>
					<div class="fx-entity-header-title">
						<h1>
						@i18n("nadm.search.equipment.title")
						</h1>
					</div>
				</div>
				<div class="fx-entity-header-actions">
				@authorized(AAAPIResources.device(context).C.toString()) {
					<button id="create-button" class="btn btn-primary fx-call-to-action" data-ng-click="createEquipment()">
						<i class="glyphicon glyphicon-plus"></i>
						@i18n("na.portal.nadm.create")
					</button>
				}
				</div>
			</div>

			<div class="table-header-actions">
				<div class="table-top-left-group pull-left">
				@operation.bulkOperationsButton.render(context, operations)
				</div>

				<div class="table-top-right-group pull-right">
					<div class="btn-group" data-na-portal-nadm-equipment-export-all-to-csv></div>
					<div class="btn-group fx-dropdown-reverse" data-na-portal-nadm-equipment-select-visible-columns></div>
				</div>

			</div>

			<x-shadow-scroll>
				<div id="entity-content" class="fx-entity-info">
					<div data-na-portal-nadm-equipment-table-error-mode-notification class="notification--nadm-equipments-error-mode"></div>
					<div data-na-portal-nadm-warning-about-max-selectable-equipments class="notification--max-selectable-equip"></div>
					<div data-na-portal-nadm-equipment-select-all-from-filter-notification class="notification--select-all-from-filter"></div>
					<div data-na-portal-toolbar-advanced-search data-table-id="datatableEquipment"></div>
					<table id="datatableEquipmentModel" class="table table-striped table-hover"
					data-na-portal-nadm-operations-dropdown
					data-na-portal-table
					data-na-portal-table-datatable
					data-na-portal-table-load-using-ajax
					data-config-url="@na.nadm.controllers.routes.TableConfigs.equipment(context)"
					data-na-portal-nadm-equip-search-table>
					</table>
				</div>
			</x-shadow-scroll>
		</div>
}

@mainSkel(context) {
	<div class="fx-push-footer page--nadm-search-page fx-full-height"
		data-ng-controller="NaPortalNadmCommonController"
		data-na-portal-nadm-equipment-page>
			@lateralNav.render(context, NavContext.EQUIPMENT, nadmSettings)
			<div data-na-portal-nadm-side-bar-search data-context="equipment">
				<x-splitter class="splitter splitter--nadm-search">
					<form slot="left" class="search-sidebar">
						@leftSideForm.render(context)
					</form>
					<div id="fx-splitter-content" slot="right">
						@rightSide
					</div>
					<div slot="collapsed-left">
						<div class="fx-info-sidebar-collapsed left">
							<div class="fx-sidebar-header search-sidebar__header">
								<span class="fx-entity-header-icon">
									<i class="fa fa-search"></i>
								</span>
							</div>
						</div>
					</div>
				</x-splitter>
			</div>
	</div>
}