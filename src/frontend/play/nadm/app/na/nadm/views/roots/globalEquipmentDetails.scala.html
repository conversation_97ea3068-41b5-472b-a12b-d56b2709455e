@import na.nadm.settings.NadmConstants.NavContext
@import na.nadm.settings.NadmSettings
@import na.nadm.utils.FormBuilderUtil.ViewType
@import na.nadm.views.html.components.common.detail.headerButtons
@import na.nadm.views.html.components.common.lateralNav
@import na.nadm.views.html.components.common.tableform.tableForm
@import na.nadm.views.html.skeletons.mainSkel
@import na.naportalbase.views.tags.i18n
@import pt.ptinovacao.naportal.nadm.Configurations

@(
        context: String,
        manufacturer: String,
        model: String,
        version: String,
        firmwareFilename: String,
        configurations: Configurations,
        viewContext: ViewType,
        nadmSettings: NadmSettings
)


@navBar = {
@lateralNav.render(context, NavContext.GLOBAL_EQUIP, nadmSettings)
}

@headerAction = {
@headerButtons.render(context, viewContext)
}

@mainSkel(context) {
    <div class="layout--page-with-navbar">
        @navBar
        <div class="fx-main-content-wrapper layout--header-with-tab" data-ng-controller="NaPortalNadmCommonController" data-na-portal-nadm-global-equip-details-page data-manufacturer-id="@manufacturer" data-model-id="@model" data-firmware-filename="@firmwareFilename" data-version-id="@version" data-configurations="">
            <div class="fx-entity-header">
                <div class="fx-entity-header-info">
                    <span class="fx-entity-header-icon">
                        <i class="fuxicons fuxicons-equipment-configuration"></i>
                    </span>
                    <div class="fx-entity-header-title">
                        <h1>
                            @manufacturer - @model - @version
                        </h1>
                        @if(!firmwareFilename.equals("")) {
                            <i class="fa fa-file-code"></i>
                            <span id="na-nadm-firmwareFilename" style="margin-left: 5px;">
                                @i18n("na.portal.nadm.detail.equipmentassociation.view.firmwareFilename")
                                : @firmwareFilename</span>
                        }
                    </div>
                </div>
                <div class="fx-entity-header-actions">
                @headerAction
                </div>
            </div>
            <div class="fx-entity-info">
                <x-tab-container>
                    <x-tab label="@i18n("na.portal.nadm.tabname.characteristics")">
                        <x-shadow-scroll inline>
                            <div class="tab-pane">
                            @tableForm.render(configurations, viewContext, context, nadmSettings)
                            </div>
                        </x-shadow-scroll>
                    </x-tab>
                </x-tab-container>
            </div>
        </div>
    </div>
}