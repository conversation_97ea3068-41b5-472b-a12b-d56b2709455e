# Application Errors
EXCEPTION-NADM-CONF.NADM_AP_00001.user.message = erro desconhecido
EXCEPTION-NADM-CONF.NADM_AP_00002.user.message = erro ao fundir par\u00E2metros de cache de equipamento
EXCEPTION-NADM-CONF.NADM_AP_00003.user.message = erro ao fundir par\u00E2metros de cache de cache
EXCEPTION-NADM-CONF.NADM_AP_00004.user.message = erro ao fundir par\u00E2metros de cache de  servi\u00E7
EXCEPTION-NADM-CONF.NADM_AP_00005.user.message = tipo de servi\u00E7o n\u00E3o suportado
EXCEPTION-NADM-CONF.NADM_AP_00006.user.message = ind\u00EDce de servi\u00E7 inv\u00E1lido
EXCEPTION-NADM-CONF.NADM_AP_00007.user.message = erro a processar template
EXCEPTION-NADM-CONF.NADM_AP_00008.user.message = erro a migarar dados
EXCEPTION-NADM-CONF.NADM_AP_00009.user.message = erro em hist\u00F3rico
EXCEPTION-NADM-CONF.NADM_AP_00010.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00011.user.message = erro ao fundir par\u00E2metros

# File Errors
EXCEPTION-NADM-CONF.NADM_AP_00040.user.message = erro a fazer upload do ficheiro de firmware
EXCEPTION-NADM-CONF.NADM_AP_00041.user.message = Erro a tentar eliminar ficheiro inexistente

# Template Errors
EXCEPTION-NADM-CONF.NADM_AP_00050.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00051.user.message = template j\u00E1 existe
EXCEPTION-NADM-CONF.NADM_AP_00052.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00054.user.message = template n\u00E3o encontrado
EXCEPTION-NADM-CONF.NADM_AP_00055.user.message = erro a processar template
EXCEPTION-NADM-CONF.NADM_AP_00056.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00057.user.message = template n\u00E3o encontrado
EXCEPTION-NADM-CONF.NADM_AP_00058.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00059.user.message = template n\u00E3o encontrado

# Client Errors
EXCEPTION-NADM-CONF.NADM_AP_00100.user.message = cliente n\u00E3o encontrado
EXCEPTION-NADM-CONF.NADM_AP_00102.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00103.user.message = cliente j\u00E1 existe
EXCEPTION-NADM-CONF.NADM_AP_00104.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00105.user.message = cliente tem servi\u00E7os associados n\u00E3o pode ser removido
EXCEPTION-NADM-CONF.NADM_AP_00106.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00107.user.message = cliente tem equipamentos associados n\u00E3o pode ser removido
EXCEPTION-NADM-CONF.NADM_AP_00108.user.message = cliente n\u00E3o encontrado

# History Errors
EXCEPTION-NADM-CONF.NADM_AP_00200.user.message = hist\u00F3rico de cliente n\u00E3o encontrado

# Equipment Errors
EXCEPTION-NADM-CONF.NADM_AP_00300.user.message = equipamento n\u00E3o encontrado
EXCEPTION-NADM-CONF.NADM_AP_00301.user.message = equipamento n\u00E3o encontrado
EXCEPTION-NADM-CONF.NADM_AP_00302.user.message = equipamento j\u00E1 existe
EXCEPTION-NADM-CONF.NADM_AP_00303.user.message = equipamento j\u00E1 existe
EXCEPTION-NADM-CONF.NADM_AP_00304.user.message = equipamento tem valores inv\u00E1lidos
EXCEPTION-NADM-CONF.NADM_AP_00305.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00306.user.message = equipamento j\u00E1 existe
EXCEPTION-NADM-CONF.NADM_AP_00307.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00308.user.message = cliente n\u00E3o encontrado
EXCEPTION-NADM-CONF.NADM_AP_00309.user.message = equipmento global n\u00E3o encontrado
EXCEPTION-NADM-CONF.NADM_AP_00310.user.message = n\u00E3o existem equipamentos n\u00E3o provisionados
EXCEPTION-NADM-CONF.NADM_AP_00311.user.message = cliente n\u00E3o encontrado
EXCEPTION-NADM-CONF.NADM_AP_00312.user.message = wan mac adddress j\u00E1 existe
EXCEPTION-NADM-CONF.NADM_AP_00313.user.message = wan mac adddress n\u00E3o existe
EXCEPTION-NADM-CONF.NADM_AP_00314.user.message = serial number j\u00E1 existe
EXCEPTION-NADM-CONF.NADM_AP_00315.user.message = serial number n\u00E3o existe
EXCEPTION-NADM-CONF.NADM_AP_00316.user.message = cliente n\u00E3o encontrado
EXCEPTION-NADM-CONF.NADM_AP_00317.user.message = equipamento j\u00E1 existe
EXCEPTION-NADM-CONF.NADM_AP_00318.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00319.user.message = equipamento n\u00E3o encontrado
EXCEPTION-NADM-CONF.NADM_AP_00320.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00321.user.message = equipamento n\u00E3o encontrado
EXCEPTION-NADM-CONF.NADM_AP_00322.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00323.user.message = erro de persist\u00EAncia

# Client Equipment Errors
EXCEPTION-NADM-CONF.NADM_AP_00400.user.message = equipamento de cliente n\u00E3o encontrado
EXCEPTION-NADM-CONF.NADM_AP_00401.user.message = equipamento de cliente n\u00E3o encontrado
EXCEPTION-NADM-CONF.NADM_AP_00405.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00406.user.message = equipamento de cliente j\u00E1 existe
EXCEPTION-NADM-CONF.NADM_AP_00407.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00408.user.message = cliente n\u00E3o existe
EXCEPTION-NADM-CONF.NADM_AP_00409.user.message = equipamento global n\u00E3o encontrado
EXCEPTION-NADM-CONF.NADM_AP_00410.user.message = cliente n\u00E3o existe
EXCEPTION-NADM-CONF.NADM_AP_00411.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00412.user.message = equipamento de cliente n\u00E3o encontrado
EXCEPTION-NADM-CONF.NADM_AP_00413.user.message = erro de persist\u00EAncia

# Service Errors
EXCEPTION-NADM-CONF.NADM_AP_00500.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00501.user.message = servi\u00E7o j\u00E1 existe
EXCEPTION-NADM-CONF.NADM_AP_00502.user.message = cliente n\u00E3o existe
EXCEPTION-NADM-CONF.NADM_AP_00503.user.message = servi\u00E7o n\u00E3o encontrado
EXCEPTION-NADM-CONF.NADM_AP_00506.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00507.user.message = servi\u00E7o n\u00E3o encontrado
EXCEPTION-NADM-CONF.NADM_AP_00508.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00509.user.message = servi\u00E7o n\u00E3o encontrado
EXCEPTION-NADM-CONF.NADM_AP_00510.user.message = erro de persist\u00EAncia

# Global Equipment Errors
EXCEPTION-NADM-CONF.NADM_AP_00600.user.message = equipamento global j\u00E1 existe
EXCEPTION-NADM-CONF.NADM_AP_00601.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00602.user.message = equipamento global n\u00E3o encontrado
EXCEPTION-NADM-CONF.NADM_AP_00603.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00604.user.message = equipamento global n\u00E3o encontrado
EXCEPTION-NADM-CONF.NADM_AP_00605.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00606.user.message = equipamento global n\u00E3o encontrado
EXCEPTION-NADM-CONF.NADM_AP_00607.user.message = erro de persist\u00EAncia

# Generic History Errors
EXCEPTION-NADM-CONF.NADM_AP_00700.user.message = hist\u00F3rico j\u00E1 existe
EXCEPTION-NADM-CONF.NADM_AP_00701.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00702.user.message = hist\u00F3rico n\u00E3o encontrado
EXCEPTION-NADM-CONF.NADM_AP_00703.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00704.user.message = hist\u00F3rico n\u00E3o encontrado
EXCEPTION-NADM-CONF.NADM_AP_00705.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00706.user.message = hist\u00F3rico n\u00E3o encontrado
EXCEPTION-NADM-CONF.NADM_AP_00707.user.message = erro de persist\u00EAncia

# Association Errors
EXCEPTION-NADM-CONF.NADM_AP_00800.user.message = associa\u00E7\u00E3o j\u00E1 existe
EXCEPTION-NADM-CONF.NADM_AP_00801.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00802.user.message = associa\u00E7\u00E3o n\u00E3o encontrada
EXCEPTION-NADM-CONF.NADM_AP_00803.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00804.user.message = associa\u00E7\u00E3o n\u00E3o encontrada
EXCEPTION-NADM-CONF.NADM_AP_00805.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_00806.user.message = associa\u00E7\u00E3o n\u00E3o encontrada
EXCEPTION-NADM-CONF.NADM_AP_00807.user.message = erro de persist\u00EAncia

# Cache Errors
EXCEPTION-NADM-CONF.NADM_AP_01000.user.message = cache n\u00E3o encontrada
EXCEPTION-NADM-CONF.NADM_AP_01001.user.message = cache j\u00E1 existe
EXCEPTION-NADM-CONF.NADM_AP_01002.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_01003.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_01004.user.message = cache n\u00E3o encontrada
EXCEPTION-NADM-CONF.NADM_AP_01005.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_01006.user.message = cache n\u00E3o encontrada
EXCEPTION-NADM-CONF.NADM_AP_01007.user.message = erro de persist\u00EAncia
EXCEPTION-NADM-CONF.NADM_AP_01100.user.message = hist\u00F3rico de cache n\u00E3o encontrado

# Client Request Validation Errors
EXCEPTION-NADM-CONF.NADM_CL_10000.user.message = par\u00E2metro inv\u00E1lido
EXCEPTION-NADM-CONF.NADM_CL_10001.user.message = fabricante em falta
EXCEPTION-NADM-CONF.NADM_CL_10002.user.message = modelo em falta
EXCEPTION-NADM-CONF.NADM_CL_10003.user.message = vers\u00E3o em falta
EXCEPTION-NADM-CONF.NADM_CL_10004.user.message = clientes em falta
EXCEPTION-NADM-CONF.NADM_CL_10005.user.message = servi\u00E7os em falta
EXCEPTION-NADM-CONF.NADM_CL_10006.user.message = equipamentos em falta
EXCEPTION-NADM-CONF.NADM_CL_10007.user.message = lista de cache em falta
EXCEPTION-NADM-CONF.NADM_CL_10008.user.message = cliente em falta
EXCEPTION-NADM-CONF.NADM_CL_10009.user.message = servi\u00E7o em falta
EXCEPTION-NADM-CONF.NADM_CL_10010.user.message = dados de servi\u00E7o em falta
EXCEPTION-NADM-CONF.NADM_CL_10011.user.message = tipo de servi\u00E7o em falta
EXCEPTION-NADM-CONF.NADM_CL_10012.user.message = apenas pode existir um tipo de servi\u00E7o
EXCEPTION-NADM-CONF.NADM_CL_10013.user.message = nome do cliente associado ao servi\u00E7o em falta
EXCEPTION-NADM-CONF.NADM_CL_10014.user.message = fabricante do equipamento em falta
EXCEPTION-NADM-CONF.NADM_CL_10015.user.message = modelo do equipamento em falta
EXCEPTION-NADM-CONF.NADM_CL_10016.user.message = vers\u00E3o do equipamento em falta
EXCEPTION-NADM-CONF.NADM_CL_10017.user.message = cliente associado ao equipamento em falta
EXCEPTION-NADM-CONF.NADM_CL_10018.user.message = nome do equipamento em falta
EXCEPTION-NADM-CONF.NADM_CL_10019.user.message = nome de equipamento inv\u00E1lido
EXCEPTION-NADM-CONF.NADM_CL_10020.user.message = entrada de cache em falta
EXCEPTION-NADM-CONF.NADM_CL_10021.user.message = entrada de cache inv\u00E1lida
EXCEPTION-NADM-CONF.NADM_CL_10022.user.message = equipamento em falta
EXCEPTION-NADM-CONF.NADM_CL_10023.user.message = cliente em falta
EXCEPTION-NADM-CONF.NADM_CL_10024.user.message = clientes em falta
EXCEPTION-NADM-CONF.NADM_CL_10025.user.message = apens um cliente poder\u00E1 estar associado
EXCEPTION-NADM-CONF.NADM_CL_10026.user.message = hist\u00F3rico em falta
EXCEPTION-NADM-CONF.NADM_CL_10027.user.message = data de backup em falta
EXCEPTION-NADM-CONF.NADM_CL_10028.user.message = hist\u00F3rico vers\u00E3o inv\u00E1lida
EXCEPTION-NADM-CONF.NADM_CL_10029.user.message = n\u00FAmero de subinst\u00E2ncias de template inv\u00E1lido
EXCEPTION-NADM-CONF.NADM_CL_10030.user.message = contexto desconhecido
EXCEPTION-NADM-CONF.NADM_CL_10031.user.message = template em falta
