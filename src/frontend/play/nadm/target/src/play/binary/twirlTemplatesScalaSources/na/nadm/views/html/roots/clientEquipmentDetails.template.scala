
package na.nadm.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.NadmConstants.NavContext
/*2.2*/import na.nadm.settings.NadmSettings
/*3.2*/import na.nadm.utils.FormBuilderUtil.ViewType
/*4.2*/import na.nadm.views.html.components.common.detail.headerButtons
/*5.2*/import na.nadm.views.html.components.common.lateralNav
/*6.2*/import na.nadm.views.html.components.common.tableform.tableForm
/*7.2*/import na.nadm.views.html.skeletons.mainSkel
/*8.2*/import na.naportalbase.views.html.components.tabs.contentTabComp
/*9.2*/import na.naportalbase.views.html.skeletons.tabs.contentTabSkel
/*10.2*/import na.naportalbase.views.tags.i18n
/*11.2*/import pt.ptinovacao.naportal.nadm.Configurations
/*13.2*/import scala.collection.immutable

object clientEquipmentDetails extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template8[String,String,String,String,String,Configurations,ViewType,NadmSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*15.2*/(
		context: String,
		clientId: String,
		manufacturer: String,
		model:String,
		version:String,
		configurations: Configurations,
		viewContext : ViewType,
		nadmSettings: NadmSettings
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*27.2*/navBar/*27.8*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*27.12*/("""
	"""),_display_(/*28.3*/lateralNav/*28.13*/.render(context, NavContext.CLIENT, nadmSettings)),format.raw/*28.62*/("""
""")))};def /*32.2*/header/*32.8*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*32.12*/("""
    """),format.raw/*33.5*/("""<div class="pull-left">
        <h1>
            <span class="fx-info-icon">
                <i class="fuxicons fuxicons-equipment-configuration"></i>
            </span>
            """),_display_(/*38.14*/manufacturer),format.raw/*38.26*/(""" """),format.raw/*38.27*/("""- """),_display_(/*38.30*/model),format.raw/*38.35*/(""" """),format.raw/*38.36*/("""- """),_display_(/*38.39*/version),format.raw/*38.46*/("""
        """),format.raw/*39.9*/("""</h1>
    </div>
""")))};def /*43.2*/headerAction/*43.14*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*43.18*/("""
    """),_display_(/*44.6*/headerButtons/*44.19*/.render(context, viewContext)),format.raw/*44.48*/("""
""")))};def /*47.2*/rightSide/*47.11*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*47.15*/("""
	"""),format.raw/*48.2*/("""<div id="fx-splitter-content" class="fx-splitter-content">
		<div class="fx-splitter-content-inner">
		</div>
	</div>
""")))};def /*63.2*/tabs/*63.6*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*63.10*/("""
	"""),_display_(/*64.3*/contentTabComp/*64.17*/.render(true,i18n("na.portal.nadm.tabname.characteristics"),characteristicsTabAttributes)),format.raw/*64.106*/("""
""")))};def /*67.2*/AdditionalHeaderContent/*67.25*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*67.29*/("""
	"""),_display_(/*68.3*/contentTabSkel/*68.17*/.render(tabs)),format.raw/*68.30*/("""
""")))};def /*54.2*/characteristicsTabAttributes/*54.30*/ = {{

	var charTab = immutable.Map[String, String](
		"id" -> "global-equip-edit-tab",
		"context" -> "characteristics"
	)
	charTab
}};
Seq[Any](format.raw/*24.2*/("""


"""),format.raw/*29.2*/("""


"""),format.raw/*41.2*/("""

"""),format.raw/*45.2*/("""

"""),format.raw/*52.2*/("""

"""),format.raw/*61.2*/("""

"""),format.raw/*65.2*/("""

"""),format.raw/*69.2*/("""


"""),_display_(/*72.2*/mainSkel(context)/*72.19*/ {_display_(Seq[Any](format.raw/*72.21*/("""
    """),format.raw/*73.5*/("""<div data-fuxi-header></div>
    """),_display_(/*74.6*/navBar),format.raw/*74.12*/("""
	"""),format.raw/*75.2*/("""<div class="fx-main-content-wrapper fx-with-main-nav" data-ng-controller="NaPortalNadmCommonController" data-na-portal-nadm-client-equip-details-page data-client-id=""""),_display_(/*75.169*/clientId),format.raw/*75.177*/("""" data-manufacturer=""""),_display_(/*75.199*/manufacturer),format.raw/*75.211*/("""" data-model=""""),_display_(/*75.226*/model),format.raw/*75.231*/("""" data-version=""""),_display_(/*75.248*/version),format.raw/*75.255*/("""" >
		"""),_display_(/*76.4*/na/*76.6*/.naportalbase.views.html.skeletons.pageHeaderSkel.render(header, headerAction, null, AdditionalHeaderContent)),format.raw/*76.115*/("""
		"""),format.raw/*77.3*/("""<div id="overLay">
			<div class="tab-content fx-main-tab-content" data-template-loader data-context="equipmentModel">
				<div class="tab-pane active">
					<div class="fx-tab-pane-inner">
						"""),_display_(/*81.8*/tableForm/*81.17*/.render(configurations, viewContext, context, nadmSettings)),format.raw/*81.76*/("""
					"""),format.raw/*82.6*/("""</div>
				</div>
			</div>
		</div>
	</div>
""")))}))
      }
    }
  }

  def render(context:String,clientId:String,manufacturer:String,model:String,version:String,configurations:Configurations,viewContext:ViewType,nadmSettings:NadmSettings): play.twirl.api.HtmlFormat.Appendable = apply(context,clientId,manufacturer,model,version,configurations,viewContext,nadmSettings)

  def f:((String,String,String,String,String,Configurations,ViewType,NadmSettings) => play.twirl.api.HtmlFormat.Appendable) = (context,clientId,manufacturer,model,version,configurations,viewContext,nadmSettings) => apply(context,clientId,manufacturer,model,version,configurations,viewContext,nadmSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 19:41:26 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/roots/clientEquipmentDetails.scala.html
                  HASH: da009786424aafc25bdf812319c231b3999b03b0
                  MATRIX: 665->1|721->52|765->91|818->139|890->206|952->263|1023->329|1075->376|1147->443|1219->509|1266->550|1324->604|1745->642|2022->847|2036->853|2117->857|2147->861|2166->871|2236->920|2262->930|2276->936|2357->940|2390->946|2606->1135|2639->1147|2668->1148|2698->1151|2724->1156|2753->1157|2783->1160|2811->1167|2848->1177|2891->1202|2912->1214|2993->1218|3026->1225|3048->1238|3098->1267|3124->1275|3142->1284|3223->1288|3253->1291|3399->1593|3411->1597|3492->1601|3522->1605|3545->1619|3656->1708|3682->1716|3714->1739|3795->1743|3825->1747|3848->1761|3882->1774|3908->1419|3945->1447|4116->840|4149->923|4182->1197|4213->1270|4244->1414|4275->1588|4306->1711|4337->1777|4370->1784|4396->1801|4436->1803|4469->1809|4530->1844|4557->1850|4587->1853|4782->2020|4812->2028|4862->2050|4896->2062|4939->2077|4966->2082|5011->2099|5040->2106|5074->2114|5084->2116|5215->2225|5246->2229|5473->2430|5491->2439|5571->2498|5605->2505
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|32->9|33->10|34->11|35->13|40->15|53->27|53->27|55->27|56->28|56->28|56->28|57->32|57->32|59->32|60->33|65->38|65->38|65->38|65->38|65->38|65->38|65->38|65->38|66->39|68->43|68->43|70->43|71->44|71->44|71->44|72->47|72->47|74->47|75->48|79->63|79->63|81->63|82->64|82->64|82->64|83->67|83->67|85->67|86->68|86->68|86->68|87->54|87->54|95->24|98->29|101->41|103->45|105->52|107->61|109->65|111->69|114->72|114->72|114->72|115->73|116->74|116->74|117->75|117->75|117->75|117->75|117->75|117->75|117->75|117->75|117->75|118->76|118->76|118->76|119->77|123->81|123->81|123->81|124->82
                  -- GENERATED --
              */
          