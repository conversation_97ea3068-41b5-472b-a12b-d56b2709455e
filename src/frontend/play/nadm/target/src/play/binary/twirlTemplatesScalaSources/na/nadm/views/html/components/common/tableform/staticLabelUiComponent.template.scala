
package na.nadm.views.html.components.common.tableform

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.settings.DateFormats
/*2.2*/import na.naportalbase.views.tags.i18n
/*3.2*/import pt.ptinovacao.naportal.components.{DateUiComponent, TextUiComponent, UiComponent}

object staticLabelUiComponent extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[UiComponent,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*5.2*/(uiComponent: UiComponent):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*7.2*/timeFormat/*7.12*/ = {{
	uiComponent match {
		case dateUiComponent: DateUiComponent => dateUiComponent.getType match {
			case DateUiComponent.Type.DateTimePicker => i18n(DateFormats.DATETIME_SHORT.key)
			case DateUiComponent.Type.TimePicker => i18n(DateFormats.TIME_SHORT.key)
			case DateUiComponent.Type.DatePicker => i18n(DateFormats.DATE_SHORT.key)
		}
		case _ => null
	}
}};def /*18.2*/isPassword/*18.12*/ = {{
	uiComponent match {
		case textComponent : TextUiComponent if textComponent.getType == TextUiComponent.Type.passwordField => true
		case _ => false
	}
}};
Seq[Any](format.raw/*5.28*/("""

"""),format.raw/*16.2*/("""

"""),format.raw/*23.2*/("""
"""),format.raw/*24.1*/("""<div class="fx-form-control-group-preview """),_display_(/*24.44*/{("password").when(isPassword)}),format.raw/*24.75*/("""" """),_display_(/*24.78*/{(s"""data-time-format=${timeFormat} """).when(timeFormat != null)}),format.raw/*24.145*/(""" """),format.raw/*24.146*/("""data-na-portal-nadm-value-label>
	<div class="fx-form-control-group-preview-info" >
		<i data-ng-show="model.isInherit" data-ng-class="icon.classes" title=""""),format.raw/*26.73*/("""{"""),format.raw/*26.74*/("""{"""),format.raw/*26.75*/("""icon.title"""),format.raw/*26.85*/("""}"""),format.raw/*26.86*/("""}"""),format.raw/*26.87*/(""""></i>
		<span class="content"></span>
	</div>
</div>


"""))
      }
    }
  }

  def render(uiComponent:UiComponent): play.twirl.api.HtmlFormat.Appendable = apply(uiComponent)

  def f:((UiComponent) => play.twirl.api.HtmlFormat.Appendable) = (uiComponent) => apply(uiComponent)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:39 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/common/tableform/staticLabelUiComponent.scala.html
                  HASH: 4c6876f5e95db4294b0f9fb8b1a678bec23034d1
                  MATRIX: 687->1|738->46|784->86|1199->177|1303->206|1321->216|1698->582|1717->592|1906->203|1935->579|1964->751|1992->752|2062->795|2114->826|2144->829|2233->896|2263->897|2447->1053|2476->1054|2505->1055|2543->1065|2572->1066|2601->1067
                  LINES: 24->1|25->2|26->3|31->5|35->7|35->7|44->18|44->18|50->5|52->16|54->23|55->24|55->24|55->24|55->24|55->24|55->24|57->26|57->26|57->26|57->26|57->26|57->26
                  -- GENERATED --
              */
          