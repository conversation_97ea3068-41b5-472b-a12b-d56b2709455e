
package na.nadm.views.html.components.service

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.NadmSettings
/*2.2*/import na.nadm.utils.FormBuilderUtil.ViewType
/*3.2*/import na.nadm.views.html.components.service
/*4.2*/import na.naportalbase.settings.BaseMappings
/*5.2*/import pt.ptinovacao.naportal.nadm.ServiceConfigurations

object serviceFormInner extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[ServiceConfigurations,String,NadmSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*7.2*/(configurations: ServiceConfigurations, context: String, nadmSettings: NadmSettings):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*9.2*/jsonConfigurations/*9.20*/ = {{ BaseMappings.defaultObjectMapper.valueToTree(configurations.getValues).toString }};
Seq[Any](format.raw/*7.86*/("""

"""),format.raw/*9.107*/("""

"""),format.raw/*11.1*/("""<div data-context-value="services" data-json-configs-value=""""),_display_(/*11.62*/jsonConfigurations),format.raw/*11.80*/("""" data-na-portal-nadm-service-create-form>
    """),_display_(/*12.6*/service/*12.13*/.serviceFormBlock(configurations, ViewType.EDIT, context, nadmSettings)),format.raw/*12.84*/("""
"""),format.raw/*13.1*/("""</div>"""))
      }
    }
  }

  def render(configurations:ServiceConfigurations,context:String,nadmSettings:NadmSettings): play.twirl.api.HtmlFormat.Appendable = apply(configurations,context,nadmSettings)

  def f:((ServiceConfigurations,String,NadmSettings) => play.twirl.api.HtmlFormat.Appendable) = (configurations,context,nadmSettings) => apply(configurations,context,nadmSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:29 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/service/serviceFormInner.scala.html
                  HASH: 8ada28ca85abcced52de5433ac25f0a03e6cb0bd
                  MATRIX: 678->1|722->40|775->88|827->135|879->182|1286->243|1448->332|1474->350|1591->327|1623->437|1654->441|1742->502|1781->520|1856->569|1872->576|1964->647|1993->649
                  LINES: 24->1|25->2|26->3|27->4|28->5|33->7|37->9|37->9|38->7|40->9|42->11|42->11|42->11|43->12|43->12|43->12|44->13
                  -- GENERATED --
              */
          