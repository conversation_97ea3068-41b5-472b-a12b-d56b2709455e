
package na.nadm.views.html.components.common.modalform

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.views.html.components.equipmentAssociations.createWizardSectionType
/*2.2*/import na.naportalbase.views.tags.i18n
/*3.2*/import pt.ptinovacao.naportal.nadm.model.ResourceConfigurationIf

object createAssociationwizard extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[ResourceConfigurationIf,String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*5.2*/(configuration: ResourceConfigurationIf, context: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*7.2*/advancedSearchTemplate/*7.24*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*7.28*/("""
    """),format.raw/*8.5*/("""<div id="table-advanced-search-datatableEquipmentServiceAssociations" class="fx-advanced-search in">
        <div class="advanced-search__container">
            <div class="fx-advanced-search-title">
            """),_display_(/*11.14*/{
                i18n("na.portal.nadm.associations.search.service.title")
            }),format.raw/*13.14*/("""
            """),format.raw/*14.13*/("""</div>
            <div class="advanced-search__input">
                <form class="form-group fx-search-input">
                    <div class="fx-pos-rel">
                        <button id="table-advanced-search-datatableEquipmentServiceAssociations__search-button" class="glyphicon glyphicon-search"></button>
                        <input id="table-advanced-search-datatableEquipmentServiceAssociations__search-input"
                        type="text"
                        placeholder=""""),_display_(/*21.39*/{
                            i18n("datatables.action.label.search")
                        }),format.raw/*23.26*/(""""
                        class="form-control input-sm full-width">
                    </div>
                </form>
            </div>
            <div class="fx-advanced-search-help">
                <a class="fx-alert-button"
                popover-value='"""),_display_(/*30.33*/{
                    i18n("datatables.search.advancedsearch.minimum.length")
                }),format.raw/*32.18*/("""'
                popover-placement="left"
                data-enable-popover>
                    <i class="fuxicons fuxicons-help"></i>
                </a>
            </div>
        </div>
    </div>
""")))};
Seq[Any](format.raw/*5.59*/("""

"""),format.raw/*40.2*/("""

"""),format.raw/*42.1*/("""<div id="fx-wizard-modal" class="fx-wizard-wrapper" data-na-portal-nadm-equipment-associations-wizard>

    <nav>
        <ul class="list-unstyled fx-nav-items">
            <li id="navWizStep1" data-na-portal-wizard-tab data-label=""""),_display_(/*46.73*/i18n("na.type")),format.raw/*46.88*/("""" data-tab-name="type"></li>
            <li id="navWizStep2" data-na-portal-wizard-tab data-label=""""),_display_(/*47.73*/i18n("na.values")),format.raw/*47.90*/("""" data-tab-name="values"></li>
        </ul>
    </nav>
    <section id="wizStep1-m" class="fx-wizardsteps-content" data-ng-class=""""),format.raw/*50.76*/("""{"""),format.raw/*50.77*/("""hidden: stepNumber != 1"""),format.raw/*50.100*/("""}"""),format.raw/*50.101*/("""">
    """),_display_(/*51.6*/createWizardSectionType/*51.29*/.render(configuration)),format.raw/*51.51*/("""
    """),format.raw/*52.5*/("""</section>
        <!--STEP 2-->
    <section id="wizStep2-m" class="fx-wizardsteps-content" data-ng-show="stepNumber == 2 && !isService">
        <h4><span class="fx-wizardstep-number">2</span> | """),_display_(/*55.60*/i18n("na.wizard.step", "2")),format.raw/*55.87*/("""</h4>
        <p class="fx-required-fields-message">"""),_display_(/*56.48*/i18n("na.fill.all.required.fields")),format.raw/*56.83*/("""</p>
        <div class="fx-wizardsteps-content-block" data-na-portal-wizard-template-loader>

        </div>
    </section>

    <section id="wizStep2-service" class="fx-wizardsteps-content" data-ng-if="stepNumber == 2 && isService">
        <div id="entity-content" class="fx-entity-info equipment-associations-modal__content">
            <div id="selectLineForm" class="form-group equipment-associations-modal__line" data-na-portal-nadm-equipment-associations-select-line-form style="margin-bottom: 40px;">
                <div class="col-sm-12 col-md-4 col-lg-4" style="left: 40%;
                    display: flex;
                    align-items: center;">
                    <label for="line" class="col-sm-2 control-label fx-required-field">"""),_display_(/*68.89*/i18n("na.portal.nadm.associations.type.service.line")),format.raw/*68.142*/("""</label>
                    <div class="col-xs-8">
                    </div>
                </div>
            </div>
            <div class="equipment-associations-modal__advanced-search" data-na-portal-nadm-equipment-associations-toolbar-advanced-search data-table-id="datatableEquipmentServiceAssociations" data-ng-hide="noSelectedLine">
            """),_display_(/*74.14*/advancedSearchTemplate),format.raw/*74.36*/("""
            """),format.raw/*75.13*/("""</div>
            <div class="equipment-associations-modal__table" data-ng-hide="noSelectedLine">
                <x-shadow-scroll>
                    <table id="datatableEquipmentServiceAssociations"
                    class="table table-striped table-hover"
                    data-na-portal-table
                    data-na-portal-table-datatable
                    data-na-portal-table-load-using-ajax
                    data-config-url=""""),_display_(/*83.39*/na/*83.41*/.nadm.controllers.routes.TableConfigs.equipmentAssociationsTypeServices(context)),format.raw/*83.121*/(""""
                    data-na-portal-nadm-equipment-associations-services-table
                    ></table>
                </x-shadow-scroll>
            </div>
        </div>
    </section>
</div>
"""))
      }
    }
  }

  def render(configuration:ResourceConfigurationIf,context:String): play.twirl.api.HtmlFormat.Appendable = apply(configuration,context)

  def f:((ResourceConfigurationIf,String) => play.twirl.api.HtmlFormat.Appendable) = (configuration,context) => apply(configuration,context)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:39 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/common/modalform/createAssociationwizard.scala.html
                  HASH: 9ad004618125471cbd9cc36eb6957a6af442955e
                  MATRIX: 687->1|777->85|823->125|1234->192|1369->252|1399->274|1479->278|1510->283|1751->497|1860->585|1901->598|2428->1098|2543->1192|2833->1455|2949->1550|3194->249|3223->1756|3252->1758|3513->1992|3549->2007|3677->2108|3715->2125|3874->2256|3903->2257|3955->2280|3985->2281|4019->2289|4051->2312|4094->2334|4126->2339|4351->2537|4399->2564|4479->2617|4535->2652|5314->3404|5389->3457|5773->3814|5816->3836|5857->3849|6334->4299|6345->4301|6447->4381
                  LINES: 24->1|25->2|26->3|31->5|35->7|35->7|37->7|38->8|41->11|43->13|44->14|51->21|53->23|60->30|62->32|71->5|73->40|75->42|79->46|79->46|80->47|80->47|83->50|83->50|83->50|83->50|84->51|84->51|84->51|85->52|88->55|88->55|89->56|89->56|101->68|101->68|107->74|107->74|108->75|116->83|116->83|116->83
                  -- GENERATED --
              */
          