
package na.nadm.views.html.components.clientEquipmentModel

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.NadmSettings
/*2.2*/import na.nadm.utils.FormBuilderUtil.ViewType
/*3.2*/import na.nadm.views.html.components.common.tableform.tableForm
/*4.2*/import na.naportalbase.views.tags.i18n
/*5.2*/import pt.ptinovacao.naportal.nadm.Configurations

object clientEquipmentDetailsModal extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template8[String,String,String,String,String,Configurations,ViewType,NadmSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*7.2*/(
        context: String,
        clientId: String,
        manufacturer: String,
        model: String,
        version: String,
        configurations: Configurations,
        viewContext: ViewType,
        nadmSettings: NadmSettings
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*16.2*/("""

"""),format.raw/*18.1*/("""<div class="fx-main-content-wrapper" data-ng-controller="NaPortalNadmCommonController" data-na-portal-nadm-client-equip-details-page data-client-id=""""),_display_(/*18.151*/clientId),format.raw/*18.159*/("""" data-manufacturer=""""),_display_(/*18.181*/manufacturer),format.raw/*18.193*/("""" data-model=""""),_display_(/*18.208*/model),format.raw/*18.213*/("""" data-version=""""),_display_(/*18.230*/version),format.raw/*18.237*/("""" >
    <x-shadow-scroll vertical>
        <x-tab-container>
            <x-tab label=""""),_display_(/*21.28*/i18n("na.portal.nadm.tabname.characteristics")),format.raw/*21.74*/("""">
                <div class="tab-pane">
                """),_display_(/*23.18*/tableForm/*23.27*/.render(configurations, viewContext, context, nadmSettings)),format.raw/*23.86*/("""
                """),format.raw/*24.17*/("""</div>
            </x-tab>
        </x-tab-container>
    </x-shadow-scroll>
</div>"""))
      }
    }
  }

  def render(context:String,clientId:String,manufacturer:String,model:String,version:String,configurations:Configurations,viewContext:ViewType,nadmSettings:NadmSettings): play.twirl.api.HtmlFormat.Appendable = apply(context,clientId,manufacturer,model,version,configurations,viewContext,nadmSettings)

  def f:((String,String,String,String,String,Configurations,ViewType,NadmSettings) => play.twirl.api.HtmlFormat.Appendable) = (context,clientId,manufacturer,model,version,configurations,viewContext,nadmSettings) => apply(context,clientId,manufacturer,model,version,configurations,viewContext,nadmSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:28 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/clientEquipmentModel/clientEquipmentDetailsModal.scala.html
                  HASH: 54e816facd4b9d38d6293ed70021efed236a542e
                  MATRIX: 691->1|735->39|788->86|859->151|905->191|1346->243|1679->481|1708->483|1886->633|1916->641|1966->663|2000->675|2043->690|2070->695|2115->712|2144->719|2259->807|2326->853|2412->912|2430->921|2510->980|2555->997
                  LINES: 24->1|25->2|26->3|27->4|28->5|33->7|47->16|49->18|49->18|49->18|49->18|49->18|49->18|49->18|49->18|49->18|52->21|52->21|54->23|54->23|54->23|55->24
                  -- GENERATED --
              */
          