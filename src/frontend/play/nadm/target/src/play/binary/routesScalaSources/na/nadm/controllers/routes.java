// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/conf/nadm.routes
// @DATE:Tue Jul 01 09:49:39 WEST 2025

package na.nadm.controllers;

import nadm.RoutesPrefix;

public class routes {
  
  public static final na.nadm.controllers.ReverseDetail Detail = new na.nadm.controllers.ReverseDetail(RoutesPrefix.byNamePrefix());
  public static final na.nadm.controllers.ReverseAssets Assets = new na.nadm.controllers.ReverseAssets(RoutesPrefix.byNamePrefix());
  public static final na.nadm.controllers.ReverseExport Export = new na.nadm.controllers.ReverseExport(RoutesPrefix.byNamePrefix());
  public static final na.nadm.controllers.ReverseOperation Operation = new na.nadm.controllers.ReverseOperation(RoutesPrefix.byNamePrefix());
  public static final na.nadm.controllers.ReverseTag Tag = new na.nadm.controllers.ReverseTag(RoutesPrefix.byNamePrefix());
  public static final na.nadm.controllers.ReverseApplication Application = new na.nadm.controllers.ReverseApplication(RoutesPrefix.byNamePrefix());
  public static final na.nadm.controllers.ReverseTableConfigs TableConfigs = new na.nadm.controllers.ReverseTableConfigs(RoutesPrefix.byNamePrefix());
  public static final na.nadm.controllers.ReverseSearch Search = new na.nadm.controllers.ReverseSearch(RoutesPrefix.byNamePrefix());
  public static final na.nadm.controllers.ReverseFilters Filters = new na.nadm.controllers.ReverseFilters(RoutesPrefix.byNamePrefix());
  public static final na.nadm.controllers.ReverseNavigation Navigation = new na.nadm.controllers.ReverseNavigation(RoutesPrefix.byNamePrefix());

  public static class javascript {
    
    public static final na.nadm.controllers.javascript.ReverseDetail Detail = new na.nadm.controllers.javascript.ReverseDetail(RoutesPrefix.byNamePrefix());
    public static final na.nadm.controllers.javascript.ReverseAssets Assets = new na.nadm.controllers.javascript.ReverseAssets(RoutesPrefix.byNamePrefix());
    public static final na.nadm.controllers.javascript.ReverseExport Export = new na.nadm.controllers.javascript.ReverseExport(RoutesPrefix.byNamePrefix());
    public static final na.nadm.controllers.javascript.ReverseOperation Operation = new na.nadm.controllers.javascript.ReverseOperation(RoutesPrefix.byNamePrefix());
    public static final na.nadm.controllers.javascript.ReverseTag Tag = new na.nadm.controllers.javascript.ReverseTag(RoutesPrefix.byNamePrefix());
    public static final na.nadm.controllers.javascript.ReverseApplication Application = new na.nadm.controllers.javascript.ReverseApplication(RoutesPrefix.byNamePrefix());
    public static final na.nadm.controllers.javascript.ReverseTableConfigs TableConfigs = new na.nadm.controllers.javascript.ReverseTableConfigs(RoutesPrefix.byNamePrefix());
    public static final na.nadm.controllers.javascript.ReverseSearch Search = new na.nadm.controllers.javascript.ReverseSearch(RoutesPrefix.byNamePrefix());
    public static final na.nadm.controllers.javascript.ReverseFilters Filters = new na.nadm.controllers.javascript.ReverseFilters(RoutesPrefix.byNamePrefix());
    public static final na.nadm.controllers.javascript.ReverseNavigation Navigation = new na.nadm.controllers.javascript.ReverseNavigation(RoutesPrefix.byNamePrefix());
  }

}
