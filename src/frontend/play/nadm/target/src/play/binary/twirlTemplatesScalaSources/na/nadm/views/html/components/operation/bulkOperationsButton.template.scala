
package na.nadm.views.html.components.operation

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.services.Authorization
/*2.2*/import na.nadm.views.tags.operationI18n
/*3.2*/import na.naportalbase.views.tags.i18n

object bulkOperationsButton extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[String,java.util.Map[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*5.2*/(context: String, operations: java.util.Map[String, String]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*7.2*/operationMenuItem/*7.19*/(operationId: String, operationName: String):play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*7.67*/("""
    """),_display_(/*8.6*/defining(operationI18n(context, operationName))/*8.53*/{ operationI18nResult =>_display_(Seq[Any](format.raw/*8.77*/("""
        """),format.raw/*9.9*/("""<li class="dropdown-menu__item--nadm-bulk-operation">
            <a data-action="operation-execution" data-id=""""),_display_(/*10.60*/operationId),format.raw/*10.71*/("""" data-name=""""),_display_(/*10.85*/operationName),format.raw/*10.98*/("""" title=""""),_display_(/*10.108*/operationI18nResult/*10.127*/.title),format.raw/*10.133*/("""">
                """),_display_(/*11.18*/{operationI18nResult.name}),format.raw/*11.44*/("""
            """),format.raw/*12.13*/("""</a>
        </li>
    """)))}),format.raw/*14.6*/("""
""")))};def /*16.2*/canManageTags/*16.15*/ = {{Authorization.isAuthorizedToManageTags(context)}};def /*17.2*/canRemoveEquipments/*17.21*/ = {{Authorization.isAuthorizedToMassivelyDeleteEquipments(context)}};
Seq[Any](format.raw/*5.62*/("""

"""),format.raw/*15.2*/("""
"""),format.raw/*16.68*/("""
"""),format.raw/*17.89*/("""

"""),format.raw/*19.1*/("""<div class="bulk-operations__button btn-group pull-left" data-na-portal-nadm-bulk-operations-dropdown>
    <button id="bulkOperationsButton" type="button" class="pull-right btn btn-primary btn-sm" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
        <span>"""),_display_(/*21.16*/i18n("na.portal.nadm.bulkoperations.button")),format.raw/*21.60*/("""</span>
        <i class="caret fx-icon"></i>
    </button>
    <ul id="bulkOperationsDropdown" class="bulk-operations__dropdown dropdown-menu fx-dropdown fx-dropdown-s fx-dropdown-open-to-right" aria-labelledby="bulkOperationsButton">
        """),_display_(/*25.10*/if(!operations.isEmpty)/*25.33*/{_display_(Seq[Any](format.raw/*25.34*/("""
            """),format.raw/*26.13*/("""<li class="dropdown-header"><b>"""),_display_(/*26.45*/i18n("na.portal.nadm.button.bulkoperations.group")),format.raw/*26.95*/("""</b></li>
            """),_display_(/*27.14*/for((operationId, operationName) <- operations) yield /*27.61*/{_display_(Seq[Any](format.raw/*27.62*/(""" """),_display_(/*27.64*/operationMenuItem(operationId, operationName)),format.raw/*27.109*/(""" """)))}),format.raw/*27.111*/("""
            """),format.raw/*28.13*/("""<li role="separator" class="divider"></li>
        """)))}),format.raw/*29.10*/("""
        """),_display_(/*30.10*/if(canManageTags)/*30.27*/{_display_(Seq[Any](format.raw/*30.28*/("""
            """),format.raw/*31.13*/("""<li data-action="manage-tags"><a><i class="glyphicon glyphicon-tag"></i><span> """),_display_(/*31.93*/i18n("na.portal.nadm.bulkoperations.tags.button")),format.raw/*31.142*/("""</span></a><li></li>
        """)))}),format.raw/*32.10*/("""
        """),format.raw/*33.9*/("""<li><a href="#" class="bulk-operations__massive-action-export"><i class="glyphicon glyphicon-export"></i><span> """),_display_(/*33.122*/i18n("na.portal.nadm.bulkoperations.export.button")),format.raw/*33.173*/("""</span></a><li></li>
        """),_display_(/*34.10*/if(canRemoveEquipments)/*34.33*/{_display_(Seq[Any](format.raw/*34.34*/("""
            """),format.raw/*35.13*/("""<li data-action="delete"><a><i class="glyphicon glyphicon-remove"></i><span> """),_display_(/*35.91*/i18n("na.portal.nadm.bulkoperations.delete.button")),format.raw/*35.142*/("""</span></a></li>
        """)))}),format.raw/*36.10*/("""
    """),format.raw/*37.5*/("""</ul>
</div>


"""))
      }
    }
  }

  def render(context:String,operations:java.util.Map[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(context,operations)

  def f:((String,java.util.Map[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (context,operations) => apply(context,operations)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:29 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/operation/bulkOperationsButton.scala.html
                  HASH: c760cfcd9b54f47ee36faa152b0144db7e99f76d
                  MATRIX: 680->1|725->40|772->81|1160->122|1298->185|1323->202|1447->250|1478->256|1533->303|1594->327|1629->336|1769->449|1801->460|1842->474|1876->487|1914->497|1943->516|1971->522|2018->542|2065->568|2106->581|2160->605|2185->609|2207->622|2274->677|2302->696|2400->182|2429->607|2458->675|2487->764|2516->766|2822->1045|2887->1089|3159->1334|3191->1357|3230->1358|3271->1371|3330->1403|3401->1453|3451->1476|3514->1523|3553->1524|3582->1526|3649->1571|3683->1573|3724->1586|3807->1638|3844->1648|3870->1665|3909->1666|3950->1679|4057->1759|4128->1808|4189->1838|4225->1847|4366->1960|4439->2011|4496->2041|4528->2064|4567->2065|4608->2078|4713->2156|4786->2207|4843->2233|4875->2238
                  LINES: 24->1|25->2|26->3|31->5|35->7|35->7|37->7|38->8|38->8|38->8|39->9|40->10|40->10|40->10|40->10|40->10|40->10|40->10|41->11|41->11|42->12|44->14|45->16|45->16|45->17|45->17|46->5|48->15|49->16|50->17|52->19|54->21|54->21|58->25|58->25|58->25|59->26|59->26|59->26|60->27|60->27|60->27|60->27|60->27|60->27|61->28|62->29|63->30|63->30|63->30|64->31|64->31|64->31|65->32|66->33|66->33|66->33|67->34|67->34|67->34|68->35|68->35|68->35|69->36|70->37
                  -- GENERATED --
              */
          