
package na.nadm.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.NadmSettings
/*2.2*/import na.nadm.views.html.skeletons.mainSkel
/*3.2*/import na.naportalbase.views.tags.i18n
/*4.2*/import na.nadm.settings.NadmConstants.NavContext
/*5.2*/import na.nadm.views.html.components.common.lateralNav
/*6.2*/import na.nadm.views.html.components.client.leftSideForm
/*7.2*/import pt.alticelabs.nossis.security.views.html.authorized
/*8.2*/import na.nadm.settings.AAAPIResources

object clientSearch extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[String,NadmSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*10.2*/(context: String, nadmSettings: NadmSettings):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*12.2*/rightSide/*12.11*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*12.15*/("""
	"""),format.raw/*13.2*/("""<div class="fx-splitter-content-inner">
		<div class="fx-entity-header">
			<div class="fx-entity-header-info">
				<span class="fx-entity-header-icon">
					<i class="glyphicon glyphicon-user"></i>
				</span>
				<div class="fx-entity-header-title">
					<h1>
					"""),_display_(/*21.7*/i18n("nadm.search.client.title")),format.raw/*21.39*/("""
					"""),format.raw/*22.6*/("""</h1>
				</div>
			</div>
			<div class="fx-entity-header-actions">
			"""),_display_(/*26.5*/authorized(AAAPIResources.device(context).C.toString())/*26.60*/ {_display_(Seq[Any](format.raw/*26.62*/("""
				"""),format.raw/*27.5*/("""<button id="create-button" class="btn btn-primary fx-call-to-action" data-ng-click="createClient()">
					<i class="glyphicon glyphicon-plus"></i>
					"""),_display_(/*29.7*/i18n("na.portal.nadm.create")),format.raw/*29.36*/("""
				"""),format.raw/*30.5*/("""</button>
			""")))}),format.raw/*31.5*/("""
			"""),format.raw/*32.4*/("""</div>
		</div>

		<x-shadow-scroll vertical>
			<div id="entity-content" class="fx-entity-info" data-na-portal-nadm-client-create regex=""""),_display_(/*36.94*/nadmSettings/*36.106*/.getClientNameRegex(context)),format.raw/*36.134*/("""">
				<div data-na-portal-datatables-toolbar class="fx-bulk-actions fx-table-actions clearfix"></div>
				<div data-na-portal-toolbar-advanced-search data-table-id="datatableClient"></div>
				<table id="datatableClient" class="table table-striped table-hover"
					   data-na-portal-table
					   data-na-portal-table-datatable
					   data-na-portal-table-load-using-ajax
					   data-config-url=""""),_display_(/*43.27*/na/*43.29*/.nadm.controllers.routes.TableConfigs.client(context)),format.raw/*43.82*/(""""
					   data-na-portal-nadm-client-table>
				</table>
			</div>
		</x-shadow-scroll>
	</div>
""")))};
Seq[Any](format.raw/*10.47*/("""

"""),format.raw/*49.2*/("""

"""),_display_(/*51.2*/mainSkel(context)/*51.19*/ {_display_(Seq[Any](format.raw/*51.21*/("""
	"""),format.raw/*52.2*/("""<div class="fx-push-footer page--nadm-search-page fx-full-height" data-na-portal-nadm-client-page>
		"""),_display_(/*53.4*/lateralNav/*53.14*/.render(context, NavContext.CLIENT, nadmSettings)),format.raw/*53.63*/("""
		"""),format.raw/*54.3*/("""<div data-na-portal-nadm-side-bar-search data-context="client">
			<x-splitter class="splitter splitter--nadm-search">
				<form slot="left"  class="search-sidebar">
				"""),_display_(/*57.6*/leftSideForm/*57.18*/.render()),format.raw/*57.27*/("""
				"""),format.raw/*58.5*/("""</form>
				<div id="fx-splitter-content" slot="right">
				"""),_display_(/*60.6*/rightSide),format.raw/*60.15*/("""
				"""),format.raw/*61.5*/("""</div>
				<div slot="collapsed-left">
					<div class="fx-info-sidebar-collapsed left">
						<div class="fx-sidebar-header search-sidebar__header">
							<span class="fx-entity-header-icon">
								<i class="fa fa-search"></i>
							</span>
						</div>
					</div>
				</div>
			</x-splitter>
		</div>
	</div>
""")))}))
      }
    }
  }

  def render(context:String,nadmSettings:NadmSettings): play.twirl.api.HtmlFormat.Appendable = apply(context,nadmSettings)

  def f:((String,NadmSettings) => play.twirl.api.HtmlFormat.Appendable) = (context,nadmSettings) => apply(context,nadmSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:39 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/roots/clientSearch.scala.html
                  HASH: 23729d6b29d24bdeb906721493c1e38a6d5c7a3f
                  MATRIX: 665->1|709->40|761->87|807->128|863->179|925->236|989->295|1055->356|1419->399|1543->449|1561->458|1642->462|1672->465|1974->741|2027->773|2061->780|2164->857|2228->912|2268->914|2301->920|2482->1075|2532->1104|2565->1110|2610->1125|2642->1130|2812->1273|2834->1285|2884->1313|3319->1721|3330->1723|3404->1776|3547->444|3578->1879|3609->1884|3635->1901|3675->1903|3705->1906|3834->2009|3853->2019|3923->2068|3954->2072|4154->2246|4175->2258|4205->2267|4238->2273|4327->2336|4357->2345|4390->2351
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|36->10|40->12|40->12|42->12|43->13|51->21|51->21|52->22|56->26|56->26|56->26|57->27|59->29|59->29|60->30|61->31|62->32|66->36|66->36|66->36|73->43|73->43|73->43|80->10|82->49|84->51|84->51|84->51|85->52|86->53|86->53|86->53|87->54|90->57|90->57|90->57|91->58|93->60|93->60|94->61
                  -- GENERATED --
              */
          