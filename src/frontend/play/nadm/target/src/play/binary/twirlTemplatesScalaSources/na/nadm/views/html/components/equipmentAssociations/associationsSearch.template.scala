
package na.nadm.views.html.components.equipmentAssociations

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.controllers.routes.TableConfigs
/*2.2*/import na.nadm.utils.FormBuilderUtil.ViewType
/*3.2*/import na.nadm.views.html.components.equipmentAssociations.leftSideForm
/*5.2*/import scala.collection.mutable

object associationsSearch extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[String,String,ViewType,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*7.2*/(context: String, equipmentId: String, viewContext: ViewType):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*9.2*/leftSideBarAttributes/*9.23*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-na-portal-nadm-equipment-associations-search-form", null)
    map
}};
Seq[Any](format.raw/*7.63*/("""

"""),format.raw/*13.2*/("""

"""),format.raw/*15.1*/("""<div class="page--nadm-search-page fx-full-height" data-na-portal-nadm-equip-associations-tab>
    <div data-na-portal-nadm-equipment-associations-search-form>
        <x-splitter class="splitter splitter--nadm-search">
            <form slot="left" class="search-sidebar">
            """),_display_(/*19.14*/leftSideForm/*19.26*/.render(context)),format.raw/*19.42*/("""
            """),format.raw/*20.13*/("""</form>
            <div id="fx-splitter-content" slot="right">
                <div class="fx-splitter-content-inner fx-splitter-content-inner--nadm-equipment-associations">
                    <div class="associations-page__header">
                        <div class="alert alert--inline alert-info">
                            <div class="fx-alert-icon">
                            </div>
                            <div class="fx-alert-message--inline">
                            </div>
                        </div>
                        <div class="btn-wrapper btn-group" data-na-portal-nadm-association-types-dropdown>
                            <div class="btn-wrapper__child">
                                <button id="associateTypeService" type="button"
                                class="btn btn-primary btn-sm dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="glyphicon glyphicon-plus"></i>
                                    <span>
                                        <x-i18n key="na.portal.nadm.associations.type.service.associate"></x-i18n>
                                    </span>
                                    <i class="caret fx-icon"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="entity-info-container">
                        <x-shadow-scroll>
                            <div id="entity-content" class="fx-entity-info"
                            data-na-portal-nadm-equipment-associations-datatable data-equipment-id=""""),_display_(/*46.102*/equipmentId),format.raw/*46.113*/(""""
                            data-mode=""""),_display_(/*47.41*/viewContext/*47.52*/.toString.toLowerCase),format.raw/*47.73*/("""">
                                <table id="datatableEquipmentAssociations" class="table table-striped table-hover"
                                data-na-portal-table
                                data-na-portal-table-datatable
                                data-config-url=""""),_display_(/*51.51*/TableConfigs/*51.63*/.equipmentassociations(context, equipmentId)),format.raw/*51.107*/(""""
                                data-na-portal-nadm-equipment-associations-table>
                                </table>
                            </div>
                        </x-shadow-scroll>
                    </div>
                </div>
            </div>
            <div slot="collapsed-left">
                <div class="fx-info-sidebar-collapsed left">
                    <div class="fx-sidebar-header search-sidebar__header">
                        <span class="fx-entity-header-icon">
                            <i class="fa fa-search"></i>
                        </span>
                    </div>
                </div>
            </div>
        </x-splitter>
    </div>
</div>
"""))
      }
    }
  }

  def render(context:String,equipmentId:String,viewContext:ViewType): play.twirl.api.HtmlFormat.Appendable = apply(context,equipmentId,viewContext)

  def f:((String,String,ViewType) => play.twirl.api.HtmlFormat.Appendable) = (context,equipmentId,viewContext) => apply(context,equipmentId,viewContext)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:29 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/equipmentAssociations/associationsSearch.scala.html
                  HASH: a71e335b5a462b908421f4d350390fb4f3ca0805
                  MATRIX: 692->1|746->50|799->98|878->174|1243->210|1382->276|1411->297|1584->271|1615->440|1646->444|1964->735|1985->747|2022->763|2064->777|3795->2480|3828->2491|3898->2534|3918->2545|3960->2566|4275->2854|4296->2866|4362->2910
                  LINES: 24->1|25->2|26->3|27->5|32->7|36->9|36->9|41->7|43->13|45->15|49->19|49->19|49->19|50->20|76->46|76->46|77->47|77->47|77->47|81->51|81->51|81->51
                  -- GENERATED --
              */
          