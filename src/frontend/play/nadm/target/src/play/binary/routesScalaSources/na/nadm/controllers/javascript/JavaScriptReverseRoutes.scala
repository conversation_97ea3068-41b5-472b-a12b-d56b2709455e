// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/conf/nadm.routes
// @DATE:Tue Jul 01 09:49:39 WEST 2025

import play.api.routing.JavaScriptReverseRoute


import _root_.controllers.Assets.Asset

// @LINE:2
package na.nadm.controllers.javascript {

  // @LINE:23
  class ReverseDetail(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:55
    def updateEquipment: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Detail.updateEquipment",
      """
        function(context0,clientId1,equipmentId2) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/equipment/characteristics/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("clientId", clientId1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("equipmentId", equipmentId2))})
        }
      """
    )
  
    // @LINE:69
    def createService: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Detail.createService",
      """
        function(context0,serviceType1) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/service/create/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("serviceType", serviceType1))})
        }
      """
    )
  
    // @LINE:43
    def createClientEquipment: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Detail.createClientEquipment",
      """
        function(context0,manufacturer1,model2,version3,clientId4) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/clientequipment/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("manufacturer", manufacturer1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("model", model2)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version3)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("clientId", clientId4))})
        }
      """
    )
  
    // @LINE:73
    def updateService: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Detail.updateService",
      """
        function(context0,clientName1,serviceName2) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/service/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("clientName", clientName1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("serviceName", serviceName2))})
        }
      """
    )
  
    // @LINE:36
    def updateClientEquipment: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Detail.updateClientEquipment",
      """
        function(context0,clientId1,manufacturer2,model3,version4) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/client/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("clientId", clientId1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("manufacturer", manufacturer2)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("model", model3)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version4))})
        }
      """
    )
  
    // @LINE:39
    def removeClientEquipment: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Detail.removeClientEquipment",
      """
        function(context0,clientId1,manufacturer2,model3,version4) {
          return _wA({method:"DELETE", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/client/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("clientId", clientId1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("manufacturer", manufacturer2)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("model", model3)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version4))})
        }
      """
    )
  
    // @LINE:40
    def removeClient: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Detail.removeClient",
      """
        function(context0,clientId1) {
          return _wA({method:"DELETE", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/client/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("clientId", clientId1))})
        }
      """
    )
  
    // @LINE:56
    def updateEquipmentAssociation: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Detail.updateEquipmentAssociation",
      """
        function(context0,equipmentId1) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/equipment/association/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("equipmentId", equipmentId1))})
        }
      """
    )
  
    // @LINE:74
    def removeService: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Detail.removeService",
      """
        function(context0,clientName1,serviceName2) {
          return _wA({method:"DELETE", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/service/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("clientName", clientName1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("serviceName", serviceName2))})
        }
      """
    )
  
    // @LINE:23
    def getGlobalEquipmentDetails: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Detail.getGlobalEquipmentDetails",
      """
        function(context0,manufacturer1,model2,version3) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/globalequipment/details/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("manufacturer", manufacturer1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("model", model2)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version3))})
        }
      """
    )
  
    // @LINE:24
    def updateGlobalEquipment: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Detail.updateGlobalEquipment",
      """
        function(context0,manufacturer1,model2,version3) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/globalequipment/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("manufacturer", manufacturer1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("model", model2)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version3))})
        }
      """
    )
  
    // @LINE:37
    def createClient: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Detail.createClient",
      """
        function(context0,clientId1) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/client/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("clientId", clientId1)) + "/create"})
        }
      """
    )
  
    // @LINE:54
    def createEquipment: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Detail.createEquipment",
      """
        function(context0,manufacturer1,model2,version3,clientId4) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/equipment/create/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("manufacturer", manufacturer1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("model", model2)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version3)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("clientId", clientId4))})
        }
      """
    )
  
    // @LINE:25
    def updateGlobalEquipmentAndTemplate: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Detail.updateGlobalEquipmentAndTemplate",
      """
        function(context0,manufacturer1,model2,version3,firmwareFilename4) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/globalequipment/template/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("manufacturer", manufacturer1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("model", model2)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version3)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("firmwareFilename", firmwareFilename4))})
        }
      """
    )
  
    // @LINE:62
    def removeEquipment: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Detail.removeEquipment",
      """
        function(context0,equipmentId1) {
          return _wA({method:"DELETE", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/equipment/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("equipmentId", equipmentId1))})
        }
      """
    )
  
    // @LINE:26
    def removeGlobalEquipment: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Detail.removeGlobalEquipment",
      """
        function(context0,manufacturer1,model2,version3) {
          return _wA({method:"DELETE", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/globalequipment/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("manufacturer", manufacturer1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("model", model2)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version3))})
        }
      """
    )
  
  }

  // @LINE:16
  class ReverseAssets(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:16
    def versioned: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Assets.versioned",
      """
        function(file1) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "nadm/assets/" + (""" + implicitly[play.api.mvc.PathBindable[Asset]].javascriptUnbind + """)("file", file1)})
        }
      """
    )
  
  }

  // @LINE:59
  class ReverseExport(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:59
    def getEquipmentsCsv: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Export.getEquipmentsCsv",
      """
        function(context0,paramsOnBase641) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/equipment/csv/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("paramsOnBase64", paramsOnBase641))})
        }
      """
    )
  
  }

  // @LINE:63
  class ReverseOperation(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:114
    def deleteFirmwareFile: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Operation.deleteFirmwareFile",
      """
        function(context0,manufacturer1,model2,version3) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/delete/firmwarefile/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("manufacturer", manufacturer1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("model", model2)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version3))})
        }
      """
    )
  
    // @LINE:107
    def getEquipmentOperations: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Operation.getEquipmentOperations",
      """
        function(context0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/operation/operations/equipment"})
        }
      """
    )
  
    // @LINE:106
    def getMappingModel: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Operation.getMappingModel",
      """
        function(context0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/operation/mappingmodel"})
        }
      """
    )
  
    // @LINE:115
    def executeOperation: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Operation.executeOperation",
      """
        function(context0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/operation"})
        }
      """
    )
  
    // @LINE:113
    def uploadFirmwareFile: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Operation.uploadFirmwareFile",
      """
        function(context0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/upload/firmwarefile"})
        }
      """
    )
  
    // @LINE:110
    def getMassiveOperationForm: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Operation.getMassiveOperationForm",
      """
        function(context0,operationId1) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/operation/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("operationId", operationId1)) + "/form/massive"})
        }
      """
    )
  
    // @LINE:116
    def executeBulkOperation: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Operation.executeBulkOperation",
      """
        function(context0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/bulkoperation"})
        }
      """
    )
  
    // @LINE:108
    def getGlobalEquipmentOperations: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Operation.getGlobalEquipmentOperations",
      """
        function(context0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/operation/operations/globalequipment"})
        }
      """
    )
  
    // @LINE:109
    def getOperationForm: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Operation.getOperationForm",
      """
        function(context0,operationId1) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/operation/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("operationId", operationId1)) + "/form/individual"})
        }
      """
    )
  
    // @LINE:63
    def executeBulkDelete: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Operation.executeBulkDelete",
      """
        function(context0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/equipment/delete"})
        }
      """
    )
  
  }

  // @LINE:84
  class ReverseTag(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:84
    def tags: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Tag.tags",
      """
        function(context0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/tags"})
        }
      """
    )
  
    // @LINE:85
    def equipmentTagSearch: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Tag.equipmentTagSearch",
      """
        function(context0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/equipment-tags"})
        }
      """
    )
  
    // @LINE:86
    def equipmentTagSearchAssociatedTagsContinuation: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Tag.equipmentTagSearchAssociatedTagsContinuation",
      """
        function(context0,uuid1) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/equipment-tags/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("uuid", uuid1))})
        }
      """
    )
  
    // @LINE:87
    def manageEquipmentsTags: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Tag.manageEquipmentsTags",
      """
        function(context0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/equipment-tags/manage"})
        }
      """
    )
  
  }

  // @LINE:2
  class ReverseApplication(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:6
    def jsSettings: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Application.jsSettings",
      """
        function(context0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/jsSettings"})
        }
      """
    )
  
    // @LINE:2
    def index: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Application.index",
      """
        function(context0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/home"})
        }
      """
    )
  
    // @LINE:13
    def getModuleAngularScripts: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Application.getModuleAngularScripts",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "nadm/imports/jsscripts"})
        }
      """
    )
  
    // @LINE:5
    def resume: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Application.resume",
      """
        function(context0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/resume"})
        }
      """
    )
  
    // @LINE:10
    def javascriptRoutes: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Application.javascriptRoutes",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "nadm/assets/javascripts/routes"})
        }
      """
    )
  
  }

  // @LINE:90
  class ReverseTableConfigs(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:91
    def globalEquipment: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.TableConfigs.globalEquipment",
      """
        function(context0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/table-configs/globalequipment"})
        }
      """
    )
  
    // @LINE:96
    def equipmentAssociationsTypeServices: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.TableConfigs.equipmentAssociationsTypeServices",
      """
        function(context0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/table-configs/equipmentassociationtypeservices"})
        }
      """
    )
  
    // @LINE:93
    def clientEquipmentModel: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.TableConfigs.clientEquipmentModel",
      """
        function(context0,clientId1) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/table-configs/clientEquipmentModel" + _qS([(""" + implicitly[play.api.mvc.QueryStringBindable[String]].javascriptUnbind + """)("clientId", clientId1)])})
        }
      """
    )
  
    // @LINE:92
    def client: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.TableConfigs.client",
      """
        function(context0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/table-configs/client"})
        }
      """
    )
  
    // @LINE:90
    def equipment: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.TableConfigs.equipment",
      """
        function(context0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/table-configs/equipment"})
        }
      """
    )
  
    // @LINE:94
    def service: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.TableConfigs.service",
      """
        function(context0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/table-configs/service"})
        }
      """
    )
  
    // @LINE:95
    def equipmentassociations: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.TableConfigs.equipmentassociations",
      """
        function(context0,equipmentId1) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/table-configs/equipmentassociations" + _qS([(""" + implicitly[play.api.mvc.QueryStringBindable[String]].javascriptUnbind + """)("equipmentId", equipmentId1)])})
        }
      """
    )
  
  }

  // @LINE:77
  class ReverseSearch(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:82
    def searchClientEquipmentModel: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Search.searchClientEquipmentModel",
      """
        function(context0,clientId1) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/client/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("clientId", clientId1)) + "/searchEquipmentModel"})
        }
      """
    )
  
    // @LINE:81
    def searchService: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Search.searchService",
      """
        function(context0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/searchservice"})
        }
      """
    )
  
    // @LINE:79
    def searchEquipment: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Search.searchEquipment",
      """
        function(context0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/searchequipment"})
        }
      """
    )
  
    // @LINE:80
    def searchEquipmentAssociations: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Search.searchEquipmentAssociations",
      """
        function(context0,equipmentId1) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/searchequipmentassociations/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("equipmentId", equipmentId1))})
        }
      """
    )
  
    // @LINE:101
    def catalogModels: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Search.catalogModels",
      """
        function(context0,multipleChoices1) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/catalog/models" + _qS([(""" + implicitly[play.api.mvc.QueryStringBindable[Boolean]].javascriptUnbind + """)("multipleChoices", multipleChoices1)])})
        }
      """
    )
  
    // @LINE:77
    def searchGlobalEquipment: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Search.searchGlobalEquipment",
      """
        function(context0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/searchglobalequipment"})
        }
      """
    )
  
    // @LINE:78
    def searchClient: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Search.searchClient",
      """
        function(context0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/searchclient"})
        }
      """
    )
  
    // @LINE:83
    def clientNames: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Search.clientNames",
      """
        function(context0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/clients/names"})
        }
      """
    )
  
    // @LINE:100
    def catalogManufacturers: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Search.catalogManufacturers",
      """
        function(context0,multipleChoices1) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/catalog/manufacturers" + _qS([(""" + implicitly[play.api.mvc.QueryStringBindable[Boolean]].javascriptUnbind + """)("multipleChoices", multipleChoices1)])})
        }
      """
    )
  
    // @LINE:102
    def catalogVersions: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Search.catalogVersions",
      """
        function(context0,multipleChoices1) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/catalog/versions" + _qS([(""" + implicitly[play.api.mvc.QueryStringBindable[Boolean]].javascriptUnbind + """)("multipleChoices", multipleChoices1)])})
        }
      """
    )
  
  }

  // @LINE:119
  class ReverseFilters(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:120
    def editFilter: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Filters.editFilter",
      """
        function(context0,filterContext1,filterName2) {
          return _wA({method:"PUT", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/filters/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("filterContext", filterContext1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("filterName", filterName2))})
        }
      """
    )
  
    // @LINE:119
    def saveFilter: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Filters.saveFilter",
      """
        function(context0,filterContext1) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/filters/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("filterContext", filterContext1))})
        }
      """
    )
  
    // @LINE:122
    def getAllFilters: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Filters.getAllFilters",
      """
        function(context0,filterContext1) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/filters/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("filterContext", filterContext1))})
        }
      """
    )
  
    // @LINE:123
    def getSelectedFilter: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Filters.getSelectedFilter",
      """
        function(context0,filterContext1,filterName2) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/filters/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("filterContext", filterContext1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("filterName", filterName2))})
        }
      """
    )
  
    // @LINE:121
    def deleteFilter: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Filters.deleteFilter",
      """
        function(context0,filterContext1,filterName2) {
          return _wA({method:"DELETE", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/filters/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("filterContext", filterContext1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("filterName", filterName2))})
        }
      """
    )
  
  }

  // @LINE:20
  class ReverseNavigation(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:33
    def clientEquipmentDetailModal: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Navigation.clientEquipmentDetailModal",
      """
        function(context0,clientId1,manufacturer2,model3,version4) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/client/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("clientId", clientId1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("manufacturer", manufacturer2)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("model", model3)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version4)) + "/modal"})
        }
      """
    )
  
    // @LINE:52
    def equipmentEdit: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Navigation.equipmentEdit",
      """
        function(context0,manufacturer1,model2,version3,clientId4,equipmentId5,tabContext6) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/equipment/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("manufacturer", manufacturer1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("model", model2)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version3)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("clientId", clientId4)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("equipmentId", equipmentId5)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("tabContext", tabContext6)) + "/edit"})
        }
      """
    )
  
    // @LINE:48
    def equipmentCreate: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Navigation.equipmentCreate",
      """
        function(context0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/equipment/create"})
        }
      """
    )
  
    // @LINE:20
    def globalEquipment: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Navigation.globalEquipment",
      """
        function(context0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/globalequipment"})
        }
      """
    )
  
    // @LINE:22
    def globalEquipmentEdit: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Navigation.globalEquipmentEdit",
      """
        function(context0,manufacturer1,model2,version3) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/globalequipment/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("manufacturer", manufacturer1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("model", model2)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version3)) + "/edit"})
        }
      """
    )
  
    // @LINE:32
    def clientEquipmentDetail: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Navigation.clientEquipmentDetail",
      """
        function(context0,clientId1,manufacturer2,model3,version4) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/client/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("clientId", clientId1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("manufacturer", manufacturer2)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("model", model3)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version4))})
        }
      """
    )
  
    // @LINE:71
    def serviceEdit: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Navigation.serviceEdit",
      """
        function(context0,clientName1,serviceName2,serviceType3) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/service/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("clientName", clientName1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("serviceName", serviceName2)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("serviceType", serviceType3)) + "/edit"})
        }
      """
    )
  
    // @LINE:51
    def equipmentDetail: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Navigation.equipmentDetail",
      """
        function(context0,manufacturer1,model2,version3,clientId4,equipmentId5,tabContext6) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/equipment/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("manufacturer", manufacturer1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("model", model2)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version3)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("clientId", clientId4)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("equipmentId", equipmentId5)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("tabContext", tabContext6))})
        }
      """
    )
  
    // @LINE:50
    def equipmentCharacteristics: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Navigation.equipmentCharacteristics",
      """
        function(context0,equipmentId1,mode2) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/equipment/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("equipmentId", equipmentId1)) + "/characteristics/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("mode", mode2))})
        }
      """
    )
  
    // @LINE:47
    def equipmentCreateTemplate: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Navigation.equipmentCreateTemplate",
      """
        function(context0,manufacturer1,model2,version3,clientId4) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/equipment/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("manufacturer", manufacturer1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("model", model2)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version3)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("clientId", clientId4)) + "/createtemplate"})
        }
      """
    )
  
    // @LINE:31
    def clientDetails: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Navigation.clientDetails",
      """
        function(context0,clientId1) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/client/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("clientId", clientId1))})
        }
      """
    )
  
    // @LINE:66
    def serviceCreateTemplate: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Navigation.serviceCreateTemplate",
      """
        function(context0,serviceType1) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/service/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("serviceType", serviceType1)) + "/createtemplate"})
        }
      """
    )
  
    // @LINE:30
    def clientEquipmentCreateTemplate: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Navigation.clientEquipmentCreateTemplate",
      """
        function(context0,manufacturer1,model2,version3) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/client/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("manufacturer", manufacturer1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("model", model2)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version3)) + "/createtemplate"})
        }
      """
    )
  
    // @LINE:29
    def client: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Navigation.client",
      """
        function(context0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/client"})
        }
      """
    )
  
    // @LINE:49
    def equipment: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Navigation.equipment",
      """
        function(context0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/equipment"})
        }
      """
    )
  
    // @LINE:68
    def serviceCreate: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Navigation.serviceCreate",
      """
        function(context0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/service/create"})
        }
      """
    )
  
    // @LINE:34
    def clientEquipmentEditModal: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Navigation.clientEquipmentEditModal",
      """
        function(context0,clientId1,manufacturer2,model3,version4) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/client/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("clientId", clientId1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("manufacturer", manufacturer2)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("model", model3)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version4)) + "/editModal"})
        }
      """
    )
  
    // @LINE:67
    def service: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Navigation.service",
      """
        function(context0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/service"})
        }
      """
    )
  
    // @LINE:70
    def serviceDetails: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Navigation.serviceDetails",
      """
        function(context0,clientName1,serviceName2,serviceType3) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/service/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("clientName", clientName1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("serviceName", serviceName2)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("serviceType", serviceType3))})
        }
      """
    )
  
    // @LINE:21
    def globalEquipmentDetails: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Navigation.globalEquipmentDetails",
      """
        function(context0,manufacturer1,model2,version3) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/globalequipment/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("manufacturer", manufacturer1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("model", model2)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version3))})
        }
      """
    )
  
    // @LINE:35
    def clientEquipmentCreateModal: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Navigation.clientEquipmentCreateModal",
      """
        function(context0,clientId1) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/client/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("clientId", clientId1)) + "/createModal"})
        }
      """
    )
  
    // @LINE:72
    def serviceValues: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.nadm.controllers.Navigation.serviceValues",
      """
        function(context0,clientName1) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0)) + "/nadm/service/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("clientName", clientName1)) + "/values"})
        }
      """
    )
  
  }


}
