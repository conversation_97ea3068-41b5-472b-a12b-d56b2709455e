
package na.nadm.views.html.components.common.modalform

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.utils.FormBuilderUtil
/*2.2*/import na.naportalbase.views.html.components.selectsComp.selectComp
/*3.2*/import pt.ptinovacao.naportal.components.{Option, SelectableUiComponent}
/*5.2*/import scala.collection.mutable

object selectableUiComponent extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[SelectableUiComponent,String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*7.2*/(uiComponent: SelectableUiComponent, angularModel: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*30.2*/selectOptions/*30.15*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*30.19*/("""
    """),_display_(/*31.6*/for(option: Option <- uiComponent.getOptions) yield /*31.51*/ {_display_(Seq[Any](format.raw/*31.53*/("""
        """),format.raw/*32.9*/("""<option value=""""),_display_(/*32.25*/option/*32.31*/.getValue),format.raw/*32.40*/(""""><i class="fa fa-undo"></i>"""),_display_(/*32.69*/{
            val i18nText = FormBuilderUtil.parseI18n(option.getI18n)
            if(i18nText != "")
                i18nText
            else
                option.getValue
        }),format.raw/*38.10*/("""</option>
    """)))}),format.raw/*39.6*/("""
""")))};def /*9.2*/elementAttr/*9.13*/ = {{
    var map = mutable.HashMap[String, String] (
        "id" -> uiComponent.getElementId,
        "name" -> uiComponent.getElementId,
	    "data-ng-model" -> angularModel,
        "data-na-portal-select-box" -> null,
        "data-na-portal-nadm-equip-associations-selectable-field" -> null,
        "data-show-icons" -> null,
        "class" -> "form-control"
    )

    if(uiComponent.getType == SelectableUiComponent.Type.selectBox){
        map.put("select-width","100%")
    }

    if (uiComponent.getRequired != null && uiComponent.getRequired) {
        map.put("data-na-portal-required", null)
    }

    collection.immutable.HashMap(map.toSeq:_*)
}};
Seq[Any](format.raw/*7.60*/("""

"""),format.raw/*29.2*/("""
"""),format.raw/*40.2*/("""
"""),format.raw/*41.1*/("""<div data-na-portal-field-notification data-na-portal-input-notification-hr>
"""),_display_(/*42.2*/uiComponent/*42.13*/.getType/*42.21*/ match/*42.27*/ {/*43.9*/case SelectableUiComponent.Type.selectBox =>/*43.53*/ {_display_(Seq[Any](format.raw/*43.55*/("""
		        """),_display_(/*44.12*/selectComp/*44.22*/.render(elementAttr, null, null, selectOptions)),format.raw/*44.69*/("""
        """)))}/*46.9*/case SelectableUiComponent.Type.checkBox =>/*46.52*/ {}/*48.9*/case SelectableUiComponent.Type.radioButton =>/*48.55*/ {}/*50.9*/case _ =>/*50.18*/ {}}),format.raw/*51.6*/("""
"""),format.raw/*52.1*/("""</div>

"""))
      }
    }
  }

  def render(uiComponent:SelectableUiComponent,angularModel:String): play.twirl.api.HtmlFormat.Appendable = apply(uiComponent,angularModel)

  def f:((SelectableUiComponent,String) => play.twirl.api.HtmlFormat.Appendable) = (uiComponent,angularModel) => apply(uiComponent,angularModel)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:39 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/common/modalform/selectableUiComponent.scala.html
                  HASH: fd6944bf34f19397654edab5f7d9b78dc1dd1187
                  MATRIX: 687->1|731->39|806->108|886->183|1260->217|1397->954|1419->967|1500->971|1532->977|1593->1022|1633->1024|1669->1033|1712->1049|1727->1055|1757->1064|1813->1093|2019->1278|2064->1293|2088->278|2107->289|2800->275|2829->952|2857->1295|2885->1296|2989->1374|3009->1385|3026->1393|3041->1399|3051->1410|3104->1454|3144->1456|3183->1468|3202->1478|3270->1525|3298->1544|3350->1587|3361->1600|3416->1646|3427->1659|3445->1668|3469->1677|3497->1678
                  LINES: 24->1|25->2|26->3|27->5|32->7|36->30|36->30|38->30|39->31|39->31|39->31|40->32|40->32|40->32|40->32|40->32|46->38|47->39|48->9|48->9|69->7|71->29|72->40|73->41|74->42|74->42|74->42|74->42|74->43|74->43|74->43|75->44|75->44|75->44|76->46|76->46|76->48|76->48|76->50|76->50|76->51|77->52
                  -- GENERATED --
              */
          