
package na.nadm.views.html.components.common.modalform

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.NadmSettings
/*2.2*/import na.nadm.views.html.components.common.modalform
/*3.2*/import pt.ptinovacao.naportal.components.{Component, GroupComponent, UiComponent}
/*4.2*/import pt.ptinovacao.naportal.nadm.model.ResourceTemplateIf

object associationsForm extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[ResourceTemplateIf,String,NadmSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*6.2*/(configurations: ResourceTemplateIf, context: String, nadmSettings: NadmSettings):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*9.2*/renderUiComponent/*9.19*/(uiComponent: UiComponent):play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*9.49*/("""
	"""),_display_(/*10.3*/modalform/*10.12*/.uiComponent.render(uiComponent, context, nadmSettings)),format.raw/*10.67*/("""
""")))};
Seq[Any](format.raw/*6.83*/("""


"""),format.raw/*11.2*/("""

"""),format.raw/*13.1*/("""<form id="association-modal" autocomplete="off" class="form-horizontal">
	<div class="fx-form-content">
		"""),_display_(/*15.4*/for(component: Component <- configurations.getTemplate.getComponentList) yield /*15.76*/ {_display_(Seq[Any](format.raw/*15.78*/("""
			"""),_display_(/*16.5*/component/*16.14*/ match/*16.20*/ {/*17.5*/case uiComponent: UiComponent =>/*17.37*/ {_display_(_display_(/*17.40*/renderUiComponent(uiComponent)))}/*18.5*/case groupComponent: GroupComponent =>/*18.43*/ {_display_(Seq[Any](format.raw/*18.45*/("""
					"""),format.raw/*19.6*/("""<span class="nadm-group-component">
					"""),_display_(/*20.7*/for(uiComponent: UiComponent <- groupComponent.getUiComponentList) yield /*20.73*/{_display_(Seq[Any](format.raw/*20.74*/("""
						"""),_display_(/*21.8*/renderUiComponent(uiComponent)),format.raw/*21.38*/("""
					""")))}),format.raw/*22.7*/("""
					"""),format.raw/*23.6*/("""</span>
				""")))}}),format.raw/*25.5*/("""
		""")))}),format.raw/*26.4*/("""
	"""),format.raw/*27.2*/("""</div>
</form>
"""))
      }
    }
  }

  def render(configurations:ResourceTemplateIf,context:String,nadmSettings:NadmSettings): play.twirl.api.HtmlFormat.Appendable = apply(configurations,context,nadmSettings)

  def f:((ResourceTemplateIf,String,NadmSettings) => play.twirl.api.HtmlFormat.Appendable) = (configurations,context,nadmSettings) => apply(configurations,context,nadmSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:39 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/common/modalform/associationsForm.scala.html
                  HASH: f9d9aff0fbb52c1720056c42abeb797c79dbca47
                  MATRIX: 687->1|731->39|792->94|881->177|1288->239|1447->324|1472->341|1578->371|1607->374|1625->383|1701->438|1742->320|1772->440|1801->442|1934->549|2022->621|2062->623|2093->628|2111->637|2126->643|2136->650|2177->682|2208->685|2249->721|2296->759|2336->761|2369->767|2437->809|2519->875|2558->876|2592->884|2643->914|2680->921|2713->927|2757->945|2791->949|2820->951
                  LINES: 24->1|25->2|26->3|27->4|32->6|36->9|36->9|38->9|39->10|39->10|39->10|41->6|44->11|46->13|48->15|48->15|48->15|49->16|49->16|49->16|49->17|49->17|49->17|49->18|49->18|49->18|50->19|51->20|51->20|51->20|52->21|52->21|53->22|54->23|55->25|56->26|57->27
                  -- GENERATED --
              */
          