
package na.nadm.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.NadmConstants.NavContext
/*2.2*/import na.nadm.settings.NadmSettings
/*3.2*/import na.nadm.utils.FormBuilderUtil.ViewType
/*4.2*/import na.nadm.views.components.common.form.HelperFieldDrawingAttributes
/*5.2*/import na.nadm.views.html.components.common.detail.headerButtons
/*6.2*/import na.nadm.views.html.components.common.lateralNav
/*7.2*/import na.nadm.views.html.skeletons.mainSkel
/*8.2*/import na.naportalbase.views.html.components.selectsComp.{dynamicSelectOptionComp, selectComp}
/*9.2*/import na.naportalbase.views.tags.i18n
/*11.2*/import scala.collection.immutable

object serviceCreateNoSelect extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[String,String,ViewType,NadmSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*13.2*/(context: String, serviceType: String, viewContext: ViewType, nadmSettings: NadmSettings):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*16.2*/navBar/*16.8*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*16.12*/("""
"""),_display_(/*17.2*/lateralNav/*17.12*/.render(context, NavContext.SERVICE, nadmSettings)),format.raw/*17.62*/("""
""")))};def /*52.2*/headerAction/*52.14*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*52.18*/("""
"""),_display_(/*53.2*/headerButtons/*53.15*/.render(context, viewContext)),format.raw/*53.44*/("""
""")))};def /*56.2*/identificationBlock/*56.21*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*56.25*/("""
    """),format.raw/*57.5*/("""<div class="form-group" data-na-portal-nadm-service-auto-characterize>
        <label for="type" class="col-sm-2 control-label">"""),_display_(/*58.59*/i18n("na.portal.nadm.type")),format.raw/*58.86*/("""</label>
        <div class="col-sm-10">
        """),_display_(/*60.10*/selectComp/*60.20*/.render(
            serviceTypeSelectAttributes, i18n(context + "." + serviceType), initialOptionsAttributes, serviceTypeSelectData
        )),format.raw/*62.10*/("""
        """),format.raw/*63.9*/("""</div>
    </div>
""")))};def /*67.2*/characteristicsBlock/*67.22*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*67.26*/("""
    """),format.raw/*68.5*/("""<div id="characteristics">
        <div class="alert alert-info">
            <div class="fx-alert-icon">
            </div>
            <div class="fx-alert-desc">
                <strong>"""),_display_(/*73.26*/i18n("na.portal.nadm.characterize.after.fill")),format.raw/*73.72*/("""</strong>
            </div>
        </div>
    </div>
""")))};def /*20.2*/serviceTypeSelectAttributes/*20.29*/ = {{
    var map = HelperFieldDrawingAttributes.serviceTypeSelectAttributes("createCtrl")
    map.put("disabled", null)
    map.put("value", serviceType)
    immutable.HashMap(map.toSeq: _*)

}};def /*28.2*/initialOptionsAttributes/*28.26*/ = {{
    var optionAttributes = immutable.HashMap[String, String](
        "name" -> serviceType,
        "id" -> serviceType,
        "value" -> serviceType
    )
    optionAttributes
}};def /*37.2*/serviceTypeSelectData/*37.23*/ = {{
    var stringBuilder = new StringBuilder
    var optionLabel = i18n(context + "." + serviceType)
    var optionAttributes = immutable.HashMap[String, String](
        "name" -> serviceType,
        "id" -> serviceType,
        "value" -> serviceType,
        "selection" -> "selected"

    )
    stringBuilder.append(dynamicSelectOptionComp.render(optionLabel, optionAttributes))

    play.twirl.api.Html("")
}};
Seq[Any](format.raw/*13.91*/("""


"""),format.raw/*18.2*/("""

"""),format.raw/*26.2*/("""

"""),format.raw/*35.2*/("""

"""),format.raw/*50.2*/("""

"""),format.raw/*54.2*/("""

"""),format.raw/*65.2*/("""

"""),format.raw/*77.2*/("""

"""),_display_(/*79.2*/mainSkel(context)/*79.19*/ {_display_(Seq[Any](format.raw/*79.21*/("""
    """),format.raw/*80.5*/("""<div class="layout--page-with-navbar">
        """),_display_(/*81.10*/navBar),format.raw/*81.16*/("""
        """),format.raw/*82.9*/("""<div data-ng-controller="NaPortalNadmCommonController" data-na-portal-nadm-service-create-page >
            <div class="fx-entity-header">
                <div class="fx-entity-header-info">
                    <span class="fx-entity-header-icon">
                        <i class="fa fa-cogs"></i>
                    </span>
                    <div class="fx-entity-header-title">
                        <h1>
                        """),_display_(/*90.26*/i18n("na.portal.nadm.create.service")),format.raw/*90.63*/("""
                        """),format.raw/*91.25*/("""</h1>
                    </div>
                </div>
                <div class="fx-entity-header-actions">
                """),_display_(/*95.18*/headerAction),format.raw/*95.30*/("""
                """),format.raw/*96.17*/("""</div>
            </div>
            <div id="overLay">
                <div class="fx-entity-info" data-context="serviceModel">
                    <x-collapsible class="fx-section" header=""""),_display_(/*100.64*/i18n("nadm.detail.section.identification")),format.raw/*100.106*/("""">
                        <div class="form-horizontal">
                        """),_display_(/*102.26*/identificationBlock),format.raw/*102.45*/("""
                        """),format.raw/*103.25*/("""</div>
                    </x-collapsible >
                    <x-collapsible class="fx-section" header=""""),_display_(/*105.64*/i18n("nadm.detail.section.characteristics")),format.raw/*105.107*/("""">
                        <div class="form-horizontal">
                        """),_display_(/*107.26*/characteristicsBlock),format.raw/*107.46*/("""
                        """),format.raw/*108.25*/("""</div>
                    </x-collapsible>
                </div>
            </div>
        </div>
    </div>
""")))}))
      }
    }
  }

  def render(context:String,serviceType:String,viewContext:ViewType,nadmSettings:NadmSettings): play.twirl.api.HtmlFormat.Appendable = apply(context,serviceType,viewContext,nadmSettings)

  def f:((String,String,ViewType,NadmSettings) => play.twirl.api.HtmlFormat.Appendable) = (context,serviceType,viewContext,nadmSettings) => apply(context,serviceType,viewContext,nadmSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:39 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/roots/serviceCreateNoSelect.scala.html
                  HASH: 2ac7c68b168b7c682ed403f875a5d7e4be1a64ac
                  MATRIX: 665->1|721->52|765->91|818->139|898->214|970->281|1032->338|1084->385|1186->482|1233->525|1617->563|1785->659|1799->665|1880->669|1909->672|1928->682|1999->732|2025->1651|2046->1663|2127->1667|2156->1670|2178->1683|2228->1712|2254->1720|2282->1739|2363->1743|2396->1749|2553->1879|2601->1906|2680->1958|2699->1968|2864->2112|2901->2122|2945->2148|2974->2168|3055->2172|3088->2178|3310->2373|3377->2419|3460->740|3496->767|3710->972|3743->996|3951->1195|3981->1216|4442->652|4475->735|4506->967|4537->1190|4568->1646|4599->1715|4630->2143|4661->2479|4692->2484|4718->2501|4758->2503|4791->2509|4867->2558|4894->2564|4931->2574|5405->3021|5463->3058|5517->3084|5676->3216|5709->3228|5755->3246|5980->3443|6045->3485|6157->3569|6198->3588|6253->3614|6391->3724|6457->3767|6569->3851|6611->3871|6666->3897
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|32->9|33->11|38->13|42->16|42->16|44->16|45->17|45->17|45->17|46->52|46->52|48->52|49->53|49->53|49->53|50->56|50->56|52->56|53->57|54->58|54->58|56->60|56->60|58->62|59->63|61->67|61->67|63->67|64->68|69->73|69->73|73->20|73->20|79->28|79->28|86->37|86->37|100->13|103->18|105->26|107->35|109->50|111->54|113->65|115->77|117->79|117->79|117->79|118->80|119->81|119->81|120->82|128->90|128->90|129->91|133->95|133->95|134->96|138->100|138->100|140->102|140->102|141->103|143->105|143->105|145->107|145->107|146->108
                  -- GENERATED --
              */
          