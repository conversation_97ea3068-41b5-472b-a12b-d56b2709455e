
package na.nadm.views.html.components.equipment

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.views.html.components.common.form.{EquipIdField, clientField, contact<PERSON><PERSON>ield, dateField, firstContactT<PERSON><PERSON>nter<PERSON><PERSON>ield, lastC<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, manufacturer<PERSON>ield, model<PERSON>ield, provision<PERSON>tate<PERSON>ield, tags<PERSON>ield, versionField}
/*2.2*/import na.naportalbase.views.html.components.buttonsComp.leftSideFormInputButtonsComp
/*3.2*/import na.naportalbase.views.tags.i18n
/*4.2*/import scala.collection.mutable
/*5.2*/import pt.ptinovacao.na.portal.web.ui.naem.schemas.Context

object leftSideForm extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*7.2*/(context: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*9.2*/searchButtonAttributes/*9.24*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("id", "searchButton")
    map.put("type","submit")
    map.put("value",i18n("na.buttons.search"))
    map
}};def /*17.2*/clearButtonAttributes/*17.23*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("id", "clearSearchButton")
    map.put("type", "reset")
    map.put("value", i18n("na.buttons.clear"))
    map
}};
Seq[Any](format.raw/*7.19*/("""

"""),format.raw/*15.2*/("""

"""),format.raw/*23.2*/("""

"""),format.raw/*25.1*/("""<header class="fx-sidebar-header search-sidebar__header">
    <div class="fx-sidebar-header-inner">
        <p>
            <span class="fx-entity-header-icon">
                <i class="fa fa-search"></i>
            </span>
            <x-i18n key="na.basemodule.lateralSearch.title"></x-i18n>
        </p>
    </div>
</header>
<div class="fx-sidebar-content-wrapper search-sidebar__field-list">
    <x-nadm-filter data-filter-context=""""),_display_(/*36.42*/Context/*36.49*/.NA_EQUIPMENTS),format.raw/*36.63*/("""">
        <x-shadow-scroll>
            """),_display_(/*38.14*/EquipIdField/*38.26*/.render()),format.raw/*38.35*/("""
            """),_display_(/*39.14*/manufacturerField/*39.31*/.render(context)),format.raw/*39.47*/("""
            """),_display_(/*40.14*/modelField/*40.24*/.render(context)),format.raw/*40.40*/("""
            """),_display_(/*41.14*/versionField/*41.26*/.render(context)),format.raw/*41.42*/("""
            """),_display_(/*42.14*/clientField/*42.25*/.render()),format.raw/*42.34*/("""
            """),_display_(/*43.14*/contactIPField/*43.28*/.render()),format.raw/*43.37*/("""
            """),_display_(/*44.14*/tagsField/*44.23*/.render(context)),format.raw/*44.39*/("""
            """),_display_(/*45.14*/provisionStateField/*45.33*/.render()),format.raw/*45.42*/("""
            """),_display_(/*46.14*/firstContactTimeIntervalField/*46.43*/.render()),format.raw/*46.52*/("""
            """),_display_(/*47.14*/lastContactTimeIntervalField/*47.42*/.render()),format.raw/*47.51*/("""
            """),_display_(/*48.14*/dateField/*48.23*/.render()),format.raw/*48.32*/("""
        """),format.raw/*49.9*/("""</x-shadow-scroll>
    </x-nadm-filter>
</div>
<div class="form-group search-sidebar__button-list">
    <div class="fx-splitter-sidebar-buttons">
        <div class="fx-splitter-sidebar-buttons-inner">
        """),_display_(/*55.10*/leftSideFormInputButtonsComp/*55.38*/.render(searchButtonAttributes, clearButtonAttributes)),format.raw/*55.92*/("""
        """),format.raw/*56.9*/("""</div>
    </div>
</div>
"""))
      }
    }
  }

  def render(context:String): play.twirl.api.HtmlFormat.Appendable = apply(context)

  def f:((String) => play.twirl.api.HtmlFormat.Appendable) = (context) => apply(context)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:39 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/equipment/leftSideForm.scala.html
                  HASH: f4ea695767b8cf0800174faaf428d5d0c94624b5
                  MATRIX: 680->1|929->244|1022->331|1068->371|1107->404|1477->465|1572->485|1602->507|1789->683|1819->704|2027->482|2056->680|2085->882|2114->884|2580->1323|2596->1330|2631->1344|2700->1386|2721->1398|2751->1407|2792->1421|2818->1438|2855->1454|2896->1468|2915->1478|2952->1494|2993->1508|3014->1520|3051->1536|3092->1550|3112->1561|3142->1570|3183->1584|3206->1598|3236->1607|3277->1621|3295->1630|3332->1646|3373->1660|3401->1679|3431->1688|3472->1702|3510->1731|3540->1740|3581->1754|3618->1782|3648->1791|3689->1805|3707->1814|3737->1823|3773->1832|4011->2043|4048->2071|4123->2125|4159->2134
                  LINES: 24->1|25->2|26->3|27->4|28->5|33->7|37->9|37->9|43->17|43->17|50->7|52->15|54->23|56->25|67->36|67->36|67->36|69->38|69->38|69->38|70->39|70->39|70->39|71->40|71->40|71->40|72->41|72->41|72->41|73->42|73->42|73->42|74->43|74->43|74->43|75->44|75->44|75->44|76->45|76->45|76->45|77->46|77->46|77->46|78->47|78->47|78->47|79->48|79->48|79->48|80->49|86->55|86->55|86->55|87->56
                  -- GENERATED --
              */
          