
package na.nadm.views.html.components.common.tableform

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.NadmSettings
/*2.2*/import na.nadm.utils.FormBuilderUtil
/*3.2*/import na.nadm.utils.FormBuilderUtil.ViewType
/*4.2*/import na.nadm.views.html.components.common.tableform.tableRowForm
/*5.2*/import na.naportalbase.settings.BaseMappings.defaultObjectMapper
/*6.2*/import na.naportalbase.views.tags.i18n
/*7.2*/import pt.ptinovacao.naportal.components.{Component, GroupComponent, UiComponent}
/*8.2*/import pt.ptinovacao.naportal.nadm.Configurations

object tableForm extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[Configurations,ViewType,String,NadmSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*10.2*/(configurations: Configurations, viewContext: ViewType, context: String, nadmSettings: NadmSettings):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*12.2*/jsonConfigurations/*12.20*/ = {{ defaultObjectMapper.valueToTree(configurations.getConfiguration).toString }};def /*13.2*/parseI18n/*13.11*/(groupComponent : GroupComponent) = {{ FormBuilderUtil.parseI18n(groupComponent.getI18n) }};def /*15.2*/group/*15.7*/ = {{
	val group = new GroupComponent()
	for(component: Component <- configurations.getCharacteristics.getComponentList){
		component match {
			case uiComponent: UiComponent => group.addUiComponent(uiComponent);
			case _ =>
		}
	}
	group
}};
Seq[Any](format.raw/*10.102*/("""

"""),format.raw/*12.101*/("""
"""),format.raw/*13.101*/("""

"""),format.raw/*24.2*/("""

"""),format.raw/*26.1*/("""<div data-na-portal-nadm-table-form class="hidden col-md-12 col-sm-12 col-xs-12" id="tableForm" data-view-context=""""),_display_(/*26.117*/viewContext),format.raw/*26.128*/("""" data-context-value=""""),_display_(/*26.151*/configurations/*26.165*/.getContext),format.raw/*26.176*/("""" data-json-configs-value=""""),_display_(/*26.204*/jsonConfigurations),format.raw/*26.222*/("""">
	<table class="dataTable table row-border fx-table">
		<thead>
			<tr>
				<th class="description-col" style="width: 30%">"""),_display_(/*30.53*/i18n("na.portal.nadm.description")),format.raw/*30.87*/("""</th>
				<th class="value-col" style="width: 30%">"""),_display_(/*31.47*/i18n("na.portal.nadm.value")),format.raw/*31.75*/("""</th>
                """),_display_(/*32.18*/if(viewContext == ViewType.CREATE || viewContext == ViewType.EDIT)/*32.84*/ {_display_(Seq[Any](format.raw/*32.86*/("""
                    """),format.raw/*33.21*/("""<th class="impact-col" style="width : 30 %">"""),_display_(/*33.66*/i18n("na.portal.nadm.impact")),format.raw/*33.95*/("""</th>
                    <th class="actions-col" style="width : 10 %">"""),_display_(/*34.67*/i18n("na.portal.nadm.actions")),format.raw/*34.97*/("""</th>
                """)))}),format.raw/*35.18*/("""
			"""),format.raw/*36.4*/("""</tr>
		</thead>
		<tbody>

			"""),_display_(/*40.5*/if(group.getUiComponentList != null && !group.getUiComponentList.isEmpty)/*40.78*/{_display_(Seq[Any](format.raw/*40.79*/("""
				"""),format.raw/*41.5*/("""<tr>
					<td class="group expanded-group group-item-expander" colspan="4" data-na-portal-tr-group data-group=""""),_display_(/*42.108*/parseI18n(group)),format.raw/*42.124*/("""">
					     &nbsp;
					</td>
				</tr>
				"""),_display_(/*46.6*/for(uiComponent: UiComponent <- group.getUiComponentList) yield /*46.63*/{_display_(Seq[Any](format.raw/*46.64*/("""
					"""),_display_(/*47.7*/tableRowForm/*47.19*/.render(parseI18n(group), uiComponent, viewContext, configurations.getContext, context, nadmSettings)),format.raw/*47.120*/("""
				""")))}),format.raw/*48.6*/("""
			""")))}),format.raw/*49.5*/("""
            """),_display_(/*50.14*/for(component: Component <- configurations.getCharacteristics.getComponentList) yield /*50.93*/ {_display_(Seq[Any](format.raw/*50.95*/("""
                """),_display_(/*51.18*/component/*51.27*/ match/*51.33*/ {/*52.21*/case groupComponent : GroupComponent =>/*52.60*/ {_display_(Seq[Any](format.raw/*52.62*/("""
                        """),format.raw/*53.25*/("""<tr class="trigger-expander">
	                        <td class="group expanded-group group-item-expander" colspan="4" data-na-portal-tr-group data-group=""""),_display_(/*54.128*/parseI18n(groupComponent)),format.raw/*54.153*/("""">
	                            """),_display_(/*55.31*/parseI18n(groupComponent)),format.raw/*55.56*/("""
	                        """),format.raw/*56.26*/("""</td>
                        </tr>
                        """),_display_(/*58.26*/if(groupComponent.getUiComponentList != null)/*58.71*/ {_display_(Seq[Any](format.raw/*58.73*/("""
                        	"""),_display_(/*59.27*/for(uiComponent: UiComponent <- groupComponent.getUiComponentList) yield /*59.93*/{_display_(Seq[Any](format.raw/*59.94*/("""
                        	    """),_display_(/*60.31*/tableRowForm/*60.43*/.render(parseI18n(groupComponent), uiComponent, viewContext, configurations.getContext, context, nadmSettings)),format.raw/*60.153*/("""
                        	""")))}),format.raw/*61.27*/("""
                    	""")))}),format.raw/*62.23*/("""
                    """)))}/*64.21*/case _ =>/*64.30*/ {}}),format.raw/*65.18*/("""
            """)))}),format.raw/*66.14*/("""
		"""),format.raw/*67.3*/("""</tbody>
	</table>
</div>
"""))
      }
    }
  }

  def render(configurations:Configurations,viewContext:ViewType,context:String,nadmSettings:NadmSettings): play.twirl.api.HtmlFormat.Appendable = apply(configurations,viewContext,context,nadmSettings)

  def f:((Configurations,ViewType,String,NadmSettings) => play.twirl.api.HtmlFormat.Appendable) = (configurations,viewContext,context,nadmSettings) => apply(configurations,viewContext,context,nadmSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:39 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/common/tableform/tableForm.scala.html
                  HASH: 3a5c9fe1c924f4c1eedaa9576c42dc7cd3efef2a
                  MATRIX: 687->1|731->40|775->79|828->127|902->196|974->263|1020->304|1109->388|1505->442|1684->547|1711->565|1806->649|1824->658|1928->753|1941->758|2223->542|2256->646|2287->748|2318->1008|2349->1012|2493->1128|2526->1139|2577->1162|2601->1176|2634->1187|2690->1215|2730->1233|2887->1363|2942->1397|3022->1450|3071->1478|3122->1502|3197->1568|3237->1570|3287->1592|3359->1637|3409->1666|3509->1739|3560->1769|3615->1793|3647->1798|3709->1834|3791->1907|3830->1908|3863->1914|4004->2027|4042->2043|4118->2093|4191->2150|4230->2151|4264->2159|4285->2171|4408->2272|4445->2279|4481->2285|4523->2300|4618->2379|4658->2381|4704->2400|4722->2409|4737->2415|4748->2439|4796->2478|4836->2480|4890->2506|5076->2664|5123->2689|5184->2723|5230->2748|5285->2775|5375->2838|5429->2883|5469->2885|5524->2913|5606->2979|5645->2980|5704->3012|5725->3024|5857->3134|5916->3162|5971->3186|6013->3231|6031->3240|6056->3262|6102->3277|6133->3281
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|36->10|40->12|40->12|40->13|40->13|40->15|40->15|50->10|52->12|53->13|55->24|57->26|57->26|57->26|57->26|57->26|57->26|57->26|57->26|61->30|61->30|62->31|62->31|63->32|63->32|63->32|64->33|64->33|64->33|65->34|65->34|66->35|67->36|71->40|71->40|71->40|72->41|73->42|73->42|77->46|77->46|77->46|78->47|78->47|78->47|79->48|80->49|81->50|81->50|81->50|82->51|82->51|82->51|82->52|82->52|82->52|83->53|84->54|84->54|85->55|85->55|86->56|88->58|88->58|88->58|89->59|89->59|89->59|90->60|90->60|90->60|91->61|92->62|93->64|93->64|93->65|94->66|95->67
                  -- GENERATED --
              */
          