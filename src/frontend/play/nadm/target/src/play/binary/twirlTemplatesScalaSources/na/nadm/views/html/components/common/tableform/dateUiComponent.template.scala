
package na.nadm.views.html.components.common.tableform

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.settings.DateFormats
/*2.2*/import na.naportalbase.views.tags.i18n
/*3.2*/import pt.ptinovacao.naportal.components.DateUiComponent

object dateUiComponent extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[DateUiComponent,String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*5.2*/(uiComponent: DateUiComponent, angularModel: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*7.2*/dateTimeElement/*7.17*/(elementId: String, dateFormat: String):play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*7.60*/("""
        """),format.raw/*8.9*/("""<div class="form-group">
            <x-date-time-picker class="no-button-picker" data-field=""""),_display_(/*9.71*/elementId),format.raw/*9.80*/("""" format=""""),_display_(/*9.91*/dateFormat),format.raw/*9.101*/(""""></x-date-time-picker>
        </div>
""")))};def /*13.2*/componentElement/*13.18*/ = {{
    uiComponent.getType match {
        case DateUiComponent.Type.DateTimePicker =>
                dateTimeElement(uiComponent.getElementId, i18n(DateFormats.DATETIME_SHORT.key))
        case DateUiComponent.Type.TimePicker =>
                dateTimeElement(uiComponent.getElementId, i18n(DateFormats.TIME_SHORT.key))
        case DateUiComponent.Type.DatePicker =>
                dateTimeElement(uiComponent.getElementId, i18n(DateFormats.DATE_SHORT.key))
        case _ => ""
    }
}};
Seq[Any](format.raw/*5.54*/("""

"""),format.raw/*11.2*/("""

"""),format.raw/*23.2*/("""

"""),format.raw/*25.1*/("""<div style="width: 280px" data-na-portal-field-notification>"""),_display_(/*25.62*/componentElement),format.raw/*25.78*/("""</div>
"""))
      }
    }
  }

  def render(uiComponent:DateUiComponent,angularModel:String): play.twirl.api.HtmlFormat.Appendable = apply(uiComponent,angularModel)

  def f:((DateUiComponent,String) => play.twirl.api.HtmlFormat.Appendable) = (uiComponent,angularModel) => apply(uiComponent,angularModel)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:29 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/common/tableform/dateUiComponent.scala.html
                  HASH: c4fc801ac100062b934bb22f45b7ecb76b4b6eec
                  MATRIX: 687->1|738->46|784->86|1171->145|1301->200|1324->215|1443->258|1478->267|1599->362|1628->371|1665->382|1696->392|1759->435|1784->451|2308->197|2337->432|2366->945|2395->947|2483->1008|2520->1024
                  LINES: 24->1|25->2|26->3|31->5|35->7|35->7|37->7|38->8|39->9|39->9|39->9|39->9|41->13|41->13|52->5|54->11|56->23|58->25|58->25|58->25
                  -- GENERATED --
              */
          