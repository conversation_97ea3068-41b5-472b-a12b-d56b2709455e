
package na.nadm.views.html.imports

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object scripts extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.1*/("""<script src=""""),_display_(/*1.15*/na/*1.17*/.nadm.controllers.routes.Application.javascriptRoutes()),format.raw/*1.72*/(""""></script>
<script src='"""),_display_(/*2.15*/na/*2.17*/.nadm.controllers.routes.Assets.versioned("basemodule/runtime.js")),format.raw/*2.83*/("""'></script>
<script src='"""),_display_(/*3.15*/na/*3.17*/.nadm.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-fuxi.js")),format.raw/*3.102*/("""'></script>
<script src='"""),_display_(/*4.15*/na/*4.17*/.nadm.controllers.routes.Assets.versioned("na-portal-assets/base-styles.js")),format.raw/*4.93*/("""'></script>
<script src='"""),_display_(/*5.15*/na/*5.17*/.nadm.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-bootstrap.js")),format.raw/*5.107*/("""'></script>
<script src='"""),_display_(/*6.15*/na/*6.17*/.nadm.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-selects.js")),format.raw/*6.105*/("""'></script>
<script src='"""),_display_(/*7.15*/na/*7.17*/.nadm.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-std-utils.js")),format.raw/*7.107*/("""'></script>
<script src='"""),_display_(/*8.15*/na/*8.17*/.nadm.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-tables.js")),format.raw/*8.104*/("""'></script>
<script src='"""),_display_(/*9.15*/na/*9.17*/.nadm.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-jquery.js")),format.raw/*9.104*/("""'></script>
<script src='"""),_display_(/*10.15*/na/*10.17*/.nadm.controllers.routes.Assets.versioned("na-portal-assets-vendors/common.js")),format.raw/*10.96*/("""'></script>
<script src='"""),_display_(/*11.15*/na/*11.17*/.nadm.controllers.routes.Assets.versioned("utils/utils.js")),format.raw/*11.76*/("""'></script>
<script src='"""),_display_(/*12.15*/na/*12.17*/.nadm.controllers.routes.Assets.versioned("basemodule/basemodule.js")),format.raw/*12.86*/("""'></script>
<script src='"""),_display_(/*13.15*/na/*13.17*/.nadm.controllers.routes.Assets.versioned("nadm/nadm.js")),format.raw/*13.74*/("""'></script>

"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:39 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/imports/scripts.scala.html
                  HASH: 6ff0957d1033e5358045fbc72d5a97d615ec16a9
                  MATRIX: 1048->0|1088->14|1098->16|1173->71|1225->97|1235->99|1321->165|1373->191|1383->193|1489->278|1541->304|1551->306|1647->382|1699->408|1709->410|1820->500|1872->526|1882->528|1991->616|2043->642|2053->644|2164->734|2216->760|2226->762|2334->849|2386->875|2396->877|2504->964|2557->990|2568->992|2668->1071|2721->1097|2732->1099|2812->1158|2865->1184|2876->1186|2966->1255|3019->1281|3030->1283|3108->1340
                  LINES: 33->1|33->1|33->1|33->1|34->2|34->2|34->2|35->3|35->3|35->3|36->4|36->4|36->4|37->5|37->5|37->5|38->6|38->6|38->6|39->7|39->7|39->7|40->8|40->8|40->8|41->9|41->9|41->9|42->10|42->10|42->10|43->11|43->11|43->11|44->12|44->12|44->12|45->13|45->13|45->13
                  -- GENERATED --
              */
          