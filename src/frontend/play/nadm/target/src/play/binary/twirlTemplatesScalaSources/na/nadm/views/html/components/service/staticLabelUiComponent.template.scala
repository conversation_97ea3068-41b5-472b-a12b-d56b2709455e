
package na.nadm.views.html.components.service

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import pt.ptinovacao.naportal.components.{TextUiComponent, UiComponent}

object staticLabelUiComponent extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[UiComponent,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*3.2*/(uiComponent: UiComponent):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*5.2*/isPassword/*5.12*/ = {{
	uiComponent match {
		case textComponent : TextUiComponent if textComponent.getType == TextUiComponent.Type.passwordField => true
		case _ => false
	}
}};
Seq[Any](format.raw/*3.28*/("""

"""),format.raw/*10.2*/("""

"""),_display_(/*12.2*/if(isPassword)/*12.16*/{_display_(Seq[Any](format.raw/*12.17*/("""
    """),format.raw/*13.5*/("""<p class="form-control-static" id=""""),_display_(/*13.41*/uiComponent/*13.52*/.getElementId),format.raw/*13.65*/("""">••••••••••••••••••••</p>
""")))}/*14.2*/else/*14.6*/{_display_(Seq[Any](format.raw/*14.7*/("""
    """),format.raw/*15.5*/("""<p class="form-control-static" id=""""),_display_(/*15.41*/uiComponent/*15.52*/.getElementId),format.raw/*15.65*/("""" data-ng-bind="formData['"""),_display_(/*15.92*/uiComponent/*15.103*/.getElementId),format.raw/*15.116*/("""']"></p>
""")))}))
      }
    }
  }

  def render(uiComponent:UiComponent): play.twirl.api.HtmlFormat.Appendable = apply(uiComponent)

  def f:((UiComponent) => play.twirl.api.HtmlFormat.Appendable) = (uiComponent) => apply(uiComponent)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 19:41:26 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/service/staticLabelUiComponent.scala.html
                  HASH: 00e3eb9bfc8699bc45dcc210ab5dc71c292afb85
                  MATRIX: 678->1|1076->75|1180->104|1198->114|1387->101|1416->273|1445->276|1468->290|1507->291|1539->296|1602->332|1622->343|1656->356|1702->384|1714->388|1752->389|1784->394|1847->430|1867->441|1901->454|1955->481|1976->492|2011->505
                  LINES: 24->1|29->3|33->5|33->5|39->3|41->10|43->12|43->12|43->12|44->13|44->13|44->13|44->13|45->14|45->14|45->14|46->15|46->15|46->15|46->15|46->15|46->15|46->15
                  -- GENERATED --
              */
          