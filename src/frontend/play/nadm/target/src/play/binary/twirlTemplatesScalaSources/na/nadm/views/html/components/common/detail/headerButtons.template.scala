
package na.nadm.views.html.components.common.detail

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.AAAPIResources
/*2.2*/import na.nadm.utils.FormBuilderUtil.ViewType
/*3.2*/import na.naportalbase.views.tags.i18n
/*4.2*/import pt.alticelabs.nossis.security.views.html.authorized

object headerButtons extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[String,ViewType,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*6.2*/(context: String, viewContext : ViewType):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*6.43*/("""

"""),_display_(/*8.2*/viewContext/*8.13*/ match/*8.19*/ {/*9.5*/case ViewType.CREATE =>/*9.28*/ {_display_(Seq[Any](format.raw/*9.30*/("""
        """),_display_(/*10.10*/authorized(AAAPIResources.device(context).C.toString())/*10.65*/{_display_(Seq[Any](format.raw/*10.66*/("""
            """),format.raw/*11.13*/("""<button class="btn btn-primary fx-call-to-action disabled" data-na-portal-nadm-save-button data-click-event="create" id="forms_button_save">
                <i class="fuxicons fuxicons-floppy-disk"></i>
                """),_display_(/*13.18*/i18n("na.button.label.save")),format.raw/*13.46*/("""
            """),format.raw/*14.13*/("""</button>
            <button class="btn btn-default fx-call-to-action " data-ng-click="cancelCreate()" id="catalog_forms_button_cancel">
                """),_display_(/*16.18*/i18n("na.button.label.cancel")),format.raw/*16.48*/("""
            """),format.raw/*17.13*/("""</button>
        """)))}),format.raw/*18.10*/("""
    """)))}/*21.5*/case ViewType.EDIT =>/*21.26*/ {_display_(Seq[Any](format.raw/*21.28*/("""
        """),_display_(/*22.10*/authorized(AAAPIResources.device(context).U.toString())/*22.65*/{_display_(Seq[Any](format.raw/*22.66*/("""
            """),format.raw/*23.13*/("""<button class="btn btn-primary fx-call-to-action disabled" data-na-portal-nadm-save-button data-click-event="update" id="forms_button_save">
                <i class="fuxicons fuxicons-floppy-disk"></i>
                """),_display_(/*25.18*/i18n("na.button.label.save")),format.raw/*25.46*/("""
            """),format.raw/*26.13*/("""</button>
            <button class="btn btn-default fx-call-to-action" data-ng-click="cancelEdit()" id="catalog_forms_button_cancel">
                """),_display_(/*28.18*/i18n("na.button.label.cancel")),format.raw/*28.48*/("""
            """),format.raw/*29.13*/("""</button>
        """)))}),format.raw/*30.10*/("""
    """)))}/*33.5*/case ViewType.VIEW =>/*33.26*/ {_display_(Seq[Any](format.raw/*33.28*/("""
        """),_display_(/*34.10*/authorized(AAAPIResources.device(context).U.toString())/*34.65*/{_display_(Seq[Any](format.raw/*34.66*/("""
            """),format.raw/*35.13*/("""<button class="btn btn-default btn-sm fx-call-to-action" data-ng-click="editForm()" id="forms_button_edit" title=""""),_display_(/*35.128*/i18n("na.button.label.edit")),format.raw/*35.156*/("""">
                <i class="glyphicon glyphicon-pencil"></i>
            </button>
        """)))}),format.raw/*38.10*/("""
    """)))}}))
      }
    }
  }

  def render(context:String,viewContext:ViewType): play.twirl.api.HtmlFormat.Appendable = apply(context,viewContext)

  def f:((String,ViewType) => play.twirl.api.HtmlFormat.Appendable) = (context,viewContext) => apply(context,viewContext)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:29 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/common/detail/headerButtons.scala.html
                  HASH: 8de7bda46d10c982ed8838f783c82c67323919d0
                  MATRIX: 684->1|730->41|783->88|829->128|1209->189|1345->230|1373->233|1392->244|1406->250|1415->257|1446->280|1485->282|1522->292|1586->347|1625->348|1666->361|1913->581|1962->609|2003->622|2185->777|2236->807|2277->820|2327->839|2351->851|2381->872|2421->874|2458->884|2522->939|2561->940|2602->953|2849->1173|2898->1201|2939->1214|3118->1366|3169->1396|3210->1409|3260->1428|3284->1440|3314->1461|3354->1463|3391->1473|3455->1528|3494->1529|3535->1542|3678->1657|3728->1685|3852->1778
                  LINES: 24->1|25->2|26->3|27->4|32->6|37->6|39->8|39->8|39->8|39->9|39->9|39->9|40->10|40->10|40->10|41->11|43->13|43->13|44->14|46->16|46->16|47->17|48->18|49->21|49->21|49->21|50->22|50->22|50->22|51->23|53->25|53->25|54->26|56->28|56->28|57->29|58->30|59->33|59->33|59->33|60->34|60->34|60->34|61->35|61->35|61->35|64->38
                  -- GENERATED --
              */
          