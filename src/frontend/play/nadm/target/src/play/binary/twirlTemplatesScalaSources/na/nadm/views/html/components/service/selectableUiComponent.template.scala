
package na.nadm.views.html.components.service

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.utils.FormBuilderUtil
/*2.2*/import na.naportalbase.views.html.components.selectsComp.selectComp
/*3.2*/import pt.ptinovacao.naportal.components.{Option, SelectableUiComponent}
/*5.2*/import java.lang.Boolean.{FALSE, TRUE}
/*6.2*/import scala.collection.{immutable, mutable}

object selectableUiComponent extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[SelectableUiComponent,String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*8.2*/(uiComponent: SelectableUiComponent, angularModel: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*33.2*/selectOptions/*33.15*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*33.19*/("""
    """),_display_(/*34.6*/for(option: Option <- uiComponent.getOptions) yield /*34.51*/ {_display_(Seq[Any](format.raw/*34.53*/("""
        """),format.raw/*35.9*/("""<option value=""""),_display_(/*35.25*/option/*35.31*/.getValue),format.raw/*35.40*/(""""><i class="fa fa-undo"></i>
        """),_display_(/*36.10*/{
            val i18nText = FormBuilderUtil.parseI18n(option.getI18n)
            if (i18nText != "") {
                i18nText
            } else {
                option.getValue
            }
        }),format.raw/*43.10*/("""
        """),format.raw/*44.9*/("""</option>
    """)))}),format.raw/*45.6*/("""
""")))};def /*10.2*/elementAttr/*10.13*/ = {{
    var map = mutable.HashMap[String, String] (
        "id" -> uiComponent.getElementId,
        "name" -> uiComponent.getElementId,
	    "data-ng-model" -> angularModel,
        "data-na-portal-select-box" -> null,
        "class" -> "form-control input-sm",
        "full-width" -> null,
    )

    if(TRUE.equals(uiComponent.getRequired)){
        map.put("data-na-portal-required", null)
    } else {
        map.put("allow-clear", null)
    }

    if(FALSE.equals(uiComponent.getEditable)){
        map.put("disabled","")
    }

    immutable.HashMap(map.toSeq:_*)
}};
Seq[Any](format.raw/*8.60*/("""

"""),format.raw/*31.2*/("""

"""),format.raw/*46.2*/("""

"""),format.raw/*48.1*/("""<div data-na-portal-field-notification data-na-portal-input-notification-hr>
    """),_display_(/*49.6*/uiComponent/*49.17*/.getType/*49.25*/ match/*49.31*/ {/*50.9*/case SelectableUiComponent.Type.selectBox =>/*50.53*/ {_display_(Seq[Any](format.raw/*50.55*/("""
            """),_display_(/*51.14*/selectComp/*51.24*/.render(elementAttr, null, null, selectOptions)),format.raw/*51.71*/("""
        """)))}/*53.9*/case SelectableUiComponent.Type.checkBox =>/*53.52*/ {}/*55.9*/case SelectableUiComponent.Type.radioButton =>/*55.55*/ {}/*57.9*/case _ =>/*57.18*/ {}}),format.raw/*58.6*/("""
"""),format.raw/*59.1*/("""</div>

"""))
      }
    }
  }

  def render(uiComponent:SelectableUiComponent,angularModel:String): play.twirl.api.HtmlFormat.Appendable = apply(uiComponent,angularModel)

  def f:((SelectableUiComponent,String) => play.twirl.api.HtmlFormat.Appendable) = (uiComponent,angularModel) => apply(uiComponent,angularModel)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:29 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/service/selectableUiComponent.scala.html
                  HASH: 432c076562362b20738479cc4de889f0fc99d5cc
                  MATRIX: 678->1|722->39|797->108|877->183|923->223|1310->270|1447->923|1469->936|1550->940|1582->946|1643->991|1683->993|1719->1002|1762->1018|1777->1024|1807->1033|1872->1071|2099->1277|2135->1286|2180->1301|2205->331|2225->342|2833->328|2862->920|2891->1303|2920->1305|3028->1387|3048->1398|3065->1406|3080->1412|3090->1423|3143->1467|3183->1469|3224->1483|3243->1493|3311->1540|3339->1559|3391->1602|3402->1615|3457->1661|3468->1674|3486->1683|3510->1692|3538->1693
                  LINES: 24->1|25->2|26->3|27->5|28->6|33->8|37->33|37->33|39->33|40->34|40->34|40->34|41->35|41->35|41->35|41->35|42->36|49->43|50->44|51->45|52->10|52->10|74->8|76->31|78->46|80->48|81->49|81->49|81->49|81->49|81->50|81->50|81->50|82->51|82->51|82->51|83->53|83->53|83->55|83->55|83->57|83->57|83->58|84->59
                  -- GENERATED --
              */
          