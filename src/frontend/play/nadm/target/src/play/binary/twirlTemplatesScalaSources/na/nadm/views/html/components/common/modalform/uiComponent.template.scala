
package na.nadm.views.html.components.common.modalform

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.NadmSettings
/*2.2*/import na.nadm.utils.FormBuilderUtil
/*3.2*/import na.nadm.views.html.components.common.modalform.selectableUiComponent
/*4.2*/import na.nadm.views.html.components.common.tableform.{dateUiComponent, textUiComponent}
/*5.2*/import pt.ptinovacao.naportal.components.{DateUiComponent, SelectableUiComponent, TextUiComponent}
/*7.2*/import java.lang.Boolean.TRUE

object uiComponent extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[pt.ptinovacao.naportal.components.UiComponent,String,NadmSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*9.2*/(uiComponent: pt.ptinovacao.naportal.components.UiComponent, context: String, nadmSettings: NadmSettings):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*11.2*/angularModel/*11.14*/ = {{s"""model["${uiComponent.getElementId}"]"""}};def /*13.2*/requiredClass/*13.15*/ = {{
	if(TRUE.equals(uiComponent.getRequired)){
		"fx-required-field"
	} else {
		""
	}
}};
Seq[Any](format.raw/*9.107*/("""

"""),format.raw/*11.63*/("""

"""),format.raw/*19.2*/("""

"""),format.raw/*21.1*/("""<div class="form-group" data-ng-if="!readOnly" data-comp-name=""""),_display_(/*21.65*/uiComponent/*21.76*/.getElementId),format.raw/*21.89*/("""">
	<label class="col-sm-3 control-label """),_display_(/*22.40*/requiredClass),format.raw/*22.53*/("""">"""),_display_(/*22.56*/FormBuilderUtil/*22.71*/.parseI18n(uiComponent.getI18n)),format.raw/*22.102*/("""</label>
	<div class="col-sm-9">
	"""),_display_(/*24.3*/{
		uiComponent match {
			case textComp: TextUiComponent =>
				textUiComponent.render(textComp, angularModel, context, nadmSettings)
			case dateComp: DateUiComponent =>
				dateUiComponent.render(dateComp, angularModel)
			case selectableComp: SelectableUiComponent =>
				selectableUiComponent.render(selectableComp, angularModel)
			case _ =>
		}
	}),format.raw/*34.3*/("""
	"""),format.raw/*35.2*/("""</div>
</div>
<div class="form-group" data-ng-if="readOnly" data-comp-name=""""),_display_(/*37.64*/uiComponent/*37.75*/.getElementId),format.raw/*37.88*/("""">
	<label class="col-sm-3 control-label">"""),_display_(/*38.41*/FormBuilderUtil/*38.56*/.parseI18n(uiComponent.getI18n)),format.raw/*38.87*/("""</label>
	<div class="col-sm-9">
	"""),_display_(/*40.3*/if(uiComponent.isInstanceOf[TextUiComponent] && uiComponent.asInstanceOf[TextUiComponent].getType == TextUiComponent.Type.passwordField)/*40.139*/ {_display_(Seq[Any](format.raw/*40.141*/("""
		"""),format.raw/*41.3*/("""<p class="form-control-static" id=""""),_display_(/*41.39*/uiComponent/*41.50*/.getElementId),format.raw/*41.63*/("""">••••••••••••••••••••</p>
	""")))}/*42.4*/else/*42.9*/{_display_(Seq[Any](format.raw/*42.10*/("""
		"""),format.raw/*43.3*/("""<p class="form-control-static" id=""""),_display_(/*43.39*/uiComponent/*43.50*/.getElementId),format.raw/*43.63*/("""" data-ng-bind=""""),_display_(/*43.80*/angularModel),format.raw/*43.92*/(""" """),format.raw/*43.93*/("""|| '--'"></p>
	""")))}),format.raw/*44.3*/("""
	"""),format.raw/*45.2*/("""</div>
</div>


"""))
      }
    }
  }

  def render(uiComponent:pt.ptinovacao.naportal.components.UiComponent,context:String,nadmSettings:NadmSettings): play.twirl.api.HtmlFormat.Appendable = apply(uiComponent,context,nadmSettings)

  def f:((pt.ptinovacao.naportal.components.UiComponent,String,NadmSettings) => play.twirl.api.HtmlFormat.Appendable) = (uiComponent,context,nadmSettings) => apply(uiComponent,context,nadmSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:29 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/common/modalform/uiComponent.scala.html
                  HASH: a673b5713d38687b14647cdeeac1005ed6d2398b
                  MATRIX: 687->1|731->39|775->77|858->154|954->244|1060->345|1459->377|1643->485|1664->497|1727->549|1749->562|1870->482|1900->546|1929->651|1958->653|2049->717|2069->728|2103->741|2172->783|2206->796|2236->799|2260->814|2313->845|2374->880|2748->1234|2777->1236|2881->1313|2901->1324|2935->1337|3005->1380|3029->1395|3081->1426|3142->1461|3288->1597|3329->1599|3359->1602|3422->1638|3442->1649|3476->1662|3523->1692|3535->1697|3574->1698|3604->1701|3667->1737|3687->1748|3721->1761|3765->1778|3798->1790|3827->1791|3873->1807|3902->1809
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->7|34->9|38->11|38->11|38->13|38->13|45->9|47->11|49->19|51->21|51->21|51->21|51->21|52->22|52->22|52->22|52->22|52->22|54->24|64->34|65->35|67->37|67->37|67->37|68->38|68->38|68->38|70->40|70->40|70->40|71->41|71->41|71->41|71->41|72->42|72->42|72->42|73->43|73->43|73->43|73->43|73->43|73->43|73->43|74->44|75->45
                  -- GENERATED --
              */
          