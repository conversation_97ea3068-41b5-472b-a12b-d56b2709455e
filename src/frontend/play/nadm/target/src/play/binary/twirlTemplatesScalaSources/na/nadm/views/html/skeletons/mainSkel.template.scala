
package na.nadm.views.html.skeletons

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.views.html.imports
/*2.2*/import na.naportalbase.views.html.skeletons.root

object mainSkel extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[String,String,Html,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*4.2*/(context: String, title: String = "")(content: Html):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*6.2*/scripts/*6.9*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*6.13*/("""
"""),_display_(/*7.2*/imports/*7.9*/.scripts.render()),format.raw/*7.26*/("""
""")))};def /*10.2*/body/*10.6*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*10.10*/("""
    """),format.raw/*11.5*/("""<div id="nadm" class="page page--nadm fx-full-height inherit" data-na-portal-page-container data-nadm-context=""""),_display_(/*11.117*/context),format.raw/*11.124*/("""">
    """),_display_(/*12.6*/content),format.raw/*12.13*/("""
    """),format.raw/*13.5*/("""</div>
""")))};
Seq[Any](format.raw/*4.54*/("""

"""),format.raw/*8.2*/("""

"""),format.raw/*14.2*/("""

"""),_display_(/*16.2*/root/*16.6*/.render(title, body, scripts)),format.raw/*16.35*/("""
"""))
      }
    }
  }

  def render(context:String,title:String,content:Html): play.twirl.api.HtmlFormat.Appendable = apply(context,title)(content)

  def f:((String,String) => (Html) => play.twirl.api.HtmlFormat.Appendable) = (context,title) => (content) => apply(context,title)(content)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:39 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/skeletons/mainSkel.scala.html
                  HASH: 0693330d5361a4b5dfacfa7aaa35b94be58f8dcc
                  MATRIX: 669->1|710->36|1078->87|1208->142|1222->149|1302->153|1329->155|1343->162|1380->179|1405->184|1417->188|1498->192|1530->197|1670->309|1699->316|1733->324|1761->331|1793->336|1840->139|1868->181|1897->344|1926->347|1938->351|1988->380
                  LINES: 24->1|25->2|30->4|34->6|34->6|36->6|37->7|37->7|37->7|38->10|38->10|40->10|41->11|41->11|41->11|42->12|42->12|43->13|45->4|47->8|49->14|51->16|51->16|51->16
                  -- GENERATED --
              */
          