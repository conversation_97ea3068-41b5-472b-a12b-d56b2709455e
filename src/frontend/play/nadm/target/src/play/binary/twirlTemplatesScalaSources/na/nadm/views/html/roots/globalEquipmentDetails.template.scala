
package na.nadm.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.NadmConstants.NavContext
/*2.2*/import na.nadm.settings.NadmSettings
/*3.2*/import na.nadm.utils.FormBuilderUtil.ViewType
/*4.2*/import na.nadm.views.html.components.common.detail.headerButtons
/*5.2*/import na.nadm.views.html.components.common.lateralNav
/*6.2*/import na.nadm.views.html.components.common.tableform.tableForm
/*7.2*/import na.nadm.views.html.skeletons.mainSkel
/*8.2*/import na.naportalbase.views.tags.i18n
/*9.2*/import pt.ptinovacao.naportal.nadm.Configurations

object globalEquipmentDetails extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template8[String,String,String,String,String,Configurations,ViewType,NadmSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*11.2*/(
        context: String,
        manufacturer: String,
        model: String,
        version: String,
        firmwareFilename: String,
        configurations: Configurations,
        viewContext: ViewType,
        nadmSettings: NadmSettings
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*23.2*/navBar/*23.8*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*23.12*/("""
"""),_display_(/*24.2*/lateralNav/*24.12*/.render(context, NavContext.GLOBAL_EQUIP, nadmSettings)),format.raw/*24.67*/("""
""")))};def /*27.2*/headerAction/*27.14*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*27.18*/("""
"""),_display_(/*28.2*/headerButtons/*28.15*/.render(context, viewContext)),format.raw/*28.44*/("""
""")))};
Seq[Any](format.raw/*20.2*/("""


"""),format.raw/*25.2*/("""

"""),format.raw/*29.2*/("""

"""),_display_(/*31.2*/mainSkel(context)/*31.19*/ {_display_(Seq[Any](format.raw/*31.21*/("""
    """),format.raw/*32.5*/("""<div class="layout--page-with-navbar">
        """),_display_(/*33.10*/navBar),format.raw/*33.16*/("""
        """),format.raw/*34.9*/("""<div class="fx-main-content-wrapper layout--header-with-tab" data-ng-controller="NaPortalNadmCommonController" data-na-portal-nadm-global-equip-details-page data-manufacturer-id=""""),_display_(/*34.189*/manufacturer),format.raw/*34.201*/("""" data-model-id=""""),_display_(/*34.219*/model),format.raw/*34.224*/("""" data-firmware-filename=""""),_display_(/*34.251*/firmwareFilename),format.raw/*34.267*/("""" data-version-id=""""),_display_(/*34.287*/version),format.raw/*34.294*/("""" data-configurations="">
            <div class="fx-entity-header">
                <div class="fx-entity-header-info">
                    <span class="fx-entity-header-icon">
                        <i class="fuxicons fuxicons-equipment-configuration"></i>
                    </span>
                    <div class="fx-entity-header-title">
                        <h1>
                            """),_display_(/*42.30*/manufacturer),format.raw/*42.42*/(""" """),format.raw/*42.43*/("""- """),_display_(/*42.46*/model),format.raw/*42.51*/(""" """),format.raw/*42.52*/("""- """),_display_(/*42.55*/version),format.raw/*42.62*/("""
                        """),format.raw/*43.25*/("""</h1>
                        """),_display_(/*44.26*/if(!firmwareFilename.equals(""))/*44.58*/ {_display_(Seq[Any](format.raw/*44.60*/("""
                            """),format.raw/*45.29*/("""<i class="fa fa-file-code"></i>
                            <span id="na-nadm-firmwareFilename" style="margin-left: 5px;">
                                """),_display_(/*47.34*/i18n("na.portal.nadm.detail.equipmentassociation.view.firmwareFilename")),format.raw/*47.106*/("""
                                """),format.raw/*48.33*/(""": """),_display_(/*48.36*/firmwareFilename),format.raw/*48.52*/("""</span>
                        """)))}),format.raw/*49.26*/("""
                    """),format.raw/*50.21*/("""</div>
                </div>
                <div class="fx-entity-header-actions">
                """),_display_(/*53.18*/headerAction),format.raw/*53.30*/("""
                """),format.raw/*54.17*/("""</div>
            </div>
            <div class="fx-entity-info">
                <x-tab-container>
                    <x-tab label=""""),_display_(/*58.36*/i18n("na.portal.nadm.tabname.characteristics")),format.raw/*58.82*/("""">
                        <x-shadow-scroll inline>
                            <div class="tab-pane">
                            """),_display_(/*61.30*/tableForm/*61.39*/.render(configurations, viewContext, context, nadmSettings)),format.raw/*61.98*/("""
                            """),format.raw/*62.29*/("""</div>
                        </x-shadow-scroll>
                    </x-tab>
                </x-tab-container>
            </div>
        </div>
    </div>
""")))}))
      }
    }
  }

  def render(context:String,manufacturer:String,model:String,version:String,firmwareFilename:String,configurations:Configurations,viewContext:ViewType,nadmSettings:NadmSettings): play.twirl.api.HtmlFormat.Appendable = apply(context,manufacturer,model,version,firmwareFilename,configurations,viewContext,nadmSettings)

  def f:((String,String,String,String,String,Configurations,ViewType,NadmSettings) => play.twirl.api.HtmlFormat.Appendable) = (context,manufacturer,model,version,firmwareFilename,configurations,viewContext,nadmSettings) => apply(context,manufacturer,model,version,firmwareFilename,configurations,viewContext,nadmSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:28 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/roots/globalEquipmentDetails.scala.html
                  HASH: 19ae3f8131252edf6e5403235ffe3d71805639eb
                  MATRIX: 665->1|721->52|765->91|818->139|890->206|952->263|1023->329|1075->376|1121->417|1558->471|1892->733|1906->739|1987->743|2016->746|2035->756|2111->811|2137->819|2158->831|2239->835|2268->838|2290->851|2340->880|2382->726|2415->814|2446->883|2477->888|2503->905|2543->907|2576->913|2652->962|2679->968|2716->978|2924->1158|2958->1170|3004->1188|3031->1193|3086->1220|3124->1236|3172->1256|3201->1263|3639->1674|3672->1686|3701->1687|3731->1690|3757->1695|3786->1696|3816->1699|3844->1706|3898->1732|3957->1764|3998->1796|4038->1798|4096->1828|4281->1986|4375->2058|4437->2092|4467->2095|4504->2111|4569->2145|4619->2167|4751->2272|4784->2284|4830->2302|4997->2442|5064->2488|5226->2623|5244->2632|5324->2691|5382->2721
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|32->9|37->11|50->23|50->23|52->23|53->24|53->24|53->24|54->27|54->27|56->27|57->28|57->28|57->28|59->20|62->25|64->29|66->31|66->31|66->31|67->32|68->33|68->33|69->34|69->34|69->34|69->34|69->34|69->34|69->34|69->34|69->34|77->42|77->42|77->42|77->42|77->42|77->42|77->42|77->42|78->43|79->44|79->44|79->44|80->45|82->47|82->47|83->48|83->48|83->48|84->49|85->50|88->53|88->53|89->54|93->58|93->58|96->61|96->61|96->61|97->62
                  -- GENERATED --
              */
          