
package na.nadm.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.NadmConstants.NavContext
/*2.2*/import na.nadm.settings.NadmSettings
/*3.2*/import na.nadm.utils.FormBuilderUtil.ViewType
/*4.2*/import na.nadm.views.components.common.form.HelperFieldDrawingAttributes
/*5.2*/import na.nadm.views.html.components.common.detail.headerButtons
/*6.2*/import na.nadm.views.html.skeletons.mainSkel
/*7.2*/import na.naportalbase.views.html.components.inputsComp.inputTextComp
/*8.2*/import na.naportalbase.views.tags.i18n

object equipmentCreate extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[String,ViewType,NadmSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*10.2*/(context: String, viewContext: ViewType, nadmSettings: NadmSettings):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*12.2*/navBar/*12.8*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*12.12*/("""
"""),_display_(/*13.2*/na/*13.4*/.nadm.views.html.components.common.lateralNav.render(context, NavContext.EQUIPMENT, nadmSettings)),format.raw/*13.101*/("""
""")))};def /*17.2*/headerAction/*17.14*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*17.18*/("""
"""),_display_(/*18.2*/headerButtons/*18.15*/.render(context, viewContext)),format.raw/*18.44*/("""
""")))};def /*21.2*/rightSide/*21.11*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*21.15*/("""
    """),format.raw/*22.5*/("""<div id="fx-splitter-content" class="fx-splitter-content">
        <div class="fx-splitter-content-inner"></div>
    </div>
""")))};def /*27.2*/identificationBlock/*27.21*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*27.25*/("""
    """),format.raw/*28.5*/("""<div class="form form--mmv">
        <div class="form-group">
            <label class="control-label">"""),_display_(/*30.43*/i18n("na.portal.nadm.manufacturer")),format.raw/*30.78*/("""</label>
            <x-manufacturer-field></x-manufacturer-field>
        </div>
        <div class="form-group">
            <label class="control-label">"""),_display_(/*34.43*/i18n("na.portal.nadm.model")),format.raw/*34.71*/("""</label>
            <x-model-field></x-model-field>
        </div>
        <div class="form-group">
            <label class="control-label">"""),_display_(/*38.43*/i18n("na.portal.nadm.version")),format.raw/*38.73*/("""</label>
            <x-version-field></x-version-field>
        </div>
        <div class="form-group">
            <label class="control-label">"""),_display_(/*42.43*/i18n("na.portal.nadm.field.user.label")),format.raw/*42.82*/("""</label>
            <x-select data-field="client" url=""""),_display_(/*43.49*/na/*43.51*/.nadm.controllers.routes.Search.clientNames(context)),format.raw/*43.103*/(""""></x-select>
        </div>
        <div class="form-group">
            <a class="pull-right btn btn-default btn-sm disabled ng-scope" data-ng-click="characterizeEquipment()" data-na-portal-nadm-commons-characterize-button id="button-characterize">
                <i class="fa fa-angle-double-down"></i>
                """),_display_(/*48.18*/i18n("na.portal.nadm.characterize.equipment")),format.raw/*48.63*/("""
            """),format.raw/*49.13*/("""</a>
        </div>
    </div>
""")))};def /*54.2*/characteristicsBlock/*54.22*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*54.26*/("""
    """),format.raw/*55.5*/("""<div id="characteristics">
        <div class="alert alert-info">
            <div class="fx-alert-icon">
            </div>
            <div class="fx-alert-message">
                <strong>"""),_display_(/*60.26*/i18n("na.portal.nadm.characterize.after.fill")),format.raw/*60.72*/("""</strong>
            </div>
        </div>
    </div>
""")))};
Seq[Any](format.raw/*10.70*/("""

"""),format.raw/*14.2*/("""


"""),format.raw/*19.2*/("""

"""),format.raw/*25.2*/("""

"""),format.raw/*52.2*/("""

"""),format.raw/*64.2*/("""

"""),_display_(/*66.2*/mainSkel(context)/*66.19*/ {_display_(Seq[Any](format.raw/*66.21*/("""
    """),format.raw/*67.5*/("""<div class="layout--page-with-navbar">
        """),_display_(/*68.10*/navBar),format.raw/*68.16*/("""
        """),format.raw/*69.9*/("""<div class="fx-main-content-wrapper fx-with-main-nav" data-ng-controller="NaPortalNadmCommonController" data-na-portal-nadm-equipment-create-page>
            <div class="fx-entity-header">
                <div class="fx-entity-header-info">
                    <span class="fx-entity-header-icon">
                        <i class="glyphicon glyphicon-hdd"></i>
                    </span>
                    <div class="fx-entity-header-title">
                        <h1>
                        """),_display_(/*77.26*/i18n("na.portal.nadm.create.equipment")),format.raw/*77.65*/("""
                        """),format.raw/*78.25*/("""</h1>
                    </div>
                </div>
                <div class="fx-entity-header-actions">
                """),_display_(/*82.18*/headerAction),format.raw/*82.30*/("""
                """),format.raw/*83.17*/("""</div>
            </div>
            <div class="fx-entity-info" data-context="equipmentModel">
                <x-shadow-scroll>
                    <x-collapsible class="fx-section" header=""""),_display_(/*87.64*/i18n("nadm.detail.section.identification")),format.raw/*87.106*/("""">
                    """),_display_(/*88.22*/identificationBlock),format.raw/*88.41*/("""
                    """),format.raw/*89.21*/("""</x-collapsible>
                    <x-collapsible class="fx-section" header=""""),_display_(/*90.64*/i18n("nadm.detail.section.characteristics")),format.raw/*90.107*/("""">
                    """),_display_(/*91.22*/characteristicsBlock),format.raw/*91.42*/("""
                    """),format.raw/*92.21*/("""</x-collapsible>
                </x-shadow-scroll>
            </div>
        </div>
    </div>
""")))}),format.raw/*97.2*/("""
"""))
      }
    }
  }

  def render(context:String,viewContext:ViewType,nadmSettings:NadmSettings): play.twirl.api.HtmlFormat.Appendable = apply(context,viewContext,nadmSettings)

  def f:((String,ViewType,NadmSettings) => play.twirl.api.HtmlFormat.Appendable) = (context,viewContext,nadmSettings) => apply(context,viewContext,nadmSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:39 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/roots/equipmentCreate.scala.html
                  HASH: 0ef9a8f4111f0d87acb748c4e6ef50afdcafc0e2
                  MATRIX: 665->1|721->52|765->91|818->139|898->214|970->281|1022->328|1099->400|1475->443|1622->516|1636->522|1717->526|1746->529|1756->531|1875->628|1901->638|1922->650|2003->654|2032->657|2054->670|2104->699|2130->707|2148->716|2229->720|2262->726|2413->859|2441->878|2522->882|2555->888|2688->994|2744->1029|2932->1190|2981->1218|3155->1365|3206->1395|3384->1546|3444->1585|3529->1643|3540->1645|3614->1697|3970->2026|4036->2071|4078->2085|4136->2125|4165->2145|4246->2149|4279->2155|4504->2353|4571->2399|4671->511|4702->631|4735->702|4766->854|4797->2120|4828->2459|4859->2464|4885->2481|4925->2483|4958->2489|5034->2538|5061->2544|5098->2554|5635->3064|5695->3103|5749->3129|5908->3261|5941->3273|5987->3291|6212->3489|6276->3531|6328->3556|6368->3575|6418->3597|6526->3678|6591->3721|6643->3746|6684->3766|6734->3788|6867->3891
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|36->10|40->12|40->12|42->12|43->13|43->13|43->13|44->17|44->17|46->17|47->18|47->18|47->18|48->21|48->21|50->21|51->22|54->27|54->27|56->27|57->28|59->30|59->30|63->34|63->34|67->38|67->38|71->42|71->42|72->43|72->43|72->43|77->48|77->48|78->49|81->54|81->54|83->54|84->55|89->60|89->60|94->10|96->14|99->19|101->25|103->52|105->64|107->66|107->66|107->66|108->67|109->68|109->68|110->69|118->77|118->77|119->78|123->82|123->82|124->83|128->87|128->87|129->88|129->88|130->89|131->90|131->90|132->91|132->91|133->92|138->97
                  -- GENERATED --
              */
          