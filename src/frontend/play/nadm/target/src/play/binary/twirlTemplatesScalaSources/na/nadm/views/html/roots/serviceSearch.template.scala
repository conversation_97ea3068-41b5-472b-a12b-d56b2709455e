
package na.nadm.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.NadmConstants.NavContext
/*2.2*/import na.nadm.settings.NadmSettings
/*3.2*/import na.nadm.views.html.components.common.lateralNav
/*4.2*/import na.nadm.views.html.components.service.leftSideForm
/*5.2*/import na.nadm.views.html.skeletons.mainSkel
/*6.2*/import na.naportalbase.views.tags.i18n
/*7.2*/import pt.alticelabs.nossis.security.views.html.authorized
/*8.2*/import na.nadm.settings.AAAPIResources

object serviceSearch extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[String,List[String],NadmSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*10.2*/(context: String, serviceTypes: List[String], nadmSettings: NadmSettings):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*12.2*/rightSide/*12.11*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*12.15*/("""
    """),format.raw/*13.5*/("""<div class="fx-splitter-content-inner">
        <div class="fx-entity-header">
            <div class="fx-entity-header-info">
                <span class="fx-entity-header-icon">
                    <i class="fa fa-cogs"></i>
                </span>
                <div class="fx-entity-header-title">
                    <h1>
                    """),_display_(/*21.22*/i18n("nadm.search.service.title")),format.raw/*21.55*/("""
                    """),format.raw/*22.21*/("""</h1>
                </div>
            </div>
            <div class="fx-entity-header-actions">
            """),_display_(/*26.14*/authorized(AAAPIResources.device(context).C.toString())/*26.69*/ {_display_(Seq[Any](format.raw/*26.71*/("""
                """),format.raw/*27.17*/("""<button id="create-button" class="btn btn-primary fx-call-to-action" data-ng-click="createService()">
                    <i class="glyphicon glyphicon-plus"></i>
                    """),_display_(/*29.22*/i18n("na.portal.nadm.create")),format.raw/*29.51*/("""
                """),format.raw/*30.17*/("""</button>
            """)))}),format.raw/*31.14*/("""
            """),format.raw/*32.13*/("""</div>
        </div>
        <x-shadow-scroll vertical>
            <div id="entity-content" class="fx-entity-info" >
                <div data-na-portal-datatables-toolbar class="fx-bulk-actions fx-table-actions clearfix"></div>
                <div data-na-portal-toolbar-advanced-search data-table-id="datatableService"></div>
                <table id="datatableService" class="table table-striped table-hover"
                data-na-portal-table
                data-na-portal-table-datatable
                data-na-portal-table-load-using-ajax
                data-config-url=""""),_display_(/*42.35*/na/*42.37*/.nadm.controllers.routes.TableConfigs.service(context)),format.raw/*42.91*/(""""
                data-na-portal-nadm-service-table></table>
            </div>
        </x-shadow-scroll>
    </div>
""")))};
Seq[Any](format.raw/*10.75*/("""

"""),format.raw/*47.2*/("""

"""),_display_(/*49.2*/mainSkel(context)/*49.19*/ {_display_(Seq[Any](format.raw/*49.21*/("""
    """),format.raw/*50.5*/("""<div class="fx-push-footer page--nadm-search-page fx-full-height" data-na-portal-nadm-service-page>
        """),_display_(/*51.10*/lateralNav/*51.20*/.render(context, NavContext.SERVICE, nadmSettings)),format.raw/*51.70*/("""
        """),format.raw/*52.9*/("""<div data-na-portal-nadm-side-bar-search data-context="service">
            <x-splitter class="splitter splitter--nadm-search">
                <form slot="left" data-na-portal-nadm-service-search-form class="search-sidebar">
                    """),_display_(/*55.22*/leftSideForm/*55.34*/.render(context, serviceTypes)),format.raw/*55.64*/("""
                """),format.raw/*56.17*/("""</form>
                <div id="fx-splitter-content" slot="right">
                    """),_display_(/*58.22*/rightSide),format.raw/*58.31*/("""
                """),format.raw/*59.17*/("""</div>
                <div slot="collapsed-left">
                    <div class="fx-info-sidebar-collapsed left">
                        <div class="fx-sidebar-header search-sidebar__header">
                            <span class="fx-entity-header-icon">
                                <i class="fa fa-search"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </x-splitter>
        </div>
    </div>
""")))}))
      }
    }
  }

  def render(context:String,serviceTypes:List[String],nadmSettings:NadmSettings): play.twirl.api.HtmlFormat.Appendable = apply(context,serviceTypes,nadmSettings)

  def f:((String,List[String],NadmSettings) => play.twirl.api.HtmlFormat.Appendable) = (context,serviceTypes,nadmSettings) => apply(context,serviceTypes,nadmSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:39 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/roots/serviceSearch.scala.html
                  HASH: 84f1903e73f1b6db2a8f083d3a455857087fcb3b
                  MATRIX: 665->1|721->52|765->91|827->148|892->208|944->255|990->296|1056->357|1434->400|1586->478|1604->487|1685->491|1718->497|2103->855|2157->888|2207->910|2350->1026|2414->1081|2454->1083|2500->1101|2713->1287|2763->1316|2809->1334|2864->1358|2906->1372|3530->1969|3541->1971|3616->2025|3780->473|3811->2149|3842->2154|3868->2171|3908->2173|3941->2179|4078->2289|4097->2299|4168->2349|4205->2359|4483->2610|4504->2622|4555->2652|4601->2670|4719->2761|4749->2770|4795->2788
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|36->10|40->12|40->12|42->12|43->13|51->21|51->21|52->22|56->26|56->26|56->26|57->27|59->29|59->29|60->30|61->31|62->32|72->42|72->42|72->42|78->10|80->47|82->49|82->49|82->49|83->50|84->51|84->51|84->51|85->52|88->55|88->55|88->55|89->56|91->58|91->58|92->59
                  -- GENERATED --
              */
          