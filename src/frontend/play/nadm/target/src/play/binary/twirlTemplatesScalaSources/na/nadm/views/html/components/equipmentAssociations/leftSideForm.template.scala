
package na.nadm.views.html.components.equipmentAssociations

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.views.html.components.common.form.{associationsNameField, associationsTypeField}
/*2.2*/import na.naportalbase.views.html.components.buttonsComp.leftSideFormInputButtonsComp
/*3.2*/import na.naportalbase.views.tags.i18n
/*5.2*/import scala.collection.mutable

object leftSideForm extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*7.2*/(context: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*9.2*/clearButtonAttributes/*9.23*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-click", "searchCtrl.clearForm($event)")
    map.put("id", "clearSearchButton")
    map.put("type", "reset")
    map.put("value", i18n("na.buttons.clear"))
    map
}};
Seq[Any](format.raw/*7.19*/("""

"""),format.raw/*16.2*/("""

"""),format.raw/*18.1*/("""<header class="fx-sidebar-header search-sidebar__header">
    <div class="fx-sidebar-header-inner">
        <p>
            <span class="fx-entity-header-icon">
                <i class="fa fa-search"></i>
            </span>
            <x-i18n key="na.basemodule.lateralSearch.title"></x-i18n>
        </p>
    </div>
</header>
<div class="fx-sidebar-content-wrapper search-sidebar__field-list">
    <x-shadow-scroll vertical>
        """),_display_(/*30.10*/associationsTypeField/*30.31*/.render()),format.raw/*30.40*/("""
        """),_display_(/*31.10*/associationsNameField/*31.31*/.render()),format.raw/*31.40*/("""
    """),format.raw/*32.5*/("""</x-shadow-scroll>
</div>
<div class="form-group search-sidebar__button-list">
    """),_display_(/*35.6*/leftSideFormInputButtonsComp/*35.34*/.render(null, clearButtonAttributes)),format.raw/*35.70*/("""
"""),format.raw/*36.1*/("""</div>
"""))
      }
    }
  }

  def render(context:String): play.twirl.api.HtmlFormat.Appendable = apply(context)

  def f:((String) => play.twirl.api.HtmlFormat.Appendable) = (context) => apply(context)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:29 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/equipmentAssociations/leftSideForm.scala.html
                  HASH: 23511933de59576097ba16fcdfabb29340d3741b
                  MATRIX: 692->1|795->98|888->185|934->226|1277->260|1372->280|1401->301|1670->277|1699->540|1728->542|2193->980|2223->1001|2253->1010|2290->1020|2320->1041|2350->1050|2382->1055|2492->1139|2529->1167|2586->1203|2614->1204
                  LINES: 24->1|25->2|26->3|27->5|32->7|36->9|36->9|44->7|46->16|48->18|60->30|60->30|60->30|61->31|61->31|61->31|62->32|65->35|65->35|65->35|66->36
                  -- GENERATED --
              */
          