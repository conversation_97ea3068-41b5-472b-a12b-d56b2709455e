
package na.nadm.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.NadmConstants.{EqDetailsTabContext, NavContext}
/*2.2*/import na.nadm.settings.NadmSettings
/*3.2*/import na.nadm.utils.FormBuilderUtil.ViewType
/*4.2*/import na.nadm.views.html.components.common.detail.headerButtons
/*5.2*/import na.nadm.views.html.components.common.lateralNav
/*6.2*/import na.nadm.views.html.components.common.tableform.tableForm
/*7.2*/import na.nadm.views.html.components.equipmentAssociations.associationsSearch
/*8.2*/import na.nadm.views.html.skeletons.mainSkel
/*9.2*/import na.naportalbase.views.tags.i18n
/*10.2*/import pt.ptinovacao.naportal.nadm.Configurations

object equipmentDetails extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template11[String,String,String,String,String,String,String,Configurations,ViewType,EqDetailsTabContext,NadmSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*12.2*/(
        context: String,
        manufacturer: String,
        model: String,
        version: String,
        clientId: String,
        equipmentId: String,
        tagsValue: String,
        configurations: Configurations,
        viewContext: ViewType,
        tabContext: EqDetailsTabContext,
        nadmSettings: NadmSettings
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*26.2*/navBar/*26.8*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*26.12*/("""
"""),_display_(/*27.2*/lateralNav/*27.12*/.render(context, NavContext.EQUIPMENT, nadmSettings)),format.raw/*27.64*/("""
""")))};def /*30.2*/headerAction/*30.14*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*30.18*/("""
"""),_display_(/*31.2*/headerButtons/*31.15*/.render(context, viewContext)),format.raw/*31.44*/("""
""")))};def /*34.2*/characteristicsTab/*34.20*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*34.24*/("""
    """),format.raw/*35.5*/("""<x-shadow-scroll inline>
        <div class="tab-pane">
            <x-collapsible class="fx-section" header=""""),_display_(/*37.56*/i18n("na.portal.nadm.blockname.characteristics.base")),format.raw/*37.109*/("""">
                <div class="form-horizontal">
                    <div class="form-group">
                        <label class="control-label col-sm-2">
                            <x-i18n key="na.portal.nadm.field.tags.label"></x-i18n>
                        </label>
                        <div data-na-portal-nadm-tags data-tags=""""),_display_(/*43.67*/tagsValue),format.raw/*43.76*/("""" class="nadm-detail-tag-container form-control-static col-sm-10"></div>
                    </div>
                </div>
            </x-collapsible>
            <x-collapsible class="fx-section" header=""""),_display_(/*47.56*/i18n("na.portal.nadm.blockname.characteristics")),format.raw/*47.104*/("""">
                """),_display_(/*48.18*/tableForm/*48.27*/.render(configurations, viewContext, context, nadmSettings)),format.raw/*48.86*/("""
            """),format.raw/*49.13*/("""</x-collapsible>
        </div>
    </x-shadow-scroll>
""")))};
Seq[Any](format.raw/*24.2*/("""

"""),format.raw/*28.2*/("""

"""),format.raw/*32.2*/("""

"""),format.raw/*52.2*/("""

"""),_display_(/*54.2*/mainSkel(context)/*54.19*/ {_display_(Seq[Any](format.raw/*54.21*/("""
    """),format.raw/*55.5*/("""<div class="layout--page-with-navbar">
        """),_display_(/*56.10*/navBar),format.raw/*56.16*/("""
        """),format.raw/*57.9*/("""<div class="page--nadm-equipment-detail nadm-equipment__equipment-view-page fx-main-content-wrapper layout--header-with-tab" data-ng-controller="NaPortalNadmCommonController" data-na-portal-nadm-equipment-details-page data-equipment-id=""""),_display_(/*57.247*/equipmentId),format.raw/*57.258*/("""" data-client-id=""""),_display_(/*57.277*/clientId),format.raw/*57.285*/("""" data-manufacturer=""""),_display_(/*57.307*/manufacturer),format.raw/*57.319*/("""" data-tags=""""),_display_(/*57.333*/tagsValue),format.raw/*57.342*/("""" data-model=""""),_display_(/*57.357*/model),format.raw/*57.362*/("""" data-version=""""),_display_(/*57.379*/version),format.raw/*57.386*/("""" data-mode=""""),_display_(/*57.400*/viewContext/*57.411*/.toString.toLowerCase),format.raw/*57.432*/("""">
            <div class="fx-entity-header">
                <div class="fx-entity-header-info">
                    <span class="fx-entity-header-icon">
                        <i class="glyphicon glyphicon-hdd"></i>
                    </span>
                    <div class="fx-entity-header-title">
                        <h1>
                            <div style="display: flex">
                            """),_display_(/*66.30*/equipmentId),format.raw/*66.41*/(""" """),format.raw/*66.42*/("""-&nbsp<span is="x-dynamic-span" data-truncated-at="middle">"""),_display_(/*66.102*/clientId),format.raw/*66.110*/("""</span>
                            </div>
                            """),_display_(/*68.30*/manufacturer),format.raw/*68.42*/(""" """),format.raw/*68.43*/("""- """),_display_(/*68.46*/model),format.raw/*68.51*/(""" """),format.raw/*68.52*/("""- """),_display_(/*68.55*/version),format.raw/*68.62*/("""
                        """),format.raw/*69.25*/("""</h1>
                    </div>
                </div>
                <div class="fx-entity-header-actions">
                """),_display_(/*73.18*/headerAction),format.raw/*73.30*/("""
                """),format.raw/*74.17*/("""</div>
            </div>
            <div class="fx-entity-info">
                <x-tab-container data-na-portal-nadm-equipment-details-tab-selector>
                    <x-tab context="characteristics" label=""""),_display_(/*78.62*/i18n("na.portal.nadm.tabname.characteristics")),format.raw/*78.108*/(""""
                        """),_display_(/*79.26*/("active".when(tabContext.toString.toLowerCase() == "characteristics"))),format.raw/*79.97*/(""">
                        """),_display_(/*80.26*/characteristicsTab),format.raw/*80.44*/("""
                    """),format.raw/*81.21*/("""</x-tab>
                    <x-tab context="associations" label=""""),_display_(/*82.59*/i18n("na.portal.nadm.tabname.associations")),format.raw/*82.102*/(""""
                        """),_display_(/*83.26*/("active".when(tabContext.toString.toLowerCase() == "associations"))),format.raw/*83.94*/(""">
                        <div class="tab-pane">
                        """),_display_(/*85.26*/associationsSearch/*85.44*/.render(context, equipmentId, viewContext)),format.raw/*85.86*/("""
                        """),format.raw/*86.25*/("""</div>
                    </x-tab>
                </x-tab-container>
            </div>
        </div>
    </div>
""")))}))
      }
    }
  }

  def render(context:String,manufacturer:String,model:String,version:String,clientId:String,equipmentId:String,tagsValue:String,configurations:Configurations,viewContext:ViewType,tabContext:EqDetailsTabContext,nadmSettings:NadmSettings): play.twirl.api.HtmlFormat.Appendable = apply(context,manufacturer,model,version,clientId,equipmentId,tagsValue,configurations,viewContext,tabContext,nadmSettings)

  def f:((String,String,String,String,String,String,String,Configurations,ViewType,EqDetailsTabContext,NadmSettings) => play.twirl.api.HtmlFormat.Appendable) = (context,manufacturer,model,version,clientId,equipmentId,tagsValue,configurations,viewContext,tabContext,nadmSettings) => apply(context,manufacturer,model,version,clientId,equipmentId,tagsValue,configurations,viewContext,tabContext,nadmSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:39 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/roots/equipmentDetails.scala.html
                  HASH: 3c2c342be85337e5074b6ea17ccd89a553d07296
                  MATRIX: 665->1|744->75|788->114|841->162|913->229|975->286|1046->352|1131->432|1183->479|1230->520|1696->574|2122->926|2136->932|2217->936|2246->939|2265->949|2338->1001|2364->1009|2385->1021|2466->1025|2495->1028|2517->1041|2567->1070|2593->1078|2620->1096|2701->1100|2734->1106|2874->1219|2949->1272|3322->1618|3352->1627|3590->1838|3660->1886|3708->1907|3726->1916|3806->1975|3848->1989|3946->921|3977->1004|4008->1073|4039->2048|4070->2053|4096->2070|4136->2072|4169->2078|4245->2127|4272->2133|4309->2143|4575->2381|4608->2392|4655->2411|4685->2419|4735->2441|4769->2453|4811->2467|4842->2476|4885->2491|4912->2496|4957->2513|4986->2520|5028->2534|5049->2545|5092->2566|5546->2993|5578->3004|5607->3005|5695->3065|5725->3073|5826->3147|5859->3159|5888->3160|5918->3163|5944->3168|5973->3169|6003->3172|6031->3179|6085->3205|6244->3337|6277->3349|6323->3367|6567->3584|6635->3630|6690->3658|6782->3729|6837->3757|6876->3775|6926->3797|7021->3865|7086->3908|7141->3936|7230->4004|7333->4080|7360->4098|7423->4140|7477->4166
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|32->9|33->10|38->12|54->26|54->26|56->26|57->27|57->27|57->27|58->30|58->30|60->30|61->31|61->31|61->31|62->34|62->34|64->34|65->35|67->37|67->37|73->43|73->43|77->47|77->47|78->48|78->48|78->48|79->49|83->24|85->28|87->32|89->52|91->54|91->54|91->54|92->55|93->56|93->56|94->57|94->57|94->57|94->57|94->57|94->57|94->57|94->57|94->57|94->57|94->57|94->57|94->57|94->57|94->57|94->57|103->66|103->66|103->66|103->66|103->66|105->68|105->68|105->68|105->68|105->68|105->68|105->68|105->68|106->69|110->73|110->73|111->74|115->78|115->78|116->79|116->79|117->80|117->80|118->81|119->82|119->82|120->83|120->83|122->85|122->85|122->85|123->86
                  -- GENERATED --
              */
          