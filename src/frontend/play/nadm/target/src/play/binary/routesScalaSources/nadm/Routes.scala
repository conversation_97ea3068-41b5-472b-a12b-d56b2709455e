// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/conf/nadm.routes
// @DATE:Tue Jul 01 11:21:28 WEST 2025

package nadm

import play.core.routing._
import play.core.routing.HandlerInvokerFactory._

import play.api.mvc._

import _root_.controllers.Assets.Asset

class Routes(
  override val errorHandler: play.api.http.HttpErrorHandler, 
  // @LINE:2
  Application_7: javax.inject.Provider[na.nadm.controllers.Application],
  // @LINE:16
  Assets_6: javax.inject.Provider[na.nadm.controllers.Assets],
  // @LINE:20
  Navigation_5: javax.inject.Provider[na.nadm.controllers.Navigation],
  // @LINE:23
  Detail_2: javax.inject.Provider[na.nadm.controllers.Detail],
  // @LINE:59
  Export_3: javax.inject.Provider[na.nadm.controllers.Export],
  // @LINE:63
  Operation_1: javax.inject.Provider[na.nadm.controllers.Operation],
  // @LINE:77
  Search_0: javax.inject.Provider[na.nadm.controllers.Search],
  // @LINE:84
  Tag_9: javax.inject.Provider[na.nadm.controllers.Tag],
  // @LINE:90
  TableConfigs_8: javax.inject.Provider[na.nadm.controllers.TableConfigs],
  // @LINE:119
  Filters_4: javax.inject.Provider[na.nadm.controllers.Filters],
  // @LINE:129
  naportalbase_Routes_0: naportalbase.Routes,
  val prefix: String
) extends GeneratedRouter {

   @javax.inject.Inject()
   def this(errorHandler: play.api.http.HttpErrorHandler,
    // @LINE:2
    Application_7: javax.inject.Provider[na.nadm.controllers.Application],
    // @LINE:16
    Assets_6: javax.inject.Provider[na.nadm.controllers.Assets],
    // @LINE:20
    Navigation_5: javax.inject.Provider[na.nadm.controllers.Navigation],
    // @LINE:23
    Detail_2: javax.inject.Provider[na.nadm.controllers.Detail],
    // @LINE:59
    Export_3: javax.inject.Provider[na.nadm.controllers.Export],
    // @LINE:63
    Operation_1: javax.inject.Provider[na.nadm.controllers.Operation],
    // @LINE:77
    Search_0: javax.inject.Provider[na.nadm.controllers.Search],
    // @LINE:84
    Tag_9: javax.inject.Provider[na.nadm.controllers.Tag],
    // @LINE:90
    TableConfigs_8: javax.inject.Provider[na.nadm.controllers.TableConfigs],
    // @LINE:119
    Filters_4: javax.inject.Provider[na.nadm.controllers.Filters],
    // @LINE:129
    naportalbase_Routes_0: naportalbase.Routes
  ) = this(errorHandler, Application_7, Assets_6, Navigation_5, Detail_2, Export_3, Operation_1, Search_0, Tag_9, TableConfigs_8, Filters_4, naportalbase_Routes_0, "/")

  def withPrefix(prefix: String): Routes = {
    nadm.RoutesPrefix.setPrefix(prefix)
    new Routes(errorHandler, Application_7, Assets_6, Navigation_5, Detail_2, Export_3, Operation_1, Search_0, Tag_9, TableConfigs_8, Filters_4, naportalbase_Routes_0, prefix)
  }

  private[this] val defaultPrefix: String = {
    if (this.prefix.endsWith("/")) "" else "/"
  }

  def documentation = List(
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/home""", """@na.nadm.controllers.Application@.index(context:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/resume""", """@na.nadm.controllers.Application@.resume(context:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/jsSettings""", """@na.nadm.controllers.Application@.jsSettings(context:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """nadm/assets/javascripts/routes""", """@na.nadm.controllers.Application@.javascriptRoutes()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """nadm/imports/jsscripts""", """@na.nadm.controllers.Application@.getModuleAngularScripts()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """nadm/assets/""" + "$" + """file<.+>""", """@na.nadm.controllers.Assets@.versioned(path:String = "/public", file:Asset)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/globalequipment""", """@na.nadm.controllers.Navigation@.globalEquipment(context:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/globalequipment/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>""", """@na.nadm.controllers.Navigation@.globalEquipmentDetails(context:String, manufacturer:String, model:String, version:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/globalequipment/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>/edit""", """@na.nadm.controllers.Navigation@.globalEquipmentEdit(context:String, manufacturer:String, model:String, version:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/globalequipment/details/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>""", """@na.nadm.controllers.Detail@.getGlobalEquipmentDetails(context:String, manufacturer:String, model:String, version:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/globalequipment/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>""", """@na.nadm.controllers.Detail@.updateGlobalEquipment(context:String, manufacturer:String, model:String, version:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/globalequipment/template/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>/""" + "$" + """firmwareFilename<[^/]+>""", """@na.nadm.controllers.Detail@.updateGlobalEquipmentAndTemplate(context:String, manufacturer:String, model:String, version:String, firmwareFilename:String)"""),
    ("""DELETE""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/globalequipment/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>""", """@na.nadm.controllers.Detail@.removeGlobalEquipment(context:String, manufacturer:String, model:String, version:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/client""", """@na.nadm.controllers.Navigation@.client(context:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/client/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>/createtemplate""", """@na.nadm.controllers.Navigation@.clientEquipmentCreateTemplate(context:String, manufacturer:String, model:String, version:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/client/""" + "$" + """clientId<[^/]+>""", """@na.nadm.controllers.Navigation@.clientDetails(context:String, clientId:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/client/""" + "$" + """clientId<[^/]+>/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>""", """@na.nadm.controllers.Navigation@.clientEquipmentDetail(context:String, clientId:String, manufacturer:String, model:String, version:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/client/""" + "$" + """clientId<[^/]+>/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>/modal""", """@na.nadm.controllers.Navigation@.clientEquipmentDetailModal(context:String, clientId:String, manufacturer:String, model:String, version:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/client/""" + "$" + """clientId<[^/]+>/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>/editModal""", """@na.nadm.controllers.Navigation@.clientEquipmentEditModal(context:String, clientId:String, manufacturer:String, model:String, version:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/client/""" + "$" + """clientId<[^/]+>/createModal""", """@na.nadm.controllers.Navigation@.clientEquipmentCreateModal(context:String, clientId:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/client/""" + "$" + """clientId<[^/]+>/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>""", """@na.nadm.controllers.Detail@.updateClientEquipment(context:String, clientId:String, manufacturer:String, model:String, version:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/client/""" + "$" + """clientId<[^/]+>/create""", """@na.nadm.controllers.Detail@.createClient(context:String, clientId:String)"""),
    ("""DELETE""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/client/""" + "$" + """clientId<[^/]+>/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>""", """@na.nadm.controllers.Detail@.removeClientEquipment(context:String, clientId:String, manufacturer:String, model:String, version:String)"""),
    ("""DELETE""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/client/""" + "$" + """clientId<[^/]+>""", """@na.nadm.controllers.Detail@.removeClient(context:String, clientId:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/clientequipment/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>/""" + "$" + """clientId<[^/]+>""", """@na.nadm.controllers.Detail@.createClientEquipment(context:String, manufacturer:String, model:String, version:String, clientId:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/equipment/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>/""" + "$" + """clientId<[^/]+>/createtemplate""", """@na.nadm.controllers.Navigation@.equipmentCreateTemplate(context:String, manufacturer:String, model:String, version:String, clientId:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/equipment/create""", """@na.nadm.controllers.Navigation@.equipmentCreate(context:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/equipment""", """@na.nadm.controllers.Navigation@.equipment(context:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/equipment/""" + "$" + """equipmentId<[^/]+>/characteristics/""" + "$" + """mode<[^/]+>""", """@na.nadm.controllers.Navigation@.equipmentCharacteristics(context:String, equipmentId:String, mode:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/equipment/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>/""" + "$" + """clientId<[^/]+>/""" + "$" + """equipmentId<[^/]+>/""" + "$" + """tabContext<[^/]+>""", """@na.nadm.controllers.Navigation@.equipmentDetail(context:String, manufacturer:String, model:String, version:String, clientId:String, equipmentId:String, tabContext:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/equipment/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>/""" + "$" + """clientId<[^/]+>/""" + "$" + """equipmentId<[^/]+>/""" + "$" + """tabContext<[^/]+>/edit""", """@na.nadm.controllers.Navigation@.equipmentEdit(context:String, manufacturer:String, model:String, version:String, clientId:String, equipmentId:String, tabContext:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/equipment/create/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>/""" + "$" + """clientId<[^/]+>""", """@na.nadm.controllers.Detail@.createEquipment(context:String, manufacturer:String, model:String, version:String, clientId:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/equipment/characteristics/""" + "$" + """clientId<[^/]+>/""" + "$" + """equipmentId<[^/]+>""", """@na.nadm.controllers.Detail@.updateEquipment(context:String, clientId:String, equipmentId:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/equipment/association/""" + "$" + """equipmentId<[^/]+>""", """@na.nadm.controllers.Detail@.updateEquipmentAssociation(context:String, equipmentId:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/equipment/csv/""" + "$" + """paramsOnBase64<[^/]+>""", """@na.nadm.controllers.Export@.getEquipmentsCsv(context:String, paramsOnBase64:String)"""),
    ("""DELETE""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/equipment/""" + "$" + """equipmentId<[^/]+>""", """@na.nadm.controllers.Detail@.removeEquipment(context:String, equipmentId:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/equipment/delete""", """@na.nadm.controllers.Operation@.executeBulkDelete(context:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/service/""" + "$" + """serviceType<[^/]+>/createtemplate""", """@na.nadm.controllers.Navigation@.serviceCreateTemplate(context:String, serviceType:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/service""", """@na.nadm.controllers.Navigation@.service(context:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/service/create""", """@na.nadm.controllers.Navigation@.serviceCreate(context:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/service/create/""" + "$" + """serviceType<[^/]+>""", """@na.nadm.controllers.Detail@.createService(context:String, serviceType:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/service/""" + "$" + """clientName<[^/]+>/""" + "$" + """serviceName<[^/]+>/""" + "$" + """serviceType<[^/]+>""", """@na.nadm.controllers.Navigation@.serviceDetails(context:String, clientName:String, serviceName:String, serviceType:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/service/""" + "$" + """clientName<[^/]+>/""" + "$" + """serviceName<[^/]+>/""" + "$" + """serviceType<[^/]+>/edit""", """@na.nadm.controllers.Navigation@.serviceEdit(context:String, clientName:String, serviceName:String, serviceType:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/service/""" + "$" + """clientName<[^/]+>/values""", """@na.nadm.controllers.Navigation@.serviceValues(context:String, clientName:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/service/""" + "$" + """clientName<[^/]+>/""" + "$" + """serviceName<[^/]+>""", """@na.nadm.controllers.Detail@.updateService(context:String, clientName:String, serviceName:String)"""),
    ("""DELETE""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/service/""" + "$" + """clientName<[^/]+>/""" + "$" + """serviceName<[^/]+>""", """@na.nadm.controllers.Detail@.removeService(context:String, clientName:String, serviceName:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/searchglobalequipment""", """@na.nadm.controllers.Search@.searchGlobalEquipment(context:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/searchclient""", """@na.nadm.controllers.Search@.searchClient(context:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/searchequipment""", """@na.nadm.controllers.Search@.searchEquipment(context:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/searchequipmentassociations/""" + "$" + """equipmentId<[^/]+>""", """@na.nadm.controllers.Search@.searchEquipmentAssociations(context:String, equipmentId:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/searchservice""", """@na.nadm.controllers.Search@.searchService(context:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/client/""" + "$" + """clientId<[^/]+>/searchEquipmentModel""", """@na.nadm.controllers.Search@.searchClientEquipmentModel(context:String, clientId:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/clients/names""", """@na.nadm.controllers.Search@.clientNames(context:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/tags""", """@na.nadm.controllers.Tag@.tags(context:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/equipment-tags""", """@na.nadm.controllers.Tag@.equipmentTagSearch(context:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/equipment-tags/""" + "$" + """uuid<[^/]+>""", """@na.nadm.controllers.Tag@.equipmentTagSearchAssociatedTagsContinuation(context:String, uuid:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/equipment-tags/manage""", """@na.nadm.controllers.Tag@.manageEquipmentsTags(context:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/table-configs/equipment""", """@na.nadm.controllers.TableConfigs@.equipment(context:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/table-configs/globalequipment""", """@na.nadm.controllers.TableConfigs@.globalEquipment(context:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/table-configs/client""", """@na.nadm.controllers.TableConfigs@.client(context:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/table-configs/clientEquipmentModel""", """@na.nadm.controllers.TableConfigs@.clientEquipmentModel(context:String, clientId:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/table-configs/service""", """@na.nadm.controllers.TableConfigs@.service(context:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/table-configs/equipmentassociations""", """@na.nadm.controllers.TableConfigs@.equipmentassociations(context:String, equipmentId:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/table-configs/equipmentassociationtypeservices""", """@na.nadm.controllers.TableConfigs@.equipmentAssociationsTypeServices(context:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/catalog/manufacturers""", """@na.nadm.controllers.Search@.catalogManufacturers(context:String, multipleChoices:Boolean)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/catalog/models""", """@na.nadm.controllers.Search@.catalogModels(context:String, multipleChoices:Boolean)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/catalog/versions""", """@na.nadm.controllers.Search@.catalogVersions(context:String, multipleChoices:Boolean)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/operation/mappingmodel""", """@na.nadm.controllers.Operation@.getMappingModel(context:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/operation/operations/equipment""", """@na.nadm.controllers.Operation@.getEquipmentOperations(context:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/operation/operations/globalequipment""", """@na.nadm.controllers.Operation@.getGlobalEquipmentOperations(context:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/operation/""" + "$" + """operationId<[^/]+>/form/individual""", """@na.nadm.controllers.Operation@.getOperationForm(context:String, operationId:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/operation/""" + "$" + """operationId<[^/]+>/form/massive""", """@na.nadm.controllers.Operation@.getMassiveOperationForm(context:String, operationId:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/upload/firmwarefile""", """@na.nadm.controllers.Operation@.uploadFirmwareFile(context:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/delete/firmwarefile/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>""", """@na.nadm.controllers.Operation@.deleteFirmwareFile(context:String, manufacturer:String, model:String, version:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/operation""", """@na.nadm.controllers.Operation@.executeOperation(context:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/bulkoperation""", """@na.nadm.controllers.Operation@.executeBulkOperation(context:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/filters/""" + "$" + """filterContext<[^/]+>""", """@na.nadm.controllers.Filters@.saveFilter(context:String, filterContext:String)"""),
    ("""PUT""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/filters/""" + "$" + """filterContext<[^/]+>/""" + "$" + """filterName<[^/]+>""", """@na.nadm.controllers.Filters@.editFilter(context:String, filterContext:String, filterName:String)"""),
    ("""DELETE""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/filters/""" + "$" + """filterContext<[^/]+>/""" + "$" + """filterName<[^/]+>""", """@na.nadm.controllers.Filters@.deleteFilter(context:String, filterContext:String, filterName:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/filters/""" + "$" + """filterContext<[^/]+>""", """@na.nadm.controllers.Filters@.getAllFilters(context:String, filterContext:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """""" + "$" + """context<[^/]+>/nadm/filters/""" + "$" + """filterContext<[^/]+>/""" + "$" + """filterName<[^/]+>""", """@na.nadm.controllers.Filters@.getSelectedFilter(context:String, filterContext:String, filterName:String)"""),
    prefixed_naportalbase_Routes_0_81.router.documentation,
    Nil
  ).foldLeft(List.empty[(String,String,String)]) { (s,e) => e.asInstanceOf[Any] match {
    case r @ (_,_,_) => s :+ r.asInstanceOf[(String,String,String)]
    case l => s ++ l.asInstanceOf[List[(String,String,String)]]
  }}


  // @LINE:2
  private[this] lazy val na_nadm_controllers_Application_index0_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/home")))
  )
  private[this] lazy val na_nadm_controllers_Application_index0_invoker = createInvoker(
    Application_7.get.index(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Application",
      "index",
      Seq(classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/home""",
      """ Home page""",
      Seq()
    )
  )

  // @LINE:5
  private[this] lazy val na_nadm_controllers_Application_resume1_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/resume")))
  )
  private[this] lazy val na_nadm_controllers_Application_resume1_invoker = createInvoker(
    Application_7.get.resume(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Application",
      "resume",
      Seq(classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/resume""",
      """Resume""",
      Seq()
    )
  )

  // @LINE:6
  private[this] lazy val na_nadm_controllers_Application_jsSettings2_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/jsSettings")))
  )
  private[this] lazy val na_nadm_controllers_Application_jsSettings2_invoker = createInvoker(
    Application_7.get.jsSettings(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Application",
      "jsSettings",
      Seq(classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/jsSettings""",
      """""",
      Seq()
    )
  )

  // @LINE:10
  private[this] lazy val na_nadm_controllers_Application_javascriptRoutes3_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("nadm/assets/javascripts/routes")))
  )
  private[this] lazy val na_nadm_controllers_Application_javascriptRoutes3_invoker = createInvoker(
    Application_7.get.javascriptRoutes(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Application",
      "javascriptRoutes",
      Nil,
      "GET",
      this.prefix + """nadm/assets/javascripts/routes""",
      """Javascript Routing""",
      Seq()
    )
  )

  // @LINE:13
  private[this] lazy val na_nadm_controllers_Application_getModuleAngularScripts4_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("nadm/imports/jsscripts")))
  )
  private[this] lazy val na_nadm_controllers_Application_getModuleAngularScripts4_invoker = createInvoker(
    Application_7.get.getModuleAngularScripts(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Application",
      "getModuleAngularScripts",
      Nil,
      "GET",
      this.prefix + """nadm/imports/jsscripts""",
      """Angular Scripts""",
      Seq()
    )
  )

  // @LINE:16
  private[this] lazy val na_nadm_controllers_Assets_versioned5_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("nadm/assets/"), DynamicPart("file", """.+""",false)))
  )
  private[this] lazy val na_nadm_controllers_Assets_versioned5_invoker = createInvoker(
    Assets_6.get.versioned(fakeValue[String], fakeValue[Asset]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Assets",
      "versioned",
      Seq(classOf[String], classOf[Asset]),
      "GET",
      this.prefix + """nadm/assets/""" + "$" + """file<.+>""",
      """ Map static resources from the /public folder to the /assets URL path""",
      Seq()
    )
  )

  // @LINE:20
  private[this] lazy val na_nadm_controllers_Navigation_globalEquipment6_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/globalequipment")))
  )
  private[this] lazy val na_nadm_controllers_Navigation_globalEquipment6_invoker = createInvoker(
    Navigation_5.get.globalEquipment(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Navigation",
      "globalEquipment",
      Seq(classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/globalequipment""",
      """########## Navigation ###########
Global Equipments""",
      Seq()
    )
  )

  // @LINE:21
  private[this] lazy val na_nadm_controllers_Navigation_globalEquipmentDetails7_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/globalequipment/"), DynamicPart("manufacturer", """[^/]+""",true), StaticPart("/"), DynamicPart("model", """[^/]+""",true), StaticPart("/"), DynamicPart("version", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Navigation_globalEquipmentDetails7_invoker = createInvoker(
    Navigation_5.get.globalEquipmentDetails(fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Navigation",
      "globalEquipmentDetails",
      Seq(classOf[String], classOf[String], classOf[String], classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/globalequipment/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:22
  private[this] lazy val na_nadm_controllers_Navigation_globalEquipmentEdit8_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/globalequipment/"), DynamicPart("manufacturer", """[^/]+""",true), StaticPart("/"), DynamicPart("model", """[^/]+""",true), StaticPart("/"), DynamicPart("version", """[^/]+""",true), StaticPart("/edit")))
  )
  private[this] lazy val na_nadm_controllers_Navigation_globalEquipmentEdit8_invoker = createInvoker(
    Navigation_5.get.globalEquipmentEdit(fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Navigation",
      "globalEquipmentEdit",
      Seq(classOf[String], classOf[String], classOf[String], classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/globalequipment/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>/edit""",
      """""",
      Seq()
    )
  )

  // @LINE:23
  private[this] lazy val na_nadm_controllers_Detail_getGlobalEquipmentDetails9_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/globalequipment/details/"), DynamicPart("manufacturer", """[^/]+""",true), StaticPart("/"), DynamicPart("model", """[^/]+""",true), StaticPart("/"), DynamicPart("version", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Detail_getGlobalEquipmentDetails9_invoker = createInvoker(
    Detail_2.get.getGlobalEquipmentDetails(fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Detail",
      "getGlobalEquipmentDetails",
      Seq(classOf[String], classOf[String], classOf[String], classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/globalequipment/details/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:24
  private[this] lazy val na_nadm_controllers_Detail_updateGlobalEquipment10_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/globalequipment/"), DynamicPart("manufacturer", """[^/]+""",true), StaticPart("/"), DynamicPart("model", """[^/]+""",true), StaticPart("/"), DynamicPart("version", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Detail_updateGlobalEquipment10_invoker = createInvoker(
    Detail_2.get.updateGlobalEquipment(fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Detail",
      "updateGlobalEquipment",
      Seq(classOf[String], classOf[String], classOf[String], classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/globalequipment/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:25
  private[this] lazy val na_nadm_controllers_Detail_updateGlobalEquipmentAndTemplate11_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/globalequipment/template/"), DynamicPart("manufacturer", """[^/]+""",true), StaticPart("/"), DynamicPart("model", """[^/]+""",true), StaticPart("/"), DynamicPart("version", """[^/]+""",true), StaticPart("/"), DynamicPart("firmwareFilename", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Detail_updateGlobalEquipmentAndTemplate11_invoker = createInvoker(
    Detail_2.get.updateGlobalEquipmentAndTemplate(fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Detail",
      "updateGlobalEquipmentAndTemplate",
      Seq(classOf[String], classOf[String], classOf[String], classOf[String], classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/globalequipment/template/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>/""" + "$" + """firmwareFilename<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:26
  private[this] lazy val na_nadm_controllers_Detail_removeGlobalEquipment12_route = Route("DELETE",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/globalequipment/"), DynamicPart("manufacturer", """[^/]+""",true), StaticPart("/"), DynamicPart("model", """[^/]+""",true), StaticPart("/"), DynamicPart("version", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Detail_removeGlobalEquipment12_invoker = createInvoker(
    Detail_2.get.removeGlobalEquipment(fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Detail",
      "removeGlobalEquipment",
      Seq(classOf[String], classOf[String], classOf[String], classOf[String]),
      "DELETE",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/globalequipment/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:29
  private[this] lazy val na_nadm_controllers_Navigation_client13_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/client")))
  )
  private[this] lazy val na_nadm_controllers_Navigation_client13_invoker = createInvoker(
    Navigation_5.get.client(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Navigation",
      "client",
      Seq(classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/client""",
      """Client""",
      Seq()
    )
  )

  // @LINE:30
  private[this] lazy val na_nadm_controllers_Navigation_clientEquipmentCreateTemplate14_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/client/"), DynamicPart("manufacturer", """[^/]+""",true), StaticPart("/"), DynamicPart("model", """[^/]+""",true), StaticPart("/"), DynamicPart("version", """[^/]+""",true), StaticPart("/createtemplate")))
  )
  private[this] lazy val na_nadm_controllers_Navigation_clientEquipmentCreateTemplate14_invoker = createInvoker(
    Navigation_5.get.clientEquipmentCreateTemplate(fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Navigation",
      "clientEquipmentCreateTemplate",
      Seq(classOf[String], classOf[String], classOf[String], classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/client/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>/createtemplate""",
      """""",
      Seq()
    )
  )

  // @LINE:31
  private[this] lazy val na_nadm_controllers_Navigation_clientDetails15_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/client/"), DynamicPart("clientId", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Navigation_clientDetails15_invoker = createInvoker(
    Navigation_5.get.clientDetails(fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Navigation",
      "clientDetails",
      Seq(classOf[String], classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/client/""" + "$" + """clientId<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:32
  private[this] lazy val na_nadm_controllers_Navigation_clientEquipmentDetail16_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/client/"), DynamicPart("clientId", """[^/]+""",true), StaticPart("/"), DynamicPart("manufacturer", """[^/]+""",true), StaticPart("/"), DynamicPart("model", """[^/]+""",true), StaticPart("/"), DynamicPart("version", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Navigation_clientEquipmentDetail16_invoker = createInvoker(
    Navigation_5.get.clientEquipmentDetail(fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Navigation",
      "clientEquipmentDetail",
      Seq(classOf[String], classOf[String], classOf[String], classOf[String], classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/client/""" + "$" + """clientId<[^/]+>/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:33
  private[this] lazy val na_nadm_controllers_Navigation_clientEquipmentDetailModal17_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/client/"), DynamicPart("clientId", """[^/]+""",true), StaticPart("/"), DynamicPart("manufacturer", """[^/]+""",true), StaticPart("/"), DynamicPart("model", """[^/]+""",true), StaticPart("/"), DynamicPart("version", """[^/]+""",true), StaticPart("/modal")))
  )
  private[this] lazy val na_nadm_controllers_Navigation_clientEquipmentDetailModal17_invoker = createInvoker(
    Navigation_5.get.clientEquipmentDetailModal(fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Navigation",
      "clientEquipmentDetailModal",
      Seq(classOf[String], classOf[String], classOf[String], classOf[String], classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/client/""" + "$" + """clientId<[^/]+>/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>/modal""",
      """""",
      Seq()
    )
  )

  // @LINE:34
  private[this] lazy val na_nadm_controllers_Navigation_clientEquipmentEditModal18_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/client/"), DynamicPart("clientId", """[^/]+""",true), StaticPart("/"), DynamicPart("manufacturer", """[^/]+""",true), StaticPart("/"), DynamicPart("model", """[^/]+""",true), StaticPart("/"), DynamicPart("version", """[^/]+""",true), StaticPart("/editModal")))
  )
  private[this] lazy val na_nadm_controllers_Navigation_clientEquipmentEditModal18_invoker = createInvoker(
    Navigation_5.get.clientEquipmentEditModal(fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Navigation",
      "clientEquipmentEditModal",
      Seq(classOf[String], classOf[String], classOf[String], classOf[String], classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/client/""" + "$" + """clientId<[^/]+>/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>/editModal""",
      """""",
      Seq()
    )
  )

  // @LINE:35
  private[this] lazy val na_nadm_controllers_Navigation_clientEquipmentCreateModal19_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/client/"), DynamicPart("clientId", """[^/]+""",true), StaticPart("/createModal")))
  )
  private[this] lazy val na_nadm_controllers_Navigation_clientEquipmentCreateModal19_invoker = createInvoker(
    Navigation_5.get.clientEquipmentCreateModal(fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Navigation",
      "clientEquipmentCreateModal",
      Seq(classOf[String], classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/client/""" + "$" + """clientId<[^/]+>/createModal""",
      """""",
      Seq()
    )
  )

  // @LINE:36
  private[this] lazy val na_nadm_controllers_Detail_updateClientEquipment20_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/client/"), DynamicPart("clientId", """[^/]+""",true), StaticPart("/"), DynamicPart("manufacturer", """[^/]+""",true), StaticPart("/"), DynamicPart("model", """[^/]+""",true), StaticPart("/"), DynamicPart("version", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Detail_updateClientEquipment20_invoker = createInvoker(
    Detail_2.get.updateClientEquipment(fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Detail",
      "updateClientEquipment",
      Seq(classOf[String], classOf[String], classOf[String], classOf[String], classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/client/""" + "$" + """clientId<[^/]+>/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:37
  private[this] lazy val na_nadm_controllers_Detail_createClient21_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/client/"), DynamicPart("clientId", """[^/]+""",true), StaticPart("/create")))
  )
  private[this] lazy val na_nadm_controllers_Detail_createClient21_invoker = createInvoker(
    Detail_2.get.createClient(fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Detail",
      "createClient",
      Seq(classOf[String], classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/client/""" + "$" + """clientId<[^/]+>/create""",
      """""",
      Seq()
    )
  )

  // @LINE:39
  private[this] lazy val na_nadm_controllers_Detail_removeClientEquipment22_route = Route("DELETE",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/client/"), DynamicPart("clientId", """[^/]+""",true), StaticPart("/"), DynamicPart("manufacturer", """[^/]+""",true), StaticPart("/"), DynamicPart("model", """[^/]+""",true), StaticPart("/"), DynamicPart("version", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Detail_removeClientEquipment22_invoker = createInvoker(
    Detail_2.get.removeClientEquipment(fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Detail",
      "removeClientEquipment",
      Seq(classOf[String], classOf[String], classOf[String], classOf[String], classOf[String]),
      "DELETE",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/client/""" + "$" + """clientId<[^/]+>/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:40
  private[this] lazy val na_nadm_controllers_Detail_removeClient23_route = Route("DELETE",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/client/"), DynamicPart("clientId", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Detail_removeClient23_invoker = createInvoker(
    Detail_2.get.removeClient(fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Detail",
      "removeClient",
      Seq(classOf[String], classOf[String]),
      "DELETE",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/client/""" + "$" + """clientId<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:43
  private[this] lazy val na_nadm_controllers_Detail_createClientEquipment24_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/clientequipment/"), DynamicPart("manufacturer", """[^/]+""",true), StaticPart("/"), DynamicPart("model", """[^/]+""",true), StaticPart("/"), DynamicPart("version", """[^/]+""",true), StaticPart("/"), DynamicPart("clientId", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Detail_createClientEquipment24_invoker = createInvoker(
    Detail_2.get.createClientEquipment(fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Detail",
      "createClientEquipment",
      Seq(classOf[String], classOf[String], classOf[String], classOf[String], classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/clientequipment/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>/""" + "$" + """clientId<[^/]+>""",
      """Client equipment""",
      Seq()
    )
  )

  // @LINE:47
  private[this] lazy val na_nadm_controllers_Navigation_equipmentCreateTemplate25_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/equipment/"), DynamicPart("manufacturer", """[^/]+""",true), StaticPart("/"), DynamicPart("model", """[^/]+""",true), StaticPart("/"), DynamicPart("version", """[^/]+""",true), StaticPart("/"), DynamicPart("clientId", """[^/]+""",true), StaticPart("/createtemplate")))
  )
  private[this] lazy val na_nadm_controllers_Navigation_equipmentCreateTemplate25_invoker = createInvoker(
    Navigation_5.get.equipmentCreateTemplate(fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Navigation",
      "equipmentCreateTemplate",
      Seq(classOf[String], classOf[String], classOf[String], classOf[String], classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/equipment/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>/""" + "$" + """clientId<[^/]+>/createtemplate""",
      """Equipment""",
      Seq()
    )
  )

  // @LINE:48
  private[this] lazy val na_nadm_controllers_Navigation_equipmentCreate26_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/equipment/create")))
  )
  private[this] lazy val na_nadm_controllers_Navigation_equipmentCreate26_invoker = createInvoker(
    Navigation_5.get.equipmentCreate(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Navigation",
      "equipmentCreate",
      Seq(classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/equipment/create""",
      """""",
      Seq()
    )
  )

  // @LINE:49
  private[this] lazy val na_nadm_controllers_Navigation_equipment27_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/equipment")))
  )
  private[this] lazy val na_nadm_controllers_Navigation_equipment27_invoker = createInvoker(
    Navigation_5.get.equipment(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Navigation",
      "equipment",
      Seq(classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/equipment""",
      """""",
      Seq()
    )
  )

  // @LINE:50
  private[this] lazy val na_nadm_controllers_Navigation_equipmentCharacteristics28_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/equipment/"), DynamicPart("equipmentId", """[^/]+""",true), StaticPart("/characteristics/"), DynamicPart("mode", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Navigation_equipmentCharacteristics28_invoker = createInvoker(
    Navigation_5.get.equipmentCharacteristics(fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Navigation",
      "equipmentCharacteristics",
      Seq(classOf[String], classOf[String], classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/equipment/""" + "$" + """equipmentId<[^/]+>/characteristics/""" + "$" + """mode<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:51
  private[this] lazy val na_nadm_controllers_Navigation_equipmentDetail29_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/equipment/"), DynamicPart("manufacturer", """[^/]+""",true), StaticPart("/"), DynamicPart("model", """[^/]+""",true), StaticPart("/"), DynamicPart("version", """[^/]+""",true), StaticPart("/"), DynamicPart("clientId", """[^/]+""",true), StaticPart("/"), DynamicPart("equipmentId", """[^/]+""",true), StaticPart("/"), DynamicPart("tabContext", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Navigation_equipmentDetail29_invoker = createInvoker(
    Navigation_5.get.equipmentDetail(fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Navigation",
      "equipmentDetail",
      Seq(classOf[String], classOf[String], classOf[String], classOf[String], classOf[String], classOf[String], classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/equipment/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>/""" + "$" + """clientId<[^/]+>/""" + "$" + """equipmentId<[^/]+>/""" + "$" + """tabContext<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:52
  private[this] lazy val na_nadm_controllers_Navigation_equipmentEdit30_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/equipment/"), DynamicPart("manufacturer", """[^/]+""",true), StaticPart("/"), DynamicPart("model", """[^/]+""",true), StaticPart("/"), DynamicPart("version", """[^/]+""",true), StaticPart("/"), DynamicPart("clientId", """[^/]+""",true), StaticPart("/"), DynamicPart("equipmentId", """[^/]+""",true), StaticPart("/"), DynamicPart("tabContext", """[^/]+""",true), StaticPart("/edit")))
  )
  private[this] lazy val na_nadm_controllers_Navigation_equipmentEdit30_invoker = createInvoker(
    Navigation_5.get.equipmentEdit(fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Navigation",
      "equipmentEdit",
      Seq(classOf[String], classOf[String], classOf[String], classOf[String], classOf[String], classOf[String], classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/equipment/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>/""" + "$" + """clientId<[^/]+>/""" + "$" + """equipmentId<[^/]+>/""" + "$" + """tabContext<[^/]+>/edit""",
      """""",
      Seq()
    )
  )

  // @LINE:54
  private[this] lazy val na_nadm_controllers_Detail_createEquipment31_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/equipment/create/"), DynamicPart("manufacturer", """[^/]+""",true), StaticPart("/"), DynamicPart("model", """[^/]+""",true), StaticPart("/"), DynamicPart("version", """[^/]+""",true), StaticPart("/"), DynamicPart("clientId", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Detail_createEquipment31_invoker = createInvoker(
    Detail_2.get.createEquipment(fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Detail",
      "createEquipment",
      Seq(classOf[String], classOf[String], classOf[String], classOf[String], classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/equipment/create/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>/""" + "$" + """clientId<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:55
  private[this] lazy val na_nadm_controllers_Detail_updateEquipment32_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/equipment/characteristics/"), DynamicPart("clientId", """[^/]+""",true), StaticPart("/"), DynamicPart("equipmentId", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Detail_updateEquipment32_invoker = createInvoker(
    Detail_2.get.updateEquipment(fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Detail",
      "updateEquipment",
      Seq(classOf[String], classOf[String], classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/equipment/characteristics/""" + "$" + """clientId<[^/]+>/""" + "$" + """equipmentId<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:56
  private[this] lazy val na_nadm_controllers_Detail_updateEquipmentAssociation33_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/equipment/association/"), DynamicPart("equipmentId", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Detail_updateEquipmentAssociation33_invoker = createInvoker(
    Detail_2.get.updateEquipmentAssociation(fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Detail",
      "updateEquipmentAssociation",
      Seq(classOf[String], classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/equipment/association/""" + "$" + """equipmentId<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:59
  private[this] lazy val na_nadm_controllers_Export_getEquipmentsCsv34_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/equipment/csv/"), DynamicPart("paramsOnBase64", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Export_getEquipmentsCsv34_invoker = createInvoker(
    Export_3.get.getEquipmentsCsv(fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Export",
      "getEquipmentsCsv",
      Seq(classOf[String], classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/equipment/csv/""" + "$" + """paramsOnBase64<[^/]+>""",
      """ Export equipments""",
      Seq()
    )
  )

  // @LINE:62
  private[this] lazy val na_nadm_controllers_Detail_removeEquipment35_route = Route("DELETE",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/equipment/"), DynamicPart("equipmentId", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Detail_removeEquipment35_invoker = createInvoker(
    Detail_2.get.removeEquipment(fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Detail",
      "removeEquipment",
      Seq(classOf[String], classOf[String]),
      "DELETE",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/equipment/""" + "$" + """equipmentId<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:63
  private[this] lazy val na_nadm_controllers_Operation_executeBulkDelete36_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/equipment/delete")))
  )
  private[this] lazy val na_nadm_controllers_Operation_executeBulkDelete36_invoker = createInvoker(
    Operation_1.get.executeBulkDelete(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Operation",
      "executeBulkDelete",
      Seq(classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/equipment/delete""",
      """""",
      Seq()
    )
  )

  // @LINE:66
  private[this] lazy val na_nadm_controllers_Navigation_serviceCreateTemplate37_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/service/"), DynamicPart("serviceType", """[^/]+""",true), StaticPart("/createtemplate")))
  )
  private[this] lazy val na_nadm_controllers_Navigation_serviceCreateTemplate37_invoker = createInvoker(
    Navigation_5.get.serviceCreateTemplate(fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Navigation",
      "serviceCreateTemplate",
      Seq(classOf[String], classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/service/""" + "$" + """serviceType<[^/]+>/createtemplate""",
      """Service""",
      Seq()
    )
  )

  // @LINE:67
  private[this] lazy val na_nadm_controllers_Navigation_service38_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/service")))
  )
  private[this] lazy val na_nadm_controllers_Navigation_service38_invoker = createInvoker(
    Navigation_5.get.service(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Navigation",
      "service",
      Seq(classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/service""",
      """""",
      Seq()
    )
  )

  // @LINE:68
  private[this] lazy val na_nadm_controllers_Navigation_serviceCreate39_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/service/create")))
  )
  private[this] lazy val na_nadm_controllers_Navigation_serviceCreate39_invoker = createInvoker(
    Navigation_5.get.serviceCreate(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Navigation",
      "serviceCreate",
      Seq(classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/service/create""",
      """""",
      Seq()
    )
  )

  // @LINE:69
  private[this] lazy val na_nadm_controllers_Detail_createService40_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/service/create/"), DynamicPart("serviceType", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Detail_createService40_invoker = createInvoker(
    Detail_2.get.createService(fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Detail",
      "createService",
      Seq(classOf[String], classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/service/create/""" + "$" + """serviceType<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:70
  private[this] lazy val na_nadm_controllers_Navigation_serviceDetails41_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/service/"), DynamicPart("clientName", """[^/]+""",true), StaticPart("/"), DynamicPart("serviceName", """[^/]+""",true), StaticPart("/"), DynamicPart("serviceType", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Navigation_serviceDetails41_invoker = createInvoker(
    Navigation_5.get.serviceDetails(fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Navigation",
      "serviceDetails",
      Seq(classOf[String], classOf[String], classOf[String], classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/service/""" + "$" + """clientName<[^/]+>/""" + "$" + """serviceName<[^/]+>/""" + "$" + """serviceType<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:71
  private[this] lazy val na_nadm_controllers_Navigation_serviceEdit42_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/service/"), DynamicPart("clientName", """[^/]+""",true), StaticPart("/"), DynamicPart("serviceName", """[^/]+""",true), StaticPart("/"), DynamicPart("serviceType", """[^/]+""",true), StaticPart("/edit")))
  )
  private[this] lazy val na_nadm_controllers_Navigation_serviceEdit42_invoker = createInvoker(
    Navigation_5.get.serviceEdit(fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Navigation",
      "serviceEdit",
      Seq(classOf[String], classOf[String], classOf[String], classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/service/""" + "$" + """clientName<[^/]+>/""" + "$" + """serviceName<[^/]+>/""" + "$" + """serviceType<[^/]+>/edit""",
      """""",
      Seq()
    )
  )

  // @LINE:72
  private[this] lazy val na_nadm_controllers_Navigation_serviceValues43_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/service/"), DynamicPart("clientName", """[^/]+""",true), StaticPart("/values")))
  )
  private[this] lazy val na_nadm_controllers_Navigation_serviceValues43_invoker = createInvoker(
    Navigation_5.get.serviceValues(fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Navigation",
      "serviceValues",
      Seq(classOf[String], classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/service/""" + "$" + """clientName<[^/]+>/values""",
      """""",
      Seq()
    )
  )

  // @LINE:73
  private[this] lazy val na_nadm_controllers_Detail_updateService44_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/service/"), DynamicPart("clientName", """[^/]+""",true), StaticPart("/"), DynamicPart("serviceName", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Detail_updateService44_invoker = createInvoker(
    Detail_2.get.updateService(fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Detail",
      "updateService",
      Seq(classOf[String], classOf[String], classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/service/""" + "$" + """clientName<[^/]+>/""" + "$" + """serviceName<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:74
  private[this] lazy val na_nadm_controllers_Detail_removeService45_route = Route("DELETE",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/service/"), DynamicPart("clientName", """[^/]+""",true), StaticPart("/"), DynamicPart("serviceName", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Detail_removeService45_invoker = createInvoker(
    Detail_2.get.removeService(fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Detail",
      "removeService",
      Seq(classOf[String], classOf[String], classOf[String]),
      "DELETE",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/service/""" + "$" + """clientName<[^/]+>/""" + "$" + """serviceName<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:77
  private[this] lazy val na_nadm_controllers_Search_searchGlobalEquipment46_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/searchglobalequipment")))
  )
  private[this] lazy val na_nadm_controllers_Search_searchGlobalEquipment46_invoker = createInvoker(
    Search_0.get.searchGlobalEquipment(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Search",
      "searchGlobalEquipment",
      Seq(classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/searchglobalequipment""",
      """ Search""",
      Seq()
    )
  )

  // @LINE:78
  private[this] lazy val na_nadm_controllers_Search_searchClient47_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/searchclient")))
  )
  private[this] lazy val na_nadm_controllers_Search_searchClient47_invoker = createInvoker(
    Search_0.get.searchClient(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Search",
      "searchClient",
      Seq(classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/searchclient""",
      """""",
      Seq()
    )
  )

  // @LINE:79
  private[this] lazy val na_nadm_controllers_Search_searchEquipment48_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/searchequipment")))
  )
  private[this] lazy val na_nadm_controllers_Search_searchEquipment48_invoker = createInvoker(
    Search_0.get.searchEquipment(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Search",
      "searchEquipment",
      Seq(classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/searchequipment""",
      """""",
      Seq()
    )
  )

  // @LINE:80
  private[this] lazy val na_nadm_controllers_Search_searchEquipmentAssociations49_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/searchequipmentassociations/"), DynamicPart("equipmentId", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Search_searchEquipmentAssociations49_invoker = createInvoker(
    Search_0.get.searchEquipmentAssociations(fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Search",
      "searchEquipmentAssociations",
      Seq(classOf[String], classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/searchequipmentassociations/""" + "$" + """equipmentId<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:81
  private[this] lazy val na_nadm_controllers_Search_searchService50_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/searchservice")))
  )
  private[this] lazy val na_nadm_controllers_Search_searchService50_invoker = createInvoker(
    Search_0.get.searchService(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Search",
      "searchService",
      Seq(classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/searchservice""",
      """""",
      Seq()
    )
  )

  // @LINE:82
  private[this] lazy val na_nadm_controllers_Search_searchClientEquipmentModel51_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/client/"), DynamicPart("clientId", """[^/]+""",true), StaticPart("/searchEquipmentModel")))
  )
  private[this] lazy val na_nadm_controllers_Search_searchClientEquipmentModel51_invoker = createInvoker(
    Search_0.get.searchClientEquipmentModel(fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Search",
      "searchClientEquipmentModel",
      Seq(classOf[String], classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/client/""" + "$" + """clientId<[^/]+>/searchEquipmentModel""",
      """""",
      Seq()
    )
  )

  // @LINE:83
  private[this] lazy val na_nadm_controllers_Search_clientNames52_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/clients/names")))
  )
  private[this] lazy val na_nadm_controllers_Search_clientNames52_invoker = createInvoker(
    Search_0.get.clientNames(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Search",
      "clientNames",
      Seq(classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/clients/names""",
      """""",
      Seq()
    )
  )

  // @LINE:84
  private[this] lazy val na_nadm_controllers_Tag_tags53_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/tags")))
  )
  private[this] lazy val na_nadm_controllers_Tag_tags53_invoker = createInvoker(
    Tag_9.get.tags(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Tag",
      "tags",
      Seq(classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/tags""",
      """""",
      Seq()
    )
  )

  // @LINE:85
  private[this] lazy val na_nadm_controllers_Tag_equipmentTagSearch54_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/equipment-tags")))
  )
  private[this] lazy val na_nadm_controllers_Tag_equipmentTagSearch54_invoker = createInvoker(
    Tag_9.get.equipmentTagSearch(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Tag",
      "equipmentTagSearch",
      Seq(classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/equipment-tags""",
      """""",
      Seq()
    )
  )

  // @LINE:86
  private[this] lazy val na_nadm_controllers_Tag_equipmentTagSearchAssociatedTagsContinuation55_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/equipment-tags/"), DynamicPart("uuid", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Tag_equipmentTagSearchAssociatedTagsContinuation55_invoker = createInvoker(
    Tag_9.get.equipmentTagSearchAssociatedTagsContinuation(fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Tag",
      "equipmentTagSearchAssociatedTagsContinuation",
      Seq(classOf[String], classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/equipment-tags/""" + "$" + """uuid<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:87
  private[this] lazy val na_nadm_controllers_Tag_manageEquipmentsTags56_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/equipment-tags/manage")))
  )
  private[this] lazy val na_nadm_controllers_Tag_manageEquipmentsTags56_invoker = createInvoker(
    Tag_9.get.manageEquipmentsTags(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Tag",
      "manageEquipmentsTags",
      Seq(classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/equipment-tags/manage""",
      """""",
      Seq()
    )
  )

  // @LINE:90
  private[this] lazy val na_nadm_controllers_TableConfigs_equipment57_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/table-configs/equipment")))
  )
  private[this] lazy val na_nadm_controllers_TableConfigs_equipment57_invoker = createInvoker(
    TableConfigs_8.get.equipment(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.TableConfigs",
      "equipment",
      Seq(classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/table-configs/equipment""",
      """ tables""",
      Seq()
    )
  )

  // @LINE:91
  private[this] lazy val na_nadm_controllers_TableConfigs_globalEquipment58_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/table-configs/globalequipment")))
  )
  private[this] lazy val na_nadm_controllers_TableConfigs_globalEquipment58_invoker = createInvoker(
    TableConfigs_8.get.globalEquipment(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.TableConfigs",
      "globalEquipment",
      Seq(classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/table-configs/globalequipment""",
      """""",
      Seq()
    )
  )

  // @LINE:92
  private[this] lazy val na_nadm_controllers_TableConfigs_client59_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/table-configs/client")))
  )
  private[this] lazy val na_nadm_controllers_TableConfigs_client59_invoker = createInvoker(
    TableConfigs_8.get.client(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.TableConfigs",
      "client",
      Seq(classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/table-configs/client""",
      """""",
      Seq()
    )
  )

  // @LINE:93
  private[this] lazy val na_nadm_controllers_TableConfigs_clientEquipmentModel60_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/table-configs/clientEquipmentModel")))
  )
  private[this] lazy val na_nadm_controllers_TableConfigs_clientEquipmentModel60_invoker = createInvoker(
    TableConfigs_8.get.clientEquipmentModel(fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.TableConfigs",
      "clientEquipmentModel",
      Seq(classOf[String], classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/table-configs/clientEquipmentModel""",
      """""",
      Seq()
    )
  )

  // @LINE:94
  private[this] lazy val na_nadm_controllers_TableConfigs_service61_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/table-configs/service")))
  )
  private[this] lazy val na_nadm_controllers_TableConfigs_service61_invoker = createInvoker(
    TableConfigs_8.get.service(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.TableConfigs",
      "service",
      Seq(classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/table-configs/service""",
      """""",
      Seq()
    )
  )

  // @LINE:95
  private[this] lazy val na_nadm_controllers_TableConfigs_equipmentassociations62_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/table-configs/equipmentassociations")))
  )
  private[this] lazy val na_nadm_controllers_TableConfigs_equipmentassociations62_invoker = createInvoker(
    TableConfigs_8.get.equipmentassociations(fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.TableConfigs",
      "equipmentassociations",
      Seq(classOf[String], classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/table-configs/equipmentassociations""",
      """""",
      Seq()
    )
  )

  // @LINE:96
  private[this] lazy val na_nadm_controllers_TableConfigs_equipmentAssociationsTypeServices63_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/table-configs/equipmentassociationtypeservices")))
  )
  private[this] lazy val na_nadm_controllers_TableConfigs_equipmentAssociationsTypeServices63_invoker = createInvoker(
    TableConfigs_8.get.equipmentAssociationsTypeServices(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.TableConfigs",
      "equipmentAssociationsTypeServices",
      Seq(classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/table-configs/equipmentassociationtypeservices""",
      """""",
      Seq()
    )
  )

  // @LINE:100
  private[this] lazy val na_nadm_controllers_Search_catalogManufacturers64_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/catalog/manufacturers")))
  )
  private[this] lazy val na_nadm_controllers_Search_catalogManufacturers64_invoker = createInvoker(
    Search_0.get.catalogManufacturers(fakeValue[String], fakeValue[Boolean]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Search",
      "catalogManufacturers",
      Seq(classOf[String], classOf[Boolean]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/catalog/manufacturers""",
      """ Catalog Search""",
      Seq()
    )
  )

  // @LINE:101
  private[this] lazy val na_nadm_controllers_Search_catalogModels65_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/catalog/models")))
  )
  private[this] lazy val na_nadm_controllers_Search_catalogModels65_invoker = createInvoker(
    Search_0.get.catalogModels(fakeValue[String], fakeValue[Boolean]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Search",
      "catalogModels",
      Seq(classOf[String], classOf[Boolean]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/catalog/models""",
      """""",
      Seq()
    )
  )

  // @LINE:102
  private[this] lazy val na_nadm_controllers_Search_catalogVersions66_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/catalog/versions")))
  )
  private[this] lazy val na_nadm_controllers_Search_catalogVersions66_invoker = createInvoker(
    Search_0.get.catalogVersions(fakeValue[String], fakeValue[Boolean]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Search",
      "catalogVersions",
      Seq(classOf[String], classOf[Boolean]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/catalog/versions""",
      """""",
      Seq()
    )
  )

  // @LINE:106
  private[this] lazy val na_nadm_controllers_Operation_getMappingModel67_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/operation/mappingmodel")))
  )
  private[this] lazy val na_nadm_controllers_Operation_getMappingModel67_invoker = createInvoker(
    Operation_1.get.getMappingModel(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Operation",
      "getMappingModel",
      Seq(classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/operation/mappingmodel""",
      """ Operations""",
      Seq()
    )
  )

  // @LINE:107
  private[this] lazy val na_nadm_controllers_Operation_getEquipmentOperations68_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/operation/operations/equipment")))
  )
  private[this] lazy val na_nadm_controllers_Operation_getEquipmentOperations68_invoker = createInvoker(
    Operation_1.get.getEquipmentOperations(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Operation",
      "getEquipmentOperations",
      Seq(classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/operation/operations/equipment""",
      """""",
      Seq()
    )
  )

  // @LINE:108
  private[this] lazy val na_nadm_controllers_Operation_getGlobalEquipmentOperations69_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/operation/operations/globalequipment")))
  )
  private[this] lazy val na_nadm_controllers_Operation_getGlobalEquipmentOperations69_invoker = createInvoker(
    Operation_1.get.getGlobalEquipmentOperations(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Operation",
      "getGlobalEquipmentOperations",
      Seq(classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/operation/operations/globalequipment""",
      """""",
      Seq()
    )
  )

  // @LINE:109
  private[this] lazy val na_nadm_controllers_Operation_getOperationForm70_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/operation/"), DynamicPart("operationId", """[^/]+""",true), StaticPart("/form/individual")))
  )
  private[this] lazy val na_nadm_controllers_Operation_getOperationForm70_invoker = createInvoker(
    Operation_1.get.getOperationForm(fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Operation",
      "getOperationForm",
      Seq(classOf[String], classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/operation/""" + "$" + """operationId<[^/]+>/form/individual""",
      """""",
      Seq()
    )
  )

  // @LINE:110
  private[this] lazy val na_nadm_controllers_Operation_getMassiveOperationForm71_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/operation/"), DynamicPart("operationId", """[^/]+""",true), StaticPart("/form/massive")))
  )
  private[this] lazy val na_nadm_controllers_Operation_getMassiveOperationForm71_invoker = createInvoker(
    Operation_1.get.getMassiveOperationForm(fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Operation",
      "getMassiveOperationForm",
      Seq(classOf[String], classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/operation/""" + "$" + """operationId<[^/]+>/form/massive""",
      """""",
      Seq()
    )
  )

  // @LINE:113
  private[this] lazy val na_nadm_controllers_Operation_uploadFirmwareFile72_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/upload/firmwarefile")))
  )
  private[this] lazy val na_nadm_controllers_Operation_uploadFirmwareFile72_invoker = createInvoker(
    Operation_1.get.uploadFirmwareFile(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Operation",
      "uploadFirmwareFile",
      Seq(classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/upload/firmwarefile""",
      """""",
      Seq()
    )
  )

  // @LINE:114
  private[this] lazy val na_nadm_controllers_Operation_deleteFirmwareFile73_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/delete/firmwarefile/"), DynamicPart("manufacturer", """[^/]+""",true), StaticPart("/"), DynamicPart("model", """[^/]+""",true), StaticPart("/"), DynamicPart("version", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Operation_deleteFirmwareFile73_invoker = createInvoker(
    Operation_1.get.deleteFirmwareFile(fakeValue[String], fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Operation",
      "deleteFirmwareFile",
      Seq(classOf[String], classOf[String], classOf[String], classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/delete/firmwarefile/""" + "$" + """manufacturer<[^/]+>/""" + "$" + """model<[^/]+>/""" + "$" + """version<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:115
  private[this] lazy val na_nadm_controllers_Operation_executeOperation74_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/operation")))
  )
  private[this] lazy val na_nadm_controllers_Operation_executeOperation74_invoker = createInvoker(
    Operation_1.get.executeOperation(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Operation",
      "executeOperation",
      Seq(classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/operation""",
      """""",
      Seq()
    )
  )

  // @LINE:116
  private[this] lazy val na_nadm_controllers_Operation_executeBulkOperation75_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/bulkoperation")))
  )
  private[this] lazy val na_nadm_controllers_Operation_executeBulkOperation75_invoker = createInvoker(
    Operation_1.get.executeBulkOperation(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Operation",
      "executeBulkOperation",
      Seq(classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/bulkoperation""",
      """""",
      Seq()
    )
  )

  // @LINE:119
  private[this] lazy val na_nadm_controllers_Filters_saveFilter76_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/filters/"), DynamicPart("filterContext", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Filters_saveFilter76_invoker = createInvoker(
    Filters_4.get.saveFilter(fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Filters",
      "saveFilter",
      Seq(classOf[String], classOf[String]),
      "POST",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/filters/""" + "$" + """filterContext<[^/]+>""",
      """ Search Filters""",
      Seq()
    )
  )

  // @LINE:120
  private[this] lazy val na_nadm_controllers_Filters_editFilter77_route = Route("PUT",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/filters/"), DynamicPart("filterContext", """[^/]+""",true), StaticPart("/"), DynamicPart("filterName", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Filters_editFilter77_invoker = createInvoker(
    Filters_4.get.editFilter(fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Filters",
      "editFilter",
      Seq(classOf[String], classOf[String], classOf[String]),
      "PUT",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/filters/""" + "$" + """filterContext<[^/]+>/""" + "$" + """filterName<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:121
  private[this] lazy val na_nadm_controllers_Filters_deleteFilter78_route = Route("DELETE",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/filters/"), DynamicPart("filterContext", """[^/]+""",true), StaticPart("/"), DynamicPart("filterName", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Filters_deleteFilter78_invoker = createInvoker(
    Filters_4.get.deleteFilter(fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Filters",
      "deleteFilter",
      Seq(classOf[String], classOf[String], classOf[String]),
      "DELETE",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/filters/""" + "$" + """filterContext<[^/]+>/""" + "$" + """filterName<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:122
  private[this] lazy val na_nadm_controllers_Filters_getAllFilters79_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/filters/"), DynamicPart("filterContext", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Filters_getAllFilters79_invoker = createInvoker(
    Filters_4.get.getAllFilters(fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Filters",
      "getAllFilters",
      Seq(classOf[String], classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/filters/""" + "$" + """filterContext<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:123
  private[this] lazy val na_nadm_controllers_Filters_getSelectedFilter80_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), DynamicPart("context", """[^/]+""",true), StaticPart("/nadm/filters/"), DynamicPart("filterContext", """[^/]+""",true), StaticPart("/"), DynamicPart("filterName", """[^/]+""",true)))
  )
  private[this] lazy val na_nadm_controllers_Filters_getSelectedFilter80_invoker = createInvoker(
    Filters_4.get.getSelectedFilter(fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "nadm",
      "na.nadm.controllers.Filters",
      "getSelectedFilter",
      Seq(classOf[String], classOf[String], classOf[String]),
      "GET",
      this.prefix + """""" + "$" + """context<[^/]+>/nadm/filters/""" + "$" + """filterContext<[^/]+>/""" + "$" + """filterName<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:129
  private[this] val prefixed_naportalbase_Routes_0_81 = Include(naportalbase_Routes_0.withPrefix(this.prefix + (if (this.prefix.endsWith("/")) "" else "/") + "nadm"))


  def routes: PartialFunction[RequestHeader, Handler] = {
  
    // @LINE:2
    case na_nadm_controllers_Application_index0_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_Application_index0_invoker.call(Application_7.get.index(context))
      }
  
    // @LINE:5
    case na_nadm_controllers_Application_resume1_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_Application_resume1_invoker.call(Application_7.get.resume(context))
      }
  
    // @LINE:6
    case na_nadm_controllers_Application_jsSettings2_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_Application_jsSettings2_invoker.call(Application_7.get.jsSettings(context))
      }
  
    // @LINE:10
    case na_nadm_controllers_Application_javascriptRoutes3_route(params@_) =>
      call { 
        na_nadm_controllers_Application_javascriptRoutes3_invoker.call(Application_7.get.javascriptRoutes())
      }
  
    // @LINE:13
    case na_nadm_controllers_Application_getModuleAngularScripts4_route(params@_) =>
      call { 
        na_nadm_controllers_Application_getModuleAngularScripts4_invoker.call(Application_7.get.getModuleAngularScripts())
      }
  
    // @LINE:16
    case na_nadm_controllers_Assets_versioned5_route(params@_) =>
      call(Param[String]("path", Right("/public")), params.fromPath[Asset]("file", None)) { (path, file) =>
        na_nadm_controllers_Assets_versioned5_invoker.call(Assets_6.get.versioned(path, file))
      }
  
    // @LINE:20
    case na_nadm_controllers_Navigation_globalEquipment6_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_Navigation_globalEquipment6_invoker.call(Navigation_5.get.globalEquipment(context))
      }
  
    // @LINE:21
    case na_nadm_controllers_Navigation_globalEquipmentDetails7_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("manufacturer", None), params.fromPath[String]("model", None), params.fromPath[String]("version", None)) { (context, manufacturer, model, version) =>
        na_nadm_controllers_Navigation_globalEquipmentDetails7_invoker.call(Navigation_5.get.globalEquipmentDetails(context, manufacturer, model, version))
      }
  
    // @LINE:22
    case na_nadm_controllers_Navigation_globalEquipmentEdit8_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("manufacturer", None), params.fromPath[String]("model", None), params.fromPath[String]("version", None)) { (context, manufacturer, model, version) =>
        na_nadm_controllers_Navigation_globalEquipmentEdit8_invoker.call(Navigation_5.get.globalEquipmentEdit(context, manufacturer, model, version))
      }
  
    // @LINE:23
    case na_nadm_controllers_Detail_getGlobalEquipmentDetails9_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("manufacturer", None), params.fromPath[String]("model", None), params.fromPath[String]("version", None)) { (context, manufacturer, model, version) =>
        na_nadm_controllers_Detail_getGlobalEquipmentDetails9_invoker.call(Detail_2.get.getGlobalEquipmentDetails(context, manufacturer, model, version))
      }
  
    // @LINE:24
    case na_nadm_controllers_Detail_updateGlobalEquipment10_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("manufacturer", None), params.fromPath[String]("model", None), params.fromPath[String]("version", None)) { (context, manufacturer, model, version) =>
        na_nadm_controllers_Detail_updateGlobalEquipment10_invoker.call(Detail_2.get.updateGlobalEquipment(context, manufacturer, model, version))
      }
  
    // @LINE:25
    case na_nadm_controllers_Detail_updateGlobalEquipmentAndTemplate11_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("manufacturer", None), params.fromPath[String]("model", None), params.fromPath[String]("version", None), params.fromPath[String]("firmwareFilename", None)) { (context, manufacturer, model, version, firmwareFilename) =>
        na_nadm_controllers_Detail_updateGlobalEquipmentAndTemplate11_invoker.call(Detail_2.get.updateGlobalEquipmentAndTemplate(context, manufacturer, model, version, firmwareFilename))
      }
  
    // @LINE:26
    case na_nadm_controllers_Detail_removeGlobalEquipment12_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("manufacturer", None), params.fromPath[String]("model", None), params.fromPath[String]("version", None)) { (context, manufacturer, model, version) =>
        na_nadm_controllers_Detail_removeGlobalEquipment12_invoker.call(Detail_2.get.removeGlobalEquipment(context, manufacturer, model, version))
      }
  
    // @LINE:29
    case na_nadm_controllers_Navigation_client13_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_Navigation_client13_invoker.call(Navigation_5.get.client(context))
      }
  
    // @LINE:30
    case na_nadm_controllers_Navigation_clientEquipmentCreateTemplate14_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("manufacturer", None), params.fromPath[String]("model", None), params.fromPath[String]("version", None)) { (context, manufacturer, model, version) =>
        na_nadm_controllers_Navigation_clientEquipmentCreateTemplate14_invoker.call(Navigation_5.get.clientEquipmentCreateTemplate(context, manufacturer, model, version))
      }
  
    // @LINE:31
    case na_nadm_controllers_Navigation_clientDetails15_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("clientId", None)) { (context, clientId) =>
        na_nadm_controllers_Navigation_clientDetails15_invoker.call(Navigation_5.get.clientDetails(context, clientId))
      }
  
    // @LINE:32
    case na_nadm_controllers_Navigation_clientEquipmentDetail16_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("clientId", None), params.fromPath[String]("manufacturer", None), params.fromPath[String]("model", None), params.fromPath[String]("version", None)) { (context, clientId, manufacturer, model, version) =>
        na_nadm_controllers_Navigation_clientEquipmentDetail16_invoker.call(Navigation_5.get.clientEquipmentDetail(context, clientId, manufacturer, model, version))
      }
  
    // @LINE:33
    case na_nadm_controllers_Navigation_clientEquipmentDetailModal17_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("clientId", None), params.fromPath[String]("manufacturer", None), params.fromPath[String]("model", None), params.fromPath[String]("version", None)) { (context, clientId, manufacturer, model, version) =>
        na_nadm_controllers_Navigation_clientEquipmentDetailModal17_invoker.call(Navigation_5.get.clientEquipmentDetailModal(context, clientId, manufacturer, model, version))
      }
  
    // @LINE:34
    case na_nadm_controllers_Navigation_clientEquipmentEditModal18_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("clientId", None), params.fromPath[String]("manufacturer", None), params.fromPath[String]("model", None), params.fromPath[String]("version", None)) { (context, clientId, manufacturer, model, version) =>
        na_nadm_controllers_Navigation_clientEquipmentEditModal18_invoker.call(Navigation_5.get.clientEquipmentEditModal(context, clientId, manufacturer, model, version))
      }
  
    // @LINE:35
    case na_nadm_controllers_Navigation_clientEquipmentCreateModal19_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("clientId", None)) { (context, clientId) =>
        na_nadm_controllers_Navigation_clientEquipmentCreateModal19_invoker.call(Navigation_5.get.clientEquipmentCreateModal(context, clientId))
      }
  
    // @LINE:36
    case na_nadm_controllers_Detail_updateClientEquipment20_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("clientId", None), params.fromPath[String]("manufacturer", None), params.fromPath[String]("model", None), params.fromPath[String]("version", None)) { (context, clientId, manufacturer, model, version) =>
        na_nadm_controllers_Detail_updateClientEquipment20_invoker.call(Detail_2.get.updateClientEquipment(context, clientId, manufacturer, model, version))
      }
  
    // @LINE:37
    case na_nadm_controllers_Detail_createClient21_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("clientId", None)) { (context, clientId) =>
        na_nadm_controllers_Detail_createClient21_invoker.call(Detail_2.get.createClient(context, clientId))
      }
  
    // @LINE:39
    case na_nadm_controllers_Detail_removeClientEquipment22_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("clientId", None), params.fromPath[String]("manufacturer", None), params.fromPath[String]("model", None), params.fromPath[String]("version", None)) { (context, clientId, manufacturer, model, version) =>
        na_nadm_controllers_Detail_removeClientEquipment22_invoker.call(Detail_2.get.removeClientEquipment(context, clientId, manufacturer, model, version))
      }
  
    // @LINE:40
    case na_nadm_controllers_Detail_removeClient23_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("clientId", None)) { (context, clientId) =>
        na_nadm_controllers_Detail_removeClient23_invoker.call(Detail_2.get.removeClient(context, clientId))
      }
  
    // @LINE:43
    case na_nadm_controllers_Detail_createClientEquipment24_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("manufacturer", None), params.fromPath[String]("model", None), params.fromPath[String]("version", None), params.fromPath[String]("clientId", None)) { (context, manufacturer, model, version, clientId) =>
        na_nadm_controllers_Detail_createClientEquipment24_invoker.call(Detail_2.get.createClientEquipment(context, manufacturer, model, version, clientId))
      }
  
    // @LINE:47
    case na_nadm_controllers_Navigation_equipmentCreateTemplate25_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("manufacturer", None), params.fromPath[String]("model", None), params.fromPath[String]("version", None), params.fromPath[String]("clientId", None)) { (context, manufacturer, model, version, clientId) =>
        na_nadm_controllers_Navigation_equipmentCreateTemplate25_invoker.call(Navigation_5.get.equipmentCreateTemplate(context, manufacturer, model, version, clientId))
      }
  
    // @LINE:48
    case na_nadm_controllers_Navigation_equipmentCreate26_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_Navigation_equipmentCreate26_invoker.call(Navigation_5.get.equipmentCreate(context))
      }
  
    // @LINE:49
    case na_nadm_controllers_Navigation_equipment27_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_Navigation_equipment27_invoker.call(Navigation_5.get.equipment(context))
      }
  
    // @LINE:50
    case na_nadm_controllers_Navigation_equipmentCharacteristics28_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("equipmentId", None), params.fromPath[String]("mode", None)) { (context, equipmentId, mode) =>
        na_nadm_controllers_Navigation_equipmentCharacteristics28_invoker.call(Navigation_5.get.equipmentCharacteristics(context, equipmentId, mode))
      }
  
    // @LINE:51
    case na_nadm_controllers_Navigation_equipmentDetail29_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("manufacturer", None), params.fromPath[String]("model", None), params.fromPath[String]("version", None), params.fromPath[String]("clientId", None), params.fromPath[String]("equipmentId", None), params.fromPath[String]("tabContext", None)) { (context, manufacturer, model, version, clientId, equipmentId, tabContext) =>
        na_nadm_controllers_Navigation_equipmentDetail29_invoker.call(Navigation_5.get.equipmentDetail(context, manufacturer, model, version, clientId, equipmentId, tabContext))
      }
  
    // @LINE:52
    case na_nadm_controllers_Navigation_equipmentEdit30_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("manufacturer", None), params.fromPath[String]("model", None), params.fromPath[String]("version", None), params.fromPath[String]("clientId", None), params.fromPath[String]("equipmentId", None), params.fromPath[String]("tabContext", None)) { (context, manufacturer, model, version, clientId, equipmentId, tabContext) =>
        na_nadm_controllers_Navigation_equipmentEdit30_invoker.call(Navigation_5.get.equipmentEdit(context, manufacturer, model, version, clientId, equipmentId, tabContext))
      }
  
    // @LINE:54
    case na_nadm_controllers_Detail_createEquipment31_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("manufacturer", None), params.fromPath[String]("model", None), params.fromPath[String]("version", None), params.fromPath[String]("clientId", None)) { (context, manufacturer, model, version, clientId) =>
        na_nadm_controllers_Detail_createEquipment31_invoker.call(Detail_2.get.createEquipment(context, manufacturer, model, version, clientId))
      }
  
    // @LINE:55
    case na_nadm_controllers_Detail_updateEquipment32_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("clientId", None), params.fromPath[String]("equipmentId", None)) { (context, clientId, equipmentId) =>
        na_nadm_controllers_Detail_updateEquipment32_invoker.call(Detail_2.get.updateEquipment(context, clientId, equipmentId))
      }
  
    // @LINE:56
    case na_nadm_controllers_Detail_updateEquipmentAssociation33_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("equipmentId", None)) { (context, equipmentId) =>
        na_nadm_controllers_Detail_updateEquipmentAssociation33_invoker.call(Detail_2.get.updateEquipmentAssociation(context, equipmentId))
      }
  
    // @LINE:59
    case na_nadm_controllers_Export_getEquipmentsCsv34_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("paramsOnBase64", None)) { (context, paramsOnBase64) =>
        na_nadm_controllers_Export_getEquipmentsCsv34_invoker.call(Export_3.get.getEquipmentsCsv(context, paramsOnBase64))
      }
  
    // @LINE:62
    case na_nadm_controllers_Detail_removeEquipment35_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("equipmentId", None)) { (context, equipmentId) =>
        na_nadm_controllers_Detail_removeEquipment35_invoker.call(Detail_2.get.removeEquipment(context, equipmentId))
      }
  
    // @LINE:63
    case na_nadm_controllers_Operation_executeBulkDelete36_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_Operation_executeBulkDelete36_invoker.call(Operation_1.get.executeBulkDelete(context))
      }
  
    // @LINE:66
    case na_nadm_controllers_Navigation_serviceCreateTemplate37_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("serviceType", None)) { (context, serviceType) =>
        na_nadm_controllers_Navigation_serviceCreateTemplate37_invoker.call(Navigation_5.get.serviceCreateTemplate(context, serviceType))
      }
  
    // @LINE:67
    case na_nadm_controllers_Navigation_service38_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_Navigation_service38_invoker.call(Navigation_5.get.service(context))
      }
  
    // @LINE:68
    case na_nadm_controllers_Navigation_serviceCreate39_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_Navigation_serviceCreate39_invoker.call(Navigation_5.get.serviceCreate(context))
      }
  
    // @LINE:69
    case na_nadm_controllers_Detail_createService40_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("serviceType", None)) { (context, serviceType) =>
        na_nadm_controllers_Detail_createService40_invoker.call(Detail_2.get.createService(context, serviceType))
      }
  
    // @LINE:70
    case na_nadm_controllers_Navigation_serviceDetails41_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("clientName", None), params.fromPath[String]("serviceName", None), params.fromPath[String]("serviceType", None)) { (context, clientName, serviceName, serviceType) =>
        na_nadm_controllers_Navigation_serviceDetails41_invoker.call(Navigation_5.get.serviceDetails(context, clientName, serviceName, serviceType))
      }
  
    // @LINE:71
    case na_nadm_controllers_Navigation_serviceEdit42_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("clientName", None), params.fromPath[String]("serviceName", None), params.fromPath[String]("serviceType", None)) { (context, clientName, serviceName, serviceType) =>
        na_nadm_controllers_Navigation_serviceEdit42_invoker.call(Navigation_5.get.serviceEdit(context, clientName, serviceName, serviceType))
      }
  
    // @LINE:72
    case na_nadm_controllers_Navigation_serviceValues43_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("clientName", None)) { (context, clientName) =>
        na_nadm_controllers_Navigation_serviceValues43_invoker.call(Navigation_5.get.serviceValues(context, clientName))
      }
  
    // @LINE:73
    case na_nadm_controllers_Detail_updateService44_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("clientName", None), params.fromPath[String]("serviceName", None)) { (context, clientName, serviceName) =>
        na_nadm_controllers_Detail_updateService44_invoker.call(Detail_2.get.updateService(context, clientName, serviceName))
      }
  
    // @LINE:74
    case na_nadm_controllers_Detail_removeService45_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("clientName", None), params.fromPath[String]("serviceName", None)) { (context, clientName, serviceName) =>
        na_nadm_controllers_Detail_removeService45_invoker.call(Detail_2.get.removeService(context, clientName, serviceName))
      }
  
    // @LINE:77
    case na_nadm_controllers_Search_searchGlobalEquipment46_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_Search_searchGlobalEquipment46_invoker.call(Search_0.get.searchGlobalEquipment(context))
      }
  
    // @LINE:78
    case na_nadm_controllers_Search_searchClient47_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_Search_searchClient47_invoker.call(Search_0.get.searchClient(context))
      }
  
    // @LINE:79
    case na_nadm_controllers_Search_searchEquipment48_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_Search_searchEquipment48_invoker.call(Search_0.get.searchEquipment(context))
      }
  
    // @LINE:80
    case na_nadm_controllers_Search_searchEquipmentAssociations49_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("equipmentId", None)) { (context, equipmentId) =>
        na_nadm_controllers_Search_searchEquipmentAssociations49_invoker.call(Search_0.get.searchEquipmentAssociations(context, equipmentId))
      }
  
    // @LINE:81
    case na_nadm_controllers_Search_searchService50_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_Search_searchService50_invoker.call(Search_0.get.searchService(context))
      }
  
    // @LINE:82
    case na_nadm_controllers_Search_searchClientEquipmentModel51_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("clientId", None)) { (context, clientId) =>
        na_nadm_controllers_Search_searchClientEquipmentModel51_invoker.call(Search_0.get.searchClientEquipmentModel(context, clientId))
      }
  
    // @LINE:83
    case na_nadm_controllers_Search_clientNames52_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_Search_clientNames52_invoker.call(Search_0.get.clientNames(context))
      }
  
    // @LINE:84
    case na_nadm_controllers_Tag_tags53_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_Tag_tags53_invoker.call(Tag_9.get.tags(context))
      }
  
    // @LINE:85
    case na_nadm_controllers_Tag_equipmentTagSearch54_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_Tag_equipmentTagSearch54_invoker.call(Tag_9.get.equipmentTagSearch(context))
      }
  
    // @LINE:86
    case na_nadm_controllers_Tag_equipmentTagSearchAssociatedTagsContinuation55_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("uuid", None)) { (context, uuid) =>
        na_nadm_controllers_Tag_equipmentTagSearchAssociatedTagsContinuation55_invoker.call(Tag_9.get.equipmentTagSearchAssociatedTagsContinuation(context, uuid))
      }
  
    // @LINE:87
    case na_nadm_controllers_Tag_manageEquipmentsTags56_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_Tag_manageEquipmentsTags56_invoker.call(Tag_9.get.manageEquipmentsTags(context))
      }
  
    // @LINE:90
    case na_nadm_controllers_TableConfigs_equipment57_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_TableConfigs_equipment57_invoker.call(TableConfigs_8.get.equipment(context))
      }
  
    // @LINE:91
    case na_nadm_controllers_TableConfigs_globalEquipment58_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_TableConfigs_globalEquipment58_invoker.call(TableConfigs_8.get.globalEquipment(context))
      }
  
    // @LINE:92
    case na_nadm_controllers_TableConfigs_client59_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_TableConfigs_client59_invoker.call(TableConfigs_8.get.client(context))
      }
  
    // @LINE:93
    case na_nadm_controllers_TableConfigs_clientEquipmentModel60_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromQuery[String]("clientId", None)) { (context, clientId) =>
        na_nadm_controllers_TableConfigs_clientEquipmentModel60_invoker.call(TableConfigs_8.get.clientEquipmentModel(context, clientId))
      }
  
    // @LINE:94
    case na_nadm_controllers_TableConfigs_service61_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_TableConfigs_service61_invoker.call(TableConfigs_8.get.service(context))
      }
  
    // @LINE:95
    case na_nadm_controllers_TableConfigs_equipmentassociations62_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromQuery[String]("equipmentId", None)) { (context, equipmentId) =>
        na_nadm_controllers_TableConfigs_equipmentassociations62_invoker.call(TableConfigs_8.get.equipmentassociations(context, equipmentId))
      }
  
    // @LINE:96
    case na_nadm_controllers_TableConfigs_equipmentAssociationsTypeServices63_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_TableConfigs_equipmentAssociationsTypeServices63_invoker.call(TableConfigs_8.get.equipmentAssociationsTypeServices(context))
      }
  
    // @LINE:100
    case na_nadm_controllers_Search_catalogManufacturers64_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromQuery[Boolean]("multipleChoices", None)) { (context, multipleChoices) =>
        na_nadm_controllers_Search_catalogManufacturers64_invoker.call(Search_0.get.catalogManufacturers(context, multipleChoices))
      }
  
    // @LINE:101
    case na_nadm_controllers_Search_catalogModels65_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromQuery[Boolean]("multipleChoices", None)) { (context, multipleChoices) =>
        na_nadm_controllers_Search_catalogModels65_invoker.call(Search_0.get.catalogModels(context, multipleChoices))
      }
  
    // @LINE:102
    case na_nadm_controllers_Search_catalogVersions66_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromQuery[Boolean]("multipleChoices", None)) { (context, multipleChoices) =>
        na_nadm_controllers_Search_catalogVersions66_invoker.call(Search_0.get.catalogVersions(context, multipleChoices))
      }
  
    // @LINE:106
    case na_nadm_controllers_Operation_getMappingModel67_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_Operation_getMappingModel67_invoker.call(Operation_1.get.getMappingModel(context))
      }
  
    // @LINE:107
    case na_nadm_controllers_Operation_getEquipmentOperations68_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_Operation_getEquipmentOperations68_invoker.call(Operation_1.get.getEquipmentOperations(context))
      }
  
    // @LINE:108
    case na_nadm_controllers_Operation_getGlobalEquipmentOperations69_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_Operation_getGlobalEquipmentOperations69_invoker.call(Operation_1.get.getGlobalEquipmentOperations(context))
      }
  
    // @LINE:109
    case na_nadm_controllers_Operation_getOperationForm70_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("operationId", None)) { (context, operationId) =>
        na_nadm_controllers_Operation_getOperationForm70_invoker.call(Operation_1.get.getOperationForm(context, operationId))
      }
  
    // @LINE:110
    case na_nadm_controllers_Operation_getMassiveOperationForm71_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("operationId", None)) { (context, operationId) =>
        na_nadm_controllers_Operation_getMassiveOperationForm71_invoker.call(Operation_1.get.getMassiveOperationForm(context, operationId))
      }
  
    // @LINE:113
    case na_nadm_controllers_Operation_uploadFirmwareFile72_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_Operation_uploadFirmwareFile72_invoker.call(Operation_1.get.uploadFirmwareFile(context))
      }
  
    // @LINE:114
    case na_nadm_controllers_Operation_deleteFirmwareFile73_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("manufacturer", None), params.fromPath[String]("model", None), params.fromPath[String]("version", None)) { (context, manufacturer, model, version) =>
        na_nadm_controllers_Operation_deleteFirmwareFile73_invoker.call(Operation_1.get.deleteFirmwareFile(context, manufacturer, model, version))
      }
  
    // @LINE:115
    case na_nadm_controllers_Operation_executeOperation74_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_Operation_executeOperation74_invoker.call(Operation_1.get.executeOperation(context))
      }
  
    // @LINE:116
    case na_nadm_controllers_Operation_executeBulkOperation75_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_nadm_controllers_Operation_executeBulkOperation75_invoker.call(Operation_1.get.executeBulkOperation(context))
      }
  
    // @LINE:119
    case na_nadm_controllers_Filters_saveFilter76_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("filterContext", None)) { (context, filterContext) =>
        na_nadm_controllers_Filters_saveFilter76_invoker.call(Filters_4.get.saveFilter(context, filterContext))
      }
  
    // @LINE:120
    case na_nadm_controllers_Filters_editFilter77_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("filterContext", None), params.fromPath[String]("filterName", None)) { (context, filterContext, filterName) =>
        na_nadm_controllers_Filters_editFilter77_invoker.call(Filters_4.get.editFilter(context, filterContext, filterName))
      }
  
    // @LINE:121
    case na_nadm_controllers_Filters_deleteFilter78_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("filterContext", None), params.fromPath[String]("filterName", None)) { (context, filterContext, filterName) =>
        na_nadm_controllers_Filters_deleteFilter78_invoker.call(Filters_4.get.deleteFilter(context, filterContext, filterName))
      }
  
    // @LINE:122
    case na_nadm_controllers_Filters_getAllFilters79_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("filterContext", None)) { (context, filterContext) =>
        na_nadm_controllers_Filters_getAllFilters79_invoker.call(Filters_4.get.getAllFilters(context, filterContext))
      }
  
    // @LINE:123
    case na_nadm_controllers_Filters_getSelectedFilter80_route(params@_) =>
      call(params.fromPath[String]("context", None), params.fromPath[String]("filterContext", None), params.fromPath[String]("filterName", None)) { (context, filterContext, filterName) =>
        na_nadm_controllers_Filters_getSelectedFilter80_invoker.call(Filters_4.get.getSelectedFilter(context, filterContext, filterName))
      }
  
    // @LINE:129
    case prefixed_naportalbase_Routes_0_81(handler) => handler
  }
}
