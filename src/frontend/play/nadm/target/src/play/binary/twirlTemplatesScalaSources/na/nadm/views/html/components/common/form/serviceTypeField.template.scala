
package na.nadm.views.html.components.common.form

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.html.components.selectsComp.dynamicSelectOptionComp
/*2.2*/import na.naportalbase.views.tags.i18n
/*4.2*/import scala.collection.immutable

object serviceType<PERSON>ield extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[String,List[String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*6.2*/(context: String, serviceTypes: List[String]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*8.2*/serviceTypeSelectData/*8.23*/ = {{
    var list = serviceTypes
    var stringBuilder = new StringBuilder
    for(serviceType <- list){
        var optionName = serviceType
        var optionLabel = i18n(context+"."+serviceType)
        var optionAttributes = immutable.HashMap[String, String](
            "name" -> optionName,
            "id" -> optionName,
            "value" -> optionName
        )
        stringBuilder.append(dynamicSelectOptionComp.render(optionLabel,optionAttributes))
    }
    play.twirl.api.Html(stringBuilder.toString())
}};
Seq[Any](format.raw/*6.47*/("""

"""),format.raw/*22.2*/("""


"""),format.raw/*25.1*/("""<div class="form-group">
    <label for="serviceType" class="control-label">"""),_display_(/*26.53*/i18n("na.portal.nadm.type")),format.raw/*26.80*/("""</label>
    <x-select data-field="type"
        id="serviceType"
        full-width
        has-clear-button
        placeholder=""""),_display_(/*31.23*/i18n("na.portal.nadm.field.serviceType.allOptions")),format.raw/*31.74*/("""">
            """),_display_(/*32.14*/serviceTypeSelectData),format.raw/*32.35*/("""
    """),format.raw/*33.5*/("""</x-select>
</div>"""))
      }
    }
  }

  def render(context:String,serviceTypes:List[String]): play.twirl.api.HtmlFormat.Appendable = apply(context,serviceTypes)

  def f:((String,List[String]) => play.twirl.api.HtmlFormat.Appendable) = (context,serviceTypes) => apply(context,serviceTypes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:39 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/common/form/serviceTypeField.scala.html
                  HASH: 431d83d55716e26b93e0102ceaba5b81c224bf24
                  MATRIX: 682->1|770->83|816->124|1178->160|1301->208|1330->229|1883->205|1912->752|1942->755|2046->832|2094->859|2253->991|2325->1042|2368->1058|2410->1079|2442->1084
                  LINES: 24->1|25->2|26->4|31->6|35->8|35->8|50->6|52->22|55->25|56->26|56->26|61->31|61->31|62->32|62->32|63->33
                  -- GENERATED --
              */
          