
package na.nadm.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import play.mvc.Http
/*2.2*/import na.naportalbase.views.tags.i18n

object resume extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*3.2*/(context: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*3.19*/("""
"""),format.raw/*4.1*/("""<!DOCTYPE html>
<html lang=""""),_display_(/*5.14*/{Http.Context.current().lang().code()}),format.raw/*5.52*/("""" data-app-theme="nossis-one">
    <head>
        <meta charset="utf-8">
        <title>"""),_display_(/*8.17*/i18n("na.information.title")),format.raw/*8.45*/("""</title>
        """),_display_(/*9.10*/na/*9.12*/.naportalbase.views.html.imports.resumescripts.render()),format.raw/*9.67*/("""
    """),format.raw/*10.5*/("""</head>
    <body>
        <x-resume-section button-action-go-to-module=""""),_display_(/*12.56*/context),format.raw/*12.63*/("""" data-module="na.portal."""),_display_(/*12.89*/context),format.raw/*12.96*/(""""></x-resume-section>
    </body>
</html>
"""))
      }
    }
  }

  def render(context:String): play.twirl.api.HtmlFormat.Appendable = apply(context)

  def f:((String) => play.twirl.api.HtmlFormat.Appendable) = (context) => apply(context)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:39 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/roots/resume.scala.html
                  HASH: 1b3d603e7f97f0b2e3baf4af01f4062d489ca77d
                  MATRIX: 665->1|693->24|1037->65|1149->82|1177->84|1233->114|1291->152|1409->244|1457->272|1502->291|1512->293|1587->348|1620->354|1723->430|1751->437|1804->463|1832->470
                  LINES: 24->1|25->2|30->3|35->3|36->4|37->5|37->5|40->8|40->8|41->9|41->9|41->9|42->10|44->12|44->12|44->12|44->12
                  -- GENERATED --
              */
          