
package na.nadm.views.html.components.client

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.views.html.components.common.form.clientField
/*2.2*/import na.naportalbase.views.html.components.buttonsComp.leftSideFormInputButtonsComp
/*3.2*/import na.naportalbase.views.tags.i18n
/*5.2*/import scala.collection.mutable

object leftSideForm extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*7.2*/():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*9.2*/searchButtonAttributes/*9.24*/ = {{
	var map = mutable.HashMap[String, String]()
	map.put("data-ng-click", "searchCtrl.applySearch()")
	map.put("id", "searchButton")
	map.put("type","submit")
	map.put("value", i18n("na.buttons.search"))
	map
}};def /*18.2*/clearButtonAttributes/*18.23*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-click", "searchCtrl.clearForm($event)")
    map.put("id", "clearSearchButton")
    map.put("type", "reset")
    map.put("value", i18n("na.buttons.clear"))
    map
}};
Seq[Any](format.raw/*7.4*/("""

"""),format.raw/*16.2*/("""

"""),format.raw/*25.2*/("""

"""),format.raw/*27.1*/("""<header class="fx-sidebar-header search-sidebar__header">
	<div class="fx-sidebar-header-inner">
		<p>
			<span class="fx-entity-header-icon">
				<i class="fa fa-search"></i>
			</span>
			<x-i18n key="na.basemodule.lateralSearch.title"></x-i18n>
		</p>
	</div>
</header>
<div class="fx-sidebar-content-wrapper search-sidebar__field-list">
	<x-shadow-scroll vertical>
		"""),_display_(/*39.4*/clientField/*39.15*/.render()),format.raw/*39.24*/("""
	"""),format.raw/*40.2*/("""</x-shadow-scroll>
</div>
<div class="form-group search-sidebar__button-list">
	<div class="fx-splitter-sidebar-buttons">
		<div class="fx-splitter-sidebar-buttons-inner">
			"""),_display_(/*45.5*/leftSideFormInputButtonsComp/*45.33*/.render(searchButtonAttributes, clearButtonAttributes)),format.raw/*45.87*/("""
		"""),format.raw/*46.3*/("""</div>
	</div>
</div>
"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:39 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/client/leftSideForm.scala.html
                  HASH: 748a333ac10fdec453af5c1e9f5c13797b80acec
                  MATRIX: 677->1|745->63|838->150|884->191|1220->225|1300->230|1330->252|1557->468|1587->489|1855->227|1884->465|1913->728|1942->730|2340->1102|2360->1113|2390->1122|2419->1124|2621->1300|2658->1328|2733->1382|2763->1385
                  LINES: 24->1|25->2|26->3|27->5|32->7|36->9|36->9|43->18|43->18|51->7|53->16|55->25|57->27|69->39|69->39|69->39|70->40|75->45|75->45|75->45|76->46
                  -- GENERATED --
              */
          