
package na.nadm.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.NadmConstants.NavContext
/*2.2*/import na.nadm.settings.NadmSettings
/*3.2*/import na.nadm.views.html.components.common.lateralNav
/*4.2*/import na.nadm.views.html.skeletons.mainSkel
/*5.2*/import na.naportalbase.views.html.skeletons.pageHeaderSkel
/*6.2*/import na.naportalbase.views.tags.i18n

object home extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[String,NadmSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*8.2*/(context: String, nadmSettings: NadmSettings):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*10.2*/header/*10.8*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*10.12*/("""
    """),format.raw/*11.5*/("""<div class="pull-left" data-na-portal-nadm-initial-breadcrumb>
        <h1>
            <span class="fx-info-icon">
                <i class="fuxicons fuxicons-equipment-management"></i>
            </span>
            """),_display_(/*16.14*/i18n(context+".home.label.title")),format.raw/*16.47*/("""
        """),format.raw/*17.9*/("""</h1>
    </div>
""")))};
Seq[Any](format.raw/*8.47*/("""

"""),format.raw/*19.2*/("""

"""),_display_(/*21.2*/mainSkel(context)/*21.19*/ {_display_(Seq[Any](format.raw/*21.21*/("""
    """),format.raw/*22.5*/("""<div class="fx-push-footer fx-full-height">
        <div data-fuxi-header></div>
        """),_display_(/*24.10*/lateralNav/*24.20*/.render(context, NavContext.SEARCH, nadmSettings)),format.raw/*24.69*/("""
        """),format.raw/*25.9*/("""<div class="fx-main-content-wrapper fx-with-main-nav">
            """),_display_(/*26.14*/pageHeaderSkel/*26.28*/.render(header,null,null,null)),format.raw/*26.58*/("""
        """),format.raw/*27.9*/("""</div>
    </div>
""")))}),format.raw/*29.2*/("""

"""))
      }
    }
  }

  def render(context:String,nadmSettings:NadmSettings): play.twirl.api.HtmlFormat.Appendable = apply(context,nadmSettings)

  def f:((String,NadmSettings) => play.twirl.api.HtmlFormat.Appendable) = (context,nadmSettings) => apply(context,nadmSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:39 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/roots/home.scala.html
                  HASH: bb435656ed4af2d776ce9701e21ff174c7a1c64c
                  MATRIX: 665->1|721->51|765->89|827->145|879->191|945->251|1300->292|1424->340|1438->346|1519->350|1551->355|1798->575|1852->608|1888->617|1945->337|1974->635|2003->638|2029->655|2069->657|2101->662|2218->752|2237->762|2307->811|2343->820|2438->888|2461->902|2512->932|2548->941|2597->960
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|34->8|38->10|38->10|40->10|41->11|46->16|46->16|47->17|50->8|52->19|54->21|54->21|54->21|55->22|57->24|57->24|57->24|58->25|59->26|59->26|59->26|60->27|62->29
                  -- GENERATED --
              */
          