
package na.nadm.views.html.components.common

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.NadmConstants.NavContext
/*2.2*/import na.nadm.settings.NadmSettings
/*3.2*/import na.naportalbase.views.tags.i18n

object lateralNav extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[String,NavContext,NadmSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*5.2*/(context:String, activeNavigationContext: NavContext, nadmSettings: NadmSettings):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*7.2*/navElements/*7.13*/ = {{

    class NavElementAttribute(
            val action: String, val icon: String, val aClass: String,
            val activeNavContext: NavContext
    ){
        val linkClasses = this.aClass + (if(this.activeNavContext.equals(activeNavigationContext)) " fx-active" else "")
    }

    var map = scala.collection.mutable.LinkedHashMap[String, NavElementAttribute](
        "na.portal.nadm.nav.globalEquip" ->  new NavElementAttribute(
            action = "searchGlobalEquip",
            icon = "fuxicons fuxicons-equipment-configuration",
            aClass = "nav-model",
            activeNavContext = NavContext.GLOBAL_EQUIP
        ),
        "na.portal.nadm.nav.equipment" ->  new NavElementAttribute(
            action = "searchEquipment",
            icon = "glyphicon glyphicon-hdd",
            aClass = "nav-equipment",
            activeNavContext = NavContext.EQUIPMENT
        ),
        "na.portal.nadm.nav.client" ->  new NavElementAttribute(
            action = "searchClient",
            icon = "glyphicon glyphicon-user",
            aClass = "nav-client",
            activeNavContext = NavContext.CLIENT
        ),
        "na.portal.nadm.nav.services" -> new NavElementAttribute(
            action = "searchService",
            icon = "fa fa-cogs",
            aClass = "nav-service" + (if(nadmSettings.hasServiceTypes(context)) "" else " disabled"),
            activeNavContext = NavContext.SERVICE
        )
    )
    map
}};
Seq[Any](format.raw/*5.83*/("""

"""),format.raw/*43.2*/("""

"""),format.raw/*45.1*/("""<!-- data-enable-tooltip -->
<nav id="fx-page-main-nav" class="fx-ico-nav" data-na-portal-nadm-navigation-sidebar>
    <ul>
    """),_display_(/*48.6*/for((key,value) <- navElements) yield /*48.37*/ {_display_(Seq[Any](format.raw/*48.39*/("""
        """),format.raw/*49.9*/("""<li class=""""),_display_(/*49.21*/value/*49.26*/.linkClasses),format.raw/*49.38*/("""" data-placement="right" data-toggle="tooltip" data-enable-tooltip data-original-title=""""),_display_(/*49.127*/i18n(key)),format.raw/*49.136*/("""">
            <a data-nav-action=""""),_display_(/*50.34*/value/*50.39*/.action),format.raw/*50.46*/("""">
                <i class=""""),_display_(/*51.28*/value/*51.33*/.icon),format.raw/*51.38*/(""""></i>
            </a>
        </li>
    """)))}),format.raw/*54.6*/("""
    """),format.raw/*55.5*/("""</ul>
</nav>
"""))
      }
    }
  }

  def render(context:String,activeNavigationContext:NavContext,nadmSettings:NadmSettings): play.twirl.api.HtmlFormat.Appendable = apply(context,activeNavigationContext,nadmSettings)

  def f:((String,NavContext,NadmSettings) => play.twirl.api.HtmlFormat.Appendable) = (context,activeNavigationContext,nadmSettings) => apply(context,activeNavigationContext,nadmSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:29 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/common/lateralNav.scala.html
                  HASH: dca26f1384a9b90594fe86f786f2e636b7dc7820
                  MATRIX: 677->1|733->51|777->89|1149->130|1308->214|1327->225|2817->211|2846->1685|2875->1687|3030->1816|3077->1847|3117->1849|3153->1858|3192->1870|3206->1875|3239->1887|3356->1976|3387->1985|3450->2021|3464->2026|3492->2033|3549->2063|3563->2068|3589->2073|3662->2116|3694->2121
                  LINES: 24->1|25->2|26->3|31->5|35->7|35->7|72->5|74->43|76->45|79->48|79->48|79->48|80->49|80->49|80->49|80->49|80->49|80->49|81->50|81->50|81->50|82->51|82->51|82->51|85->54|86->55
                  -- GENERATED --
              */
          