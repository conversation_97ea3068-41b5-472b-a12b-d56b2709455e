
package na.nadm.views.html.components.common.tableform

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.NadmSettings
/*2.2*/import na.nadm.utils.FormBuilderUtil
/*3.2*/import na.naportalbase.views.html.components.inputsComp.{inputPassword, inputPasswordWithShowContentComp, inputTextAreaComp, inputTextComp}
/*4.2*/import pt.ptinovacao.naportal.components.TextUiComponent
/*6.2*/import java.lang.Boolean.FALSE
/*7.2*/import scala.collection.mutable

object textUiComponent extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[TextUiComponent,String,String,NadmSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*9.2*/(uiComponent: TextUiComponent, angularModel: String, context: String, nadmSettings: NadmSettings):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*11.2*/textPlaceholder/*11.17*/ = {{ FormBuilderUtil.parseI18n(uiComponent.getPlaceholder)}};def /*13.2*/componentName/*13.15*/ = {{ FormBuilderUtil.parseI18n(uiComponent.getI18n)}};def /*15.2*/elementAttr/*15.13*/ = {{
    val map = mutable.HashMap[String, String](
        "data-ng-model" -> angularModel,
        "value" -> "",
        "id" -> uiComponent.getElementId,
        "name" -> uiComponent.getElementId,
        "class" -> "form-control input-sm"
    )

    if(FALSE.equals(uiComponent.getEditable)){
        map.put("disabled","")
    }

    if(uiComponent.getHeight != null) {
        map.put("height",uiComponent.getHeight.toString)
    }

    if(uiComponent.getPlaceholder != null) {
        map.put("placeholder", textPlaceholder)
    }

    if(uiComponent.getRegex != null) {
        map.put("data-na-portal-regex-error", null)
        map.put("regex", uiComponent.getRegex.getExpression)
        val regexMessage = FormBuilderUtil.parseI18n(uiComponent.getRegex.getI18n)
        if(regexMessage == null || "".equals(regexMessage)){
            map.put("notification-message", "not valid (test error message)")
        } else {
            map.put("notification-message", FormBuilderUtil.parseI18n(uiComponent.getRegex.getI18n))
        }
    }

    if (uiComponent.getRequired != null && uiComponent.getRequired) {
        map.put("data-na-portal-required", null)
    }

    map
}};def /*55.2*/componentElement/*55.18*/ = {{
    uiComponent.getType match {
        case TextUiComponent.Type.textField =>
            inputTextComp.render(uiComponent.getElementId,textPlaceholder, elementAttr)

        case TextUiComponent.Type.textArea =>
            inputTextAreaComp.render(uiComponent.getElementId,elementAttr, "")

        case TextUiComponent.Type.passwordField =>
            if(nadmSettings.isShowPasswordsEnabled(context)){
                inputPasswordWithShowContentComp.render(uiComponent.getElementId, textPlaceholder,elementAttr)
            } else {
                inputPassword.render(uiComponent.getElementId, textPlaceholder,elementAttr)
            }

        case TextUiComponent.Type.numberField =>
            inputTextComp.render(uiComponent.getElementId, textPlaceholder,elementAttr)

        case _ => ""
    }
}};
Seq[Any](format.raw/*9.99*/("""

"""),format.raw/*11.77*/("""

"""),format.raw/*13.68*/("""

"""),format.raw/*52.2*/("""


"""),format.raw/*75.2*/("""

"""),format.raw/*77.1*/("""<div data-na-portal-field-notification data-na-portal-input-notification-hr>
    """),_display_(/*78.6*/componentElement),format.raw/*78.22*/("""
"""),format.raw/*79.1*/("""</div>
"""))
      }
    }
  }

  def render(uiComponent:TextUiComponent,angularModel:String,context:String,nadmSettings:NadmSettings): play.twirl.api.HtmlFormat.Appendable = apply(uiComponent,angularModel,context,nadmSettings)

  def f:((TextUiComponent,String,String,NadmSettings) => play.twirl.api.HtmlFormat.Appendable) = (uiComponent,angularModel,context,nadmSettings) => apply(uiComponent,angularModel,context,nadmSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:29 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/common/tableform/textUiComponent.scala.html
                  HASH: ee0874f2bf024c5aeae07854f41d4c6638de4de2
                  MATRIX: 687->1|731->39|775->77|922->218|986->277|1024->309|1406->343|1582->443|1606->458|1680->521|1702->534|1769->590|1789->601|2989->1791|3014->1807|3862->440|3892->518|3922->587|3951->1787|3981->2625|4010->2627|4118->2709|4155->2725|4183->2726
                  LINES: 24->1|25->2|26->3|27->4|28->6|29->7|34->9|38->11|38->11|38->13|38->13|38->15|38->15|75->55|75->55|96->9|98->11|100->13|102->52|105->75|107->77|108->78|108->78|109->79
                  -- GENERATED --
              */
          