
package na.nadm.views.html.components.common.form

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n

object tagsField extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*3.2*/(context: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*3.19*/("""

"""),format.raw/*5.1*/("""<div class="form-group">
    <label for="tags" class="control-label">"""),_display_(/*6.46*/i18n("na.portal.nadm.field.tags.label")),format.raw/*6.85*/("""</label><br/>
    <x-multiselect
        id="tags"
        data-field="tags"
        full-width
        hide-selected-values
        has-clear-button
        adapter="nadm-tags"
        url=""""),_display_(/*14.15*/na/*14.17*/.nadm.controllers.routes.Tag.tags(context).url),format.raw/*14.63*/(""""
        placeholder=""""),_display_(/*15.23*/i18n("na.portal.nadm.field.tags.placeholder")),format.raw/*15.68*/("""">
    </x-multiselect>
</div>
"""))
      }
    }
  }

  def render(context:String): play.twirl.api.HtmlFormat.Appendable = apply(context)

  def f:((String) => play.twirl.api.HtmlFormat.Appendable) = (context) => apply(context)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:39 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/common/form/tagsField.scala.html
                  HASH: f2745d7f0db628ee6e1912b0e5f27aa8e7614b40
                  MATRIX: 682->1|1029->42|1141->59|1169->61|1265->131|1324->170|1543->362|1554->364|1621->410|1672->434|1738->479
                  LINES: 24->1|29->3|34->3|36->5|37->6|37->6|45->14|45->14|45->14|46->15|46->15
                  -- GENERATED --
              */
          