
package na.nadm.views.html.components.service

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.views.html.components.common.form.{clientField, serviceField, serviceTypeField}
/*2.2*/import na.naportalbase.views.html.components.buttonsComp.leftSideFormInputButtonsComp
/*3.2*/import scala.collection.mutable
/*4.2*/import na.naportalbase.views.tags.i18n
/*5.2*/import pt.ptinovacao.na.portal.web.ui.naem.schemas.Context

object leftSideForm extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[String,List[String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*7.2*/(context: String, serviceTypes: List[String]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*9.2*/searchButtonAttributes/*9.24*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-click", "searchCtrl.applySearch()")
    map.put("id", "searchButton")
    map.put("type", "submit")
    map.put("value", i18n("na.buttons.search"))
    map
}};def /*18.2*/clearButtonAttributes/*18.23*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-click", "searchCtrl.clearForm($event)")
    map.put("id", "clearSearchButton")
    map.put("type", "reset")
    map.put("value", i18n("na.buttons.clear"))
    map
}};
Seq[Any](format.raw/*7.47*/("""

"""),format.raw/*16.2*/("""

"""),format.raw/*25.2*/("""

"""),format.raw/*27.1*/("""<header class="fx-sidebar-header search-sidebar__header">
    <div class="fx-sidebar-header-inner">
        <p>
            <span class="fx-entity-header-icon">
                <i class="fa fa-search"></i>
            </span>
            <x-i18n key="na.basemodule.lateralSearch.title"></x-i18n>
        </p>
    </div>
</header>
<div class="fx-sidebar-content-wrapper search-sidebar__field-list">
    <x-nadm-filter data-filter-context=""""),_display_(/*38.42*/Context/*38.49*/.NA_SERVICE),format.raw/*38.60*/("""">
        <x-shadow-scroll>
            """),_display_(/*40.14*/serviceField/*40.26*/.render()),format.raw/*40.35*/("""
            """),_display_(/*41.14*/serviceTypeField/*41.30*/.render(context, serviceTypes)),format.raw/*41.60*/("""
            """),_display_(/*42.14*/clientField/*42.25*/.render()),format.raw/*42.34*/("""
        """),format.raw/*43.9*/("""</x-shadow-scroll>
    </x-nadm-filter>
</div>
<div class="form-group search-sidebar__button-list">
        """),_display_(/*47.10*/leftSideFormInputButtonsComp/*47.38*/.render(searchButtonAttributes,clearButtonAttributes)),format.raw/*47.91*/("""
"""),format.raw/*48.1*/("""</div>
"""))
      }
    }
  }

  def render(context:String,serviceTypes:List[String]): play.twirl.api.HtmlFormat.Appendable = apply(context,serviceTypes)

  def f:((String,List[String]) => play.twirl.api.HtmlFormat.Appendable) = (context,serviceTypes) => apply(context,serviceTypes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:29 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/service/leftSideForm.scala.html
                  HASH: bdff5d4e73889708c2e393384f4f1551e134ac8d
                  MATRIX: 678->1|780->97|873->184|912->217|958->257|1341->318|1464->366|1494->388|1740->623|1770->644|2039->363|2068->620|2097->883|2126->885|2592->1324|2608->1331|2640->1342|2709->1384|2730->1396|2760->1405|2801->1419|2826->1435|2877->1465|2918->1479|2938->1490|2968->1499|3004->1508|3140->1617|3177->1645|3251->1698|3279->1699
                  LINES: 24->1|25->2|26->3|27->4|28->5|33->7|37->9|37->9|44->18|44->18|52->7|54->16|56->25|58->27|69->38|69->38|69->38|71->40|71->40|71->40|72->41|72->41|72->41|73->42|73->42|73->42|74->43|78->47|78->47|78->47|79->48
                  -- GENERATED --
              */
          