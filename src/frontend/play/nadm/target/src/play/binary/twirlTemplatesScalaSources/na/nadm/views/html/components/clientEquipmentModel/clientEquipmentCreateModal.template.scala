
package na.nadm.views.html.components.clientEquipmentModel

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.utils.FormBuilderUtil.ViewType
/*2.2*/import na.nadm.views.components.common.form.HelperFieldDrawingAttributes
/*3.2*/import na.naportalbase.views.tags.i18n

object clientEquipmentCreateModal extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[String,String,ViewType,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*5.2*/(context: String, clientId: String, viewContext : ViewType):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*7.2*/identificationBlock/*7.21*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*7.25*/("""
    """),format.raw/*8.5*/("""<div class="form form--mmv">
        <div class="form-group">
            <label for="manufacturer" class="control-label">"""),_display_(/*10.62*/i18n("na.portal.nadm.manufacturer")),format.raw/*10.97*/("""</label>
            <x-manufacturer-field></x-manufacturer-field>
        </div>
        <div class="form-group">
            <label for="model" class="control-label">"""),_display_(/*14.55*/i18n("na.portal.nadm.model")),format.raw/*14.83*/("""</label>
            <x-model-field></x-model-field>
        </div>
        <div class="form-group">
            <label for="version" class="control-label">"""),_display_(/*18.57*/i18n("na.portal.nadm.version")),format.raw/*18.87*/("""</label>
            <x-version-field></x-version-field>
        </div>
        <div class="form-group">
            <button class="btn btn-default btn-sm disabled ng-scope pull-right" data-ng-click="characterizeEquipment()" data-na-portal-nadm-client-equip-characterize-button id="button-characterize">
                <i class="fa fa-angle-double-down"></i>
                """),_display_(/*24.18*/i18n("na.portal.nadm.characterize.equipment")),format.raw/*24.63*/("""
            """),format.raw/*25.13*/("""</button>
        </div>
    </div>
""")))};def /*30.2*/characteristicsBlock/*30.22*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*30.26*/("""
    """),format.raw/*31.5*/("""<div id="characteristics">
        <div class="alert alert-info">
            <div class="fx-alert-icon"></div>
            <div class="fx-alert-message">
                <strong>"""),_display_(/*35.26*/i18n("na.portal.nadm.characterize.after.fill")),format.raw/*35.72*/("""</strong>
            </div>
        </div>
    </div>
""")))};
Seq[Any](format.raw/*5.61*/("""

"""),format.raw/*28.2*/("""

"""),format.raw/*39.2*/("""

"""),format.raw/*41.1*/("""<div class="fx-main-content-wrapper " data-ng-controller="NaPortalNadmCommonController" data-na-portal-nadm-client-equip-model-create-modal data-client-id=""""),_display_(/*41.158*/clientId),format.raw/*41.166*/("""">
        <div class="fx-entity-info" data-context="equipmentModel">
            <x-shadow-scroll vertical>
                    <x-collapsible class="fx-section" header=""""),_display_(/*44.64*/i18n("nadm.detail.section.identification")),format.raw/*44.106*/("""">
                        """),_display_(/*45.26*/identificationBlock),format.raw/*45.45*/("""
                    """),format.raw/*46.21*/("""</x-collapsible >
                    <x-collapsible class="fx-section" header=""""),_display_(/*47.64*/i18n("nadm.detail.section.characteristics")),format.raw/*47.107*/("""">
                        """),_display_(/*48.26*/characteristicsBlock),format.raw/*48.46*/("""
                    """),format.raw/*49.21*/("""</x-collapsible>
            </x-shadow-scroll>
        </div>
</div>
"""))
      }
    }
  }

  def render(context:String,clientId:String,viewContext:ViewType): play.twirl.api.HtmlFormat.Appendable = apply(context,clientId,viewContext)

  def f:((String,String,ViewType) => play.twirl.api.HtmlFormat.Appendable) = (context,clientId,viewContext) => apply(context,clientId,viewContext)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:39 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/clientEquipmentModel/clientEquipmentCreateModal.scala.html
                  HASH: e965d3f9938bbf5f655d8844d0efc95da53d43f5
                  MATRIX: 691->1|744->48|824->122|1204->163|1341->225|1368->244|1448->248|1479->253|1629->376|1685->411|1881->580|1930->608|2114->765|2165->795|2569->1172|2635->1217|2676->1230|2736->1270|2765->1290|2846->1294|2878->1299|3085->1479|3152->1525|3247->222|3276->1267|3305->1581|3334->1583|3519->1740|3549->1748|3748->1920|3812->1962|3867->1990|3907->2009|3956->2030|4064->2111|4129->2154|4184->2182|4225->2202|4274->2223
                  LINES: 24->1|25->2|26->3|31->5|35->7|35->7|37->7|38->8|40->10|40->10|44->14|44->14|48->18|48->18|54->24|54->24|55->25|58->30|58->30|60->30|61->31|65->35|65->35|70->5|72->28|74->39|76->41|76->41|76->41|79->44|79->44|80->45|80->45|81->46|82->47|82->47|83->48|83->48|84->49
                  -- GENERATED --
              */
          