
package na.nadm.views.html.components.common.tableform

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.NadmSettings
/*2.2*/import na.nadm.utils.FormBuilderUtil
/*3.2*/import na.nadm.utils.FormBuilderUtil.ViewType
/*4.2*/import na.nadm.views.html.components.common.tableform
/*5.2*/import na.naportalbase.views.tags.i18n
/*6.2*/import pt.ptinovacao.naportal.components.{DateUiComponent, SelectableUiComponent, TextUiComponent, UiComponent}
/*8.2*/import java.lang.Boolean.{FALSE, TRUE}

object tableRowForm extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template6[String,UiComponent,ViewType,String,String,NadmSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*10.2*/(
        componentGroupName: String,
        uiComponent : UiComponent,
        viewContext: ViewType,
        pageContext: String,
        context: String,
        nadmSettings: NadmSettings
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*24.2*/isEditable/*24.12*/ = {{ uiComponent != null && !FALSE.equals(uiComponent.getEditable) }};def /*31.2*/isRequired/*31.12*/ = {{ uiComponent != null && TRUE.equals(uiComponent.getRequired) }};def /*33.2*/isSelectable/*33.14*/ = {{ uiComponent.isInstanceOf[SelectableUiComponent] }};def /*34.2*/componentName/*34.15*/ = {{ FormBuilderUtil.parseI18n(uiComponent.getI18n) }};def /*35.2*/componentClasses/*35.18*/ = {{
	uiComponent match {
		case textComponent: TextUiComponent => textComponent.getType match {
			case TextUiComponent.Type.numberField   => "text-row number-field"
			case TextUiComponent.Type.passwordField => "text-row password-field"
			case TextUiComponent.Type.textArea      => "text-row text-area"
			case TextUiComponent.Type.textField     => "text-row text-field"
		}
		case selectableComponent: SelectableUiComponent => selectableComponent.getType  match {
			case SelectableUiComponent.Type.checkBox    => "selectable-row check-box"
			case SelectableUiComponent.Type.radioButton => "selectable-row radio-buttons"
			case SelectableUiComponent.Type.selectBox   => "selectable-row select-box-field"
		}
		case dateComponent: DateUiComponent => dateComponent.getType  match {
			case DateUiComponent.Type.DatePicker     => "date-row date-picker"
			case DateUiComponent.Type.TimePicker     => "date-row time-picker"
			case DateUiComponent.Type.DateTimePicker => "date-row date-time-picker"
		}
	}
}};
Seq[Any](format.raw/*17.2*/("""

"""),format.raw/*23.3*/("""
"""),format.raw/*24.81*/("""

"""),format.raw/*30.3*/("""
"""),format.raw/*31.79*/("""

"""),format.raw/*33.69*/("""
"""),format.raw/*34.69*/("""
"""),format.raw/*54.2*/("""

"""),format.raw/*56.1*/("""<tr class=""""),_display_(/*56.13*/componentClasses),format.raw/*56.29*/("""" data-group=""""),_display_(/*56.44*/componentGroupName),format.raw/*56.62*/("""" data-na-portal-nadm-table-row data-table-form-mode="" data-required=""""),_display_(/*56.134*/isRequired),format.raw/*56.144*/("""" data-id=""""),_display_(/*56.156*/uiComponent/*56.167*/.getElementId),format.raw/*56.180*/("""" data-selectable=""""),_display_(/*56.200*/isSelectable),format.raw/*56.212*/("""">
    <td class="description-col">"""),_display_(/*57.34*/componentName),format.raw/*57.47*/("""</td>
    <td class="value-col">
        """),_display_(/*59.10*/if(uiComponent != null)/*59.33*/ {_display_(Seq[Any](format.raw/*59.35*/("""
            """),_display_(/*60.14*/tableform/*60.23*/.uiComponent.render(uiComponent, viewContext, context, nadmSettings)),format.raw/*60.91*/("""
        """)))}),format.raw/*61.10*/("""
    """),format.raw/*62.5*/("""</td>
    """),_display_(/*63.6*/if(viewContext == ViewType.CREATE || viewContext == ViewType.EDIT)/*63.72*/{_display_(Seq[Any](format.raw/*63.73*/("""

        """),format.raw/*65.9*/("""<td class="impact-col">
            """),_display_(/*66.14*/if(isEditable && (!isRequired || isSelectable))/*66.61*/{_display_(Seq[Any](format.raw/*66.62*/("""
                """),format.raw/*67.17*/("""<div data-ng-show="isValueEdited()" class="impact-warning">
                    <div class="alert alert--inline alert-warning">
                        <div class="fx-alert-icon">
                        </div>
                        <div class="fx-alert-message--inline">
                            """),_display_(/*72.30*/i18n("na.portal.nadm."+pageContext+".impact")),format.raw/*72.75*/("""
                        """),format.raw/*73.25*/("""</div>
                        <button class="btn btn-default btn-xs revert-param ng-scope" title=""""),_display_(/*74.94*/i18n("na.portal.nadm.revert")),format.raw/*74.123*/("""">
                            <i class="fa fa-undo"></i>
                        </button>
                    </div>
                </div>
            """)))}),format.raw/*79.14*/("""
        """),format.raw/*80.9*/("""</td>
        <td class=" fx-table-actions actions-col">
            """),_display_(/*82.14*/if(isEditable && (!isRequired || isSelectable))/*82.61*/{_display_(Seq[Any](format.raw/*82.62*/("""

                """),format.raw/*84.17*/("""<button data-ng-hide="confirmMode"
                    data-ng-class=""""),format.raw/*85.36*/("""{"""),format.raw/*85.37*/("""disabled: model.isInherit"""),format.raw/*85.62*/("""}"""),format.raw/*85.63*/(""""
                    data-ng-click="TableRowController.addNextLevelConfirmation()"
                    title=""""),_display_(/*87.29*/i18n("na.portal.nadm.inheritValue")),format.raw/*87.64*/(""""
                    class="btn btn-default btn-xs inherit-value"
                    data-action-name="inheritValue">
                    <i class="fuxicons fuxicons-hybrid"></i>
                </button>
                <button data-ng-show="confirmMode"
                    data-ng-click="TableRowController.confirmInherit()"
                    title=""""),_display_(/*94.29*/i18n("na.portal.nadm.confirm")),format.raw/*94.59*/(""""
                    class="btn btn btn-primary btn-xs confirm-inherit"
                    data-action-name="confirmInherit">
                    <i class="glyphicon glyphicon-ok"></i>
                </button>
                <button data-ng-show="confirmMode"
                    data-ng-click="TableRowController.cancelInherit()"
                    title=""""),_display_(/*101.29*/i18n("na.portal.nadm.cancel")),format.raw/*101.58*/(""""
                    class="btn btn btn-default btn-xs cancel-inherit"
                    data-action-name="edit">
                    <i class="glyphicon glyphicon-remove"></i>
                </button>

            """)))}/*107.15*/else/*107.20*/{_display_(Seq[Any](format.raw/*107.21*/("""

                """),format.raw/*109.17*/("""<button	class="btn btn-default btn-xs revert-param disabled">
                    <i class="fuxicons fuxicons-hybrid"></i>
                </button>

            """)))}),format.raw/*113.14*/("""
        """),format.raw/*114.9*/("""</td>
    """)))}),format.raw/*115.6*/("""
"""),format.raw/*116.1*/("""</tr>"""))
      }
    }
  }

  def render(componentGroupName:String,uiComponent:UiComponent,viewContext:ViewType,pageContext:String,context:String,nadmSettings:NadmSettings): play.twirl.api.HtmlFormat.Appendable = apply(componentGroupName,uiComponent,viewContext,pageContext,context,nadmSettings)

  def f:((String,UiComponent,ViewType,String,String,NadmSettings) => play.twirl.api.HtmlFormat.Appendable) = (componentGroupName,uiComponent,viewContext,pageContext,context,nadmSettings) => apply(componentGroupName,uiComponent,viewContext,pageContext,context,nadmSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:39 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/common/tableform/tableRowForm.scala.html
                  HASH: c34a675a6cbd02a5b97759821774b3ac647ee250
                  MATRIX: 687->1|731->40|775->79|828->127|889->183|935->224|1054->340|1453->383|1733->779|1752->789|1835->1053|1854->1063|1935->1135|1956->1147|2025->1205|2047->1218|2115->1275|2140->1291|3199->584|3230->776|3260->858|3291->1050|3321->1130|3353->1202|3383->1272|3412->2320|3443->2324|3482->2336|3519->2352|3561->2367|3600->2385|3700->2457|3732->2467|3772->2479|3793->2490|3828->2503|3876->2523|3910->2535|3974->2572|4008->2585|4079->2629|4111->2652|4151->2654|4193->2669|4211->2678|4300->2746|4342->2757|4375->2763|4413->2775|4488->2841|4527->2842|4566->2854|4631->2892|4687->2939|4726->2940|4772->2958|5107->3266|5173->3311|5227->3337|5355->3438|5406->3467|5597->3627|5634->3637|5733->3709|5789->3756|5828->3757|5876->3777|5975->3848|6004->3849|6057->3874|6086->3875|6227->3989|6283->4024|6675->4389|6726->4419|7124->4789|7175->4818|7421->5045|7435->5050|7475->5051|7524->5071|7723->5238|7761->5248|7804->5260|7834->5262
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->8|35->10|46->24|46->24|46->31|46->31|46->33|46->33|46->34|46->34|46->35|46->35|66->17|68->23|69->24|71->30|72->31|74->33|75->34|76->54|78->56|78->56|78->56|78->56|78->56|78->56|78->56|78->56|78->56|78->56|78->56|78->56|79->57|79->57|81->59|81->59|81->59|82->60|82->60|82->60|83->61|84->62|85->63|85->63|85->63|87->65|88->66|88->66|88->66|89->67|94->72|94->72|95->73|96->74|96->74|101->79|102->80|104->82|104->82|104->82|106->84|107->85|107->85|107->85|107->85|109->87|109->87|116->94|116->94|123->101|123->101|129->107|129->107|129->107|131->109|135->113|136->114|137->115|138->116
                  -- GENERATED --
              */
          