# Home page
GET         /monitoring/home                                            na.monitoring.controllers.Application.goToHome()

# Resume
GET         /monitoring/resume                                          na.monitoring.controllers.Application.resume()

# Javascript Routing
GET         /monitoring/assets/javascripts/routes                       na.monitoring.controllers.Application.javascriptRoutes()

# Angular Scripts
GET         /monitoring/imports/jsscripts                               na.monitoring.controllers.Application.getModuleAngularScripts

# Map static resources from the /public folder to the /assets URL path
GET         /monitoring/assets/*file                                    na.monitoring.controllers.Assets.versioned(path="/public", file: Asset)

# User allowed actions
GET         /monitoring/orders/allowed-actions                          na.monitoring.controllers.OrderController.getAllowedOrderActions

# ---------------------
# --- Table configs ---
# ---------------------

GET         /monitoring/table-configs/orderMonitoring                                             na.monitoring.controllers.TableController.dataTableOrderMonitoring
GET         /monitoring/table-configs/operationServiceMonitoring                                  na.monitoring.controllers.TableController.dataTableOperationServiceMonitoring
GET         /monitoring/table-configs/responseRetries                                             na.monitoring.controllers.TableController.dataTableResponseRetries
GET         /monitoring/table-configs/deliveryAttempts                                            na.monitoring.controllers.TableController.dataTableDeliveryAttempts
GET         /monitoring/table-configs/validationMessages                                          na.monitoring.controllers.TableController.dataTableValidationMessages
GET         /monitoring/table-configs/external-system-history                                     na.monitoring.controllers.TableController.dataTableExternalSystemInteractionsHistory
GET         /monitoring/table-configs/notificationDetails                                         na.monitoring.controllers.TableController.dataTableNotificationDetails



# ---------------
# ---  Orders ---
# ---------------

GET         /monitoring/orders/create                                                             na.monitoring.controllers.OrderController.createPage
GET         /monitoring/orders/clone/:orderId                                                     na.monitoring.controllers.OrderController.clonePage(orderId: String)
POST        /monitoring/orders/create                                                             na.monitoring.controllers.OrderController.createOrder
POST        /monitoring/search/orders                                                             na.monitoring.controllers.OrderController.queryOrders
GET         /monitoring/orders                                                                    na.monitoring.controllers.OrderController.home
GET         /monitoring/orders/view/:orderId                                                      na.monitoring.controllers.OrderController.view(orderId: String)
GET         /monitoring/orders/view/:orderId/info                                                 na.monitoring.controllers.OrderController.getOrderById(orderId: String)
POST        /monitoring/orders/view/summary                                                       na.monitoring.controllers.OrderController.getOrderSummary()
GET         /monitoring/orders/settings                                                           na.monitoring.controllers.OrderController.getSettings
GET         /monitoring/orders/states                                                             na.monitoring.controllers.OrderController.getStates
POST        /monitoring/orders/systems                                                            na.monitoring.controllers.OrderController.getSystems
GET         /monitoring/orders/systemsUsers                                                       na.monitoring.controllers.OrderController.getSystemsWithUsers
GET         /monitoring/orders/filters                                                            na.monitoring.controllers.OrderController.getFiltersAsJson
POST        /monitoring/orders/operations                                                         na.monitoring.controllers.OrderController.getOperations
GET         /monitoring/orders/operation-info/:id                                                 na.monitoring.controllers.OrderController.getOperationById(id: String)
GET         /monitoring/orders/operation-info/:version/:name                                      na.monitoring.controllers.OrderController.getOperationByVersionAndName(version: String, name: String)
GET         /monitoring/orders/operationsByVersion                                                na.monitoring.controllers.OrderController.getOperationsByVersion(versionName: String)
POST        /monitoring/orders/serviceTypes                                                       na.monitoring.controllers.OrderController.getServiceTypes
GET         /monitoring/orders/graph                                                              na.monitoring.controllers.OrderController.getGraph(workflowExternalId: String)
POST        /monitoring/orders/actions/cancel                                                     na.monitoring.controllers.OrderController.cancelOrders
POST        /monitoring/orders/actions/retry                                                      na.monitoring.controllers.OrderController.retryOrders
POST        /monitoring/orders/actions/rollback                                                   na.monitoring.controllers.OrderController.rollBackOrders
POST        /monitoring/orders/actions/cancelWithRollback                                         na.monitoring.controllers.OrderController.cancelOrdersWithRollback
POST        /monitoring/orders/actions/skip                                                       na.monitoring.controllers.OrderController.skipOrders
POST        /monitoring/orders/actions/remove                                                     na.monitoring.controllers.OrderController.removeOrders
POST        /monitoring/orders/requestCallback                                                    na.monitoring.controllers.RequesterCallbackController.requesterCallback

# -------------------
# ---  Notifications ---
# -------------------


GET         /monitoring/notifications/orderId/:orderId                                            na.monitoring.controllers.NotificationController.getNotificationsByOrderId(orderId: String)
POST        /monitoring/notifications/:notificationId/subscriber/:subscriberName/retry            na.monitoring.controllers.NotificationController.retryNotificationBySubscriber(notificationId: String, subscriberName: String )
POST        /monitoring/notifications/:notificationId/retry                                       na.monitoring.controllers.NotificationController.retryNotification(notificationId: String )


# -------------------
# ---  Activities ---
# -------------------

POST       /monitoring/activity/skip                                                              na.monitoring.controllers.ActivityController.skip()
POST       /monitoring/activity/rollback                                                          na.monitoring.controllers.ActivityController.rollback()
POST       /monitoring/activity/restart                                                           na.monitoring.controllers.ActivityController.restart()
POST       /monitoring/activity/retry                                                             na.monitoring.controllers.ActivityController.retry(isNewRequest: Boolean ?= false)
POST       /monitoring/activity/complete                                                          na.monitoring.controllers.ActivityController.complete()

# ----------------------------------------------------------------------------------------------------------------------
# --- BASEMODULE: this route must be placed at the end of the file
# ----------------------------------------------------------------------------------------------------------------------

->        /monitoring        naportalbase.Routes