

na.monitoring.backend.host = naportal-ci.c.ptin.corppt.com
na.monitoring.backend.port = 39006

na.monitoring.backend.swe.host = naportal-ci.c.ptin.corppt.com
na.monitoring.backend.swe.port = 39004

# states
na.monitoring.states = [
  { value: VALIDATING,   text: na.portal.monitoring.home.filters.state.validating,   icon: badge-validating,    execution: none }
  { value: ACKNOWLEDGED, text: na.portal.monitoring.home.filters.state.acknowledged, icon: badge-acknowledged,  execution: none }
  { value: CANCELLED,    text: na.portal.monitoring.home.filters.state.canceled,     icon: badge-canceled,      execution: error }
  { value: HELD,         text: na.portal.monitoring.home.filters.state.held,         icon: badge-held,          execution: error }
  { value: FAILED,       text: na.portal.monitoring.home.filters.state.failed,       icon: badge-held,          execution: error }
  { value: IN_PROGRESS,  text: na.portal.monitoring.home.filters.state.in.progress,  icon: badge-in-progress,   execution: executing }
  { value: PARTIAL,      text: na.portal.monitoring.home.filters.state.partial,      icon: badge-partial,       execution: executing }
  { value: PENDING,      text: na.portal.monitoring.home.filters.state.pending,      icon: badge-pending,       execution: executing }
  { value: REJECTED,     text: na.portal.monitoring.home.filters.state.rejected,     icon: badge-rejected,      execution: none }
  { value: COMPLETED,    text: na.portal.monitoring.home.filters.state.completed,    icon: badge-completed,     execution: success }
]

#filter by
na.monitoring.search.date.filterby = [ systemEntry, lastUpdate ]

na.monitoring.possible.order.actions = [ cancel, remove, skip, retry, rollback, cancelWithRollback ]

na.monitoring.statespriority = [ HELD, IN_PROGRESS, COMPLETED ]


# validation error codes to determine if validation response has a structure or signature error
na.monitoring.validation.structure.errorcode = "CL_01000"
na.monitoring.validation.signature.errorcode = "CL_02000"

# Default daterangepicker interval: LAST_30_MINUTES, LAST_HOUR, LAST_12_HOURS, LAST_24_HOURS, LAST_WEEK, LAST_MONTH
# Fallback: LAST_24_HOURS
na.monitoring.daterangepicker.default.interval = LAST_24_HOURS

# Service Locator config file path
# Fallback: /opt/alticelabs/na-portal/conf/na-portal-frontend-monitoring/cluster-client-config.yml
na.monitoring.service.locator.configfile = conf-dev/cluster-client-config.yml

na.monitoring.order.create.allowedVersionStates = [TESTING, DEPRECATED, PRODUCTION]

na.monitoring.requesterCallback.url = "http://nacommons-test-bed.c.ptin.corppt.com:35000/requesterCallback"

# this is optional, it's just here to validate the configuration in dev mode
na.monitoring.features.whitelist = [
  "order-create",
  "order-clone:demo",
  "operation-attributes-view",
]

# External systems whitelist
na.monitoring.order.create.system.whitelist = [
    "NA",
    "OM",
    "user_DEVELOPMENT_WITH_SUBSCRIBER"
]

na.monitoring.tables = { include "../conf/tables.conf" }

# BOT School API Configuration
na.monitoring.botschool.api.url = "https://api.ng.botschool.ai/rest-interface/chat-llms"
na.monitoring.botschool.api.key = "1e24e0f4ea214780846a6c00d777c3d5"
na.monitoring.botschool.prompt.template = "I am going to give you details of a Orchestration Service order in JSON. Please summarize it into a short paragraph in plain text suitable for a manager, using a maximum of 200 words. Ensure the text is formatted using <strong> tags for the order ID, externalId and serviceId and <br> tags for new lines. Write the whole text in english language and format the summary like this example: 'The order <strong>TT-2024164-000037783</strong>, with externalId <strong>Test</strong>, for serviceId <strong> test</strong> was created on June 12, 2024 and took 1 minute to get to current state closed, on June 13, 2024. It has 2 order items: cfs.1 is completed and cfs.2 had an error' Here are the details for the service order: "
