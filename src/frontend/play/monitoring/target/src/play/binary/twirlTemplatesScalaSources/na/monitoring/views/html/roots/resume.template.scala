
package na.monitoring.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import play.mvc.Http
/*2.2*/import na.naportalbase.views.tags.i18n

object resume extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*3.1*/("""<!DOCTYPE html>
<html lang=""""),_display_(/*4.14*/{Http.Context.current().lang().code()}),format.raw/*4.52*/("""" data-app-theme="nossis-one">
    <head>
        <meta charset="utf-8">
        <title>"""),_display_(/*7.17*/i18n("na.information.title")),format.raw/*7.45*/("""</title>
        """),_display_(/*8.10*/na/*8.12*/.naportalbase.views.html.imports.resumescripts.render()),format.raw/*8.67*/("""
    """),format.raw/*9.5*/("""</head>
    <body>
        <x-resume-section button-action-go-to-module="monitoring" data-module="na.portal.monitoring"></x-resume-section>
    </body>
</html>
"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 12:39:10 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/monitoring/target/TwirlSource/na/monitoring/views/roots/resume.scala.html
                  HASH: f2a875b92a05976c6a87e628fb6a49d78e49e211
                  MATRIX: 671->1|699->23|1125->62|1180->91|1238->129|1353->218|1401->246|1445->264|1455->266|1530->321|1561->326
                  LINES: 24->1|25->2|35->3|36->4|36->4|39->7|39->7|40->8|40->8|40->8|41->9
                  -- GENERATED --
              */
          