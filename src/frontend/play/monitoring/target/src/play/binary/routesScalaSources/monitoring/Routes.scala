// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/monitoring/conf/monitoring.routes
// @DATE:Tue Jul 01 11:21:17 WEST 2025

package monitoring

import play.core.routing._
import play.core.routing.HandlerInvokerFactory._

import play.api.mvc._

import _root_.controllers.Assets.Asset

class Routes(
  override val errorHandler: play.api.http.HttpErrorHandler, 
  // @LINE:2
  Application_3: na.monitoring.controllers.Application,
  // @LINE:14
  Assets_0: na.monitoring.controllers.Assets,
  // @LINE:17
  OrderController_5: na.monitoring.controllers.OrderController,
  // @LINE:23
  TableController_2: na.monitoring.controllers.TableController,
  // @LINE:61
  RequesterCallbackController_6: na.monitoring.controllers.RequesterCallbackController,
  // @LINE:68
  NotificationController_1: na.monitoring.controllers.NotificationController,
  // @LINE:77
  ActivityController_4: na.monitoring.controllers.ActivityController,
  // @LINE:87
  naportalbase_Routes_0: naportalbase.Routes,
  val prefix: String
) extends GeneratedRouter {

   @javax.inject.Inject()
   def this(errorHandler: play.api.http.HttpErrorHandler,
    // @LINE:2
    Application_3: na.monitoring.controllers.Application,
    // @LINE:14
    Assets_0: na.monitoring.controllers.Assets,
    // @LINE:17
    OrderController_5: na.monitoring.controllers.OrderController,
    // @LINE:23
    TableController_2: na.monitoring.controllers.TableController,
    // @LINE:61
    RequesterCallbackController_6: na.monitoring.controllers.RequesterCallbackController,
    // @LINE:68
    NotificationController_1: na.monitoring.controllers.NotificationController,
    // @LINE:77
    ActivityController_4: na.monitoring.controllers.ActivityController,
    // @LINE:87
    naportalbase_Routes_0: naportalbase.Routes
  ) = this(errorHandler, Application_3, Assets_0, OrderController_5, TableController_2, RequesterCallbackController_6, NotificationController_1, ActivityController_4, naportalbase_Routes_0, "/")

  def withPrefix(prefix: String): Routes = {
    monitoring.RoutesPrefix.setPrefix(prefix)
    new Routes(errorHandler, Application_3, Assets_0, OrderController_5, TableController_2, RequesterCallbackController_6, NotificationController_1, ActivityController_4, naportalbase_Routes_0, prefix)
  }

  private[this] val defaultPrefix: String = {
    if (this.prefix.endsWith("/")) "" else "/"
  }

  def documentation = List(
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/home""", """na.monitoring.controllers.Application.goToHome()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/resume""", """na.monitoring.controllers.Application.resume()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/assets/javascripts/routes""", """na.monitoring.controllers.Application.javascriptRoutes()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/imports/jsscripts""", """na.monitoring.controllers.Application.getModuleAngularScripts"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/assets/""" + "$" + """file<.+>""", """na.monitoring.controllers.Assets.versioned(path:String = "/public", file:Asset)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders/allowed-actions""", """na.monitoring.controllers.OrderController.getAllowedOrderActions"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/table-configs/orderMonitoring""", """na.monitoring.controllers.TableController.dataTableOrderMonitoring"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/table-configs/operationServiceMonitoring""", """na.monitoring.controllers.TableController.dataTableOperationServiceMonitoring"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/table-configs/responseRetries""", """na.monitoring.controllers.TableController.dataTableResponseRetries"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/table-configs/deliveryAttempts""", """na.monitoring.controllers.TableController.dataTableDeliveryAttempts"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/table-configs/validationMessages""", """na.monitoring.controllers.TableController.dataTableValidationMessages"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/table-configs/external-system-history""", """na.monitoring.controllers.TableController.dataTableExternalSystemInteractionsHistory"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/table-configs/notificationDetails""", """na.monitoring.controllers.TableController.dataTableNotificationDetails"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders/create""", """na.monitoring.controllers.OrderController.createPage"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders/clone/""" + "$" + """orderId<[^/]+>""", """na.monitoring.controllers.OrderController.clonePage(orderId:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders/create""", """na.monitoring.controllers.OrderController.createOrder"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/search/orders""", """na.monitoring.controllers.OrderController.queryOrders"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders""", """na.monitoring.controllers.OrderController.home"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders/view/""" + "$" + """orderId<[^/]+>""", """na.monitoring.controllers.OrderController.view(orderId:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders/view/""" + "$" + """orderId<[^/]+>/info""", """na.monitoring.controllers.OrderController.getOrderById(orderId:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders/settings""", """na.monitoring.controllers.OrderController.getSettings"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders/states""", """na.monitoring.controllers.OrderController.getStates"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders/systems""", """na.monitoring.controllers.OrderController.getSystems"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders/systemsUsers""", """na.monitoring.controllers.OrderController.getSystemsWithUsers"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders/filters""", """na.monitoring.controllers.OrderController.getFiltersAsJson"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders/operations""", """na.monitoring.controllers.OrderController.getOperations"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders/operation-info/""" + "$" + """id<[^/]+>""", """na.monitoring.controllers.OrderController.getOperationById(id:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders/operation-info/""" + "$" + """version<[^/]+>/""" + "$" + """name<[^/]+>""", """na.monitoring.controllers.OrderController.getOperationByVersionAndName(version:String, name:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders/operationsByVersion""", """na.monitoring.controllers.OrderController.getOperationsByVersion(versionName:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders/serviceTypes""", """na.monitoring.controllers.OrderController.getServiceTypes"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders/graph""", """na.monitoring.controllers.OrderController.getGraph(workflowExternalId:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders/actions/cancel""", """na.monitoring.controllers.OrderController.cancelOrders"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders/actions/retry""", """na.monitoring.controllers.OrderController.retryOrders"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders/actions/rollback""", """na.monitoring.controllers.OrderController.rollBackOrders"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders/actions/cancelWithRollback""", """na.monitoring.controllers.OrderController.cancelOrdersWithRollback"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders/actions/skip""", """na.monitoring.controllers.OrderController.skipOrders"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders/actions/remove""", """na.monitoring.controllers.OrderController.removeOrders"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/orders/requestCallback""", """na.monitoring.controllers.RequesterCallbackController.requesterCallback"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/notifications/orderId/""" + "$" + """orderId<[^/]+>""", """na.monitoring.controllers.NotificationController.getNotificationsByOrderId(orderId:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/notifications/""" + "$" + """notificationId<[^/]+>/subscriber/""" + "$" + """subscriberName<[^/]+>/retry""", """na.monitoring.controllers.NotificationController.retryNotificationBySubscriber(notificationId:String, subscriberName:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/notifications/""" + "$" + """notificationId<[^/]+>/retry""", """na.monitoring.controllers.NotificationController.retryNotification(notificationId:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/activity/skip""", """na.monitoring.controllers.ActivityController.skip()"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/activity/rollback""", """na.monitoring.controllers.ActivityController.rollback()"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/activity/restart""", """na.monitoring.controllers.ActivityController.restart()"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/activity/retry""", """na.monitoring.controllers.ActivityController.retry(isNewRequest:Boolean ?= false)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """monitoring/activity/complete""", """na.monitoring.controllers.ActivityController.complete()"""),
    prefixed_naportalbase_Routes_0_46.router.documentation,
    Nil
  ).foldLeft(List.empty[(String,String,String)]) { (s,e) => e.asInstanceOf[Any] match {
    case r @ (_,_,_) => s :+ r.asInstanceOf[(String,String,String)]
    case l => s ++ l.asInstanceOf[List[(String,String,String)]]
  }}


  // @LINE:2
  private[this] lazy val na_monitoring_controllers_Application_goToHome0_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/home")))
  )
  private[this] lazy val na_monitoring_controllers_Application_goToHome0_invoker = createInvoker(
    Application_3.goToHome(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.Application",
      "goToHome",
      Nil,
      "GET",
      this.prefix + """monitoring/home""",
      """ Home page""",
      Seq()
    )
  )

  // @LINE:5
  private[this] lazy val na_monitoring_controllers_Application_resume1_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/resume")))
  )
  private[this] lazy val na_monitoring_controllers_Application_resume1_invoker = createInvoker(
    Application_3.resume(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.Application",
      "resume",
      Nil,
      "GET",
      this.prefix + """monitoring/resume""",
      """ Resume""",
      Seq()
    )
  )

  // @LINE:8
  private[this] lazy val na_monitoring_controllers_Application_javascriptRoutes2_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/assets/javascripts/routes")))
  )
  private[this] lazy val na_monitoring_controllers_Application_javascriptRoutes2_invoker = createInvoker(
    Application_3.javascriptRoutes(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.Application",
      "javascriptRoutes",
      Nil,
      "GET",
      this.prefix + """monitoring/assets/javascripts/routes""",
      """ Javascript Routing""",
      Seq()
    )
  )

  // @LINE:11
  private[this] lazy val na_monitoring_controllers_Application_getModuleAngularScripts3_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/imports/jsscripts")))
  )
  private[this] lazy val na_monitoring_controllers_Application_getModuleAngularScripts3_invoker = createInvoker(
    Application_3.getModuleAngularScripts,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.Application",
      "getModuleAngularScripts",
      Nil,
      "GET",
      this.prefix + """monitoring/imports/jsscripts""",
      """ Angular Scripts""",
      Seq()
    )
  )

  // @LINE:14
  private[this] lazy val na_monitoring_controllers_Assets_versioned4_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/assets/"), DynamicPart("file", """.+""",false)))
  )
  private[this] lazy val na_monitoring_controllers_Assets_versioned4_invoker = createInvoker(
    Assets_0.versioned(fakeValue[String], fakeValue[Asset]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.Assets",
      "versioned",
      Seq(classOf[String], classOf[Asset]),
      "GET",
      this.prefix + """monitoring/assets/""" + "$" + """file<.+>""",
      """ Map static resources from the /public folder to the /assets URL path""",
      Seq()
    )
  )

  // @LINE:17
  private[this] lazy val na_monitoring_controllers_OrderController_getAllowedOrderActions5_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders/allowed-actions")))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_getAllowedOrderActions5_invoker = createInvoker(
    OrderController_5.getAllowedOrderActions,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "getAllowedOrderActions",
      Nil,
      "GET",
      this.prefix + """monitoring/orders/allowed-actions""",
      """ User allowed actions""",
      Seq()
    )
  )

  // @LINE:23
  private[this] lazy val na_monitoring_controllers_TableController_dataTableOrderMonitoring6_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/table-configs/orderMonitoring")))
  )
  private[this] lazy val na_monitoring_controllers_TableController_dataTableOrderMonitoring6_invoker = createInvoker(
    TableController_2.dataTableOrderMonitoring,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.TableController",
      "dataTableOrderMonitoring",
      Nil,
      "GET",
      this.prefix + """monitoring/table-configs/orderMonitoring""",
      """""",
      Seq()
    )
  )

  // @LINE:24
  private[this] lazy val na_monitoring_controllers_TableController_dataTableOperationServiceMonitoring7_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/table-configs/operationServiceMonitoring")))
  )
  private[this] lazy val na_monitoring_controllers_TableController_dataTableOperationServiceMonitoring7_invoker = createInvoker(
    TableController_2.dataTableOperationServiceMonitoring,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.TableController",
      "dataTableOperationServiceMonitoring",
      Nil,
      "GET",
      this.prefix + """monitoring/table-configs/operationServiceMonitoring""",
      """""",
      Seq()
    )
  )

  // @LINE:25
  private[this] lazy val na_monitoring_controllers_TableController_dataTableResponseRetries8_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/table-configs/responseRetries")))
  )
  private[this] lazy val na_monitoring_controllers_TableController_dataTableResponseRetries8_invoker = createInvoker(
    TableController_2.dataTableResponseRetries,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.TableController",
      "dataTableResponseRetries",
      Nil,
      "GET",
      this.prefix + """monitoring/table-configs/responseRetries""",
      """""",
      Seq()
    )
  )

  // @LINE:26
  private[this] lazy val na_monitoring_controllers_TableController_dataTableDeliveryAttempts9_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/table-configs/deliveryAttempts")))
  )
  private[this] lazy val na_monitoring_controllers_TableController_dataTableDeliveryAttempts9_invoker = createInvoker(
    TableController_2.dataTableDeliveryAttempts,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.TableController",
      "dataTableDeliveryAttempts",
      Nil,
      "GET",
      this.prefix + """monitoring/table-configs/deliveryAttempts""",
      """""",
      Seq()
    )
  )

  // @LINE:27
  private[this] lazy val na_monitoring_controllers_TableController_dataTableValidationMessages10_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/table-configs/validationMessages")))
  )
  private[this] lazy val na_monitoring_controllers_TableController_dataTableValidationMessages10_invoker = createInvoker(
    TableController_2.dataTableValidationMessages,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.TableController",
      "dataTableValidationMessages",
      Nil,
      "GET",
      this.prefix + """monitoring/table-configs/validationMessages""",
      """""",
      Seq()
    )
  )

  // @LINE:28
  private[this] lazy val na_monitoring_controllers_TableController_dataTableExternalSystemInteractionsHistory11_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/table-configs/external-system-history")))
  )
  private[this] lazy val na_monitoring_controllers_TableController_dataTableExternalSystemInteractionsHistory11_invoker = createInvoker(
    TableController_2.dataTableExternalSystemInteractionsHistory,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.TableController",
      "dataTableExternalSystemInteractionsHistory",
      Nil,
      "GET",
      this.prefix + """monitoring/table-configs/external-system-history""",
      """""",
      Seq()
    )
  )

  // @LINE:29
  private[this] lazy val na_monitoring_controllers_TableController_dataTableNotificationDetails12_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/table-configs/notificationDetails")))
  )
  private[this] lazy val na_monitoring_controllers_TableController_dataTableNotificationDetails12_invoker = createInvoker(
    TableController_2.dataTableNotificationDetails,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.TableController",
      "dataTableNotificationDetails",
      Nil,
      "GET",
      this.prefix + """monitoring/table-configs/notificationDetails""",
      """""",
      Seq()
    )
  )

  // @LINE:37
  private[this] lazy val na_monitoring_controllers_OrderController_createPage13_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders/create")))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_createPage13_invoker = createInvoker(
    OrderController_5.createPage,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "createPage",
      Nil,
      "GET",
      this.prefix + """monitoring/orders/create""",
      """""",
      Seq()
    )
  )

  // @LINE:38
  private[this] lazy val na_monitoring_controllers_OrderController_clonePage14_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders/clone/"), DynamicPart("orderId", """[^/]+""",true)))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_clonePage14_invoker = createInvoker(
    OrderController_5.clonePage(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "clonePage",
      Seq(classOf[String]),
      "GET",
      this.prefix + """monitoring/orders/clone/""" + "$" + """orderId<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:39
  private[this] lazy val na_monitoring_controllers_OrderController_createOrder15_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders/create")))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_createOrder15_invoker = createInvoker(
    OrderController_5.createOrder,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "createOrder",
      Nil,
      "POST",
      this.prefix + """monitoring/orders/create""",
      """""",
      Seq()
    )
  )

  // @LINE:40
  private[this] lazy val na_monitoring_controllers_OrderController_queryOrders16_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/search/orders")))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_queryOrders16_invoker = createInvoker(
    OrderController_5.queryOrders,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "queryOrders",
      Nil,
      "POST",
      this.prefix + """monitoring/search/orders""",
      """""",
      Seq()
    )
  )

  // @LINE:41
  private[this] lazy val na_monitoring_controllers_OrderController_home17_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders")))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_home17_invoker = createInvoker(
    OrderController_5.home,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "home",
      Nil,
      "GET",
      this.prefix + """monitoring/orders""",
      """""",
      Seq()
    )
  )

  // @LINE:42
  private[this] lazy val na_monitoring_controllers_OrderController_view18_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders/view/"), DynamicPart("orderId", """[^/]+""",true)))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_view18_invoker = createInvoker(
    OrderController_5.view(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "view",
      Seq(classOf[String]),
      "GET",
      this.prefix + """monitoring/orders/view/""" + "$" + """orderId<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:43
  private[this] lazy val na_monitoring_controllers_OrderController_getOrderById19_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders/view/"), DynamicPart("orderId", """[^/]+""",true), StaticPart("/info")))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_getOrderById19_invoker = createInvoker(
    OrderController_5.getOrderById(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "getOrderById",
      Seq(classOf[String]),
      "GET",
      this.prefix + """monitoring/orders/view/""" + "$" + """orderId<[^/]+>/info""",
      """""",
      Seq()
    )
  )

  // @LINE:44
  private[this] lazy val na_monitoring_controllers_OrderController_getSettings20_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders/settings")))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_getSettings20_invoker = createInvoker(
    OrderController_5.getSettings,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "getSettings",
      Nil,
      "GET",
      this.prefix + """monitoring/orders/settings""",
      """""",
      Seq()
    )
  )

  // @LINE:45
  private[this] lazy val na_monitoring_controllers_OrderController_getStates21_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders/states")))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_getStates21_invoker = createInvoker(
    OrderController_5.getStates,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "getStates",
      Nil,
      "GET",
      this.prefix + """monitoring/orders/states""",
      """""",
      Seq()
    )
  )

  // @LINE:46
  private[this] lazy val na_monitoring_controllers_OrderController_getSystems22_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders/systems")))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_getSystems22_invoker = createInvoker(
    OrderController_5.getSystems,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "getSystems",
      Nil,
      "POST",
      this.prefix + """monitoring/orders/systems""",
      """""",
      Seq()
    )
  )

  // @LINE:47
  private[this] lazy val na_monitoring_controllers_OrderController_getSystemsWithUsers23_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders/systemsUsers")))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_getSystemsWithUsers23_invoker = createInvoker(
    OrderController_5.getSystemsWithUsers,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "getSystemsWithUsers",
      Nil,
      "GET",
      this.prefix + """monitoring/orders/systemsUsers""",
      """""",
      Seq()
    )
  )

  // @LINE:48
  private[this] lazy val na_monitoring_controllers_OrderController_getFiltersAsJson24_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders/filters")))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_getFiltersAsJson24_invoker = createInvoker(
    OrderController_5.getFiltersAsJson,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "getFiltersAsJson",
      Nil,
      "GET",
      this.prefix + """monitoring/orders/filters""",
      """""",
      Seq()
    )
  )

  // @LINE:49
  private[this] lazy val na_monitoring_controllers_OrderController_getOperations25_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders/operations")))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_getOperations25_invoker = createInvoker(
    OrderController_5.getOperations,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "getOperations",
      Nil,
      "POST",
      this.prefix + """monitoring/orders/operations""",
      """""",
      Seq()
    )
  )

  // @LINE:50
  private[this] lazy val na_monitoring_controllers_OrderController_getOperationById26_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders/operation-info/"), DynamicPart("id", """[^/]+""",true)))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_getOperationById26_invoker = createInvoker(
    OrderController_5.getOperationById(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "getOperationById",
      Seq(classOf[String]),
      "GET",
      this.prefix + """monitoring/orders/operation-info/""" + "$" + """id<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:51
  private[this] lazy val na_monitoring_controllers_OrderController_getOperationByVersionAndName27_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders/operation-info/"), DynamicPart("version", """[^/]+""",true), StaticPart("/"), DynamicPart("name", """[^/]+""",true)))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_getOperationByVersionAndName27_invoker = createInvoker(
    OrderController_5.getOperationByVersionAndName(fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "getOperationByVersionAndName",
      Seq(classOf[String], classOf[String]),
      "GET",
      this.prefix + """monitoring/orders/operation-info/""" + "$" + """version<[^/]+>/""" + "$" + """name<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:52
  private[this] lazy val na_monitoring_controllers_OrderController_getOperationsByVersion28_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders/operationsByVersion")))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_getOperationsByVersion28_invoker = createInvoker(
    OrderController_5.getOperationsByVersion(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "getOperationsByVersion",
      Seq(classOf[String]),
      "GET",
      this.prefix + """monitoring/orders/operationsByVersion""",
      """""",
      Seq()
    )
  )

  // @LINE:53
  private[this] lazy val na_monitoring_controllers_OrderController_getServiceTypes29_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders/serviceTypes")))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_getServiceTypes29_invoker = createInvoker(
    OrderController_5.getServiceTypes,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "getServiceTypes",
      Nil,
      "POST",
      this.prefix + """monitoring/orders/serviceTypes""",
      """""",
      Seq()
    )
  )

  // @LINE:54
  private[this] lazy val na_monitoring_controllers_OrderController_getGraph30_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders/graph")))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_getGraph30_invoker = createInvoker(
    OrderController_5.getGraph(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "getGraph",
      Seq(classOf[String]),
      "GET",
      this.prefix + """monitoring/orders/graph""",
      """""",
      Seq()
    )
  )

  // @LINE:55
  private[this] lazy val na_monitoring_controllers_OrderController_cancelOrders31_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders/actions/cancel")))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_cancelOrders31_invoker = createInvoker(
    OrderController_5.cancelOrders,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "cancelOrders",
      Nil,
      "POST",
      this.prefix + """monitoring/orders/actions/cancel""",
      """""",
      Seq()
    )
  )

  // @LINE:56
  private[this] lazy val na_monitoring_controllers_OrderController_retryOrders32_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders/actions/retry")))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_retryOrders32_invoker = createInvoker(
    OrderController_5.retryOrders,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "retryOrders",
      Nil,
      "POST",
      this.prefix + """monitoring/orders/actions/retry""",
      """""",
      Seq()
    )
  )

  // @LINE:57
  private[this] lazy val na_monitoring_controllers_OrderController_rollBackOrders33_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders/actions/rollback")))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_rollBackOrders33_invoker = createInvoker(
    OrderController_5.rollBackOrders,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "rollBackOrders",
      Nil,
      "POST",
      this.prefix + """monitoring/orders/actions/rollback""",
      """""",
      Seq()
    )
  )

  // @LINE:58
  private[this] lazy val na_monitoring_controllers_OrderController_cancelOrdersWithRollback34_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders/actions/cancelWithRollback")))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_cancelOrdersWithRollback34_invoker = createInvoker(
    OrderController_5.cancelOrdersWithRollback,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "cancelOrdersWithRollback",
      Nil,
      "POST",
      this.prefix + """monitoring/orders/actions/cancelWithRollback""",
      """""",
      Seq()
    )
  )

  // @LINE:59
  private[this] lazy val na_monitoring_controllers_OrderController_skipOrders35_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders/actions/skip")))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_skipOrders35_invoker = createInvoker(
    OrderController_5.skipOrders,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "skipOrders",
      Nil,
      "POST",
      this.prefix + """monitoring/orders/actions/skip""",
      """""",
      Seq()
    )
  )

  // @LINE:60
  private[this] lazy val na_monitoring_controllers_OrderController_removeOrders36_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders/actions/remove")))
  )
  private[this] lazy val na_monitoring_controllers_OrderController_removeOrders36_invoker = createInvoker(
    OrderController_5.removeOrders,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.OrderController",
      "removeOrders",
      Nil,
      "POST",
      this.prefix + """monitoring/orders/actions/remove""",
      """""",
      Seq()
    )
  )

  // @LINE:61
  private[this] lazy val na_monitoring_controllers_RequesterCallbackController_requesterCallback37_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/orders/requestCallback")))
  )
  private[this] lazy val na_monitoring_controllers_RequesterCallbackController_requesterCallback37_invoker = createInvoker(
    RequesterCallbackController_6.requesterCallback,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.RequesterCallbackController",
      "requesterCallback",
      Nil,
      "POST",
      this.prefix + """monitoring/orders/requestCallback""",
      """""",
      Seq()
    )
  )

  // @LINE:68
  private[this] lazy val na_monitoring_controllers_NotificationController_getNotificationsByOrderId38_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/notifications/orderId/"), DynamicPart("orderId", """[^/]+""",true)))
  )
  private[this] lazy val na_monitoring_controllers_NotificationController_getNotificationsByOrderId38_invoker = createInvoker(
    NotificationController_1.getNotificationsByOrderId(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.NotificationController",
      "getNotificationsByOrderId",
      Seq(classOf[String]),
      "GET",
      this.prefix + """monitoring/notifications/orderId/""" + "$" + """orderId<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:69
  private[this] lazy val na_monitoring_controllers_NotificationController_retryNotificationBySubscriber39_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/notifications/"), DynamicPart("notificationId", """[^/]+""",true), StaticPart("/subscriber/"), DynamicPart("subscriberName", """[^/]+""",true), StaticPart("/retry")))
  )
  private[this] lazy val na_monitoring_controllers_NotificationController_retryNotificationBySubscriber39_invoker = createInvoker(
    NotificationController_1.retryNotificationBySubscriber(fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.NotificationController",
      "retryNotificationBySubscriber",
      Seq(classOf[String], classOf[String]),
      "POST",
      this.prefix + """monitoring/notifications/""" + "$" + """notificationId<[^/]+>/subscriber/""" + "$" + """subscriberName<[^/]+>/retry""",
      """""",
      Seq()
    )
  )

  // @LINE:70
  private[this] lazy val na_monitoring_controllers_NotificationController_retryNotification40_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/notifications/"), DynamicPart("notificationId", """[^/]+""",true), StaticPart("/retry")))
  )
  private[this] lazy val na_monitoring_controllers_NotificationController_retryNotification40_invoker = createInvoker(
    NotificationController_1.retryNotification(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.NotificationController",
      "retryNotification",
      Seq(classOf[String]),
      "POST",
      this.prefix + """monitoring/notifications/""" + "$" + """notificationId<[^/]+>/retry""",
      """""",
      Seq()
    )
  )

  // @LINE:77
  private[this] lazy val na_monitoring_controllers_ActivityController_skip41_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/activity/skip")))
  )
  private[this] lazy val na_monitoring_controllers_ActivityController_skip41_invoker = createInvoker(
    ActivityController_4.skip(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.ActivityController",
      "skip",
      Nil,
      "POST",
      this.prefix + """monitoring/activity/skip""",
      """""",
      Seq()
    )
  )

  // @LINE:78
  private[this] lazy val na_monitoring_controllers_ActivityController_rollback42_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/activity/rollback")))
  )
  private[this] lazy val na_monitoring_controllers_ActivityController_rollback42_invoker = createInvoker(
    ActivityController_4.rollback(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.ActivityController",
      "rollback",
      Nil,
      "POST",
      this.prefix + """monitoring/activity/rollback""",
      """""",
      Seq()
    )
  )

  // @LINE:79
  private[this] lazy val na_monitoring_controllers_ActivityController_restart43_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/activity/restart")))
  )
  private[this] lazy val na_monitoring_controllers_ActivityController_restart43_invoker = createInvoker(
    ActivityController_4.restart(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.ActivityController",
      "restart",
      Nil,
      "POST",
      this.prefix + """monitoring/activity/restart""",
      """""",
      Seq()
    )
  )

  // @LINE:80
  private[this] lazy val na_monitoring_controllers_ActivityController_retry44_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/activity/retry")))
  )
  private[this] lazy val na_monitoring_controllers_ActivityController_retry44_invoker = createInvoker(
    ActivityController_4.retry(fakeValue[Boolean]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.ActivityController",
      "retry",
      Seq(classOf[Boolean]),
      "POST",
      this.prefix + """monitoring/activity/retry""",
      """""",
      Seq()
    )
  )

  // @LINE:81
  private[this] lazy val na_monitoring_controllers_ActivityController_complete45_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("monitoring/activity/complete")))
  )
  private[this] lazy val na_monitoring_controllers_ActivityController_complete45_invoker = createInvoker(
    ActivityController_4.complete(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "monitoring",
      "na.monitoring.controllers.ActivityController",
      "complete",
      Nil,
      "POST",
      this.prefix + """monitoring/activity/complete""",
      """""",
      Seq()
    )
  )

  // @LINE:87
  private[this] val prefixed_naportalbase_Routes_0_46 = Include(naportalbase_Routes_0.withPrefix(this.prefix + (if (this.prefix.endsWith("/")) "" else "/") + "monitoring"))


  def routes: PartialFunction[RequestHeader, Handler] = {
  
    // @LINE:2
    case na_monitoring_controllers_Application_goToHome0_route(params@_) =>
      call { 
        na_monitoring_controllers_Application_goToHome0_invoker.call(Application_3.goToHome())
      }
  
    // @LINE:5
    case na_monitoring_controllers_Application_resume1_route(params@_) =>
      call { 
        na_monitoring_controllers_Application_resume1_invoker.call(Application_3.resume())
      }
  
    // @LINE:8
    case na_monitoring_controllers_Application_javascriptRoutes2_route(params@_) =>
      call { 
        na_monitoring_controllers_Application_javascriptRoutes2_invoker.call(Application_3.javascriptRoutes())
      }
  
    // @LINE:11
    case na_monitoring_controllers_Application_getModuleAngularScripts3_route(params@_) =>
      call { 
        na_monitoring_controllers_Application_getModuleAngularScripts3_invoker.call(Application_3.getModuleAngularScripts)
      }
  
    // @LINE:14
    case na_monitoring_controllers_Assets_versioned4_route(params@_) =>
      call(Param[String]("path", Right("/public")), params.fromPath[Asset]("file", None)) { (path, file) =>
        na_monitoring_controllers_Assets_versioned4_invoker.call(Assets_0.versioned(path, file))
      }
  
    // @LINE:17
    case na_monitoring_controllers_OrderController_getAllowedOrderActions5_route(params@_) =>
      call { 
        na_monitoring_controllers_OrderController_getAllowedOrderActions5_invoker.call(OrderController_5.getAllowedOrderActions)
      }
  
    // @LINE:23
    case na_monitoring_controllers_TableController_dataTableOrderMonitoring6_route(params@_) =>
      call { 
        na_monitoring_controllers_TableController_dataTableOrderMonitoring6_invoker.call(TableController_2.dataTableOrderMonitoring)
      }
  
    // @LINE:24
    case na_monitoring_controllers_TableController_dataTableOperationServiceMonitoring7_route(params@_) =>
      call { 
        na_monitoring_controllers_TableController_dataTableOperationServiceMonitoring7_invoker.call(TableController_2.dataTableOperationServiceMonitoring)
      }
  
    // @LINE:25
    case na_monitoring_controllers_TableController_dataTableResponseRetries8_route(params@_) =>
      call { 
        na_monitoring_controllers_TableController_dataTableResponseRetries8_invoker.call(TableController_2.dataTableResponseRetries)
      }
  
    // @LINE:26
    case na_monitoring_controllers_TableController_dataTableDeliveryAttempts9_route(params@_) =>
      call { 
        na_monitoring_controllers_TableController_dataTableDeliveryAttempts9_invoker.call(TableController_2.dataTableDeliveryAttempts)
      }
  
    // @LINE:27
    case na_monitoring_controllers_TableController_dataTableValidationMessages10_route(params@_) =>
      call { 
        na_monitoring_controllers_TableController_dataTableValidationMessages10_invoker.call(TableController_2.dataTableValidationMessages)
      }
  
    // @LINE:28
    case na_monitoring_controllers_TableController_dataTableExternalSystemInteractionsHistory11_route(params@_) =>
      call { 
        na_monitoring_controllers_TableController_dataTableExternalSystemInteractionsHistory11_invoker.call(TableController_2.dataTableExternalSystemInteractionsHistory)
      }
  
    // @LINE:29
    case na_monitoring_controllers_TableController_dataTableNotificationDetails12_route(params@_) =>
      call { 
        na_monitoring_controllers_TableController_dataTableNotificationDetails12_invoker.call(TableController_2.dataTableNotificationDetails)
      }
  
    // @LINE:37
    case na_monitoring_controllers_OrderController_createPage13_route(params@_) =>
      call { 
        na_monitoring_controllers_OrderController_createPage13_invoker.call(OrderController_5.createPage)
      }
  
    // @LINE:38
    case na_monitoring_controllers_OrderController_clonePage14_route(params@_) =>
      call(params.fromPath[String]("orderId", None)) { (orderId) =>
        na_monitoring_controllers_OrderController_clonePage14_invoker.call(OrderController_5.clonePage(orderId))
      }
  
    // @LINE:39
    case na_monitoring_controllers_OrderController_createOrder15_route(params@_) =>
      call { 
        na_monitoring_controllers_OrderController_createOrder15_invoker.call(OrderController_5.createOrder)
      }
  
    // @LINE:40
    case na_monitoring_controllers_OrderController_queryOrders16_route(params@_) =>
      call { 
        na_monitoring_controllers_OrderController_queryOrders16_invoker.call(OrderController_5.queryOrders)
      }
  
    // @LINE:41
    case na_monitoring_controllers_OrderController_home17_route(params@_) =>
      call { 
        na_monitoring_controllers_OrderController_home17_invoker.call(OrderController_5.home)
      }
  
    // @LINE:42
    case na_monitoring_controllers_OrderController_view18_route(params@_) =>
      call(params.fromPath[String]("orderId", None)) { (orderId) =>
        na_monitoring_controllers_OrderController_view18_invoker.call(OrderController_5.view(orderId))
      }
  
    // @LINE:43
    case na_monitoring_controllers_OrderController_getOrderById19_route(params@_) =>
      call(params.fromPath[String]("orderId", None)) { (orderId) =>
        na_monitoring_controllers_OrderController_getOrderById19_invoker.call(OrderController_5.getOrderById(orderId))
      }
  
    // @LINE:44
    case na_monitoring_controllers_OrderController_getSettings20_route(params@_) =>
      call { 
        na_monitoring_controllers_OrderController_getSettings20_invoker.call(OrderController_5.getSettings)
      }
  
    // @LINE:45
    case na_monitoring_controllers_OrderController_getStates21_route(params@_) =>
      call { 
        na_monitoring_controllers_OrderController_getStates21_invoker.call(OrderController_5.getStates)
      }
  
    // @LINE:46
    case na_monitoring_controllers_OrderController_getSystems22_route(params@_) =>
      call { 
        na_monitoring_controllers_OrderController_getSystems22_invoker.call(OrderController_5.getSystems)
      }
  
    // @LINE:47
    case na_monitoring_controllers_OrderController_getSystemsWithUsers23_route(params@_) =>
      call { 
        na_monitoring_controllers_OrderController_getSystemsWithUsers23_invoker.call(OrderController_5.getSystemsWithUsers)
      }
  
    // @LINE:48
    case na_monitoring_controllers_OrderController_getFiltersAsJson24_route(params@_) =>
      call { 
        na_monitoring_controllers_OrderController_getFiltersAsJson24_invoker.call(OrderController_5.getFiltersAsJson)
      }
  
    // @LINE:49
    case na_monitoring_controllers_OrderController_getOperations25_route(params@_) =>
      call { 
        na_monitoring_controllers_OrderController_getOperations25_invoker.call(OrderController_5.getOperations)
      }
  
    // @LINE:50
    case na_monitoring_controllers_OrderController_getOperationById26_route(params@_) =>
      call(params.fromPath[String]("id", None)) { (id) =>
        na_monitoring_controllers_OrderController_getOperationById26_invoker.call(OrderController_5.getOperationById(id))
      }
  
    // @LINE:51
    case na_monitoring_controllers_OrderController_getOperationByVersionAndName27_route(params@_) =>
      call(params.fromPath[String]("version", None), params.fromPath[String]("name", None)) { (version, name) =>
        na_monitoring_controllers_OrderController_getOperationByVersionAndName27_invoker.call(OrderController_5.getOperationByVersionAndName(version, name))
      }
  
    // @LINE:52
    case na_monitoring_controllers_OrderController_getOperationsByVersion28_route(params@_) =>
      call(params.fromQuery[String]("versionName", None)) { (versionName) =>
        na_monitoring_controllers_OrderController_getOperationsByVersion28_invoker.call(OrderController_5.getOperationsByVersion(versionName))
      }
  
    // @LINE:53
    case na_monitoring_controllers_OrderController_getServiceTypes29_route(params@_) =>
      call { 
        na_monitoring_controllers_OrderController_getServiceTypes29_invoker.call(OrderController_5.getServiceTypes)
      }
  
    // @LINE:54
    case na_monitoring_controllers_OrderController_getGraph30_route(params@_) =>
      call(params.fromQuery[String]("workflowExternalId", None)) { (workflowExternalId) =>
        na_monitoring_controllers_OrderController_getGraph30_invoker.call(OrderController_5.getGraph(workflowExternalId))
      }
  
    // @LINE:55
    case na_monitoring_controllers_OrderController_cancelOrders31_route(params@_) =>
      call { 
        na_monitoring_controllers_OrderController_cancelOrders31_invoker.call(OrderController_5.cancelOrders)
      }
  
    // @LINE:56
    case na_monitoring_controllers_OrderController_retryOrders32_route(params@_) =>
      call { 
        na_monitoring_controllers_OrderController_retryOrders32_invoker.call(OrderController_5.retryOrders)
      }
  
    // @LINE:57
    case na_monitoring_controllers_OrderController_rollBackOrders33_route(params@_) =>
      call { 
        na_monitoring_controllers_OrderController_rollBackOrders33_invoker.call(OrderController_5.rollBackOrders)
      }
  
    // @LINE:58
    case na_monitoring_controllers_OrderController_cancelOrdersWithRollback34_route(params@_) =>
      call { 
        na_monitoring_controllers_OrderController_cancelOrdersWithRollback34_invoker.call(OrderController_5.cancelOrdersWithRollback)
      }
  
    // @LINE:59
    case na_monitoring_controllers_OrderController_skipOrders35_route(params@_) =>
      call { 
        na_monitoring_controllers_OrderController_skipOrders35_invoker.call(OrderController_5.skipOrders)
      }
  
    // @LINE:60
    case na_monitoring_controllers_OrderController_removeOrders36_route(params@_) =>
      call { 
        na_monitoring_controllers_OrderController_removeOrders36_invoker.call(OrderController_5.removeOrders)
      }
  
    // @LINE:61
    case na_monitoring_controllers_RequesterCallbackController_requesterCallback37_route(params@_) =>
      call { 
        na_monitoring_controllers_RequesterCallbackController_requesterCallback37_invoker.call(RequesterCallbackController_6.requesterCallback)
      }
  
    // @LINE:68
    case na_monitoring_controllers_NotificationController_getNotificationsByOrderId38_route(params@_) =>
      call(params.fromPath[String]("orderId", None)) { (orderId) =>
        na_monitoring_controllers_NotificationController_getNotificationsByOrderId38_invoker.call(NotificationController_1.getNotificationsByOrderId(orderId))
      }
  
    // @LINE:69
    case na_monitoring_controllers_NotificationController_retryNotificationBySubscriber39_route(params@_) =>
      call(params.fromPath[String]("notificationId", None), params.fromPath[String]("subscriberName", None)) { (notificationId, subscriberName) =>
        na_monitoring_controllers_NotificationController_retryNotificationBySubscriber39_invoker.call(NotificationController_1.retryNotificationBySubscriber(notificationId, subscriberName))
      }
  
    // @LINE:70
    case na_monitoring_controllers_NotificationController_retryNotification40_route(params@_) =>
      call(params.fromPath[String]("notificationId", None)) { (notificationId) =>
        na_monitoring_controllers_NotificationController_retryNotification40_invoker.call(NotificationController_1.retryNotification(notificationId))
      }
  
    // @LINE:77
    case na_monitoring_controllers_ActivityController_skip41_route(params@_) =>
      call { 
        na_monitoring_controllers_ActivityController_skip41_invoker.call(ActivityController_4.skip())
      }
  
    // @LINE:78
    case na_monitoring_controllers_ActivityController_rollback42_route(params@_) =>
      call { 
        na_monitoring_controllers_ActivityController_rollback42_invoker.call(ActivityController_4.rollback())
      }
  
    // @LINE:79
    case na_monitoring_controllers_ActivityController_restart43_route(params@_) =>
      call { 
        na_monitoring_controllers_ActivityController_restart43_invoker.call(ActivityController_4.restart())
      }
  
    // @LINE:80
    case na_monitoring_controllers_ActivityController_retry44_route(params@_) =>
      call(params.fromQuery[Boolean]("isNewRequest", Some(false))) { (isNewRequest) =>
        na_monitoring_controllers_ActivityController_retry44_invoker.call(ActivityController_4.retry(isNewRequest))
      }
  
    // @LINE:81
    case na_monitoring_controllers_ActivityController_complete45_route(params@_) =>
      call { 
        na_monitoring_controllers_ActivityController_complete45_invoker.call(ActivityController_4.complete())
      }
  
    // @LINE:87
    case prefixed_naportalbase_Routes_0_46(handler) => handler
  }
}
