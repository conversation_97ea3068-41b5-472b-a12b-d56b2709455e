
package na.monitoring.views.html.skeletons

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object mainSkel extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[String,Html,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(title: String = "")(content: Html):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*3.2*/scripts/*3.9*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*3.13*/("""
"""),_display_(/*4.2*/na/*4.4*/.monitoring.views.html.imports.scripts.render()),format.raw/*4.51*/("""
""")))};def /*7.2*/body/*7.6*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*7.10*/("""
"""),format.raw/*8.1*/("""<div id="monitoring" class="page page--monitoring fx-full-height inherit" data-na-portal-page-container>
    """),_display_(/*9.6*/content),format.raw/*9.13*/("""
"""),format.raw/*10.1*/("""</div>
""")))};
Seq[Any](format.raw/*1.37*/("""

"""),format.raw/*5.2*/("""

"""),format.raw/*11.2*/("""

"""),_display_(/*13.2*/na/*13.4*/.naportalbase.views.html.skeletons.root.render(title, body, scripts)))
      }
    }
  }

  def render(title:String,content:Html): play.twirl.api.HtmlFormat.Appendable = apply(title)(content)

  def f:((String) => (Html) => play.twirl.api.HtmlFormat.Appendable) = (title) => (content) => apply(title)(content)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 12:11:15 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/monitoring/target/TwirlSource/na/monitoring/views/skeletons/mainSkel.scala.html
                  HASH: 70ce77c8d6b0ae4411ee9305bbcfe719d56c7d53
                  MATRIX: 980->1|1093->39|1107->46|1187->50|1214->52|1223->54|1290->101|1314->106|1325->110|1405->114|1432->115|1567->225|1594->232|1622->233|1669->36|1697->103|1726->241|1755->244|1765->246
                  LINES: 28->1|32->3|32->3|34->3|35->4|35->4|35->4|36->7|36->7|38->7|39->8|40->9|40->9|41->10|43->1|45->5|47->11|49->13|49->13
                  -- GENERATED --
              */
          