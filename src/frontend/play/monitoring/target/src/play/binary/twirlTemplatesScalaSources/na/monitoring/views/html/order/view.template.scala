
package na.monitoring.views.html.order

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.monitoring.models.order.Order
/*2.2*/import na.monitoring.views.html.skeletons.mainSkel
/*3.2*/import na.naportalbase.views.tags.i18n
/*4.2*/import play.libs.Json
/*5.2*/import na.monitoring.settings.Features
/*6.2*/import na.monitoring.views.tags.feature

object view extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[Order,String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*8.2*/(order: Order, orderItemId: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*10.2*/validation/*10.12*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*10.16*/("""
    """),format.raw/*11.5*/("""<button class="tab tab--grouped" disabled role="tab" data-group="validation">
        <div class="tab-status">
            <div class="bullet"></div>
        </div>
        <div class="tab-icon">
            <i class="far fa-file-alt"></i>
        </div>
        <div class="tab-label">
            <x-i18n key="na.portal.monitoring.order.status.validation"></x-i18n>
        </div>
    </button>
""")))};def /*24.2*/execution/*24.11*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*24.15*/("""
    """),format.raw/*25.5*/("""<button class="tab tab--grouped" disabled role="tab" data-group="execution">
        <div class="tab-status">
            <div class="bullet"></div>
        </div>
        <div class="tab-icon">
            <i class="glyphicon glyphicon-play-circle"></i>
        </div>
        <div class="tab-label">
            <x-i18n key="na.portal.monitoring.order.status.execution"></x-i18n>
        </div>
    </button>
""")))};def /*38.2*/response/*38.10*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*38.14*/("""
    """),format.raw/*39.5*/("""<button class="tab tab--grouped" disabled role="tab" data-group="response">
        <div class="tab-status">
            <div class="bullet"></div>
        </div>
        <div class="tab-icon">
            <i class="glyphicon glyphicon-comment"></i>
        </div>
        <div class="tab-label">
            <x-i18n key="na.portal.monitoring.order.status.response"></x-i18n>
        </div>
    </button>
""")))};def /*52.2*/notifications/*52.15*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*52.19*/("""
    """),format.raw/*53.5*/("""<button class="tab tab--ungrouped" disabled role="tab" data-group="notifications">
        <div class="tab-icon">
            <i class="fa fa-envelope"></i>
        </div>
        <div class="tab-label">
            <x-i18n key="na.portal.monitoring.order.status.notifications"></x-i18n>
        </div>
    </button>
""")))};def /*71.2*/validationContent/*71.19*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*71.23*/("""

    """),format.raw/*73.5*/("""<div class="validation-page fx-full-height" data-group="validation" data-na-portal-monitoring-order-view-validation-page data-store="validationStore">
        <div class="monitoring-order-view-validation__page_header" style="display: inline-block;">
            <br/>
            <div class="status-diagram pull-left"></div>
            <div class="pull-left" style="padding-left: 0px; width: 200px">
                <label>"""),_display_(/*78.25*/i18n("na.portal.monitoring.order.validation.response.label.response")),format.raw/*78.94*/("""</label>
            </div>
            <div class='col-xs-8 validation-page__status pull-left'></div>
        </div>
        <div class="tabs tabs--monitoring-order-view validation-page__content" >
            <ul class="nav nav-tabs nav-stacked nav-tabs-stacked-left pull-left" style="width: 200px;">
                <li class="navigation-tab active">
                    <a href="#tab-validation-request-detail" class="tab-validation-request-detail tab-detail fx-tab" data-toggle="tab">"""),_display_(/*85.137*/i18n("na.portal.monitoring.order.modal.tab.request")),format.raw/*85.189*/("""</a>
                </li>
                <li class="navigation-tab">
                    <a href="#tab-validation-response-detail" class="tab-validation-response-detail tab-detail fx-tab" data-toggle="tab">"""),_display_(/*88.139*/i18n("na.portal.monitoring.order.modal.tab.response")),format.raw/*88.192*/("""</a>
                </li>
            </ul>
            <div class="tab-content nav-tabs-stacked-left-content validation-content fx-full-height">
                <section id="tab-validation-request-detail" class="tab-validation-request-detail tab-pane active fx-full-height">
                    <div class="validation-page__request"></div>
                </section>
                <section id="tab-validation-response-detail" class="tab-validation-response-detail tab-pane fx-full-height">
                    <div class="pull-right toolbar">
                        <div class="btn-group" id="view-types-btn">
                            <button class="btn btn-default btn-sm active" data-target="table" title=""""),_display_(/*98.103*/i18n("na.portal.monitoring.order.response.tooltip.seeTable")),format.raw/*98.163*/("""">
                                <i class="fa fa-table"></i>
                            </button>
                            <button class="btn btn-default btn-sm" data-target="code" title=""""),_display_(/*101.95*/i18n("na.portal.monitoring.order.response.tooltip.seeCode")),format.raw/*101.154*/("""">
                                <i class="fa fa-code"></i>
                            </button>
                        </div>
                    </div>
                    <div class="content--order-view-validation-response-detail">
                        <div data-container="table">
                            <table id="validationErrorMessages"
                            data-na-portal-table-datatable
                            data-na-portal-monitoring-order-view-validation-datatable
                            data-config-url=""""),_display_(/*111.47*/na/*111.49*/.monitoring.controllers.routes.TableController.dataTableValidationMessages()),format.raw/*111.125*/(""""
                            data-store="validationStore"
                            ></table>
                        </div>
                        <div class="validation-page__response" data-container="code"></div>
                    </div>
                </section>
            </div>
        </div>
    </div>

""")))};def /*124.2*/executionContent/*124.18*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*124.22*/("""

    """),format.raw/*126.5*/("""<div data-group="execution" class="page page--monitoring-order-execution fx-full-height" data-na-portal-monitoring-order-execution-page>
        <div class="monitoring-order-view__execution-sidebar" data-na-portal-monitoring-order-sidebar-page data-order-item-id=""""),_display_(/*127.129*/orderItemId),format.raw/*127.140*/(""""></div>
        <div class="monitoring-order-view__execution-info">
            <div class="monitoring-order-view__execution-details-table-container">
                <table id="execution-details-table" class="monitoring-order-view__execution-details-table table """),_display_(/*130.114*/feature(Features.OPERATION_ATTRIBUTES_VIEW)/*130.157*/{_display_(Seq[Any](format.raw/*130.158*/("""table--view-attributes""")))}),format.raw/*130.181*/("""">
                    <thead>
                        <tr>
                            <th>"""),_display_(/*133.34*/i18n("na.portal.monitoring.order.item.resume.table.operation.category")),format.raw/*133.105*/("""</th>
                            <th>"""),_display_(/*134.34*/i18n("na.portal.monitoring.order.item.resume.table.service.id")),format.raw/*134.97*/("""</th>
                            <th>"""),_display_(/*135.34*/i18n("na.portal.monitoring.order.item.resume.table.state")),format.raw/*135.92*/("""</th>
                            <th>"""),_display_(/*136.34*/i18n("na.portal.monitoring.order.item.resume.table.duration")),format.raw/*136.95*/("""</th>
                            <th>"""),_display_(/*137.34*/i18n("na.portal.monitoring.order.item.resume.table.end.date")),format.raw/*137.95*/("""</th>
                            <th>"""),_display_(/*138.34*/i18n("na.portal.monitoring.order.item.resume.table.has.process")),format.raw/*138.98*/("""</th>
                            <th>"""),_display_(/*139.34*/i18n("na.portal.monitoring.order.item.resume.table.actions")),format.raw/*139.94*/("""</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="data">
                            <td class="operationCategory"></td>
                            <td class="serviceId"></td>
                            <td class="state"></td>
                            <td class="duration"></td>
                            <td class="endDate"></td>
                            <td class="hasProcess"></td>
                            <td class="actions"></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <x-splitter class="monitoring-order-view__execution-item-splitter">
                <span class="fx-info-icon" slot="collapsed-separator-icon" style="margin: 10px">
                    <i class="glyphicon glyphicon-play-circle"></i>
                </span>
                <span class="fx-info-icon" slot="inverse-collapsed-separator-icon" style="margin: 10px">
                    <i class="fuxicons fuxicons-resources"></i>
                </span>
                <div id="timeline-pane" class="pane pane--monitoring-timeline-pane" slot="left">
                    <div class="timeline" data-na-portal-monitoring-execution-timeline data-na-portal-monitoring-order-execution-activity-manager></div>
                </div>
                <div slot="right" style="min-width: 300px">
                    <x-activity-graph id="graph" store="Monitoring - Order View Execution Graph Store"
                             class="graph graph--monitoring-order-view graph--loading"
                              data-na-portal-monitoring-execution-graph
                              data-na-portal-monitoring-order-execution-activity-manager>
                    </x-activity-graph>
                </div>
            </x-splitter>
        </div>
    </div>
""")))};def /*176.2*/responseContent/*176.17*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*176.21*/("""
    """),format.raw/*177.5*/("""<div class="monitoring-order-view__response-page fx-full-height" data-group="response" data-na-portal-monitoring-order-view-response-page data-store="responseStore">

        <div class="monitoring-order-view__page_header">
            <div class="pull-left">
                <label class="response-page__response-label"><b>"""),_display_(/*181.66*/i18n("na.portal.monitoring.order.response.label.response")),format.raw/*181.124*/("""</b></label>
            </div>
            <div class="pull-right">
                <label>"""),_display_(/*184.25*/i18n("na.portal.monitoring.order.response.label.destination")),format.raw/*184.86*/("""</label>
                <span class="response-page__destination"></span>
            </div>
            <br/>
            <div class="clearfix">
                <div class="monitoring-order-view__page_status"></div>
            </div>
        </div>
        """),format.raw/*192.26*/("""
        """),format.raw/*193.9*/("""<div class="tabs tabs--monitoring-order-view fx-full-height">
            <ul class="nav nav-tabs nav-stacked nav-tabs-stacked-left pull-left">
                <li class="active">
                    <a href="#tab-response-detail" class="tab-response-detail fx-tab" data-toggle="tab">"""),_display_(/*196.106*/i18n("na.portal.monitoring.order.modal.tab.responseDetail")),format.raw/*196.165*/("""</a>
                </li>
                <li>
                    <a href="#tab-history" class="tab-history fx-tab response-page__history-tab" data-toggle="tab">"""),_display_(/*199.117*/i18n("na.portal.monitoring.order.modal.tab.history")),format.raw/*199.169*/("""</a>
                </li>
            </ul>
            <div class="tab-content nav-tabs-stacked-left-content tab-content--order-view-response-page">
                <section id="tab-response-detail" class="tab-response-detail tab-pane active "></section>
                <section id="tab-history" class="tab-history tab-pane">
                    <table id="response-retries-table" data-store="responseStore"
                           data-na-portal-table-datatable
                           data-config-url=""""),_display_(/*207.46*/na/*207.48*/.monitoring.controllers.routes.TableController.dataTableResponseRetries()),format.raw/*207.121*/(""""
                           data-na-portal-monitoring-order-view-response-retry-table
                    ></table>
                </section>
            </div>
        </div>

    </div>
""")))};def /*217.2*/notificationsContent/*217.22*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*217.26*/("""
    """),format.raw/*218.5*/("""<div class="monitoring-order-view__notification-page fx-full-height" data-group="notifications" data-store="notificationStore">
        <h6><strong>"""),_display_(/*219.22*/i18n("na.portal.monitoring.order.notification.details.table.logs_history")),format.raw/*219.96*/("""</strong></h6>
        <table id="notification-details-table" data-store="notificationStore"
        data-na-portal-table-datatable
        data-config-url=""""),_display_(/*222.27*/na/*222.29*/.monitoring.controllers.routes.TableController.dataTableNotificationDetails()),format.raw/*222.106*/(""""
        data-na-portal-monitoring-order-view-notification-details-table
        ></table>
    </div>
""")))};def /*63.2*/orderNameLabel/*63.16*/ = {{
    if(order.getCategory != null && !order.getCategory.isEmpty){
        order.getExternalOrderId + " - " +order.getCategory
    } else {
        order.getExternalOrderId
    }
}};
Seq[Any](format.raw/*8.37*/("""

"""),format.raw/*22.2*/("""

"""),format.raw/*36.2*/("""

"""),format.raw/*50.2*/("""

"""),format.raw/*61.2*/("""

"""),format.raw/*69.2*/("""

"""),format.raw/*122.2*/("""

"""),format.raw/*175.2*/("""
"""),format.raw/*215.2*/("""

"""),format.raw/*226.2*/("""

"""),_display_(/*228.2*/mainSkel()/*228.12*/ {_display_(Seq[Any](format.raw/*228.14*/("""
    """),format.raw/*229.5*/("""<div
        class="main-content-wrapper order-view fx-full-height page--order-view"
        data-na-portal-monitoring-order-view
        data-na-portal-monitoring-order-view-breadcrumb
        data-na-portal-monitoring-order-tabs
    >
        <script data-initial-store-content type="application/json">"""),_display_(/*235.69*/Json/*235.73*/.toJson(order)),format.raw/*235.87*/("""</script>

        <div class="header page__header">
            """),format.raw/*238.29*/("""
            """),format.raw/*239.13*/("""<div class="summary summary--monitoring-order-view fx-constraint two-rows pull-left">
                """),format.raw/*240.27*/("""
                """),format.raw/*241.17*/("""<span class="summary__icon-container fx-info-icon">
                <i class="summary__icon fuxicons fuxicons-reports"></i>
            </span>
                """),format.raw/*244.27*/("""
                """),format.raw/*245.17*/("""<div class="summary__info">
                    <div class="summary__info-title">
                        """),format.raw/*247.35*/("""
                        """),format.raw/*248.25*/("""<h1 class="summary__name" id="name">"""),_display_(/*248.62*/orderNameLabel),format.raw/*248.76*/("""</h1>
                        """),format.raw/*249.36*/("""
                        """),format.raw/*250.25*/("""<div>
                            <span id="state" class="summary__state filter-badge badge" data-state=""""),_display_(/*251.101*/order/*251.106*/.getState),format.raw/*251.115*/("""" data-na-portal-monitoring-order-state-badge></span>
                        </div>
                        <div class="order-actions-container" style="display: inline-block;">
                            <div class="dropdown-list--monitoring-order-view-actions" data-na-portal-monitoring-order-view-actions-dropdown style="display: inline-block;"></div>
                        </div>
                        <x-order-summary-button order-id=""""),_display_(/*256.60*/order/*256.65*/.getOrderId),format.raw/*256.76*/("""" style="margin-right: 8px; display: inline-block;"></x-order-summary-button>
                    </div>
                    """),format.raw/*258.38*/("""
                    """),format.raw/*259.21*/("""<h6 class="summary__description" id="description">"""),_display_(/*259.72*/order/*259.77*/.getDescription),format.raw/*259.92*/("""</h6>
                    """),format.raw/*260.40*/("""
                    """),format.raw/*261.21*/("""<div class="order-details">
                        <h6>
                            <i class="glyphicon glyphicon-time"></i> """),_display_(/*263.71*/i18n("na.portal.monitoring.orders.table.system.entry")),format.raw/*263.125*/(""" """),format.raw/*263.126*/("""- <strong id="systemEntry" data-na-portal-monitoring-date-formatter data-date-format="full"></strong> """),_display_(/*263.229*/i18n("na.portal.monitoring.order.from.system")),format.raw/*263.275*/(""" """),format.raw/*263.276*/("""<strong id="system">"""),_display_(/*263.297*/order/*263.302*/.getSystem),format.raw/*263.312*/("""</strong> |
                            """),_display_(/*264.30*/if(Option(order.getTechnician).isDefined)/*264.71*/{_display_(Seq[Any](format.raw/*264.72*/("""<i class="fa fa-user"></i>"""),_display_(/*264.99*/i18n("na.portal.monitoring.orders.table.technician")),format.raw/*264.151*/(""" """),format.raw/*264.152*/("""- <strong id="technician">"""),_display_(/*264.179*/order/*264.184*/.getTechnician),format.raw/*264.198*/("""</strong> |""")))}),format.raw/*264.210*/("""
                            """),_display_(/*265.30*/if(Option(order.getCustomer).isDefined)/*265.69*/{_display_(Seq[Any](format.raw/*265.70*/("""<i data-na-portal-monitoring-order-view-customer></i> <strong id="customer">"""),_display_(/*265.147*/order/*265.152*/.getCustomer),format.raw/*265.164*/("""</strong> |""")))}),format.raw/*265.176*/("""
                            """),format.raw/*266.29*/("""<strong id="elapsedTime" data-na-portal-monitoring-date-formatter data-date-format="short"></strong>
                        </h6>
                    </div>
                </div>
            </div>

            """),format.raw/*272.31*/("""
            """),format.raw/*273.13*/("""<div class="monitoring-order-view__tabs">
                """),_display_(/*274.18*/validation),format.raw/*274.28*/("""
                """),_display_(/*275.18*/execution),format.raw/*275.27*/("""
                """),_display_(/*276.18*/response),format.raw/*276.26*/("""
                """),_display_(/*277.18*/notifications),format.raw/*277.31*/("""
            """),format.raw/*278.13*/("""</div>
        </div>
        """),format.raw/*280.22*/("""
        """),format.raw/*281.9*/("""<div class="content page__content">
            """),_display_(/*282.14*/validationContent),format.raw/*282.31*/("""
            """),_display_(/*283.14*/executionContent),format.raw/*283.30*/("""
            """),_display_(/*284.14*/responseContent),format.raw/*284.29*/("""
            """),_display_(/*285.14*/notificationsContent),format.raw/*285.34*/("""
        """),format.raw/*286.9*/("""</div>
    </div>
""")))}))
      }
    }
  }

  def render(order:Order,orderItemId:String): play.twirl.api.HtmlFormat.Appendable = apply(order,orderItemId)

  def f:((Order,String) => play.twirl.api.HtmlFormat.Appendable) = (order,orderItemId) => apply(order,orderItemId)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 12:11:15 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/monitoring/target/TwirlSource/na/monitoring/views/order/view.scala.html
                  HASH: ec099fb12a99c12ce1b5496687cd548c1916ba08
                  MATRIX: 671->1|718->42|776->94|822->134|851->157|897->197|1246->239|1360->277|1379->287|1460->291|1492->296|1913->697|1931->706|2012->710|2044->715|2479->1130|2496->1138|2577->1142|2609->1147|3038->1556|3060->1569|3141->1573|3173->1578|3514->2100|3540->2117|3621->2121|3654->2127|4106->2552|4196->2621|4714->3111|4788->3163|5025->3372|5100->3425|5845->4142|5927->4202|6150->4397|6232->4456|6807->5003|6819->5005|6918->5081|7263->5405|7289->5421|7371->5425|7405->5431|7699->5696|7733->5707|8027->5972|8081->6015|8122->6016|8178->6039|8299->6132|8393->6203|8460->6242|8545->6305|8612->6344|8692->6402|8759->6441|8842->6502|8909->6541|8992->6602|9059->6641|9145->6705|9212->6744|9294->6804|11211->8699|11236->8714|11318->8718|11351->8723|11704->9048|11785->9106|11906->9199|11989->9260|12277->9536|12314->9545|12628->9830|12710->9889|12903->10053|12978->10105|13520->10619|13532->10621|13628->10694|13843->10888|13873->10908|13955->10912|13988->10917|14165->11066|14261->11140|14447->11298|14459->11300|14559->11377|14686->1899|14709->1913|14923->274|14952->694|14981->1127|15010->1553|15039->1896|15068->2097|15098->5402|15128->8697|15157->10885|15187->11481|15217->11484|15237->11494|15278->11496|15311->11501|15644->11806|15658->11810|15694->11824|15788->11905|15830->11918|15961->12030|16007->12047|16196->12217|16242->12234|16377->12350|16431->12375|16496->12412|16532->12426|16591->12467|16645->12492|16780->12598|16796->12603|16828->12612|17302->13058|17317->13063|17350->13074|17504->13216|17554->13237|17633->13288|17648->13293|17685->13308|17740->13353|17790->13374|17945->13501|18022->13555|18053->13556|18185->13659|18254->13705|18285->13706|18335->13727|18351->13732|18384->13742|18453->13783|18504->13824|18544->13825|18599->13852|18674->13904|18705->13905|18761->13932|18777->13937|18814->13951|18859->13963|18917->13993|18966->14032|19006->14033|19112->14110|19128->14115|19163->14127|19208->14139|19266->14168|19508->14399|19550->14412|19637->14471|19669->14481|19715->14499|19746->14508|19792->14526|19822->14534|19868->14552|19903->14565|19945->14578|20004->14621|20041->14630|20118->14679|20157->14696|20199->14710|20237->14726|20279->14740|20316->14755|20358->14769|20400->14789|20437->14798
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|34->8|38->10|38->10|40->10|41->11|52->24|52->24|54->24|55->25|66->38|66->38|68->38|69->39|80->52|80->52|82->52|83->53|91->71|91->71|93->71|95->73|100->78|100->78|107->85|107->85|110->88|110->88|120->98|120->98|123->101|123->101|133->111|133->111|133->111|144->124|144->124|146->124|148->126|149->127|149->127|152->130|152->130|152->130|152->130|155->133|155->133|156->134|156->134|157->135|157->135|158->136|158->136|159->137|159->137|160->138|160->138|161->139|161->139|197->176|197->176|199->176|200->177|204->181|204->181|207->184|207->184|215->192|216->193|219->196|219->196|222->199|222->199|230->207|230->207|230->207|238->217|238->217|240->217|241->218|242->219|242->219|245->222|245->222|245->222|249->63|249->63|256->8|258->22|260->36|262->50|264->61|266->69|268->122|270->175|271->215|273->226|275->228|275->228|275->228|276->229|282->235|282->235|282->235|285->238|286->239|287->240|288->241|291->244|292->245|294->247|295->248|295->248|295->248|296->249|297->250|298->251|298->251|298->251|303->256|303->256|303->256|305->258|306->259|306->259|306->259|306->259|307->260|308->261|310->263|310->263|310->263|310->263|310->263|310->263|310->263|310->263|310->263|311->264|311->264|311->264|311->264|311->264|311->264|311->264|311->264|311->264|311->264|312->265|312->265|312->265|312->265|312->265|312->265|312->265|313->266|319->272|320->273|321->274|321->274|322->275|322->275|323->276|323->276|324->277|324->277|325->278|327->280|328->281|329->282|329->282|330->283|330->283|331->284|331->284|332->285|332->285|333->286
                  -- GENERATED --
              */
          