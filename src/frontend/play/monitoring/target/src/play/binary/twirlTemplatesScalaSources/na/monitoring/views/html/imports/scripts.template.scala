
package na.monitoring.views.html.imports

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object scripts extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.1*/("""<script src=""""),_display_(/*1.15*/na/*1.17*/.monitoring.controllers.routes.Application.javascriptRoutes()),format.raw/*1.78*/(""""></script><script src='"""),_display_(/*1.103*/na/*1.105*/.monitoring.controllers.routes.Assets.versioned("basemodule/runtime.ef4cdaceb8028d495c4b.min.js")),format.raw/*1.202*/("""'></script><script src='"""),_display_(/*1.227*/na/*1.229*/.monitoring.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-fuxi.44e71963e917fd2be2ca.min.js")),format.raw/*1.345*/("""'></script><script src='"""),_display_(/*1.370*/na/*1.372*/.monitoring.controllers.routes.Assets.versioned("na-portal-assets/base-styles.9e693254edb056a100ad.min.js")),format.raw/*1.479*/("""'></script><script src='"""),_display_(/*1.504*/na/*1.506*/.monitoring.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-bootstrap.6f86c32db7bf4ccaf010.min.js")),format.raw/*1.627*/("""'></script><script src='"""),_display_(/*1.652*/na/*1.654*/.monitoring.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-selects.c845a1da27d2bbccf773.min.js")),format.raw/*1.773*/("""'></script><script src='"""),_display_(/*1.798*/na/*1.800*/.monitoring.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-std-utils.ab8ed0c92f51f5eb905e.min.js")),format.raw/*1.921*/("""'></script><script src='"""),_display_(/*1.946*/na/*1.948*/.monitoring.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-tables.bc173ec447d65947d31a.min.js")),format.raw/*1.1066*/("""'></script><script src='"""),_display_(/*1.1091*/na/*1.1093*/.monitoring.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-jquery.c69a6ab040df2ef632e6.min.js")),format.raw/*1.1211*/("""'></script><script src='"""),_display_(/*1.1236*/na/*1.1238*/.monitoring.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-graphs.ab132b76bf73cc9c0ceb.min.js")),format.raw/*1.1356*/("""'></script><script src='"""),_display_(/*1.1381*/na/*1.1383*/.monitoring.controllers.routes.Assets.versioned("na-portal-assets-vendors/common.a53001b54dbc3b115cc4.min.js")),format.raw/*1.1493*/("""'></script><script src='"""),_display_(/*1.1518*/na/*1.1520*/.monitoring.controllers.routes.Assets.versioned("utils/utils.88e56ee512f1a3af972d.min.js")),format.raw/*1.1610*/("""'></script><script src='"""),_display_(/*1.1635*/na/*1.1637*/.monitoring.controllers.routes.Assets.versioned("basemodule/basemodule.823a1b10758c828389cd.min.js")),format.raw/*1.1737*/("""'></script><script src='"""),_display_(/*1.1762*/na/*1.1764*/.monitoring.controllers.routes.Assets.versioned("monitoring/monitoring.72de71618de2271fe6d6.min.js")),format.raw/*1.1864*/("""'></script>"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:17 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/monitoring/target/TwirlSource/na/monitoring/views/imports/scripts.scala.html
                  HASH: 0a47d1b43ad46af24460e9f868c22a8695562e59
                  MATRIX: 1054->0|1094->14|1104->16|1185->77|1237->102|1248->104|1366->201|1418->226|1429->228|1566->344|1618->369|1629->371|1757->478|1809->503|1820->505|1962->626|2014->651|2025->653|2165->772|2217->797|2228->799|2370->920|2422->945|2433->947|2573->1065|2626->1090|2638->1092|2778->1210|2831->1235|2843->1237|2983->1355|3036->1380|3048->1382|3180->1492|3233->1517|3245->1519|3357->1609|3410->1634|3422->1636|3544->1736|3597->1761|3609->1763|3731->1863
                  LINES: 33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1
                  -- GENERATED --
              */
          