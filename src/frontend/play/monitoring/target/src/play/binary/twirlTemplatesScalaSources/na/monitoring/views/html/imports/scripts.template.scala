
package na.monitoring.views.html.imports

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object scripts extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.1*/("""<script src=""""),_display_(/*1.15*/na/*1.17*/.monitoring.controllers.routes.Application.javascriptRoutes()),format.raw/*1.78*/(""""></script>
<script src='"""),_display_(/*2.15*/na/*2.17*/.monitoring.controllers.routes.Assets.versioned("basemodule/runtime.js")),format.raw/*2.89*/("""'></script>
<script src='"""),_display_(/*3.15*/na/*3.17*/.monitoring.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-fuxi.js")),format.raw/*3.108*/("""'></script>
<script src='"""),_display_(/*4.15*/na/*4.17*/.monitoring.controllers.routes.Assets.versioned("na-portal-assets/base-styles.js")),format.raw/*4.99*/("""'></script>
<script src='"""),_display_(/*5.15*/na/*5.17*/.monitoring.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-bootstrap.js")),format.raw/*5.113*/("""'></script>
<script src='"""),_display_(/*6.15*/na/*6.17*/.monitoring.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-selects.js")),format.raw/*6.111*/("""'></script>
<script src='"""),_display_(/*7.15*/na/*7.17*/.monitoring.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-std-utils.js")),format.raw/*7.113*/("""'></script>
<script src='"""),_display_(/*8.15*/na/*8.17*/.monitoring.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-tables.js")),format.raw/*8.110*/("""'></script>
<script src='"""),_display_(/*9.15*/na/*9.17*/.monitoring.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-jquery.js")),format.raw/*9.110*/("""'></script>
<script src='"""),_display_(/*10.15*/na/*10.17*/.monitoring.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-graphs.js")),format.raw/*10.110*/("""'></script>
<script src='"""),_display_(/*11.15*/na/*11.17*/.monitoring.controllers.routes.Assets.versioned("na-portal-assets-vendors/common.js")),format.raw/*11.102*/("""'></script>
<script src='"""),_display_(/*12.15*/na/*12.17*/.monitoring.controllers.routes.Assets.versioned("utils/utils.js")),format.raw/*12.82*/("""'></script>
<script src='"""),_display_(/*13.15*/na/*13.17*/.monitoring.controllers.routes.Assets.versioned("basemodule/basemodule.js")),format.raw/*13.92*/("""'></script>
<script src='"""),_display_(/*14.15*/na/*14.17*/.monitoring.controllers.routes.Assets.versioned("monitoring/monitoring.js")),format.raw/*14.92*/("""'></script>

"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:27 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/monitoring/target/TwirlSource/na/monitoring/views/imports/scripts.scala.html
                  HASH: 1ab9c91dee834e49d980a72641b9c3541c267351
                  MATRIX: 1054->0|1094->14|1104->16|1185->77|1237->103|1247->105|1339->177|1391->203|1401->205|1513->296|1565->322|1575->324|1677->406|1729->432|1739->434|1856->530|1908->556|1918->558|2033->652|2085->678|2095->680|2212->776|2264->802|2274->804|2388->897|2440->923|2450->925|2564->1018|2617->1044|2628->1046|2743->1139|2796->1165|2807->1167|2914->1252|2967->1278|2978->1280|3064->1345|3117->1371|3128->1373|3224->1448|3277->1474|3288->1476|3384->1551
                  LINES: 33->1|33->1|33->1|33->1|34->2|34->2|34->2|35->3|35->3|35->3|36->4|36->4|36->4|37->5|37->5|37->5|38->6|38->6|38->6|39->7|39->7|39->7|40->8|40->8|40->8|41->9|41->9|41->9|42->10|42->10|42->10|43->11|43->11|43->11|44->12|44->12|44->12|45->13|45->13|45->13|46->14|46->14|46->14
                  -- GENERATED --
              */
          