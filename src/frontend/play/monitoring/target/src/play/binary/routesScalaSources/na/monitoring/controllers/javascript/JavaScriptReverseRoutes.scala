// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/monitoring/conf/monitoring.routes
// @DATE:Wed Jul 02 10:59:33 WEST 2025

import play.api.routing.JavaScriptReverseRoute


import _root_.controllers.Assets.Asset

// @LINE:2
package na.monitoring.controllers.javascript {

  // @LINE:14
  class ReverseAssets(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:14
    def versioned: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.Assets.versioned",
      """
        function(file1) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/assets/" + (""" + implicitly[play.api.mvc.PathBindable[Asset]].javascriptUnbind + """)("file", file1)})
        }
      """
    )
  
  }

  // @LINE:78
  class ReverseActivityController(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:80
    def restart: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.ActivityController.restart",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/activity/restart"})
        }
      """
    )
  
    // @LINE:78
    def skip: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.ActivityController.skip",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/activity/skip"})
        }
      """
    )
  
    // @LINE:79
    def rollback: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.ActivityController.rollback",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/activity/rollback"})
        }
      """
    )
  
    // @LINE:82
    def complete: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.ActivityController.complete",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/activity/complete"})
        }
      """
    )
  
    // @LINE:81
    def retry: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.ActivityController.retry",
      """
        function(isNewRequest0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/activity/retry" + _qS([(isNewRequest0 == null ? null : (""" + implicitly[play.api.mvc.QueryStringBindable[Boolean]].javascriptUnbind + """)("isNewRequest", isNewRequest0))])})
        }
      """
    )
  
  }

  // @LINE:23
  class ReverseTableController(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:26
    def dataTableDeliveryAttempts: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.TableController.dataTableDeliveryAttempts",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/table-configs/deliveryAttempts"})
        }
      """
    )
  
    // @LINE:25
    def dataTableResponseRetries: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.TableController.dataTableResponseRetries",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/table-configs/responseRetries"})
        }
      """
    )
  
    // @LINE:29
    def dataTableNotificationDetails: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.TableController.dataTableNotificationDetails",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/table-configs/notificationDetails"})
        }
      """
    )
  
    // @LINE:24
    def dataTableOperationServiceMonitoring: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.TableController.dataTableOperationServiceMonitoring",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/table-configs/operationServiceMonitoring"})
        }
      """
    )
  
    // @LINE:27
    def dataTableValidationMessages: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.TableController.dataTableValidationMessages",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/table-configs/validationMessages"})
        }
      """
    )
  
    // @LINE:23
    def dataTableOrderMonitoring: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.TableController.dataTableOrderMonitoring",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/table-configs/orderMonitoring"})
        }
      """
    )
  
    // @LINE:28
    def dataTableExternalSystemInteractionsHistory: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.TableController.dataTableExternalSystemInteractionsHistory",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/table-configs/external-system-history"})
        }
      """
    )
  
  }

  // @LINE:62
  class ReverseRequesterCallbackController(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:62
    def requesterCallback: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.RequesterCallbackController.requesterCallback",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/requestCallback"})
        }
      """
    )
  
  }

  // @LINE:2
  class ReverseApplication(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:11
    def getModuleAngularScripts: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.Application.getModuleAngularScripts",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/imports/jsscripts"})
        }
      """
    )
  
    // @LINE:2
    def goToHome: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.Application.goToHome",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/home"})
        }
      """
    )
  
    // @LINE:8
    def javascriptRoutes: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.Application.javascriptRoutes",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/assets/javascripts/routes"})
        }
      """
    )
  
    // @LINE:5
    def resume: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.Application.resume",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/resume"})
        }
      """
    )
  
  }

  // @LINE:17
  class ReverseOrderController(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:52
    def getOperationByVersionAndName: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.getOperationByVersionAndName",
      """
        function(version0,name1) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/operation-info/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version0)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("name", name1))})
        }
      """
    )
  
    // @LINE:44
    def getOrderSummary: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.getOrderSummary",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/view/summary"})
        }
      """
    )
  
    // @LINE:40
    def queryOrders: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.queryOrders",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/search/orders"})
        }
      """
    )
  
    // @LINE:53
    def getOperationsByVersion: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.getOperationsByVersion",
      """
        function(versionName0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/operationsByVersion" + _qS([(""" + implicitly[play.api.mvc.QueryStringBindable[String]].javascriptUnbind + """)("versionName", versionName0)])})
        }
      """
    )
  
    // @LINE:45
    def getSettings: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.getSettings",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/settings"})
        }
      """
    )
  
    // @LINE:46
    def getStates: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.getStates",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/states"})
        }
      """
    )
  
    // @LINE:59
    def cancelOrdersWithRollback: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.cancelOrdersWithRollback",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/actions/cancelWithRollback"})
        }
      """
    )
  
    // @LINE:51
    def getOperationById: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.getOperationById",
      """
        function(id0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/operation-info/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("id", id0))})
        }
      """
    )
  
    // @LINE:55
    def getGraph: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.getGraph",
      """
        function(workflowExternalId0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/graph" + _qS([(""" + implicitly[play.api.mvc.QueryStringBindable[String]].javascriptUnbind + """)("workflowExternalId", workflowExternalId0)])})
        }
      """
    )
  
    // @LINE:61
    def removeOrders: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.removeOrders",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/actions/remove"})
        }
      """
    )
  
    // @LINE:49
    def getFiltersAsJson: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.getFiltersAsJson",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/filters"})
        }
      """
    )
  
    // @LINE:58
    def rollBackOrders: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.rollBackOrders",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/actions/rollback"})
        }
      """
    )
  
    // @LINE:60
    def skipOrders: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.skipOrders",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/actions/skip"})
        }
      """
    )
  
    // @LINE:48
    def getSystemsWithUsers: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.getSystemsWithUsers",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/systemsUsers"})
        }
      """
    )
  
    // @LINE:39
    def createOrder: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.createOrder",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/create"})
        }
      """
    )
  
    // @LINE:56
    def cancelOrders: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.cancelOrders",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/actions/cancel"})
        }
      """
    )
  
    // @LINE:57
    def retryOrders: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.retryOrders",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/actions/retry"})
        }
      """
    )
  
    // @LINE:17
    def getAllowedOrderActions: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.getAllowedOrderActions",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/allowed-actions"})
        }
      """
    )
  
    // @LINE:54
    def getServiceTypes: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.getServiceTypes",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/serviceTypes"})
        }
      """
    )
  
    // @LINE:42
    def view: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.view",
      """
        function(orderId0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/view/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("orderId", orderId0))})
        }
      """
    )
  
    // @LINE:37
    def createPage: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.createPage",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/create"})
        }
      """
    )
  
    // @LINE:41
    def home: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.home",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders"})
        }
      """
    )
  
    // @LINE:47
    def getSystems: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.getSystems",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/systems"})
        }
      """
    )
  
    // @LINE:50
    def getOperations: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.getOperations",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/operations"})
        }
      """
    )
  
    // @LINE:43
    def getOrderById: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.getOrderById",
      """
        function(orderId0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/view/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("orderId", orderId0)) + "/info"})
        }
      """
    )
  
    // @LINE:38
    def clonePage: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.OrderController.clonePage",
      """
        function(orderId0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/orders/clone/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("orderId", orderId0))})
        }
      """
    )
  
  }

  // @LINE:69
  class ReverseNotificationController(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:70
    def retryNotificationBySubscriber: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.NotificationController.retryNotificationBySubscriber",
      """
        function(notificationId0,subscriberName1) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/notifications/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("notificationId", notificationId0)) + "/subscriber/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("subscriberName", subscriberName1)) + "/retry"})
        }
      """
    )
  
    // @LINE:71
    def retryNotification: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.NotificationController.retryNotification",
      """
        function(notificationId0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/notifications/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("notificationId", notificationId0)) + "/retry"})
        }
      """
    )
  
    // @LINE:69
    def getNotificationsByOrderId: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.monitoring.controllers.NotificationController.getNotificationsByOrderId",
      """
        function(orderId0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "monitoring/notifications/orderId/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("orderId", orderId0))})
        }
      """
    )
  
  }


}
