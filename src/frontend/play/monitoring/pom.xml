<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>pt.ptinovacao.na-portal</groupId>
        <artifactId>na-portal-frontend-play</artifactId>
        <version>100.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>na-portal-frontend-monitoring</artifactId>
    <packaging>jar</packaging>

    <properties>
        <skipGradleWrapper>true</skipGradleWrapper>
        <skipNpmTests>true</skipNpmTests>
    </properties>

    <!--
        These dependencies serve the purpose of quickly validating the frontend existing dependencies.
        It's important to note that the actual POM file that will be published to the Maven repository is generated by <PERSON>radle.
        You can find this POM file in the './target/publications/maven/pom-default.xml' directory.
        The dependencies listed in this POM file are derived from the generated POM file,
        which, in turn, is created based on the 'build.gradle' file where dependencies are explicitly declared.
        For consistency, it's essential to remember that whenever you add a dependency in Gradle,
        it must also be included in this POM file.
     -->
    <dependencies>
        <!-- ########################################## -->
        <!-- # NA PORTAL                              # -->
        <!-- ########################################## -->
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-frontend-basemodule</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-frontend-catalog-basemodule</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-frontend-router</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- ########################################## -->
        <!-- # NOSSIS FRAMEWORK                       # -->
        <!-- ########################################## -->
        <dependency>
            <groupId>pt.ptinovacao.nossis-frmwrk</groupId>
            <artifactId>exceptions</artifactId>
        </dependency>

        <!-- ########################################## -->
        <!-- # NOSSIS UI                              # -->
        <!-- ########################################## -->
        <dependency>
            <groupId>pt.ptinovacao.nossis-ui</groupId>
            <artifactId>nossis-ui-messages</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.typesafe.play</groupId>
                    <artifactId>play-logback_${version.org.scala.base}</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.nossis-ui</groupId>
            <artifactId>nossis-ui-security-adapter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.typesafe.play</groupId>
                    <artifactId>play-logback_${version.org.scala.base}</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- ########################################## -->
        <!-- # NOSSIS SECURITY                        # -->
        <!-- ########################################## -->
        <dependency>
            <groupId>pt.ptinovacao.nossis-security</groupId>
            <artifactId>nossis-security-provider-iam</artifactId>
            <scope>runtime</scope>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.typesafe.play</groupId>
                    <artifactId>play-logback_${version.org.scala.base}</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- ########################################## -->
        <!-- # NOSSIS INV                             # -->
        <!-- ########################################## -->
        <dependency>
            <groupId>pt.ptinovacao.nossis-inv</groupId>
            <artifactId>nossis-inv-kernel-catalog-client-api</artifactId>
        </dependency>

        <!-- ########################################## -->
        <!-- # NA SWE                                 # -->
        <!-- ########################################## -->
        <dependency>
            <groupId>pt.ptinovacao.na</groupId>
            <artifactId>na-swe-order-model</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.glassfish.hk2.external</groupId>
                    <artifactId>javax.inject</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na</groupId>
            <artifactId>na-order-followup-dto</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na</groupId>
            <artifactId>na-swe-dsl-external-actions-model</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na</groupId>
            <artifactId>na-swe-async-notifications-handling-model</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na</groupId>
            <artifactId>na-swe-external-systems-handling-model</artifactId>
        </dependency>

        <!-- ########################################## -->
        <!-- # NACOMMONS                              # -->
        <!-- ########################################## -->
        <dependency>
            <groupId>pt.ptinovacao.na-commons</groupId>
            <artifactId>na-commons-cluster-service-locator-core</artifactId>
        </dependency>

        <!-- ########################################## -->
        <!-- # ASF                                    # -->
        <!-- ########################################## -->
        <dependency>
            <groupId>pt.ptinovacao.asf</groupId>
            <artifactId>asf-cluster-api</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.asf</groupId>
            <artifactId>asf-cluster-api-enumerations</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.asf</groupId>
            <artifactId>asf-cluster-implementations-zookeeper</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.asf</groupId>
            <artifactId>asf-components-manager</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.asf</groupId>
            <artifactId>asf-model</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- ########################################## -->
        <!-- # EXTERNAL DEPENDENCIES                  # -->
        <!-- ########################################## -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <dependency>
            <groupId>com.typesafe</groupId>
            <artifactId>config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.typesafe.play</groupId>
            <artifactId>play_${version.org.scala.base}</artifactId>
        </dependency>
        <dependency>
            <groupId>com.typesafe.play</groupId>
            <artifactId>play-java_${version.org.scala.base}</artifactId>
        </dependency>
        <dependency>
            <groupId>com.typesafe.play</groupId>
            <artifactId>play-ws_${version.org.scala.base}</artifactId>
        </dependency>

        <dependency>
            <groupId>com.typesafe.play</groupId>
            <artifactId>twirl-api_${version.org.scala.base}</artifactId>
        </dependency>

        <dependency>
            <groupId>com.typesafe.play</groupId>
            <artifactId>play-docs_${version.org.scala.base}</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.inject</groupId>
            <artifactId>guice</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.inject</groupId>
            <artifactId>javax.inject</artifactId>
        </dependency>

        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
        </dependency>
        <dependency>
            <groupId>org.scala-lang.modules</groupId>
            <artifactId>scala-xml_${version.org.scala.base}</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <scope>runtime</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <configuration>
                    <!-- These are transitive dependencies with scope provided, but we need them on compile time.
                    This will be resolved in na-commons version 5.0.0 -->
                    <usedDependencies>
                        <dependency>pt.ptinovacao.asf:asf-components-manager</dependency>
                    </usedDependencies>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>