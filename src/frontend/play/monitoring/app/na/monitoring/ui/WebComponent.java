package na.monitoring.ui;

import play.twirl.api.Html;

import java.util.List;
import java.util.Map;

public abstract class WebComponent {

    private final String id;
    private final String dataField;
    private final I18n i18n;
    private final Boolean hidden;
    private final Map<String, String> dataAttributes;
    private final Action action;
    private final Object value;
    private final Boolean isRequired;
    private final List<String> classes;

    public WebComponent(String id) {
        this(id,
                null,
                null,
                false,
                null,
                Action.VIEW,
                null,
                false,
                null);
    }

    public WebComponent(String id,
                        String dataField,
                        I18n i18n,
                        Boolean hidden,
                        Map<String, String> dataAttributes,
                        Action action,
                        Object value,
                        Boolean isRequired,
                        List<String> classes) {
        this.id = id;
        this.dataField = dataField;
        this.i18n = i18n;
        this.hidden = hidden;
        this.dataAttributes = dataAttributes;
        this.action = action;
        this.value = value;
        this.isRequired = isRequired;
        this.classes = classes;
    }

    public String getId() {
        return id;
    }

    public String getDataField() {
        return dataField;
    }

    public I18n getI18n() {
        return i18n;
    }

    public boolean isHidden() {
        return hidden;
    }

    public Map<String, String> getDataAttributes() {
        return dataAttributes;
    }

    public Action getAction() {
        return action;
    }

    public Object getValue() {
        return value;
    }

    public boolean isRequired() {
        return isRequired;
    }

    public abstract Html render();

    public List<String> getClasses() {
        return classes;
    }
}