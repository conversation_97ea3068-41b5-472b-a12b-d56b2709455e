package na.monitoring.controllers;

import na.monitoring.services.NotificationsServices;
import na.monitoring.settings.AAAPIResources;
import na.naportalbase.settings.AAAPIAccess;
import play.libs.Json;
import play.libs.concurrent.HttpExecutionContext;
import play.mvc.Result;
import play.mvc.Results;
import pt.alticelabs.nossis.security.actions.Authenticated;
import pt.alticelabs.nossis.security.actions.Authorized;

import javax.inject.Inject;
import java.util.concurrent.CompletionStage;

import static play.mvc.Results.ok;

public class NotificationController {
    private final HttpExecutionContext ec;
    private final NotificationsServices notificationService;

    @Inject
    public NotificationController(HttpExecutionContext ec, NotificationsServices notificationService) {
        this.ec = ec;
        this.notificationService = notificationService;
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public CompletionStage<Result> getNotificationsByOrderId(String orderId) {
        return notificationService.queryNotificationsByOrderId(orderId).thenApplyAsync(notifications -> ok(Json.toJson(notifications)), ec.current());
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public CompletionStage<Result> retryNotificationBySubscriber(String notificationId, String subscriberName) {
        return notificationService.retryNotificationBySubscriberName(notificationId, subscriberName)
                .thenApply(Json::toJson)
                .thenApply(Results::ok);
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public CompletionStage<Result> retryNotification(String notificationId) {
        return notificationService.retryNotification(notificationId)
                .thenApply(Json::toJson)
                .thenApply(Results::ok);
    }

}
