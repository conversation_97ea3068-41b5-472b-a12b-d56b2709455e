package na.monitoring.controllers;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import na.monitoring.actions.Feature;
import na.monitoring.models.externalsystems.ExternalSystemsQuery;
import na.monitoring.models.operation.OperationQuery;
import na.monitoring.models.responses.ExternalSystemUsersResponse;
import na.monitoring.models.selection.CancelOrdersSelectionData;
import na.monitoring.models.selection.CancelOrdersWithRollbackSelectionData;
import na.monitoring.models.selection.RetryOrdersSelectionData;
import na.monitoring.models.selection.RollbackOrdersSelectionData;
import na.monitoring.models.selection.SkipOrdersSelectionData;
import na.monitoring.models.selection.RemoveOrdersData;
import na.monitoring.settings.Features;
import na.monitoring.settings.MonitoringServiceSettings;
import na.monitoring.services.OrdersServices;
import na.monitoring.services.backend.ExternalSystemsBackendService;
import na.monitoring.services.backend.OperationsBackendService;
import na.monitoring.settings.AAAPIResources;
import na.monitoring.settings.MonitoringConstants;
import na.monitoring.ui.I18n;
import na.monitoring.ui.filter.Filter;
import na.monitoring.ui.filter.implementations.BadgeFilter;
import na.monitoring.ui.filter.implementations.ListFilter;
import na.monitoring.ui.filter.implementations.TextFilter;
import na.naportalbase.models.tables.query.TableQuery;
import na.naportalbase.models.tables.response.TableResponse;
import na.naportalbase.settings.AAAPIAccess;
import na.naportalbase.settings.BaseConstants;
import na.naportalbase.utils.SecurityUtils;
import play.api.mvc.Call;
import play.libs.Json;
import play.libs.concurrent.HttpExecutionContext;
import play.mvc.Controller;
import play.mvc.Http;
import play.mvc.Result;
import play.mvc.Results;
import pt.alticelabs.nossis.security.Security;
import pt.alticelabs.nossis.security.actions.Authenticated;
import pt.alticelabs.nossis.security.actions.Authorized;
import pt.ptinovacao.na.swe.external.system.handling.model.dto.UserDTO;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

import static na.monitoring.services.Authorization.getListOfAllowedActions;
import static na.monitoring.services.Authorization.isAuthorizedToExecuteAnyOrderAction;

public class OrderController extends Controller {

    private static final play.Logger.ALogger LOGGER = play.Logger.of(MonitoringConstants.LOGGER_NAME);
    private static final String USERNAME_FIELD = "username";

    private final HttpExecutionContext ec;
    private final MonitoringServiceSettings serviceSettings;
    private final OrdersServices ordersServices;
    private final OperationsBackendService operationsService;
    private final ExternalSystemsBackendService externalSystemsService;

    @Inject
    public OrderController(HttpExecutionContext ec, MonitoringServiceSettings serviceSettings, OrdersServices ordersServices, OperationsBackendService operationsService, ExternalSystemsBackendService externalSystemsService) {
        this.ec = ec;
        this.serviceSettings = serviceSettings;
        this.ordersServices = ordersServices;
        this.operationsService = operationsService;
        this.externalSystemsService = externalSystemsService;
    }

    /**
     * Render the orders home page
     *
     * @return orders home page template
     */
    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public Result home() {

        LOGGER.info("Rendering orders home page...");

        // set cookie
        response().setHeader(CACHE_CONTROL, "no-cache, no-store, must-revalidate");
        response().setCookie(Http.Cookie.builder(
                        BaseConstants.CookieKeys.SESSION_ID,
                        Security.getSessionId(Http.Context.current()))
                .withSecure(true)
                .build()
        );

        // render page
        List<String> dateFilterBy = serviceSettings.getSearchDateFilterBy();
        return ok(na.monitoring.views.html.roots.orders.render(dateFilterBy, isAuthorizedToExecuteAnyOrderAction()));
    }

    /**
     * Render the order view page
     *
     * @return order view page template
     */
    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public CompletionStage<Result> view(final String orderId) {
        LOGGER.info("Rendering order view page...");

        final String orderItemId = request().getQueryString("orderItemId");

        return ordersServices.getOrderById(orderId, isAuthorizedToExecuteAnyOrderAction())
                .thenApplyAsync(order -> ok(na.monitoring.views.html.order.view.render(order, orderItemId)), ec.current());
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.X})
    @Feature(Features.ORDER_CREATE)
    public Result createPage() {
        return ok(na.monitoring.views.html.roots.orderCreate.render());
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.X})
    @Feature({Features.ORDER_CREATE, Features.ORDER_CLONE})
    public CompletionStage<Result> clonePage(final String id) {
        return ordersServices.checkOrderIdIsCloneable(id)
                .thenApplyAsync(order -> ok(na.monitoring.views.html.roots.orderClone.render(id)), ec.current());
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public CompletionStage<Result> getOrderById(final String orderId) {
        return ordersServices.getOrderById(orderId, isAuthorizedToExecuteAnyOrderAction())
                .thenApplyAsync(order -> ok(Json.toJson(order)), ec.current());
    }

    /**
     * Get order summary from BOT School API
     *
     * @return JSON response with order summary
     */
    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public CompletionStage<Result> getOrderSummary() {
        JsonNode requestBody = request().body().asJson();

        return ordersServices.getOrderSummaryWithData( requestBody)
                .thenApplyAsync(summary -> ok(Json.toJson(summary)), ec.current())
                .exceptionally(throwable -> {
                    LOGGER.error("Error generating order summary with data for order", throwable);
                    Map<String, String> errorMap = new HashMap<>();
                    errorMap.put("error", "Failed to generate order summary");
                    errorMap.put("message", throwable.getMessage());
                    return internalServerError(Json.toJson(errorMap));
                });
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public CompletionStage<Result> getGraph(final String workflowId) {
        return ordersServices.getWorkflow(workflowId).thenApplyAsync(graph -> ok(Json.toJson(graph)), ec.current());
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public CompletionStage<Result> getSettings() {
        LOGGER.info("Retrieving settings...");
        return CompletableFuture.completedFuture(ok(Json.toJson(serviceSettings.getFrontendSettings())));
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public CompletionStage<Result> getStates() {
        LOGGER.info("Retrieving states...");
        final String query = request().getQueryString("q");
        return CompletableFuture.completedFuture(ok(Json.toJson(serviceSettings.getStates(query))));
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public CompletionStage<Result> getSystems() {
        LOGGER.info("Retrieving systems...");
        JsonNode jsonBody = request().body().asJson();
        final ExternalSystemsQuery externalSystemsQuery = Json.fromJson(jsonBody, ExternalSystemsQuery.class);
        return externalSystemsService.searchFilterExternalSystemItems(externalSystemsQuery)
                .thenApply(Json::toJson)
                .thenApply(Results::ok);
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public CompletionStage<Result> getOperations() {
        JsonNode jsonBody = request().body().asJson();
        final OperationQuery operationQuery = Json.fromJson(jsonBody, OperationQuery.class);
        return operationsService.searchFilterOperationItems(operationQuery)
                .thenApply(Json::toJson)
                .thenApply(Results::ok);
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public CompletionStage<Result> getOperationsByVersion(String versionName) {
        return operationsService.searchOperationsByVersion(versionName)
                .thenApply(Json::toJson)
                .thenApply(Results::ok);
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public CompletionStage<Result> getOperationById(String id) {
        return operationsService.getOperationById(id)
                .thenApply(Json::toJson)
                .thenApply(Results::ok);
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public CompletionStage<Result> getOperationByVersionAndName(String version, String operationName) {
        return operationsService.getOperationByNameAndVersion(operationName, version)
                .thenApply(Json::toJson)
                .thenApply(Results::ok);
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public CompletionStage<Result> getServiceTypes() {
        JsonNode jsonBody = request().body().asJson();
        final OperationQuery operationQuery = Json.fromJson(jsonBody, OperationQuery.class);
        return operationsService.searchFilterServiceTypeItems(operationQuery)
                .thenApply(Json::toJson)
                .thenApply(Results::ok);
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public Result getAllowedOrderActions() {
        List<String> allowedOrderActions = getListOfAllowedActions();
        List<String> possibleActions = serviceSettings.getPossibleOrderActions();
        List<String> result = new ArrayList<>(allowedOrderActions);

        result.retainAll(possibleActions);

        return Results.ok(Json.toJson(result));
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public CompletionStage<Result> queryOrders() {
        final TableQuery<OrdersServices.OrderQueryParams> query = ordersServices.parseTableQuery(request().body().asJson());
        return ordersServices.queryOrders(query, isAuthorizedToExecuteAnyOrderAction())
                .thenApply(orders -> TableResponse.fromQueryAndResponse(query, orders.getEntries(), orders.getCount()))
                .thenApply(Json::toJson)
                .thenApply(Results::ok);
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public CompletionStage<Result> cancelOrders() {
        ObjectNode jsonBody = (ObjectNode) request().body().asJson();
        jsonBody.put(USERNAME_FIELD, SecurityUtils.getUsername());
        CancelOrdersSelectionData selectionData = Json.fromJson(jsonBody, CancelOrdersSelectionData.class);
        return ordersServices.cancelOrders(selectionData)
                .thenApply(Json::toJson)
                .thenApply(Results::ok);
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public CompletionStage<Result> skipOrders() {
        ObjectNode jsonBody = (ObjectNode) request().body().asJson();
        jsonBody.put(USERNAME_FIELD, SecurityUtils.getUsername());
        SkipOrdersSelectionData selectionData = Json.fromJson(jsonBody, SkipOrdersSelectionData.class);
        return ordersServices.skipOrders(selectionData)
                .thenApply(Json::toJson)
                .thenApply(Results::ok);
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public CompletionStage<Result> retryOrders() {
        ObjectNode jsonBody = (ObjectNode) request().body().asJson();
        jsonBody.put(USERNAME_FIELD, SecurityUtils.getUsername());
        RetryOrdersSelectionData selectionData = Json.fromJson(jsonBody, RetryOrdersSelectionData.class);
        return ordersServices.retryOrders(selectionData)
                .thenApply(Json::toJson)
                .thenApply(Results::ok);
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public CompletionStage<Result> rollBackOrders() {
        ObjectNode jsonBody = (ObjectNode) request().body().asJson();
        jsonBody.put(USERNAME_FIELD, SecurityUtils.getUsername());
        RollbackOrdersSelectionData selectionData = Json.fromJson(jsonBody, RollbackOrdersSelectionData.class);
        return ordersServices.rollbackOrders(selectionData)
                .thenApply(Json::toJson)
                .thenApply(Results::ok);
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public CompletionStage<Result> cancelOrdersWithRollback() {
        ObjectNode jsonBody = (ObjectNode) request().body().asJson();
        jsonBody.put(USERNAME_FIELD, SecurityUtils.getUsername());
        CancelOrdersWithRollbackSelectionData selectionData = Json.fromJson(jsonBody, CancelOrdersWithRollbackSelectionData.class);
        return ordersServices.cancelOrdersWithRollback(selectionData)
                .thenApply(Json::toJson)
                .thenApply(Results::ok);
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.D})
    public CompletionStage<Result> removeOrders() {
        ObjectNode jsonBody = (ObjectNode) request().body().asJson();
        jsonBody.put(USERNAME_FIELD, SecurityUtils.getUsername());
        RemoveOrdersData selectionData = Json.fromJson(jsonBody, RemoveOrdersData.class);
        return ordersServices.removeOrders(selectionData)
                .thenApply(Json::toJson)
                .thenApply(Results::ok);
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public CompletionStage<Result> getSystemsWithUsers() {
        return externalSystemsService.searchExternalSystemsWithUsers()
                .thenApply(Json::toJson)
                .thenApply(Results::ok);
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.X})
    @Feature(Features.ORDER_CREATE)
    public CompletionStage<Result> createOrder() {
        ObjectNode jsonBody = (ObjectNode) request().body().asJson();
        LOGGER.info(String.valueOf(jsonBody));
        String system = jsonBody.get("system").asText();
        String user = jsonBody.get("user").asText();
        JsonNode orderData = jsonBody.get("orderData");

        return externalSystemsService.getUsers()
                .thenApply((ExternalSystemUsersResponse users) -> {
                    Optional<UserDTO> optionalUser = users.getEntries().stream()
                            .filter(userDTO -> userDTO.getUsername().equals(user) && userDTO.getExternalSystem().equals(system))
                            .findFirst();

                    if (!optionalUser.isPresent()) {
                        return Results.notFound();
                    }

                    UserDTO userDTO = optionalUser.get();
                    ordersServices.createOrder(userDTO, orderData);
                    return Results.ok();
                });
    }

    @Authenticated
    @Authorized({AAAPIResources.MONITORING + AAAPIAccess.R})
    public Result getFiltersAsJson() {
        return ok(Json.toJson(getFilters()));
    }


    private List<Filter> getFilters() {

        final List<Filter> filters = new ArrayList<>();

        // state (badge filter)
        final Call getStatesRoute = na.monitoring.controllers.routes.OrderController.getStates();
        final BadgeFilter state = new BadgeFilter("filter-state",
                new I18n("na.portal.monitoring.home.filters.items.state"),
                "state",
                getStatesRoute.url(),
                getStatesRoute.method());

        // category (text filter)
        final TextFilter category = createTextFilter("filter-category",
                "na.portal.monitoring.home.filters.items.category",
                "category");

        // system (list filter)
        final Call getSystemsRoute = routes.OrderController.getSystems();
        final ListFilter system = createListFilter("filter-system",
                "na.portal.monitoring.home.filters.items.system",
                "system",
                getSystemsRoute);

        // operations (list filter)
        final Call getOperationsRoute = routes.OrderController.getOperations();
        final ListFilter operations = createListFilter("filter-operation",
                "na.portal.monitoring.home.filters.items.operation",
                "operation",
                getOperationsRoute);

        // operations (list filter)
        final Call getServiceTypesRoute = routes.OrderController.getServiceTypes();
        final ListFilter serviceType = createListFilter("filter-service-type",
                "na.portal.monitoring.home.filters.items.service.type",
                "serviceType",
                getServiceTypesRoute);

        // response code  (text filter)
        final TextFilter responseCode = createTextFilter("filter-response-code",
                "na.portal.monitoring.home.filters.items.response.code",
                "responseCode");

        // response message  (text filter)
        final TextFilter responseMessage = createTextFilter("filter-response-message",
                "na.portal.monitoring.home.filters.items.response.message",
                "responseMessage");

        filters.add(state);
        filters.add(category);
        filters.add(system);
        filters.add(operations);
        filters.add(serviceType);
        filters.add(responseCode);
        filters.add(responseMessage);

        return filters;
    }

    private static ListFilter createListFilter(String id, String i18nKey, String property, Call route) {
        return new ListFilter(id, new I18n(i18nKey), property, true, route.url(), route.method(), true);
    }

    private static TextFilter createTextFilter(String id, String i18nKey, String property) {
        return new TextFilter(id, new I18n(i18nKey), property, true);
    }
}