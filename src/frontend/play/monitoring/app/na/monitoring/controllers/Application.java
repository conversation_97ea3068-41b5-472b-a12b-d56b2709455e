package na.monitoring.controllers;

import play.mvc.Controller;
import play.mvc.Http;
import play.mvc.Result;
import play.routing.JavaScriptReverseRouter;
import pt.alticelabs.nossis.security.actions.Authenticated;

public class Application extends Controller {

    /**
     * Render the template scripts with every angular files imports
     *
     * @return angular scripts
     */
    @Authenticated
    public Result getModuleAngularScripts() {
        return ok(na.monitoring.views.html.imports.scripts.render());
    }

    /**
     * Render the resume template
     */
    @Authenticated
    public Result resume() {
        response().setHeader(CACHE_CONTROL, "no-cache, no-store, must-revalidate");
        return ok(na.monitoring.views.html.roots.resume.render());
    }

    @Authenticated
    public Result goToHome() {
        response().setHeader(CACHE_CONTROL, "no-cache, no-store, no-transform");
        return redirect(routes.OrderController.home());
    }

    /**
     * Instantiate the javascript object containing the module's routes.
     *
     * @return javascript object with routes
     */
    public Result javascriptRoutes() {
        return ok(JavaScriptReverseRouter.create("monitoringJsRoutes",
                routes.javascript.Application.goToHome(),
                routes.javascript.OrderController.home(),
                routes.javascript.OrderController.view(),
                routes.javascript.OrderController.createPage(),
                routes.javascript.OrderController.clonePage(),
                routes.javascript.OrderController.createOrder(),
                routes.javascript.OrderController.getOrderById(),
                routes.javascript.OrderController.getOrderSummary(),
                routes.javascript.OrderController.getOperations(),
                routes.javascript.OrderController.getOperationById(),
                routes.javascript.OrderController.getOperationsByVersion(),
                routes.javascript.OrderController.getOperationByVersionAndName(),
                routes.javascript.OrderController.getSystemsWithUsers(),
                routes.javascript.OrderController.getServiceTypes(),
                routes.javascript.OrderController.getFiltersAsJson(),
                routes.javascript.OrderController.getStates(),
                routes.javascript.OrderController.getSettings(),
                routes.javascript.OrderController.getGraph(),
                routes.javascript.OrderController.cancelOrders(),
                routes.javascript.OrderController.skipOrders(),
                routes.javascript.OrderController.retryOrders(),
                routes.javascript.OrderController.rollBackOrders(),
                routes.javascript.OrderController.cancelOrdersWithRollback(),
                routes.javascript.OrderController.getAllowedOrderActions(),
                routes.javascript.OrderController.removeOrders(),
                routes.javascript.NotificationController.getNotificationsByOrderId(),
                routes.javascript.NotificationController.retryNotificationBySubscriber(),
                routes.javascript.NotificationController.retryNotification(),
                routes.javascript.ActivityController.skip(),
                routes.javascript.ActivityController.rollback(),
                routes.javascript.ActivityController.restart(),
                routes.javascript.ActivityController.retry(),
                routes.javascript.ActivityController.complete(),
                routes.javascript.TableController.dataTableOperationServiceMonitoring(),
                routes.javascript.TableController.dataTableExternalSystemInteractionsHistory(),
                routes.javascript.TableController.dataTableResponseRetries(),
                routes.javascript.TableController.dataTableDeliveryAttempts(),
                routes.javascript.TableController.dataTableValidationMessages(),
                routes.javascript.TableController.dataTableNotificationDetails(),
                routes.javascript.Assets.versioned()
        )).as(Http.MimeTypes.JAVASCRIPT);
    }
}