package na.monitoring.services.backend;

import com.google.common.base.Stopwatch;
import na.monitoring.services.OperationsCatalogServiceLocator;
import na.monitoring.settings.MonitoringConstants;
import na.monitoring.utils.MonitoringExceptionCodes;
import na.naportalbase.exceptions.ExceptionCodes;
import na.naportalbase.services.ServicesHelper;
import na.naportalbase.services.ws.Request;
import pt.ptinovacao.asf.serviceregistry.api.CallInstance;
import pt.ptinovacao.asf.serviceregistry.api.ServiceInstance;
import pt.ptinovacao.asf.serviceregistry.api.ServiceInstanceGetter;
import pt.ptinovacao.nossis.exceptions.BusinessException;
import pt.ptinovacao.nossis.exceptions.FaultException;
import play.Logger.ALogger;

import javax.inject.Inject;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

public final class OperationsBackendServicesHelper {

    private static final ALogger LOGGER = play.Logger.of(MonitoringConstants.LOGGER_NAME);
    private final OperationsCatalogServiceLocator serviceLocator;
    private final ServicesHelper servicesHelper;

    @Inject
    public OperationsBackendServicesHelper(ServicesHelper servicesHelper,
                                           OperationsCatalogServiceLocator serviceLocator) {
        this.servicesHelper = servicesHelper;
        this.serviceLocator = serviceLocator;
    }

    public <T> CompletionStage<T> send(Function<Request<T>, CompletionStage<T>> fn, Request<T> request) {
        final Stopwatch stopwatch = initStopWatch();
        final ServiceInstanceGetter serviceInstanceGetter = serviceLocator.getServiceLocator().getServiceInstanceGetter();
        final ServiceInstance serviceInstance = serviceInstanceGetter.get();
        evalServiceInstance(serviceInstance);
        final CallInstance callInstance = serviceInstanceGetter.beginCall(serviceInstance);
        request.setUrl(getUrl(serviceInstance, request.getUrl()));
        logServiceLocationTime(stopwatch, serviceInstance);

        return fn.apply(request).handle((T o, Throwable throwable) -> handleError(serviceInstanceGetter, callInstance, o, throwable, stopwatch));
    }

    public <T> CompletionStage<T> sendGet(Request<T> request) {
        return send(servicesHelper::sendGet, request);
    }

    public <T> CompletionStage<T> sendPost(Request<T> request) {
        return send(servicesHelper::sendPost, request);
    }

    public <T> CompletionStage<T> sendPut(Request<T> request) {
        return send(servicesHelper::sendPut, request);
    }

    public CompletionStage<Boolean> sendDelete(Request<Boolean> request) {
        return send(servicesHelper::sendDelete, request);
    }

    private Stopwatch initStopWatch() {
        return LOGGER.isDebugEnabled() ? Stopwatch.createStarted() : null;
    }

    private void logServiceLocationTime(Stopwatch stopwatch, ServiceInstance serviceInstance) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("------------------------------------------------------------------------");
            LOGGER.debug("Service Location time :" + stopwatch.elapsed(TimeUnit.MILLISECONDS) + " ms (Resolved Service Instance" + serviceInstance);
            stopwatch.reset();
            stopwatch.start();
        }

    }

    private String getUrl(ServiceInstance serviceInstance, String uri) {
        return serviceInstance.protocol().name().toLowerCase() + "://" + serviceInstance.ip() + ":" + serviceInstance.port() + uri;
    }

    private void evalServiceInstance(ServiceInstance serviceInstance) {
        if (serviceInstance == null) {
            throw new BusinessException(MonitoringExceptionCodes.ERROR_RESOLVING_OPER_CAT_SERVICE);
        }
    }

    private <T> T handleError(ServiceInstanceGetter serviceInstanceGetter, CallInstance callInstance, T o, Throwable throwable, Stopwatch stopwatch) {
        boolean success = false;
        try {
            if (throwable == null) {
                success = true;
                return o;
            } else {
                Throwable cause = throwable.getCause();
                if (cause instanceof BusinessException) {
                    success = true;
                    throw (BusinessException) cause;
                } else if (cause instanceof FaultException) {
                    throw (FaultException) cause;
                } else {
                    throw new FaultException(ExceptionCodes.REST_EXCEPTION, cause);
                }
            }
        } finally {
            serviceInstanceGetter.endCall(callInstance, success);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("Request ExecutionTime : " + stopwatch.elapsed(TimeUnit.MILLISECONDS) + " ms");
                LOGGER.debug("------------------------------------------------------------------------");
                stopwatch.stop();
            }
        }
    }
}
