package na.monitoring.services.backend;

import com.fasterxml.jackson.databind.JsonNode;
import na.monitoring.settings.MonitoringServiceSettings;
import na.naportalbase.services.ServicesHelper;
import na.naportalbase.services.ws.RequestBuilder;
import na.naportalbase.services.ws.RequestBuilderFactory;
import play.mvc.Http;

import javax.inject.Inject;
import java.util.Base64;
import java.util.concurrent.CompletionStage;

public class ServiceOrderingApiBackendService {
    private final ServicesHelper servicesHelper;
    private final MonitoringServiceSettings serviceSettings;
    private final RequestBuilderFactory requestBuilderFactory;

    @Inject
    public ServiceOrderingApiBackendService(ServicesHelper ws,
                                            MonitoringServiceSettings serviceSettings,
                                            RequestBuilderFactory requestBuilderFactory){
        this.servicesHelper = ws;
        this.serviceSettings = serviceSettings;
        this.requestBuilderFactory = requestBuilderFactory;
    }

    public CompletionStage<JsonNode> createOrderWithBasicAuth(String user, String pass, JsonNode data) {
        String basicAuthToken = toBase64String(user+":"+pass);
        String basicAuthHeaderValue = "Basic " + basicAuthToken;

        RequestBuilder<JsonNode> builder = requestBuilderFactory.create()
                .setUrl(getServiceOrderEndpoint())
                .addHeader(Http.HeaderNames.AUTHORIZATION, basicAuthHeaderValue)
                .setPayloadAsJson(data)
                .responseAs(JsonNode.class);


        return servicesHelper.sendPost(builder.build());
    }

    private String toBase64String(String text){
        Base64.Encoder enc = Base64.getEncoder();
        return enc.encodeToString(text.getBytes());
    }

    private String getBaseEndpoint(){
        return String.format("http://%s:%s/ptinovacao/serviceOrderingManagement/", serviceSettings.getBackendSweHost(), serviceSettings.getBackendSwePort());
    }

    private String getServiceOrderEndpoint(){
        return getBaseEndpoint() + "serviceOrder";
    }




}
