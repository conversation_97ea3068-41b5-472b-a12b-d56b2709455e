package na.monitoring.services;

import na.monitoring.settings.MonitoringServiceSettings;
import pt.ptinovacao.asf.serviceregistry.api.ServiceInstance;
import pt.ptinovacao.asf.serviceregistry.api.ServiceInstanceGetter;
import na.catalog.basemodule.services.catalog.client.api.CatalogImplConfig;

import javax.inject.Inject;


public final class MonitoringOperationsConfig implements CatalogImplConfig {

    private final OperationsCatalogServiceLocator serviceLocator;

    private final MonitoringServiceSettings serviceSettings;

    @Inject
    public MonitoringOperationsConfig(MonitoringServiceSettings serviceSettings,
                                      OperationsCatalogServiceLocator serviceLocator) {
        this.serviceLocator = serviceLocator;
        this.serviceSettings = serviceSettings;
    }

    @Override
    public String getURLServiceCatalog(Long id, Long version) {
        ServiceInstanceGetter serviceInstanceGetter = serviceLocator.getServiceLocator().getServiceInstanceGetter();
        ServiceInstance serviceInstance = serviceInstanceGetter.get();
        final StringBuilder url = new StringBuilder()
                .append(serviceInstance.protocol().name().toLowerCase())
                .append("://")
                .append(serviceInstance.ip())
                .append(":")
                .append(serviceInstance.port())
                .append(serviceSettings.getOperationsServicesSuffix())
                .append("/catalog/entities-full/");

        if (id != null) {
            url.append(id).append("/");
        }

        if (version != null) {
            url.append(version);
        }

        return url.toString();
    }

    public String getURLServiceCatalog(Long id) {
        return getURLServiceCatalog(id, null);
    }

    public String getURLServiceCatalog() {
        return getURLServiceCatalog(null, null);
    }
}