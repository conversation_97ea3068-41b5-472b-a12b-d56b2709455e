package na.monitoring.services.backend;

import com.fasterxml.jackson.databind.node.ObjectNode;
import na.monitoring.models.adapters.OperationAdapter;
import na.monitoring.models.operation.OperationQuery;
import na.monitoring.models.operation.OperationWithAttributes;
import na.monitoring.models.order.Item;
import na.monitoring.models.responses.EntityResponse;
import na.monitoring.models.responses.OperationsResponse;
import na.monitoring.models.responses.OperationsWithAttributeResponse;
import na.monitoring.models.responses.VersionsResponse;
import na.monitoring.models.version.Version;
import na.monitoring.services.MonitoringCatEntityAPIServices;
import na.monitoring.settings.MonitoringConstants;
import na.monitoring.settings.MonitoringServiceSettings;
import na.monitoring.utils.MonitoringExceptionCodes;
import na.monitoring.utils.SearchPayLoadUtils;
import na.naportalbase.services.ws.RequestBuilderFactory;
import na.naportalbase.utils.ODataQueryParametersBuilder;
import play.libs.Json;
import play.mvc.Http;
import pt.ptinovacao.netwin.kernel.catalog.client.model.CatState;
import pt.ptinovacao.netwin.kernel.catalog.client.model.CatStateMachine;
import pt.ptinovacao.nossis.exceptions.BusinessException;

import javax.inject.Inject;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Castro <<EMAIL>>, 28/10/21.
 */
public class OperationsBackendService {

    private static final play.Logger.ALogger LOGGER = play.Logger.of(MonitoringConstants.LOGGER_NAME);

    private final MonitoringCatEntityAPIServices monitoringCatEntityAPIServices;

    private final SearchPayLoadUtils searchPayLoadUtils;

    private final OperationsBackendServicesHelper ws;

    private final MonitoringServiceSettings serviceSettings;

    private final OperationAdapter operationAdapter;

    private final RequestBuilderFactory requestBuilderFactory;

    @Inject
    public OperationsBackendService(SearchPayLoadUtils searchPayLoadUtils,
                                    MonitoringCatEntityAPIServices monitoringCatEntityAPIServices,
                                    OperationsBackendServicesHelper ws,
                                    MonitoringServiceSettings serviceSettings,
                                    OperationAdapter operationAdapter,
                                    RequestBuilderFactory requestBuilderFactory){
        this.monitoringCatEntityAPIServices = monitoringCatEntityAPIServices;
        this.searchPayLoadUtils = searchPayLoadUtils;
        this.ws = ws;
        this.serviceSettings = serviceSettings;
        this.operationAdapter = operationAdapter;
        this.requestBuilderFactory = requestBuilderFactory;
    }


    public CompletionStage<EntityResponse<Item>> searchFilterOperationItems(OperationQuery query) {
        return searchOperations(OperationSearchParam.NAME, query).thenApply(this::mapToNameItems);
    }


    public CompletionStage<EntityResponse<Item>> searchFilterServiceTypeItems(OperationQuery query) {
        return searchOperations(OperationSearchParam.ENTITY_NAME, query).thenApply(this::mapToEntityNameItems);
    }

    public CompletionStage<List<Version>> getVersions() {
        final CatStateMachine stateMachine = monitoringCatEntityAPIServices.getVersionStateCatStateMachine();
        final String language = Http.Context.current().lang().language();

        return ws.sendGet(requestBuilderFactory.create()
                        .setUrl(serviceSettings.getOperationsServicesSuffix() + "/versions")
                        .responseAs(VersionsResponse.class)
                        .build()
                )
                .thenApply(versions -> versions.getVersions().stream()
                        .map(version -> {
                            final CatState state = stateMachine.getState(version.getState());
                            return new Version(version.getName(), version.getState(), state.getI18n(language), state.getName());
                        })
                        .collect(Collectors.toList())
                );
    }

    public CompletionStage<OperationsResponse> searchOperationsByVersion(String versionName) {
        ODataQueryParametersBuilder builder = ODataQueryParametersBuilder.get()
                .orderBy(OperationSearchParam.NAME.getAttributeName() + " asc");

        return ws.sendPost(requestBuilderFactory.create()
                .setUrl(serviceSettings.getOperationsServicesSuffix() + "/search/operations")
                .addQueryParameters(builder.build())
                .setPayloadAsJson(searchPayLoadUtils.buildOperationByVersionSearchPayload(versionName))
                .responseAs(OperationsResponse.class)
                .build()
        ).thenApply(operations -> new OperationsResponse(
                        operationAdapter.adaptOperations(operations.getEntries())
                        ,operations.getCount()
                )
        );
    }

    public CompletionStage<OperationWithAttributes> getOperationById(String id) {

        ODataQueryParametersBuilder builder = ODataQueryParametersBuilder.get()
                .expand("operationAttributes," +
                        "tags," +
                        "operationAttributes/constraints," +
                        "operationAttributes/entityConstraints," +
                        "operationAttributes/entityConstraints/valueList,"+
                        "version");

        return ws.sendGet(requestBuilderFactory.create()
                .setUrl(serviceSettings.getOperationsServicesSuffix() + "/operations/"+id)
                .addQueryParameters(builder.build())
                .responseAs(OperationWithAttributes.class)
                .build()
        ).thenApply(operationAdapter::adaptOperation);
    }

    public CompletionStage<OperationWithAttributes> getOperationByNameAndVersion(String operationName, String versionName) {

        ODataQueryParametersBuilder builder = ODataQueryParametersBuilder.get()
                .expand("operationAttributes," +
                        "tags," +
                        "operationAttributes/constraints," +
                        "operationAttributes/entityConstraints," +
                        "operationAttributes/entityConstraints/valueList,"+
                        "version");

        return ws.sendPost(requestBuilderFactory.create()
                .setUrl(serviceSettings.getOperationsServicesSuffix() + "/search/operations")
                .addQueryParameters(builder.build())
                .setPayloadAsJson(searchPayLoadUtils.buildOperationByNameAndVersionSearchPayload(operationName, versionName))
                .responseAs(OperationsWithAttributeResponse.class)
                .build()
        ).thenApply(response -> {
            if(response.getCount() < 0 || response.getEntries().isEmpty()){
                throw new BusinessException(MonitoringExceptionCodes.OPERATION_NOT_FOUND, "operationName", operationName);
            }
            return operationAdapter.adaptOperation(response.getEntries().get(0));
        });
    }


    private EntityResponse<Item> mapToNameItems(OperationsResponse response){
        List<Item> adaptedEntries = response.getEntries().stream()
                .map(operation -> new Item(operation.getName(), operation.getName(), ""))
                .collect(Collectors.toList());
        return new EntityResponse<>(adaptedEntries, response.getCount());
    }

    private EntityResponse<Item> mapToEntityNameItems(OperationsResponse response){
        List<Item> adaptedEntries = response.getEntries().stream()
                .map(operation -> new Item(operation.getEntityName(), operation.getEntityName(), ""))
                .collect(Collectors.toList());
        return new EntityResponse<>(adaptedEntries, response.getCount());
    }



    private CompletionStage<OperationsResponse> searchOperations(OperationSearchParam param, OperationQuery query) {

        final Long cfsTypeId = monitoringCatEntityAPIServices.getCfsTypeId();
        final Long rfsTypeId = monitoringCatEntityAPIServices.getRfsTypeId();
        String attributeName = param.getAttributeName();

        ODataQueryParametersBuilder builder = ODataQueryParametersBuilder.get()
        .orderBy(attributeName+" asc")
        .select(attributeName);

        Map<String, Object> payloadMap = new HashMap<>();
        final List<String> values = query.getValues();
        final String textFilter = query.getQuery();
        LOGGER.info("values {}", values);
        LOGGER.info("textFilter {}", textFilter);
        if(values != null && !values.isEmpty()){
            payloadMap.put(attributeName, values);
            builder.top(values.size()).skip(0);
        } else if(textFilter != null && !textFilter.isEmpty()){
            payloadMap.put(attributeName, "*" + textFilter + "*");
            builder.top(query.getTop()).skip(query.getSkip());
        } else {
            builder.top(query.getTop()).skip(query.getSkip());
        }
        payloadMap.put("type", Arrays.asList(cfsTypeId, rfsTypeId));
        final ObjectNode searchPayload = searchPayLoadUtils.buildSearchPayload(Json.toJson(payloadMap), "OPERATION", true);
        return ws.sendPost(requestBuilderFactory.create()
            .setUrl(serviceSettings.getOperationsServicesSuffix() + "/search/operations")
            .addQueryParameters(builder.build())
            .setPayloadAsJson(searchPayload)
            .responseAs(OperationsResponse.class)
            .build()
        );
    }

    private enum OperationSearchParam {
        NAME("name"),
        ENTITY_NAME("entityName");

        final String attributeName;

        OperationSearchParam(String attributeName) {
            this.attributeName = attributeName;
        }

        public String getAttributeName() {
            return attributeName;
        }
    }


}
