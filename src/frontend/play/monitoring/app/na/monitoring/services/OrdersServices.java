package na.monitoring.services;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import na.monitoring.models.adapters.OrderPojoAdapter;
import na.monitoring.models.adapters.WorkflowAdapter;
import na.monitoring.models.order.Order;
import na.monitoring.models.responses.EntityResponse;
import na.monitoring.models.selection.CancelOrdersSelectionData;
import na.monitoring.models.selection.CancelOrdersWithRollbackSelectionData;
import na.monitoring.models.selection.MassiveOrderSelectionData;
import na.monitoring.models.selection.RetryOrdersSelectionData;
import na.monitoring.models.selection.RollbackOrdersSelectionData;
import na.monitoring.models.selection.SkipOrdersSelectionData;
import na.monitoring.models.selection.RemoveOrdersData;
import na.monitoring.services.backend.ActionsBackendService;
import na.monitoring.services.backend.ExternalSystemsBackendService;
import na.monitoring.services.backend.FollowupBackendService;
import na.monitoring.services.backend.ServiceOrderingApiBackendService;
import na.monitoring.settings.MonitoringServiceSettings;
import na.monitoring.utils.AllOrderRecordsIterable;
import na.monitoring.utils.MonitoringExceptionCodes;
import na.naportalbase.exceptions.ExceptionCodes;
import na.naportalbase.models.tables.query.TableQuery;
import na.naportalbase.utils.ODataBuilder;
import na.naportalbase.utils.ODataQueryParametersBuilder;
import org.jetbrains.annotations.NotNull;
import pt.ptinovacao.na.swe.dsl.external.actions.model.OrderActionRequest;
import pt.ptinovacao.na.swe.external.system.handling.model.dto.UserDTO;
import pt.ptinovacao.nossis.exceptions.BusinessException;
import pt.ptinovacao.nossis.exceptions.FaultException;
import play.libs.ws.WSClient;
import play.libs.ws.WSRequest;
import play.libs.Json;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR> Castro <<EMAIL>>, 14-06-2018.
 */
public class OrdersServices {
    private final FollowupBackendService backend;
    private final ActionsBackendService actionsBackend;
    private final OrderPojoAdapter orderPojoAdapter;
    private final ServiceOrderingApiBackendService serviceOrderingApiBackendService;
    private final MonitoringServiceSettings serviceSettings;
    private final ExternalSystemsBackendService externalSystemsBackendService;
    private final WSClient wsClient;

    @Inject
    public OrdersServices(
            FollowupBackendService backend,
            ActionsBackendService actionsBackend,
            OrderPojoAdapter orderPojoAdapter,
            ServiceOrderingApiBackendService serviceOrderingApiBackendService,
            MonitoringServiceSettings serviceSettings,
            ExternalSystemsBackendService externalSystemsBackendService,
            WSClient wsClient
    ) {
        this.backend = backend;
        this.actionsBackend = actionsBackend;
        this.orderPojoAdapter = orderPojoAdapter;
        this.serviceOrderingApiBackendService = serviceOrderingApiBackendService;
        this.serviceSettings = serviceSettings;
        this.externalSystemsBackendService = externalSystemsBackendService;
        this.wsClient = wsClient;
    }

    public CompletionStage<Order> getOrderById(String orderId, Boolean actionsPermission) {
        final ODataBuilder oDataBuilder = new ODataBuilder();
        oDataBuilder.addTextField("orderId", orderId, ODataBuilder.FilterOperation.EQUAL);
        List<String> possibleActions = serviceSettings.getPossibleOrderActions();
        List<String> allowedActions = Authorization.filterAuthorizedActions(possibleActions);


        return backend.queryOrders(ODataQueryParametersBuilder.get()
                        .filter(oDataBuilder)
                        .build())
                .thenApply(orderPojoResponse -> {
                    final Optional<Order> order;
                    try {
                        order = orderPojoResponse.getEntries().stream()
                                .map(orderDTO -> orderPojoAdapter.toOrder(orderDTO, actionsPermission, Collections.emptyList(), allowedActions))
                                .findFirst();
                    } catch (Exception e) {
                        throw new FaultException(ExceptionCodes.UNKNOWN_EXCEPTION, new Throwable("Error while parsing response from rest service - " + e.getMessage()));
                    }
                    return order.orElseThrow(() -> new BusinessException(MonitoringExceptionCodes.ORDER_NOT_FOUND, "orderId", orderId));
                });
    }

    public CompletionStage<Order> checkOrderIdIsCloneable(String orderId) {
        final ODataBuilder oDataBuilder = new ODataBuilder();
        oDataBuilder.addTextField("orderId", orderId, ODataBuilder.FilterOperation.EQUAL);
        List<String> possibleActions = serviceSettings.getPossibleOrderActions();
        List<String> allowedActions = Authorization.filterAuthorizedActions(possibleActions);

        return backend.queryOrders(ODataQueryParametersBuilder.get()
                        .filter(oDataBuilder)
                        .build())
                .thenCombine(externalSystemsBackendService.searchExternalSystemsWithUsers(),  (orderPojoResponse, externalSystemItems) -> {
                    final Optional<Order> optionalOrder;
                    try {
                        optionalOrder = orderPojoResponse.getEntries().stream()
                                .map(orderDTO -> orderPojoAdapter.toOrder(orderDTO, false, externalSystemItems, allowedActions))
                                .findFirst();
                    } catch (Exception e) {
                        throw new FaultException(ExceptionCodes.UNKNOWN_EXCEPTION, new Throwable("Error while parsing response from rest service - " + e.getMessage()));
                    }
                    final Order order = optionalOrder.orElseThrow(() -> new BusinessException(MonitoringExceptionCodes.ORDER_NOT_FOUND, "orderId", orderId));
                    if(!Boolean.TRUE.equals(order.getCloneable())){
                        throw new BusinessException(MonitoringExceptionCodes.CANNOT_CLONE_ORDER, "orderId", orderId);
                    }
                    return order;
                });
    }

    public CompletionStage<Map<String, Object>> getWorkflow(final String workflowId) {
        List<String> allowedActions = Authorization.getListOfAllowedActions();

        return backend.getWorkflow(workflowId)
                .thenApply(workflow -> WorkflowAdapter.adapt(workflow, allowedActions));
    }

    public TableQuery<OrderQueryParams> parseTableQuery(JsonNode json) {
        return TableQuery.fromJson(json, OrderQueryParams.class);
    }

    public CompletionStage<JsonNode> createOrder(UserDTO user, JsonNode data) {
        String requesterCallback = serviceSettings.getRequesterCallback();
        ObjectNode objectNode = (ObjectNode) data;
        objectNode.put("requesterCallback", requesterCallback);
        if(objectNode.hasNonNull("orderItem")){
            ArrayNode orderItems = (ArrayNode) objectNode.get("orderItem");
            for (JsonNode element : orderItems){
                ((ObjectNode) element).put("id", UUID.randomUUID().toString());
            }
        }
        return serviceOrderingApiBackendService.createOrderWithBasicAuth(user.getUsername(), user.getPassword(), data);
    }


    private void searchOrdersFilterBy(Map<String, Object> parameters, ODataBuilder oDataBuilder) {

        String filterBy = parameters.getOrDefault("filterBy", "").toString();

        String dateField;
        switch (filterBy) {
            case "lastUpdate":
                dateField = "lastUpdate";
                break;
            case "systemEntry":
            default:
                dateField = "startTime";
        }

        if (!parameters.getOrDefault("startDate", "").toString().isEmpty()) {
            oDataBuilder.addDateField(dateField, parameters.get("startDate").toString(), ODataBuilder.FilterOperation.GREATER_EQUAL);
        }

        if (!parameters.getOrDefault("endDate", "").toString().isEmpty()) {
            oDataBuilder.addDateField(dateField, parameters.get("endDate").toString(), ODataBuilder.FilterOperation.LESSER_EQUAL);
        }
    }

    private void completeODataBuilder(ODataBuilder oDataBuilder, Map<String, Object> parameters) {

        if (!parameters.getOrDefault("serviceId", "").toString().isEmpty()) {
            oDataBuilder.addTextFieldWithWildcard("serviceIds", parameters.get("serviceId").toString(), true);
        }

        searchOrdersFilterBy(parameters, oDataBuilder);

        if (parameters.get("category") != null) {
            String categories = (String) parameters.get("category");
            if (!categories.isEmpty()) {
                oDataBuilder.addTextFieldWithWildcard("internal/category", categories, true);
            }
        }

        if (parameters.get("state") != null) {
            List<String> categories = (List<String>) parameters.get("state");
            if (!categories.isEmpty()) {
                oDataBuilder.addTextField("internal/state", categories, ODataBuilder.FilterOperation.NEW_IN);
            }
        }

        if (parameters.get("system") != null) {
            List<String> categories = (List<String>) parameters.get("system");
            if (!categories.isEmpty()) {
                oDataBuilder.addTextField("system", categories, ODataBuilder.FilterOperation.NEW_IN);
            }
        }

        if (parameters.get("operation") != null) {
            List<String> operations = (List<String>) parameters.get("operation");
            if (!operations.isEmpty()) {
                oDataBuilder.addTextField("internal/orderProcesses/action", operations, ODataBuilder.FilterOperation.NEW_IN);
            }
        }

        if (parameters.get("serviceType") != null) {
            List<String> serviceTypes = (List<String>) parameters.get("serviceType");
            if (!serviceTypes.isEmpty()) {
                oDataBuilder.addTextField("internal/orderProcesses/name", serviceTypes, ODataBuilder.FilterOperation.NEW_IN);
            }
        }

        if (parameters.get("responseCode") != null) {
            String responseCode = (String) parameters.get("responseCode");
            if (!responseCode.isEmpty()) {
                oDataBuilder.addTextFieldWithWildcard("internal/orderProcesses/statusMessage/code", responseCode, true);
            }
        }

        if (parameters.get("responseMessage") != null) {
            String responseMessage = (String) parameters.get("responseMessage");
            if (!responseMessage.isEmpty()) {
                oDataBuilder.addTextFieldWithWildcard("internal/orderProcesses/statusMessage/message", responseMessage, true);
            }
        }
    }

    private ODataBuilder tableQueryToOdataFilter(TableQuery<OrderQueryParams> query) {
        return tableQueryToOdataFilter(query.getParameters());
    }

    private ODataBuilder tableQueryToOdataFilter(Map<String, Object> parameters) {
        final ODataBuilder oDataBuilder = new ODataBuilder();

        if (!parameters.getOrDefault("orderId", "").toString().isEmpty()) {
            oDataBuilder.addTextFieldWithWildcard("externalId", parameters.get("orderId").toString(), true);
        }

        completeODataBuilder(oDataBuilder, parameters);

        return oDataBuilder;
    }

    private ODataBuilder tableQueryToOdataFilter(MassiveOrderSelectionData selectionData) {
        Map<String, Object> parameters = selectionData.getFilterAttributes();
        parameters.put("ignoreOrdersId", selectionData.getIgnoreOrdersAttr().stream()
                .map(MassiveOrderSelectionData.OrderSelectionData::getOrderId)
                .collect(Collectors.toList()));

        final ODataBuilder oDataBuilder = new ODataBuilder();

        if (parameters.get("ignoreOrdersId") != null) {
            List<String> orderIds = (List<String>) parameters.get("ignoreOrdersId");
            if (!orderIds.isEmpty()) {
                oDataBuilder.addTextField("orderId", orderIds, ODataBuilder.FilterOperation.NIN);
            }
        }

        completeODataBuilder(oDataBuilder, parameters);

        return oDataBuilder;
    }

    public CompletionStage<Boolean> execMassiveAction(
            MassiveOrderSelectionData selectionData,
            Function<List<String>, CompletionStage<Boolean>> actionToExecute
    ) {
        final int bulkSize = 1000;
        final List<String> list = new ArrayList<>(bulkSize);
        final List<CompletableFuture<Boolean>> requests = new ArrayList<>();
        final Iterator<String> orderIdIter = orderIdIterator(selectionData);
        while (orderIdIter.hasNext()) {
            final String orderId = orderIdIter.next();
            list.add(orderId);
            if (list.size() >= bulkSize) {
                final CompletableFuture<Boolean> future = actionToExecute.apply(list)
                        .toCompletableFuture();
                requests.add(future);
                list.clear();
            }
        }
        if (!list.isEmpty()) {
            final CompletableFuture<Boolean> future = actionToExecute.apply(list)
                    .toCompletableFuture();
            requests.add(future);
        }

        return CompletableFuture.allOf(requests.toArray(new CompletableFuture[0]))
                .thenApply(aVoid -> true);

    }

    public CompletionStage<Boolean> cancelOrders(CancelOrdersSelectionData selectionData) {

        final String username = selectionData.getUsername();
        final String reason = selectionData.getReason();
        final Long timestamp = selectionData.getTimestamp();

        return execMassiveAction(selectionData, (orderIds -> actionsBackend.cancel(new OrderActionRequest(username, reason, timestamp, orderIds))));
    }

    public CompletionStage<Boolean> skipOrders(SkipOrdersSelectionData selectionData) {

        final String username = selectionData.getUsername();
        final String reason = selectionData.getReason();
        final Long timestamp = selectionData.getTimestamp();

        return execMassiveAction(selectionData, (orderIds -> actionsBackend.skipOrders(new OrderActionRequest(username, reason, timestamp, orderIds))));
    }

    public CompletionStage<Boolean> retryOrders(RetryOrdersSelectionData selectionData) {

        final String username = selectionData.getUsername();
        final String reason = selectionData.getReason();
        final Long timestamp = selectionData.getTimestamp();

        return execMassiveAction(selectionData, (orderIds -> actionsBackend.retryOrders(new OrderActionRequest(username, reason, timestamp, orderIds))));
    }

    public CompletionStage<Boolean> rollbackOrders(RollbackOrdersSelectionData selectionData) {

        final String username = selectionData.getUsername();
        final String reason = selectionData.getReason();
        final Long timestamp = selectionData.getTimestamp();

        return execMassiveAction(selectionData, (orderIds -> actionsBackend.rollbackOrders(new OrderActionRequest(username, reason, timestamp, orderIds))));
    }

    public CompletionStage<Boolean> cancelOrdersWithRollback(CancelOrdersWithRollbackSelectionData selectionData) {

        final String username = selectionData.getUsername();
        final String reason = selectionData.getReason();
        final Long timestamp = selectionData.getTimestamp();

        return execMassiveAction(selectionData, (orderIds -> actionsBackend.cancelOrdersWithRollback(new OrderActionRequest(username, reason, timestamp, orderIds))));
    }

    public CompletionStage<Boolean> removeOrders(RemoveOrdersData selectionData) {

        final String username = selectionData.getUsername();
        final String reason = selectionData.getReason();
        final Long timestamp = selectionData.getTimestamp();

        return execMassiveAction(selectionData, (orderIds -> actionsBackend.removeOrders(new OrderActionRequest(username, reason, timestamp, orderIds))));
    }

    public CompletionStage<EntityResponse<Order>> queryOrders(TableQuery<OrderQueryParams> query, boolean actionsPermission) {
        List<String> possibleActions = serviceSettings.getPossibleOrderActions();
        List<String> allowedActions = Authorization.filterAuthorizedActions(possibleActions);

        final ODataQueryParametersBuilder queryParametersBuilder = ODataQueryParametersBuilder.get()
                .top(query)
                .skip(query)
                .inlinecount(ODataQueryParametersBuilder.InlineCount.ALL_PAGES)
                .select("orderId")
                .select("externalId")
                .select("internal")
                .select("startTime")
                .select("endTime")
                .select("lastUpdate")
                .select("system")
                .filter(tableQueryToOdataFilter(query));

        query.getSorts().entrySet().stream().map(entry -> {
            String property = entry.getKey();
            String direction = entry.getValue().shortVersion().toLowerCase();
            if (Objects.equals("systemEntry", property)) {
                property = "startTime";
            }
            return property + " " + direction;
        }).reduce((acc, sort) -> acc + "," + sort).ifPresent(queryParametersBuilder::orderBy);

        return backend.queryOrders(queryParametersBuilder.build())
                .thenCombine(externalSystemsBackendService.searchExternalSystemsWithUsers(), (orderPojoResponse, externalSystemItems) -> {
                    try {
                        List<Order> orders = orderPojoResponse.getEntries().stream()
                                .map(orderDTO -> orderPojoAdapter.toOrder(orderDTO, actionsPermission, externalSystemItems, allowedActions))
                                .collect(Collectors.toList());
                        return new EntityResponse<>(orders, orderPojoResponse.getCount());
                    } catch (Exception e1) {
                        throw new FaultException(ExceptionCodes.UNKNOWN_EXCEPTION, new Throwable("Error while parsing response from rest service - " + e1.getMessage()));
                    }
                });
    }

    private Iterator<String> orderIdIterator(MassiveOrderSelectionData selectionData) {
        switch (selectionData.getType()) {
            case MASSIVE_SELECTED:
                return orderIdIteratorSelectedRecords(selectionData);
            case MASSIVE_ALL_FROM_FILTER:
                return orderIdIteratorAllRecords(selectionData);
        }
        throw new RuntimeException("unreachable code");
    }

    private Iterator<String> orderIdIteratorSelectedRecords(MassiveOrderSelectionData selectionData) {
        return selectionData.getOrdersAttributes().stream()
                .map(MassiveOrderSelectionData.OrderSelectionData::getOrderId)
                .iterator();
    }

    private Iterator<String> orderIdIteratorAllRecords(MassiveOrderSelectionData selectionData) {
        final String builder = tableQueryToOdataFilter(selectionData).build();
        return new AllOrderRecordsIterable(builder, backend).streamOrderIds().iterator();
    }

    @NotNull
    private static String getPrompt(String orderData, String x) {
        String escapedOrderData = orderData
                .replace("\"", "\\\"")
                .replace("\n", "\\n")
                .replace("\r", "\\r");

        return x + escapedOrderData;
    }

    /**
     * Generate order summary using provided order data (to avoid redundant Order Follow-up calls)
     *
     * @param orderData the order data from the frontend
     * @return CompletionStage with summary response
     */
    public CompletionStage<Map<String, Object>> getOrderSummaryWithData(JsonNode orderData) {
        String orderDataJson = orderData.toString();

        String botSchoolUrl = "https://api.ng.botschool.ai/rest-interface/chat-llms?apikey=1e24e0f4ea214780846a6c00d777c3d5";
        String prompt = getPrompt(orderDataJson, "I am going to give you details of a Orchestration Service order in JSON. Please summarize it into a short paragraph in plain text suitable for a manager, using a maximum of 200 words. Ensure the text is formatted using <strong> tags for the order ID, externalId and serviceId and <br> tags for new lines. Write the whole text in english language and format the summary like this example: 'The order <strong>TT-2024164-000037783</strong>, with externalId <strong>Test</strong>, for serviceId <strong> test</strong> was created on June 12, 2024 and took 1 minute to get to current state closed, on June 13, 2024. It has 2 order items: cfs.1 is completed and cfs.2 had an error' Here are the details for the service order: ");

        ObjectNode requestBody = Json.newObject();
        requestBody.put("message", prompt);

        WSRequest request = wsClient.url(botSchoolUrl)
                .setHeader("Content-Type", "application/json")
                .setRequestTimeout(java.time.Duration.ofSeconds(60));
        
        return request.post(requestBody)
                .thenApply(botResponse -> {
                    JsonNode botResponseJson = botResponse.asJson();
                    String summary = botResponseJson.path("response").asText();
                    
                    Map<String, Object> result = new HashMap<>();
                    result.put("summary", summary);
                    result.put("success", true);
                    
                    return result;
                })
                .exceptionally(throwable -> {
                    Map<String, Object> errorResult = new HashMap<>();
                    errorResult.put("error", "Failed to generate order summary: " + throwable.getMessage());
                    errorResult.put("success", false);
                    return errorResult;
                });
    }

    public static class OrderQueryParams extends HashMap<String, Object> {
    }


}