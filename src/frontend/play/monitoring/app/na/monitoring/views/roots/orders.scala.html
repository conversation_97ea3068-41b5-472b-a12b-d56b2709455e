@import na.monitoring.settings.{AAAPIResources, Features}
@import na.monitoring.views.html.skeletons.mainSkel
@import na.monitoring.views.tags.feature
@import na.naportalbase.views.html.components.selectsComp.dynamicSelectOptionComp
@import na.naportalbase.views.tags.i18n
@import pt.alticelabs.nossis.security.views.html.authorized
@(searchDateFilterByOptions: List[String], showMassiveActions: Boolean)


@filterBySelectData = @{
    var stringBuilder = new StringBuilder
    for(option <- searchDateFilterByOptions) {
        var optionAttributes = scala.collection.immutable.HashMap[String,String](
            "value" -> option
        )
        stringBuilder.append(dynamicSelectOptionComp.render(i18n(s"na.portal.monitoring.search.date.filterby.${option}"),optionAttributes))
    }
    Html(stringBuilder.toString())
}

@filterBySelectAttrs = @{
    var map = scala.collection.immutable.HashMap[String, String](
        "id" -> "filter-by-select-box",
        "data-na-portal-select-box" -> null,
        "data-na-portal-monitoring-order-listing-search-date-filter-by-select-box" -> null
    )
    map
}

@header() = {
    <div id="entity-header" class="fx-entity-header">

        <div id="header-fields" class="fx-entity-header-inner" data-view="search-form">
            @* fields *@
            <div class="filters-view in" data-view="search-by-filters">
                @* filters *@
                <div id="filters-col">
                    <div data-na-portal-monitoring-order-listing-filter-list data-parent-container-selector=".fx-entity-header-inner" data-store="filterStore"></div>
                </div>
                @* add fields *@
                <div id="add-filters-col">
                    <div>
                        <a id="SwitchToSearchByIdView" data-page-action="SwitchToSearchByIdView">@i18n("na.portal.monitoring.home.filters.search.by.id")</a>
                    </div>
                    <div class="filter-configuration" data-na-portal-monitoring-order-listing-filter-list-manager data-label='@i18n("na.portal.monitoring.home.filters.add.fields")' data-store="filterStore"></div>
                </div>
            </div>

            @* search by id *@
            <div class="search-by-id-view" data-na-portal-monitoring-id-search-form data-submit-action="submitAction" data-store="idSearchStore" data-view="search-by-id">
                <div class="row">
                    <div class="col-sm-12">
                        <div data-na-portal-monitoring-id-search-form-tabs data-store="idSearchStore" class="pull-left"></div>
                        <a id="SwitchToFiltersView" data-page-action="SwitchToFiltersView" class="pull-right"> @i18n("na.portal.monitoring.home.filters.search.with.multiple.fields") </a>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12">
                        <input id="id-search-input" title="" class="form-control input-sm" type="text">
                    </div>
                </div>
            </div>
        </div>


        @* search expressions *@
        <div data-view="search-expression">
            <div class="row" data-view="search-by-filters">
                @* filters *@
                <div class="filterExpression col-sm-12">
                    <div id="filterExpression" data-na-portal-monitoring-order-listing-filter-expression data-store="filterStore"></div>
                </div>
            </div>
            <div class="row" data-view="search-by-id">
                    @* filters *@
                <div class="filterExpression col-sm-12">
                    <div data-na-portal-monitoring-order-listing-filter-expression data-store="idSearchStore"></div>
                </div>
            </div>
        </div>

        @* search *@
        <div id="header-search" class="fx-search-actions-container clearfix">
            @* left *@
            <div class="pull-left">
                <form class="form-inline">
                    <div class="pull-left" style="margin-right: 10px">
                        <label> @i18n("na.portal.monitoring.search.date.filterby") </label>
                        @na.naportalbase.views.html.components.selectsComp.selectComp.render(filterBySelectAttrs,null,null,filterBySelectData)
                    </div>
                    <div
                        id="search-date"
                        class="form-group"
                        data-na-portal-monitoring-daterangepicker
                        data-on-change="onRangeChange"
                        data-initial-range="initialRange"
                    >
                        <div id="search-date-range">
                            <i class="fa fa-calendar"></i>
                            <input id="daterangepicker" title="" type="text" autocomplete="off"> <i class="fa fa-caret-down"></i>
                        </div>
                        <i id="daterangepicker-message">@i18n("na.portal.monitoring.home.filters.need.to.refresh")</i>
                        <i class="glyphicon glyphicon-repeat"></i>
                    </div>
                </form>
            </div>
            @* right *@
            <div class="pull-right">
                <button class="btn btn-sm btn-primary" data-page-action="search">@i18n("na.buttons.search")</button>
                <button class="btn btn-default btn-sm" data-page-action="clear">@i18n("na.buttons.clear")</button>
            </div>
        </div>
    </div>
}

@horizontalSplitter() = {
    <div id="horizontal-splitter" class="ui-layout-pane">
        <div class="toggler">
            <a role="button">
                <span class="ui-layout-toggler-north"></span>
            </a>
        </div>
    </div>
}

@mainSkel() {
    <div class="main-content-wrapper page page--monitoring-order-search" data-na-portal-monitoring-order-listing-page data-na-portal-monitoring-order-search-breadcrumb>
        @* blue header *@
        <div id="header-info" class="monitoring-header">
            <h1>@i18n("na.portal.monitoring.home.title")</h1>
            @feature(Features.ORDER_CREATE){
            @authorized(AAAPIResources.monitoring.X.toString()){
                <button class="btn btn-default button button--monitoring-order-create">
                    <i class="glyphicon glyphicon-plus"></i>
                    <x-i18n key="na.portal.monitoring.order.create.label"></x-i18n>
                </button>
            }
            }
        </div>
        <div>
            @* header *@
            <div id="header" data-na-portal-monitoring-order-listing-header data-store="headerStore">
                @header
                @horizontalSplitter
            </div>

            @* content *@
            <div id="splitter-content">
                <div id="entity-content">
                    @if( showMassiveActions ){
                        <div data-na-portal-monitoring-orders-massive-actions-dropdown></div>
                        <div data-na-portal-monitoring-orders-select-all-from-filter-notification class="notification--select-all-from-filter"></div>
                    }
                    <table
                        id="order-listing-table"
                        data-na-portal-table
                        data-na-portal-table-datatable
                        data-na-portal-table-load-using-ajax
                        data-config-url="@na.monitoring.controllers.routes.TableController.dataTableOrderMonitoring()"
                        data-na-portal-monitoring-order-listing-table
                        data-na-portal-monitoring-order-listing-table-store="orderListingTable"
                    ></table>
                </div>
            </div>
        </div>
    </div>
}