@import na.monitoring.models.order.Order
@import na.monitoring.views.html.skeletons.mainSkel
@import na.naportalbase.views.tags.i18n
@import play.libs.Json
@import na.monitoring.settings.Features
@import na.monitoring.views.tags.feature

@(order: Order, orderItemId: String)

@validation = {
    <button class="tab tab--grouped" disabled role="tab" data-group="validation">
        <div class="tab-status">
            <div class="bullet"></div>
        </div>
        <div class="tab-icon">
            <i class="far fa-file-alt"></i>
        </div>
        <div class="tab-label">
            <x-i18n key="na.portal.monitoring.order.status.validation"></x-i18n>
        </div>
    </button>
}

@execution = {
    <button class="tab tab--grouped" disabled role="tab" data-group="execution">
        <div class="tab-status">
            <div class="bullet"></div>
        </div>
        <div class="tab-icon">
            <i class="glyphicon glyphicon-play-circle"></i>
        </div>
        <div class="tab-label">
            <x-i18n key="na.portal.monitoring.order.status.execution"></x-i18n>
        </div>
    </button>
}

@response = {
    <button class="tab tab--grouped" disabled role="tab" data-group="response">
        <div class="tab-status">
            <div class="bullet"></div>
        </div>
        <div class="tab-icon">
            <i class="glyphicon glyphicon-comment"></i>
        </div>
        <div class="tab-label">
            <x-i18n key="na.portal.monitoring.order.status.response"></x-i18n>
        </div>
    </button>
}

@notifications = {
    <button class="tab tab--ungrouped" disabled role="tab" data-group="notifications">
        <div class="tab-icon">
            <i class="fa fa-envelope"></i>
        </div>
        <div class="tab-label">
            <x-i18n key="na.portal.monitoring.order.status.notifications"></x-i18n>
        </div>
    </button>
}

@orderNameLabel = @{
    if(order.getCategory != null && !order.getCategory.isEmpty){
        order.getExternalOrderId + " - " +order.getCategory
    } else {
        order.getExternalOrderId
    }
}

@validationContent = {

    <div class="validation-page fx-full-height" data-group="validation" data-na-portal-monitoring-order-view-validation-page data-store="validationStore">
        <div class="monitoring-order-view-validation__page_header" style="display: inline-block;">
            <br/>
            <div class="status-diagram pull-left"></div>
            <div class="pull-left" style="padding-left: 0px; width: 200px">
                <label>@i18n("na.portal.monitoring.order.validation.response.label.response")</label>
            </div>
            <div class='col-xs-8 validation-page__status pull-left'></div>
        </div>
        <div class="tabs tabs--monitoring-order-view validation-page__content" >
            <ul class="nav nav-tabs nav-stacked nav-tabs-stacked-left pull-left" style="width: 200px;">
                <li class="navigation-tab active">
                    <a href="#tab-validation-request-detail" class="tab-validation-request-detail tab-detail fx-tab" data-toggle="tab">@i18n("na.portal.monitoring.order.modal.tab.request")</a>
                </li>
                <li class="navigation-tab">
                    <a href="#tab-validation-response-detail" class="tab-validation-response-detail tab-detail fx-tab" data-toggle="tab">@i18n("na.portal.monitoring.order.modal.tab.response")</a>
                </li>
            </ul>
            <div class="tab-content nav-tabs-stacked-left-content validation-content fx-full-height">
                <section id="tab-validation-request-detail" class="tab-validation-request-detail tab-pane active fx-full-height">
                    <div class="validation-page__request"></div>
                </section>
                <section id="tab-validation-response-detail" class="tab-validation-response-detail tab-pane fx-full-height">
                    <div class="pull-right toolbar">
                        <div class="btn-group" id="view-types-btn">
                            <button class="btn btn-default btn-sm active" data-target="table" title="@i18n("na.portal.monitoring.order.response.tooltip.seeTable")">
                                <i class="fa fa-table"></i>
                            </button>
                            <button class="btn btn-default btn-sm" data-target="code" title="@i18n("na.portal.monitoring.order.response.tooltip.seeCode")">
                                <i class="fa fa-code"></i>
                            </button>
                        </div>
                    </div>
                    <div class="content--order-view-validation-response-detail">
                        <div data-container="table">
                            <table id="validationErrorMessages"
                            data-na-portal-table-datatable
                            data-na-portal-monitoring-order-view-validation-datatable
                            data-config-url="@na.monitoring.controllers.routes.TableController.dataTableValidationMessages()"
                            data-store="validationStore"
                            ></table>
                        </div>
                        <div class="validation-page__response" data-container="code"></div>
                    </div>
                </section>
            </div>
        </div>
    </div>

}

@executionContent = {

    <div data-group="execution" class="page page--monitoring-order-execution fx-full-height" data-na-portal-monitoring-order-execution-page>
        <div class="monitoring-order-view__execution-sidebar" data-na-portal-monitoring-order-sidebar-page data-order-item-id="@orderItemId"></div>
        <div class="monitoring-order-view__execution-info">
            <div class="monitoring-order-view__execution-details-table-container">
                <table id="execution-details-table" class="monitoring-order-view__execution-details-table table @feature(Features.OPERATION_ATTRIBUTES_VIEW){table--view-attributes}">
                    <thead>
                        <tr>
                            <th>@i18n("na.portal.monitoring.order.item.resume.table.operation.category")</th>
                            <th>@i18n("na.portal.monitoring.order.item.resume.table.service.id")</th>
                            <th>@i18n("na.portal.monitoring.order.item.resume.table.state")</th>
                            <th>@i18n("na.portal.monitoring.order.item.resume.table.duration")</th>
                            <th>@i18n("na.portal.monitoring.order.item.resume.table.end.date")</th>
                            <th>@i18n("na.portal.monitoring.order.item.resume.table.has.process")</th>
                            <th>@i18n("na.portal.monitoring.order.item.resume.table.actions")</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="data">
                            <td class="operationCategory"></td>
                            <td class="serviceId"></td>
                            <td class="state"></td>
                            <td class="duration"></td>
                            <td class="endDate"></td>
                            <td class="hasProcess"></td>
                            <td class="actions"></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <x-splitter class="monitoring-order-view__execution-item-splitter">
                <span class="fx-info-icon" slot="collapsed-separator-icon" style="margin: 10px">
                    <i class="glyphicon glyphicon-play-circle"></i>
                </span>
                <span class="fx-info-icon" slot="inverse-collapsed-separator-icon" style="margin: 10px">
                    <i class="fuxicons fuxicons-resources"></i>
                </span>
                <div id="timeline-pane" class="pane pane--monitoring-timeline-pane" slot="left">
                    <div class="timeline" data-na-portal-monitoring-execution-timeline data-na-portal-monitoring-order-execution-activity-manager></div>
                </div>
                <div slot="right" style="min-width: 300px">
                    <x-activity-graph id="graph" store="Monitoring - Order View Execution Graph Store"
                             class="graph graph--monitoring-order-view graph--loading"
                              data-na-portal-monitoring-execution-graph
                              data-na-portal-monitoring-order-execution-activity-manager>
                    </x-activity-graph>
                </div>
            </x-splitter>
        </div>
    </div>
}
@responseContent = {
    <div class="monitoring-order-view__response-page fx-full-height" data-group="response" data-na-portal-monitoring-order-view-response-page data-store="responseStore">

        <div class="monitoring-order-view__page_header">
            <div class="pull-left">
                <label class="response-page__response-label"><b>@i18n("na.portal.monitoring.order.response.label.response")</b></label>
            </div>
            <div class="pull-right">
                <label>@i18n("na.portal.monitoring.order.response.label.destination")</label>
                <span class="response-page__destination"></span>
            </div>
            <br/>
            <div class="clearfix">
                <div class="monitoring-order-view__page_status"></div>
            </div>
        </div>
        @* starts here *@
        <div class="tabs tabs--monitoring-order-view fx-full-height">
            <ul class="nav nav-tabs nav-stacked nav-tabs-stacked-left pull-left">
                <li class="active">
                    <a href="#tab-response-detail" class="tab-response-detail fx-tab" data-toggle="tab">@i18n("na.portal.monitoring.order.modal.tab.responseDetail")</a>
                </li>
                <li>
                    <a href="#tab-history" class="tab-history fx-tab response-page__history-tab" data-toggle="tab">@i18n("na.portal.monitoring.order.modal.tab.history")</a>
                </li>
            </ul>
            <div class="tab-content nav-tabs-stacked-left-content tab-content--order-view-response-page">
                <section id="tab-response-detail" class="tab-response-detail tab-pane active "></section>
                <section id="tab-history" class="tab-history tab-pane">
                    <table id="response-retries-table" data-store="responseStore"
                           data-na-portal-table-datatable
                           data-config-url="@na.monitoring.controllers.routes.TableController.dataTableResponseRetries()"
                           data-na-portal-monitoring-order-view-response-retry-table
                    ></table>
                </section>
            </div>
        </div>

    </div>
}

@notificationsContent = {
    <div class="monitoring-order-view__notification-page fx-full-height" data-group="notifications" data-store="notificationStore">
        <h6><strong>@i18n("na.portal.monitoring.order.notification.details.table.logs_history")</strong></h6>
        <table id="notification-details-table" data-store="notificationStore"
        data-na-portal-table-datatable
        data-config-url="@na.monitoring.controllers.routes.TableController.dataTableNotificationDetails()"
        data-na-portal-monitoring-order-view-notification-details-table
        ></table>
    </div>
}

@mainSkel() {
    <div
        class="main-content-wrapper order-view fx-full-height page--order-view"
        data-na-portal-monitoring-order-view
        data-na-portal-monitoring-order-view-breadcrumb
        data-na-portal-monitoring-order-tabs
    >
        <script data-initial-store-content type="application/json">@Json.toJson(order)</script>

        <div class="header page__header">
            @* order info *@
            <div class="summary summary--monitoring-order-view fx-constraint two-rows pull-left">
                @* icon *@
                <span class="summary__icon-container fx-info-icon">
                <i class="summary__icon fuxicons fuxicons-reports"></i>
            </span>
                @* info *@
                <div class="summary__info">
                    <div class="summary__info-title">
                        @* name *@
                        <h1 class="summary__name" id="name">@orderNameLabel</h1>
                        @* badge *@
                        <div>
                            <span id="state" class="summary__state filter-badge badge" data-state="@order.getState" data-na-portal-monitoring-order-state-badge></span>
                        </div>
                        <div class="order-actions-container" style="display: inline-block;">
                            <div class="dropdown-list--monitoring-order-view-actions" data-na-portal-monitoring-order-view-actions-dropdown style="display: inline-block;"></div>
                        </div>
                        <x-order-summary-button order-id="@order.getOrderId" style="margin-right: 8px; display: inline-block;"></x-order-summary-button href="na/monitoring/assets/imagembatata.png">
                    </div>
                    @* description *@
                    <h6 class="summary__description" id="description">@order.getDescription</h6>
                    @* order details *@
                    <div class="order-details">
                        <h6>
                            <i class="glyphicon glyphicon-time"></i> @i18n("na.portal.monitoring.orders.table.system.entry") - <strong id="systemEntry" data-na-portal-monitoring-date-formatter data-date-format="full"></strong> @i18n("na.portal.monitoring.order.from.system") <strong id="system">@order.getSystem</strong> |
                            @if(Option(order.getTechnician).isDefined){<i class="fa fa-user"></i>@i18n("na.portal.monitoring.orders.table.technician") - <strong id="technician">@order.getTechnician</strong> |}
                            @if(Option(order.getCustomer).isDefined){<i data-na-portal-monitoring-order-view-customer></i> <strong id="customer">@order.getCustomer</strong> |}
                            <strong id="elapsedTime" data-na-portal-monitoring-date-formatter data-date-format="short"></strong>
                        </h6>
                    </div>
                </div>
            </div>

            @* order phases *@
            <div class="monitoring-order-view__tabs">
                @validation
                @execution
                @response
                @notifications
            </div>
        </div>
        @* content *@
        <div class="content page__content">
            @validationContent
            @executionContent
            @responseContent
            @notificationsContent
        </div>
    </div>
}