package na.monitoring.models.order;

import pt.alticelabs.nossis.messages.api.Messages;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Arrays;

/**
 * <AUTHOR> <<EMAIL>>, 14-06-2018.
 */
public class Order {

    private final String orderId;
    private final String externalOrderId;
    private final String category;
    private final String description;
    private final String state;
    private final String stateIcon;
    private final String stateI18n;
    private final String customer;
    private final String customerType;
    private final String technician;
    private final String system;
    private final Long systemEntry;
    private final Long lastUpdate;
    private final OrderValidation orderValidation;
    private final OrderResponse orderResponse;
    private final Map<String, String> possibleActions;
    private final boolean actionsPermission;
    private final List<OrderItem> items;
    private final Boolean cloneable;

    private Order(Builder builder) {
        this.orderId = builder.orderId;
        this.externalOrderId = builder.externalOrderId;
        this.category = builder.category;
        this.description = builder.description;
        this.state = builder.state;
        this.stateIcon = builder.stateIcon;
        this.stateI18n = builder.stateI18n;
        this.customer = builder.customer;
        this.customerType = builder.customerType;
        this.technician = builder.technician;
        this.system = builder.system;
        this.systemEntry = builder.systemEntry;
        this.lastUpdate = builder.lastUpdate;
        this.orderValidation = builder.orderValidation;
        this.orderResponse = builder.orderResponse;
        this.possibleActions = builder.possibleActions;
        this.actionsPermission = builder.actionsPermission;
        this.cloneable = builder.cloneable;
        this.items = Optional.ofNullable(builder.items).orElse(Collections.emptyList());
    }

    public Map<String, String> getEnumStatus() {
        return Status.toMap();
    }

    public String getOrderId() {
        return orderId;
    }

    public String getExternalOrderId() {
        return externalOrderId;
    }

    public String getCategory() {
        return category;
    }

    public String getDescription() {
        return description;
    }

    public String getState() {
        return state;
    }

    public String getStateIcon() {
        return stateIcon;
    }

    public String getStateI18n() {
        return stateI18n;
    }

    public String getCustomer() {
        return customer;
    }

    public String getCustomerType() {
        return customerType;
    }

    public String getTechnician() {
        return technician;
    }

    public String getSystem() {
        return system;
    }

    public Long getSystemEntry() {
        return systemEntry;
    }

    public Long getLastUpdate() { return lastUpdate; }

    public OrderValidation getOrderValidation() {
        return orderValidation;
    }

    public OrderResponse getOrderResponse() {
        return orderResponse;
    }

    public List<OrderItem> getItems() {
        return items;
    }

    public Map<String, String> getPossibleActions() {
        return possibleActions;
    }

    public boolean isActionsPermission() {
        return actionsPermission;
    }

    public Boolean getCloneable(){
        return cloneable;
    }

    public static class Builder {
        private final String orderId;
        private final String externalOrderId;
        private final String system;
        private final Long systemEntry;
        private final Long lastUpdate;
        private final boolean actionsPermission;
        private String category;
        private String description;
        private String state;
        private String stateIcon;
        private String stateI18n;
        private String customer;
        private String customerType;
        private String technician;
        private OrderValidation orderValidation;
        private OrderResponse orderResponse;
        private Map<String, String> possibleActions;
        private List<OrderItem> items;

        private List<String> allPossibleActions;
        private final List<String> allowedActions;

        public Boolean cloneable;

        public Builder(String orderId,
                       String externalOrderId,
                       String system,
                       Long systemEntry,
                       Long lastUpdate,
                       boolean actionsPermission,
                       List<String> allowedActions) {

            this.orderId = orderId;
            this.externalOrderId = externalOrderId;
            this.system = system;
            this.systemEntry = systemEntry;
            this.lastUpdate = lastUpdate;
            this.actionsPermission = actionsPermission;
            this.allowedActions = allowedActions;
        }

        public Builder category(String category) {
            this.category = category;
            return this;
        }

        public Builder description(String description) {
            this.description = description;
            return this;
        }

        public Builder state(State state) {
            if (state != null) {
                this.state = state.getValue();
                this.stateIcon = state.getIcon();
                this.stateI18n = Messages.get(state.getText());
            }
            return this;
        }

        public Builder customer(String customer) {
            this.customer = customer;
            return this;
        }

        public Builder customerType(String customerType) {
            this.customerType = customerType;
            return this;
        }

        public Builder technician(String technician) {
            this.technician = technician;
            return this;
        }

        public Builder orderValidation(OrderValidation orderValidation) {
            this.orderValidation = orderValidation;
            return this;
        }

        public Builder orderResponse(OrderResponse orderResponse) {
            this.orderResponse = orderResponse;
            return this;
        }

        public Builder possibleActions(List<String> possibleActions) {
            this.allPossibleActions = possibleActions;
            return this;
        }

        public Builder cloneable(Boolean cloneable) {
            this.cloneable = cloneable;
            return this;
        }

        public Builder items(List<OrderItem> items) {
            this.items = items;
            return this;
        }

        public Order build() {
            final Map<String, String> possibleOrderActionsMap = new HashMap<>();
            for (String possibleOrderAction : this.allPossibleActions) {
                if (!allowedActions.contains(possibleOrderAction)) {
                    continue;
                }
                switch (possibleOrderAction) {
                    case "cancel":
                    case "retry":
                    case "rollback":
                    case "cancelWithRollback":
                    case "skip":
                        possibleOrderActionsMap.put(possibleOrderAction, isFinalState(this.state) ? "disabled" : "enabled");
                        break;
                    case "remove":
                        possibleOrderActionsMap.put(possibleOrderAction, isFinalState(this.state) ? "enabled" : "disabled");
                        break;
                    default:
                        possibleOrderActionsMap.put(possibleOrderAction, "disabled");
                }
            }
            this.possibleActions = possibleOrderActionsMap;
            return new Order(this);
        }

        private static boolean isFinalState(String stateValue) {
            return Arrays.stream(pt.ptinovacao.na.swe.order.model.State.values())
                    .filter(v -> v.getState().toUpperCase().equals(stateValue))
                    .map(pt.ptinovacao.na.swe.order.model.State::isTerminalState)
                    .findFirst()
                    .orElse(false);
        }
    }
}