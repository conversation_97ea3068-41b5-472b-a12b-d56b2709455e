package na.monitoring.models.responses;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import na.monitoring.models.operation.OperationWithAttributes;

import java.util.List;

public class OperationsWithAttributeResponse extends EntityResponse<OperationWithAttributes>{
    @JsonCreator
    public OperationsWithAttributeResponse(@JsonProperty("operations") List<OperationWithAttributes> orders,
                                           @JsonProperty("count") int count) {
        super(orders, count);
    }
}
