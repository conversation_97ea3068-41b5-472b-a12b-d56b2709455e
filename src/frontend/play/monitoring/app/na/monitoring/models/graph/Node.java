package na.monitoring.models.graph;

import java.util.Map;

public class Node {

    private final String id;
    private final String name;
    private final String nodeType;
    private final Map<String, Object> data;

    public Node(String id, String name, String nodeType, Map<String, Object> data) {
        this.id = id;
        this.name = name;
        this.nodeType = nodeType;
        this.data = data;
    }

    public String getId() {
        return id;
    }

    public String getNodeType() {
        return nodeType;
    }

    public Map<String, Object> getData() {
        return data;
    }

    public String getName() {
        return name;
    }
}