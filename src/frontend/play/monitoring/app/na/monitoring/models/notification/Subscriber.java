package na.monitoring.models.notification;

import na.monitoring.models.order.activity.DeliveryAttempt;

import java.util.List;

public class Subscriber {
    private final String name;
    private final List<DeliveryAttempt> attempts;
    private final String callbackUrl;

    public Subscriber(String name, List<DeliveryAttempt> attempts, String callbackUrl) {
        this.name = name;
        this.attempts = attempts;
        this.callbackUrl = callbackUrl;
    }

    public String getName() {
        return name;
    }

    public List<DeliveryAttempt> getAttempts() {
        return attempts;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }
}
