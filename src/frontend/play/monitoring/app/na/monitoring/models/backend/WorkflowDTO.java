package na.monitoring.models.backend;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import pt.ptinovacao.na.followup.order.model.drawing.Flow;
import pt.ptinovacao.na.followup.order.model.workflow.Timeline;

/**
 * pt.ptinovacao.na.order.followup.dto.workflow.WorkflowDTO is parameterized and we needed it as a method argument.
 * This class is here to fix that generic part.
 */
public class WorkflowDTO extends pt.ptinovacao.na.order.followup.dto.workflow.WorkflowDTO<Timeline, Flow>{

    @JsonCreator
    public WorkflowDTO(@JsonProperty("_id") String id,
                       @JsonProperty("name") String name,
                       @JsonProperty("externalId") String externalId,
                       @JsonProperty("timeline") Timeline timeline,
                       @JsonProperty("draw") Flow draw) {
        super(id, name, externalId, timeline, draw);
    }
}
