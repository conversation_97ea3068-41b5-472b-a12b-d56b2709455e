package na.monitoring.models.timeline;

import pt.ptinovacao.na.followup.order.model.common.ActionType;

import java.util.List;
import java.util.Objects;

public class TimelineActivityOperation {
    private final String id;
    private final String status;
    private final String message;
    private final List<ActionType> actions;

    public TimelineActivityOperation(String id, String status, String message, List<ActionType> actions) {
        this.id = id;
        this.status = status;
        this.message = message;
        this.actions = actions;
    }

    public String getMessage() {
        return message;
    }

    public List<ActionType> getActions() {
        return actions;
    }

    public String getStatus() {
        return status;
    }

    public String getId() {
        return id;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TimelineActivityOperation that = (TimelineActivityOperation) o;
        return Objects.equals(id, that.id) && Objects.equals(status, that.status) && Objects.equals(message, that.message) && Objects.equals(actions, that.actions);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, status, message, actions);
    }
}
