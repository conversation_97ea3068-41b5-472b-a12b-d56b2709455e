package na.monitoring.models.selection;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;

public class RollbackOrdersSelectionData extends MassiveOrderSelectionData {

    private final String reason;
    private final String username;
    private final Long timestamp;

    public RollbackOrdersSelectionData(List<OrderSelectionData> ordersAttributes, Map<String, Object> filterAttributes, String reason) {
        this(ordersAttributes, filterAttributes, null, reason, null, null);
    }

    @JsonCreator
    public RollbackOrdersSelectionData(@JsonProperty("ordersAttributes") List<OrderSelectionData> ordersAttributes,
                                       @JsonProperty("filterAttributes") Map<String, Object> filterAttributes,
                                       @JsonProperty("ignoreOrdersAttr") List<OrderSelectionData> ignoreOrdersAttr,
                                       @JsonProperty("reason") String reason,
                                       @JsonProperty("username") String username,
                                       @JsonProperty("timestamp") Long timestamp) {
        super(ordersAttributes, filterAttributes, ignoreOrdersAttr);
        this.reason = reason;
        this.username = username;
        this.timestamp = timestamp;
    }

    public String getReason() {
        return reason;
    }

    public String getUsername() {
        return username;
    }

    public Long getTimestamp() {
        return timestamp;
    }
}
