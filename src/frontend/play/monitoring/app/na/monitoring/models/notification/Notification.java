package na.monitoring.models.notification;

import java.util.List;

public class Notification {
    private final String id;
    private final String orderId;
    private final String message;
    private final List<Subscriber> subscribers;

    public Notification(String id, String orderId, String message, List<Subscriber> subscribers) {
        this.id = id;
        this.orderId = orderId;
        this.message = message;
        this.subscribers = subscribers;
    }

    public String getId() {
        return id;
    }

    public String getOrderId() {
        return orderId;
    }

    public String getMessage() {
        return message;
    }

    public List<Subscriber> getSubscribers() {
        return subscribers;
    }
}
