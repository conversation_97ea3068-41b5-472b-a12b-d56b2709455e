package na.monitoring.models.responses;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import na.monitoring.models.operation.Operation;

import java.util.List;

public class OperationsResponse extends EntityResponse<Operation>{
    @JsonCreator
    public OperationsResponse(@JsonProperty("operations") List<Operation> orders,
                              @JsonProperty("count") int count) {
        super(orders, count);
    }
}
