package na.monitoring.settings;

import com.typesafe.config.Config;
import na.monitoring.models.order.State;
import na.monitoring.models.settings.FrontendSettingsDTO;
import na.monitoring.ui.I18n;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

@Singleton
public final class MonitoringServiceSettings {

    private final Config configuration;

    @Inject
    private MonitoringServiceSettings(Config configuration) {
        this.configuration = configuration;
    }

    public String getBackendHost() {
        return configuration.getString("na.monitoring.backend.host");
    }

    public int getBackendPort() {
        return configuration.getInt( "na.monitoring.backend.port");
    }
    public String getBackendSweHost() {
        return configuration.getString("na.monitoring.backend.swe.host");
    }

    public int getBackendSwePort() {
        return configuration.getInt( "na.monitoring.backend.swe.port");
    }


    public String getServiceLocatorConfigFile(){
        return getStringOrElse("na.monitoring.service.locator.configfile",
                "/opt/alticelabs/na-portal/conf/cluster-client-config.yml");
    }

    public String getServiceLocatorClusterName(){
        return getStringOrElse("na.monitoring.service.locator.cluster-name",
                "na-operations-catalog-rest-backend-service@na-operations-catalog-clusters");
    }

    public String getServiceLocatorClusterService(){
        return getStringOrElse("na.monitoring.service.locator.cluster-service",
                "na-operations-catalog-rest");
    }

    public List<State> getStates() {
        return getStates(null);
    }

    public List<State> getStates(final String query) {

        final List<State> values = configuration.getConfigList("na.monitoring.states").stream()
                .map(config -> {
                    final String value = config.getString("value");
                    final String icon = getStringOrNull(config, "icon");
                    final String text = getStringOrNull(config, "text");
                    final String execution = getStringOrNull(config, "execution");
                    return new State(value, icon, text, execution);
                })
                .collect(Collectors.toList());

        if (query != null && !query.trim().isEmpty()) {
            return values.stream().filter(state -> new I18n(state.getText()).getTranslation().contains(query.trim())).collect(Collectors.toList());
        }

        return values;
    }

    public List<String> getStatesPriority() {
        return configuration.getStringList("na.monitoring.statespriority");
    }

    public List<String> getPossibleOrderActions() {
        return new LinkedList<>(configuration.getStringList("na.monitoring.possible.order.actions"));
    }

    public List<String> getSearchDateFilterBy() {
        return configuration.getStringList("na.monitoring.search.date.filterby");
    }

    public String getValidationStructureErrorCode() {
        return configuration.getString("na.monitoring.validation.structure.errorcode");
    }

    public String getValidationSignatureErrorCode() {
        return configuration.getString("na.monitoring.validation.signature.errorcode");
    }

    public String getDefaultDaterangepickerInterval() {
        return getStringOrNull("monitoring.daterangepicker.default.interval");
    }

    public FrontendSettingsDTO getFrontendSettings(){
        return new FrontendSettingsDTO(
                getStates(),
                getStatesPriority(),
                getDefaultDaterangepickerInterval(),
                getAllowedVersionStates()
        );
    }

    public String getOperationsServicesSuffix() {
        final String CONFIG_KEY = "monitoring.operations.context.path";
        if (!configuration.hasPath(CONFIG_KEY)) {
            return "/netwin";
        }
        String suffix = configuration.getString(CONFIG_KEY);
        if (!suffix.startsWith("/")) {
            suffix = "/" + suffix;
        }
        return suffix;

    }

    public List<String> getAllowedVersionStates() {
        return configuration.getStringList("na.monitoring.order.create.allowedVersionStates");
    }

    public List<String> getWhitelistedExternalSystems() {
        return configuration.hasPath("na.monitoring.order.create.system.whitelist")
                ? configuration.getStringList("na.monitoring.order.create.system.whitelist")
                : Collections.emptyList();
    }

    public String getRequesterCallback(){
        final String URL_KEY = "na.monitoring.requesterCallback.url";
        final String HOST_KEY = "na.monitoring.requesterCallback.base.url";
        if(configuration.hasPath(URL_KEY)){
            return configuration.getString(URL_KEY);
        }
        String key = configuration.getString(HOST_KEY);
        return (key.endsWith("/") ? key.substring(0, key.length()-1) : key)
                + na.monitoring.controllers.routes.RequesterCallbackController.requesterCallback().url();
    }

    private String getStringOrElse(String key, String defaultValue) {
        return getStringOrElse(configuration, key, defaultValue);
    }

    private String getStringOrElse(Config config, String key, String defaultValue) {
        if (config.hasPath(key)) {
            return config.getString(key);
        }
        return defaultValue;
    }

    private String getStringOrNull(String key) {
        return getStringOrNull(configuration, key);
    }

    private String getStringOrNull(Config config, String key) {
        return getStringOrElse(config, key, null);
    }

    public String getBotSchoolApiUrl() {
        return configuration.getString("na.monitoring.botschool.api.url");
    }

    public String getBotSchoolApiKey() {
        return configuration.getString("na.monitoring.botschool.api.key");
    }

    public String getBotSchoolPromptTemplate() {
        return configuration.getString("na.monitoring.botschool.prompt.template");
    }
}
