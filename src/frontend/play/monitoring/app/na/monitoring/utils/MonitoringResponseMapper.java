package na.monitoring.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import na.naportalbase.exceptions.ExceptionCodes;
import na.naportalbase.exceptions.FrontendException;
import na.naportalbase.models.services.GenericResponse;
import na.naportalbase.services.DefaultErrorHandler;
import na.naportalbase.services.ErrorHandler;
import na.naportalbase.services.ResponseMapperIf;
import na.naportalbase.services.ServiceInvokeLogger;
import na.naportalbase.services.ServicesHelper;
import na.naportalbase.settings.BaseConstants;
import na.naportalbase.settings.BaseMappings;
import na.naportalbase.settings.ExpectedHttpResponseCodes;
import play.libs.Json;
import play.libs.ws.WSResponse;
import play.mvc.Http;
import pt.ptinovacao.nossis.exceptions.BusinessException;
import pt.ptinovacao.nossis.exceptions.FaultException;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletionStage;

import static org.apache.commons.lang3.StringUtils.isNumeric;

public class MonitoringResponseMapper implements ResponseMapperIf {

    private final ErrorHandler errorHandler;

    public MonitoringResponseMapper() {
        this.errorHandler = new DefaultErrorHandler();

    }

    @Override
    public <T> CompletionStage<T> mapResponse(Class<T> generic, String requestType, CompletionStage<WSResponse> wsResponse, String logId) {
        return wsResponse.handle((response, throwable) -> {

            if (throwable != null && throwable.getCause() instanceof java.net.ConnectException) {
                throw new FaultException(ExceptionCodes.REST_API_UNAVAILABLE_EXCEPTION, throwable.getCause());
            }

            GenericResponse<WSResponse> genericResponse = new GenericResponse<>(response);

            Object responseObject = null;

            ServiceInvokeLogger.logServiceResponse(logId, response.getAllHeaders(), response.getStatus(), response.getBody(), requestType);

            if (ExpectedHttpResponseCodes.isExpectedSuccessCode(requestType, genericResponse.getStatus())) {
                JsonNode jsonNode = response.asJson();

                if (jsonNode instanceof ArrayNode) {
                    jsonNode = Json.newObject()
                            .set("entries", jsonNode);
                }

                if (jsonNode instanceof ObjectNode) {
                    String totalCountHeader = getHeaderValue(response, "X-TOTAL-COUNT").orElse(null);
                    String contentRangeTotal = getHeaderValue(response,"Content-Range")
                            .map(contentRangeHeader -> {
                                String[] contentRangeSegments = contentRangeHeader.split("/");
                                return contentRangeSegments.length != 2 ? null : contentRangeSegments[1];
                            }).orElse(null);

                    if (isNumeric(contentRangeTotal)) {
                        ((ObjectNode) jsonNode).put("count", contentRangeTotal);
                    } else if (isNumeric(totalCountHeader)) {
                        ((ObjectNode) jsonNode).put("count", totalCountHeader);
                    }
                }

                responseObject = parseJson(generic, jsonNode);

            } else {
                handleBadResponse(requestType, genericResponse);
            }
            return (T) responseObject;
        });
    }

    public void handleBadResponse(final String requestType, final GenericResponse<?> genericResponse) {
        if (genericResponse.getStatus() == Http.Status.NOT_FOUND) {
            throw new BusinessException(ExceptionCodes.NOT_FOUND);
        }
        errorHandler.handleBadResponse(requestType, genericResponse);
    }

    protected <T> Object parseJson(final Class<T> generic, JsonNode jsonNode) {
        try {
            return BaseMappings.dualMapper.convertValue(jsonNode, generic);
        } catch (Exception e) {
            // critical error
            throw new FrontendException(ExceptionCodes.CONFIGURATION_EXCEPTION, e);
        }
    }

    /** gets value of response header, gets first only if there are multiple header with same key */
    private Optional<String> getHeaderValue(WSResponse response, String headerKey){
        Map<String, List<String>> headers = response.getHeaders();
        if(!headers.containsKey(headerKey)){
            return Optional.empty();
        }
        List<String> headerValueList = headers.get(headerKey);
        if(headerValueList == null || headerValueList.isEmpty()){
            return Optional.empty();
        }
        return Optional.ofNullable(headerValueList.get(0));
    }

}
