package na.monitoring.actions;

import na.monitoring.settings.Features;
import play.mvc.Action;
import play.mvc.Http;
import play.mvc.Result;
import play.mvc.Results;

import javax.inject.Inject;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

public class FeatureAction extends Action<Feature> {

    private final Features features;

    @Inject
    public FeatureAction(Features features) {
        this.features = features;
    }

    @Override
    public CompletionStage<Result> call(Http.Context context) {
        if(features.areFeaturesEnabledArray(this.configuration.value())){
            return this.delegate.call(context);
        }
        return CompletableFuture.completedFuture(Results.notFound());
    }
}
