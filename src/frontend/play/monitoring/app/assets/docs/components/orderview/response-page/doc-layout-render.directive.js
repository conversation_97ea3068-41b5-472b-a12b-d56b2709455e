(function () {
    angular.module(modules.na.monitoring).directive("docLayoutRenderer", [filterListDirective]);

    function filterListDirective() {
        return {
            restrict: 'A',
            terminal: false,
            scope: true,
            controller:[function () {
                this.store = na.monitoring.common.Store(na.components.monitoring.order.response.store);
            }],
            controllerAs: "NodeDoc",
            link: {
                pre: function (scope, element, attrs, ctrl) {
                    scope.store = ctrl.store;

                    element.find("input.status-field[type='number'][name],input.status-field[type='text'][name]").each(function () {
                        var $el = $(this);
                        if(scope.store.state.status[$el.attr("name")]){
                            $el.val(scope.store.state.status[$el.attr("name")]);
                        }
                    });
                    element.find("select.status-field").each(function () {
                        var $el = $(this);
                        if(scope.store.state.status[$el.attr("name")]){
                            $el.val(scope.store.state.status[$el.attr("name")]);
                        }
                    });


                    element.find("input.i18n-field[type='text'][name], textarea.i18n-field[name]").each(function () {
                        var $el = $(this);
                        if(scope.store.state.i18n[$el.attr("name")]){
                            $el.val(scope.store.state.i18n[$el.attr("name")]);
                        }
                    });


                    element.on("change input", "input, select, textarea", function(){
                        var layout = {};
                        element.find("input.status-field[type='number'][name]").each(function () {
                            var $el = $(this);
                            layout[$el.attr("name")] = parseInt($el.val(), 10) || 0;
                        });
                        element.find("input.status-field[type='text'][name]").each(function () {
                            var $el = $(this);
                            layout[$el.attr("name")] = $el.val();
                        });
                        element.find("select.status-field").each(function () {
                            var $el = $(this);
                            layout[$el.attr("name")] = $el.val();
                        });
                        if(layout.value === "SENT"){
                            scope.store.setters.setSuccessStatus();
                        } else if(layout.value === "DELIVERING"){
                            scope.store.setters.setDeliveringStatus();
                        } else {
                            scope.store.setters.setErrorStatus(layout);
                        }

                        var i18n = {};
                        element.find("input.i18n-field[type='text'][name], textarea.i18n-field[name]").each(function () {
                            var $el = $(this);
                            i18n[$el.attr("name")] = $el.val();
                        });
                        scope.store.setters.i18n(i18n);

                    });


                    na.monitoring.common.Store.enableDebug({listeners: false});


                }
            }
        };
    }


}());
