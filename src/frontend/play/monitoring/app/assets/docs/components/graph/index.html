<html>
<head>


    <title>Graph Documentation</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge; text/html;">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, minimum-scale=1.0, maximum-scale=1.0,  user-scalable=yes">
    <script src="/newcs/javascript/vendor/pace.min.js"></script>
    <link type="text/css" rel="stylesheet" href="/newcs/stylesheets/pace-theme-flash.css">
    <link rel="icon" href="/fuxi/common/html/css/images/favicon.ico" type="image/x-icon">
    <link href="/basepack/bootstrap-pt/3.0/css/bootstrap.min.css" rel="stylesheet">
    <link type="text/css" rel="stylesheet" href="/fuxi/lib/toastr/toastr.css">
    <link type="text/css" rel="stylesheet" href="/fuxi/common/html/css/fuxi-ext-toastr.min.css">
    <link type="text/css" rel="stylesheet" href="/fuxi/common/html/css/fuxi.min.css">
    <link type="text/css" rel="stylesheet" href="/fuxi/fuxi-nossis/html/css/nossis.min.css">
    <link type="text/css" rel="stylesheet" href="/fuxi/fuxi-nossis/html/css/nossis-portal.min.css">
    <link type="text/css" rel="stylesheet" href="/fuxi/fuxi-nossis/html/css/nossis-module.min.css">
    <link type="text/css" rel="stylesheet" href="/newcs/stylesheets/nossiscommon.css">
    <link type="text/css" rel="stylesheet" href="/portal/assets/stylesheets/portal-main.css">
    <link type="text/css" rel="stylesheet" href="/fuxi/lib/codemirror/5.1/css/codemirror.css">


    <script src="/basepack/modernizr/2.6/modernizr.js"></script>
    <script type="text/javascript" src="/portal/assets/javascripts/i18n.js"></script>
    <script type="text/javascript" src="/portal/assets/javascripts/routes"></script>
    <script src="/basepack/jquery/1.11/jquery.min.js"></script>
    <script src="/basepack/angularjs/1.4/angular.min.js"></script>
    <!-- bootstrap -->
    <script src="/basepack/bootstrap-pt/3.0/js/bootstrap.min.js"></script>
    <!-- FUXI -->
    <script src="/fuxi/common/exec/fuxi.min.js"></script>
    <!-- NOSSIS  -->
    <script src="/newcs/javascript/angular/newcs.js"></script>
    <script src="/newcs/javascript/angular/session/session.js"></script>
    <script src="/newcs/javascript/angular/session/services/sessionservices.js"></script>
    <script src="/newcs/javascript/angular/hasher/hasher.js"></script>
    <script src="/newcs/javascript/angular/hasher/services/hasherservice.js"></script>
    <script src="/newcs/javascript/angular/notification/notification.js"></script>
    <script src="/newcs/javascript/angular/notification/services/notificationservice.js"></script>
    <script src="/newcs/javascript/angular/notification/directives/notificationdirective.js"></script>
    <script src="/newcs/javascript/angular/dialogs/dialogs.js"></script>
    <script src="/newcs/javascript/angular/dialogs/services/modalservice.js"></script>
    <script src="/newcs/javascript/angular/dialogs/directives/modaldirective.js"></script>
    <script src="/newcs/javascript/angular/dialogs/controllers/modalcontrollers.js"></script>
    <script src="/newcs/javascript/angular/proxy/proxy.js"></script>
    <script src="/newcs/javascript/angular/proxy/services/proxyservice.js"></script>
    <script src="/newcs/javascript/angular/signal/signal.js"></script>
    <script src="/newcs/javascript/angular/signal/services/signalservice.js"></script>
    <script src="/newcs/javascript/angular/navigation/navigation.js"></script>
    <script src="/newcs/javascript/angular/navigation/services/navigationservice.js"></script>
    <script src="/newcs/javascript/angular/navigation/directives/navigationdirective.js"></script>
    <script src="/fuxi/fuxi-nossis/exec/nossis-template-portal.min.js"></script>

    <!-- #HasherJS depends on SingalsJS -->
    <script src="/newcs/javascript/vendor/signals.js" type="text/javascript"></script>
    <script src="/newcs/javascript/vendor/hasher.js" type="text/javascript"></script>
    <!-- UnderscoreJS utility belt -->
    <script src="/newcs/javascript/vendor/underscore.js" type="text/javascript"></script>
    <!-- URI library -->
    <script src="/newcs/javascript/vendor/URI.min.js" type="text/javascript"></script>
    <!-- Cookies library -->
    <script src="/newcs/javascript/vendor/cookies.js" type="text/javascript"></script>
    <!-- seamless iframe library -->
    <script src="/newcs/javascript/vendor/seamless.parent.min.js" type="text/javascript"></script>
    <script src="/newcs/javascript/vendor/seamless.child.min.js" type="text/javascript"></script>
    <!-- random key library -->
    <script src="/newcs/javascript/lib/random-string.js" type="text/javascript"></script>
    <!-- toastr library-->
    <script src="/fuxi/lib/toastr/toastr.js" type="text/javascript"></script>
    <!-- Application Scripts -->
    <script src="/portal/assets/javascripts/src/application.js"></script>
    <script src="/portal/assets/javascripts/nossis-scripts.js"></script>

    <style>
        OL { counter-reset: item }
        OL LI { display: block }
        OL LI:before { content: counters(item, ".") " "; counter-increment: item }
        body {overflow: auto; padding: 2em; }
        [data-doc-intro]{padding: 2em; }
        .graph-container {position: relative; height: 200px; overflow: auto}
        [data-doc-node-renderer] {margin: 1em;}
        [data-doc-node-renderer] > *{ border: solid 1px #d2d2d2; position: relative;}
        [data-doc-node-renderer] > .html, [data-doc-node-renderer] > .code, [data-doc-node-renderer] > .graph-container{ min-height: 200px;}
        [data-doc-node-renderer] > .examples { padding: .5em;}
        [data-doc-layout-renderer] > *{ border: solid 1px #d2d2d2; position: relative; height: 300px}
        [data-doc-edge-renderer] > *{ border: solid 1px #d2d2d2; position: relative; height: 200px}

        .CodeMirror { position: absolute; height: auto; top: 0; bottom: 0; left: 0; right: 0 }
        details .CodeMirror { position: relative; height: 200px; }

    </style>
</head>
<body id="application">
<div class="container" data-doc-intro>
    <h1> Graph documentation </h1>


    <p> </p>
<section>
    <h2>Index</h2>
    <ol>
        <li><a href="#intro">Introduction</a></li>
        <li><a href="#intro">Technologies used</a></li>
        <li><a href="#state-model">State model</a>
            <ol>
                <li><a href="#model">Model</a></li>
            </ol>
        </li>
        <li>
            <a href="#view">View</a>
            <ol>
                <li><a href="#node-renderers">Node Renderers</a></li>
                <li><a href="#layout">Layout</a></li>
                <li><a href="#styling">Styling</a></li>
            </ol>
        </li>
        <li><a href="#nodes">Nodes</a>
            <ol>
                <li><a href="#node-model">Node model</a></li>
                <li><a href="#renderers">Node renderers</a>
                <ol>
                    <li><a href="#entry-node-renderer">entry</a></li>
                    <li><a href="#exit-node-renderer">exit</a></li>
                    <li><a href="#activity-node-renderer">activity</a></li>
                    <li><a href="#parallel-gateway-node-renderer">parallel gateway</a></li>
                    <li><a href="#basic-activity-node-renderer">basic activity</a></li>
                </ol>
                </li>
            </ol>
        </li>
        <li><a href="#edges">Edges</a>
            <ol>
                <li><a href="#edge-model">Edge Model</a></li>
                <li><a href="#edge-view">Edge View</a></li>
            </ol>
        </li>
        <li><a href="#graph-examples">Graph examples</a></li>
        <li><a href="#references">References</a></li>
    </ol>
</section>
    <section>

    <h2 id="intro">1. Introduction</h2>
      This document contains information about the usages and configuration of the graph component of monitoring module.
    The component is created as an AngularJS directive and uses JSPlumb library used to create edges.
    The next figure show the working component with a basic graph.

    <div class="graph-container">
        <div data-na-portal-monitoring-graph
             data-na-portal-monitoring-graph-order-details
             data-store="storeIntro"
             data-graph-node-click-log></div>
    </div>

        The first section of the document shows a small description of the technologies used. The next part will describe
        the model used to define the graph state. The next section outline how the graph is rendered and displays how
        the view reflects the model. After that, the next section shows the node configuration and some
        of the implemented node renderers. The edge configuration will be described on the next section. Finally,
        the last section shows a link to a page with graph examples

    </section>
    <section>

    <h2 id="tech">2. Technologies used</h2>

    <p>
            As previously written, the directive is defined using <a href="#references-1">AngularJS</a>,
        a JavaScript web framework. The version that this component was first created is 1.4.
    </p>
    <p>
            The are also two important libraries used while developing the graph component. The first is
        <a href="#references-2">JSPlumb</a> a library that connects two DOM element, each element represents
        a node in the graph, and paints an edge, connecting the two nodes.
    </p>
    <p>
            Now we have nodes and edges and know how to paint them, there should be something missing, what would it be? Well,
        when we are painting nodes, we would like each node not to overlap another, so we would have to position them.
        However, we would like to position them as to minimize the number of edges crossing each other, that is a big
        challenge creating an algorithm for it. That's where comes the second library that was used in the development
        of the graph, <a href="references-3">Dagre</a>, a JavaScript library that lay out directed graphs on the browser. This libray is used to
        define the position of our nodes, it's algorithms to layout the graph are enough to our use cases.
    </p>
    </section>
    <section>

   <h2 id="state-model">3. State Model </h2>

    <p>
            To render the graph, a model is needed to define how to paint the graph according to many use cases:
        how to color each node and edge, add interactive features to a node, change graph layout, etc...
    </p>
    <p>
            For that purpose, a store that saves the state of the graph is created, the graph state contains all
        information needed to paint a graph, be it node and edge information, layout and other useful configurations.
    </p>

    <h3 id="model">3.1 Model</h3>

    <p>
            Since we decided to use states to save all information to render the graph, a model of said state was
        designed. The next code shows the model of said state. Properties with postfix <code>?</code> indicates that the
        property is optional.
    </p>

        <pre><code><b class="text-info">GraphState</b> {
    nodes: <span class="text-info">NodeState[]</span>,
    edges: <span class="text-info">EdgeState[]</span>,
    layout: <span class="text-info">LayoutConfiguration</span>,
    nodeTypes: <span class="text-info">{
        [string] : NodeTypeInfo
    }</span>
}

<b class="text-info">NodeState</b> {
    id: <span class="text-info">string</span>,
    type: <span class="text-info">string</span>,
    data: <span class="text-info">object</span>
}

<b class="text-info">EdgeState</b> {
    source: <span class="text-info">string</span>,
    target: <span class="text-info">string</span>,
    sourceOverlay: <span class="text-info">"arrow" | "none"</span>,
    targetOverlay: <span class="text-info">"arrow" | "none"</span>
}

<b class="text-info">LayoutConfiguration</b> {
    nodeSeparation: <span class="text-info">number</span>,
    rankSeparation: <span class="text-info">number</span>,
    orientation: <span class="text-info">"vertical" | "horizontal"</span>,
    centerOnNode?: <span class="text-info">string</span>,
    marginy: <span class="text-info">number</span>,
    marginx: <span class="text-info">number</span>
}

<b class="text-info">NodeTypeInfo</b> {
    render: <span class="text-info">string</span>
    params?: <span class="text-info">object</span>
}</code></pre>

    <p>
        The whole graph state model is represented by the <code><b class="text-info">GraphState</b></code> object definition.
        It contains the following fields:
    </p>

        <ul>
            <li><label>nodes</label> - A list of nodes to render, each node is represented by the <code><b class="text-info">NodeState</b></code>;  </li>
            <li><label>edges</label> - A list of edges to paint, each edge is represented by the <code><b class="text-info">EdgeState</b></code>;  </li>
            <li><label>layout</label> - The layout configuration of the graph, defined on <code><b class="text-info">LayoutConfiguration</b></code> object;  </li>
            <li><label>nodeTypes</label> - A map of node types, as <code class="text-info">string</code>, to a configuration of the respective node type to render a group of nodes, as  <code><b class="text-info">NodeTypeInfo</b></code>.  </li>
        </ul>

    </section>

    <h2 id="view">4. View </h2>

    The view of the graph should be a reflection of the state and defined renderers.

    <h3 id="node-renderers">4.1 Node Renderers </h3>

    <p>
        Each node has it is own renderer caracterized by the type. The rendering logic is separated from the graph component,
        allowing the possibility of extending the graph to render complex nodes.

    </p>
    <p>
        A renderer should be defined as a function that consumes a node to query, and nodeInfo; produces the
        respective html. Said function should be able to be fetched from the global variable. The renderers that are
        used on the graph are specified on the <code>nodeTypes</code> variable of
        <code><b class="text-info">GraphState</b></code>. Each value of the map is a
        <code><b class="text-info">NodeTypeInfo</b></code> and contains the following properties:
    </p>

    <ul>
        <li><label>render</label> - the path of the renderer;  </li>
        <li><label>params</label> - optional parameter, used to add configuration to the render if necessary.  </li>
    </ul>




    <div class="alert alert-info">

        <strong> Note: </strong> Avoid defining directly in the global object, use namespaces to avoid collision with
        libraries and frameworks global variables.
    </div>

    <h3 id="layout">4.2 Layout </h3>

    <p>
        To position the nodes of the graph, a layout is necessary. The layout configuration is specified on the <code>layout</code> variable of
    <code><b class="text-info">GraphState</b></code> and contains the following properties:
    </p>

    <ul>
        <li><label>nodeSeparation</label> - separation in pixels between nodes in the same level;  </li>
        <li><label>rankSeparation</label> - separation in pixels between levels of nodes;  </li>
        <li><label>orientation</label> - layout orientation;  </li>
        <li><label>centerOnNode</label> - id of node for the graph to <strong><i>scroll</i></strong> the view to the node;
        <li><label>marginy</label> - layout vertical margin;  </li>
        <li><label>marginx</label> - layout horizontal margin.  </li>
    </ul>

    The next container shows an example of the graph reflecting the layout state. The left side shows a form that
    specifies the layout part of the graph state, and the right side shows the view that reflects the form.

    <div data-doc-layout-renderer class="row" data-render="na.components.monitoring.graph.node.render.entry">
<pre class="col-sm-6 form-inline"><code><b class="text-info">LayoutConfiguration</b> {
    nodeSeparation: <input class="form-control" type="number" value="50" name="nodeSeparation"/>,
    rankSeparation: <input class="form-control" type="number" value="40" name="rankSeparation"/>,
    orientation: <select class="form-control" name="orientation"><option>horizontal</option><option>vertical</option> </select>,
    centerOnNode: <select class="form-control" name="centerOnNode"><option></option></select>,
    marginy: <input class="form-control" type="number" name="marginy"/>,
    marginx: <input class="form-control" type="number" name="marginx"/>
}</code></pre>
        <div class="graph-container col-sm-6">
            <div data-na-portal-monitoring-graph data-na-portal-monitoring-graph-order-details
                 data-store="NodeDoc.store" data-graph-node-click-log></div>
        </div>
    </div>

    <h3 id="styling">4.3 Styling </h3>

    A good frontend development practice is to apply classes to DOM elements as to styles using CSS (Cascading Style Sheets),
    for that reason it is recommended to put classes on the DOM elements on the output of the node renderer.
    The renderers implemented followed the <a href="#references-4">BEM</a> methodology and the root DOM has respected
    following class template <code ng-non-bindable>node node--render-{{ render name }}</code>


    <h2 id="nodes">5. Nodes</h2>

    A node, or vertex, is a unit which a graph is formed. The next graph simply shows a node.

    <div class="graph-container">
        <div data-na-portal-monitoring-graph
             data-na-portal-monitoring-graph-order-details
             data-store="storeNode"
             data-graph-node-click-log></div>
    </div>

    <h3 id="node-model">5.1 Node model</h3>

    As shown previously on the <a href="#model">model</a> section, the model of a node is the
    <code><b class="text-info">NodeState</b></code> and contains the following fields:
    <ul>
        <li><label>id</label> - An identifier of the node, used to identify the node on the DOM, as well as the
            <code>source</code> and <code>target</code> property of an <code><b class="text-info">EdgeState</b></code>;  </li>
        <li><label>nodeType</label> - Indicated the type of the node, used mainly for rendering purposes;  </li>
        <li><label>data</label> - Metadata used for various purposes, be it rendering, action handling, etc...  </li>
    </ul>



    <h3 id="renderers">5.2 renderers</h3>
    <p>
            As previously written, each node has its own renderer defined on the type. All renderers that are going
        to be shown are mostly specific to the NA Portal monitoring module. However they can be refactored to a
        place where it can be reused on various modules or application without affecting the component,
        of course, the path would need to be changed.
    </p>
    <p>
        The next level subsections will show the implemented renderers. Each renderer will be presentend in a container
        where the left side is an editor that show the model of the node, said model should respect the
        <code><b class="text-info">NodeState</b></code> definition. The right side the view of the
        node that is directly translated from the model on the left side, which means, the view is updated automatically
        each time the contents of the editor changes. One caveat to take in mind, the
        <code>nodeType</code> property will always be <code>"example"</code>, so any change to the <code>nodeType</code>
        property on the editor will be ignored, including removal of the property.
    </p>
    <h4 id="entry-node-renderer">5.2.1 entry</h4>

    The entry node indicates the starting point of a workflow, this renderer does not use node's <code>data</code>
    property to render. The render is caracterized on <code>na.components.monitoring.graph.node.render.entry</code> variable
    . For this case, the <code><b class="text-info">GraphState</b></code> <code>nodeTypes</code> value on the graph is
    <pre><code>{ "example": { "render":"na.components.monitoring.graph.node.render.entry" } }</code></pre>
    With this definition the graph will render the node with the specified renderer. The same will be applied on the next
    renderer's presentations.

    <div data-doc-node-renderer class="row" data-render="na.components.monitoring.graph.node.render.entry">
        <div class="code col-sm-6"></div>
        <div class="graph-container col-sm-6">
            <div data-na-portal-monitoring-graph data-na-portal-monitoring-graph-order-details
                 data-store="NodeDoc.store" data-graph-node-click-log></div>
        </div>
        <div class="col-sm-12"><details class="html-details"><summary>html</summary><div class="html"></div></details></div>

    </div>

    <h4 id="exit-node-renderer">5.2.2 exit</h4>

    The exit node indicates the ending point of a workflow, this renderer also does not use node's <code>data</code>
    property to render, and is defined on <code>na.components.monitoring.graph.node.render.exit</code> variable.


    <div data-doc-node-renderer class="row" data-render="na.components.monitoring.graph.node.render.exit">
        <div class="code col-sm-6"></div>
        <div class="graph-container col-sm-6">
            <div data-na-portal-monitoring-graph data-na-portal-monitoring-graph-order-details
                 data-store="NodeDoc.store" data-graph-node-click-log></div>
        </div>
        <div class="col-sm-12"><details class="html-details"><summary>html</summary><div class="html"></div></details></div>
    </div>


    <h4 id="activity-node-renderer">5.2.3 activity</h4>

    <p>
        The activity is the main node used on the monitoring module, This renderer
        use node's <code>data</code> property to render, the following list shows the properties of <code>data</code> that are
        used:

    <ul>
    <li><label>id</label> - An identifier of the activity show the top text; </li>
    <li><label>name</label> - the name of the operation, shows the main text; </li>
    <li><label>icon</label> - icon to show on the left side; </li>
    <li><label>bottomLabel</label> - the label to show below the label, on the left side; </li>
    <li><label>status</label> - styles the node and shows a icon on the right side, separated by a vertical bar; </li>
    <li><label>clickAction</label> - the name of click action, the action should be handled on another directive that
        will add behaviour to the graph to handle it. </li>
    </ul>

        In this case the graph component have an additional directive that handles the click action, it shows a modal with information
    of the action and node. The render is defined on <code>na.components.monitoring.graph.node.render.activity</code> variable.
    </p>


    <div data-doc-node-renderer class="row" data-render="na.components.monitoring.graph.node.render.activity">
        <div data-initial-node-state hidden>{ "id": "node", "nodeType": "example", "data": { "icon": "fa fa-envelope-o", "status": "success" }}</div>
        <div data-node-state-of-example="icon" hidden>{ "id": "node", "nodeType": "example", "data": { "icon": "fa fa-envelope-o", "status": "success" }}</div>
        <div data-node-state-of-example="text" hidden>{ "id": "node", "nodeType": "example", "data": {
            "name": "Allocate Resources"
            }}</div>
        <div data-node-state-of-example="success" hidden>{ "id": "node", "nodeType": "example", "data": {
            "id": "123456",
            "name": "Allocate Resources",
            "bottomLabel": "Netwin",
            "status": "success"
            }}</div>
        <div data-node-state-of-example="error" hidden>{ "id": "node", "nodeType": "example", "data": {
            "id": "123456",
            "name": "Allocate Resources",
            "bottomLabel": "Netwin",
            "status": "error"
            }}</div>

        <div data-node-state-of-example="waiting" hidden>{ "id": "node", "nodeType": "example", "data": {
            "id": "180394",
            "name": "Configure Network",
            "bottomLabel": "NA",
            "status": "waiting"
            }}</div>

        <div data-node-state-of-example="not executed" hidden>{ "id": "node", "nodeType": "example", "data": {
            "id": "180394",
            "name": "Configure Network",
            "bottomLabel": "NA",
            "status": "not executed"
            }}</div>

        <div data-node-state-of-example="clickable" hidden>{ "id": "node", "nodeType": "example", "data": {
            "id": "180394",
            "name": "Configure Network",
            "bottomLabel": "NA",
            "status": "success",
            "clickAction": "action"
            }}</div>

        <div class="code col-sm-6"></div>
        <div class="graph-container col-sm-6">
            <div data-na-portal-monitoring-graph data-na-portal-monitoring-graph-order-details
                 data-store="NodeDoc.store" data-graph-node-click-log></div>
        </div>
        <div class="col-sm-12"><details class="html-details"><summary>html</summary><div class="html"></div></details></div>
        <div class="examples col-sm-12"><label>Examples: </label> </div>

    </div>

    <h4 id="parallel-gateway-node-renderer">5.2.3 parallel gateway</h4>

    The parallel gateway node indicates the a fork and join of operations. This renderer also does not use node's <code>data</code>
    property to render, and is defined on <code>na.components.monitoring.graph.node.render.parallelGateway</code> variable.

    <div data-doc-node-renderer class="row" data-render="na.components.monitoring.graph.node.render.parallelGateway">
        <div class="code col-sm-6"></div>
        <div class="graph-container col-sm-6">
            <div data-na-portal-monitoring-graph data-na-portal-monitoring-graph-order-details
                 data-store="NodeDoc.store" data-graph-node-click-log></div>
        </div>
        <div class="col-sm-12"><details class="html-details"><summary>html</summary><div class="html"></div></details></div>
    </div>


    <h4 id="basic-activity-node-renderer">5.2.4 basic activity</h4>

    <p>
        The basic activity is simply renderer used only for documentation purposes, that is why it is the
        last one listed here. This renderer also does not use node's <code>data</code> property to render. But
        it renders the label based on the node id, if you change the <code>id</code> property on the editor the label
        on the view should change immediately. The render is defined on
        <code>na.components.monitoring.graph.node.render.basicActivity</code> variable.
    </p>


    <div data-doc-node-renderer class="row" data-render="na.components.monitoring.graph.node.render.basicActivity">
        <div class="code col-sm-6"></div>
        <div class="graph-container col-sm-6">
            <div data-na-portal-monitoring-graph data-na-portal-monitoring-graph-order-details
                 data-store="NodeDoc.store" data-graph-node-click-log></div>
        </div>
        <div class="col-sm-12"><details class="html-details"><summary>html</summary><div class="html"></div></details></div>
    </div>


    <h2 id="edges">6. Edges </h2>

    An edge is a link between two nodes of a graph, the following figure show an example of said edge:

    <div class="graph-container">
        <div data-na-portal-monitoring-graph
             data-na-portal-monitoring-graph-order-details
             data-store="storeEdge"
             data-graph-node-click-log></div>
    </div>

    <h3 id="edge-model">6.1 Edge model</h3>

    As shown previously on the <a href="#model">model</a> section, the model of a edge is the
    <code><b class="text-info">EdgeState</b></code> and contains the following fields:
    <ul>
        <li><label>source</label> - The edge source's node id;  </li>
        <li><label>target</label> - The edge target's node id;  </li>
        <li><label>sourceOverlay</label> - defines the symbol to show on the source end of the edge;  </li>
        <li><label>targetOverlay</label> - defines the symbol to show on the target end of the edge.  </li>
    </ul>

    <h3 id="edge-view">6.2 Edge view</h3>


    The next container show an example of a graph edge reflecting the edge state, the left side shows a form that
    specifies the edge state, and the right side shows the view that reflects it.


    <div data-doc-edge-renderer class="row" data-render="na.components.monitoring.graph.node.render.entry">
<pre class="col-sm-6 form-inline"><code><b class="text-info">EdgeState</b> {
    source: "activity1",
    target: "activity2",
    sourceOverlay: <select class="form-control" name="sourceOverlay"><option>none</option><option>arrow</option> </select>,
    targetOverlay: <select class="form-control" name="targetOverlay"><option>none</option><option>arrow</option> </select>,
}</code></pre>
        <div class="graph-container col-sm-6">
            <div data-na-portal-monitoring-graph data-na-portal-monitoring-graph-order-details
                 data-store="EdgeDoc.store" data-graph-node-click-log></div>
        </div>
    </div>

    <h2 id="graph-examples">7. Graph Editor & examples </h2>

    This <a href="graph.html">page</a> contain a graph editor with three columns: "examples", "state" and "view". The
    first column, "examples", shows a list examples of diagram states; when an example clicked, changes the content
    of the "state" column. The second column, "state", shows an editor with a state validator on the top bar. The third
    and last, column, "view" shows the diagram view that reflects the content of the "state" columns.

    <h2 id="references">8. References </h2>
    <p id="references-1"> 1. <i>AngularJS — Superheroic JavaScript MVW Framework</i> [<a href="https://angularjs.org">https://angularjs.org</a>] </p>
    <p id="references-2"> 2. <i>jsPlumb Toolkit - build Flowcharts, Diagrams and connectivity based applications fast</i> [<a href="https://jsplumbtoolkit.com">https://jsplumbtoolkit.com</a>] </p>
    <p id="references-3"> 3. <i>Home · dagrejs/dagre Wiki</i> [<a href="https://github.com/dagrejs/dagre/wiki">https://github.com/dagrejs/dagre/wiki</a>] </p>
    <p id="references-4"> 4. <i>BEM — Block Element Modifier</i> [<a href="http://getbem.com">http://getbem.com</a>] </p>


</div>

<script src="/fuxi/lib/codemirror/5.1/js/codemirror.js"></script>
<script src="/fuxi/lib/codemirror/5.1/mode/javascript/javascript.js"></script>
<script src="/fuxi/lib/codemirror/5.1/mode/xml/xml.js"></script>

<script src="/fuxi/resources/fuxi_pt.js"></script>

<!-- Dagre -->
<script src="/na-ext/js/dagre.js"></script>

<!-- JSPlumb Diagram -->
<script src="/fuxi/lib/jsplumb/2.0/js/jsPlumb-2.0.4-min.js"></script>
<script src="/naportalbase/assets/javascripts/dateformats.js"></script>
<script type="text/javascript" src="/naportalbase/assets/javascripts/routes"></script>
<script src="/naportalbase/assets/javascripts/na-portal-frontend-basemodule-scripts.js"></script>

<script src="/fuxi/lib/jqueryui/1.10/jquery-ui-1.10.4.custom.min.js"></script>
<script src="/fuxi/lib/uilayout/jquery.layout-latest.js"></script>
<script src="/fuxi/components/jstree/dist/fxtree.js"></script>
<script src="/fuxi/components/selectboxtree/dist/fxselectboxtree.js"></script>
<script src="/fuxi/lib/spectrum/1.6/js/spectrum.min.js"></script>

<script src="/monitoring/assets/javascripts/na-portal-frontend-monitoring-scripts.js"></script>


<script src="doc-intro.directive.js"></script>
<script src="doc-node-render.directive.js"></script>
<script src="doc-edge-render.directive.js"></script>
<script src="doc-layout-render.directive.js"></script>
<script src="graph-node-click-log.directive.js"></script>
<script>
    angular.bootstrap($('#application'),["application", "newcs", "naportalbase", "naportalmonitoring"]);
</script>



</body>
</html>