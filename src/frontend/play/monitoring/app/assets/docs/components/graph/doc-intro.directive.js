(function () {
    angular.module(modules.na.monitoring).directive("docIntro", [filterListDirective]);
    function filterListDirective() {
        return {
            restrict: 'A',
            terminal: false,
            link: {
                pre: function (scope) {


                        var introGraphState = {
                            nodes: [
                                { id: "entry", nodeType: "entry"},
                                { id: "HelloWorld", nodeType: "activity"},
                                { id: "exit", nodeType: "exit"}
                            ],
                            edges: [
                                { source: "entry", target: "HelloWorld" },
                                { source: "HelloWorld", target: "exit" }
                            ],
                            nodeTypes: {
                                entry: { render: "na.components.monitoring.graph.node.render.entry" },
                                exit: { render: "na.components.monitoring.graph.node.render.exit" },
                                activity: { render: "na.components.monitoring.graph.node.render.basicActivity" }
                            },
                            layout: { orientation: "horizontal" }
                        };


                    var nodeGraphState = {
                        nodes: [
                            { id: "HelloWorld", nodeType: "activity"}
                        ],
                        nodeTypes: {
                            activity: { render: "na.components.monitoring.graph.node.render.basicActivity" }
                        },
                        layout: { orientation: "horizontal" }
                    };

                    var edgeGraphState = {
                        nodes: [
                            { id: "entry", nodeType: "entry"},
                            { id: "exit", nodeType: "exit"}
                        ],
                        edges: [
                            { source: "entry", target: "exit" }
                        ],
                        nodeTypes: {
                            entry: { render: "na.components.monitoring.graph.node.render.entry" },
                            exit: { render: "na.components.monitoring.graph.node.render.exit" },
                            activity: { render: "na.components.monitoring.graph.node.render.basicActivity" }
                        },
                        layout: { orientation: "horizontal" }
                    };

                    scope.storeIntro = na.monitoring.common.Store(na.components.monitoring.graph.store);
                    scope.storeIntro.setters.setGraph(introGraphState);

                    scope.storeNode = na.monitoring.common.Store(na.components.monitoring.graph.store);
                    scope.storeNode.setters.setGraph(nodeGraphState);

                    scope.storeEdge = na.monitoring.common.Store(na.components.monitoring.graph.store);
                    scope.storeEdge.setters.setGraph(edgeGraphState);


                    na.monitoring.common.Store.enableDebug({listeners: false});


                }
            }
        };
    }


}());
