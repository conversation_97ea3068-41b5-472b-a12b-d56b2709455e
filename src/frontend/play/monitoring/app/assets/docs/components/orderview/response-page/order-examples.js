(function () {
    var graphExamples = {
        "basic": function () {
            return {
               responseText: '{ "externalId": "254" }',
               status: {
                   value:"OK",
                   errorMessage: ""
               },
               destination: "https://som.uat1.idahs.com/alt/nossis/notification",
               retries: []
            };
        },

        "delivering first response": function () {
            return {
                responseText: json,
                status: {
                    value:"DELIVERING",
                    errorMessage: ""
                },
                destination: "https://som.uat1.idahs.com/alt/nossis/notification",
                retries: []
            };
        },

        "error - some attempts left": function () {
            return {
                responseText: json,
                status: {
                    value:"FAILED",
                    errorMessage: "503 Service not Found"
                },
                destination: "https://som.uat1.idahs.com/alt/nossis/notification",
                retries: []
            };
        },

        "error - no attempts left": function () {
            return {
                responseText: json,
                status: {
                    value:"FAILED",
                    errorMessage: "503 Service not Found"
                },
                destination: "https://som.uat1.idahs.com/alt/nossis/notification",
                retries: []
            };
        }
    };

    window.doc = window.doc || {};
    window.doc.graphExamples = graphExamples;

    Object.keys(graphExamples).forEach(function (example) {
        $("<button data-example=\""+example+"\"> "+example+" </button>").appendTo($(".examples"));
    });


    var json = '{\n' +
        '    "externalId": "254",\n' +
        '    "category": "SALES_ORDER",\n' +
        '    "orderDate": "2017-12-15T14:00:03",\n' +
        '    "requestedCompletionDate": "2017-12-15T14:00:03",\n' +
        '    "relatedParty": [{\n' +
        '        "id": "40992497",\n' +
        '        "role": "customer",\n' +
        '        "property": [{\n' +
        '            "name": "first-name",\n' +
        '            "value": "NL"\n' +
        '        },\n' +
        '        {\n' +
        '            "name": "last-name",\n' +
        '            "value": "NL"\n' +
        '        },\n' +
        '        {\n' +
        '            "name": "type",\n' +
        '            "value": "SOHO"\n' +
        '        }]\n' +
        '    }],\n' +
        '    "orderItem": [{\n' +
        '        "id": "AF29425FA0434174A8AA17D5F66BCA2F",\n' +
        '        "action": "delete",\n' +
        '        "serviceSpecification": {\n' +
        '            "id": "CFS.3PGPON.BUNDLE"\n' +
        '        },\n' +
        '        "service": {\n' +
        '            "id": "0780173981601_3GPONBundle",\n' +
        '            "category": "CFS",\n' +
        '            "description": "3P GPON Bundle",\n' +
        '            "place": [{\n' +
        '                "role": "installation address",\n' +
        '                "property": [{\n' +
        '                    "name": "postCode",\n' +
        '                    "value": "11801"\n' +
        '                },\n' +
        '                {\n' +
        '                    "name": "address",\n' +
        '                    "value": "311ACRE LN"\n' +
        '                },\n' +
        '                {\n' +
        '                    "name": "state",\n' +
        '                    "value": "NY"\n' +
        '                },\n' +
        '                {\n' +
        '                    "name": "city",\n' +
        '                    "value": ""\n' +
        '                },\n' +
        '                {\n' +
        '                    "name": "country",\n' +
        '                    "value": "USA"\n' +
        '                },\n' +
        '                {\n' +
        '                    "name": "timeZone",\n' +
        '                    "value": "UTC-05:00"\n' +
        '                }]\n' +
        '            }]\n' +
        '        }\n' +
        '    },\n' +
        '    {\n' +
        '        "id": "0C79799C5B8E4C4080284F204D23F6E8",\n' +
        '        "action": "delete",\n' +
        '        "serviceSpecification": {\n' +
        '            "id": "CFS.GPONACCESS"\n' +
        '        },\n' +
        '        "orderItemRelationship": [{\n' +
        '            "type": "bundled",\n' +
        '            "id": "AF29425FA0434174A8AA17D5F66BCA2F"\n' +
        '        }],\n' +
        '        "service": {\n' +
        '            "id": "0780173981601_FiberGateway",\n' +
        '            "category": "CFS",\n' +
        '            "description": "GPON Access",\n' +
        '            "serviceCharacteristic": [{\n' +
        '                "name": "reserveId",\n' +
        '                "value": "1808749"\n' +
        '            }],\n' +
        '            "resource": [{\n' +
        '                "name": "accessNetworkAccount",\n' +
        '                "resource": {\n' +
        '                    "id": "0780173981601_0F7B050FB0D240F19B196147E376ED77",\n' +
        '                    "type": "GPON.ACCOUNT",\n' +
        '                    "property": [{\n' +
        '                        "name": "action",\n' +
        '                        "value": "delete"\n' +
        '                    },\n' +
        '                    {\n' +
        '                        "name": "category",\n' +
        '                        "value": "Account"\n' +
        '                    },\n' +
        '                    {\n' +
        '                        "name": "description",\n' +
        '                        "value": "GPON Account"\n' +
        '                    },\n' +
        '                    {\n' +
        '                        "name": "ipType",\n' +
        '                        "value": "public"\n' +
        '                    },\n' +
        '                    {\n' +
        '                        "name": "ipVersion",\n' +
        '                        "value": "ipv4"\n' +
        '                    }]\n' +
        '                }\n' +
        '            },\n' +
        '            {\n' +
        '                "name": "cpeOntRg",\n' +
        '                "resource": {\n' +
        '                    "id": "0780173981601_BD4F77B2411B4D989D3AC173F6B6F65D",\n' +
        '                    "type": "PD.CPE.ONT_RG",\n' +
        '                    "property": [{\n' +
        '                        "name": "action",\n' +
        '                        "value": "delete"\n' +
        '                    },\n' +
        '                    {\n' +
        '                        "name": "category",\n' +
        '                        "value": "Equipment"\n' +
        '                    },\n' +
        '                    {\n' +
        '                        "name": "description",\n' +
        '                        "value": "RG and ONT"\n' +
        '                    },\n' +
        '                    {\n' +
        '                        "name": "regime",\n' +
        '                        "value": "rent"\n' +
        '                    },\n' +
        '                    {\n' +
        '                        "name": "context",\n' +
        '                        "value": "1"\n' +
        '                    },\n' +
        '                    {\n' +
        '                        "name": "vendor",\n' +
        '                        "value": "ALTICELABS"\n' +
        '                    },\n' +
        '                    {\n' +
        '                        "name": "model",\n' +
        '                        "value": "GR241AG"\n' +
        '                    },\n' +
        '                    {\n' +
        '                        "name": "version",\n' +
        '                        "value": "1.0"\n' +
        '                    },\n' +
        '                    {\n' +
        '                        "name": "mac",\n' +
        '                        "value": "00069121570F"\n' +
        '                    },\n' +
        '                    {\n' +
        '                        "name": "serialNumber",\n' +
        '                        "value": "5054494E9121570F"\n' +
        '                    }]\n' +
        '                }\n' +
        '            }],\n' +
        '            "component": [{\n' +
        '                "name": "gponProfile",\n' +
        '                "component": {\n' +
        '                    "id": "0780173981601_02475A5056934FE5B37F7C36C546C2BE",\n' +
        '                    "type": "GPON.PROFILE",\n' +
        '                    "property": [{\n' +
        '                        "name": "action",\n' +
        '                        "value": "delete"\n' +
        '                    },\n' +
        '                    {\n' +
        '                        "name": "category",\n' +
        '                        "value": "Service Plan"\n' +
        '                    },\n' +
        '                    {\n' +
        '                        "name": "description",\n' +
        '                        "value": "GPON Profile"\n' +
        '                    },\n' +
        '                    {\n' +
        '                        "name": "gponProfile",\n' +
        '                        "value": "GPP1"\n' +
        '                    }]\n' +
        '                }\n' +
        '            }]\n' +
        '        }\n' +
        '    },\n' +
        '    {\n' +
        '        "id": "A0C93C8EB075418A88B1427F5F53A82B",\n' +
        '        "action": "delete",\n' +
        '        "serviceSpecification": {\n' +
        '            "id": "CFS.HSI"\n' +
        '        },\n' +
        '        "orderItemRelationship": [{\n' +
        '            "type": "bundled",\n' +
        '            "id": "AF29425FA0434174A8AA17D5F66BCA2F"\n' +
        '        }],\n' +
        '        "service": {\n' +
        '            "id": "0780173981601_Data",\n' +
        '            "category": "CFS",\n' +
        '            "description": "HSI",\n' +
        '            "serviceRelationship": [{\n' +
        '                "type": "reliesOn",\n' +
        '                "service": {\n' +
        '                    "id": "0780173981601_FiberGateway"\n' +
        '                }\n' +
        '            }],\n' +
        '            "component": [{\n' +
        '                "name": "hsiProfile",\n' +
        '                "component": {\n' +
        '                    "id": "0780173981601_BCADE23C6DE948B89B0BE6FEDD9AC617",\n' +
        '                    "type": "HSI.PROFILE",\n' +
        '                    "property": [{\n' +
        '                        "name": "action",\n' +
        '                        "value": "delete"\n' +
        '                    },\n' +
        '                    {\n' +
        '                        "name": "category",\n' +
        '                        "value": "Service Plan"\n' +
        '                    },\n' +
        '                    {\n' +
        '                        "name": "description",\n' +
        '                        "value": "Internet Profile"\n' +
        '                    },\n' +
        '                    {\n' +
        '                        "name": "hsiProfile",\n' +
        '                        "value": "FTTHBIZ1500"\n' +
        '                    }]\n' +
        '                }\n' +
        '            }]\n' +
        '        }\n' +
        '    }],\n' +
        '    "requesterCallback": "https://som.uat1.idahs.com/alt/nossis/notification"\n' +
        '}';

}());
