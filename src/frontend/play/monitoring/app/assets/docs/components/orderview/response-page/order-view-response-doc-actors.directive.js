(function () {
    angular.module(modules.na.monitoring) .directive("orderViewRetryLogs", [ OrderViewResponsePageDirective ]);

    function OrderViewResponsePageDirective() {
        return {
            restrict: 'A',
            terminal: false,
            link: function (scope, element) {
                element.on("click","[data-action='show-attempt-history']", function () {
                    alert("clicked action 'show-attempt-history'");
                });

            }
        };
    }
}());