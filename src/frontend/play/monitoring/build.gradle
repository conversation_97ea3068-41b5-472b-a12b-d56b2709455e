dependencies {
    play project(":na-portal-frontend-basemodule")
    play project(":na-portal-frontend-catalog-basemodule")
    play project(":na-portal-frontend-router")

    // nossis-frmwrk
    play("pt.ptinovacao.nossis-frmwrk:exceptions:$version_pt_ptinovacao_nossis_frmwrk")

    // nossis-ui
    play("pt.ptinovacao.nossis-ui:nossis-ui-messages:$version_pt_ptinovacao_nossis_ui") {
        transitive true
        exclude group: "com.typesafe.play", module: "play-logback_2.12"
        exclude group: "ch.qos.logback", module: "*"
    }
    play("pt.ptinovacao.nossis-ui:nossis-ui-security-adapter:$version_pt_ptinovacao_nossis_ui") {
        transitive true
        exclude group: "com.typesafe.play", module: "play-logback_2.12"
        exclude group: "ch.qos.logback", module: "*"
    }

    // nossis-inv
    play("pt.ptinovacao.nossis-inv:nossis-inv-kernel-catalog-client-api:$version_pt_ptinovacao_nossis_inv")

    // nossis-security-provider-iam
    playRun("pt.ptinovacao.nossis-security:nossis-security-provider-iam:$version_pt_ptinovacao_nossis_security") {
        transitive true
        exclude group: "com.typesafe.play", module: "play-logback_2.12"
        exclude group: "ch.qos.logback", module: "*"
    }

    // na
    play("pt.ptinovacao.na:na-swe-order-model:$version_pt_ptinovacao_na") {
        transitive true
        exclude group: "org.glassfish.hk2.external", module: "javax.inject"
    }
    play("pt.ptinovacao.na:na-order-followup-dto:$version_pt_ptinovacao_na")
    play("pt.ptinovacao.na:na-swe-dsl-external-actions-model:$version_pt_ptinovacao_na")
    play("pt.ptinovacao.na:na-swe-async-notifications-handling-model:$version_pt_ptinovacao_na")
    play("pt.ptinovacao.na:na-swe-external-systems-handling-model:$version_pt_ptinovacao_na")

    // na-commons
    play("pt.ptinovacao.na-commons:na-commons-cluster-service-locator-core:$version_pt_ptinovacao_na_commons")

    // asf
    play("pt.ptinovacao.asf:asf-cluster-api:$version_pt_ptinovacao_asf")
    play("pt.ptinovacao.asf:asf-cluster-api-enumerations:$version_pt_ptinovacao_asf")
    playRun("pt.ptinovacao.asf:asf-cluster-implementations-zookeeper:$version_pt_ptinovacao_asf")
    play("pt.ptinovacao.asf:asf-components-manager:$version_pt_ptinovacao_asf")
    playRun("pt.ptinovacao.asf:asf-model:$version_pt_ptinovacao_asf")

    // external
    play("com.fasterxml.jackson.core:jackson-annotations:$version_com_fasterxml_jackson")
    play("com.fasterxml.jackson.core:jackson-databind:$version_com_fasterxml_jackson")

    play("com.typesafe:config:$version_com_typesafe_config")

    play("com.typesafe.play:play_$version_org_scala_base:$version_com_typesafe_play")
    play("com.typesafe.play:play-java_$version_org_scala_base:$version_com_typesafe_play")
    play("com.typesafe.play:play-ws_$version_org_scala_base:$version_com_typesafe_play")

    play("com.typesafe.play:twirl-api_$version_org_scala_base:$version_com_typesafe_play_twirl_api")

    play("com.google.guava:guava:$version_com_google_guava")

    play("com.google.inject:guice:$version_com_google_inject")

    play("org.apache.commons:commons-lang3:$version_org_apache_commons_commons_lang3")

    play("javax.inject:javax.inject:$version_javax_inject")

    play("org.scala-lang:scala-library:$version_org_scala_lang")
    play("org.scala-lang.modules:scala-xml_$version_org_scala_base:$version_org_scala_lang_modules")

    playRun("org.jetbrains.kotlin:kotlin-stdlib:${version_org_jetbrains_kotlin}")
}