// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/portal/conf/naportal.routes
// @DATE:Tue Jul 01 18:12:15 WEST 2025

import play.api.mvc.Call


import _root_.controllers.Assets.Asset

// @LINE:8
package na.portal.controllers {

  // @LINE:14
  class ReverseAssets(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:14
    def versioned(file:Asset): Call = {
      implicit lazy val _rrc = new play.core.routing.ReverseRouteContext(Map(("path", "/public"))); _rrc
      Call("GET", _prefix + { _defaultPrefix } + "portal/assets/" + implicitly[play.api.mvc.PathBindable[Asset]].unbind("file", file))
    }
  
  }

  // @LINE:8
  class ReverseApplication(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:11
    def aboutExternalLibraries(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "portal/about/externallibraries")
    }
  
    // @LINE:8
    def resume(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "portal/resume")
    }
  
  }


}
