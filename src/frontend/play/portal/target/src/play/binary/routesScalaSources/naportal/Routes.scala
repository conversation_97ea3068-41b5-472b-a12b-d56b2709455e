// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/portal/conf/naportal.routes
// @DATE:Tue Jul 01 09:50:00 WEST 2025

package naportal

import play.core.routing._
import play.core.routing.HandlerInvokerFactory._

import play.api.mvc._

import _root_.controllers.Assets.Asset

class Routes(
  override val errorHandler: play.api.http.HttpErrorHandler, 
  // @LINE:8
  Application_0: na.portal.controllers.Application,
  // @LINE:14
  Assets_1: na.portal.controllers.Assets,
  // @LINE:20
  pppoort_Routes_0: pppoort.Routes,
  val prefix: String
) extends GeneratedRouter {

   @javax.inject.Inject()
   def this(errorHandler: play.api.http.HttpErrorHandler,
    // @LINE:8
    Application_0: na.portal.controllers.Application,
    // @LINE:14
    Assets_1: na.portal.controllers.Assets,
    // @LINE:20
    pppoort_Routes_0: pppoort.Routes
  ) = this(errorH<PERSON><PERSON>, Application_0, Assets_1, pppoort_Routes_0, "/")

  def withPrefix(prefix: String): Routes = {
    naportal.RoutesPrefix.setPrefix(prefix)
    new Routes(errorHandler, Application_0, Assets_1, pppoort_Routes_0, prefix)
  }

  private[this] val defaultPrefix: String = {
    if (this.prefix.endsWith("/")) "" else "/"
  }

  def documentation = List(
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """portal/resume""", """na.portal.controllers.Application.resume()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """portal/about/externallibraries""", """na.portal.controllers.Application.aboutExternalLibraries()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """portal/assets/""" + "$" + """file<.+>""", """na.portal.controllers.Assets.versioned(path:String = "/public", file:Asset)"""),
    prefixed_pppoort_Routes_0_3.router.documentation,
    Nil
  ).foldLeft(List.empty[(String,String,String)]) { (s,e) => e.asInstanceOf[Any] match {
    case r @ (_,_,_) => s :+ r.asInstanceOf[(String,String,String)]
    case l => s ++ l.asInstanceOf[List[(String,String,String)]]
  }}


  // @LINE:8
  private[this] lazy val na_portal_controllers_Application_resume0_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("portal/resume")))
  )
  private[this] lazy val na_portal_controllers_Application_resume0_invoker = createInvoker(
    Application_0.resume(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "naportal",
      "na.portal.controllers.Application",
      "resume",
      Nil,
      "GET",
      this.prefix + """portal/resume""",
      """""",
      Seq()
    )
  )

  // @LINE:11
  private[this] lazy val na_portal_controllers_Application_aboutExternalLibraries1_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("portal/about/externallibraries")))
  )
  private[this] lazy val na_portal_controllers_Application_aboutExternalLibraries1_invoker = createInvoker(
    Application_0.aboutExternalLibraries(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "naportal",
      "na.portal.controllers.Application",
      "aboutExternalLibraries",
      Nil,
      "GET",
      this.prefix + """portal/about/externallibraries""",
      """ External Libraries Versions and Licenses""",
      Seq()
    )
  )

  // @LINE:14
  private[this] lazy val na_portal_controllers_Assets_versioned2_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("portal/assets/"), DynamicPart("file", """.+""",false)))
  )
  private[this] lazy val na_portal_controllers_Assets_versioned2_invoker = createInvoker(
    Assets_1.versioned(fakeValue[String], fakeValue[Asset]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "naportal",
      "na.portal.controllers.Assets",
      "versioned",
      Seq(classOf[String], classOf[Asset]),
      "GET",
      this.prefix + """portal/assets/""" + "$" + """file<.+>""",
      """ Map static resources from the /public folder to the /assets URL path""",
      Seq()
    )
  )

  // @LINE:20
  private[this] val prefixed_pppoort_Routes_0_3 = Include(pppoort_Routes_0.withPrefix(this.prefix + (if (this.prefix.endsWith("/")) "" else "/") + ""))


  def routes: PartialFunction[RequestHeader, Handler] = {
  
    // @LINE:8
    case na_portal_controllers_Application_resume0_route(params@_) =>
      call { 
        na_portal_controllers_Application_resume0_invoker.call(Application_0.resume())
      }
  
    // @LINE:11
    case na_portal_controllers_Application_aboutExternalLibraries1_route(params@_) =>
      call { 
        na_portal_controllers_Application_aboutExternalLibraries1_invoker.call(Application_0.aboutExternalLibraries())
      }
  
    // @LINE:14
    case na_portal_controllers_Assets_versioned2_route(params@_) =>
      call(Param[String]("path", Right("/public")), params.fromPath[Asset]("file", None)) { (path, file) =>
        na_portal_controllers_Assets_versioned2_invoker.call(Assets_1.versioned(path, file))
      }
  
    // @LINE:20
    case prefixed_pppoort_Routes_0_3(handler) => handler
  }
}
