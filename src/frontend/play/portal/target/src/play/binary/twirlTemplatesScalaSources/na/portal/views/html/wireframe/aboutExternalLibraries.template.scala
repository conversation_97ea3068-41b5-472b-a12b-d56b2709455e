
package na.portal.views.html.wireframe

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import pt.alticelabs.nossis.messages.api.Messages
/*2.2*/import com.typesafe.config.ConfigObject

object aboutExternalLibraries extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[ConfigObject,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*4.2*/(module: ConfigObject):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*6.2*/libraryUrl/*6.12*/ = {{
    module.toConfig.getString("library-url")
}};def /*9.2*/libraryName/*9.13*/ = {{
    module.toConfig.getString("library-name")
}};def /*12.2*/libraryVersion/*12.16*/ = {{
    module.toConfig.getString("library-version")
}};def /*15.2*/licenseList/*15.13*/ = {{

    var stringBuilder = new StringBuilder
    var libraryLicenses = module.toConfig.getConfigList("library-licenses")
    var i = 0
    libraryLicenses.forEach(libraryLicense => {
        if(i != 0) stringBuilder.append(", ")

        val name = libraryLicense.getString("name")
        val url = libraryLicense.getString("url")
        val link = s"<a href='$url'>$name</a>"
        stringBuilder.append(link)

        i = i + 1
    })

    Html(stringBuilder.toString())
}};
Seq[Any](format.raw/*4.24*/("""

"""),format.raw/*8.2*/("""
"""),format.raw/*11.2*/("""
"""),format.raw/*14.2*/("""
"""),format.raw/*32.2*/("""

"""),format.raw/*34.1*/("""<div class="form-group">
    <label class="col-md-4 control-label">
        <a href='"""),_display_(/*36.19*/libraryUrl),format.raw/*36.29*/("""'>"""),_display_(/*36.32*/libraryName),format.raw/*36.43*/(""" """),format.raw/*36.44*/("""- """),_display_(/*36.47*/libraryVersion),format.raw/*36.61*/("""</a>
    </label>
    <div class="col-md-8">
        <p class="form-control-static">"""),_display_(/*39.41*/Messages/*39.49*/.get("nossis.portal.modules.licence.conditions")),format.raw/*39.97*/(""" """),_display_(/*39.99*/licenseList),format.raw/*39.110*/("""</p>
    </div>
</div>
"""))
      }
    }
  }

  def render(module:ConfigObject): play.twirl.api.HtmlFormat.Appendable = apply(module)

  def f:((ConfigObject) => play.twirl.api.HtmlFormat.Appendable) = (module) => apply(module)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:50:00 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/portal/target/TwirlSource/na/portal/views/wireframe/aboutExternalLibraries.scala.html
                  HASH: 8250db386a5b606d17872c16149f6a4392855647
                  MATRIX: 671->1|728->52|1095->94|1195->119|1213->129|1278->183|1297->194|1364->249|1387->263|1457->321|1477->332|1988->116|2016->181|2044->247|2072->319|2100->813|2129->815|2242->901|2273->911|2303->914|2335->925|2364->926|2394->929|2429->943|2541->1028|2558->1036|2627->1084|2656->1086|2689->1097
                  LINES: 24->1|25->2|30->4|34->6|34->6|36->9|36->9|38->12|38->12|40->15|40->15|58->4|60->8|61->11|62->14|63->32|65->34|67->36|67->36|67->36|67->36|67->36|67->36|67->36|70->39|70->39|70->39|70->39|70->39
                  -- GENERATED --
              */
          