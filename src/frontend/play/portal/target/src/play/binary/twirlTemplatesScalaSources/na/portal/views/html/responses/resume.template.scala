
package na.portal.views.html.responses

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.portal.views.tags.i18n
/*2.2*/import play.mvc.Http

object resume extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*3.1*/("""<!DOCTYPE html>
<html lang=""""),_display_(/*4.14*/{Http.Context.current().lang().code()}),format.raw/*4.52*/("""">
	<head>
		<meta charset="utf-8">
		<title>"""),_display_(/*7.11*/i18n("na.information.title")),format.raw/*7.39*/("""</title>
	</head>
	<body>
		<h5 class="fx-op-module-main-title resume-title">"""),_display_(/*10.53*/i18n("na.information.title")),format.raw/*10.81*/("""</h5>
		<p>
			<b>"""),_display_(/*12.8*/i18n("na.information.title")),format.raw/*12.36*/("""</b><br />
			"""),_display_(/*13.5*/play/*13.9*/.twirl.api.Html(i18n("na.information.description"))),format.raw/*13.60*/("""
		"""),format.raw/*14.3*/("""</p>
		<p>
			<b>"""),_display_(/*16.8*/i18n("na.information.customerbenefits.title")),format.raw/*16.53*/("""</b><br />
			"""),_display_(/*17.5*/play/*17.9*/.twirl.api.Html(i18n("na.information.customerbenefits.description"))),format.raw/*17.77*/("""
		"""),format.raw/*18.3*/("""</p>
		<p>
			<b>"""),_display_(/*20.8*/i18n("na.information.mainfunctionalities.title")),format.raw/*20.56*/("""</b><br />
			"""),_display_(/*21.5*/play/*21.9*/.twirl.api.Html(i18n("na.information.mainfunctionalities.description"))),format.raw/*21.80*/("""
		"""),format.raw/*22.3*/("""</p>
	</body>
</html>
"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:50:00 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/portal/target/TwirlSource/na/portal/views/responses/resume.scala.html
                  HASH: be7f525fe5c5297788dbb2c5f17c74bb276e9b66
                  MATRIX: 671->1|711->36|1119->58|1175->88|1233->126|1308->175|1356->203|1464->284|1513->312|1560->333|1609->361|1651->377|1663->381|1735->432|1766->436|1812->456|1878->501|1920->517|1932->521|2021->589|2052->593|2098->613|2167->661|2209->677|2221->681|2313->752|2344->756
                  LINES: 24->1|25->2|35->3|36->4|36->4|39->7|39->7|42->10|42->10|44->12|44->12|45->13|45->13|45->13|46->14|48->16|48->16|49->17|49->17|49->17|50->18|52->20|52->20|53->21|53->21|53->21|54->22
                  -- GENERATED --
              */
          