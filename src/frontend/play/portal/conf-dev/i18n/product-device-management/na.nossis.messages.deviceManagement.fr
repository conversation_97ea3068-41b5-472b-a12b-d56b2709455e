#### Device Management ABOUT ####
nossis.portal.title = Device Management
nossis.portal.name = Device Management
nossis.portal.slogan = Suite NOSSIS
nossis.portal.version = version:

#Login
na.login.title.key.context.portal=Device Management
na.login.title.key.main.label=Suite Device Management
na.login.title.key.product.label=Device Management

#Resumos NA
na.information.description = Device Management est une plate-forme qui gère les appareils via un ensemble de protocoles, standard ou propriétaires.
na.information.mainfunctionalities.title = Principales fonctionnalités
na.information.mainfunctionalities.description = Configuration du service, par exemple VoIP ou HSI. <br>Gestion opérationnelle des équipements permettant les mises à jour du firmware, les tests de connectivité, la réinitialisation de la configuration et le redémarrage.<br>Protocoles standard pris en charge : CWMP (TR-069) et DOCSIS
na.information.customerbenefits.title = Avantages pour le client
na.information.customerbenefits.description = Automatisation des processus de configuration des dispositifs.<br>Fournit des processus d'auto-réparation, réduisant les temps d'immobilisation du service.<br>Améliore l'efficacité organisationnelle, en réduisant les interventions manuelles.
naportal.operationalview = Device Management
na.operationalview = Device Management

#footer
nossis.portal.footer.copyright = Device Management © 2024 Altice Labs - tous les droits réservés
