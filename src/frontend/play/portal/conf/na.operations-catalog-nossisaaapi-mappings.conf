nossisaaapi {

  mappings {
    instance = "pt.ptinovacao.nossis.security.provider.sca.SCAAuthorization"
    resource-groups = [
      {
        suffix = "@na.operations-catalog"
        resources = [
          {
            resource = "operations-catalog"
            C = "operations_catalog_create"
            R = "operations_catalog_read"
            U = "operations_catalog_update"
            D = "operations_catalog_delete"
            X = "operations_catalog_execute"
          }
          {
            resource = "na.operations-catalog.security-application-name"
            R = "application-name"
          }
        ]
      }
    ]
  }
}