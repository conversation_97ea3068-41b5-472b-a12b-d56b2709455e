customKey.keyId.1stSidekey = 1ª lateral
customKey.keyId.1stKeyOnTheLeftColumnOfThe1stmodule = 1ª do 1º módulo coluna esquerda
customKey.keyId.1stKeyOnTheRightColumnOfThe1stModule = 1ª do 1º módulo coluna direita
customKey.keyId.1stKeyOnTheLeftColumnOfThe2ndModule = 1ª do 2º módulo coluna esquerda
customKey.keyId.1stKeyOnTheRightColumnOfThe2ndModule = 1ª do 2º módulo coluna direita
customKey.keyId.1stKeyOnTheLeftColumnOfThe3rdModule = 1ª do 3º módulo coluna esquerda
customKey.keyId.1stKeyOnTheRightColumnOfThe3rdModule = 1ª do 3º módulo coluna direita
customKey.keyId.1stKeyOfThe1stPageOfThe1stModule = 1ª da 1ª página do 1ª módulo
customKey.keyId.1stKeyOfThe2ndPageOfThe1stModule = 1ª da 2ª página do 1ª módulo
customKey.keyId.1stKeyOfThe1stPageOfThe2ndModule = 1ª da 1ª página do 2ª módulo
customKey.keyId.1stKeyOfThe2ndPageOfThe2ndModule = 1ª da 2ª página do 2ª módulo
customKey.keyId.1stKeyOfThe1stPageOfThe3rdModule = 1ª da 1ª página do 3ª módulo
customKey.keyId.1stKeyOfThe2ndPageOfThe3rdModule = 1ª da 2ª página do 3ª módulo
customKey.keyId.1stKeyOfThe1stPageOfThe4thModule = 1ª da 1ª página do 4ª módulo
customKey.keyId.1stKeyOfThe2ndPageOfThe4thModule = 1ª da 2ª página do 4ª módulo
customKey.keyId.1stKey2ndPageOfTheScreen = 1ª da 2ª página de ecrã
customKey.keyId.1stKey3rdPageOfTheScreen = 1ª da 3ª página de ecrã
customKey.keyId.1stKeyOfThe1stModule = 1ª do 1º módulo
customKey.keyId.1stKeyOfThe2ndModule = 1ª do 2º módulo
customKey.type.opt.SpeedDial = Speed Dial
customKey.type.opt.CallPickup = Call Pickup
customKey.type.opt.blfWithSpeedDial = BLF com speedDial
customKey.type.opt.blfWithCallPickup = BLF com callPickup
customKey.type.opt.blfWithCallPickupAndSpeedDial = BLF com Call Pickup e Speed Dial
customKey.type.opt.blf = BLF
customKey.type.opt.aggregateLine = Linha Agregada
NADM.genericPhone.customKeys.customKey.type.intercomPaging = Intercom Paging
NADM.genericPhone.customKeys.customKey.type.multicastPaging = Multicast Paging

NADM.genericPhone.multicastPaging = Multicast Paging
NADM.genericPhone.multicastPaging.codecs = Multicast Paging Codec
NADM.genericPhone.multicastPaging.priorityActive = Paging Priority Active
NADM.genericPhone.multicastPaging.pagingBarge = Paging Barge
NADM.genericPhone.multicastPaging.listeningAddress1 = Multicast Listenging Address Priority 1
NADM.genericPhone.multicastPaging.listeningName1 = Multicast Listenging Name Priority 1
NADM.genericPhone.multicastPaging.listeningAddress2 = Multicast Listenging Address Priority 2
NADM.genericPhone.multicastPaging.listeningName2 = Multicast Listenging Address Priority 2
NADM.genericPhone.multicastPaging.listeningAddress3 = Multicast Listenging Address Priority 3
NADM.genericPhone.multicastPaging.listeningName3 = Multicast Listenging Name Priority 3
NADM.genericPhone.multicastPaging.listeningAddress4 = Multicast Listenging Address Priority 4
NADM.genericPhone.multicastPaging.listeningName4 = Multicast Listenging Name Priority 4
NADM.genericPhone.multicastPaging.listeningAddress5 = Multicast Listenging Address Priority 5
NADM.genericPhone.multicastPaging.listeningName5 = Multicast Listenging Name Priority 5
NADM.genericPhone.multicastPaging.listeningAddress6 = Multicast Listenging Address Priority 6
NADM.genericPhone.multicastPaging.listeningName6 = Multicast Listenging Name Priority 6
NADM.genericPhone.multicastPaging.listeningAddress7 = Multicast Listenging Address Priority 7
NADM.genericPhone.multicastPaging.listeningName7 = Multicast Listenging Name Priority 7
NADM.genericPhone.multicastPaging.listeningAddress8 = Multicast Listenging Address Priority 8
NADM.genericPhone.multicastPaging.listeningName8 = Multicast Listenging Name Priority 8
NADM.genericPhone.multicastPaging.listeningAddress9 = Multicast Listenging Address Priority 9
NADM.genericPhone.multicastPaging.listeningName9 = Multicast Listenging Name Priority 9
NADM.genericPhone.multicastPaging.listeningAddress10 = Multicast Listenging Address Priority 10
NADM.genericPhone.multicastPaging.listeningName10 = Multicast Listenging Name Priority 10

NADM.Equipment = Equipamento
NADM.name = Endereço MAC 
NADM.deviceInfo.time = Tempo
NADM.deviceInfo.time.dateformat = Formato da data
NADM.deviceInfo.time.timeformat = Formato do tempo
NADM.deviceInfo.time.timezone = Fuso horário
NADM.deviceInfo.daylightSavingTime = Horário de verão
NADM.deviceInfo.daylightSavingTime.enable = Habilitar
NADM.deviceInfo.daylightSavingTime.type = Tipo
NADM.deviceInfo.daylightSavingTime.startWeekDay = Start Week Day
NADM.deviceInfo.daylightSavingTime.startWeekDayOcurrence = Start Week Day Ocurrence
NADM.deviceInfo.daylightSavingTime.startMonth = Start Month
NADM.deviceInfo.daylightSavingTime.startDay = Start Day
NADM.deviceInfo.daylightSavingTime.startHour = Start Hour
NADM.deviceInfo.daylightSavingTime.endWeekDay = End Week Day
NADM.deviceInfo.daylightSavingTime.endWeekDayOcurrence = End Week Day Ocurrence
NADM.deviceInfo.daylightSavingTime.endMonth = End Month
NADM.deviceInfo.daylightSavingTime.endDay = End Day
NADM.deviceInfo.daylightSavingTime.endHour = End Hour
NADM.deviceInfo.daylightSavingTime.offset = Offset
NADM.deviceInfo.daylightSavingTime.options = Options
NADM.deviceInfo.DNS = DNS
NADM.deviceInfo.DNS.primary = DNS Primário
NADM.deviceInfo.DNS.secondary = DNS Secundário
NADM.deviceInfo.NTP = NTP
NADM.deviceInfo.NTP.primary = NTP Primário
NADM.deviceInfo.NTP.secondary = NTP Secundário
NADM.deviceInfo.lan = Lan
NADM.deviceInfo.lan.addressingType = Tipo de endereçamento 
NADM.deviceInfo.lan.ipAddress = Endereço IP
NADM.deviceInfo.lan.subnetMask = Máscara de sub-rede
NADM.deviceInfo.lan.defaultGateway = Default Gateway
NADM.deviceInfo.ldap = LDAP
NADM.deviceInfo.ldap.enable = Habilitar
NADM.deviceInfo.ldap.host = Host
NADM.deviceInfo.ldap.port = Porto
NADM.deviceInfo.ldap.username = Username
NADM.deviceInfo.ldap.password = Senha
NADM.deviceInfo.ldap.base = Base
NADM.deviceInfo.ldap.sort = Ordenar
NADM.deviceInfo.ldap.nameFilter = Name Filter
NADM.deviceInfo.ldap.numberFilter = Number Filter
NADM.deviceInfo.ldap.displayNameAttributes = Display Name Attributes
NADM.deviceInfo.ldap.displayNumberAttributes = Display Number Attributes
NADM.deviceInfo.ldap.displayName = Display Name
NADM.deviceInfo.ldap.lookupDisplayName = Lookup Display Name
NADM.deviceInfo.ldap.lookup = Lookup
NADM.deviceInfo.ldap.clientDn = Client Dn
NADM.deviceInfo.ldap.firstNameFilter = First Name Filter
NADM.deviceInfo.ldap.lastNameFilter = Last Name Filter
NADM.deviceInfo.ldap.searchItem3 = searchItem3
NADM.deviceInfo.ldap.searchFilter3 = Search Filter 3
NADM.deviceInfo.ldap.searchItem4 = Search Item 4
NADM.deviceInfo.ldap.searchFilter4 = Search Filter 4
NADM.deviceInfo.ldap.displayAttributes = Display Attributes
NADM.deviceInfo.ldap.mode = Mode
NADM.deviceInfo.ldap.version = Versão
NADM.deviceInfo.ldap.maxHits = Max Hits
NADM.deviceInfo.ldap.doInitialQuery = Fazer consulta inicial
NADM.deviceInfo.ldap.predictText = prever Texto
NADM.deviceInfo.ldap.authMethod = Método de Autenticação
NADM.deviceInfo.ldap.numberMapping = Number Mapping
NADM.deviceInfo.ldap.corpDirName = Nome do Directório Corporativo

NADM.genericPhone = Generic Phone
NADM.genericPhone.language = Língua
NADM.genericPhone.voiceVlanId = ID de Vlan de voz
NADM.genericPhone.dataVlanId = ID de Vlan de Dados
NADM.genericPhone.speakerGain = Speaker Gain
NADM.genericPhone.handsetGain = Handset Gain
NADM.genericPhone.headsetGain = Headset Gain
NADM.genericPhone.userPassword = Password de User
NADM.genericPhone.adminPassword = Password de Admin
NADM.genericPhone.screensaverEnable = Habilitar Screensaver 
NADM.genericPhone.screensaverWaitTime = Tempo de esperar para Screensaver
NADM.genericPhone.screensaverImageSelect = Selecionar imagem para Screensaver 
NADM.genericPhone.backgroundImageURL = URL para imagem de fundo 
NADM.genericPhone.backgroundImageText = Texto de fundo
NADM.genericPhone.backgroundImageSelect = Selecionar Imagem de fundo 
NADM.genericPhone.callPickupCode = Código de CallPickup 
NADM.genericPhone.callPickupAudioNotification = Call Pickup Audio Notification
NADM.genericPhone.callPickupTone = Tom de CallPickup
NADM.genericPhone.blfSubscriptionTimeout = BLF Subscription Time Out
NADM.genericPhone.customKeyPrimaryLine = CustomKey Primary Line
NADM.genericPhone.customKeys = Custom Keys
NADM.genericPhone.customKeys.customKey = Custom Key
NADM.genericPhone.customKeys.customKey.keyId = Key ID
NADM.genericPhone.customKeys.customKey.line = Line
NADM.genericPhone.customKeys.customKey.name = Name
NADM.genericPhone.customKeys.customKey.monitor = Monitor
NADM.genericPhone.customKeys.customKey.type = Custom key type
NADM.genericPhone.stationName = Station Name
NADM.genericPhone.dialplan = Dial Plan
NADM.genericPhone.dialplan.rule = Rule
NADM.genericPhone.dialplan.areaCode = Area Code
NADM.genericPhone.dialplan.areaCode.code = Code
NADM.genericPhone.dialplan.areaCode.minLength = Min Length
NADM.genericPhone.dialplan.areaCode.maxLength = Max Length
NADM.genericPhone.dialplan.blockOut = Block Out
NADM.genericPhone.dialplan.dialNow = Dial Now

NADM.firmwareUpgrade = Firmware Upgrade
NADM.firmwareUpgrade.enable = Habilitar
NADM.firmwareUpgrade.expectedVersion = Versão esperada
NADM.firmwareUpgrade.migrateConfig = Migrar Configurações
NADM.firmwareUpgrade.scheduleEnabled = Habilitar Calendarização
NADM.firmwareUpgrade.scheduleStartTime = Calendarizar tempo de início
NADM.firmwareUpgrade.scheduleEndTime = Calendarizar tempo de fim
NADM.firmwareUpgrade.file = Ficheiro de Firmware 
NADM.firmwareUpgrade.customUrl = Custom URL
NADM.firmwareUpgrade.associatedEquipmentEnabled = Associated Equipment Enabled
NADM.firmwareUpgrade.associatedEquipmentFile = Associated Equipment File
NADM.firmwareUpgrade.associatedEquipmentCustomUrl = Associated Equipment CustomUrl

NADM.services = Serviços
NADM.services.service = Serviço
NADM.services.service.name = Nome
NADM.services.service.serviceData = Service Data
NADM.services.service.voiceProfile = Voice Profile
NADM.services.service.serviceData.voiceProfile = Voice Profile
NADM.services.service.voiceProfile.line = Linha
NADM.services.service.voiceProfile.password = Password
NADM.services.service.voiceProfile.codecs = Codecs
NADM.service.service.voiceProfile.dialplan = Dial Plan
NADM.services.service.voiceProfile.proxy = Proxy
NADM.services.service.voiceProfile.outboundproxy = OutboundProxy
NADM.services.service.voiceProfile.displayName = Display Name
NADM.advancedData = Advanced Data

NADM.regex.mac = Erro: Endereço MAC inválido
NADM.regex.ip = Erro: Endereço IP inválido