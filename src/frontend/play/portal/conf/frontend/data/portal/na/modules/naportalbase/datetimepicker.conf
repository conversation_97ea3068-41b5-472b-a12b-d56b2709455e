dateTimePickerDefinition {
  dateFormat = DD/MM/YYYY
  pickDate = true
  pickTime = false        // enables/disables the time picker
  useMinutes = false      // enables/disables the minutes picker
  useSeconds = false      // enables/disables the seconds picker
  useCurrent = false      // when true, picker will set the value to the current date/time
  minuteStepping = 1      // set the minute stepping
  minDate = 01/01/2014    // set a minimum date
  maxDate = 31/12/2099    // set a maximum date (defaults to today +100 years)
  showToday = true        // shows the today indicator
  language = "pt"         // sets language locale
  defaultDate = ""        // sets a default date, accepts js dates, strings and moment objects
  disabledDates = []      // an array of dates that cannot be selected
  enabledDates = []       // an array of dates that can be selected
  icons = {
    time = ""
    date = ""
    up = ""
    down = ""
  }
  useStrict = true        // use "strict" when validating dates
  sideBySide = false      // show the date and time picker side by side
  daysOfWeekDisabled = []
}