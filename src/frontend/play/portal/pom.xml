<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>pt.ptinovacao.na-portal</groupId>
        <artifactId>na-portal-frontend-play</artifactId>
        <version>10.3.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>na-portal-frontend-portal</artifactId>
    <packaging>jar</packaging>

    <properties>
        <skipGradleWrapper>true</skipGradleWrapper>
        <skipNpmTests>true</skipNpmTests>
    </properties>

    <!--
        These dependencies serve the purpose of quickly validating the frontend existing dependencies.
        It's important to note that the actual POM file that will be published to the Maven repository is generated by <PERSON><PERSON><PERSON>.
        You can find this POM file in the './target/publications/maven/pom-default.xml' directory.
        The dependencies listed in this POM file are derived from the generated POM file,
        which, in turn, is created based on the 'build.gradle' file where dependencies are explicitly declared.
        For consistency, it's essential to remember that whenever you add a dependency in Gradle,
        it must also be included in this POM file.
     -->
    <dependencies>
        <!-- ########################################## -->
        <!-- # NA PORTAL                              # -->
        <!-- ########################################## -->
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-frontend-router</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- ########################################## -->
        <!-- # NOSSIS UI                              # -->
        <!-- ########################################## -->
        <dependency>
            <groupId>pt.ptinovacao.nossis-ui</groupId>
            <artifactId>nossis-ui-portal-frmwrk</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.typesafe.play</groupId>
                    <artifactId>play-logback_${version.org.scala.base}</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.typesafe.play</groupId>
                    <artifactId>play-java-jpa_${version.org.scala.base}</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.nossis-ui</groupId>
            <artifactId>nossis-ui-portal-frmwrk-assets</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.nossis-ui</groupId>
            <artifactId>nossis-ui-messages</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.typesafe.play</groupId>
                    <artifactId>play-logback_${version.org.scala.base}</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.nossis-ui</groupId>
            <artifactId>nossis-ui-security-adapter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.typesafe.play</groupId>
                    <artifactId>play-logback_${version.org.scala.base}</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.nossis-ui</groupId>
            <artifactId>nossis-ui-menu-models</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.nossis-ui</groupId>
            <artifactId>nossis-ui-menu</artifactId>
            <scope>runtime</scope>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.typesafe.play</groupId>
                    <artifactId>play-logback_${version.org.scala.base}</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- ########################################## -->
        <!-- # NOSSIS SECURITY                        # -->
        <!-- ########################################## -->
        <dependency>
            <groupId>pt.ptinovacao.nossis-security</groupId>
            <artifactId>nossis-security-provider-iam</artifactId>
            <scope>runtime</scope>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.typesafe.play</groupId>
                    <artifactId>play-logback_${version.org.scala.base}</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- ########################################## -->
        <!-- # EXTERNAL DEPENDENCIES                  # -->
        <!-- ########################################## -->
        <dependency>
            <groupId>com.typesafe</groupId>
            <artifactId>config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.typesafe.play</groupId>
            <artifactId>play_${version.org.scala.base}</artifactId>
        </dependency>
        <dependency>
            <groupId>com.typesafe.play</groupId>
            <artifactId>twirl-api_${version.org.scala.base}</artifactId>
        </dependency>
        <dependency>
            <groupId>com.typesafe.play</groupId>
            <artifactId>play-docs_${version.org.scala.base}</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.inject</groupId>
            <artifactId>javax.inject</artifactId>
        </dependency>

        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
        </dependency>
        <dependency>
            <groupId>org.scala-lang.modules</groupId>
            <artifactId>scala-xml_${version.org.scala.base}</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
    </dependencies>

</project>