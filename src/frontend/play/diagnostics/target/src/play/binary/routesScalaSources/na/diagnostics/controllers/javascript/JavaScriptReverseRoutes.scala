// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/diagnostics/conf/diagnostics.routes
// @DATE:Tue Jul 01 09:30:53 WEST 2025

import play.api.routing.JavaScriptReverseRoute


import _root_.controllers.Assets.Asset

// @LINE:2
package na.diagnostics.controllers.javascript {

  // @LINE:2
  class ReverseApplication(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:2
    def resume: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.diagnostics.controllers.Application.resume",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "diagnostics/resume"})
        }
      """
    )
  
  }


}
