// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/diagnostics/conf/diagnostics.routes
// @DATE:Tue Jul 01 09:30:53 WEST 2025

package na.diagnostics.controllers;

import diagnostics.RoutesPrefix;

public class routes {
  
  public static final na.diagnostics.controllers.ReverseApplication Application = new na.diagnostics.controllers.ReverseApplication(RoutesPrefix.byNamePrefix());

  public static class javascript {
    
    public static final na.diagnostics.controllers.javascript.ReverseApplication Application = new na.diagnostics.controllers.javascript.ReverseApplication(RoutesPrefix.byNamePrefix());
  }

}
