// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/diagnostics/conf/diagnostics.routes
// @DATE:Tue Jul 01 11:21:08 WEST 2025

import play.api.mvc.Call


import _root_.controllers.Assets.Asset

// @LINE:2
package na.diagnostics.controllers {

  // @LINE:2
  class ReverseApplication(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:2
    def resume(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "diagnostics/resume")
    }
  
  }


}
