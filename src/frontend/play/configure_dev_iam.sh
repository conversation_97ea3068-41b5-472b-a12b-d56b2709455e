#!/usr/bin/env bash

VERBOSE=0
LOG_PREFIX="[iam keystore configuration] "

main(){
  # Project root directory
  PROJECT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )"/../../.. && pwd )"

  echo "$LOG_PREFIX starting ..."

  # load configurations
  echo "$LOG_PREFIX loading configurations ..."

  CONF_DEV_SEC_FOLDER="$PROJECT_DIR/src/frontend/play/portal/conf-dev-security"
  NOSSISAAAPI_FILE="$CONF_DEV_SEC_FOLDER/nossisaaapi.conf"

  IAM_HOST="$(grep -A2  '# IAM base url' "$NOSSISAAAPI_FILE" | grep -oP  '^[[:space:]]*url = "https://\K[^"]+')"
  KEYSTORE_FILE="$CONF_DEV_SEC_FOLDER/na-iam.jks"
  KEYSTORE_PASS="123456"
  PUBLIC_CERT_FILE="$PROJECT_DIR/automation/roles/ci-na-portal-iam-configurator/templates/public-cert.pem"

  log_verbose "╔══ configurations ══╗"
  log_verbose "║           IAM_HOST ║ $IAM_HOST "
  log_verbose "║      KEYSTORE_FILE ║ $KEYSTORE_FILE "
  log_verbose "║      KEYSTORE_PASS ║ $KEYSTORE_PASS "
  log_verbose "║   PUBLIC_CERT_FILE ║ $PUBLIC_CERT_FILE "
  log_verbose "╚════════════════════╝"
  rm "$KEYSTORE_FILE"

  IAM_CERT_FILE="$(mktemp /tmp/iam_XXXXXXX.pem)"
  curl -X GET "https://$IAM_HOST/idp/iam.pem" --insecure -q > "$IAM_CERT_FILE"
  log_verbose "IAM IDP certificate downloaded to $IAM_CERT_FILE"
  keytool -import -noprompt -keystore "$KEYSTORE_FILE" -alias iam -storepass "$KEYSTORE_PASS" -file "$IAM_CERT_FILE"


  CONNECTION_CERT_FILE="$(mktemp /tmp/iam_conn_XXXXXXX.pem)"
  openssl s_client -showcerts -connect "$IAM_HOST:443" < /dev/null | openssl x509 -outform PEM > "$CONNECTION_CERT_FILE"
  log_verbose "SSL certificate to connect to IAM host machine downloaded to $IAM_CERT_FILE"
  keytool -import -noprompt -keystore "$KEYSTORE_FILE" -storepass "$KEYSTORE_PASS" -alias "$IAM_HOST" -file "$CONNECTION_CERT_FILE"

  keytool -import -noprompt -keystore "$KEYSTORE_FILE" -storepass "$KEYSTORE_PASS" -alias na-portal-service -file "$PUBLIC_CERT_FILE"

}

log_verbose() {
  if [ "$VERBOSE" = "1" ]; then echo "$LOG_PREFIX" "$@"; fi
}

display_help() {
  echo "Usage: $0 [option]" >&2
  echo
  echo "Options"
  echo "   -V, --verbose,   Enable verbose log"
  echo "   -h, --address,   This help message"
  echo
  return 1
}

while [[ "$#" -gt 0 ]]; do
  case "$1" in
  -h | --help)
    display_help
    exit 0
    ;;
  -V | --verbose)
    VERBOSE=1
    shift 1
    ;;
  --)
    shift
    break
    ;;
  -*)
    echo "Error: Unknown option: $1" >&2
    exit 2
    ;;
  *)
    break
    ;;
  esac
done

main
