
package na.naportalbase.views.html.components.tabs

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object blockContainerComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template5[String,Html,Boolean,Boolean,Boolean,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(columnLabel: String, content: Html, isFirst: Boolean, isLast: Boolean, isRequired : Boolean):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*3.2*/import na.naportalbase.views.tags.i18n

def /*5.2*/appendableClass/*5.17*/(isFirst: Boolean, isLast: Boolean) = {{
	var cssClasses = ""
        if(isFirst){
        		cssClasses += "fx-first"
        	} 
        if(isLast){
        	cssClasses += " fx-last-block"
        }
        " " + cssClasses
}};
Seq[Any](format.raw/*1.95*/("""

"""),format.raw/*4.1*/("""
"""),format.raw/*14.2*/("""

"""),format.raw/*16.1*/("""<div class="fx-form-block">
    <div class="fx-form-block-help """),_display_(/*17.37*/appendableClass(isFirst,isLast)),format.raw/*17.68*/("""">
        <h6>
            <i class="caret"></i>
            """),_display_(/*20.14*/columnLabel),format.raw/*20.25*/("""
        """),format.raw/*21.9*/("""</h6>
        """),_display_(/*22.10*/if(isRequired)/*22.24*/{_display_(Seq[Any](format.raw/*22.25*/("""
        """),format.raw/*23.9*/("""<p class="fx-required-fields-message">
            """),_display_(/*24.14*/i18n("na.naportalbase.requiredFields")),format.raw/*24.52*/("""
        """),format.raw/*25.9*/("""</p>
        """)))}),format.raw/*26.10*/("""
     """),format.raw/*27.6*/("""</div>
     <div class="fx-form-block-controls """),_display_(/*28.42*/appendableClass(isFirst,isLast)),format.raw/*28.73*/("""">
        """),_display_(/*29.10*/content),format.raw/*29.17*/("""
     """),format.raw/*30.6*/("""</div>
</div>"""))
      }
    }
  }

  def render(columnLabel:String,content:Html,isFirst:Boolean,isLast:Boolean,isRequired:Boolean): play.twirl.api.HtmlFormat.Appendable = apply(columnLabel,content,isFirst,isLast,isRequired)

  def f:((String,Html,Boolean,Boolean,Boolean) => play.twirl.api.HtmlFormat.Appendable) = (columnLabel,content,isFirst,isLast,isRequired) => apply(columnLabel,content,isFirst,isLast,isRequired)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:30:45 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/tabs/blockContainerComp.scala.html
                  HASH: b59a6016dcb959f18bb2c8be63a6fb3ab2eb8c6c
                  MATRIX: 1022->1|1188->97|1239->138|1262->153|1518->94|1546->136|1574->379|1603->381|1694->445|1746->476|1836->539|1868->550|1904->559|1946->574|1969->588|2008->589|2044->598|2123->650|2182->688|2218->697|2263->711|2296->717|2371->765|2423->796|2462->808|2490->815|2523->821
                  LINES: 28->1|31->3|33->5|33->5|43->1|45->4|46->14|48->16|49->17|49->17|52->20|52->20|53->21|54->22|54->22|54->22|55->23|56->24|56->24|57->25|58->26|59->27|60->28|60->28|61->29|61->29|62->30
                  -- GENERATED --
              */
          