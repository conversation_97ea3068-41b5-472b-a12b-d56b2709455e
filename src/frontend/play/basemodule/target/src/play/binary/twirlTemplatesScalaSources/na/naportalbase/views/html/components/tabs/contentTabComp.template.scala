
package na.naportalbase.views.html.components.tabs

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.utils.TemplateUtils

object contentTabComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[Boolean,String,Map[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*3.2*/(isActive: Boolean, tabLabel: String, attributes: Map[String, String]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*3.72*/("""

"""),format.raw/*5.1*/("""<li class=""""),_display_(/*5.13*/("active".when(isActive))),format.raw/*5.38*/(""" """),format.raw/*5.39*/("""clearfix">
    <a data-toggle="tab" class="fx-lc-content-selector" """),_display_(/*6.58*/TemplateUtils/*6.71*/.dynamicElementsAttributes(attributes)),format.raw/*6.109*/(""">
        """),_display_(/*7.10*/tabLabel),format.raw/*7.18*/("""
    """),format.raw/*8.5*/("""</a>
</li>"""))
      }
    }
  }

  def render(isActive:Boolean,tabLabel:String,attributes:Map[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(isActive,tabLabel,attributes)

  def f:((Boolean,String,Map[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (isActive,tabLabel,attributes) => apply(isActive,tabLabel,attributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:00 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/tabs/contentTabComp.scala.html
                  HASH: 52cad181174df2a4d983a1f02b5b85c3ca3d0da0
                  MATRIX: 683->1|1067->46|1232->116|1260->118|1298->130|1343->155|1371->156|1465->224|1486->237|1545->275|1582->286|1610->294|1641->299
                  LINES: 24->1|29->3|34->3|36->5|36->5|36->5|36->5|37->6|37->6|37->6|38->7|38->7|39->8
                  -- GENERATED --
              */
          