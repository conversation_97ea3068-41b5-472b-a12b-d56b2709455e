
package na.naportalbase.views.html.skeletons

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object splitterTemplateWithButtonsSkel extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template5[Html,Html,scala.collection.mutable.HashMap[String, String],Html,String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(leftContent: Html, rightContent: Html, leftSideBarAttributes: scala.collection.mutable.HashMap[String, String], navBar: Html, helperController: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*2.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*1.154*/("""
"""),format.raw/*3.1*/("""
"""),format.raw/*4.1*/("""<div id="splitter-parent" class="fx-splitter-area" """),_display_(/*4.53*/if(helperController != null)/*4.81*/{_display_(Seq[Any](format.raw/*4.82*/("""data-ng-controller=""""),_display_(/*4.103*/helperController),format.raw/*4.119*/(""""""")))}),format.raw/*4.121*/(""">
    """),_display_(/*5.6*/navBar),format.raw/*5.12*/("""
    """),format.raw/*6.5*/("""<form id="fx-sidebar" class="fx-splitter-sidebar with-buttons" """),_display_(/*6.69*/TemplateUtils/*6.82*/.dynamicElementsAttributes(leftSideBarAttributes)),format.raw/*6.131*/(""">
        """),_display_(/*7.10*/leftContent),format.raw/*7.21*/("""
        """),_display_(/*8.10*/na/*8.12*/.naportalbase.views.html.components.splitterComp.splitterComp.render()),format.raw/*8.82*/("""
    """),format.raw/*9.5*/("""</form>
    <div id="fx-splitter-content" class="fx-splitter-content">
        """),_display_(/*11.10*/rightContent),format.raw/*11.22*/("""
    """),format.raw/*12.5*/("""</div>
</div>

"""))
      }
    }
  }

  def render(leftContent:Html,rightContent:Html,leftSideBarAttributes:scala.collection.mutable.HashMap[String, String],navBar:Html,helperController:String): play.twirl.api.HtmlFormat.Appendable = apply(leftContent,rightContent,leftSideBarAttributes,navBar,helperController)

  def f:((Html,Html,scala.collection.mutable.HashMap[String, String],Html,String) => play.twirl.api.HtmlFormat.Appendable) = (leftContent,rightContent,leftSideBarAttributes,navBar,helperController) => apply(leftContent,rightContent,leftSideBarAttributes,navBar,helperController)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:00 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/skeletons/splitterTemplateWithButtonsSkel.scala.html
                  HASH: 1a4a34d5ec25c379659fb49d56fe95735d26c309
                  MATRIX: 1064->1|1289->156|1362->153|1390->200|1418->202|1496->254|1532->282|1570->283|1618->304|1655->320|1688->322|1721->330|1747->336|1779->342|1869->406|1890->419|1960->468|1998->480|2029->491|2066->502|2076->504|2166->574|2198->580|2307->662|2340->674|2373->680
                  LINES: 28->1|31->2|34->1|35->3|36->4|36->4|36->4|36->4|36->4|36->4|36->4|37->5|37->5|38->6|38->6|38->6|38->6|39->7|39->7|40->8|40->8|40->8|41->9|43->11|43->11|44->12
                  -- GENERATED --
              */
          