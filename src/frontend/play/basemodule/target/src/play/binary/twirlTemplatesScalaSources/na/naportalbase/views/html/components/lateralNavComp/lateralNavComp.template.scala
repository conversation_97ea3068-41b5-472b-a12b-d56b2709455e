
package na.naportalbase.views.html.components.lateralNavComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import scala.collection.mutable.ArrayBuffer

object lateralNavComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[scala.collection.mutable.LinkedHashMap[String, scala.collection.mutable.HashMap[String, String]],String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*2.2*/(navElements: scala.collection.mutable.LinkedHashMap[String, scala.collection.mutable.HashMap[String,String]],controller:String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*4.2*/import na.naportalbase.views.tags.i18n

def /*5.2*/linkClassAttr/*5.15*/(value:scala.collection.mutable.HashMap[String,String]) = {{
	var result = new ArrayBuffer[String]()
	if(value.contains("data-active-tab")){
		result += "active"
	}
	if(value.contains("link-class")){
		result += value.get("link-class").get
	}

	if (result.nonEmpty) result.mkString(" ") else ""
}};
Seq[Any](format.raw/*2.130*/("""

"""),format.raw/*15.2*/("""
"""),format.raw/*16.1*/("""<!-- data-enable-tooltip -->
<nav id="fx-page-main-nav" class="fx-ico-nav" data-ng-controller=""""),_display_(/*17.68*/controller),format.raw/*17.78*/("""">
    <ul class="list-unstyled">
        """),_display_(/*19.10*/for((key,value) <- navElements) yield /*19.41*/ {_display_(Seq[Any](format.raw/*19.43*/("""

            """),format.raw/*21.13*/("""<li data-placement="right" data-toggle="tooltip" data-enable-tooltip
            data-original-title=""""),_display_(/*22.35*/i18n(key)),format.raw/*22.44*/("""">
                <a class=""""),_display_(/*23.28*/linkClassAttr(value)),format.raw/*23.48*/("""" data-ng-click="""),_display_(/*23.65*/value/*23.70*/.get("data-ng-click")),format.raw/*23.91*/(""">
                    <i class=""""),_display_(/*24.32*/value/*24.37*/.get("data-tab-icon")),format.raw/*24.58*/(""""></i>
                </a>
            </li>
        """)))}),format.raw/*27.10*/("""
    """),format.raw/*28.5*/("""</ul>
</nav>



"""))
      }
    }
  }

  def render(navElements:scala.collection.mutable.LinkedHashMap[String, scala.collection.mutable.HashMap[String, String]],controller:String): play.twirl.api.HtmlFormat.Appendable = apply(navElements,controller)

  def f:((scala.collection.mutable.LinkedHashMap[String, scala.collection.mutable.HashMap[String, String]],String) => play.twirl.api.HtmlFormat.Appendable) = (navElements,controller) => apply(navElements,controller)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:00 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/lateralNavComp/lateralNavComp.scala.html
                  HASH: 709a0e54ef23137990c64203f0377d4e58e522d9
                  MATRIX: 693->1|1147->46|1348->177|1399->217|1420->230|1747->174|1776->526|1804->527|1927->623|1958->633|2028->676|2075->707|2115->709|2157->723|2287->826|2317->835|2374->865|2415->885|2459->902|2473->907|2515->928|2575->961|2589->966|2631->987|2717->1042|2749->1047
                  LINES: 24->1|29->2|32->4|34->5|34->5|45->2|47->15|48->16|49->17|49->17|51->19|51->19|51->19|53->21|54->22|54->22|55->23|55->23|55->23|55->23|55->23|56->24|56->24|56->24|59->27|60->28
                  -- GENERATED --
              */
          