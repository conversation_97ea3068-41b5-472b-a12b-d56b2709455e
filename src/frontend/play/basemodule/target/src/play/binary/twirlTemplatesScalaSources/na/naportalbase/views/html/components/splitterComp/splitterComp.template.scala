
package na.naportalbase.views.html.components.splitterComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n

object splitterComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*3.1*/("""<div id="fx-splitter-bar" class="fx-splitter-bar" data-splitter-content="#fx-splitter-content"
     data-splitter-sidebar="#fx-sidebar"
     data-splitter-position="left" data-toggle="splitter" data-splitter-init
     data-original-title=""""),_display_(/*6.28*/i18n("na.general.minimize")),format.raw/*6.55*/(""""></div>"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:00 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/splitterComp/splitterComp.scala.html
                  HASH: c079f6ff54d301872fbc92c3d4f4368b8dc826fd
                  MATRIX: 691->1|1123->41|1389->281|1436->308
                  LINES: 24->1|34->3|37->6|37->6
                  -- GENERATED --
              */
          