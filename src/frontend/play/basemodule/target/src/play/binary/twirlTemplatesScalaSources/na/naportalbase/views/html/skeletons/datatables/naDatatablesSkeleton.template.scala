
package na.naportalbase.views.html.skeletons.datatables

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object naDatatablesSkeleton extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[scala.collection.mutable.Map[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(tableAttributes: scala.collection.mutable.Map[String, String]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*2.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*1.65*/("""
"""),format.raw/*3.1*/("""
"""),format.raw/*4.1*/("""<div id="loadingWrapper_"""),_display_(/*4.26*/tableAttributes/*4.41*/.get("id")),format.raw/*4.51*/("""">
    <div data-ng-controller="NaPortalDatatablesController">
        <table """),_display_(/*6.17*/TemplateUtils/*6.30*/.dynamicElementsAttributes(tableAttributes)),format.raw/*6.73*/(""" """),format.raw/*6.74*/("""data-na-portal-datatable data-dt-options="dtOptions" data-dt-columns="dtColumns" dt-disable-deep-watchers="true" class="table table-striped table-hover"></table>
    </div>
</div>"""))
      }
    }
  }

  def render(tableAttributes:scala.collection.mutable.Map[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(tableAttributes)

  def f:((scala.collection.mutable.Map[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (tableAttributes) => apply(tableAttributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:30:45 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/skeletons/datatables/naDatatablesSkeleton.scala.html
                  HASH: 64e6897899a9240bf0c665e6cee2c961e6083f64
                  MATRIX: 1038->1|1174->66|1246->64|1273->109|1300->110|1351->135|1374->150|1404->160|1509->239|1530->252|1593->295|1621->296
                  LINES: 28->1|31->2|34->1|35->3|36->4|36->4|36->4|36->4|38->6|38->6|38->6|38->6
                  -- GENERATED --
              */
          