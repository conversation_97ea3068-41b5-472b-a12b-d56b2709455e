
package na.naportalbase.views.html.components.tabs

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object searchTabComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[Boolean,String,scala.collection.immutable.Map[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(isActive: Boolean, icon: String, listAttributes: scala.collection.immutable.Map[String, String]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*2.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*1.99*/("""
"""),format.raw/*3.1*/("""
"""),format.raw/*4.1*/("""<li class=""""),_display_(/*4.13*/("active".when(isActive))),format.raw/*4.38*/("""">
    <a data-toggle="tab" class="fx-tab" """),_display_(/*5.42*/TemplateUtils/*5.55*/.dynamicElementsAttributes(listAttributes)),format.raw/*5.97*/(""">
        <i class='"""),_display_(/*6.20*/icon),format.raw/*6.24*/("""'></i>
    </a>
</li>"""))
      }
    }
  }

  def render(isActive:Boolean,icon:String,listAttributes:scala.collection.immutable.Map[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(isActive,icon,listAttributes)

  def f:((Boolean,String,scala.collection.immutable.Map[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (isActive,icon,listAttributes) => apply(isActive,icon,listAttributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:00 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/tabs/searchTabComp.scala.html
                  HASH: 39d8036c5aecca97ccb186b0f5093fd9070d4bab
                  MATRIX: 1043->1|1213->100|1285->98|1312->143|1339->144|1377->156|1422->181|1492->225|1513->238|1575->280|1622->301|1646->305
                  LINES: 28->1|31->2|34->1|35->3|36->4|36->4|36->4|37->5|37->5|37->5|38->6|38->6
                  -- GENERATED --
              */
          