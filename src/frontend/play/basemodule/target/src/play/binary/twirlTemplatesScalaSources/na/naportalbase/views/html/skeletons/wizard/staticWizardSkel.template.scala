
package na.naportalbase.views.html.skeletons.wizard

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.WizardStep

object staticWizardSkel extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[Seq[WizardStep],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*2.2*/(steps : Seq[WizardStep]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*4.2*/import na.naportalbase.views.tags.i18n


Seq[Any](format.raw/*2.27*/("""

"""),format.raw/*5.1*/("""
"""),format.raw/*6.1*/("""<div id="wizard" data-na-portal-wizard class="fx-wizard-wrapper">
    <nav>
        <ul class="list-unstyled fx-nav-items">
        """),_display_(/*9.10*/for((step, index) <- steps.zipWithIndex) yield /*9.50*/{_display_(Seq[Any](format.raw/*9.51*/("""
	        """),format.raw/*10.10*/("""<li id="navWizStep"""),_display_(/*10.29*/{index+1}),format.raw/*10.38*/("""" data-na-portal-wizard-tab data-tab-name=""""),_display_(/*10.82*/{step.stepName}),format.raw/*10.97*/("""" data-label=""""),_display_(/*10.112*/{step.stepLabel}),format.raw/*10.128*/(""""></li>
        """)))}),format.raw/*11.10*/("""
        """),format.raw/*12.9*/("""</ul>
    </nav>
    """),_display_(/*14.6*/for((step, index) <- steps.zipWithIndex) yield /*14.46*/{_display_(Seq[Any](format.raw/*14.47*/("""
        """),format.raw/*15.9*/("""<section id="wizStep"""),_display_(/*15.30*/{index+1}),format.raw/*15.39*/("""-m" class="fx-wizardsteps-content" """),_display_(/*15.75*/if(step.destroyOnHide)/*15.97*/{_display_(Seq[Any](format.raw/*15.98*/("""data-ng-if="currentStepIndex == """),_display_(/*15.131*/{index}),format.raw/*15.138*/(""""""")))}/*15.140*/else/*15.144*/{_display_(Seq[Any](format.raw/*15.145*/("""data-ng-class=""""),format.raw/*15.160*/("""{"""),format.raw/*15.161*/("""hidden: currentStepIndex != """),_display_(/*15.190*/{index}),format.raw/*15.197*/("""}"""),format.raw/*15.198*/(""""""")))}),format.raw/*15.200*/(""">
            """),_display_(/*16.14*/{step.content}),format.raw/*16.28*/("""
        """),format.raw/*17.9*/("""</section>
    """)))}),format.raw/*18.6*/("""
    """),format.raw/*19.5*/("""<div class="fx-wizard-buttons-wrapper">
        <div class="fx-wizard-buttons-wrapper-inner">
            <button id="wiz-prev" class="btn btn-default btn-sm" data-na-portal-wizard-previous-step type="button">
                <i class="glyphicon glyphicon-chevron-left"></i>"""),_display_(/*22.66*/i18n("na.wizard.buttons.before")),format.raw/*22.98*/("""
            """),format.raw/*23.13*/("""</button>
            <button id="wiz-next" class="btn btn-sm" data-na-portal-wizard-next-step type="button">
                """),_display_(/*25.18*/i18n("na.wizard.buttons.next")),format.raw/*25.48*/("""<i class="glyphicon glyphicon-chevron-right"></i>
            </button>
            <button id="wiz-complete" class="btn btn-sm" data-na-portal-wizard-complete type="button">
                """),_display_(/*28.18*/i18n("na.wizard.buttons.finalize")),format.raw/*28.52*/("""
            """),format.raw/*29.13*/("""</button>
            <button id="wiz-cancel" class="btn btn-default btn-sm" data-na-portal-wizard-cancel type="button">
                """),_display_(/*31.18*/i18n("na.wizard.buttons.cancel")),format.raw/*31.50*/("""
            """),format.raw/*32.13*/("""</button>
        </div>
    </div>
</div>



"""))
      }
    }
  }

  def render(steps:Seq[WizardStep]): play.twirl.api.HtmlFormat.Appendable = apply(steps)

  def f:((Seq[WizardStep]) => play.twirl.api.HtmlFormat.Appendable) = (steps) => apply(steps)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:00 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/skeletons/wizard/staticWizardSkel.scala.html
                  HASH: cdbc3d70a9b913ea9a67b790e988ace50b19935e
                  MATRIX: 684->1|1053->47|1151->75|1219->72|1247->114|1274->115|1433->248|1488->288|1526->289|1564->299|1610->318|1640->327|1711->371|1747->386|1790->401|1828->417|1876->434|1912->443|1960->465|2016->505|2055->506|2091->515|2139->536|2169->545|2232->581|2263->603|2302->604|2363->637|2392->644|2414->646|2428->650|2468->651|2512->666|2542->667|2599->696|2628->703|2658->704|2692->706|2734->721|2769->735|2805->744|2851->760|2883->765|3185->1040|3238->1072|3279->1085|3433->1212|3484->1242|3703->1434|3758->1468|3799->1481|3964->1619|4017->1651|4058->1664
                  LINES: 24->1|29->2|32->4|35->2|37->5|38->6|41->9|41->9|41->9|42->10|42->10|42->10|42->10|42->10|42->10|42->10|43->11|44->12|46->14|46->14|46->14|47->15|47->15|47->15|47->15|47->15|47->15|47->15|47->15|47->15|47->15|47->15|47->15|47->15|47->15|47->15|47->15|47->15|48->16|48->16|49->17|50->18|51->19|54->22|54->22|55->23|57->25|57->25|60->28|60->28|61->29|63->31|63->31|64->32
                  -- GENERATED --
              */
          