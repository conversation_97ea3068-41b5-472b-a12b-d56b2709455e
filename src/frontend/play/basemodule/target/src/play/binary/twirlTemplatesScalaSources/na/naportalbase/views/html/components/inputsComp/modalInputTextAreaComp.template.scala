
package na.naportalbase.views.html.components.inputsComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object modalInputTextAreaComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[String,String,scala.collection.mutable.HashMap[String, String],Any,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(label: String, inputId: String, inputAttributes: scala.collection.mutable.HashMap[String, String], inputValue: Any):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.118*/("""

"""),format.raw/*3.1*/("""<label for=""""),_display_(/*3.14*/inputId),format.raw/*3.21*/("""" class="control-label col-sm-3">"""),_display_(/*3.55*/label),format.raw/*3.60*/("""</label>
<div class="col-sm-9">
    """),_display_(/*5.6*/na/*5.8*/.naportalbase.views.html.components.inputsComp.inputTextAreaComp.render(inputId, inputAttributes, inputValue)),format.raw/*5.117*/("""
"""),format.raw/*6.1*/("""</div>"""))
      }
    }
  }

  def render(label:String,inputId:String,inputAttributes:scala.collection.mutable.HashMap[String, String],inputValue:Any): play.twirl.api.HtmlFormat.Appendable = apply(label,inputId,inputAttributes,inputValue)

  def f:((String,String,scala.collection.mutable.HashMap[String, String],Any) => play.twirl.api.HtmlFormat.Appendable) = (label,inputId,inputAttributes,inputValue) => apply(label,inputId,inputAttributes,inputValue)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:30:45 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/inputsComp/modalInputTextAreaComp.scala.html
                  HASH: 8f02c1997a3dbd0351b7f85fade6c01f7ca95e6e
                  MATRIX: 1063->1|1275->117|1303->119|1342->132|1369->139|1429->173|1454->178|1516->215|1525->217|1655->326|1682->327
                  LINES: 28->1|33->1|35->3|35->3|35->3|35->3|35->3|37->5|37->5|37->5|38->6
                  -- GENERATED --
              */
          