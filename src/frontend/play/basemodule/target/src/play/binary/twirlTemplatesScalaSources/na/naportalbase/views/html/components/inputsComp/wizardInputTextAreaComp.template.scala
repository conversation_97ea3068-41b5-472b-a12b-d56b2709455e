
package na.naportalbase.views.html.components.inputsComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object wizardInputTextAreaComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template5[String,String,scala.collection.mutable.HashMap[String, String],Any,scala.collection.mutable.HashMap[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(label: String, inputId: String, inputAttributes: scala.collection.mutable.HashMap[String, String], inputValue: Any,labelAttributes: scala.collection.mutable.HashMap[String, String]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*3.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*1.184*/("""

"""),format.raw/*4.1*/("""
"""),format.raw/*5.1*/("""<label for=""""),_display_(/*5.14*/inputId),format.raw/*5.21*/("""" """),_display_(/*5.24*/TemplateUtils/*5.37*/.dynamicElementsAttributes(labelAttributes)),format.raw/*5.80*/(""">"""),_display_(/*5.82*/label),format.raw/*5.87*/("""</label>
<div class="col-sm-10">
    """),_display_(/*7.6*/na/*7.8*/.naportalbase.views.html.components.inputsComp.inputTextAreaComp.render(inputId, inputAttributes, inputValue)),format.raw/*7.117*/("""
"""),format.raw/*8.1*/("""</div>"""))
      }
    }
  }

  def render(label:String,inputId:String,inputAttributes:scala.collection.mutable.HashMap[String, String],inputValue:Any,labelAttributes:scala.collection.mutable.HashMap[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(label,inputId,inputAttributes,inputValue,labelAttributes)

  def f:((String,String,scala.collection.mutable.HashMap[String, String],Any,scala.collection.mutable.HashMap[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (label,inputId,inputAttributes,inputValue,labelAttributes) => apply(label,inputId,inputAttributes,inputValue,labelAttributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:30:45 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/inputsComp/wizardInputTextAreaComp.scala.html
                  HASH: f7f4dd751a7b4a51f7960f7a0e471c3baf1c1b55
                  MATRIX: 1113->1|1368->186|1441->183|1469->229|1496->230|1535->243|1562->250|1591->253|1612->266|1675->309|1703->311|1728->316|1791->354|1800->356|1930->465|1957->466
                  LINES: 28->1|31->3|34->1|36->4|37->5|37->5|37->5|37->5|37->5|37->5|37->5|37->5|39->7|39->7|39->7|40->8
                  -- GENERATED --
              */
          