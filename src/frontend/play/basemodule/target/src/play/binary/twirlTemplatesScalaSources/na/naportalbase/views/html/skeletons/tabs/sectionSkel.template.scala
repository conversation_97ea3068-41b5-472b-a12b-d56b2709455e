
package na.naportalbase.views.html.skeletons.tabs

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object sectionSkel extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[String,Boolean,scala.collection.immutable.HashMap[String, String],Html,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(id: String, isActive: Boolean, listAttributes: scala.collection.immutable.HashMap[String, String])(content : Html):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*2.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*1.117*/("""
"""),format.raw/*3.1*/("""
"""),format.raw/*4.1*/("""<section """),_display_(/*4.11*/TemplateUtils/*4.24*/.dynamicElementsAttributes(listAttributes)),format.raw/*4.66*/(""" """),format.raw/*4.67*/("""id=""""),_display_(/*4.72*/id),format.raw/*4.74*/("""" class=""""),_display_(/*4.84*/("active".when(isActive))),format.raw/*4.109*/(""" """),format.raw/*4.110*/("""tab-pane">
    """),_display_(/*5.6*/content),format.raw/*5.13*/("""
"""),format.raw/*6.1*/("""</section>"""))
      }
    }
  }

  def render(id:String,isActive:Boolean,listAttributes:scala.collection.immutable.HashMap[String, String],content:Html): play.twirl.api.HtmlFormat.Appendable = apply(id,isActive,listAttributes)(content)

  def f:((String,Boolean,scala.collection.immutable.HashMap[String, String]) => (Html) => play.twirl.api.HtmlFormat.Appendable) = (id,isActive,listAttributes) => (content) => apply(id,isActive,listAttributes)(content)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:00 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/skeletons/tabs/sectionSkel.scala.html
                  HASH: a0bc031326de9284b33c7e16d3f1008c462db8c9
                  MATRIX: 1049->1|1237->118|1310->116|1337->161|1364->162|1400->172|1421->185|1483->227|1511->228|1542->233|1564->235|1600->245|1646->270|1675->271|1716->287|1743->294|1770->295
                  LINES: 28->1|31->2|34->1|35->3|36->4|36->4|36->4|36->4|36->4|36->4|36->4|36->4|36->4|36->4|37->5|37->5|38->6
                  -- GENERATED --
              */
          