
package na.naportalbase.views.html.components.inputsComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object inputTextAreaComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[String,scala.collection.mutable.HashMap[String, String],Any,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(inputId: String, inputAttributes: scala.collection.mutable.HashMap[String, String], inputValue: Any):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*2.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*1.103*/("""
"""),format.raw/*3.1*/("""
"""),format.raw/*4.1*/("""<textarea id=""""),_display_(/*4.16*/inputId),format.raw/*4.23*/("""" """),_display_(/*4.26*/TemplateUtils/*4.39*/.dynamicElementsAttributes(inputAttributes)),format.raw/*4.82*/(""">"""),_display_(/*4.84*/inputValue),format.raw/*4.94*/("""</textarea>"""))
      }
    }
  }

  def render(inputId:String,inputAttributes:scala.collection.mutable.HashMap[String, String],inputValue:Any): play.twirl.api.HtmlFormat.Appendable = apply(inputId,inputAttributes,inputValue)

  def f:((String,scala.collection.mutable.HashMap[String, String],Any) => play.twirl.api.HtmlFormat.Appendable) = (inputId,inputAttributes,inputValue) => apply(inputId,inputAttributes,inputValue)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:00 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/inputsComp/inputTextAreaComp.scala.html
                  HASH: 58c8272fea9c4bb4ea16110bcfd762f6d1f3d258
                  MATRIX: 1051->1|1225->104|1298->102|1325->147|1352->148|1393->163|1420->170|1449->173|1470->186|1533->229|1561->231|1591->241
                  LINES: 28->1|31->2|34->1|35->3|36->4|36->4|36->4|36->4|36->4|36->4|36->4|36->4
                  -- GENERATED --
              */
          