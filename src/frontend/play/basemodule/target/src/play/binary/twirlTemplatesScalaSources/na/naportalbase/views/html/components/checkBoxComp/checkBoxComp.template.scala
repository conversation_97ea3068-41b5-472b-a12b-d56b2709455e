
package na.naportalbase.views.html.components.checkBoxComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object checkBoxComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[String,String,scala.collection.mutable.HashMap[String, String],scala.collection.mutable.HashMap[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(label: String,
inputId: String,
inputAttributes: scala.collection.mutable.HashMap[String, String],
labelAttributes: scala.collection.mutable.HashMap[String, String]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*6.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*4.67*/("""

"""),format.raw/*7.1*/("""
"""),format.raw/*8.1*/("""<div>
    <span """),_display_(/*9.12*/TemplateUtils/*9.25*/.dynamicElementsAttributes(labelAttributes)),format.raw/*9.68*/(""">
        <input id=""""),_display_(/*10.21*/inputId),format.raw/*10.28*/("""" type="checkbox" """),_display_(/*10.47*/TemplateUtils/*10.60*/.dynamicElementsAttributes(inputAttributes)),format.raw/*10.103*/("""> """),_display_(/*10.106*/label),format.raw/*10.111*/("""
    """),format.raw/*11.5*/("""</span>
</div>


"""))
      }
    }
  }

  def render(label:String,inputId:String,inputAttributes:scala.collection.mutable.HashMap[String, String],labelAttributes:scala.collection.mutable.HashMap[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(label,inputId,inputAttributes,labelAttributes)

  def f:((String,String,scala.collection.mutable.HashMap[String, String],scala.collection.mutable.HashMap[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (label,inputId,inputAttributes,labelAttributes) => apply(label,inputId,inputAttributes,labelAttributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:30:45 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/checkBoxComp/checkBoxComp.scala.html
                  HASH: e6908dc80399f07088c087641b8e5e30f11ea219
                  MATRIX: 1100->1|1342->175|1414->170|1444->219|1472->221|1516->239|1537->252|1600->295|1650->318|1678->325|1724->344|1746->357|1811->400|1842->403|1869->408|1902->414
                  LINES: 28->1|34->6|37->4|39->7|40->8|41->9|41->9|41->9|42->10|42->10|42->10|42->10|42->10|42->10|42->10|43->11
                  -- GENERATED --
              */
          