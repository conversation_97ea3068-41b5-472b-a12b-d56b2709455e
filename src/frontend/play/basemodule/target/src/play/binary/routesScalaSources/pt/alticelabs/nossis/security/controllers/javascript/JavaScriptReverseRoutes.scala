// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/conf/naportalbase.routes
// @DATE:Tue Jul 01 11:20:59 WEST 2025

import play.api.routing.JavaScriptReverseRoute


import _root_.controllers.Assets.Asset

// @LINE:5
package pt.alticelabs.nossis.security.controllers.javascript {

  // @LINE:5
  class ReverseNossisAuthenticator(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:7
    def logout: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "pt.alticelabs.nossis.security.controllers.NossisAuthenticator.logout",
      """
        function() {
        
          if (true) {
            return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "logout"})
          }
        
        }
      """
    )
  
    // @LINE:5
    def login: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "pt.alticelabs.nossis.security.controllers.NossisAuthenticator.login",
      """
        function() {
        
          if (true) {
            return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "login"})
          }
        
        }
      """
    )
  
  }


}
