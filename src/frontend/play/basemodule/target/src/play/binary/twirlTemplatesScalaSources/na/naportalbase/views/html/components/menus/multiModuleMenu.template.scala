
package na.naportalbase.views.html.components.menus

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object multiModuleMenu extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[scala.collection.mutable.LinkedHashMap[String, scala.collection.mutable.HashMap[String, String]],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(elements: scala.collection.mutable.LinkedHashMap[String, scala.collection.mutable.HashMap[String,String]]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*3.2*/import na.naportalbase.views.tags.i18n


Seq[Any](format.raw/*1.109*/("""

"""),format.raw/*4.1*/("""
"""),format.raw/*5.1*/("""<ul class="fx-nav">
"""),_display_(/*6.2*/for((key,value) <- elements) yield /*6.30*/{_display_(Seq[Any](format.raw/*6.31*/("""
    """),_display_(/*7.6*/if(value.contains("data-active-tab"))/*7.43*/{_display_(Seq[Any](format.raw/*7.44*/("""
        """),format.raw/*8.9*/("""<li class="active">
            <a title=""""),_display_(/*9.24*/i18n(key)),format.raw/*9.33*/("""" data-ng-click=""""),_display_(/*9.51*/value/*9.56*/.get("data-ng-click")),format.raw/*9.77*/("""">
                <i class=""""),_display_(/*10.28*/value/*10.33*/.get("data-button-icon")),format.raw/*10.57*/(""""></i>
                <span>"""),_display_(/*11.24*/i18n(key)),format.raw/*11.33*/("""</span>
            </a>
        </li>
    """)))}/*14.6*/else/*14.10*/{_display_(Seq[Any](format.raw/*14.11*/("""
        """),format.raw/*15.9*/("""<li>
            <a title=""""),_display_(/*16.24*/{i18n(key): String}),format.raw/*16.43*/("""" data-ng-click=""""),_display_(/*16.61*/value/*16.66*/.get("data-ng-click")),format.raw/*16.87*/("""">
                <i class=""""),_display_(/*17.28*/value/*17.33*/.get("data-button-icon")),format.raw/*17.57*/(""""></i>
                <span>"""),_display_(/*18.24*/i18n(key)),format.raw/*18.33*/("""</span>
            </a>
        </li>
    """)))}),format.raw/*21.6*/("""
""")))}),format.raw/*22.2*/("""
"""),format.raw/*23.1*/("""</ul>"""))
      }
    }
  }

  def render(elements:scala.collection.mutable.LinkedHashMap[String, scala.collection.mutable.HashMap[String, String]]): play.twirl.api.HtmlFormat.Appendable = apply(elements)

  def f:((scala.collection.mutable.LinkedHashMap[String, scala.collection.mutable.HashMap[String, String]]) => play.twirl.api.HtmlFormat.Appendable) = (elements) => apply(elements)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:00 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/menus/multiModuleMenu.scala.html
                  HASH: 5d2447dd1dc7321e421b57646dff3c72c3de260c
                  MATRIX: 1081->1|1261->111|1330->108|1358->150|1385->151|1431->172|1474->200|1512->201|1543->207|1588->244|1626->245|1661->254|1730->297|1759->306|1803->324|1816->329|1857->350|1914->380|1928->385|1973->409|2030->439|2060->448|2122->492|2135->496|2174->497|2210->506|2265->534|2305->553|2350->571|2364->576|2406->597|2463->627|2477->632|2522->656|2579->686|2609->695|2683->739|2715->741|2743->742
                  LINES: 28->1|31->3|34->1|36->4|37->5|38->6|38->6|38->6|39->7|39->7|39->7|40->8|41->9|41->9|41->9|41->9|41->9|42->10|42->10|42->10|43->11|43->11|46->14|46->14|46->14|47->15|48->16|48->16|48->16|48->16|48->16|49->17|49->17|49->17|50->18|50->18|53->21|54->22|55->23
                  -- GENERATED --
              */
          