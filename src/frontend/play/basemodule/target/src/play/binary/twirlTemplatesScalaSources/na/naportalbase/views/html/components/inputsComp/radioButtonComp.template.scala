
package na.naportalbase.views.html.components.inputsComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object radioButtonComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[String,String,scala.collection.mutable.HashMap[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        label: String,
        inputId: String,
        inputAttributes: scala.collection.mutable.HashMap[String, String]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*6.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*4.75*/("""

"""),format.raw/*7.1*/("""
"""),format.raw/*8.1*/("""<input id =""""),_display_(/*8.14*/inputId),format.raw/*8.21*/("""" type="radio" """),_display_(/*8.37*/TemplateUtils/*8.50*/.dynamicElementsAttributes(inputAttributes)),format.raw/*8.93*/("""/>&nbsp;"""),_display_(/*8.102*/label),format.raw/*8.107*/("""
"""))
      }
    }
  }

  def render(label:String,inputId:String,inputAttributes:scala.collection.mutable.HashMap[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(label,inputId,inputAttributes)

  def f:((String,String,scala.collection.mutable.HashMap[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (label,inputId,inputAttributes) => apply(label,inputId,inputAttributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:30:45 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/inputsComp/radioButtonComp.scala.html
                  HASH: 5eed1037377ad142d2023f47d19b8336d60d792a
                  MATRIX: 1052->1|1252->133|1324->128|1354->177|1382->179|1421->192|1448->199|1490->215|1511->228|1574->271|1610->280|1636->285
                  LINES: 28->1|34->6|37->4|39->7|40->8|40->8|40->8|40->8|40->8|40->8|40->8|40->8
                  -- GENERATED --
              */
          