
package na.naportalbase.views.html.components.splitterComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object sidebarButtonsComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[Html,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(buttons: Html):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.17*/("""

"""),format.raw/*3.1*/("""<div class="fx-splitter-sidebar-buttons">
    <div class="fx-splitter-sidebar-buttons-inner">
    """),_display_(/*5.6*/buttons),format.raw/*5.13*/("""
    """),format.raw/*6.5*/("""</div>
</div>"""))
      }
    }
  }

  def render(buttons:Html): play.twirl.api.HtmlFormat.Appendable = apply(buttons)

  def f:((Html) => play.twirl.api.HtmlFormat.Appendable) = (buttons) => apply(buttons)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:30:45 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/splitterComp/sidebarButtonsComp.scala.html
                  HASH: 0a5ef32b0a1294f16b469017a69c3e4e5521e4ff
                  MATRIX: 999->1|1109->16|1137->18|1261->117|1288->124|1319->129
                  LINES: 28->1|33->1|35->3|37->5|37->5|38->6
                  -- GENERATED --
              */
          