// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/conf/naportalbase.routes
// @DATE:Tue Jul 01 11:20:59 WEST 2025

import play.api.routing.JavaScriptReverseRoute


import _root_.controllers.Assets.Asset

// @LINE:12
package na.naportalbase.controllers.javascript {

  // @LINE:28
  class ReverseAssets(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:28
    def versioned: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.naportalbase.controllers.Assets.versioned",
      """
        function(file1) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "naportalbase/assets/" + (""" + implicitly[play.api.mvc.PathBindable[Asset]].javascriptUnbind + """)("file", file1)})
        }
      """
    )
  
  }

  // @LINE:18
  class ReverseDateTimePicker(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:18
    def dateTimePickerConf: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.naportalbase.controllers.DateTimePicker.dateTimePickerConf",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "naportalbase/datetimepickerconf"})
        }
      """
    )
  
  }

  // @LINE:12
  class ReverseApplication(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:14
    def jsMessagesJson: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.naportalbase.controllers.Application.jsMessagesJson",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "naportalbase/i18n-json"})
        }
      """
    )
  
    // @LINE:12
    def resume: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.naportalbase.controllers.Application.resume",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "naportalbase/resume"})
        }
      """
    )
  
    // @LINE:24
    def getBasemoduleAngularScripts: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.naportalbase.controllers.Application.getBasemoduleAngularScripts",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "naportalbase/imports/jsscripts"})
        }
      """
    )
  
    // @LINE:15
    def jsMessagesJsonForLang: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.naportalbase.controllers.Application.jsMessagesJsonForLang",
      """
        function(lang0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "naportalbase/i18n-json/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("lang", lang0))})
        }
      """
    )
  
    // @LINE:21
    def javascriptRoutes: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.naportalbase.controllers.Application.javascriptRoutes",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "naportalbase/assets/javascripts/routes"})
        }
      """
    )
  
    // @LINE:22
    def jsDateFormats: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.naportalbase.controllers.Application.jsDateFormats",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "naportalbase/assets/javascripts/dateformats.js"})
        }
      """
    )
  
    // @LINE:13
    def jsMessages: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.naportalbase.controllers.Application.jsMessages",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "naportalbase/i18n"})
        }
      """
    )
  
    // @LINE:25
    def getApplicationExportLimit: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.naportalbase.controllers.Application.getApplicationExportLimit",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "naportalbase/configs/exportlimit"})
        }
      """
    )
  
  }


}
