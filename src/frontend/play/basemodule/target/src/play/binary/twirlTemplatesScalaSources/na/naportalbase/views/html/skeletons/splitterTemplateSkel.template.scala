
package na.naportalbase.views.html.skeletons

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object splitterTemplateSkel extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[Html,Html,scala.collection.mutable.HashMap[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(leftContent: Html, rightContent: Html, leftSideBarAttributes: scala.collection.mutable.HashMap[String, String]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*2.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*1.114*/("""
"""),format.raw/*3.1*/("""
"""),format.raw/*4.1*/("""<div class="fx-splitter-area" id="splitter-parent">
    <form id="fx-sidebar" class="fx-splitter-sidebar" """),_display_(/*5.56*/TemplateUtils/*5.69*/.dynamicElementsAttributes(leftSideBarAttributes)),format.raw/*5.118*/(""">
        """),_display_(/*6.10*/leftContent),format.raw/*6.21*/("""
        """),_display_(/*7.10*/na/*7.12*/.naportalbase.views.html.components.splitterComp.splitterComp.render()),format.raw/*7.82*/("""
    """),format.raw/*8.5*/("""</form>
    <div id="fx-splitter-content" class="fx-splitter-content">
        """),_display_(/*10.10*/rightContent),format.raw/*10.22*/("""
    """),format.raw/*11.5*/("""</div>
</div>"""))
      }
    }
  }

  def render(leftContent:Html,rightContent:Html,leftSideBarAttributes:scala.collection.mutable.HashMap[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(leftContent,rightContent,leftSideBarAttributes)

  def f:((Html,Html,scala.collection.mutable.HashMap[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (leftContent,rightContent,leftSideBarAttributes) => apply(leftContent,rightContent,leftSideBarAttributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:00 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/skeletons/splitterTemplateSkel.scala.html
                  HASH: ca1bec6c4ebcf7e0e4635a079b6405b02655015c
                  MATRIX: 1041->1|1226->116|1299->113|1327->160|1355->162|1489->270|1510->283|1580->332|1618->344|1649->355|1686->366|1696->368|1786->438|1818->444|1927->526|1960->538|1993->544
                  LINES: 28->1|31->2|34->1|35->3|36->4|37->5|37->5|37->5|38->6|38->6|39->7|39->7|39->7|40->8|42->10|42->10|43->11
                  -- GENERATED --
              */
          