
package na.naportalbase.views.html.imports

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object resumescripts extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.1*/("""<script src=""""),_display_(/*1.15*/na/*1.17*/.naportalbase.controllers.routes.Application.jsMessages()),format.raw/*1.74*/("""" defer="defer"></script><script src=""""),_display_(/*1.113*/na/*1.115*/.naportalbase.controllers.routes.Application.javascriptRoutes()),format.raw/*1.178*/("""" defer="defer"></script><script src='"""),_display_(/*1.217*/na/*1.219*/.naportalbase.controllers.routes.Assets.versioned("basemodule/runtime.ef4cdaceb8028d495c4b.min.js")),format.raw/*1.318*/("""' defer="defer"></script><script src='"""),_display_(/*1.357*/na/*1.359*/.naportalbase.controllers.routes.Assets.versioned("na-portal-assets/base-styles.9e693254edb056a100ad.min.js")),format.raw/*1.468*/("""' defer="defer"></script><script src='"""),_display_(/*1.507*/na/*1.509*/.naportalbase.controllers.routes.Assets.versioned("na-portal-assets-vendors/common.a53001b54dbc3b115cc4.min.js")),format.raw/*1.621*/("""' defer="defer"></script><script src='"""),_display_(/*1.660*/na/*1.662*/.naportalbase.controllers.routes.Assets.versioned("utils/utils.88e56ee512f1a3af972d.min.js")),format.raw/*1.754*/("""' defer="defer"></script><script src='"""),_display_(/*1.793*/na/*1.795*/.naportalbase.controllers.routes.Assets.versioned("basemodule/resume.8d8f27c48fd5c6b9eaa2.min.js")),format.raw/*1.893*/("""' defer="defer"></script>"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:00 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/imports/resumescripts.scala.html
                  HASH: 2f02bb0cb09e26bae7c584d502795e5e76bdb464
                  MATRIX: 1062->0|1102->14|1112->16|1189->73|1255->112|1266->114|1350->177|1416->216|1427->218|1547->317|1613->356|1624->358|1754->467|1820->506|1831->508|1964->620|2030->659|2041->661|2154->753|2220->792|2231->794|2350->892
                  LINES: 33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1
                  -- GENERATED --
              */
          