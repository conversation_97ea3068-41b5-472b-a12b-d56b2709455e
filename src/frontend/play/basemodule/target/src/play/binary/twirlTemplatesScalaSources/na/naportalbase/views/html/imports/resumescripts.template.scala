
package na.naportalbase.views.html.imports

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object resumescripts extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.1*/("""<script src=""""),_display_(/*1.15*/na/*1.17*/.naportalbase.controllers.routes.Application.jsMessages()),format.raw/*1.74*/("""" defer></script>
<script src=""""),_display_(/*2.15*/na/*2.17*/.naportalbase.controllers.routes.Application.javascriptRoutes()),format.raw/*2.80*/("""" defer></script>
<script src='"""),_display_(/*3.15*/na/*3.17*/.naportalbase.controllers.routes.Assets.versioned("basemodule/runtime.js")),format.raw/*3.91*/("""' defer></script>
<script src='"""),_display_(/*4.15*/na/*4.17*/.naportalbase.controllers.routes.Assets.versioned("na-portal-assets/base-styles.js")),format.raw/*4.101*/("""' defer></script>
<script src='"""),_display_(/*5.15*/na/*5.17*/.naportalbase.controllers.routes.Assets.versioned("na-portal-assets-vendors/common.js")),format.raw/*5.104*/("""' defer></script>
<script src='"""),_display_(/*6.15*/na/*6.17*/.naportalbase.controllers.routes.Assets.versioned("utils/utils.js")),format.raw/*6.84*/("""' defer></script>
<script src='"""),_display_(/*7.15*/na/*7.17*/.naportalbase.controllers.routes.Assets.versioned("basemodule/resume.js")),format.raw/*7.90*/("""' defer></script>
"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:13 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/imports/resumescripts.scala.html
                  HASH: e71b25cef9d3f66ceb0252c6f6eab85f706d2269
                  MATRIX: 1062->0|1102->14|1112->16|1189->73|1247->105|1257->107|1340->170|1398->202|1408->204|1502->278|1560->310|1570->312|1675->396|1733->428|1743->430|1851->517|1909->549|1919->551|2006->618|2064->650|2074->652|2167->725
                  LINES: 33->1|33->1|33->1|33->1|34->2|34->2|34->2|35->3|35->3|35->3|36->4|36->4|36->4|37->5|37->5|37->5|38->6|38->6|38->6|39->7|39->7|39->7
                  -- GENERATED --
              */
          