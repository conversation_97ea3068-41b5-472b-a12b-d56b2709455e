// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/conf/naportalbase.routes
// @DATE:Tue Jul 01 09:30:43 WEST 2025

package naportalbase

import play.core.routing._
import play.core.routing.HandlerInvokerFactory._

import play.api.mvc._

import _root_.controllers.Assets.Asset

class Routes(
  override val errorHandler: play.api.http.HttpErrorHandler, 
  // @LINE:5
  NossisAuthenticator_2: pt.alticelabs.nossis.security.controllers.NossisAuthenticator,
  // @LINE:12
  Application_0: na.naportalbase.controllers.Application,
  // @LINE:18
  DateTimePicker_1: na.naportalbase.controllers.DateTimePicker,
  // @LINE:28
  Assets_3: na.naportalbase.controllers.Assets,
  val prefix: String
) extends GeneratedRouter {

   @javax.inject.Inject()
   def this(errorHandler: play.api.http.HttpErrorHandler,
    // @LINE:5
    NossisAuthenticator_2: pt.alticelabs.nossis.security.controllers.NossisAuthenticator,
    // @LINE:12
    Application_0: na.naportalbase.controllers.Application,
    // @LINE:18
    DateTimePicker_1: na.naportalbase.controllers.DateTimePicker,
    // @LINE:28
    Assets_3: na.naportalbase.controllers.Assets
  ) = this(errorHandler, NossisAuthenticator_2, Application_0, DateTimePicker_1, Assets_3, "/")

  def withPrefix(prefix: String): Routes = {
    naportalbase.RoutesPrefix.setPrefix(prefix)
    new Routes(errorHandler, NossisAuthenticator_2, Application_0, DateTimePicker_1, Assets_3, prefix)
  }

  private[this] val defaultPrefix: String = {
    if (this.prefix.endsWith("/")) "" else "/"
  }

  def documentation = List(
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """login""", """pt.alticelabs.nossis.security.controllers.NossisAuthenticator.login()"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """login""", """pt.alticelabs.nossis.security.controllers.NossisAuthenticator.login()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """logout""", """pt.alticelabs.nossis.security.controllers.NossisAuthenticator.logout()"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """logout""", """pt.alticelabs.nossis.security.controllers.NossisAuthenticator.logout()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """naportalbase/resume""", """na.naportalbase.controllers.Application.resume()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """naportalbase/i18n""", """na.naportalbase.controllers.Application.jsMessages()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """naportalbase/i18n-json""", """na.naportalbase.controllers.Application.jsMessagesJson()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """naportalbase/i18n-json/""" + "$" + """lang<[^/]+>""", """na.naportalbase.controllers.Application.jsMessagesJsonForLang(lang:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """naportalbase/datetimepickerconf""", """na.naportalbase.controllers.DateTimePicker.dateTimePickerConf()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """naportalbase/assets/javascripts/routes""", """na.naportalbase.controllers.Application.javascriptRoutes()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """naportalbase/assets/javascripts/dateformats.js""", """na.naportalbase.controllers.Application.jsDateFormats()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """naportalbase/imports/jsscripts""", """na.naportalbase.controllers.Application.getBasemoduleAngularScripts"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """naportalbase/configs/exportlimit""", """na.naportalbase.controllers.Application.getApplicationExportLimit"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """naportalbase/assets/""" + "$" + """file<.+>""", """na.naportalbase.controllers.Assets.versioned(path:String = "/public", file:Asset)"""),
    Nil
  ).foldLeft(List.empty[(String,String,String)]) { (s,e) => e.asInstanceOf[Any] match {
    case r @ (_,_,_) => s :+ r.asInstanceOf[(String,String,String)]
    case l => s ++ l.asInstanceOf[List[(String,String,String)]]
  }}


  // @LINE:5
  private[this] lazy val pt_alticelabs_nossis_security_controllers_NossisAuthenticator_login0_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("login")))
  )
  private[this] lazy val pt_alticelabs_nossis_security_controllers_NossisAuthenticator_login0_invoker = createInvoker(
    NossisAuthenticator_2.login(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "naportalbase",
      "pt.alticelabs.nossis.security.controllers.NossisAuthenticator",
      "login",
      Nil,
      "GET",
      this.prefix + """login""",
      """""",
      Seq()
    )
  )

  // @LINE:6
  private[this] lazy val pt_alticelabs_nossis_security_controllers_NossisAuthenticator_login1_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("login")))
  )
  private[this] lazy val pt_alticelabs_nossis_security_controllers_NossisAuthenticator_login1_invoker = createInvoker(
    NossisAuthenticator_2.login(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "naportalbase",
      "pt.alticelabs.nossis.security.controllers.NossisAuthenticator",
      "login",
      Nil,
      "POST",
      this.prefix + """login""",
      """""",
      Seq()
    )
  )

  // @LINE:7
  private[this] lazy val pt_alticelabs_nossis_security_controllers_NossisAuthenticator_logout2_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("logout")))
  )
  private[this] lazy val pt_alticelabs_nossis_security_controllers_NossisAuthenticator_logout2_invoker = createInvoker(
    NossisAuthenticator_2.logout(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "naportalbase",
      "pt.alticelabs.nossis.security.controllers.NossisAuthenticator",
      "logout",
      Nil,
      "GET",
      this.prefix + """logout""",
      """""",
      Seq()
    )
  )

  // @LINE:8
  private[this] lazy val pt_alticelabs_nossis_security_controllers_NossisAuthenticator_logout3_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("logout")))
  )
  private[this] lazy val pt_alticelabs_nossis_security_controllers_NossisAuthenticator_logout3_invoker = createInvoker(
    NossisAuthenticator_2.logout(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "naportalbase",
      "pt.alticelabs.nossis.security.controllers.NossisAuthenticator",
      "logout",
      Nil,
      "POST",
      this.prefix + """logout""",
      """""",
      Seq()
    )
  )

  // @LINE:12
  private[this] lazy val na_naportalbase_controllers_Application_resume4_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("naportalbase/resume")))
  )
  private[this] lazy val na_naportalbase_controllers_Application_resume4_invoker = createInvoker(
    Application_0.resume(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "naportalbase",
      "na.naportalbase.controllers.Application",
      "resume",
      Nil,
      "GET",
      this.prefix + """naportalbase/resume""",
      """""",
      Seq()
    )
  )

  // @LINE:13
  private[this] lazy val na_naportalbase_controllers_Application_jsMessages5_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("naportalbase/i18n")))
  )
  private[this] lazy val na_naportalbase_controllers_Application_jsMessages5_invoker = createInvoker(
    Application_0.jsMessages(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "naportalbase",
      "na.naportalbase.controllers.Application",
      "jsMessages",
      Nil,
      "GET",
      this.prefix + """naportalbase/i18n""",
      """""",
      Seq()
    )
  )

  // @LINE:14
  private[this] lazy val na_naportalbase_controllers_Application_jsMessagesJson6_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("naportalbase/i18n-json")))
  )
  private[this] lazy val na_naportalbase_controllers_Application_jsMessagesJson6_invoker = createInvoker(
    Application_0.jsMessagesJson(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "naportalbase",
      "na.naportalbase.controllers.Application",
      "jsMessagesJson",
      Nil,
      "GET",
      this.prefix + """naportalbase/i18n-json""",
      """""",
      Seq()
    )
  )

  // @LINE:15
  private[this] lazy val na_naportalbase_controllers_Application_jsMessagesJsonForLang7_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("naportalbase/i18n-json/"), DynamicPart("lang", """[^/]+""",true)))
  )
  private[this] lazy val na_naportalbase_controllers_Application_jsMessagesJsonForLang7_invoker = createInvoker(
    Application_0.jsMessagesJsonForLang(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "naportalbase",
      "na.naportalbase.controllers.Application",
      "jsMessagesJsonForLang",
      Seq(classOf[String]),
      "GET",
      this.prefix + """naportalbase/i18n-json/""" + "$" + """lang<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:18
  private[this] lazy val na_naportalbase_controllers_DateTimePicker_dateTimePickerConf8_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("naportalbase/datetimepickerconf")))
  )
  private[this] lazy val na_naportalbase_controllers_DateTimePicker_dateTimePickerConf8_invoker = createInvoker(
    DateTimePicker_1.dateTimePickerConf(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "naportalbase",
      "na.naportalbase.controllers.DateTimePicker",
      "dateTimePickerConf",
      Nil,
      "GET",
      this.prefix + """naportalbase/datetimepickerconf""",
      """ DatePicker""",
      Seq()
    )
  )

  // @LINE:21
  private[this] lazy val na_naportalbase_controllers_Application_javascriptRoutes9_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("naportalbase/assets/javascripts/routes")))
  )
  private[this] lazy val na_naportalbase_controllers_Application_javascriptRoutes9_invoker = createInvoker(
    Application_0.javascriptRoutes(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "naportalbase",
      "na.naportalbase.controllers.Application",
      "javascriptRoutes",
      Nil,
      "GET",
      this.prefix + """naportalbase/assets/javascripts/routes""",
      """ Javascript Routing""",
      Seq()
    )
  )

  // @LINE:22
  private[this] lazy val na_naportalbase_controllers_Application_jsDateFormats10_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("naportalbase/assets/javascripts/dateformats.js")))
  )
  private[this] lazy val na_naportalbase_controllers_Application_jsDateFormats10_invoker = createInvoker(
    Application_0.jsDateFormats(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "naportalbase",
      "na.naportalbase.controllers.Application",
      "jsDateFormats",
      Nil,
      "GET",
      this.prefix + """naportalbase/assets/javascripts/dateformats.js""",
      """""",
      Seq()
    )
  )

  // @LINE:24
  private[this] lazy val na_naportalbase_controllers_Application_getBasemoduleAngularScripts11_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("naportalbase/imports/jsscripts")))
  )
  private[this] lazy val na_naportalbase_controllers_Application_getBasemoduleAngularScripts11_invoker = createInvoker(
    Application_0.getBasemoduleAngularScripts,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "naportalbase",
      "na.naportalbase.controllers.Application",
      "getBasemoduleAngularScripts",
      Nil,
      "GET",
      this.prefix + """naportalbase/imports/jsscripts""",
      """""",
      Seq()
    )
  )

  // @LINE:25
  private[this] lazy val na_naportalbase_controllers_Application_getApplicationExportLimit12_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("naportalbase/configs/exportlimit")))
  )
  private[this] lazy val na_naportalbase_controllers_Application_getApplicationExportLimit12_invoker = createInvoker(
    Application_0.getApplicationExportLimit,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "naportalbase",
      "na.naportalbase.controllers.Application",
      "getApplicationExportLimit",
      Nil,
      "GET",
      this.prefix + """naportalbase/configs/exportlimit""",
      """""",
      Seq()
    )
  )

  // @LINE:28
  private[this] lazy val na_naportalbase_controllers_Assets_versioned13_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("naportalbase/assets/"), DynamicPart("file", """.+""",false)))
  )
  private[this] lazy val na_naportalbase_controllers_Assets_versioned13_invoker = createInvoker(
    Assets_3.versioned(fakeValue[String], fakeValue[Asset]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "naportalbase",
      "na.naportalbase.controllers.Assets",
      "versioned",
      Seq(classOf[String], classOf[Asset]),
      "GET",
      this.prefix + """naportalbase/assets/""" + "$" + """file<.+>""",
      """ Map static resources from the /public folder to the /assets URL path""",
      Seq()
    )
  )


  def routes: PartialFunction[RequestHeader, Handler] = {
  
    // @LINE:5
    case pt_alticelabs_nossis_security_controllers_NossisAuthenticator_login0_route(params@_) =>
      call { 
        pt_alticelabs_nossis_security_controllers_NossisAuthenticator_login0_invoker.call(NossisAuthenticator_2.login())
      }
  
    // @LINE:6
    case pt_alticelabs_nossis_security_controllers_NossisAuthenticator_login1_route(params@_) =>
      call { 
        pt_alticelabs_nossis_security_controllers_NossisAuthenticator_login1_invoker.call(NossisAuthenticator_2.login())
      }
  
    // @LINE:7
    case pt_alticelabs_nossis_security_controllers_NossisAuthenticator_logout2_route(params@_) =>
      call { 
        pt_alticelabs_nossis_security_controllers_NossisAuthenticator_logout2_invoker.call(NossisAuthenticator_2.logout())
      }
  
    // @LINE:8
    case pt_alticelabs_nossis_security_controllers_NossisAuthenticator_logout3_route(params@_) =>
      call { 
        pt_alticelabs_nossis_security_controllers_NossisAuthenticator_logout3_invoker.call(NossisAuthenticator_2.logout())
      }
  
    // @LINE:12
    case na_naportalbase_controllers_Application_resume4_route(params@_) =>
      call { 
        na_naportalbase_controllers_Application_resume4_invoker.call(Application_0.resume())
      }
  
    // @LINE:13
    case na_naportalbase_controllers_Application_jsMessages5_route(params@_) =>
      call { 
        na_naportalbase_controllers_Application_jsMessages5_invoker.call(Application_0.jsMessages())
      }
  
    // @LINE:14
    case na_naportalbase_controllers_Application_jsMessagesJson6_route(params@_) =>
      call { 
        na_naportalbase_controllers_Application_jsMessagesJson6_invoker.call(Application_0.jsMessagesJson())
      }
  
    // @LINE:15
    case na_naportalbase_controllers_Application_jsMessagesJsonForLang7_route(params@_) =>
      call(params.fromPath[String]("lang", None)) { (lang) =>
        na_naportalbase_controllers_Application_jsMessagesJsonForLang7_invoker.call(Application_0.jsMessagesJsonForLang(lang))
      }
  
    // @LINE:18
    case na_naportalbase_controllers_DateTimePicker_dateTimePickerConf8_route(params@_) =>
      call { 
        na_naportalbase_controllers_DateTimePicker_dateTimePickerConf8_invoker.call(DateTimePicker_1.dateTimePickerConf())
      }
  
    // @LINE:21
    case na_naportalbase_controllers_Application_javascriptRoutes9_route(params@_) =>
      call { 
        na_naportalbase_controllers_Application_javascriptRoutes9_invoker.call(Application_0.javascriptRoutes())
      }
  
    // @LINE:22
    case na_naportalbase_controllers_Application_jsDateFormats10_route(params@_) =>
      call { 
        na_naportalbase_controllers_Application_jsDateFormats10_invoker.call(Application_0.jsDateFormats())
      }
  
    // @LINE:24
    case na_naportalbase_controllers_Application_getBasemoduleAngularScripts11_route(params@_) =>
      call { 
        na_naportalbase_controllers_Application_getBasemoduleAngularScripts11_invoker.call(Application_0.getBasemoduleAngularScripts)
      }
  
    // @LINE:25
    case na_naportalbase_controllers_Application_getApplicationExportLimit12_route(params@_) =>
      call { 
        na_naportalbase_controllers_Application_getApplicationExportLimit12_invoker.call(Application_0.getApplicationExportLimit)
      }
  
    // @LINE:28
    case na_naportalbase_controllers_Assets_versioned13_route(params@_) =>
      call(Param[String]("path", Right("/public")), params.fromPath[Asset]("file", None)) { (path, file) =>
        na_naportalbase_controllers_Assets_versioned13_invoker.call(Assets_3.versioned(path, file))
      }
  }
}
