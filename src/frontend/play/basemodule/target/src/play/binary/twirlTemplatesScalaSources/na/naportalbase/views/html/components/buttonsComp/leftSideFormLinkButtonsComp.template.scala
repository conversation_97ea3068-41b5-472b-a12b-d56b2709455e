
package na.naportalbase.views.html.components.buttonsComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object leftSideFormLinkButtonsComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[scala.collection.mutable.HashMap[String, String],scala.collection.mutable.HashMap[String, String],String,String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(button1Attributes: scala.collection.mutable.HashMap[String, String], button2Attributes: scala.collection.mutable.HashMap[String, String], label1:String, label2:String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*3.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*1.170*/("""

"""),format.raw/*4.1*/("""
"""),format.raw/*5.1*/("""<a role="button" class="btn btn-primary btn-sm btn-margin pull-right"
    """),_display_(/*6.6*/TemplateUtils/*6.19*/.dynamicElementsAttributes(button1Attributes)),format.raw/*6.64*/(""">
"""),_display_(/*7.2*/label1),format.raw/*7.8*/("""
"""),format.raw/*8.1*/("""</a>
<a role="button" class="btn btn-default btn-sm pull-right" style="margin-right:10px;"
    """),_display_(/*10.6*/TemplateUtils/*10.19*/.dynamicElementsAttributes(button2Attributes)),format.raw/*10.64*/(""">
"""),_display_(/*11.2*/label2),format.raw/*11.8*/("""
"""),format.raw/*12.1*/("""</a>"""))
      }
    }
  }

  def render(button1Attributes:scala.collection.mutable.HashMap[String, String],button2Attributes:scala.collection.mutable.HashMap[String, String],label1:String,label2:String): play.twirl.api.HtmlFormat.Appendable = apply(button1Attributes,button2Attributes,label1,label2)

  def f:((scala.collection.mutable.HashMap[String, String],scala.collection.mutable.HashMap[String, String],String,String) => play.twirl.api.HtmlFormat.Appendable) = (button1Attributes,button2Attributes,label1,label2) => apply(button1Attributes,button2Attributes,label1,label2)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:30:45 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/buttonsComp/leftSideFormLinkButtonsComp.scala.html
                  HASH: fc354090a41b1255234c0050171b7a760c8da560
                  MATRIX: 1114->1|1355->172|1428->169|1456->215|1483->216|1583->291|1604->304|1669->349|1697->352|1722->358|1749->359|1871->455|1893->468|1959->513|1988->516|2014->522|2042->523
                  LINES: 28->1|31->3|34->1|36->4|37->5|38->6|38->6|38->6|39->7|39->7|40->8|42->10|42->10|42->10|43->11|43->11|44->12
                  -- GENERATED --
              */
          