
package na.naportalbase.views.html.components.selectsComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object selectWithGroupsComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[scala.collection.immutable.Map[String, String],scala.collection.immutable.Map[scala.collection.immutable.HashMap[String, String], Html],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(selectAttributes: scala.collection.immutable.Map[String, String],
        selectData: scala.collection.immutable.Map[scala.collection.immutable.HashMap[String,String], Html]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*12.2*/import na.naportalbase.utils.TemplateUtils

def /*4.2*/dynamicOptions/*4.16*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*4.20*/("""
    """),_display_(/*5.6*/for((key,value) <- selectData) yield /*5.36*/{_display_(Seq[Any](format.raw/*5.37*/("""
        """),format.raw/*6.9*/("""<optgroup label="""),_display_(/*6.26*/key/*6.29*/.get("text")),format.raw/*6.41*/(""" """),format.raw/*6.42*/("""id="""),_display_(/*6.46*/key/*6.49*/.get("id")),format.raw/*6.59*/(""">
            """),_display_(/*7.14*/value),format.raw/*7.19*/("""
        """),format.raw/*8.9*/("""</optgroup>
    """)))}),format.raw/*9.6*/("""
""")))};
Seq[Any](format.raw/*2.109*/("""

"""),format.raw/*10.2*/("""

"""),format.raw/*13.1*/("""
"""),format.raw/*14.1*/("""<select """),_display_(/*14.10*/TemplateUtils/*14.23*/.dynamicElementsAttributes(selectAttributes)),format.raw/*14.67*/(""">
    """),format.raw/*15.71*/("""
    """),_display_(/*16.6*/if(!selectAttributes.contains("value") || selectAttributes.exists(_ == "value" -> ""))/*16.92*/{_display_(Seq[Any](format.raw/*16.93*/("""
        """),format.raw/*17.9*/("""<option></option>
    """)))}),format.raw/*18.6*/("""
    """),_display_(/*19.6*/dynamicOptions),format.raw/*19.20*/("""
"""),format.raw/*20.1*/("""</select>"""))
      }
    }
  }

  def render(selectAttributes:scala.collection.immutable.Map[String, String],selectData:scala.collection.immutable.Map[scala.collection.immutable.HashMap[String, String], Html]): play.twirl.api.HtmlFormat.Appendable = apply(selectAttributes,selectData)

  def f:((scala.collection.immutable.Map[String, String],scala.collection.immutable.Map[scala.collection.immutable.HashMap[String, String], Html]) => play.twirl.api.HtmlFormat.Appendable) = (selectAttributes,selectData) => apply(selectAttributes,selectData)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:00 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/selectsComp/selectWithGroupsComp.scala.html
                  HASH: 359fc53ff5628d16bca4415df4e7c461e414a2d2
                  MATRIX: 1131->1|1380->344|1435->179|1457->193|1537->197|1568->203|1613->233|1651->234|1686->243|1729->260|1740->263|1772->275|1800->276|1830->280|1841->283|1871->293|1912->308|1937->313|1972->322|2018->339|2060->176|2089->341|2118->387|2146->388|2182->397|2204->410|2269->454|2303->526|2335->532|2430->618|2469->619|2505->628|2558->651|2590->657|2625->671|2653->672
                  LINES: 28->1|32->12|34->4|34->4|36->4|37->5|37->5|37->5|38->6|38->6|38->6|38->6|38->6|38->6|38->6|38->6|39->7|39->7|40->8|41->9|43->2|45->10|47->13|48->14|48->14|48->14|48->14|49->15|50->16|50->16|50->16|51->17|52->18|53->19|53->19|54->20
                  -- GENERATED --
              */
          