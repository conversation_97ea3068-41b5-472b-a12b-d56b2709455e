
package na.naportalbase.views.html.components.buttonsComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object buttonLinkComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[String,scala.collection.mutable.HashMap[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(buttonValue: String, buttonAttributes: scala.collection.mutable.HashMap[String, String]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*3.2*/import na.naportalbase.utils.TemplateUtils
/*4.2*/import na.naportalbase.views.tags.i18n


Seq[Any](format.raw/*1.91*/("""

"""),format.raw/*5.1*/("""
"""),format.raw/*6.1*/("""<a title=""""),_display_(/*6.12*/i18n(buttonValue)),format.raw/*6.29*/(""""
        """),_display_(/*7.10*/TemplateUtils/*7.23*/.dynamicElementsAttributes(buttonAttributes)),format.raw/*7.67*/(""">
        """),_display_(/*8.10*/i18n(buttonValue)),format.raw/*8.27*/("""
"""),format.raw/*9.1*/("""</a>"""))
      }
    }
  }

  def render(buttonValue:String,buttonAttributes:scala.collection.mutable.HashMap[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(buttonValue,buttonAttributes)

  def f:((String,scala.collection.mutable.HashMap[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (buttonValue,buttonAttributes) => apply(buttonValue,buttonAttributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:00 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/buttonsComp/buttonLinkComp.scala.html
                  HASH: 9b5c5b79ea7cd2ae03d0e6f2e2c0e24642518130
                  MATRIX: 1045->1|1207->93|1257->137|1325->90|1353->176|1380->177|1417->188|1454->205|1491->216|1512->229|1576->273|1613->284|1650->301|1677->302
                  LINES: 28->1|31->3|32->4|35->1|37->5|38->6|38->6|38->6|39->7|39->7|39->7|40->8|40->8|41->9
                  -- GENERATED --
              */
          