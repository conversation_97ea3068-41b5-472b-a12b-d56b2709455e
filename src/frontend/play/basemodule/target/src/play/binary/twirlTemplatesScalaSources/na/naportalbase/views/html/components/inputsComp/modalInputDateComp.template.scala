
package na.naportalbase.views.html.components.inputsComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object modalInputDateComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template8[String,String,String,String,String,scala.collection.mutable.HashMap[String, String],scala.collection.mutable.HashMap[String, String],scala.collection.mutable.HashMap[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(label: String,
        inputId: String,
        datePickerId: String,
		dateFormat: String,
		extension: String,
        datePickerAttributes: scala.collection.mutable.HashMap[String, String],
        inputAttributes: scala.collection.mutable.HashMap[String, String],
        inputNotificationAttributes: scala.collection.mutable.HashMap[String, String]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*10.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*8.87*/("""

"""),format.raw/*11.1*/("""
"""),format.raw/*12.1*/("""<label for=""""),_display_(/*12.14*/datePickerId),format.raw/*12.26*/("""" class="control-label col-sm-3">"""),_display_(/*12.60*/label),format.raw/*12.65*/("""</label>
<div class="col-sm-9">
    <div class="input-group datetime" data-datetimepicker-ext="""),_display_(/*14.64*/extension),format.raw/*14.73*/("""
	"""),format.raw/*15.2*/("""data-java-date-format=""""),_display_(/*15.26*/dateFormat),format.raw/*15.36*/("""" """),_display_(/*15.39*/TemplateUtils/*15.52*/.dynamicElementsAttributes(datePickerAttributes)),format.raw/*15.100*/(""">
        <div class="input-group">
            <input class="form-control input-sm" type="text" """),_display_(/*17.63*/TemplateUtils/*17.76*/.dynamicElementsAttributes(inputAttributes)),format.raw/*17.119*/("""
            """),_display_(/*18.14*/if(inputNotificationAttributes != null)/*18.53*/ {_display_(Seq[Any](format.raw/*18.55*/("""
                """),_display_(/*19.18*/TemplateUtils/*19.31*/.dynamicElementsAttributes(inputNotificationAttributes)),format.raw/*19.86*/("""
            """)))}),format.raw/*20.14*/("""
            """),format.raw/*21.13*/("""/>
            <span class="input-group-addon datepickerbutton"><span class="glyphicon glyphicon-calendar" ></span></span>
        </div>
    </div>
</div>
"""))
      }
    }
  }

  def render(label:String,inputId:String,datePickerId:String,dateFormat:String,extension:String,datePickerAttributes:scala.collection.mutable.HashMap[String, String],inputAttributes:scala.collection.mutable.HashMap[String, String],inputNotificationAttributes:scala.collection.mutable.HashMap[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(label,inputId,datePickerId,dateFormat,extension,datePickerAttributes,inputAttributes,inputNotificationAttributes)

  def f:((String,String,String,String,String,scala.collection.mutable.HashMap[String, String],scala.collection.mutable.HashMap[String, String],scala.collection.mutable.HashMap[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (label,inputId,datePickerId,dateFormat,extension,datePickerAttributes,inputAttributes,inputNotificationAttributes) => apply(label,inputId,datePickerId,dateFormat,extension,datePickerAttributes,inputAttributes,inputNotificationAttributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:30:45 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/inputsComp/modalInputDateComp.scala.html
                  HASH: 58edd4101c1189fe3549a9060aab183a6ed002a6
                  MATRIX: 1174->1|1603->359|1675->356|1704->402|1732->403|1772->416|1805->428|1866->462|1892->467|2014->562|2044->571|2073->573|2124->597|2155->607|2185->610|2207->623|2277->671|2402->769|2424->782|2489->825|2530->839|2578->878|2618->880|2663->898|2685->911|2761->966|2806->980|2847->993
                  LINES: 28->1|38->10|41->8|43->11|44->12|44->12|44->12|44->12|44->12|46->14|46->14|47->15|47->15|47->15|47->15|47->15|47->15|49->17|49->17|49->17|50->18|50->18|50->18|51->19|51->19|51->19|52->20|53->21
                  -- GENERATED --
              */
          