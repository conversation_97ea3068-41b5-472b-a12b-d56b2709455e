// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/conf/naportalbase.routes
// @DATE:Tue Jul 01 11:20:59 WEST 2025

import play.api.mvc.Call


import _root_.controllers.Assets.Asset

// @LINE:12
package na.naportalbase.controllers {

  // @LINE:28
  class ReverseAssets(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:28
    def versioned(file:Asset): Call = {
      implicit lazy val _rrc = new play.core.routing.ReverseRouteContext(Map(("path", "/public"))); _rrc
      Call("GET", _prefix + { _defaultPrefix } + "naportalbase/assets/" + implicitly[play.api.mvc.PathBindable[Asset]].unbind("file", file))
    }
  
  }

  // @LINE:18
  class ReverseDateTimePicker(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:18
    def dateTimePickerConf(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "naportalbase/datetimepickerconf")
    }
  
  }

  // @LINE:12
  class ReverseApplication(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:14
    def jsMessagesJson(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "naportalbase/i18n-json")
    }
  
    // @LINE:12
    def resume(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "naportalbase/resume")
    }
  
    // @LINE:24
    def getBasemoduleAngularScripts(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "naportalbase/imports/jsscripts")
    }
  
    // @LINE:15
    def jsMessagesJsonForLang(lang:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "naportalbase/i18n-json/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("lang", lang)))
    }
  
    // @LINE:21
    def javascriptRoutes(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "naportalbase/assets/javascripts/routes")
    }
  
    // @LINE:22
    def jsDateFormats(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "naportalbase/assets/javascripts/dateformats.js")
    }
  
    // @LINE:13
    def jsMessages(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "naportalbase/i18n")
    }
  
    // @LINE:25
    def getApplicationExportLimit(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "naportalbase/configs/exportlimit")
    }
  
  }


}
