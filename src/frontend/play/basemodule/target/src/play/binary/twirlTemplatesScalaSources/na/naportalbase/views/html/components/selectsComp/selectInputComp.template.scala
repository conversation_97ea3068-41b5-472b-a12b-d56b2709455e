
package na.naportalbase.views.html.components.selectsComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object selectInputComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[scala.collection.mutable.HashMap[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(selectAttributes: scala.collection.mutable.HashMap[String, String]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.70*/("""
"""),_display_(/*2.2*/na/*2.4*/.naportalbase.views.html.components.selectsComp.selectComp.render(collection.immutable.HashMap(selectAttributes.toSeq:_*), null, null, null)),format.raw/*2.144*/("""
"""))
      }
    }
  }

  def render(selectAttributes:scala.collection.mutable.HashMap[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(selectAttributes)

  def f:((scala.collection.mutable.HashMap[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (selectAttributes) => apply(selectAttributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:00 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/selectsComp/selectInputComp.scala.html
                  HASH: 266cba0ea5c0eea7bc27c032fa0cfe768c4a6a7b
                  MATRIX: 1039->1|1202->69|1229->71|1238->73|1399->213
                  LINES: 28->1|33->1|34->2|34->2|34->2
                  -- GENERATED --
              */
          