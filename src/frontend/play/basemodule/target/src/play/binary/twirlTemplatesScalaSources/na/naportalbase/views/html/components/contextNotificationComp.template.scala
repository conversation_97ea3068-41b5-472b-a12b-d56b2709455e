
package na.naportalbase.views.html.components

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object contextNotificationComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[scala.collection.mutable.HashMap[String, String],String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(contextNotificationAttributes: scala.collection.mutable.HashMap[String,String],message: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*3.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*1.98*/("""

"""),format.raw/*4.1*/("""
"""),format.raw/*5.1*/("""<data-na-portal-context-notification """),_display_(/*5.39*/TemplateUtils/*5.52*/.dynamicElementsAttributes(contextNotificationAttributes)),format.raw/*5.109*/(""">
    """),_display_(/*6.6*/message),format.raw/*6.13*/("""
"""),format.raw/*7.1*/("""</data-na-portal-context-notification>
"""))
      }
    }
  }

  def render(contextNotificationAttributes:scala.collection.mutable.HashMap[String, String],message:String): play.twirl.api.HtmlFormat.Appendable = apply(contextNotificationAttributes,message)

  def f:((scala.collection.mutable.HashMap[String, String],String) => play.twirl.api.HtmlFormat.Appendable) = (contextNotificationAttributes,message) => apply(contextNotificationAttributes,message)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:30:45 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/contextNotificationComp.scala.html
                  HASH: 871e6954b17bfdf33eaed46d2a750df7107d2872
                  MATRIX: 1042->1|1211->100|1283->97|1311->143|1338->144|1402->182|1423->195|1501->252|1533->259|1560->266|1587->267
                  LINES: 28->1|31->3|34->1|36->4|37->5|37->5|37->5|37->5|38->6|38->6|39->7
                  -- GENERATED --
              */
          