
package na.naportalbase.views.html.skeletons.splitter

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object splitterSidebarContentWithFormSkel extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[Html,Html,Html,Html,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(headerContent: Html,form: Html,formButtons: Html,buttons: Html):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.66*/("""

"""),format.raw/*3.1*/("""<div class="fx-splitter-sidebar-content">
    """),_display_(/*4.6*/na/*4.8*/.naportalbase.views.html.components.splitterComp.sidebarHeaderComp.render(headerContent)),format.raw/*4.96*/("""
    """),format.raw/*5.5*/("""<div class="fx-sidebar-content-wrapper" role="form">
        """),_display_(/*6.10*/form),format.raw/*6.14*/("""
        """),format.raw/*7.9*/("""<div class="pull-right">
            <div class="form-group">
                """),_display_(/*9.18*/buttons),format.raw/*9.25*/("""
            """),format.raw/*10.13*/("""</div>
        </div>
    </div>
</div>
"""),_display_(/*14.2*/formButtons))
      }
    }
  }

  def render(headerContent:Html,form:Html,formButtons:Html,buttons:Html): play.twirl.api.HtmlFormat.Appendable = apply(headerContent,form,formButtons,buttons)

  def f:((Html,Html,Html,Html) => play.twirl.api.HtmlFormat.Appendable) = (headerContent,form,formButtons,buttons) => apply(headerContent,form,formButtons,buttons)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:30:45 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/skeletons/splitter/splitterSidebarContentWithFormSkel.scala.html
                  HASH: c569eb11f60d20a7967ee71dd2ae8a70546e50a8
                  MATRIX: 1025->1|1184->65|1212->67|1284->114|1293->116|1401->204|1432->209|1520->271|1544->275|1579->284|1684->363|1711->370|1752->383|1819->424
                  LINES: 28->1|33->1|35->3|36->4|36->4|36->4|37->5|38->6|38->6|39->7|41->9|41->9|42->10|46->14
                  -- GENERATED --
              */
          