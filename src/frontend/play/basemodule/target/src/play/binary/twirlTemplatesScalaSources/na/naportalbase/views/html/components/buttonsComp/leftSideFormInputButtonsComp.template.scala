
package na.naportalbase.views.html.components.buttonsComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object leftSideFormInputButtonsComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[scala.collection.mutable.HashMap[String, String],scala.collection.mutable.HashMap[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(button1Attributes: scala.collection.mutable.HashMap[String, String], button2Attributes: scala.collection.mutable.HashMap[String, String]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*3.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*1.140*/("""

"""),format.raw/*4.1*/("""
"""),format.raw/*5.1*/("""<input role="button" class="btn btn-default btn-sm pull-right"
    """),_display_(/*6.6*/TemplateUtils/*6.19*/.dynamicElementsAttributes(button2Attributes)),format.raw/*6.64*/(""">
</input>

"""),_display_(/*9.2*/if(button1Attributes != null)/*9.31*/ {_display_(Seq[Any](format.raw/*9.33*/("""
    """),format.raw/*10.5*/("""<input role="button" class="btn btn-primary btn-sm btn-margin pull-right" style="margin-right:10px;"
        """),_display_(/*11.10*/TemplateUtils/*11.23*/.dynamicElementsAttributes(button1Attributes)),format.raw/*11.68*/(""">
    </input>
""")))}))
      }
    }
  }

  def render(button1Attributes:scala.collection.mutable.HashMap[String, String],button2Attributes:scala.collection.mutable.HashMap[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(button1Attributes,button2Attributes)

  def f:((scala.collection.mutable.HashMap[String, String],scala.collection.mutable.HashMap[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (button1Attributes,button2Attributes) => apply(button1Attributes,button2Attributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:30:45 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/buttonsComp/leftSideFormInputButtonsComp.scala.html
                  HASH: 03edee56c5fabdb6173f2b5fb5ebb9f9eede9dc7
                  MATRIX: 1101->1|1312->142|1385->139|1413->185|1440->186|1533->254|1554->267|1619->312|1657->325|1694->354|1733->356|1765->361|1902->471|1924->484|1990->529
                  LINES: 28->1|31->3|34->1|36->4|37->5|38->6|38->6|38->6|41->9|41->9|41->9|42->10|43->11|43->11|43->11
                  -- GENERATED --
              */
          