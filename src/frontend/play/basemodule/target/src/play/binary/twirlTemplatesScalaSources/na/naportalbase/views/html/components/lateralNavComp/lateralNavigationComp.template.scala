
package na.naportalbase.views.html.components.lateralNavComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object lateralNavigationComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[scala.collection.mutable.LinkedHashMap[String, na.naportalbase.models.lateralNav.LateralNavigationComponentItem],String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        navElements: scala.collection.mutable.LinkedHashMap[String, na.naportalbase.models.lateralNav.LateralNavigationComponentItem],
        attribute: String
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*6.2*/import na.naportalbase.models.lateralNav.LateralNavigationComponentItem
/*7.2*/import na.naportalbase.views.tags.i18n
/*8.2*/import scala.collection.mutable.ArrayBuffer

def /*10.2*/linkClassAttr/*10.15*/(value: LateralNavigationComponentItem) = {{
    var result = new ArrayBuffer[String]()
    if(value.isActive) {
        result += "fx-active"
    }
    if(!value.getLinkClass.isEmpty) {
        result += value.getLinkClass
    }
    if(result.nonEmpty) result.mkString(" ") else ""
}};
Seq[Any](format.raw/*4.2*/("""

"""),format.raw/*9.1*/("""
"""),format.raw/*19.2*/("""

"""),format.raw/*21.1*/("""<!-- data-enable-tooltip -->
<nav id="fx-page-main-nav" class="fx-ico-nav" aria-label="Sidebar navigation" """),_display_(/*22.80*/attribute),format.raw/*22.89*/(""">
    <ul>
    """),_display_(/*24.6*/for((key, value) <- navElements) yield /*24.38*/ {_display_(Seq[Any](format.raw/*24.40*/("""
        """),format.raw/*25.9*/("""<li
        class=""""),_display_(/*26.17*/linkClassAttr(value)),format.raw/*26.37*/(""""
        data-placement="right"
        data-toggle="tooltip"
        data-enable-tooltip
        data-original-title=""""),_display_(/*30.31*/i18n(key)),format.raw/*30.40*/(""""
        >
            <a data-nav-action=""""),_display_(/*32.34*/value/*32.39*/.getClickAction),format.raw/*32.54*/("""">
                <i class=""""),_display_(/*33.28*/value/*33.33*/.getTabIcon),format.raw/*33.44*/(""""></i>
            </a>
        </li>
    """)))}),format.raw/*36.6*/("""
    """),format.raw/*37.5*/("""</ul>
</nav>



"""))
      }
    }
  }

  def render(navElements:scala.collection.mutable.LinkedHashMap[String, na.naportalbase.models.lateralNav.LateralNavigationComponentItem],attribute:String): play.twirl.api.HtmlFormat.Appendable = apply(navElements,attribute)

  def f:((scala.collection.mutable.LinkedHashMap[String, na.naportalbase.models.lateralNav.LateralNavigationComponentItem],String) => play.twirl.api.HtmlFormat.Appendable) = (navElements,attribute) => apply(navElements,attribute)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:00 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/lateralNavComp/lateralNavigationComp.scala.html
                  HASH: abc588b0d10be73c4e7a35475678ba997d054022
                  MATRIX: 1119->1|1356->168|1435->241|1481->281|1538->327|1560->340|1873->165|1901->325|1929->624|1958->626|2093->734|2123->743|2165->759|2213->791|2253->793|2289->802|2336->822|2377->842|2525->963|2555->972|2627->1017|2641->1022|2677->1037|2734->1067|2748->1072|2780->1083|2853->1126|2885->1131
                  LINES: 28->1|34->6|35->7|36->8|38->10|38->10|48->4|50->9|51->19|53->21|54->22|54->22|56->24|56->24|56->24|57->25|58->26|58->26|62->30|62->30|64->32|64->32|64->32|65->33|65->33|65->33|68->36|69->37
                  -- GENERATED --
              */
          