
package na.naportalbase.views.html.components.inputsComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object inputTextComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[String,String,scala.collection.mutable.HashMap[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(inputId: String, placeHolder: String, inputAttributes: scala.collection.mutable.HashMap[String, String]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*2.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*1.107*/("""
"""),format.raw/*3.1*/("""
"""),format.raw/*4.1*/("""<input id=""""),_display_(/*4.13*/inputId),format.raw/*4.20*/("""" type="text" """),_display_(/*4.35*/if(!inputAttributes.contains("class"))/*4.73*/{_display_(Seq[Any](format.raw/*4.74*/("""class="form-control input-sm"""")))}),format.raw/*4.104*/(""" """),format.raw/*4.105*/("""placeholder=""""),_display_(/*4.119*/placeHolder),format.raw/*4.130*/("""" """),_display_(/*4.133*/TemplateUtils/*4.146*/.dynamicElementsAttributes(inputAttributes)),format.raw/*4.189*/("""/>
"""))
      }
    }
  }

  def render(inputId:String,placeHolder:String,inputAttributes:scala.collection.mutable.HashMap[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(inputId,placeHolder,inputAttributes)

  def f:((String,String,scala.collection.mutable.HashMap[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (inputId,placeHolder,inputAttributes) => apply(inputId,placeHolder,inputAttributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 19:25:51 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/inputsComp/inputTextComp.scala.html
                  HASH: b7658a0d82a1d43ac4048a761d2c148670d03c8c
                  MATRIX: 1050->1|1228->109|1301->106|1329->153|1357->155|1395->167|1422->174|1463->189|1509->227|1547->228|1608->258|1637->259|1678->273|1710->284|1740->287|1762->300|1826->343
                  LINES: 28->1|31->2|34->1|35->3|36->4|36->4|36->4|36->4|36->4|36->4|36->4|36->4|36->4|36->4|36->4|36->4|36->4
                  -- GENERATED --
              */
          