
package na.naportalbase.views.html.responses

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import play.mvc.Http
/*2.2*/import na.naportalbase.views.tags.i18n

object resume extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*3.1*/("""<!DOCTYPE html>
<html lang=""""),_display_(/*4.14*/{Http.Context.current().lang().code()}),format.raw/*4.52*/("""">
	<head>
		<meta charset="utf-8">
		<title>"""),_display_(/*7.11*/i18n("na.information.title")),format.raw/*7.39*/("""</title>
		"""),_display_(/*8.4*/na/*8.6*/.naportalbase.views.html.imports.resumescripts.render()),format.raw/*8.61*/("""
	"""),format.raw/*9.2*/("""</head>
	<body>
		<h5 class="fx-op-module-main-title"><x-i18n key="na.information.title"></x-i18n></h5>
		<p>
			<b><x-i18n key="na.information.title"></x-i18n></b><br />
			<x-i18n key="na.information.description" html></x-i18n>
		</p>
		<p>
			<b><x-i18n key="na.information.title" html></x-i18n></b><br />
			<x-i18n key="na.information.customerbenefits.description" html></x-i18n>
		</p>
		<p>
			<b><x-i18n key="na.information.title"></x-i18n></b><br />
			<x-i18n key="na.information.mainfunctionalities.description" html></x-i18n>
		</p>
	</body>
</html>
"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:00 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/responses/resume.scala.html
                  HASH: 623b3d1378285684bf3d9934afcf7fe02289a11c
                  MATRIX: 677->1|705->24|1131->64|1187->94|1245->132|1320->181|1368->209|1406->222|1415->224|1490->279|1519->282
                  LINES: 24->1|25->2|35->3|36->4|36->4|39->7|39->7|40->8|40->8|40->8|41->9
                  -- GENERATED --
              */
          