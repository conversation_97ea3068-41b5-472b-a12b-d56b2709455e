// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/conf/naportalbase.routes
// @DATE:Tue Jul 01 11:20:59 WEST 2025

package na.naportalbase.controllers;

import naportalbase.RoutesPrefix;

public class routes {
  
  public static final na.naportalbase.controllers.ReverseAssets Assets = new na.naportalbase.controllers.ReverseAssets(RoutesPrefix.byNamePrefix());
  public static final na.naportalbase.controllers.ReverseDateTimePicker DateTimePicker = new na.naportalbase.controllers.ReverseDateTimePicker(RoutesPrefix.byNamePrefix());
  public static final na.naportalbase.controllers.ReverseApplication Application = new na.naportalbase.controllers.ReverseApplication(RoutesPrefix.byNamePrefix());

  public static class javascript {
    
    public static final na.naportalbase.controllers.javascript.ReverseAssets Assets = new na.naportalbase.controllers.javascript.ReverseAssets(RoutesPrefix.byNamePrefix());
    public static final na.naportalbase.controllers.javascript.ReverseDateTimePicker DateTimePicker = new na.naportalbase.controllers.javascript.ReverseDateTimePicker(RoutesPrefix.byNamePrefix());
    public static final na.naportalbase.controllers.javascript.ReverseApplication Application = new na.naportalbase.controllers.javascript.ReverseApplication(RoutesPrefix.byNamePrefix());
  }

}
