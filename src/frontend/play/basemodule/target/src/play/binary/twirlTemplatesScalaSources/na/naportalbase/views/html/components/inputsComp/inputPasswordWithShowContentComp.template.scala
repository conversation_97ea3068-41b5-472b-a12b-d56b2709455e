
package na.naportalbase.views.html.components.inputsComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object inputPasswordWithShowContentComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[String,String,scala.collection.mutable.HashMap[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(inputId: String, placeHolder: String, inputAttributes: scala.collection.mutable.HashMap[String, String]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*2.2*/import na.naportalbase.utils.TemplateUtils
/*3.2*/import na.naportalbase.views.tags.i18n


Seq[Any](format.raw/*1.107*/("""
"""),format.raw/*4.1*/("""<div class="input--password input-group input-group-sm">
	<input id=""""),_display_(/*5.14*/inputId),format.raw/*5.21*/("""" type="password" """),_display_(/*5.40*/if(!inputAttributes.contains("class"))/*5.78*/{_display_(Seq[Any](format.raw/*5.79*/("""class="form-control input-sm"""")))}),format.raw/*5.109*/(""" """),format.raw/*5.110*/("""placeholder=""""),_display_(/*5.124*/placeHolder),format.raw/*5.135*/("""" """),_display_(/*5.138*/TemplateUtils/*5.151*/.dynamicElementsAttributes(inputAttributes)),format.raw/*5.194*/("""/>
	<span class="input-group-btn">
		<button class="btn btn-default" type="button" data-na-portal-toggle-show-password data-target=""""),_display_(/*7.99*/inputId),format.raw/*7.106*/("""" title=""""),_display_(/*7.116*/i18n("na.button.label.passwordShow")),format.raw/*7.152*/(""""><i class="fuxicons fuxicons-eye"></i></button>
	</span>
</div>



"""))
      }
    }
  }

  def render(inputId:String,placeHolder:String,inputAttributes:scala.collection.mutable.HashMap[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(inputId,placeHolder,inputAttributes)

  def f:((String,String,scala.collection.mutable.HashMap[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (inputId,placeHolder,inputAttributes) => apply(inputId,placeHolder,inputAttributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:30:45 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/inputsComp/inputPasswordWithShowContentComp.scala.html
                  HASH: 5477e44232d49a0d2eea9f049a4c7260167129df
                  MATRIX: 1069->1|1247->109|1297->154|1366->106|1394->194|1491->265|1518->272|1563->291|1609->329|1647->330|1708->360|1737->361|1778->375|1810->386|1840->389|1862->402|1926->445|2087->580|2115->587|2152->597|2209->633
                  LINES: 28->1|31->2|32->3|35->1|36->4|37->5|37->5|37->5|37->5|37->5|37->5|37->5|37->5|37->5|37->5|37->5|37->5|39->7|39->7|39->7|39->7
                  -- GENERATED --
              */
          