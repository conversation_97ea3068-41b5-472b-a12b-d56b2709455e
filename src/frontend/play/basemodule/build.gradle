dependencies {
    play("org.apache.logging.log4j:log4j-slf4j-impl:$version_log4j2")
    play("org.apache.logging.log4j:log4j-api:$version_log4j2")
    play("org.apache.logging.log4j:log4j-core:$version_log4j2")

    play("pt.ptinovacao.na-portal:na-portal-auditing-schemas:$version")
    play("pt.ptinovacao.na-portal:na-portal-error-commons:$version") { transitive = false }

    play("pt.ptinovacao.na-commons:na-commons-plugins-dbmanager-schema:$version_pt_ptinovacao_na_commons")

    play("pt.ptinovacao.nossis-frmwrk:exceptions:$version_pt_ptinovacao_nossis_frmwrk")

    play("pt.ptinovacao.nossis-ui:nossis-ui-portal-frmwrk:$version_pt_ptinovacao_nossis_ui") {
        transitive true
        exclude group: "com.typesafe.play", module: "play-logback_2.12"
        exclude group: "com.typesafe.play", module: "play-java-jpa_2.12"
        exclude group: "ch.qos.logback", module: "*"
        exclude group: "org.mongodb", module: "*"
    }
    play("pt.ptinovacao.nossis-ui:nossis-ui-portal-frmwrk-assets:$version_pt_ptinovacao_nossis_ui")
    play("pt.ptinovacao.nossis-ui:nossis-ui-messages:$version_pt_ptinovacao_nossis_ui") {
        transitive true
        exclude group: "com.typesafe.play", module: "play-logback_2.12"
        exclude group: "ch.qos.logback", module: "*"
    }
    play("pt.ptinovacao.nossis-ui:nossis-ui-security-adapter:$version_pt_ptinovacao_nossis_ui") {
        transitive true
        exclude group: "com.typesafe.play", module: "play-logback_2.12"
        exclude group: "ch.qos.logback", module: "*"
    }
    play("pt.ptinovacao.nossis-security:nossis-security-aaapi:$version_pt_ptinovacao_nossis_security")

    play("com.fasterxml.jackson.core:jackson-annotations:$version_com_fasterxml_jackson")
    play("com.fasterxml.jackson.core:jackson-databind:$version_com_fasterxml_jackson")
    play("com.fasterxml.jackson.module:jackson-module-jaxb-annotations:$version_com_fasterxml_jackson")

    play("com.typesafe.akka:akka-actor_$version_org_scala_base:$version_com_typesafe_akka")
    play("com.typesafe.akka:akka-stream_$version_org_scala_base:$version_com_typesafe_akka")
    
    play("com.typesafe:config:$version_com_typesafe_config")

    play("com.typesafe.play:play_$version_org_scala_base:$version_com_typesafe_play")
    play("com.typesafe.play:play-cache_$version_org_scala_base:$version_com_typesafe_play")
    play("com.typesafe.play:play-java_$version_org_scala_base:$version_com_typesafe_play")
    play("com.typesafe.play:play-streams_$version_org_scala_base:$version_com_typesafe_play")
    play("com.typesafe.play:play-ws_$version_org_scala_base:$version_com_typesafe_play")

    play("com.typesafe.play:twirl-api_$version_org_scala_base:$version_com_typesafe_play_twirl_api")

    play("com.google.code.findbugs:jsr305:$version_com_google_code_findbugs")

    play("com.google.guava:guava:$version_com_google_guava")

    play("org.apache.commons:commons-lang3:$version_org_apache_commons_commons_lang3")

    play("javax.inject:javax.inject:$version_javax_inject")

    play("javax.validation:validation-api:$version_javax_validation")

    play("org.slf4j:slf4j-api:$version_org_slf4j")

    play("org.scala-lang:scala-library:$version_org_scala_lang")
    play("org.scala-lang.modules:scala-xml_$version_org_scala_base:$version_org_scala_lang_modules")
}