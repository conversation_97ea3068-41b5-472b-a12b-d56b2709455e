@(label: String,
        inputId: String,
        datePickerId: String,
		dateFormat: String,
		extension: String,
        datePickerAttributes: scala.collection.mutable.HashMap[String, String],
        inputAttributes: scala.collection.mutable.HashMap[String, String],
        inputNotificationAttributes: scala.collection.mutable.HashMap[String, String])

@import na.naportalbase.utils.TemplateUtils

<label for="@datePickerId" class="control-label col-sm-3">@label</label>
<div class="col-sm-9">
    <div class="input-group datetime" data-datetimepicker-ext=@extension
	data-java-date-format="@dateFormat" @TemplateUtils.dynamicElementsAttributes(datePickerAttributes)>
        <div class="input-group">
            <input class="form-control input-sm" type="text" @TemplateUtils.dynamicElementsAttributes(inputAttributes)
            @if(inputNotificationAttributes != null) {
                @TemplateUtils.dynamicElementsAttributes(inputNotificationAttributes)
            }
            />
            <span class="input-group-addon datepickerbutton"><span class="glyphicon glyphicon-calendar" ></span></span>
        </div>
    </div>
</div>
