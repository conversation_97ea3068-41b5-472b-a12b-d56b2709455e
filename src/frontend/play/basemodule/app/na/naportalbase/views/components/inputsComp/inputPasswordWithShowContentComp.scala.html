@(inputId: String, placeHolder: String, inputAttributes: scala.collection.mutable.HashMap[String, String])
@import na.naportalbase.utils.TemplateUtils
@import na.naportalbase.views.tags.i18n
<div class="input--password input-group input-group-sm">
	<input id="@inputId" type="password" @if(!inputAttributes.contains("class")){class="form-control input-sm"} placeholder="@placeHolder" @TemplateUtils.dynamicElementsAttributes(inputAttributes)/>
	<span class="input-group-btn">
		<button class="btn btn-default" type="button" data-na-portal-toggle-show-password data-target="@inputId" title="@i18n("na.button.label.passwordShow")"><i class="fuxicons fuxicons-eye"></i></button>
	</span>
</div>



