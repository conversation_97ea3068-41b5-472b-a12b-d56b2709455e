@(button1Attributes: scala.collection.mutable.HashMap[String, String], button2Attributes: scala.collection.mutable.HashMap[String, String], label1:String, label2:String)

@import na.naportalbase.utils.TemplateUtils

<a role="button" class="btn btn-primary btn-sm btn-margin pull-right"
    @TemplateUtils.dynamicElementsAttributes(button1Attributes)>
@label1
</a>
<a role="button" class="btn btn-default btn-sm pull-right" style="margin-right:10px;"
    @TemplateUtils.dynamicElementsAttributes(button2Attributes)>
@label2
</a>