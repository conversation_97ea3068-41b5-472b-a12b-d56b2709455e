@(button1Attributes: scala.collection.mutable.HashMap[String, String], button2Attributes: scala.collection.mutable.HashMap[String, String])

@import na.naportalbase.utils.TemplateUtils

<input role="button" class="btn btn-default btn-sm pull-right"
    @TemplateUtils.dynamicElementsAttributes(button2Attributes)>
</input>

@if(button1Attributes != null) {
    <input role="button" class="btn btn-primary btn-sm btn-margin pull-right" style="margin-right:10px;"
        @TemplateUtils.dynamicElementsAttributes(button1Attributes)>
    </input>
}