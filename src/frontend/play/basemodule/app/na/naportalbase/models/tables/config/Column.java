package na.naportalbase.models.tables.config;

public class Column {

    private String name;
    private String title;
    private String dataProperty;
    private String customRenderCallName;
    private boolean sortable = false;
    private boolean expandable = false;
    private boolean visible = true;
    private boolean visibilityToggle = true;
    private SortDirection initialSortDirection;
    private ColumnWidth columnWidth  = new AutoColumnWidth();

    public Column() {
    }

    public Column(String name, String title, String dataProperty, String customRenderCallName, boolean sortable, boolean expandable, boolean visible, boolean visibilityFixed, SortDirection initialSortDirection, ColumnWidth columnWidth) {
        this.name = name;
        this.title = title;
        this.dataProperty = dataProperty;
        this.customRenderCallName = customRenderCallName;
        this.sortable = sortable;
        this.expandable = expandable;
        this.visible = visible;
        this.visibilityToggle = visibilityFixed;
        this.initialSortDirection = initialSortDirection;
        this.columnWidth = columnWidth;
    }

    // getters

    public String getName() {
        return name;
    }

    public String getTitle() {
        return title;
    }

    public String getDataProperty() {
        return dataProperty;
    }

    public String getCustomRenderCallName() {
        return customRenderCallName;
    }

    public boolean isSortable() {
        return sortable;
    }

    public boolean isExpandable() {
        return expandable;
    }

    public boolean isVisible() {
        return visible;
    }

    public boolean isVisibilityToggle() {
        return visibilityToggle;
    }

    public SortDirection getInitialSortDirection() {
        return initialSortDirection;
    }

    public ColumnWidth getColumnWidth() {
        return columnWidth;
    }

    // setters

    public Column name(String name) {
        this.name = name;
        return this;
    }

    public Column title(String title) {
        this.title = title;
        return this;
    }

    public Column dataProperty(String dataProperty) {
        this.dataProperty = dataProperty;
        return this;
    }

    public Column customRenderCallName(String customRenderCallName) {
        this.customRenderCallName = customRenderCallName;
        return this;
    }

    public Column sortable(boolean sortable) {
        this.sortable = sortable;
        return this;
    }

    public Column expandable(boolean expandable) {
        this.expandable = expandable;
        return this;
    }

    public Column visible(boolean visible) {
        this.visible = visible;
        return this;
    }

    public Column initialSortDirection(SortDirection initialSortDirection) {
        this.initialSortDirection = initialSortDirection;
        return this;
    }

    public Column columnWidth(ColumnWidth columnWidth) {
        this.columnWidth = columnWidth;
        return this;
    }

    public static Column createSelectionColumn(){
        return new Column()
                .title("<input data-action='select-all-rows' type='checkbox' class='check_box checkbox_results'>")
                .name("selection-column")
                .sortable(false)
                .customRenderCallName("window.na.datatableTemplates.basemodule.checkboxRenderer");
    }
}