package na.naportalbase.models.tables.response;


import na.naportalbase.models.tables.query.TableQuery;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>, 27-11-2018.
 */
public class TableResponse {
    private final int version;
    private final int count;
    private final List<?> data;

    private TableResponse(int version, List<?> data, int count) {
        this.version = version;
        this.count = count;
        this.data = data;
    }

    public static TableResponse fromQuery(TableQuery<?> query){
        return new TableResponse(query.getVersion(), Collections.emptyList(), 0);
    }

    public static TableResponse fromQueryAndResponse(TableQuery<?> query, List<?> data, int count){
        return fromQuery(query).withDataAndCount(data, count);
    }


    public TableResponse withDataAndCount(List<?> data, int count) {
        return new TableResponse(this.version, data, count);
    }



    public int getVersion() {
        return version;
    }

    public List<?> getData() {
        return data;
    }

    public int getCount() {
        return count;
    }
}
