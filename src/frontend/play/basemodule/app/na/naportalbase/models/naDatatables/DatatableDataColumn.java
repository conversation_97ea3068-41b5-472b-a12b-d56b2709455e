package na.naportalbase.models.naDatatables;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Created by hugo-f-barrigas on 12/9/14.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class DatatableDataColumn {
    private String data;
    private String name;
    private boolean searchable;
    private boolean orderable;
    private DatatableDataSearch search;

    public DatatableDataColumn(String data, String name, boolean searchable, boolean orderable, DatatableDataSearch search) {
        this.data = data;
        this.name = name;
        this.searchable = searchable;
        this.orderable = orderable;
        this.search = search;
    }

    public DatatableDataColumn() {
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isSearchable() {
        return searchable;
    }

    public void setSearchable(boolean searchable) {
        this.searchable = searchable;
    }

    public boolean isOrderable() {
        return orderable;
    }

    public void setOrderable(boolean orderable) {
        this.orderable = orderable;
    }

    public DatatableDataSearch getSearch() {
        return search;
    }

    public void setSearch(DatatableDataSearch search) {
        this.search = search;
    }

    @Override
    public String toString() {
        return "DatatableDataColumn{" +
                "data='" + data + '\'' +
                ", name='" + name + '\'' +
                ", searchable=" + searchable +
                ", orderable=" + orderable +
                ", columnSearch=" + search.toString() +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        DatatableDataColumn that = (DatatableDataColumn) o;

        if (orderable != that.orderable) return false;
        if (searchable != that.searchable) return false;
        if (search != null ? !search.equals(that.search) : that.search != null) return false;
        if (data != null ? !data.equals(that.data) : that.data != null) return false;
        return !(name != null ? !name.equals(that.name) : that.name != null);

    }

    @Override
    public int hashCode() {
        int result = data != null ? data.hashCode() : 0;
        result = 31 * result + (name != null ? name.hashCode() : 0);
        result = 31 * result + (searchable ? 1 : 0);
        result = 31 * result + (orderable ? 1 : 0);
        result = 31 * result + (search != null ? search.hashCode() : 0);
        return result;
    }
}
