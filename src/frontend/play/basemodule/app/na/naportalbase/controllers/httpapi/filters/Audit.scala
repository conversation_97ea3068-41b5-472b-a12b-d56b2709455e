package na.naportalbase.controllers.httpapi.filters

import org.slf4j.LoggerFactory
import play.api.mvc.Result

object Audit {

  private final val LOGGER = LoggerFactory.getLogger(Audit.getClass)

  def response(response: Result, totalTimeNanoseconds: Long, throwable: Throwable) {
    if (LOGGER.isInfoEnabled) {

      val builder: StringBuilder = new StringBuilder
      builder.append("RESPONSE: sent with status code ").append(response.header.status).append(" and execution time ").append(totalTimeNanoseconds).append('\n')
      builder.append("   Headers:\n")
      response.header.headers.foreach { e => builder.append("       ").append(e._1).append(": ").append(e._2).append('\n') }

      if (throwable != null) {
        builder.append("   Exception:\n")
        builder.append(throwable.getMessage)
      }
      builder.append("\n@@@@")
      LOGGER.info(builder.toString)
    }
  }
}