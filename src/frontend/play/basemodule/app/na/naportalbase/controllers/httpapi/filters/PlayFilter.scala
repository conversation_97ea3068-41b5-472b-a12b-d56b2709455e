package na.naportalbase.controllers.httpapi.filters

import akka.util.ByteString
import org.slf4j.{Logger, LoggerFactory, MDC}
import play.api.libs.streams.Accumulator
import play.api.libs.typedmap.TypedKey
import play.api.mvc._
import play.core.j.JavaHelpers
import play.mvc.Http

import scala.concurrent.ExecutionContext.Implicits.global

class PlayFilter extends EssentialFilter {

  val LOGGER: Logger = LoggerFactory.getLogger(classOf[PlayFilter])

  def apply(nextFilter: EssentialAction): EssentialAction = new EssentialAction {

    def apply(requestHeader: RequestHeader): Accumulator[ByteString, Result] = {

      // ---------------
      // --- Request ---
      // ---------------

      val correlationId = requestHeader.headers.get(Context.CORRELATION_ID_HEADER_NAME) match {
        case Some(v) => v
        case None => null
      }

      if (Http.Context.current.get() == null) {
        Http.Context.current.set(JavaHelpers.createJavaContext(requestHeader, JavaHelpers.createContextComponents()))
      }

      try {
        Context.create(correlationId)
      } catch {
        case _: Throwable =>
          LOGGER.warn("Context already defined => clear and create")
          Context.clear()
          Context.create(correlationId)
      }

      MDC.put("requestId", Context.requestId)
      MDC.put("correlationId", Context.correlationId)
      MDC.put("startTime", java.lang.Long.toString(Context.startTime))

      requestHeader.addAttr(TypedKey.apply[String]("requestId"), Context.requestId)
      requestHeader.addAttr(TypedKey.apply[String]("correlationId"), Context.correlationId())
      requestHeader.addAttr(TypedKey.apply[String]("startTime"), java.lang.Long.toString(Context.startTime))

      LOGGER.info(requestHeader.method + " " + requestHeader.path)

      // ----------------
      // --- Response ---
      // ----------------

      nextFilter(requestHeader).map {
        result => {
          LOGGER.info("HTTP CODE: " + result.header.status)
          Audit.response(result, Context.getTotalElapsedTime, Context.exception)
          Context.clear()
          MDC.clear()
          result
        }
      }
    }
  }
}