package na.naportalbase.utils;

import na.naportalbase.models.security.ResourcePermission;
import na.naportalbase.models.security.annotations.AuthPermission;
import play.mvc.Http;
import play.mvc.Result;
import play.mvc.Results;
import pt.alticelabs.nossis.portal.views.html.wireframe.error;
import pt.alticelabs.nossis.security.Security;
import pt.ptinovacao.nossis.security.aaapi.exceptions.AuthenticationException;
import pt.ptinovacao.nossis.security.aaapi.exceptions.SecurityAAAPIException;
import pt.ptinovacao.nossis.security.aaapi.model.Domain;
import pt.ptinovacao.nossis.security.aaapi.model.Permissions;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

/**
 * Created by omar on 12-10-2015.
 */
public final class SecurityUtils {

    private SecurityUtils() {
    }

    private static String sessionId() {
        return Security.getSessionId(Http.Context.current());
    }

    public static boolean isAuthorized(ResourcePermission... resourcePermissions) {
        return isAuthorized(resourcePermissions, Http.Context.current());
    }

    public static boolean isAuthorized(ResourcePermission[] resourcePermissions, Http.Context context) {
        return isAuthorized(permissionsStr(resourcePermissions), context);
    }

    public static boolean isAuthorized(AuthPermission[] permissions, Http.Context context) {
        return isAuthorized(authPermissionToString(permissions), context);
    }

    public static boolean isAuthorizedOr(ResourcePermission... resourcePermissions) {
        return isAuthorizedOr(resourcePermissions, Http.Context.current());
    }

    public static boolean isAuthorizedOr(ResourcePermission[] resourcePermissions, Http.Context context) {
        return isAuthorizedOr(permissionsStr(resourcePermissions), context);
    }

    public static boolean isAuthorizedOr(AuthPermission[] permissions, Http.Context context) {
        return isAuthorizedOr(authPermissionToString(permissions), context);
    }

    public static boolean isAuthenticated() {
        return Security.isAuthenticated(Http.Context.current());
    }

    public static List<String> authorizedDomainsByResourceAndFamily(ResourcePermission resource, String familyName) {
        List<String> domainNamesAuthorized = new ArrayList<>();
        try {
            Map<String, Domain> domainsMap = Security.getDomainsByResourcePermissionsAndDomainFamily(sessionId(), resource, familyName);
            domainNamesAuthorized.addAll(domainsMap.keySet());
        } catch (AuthenticationException | SecurityAAAPIException e) {
            e.printStackTrace();
        }
        return domainNamesAuthorized;
    }

    public static String getUsername() {
        return Security.getUser(Http.Context.current()).getDisplayName();
    }

    public static CompletionStage<Result> unauthorizedPromise() {
        return CompletableFuture.supplyAsync(SecurityUtils::unauthorizedResult);
    }

    public static Result unauthorizedResult() {
        return Results.forbidden(error.render(Http.Status.UNAUTHORIZED, System.currentTimeMillis()));
    }

    public static boolean isAuthorized(String[] permissions, Http.Context context) {
        return isAuthorized(Arrays.asList(permissions), context);
    }

    /*
      This code verifies each permission individually instead of
    calling Security.isAuthorized() directly due to issue
    https://jira.ptin.corppt.com/browse/NAPORTAL-13225
     */
    public static boolean isAuthorized(List<String> permissions, Http.Context context) {
        for (String permission : permissions){
            String[] permissionArray = {permission};
            if(!Security.isAuthorized(permissionArray, context, false)){
                return false;
            }
        }
        return true;
    }

    public static boolean isAuthorizedOr(String[] permissions, Http.Context context) {
        return isAuthorizedOr(Arrays.asList(permissions), context);
    }

    /*
      This code verifies each permission individually instead of
    calling Security.isAuthorized() directly due to issue
    https://jira.ptin.corppt.com/browse/NAPORTAL-13225
     */
    public static boolean isAuthorizedOr(List<String> permissions, Http.Context context) {
        for (String permission : permissions){
            String[] permissionArray = {permission};
            if(Security.isAuthorized(permissionArray, context, false)){
                return true;
            }
        }
        return false;
    }

    private static String[] permissionsStr(ResourcePermission... resourcePermissions) {
        String[] authStrings = new String[resourcePermissions.length];
        for (int i = 0; i < resourcePermissions.length; i++) {
            authStrings[i] = resourcePermissions[i].toString();
        }
        return authStrings;
    }

    private static String[] authPermissionToString(AuthPermission[] authPermissions) {
        String[] result = new String[authPermissions.length];
        for (int i = 0; i < authPermissions.length; ++i) {
            AuthPermission authPermission = authPermissions[i];
            ResourcePermission resourcePermission = new ResourcePermission(authPermission.resource());
            for (Permissions permissions : authPermission.access()) {
                switch (permissions) {
                    case Create:
                        resourcePermission.create();
                        break;
                    case Read:
                        resourcePermission.read();
                        break;
                    case Update:
                        resourcePermission.update();
                        break;
                    case Delete:
                        resourcePermission.delete();
                        break;
                    case Execute:
                        resourcePermission.execute();
                        break;
                }
            }
            result[i] = resourcePermission.toString();
        }
        return result;
    }
}