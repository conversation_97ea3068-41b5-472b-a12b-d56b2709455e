package na.naportalbase.utils;

import java.util.regex.Pattern;

public class RegexUtils {

    private static final Pattern SPECIAL_REGEX_CHARS = Pattern.compile("[{}()\\[\\].+*?^$\\\\|]");

    /**
     * Escape characters with special meanings in the regex pattern.
     * It can be used to search for literal string, similar to Java & Perl's \Q ... \E.
     * It is different from {@link Pattern#quote} that simply wraps {@code pattern} with \Q and \E.
     * Not all regex engines support \Q ... \E, so this is a more reliable way to apply literal string match with
     * regex to be used by other platforms.
     *
     * @param pattern - target pattern
     * @return escaped regex pattern
     */
    public static String quotemeta(String pattern) {
        return SPECIAL_REGEX_CHARS.matcher(pattern).replaceAll("\\\\$0");
    }
}