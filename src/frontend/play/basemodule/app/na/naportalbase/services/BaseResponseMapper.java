package na.naportalbase.services;

import com.fasterxml.jackson.databind.JsonNode;
import na.naportalbase.exceptions.ExceptionCodes;
import na.naportalbase.exceptions.FrontendException;
import na.naportalbase.models.services.GenericResponse;
import na.naportalbase.settings.BaseMappings;
import na.naportalbase.settings.ExpectedHttpResponseCodes;
import play.libs.Json;
import play.libs.concurrent.HttpExecutionContext;
import play.libs.ws.WSResponse;
import play.mvc.Http;
import pt.ptinovacao.nossis.exceptions.BusinessException;
import pt.ptinovacao.nossis.exceptions.FaultException;

import java.io.IOException;
import java.util.concurrent.CompletionStage;

import static na.naportalbase.settings.BaseMappings.defaultObjectMapper;

public final class BaseResponseMapper implements ResponseMapperIf {

    private final ErrorHandler errorHandler;
    private final boolean enableLogs;
    private final HttpExecutionContext executionContext;

    public BaseResponseMapper(boolean enableLogs, HttpExecutionContext executionContext) {
        this.errorHandler = new DefaultErrorHandler();
        this.enableLogs = enableLogs;
        this.executionContext = executionContext;
    }

    public BaseResponseMapper(final ErrorHandler errorHandler, HttpExecutionContext executionContext) {
        this.errorHandler = errorHandler;
        this.enableLogs = true;
        this.executionContext = executionContext;
    }

    public BaseResponseMapper(HttpExecutionContext executionContext) {
        this.errorHandler = new DefaultErrorHandler();
        this.enableLogs = true;
        this.executionContext = executionContext;
    }

    @Override
    public <T> CompletionStage<T> mapResponse(final Class<T> generic,
                                              final String requestType,
                                              final CompletionStage<WSResponse> mResponse,
                                              final String logId) {
        return mResponse.handleAsync((response, throwable) -> {

            if (throwable != null && throwable.getCause() instanceof java.net.ConnectException) {
                throw new FaultException(ExceptionCodes.REST_API_UNAVAILABLE_EXCEPTION, throwable.getCause());
            }

            final GenericResponse<WSResponse> genericResponse = new GenericResponse<>(response);

            if (enableLogs) {
                ServiceInvokeLogger.logServiceResponse(logId, response.getHeaders(), response.getStatus(), response.getBody(), requestType);
            }

            if (ExpectedHttpResponseCodes.isExpectedSuccessCode(requestType, genericResponse.getStatus())) {
                return parseResponse(generic, response, genericResponse);
            } else {
                errorHandler.handleBadResponse(requestType, genericResponse);
            }
            return null;
        }, executionContext.current());
    }

    @SuppressWarnings("unchecked")
    private <T> T parseResponse(Class<T> generic, WSResponse response, GenericResponse<WSResponse> genericResponse) {
        if (hasEmptyBody(genericResponse)) {
            return instantiateEmptyObject(generic);
        }
        // parse the response into the Class given as argument
        if (ServiceResponseHandler.isJsonContentType(genericResponse)) {
            return (T) parseJson(generic, response);
        }
        if (ServiceResponseHandler.isXmlContentType(genericResponse)) {
            return (T) parseXML(generic, response);
        }
        return instantiateEmptyObject(generic);
    }

    private <T> Object parseJson(final Class<T> generic, final WSResponse response) {
        if (generic == JsonNode.class && response.getBody().isEmpty()) {
            return Json.newObject();
        }
        if (generic == JsonNode.class) {
            return response.asJson();
        }
        try {
            return BaseMappings.dualMapper.readValue(response.getBody(), generic);
        } catch (IOException e) {
            // critical error
            throw new FrontendException(ExceptionCodes.CONFIGURATION_EXCEPTION, e);
        }
    }

    private <T> Object parseXML(final Class<T> generic, final WSResponse response) {
        if (generic == String.class) {
            if (response.getBody().isEmpty()) {
                return "";
            }
            return response.getBody();
        }
        return response.asXml();
    }

    @SuppressWarnings("unchecked")
    private <T> T instantiateEmptyObject(final Class<T> generic) {
        try {
            if (generic == Boolean.class) {
                return (T) Boolean.TRUE;
            }
            if (generic == JsonNode.class) {
                return (T) defaultObjectMapper.createObjectNode();
            }
            return generic.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new BusinessException(ExceptionCodes.UNKNOWN_EXCEPTION, new Throwable("Error while instantiating response object from rest service - " + e.getMessage()));
        }
    }

    private static boolean hasEmptyBody(GenericResponse<WSResponse> genericResponse) {
        return genericResponse.getStatus() == Http.Status.NO_CONTENT
                || "0".equals(genericResponse.getHeader(Http.HeaderNames.CONTENT_LENGTH))
                || genericResponse.getBody().isEmpty();
        }
}