package na.naportalbase.services.configuration;

import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;
import com.typesafe.config.ConfigObject;
import na.naportalbase.exceptions.ExceptionCodes;
import na.naportalbase.exceptions.FrontendException;
import na.naportalbase.settings.BaseConstants;
import play.Environment;
import play.libs.Json;

import javax.inject.Inject;
import java.io.File;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.ReentrantReadWriteLock;

public final class Configuration {

    private static final ReentrantReadWriteLock READ_WRITE_LOCK = new ReentrantReadWriteLock();
    private static final Map<String, Config> CONFIG_CACHE = new HashMap<>();

    private final Environment environment;
    private final play.Logger.ALogger LOGGER;

    @Inject
    public Configuration(Environment environment) {
        this.environment = environment;
        this.LOGGER = play.Logger.of(BaseConstants.LOGGER_NAME);
    }

    public Configuration(Environment environment, play.Logger.ALogger LOGGER) {
        this.environment = environment;
        this.LOGGER = LOGGER;
    }

    /**
     * returns a Config object for the resource given as argument
     *
     * @param resource - resource to be parsed
     * @return config of the resource given, null if resource does not exist.
     */
    public Config load(final String resource) {

        READ_WRITE_LOCK.readLock().lock();
        Config res = CONFIG_CACHE.get(resource);
        READ_WRITE_LOCK.readLock().unlock();
        if (res == null) {

            LOGGER.info("loading configuration: " + resource);

            boolean existentConfiguration = environment.getFile(resource).exists();
            final File resourceFile = environment.getFile(resource);
            LOGGER.info("checking on path " + resourceFile.getAbsolutePath());
            if (resourceFile.length() > 0) {
                LOGGER.info("configuration found on " + resourceFile.getAbsolutePath());
                res = ConfigFactory.parseFile(environment.getFile(resource));
            }

            if (!existentConfiguration) {
                final URL resourceURL = environment.resource(resource);
                LOGGER.info("checking on resource " + resource);
                if (resourceURL != null) {
                    LOGGER.info("configuration found on " + resourceURL);
                    res = ConfigFactory.parseURL(resourceURL);
                }
            }
            if (res == null) {
                LOGGER.error("error loading configuration \"" + resource + "\" file not found");
                // critical error - on application start
                throw new FrontendException(ExceptionCodes.CONFIGURATION_EXCEPTION, new Throwable("no configuration found for " + resource));
            }
            READ_WRITE_LOCK.writeLock().lock();
            CONFIG_CACHE.put(resource, res);
            READ_WRITE_LOCK.writeLock().unlock();
        }
        return res;
    }

    /**
     * converts HOCON object to Java object
     */
    public <T> T unwrapped(final ConfigObject configObject, final Class<T> generic) {
        LOGGER.debug("unwrapped");
        return Json.fromJson(Json.toJson(configObject.unwrapped()), generic);
    }

    public boolean exists(Config config, String path) {
        return config.hasPath(path);
    }

    public Object get(Config config, String path) {
        return config.getAnyRef(path);
    }

    public String getString(Config config, String path) {
        return config.getString(path);
    }

    public String getString(Config config, String path, String defaultValue) {
        if (config.hasPath(path)) {
            return config.getString(path);
        }
        return defaultValue;
    }

    public List<String> getStringList(Config config, String path) {
        return config.getStringList(path);
    }

    public List<Long> getLongList(Config config, String path) {
        return config.getLongList(path);
    }
}