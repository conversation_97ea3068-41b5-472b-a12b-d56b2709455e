package na.naportalbase.services.ws;


import na.naportalbase.services.ResponseMapperIf;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>, 06-02-2017.
 */
public class Request<ResponseT> {

    private String url;
    private final Map<String, String> headers;
    private final Map<String, String> queryParameters;
    private final Class<ResponseT> responseType;
    private final ResponseMapperIf responseMapper;
    private final Object payload;

    /* package protected to be initialized by the builder */
    Request(String url, Map<String, String> headers, Map<String, String> queryParameters, Class<ResponseT> responseType, ResponseMapperIf responseMapper, Object payload) {
        this.url = url;
        this.headers = headers;
        this.queryParameters = queryParameters;
        this.responseType = responseType;
        this.responseMapper = responseMapper;
        this.payload = payload;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Map<String, String> getHeaders() {
        return new HashMap<>(headers);
    }

    public Map<String, String> getQueryParameters() {
        return new HashMap<>(queryParameters);
    }

    public Class<ResponseT> getResponseType() {
        return responseType;
    }

    public ResponseMapperIf getResponseMapper() {
        return responseMapper;
    }

    public Object getPayload() {
        return payload;
    }
}