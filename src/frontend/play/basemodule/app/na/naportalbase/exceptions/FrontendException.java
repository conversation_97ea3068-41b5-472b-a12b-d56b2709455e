package na.naportalbase.exceptions;

import pt.ptinovacao.nossis.exceptions.AbstractException;

public class FrontendException extends AbstractException {

    private static final long serialVersionUID = 1L;

    public FrontendException(final ExceptionCodes code, final Throwable cause) {
        super(code, cause);
    }

    public FrontendException(final ExceptionCodes code, final Throwable cause, String... paramsNameBeforeValue) {
        super(code, cause, paramsNameBeforeValue);
    }
}