<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>pt.ptinovacao.na-portal</groupId>
        <artifactId>na-portal-frontend</artifactId>
        <version>10.55.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>na-portal-frontend-play</artifactId>
    <packaging>pom</packaging>

    <properties>
        <skipGradleWrapper>false</skipGradleWrapper>
        <skipNpmTests>${skipTests}</skipNpmTests>
    </properties>
    <modules>
        <module>basemodule</module>
        <module>catalog-basemodule</module>
        <module>router</module>
        <module>reference-data</module>
        <module>operations-catalog</module>
        <module>portal</module>
        <module>monitoring</module>
        <module>mpt</module>
        <module>go</module>
        <module>nadm</module>
        <module>diagnostics</module>
    </modules>

    <build>
        <sourceDirectory>app</sourceDirectory>
        <outputDirectory>target/playBinary/classes</outputDirectory>
        <resources>
            <resource>
                <directory>conf</directory>
            </resource>
        </resources>
        <testSourceDirectory>test</testSourceDirectory>
        <testResources>
            <testResource>
                <directory>test-conf</directory>
            </testResource>
        </testResources>

        <plugins>
            <!-- generated sources -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>${version.org.codehaus.mojo.build-helper-maven-plugin}</version>
                <executions>
                    <execution>
                        <id>add-source</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>target/src/play/binary/routesScalaSources</source>
                                <source>target/src/play/binary/twirlTemplatesScalaSources</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- scala-maven-plugin is only declared so that IDE may identify the Scala SDK in use -->
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
                <version>${version.net.alchim31.maven.scala-maven-plugin}</version>
                <configuration>
                    <scalaVersion>${version.org.scala-lang}</scalaVersion>
                </configuration>
                <executions>
                    <execution>
                        <id>compile</id>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <phase>none</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- project cleanup is handled by gradle -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <version>${version.maven-clean-plugin}</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <!-- artifact deployment is handled by gradle -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>${version.maven-deploy-plugin}</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <!-- artifact install is handled by gradle -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <version>${version.maven-install-plugin}</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <!-- project compilation is handled by gradle -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${version.maven-compiler-plugin}</version>
                <configuration>
                    <skipMain>true</skipMain>
                    <skip>true</skip>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>${version.org.codehaus.mojo.exec-maven-plugin}</version>
                <executions>
                    <!-- Gradle executions -->
                    <execution>
                        <id>gradle-clean</id>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <phase>clean</phase>
                        <configuration>
                            <skip>${skipGradleWrapper}</skip>
                            <executable>./gradlew</executable>
                            <arguments>
                                <argument>clean</argument>
                                <argument>--info</argument>
                            </arguments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>gradle-build</id>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <phase>compile</phase>
                        <configuration>
                            <skip>${skipGradleWrapper}</skip>
                            <executable>./gradlew</executable>
                            <arguments>
                                <argument>build</argument>
                                <argument>--info</argument>
                            </arguments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>gradle-install</id>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <phase>install</phase>
                        <configuration>
                            <skip>${skipGradleWrapper}</skip>
                            <executable>./gradlew</executable>
                            <arguments>
                                <argument>publishToMavenLocal</argument>
                                <argument>--info</argument>
                            </arguments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>gradle-deploy</id>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <phase>deploy</phase>
                        <configuration>
                            <skip>${skipGradleWrapper}</skip>
                            <executable>./gradlew</executable>
                            <arguments>
                                <argument>publishAllPublicationsToMavenRepository</argument>
                                <argument>--info</argument>
                            </arguments>
                        </configuration>
                    </execution>
                    <!-- NPM executions -->
                    <execution>
                        <id>npm-clean</id>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <phase>clean</phase>
                        <configuration>
                            <skip>${skipGradleWrapper}</skip>
                            <executable>npm</executable>
                            <workingDirectory>${basedir}/../web</workingDirectory>
                            <arguments>
                                <argument>run</argument>
                                <argument>clean</argument>
                            </arguments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>npm-ci</id>
                        <phase>test</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <skip>${skipNpmTests}</skip>
                            <executable>npm</executable>
                            <workingDirectory>${basedir}/../web</workingDirectory>
                            <arguments>
                                <argument>ci</argument>
                                <argument>--ignore-scripts</argument>
                            </arguments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>npm-test</id>
                        <phase>test</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <skip>${skipNpmTests}</skip>
                            <executable>npm</executable>
                            <workingDirectory>${basedir}/../web</workingDirectory>
                            <arguments>
                                <argument>test</argument>
                            </arguments>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>