import java.util.regex.Pattern

boolean isDev = gradle.startParameter.getTaskNames().contains("run")
String webpackBuildDir = "${project.projectDir}/../web/build/webpack"


buildscript {
    repositories {
        mavenLocal()
        maven {
            name "ptin-maven-proxy-repo"
            url "http://maven.ptin.corppt.com/repo"
        }
        ivy {
            name "ptin-maven-proxy-repo-ivy"
            url "http://maven.ptin.corppt.com/ext-repo-ivy"
            layout "ivy"
        }
    }
    dependencies {
        classpath "pt.ptinovacao.ssov:gradle-plugins-pom:4.2.0"
    }
}

apply plugin: 'ptin.pom'
apply plugin: 'java'
apply plugin: 'idea'

ptinPom {
    settings = '../../../build/settings.xml'
}

task refreshNPMPackages(type: Exec) {
    enabled = !isDev
    // the inputs and outputs are here to enable build cache, in other words, avoid running every time
    workingDir '../web'
    commandLine "npm", "install", "--loglevel=verbose"
}

task configureIAMDev(type: Exec) {
    // the next 2 lines makes this task run once per calendar day
    inputs.property("configureIAMDev:todaysDate", new Date().clearTime() )
    outputs.upToDateWhen { true }

    enabled = isDev
    workingDir '.'
    commandLine "./configure_dev_iam.sh"
}

class WebpackTask extends DefaultTask {
    @InputDirectory
    File inputDir = project.file('../web/src')

    @OutputDirectory
    File outputDir = project.file('../web/build/webpack')

    def configFileNames = ["webpack-gradle-modules.config.js", "npm-shrinkwrap.json", "package.json"];

    Pattern inputFilePathsPattern = Pattern.compile("../web/src/");

    @TaskAction
    void execute(IncrementalTaskInputs inputs) {
        println inputs.incremental
                ? 'CHANGED inputs considered out of date'
                : 'ALL inputs considered out of date'
        if (!inputs.incremental)
            project.delete(outputDir.listFiles())

        boolean willRunBundle = false
        inputs.outOfDate { change ->
            if (change.file.directory) {
                return
            }
            if (!configFileNames.contains(change.file.name)) {
                if (!change.file.name.endsWith(".css") && !change.file.name.endsWith(".js")) {
                    return
                }
                if (!inputFilePathsPattern.matcher(change.file.path).find()) {
                    return
                }
            }
            println "out of date: ${change.file.path}"
            willRunBundle = true
        }

        inputs.removed { change ->
            if (change.file.directory) {
                return
            }
            if (!configFileNames.contains(change.file.name)) {
                if (!change.file.name.endsWith(".css") && !change.file.name.endsWith(".js")) {
                    return
                }
                if (!inputFilePathsPattern.matcher(change.file.path).find()) {
                    return
                }
            }

            println "removed: ${change.file.path}"
            willRunBundle = true
        }

        if (willRunBundle || !outputDir.exists()) {
            project.exec {
                workingDir '../web'
                commandLine "npm", "run", "gradle"
            }
        }
    }
}

task webpack(type: WebpackTask) {
    enabled = !isDev
    dependsOn refreshNPMPackages
}


subprojects {


    apply plugin: 'maven-publish'
    apply plugin: 'play'

    group = "$project_groupId"
    version = "$project_version"

    project.buildDir = 'target'
    publish.dependsOn build
    publishToMavenLocal.dependsOn build

    sourceSets {
        main {
            java {
                srcDirs = ["app"]
            }
        }
    }

    idea {
        module {
            sourceDirs += file("app")
            sourceDirs += file("target/src")
            testSourceDirs += file("test")
            scopes.COMPILE = [plus: [configurations.play], minus: []]
            scopes.RUNTIME = [plus: [configurations.playRun], minus: []]
            scopes.TEST = [plus: [configurations.playTest], minus: [configurations.playRun]]
        }
    }


    tasks.withType(PlayRun) {
        enabled = false
    }

    tasks.withType(JavaScriptMinify) { task -> task.enabled = false }

    repositories {
        mavenLocal()
        maven {
            name "ptin-maven-proxy-repo"
            url "http://maven.ptin.corppt.com/repo"
        }
        ivy {
            name "ptin-maven-proxy-repo-ivy"
            url "http://maven.ptin.corppt.com/ext-repo-ivy"
            layout "ivy"
        }
    }

    model {
        components {
            play {
                binaries.all { binary ->

                    tasks.withType(TwirlCompile) { task ->
                        task.source = "${buildDir}/TwirlSource"
                        task.defaultImports = TwirlImports.JAVA
                        task.includes = ["**/*.scala.html"]
                        task.dependsOn("CopyHtmlToAssets", "CopyUpdatedHtmlToAssets")
                    }

                    tasks.create("addJs${binary.name.capitalize()}Assets", Copy) { addJsToAssetsTask ->
                        addJsToAssetsTask.dependsOn webpack, configureIAMDev

                        from("${webpackBuildDir}/gradle-modules/${project.projectDir.getName()}") {
                            include "*.js"
                            include "*.js.gz"
                        }
                        from("${webpackBuildDir}/gradle-nossis-ui/${project.projectDir.getName()}") {
                            include "*.js"
                            include "*.js.gz"
                        }
                        into project.file("${buildDir}/${binary.name}/addJsToAssets/${project.projectDir.getName()}")

                        // Hook this task into the binary
                        binary.assets.addAssetDir project.file("${buildDir}/${binary.name}/addJsToAssets")
                        binary.assets.builtBy addJsToAssetsTask
                    }

                    tasks.create("addCss${binary.name.capitalize()}Assets", Copy) { addCssToAssetsTask ->
                        addCssToAssetsTask.dependsOn webpack

                        from("${webpackBuildDir}/gradle-modules/${project.projectDir.getName()}") {
                            include "*.css"
                        }
                        from("${webpackBuildDir}/gradle-nossis-ui/${project.projectDir.getName()}") {
                            include "*.css"
                        }
                        into project.file("${buildDir}/${binary.name}/addCssToAssets/${project.projectDir.getName()}")

                        // Hook this task into the binary
                        binary.assets.addAssetDir project.file("${buildDir}/${binary.name}/addCssToAssets")
                        binary.assets.builtBy addCssToAssetsTask
                    }

                    tasks.create("CopyHtmlToAssets", Copy) { CopyHtmlToAssetsTask ->

                        from("${project.projectDir}/app") {
                            include "**/*.html"
                        }
                        into project.file("${buildDir}/TwirlSource")
                    }

                    tasks.create("CopyUpdatedHtmlToAssets", Copy) { CopyUpdatedHtmlToAssetsTask ->
                        CopyUpdatedHtmlToAssetsTask.dependsOn webpack, "CopyHtmlToAssets"

                        from("${webpackBuildDir}/gradle-modules/${project.projectDir.getName()}/app") {
                            include "**/*.html"
                        }
                        from("${webpackBuildDir}/gradle-nossis-ui/${project.projectDir.getName()}/app") {
                            include "**/*.html"
                        }
                        into project.file("${buildDir}/TwirlSource")
                    }
                }

                platform play: version_com_typesafe_play, scala: version_org_scala_base, java: version_java
                injectedRoutesGenerator = true

                sources {
                    twirlTemplates {
                        defaultImports = TwirlImports.JAVA
                    }
                    resources {
                        source {
                            srcDir "conf"
                            if (isDev) {
                                include '**/*'
                            } else {
                                include '**/messages*'
                                include '**/exceptions*'
                            }
                        }
                    }
                }
            }
        }
    }

    publishing {
        publications {
            maven(MavenPublication) {
                artifactId "$project.name"

                pom.withXml {
                    def root = asNode()

                    // Add parent configuration
                    def parent = root.appendNode('parent')
                    parent.appendNode('groupId', 'pt.ptinovacao.na-portal')
                    parent.appendNode('artifactId', 'na-portal-frontend')
                    parent.appendNode('version', "$project_version")

                    root.appendNode('packaging', 'jar')

                    def mvnDependencies = root.appendNode('dependencies')
                    project.configurations.play.dependencies.each { d ->
                        appendDependency(mvnDependencies, d, false)
                    }
                    project.configurations.playRun.dependencies.each { d ->
                        appendDependency(mvnDependencies, d, true)
                    }
                }

                artifact source: "$buildDir/playBinary/lib/$project.name" + ".jar"
            }
            assets(MavenPublication) {
                artifactId "$project.name-assets"

                pom.withXml {
                    def root = asNode()

                    // Add parent configuration
                    def parent = root.appendNode('parent')
                    parent.appendNode('groupId', 'pt.ptinovacao.na-portal')
                    parent.appendNode('artifactId', 'na-portal-frontend')
                    parent.appendNode('version', "$project_version")
                }

                artifact source: "$buildDir/playBinary/lib/$project.name-assets" + ".jar"
            }
        }

        repositories {
            mavenLocal()
            maven {
                credentials {
                    username "ci-na-commons"
                    password "c1-na-commons"
                }

                url "https://maven.ptin.corppt.com/na-portal-${project_version.endsWith('-SNAPSHOT') ? 'snapshots' : 'releases'}"
            }
        }
    }

    configurations.all {
        //dependencyManagement - enforce the following dependencies version
        resolutionStrategy {
            force "com.fasterxml.jackson.core:jackson-core:$version_com_fasterxml_jackson"
            force "com.fasterxml.jackson.core:jackson-annotations:$version_com_fasterxml_jackson"
            force "com.fasterxml.jackson.core:jackson-databind:$version_com_fasterxml_jackson"
            force "com.typesafe.akka:akka-slf4j_$version_org_scala_base:$version_com_typesafe_akka"
            force "com.typesafe.akka:akka-actor_$version_org_scala_base:$version_com_typesafe_akka"
            force "com.typesafe.akka:akka-stream_$version_org_scala_base:$version_com_typesafe_akka"
        }

        exclude group: "net.sf.ehcache", module: "ehcache-core"
        exclude group: "com.typesafe.play", module: "play-logback_$version_org_scala_base"
        exclude group: "ch.qos.logback", module: "*"
    }
}

project(":na-portal-frontend-basemodule"){
    model {
        components {
            play {
                binaries.all { binary ->

                    tasks.create("add${binary.name.capitalize()}VendorAssets", Copy) { addVendorAssetsTask ->
                        addVendorAssetsTask.dependsOn webpack

                        from("${webpackBuildDir}/gradle-modules/na-portal-assets-vendors")
                        into project.file("${buildDir}/${binary.name}/addVendorAssetsTask/na-portal-assets-vendors")

                        // Hook this task into the binary
                        binary.assets.addAssetDir project.file("${buildDir}/${binary.name}/addVendorAssetsTask")
                        binary.assets.builtBy addVendorAssetsTask
                    }

                    tasks.create("addNaPortalAssets", Copy) { addVendorAssetsTask ->
                        addVendorAssetsTask.dependsOn webpack

                        from("${webpackBuildDir}/gradle-modules/na-portal-assets")
                        into project.file("${buildDir}/${binary.name}/addNaPortalAssets/na-portal-assets")

                        // Hook this task into the binary
                        binary.assets.addAssetDir project.file("${buildDir}/${binary.name}/addNaPortalAssets")
                        binary.assets.builtBy addVendorAssetsTask
                    }

                    tasks.create("addJsWebComponentsAssets", Copy) { addJsToAssetsTask ->
                        addJsToAssetsTask.dependsOn webpack

                        from("${webpackBuildDir}/gradle-modules/web-components")
                        into project.file("${buildDir}/${binary.name}/addWebComponentsAssets/web-components")

                        // Hook this task into the binary
                        binary.assets.addAssetDir project.file("${buildDir}/${binary.name}/addWebComponentsAssets")
                        binary.assets.builtBy addJsToAssetsTask
                    }

                    tasks.create("addJsUtilsAssets", Copy) { addJsToAssetsTask ->
                        addJsToAssetsTask.dependsOn webpack

                        from("${webpackBuildDir}/gradle-modules/utils")
                        into project.file("${buildDir}/${binary.name}/addJsUtilsAssets/utils")

                        // Hook this task into the binary
                        binary.assets.addAssetDir project.file("${buildDir}/${binary.name}/addJsUtilsAssets")
                        binary.assets.builtBy addJsToAssetsTask
                    }
                }
            }
        }
    }
}

project(":na-portal-frontend-portal") {
    model {
        components {
            play {
                binaries.all { binary ->

                    tasks.create("add${binary.name.capitalize()}VendorAssets", Copy) { addVendorAssetsTask ->
                        addVendorAssetsTask.dependsOn webpack

                        from("${webpackBuildDir}/gradle-modules/na-portal-assets-vendors")
                        into project.file("${buildDir}/${binary.name}/addVendorAssetsTask/na-portal-assets-vendors")

                        // Hook this task into the binary
                        binary.assets.addAssetDir project.file("${buildDir}/${binary.name}/addVendorAssetsTask")
                        binary.assets.builtBy addVendorAssetsTask
                    }

                    tasks.create("CopyUpdatedHtmlToAssets", Copy) { CopyUpdatedHtmlToAssetsTask ->
                        CopyUpdatedHtmlToAssetsTask.enabled = !isDev
                        CopyUpdatedHtmlToAssetsTask.dependsOn webpack, "CopyHtmlToAssets"

                        from("${webpackBuildDir}/gradle-modules/${project.projectDir.getName()}/app") {
                            include "**/*.html"
                        }
                        from("${webpackBuildDir}/gradle-nossis-ui/${project.projectDir.getName()}/app") {
                            include "**/*.html"
                        }
                        into project.file("${buildDir}/TwirlSource")
                    }

                }
            }
        }
    }
    tasks.withType(PlayRun) {
        enabled = true
        httpPort = 9041
        forkOptions.jvmArgs = [
                '-Xdebug',
                '-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=9141',
                '-Dnossisaaapi.config.file=conf-dev-security/nossisaaapi.conf',
                '-Dnossisaaapi.mappings.path=conf',
                '-Dconfig.file=conf-dev/application.conf',
                '-Dconfig.file=conf-dev/application.conf',
                '-Dna.portal.play.conf=conf/frontend/data/portal/na/modules/naportalbase/global.conf',
                '-Dna.portal.base.conf.dir=conf/frontend/data/',
                '-Dlogger.file=../log4j2-dev.xml',
                '-Dlogger.base.path=/tmp',
                '-Dlogger.module.path=/portal',
                '-Djavax.net.ssl.trustStore=conf-dev-security/na-iam.jks',
                '-Djavax.net.ssl.trustStorePassword=123456',
                '-Duser.timezone=UTC',
                '-Dportal.product.flavor=fulfillment' // This variable allows us to select the translations flavor between "device-management" and "fulfillment".
        ]
        runtimeClasspath += files('conf-dev')
        runtimeClasspath += files('../conf-dev')
        runtimeClasspath += files('../basemodule/target/playBinary/classes')
        runtimeClasspath += files('conf-dev-security')
    }
}

project(":na-portal-frontend-operations-catalog") {
    tasks.withType(PlayRun) {
        enabled = true
        httpPort = 9042
        forkOptions.jvmArgs = [
                '-Xdebug',
                '-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=9142',
                '-Dnossisaaapi.config.file=../portal/conf-dev-security/modules-nossisaaapi.conf',
                '-Dnossisaaapi.mappings.path=conf',
                '-Dconfig.file=conf-dev/application.conf',
                '-Dlogger.file=../log4j2-dev.xml',
                '-Dlogger.base.path=/tmp',
                '-Dlogger.module.path=/operations-catalog',
                '-Djavax.net.ssl.trustStore=../portal/conf-dev-security/na-iam.jks',
                '-Djavax.net.ssl.trustStorePassword=123456',
                '-Duser.timezone=UTC'
        ]
        runtimeClasspath += files('../basemodule/conf')
        runtimeClasspath += files('../conf-dev')
        runtimeClasspath += files('../basemodule/target/playBinary/classes')
        runtimeClasspath += files('conf-dev')
        runtimeClasspath += files('../portal/conf-dev-security')
    }
}

project(":na-portal-frontend-reference-data") {
    tasks.withType(PlayRun) {
        enabled = true
        httpPort = 9043
        forkOptions.jvmArgs = [
                '-Xdebug',
                '-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=9143',
                '-Dnossisaaapi.config.file=../portal/conf-dev-security/modules-nossisaaapi.conf',
                '-Dnossisaaapi.mappings.path=conf',
                '-Dconfig.file=conf-dev/application.conf',
                '-Dlogger.file=../log4j2-dev.xml',
                '-Dlogger.base.path=/tmp',
                '-Dlogger.module.path=/catalog',
                '-Djavax.net.ssl.trustStore=../portal/conf-dev-security/na-iam.jks',
                '-Djavax.net.ssl.trustStorePassword=123456',
                '-Duser.timezone=UTC'
        ]
        runtimeClasspath += files('../basemodule/conf')
        runtimeClasspath += files('../conf-dev')
        runtimeClasspath += files('../basemodule/target/playBinary/classes')
        runtimeClasspath += files('conf-dev')
        runtimeClasspath += files('../portal/conf-dev-security')
    }
}

project(":na-portal-frontend-monitoring") {
    tasks.withType(PlayRun) {
        enabled = true
        httpPort = 9044
        forkOptions.jvmArgs = [
                '-Xdebug',
                '-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=9144',
                '-Dnossisaaapi.config.file=../portal/conf-dev-security/modules-nossisaaapi.conf',
                '-Dnossisaaapi.mappings.path=conf',
                '-Dconfig.file=conf-dev/application.conf',
                '-Dlogger.file=../log4j2-dev.xml',
                '-Dlogger.base.path=/tmp',
                '-Dlogger.module.path=/monitoring',
                '-Djavax.net.ssl.trustStore=../portal/conf-dev-security/na-iam.jks',
                '-Djavax.net.ssl.trustStorePassword=123456',
                '-Duser.timezone=UTC'
        ]
        runtimeClasspath += files('../basemodule/conf')
        runtimeClasspath += files('../conf-dev')
        runtimeClasspath += files('../basemodule/target/playBinary/classes')
        runtimeClasspath += files('conf-dev')
        runtimeClasspath += files('../portal/conf-dev-security')
    }
}

project(":na-portal-frontend-mpt") {
    tasks.withType(PlayRun) {
        enabled = true
        httpPort = 9045
        forkOptions.jvmArgs = [
                '-Xdebug',
                '-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=9145',
                '-Dnossisaaapi.config.file=../portal/conf-dev-security/modules-nossisaaapi.conf',
                '-Dnossisaaapi.mappings.path=conf',
                '-Dconfig.file=conf-dev/application.conf',
                '-Dlogger.file=../log4j2-dev.xml',
                '-Dlogger.base.path=/tmp',
                '-Dlogger.module.path=/mpt',
                '-Djavax.net.ssl.trustStore=../portal/conf-dev-security/na-iam.jks',
                '-Djavax.net.ssl.trustStorePassword=123456',
                '-Duser.timezone=UTC'
        ]
        runtimeClasspath += files('../basemodule/conf')
        runtimeClasspath += files('../conf-dev')
        runtimeClasspath += files('../basemodule/target/playBinary/classes')
        runtimeClasspath += files('conf-dev')
        runtimeClasspath += files('../portal/conf-dev-security')
    }
}

project(":na-portal-frontend-go") {
    tasks.withType(PlayRun) {
        enabled = true
        httpPort = 9046
        forkOptions.jvmArgs = [
                '-Xdebug',
                '-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=9146',
                '-Dnossisaaapi.config.file=../portal/conf-dev-security/modules-nossisaaapi.conf',
                '-Dnossisaaapi.mappings.path=conf',
                '-Dconfig.file=conf-dev/application.conf',
                '-Dlogger.file=../log4j2-dev.xml',
                '-Dlogger.base.path=/tmp',
                '-Dlogger.module.path=/go',
                '-Djavax.net.ssl.trustStore=../portal/conf-dev-security/na-iam.jks',
                '-Djavax.net.ssl.trustStorePassword=123456',
                '-Duser.timezone=UTC'
        ]
        runtimeClasspath += files('../basemodule/conf')
        runtimeClasspath += files('../conf-dev')
        runtimeClasspath += files('../basemodule/target/playBinary/classes')
        runtimeClasspath += files('conf-dev')
        runtimeClasspath += files('../portal/conf-dev-security')
    }
}

project(":na-portal-frontend-nadm") {
    tasks.withType(PlayRun) {
        enabled = true
        httpPort = 9047
        forkOptions.jvmArgs = [
                '-Xdebug',
                '-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=9147',
                '-Dnossisaaapi.config.file=../portal/conf-dev-security/modules-nossisaaapi.conf',
                '-Dnossisaaapi.mappings.path=conf',
                '-Dconfig.file=conf-dev/application.conf',
                '-Dlogger.file=../log4j2-dev.xml',
                '-Dlogger.base.path=/tmp',
                '-Dlogger.module.path=/nadm',
                '-Djavax.net.ssl.trustStore=../portal/conf-dev-security/na-iam.jks',
                '-Djavax.net.ssl.trustStorePassword=123456',
                '-Duser.timezone=UTC'
        ]
        runtimeClasspath += files('../basemodule/conf')
        runtimeClasspath += files('../conf-dev')
        runtimeClasspath += files('../basemodule/target/playBinary/classes')
        runtimeClasspath += files('conf-dev')
        runtimeClasspath += files('../portal/conf-dev-security')
        runtimeClasspath += files('conf')
    }
}

project(":na-portal-frontend-diagnostics") {
    tasks.withType(PlayRun) {
        enabled = true
        httpPort = 9048
        forkOptions.jvmArgs = [
                '-Xdebug',
                '-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=9148',
                '-Dnossisaaapi.config.file=../portal/conf-dev-security/modules-nossisaaapi.conf',
                '-Dnossisaaapi.mappings.path=conf',
                '-Dconfig.file=conf-dev/application.conf',
                '-Dlogger.file=../log4j2-dev.xml',
                '-Dlogger.base.path=/tmp',
                '-Dlogger.module.path=/diagnostics',
                '-Djavax.net.ssl.trustStore=../portal/conf-dev-security/na-iam.jks',
                '-Djavax.net.ssl.trustStorePassword=123456',
                '-Duser.timezone=UTC'
        ]
        runtimeClasspath += files('../basemodule/conf')
        runtimeClasspath += files('../conf-dev')
        runtimeClasspath += files('../basemodule/target/playBinary/classes')
        runtimeClasspath += files('conf-dev')
        runtimeClasspath += files('../portal/conf-dev-security')
        runtimeClasspath += files('conf')
    }
}

static def appendDependency(mvnDependencies, playDependency, isRuntime) {
    def dependency = mvnDependencies.appendNode('dependency')
    dependency.appendNode('groupId', playDependency.getGroup())
    dependency.appendNode('artifactId', playDependency.getName())
    dependency.appendNode('version', playDependency.getVersion())

    // [HAMMER] To include <classifier>assets</classifier> in pom.xml when needed
    if(playDependency instanceof org.gradle.api.internal.artifacts.dependencies.DefaultExternalModuleDependency && playDependency.isForce()) {
        dependency.appendNode('classifier', 'assets')
    }

    // Check if the dependency has exclusions
    if (playDependency.excludeRules.size() > 0) {
        def exclusionsNode = dependency.appendNode('exclusions')
        playDependency.excludeRules.each { exclusion ->
            def exclusionNode = exclusionsNode.appendNode('exclusion')
            exclusionNode.appendNode('groupId', exclusion.group)
            exclusionNode.appendNode('artifactId', exclusion.module)
        }
    }

    // Check if transitive is false and exclude all transitive dependencies
    if (!playDependency.transitive) {
        def exclusionsNode = dependency.appendNode('exclusions')
        def exclusionNode = exclusionsNode.appendNode('exclusion')
        exclusionNode.appendNode('groupId', '*')
        exclusionNode.appendNode('artifactId', '*')
    }

    if (isRuntime) {
        dependency.appendNode('scope', 'runtime')
    }
}