package na.catalog.basemodule.models.nossisinv.attributes.uic;

public class UserInterfaceButton extends UserInterfaceComponent {

    private String event;
    private String url;
    private String iconClassesOnLeft;
    private String iconClassesOnRight;
    private String eventData;
    private boolean disabled;

    public UserInterfaceButton(String event, String iconClassesOnLeft) {
        this.event = event;
        this.iconClassesOnLeft = iconClassesOnLeft;
        this.disabled = false;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getIconClassesOnLeft() {
        return iconClassesOnLeft;
    }

    public void setIconClassesOnLeft(String iconClassesOnLeft) {
        this.iconClassesOnLeft = iconClassesOnLeft;
    }

    public String getIconClassesOnRight() {
        return iconClassesOnRight;
    }

    public void setIconClassesOnRight(String iconClassesOnRight) {
        this.iconClassesOnRight = iconClassesOnRight;
    }

    public boolean isDisabled() {
        return disabled;
    }

    public void setDisabled(boolean disabled) {
        this.disabled = disabled;
    }

    public String getEventData() {
        return eventData;
    }

    public void setEventData(String eventData) {
        this.eventData = eventData;
    }
}
