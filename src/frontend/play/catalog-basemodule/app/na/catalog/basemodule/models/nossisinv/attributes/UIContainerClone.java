package na.catalog.basemodule.models.nossisinv.attributes;

import pt.ptinovacao.netwin.kernel.catalog.client.model.CatEntity;
import pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext;

import java.util.Map;

/**
 * Created by <PERSON><PERSON> on 06-05-2015.
 */
public class UIContainerClone extends UIContainer {
    private final Long entityIdCloned;

    public UIContainerClone(UIContainer uiContainer) {
        super(null, uiContainer.getIdCatEntity(),
            uiContainer.getDomain(),
            uiContainer.getFormTarget(),
            uiContainer.getName(),
            uiContainer.getCatEntity(),
            uiContainer.getUiContext(),
            uiContainer.getMockAttributes(),
            uiContainer.getMockRelationships(),
            uiContainer.getMockCatEntityAttributes());
        this.entityIdCloned = uiContainer.getId();
    }

    @SuppressWarnings("PMD.ExcessiveParameterList")
    public UIContainerClone(Long entityId, long idCatEntity, String domain, String formTarget, String name, CatEntity catEntity, UIContext uiContext, Map<String, UICatInsAttribute<?>> mockAttributes, Map<String, UIEntityRelationship<?>> mockRelationships) {
        super(null, idCatEntity, domain, formTarget, name, catEntity, uiContext, mockAttributes, mockRelationships);
        this.entityIdCloned = entityId;
    }

    @SuppressWarnings("PMD.ExcessiveParameterList")
    public UIContainerClone(Long entityId, long idCatEntity, String domain, String formTarget, String name,
                            CatEntity catEntity,
                            UIContext uiContext,
                            Map<String, UICatInsAttribute<?>> mockAttributes,
                            Map<String, UIEntityRelationship<?>> mockRelationship,
                            Map<String, UICatEntityAttribute<?>> catEntityAttributes) {
        super(null, idCatEntity, domain, formTarget, name, catEntity, uiContext, mockAttributes, mockRelationship, catEntityAttributes);
        this.entityIdCloned = entityId;
    }

    public Long getEntityIdCloned() {
        return entityIdCloned;
    }
}
