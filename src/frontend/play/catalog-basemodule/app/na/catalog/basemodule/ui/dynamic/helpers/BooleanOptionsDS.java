package na.catalog.basemodule.ui.dynamic.helpers;

import pt.alticelabs.nossis.messages.api.Messages;

import java.util.HashMap;
import java.util.Map;

public class BooleanOptionsDS implements DataSource<Map<String, String>> {

    static final String VALUE_TYPE = "VALUE_TYPE";
    static final String TRUTHY_OPTION_LABEL = "truthyOptionLabel";
    static final String FALSY_OPTION_LABEL = "falsyOptionLabel";

    // defines the value type to use on the options
    enum ValueType {

        STRING("true", "false"), // truthy value is 'true' and falsy value is 'false'
        NUMBER("1", "0"); // truthy value is '1' and falsy value is '0'

        private final String truthyValue;
        private final String falsyValue;

        ValueType(final String truthyValue, final String falsyValue) {
            this.truthyValue = truthyValue;
            this.falsyValue = falsyValue;
        }

        public String getTruthyValue() {
            return truthyValue;
        }

        public String getFalsyValue() {
            return falsyValue;
        }
    }

    @Override
    public Map<String, String> getResults(final Map<String, String> params) {
        final Map<String, String> results = new HashMap<>();
        final ValueType valueType = ValueType.valueOf(params.getOrDefault(BooleanOptionsDS.VALUE_TYPE, ValueType.STRING.name()).toUpperCase());
        results.put(valueType.getTruthyValue(), Messages.get(params.get(BooleanOptionsDS.TRUTHY_OPTION_LABEL)));
        results.put(valueType.getFalsyValue(), Messages.get(params.get(BooleanOptionsDS.FALSY_OPTION_LABEL)));
        return results;
    }
}