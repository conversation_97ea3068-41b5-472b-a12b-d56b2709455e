package na.catalog.basemodule.models.components.impl;

import na.catalog.basemodule.models.components.Component;
import na.catalog.basemodule.models.components.ComponentDefinition;
import na.catalog.basemodule.ui.I18n;
import na.catalog.basemodule.ui.InputText;
import na.catalog.basemodule.ui.InputWebComponent;
import na.catalog.basemodule.ui.WebComponent;

public class InputTextComponentImpl implements Component {

    @Override
    public WebComponent build(final ComponentDefinition definition) {

        final InputText inputText = new InputText();
        inputText.setId(definition.getUiElement().getName());
        inputText.setType(InputWebComponent.Type.textField);
        inputText.setDataField(definition.getDataField());
        inputText.setDataAttributes(definition.getDataAttributes());
        inputText.setI18n(getI18n(definition));
        inputText.setAction(definition.getAction());
        inputText.setPlaceholder(new I18n().setKey(definition.getPlaceholderI18N()));
        inputText.setRequired(definition.getUiElement().isRequired());

        if (definition.getValue() != null) {
            inputText.setValue(definition.getValue().toString());
        }

        return inputText;
    }

    private I18n getI18n(final ComponentDefinition definition) {
        return new I18n().setKey(definition.getFallbackI18N());
    }
}