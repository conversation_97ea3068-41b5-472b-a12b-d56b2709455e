package na.catalog.basemodule.ui;

import java.util.Map;

public abstract class DynamicComponent extends WebComponent {

    private Map<String, Object> parameters;

    public Map<String, Object> getParameters() {
        return parameters;
    }

    public <T> T getParameter(final String key) {
        return (T) parameters.get(key);
    }

    public void setParameters(final Map<String, Object> parameters) {
        this.parameters = parameters;
    }

    public abstract void init(Map<String, Object> parameters);
}