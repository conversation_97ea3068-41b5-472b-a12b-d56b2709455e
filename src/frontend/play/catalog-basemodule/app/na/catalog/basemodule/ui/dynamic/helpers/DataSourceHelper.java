package na.catalog.basemodule.ui.dynamic.helpers;

import java.util.Map;

public class DataSourceHelper {

    private DataSource implementation;
    private Map<String, String> params;

    public DataSourceHelper() {
    }

    public DataSource getImplementation() {
        return implementation;
    }

    public void setImplementation(final DataSource implementation) {
        this.implementation = implementation;
    }

    public Map<String, String> getParams() {
        return params;
    }

    public void setParams(final Map<String, String> params) {
        this.params = params;
    }
}