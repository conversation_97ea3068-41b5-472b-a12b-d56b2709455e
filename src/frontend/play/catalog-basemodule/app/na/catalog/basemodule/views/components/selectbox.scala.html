@(element: na.catalog.basemodule.ui.SelectBox)

@import na.catalog.basemodule.ui.Action
@import na.naportalbase.utils.TemplateUtils
@import play.twirl.api.HtmlFormat

@viewValue() = @{
    var value: String = element.getValue.asInstanceOf[String]
    if(value.isEmpty) {
        value = "--"
    }
    value
}

@selected(value: String) = @{
    if(value != null && value.equals(element.getValue)) {
        "selected"
    } else ""
}

@dataOfOption(value: String) = @{
    if(value != null) {
        Html(s"""data-of-option="${HtmlFormat.escape(value)}" """)
    } else ""
}

@if(Action.VIEW.equals(element.getAction)) {
    <span id="@element.getId">@viewValue</span>
} else {
    <@element.getTagName @TemplateUtils.dynamicElementsAttributes(element.getDOMAttributes)>
        @for(option <- element.getOptions) {
            <option value="@option.value" @selected(option.value) @dataOfOption(option.dataOfOption)>@option.text</option>
        }
    </@element.getTagName>
}