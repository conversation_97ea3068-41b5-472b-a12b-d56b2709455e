package na.catalog.basemodule.services.catalog;

import com.fasterxml.jackson.databind.ObjectMapper;
import na.catalog.basemodule.settings.CatalogBaseModuleConstants;
import na.naportalbase.utils.ODataQueryParametersBuilder;
import na.catalog.basemodule.services.catalog.client.api.CatalogImplConfig;
import play.libs.ws.WSRequest;
import play.libs.ws.WSResponse;
import play.mvc.Http;
import pt.ptinovacao.netwin.kernel.catalog.client.api.Catalog;
import pt.ptinovacao.netwin.kernel.catalog.client.api.exceptions.CatalogExceptionCodes;
import pt.ptinovacao.netwin.kernel.catalog.client.model.*;
import pt.ptinovacao.netwin.kernel.catalog.client.model.implementation.*;
import pt.ptinovacao.nossis.exceptions.BusinessException;

import javax.inject.Inject;
import javax.persistence.NoResultException;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

public final class CatalogImpl implements Catalog {

    private static final Map<String, CatEntityRelationship> RELATIONSHIPS_BY_NAME = new HashMap<>();
    private static final Map<Long, CatAttributeType> ATTRIBUTE_TYPE_BY_ID_CACHE = new HashMap<>();
    private static final Map<String, CatEntityImpl> BY_NAME_CACHE = new HashMap<>();
    private static final Map<String, Collection<CatEntity>> QUERY_CACHE = new HashMap<>();
    private static final Map<Long, CatEntityImpl> BY_ID_CACHE = new HashMap<>();
    private static final Map<Long, CatStateMachineImpl> STATE_MACHINE_BY_ID_CACHE = new HashMap<>();
    private static final Map<Long, CatState> STATE_BY_ID_CACHE = new HashMap<>();
    private static final Map<Long, UIElement> UI_ELEMENT_BY_ID_CACHE = new HashMap<>();
    private static final Map<Long, UIEvent> UI_EVENT_BY_ID_CACHE = new HashMap<>();
    private static final CatEntityImpl NO_ENTITY = new CatEntityImpl();

    static final ReadWriteLock rwLock = new ReentrantReadWriteLock();
    static final ObjectMapper mapper = new ObjectMapper();

    static long version = 0L;

    private final play.Logger.ALogger LOGGER;
    private final CatalogImplConfig catalogImplConfig;
    private final LongCatalogCache longCatalogCache;
    private final ServicesHelper servicesHelper;
    private final StringCatalogCache stringCatalogCache;

    @Inject
    public CatalogImpl(final CatalogImplConfig catalogImplConfig,
                       final ServicesHelper servicesHelper) {
        this.catalogImplConfig = catalogImplConfig;
        this.servicesHelper = servicesHelper;
        this.longCatalogCache = new LongCatalogCache(catalogImplConfig, servicesHelper);
        this.stringCatalogCache = new StringCatalogCache(catalogImplConfig, servicesHelper);
        this.LOGGER = play.Logger.of(CatalogBaseModuleConstants.LOGGER_NAME);
    }

    @Override
    public CatEntity getRootCatEntity() {
        throw new UnsupportedOperationException("not supported method");
    }

    @Override
    public CatEntity getCatEntityByName(final String name) {
        final Map<Long, CatEntityImpl> tempByIdCache = new HashMap<>();
        return getCatEntityHolderFromCacheOrInstantiate(tempByIdCache, BY_NAME_CACHE, name);
    }

    @Override
    public CatEntity getCatEntityById(final long id) {
        final Map<Long, CatEntityImpl> tempByIdCache = new HashMap<>();
        return getCatEntityHolderFromCacheOrInstantiate(tempByIdCache, BY_ID_CACHE, id);
    }

    @Override
    public Collection<CatEntity> getCatEntities(final String query, final Map<String, Object> map) {
        return getCatEntities(query);
    }

    @Override
    public UIElement getUIElementById(final long id) {
        return UI_ELEMENT_BY_ID_CACHE.get(id);
    }

    @Override
    public UIEvent getUIEventById(final long id) {
        return UI_EVENT_BY_ID_CACHE.get(id);
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T extends Buildable<?>, K extends ClonableAndBuildable<T, ?>, Z extends AbstractBuilderImpl<T, ?, K>> Z cloneEntity(final T entity) {
        try {
            return (Z) entity.getBuilder();
        } catch (CloneNotSupportedException e) {
            LOGGER.error("Catalog entity with name '{}' not cloneable.", entity.getClass().getSimpleName());
            throw new BusinessException(CatalogExceptionCodes.CATALOG_ENTITY_NOT_CLONEABLE, new Throwable(), "name", entity.getClass().getSimpleName());
        }
    }

    @Override
    public void clearCache() {
        rwLock.writeLock().lock();
        try {
            BY_NAME_CACHE.clear();
            BY_ID_CACHE.clear();
            ATTRIBUTE_TYPE_BY_ID_CACHE.clear();
            STATE_MACHINE_BY_ID_CACHE.clear();
            STATE_BY_ID_CACHE.clear();
            UI_ELEMENT_BY_ID_CACHE.clear();
            RELATIONSHIPS_BY_NAME.clear();
            UI_EVENT_BY_ID_CACHE.clear();
            QUERY_CACHE.clear();
        } finally {
            rwLock.writeLock().unlock();
        }
    }

    @Override
    public void removeCatEntities(final Set<Long> ids) {

        final AtomicBoolean clearQueryCache = new AtomicBoolean(false);

        ids.forEach(entityId -> {
            final CatEntity catEntity = getFromCache(BY_ID_CACHE, entityId);
            if (catEntity != null) {
                for (UIContext uiContext : catEntity.getUIContexts().values()) {
                    for (UIGroup uiGroup : uiContext.getUIGroups().values()) {
                        for (UISubGroup uiSubGroup : uiGroup.getUISubGroups().values()) {
                            for (Set<UIElement> uiElements : uiSubGroup.getUIElements().values()) {
                                for (UIElement uiElement : uiElements) {
                                    UI_EVENT_BY_ID_CACHE.remove(uiElement.getId());
                                }
                            }
                        }
                    }
                }

                removeFromCache(BY_NAME_CACHE, catEntity.getName());
                removeFromCache(BY_ID_CACHE, catEntity.getId());
                clearQueryCache.set(true);
            }
        });

        if (clearQueryCache.get()) {
            QUERY_CACHE.clear();
        }
    }

    @Override
    public void removeAttributeTypes(final Set<Long> ids) {

        final AtomicBoolean clearQueryCache = new AtomicBoolean(false);

        ids.forEach(entityId -> {
            ATTRIBUTE_TYPE_BY_ID_CACHE.remove(entityId);
            clearQueryCache.set(true);
        });

        final Collection<CatEntityImpl> allCatEntities = getAllFromCache(BY_ID_CACHE);
        final Set<Long> idCatEntities2RemoveFromCache = new HashSet<>();

        for (Long idAttributeType : ids) {
            for (CatEntity catEntity : allCatEntities) {
                for (CatInsAttribute catInsAttribute : catEntity.getAttributes().values()) {
                    if (idAttributeType.equals(catInsAttribute.getAttributeType().getId())) {
                        idCatEntities2RemoveFromCache.add(idAttributeType);
                    }
                }
            }
        }

        removeCatEntities(idCatEntities2RemoveFromCache);

        if (clearQueryCache.get()) {
            QUERY_CACHE.clear();
        }
    }

    @Override
    public void clearQueryCache() {
        QUERY_CACHE.clear();
    }

    public long getVersion() {
        return version;
    }

    public CatState getCatStateById(final long id) {
        return STATE_BY_ID_CACHE.get(id);
    }

    public UIElement getUIElementById(final Long id) {
        return UI_ELEMENT_BY_ID_CACHE.get(id);
    }

    private Long getUrlId(final String url) {
        final String[] parts = url.split("/");
        return Long.parseLong(parts[parts.length - 1]);
    }

    // -----------------------------------------------------------------------------------------------------------------
    // -----------------------------------------------------------------------------------------------------------------
    // -----------------------------------------------------------------------------------------------------------------
    // -----------------------------------------------------------------------------------------------------------------
    // -----------------------------------------------------------------------------------------------------------------
    // -----------------------------------------------------------------------------------------------------------------

    // -------------
    // --- Cache ---
    // -------------

    private <T> Collection<CatEntityImpl> getAllFromCache(final Map<T, CatEntityImpl> cache) {
        rwLock.readLock().lock();
        try {
            return cache.values();
        } finally {
            rwLock.readLock().unlock();
        }
    }

    private <T> CatEntity getFromCache(final Map<T, CatEntityImpl> cache, final T key) {
        rwLock.readLock().lock();
        try {
            return cache.get(key);
        } finally {
            rwLock.readLock().unlock();
        }
    }

    private <T> T addToCache(final Map<Long, T> cache, final Long id, final T toAdd) {
        rwLock.writeLock().lock();
        try {
            return addToCacheNoLock(cache, id, toAdd);
        } finally {
            rwLock.writeLock().unlock();
        }
    }

    private <T> T addToCacheNoLock(final Map<Long, T> cache, final Long id, final T toAdd) {
        final T res = cache.get(id);
        if (res == null) {
            cache.put(id, toAdd);
            return toAdd;
        }
        return res;
    }

    private <T> void removeFromCache(final Map<T, CatEntityImpl> cache, final T key) {
        final CatEntity catEntity = getFromCache(cache, key);
        if (catEntity != null) {
            rwLock.readLock().lock();
            try {
                cache.remove(key);
            } finally {
                rwLock.readLock().unlock();
            }
        }
    }

    // --------------------
    // --- Cat Entities ---
    // --------------------

    private Collection<CatEntity> getCatEntities(final String query) {
        try {
            rwLock.readLock().lock();
            try {
                final Collection<CatEntity> res = QUERY_CACHE.get(query);
                if (res != null) {
                    return res;
                }
            } finally {
                rwLock.readLock().unlock();
            }

            final String serviceUrl = catalogImplConfig.getURLServiceCatalog(null, null);

            final ODataQueryParametersBuilder queryParameter = ODataQueryParametersBuilder.get().filter(query).select("id");

            System.out.println("serviceUrl =" + serviceUrl + "; queryParameter =" + queryParameter);

            final WSRequest requestHolder = servicesHelper.getRequestHolder(serviceUrl, queryParameter.build());
            final WSResponse response = requestHolder.get().toCompletableFuture().get(10, TimeUnit.SECONDS);
            final Map<String, Object> catEntities = getCatEntities(response);
            final Collection<CatEntity> res = new LinkedList<>();

            longCatalogCache.toObjectList(catEntities.get("entities")).forEach(e -> {
                final Map<String, Object> eMap = longCatalogCache.toAttributeMap(e);
                res.add(getCatEntityHolderFromCacheOrInstantiate(new HashMap<>(), BY_ID_CACHE, longCatalogCache.toLong(eMap.get("id"))));
            });
            rwLock.writeLock().lock();
            try {
                final Collection<CatEntity> inCache = QUERY_CACHE.get(query);
                if (inCache != null) {
                    return inCache;
                }
                QUERY_CACHE.put(query, res);
            } finally {
                rwLock.writeLock().unlock();
            }
            return res;
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            throw new RuntimeException("Error fetching CatEntities");
        }
    }

    private Map<String, Object> getCatEntities(final WSResponse response) {
        try {
            if (response.getStatus() == Http.Status.OK) {
                return mapper.readValue(response.getBody(), AbstractCatalogCache.TYPE_REF_MAP_STRING_OBJECT);
            }
            return new HashMap<>();
        } catch (IOException e) {
            throw new RuntimeException("Error fetching CatEntity from response");
        }
    }

    private CatEntity getCatEntityHolderFromCacheOrInstantiate(final Map<Long, CatEntityImpl> tempByIdCache,
                                                               final Map<Long, CatEntityImpl> cache,
                                                               final Long key) {
        final CatEntity catEntity = getFromCache(cache, key);
        if (catEntity == null) {
            return getCatEntity(tempByIdCache, cache, key);
        }
        if (catEntity.equals(NO_ENTITY)) {
            longCatalogCache.throwCatEntityNotFoundException(key);
        }
        return catEntity;
    }

    private CatEntity getCatEntityHolderFromCacheOrInstantiate(final Map<Long, CatEntityImpl> tempByIdCache,
                                                               final Map<String, CatEntityImpl> cache,
                                                               final String key) {
        final CatEntity catEntity = getFromCache(cache, key);
        if (catEntity == null) {
            return getCatEntity(tempByIdCache, cache, key);
        }
        if (catEntity == NO_ENTITY) {
            stringCatalogCache.throwCatEntityNotFoundException(key);
        }
        return catEntity;
    }

    private CatEntity getCatEntity(final Map<Long, CatEntityImpl> tempByIdCache,
                                   final Map<Long, CatEntityImpl> cache,
                                   final Long key) {
        final CatEntityImpl cachedCatEntity = tempByIdCache.get(key);
        if (cachedCatEntity != null) {
            return cachedCatEntity;
        }

        final CatEntityImpl newCatEntity = new CatEntityImpl();
        final Map<String, Object> entity = longCatalogCache.readCatEntity(cache, key);

        final CatEntityImpl catEntity = addToCacheNoLock(tempByIdCache, longCatalogCache.toLong(entity.get("id")), newCatEntity);
        return matchCatEntity(catEntity, newCatEntity, tempByIdCache, cache, entity, key, longCatalogCache);
    }

    private CatEntity getCatEntity(final Map<Long, CatEntityImpl> tempByIdCache,
                                   final Map<String, CatEntityImpl> cache,
                                   final String key) {
        final CatEntityImpl newCatEntity = new CatEntityImpl();
        final Map<String, Object> entity = stringCatalogCache.readCatEntity(cache, key);

        final CatEntityImpl catEntity = addToCacheNoLock(tempByIdCache, longCatalogCache.toLong(entity.get("id")), newCatEntity);
        return matchCatEntity(catEntity, newCatEntity, tempByIdCache, cache, entity, key, stringCatalogCache);
    }

    private <T> CatEntity matchCatEntity(final CatEntityImpl catEntity,
                                         final CatEntityImpl newCatEntity,
                                         final Map<Long, CatEntityImpl> tempByIdCache,
                                         final Map<T, CatEntityImpl> cache,
                                         final Map<String, Object> entity,
                                         final T key,
                                         final AbstractCatalogCache<T> catalogCache) {
        if (catEntity == newCatEntity) {
            try {
                return instantiateCatEntity(tempByIdCache, catEntity, entity);
            } catch (NoResultException e) {
                rwLock.writeLock().lock();
                try {
                    cache.put(key, NO_ENTITY);
                } finally {
                    rwLock.writeLock().unlock();
                }
                catalogCache.throwCatEntityNotFoundException(key);
            }
        }
        return catEntity;
    }

    // ------------
    // --- I18N ---
    // ------------

    private Map<String, String> instantiateI18n(final Map<String, Object> values) {
        final Map<String, String> finalMap = new HashMap<>();
        values.forEach((key, value) -> {
            if (value != null) {
                finalMap.put(key, value.toString());
            }
        });
        return finalMap;
    }

    // ------------------------
    // --- Create instances ---
    // ------------------------

    private CatEntity instantiateCatEntity(final Map<Long, CatEntityImpl> tempByIdCache,
                                           final CatEntityImpl catEntity,
                                           final Map<String, Object> entity) {

        final String entityName = longCatalogCache.toString(entity.get("name"));
        final long entityId = longCatalogCache.toLong(entity.get("id"));

        LOGGER.info("begin instantiateCatEntity, name = {}", entityName);
        instantiateCatEntity(catEntity, entity);
        instantiateParent(tempByIdCache, catEntity, longCatalogCache.toString(entity.get("parent")));
        instantiateRootParent(tempByIdCache, catEntity, longCatalogCache.toString(entity.get("rootParent")));
        instantiateAttributes(catEntity, catEntity.getAttributes(), longCatalogCache.toObjectList(entity.get("attributes")));
        instantiateCatEntityAttributes(catEntity.getCatEntityAttributes(), longCatalogCache.toObjectList(entity.get("catEntityAttributes")));
        instantiateRelationships(tempByIdCache, catEntity, longCatalogCache.toObjectList(entity.get("relationships")));
        instantiateRelationshipsToValidate(catEntity, longCatalogCache.toAttributeMap(entity.get("relationshipsToValidate")));
        instantiateUIContexts(catEntity.getUIContexts(), longCatalogCache.toObjectList(entity.get("uiContexts")));
        instantiateCanBecome(tempByIdCache, catEntity.getCanBecome(), longCatalogCache.toObjectList(entity.get("canbecome")));
        rwLock.writeLock().lock();
        try {
            final CatEntityImpl res;
            final CatEntityImpl inCacheCatEntity = BY_ID_CACHE.get(entityId);
            if (inCacheCatEntity == null) {
                BY_NAME_CACHE.put(entityName, catEntity);
                BY_ID_CACHE.put(entityId, catEntity);
                res = catEntity;
            } else {
                res = inCacheCatEntity;
            }
            LOGGER.info("end instantiateCatEntity, name = {}", entityName);
            return res;
        } finally {
            rwLock.writeLock().unlock();
        }
    }

    private void instantiateCatEntity(final CatEntityImpl catEntity, final Map<String, Object> entity) {
        catEntity.setAbstractEntity(longCatalogCache.toBoolean(entity.get("abstractEntity")));
        catEntity.setActive(CatActiveType.valueOf(longCatalogCache.toString(entity.get("active"))));
        catEntity.setSystem(longCatalogCache.toBoolean(entity.get("system")));
        catEntity.setDescription(longCatalogCache.toString(entity.get("description")));
        catEntity.setEjbName(longCatalogCache.toString(entity.get("ejbName")));
        catEntity.setResourceName(longCatalogCache.toString(entity.get("resourceName")));
        catEntity.setName(longCatalogCache.toString(entity.get("name")));
        catEntity.setHistoric(longCatalogCache.toBoolean(entity.get("hasHistoric")));
        catEntity.setFiles(longCatalogCache.toBoolean(entity.get("hasFiles")));
        if (entity.get("id") != null) {
            catEntity.setId(longCatalogCache.toLong(entity.get("id")));
        }
        catEntity.setI18n(instantiateI18n(longCatalogCache.toAttributeMap(entity.get("i18n"))));
    }

    private void instantiateParent(final Map<Long, CatEntityImpl> tempByIdCache,
                                   final CatEntityImpl entity,
                                   final String parentURI) {
        if (parentURI != null) {
            final CatEntity parent = getCatEntityHolderFromCacheOrInstantiate(tempByIdCache, BY_ID_CACHE, getUrlId(parentURI));
            entity.setParent(parent);
        }
    }

    private void instantiateRootParent(final Map<Long, CatEntityImpl> tempByIdCache,
                                       final CatEntityImpl entity,
                                       final String rootParentURI) {
        if (rootParentURI != null) {
            final CatEntity rootParent = getCatEntityHolderFromCacheOrInstantiate(tempByIdCache, BY_ID_CACHE, getUrlId(rootParentURI));
            entity.setRootParent(rootParent);
        }
    }

    private void instantiateAttributes(final CatEntity catEntity,
                                       final Map<String, CatInsAttribute> attributes,
                                       final List<Object> entityAttributes) {
        if (entityAttributes != null) {
            entityAttributes.forEach(attribute -> {
                final Map<String, Object> attributeMap = longCatalogCache.toAttributeMap(attribute);
                final CatInsAttributeImpl newAttribute = new CatInsAttributeImpl();
                attributes.put(longCatalogCache.toString(attributeMap.get("name")), newAttribute);
                instantiateAttribute(catEntity, newAttribute, attributeMap);
            });
        }
    }

    private void instantiateAttribute(final CatEntity catEntity,
                                      final CatInsAttributeImpl newAttribute,
                                      final Map<String, Object> attribute) {
        newAttribute.setId(longCatalogCache.toLong(attribute.get("id")));
        newAttribute.setDefaultCalculator(longCatalogCache.toString(attribute.get("defaultCalculator")));
        newAttribute.setDefaultValue(longCatalogCache.toString(attribute.get("defaultValue")));
        newAttribute.setDescription(longCatalogCache.toString(attribute.get("description")));
        newAttribute.setDynamic(longCatalogCache.toBoolean(attribute.get("dynamic")));
        newAttribute.setName(longCatalogCache.toString(attribute.get("name")));
        newAttribute.setRequired(longCatalogCache.toBoolean(attribute.get("required")));
        newAttribute.setSystem(longCatalogCache.toBoolean(attribute.get("system")));
        newAttribute.setHistoric(longCatalogCache.toBoolean(attribute.get("historic")));
        newAttribute.setAttributeType(instantiateAttributeType(catEntity, longCatalogCache.toAttributeMap(attribute.get("attributetype"))));
        newAttribute.setI18n(instantiateI18n(longCatalogCache.toAttributeMap(attribute.get("i18n"))));
        newAttribute.setAttributeOrder(longCatalogCache.toInteger(attribute.get("attributeOrder")));
    }

    private CatAttributeType instantiateAttributeType(final CatEntity catEntity,
                                                      final Map<String, Object> attributeType) {
        final CatAttributeTypeImpl newAttributeTypeImpl = new CatAttributeTypeImpl();
        final CatAttributeType newAttributeType = addToCache(ATTRIBUTE_TYPE_BY_ID_CACHE, longCatalogCache.toLong(attributeType.get("id")), newAttributeTypeImpl);
        if (newAttributeType == newAttributeTypeImpl) {
            newAttributeTypeImpl.setId(longCatalogCache.toLong(attributeType.get("id")));
            newAttributeTypeImpl.setCanAddEnumerated(longCatalogCache.toBoolean(attributeType.get("canAddEnumerated")));
            newAttributeTypeImpl.setDescription(longCatalogCache.toString(attributeType.get("description")));
            newAttributeTypeImpl.setEnumeratedQuery(longCatalogCache.toString(attributeType.get("enumeratedQuery")));
            newAttributeTypeImpl.setIsSystem(longCatalogCache.toBoolean(attributeType.get("system")));
            newAttributeTypeImpl.setMaxDate(longCatalogCache.toDate(attributeType.get("maxDate")));
            newAttributeTypeImpl.setMaxDouble(longCatalogCache.toDouble(attributeType.get("maxDouble")));
            newAttributeTypeImpl.setMaxLength(longCatalogCache.toInteger(attributeType.get("maxLength")));
            newAttributeTypeImpl.setMinLength(longCatalogCache.toInteger(attributeType.get("minLength")));
            newAttributeTypeImpl.setMaxLong(longCatalogCache.toLong(attributeType.get("maxLong")));
            newAttributeTypeImpl.setMinLong(longCatalogCache.toLong(attributeType.get("minLong")));
            newAttributeTypeImpl.setMinDate(longCatalogCache.toDate(attributeType.get("minDate")));
            newAttributeTypeImpl.setMinDouble(longCatalogCache.toDouble(attributeType.get("minDouble")));
            newAttributeTypeImpl.setName(longCatalogCache.toString(attributeType.get("name")));
            newAttributeTypeImpl.setRegExpValidation(longCatalogCache.toString(attributeType.get("regExpValidation")));
            newAttributeTypeImpl.setUnit(longCatalogCache.toString(attributeType.get("unit")));
            newAttributeTypeImpl.setType(instantiatePrimitiveAttributeType(longCatalogCache.toString(attributeType.get("type"))));
            newAttributeTypeImpl.setCatEnumeratedAttributeValue(instantiateEnumeratedAttributeValues(longCatalogCache.toObjectList(attributeType.get("enumeratedattributevalues"))));
            newAttributeTypeImpl.setCatStateMachine(instantiateStateMachine(catEntity, longCatalogCache.toString(attributeType.get("statemachine"))));
            newAttributeTypeImpl.setI18n(instantiateI18n(longCatalogCache.toAttributeMap(attributeType.get("i18n"))));

            final Long referredType = longCatalogCache.toLong(attributeType.get("refereedType"));
            final CatAttributeType attributeTypeReferred = (referredType == null) ? null : getOrCreateAttributeType(referredType);
            newAttributeTypeImpl.setRefereedType(attributeTypeReferred);
        }
        return newAttributeType;
    }

    private CatAttributeType getOrCreateAttributeType(final Long id) {
        final CatAttributeType cachedAttributeType = ATTRIBUTE_TYPE_BY_ID_CACHE.get(id);
        if (cachedAttributeType != null) {
            return cachedAttributeType;
        }
        final CatAttributeType newAttributeType = new CatAttributeTypeImpl();
        addToCache(ATTRIBUTE_TYPE_BY_ID_CACHE, id, newAttributeType);
        return newAttributeType;
    }

    private CatPrimitiveAttributeType instantiatePrimitiveAttributeType(final String theType) {
        return CatPrimitiveAttributeType.valueOf(theType);
    }

    private Collection<CatEnumeratedAttributeValue> instantiateEnumeratedAttributeValues(final List<Object> catEnumeratedAttributeValue) {
        final Collection<CatEnumeratedAttributeValue> res = new HashSet<>();
        if (catEnumeratedAttributeValue != null) {
            catEnumeratedAttributeValue.forEach(value -> res.add(instantiateEnumeratedAttributeValue(longCatalogCache.toAttributeMap(value))));
        }
        return res;
    }

    private CatEnumeratedAttributeValue instantiateEnumeratedAttributeValue(final Map<String, Object> value) {
        final CatEnumeratedAttributeValueImpl res = new CatEnumeratedAttributeValueImpl();
        res.setId(longCatalogCache.toLong(value.get("id")));
        res.setDeprecated(longCatalogCache.toBoolean(value.get("deprecated")));
        res.setPosition(longCatalogCache.toInteger(value.get("position")));
        res.setIcon(longCatalogCache.toString(value.get("icon")));
        res.setValue(longCatalogCache.toString(value.get("value")));
        res.setI18n(instantiateI18n(longCatalogCache.toAttributeMap(value.get("i18n"))));
        return res;
    }

    private CatStateMachineImpl instantiateStateMachine(final CatEntity catEntity, final String catStateMachineId) {
        if (catStateMachineId == null) {
            return null;
        }
        final long id = getUrlId(catStateMachineId);
        final CatStateMachineImpl newCatStateMachineImpl = new CatStateMachineImpl();
        final CatStateMachineImpl newCatStateMachine = addToCache(STATE_MACHINE_BY_ID_CACHE, id, newCatStateMachineImpl);
        if (newCatStateMachine == newCatStateMachineImpl) {
            final Map<String, Object> stateMachineMap = readStateMachine(catStateMachineId);
            newCatStateMachineImpl.setId(longCatalogCache.toLong(stateMachineMap.get("id")));
            newCatStateMachineImpl.setStates(instantiateCatStates(catEntity, longCatalogCache.toObjectList(stateMachineMap.get("states"))));
            newCatStateMachineImpl.setInitialState(newCatStateMachineImpl.getStates().get(longCatalogCache.toLong(longCatalogCache.toAttributeMap(stateMachineMap.get("initialstate")).get("id"))));
            newCatStateMachineImpl.setName(longCatalogCache.toString(stateMachineMap.get("name")));
        }
        return newCatStateMachine;
    }

    private Map<String, Object> readStateMachine(final String catStateMachineUrl) {
        try {
            final ODataQueryParametersBuilder queryParameter = ODataQueryParametersBuilder.get().expand("initialstate,states,statetransitions");
            System.out.println("serviceUrl =" + catStateMachineUrl + "; queryParameter =" + queryParameter);

            final WSRequest requestHolder = servicesHelper.getRequestHolder(catStateMachineUrl);
            final WSResponse response = requestHolder.get().toCompletableFuture().get(10, TimeUnit.SECONDS);
            if (response.getStatus() == Http.Status.OK) {
                return mapper.readValue(response.getBody(), AbstractCatalogCache.TYPE_REF_MAP_STRING_OBJECT);
            }
            return new HashMap<>();
        } catch (IOException | InterruptedException | ExecutionException | TimeoutException e) {
            throw new RuntimeException("Error fetching StateMachine");
        }
    }

    private Map<Long, CatState> instantiateCatStates(final CatEntity catEntity, final List<Object> states) {
        final Map<Long, CatState> newStates = new TreeMap<>();
        states.forEach(s -> {
            final Map<String, Object> sMap = longCatalogCache.toAttributeMap(s);
            newStates.put(longCatalogCache.toLong(sMap.get("id")), instantiateCatStateFirstPass(sMap));
        });
        states.forEach(s -> {
            final Map<String, Object> sMap = longCatalogCache.toAttributeMap(s);
            instantiateCatStateSecondPass(catEntity, newStates, sMap);
        });
        return newStates;
    }

    private CatState instantiateCatStateFirstPass(final Map<String, Object> s) {
        final CatStateImpl catStateImpl = new CatStateImpl();
        final CatStateImpl newState = (CatStateImpl) addToCache(STATE_BY_ID_CACHE, longCatalogCache.toLong(s.get("id")), catStateImpl);
        if (newState == catStateImpl) {
            catStateImpl.setId(longCatalogCache.toLong(s.get("id")));
            catStateImpl.setName(longCatalogCache.toString(s.get("name")));
            catStateImpl.setI18n(instantiateI18n(longCatalogCache.toAttributeMap(s.get("i18n"))));
            return catStateImpl;
        }
        return newState;
    }

    private void instantiateCatStateSecondPass(final CatEntity catEntity,
                                               final Map<Long, CatState> states,
                                               final Map<String, Object> s) {
        final CatStateImpl catStateImpl = new CatStateImpl();
        final CatStateImpl newState = (CatStateImpl) addToCache(STATE_BY_ID_CACHE, longCatalogCache.toLong(s.get("id")), catStateImpl);
        newState.setStateTransitions(instantiateStateTransitions(states, longCatalogCache.toObjectList(s.get("statetransitions"))));
        newState.setSubStateMachine(instantiateStateMachine(catEntity, longCatalogCache.toString(s.get("substatemachine"))));
    }

    private Map<String, CatStateTransition> instantiateStateTransitions(final Map<Long, CatState> states,
                                                                        final List<Object> stateTransitions) {
        final Map<String, CatStateTransition> newStateTransitions = new HashMap<>();
        if (stateTransitions != null) {
            stateTransitions.forEach(t -> {
                final Map<String, Object> tMap = longCatalogCache.toAttributeMap(t);
                final CatStateTransitionImpl newStateTransitionsImpl = new CatStateTransitionImpl();
                final Map<String, Object> nextState = longCatalogCache.toAttributeMap(tMap.get("nextState"));
                if (nextState != null) {
                    final long nextStateId = longCatalogCache.toLong(nextState.get("id"));
                    newStateTransitionsImpl.setNextState(states.get(nextStateId));
                    newStateTransitions.put(newStateTransitionsImpl.getNextState().getName(), newStateTransitionsImpl);
                }
            });
        }
        return newStateTransitions;
    }

    private void instantiateCatEntityAttributes(final Map<String, CatEntityAttribute> catEntityAttributes,
                                                final List<Object> entityCatEntityAttributes) {
        if (entityCatEntityAttributes != null) {
            for (Object attribute : entityCatEntityAttributes) {
                final Map<String, Object> attributeMap = longCatalogCache.toAttributeMap(attribute);
                final CatEntityAttributeImpl newCatEntityAttribute = new CatEntityAttributeImpl();
                catEntityAttributes.put(longCatalogCache.toString(attributeMap.get("name")), newCatEntityAttribute);
                instantiateCatEntityAttribute(newCatEntityAttribute, attributeMap);
            }
        }
    }

    private void instantiateCatEntityAttribute(final CatEntityAttributeImpl newCatEntityAttribute,
                                               final Map<String, Object> attribute) {
        newCatEntityAttribute.setId(longCatalogCache.toLong(attribute.get("id")));
        newCatEntityAttribute.setName(longCatalogCache.toString(attribute.get("name")));
        newCatEntityAttribute.setValue(longCatalogCache.toString(attribute.get("value")));
        newCatEntityAttribute.setType(CatPrimitiveAttributeType.valueOf(longCatalogCache.toString(attribute.get("type"))));
    }

    private void instantiateCanBecome(final Map<Long, CatEntityImpl> tempByIdCache,
                                      final Map<String, CatEntity> canBecome,
                                      final List<Object> entityCanBecome) {
        if (entityCanBecome != null) {
            for (Object catEntity : entityCanBecome) {
                final String strCatEntity = catEntity.toString();
                final CatEntity newCatEntity = getCatEntityHolderFromCacheOrInstantiate(tempByIdCache, BY_ID_CACHE, getUrlId(strCatEntity));
                canBecome.put(newCatEntity.getName(), newCatEntity);
            }
        }
    }

    // -------------------
    // --- UI Contexts ---
    // -------------------

    private void instantiateUIContexts(final Map<String, UIContext> uiContexts, final List<Object> entityUIContexts) {
        if (entityUIContexts != null) {
            for (Object uiContext : entityUIContexts) {
                final Map<String, Object> uiContextMap = longCatalogCache.toAttributeMap(uiContext);
                final UIContextImpl newUIContextImpl = new UIContextImpl();
                newUIContextImpl.setAction(UIContext.Action.valueOf(String.valueOf(uiContextMap.get("action"))));
                newUIContextImpl.setName(longCatalogCache.toString(uiContextMap.get("name")));
                newUIContextImpl.setUIGroups(instantiateUIGroups(longCatalogCache.toObjectList(uiContextMap.get("uiGroups"))));
                if (uiContextMap.get("uiHeader") != null) {
                    newUIContextImpl.setUiHeader(instantiateUIHeader(uiContextMap.get("uiHeader")));
                }
                uiContexts.put(newUIContextImpl.getName().concat("_").concat(String.valueOf(newUIContextImpl.getAction())), newUIContextImpl);
            }
        }
    }

    private UIHeader instantiateUIHeader(final Object uiHeader) {
        final Map<String, Object> uiHeaderMap = longCatalogCache.toAttributeMap(uiHeader);
        final UIHeaderImpl uiHeaderImpl = new UIHeaderImpl();
        if (!uiHeaderMap.isEmpty()) {
            uiHeaderImpl.setName(longCatalogCache.toString(uiHeaderMap.get("name")));
            uiHeaderImpl.setUiElements(instantiateUIElements(longCatalogCache.toAttributeMap(uiHeaderMap.get("uiElements"))));
            uiHeaderImpl.setI18n(instantiateI18n(longCatalogCache.toAttributeMap(uiHeaderMap.get("i18n"))));
        }
        return uiHeaderImpl;
    }

    private SortedMap<Integer, UIGroup> instantiateUIGroups(final List<Object> uiGroups) {
        final TreeMap<Integer, UIGroup> uiGroupsMap = new TreeMap<>();
        if (!uiGroups.isEmpty()) {
            for (Object uiGroup : uiGroups) {
                final Map<String, Object> uiGroupMap = longCatalogCache.toAttributeMap(uiGroup);
                final UIGroupImpl uiGroupImpl = new UIGroupImpl();
                uiGroupImpl.setName(longCatalogCache.toString(uiGroupMap.get("name")));
                uiGroupImpl.setOrder(longCatalogCache.toInteger(uiGroupMap.get("order")));
                uiGroupImpl.setUISubGroups(instantiateUISubGroups(longCatalogCache.toObjectList(uiGroupMap.get("uiSubGroups"))));
                uiGroupImpl.setI18n(instantiateI18n(longCatalogCache.toAttributeMap(uiGroupMap.get("i18n"))));
                uiGroupsMap.put(uiGroupImpl.getOrder(), uiGroupImpl);
            }
        }
        return uiGroupsMap;
    }

    private SortedMap<Integer, UISubGroup> instantiateUISubGroups(final List<Object> uiSubGroups) {
        final TreeMap<Integer, UISubGroup> uiSubGroupsMap = new TreeMap<>();
        if (!uiSubGroups.isEmpty()) {
            for (Object uiSubGroup : uiSubGroups) {
                final UISubGroup uiSubGroupImpl = instantiateUISubGroup(uiSubGroup);
                uiSubGroupsMap.put(uiSubGroupImpl.getOrder(), uiSubGroupImpl);
            }
        }
        return uiSubGroupsMap;
    }

    private UISubGroup instantiateUISubGroup(final Object uiSubGroup) {
        final Map<String, Object> uiSubGroupMap = longCatalogCache.toAttributeMap(uiSubGroup);
        final UISubGroupImpl uiSubGroupImpl = new UISubGroupImpl();
        uiSubGroupImpl.setName(longCatalogCache.toString(uiSubGroupMap.get("name")));
        uiSubGroupImpl.setOrder(longCatalogCache.toInteger(uiSubGroupMap.get("order")));
        uiSubGroupImpl.setUiElements(instantiateUIElements(longCatalogCache.toAttributeMap(uiSubGroupMap.get("uiElements"))));
        uiSubGroupImpl.setI18n(instantiateI18n(longCatalogCache.toAttributeMap(uiSubGroupMap.get("i18n"))));
        return uiSubGroupImpl;
    }

    private SortedMap<Integer, Set<UIElement>> instantiateUIElements(final Map<String, Object> uiElements) {
        final TreeMap<Integer, Set<UIElement>> uiElementsMap = new TreeMap<>();
        if (!uiElements.isEmpty()) {
            uiElements.forEach((line, uiElementsLists) -> {
                final List<Object> uiElementsList = longCatalogCache.toObjectList(uiElements.get(line));
                for (Object uiElement : uiElementsList) {
                    final Map<String, Object> uiElementMap = longCatalogCache.toAttributeMap(uiElement);

                    final long id = longCatalogCache.toLong(uiElementMap.get("id"));
                    final UIElementImpl uiElementImpl = (UIElementImpl) getOrCreateUIElement(id);
                    if (uiElementImpl.getId() == null) {

                        uiElementImpl.setId(id);
                        uiElementImpl.setAttribute(longCatalogCache.toString(uiElementMap.get("attribute")));
                        uiElementImpl.setUIComponent(UIElement.UIComponent.valueOf(String.valueOf(uiElementMap.get("uiComponent"))));
                        uiElementImpl.setUiComponentWidth(UIElement.UIComponentWidth.valueOf(String.valueOf(uiElementMap.get("uiComponentWidth"))));
                        uiElementImpl.setUiElementWidth(longCatalogCache.toLong(uiElementMap.get("uiElementWidth")));
                        uiElementImpl.setEditable(longCatalogCache.toBoolean(uiElementMap.get("isEditable")));
                        uiElementImpl.setVisible(longCatalogCache.toBoolean(uiElementMap.get("isVisible")));
                        uiElementImpl.setRequired(longCatalogCache.toBoolean(uiElementMap.get("isRequired")));
                        uiElementImpl.setName(longCatalogCache.toString(uiElementMap.get("name")));

                        final Map<Long, UIEvent> uiEventsAsSubject = new HashMap<>();
                        if (uiElementImpl.getUiEventsAsSubject() == null) {
                            uiElementImpl.setUiEventsAsSubject(uiEventsAsSubject);
                            uiElementImpl.setUiEventsAsObserver(new HashMap<>());
                        } else {
                            uiEventsAsSubject.putAll(uiElementImpl.getUiEventsAsSubject());
                        }
                        uiEventsAsSubject.putAll(instantiateUIEvents(uiElementImpl, longCatalogCache.toObjectList(uiElementMap.get("uiEvents"))));

                        final int lineNumber = Integer.valueOf(line);
                        uiElementImpl.setLine(lineNumber);
                    }

                    if (uiElementsMap.containsKey(uiElementImpl.getLine())) {
                        uiElementsMap.get(uiElementImpl.getLine()).add(uiElementImpl);
                    } else {
                        final Set<UIElement> newSet = new LinkedHashSet<>();
                        newSet.add(uiElementImpl);
                        uiElementsMap.put(uiElementImpl.getLine(), newSet);
                    }
                }
            });
        }
        return uiElementsMap;
    }

    private Map<Long, UIEvent> instantiateUIEvents(final UIElement uiElement, final List<Object> uiEvents) {
        final LinkedHashMap<Long, UIEvent> uiEventsMap = new LinkedHashMap<>();
        for (Object uiEvent : uiEvents) {
            final Map<String, Object> uiEventsAsAttributeMap = longCatalogCache.toAttributeMap(uiEvent);
            uiEventsMap.putAll(instantiateUIEvent(uiEventsAsAttributeMap));
            instantiateUIEventOtherSide(uiElement, uiEventsMap);
        }
        return uiEventsMap;
    }

    private Map<Long, UIEvent> instantiateUIEvent(final Map<String, Object> entityUIElementEvents) {

        final Map<Long, UIEvent> uiEventsMap = new HashMap<>();
        final UIEventImpl uiElementEvent = new UIEventImpl();
        if (!entityUIElementEvents.isEmpty()) {

            final long idMySide = longCatalogCache.toLong(entityUIElementEvents.get("mySide"));
            final long idOtherSide = longCatalogCache.toLong(entityUIElementEvents.get("otherSide"));
            final UIElement mySide = getOrCreateUIElement(idMySide);
            final UIElement otherSide = getOrCreateUIElement(idOtherSide);
            uiElementEvent.setMySide(mySide);
            uiElementEvent.setOtherSide(otherSide);
            uiElementEvent.setId(longCatalogCache.toLong(entityUIElementEvents.get("id")));
            uiElementEvent.setCondition(longCatalogCache.toString(entityUIElementEvents.get("condition")));
            uiElementEvent.setVisible(longCatalogCache.toNullableBoolean(entityUIElementEvents.get("visible")));
            uiElementEvent.setEditable(longCatalogCache.toNullableBoolean(entityUIElementEvents.get("editable")));
            uiElementEvent.setRequired(longCatalogCache.toNullableBoolean(entityUIElementEvents.get("required")));
            uiElementEvent.setMinValue(longCatalogCache.toString(entityUIElementEvents.get("minValue")));
            uiElementEvent.setMaxValue(longCatalogCache.toString(entityUIElementEvents.get("maxValue")));
            uiElementEvent.setMinLength(longCatalogCache.toInteger(entityUIElementEvents.get("minLength")));
            uiElementEvent.setMaxLength(longCatalogCache.toInteger(entityUIElementEvents.get("maxLength")));
            uiElementEvent.setRegExpValidation(longCatalogCache.toString(entityUIElementEvents.get("regExpValidation")));
            uiElementEvent.setUnit(longCatalogCache.toString(entityUIElementEvents.get("unit")));
            uiElementEvent.setService(longCatalogCache.toString(entityUIElementEvents.get("service")));
            uiElementEvent.setValue(longCatalogCache.toString(entityUIElementEvents.get("value")));
            uiElementEvent.setConsumeCurrentEntityState(longCatalogCache.toBoolean(entityUIElementEvents.get("consumeCurrentEntityState")));
            uiElementEvent.setReset(longCatalogCache.toBoolean(entityUIElementEvents.get("reset")));

            final String behaviour = longCatalogCache.toString(entityUIElementEvents.get("changeBehaviour"));
            final UIEvent.Behaviour uiEventBehaviour = (behaviour == null) ? null : UIEvent.Behaviour.valueOf(behaviour);

            uiElementEvent.setChangeBehaviour(uiEventBehaviour);

            addToCache(UI_EVENT_BY_ID_CACHE, uiElementEvent.getId(), uiElementEvent);

            uiEventsMap.put(idOtherSide, uiElementEvent);
        }
        return uiEventsMap;
    }

    private void instantiateUIEventOtherSide(final UIElement uiElementSubject, final Map<Long, UIEvent> uiEvents) {
        uiEvents.forEach((idUIElement, uiEvent) -> {
            final UIElementImpl uiElement = (UIElementImpl) UI_ELEMENT_BY_ID_CACHE.get(idUIElement);
            final Map<Long, UIEvent> uiEventsAsObserver = new HashMap<>();
            if (uiElement.getUiEventsAsObserver() == null) {
                uiElement.setUiEventsAsObserver(uiEventsAsObserver);
                uiElement.setUiEventsAsSubject(new HashMap<>());
            } else {
                uiEventsAsObserver.putAll(uiElement.getUiEventsAsObserver());
            }
            final UIEventImpl uiEventOtherSide = new UIEventImpl();
            uiEventOtherSide.setOtherSide(uiElementSubject);
            uiEventOtherSide.setId(uiEvent.getId());
            uiEventOtherSide.setCondition(uiEvent.getCondition());
            uiEventOtherSide.setVisible(uiEvent.isVisible());
            uiEventOtherSide.setEditable(uiEvent.isEditable());
            uiEventOtherSide.setRequired(uiEvent.isRequired());
            uiEventOtherSide.setMinValue(uiEvent.getMinValue());
            uiEventOtherSide.setMaxValue(uiEvent.getMaxValue());
            uiEventOtherSide.setMinLength(uiEvent.getMinLength());
            uiEventOtherSide.setMaxLength(uiEvent.getMaxLength());
            uiEventOtherSide.setRegExpValidation(uiEvent.getRegExpValidation());
            uiEventOtherSide.setUnit(uiEvent.getUnit());
            uiEventOtherSide.setService(uiEvent.getService());
            uiEventOtherSide.setValue(uiEvent.getValue());
            uiEventOtherSide.setConsumeCurrentEntityState(uiEvent.isConsumeCurrentEntityState());
            uiEventOtherSide.setReset(uiEvent.isReset());
            uiEventOtherSide.setChangeBehaviour(uiEvent.getChangeBehaviour());
            uiEventsAsObserver.put(uiElementSubject.getId(), uiEventOtherSide);
        });
    }

    private UIElement getOrCreateUIElement(final long id) {
        final UIElement cachedUiElement = UI_ELEMENT_BY_ID_CACHE.get(id);
        if (cachedUiElement != null) {
            return cachedUiElement;
        }
        final UIElement newUiElement = new UIElementImpl();
        addToCache(UI_ELEMENT_BY_ID_CACHE, id, newUiElement);
        return newUiElement;
    }

    // ---------------------
    // --- Relationships ---
    // ---------------------

    private void instantiateRelationships(final Map<Long, CatEntityImpl> tempByIdCache,
                                          final CatEntityImpl catEntity,
                                          final List<Object> entityRelationships) {

        if (entityRelationships != null) {
            final HashMap<String, CatEntityRelationship> relationships = new HashMap<>();

            for (Object entityRelationship : entityRelationships) {
                final Map<String, Object> relationshipMap = longCatalogCache.toAttributeMap(entityRelationship);
                final String relationshipName = longCatalogCache.toString(relationshipMap.get("name"));
                final String relationshipRoleName = longCatalogCache.toString(relationshipMap.get("otherRoleName"));
                final String relationshipKey = (relationshipRoleName != null)
                        ? relationshipName + "_" + relationshipRoleName
                        : relationshipName;
                final CatEntityRelationshipImpl newRelationship;
                final CatEntityRelationshipImpl cachedRelationship = (CatEntityRelationshipImpl) RELATIONSHIPS_BY_NAME.get(relationshipKey);
                if (cachedRelationship == null) {
                    newRelationship = new CatEntityRelationshipImpl();
                    instantiateRelationship(tempByIdCache, newRelationship, relationshipMap);
                    RELATIONSHIPS_BY_NAME.put(relationshipKey, newRelationship);
                } else {
                    newRelationship = cachedRelationship;
                    if (newRelationship.getId() == 0) {
                        instantiateRelationship(tempByIdCache, newRelationship, relationshipMap);
                    }
                }
                relationships.put(relationshipKey, newRelationship);
            }
            catEntity.setRelationships(relationships);
        }
    }

    private void instantiateRelationship(final Map<Long, CatEntityImpl> tempByIdCache,
                                         final CatEntityRelationshipImpl newRelationship,
                                         final Map<String, Object> relationship) {

        newRelationship.setId(longCatalogCache.toLong(relationship.get("id")));
        newRelationship.setName(longCatalogCache.toString(relationship.get("name")));
        newRelationship.setDescription(longCatalogCache.toString(relationship.get("description")));
        newRelationship.setToValidate(longCatalogCache.toBoolean(relationship.get("toValidate")));
        newRelationship.setSideA(longCatalogCache.toBoolean(relationship.get("isSideA")));

        newRelationship.setMyMaxCardinality(longCatalogCache.toLong(relationship.get("myMaxCardinality")));
        newRelationship.setMyMinCardinality(longCatalogCache.toLong(relationship.get("myMinCardinality")));
        newRelationship.setMyRoleName(longCatalogCache.toString(relationship.get("myRoleName")));
        newRelationship.setMyExclusive("1".equals(relationship.get("myExclusive")) || (relationship.get("myExclusive") == null && relationship.get("otherExclusive") == null));
        newRelationship.setOtherExclusive("1".equals(relationship.get("otherExclusive")) || (relationship.get("myExclusive") == null && relationship.get("otherExclusive") == null));
        newRelationship.setMySide(
                getCatEntityHolderFromCacheOrInstantiate(
                        tempByIdCache,
                        BY_ID_CACHE,
                        getUrlId(longCatalogCache.toString(relationship.get("mySide")))));

        newRelationship.setOtherMaxCardinality(longCatalogCache.toLong(relationship.get("otherMaxCardinality")));
        newRelationship.setOtherMinCardinality(longCatalogCache.toLong(relationship.get("otherMinCardinality")));
        newRelationship.setOtherRoleName(longCatalogCache.toString(relationship.get("otherRoleName")));
        newRelationship.setOtherSide(
                getCatEntityHolderFromCacheOrInstantiate(
                        tempByIdCache,
                        BY_ID_CACHE,
                        getUrlId(longCatalogCache.toString(relationship.get("otherSide")))));

        newRelationship.setMyI18n(instantiateI18n(longCatalogCache.toAttributeMap(relationship.get("myI18n"))));
        newRelationship.setOtherI18n(instantiateI18n(longCatalogCache.toAttributeMap(relationship.get("otherI18n"))));

        final List<Object> specificRelationshipsList = longCatalogCache.toObjectList(relationship.get("specificRelationships"));
        final HashMap<String, CatEntityRelationship> specificRelationships = new HashMap<>();

        for (Object value : specificRelationshipsList) {
            final String id = longCatalogCache.toString(value);
            final CatEntityRelationshipImpl newRel;
            final CatEntityRelationshipImpl cachedRelationship = (CatEntityRelationshipImpl) RELATIONSHIPS_BY_NAME.get(id);
            if (cachedRelationship == null) {
                newRel = new CatEntityRelationshipImpl();
                RELATIONSHIPS_BY_NAME.put(id, newRel);
            } else {
                newRel = cachedRelationship;
            }
            specificRelationships.put(id, newRel);
        }
        newRelationship.setSpecificRelationships(specificRelationships);
    }

    private void instantiateRelationshipsToValidate(final CatEntityImpl catEntity,
                                                    final Map<String, Object> entityRelationshipsToValidate) {
        if (entityRelationshipsToValidate != null) {
            final Map<String, List<CatEntityRelationship>> relationshipsToValidate = catEntity.getRelationshipsToValidate();
            for (String relationshipToValidateName : entityRelationshipsToValidate.keySet()) {
                final List<Object> relationshipToValidateList = longCatalogCache.toObjectList(entityRelationshipsToValidate.get(relationshipToValidateName));
                final List<CatEntityRelationship> relationships = new ArrayList<>();
                for (Object relationship : relationshipToValidateList) {
                    final Map<String, Object> relationshipMap = longCatalogCache.toAttributeMap(relationship);
                    final Object otherRoleName = relationshipMap.get("otherRoleName");
                    final StringBuilder relationshipName = new StringBuilder(relationshipMap.get("name").toString());
                    if (otherRoleName != null) {
                        relationshipName.append("_").append(otherRoleName.toString());
                    }
                    final CatEntityRelationshipImpl newRelationship = (CatEntityRelationshipImpl) RELATIONSHIPS_BY_NAME.get(relationshipName.toString());
                    relationships.add(newRelationship);
                }
                relationshipsToValidate.put(relationshipToValidateName, relationships);
            }
        }
    }
}