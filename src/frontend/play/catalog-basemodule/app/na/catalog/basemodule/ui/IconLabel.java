package na.catalog.basemodule.ui;

import na.catalog.basemodule.views.html.components.iconLabel;
import play.twirl.api.Html;

public class IconLabel extends Label {

    private String iconClass;

    public String getIconClass() {
        return iconClass;
    }

    public void setIconClass(final String iconClass) {
        this.iconClass = iconClass;
    }

    @Override
    public Html render() {
        return iconLabel.render(this);
    }
}