package na.catalog.basemodule.models.components;

import na.catalog.basemodule.models.components.impl.DynamicComponentImpl;
import na.catalog.basemodule.models.components.impl.InputTextComponentImpl;
import na.catalog.basemodule.ui.WebComponent;

public final class ComponentFactory {

    private final DynamicComponentImpl dynamicComponent;

    public ComponentFactory(final DynamicComponentImpl dynamicComponent) {
        this.dynamicComponent = dynamicComponent;
    }

    public WebComponent getComponent(final ComponentDefinition definition) {
        switch (definition.getUiElement().getUIComponent()) {
            case INPUT:
                return new InputTextComponentImpl().build(definition);
            case DYNAMIC_CONTENT:
                return dynamicComponent.build(definition);
            default:
                return new InputTextComponentImpl().build(definition);
        }
    }
}