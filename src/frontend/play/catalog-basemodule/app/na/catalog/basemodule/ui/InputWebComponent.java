package na.catalog.basemodule.ui;

public abstract class InputWebComponent extends WebComponent {

    public enum Type {
        text<PERSON><PERSON>,
        textField,
        numberField,
        passwordField
    }

    private Type type;
    private I18n placeholder;

    InputWebComponent() {
    }

    public Type getType() {
        return type;
    }

    public void setType(final Type type) {
        this.type = type;
    }

    public I18n getPlaceholder() {
        return placeholder;
    }

    public void setPlaceholder(final I18n placeholder) {
        this.placeholder = placeholder;
    }

    public String getValue() {
        final Object value = super.getValue();
        return value != null ? String.valueOf(value) : "";
    }

    public void setValue(final String value) {
        super.setValue(value);
    }
}