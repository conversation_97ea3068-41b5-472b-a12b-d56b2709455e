@(element: na.catalog.basemodule.ui.Label)

@import na.naportalbase.utils.TemplateUtils

<label
    @if(element.getClasses.size > 0) {
        class="@element.getClasses.mkString(" ")"
    }
    @if(element.getTargetId != null) {
        for="@element.getTargetId"
    }
    @if(element.getDataAttributes != null) {
        @TemplateUtils.dynamicElementsAttributes(element.getDataAttributes)
    }
>@element.getI18n.getTranslation</label>