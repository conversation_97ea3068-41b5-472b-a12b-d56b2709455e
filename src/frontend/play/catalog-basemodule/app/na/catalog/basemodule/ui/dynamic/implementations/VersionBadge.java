package na.catalog.basemodule.ui.dynamic.implementations;

import na.catalog.basemodule.models.other.Badge;
import na.catalog.basemodule.services.CatEntityAPIServices;
import na.catalog.basemodule.ui.DynamicComponent;
import na.catalog.basemodule.ui.SelectWithBadge;
import na.catalog.basemodule.utils.BadgeUtils;
import play.twirl.api.Html;
import pt.ptinovacao.netwin.kernel.catalog.client.model.CatState;
import pt.ptinovacao.netwin.kernel.catalog.client.model.CatStateMachine;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Map;

@SuppressWarnings("unchecked")
public final class VersionBadge extends DynamicComponent {

    private final CatEntityAPIServices catEntityAPIServices;

    @Inject
    public VersionBadge(final CatEntityAPIServices catEntityAPIServices) {
        this.catEntityAPIServices = catEntityAPIServices;
    }

    @Override
    public void init(final Map<String, Object> parameters) {
        this.setParameters(parameters);
    }

    @Override
    public Html render() {

        final SelectWithBadge select = new SelectWithBadge();
        select.setId(this.getId());
        select.setDataField(this.getDataField());
        select.setI18n(this.getI18n());
        select.setDataAttributes(this.getParameter("directives"));
        select.setAction(this.getAction());
        select.setBadges(new ArrayList<>());

        if (this.getParameter("directives") != null) {
            this.setDataAttributes(this.getParameter("directives"));
        }

        if (this.getValue() != null) {

            final Map<String, Object> values = (Map<String, Object>) this.getValue();
            final String attributeName = getParameter("catEntityAttribute");
            final String id = values.get("id").toString();
            final String name = values.get("name").toString();
            final Long stateId = (Long) values.get(attributeName);

            final CatStateMachine states = catEntityAPIServices
                    .getCatEntityByName(getParameter("catEntityName"))
                    .getAttribute(attributeName)
                    .getAttributeType()
                    .getCatStateMachine();
            final CatState state = states.getState(stateId);
            final String stateI18n = state.getI18n(getParameter("lang"));
            final String stateName = state.getName();

            final Badge badge = new Badge();
            badge.setId(id);
            badge.setText(name);
            badge.setStateClass(BadgeUtils.badgeClassByVersionState(stateName));
            badge.setStateLabel(stateI18n);

            if (values.containsKey("valid")) {
                badge.setValid(Boolean.valueOf(values.get("valid").toString()));
            }

            select.setValue(badge);
        }
        return select.render();
    }
}