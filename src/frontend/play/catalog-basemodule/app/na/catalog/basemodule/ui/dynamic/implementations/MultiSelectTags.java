package na.catalog.basemodule.ui.dynamic.implementations;

import na.catalog.basemodule.ui.Action;
import na.catalog.basemodule.ui.DynamicComponent;
import na.catalog.basemodule.ui.I18n;
import na.catalog.basemodule.ui.MultiSelect;
import na.catalog.basemodule.utils.DynamicComponentUtils;
import na.catalog.basemodule.views.html.components.tags;
import play.twirl.api.Html;

import java.util.Map;

@SuppressWarnings("unchecked")
public final class MultiSelectTags extends DynamicComponent {

    @Override
    public void init(final Map<String, Object> parameters) {
        this.setParameters(parameters);
    }

    @Override
    public Html render() {

        final Map<String, Object> params = this.getParameters();
        final MultiSelect select = new MultiSelect();
        select.setTags(true);

        if (params.get("placeholder") != null) {
            select.setPlaceholder(new I18n().setKey(String.valueOf(params.get("placeholder"))));
        }
        if (params.get("allowClear") != null) {
            select.setAllowClear(Boolean.valueOf(String.valueOf(params.get("allowClear"))));
        }
        if (params.get("cache") != null) {
            select.setCache(Boolean.valueOf(String.valueOf(params.get("cache"))));
        }
        if (params.get("tags") != null) {
            select.setTags(Boolean.valueOf(String.valueOf(params.get("tags"))));
        }
        if (params.get("route") != null) {
            select.setQueryUrl(DynamicComponentUtils.getRoute(String.valueOf(params.get("route"))).url());
        }
        if (params.get("adapter") != null) {
            select.setDataAdapter(String.valueOf(params.get("adapter")));
        }
        if (params.get("url") != null) {
            select.setQueryUrl(String.valueOf(params.get("url")));
        }
        if (params.get("newTagLabel") != null) {
            select.setNewTagLabel(new I18n().setKey(String.valueOf(params.get("newTagLabel"))));
        }
        if (params.get("dataField") != null) {
            this.setDataField(String.valueOf(params.get("dataField")));
        }
        if (params.get("directives") != null) {
            this.setDataAttributes((Map<String, String>) params.get("directives"));
        }

        select.setId(this.getId());
        select.setI18n(this.getI18n());
        select.setDataField(this.getDataField());
        select.setDataAttributes(this.getDataAttributes());
        select.setValue(this.getValue());
        select.setAction(this.getAction());
        if (select.getAction() == Action.SEARCH) {
            select.addClass("multi-select-box--tag-search");
        }
        return tags.render(select);
    }
}