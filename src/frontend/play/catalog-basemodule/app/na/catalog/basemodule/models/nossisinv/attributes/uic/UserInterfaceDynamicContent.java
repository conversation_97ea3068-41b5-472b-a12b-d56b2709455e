package na.catalog.basemodule.models.nossisinv.attributes.uic;

import play.twirl.api.Html;

import javax.validation.constraints.NotNull;

public class UserInterfaceDynamicContent extends UserInterfaceComponent {

    private Html html;

    public UserInterfaceDynamicContent() {
    }

    public UserInterfaceDynamicContent(@NotNull Html html) {
        this.html = html;
    }

    public Html getHtml() {
        return html;
    }

    public void setHtml(@NotNull Html html) {
        this.html = html;
    }
}
