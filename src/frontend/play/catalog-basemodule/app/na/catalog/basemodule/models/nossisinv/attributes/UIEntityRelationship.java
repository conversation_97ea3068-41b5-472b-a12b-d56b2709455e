package na.catalog.basemodule.models.nossisinv.attributes;

import pt.ptinovacao.netwin.kernel.catalog.client.model.CatEntityRelationship;
import pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement;

import javax.validation.constraints.NotNull;

public class UIEntityRelationship<T> extends UICatAttribute<T> {

    private final CatEntityRelationship entityRelationship;

    public UIEntityRelationship(@NotNull String webLabel,
                                @NotNull T webValue,
                                @NotNull CatEntityRelationship entityRelationship,
                                @NotNull UIElement.UIComponent uiComponent) {

        super(webLabel, webValue, uiComponent);
        this.entityRelationship = entityRelationship;
    }

    public CatEntityRelationship getEntityRelationship() {
        return entityRelationship;
    }
}
