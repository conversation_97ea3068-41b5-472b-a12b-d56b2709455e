package na.catalog.basemodule.ui;

import na.catalog.basemodule.views.html.components.inputText;
import play.twirl.api.Html;

import java.util.HashMap;
import java.util.Map;

public class InputText extends InputWebComponent {

    public InputText() {
        this.addClass("form-control");
    }

    public Map<String, String> getDOMAttributes() {
        final Map<String, String> domAttributes = new HashMap<>(getDataAttributes());
        domAttributes.put("id", getId());
        domAttributes.put("type", this.isHidden() ? "hidden" : "text");

        if(getDataField() != null && !getDataField().isEmpty()){
            domAttributes.put("data-field", getDataField());
        }

        if(getClasses() != null && !getClasses().isEmpty()){
            domAttributes.put("class", String.join(" ", getClasses()));
        }

        if (getPlaceholder() != null) {
            domAttributes.put("placeholder", getPlaceholder().getTranslation());
        }

        if (getValue() != null && !getValue().isEmpty()) {
            domAttributes.put("value", getValue());
        }

        if (isRequired()) {
            domAttributes.put("required", null);
            domAttributes.put("data-na-portal-catalog-basemodule-required", null);
        }

        return domAttributes;
    }

    @Override
    public Html render() {
        return inputText.render(this);
    }
}