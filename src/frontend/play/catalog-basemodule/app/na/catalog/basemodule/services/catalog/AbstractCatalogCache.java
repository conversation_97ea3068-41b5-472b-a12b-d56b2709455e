package na.catalog.basemodule.services.catalog;

import com.fasterxml.jackson.core.type.TypeReference;
import na.catalog.basemodule.settings.CatalogBaseModuleConstants;
import pt.ptinovacao.netwin.kernel.catalog.client.model.implementation.CatEntityImpl;

import javax.inject.Inject;
import java.security.InvalidParameterException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public abstract class AbstractCatalogCache<T> {

    static final TypeReference<HashMap<String, Object>> TYPE_REF_MAP_STRING_OBJECT = new TypeReference<HashMap<String, Object>>() {
    };

    public final ServicesHelper servicesHelper;

    @Inject
    public AbstractCatalogCache(final ServicesHelper servicesHelper) {
        this.servicesHelper = servicesHelper;
    }

    protected Long toLong(final Object o) {
        if (o instanceof Integer) {
            return toInteger(o).longValue();
        }
        return (Long) o;
    }

    protected String toString(final Object o) {
        return (String) o;
    }

    protected Boolean toBoolean(final Object o) {
        if (o == null) {
            return false;
        }
        return (Boolean) o;
    }

    protected Boolean toNullableBoolean(final Object o) {
        if (o == null) {
            return null;
        }
        return (Boolean) o;
    }

    protected Float toFloat(final Object o) {
        if (o == null) {
            return null;
        }
        return ((Double) o).floatValue();
    }

    protected Double toDouble(final Object o) {
        if (o == null) {
            return null;
        }
        return (Double) o;
    }

    protected Integer toInteger(final Object o) {
        return (Integer) o;
    }

    protected Date toDate(final Object o) {
        if (o == null) {
            return null;
        }
        return toDate(toString(o), CatalogBaseModuleConstants.ISO_DATE_TIME);
    }

    protected List<Object> toObjectList(final Object o) {
        return (List<Object>) o;
    }

    protected Map<String, Object> toAttributeMap(final Object o) {
        return (Map<String, Object>) o;
    }

    protected Set<Object> toAttributeSet(final Object o) {
        return (Set<Object>) o;
    }

    protected abstract Map<String, Object> readCatEntity(final Map<T, CatEntityImpl> cache, final T key);

    protected abstract void throwCatEntityNotFoundException(final T key);

    // *****************************************************************************************************************
    // *** Utils
    // *****************************************************************************************************************

    private Date toDate(final String strDate, final String format) {
        return toTimeZoneDate(strDate, format, Locale.getDefault());
    }

    private Date toTimeZoneDate(final String utcStrDate, final String format, final Locale locale) {
        try {
            final DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS", Locale.getDefault());
            final Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
            calendar.setTime(sdf.parse(utcStrDate.split("Z")[0]));
            calendar.add(Calendar.MINUTE, -getDateOffSet(format, locale));
            return calendar.getTime();
        } catch (ParseException e) {
            throw new InvalidParameterException("Unable to parse UTC date: " + utcStrDate);
        }
    }

    private int getDateOffSet(final String format, final Locale locale) {
        try {
            final SimpleDateFormat sdf = new SimpleDateFormat(format, locale);
            final Date newDate = sdf.parse(sdf.format(new Date()));
            // TODO replace deprecated method
            return newDate.getTimezoneOffset();
        } catch (ParseException e) {
            throw new InvalidParameterException("Unable to parse date with format: " + format);
        }
    }
}