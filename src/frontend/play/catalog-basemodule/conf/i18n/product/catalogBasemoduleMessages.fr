na.portal.catalog.basemodule.valid.option.label             = Valide
na.portal.catalog.basemodule.invalid.option.label           = Invalide

# Validations

na.portal.catalog.basemodule.validations.mandatory          = champs requis
na.portal.catalog.basemodule.validations.invalid.pattern    = Format invalide
na.portal.catalog.basemodule.validations.numeric            = Champ numérique
na.portal.catalog.basemodule.validations.integer            = Champ entier
na.portal.catalog.basemodule.validations.max.characters     = Limite maximale de {0} caractères
na.portal.catalog.basemodule.validations.min.characters     = Limite minimale de {0} caractères