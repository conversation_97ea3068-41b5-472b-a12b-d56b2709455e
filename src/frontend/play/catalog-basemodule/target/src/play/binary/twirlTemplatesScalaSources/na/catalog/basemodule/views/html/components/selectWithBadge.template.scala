
package na.catalog.basemodule.views.html.components

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object selectWithBadge extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[na.catalog.basemodule.ui.SelectWithBadge,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(element: na.catalog.basemodule.ui.SelectWithBadge):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*3.2*/import na.catalog.basemodule.ui.Action
/*4.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*1.53*/("""

"""),format.raw/*5.1*/("""
"""),_display_(/*6.2*/if(Action.VIEW.equals(element.getAction))/*6.43*/ {_display_(Seq[Any](format.raw/*6.45*/("""
    """),format.raw/*7.5*/("""<span id=""""),_display_(/*7.16*/element/*7.23*/.getId),format.raw/*7.29*/("""">
    """),_display_(/*8.6*/if(element.getBadge != null)/*8.34*/ {_display_(Seq[Any](format.raw/*8.36*/("""
        """),_display_(/*9.10*/if(!element.getBadge.isValid)/*9.39*/{_display_(Seq[Any](format.raw/*9.40*/("""
            """),format.raw/*10.13*/("""<i class="fx-alert-icon fx-alert-icon-warning fx-icon" title="">&nbsp;</i>
        """)))}),format.raw/*11.10*/("""
        """),format.raw/*12.9*/("""<span id="name">
            """),_display_(/*13.14*/element/*13.21*/.getBadge.getText),format.raw/*13.38*/("""
        """),format.raw/*14.9*/("""</span>
        <span>
            <span class=""""),_display_(/*16.27*/element/*16.34*/.getBadge.getStateClass),format.raw/*16.57*/("""">"""),_display_(/*16.60*/element/*16.67*/.getBadge.getStateLabel),format.raw/*16.90*/("""</span>
        </span>
    """)))}),format.raw/*18.6*/("""
    """),format.raw/*19.5*/("""</span>
""")))}/*20.3*/else/*20.8*/{_display_(Seq[Any](format.raw/*20.9*/("""
    """),format.raw/*21.5*/("""<select
    id="selectBadge"
    class="form-control"
    """),_display_(/*24.6*/{"data-na-portal-catalog-basemodule-required".when(element.isRequired)}),format.raw/*24.77*/("""
    """),_display_(/*25.6*/TemplateUtils/*25.19*/.dynamicElementsAttributes(element.getDataAttributes)),format.raw/*25.72*/("""
    """),format.raw/*26.5*/(""">
        """),_display_(/*27.10*/if(Option(element.getValue).isEmpty)/*27.46*/{_display_(Seq[Any](format.raw/*27.47*/("""
            """),format.raw/*28.13*/("""<option></option>
        """)))}),format.raw/*29.10*/("""

        """),_display_(/*31.10*/if(element.getBadge != null)/*31.38*/ {_display_(Seq[Any](format.raw/*31.40*/("""
            """),format.raw/*32.13*/("""<option
            class="badge-option"
            data-badge-id=""""),_display_(/*34.29*/element/*34.36*/.getBadge.getId),format.raw/*34.51*/(""""
            data-badge-state-label=""""),_display_(/*35.38*/element/*35.45*/.getBadge.getStateLabel),format.raw/*35.68*/(""""
            data-badge-text=""""),_display_(/*36.31*/element/*36.38*/.getBadge.getText),format.raw/*36.55*/(""""
            data-badge-state-class=""""),_display_(/*37.38*/element/*37.45*/.getBadge.getStateClass),format.raw/*37.68*/(""""
            data-json=""""),_display_(/*38.25*/element/*38.32*/.getBadge.getJsonData),format.raw/*38.53*/(""""
            value=""""),_display_(/*39.21*/element/*39.28*/.getBadge.getText),format.raw/*39.45*/(""""
            >"""),_display_(/*40.15*/element/*40.22*/.getBadge.getText),format.raw/*40.39*/("""</option>
        """)))}),format.raw/*41.10*/("""
        """),_display_(/*42.10*/for(badge <- element.getBadges) yield /*42.41*/ {_display_(Seq[Any](format.raw/*42.43*/("""
            """),format.raw/*43.13*/("""<option
            class="badge-option"
            data-badge-id=""""),_display_(/*45.29*/badge/*45.34*/.getId),format.raw/*45.40*/(""""
            data-badge-state-label=""""),_display_(/*46.38*/badge/*46.43*/.getStateLabel),format.raw/*46.57*/(""""
            data-badge-text=""""),_display_(/*47.31*/badge/*47.36*/.getText),format.raw/*47.44*/(""""
            data-badge-state-class=""""),_display_(/*48.38*/badge/*48.43*/.getStateClass),format.raw/*48.57*/(""""
            data-json=""""),_display_(/*49.25*/badge/*49.30*/.getJsonData),format.raw/*49.42*/(""""
            value=""""),_display_(/*50.21*/badge/*50.26*/.getText),format.raw/*50.34*/(""""
            >"""),_display_(/*51.15*/badge/*51.20*/.getText),format.raw/*51.28*/("""</option>
        """)))}),format.raw/*52.10*/("""
    """),format.raw/*53.5*/("""</select>
""")))}))
      }
    }
  }

  def render(element:na.catalog.basemodule.ui.SelectWithBadge): play.twirl.api.HtmlFormat.Appendable = apply(element)

  def f:((na.catalog.basemodule.ui.SelectWithBadge) => play.twirl.api.HtmlFormat.Appendable) = (element) => apply(element)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:30:51 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/catalog-basemodule/target/TwirlSource/na/catalog/basemodule/views/components/selectWithBadge.scala.html
                  HASH: 48e0c107c76a6ffb982dd3658b3d896aacfc7673
                  MATRIX: 1025->1|1149->55|1195->95|1267->52|1295->138|1322->140|1371->181|1410->183|1441->188|1478->199|1493->206|1519->212|1552->220|1588->248|1627->250|1663->260|1700->289|1738->290|1779->303|1894->387|1930->396|1987->426|2003->433|2041->450|2077->459|2153->508|2169->515|2213->538|2243->541|2259->548|2303->571|2362->600|2394->605|2421->615|2433->620|2471->621|2503->626|2588->685|2680->756|2712->762|2734->775|2808->828|2840->833|2878->844|2923->880|2962->881|3003->894|3061->921|3099->932|3136->960|3176->962|3217->975|3313->1044|3329->1051|3365->1066|3431->1105|3447->1112|3491->1135|3550->1167|3566->1174|3604->1191|3670->1230|3686->1237|3730->1260|3783->1286|3799->1293|3841->1314|3890->1336|3906->1343|3944->1360|3987->1376|4003->1383|4041->1400|4091->1419|4128->1429|4175->1460|4215->1462|4256->1475|4352->1544|4366->1549|4393->1555|4459->1594|4473->1599|4508->1613|4567->1645|4581->1650|4610->1658|4676->1697|4690->1702|4725->1716|4778->1742|4792->1747|4825->1759|4874->1781|4888->1786|4917->1794|4960->1810|4974->1815|5003->1823|5053->1842|5085->1847
                  LINES: 28->1|31->3|32->4|35->1|37->5|38->6|38->6|38->6|39->7|39->7|39->7|39->7|40->8|40->8|40->8|41->9|41->9|41->9|42->10|43->11|44->12|45->13|45->13|45->13|46->14|48->16|48->16|48->16|48->16|48->16|48->16|50->18|51->19|52->20|52->20|52->20|53->21|56->24|56->24|57->25|57->25|57->25|58->26|59->27|59->27|59->27|60->28|61->29|63->31|63->31|63->31|64->32|66->34|66->34|66->34|67->35|67->35|67->35|68->36|68->36|68->36|69->37|69->37|69->37|70->38|70->38|70->38|71->39|71->39|71->39|72->40|72->40|72->40|73->41|74->42|74->42|74->42|75->43|77->45|77->45|77->45|78->46|78->46|78->46|79->47|79->47|79->47|80->48|80->48|80->48|81->49|81->49|81->49|82->50|82->50|82->50|83->51|83->51|83->51|84->52|85->53
                  -- GENERATED --
              */
          