
package na.catalog.basemodule.views.html.components

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object badge extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[na.catalog.basemodule.ui.BadgeComponent,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(badge: na.catalog.basemodule.ui.BadgeComponent):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.50*/("""

"""),format.raw/*3.1*/("""<span id=""""),_display_(/*3.12*/badge/*3.17*/.getId),format.raw/*3.23*/("""" class=""""),_display_(/*3.33*/badge/*3.38*/.getStateClass),format.raw/*3.52*/("""">"""),_display_(/*3.55*/badge/*3.60*/.getI18n.getTranslation),format.raw/*3.83*/("""</span>"""))
      }
    }
  }

  def render(badge:na.catalog.basemodule.ui.BadgeComponent): play.twirl.api.HtmlFormat.Appendable = apply(badge)

  def f:((na.catalog.basemodule.ui.BadgeComponent) => play.twirl.api.HtmlFormat.Appendable) = (badge) => apply(badge)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:30:51 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/catalog-basemodule/target/TwirlSource/na/catalog/basemodule/views/components/badge.scala.html
                  HASH: 08b061f8f8674ef6ceff52b0d6e7c4b46ab15869
                  MATRIX: 1014->1|1157->49|1185->51|1222->62|1235->67|1261->73|1297->83|1310->88|1344->102|1373->105|1386->110|1429->133
                  LINES: 28->1|33->1|35->3|35->3|35->3|35->3|35->3|35->3|35->3|35->3|35->3|35->3
                  -- GENERATED --
              */
          