
package na.catalog.basemodule.views.html.components

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object selectbox extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[na.catalog.basemodule.ui.SelectBox,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(element: na.catalog.basemodule.ui.SelectBox):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*3.2*/import na.catalog.basemodule.ui.Action
/*4.2*/import na.naportalbase.utils.TemplateUtils
/*5.2*/import play.twirl.api.HtmlFormat

def /*7.2*/viewValue/*7.11*/() = {{
    var value: String = element.getValue.asInstanceOf[String]
    if(value.isEmpty) {
        value = "--"
    }
    value
}};def /*15.2*/selected/*15.10*/(value: String) = {{
    if(value != null && value.equals(element.getValue)) {
        "selected"
    } else ""
}};def /*21.2*/dataOfOption/*21.14*/(value: String) = {{
    if(value != null) {
        Html(s"""data-of-option="${HtmlFormat.escape(value)}" """)
    } else ""
}};
Seq[Any](format.raw/*1.47*/("""

"""),format.raw/*6.1*/("""
"""),format.raw/*13.2*/("""

"""),format.raw/*19.2*/("""

"""),format.raw/*25.2*/("""

"""),_display_(/*27.2*/if(Action.VIEW.equals(element.getAction))/*27.43*/ {_display_(Seq[Any](format.raw/*27.45*/("""
    """),format.raw/*28.5*/("""<span id=""""),_display_(/*28.16*/element/*28.23*/.getId),format.raw/*28.29*/("""">"""),_display_(/*28.32*/viewValue),format.raw/*28.41*/("""</span>
""")))}/*29.3*/else/*29.8*/{_display_(Seq[Any](format.raw/*29.9*/("""
    """),format.raw/*30.5*/("""<"""),_display_(/*30.7*/element/*30.14*/.getTagName),format.raw/*30.25*/(""" """),_display_(/*30.27*/TemplateUtils/*30.40*/.dynamicElementsAttributes(element.getDOMAttributes)),format.raw/*30.92*/(""">
        """),_display_(/*31.10*/for(option <- element.getOptions) yield /*31.43*/ {_display_(Seq[Any](format.raw/*31.45*/("""
            """),format.raw/*32.13*/("""<option value=""""),_display_(/*32.29*/option/*32.35*/.value),format.raw/*32.41*/("""" """),_display_(/*32.44*/selected(option.value)),format.raw/*32.66*/(""" """),_display_(/*32.68*/dataOfOption(option.dataOfOption)),format.raw/*32.101*/(""">"""),_display_(/*32.103*/option/*32.109*/.text),format.raw/*32.114*/("""</option>
        """)))}),format.raw/*33.10*/("""
    """),format.raw/*34.5*/("""</"""),_display_(/*34.8*/element/*34.15*/.getTagName),format.raw/*34.26*/(""">
""")))}))
      }
    }
  }

  def render(element:na.catalog.basemodule.ui.SelectBox): play.twirl.api.HtmlFormat.Appendable = apply(element)

  def f:((na.catalog.basemodule.ui.SelectBox) => play.twirl.api.HtmlFormat.Appendable) = (element) => apply(element)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:06 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/catalog-basemodule/target/TwirlSource/na/catalog/basemodule/views/components/selectbox.scala.html
                  HASH: d32b9ae6ae5487117158d56454df40e013b8b54c
                  MATRIX: 1013->1|1131->49|1177->89|1227->133|1272->168|1289->177|1435->312|1452->320|1579->436|1600->448|1757->46|1785->166|1813->309|1842->433|1871->575|1900->578|1950->619|1990->621|2022->626|2060->637|2076->644|2103->650|2133->653|2163->662|2190->672|2202->677|2240->678|2272->683|2300->685|2316->692|2348->703|2377->705|2399->718|2472->770|2510->781|2559->814|2599->816|2640->829|2683->845|2698->851|2725->857|2755->860|2798->882|2827->884|2882->917|2912->919|2928->925|2955->930|3005->949|3037->954|3066->957|3082->964|3114->975
                  LINES: 28->1|31->3|32->4|33->5|35->7|35->7|41->15|41->15|45->21|45->21|50->1|52->6|53->13|55->19|57->25|59->27|59->27|59->27|60->28|60->28|60->28|60->28|60->28|60->28|61->29|61->29|61->29|62->30|62->30|62->30|62->30|62->30|62->30|62->30|63->31|63->31|63->31|64->32|64->32|64->32|64->32|64->32|64->32|64->32|64->32|64->32|64->32|64->32|65->33|66->34|66->34|66->34|66->34
                  -- GENERATED --
              */
          