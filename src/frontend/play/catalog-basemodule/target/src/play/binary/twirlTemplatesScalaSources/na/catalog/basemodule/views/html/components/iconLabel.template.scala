
package na.catalog.basemodule.views.html.components

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object iconLabel extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[na.catalog.basemodule.ui.IconLabel,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(element: na.catalog.basemodule.ui.IconLabel):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.47*/("""

"""),format.raw/*3.1*/("""<span id=""""),_display_(/*3.12*/element/*3.19*/.getTargetId),format.raw/*3.31*/(""""><i class=""""),_display_(/*3.44*/element/*3.51*/.getIconClass),format.raw/*3.64*/(""""></i> """),_display_(/*3.72*/element/*3.79*/.getI18n.getTranslation),format.raw/*3.102*/("""</span>"""))
      }
    }
  }

  def render(element:na.catalog.basemodule.ui.IconLabel): play.twirl.api.HtmlFormat.Appendable = apply(element)

  def f:((na.catalog.basemodule.ui.IconLabel) => play.twirl.api.HtmlFormat.Appendable) = (element) => apply(element)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:30:51 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/catalog-basemodule/target/TwirlSource/na/catalog/basemodule/views/components/iconLabel.scala.html
                  HASH: f9a2456b254d357718f7c93f934e7b9d3fcb5f22
                  MATRIX: 1013->1|1153->46|1181->48|1218->59|1233->66|1265->78|1304->91|1319->98|1352->111|1386->119|1401->126|1445->149
                  LINES: 28->1|33->1|35->3|35->3|35->3|35->3|35->3|35->3|35->3|35->3|35->3|35->3
                  -- GENERATED --
              */
          