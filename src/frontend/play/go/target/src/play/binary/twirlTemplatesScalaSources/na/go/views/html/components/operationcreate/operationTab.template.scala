
package na.go.views.html.components.operationcreate

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping
/*3.2*/import play.libs.Json;
/*4.2*/import na.naportalbase.views.html.components.wizard.wizardContentComp;

object operationTab extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[List[Mapping],List[Mapping],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*6.2*/(requiredMappings: List[Mapping], optionalMappings: List[Mapping]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*8.2*/headContent/*8.13*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*8.17*/("""
    """),_display_(/*9.6*/configureOptionalFields(optionalMappings)),format.raw/*9.47*/("""
    """),format.raw/*10.5*/("""<p class="fx-required">
        <abbr title=""""),_display_(/*11.23*/i18n("na.basemodule.validations.mandatory")),format.raw/*11.66*/(""""></abbr>
        """),_display_(/*12.10*/i18n("na.portal.go.operation.create.legend.requiredfields.label")),format.raw/*12.75*/("""
    """),format.raw/*13.5*/("""</p>
    <br>
""")))};def /*17.2*/blockContent/*17.14*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*17.18*/("""
    """),format.raw/*18.5*/("""<form class="form-horizontal" data-na-portal-go-dynamic-form data-na-portal-go-dynamic-form-mappings=""""),_display_(/*18.108*/Json/*18.112*/.toJson(requiredMappings)),format.raw/*18.137*/(""""></form>
""")))};
Seq[Any](format.raw/*6.68*/("""

"""),format.raw/*15.2*/("""

"""),format.raw/*19.2*/("""

"""),_display_(/*21.2*/wizardContentComp/*21.19*/.render(headContent,blockContent)))
      }
    }
  }

  def render(requiredMappings:List[Mapping],optionalMappings:List[Mapping]): play.twirl.api.HtmlFormat.Appendable = apply(requiredMappings,optionalMappings)

  def f:((List[Mapping],List[Mapping]) => play.twirl.api.HtmlFormat.Appendable) = (requiredMappings,optionalMappings) => apply(requiredMappings,optionalMappings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:12 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/operationcreate/operationTab.scala.html
                  HASH: 1e1290e2bc3c164fc22e6b0f0aa074a7eed0b673
                  MATRIX: 684->1|730->41|810->115|840->139|1243->212|1387->281|1406->292|1486->296|1517->302|1578->343|1610->348|1683->394|1747->437|1793->456|1879->521|1911->526|1949->544|1970->556|2051->560|2083->565|2214->668|2228->672|2275->697|2325->278|2354->541|2383->708|2412->711|2438->728
                  LINES: 24->1|25->2|26->3|27->4|32->6|36->8|36->8|38->8|39->9|39->9|40->10|41->11|41->11|42->12|42->12|43->13|45->17|45->17|47->17|48->18|48->18|48->18|48->18|50->6|52->15|54->19|56->21|56->21
                  -- GENERATED --
              */
          