
package na.go.views.html.skeletons

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.go.views.html.imports
/*2.2*/import na.naportalbase.views.html.skeletons.root

object mainSkel extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[String,Html,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*4.2*/(title: String = "")(content: Html):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*6.2*/scripts/*6.9*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*6.13*/("""
"""),_display_(/*7.2*/imports/*7.9*/.scripts.render()),format.raw/*7.26*/("""
""")))};def /*10.2*/body/*10.6*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*10.10*/("""
    """),format.raw/*11.5*/("""<div id="go" class="page page--go fx-full-height inherit" data-na-portal-page-container >
    """),_display_(/*12.6*/content),format.raw/*12.13*/("""
    """),format.raw/*13.5*/("""</div>
""")))};
Seq[Any](format.raw/*4.37*/("""

"""),format.raw/*8.2*/("""

"""),format.raw/*14.2*/("""

"""),_display_(/*16.2*/root/*16.6*/.render(title, body, scripts)),format.raw/*16.35*/("""
"""))
      }
    }
  }

  def render(title:String,content:Html): play.twirl.api.HtmlFormat.Appendable = apply(title)(content)

  def f:((String) => (Html) => play.twirl.api.HtmlFormat.Appendable) = (title) => (content) => apply(title)(content)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:12 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/skeletons/mainSkel.scala.html
                  HASH: 663d9af2cb7a9d06eac51d9869d465b4188f7cc2
                  MATRIX: 667->1|706->34|1067->85|1180->123|1194->130|1274->134|1301->136|1315->143|1352->160|1377->165|1389->169|1470->173|1502->178|1623->273|1651->280|1683->285|1730->120|1758->162|1787->293|1816->296|1828->300|1878->329
                  LINES: 24->1|25->2|30->4|34->6|34->6|36->6|37->7|37->7|37->7|38->10|38->10|40->10|41->11|42->12|42->12|43->13|45->4|47->8|49->14|51->16|51->16|51->16
                  -- GENERATED --
              */
          