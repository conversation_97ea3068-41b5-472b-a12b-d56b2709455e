
package na.go.views.html.components.common.modals

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import scala.collection.mutable
/*2.2*/import scala.collection.immutable
/*3.2*/import na.naportalbase.views.tags.i18n
/*4.2*/import na.go.settings.GoConstants
/*5.2*/import na.go.controllers.routes.Search
/*6.2*/import na.naportalbase.views.html.components.selectsComp.selectComp
/*7.2*/import na.naportalbase.views.html.skeletons.search.horizontalFormSkel

object modalDeleteFilter extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*9.2*/(filterName: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*47.2*/form/*47.6*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*47.10*/("""
    """),format.raw/*48.5*/("""<div class="form-group">
        <label class="control-label col-sm-3">"""),_display_(/*49.48*/i18n("na.modals.filters.select.label")),format.raw/*49.86*/("""</label>
        <div class="col-sm-9">
            """),_display_(/*51.14*/selectComp/*51.24*/.render(immutable.HashMap(filterSelectAttributes.toList: _*), filterName, filterSelectInitialOptionAttributes, null)),format.raw/*51.140*/("""
        """),format.raw/*52.9*/("""</div>
    </div>
""")))};def /*17.2*/filterSelectAttributes/*17.24*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("class","form-control input-sm")
    map.put("name", "deleteRfsFilters")
    map.put("data-na-portal-select-box",null)
    map.put("value", filterName)
    map.put("style", "width: 200px")
    map.put("id", "deleteRfsFilters")
    map.put("data-is-dynamic",null)
    map.put("data-context","filters")
    map.put("data-url",""+Search.getFilterNamesForSelect(GoConstants.OPERATION_SEARCH_CONTEXT))

    map
}};def /*32.2*/filterSelectInitialOptionAttributes/*32.37*/ = {{
    var map = immutable.HashMap[String, String](
        "value" -> filterName,
        "selected" -> ""
    )
    map
}};def /*40.2*/formAttributes/*40.16*/ = {{
    var map = mutable.HashMap[String,String]()
    map.put("data-ng-controller","NaPortalGoOperationDeleteModalController as rfsModalCtrl")

    map
}};
Seq[Any](format.raw/*9.22*/("""

"""),format.raw/*11.1*/("""<script>
    //https://github.com/ivaynberg/select2/issues/1436
    $.fn.modal.Constructor.prototype.enforceFocus = function() """),format.raw/*13.64*/("""{"""),format.raw/*13.65*/("""}"""),format.raw/*13.66*/(""";
</script>

    <!--FILTER SELECT-->
"""),format.raw/*30.2*/("""

"""),format.raw/*38.2*/("""

"""),format.raw/*45.2*/("""

"""),format.raw/*54.2*/("""

"""),format.raw/*56.1*/("""<div class="fx-confirm-wrapper">
    <div class="alert alert-warning">
        <div class="fx-alert-icon"></div>
        <div class="fx-alert-message">
            <p>
                """),_display_(/*61.18*/i18n("na.modal.filters.info.deletefilter")),format.raw/*61.60*/("""
            """),format.raw/*62.13*/("""</p>
        </div>
    </div>
</div>

"""),_display_(/*67.2*/horizontalFormSkel/*67.20*/.render(formAttributes,form)))
      }
    }
  }

  def render(filterName:String): play.twirl.api.HtmlFormat.Appendable = apply(filterName)

  def f:((String) => play.twirl.api.HtmlFormat.Appendable) = (filterName) => apply(filterName)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:21 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/common/modals/modalDeleteFilter.scala.html
                  HASH: fd1eec180ad56c163f727b3c3ff74c58495f8a2a
                  MATRIX: 682->1|721->34|762->69|808->109|849->144|895->184|970->253|1356->325|1455->1350|1467->1354|1548->1358|1580->1363|1679->1435|1738->1473|1818->1526|1837->1536|1975->1652|2011->1661|2053->515|2084->537|2571->1013|2615->1048|2755->1177|2778->1191|2964->345|2993->347|3148->474|3177->475|3206->476|3271->1010|3300->1174|3329->1347|3358->1680|3387->1682|3599->1867|3662->1909|3703->1922|3769->1962|3796->1980
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|35->9|39->47|39->47|41->47|42->48|43->49|43->49|45->51|45->51|45->51|46->52|48->17|48->17|61->32|61->32|67->40|67->40|73->9|75->11|77->13|77->13|77->13|81->30|83->38|85->45|87->54|89->56|94->61|94->61|95->62|100->67|100->67
                  -- GENERATED --
              */
          