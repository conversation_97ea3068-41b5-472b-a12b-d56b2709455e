
package na.go.views.html.components.dynamicForm.fields

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import scala.collection.mutable
/*2.2*/import scala.collection.immutable
/*3.2*/import na.naportalbase.settings.BaseMappings
/*4.2*/import na.naportalbase.views.html.components.selectsComp.dynamicSelectOptionComp
/*5.2*/import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.components.SelectBox
/*6.2*/import na.naportalbase.views.html.components.selectsComp.selectComp
/*7.2*/import na.naportalbase.views.tags.i18n

object selectBoxWithSwitch extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*9.2*/(mapping: pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*11.2*/selectBox/*11.11*/ = {{mapping.getComponent.asInstanceOf[SelectBox]}};def /*12.2*/selectId/*12.10*/ = {{mapping.getTemplateMapping}};def /*13.2*/selectAttributes/*13.18*/ = {{

	var map = mutable.HashMap[String, String](
		"data-ng-model" -> ("model[\""+selectId+"\"]"),
		"data-model" -> selectId,
		"data-na-portal-select-box" -> null,
		"data-na-portal-go-dynamic-form-select-box" -> null,
		"name" -> selectId,
		"value" -> "",
		"id" -> selectId,
		"data-component" -> BaseMappings.dualMapper.valueToTree(selectBox),
		"class" -> "form-control input-sm"
	)
	if(selectBox.getRequired){
		map.put("data-na-portal-required",null)
	}
	if(!selectBox.getEditable){
		map.put("disabled", null)
	}
	immutable.HashMap(map.toSeq:_*)

}};def /*37.2*/dynamicOptions/*37.16*/ = {{
	var stringBuilder = new StringBuilder
	for(option <- selectBox.getOptions){
		if (option.getVisible) {
			val selectOptionIndex = String.valueOf(option.getIndex)
			var optionAttributes = immutable.HashMap[String, String](
				"id" -> selectOptionIndex,
				"text" -> option.getLabel,
				"value" -> option.getValue,
				"data-test" -> (selectOptionIndex.equals(selectBox.getDefaultValue) + "")
			)

			if(selectOptionIndex.equals(selectBox.getDefaultValue)) {
				optionAttributes = immutable.HashMap[String, String](
					"id" -> selectOptionIndex,
					"text" -> option.getLabel,
					"value" -> option.getValue,
					"selected" -> null
				)
			}
			stringBuilder.append(dynamicSelectOptionComp.render(option.getValue, optionAttributes))
		}
	}
	Html(stringBuilder.toString())
}};
Seq[Any](format.raw/*9.78*/("""

"""),format.raw/*11.61*/("""
"""),format.raw/*12.42*/("""
"""),format.raw/*34.2*/("""


"""),format.raw/*61.2*/("""

"""),format.raw/*63.1*/("""<div class="form-group">
	<label for=""""),_display_(/*64.15*/mapping/*64.22*/.getLabel),format.raw/*64.31*/("""" class="col-sm-2 control-label """),_display_(/*64.64*/if(mapping.getComponent.getRequired)/*64.100*/{_display_(Seq[Any](format.raw/*64.101*/("""fx-required""")))}),format.raw/*64.113*/("""" title=""""),_display_(/*64.123*/mapping/*64.130*/.getDescription),format.raw/*64.145*/("""">
		"""),_display_(/*65.4*/mapping/*65.11*/.getLabel),_display_(/*65.21*/if(mapping.getComponent.getRequired)/*65.57*/{_display_(Seq[Any](format.raw/*65.58*/("""<abbr title=""""),_display_(/*65.72*/i18n("na.basemodule.validations.mandatory")),format.raw/*65.115*/(""""></abbr>""")))}),format.raw/*65.125*/("""
	"""),format.raw/*66.2*/("""</label>
	<div class="col-sm-10" data-na-portal-field-notification>
		"""),_display_(/*68.4*/selectComp/*68.14*/.render(selectAttributes, null, null, dynamicOptions)),format.raw/*68.67*/("""
		"""),format.raw/*69.3*/("""<input id=""""),_display_(/*69.15*/{selectId}),format.raw/*69.25*/("""-locked" type="checkbox" data-na-portal-double-box name="switch-checkbox" data-size="mini" data-on-color="success" data-off-color="danger">
		<label for=""""),_display_(/*70.16*/{selectId}),format.raw/*70.26*/("""-locked">go.lock.field</label>
	</div>
</div>


"""))
      }
    }
  }

  def render(mapping:pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping): play.twirl.api.HtmlFormat.Appendable = apply(mapping)

  def f:((pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping) => play.twirl.api.HtmlFormat.Appendable) = (mapping) => apply(mapping)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:21 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/dynamicForm/fields/selectBoxWithSwitch.scala.html
                  HASH: 5d0f0268817b18725ef2f9849a08378aaad623d8
                  MATRIX: 687->1|726->34|767->69|819->115|907->197|1000->284|1075->353|1491->394|1646->473|1664->482|1728->534|1745->542|1791->576|1816->592|2390->1156|2413->1170|3233->470|3263->532|3292->574|3320->1152|3350->1960|3379->1962|3445->2001|3461->2008|3491->2017|3551->2050|3597->2086|3637->2087|3681->2099|3719->2109|3736->2116|3773->2131|3805->2137|3821->2144|3851->2154|3896->2190|3935->2191|3976->2205|4041->2248|4083->2258|4112->2260|4209->2331|4228->2341|4302->2394|4332->2397|4371->2409|4402->2419|4584->2574|4615->2584
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|35->9|39->11|39->11|39->12|39->12|39->13|39->13|60->37|60->37|85->9|87->11|88->12|89->34|92->61|94->63|95->64|95->64|95->64|95->64|95->64|95->64|95->64|95->64|95->64|95->64|96->65|96->65|96->65|96->65|96->65|96->65|96->65|96->65|97->66|99->68|99->68|99->68|100->69|100->69|100->69|101->70|101->70
                  -- GENERATED --
              */
          