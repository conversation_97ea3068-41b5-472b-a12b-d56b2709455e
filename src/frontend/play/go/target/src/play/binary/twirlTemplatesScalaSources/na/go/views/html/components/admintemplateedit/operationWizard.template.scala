
package na.go.views.html.components.admintemplateedit

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import na.naportalbase.views.tags.wizardStep
/*3.2*/import na.naportalbase.utils.TemplateUtils
/*4.2*/import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Family
/*5.2*/import na.go.views.html.components.admintemplatecreate

object operationWizard extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[List[Family],String,String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*7.2*/(groups: List[Family], templateJson: String, context: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*27.2*/step2Content/*27.14*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*27.18*/("""

""")))};def /*9.2*/step1/*9.7*/ = {{
	wizardStep(
		stepName = "characteristics",
		stepLabel = i18n("na.portal.go.operation.create.tab.characteristics.label")
	) {
		admintemplatecreate.characteristicsTab("createCtrl.characteristics", groups)
	}
}};def /*18.2*/step2/*18.7*/ = {{
	wizardStep(
		stepName = "operation",
		stepLabel = i18n("na.portal.go.operation.create.tab.operation.label")
	) {
		step2Content
	}
}};def /*31.2*/wizardAttributes/*31.18*/ = {{
	var map = new java.util.HashMap[String, String]()
	map.put("data-na-portal-go-wizard-context", context)
	map.put("data-ng-controller", "NaPortalGoAdminCreateTemplateController as createCtrl")
	map.put("data-na-portal-go-edit-template", null)
	if (templateJson != null) {
		map.put("go-admin-edit-template", templateJson)
	}
	map
}};
Seq[Any](format.raw/*7.63*/("""

"""),format.raw/*16.2*/("""

"""),format.raw/*25.2*/("""

"""),format.raw/*29.2*/("""

"""),format.raw/*40.2*/("""

"""),format.raw/*42.1*/("""<div class="tab-pane active" id="tab-car"  """),_display_(/*42.45*/TemplateUtils/*42.58*/.dynamicElementsAttributes(wizardAttributes)),format.raw/*42.102*/(""">
	<x-wizard class="edit-template-wizard">
		<x-wizard-step class="opened" data-tab-name=""""),_display_(/*44.49*/i18n("na.portal.go.operation.create.tab.characteristics.label")),format.raw/*44.112*/("""">"""),_display_(/*44.115*/step1/*44.120*/.content),format.raw/*44.128*/("""</x-wizard-step>
		<x-wizard-step class="opened open" data-tab-name=""""),_display_(/*45.54*/i18n("na.portal.go.operation.create.tab.operation.label")),format.raw/*45.111*/("""" data-na-portal-go-wizard-template-loader></x-wizard-step>
	</x-wizard>
</div>"""))
      }
    }
  }

  def render(groups:List[Family],templateJson:String,context:String): play.twirl.api.HtmlFormat.Appendable = apply(groups,templateJson,context)

  def f:((List[Family],String,String) => play.twirl.api.HtmlFormat.Appendable) = (groups,templateJson,context) => apply(groups,templateJson,context)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:12 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/admintemplateedit/operationWizard.scala.html
                  HASH: ff27045b68a9392df994d59d277d60741e6a073a
                  MATRIX: 686->1|732->41|784->87|834->131|913->204|1302->261|1442->699|1463->711|1544->715|1569->325|1581->330|1812->550|1825->555|1980->721|2005->737|2372->322|2401->547|2430->696|2459->718|2488->1074|2517->1076|2588->1120|2610->1133|2676->1177|2794->1268|2879->1331|2910->1334|2925->1339|2955->1347|3052->1417|3131->1474
                  LINES: 24->1|25->2|26->3|27->4|28->5|33->7|37->27|37->27|39->27|41->9|41->9|48->18|48->18|55->31|55->31|65->7|67->16|69->25|71->29|73->40|75->42|75->42|75->42|75->42|77->44|77->44|77->44|77->44|77->44|78->45|78->45
                  -- GENERATED --
              */
          