
package na.go.views.html.components.admintemplatecreate

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import na.naportalbase.views.tags.wizardStep
/*3.2*/import na.naportalbase.utils.TemplateUtils
/*4.2*/import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Family
/*5.2*/import na.go.views.html.components.admintemplatecreate

object operationWizard extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[List[Family],String,String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*7.2*/(groups: List[Family], templateJson: String, context: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*27.2*/step2Content/*27.14*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*27.18*/("""
	"""),format.raw/*28.2*/("""<div></div>
""")))};def /*9.2*/step1/*9.7*/ = {{
	wizardStep(
		stepName = "characteristics",
		stepLabel = i18n("na.portal.go.operation.create.tab.characteristics.label")
	) {
		admintemplatecreate.characteristicsTab("createCtrl.characteristics", groups)
	}
}};def /*18.2*/step2/*18.7*/ = {{
	wizardStep(
		stepName = "operation",
		stepLabel = i18n("na.portal.go.operation.create.tab.operation.label")
	) {
		step2Content
	}
}};def /*31.2*/wizardAttributes/*31.18*/ = {{
	var map = new java.util.HashMap[String, String]()
	map.put("data-na-portal-go-wizard-context", context)
	map.put("data-ng-controller", "NaPortalGoAdminCreateTemplateController as createCtrl")
	if (templateJson != null) {
		map.put("go-admin-edit-template", templateJson)
	}
	map
}};
Seq[Any](format.raw/*7.63*/("""

"""),format.raw/*16.2*/("""

"""),format.raw/*25.2*/("""

"""),format.raw/*29.2*/("""

"""),format.raw/*39.2*/("""

"""),format.raw/*41.1*/("""<div class="tab-pane active" id="tab-car" """),_display_(/*41.44*/TemplateUtils/*41.57*/.dynamicElementsAttributes(wizardAttributes)),format.raw/*41.101*/(""">
	<x-wizard class="create-template-wizard">
		<x-wizard-step data-tab-name=""""),_display_(/*43.34*/i18n("na.portal.go.operation.create.tab.characteristics.label")),format.raw/*43.97*/("""">"""),_display_(/*43.100*/step1/*43.105*/.content),format.raw/*43.113*/("""</x-wizard-step>
		<x-wizard-step data-tab-name=""""),_display_(/*44.34*/i18n("na.portal.go.operation.create.tab.operation.label")),format.raw/*44.91*/("""">"""),_display_(/*44.94*/step2/*44.99*/.content),format.raw/*44.107*/("""</x-wizard-step>
	</x-wizard>
</div>"""))
      }
    }
  }

  def render(groups:List[Family],templateJson:String,context:String): play.twirl.api.HtmlFormat.Appendable = apply(groups,templateJson,context)

  def f:((List[Family],String,String) => play.twirl.api.HtmlFormat.Appendable) = (groups,templateJson,context) => apply(groups,templateJson,context)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:21 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/admintemplatecreate/operationWizard.scala.html
                  HASH: 09862851b9acb48fdd002584caaa016c10e2c856
                  MATRIX: 688->1|734->41|786->87|836->131|915->204|1304->261|1444->699|1465->711|1546->715|1575->717|1610->325|1622->330|1853->550|1866->555|2021->733|2046->749|2363->322|2392->547|2421->696|2450->730|2479->1036|2508->1038|2578->1081|2600->1094|2666->1138|2771->1216|2855->1279|2886->1282|2901->1287|2931->1295|3008->1345|3086->1402|3116->1405|3130->1410|3160->1418
                  LINES: 24->1|25->2|26->3|27->4|28->5|33->7|37->27|37->27|39->27|40->28|41->9|41->9|48->18|48->18|55->31|55->31|64->7|66->16|68->25|70->29|72->39|74->41|74->41|74->41|74->41|76->43|76->43|76->43|76->43|76->43|77->44|77->44|77->44|77->44|77->44
                  -- GENERATED --
              */
          