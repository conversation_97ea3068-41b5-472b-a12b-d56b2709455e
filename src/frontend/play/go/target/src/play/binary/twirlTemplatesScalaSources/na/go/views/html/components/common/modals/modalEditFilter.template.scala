
package na.go.views.html.components.common.modals

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import scala.collection.mutable
/*2.2*/import scala.collection.immutable
/*3.2*/import na.naportalbase.views.tags.i18n
/*4.2*/import na.naportalbase.views.html.components.inputsComp.modalInputTextComp
/*5.2*/import na.go.views.html.components.adminpage.{ operationForm => adminOperationForm }
/*6.2*/import na.go.views.html.components.home.{ operationForm => operationForm }
/*7.2*/import na.naportalbase.views.html.skeletons.search.horizontalFormSkel

object modalEditFilter extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[String,na.go.models.general.OperCatFamilyList,String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*9.2*/(filterName : String, groups : na.go.models.general.OperCatFamilyList, operationFormType: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*16.2*/operationFormTemplate/*16.23*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*16.27*/("""
    """),_display_(/*17.6*/if(operationFormType eq "operationForm")/*17.46*/ {_display_(Seq[Any](format.raw/*17.48*/("""
        """),_display_(/*18.10*/operationForm/*18.23*/.render("tempSearchFormItems",immutable.HashMap[String,String](), groups, true)),format.raw/*18.102*/("""
    """)))}/*19.7*/else/*19.12*/{_display_(Seq[Any](format.raw/*19.13*/("""
        """),_display_(/*20.10*/adminOperationForm/*20.28*/.render("tempSearchFormItems",immutable.HashMap[String,String](), groups, true)),format.raw/*20.107*/("""
    """)))}),format.raw/*21.6*/("""
""")))};def /*67.2*/form/*67.6*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*67.10*/("""
    """),format.raw/*68.5*/("""<div id="generic-operation-modal-form">
        <div class="form-group" data-na-portal-field-notification>
            """),_display_(/*70.14*/modalInputTextComp/*70.32*/.render(filterNameLabel, "filterName",filterNamePlaceHolder,filterNameInputAttributes)),format.raw/*70.118*/("""
        """),format.raw/*71.9*/("""</div>

        <div class="fx-expand">
            """),_display_(/*74.14*/i18n("na.modals.filters.options")),format.raw/*74.47*/("""
        """),format.raw/*75.9*/("""</div>

        <div class="clearfix">
            """),_display_(/*78.14*/operationFormTemplate),format.raw/*78.35*/("""
        """),format.raw/*79.9*/("""</div>
    </div>
""")))};def /*25.2*/filterNameLabel/*25.17*/ = {{i18n("na.modals.fields.name.label")}};def /*26.2*/filterNamePlaceHolder/*26.23*/ = {{i18n("na.modals.fields.name.placeholder")}};def /*27.2*/filterNameInputAttributes/*27.27*/ = {{
    var map = mutable.HashMap[String,String]()
    map.put("data-ng-model", "tempSearchFormItems.filterName")
    map.put("data-na-portal-required",null)
    map.put("name", "filterName")
    map.put("id", "filterName")
    map.put("maxlength", "64")
    map.put("disabled", "true")
    map
}};def /*39.2*/filterOrderStartDateLabel/*39.27*/ = {{i18n("na.portal.go.field.beginDate.label")}};def /*40.2*/datePickerAttributes/*40.22*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("id", "filterStartDatePicker")
    map.put("data-date-time-picker", null)
    map.put("data-date-time-picker-input", null)
    map.put("data-max-date","today")
    map
}};def /*49.2*/inputAttributes/*49.17*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-model", "tempSearchFormItems.startDate")
    map.put("data-na-portal-required",null)
    map.put("data-datapickerfield", null)
    map.put("data-scope-target","tempSearchFormItems")
    map.put("data-pair-condition","filterEndDatePicker")
    map.put("name", "startDate")
    map.put("id", "startDate")
    map
}};def /*61.2*/formAttributes/*61.16*/ = {{
    var map = mutable.HashMap[String,String]()
    map.put("data-ng-controller","NaPortalGoOperationSaveModalController as rfsModalCtrl")
    map
}};
Seq[Any](format.raw/*9.99*/("""

"""),format.raw/*11.1*/("""<script>
    //https://github.com/select2/select2/issues/1436
    $.fn.modal.Constructor.prototype.enforceFocus = function() """),format.raw/*13.64*/("""{"""),format.raw/*13.65*/("""}"""),format.raw/*13.66*/(""";
</script>

"""),format.raw/*22.2*/("""

"""),format.raw/*24.1*/("""<!-- FilterName FIELD -->
"""),format.raw/*25.58*/("""
"""),format.raw/*26.70*/("""
"""),format.raw/*36.2*/("""

"""),format.raw/*38.1*/("""<!-- Filter Start Date -->
"""),format.raw/*39.75*/("""
"""),format.raw/*47.2*/("""

"""),format.raw/*59.2*/("""

"""),format.raw/*65.2*/("""

"""),format.raw/*81.2*/("""

"""),_display_(/*83.2*/horizontalFormSkel/*83.20*/.render(formAttributes,form)),format.raw/*83.48*/("""
"""))
      }
    }
  }

  def render(filterName:String,groups:na.go.models.general.OperCatFamilyList,operationFormType:String): play.twirl.api.HtmlFormat.Appendable = apply(filterName,groups,operationFormType)

  def f:((String,na.go.models.general.OperCatFamilyList,String) => play.twirl.api.HtmlFormat.Appendable) = (filterName,groups,operationFormType) => apply(filterName,groups,operationFormType)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:12 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/common/modals/modalEditFilter.scala.html
                  HASH: d087b0367b1c15b45d9ff48e65977c241f1ca1b0
                  MATRIX: 682->1|721->34|762->69|808->109|890->185|982->271|1064->347|1494->419|1670->659|1700->680|1781->684|1813->690|1862->730|1902->732|1939->742|1961->755|2062->834|2086->841|2099->846|2138->847|2175->857|2202->875|2303->954|2339->960|2364->2380|2376->2384|2457->2388|2489->2393|2636->2513|2663->2531|2771->2617|2807->2626|2887->2679|2941->2712|2977->2721|3056->2773|3098->2794|3134->2803|3176->991|3200->1006|3255->1049|3285->1070|3346->1119|3380->1144|3692->1472|3726->1497|3788->1547|3817->1567|4066->1805|4090->1820|4491->2210|4514->2224|4697->516|4726->518|4879->643|4908->644|4937->645|4977->962|5006->964|5060->1047|5089->1117|5117->1442|5146->1444|5201->1545|5229->1802|5258->2207|5287->2377|5316->2822|5345->2825|5372->2843|5421->2871
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|35->9|39->16|39->16|41->16|42->17|42->17|42->17|43->18|43->18|43->18|44->19|44->19|44->19|45->20|45->20|45->20|46->21|47->67|47->67|49->67|50->68|52->70|52->70|52->70|53->71|56->74|56->74|57->75|60->78|60->78|61->79|63->25|63->25|63->26|63->26|63->27|63->27|72->39|72->39|72->40|72->40|79->49|79->49|89->61|89->61|94->9|96->11|98->13|98->13|98->13|101->22|103->24|104->25|105->26|106->36|108->38|109->39|110->47|112->59|114->65|116->81|118->83|118->83|118->83
                  -- GENERATED --
              */
          