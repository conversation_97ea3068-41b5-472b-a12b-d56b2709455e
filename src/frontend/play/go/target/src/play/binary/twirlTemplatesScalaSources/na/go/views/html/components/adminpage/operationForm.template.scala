
package na.go.views.html.components.adminpage

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import scala.collection.immutable
/*2.2*/import na.naportalbase.views.tags.i18n
/*3.2*/import na.go.models.general.{TemplateStates, OperCatFamilyList}
/*4.2*/import na.naportalbase.views.html.components.selectsComp.dynamicSelectOptionComp
/*5.2*/import na.naportalbase.views.html.components.selectsComp.selectComp
/*6.2*/import scala.collection.mutable
/*7.2*/import na.go.views.html.components.common.saveFilterButton

object operationForm extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[String,immutable.HashMap[String, String],OperCatFamilyList,Boolean,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*9.2*/(ngModelVariable: String, inputFieldIds: immutable.HashMap[String, String], groups : OperCatFamilyList, isModal: Boolean):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*175.2*/inputDateCompSidebar/*175.22*/(label: String, datePickerId: String, dateFormat: String):play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*175.83*/("""
    """),format.raw/*176.5*/("""<label for=""""),_display_(/*176.18*/datePickerId),format.raw/*176.30*/("""">"""),_display_(/*176.33*/label),format.raw/*176.38*/("""</label>
    <x-date-time-picker data-field=""""),_display_(/*177.38*/datePickerId),format.raw/*177.50*/("""" format=""""),_display_(/*177.61*/dateFormat),format.raw/*177.71*/(""""></x-date-time-picker>
""")))};def /*180.2*/inputDateCompModal/*180.20*/(label: String, datePickerId: String, dateFormat: String):play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*180.81*/("""
    """),format.raw/*181.5*/("""<label class="control-label col-sm-3" for=""""),_display_(/*181.49*/datePickerId),format.raw/*181.61*/("""">"""),_display_(/*181.64*/label),format.raw/*181.69*/("""</label>
    <div class="col-sm-9">
        <x-date-time-picker data-field=""""),_display_(/*183.42*/datePickerId),format.raw/*183.54*/("""" format=""""),_display_(/*183.65*/dateFormat),format.raw/*183.75*/(""""></x-date-time-picker>
    </div>
""")))};def /*11.2*/prefixId/*11.10*/ = {{if(isModal) "modal-" else ""}};def /*13.2*/selectAttributes/*13.18*/(attributesMap: immutable.HashMap[String, String]) = {{
    attributesMap + ("style" -> "width: 100%")
}};def /*18.2*/operationStartTimepickerInputId/*18.33*/ = {{inputFieldIds.getOrElse("startDate","startDate")}};def /*19.2*/orderStartDateLabel/*19.21*/ = {{i18n("na.portal.go.field.modificationBeginDate.label")}};def /*21.2*/datePickerAttributes/*21.22*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-date-time-picker", null)
    map.put("data-date-time-picker-input", null)
    map.put("data-max-date","today")

    map
}};def /*31.2*/inputAttributes/*31.17*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-entity", operationStartTimepickerInputId)
    map.put("data-ng-model", ngModelVariable+"."+operationStartTimepickerInputId)
    map.put("data-datapickerfield", null)
    map.put("data-scope-target",ngModelVariable)
    map.put("name", operationStartTimepickerInputId)
    map.put("id", prefixId+operationStartTimepickerInputId)

    map
}};def /*47.2*/orderEndTimepickerInputId/*47.27*/ = {{inputFieldIds.getOrElse("endDate","endDate")}};def /*48.2*/orderEndDateLabel/*48.19*/ = {{i18n("na.portal.go.field.modificationEndDate.label")}};def /*49.2*/endDatePickerAttributes/*49.25*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-date-time-picker", null)
    map.put("data-date-time-picker-input", null)
    map.put("data-max-date","today")

    map
}};def /*57.2*/endDateinputAttributes/*57.24*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-entity", orderEndTimepickerInputId)
    map.put("data-ng-model", ngModelVariable+"."+orderEndTimepickerInputId)
    map.put("data-datapickerfield", null)
    map.put("data-scope-target",ngModelVariable)
    map.put("name", orderEndTimepickerInputId)
    map.put("id", prefixId+orderEndTimepickerInputId)

    map
}};def /*70.2*/operationFamilyId/*70.19*/ = {{inputFieldIds.getOrElse("family-field","family-field")}};def /*71.2*/operationFamilyLabel/*71.22*/ = {{i18n("na.portal.go.family.field.label")}};def /*72.2*/operationFamilySelectAttributes/*72.33*/ = {{
        var map = immutable.HashMap[String, String](
            "data-ng-model" -> (ngModelVariable+".family"),
            "name" -> operationFamilyId,
            "data-na-portal-select-box" -> null,
            "data-na-portal-select-box-auto-dropdown-size" -> null,
            "placeholder" -> i18n("na.portal.go.family.selects.empty.option"),
            "value" -> "",
            "id" -> (prefixId+operationFamilyId),
            "class" -> "form-control input-sm",
            "data-context" -> "family",
            "allow-clear" -> null
        )
        map
    }};def /*88.2*/operationFamilySelectData/*88.27*/ = {{
    var stringBuilder = new StringBuilder

   for(group <- groups.getEntries()){
      var optionAttributes = immutable.HashMap[String,String](
          "id" -> group.getName,
          "text" -> group.getName,
          "value" -> group.getName
      )
       stringBuilder.append(dynamicSelectOptionComp.render(group.getName,optionAttributes))

   }

    Html(stringBuilder.toString())
}};def /*106.2*/operationRfsId/*106.16*/ = {{inputFieldIds.getOrElse("selectRfsNames","selectRfsNames")}};def /*107.2*/operationRfsSelectAttributes/*107.30*/ = {{
    var map = immutable.HashMap[String, String](
        "data-ng-model" -> (ngModelVariable+".rfs"),
        "style" -> "100%",
        "name" -> operationRfsId,
        "data-na-portal-select-box" -> null,
        "data-na-portal-select-box-auto-dropdown-size" -> null,
        "id" -> (prefixId+operationRfsId),
        "placeholder" -> i18n("na.portal.go.rfs.selects.empty.option"),
        "value" -> "",
        "data-is-dynamic" -> null,
        "class" -> "form-control input-sm inherit-default-color",
        "data-context" -> "rfs",
        "allow-clear" -> null
        )
        map
    }};def /*126.2*/operationOpId/*126.15*/ = {{inputFieldIds.getOrElse("operation","operation")}};def /*127.2*/operationOpLabel/*127.18*/ = {{i18n( "na.portal.go.operation.field.label")}};def /*128.2*/operationOpSelectAttributes/*128.29*/ = {{ immutable.HashMap[String, String](
    "data-ng-model" -> (ngModelVariable+".operationName"),
    "name" -> operationOpId,
    "data-na-portal-select-box" -> null,
    "data-na-portal-select-box-auto-dropdown-size" -> null,
    "value" -> "",
    "data-is-dynamic" -> null,
    "placeholder" -> i18n("na.portal.go.operation.selects.empty.option"),
    "id" -> (prefixId+operationOpId),
    "class" -> "form-control input-sm inherit-default-color",
    "data-context" -> "operation",
    "style" -> "100%",
    "allow-clear" -> null
)}};def /*144.2*/operationStateId/*144.18*/ = {{inputFieldIds.getOrElse("templateState","templateState")}};def /*145.2*/operationStateLabel/*145.21*/ = {{i18n("na.portal.go.state.field.label")}};def /*146.2*/operationStateSelectAttributes/*146.32*/ = {{ immutable.HashMap[String, String](
            "data-ng-model" -> (ngModelVariable+".templateState"),
            "name" -> operationStateId,
            "data-na-portal-select-box" -> null,
            "placeholder" -> i18n("na.portal.go.state.selects.empty.option"),
            "value" -> "",
            "id" -> (prefixId+operationStateId),
            "class" -> "form-control input-sm",
            "data-context" -> "state",
            "allow-clear" -> null
         )
     }};def /*158.2*/operationStateSelectData/*158.26*/ = {{

    var list = TemplateStates.values()
    var stringBuilder = new StringBuilder
    for(option <- list){
      var optionAttributes = immutable.HashMap[String, String](
        "name" -> option.name(),
        "id" -> option.name(),
        "value" -> option.value()
      )
      var text = i18n("na.portal.go.adminoperation.state."+option.name()+".label")
      stringBuilder.append(dynamicSelectOptionComp.render(text,optionAttributes))
  }
  Html(stringBuilder.toString())
}};
Seq[Any](format.raw/*9.123*/("""

"""),format.raw/*11.44*/("""

"""),format.raw/*15.2*/("""

"""),format.raw/*17.1*/("""<!-- START DATE CONFIG -->
"""),format.raw/*18.87*/("""
"""),format.raw/*19.81*/("""

"""),format.raw/*28.2*/("""


"""),format.raw/*41.2*/("""




"""),format.raw/*46.1*/("""<!-- END DATE CONFIG -->
"""),format.raw/*47.77*/("""
"""),format.raw/*48.77*/("""
"""),format.raw/*56.2*/("""
"""),format.raw/*67.2*/("""

"""),format.raw/*69.1*/("""<!-- FAMILY FIELD CONFIG -->
"""),format.raw/*70.93*/("""
"""),format.raw/*71.78*/("""
"""),format.raw/*86.6*/("""

"""),format.raw/*102.2*/("""


"""),format.raw/*105.1*/("""<!-- RFS FIELD CONFIG -->  
"""),format.raw/*106.80*/("""
"""),format.raw/*123.6*/("""

"""),format.raw/*125.1*/("""<!-- OPERATION FIELD CONFIG -->  
"""),format.raw/*126.69*/("""
"""),format.raw/*127.67*/("""
"""),format.raw/*141.3*/("""
  
"""),format.raw/*143.1*/("""<!-- STATE FIELD CONFIG -->  
"""),format.raw/*144.80*/("""
"""),format.raw/*145.65*/("""
"""),format.raw/*157.7*/("""
"""),format.raw/*172.2*/("""


"""),format.raw/*178.2*/("""

"""),format.raw/*185.2*/("""


"""),_display_(/*188.2*/if(!isModal)/*188.14*/ {_display_(Seq[Any](format.raw/*188.16*/("""

"""),format.raw/*190.1*/("""<!-- SIDEBAR START DATE FIELD -->  
<div class="form-group" data-na-portal-field-notification data-na-portal-button-notification>
    """),_display_(/*192.6*/inputDateCompSidebar(orderStartDateLabel, operationStartTimepickerInputId, "short")),format.raw/*192.89*/("""
"""),format.raw/*193.1*/("""</div>

<!-- SIDEBAR END DATE FIELD -->  
<div class="form-group" data-na-portal-field-notification data-na-portal-button-notification>
    """),_display_(/*197.6*/inputDateCompSidebar(orderEndDateLabel, orderEndTimepickerInputId, "short")),format.raw/*197.81*/("""
"""),format.raw/*198.1*/("""</div>

<!-- SIDEBAR FAMILY FIELD -->
<div class="form-group">
    <label for=""""),_display_(/*202.18*/{operationFamilyId}),format.raw/*202.37*/("""">"""),_display_(/*202.40*/operationFamilyLabel),format.raw/*202.60*/("""</label><br/>
        """),_display_(/*203.10*/selectComp/*203.20*/.render(
            selectAttributes(operationFamilySelectAttributes), null, null, operationFamilySelectData)),format.raw/*204.102*/("""
"""),format.raw/*205.1*/("""</div>


<!-- SIDEBAR RFS FIELD -->  
<div class="form-group">
    <label for=""""),_display_(/*210.18*/{operationRfsId}),format.raw/*210.34*/("""">"""),_display_(/*210.37*/i18n("na.portal.go.rfs.field.label")),format.raw/*210.73*/("""</label>
    <br/>
    """),_display_(/*212.6*/selectComp/*212.16*/.render(selectAttributes(operationRfsSelectAttributes), "", null, null)),format.raw/*212.87*/("""
"""),format.raw/*213.1*/("""</div>

<!-- SIDEBAR OPERATION FIELD -->  
<div class="form-group">
    <label for=""""),_display_(/*217.18*/{operationOpId}),format.raw/*217.33*/("""">"""),_display_(/*217.36*/{operationOpLabel}),format.raw/*217.54*/("""</label>
    <br/>
      """),_display_(/*219.8*/selectComp/*219.18*/.render(selectAttributes(operationOpSelectAttributes), "", null, null)),format.raw/*219.88*/("""
"""),format.raw/*220.1*/("""</div>


<!-- SIDEBAR STATE FIELD -->  
<div class="form-group">
    <label for=""""),_display_(/*225.18*/{operationStateId}),format.raw/*225.36*/("""">"""),_display_(/*225.39*/{operationStateLabel}),format.raw/*225.60*/("""</label>
    <br/>
    """),_display_(/*227.6*/selectComp/*227.16*/.render(
        operationStateSelectAttributes, null, null, operationStateSelectData)),format.raw/*228.78*/("""
"""),format.raw/*229.1*/("""</div>

<!-- SAVE FILTER BUTTON -->
<div class="form-group search-form--save-filter-button">
    """),_display_(/*233.6*/saveFilterButton/*233.22*/.render("templateCtrl.openSaveFilterModal()")),format.raw/*233.67*/("""
"""),format.raw/*234.1*/("""</div>

""")))}/*236.3*/else/*236.8*/{_display_(Seq[Any](format.raw/*236.9*/("""

"""),format.raw/*238.1*/("""<!-- MODAL START DATE FIELD -->  
<div class="form-group" data-na-portal-field-notification data-na-portal-button-notification>
    """),_display_(/*240.6*/inputDateCompModal(orderStartDateLabel, operationStartTimepickerInputId, "short")),format.raw/*240.87*/("""
"""),format.raw/*241.1*/("""</div>

<!-- MODAL END DATE FIELD -->  
<div class="form-group" data-na-portal-field-notification data-na-portal-button-notification>
	"""),_display_(/*245.3*/inputDateCompModal(orderEndDateLabel, orderEndTimepickerInputId, "short")),format.raw/*245.76*/("""
"""),format.raw/*246.1*/("""</div>

<!-- MODAL FAMILY FIELD -->
<div class="form-group">
    <label for=""""),_display_(/*250.18*/{operationFamilyId}),format.raw/*250.37*/("""" class="control-label col-sm-3">"""),_display_(/*250.71*/{operationFamilyLabel}),format.raw/*250.93*/("""</label>
    <div class="col-sm-9">
        """),_display_(/*252.10*/selectComp/*252.20*/.render(
            selectAttributes(operationFamilySelectAttributes), null, null, operationFamilySelectData)),format.raw/*253.102*/("""
    """),format.raw/*254.5*/("""</div>
</div>

<!-- MODAL RFS FIELD -->  
<div class="form-group">
    <label for="selectRfsNames" class="control-label col-sm-3">"""),_display_(/*259.65*/{i18n("na.portal.go.rfs.field.label")}),format.raw/*259.103*/("""</label>
    <div class="col-sm-9">
    """),_display_(/*261.6*/selectComp/*261.16*/.render(selectAttributes(operationRfsSelectAttributes), null, null, null)),format.raw/*261.89*/("""
   """),format.raw/*262.4*/("""</div>
</div>

<!-- MODAL OPERATION FIELD -->  
<div class="form-group">
    <label for=""""),_display_(/*267.18*/{operationOpId}),format.raw/*267.33*/("""" class="control-label col-sm-3">"""),_display_(/*267.67*/{operationOpLabel}),format.raw/*267.85*/("""</label>
    <div class="col-sm-9">
      """),_display_(/*269.8*/selectComp/*269.18*/.render(selectAttributes(operationOpSelectAttributes), null, null, null)),format.raw/*269.90*/("""
      """),format.raw/*270.7*/("""</div>
</div>

<!-- MODAL STATE FIELD -->  
<div class="form-group">
    <label for=""""),_display_(/*275.18*/{operationStateId}),format.raw/*275.36*/("""" class="control-label col-sm-3">"""),_display_(/*275.70*/{operationStateLabel}),format.raw/*275.91*/("""</label>
    <div class="col-sm-9">
            """),_display_(/*277.14*/selectComp/*277.24*/.render(
                selectAttributes(operationStateSelectAttributes), null, null, operationStateSelectData)),format.raw/*278.104*/("""
    """),format.raw/*279.5*/("""</div>
</div>

""")))}))
      }
    }
  }

  def render(ngModelVariable:String,inputFieldIds:immutable.HashMap[String, String],groups:OperCatFamilyList,isModal:Boolean): play.twirl.api.HtmlFormat.Appendable = apply(ngModelVariable,inputFieldIds,groups,isModal)

  def f:((String,immutable.HashMap[String, String],OperCatFamilyList,Boolean) => play.twirl.api.HtmlFormat.Appendable) = (ngModelVariable,inputFieldIds,groups,isModal) => apply(ngModelVariable,inputFieldIds,groups,isModal)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:12 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/adminpage/operationForm.scala.html
                  HASH: cd2fe68a134297f8bdd3366231bcfae4f9848545
                  MATRIX: 678->1|719->36|765->76|836->141|924->223|999->292|1038->325|1469->386|1670->6269|1700->6289|1839->6350|1872->6355|1913->6368|1947->6380|1978->6383|2005->6388|2079->6434|2113->6446|2152->6457|2184->6467|2233->6495|2261->6513|2400->6574|2433->6579|2505->6623|2539->6635|2570->6638|2597->6643|2702->6720|2736->6732|2775->6743|2807->6753|2866->510|2883->518|2931->555|2956->571|3074->705|3114->736|3182->792|3210->811|3284->874|3313->894|3520->1091|3544->1106|3968->1547|4002->1572|4066->1624|4092->1641|4164->1701|4196->1724|4403->1919|4434->1941|4834->2359|4860->2376|4934->2452|4963->2472|5022->2530|5062->2561|5658->3146|5692->3171|6103->3599|6127->3613|6206->3679|6244->3707|6866->4351|6889->4364|6958->4420|6984->4436|7048->4487|7085->4514|7640->5089|7666->5105|7743->5169|7772->5188|7831->5234|7871->5264|8375->5755|8409->5779|8926->507|8956->552|8985->675|9014->677|9069->790|9098->871|9127->1087|9157->1516|9189->1521|9242->1622|9271->1699|9299->1917|9327->2327|9356->2329|9413->2450|9442->2528|9470->3143|9500->3567|9531->3570|9588->3677|9617->4314|9647->4316|9710->4418|9740->4485|9769->5054|9801->5058|9860->5167|9890->5232|9919->5753|9948->6265|9979->6492|10009->6789|10040->6793|10062->6805|10103->6807|10133->6809|10295->6944|10400->7027|10429->7028|10597->7169|10694->7244|10723->7245|10831->7325|10872->7344|10903->7347|10945->7367|10996->7390|11016->7400|11149->7510|11178->7511|11286->7591|11324->7607|11355->7610|11413->7646|11464->7670|11484->7680|11577->7751|11606->7752|11719->7837|11756->7852|11787->7855|11827->7873|11880->7899|11900->7909|11992->7979|12021->7980|12131->8062|12171->8080|12202->8083|12245->8104|12296->8128|12316->8138|12424->8224|12453->8225|12578->8323|12604->8339|12671->8384|12700->8385|12728->8395|12741->8400|12780->8401|12810->8403|12970->8536|13073->8617|13102->8618|13265->8754|13360->8827|13389->8828|13495->8906|13536->8925|13598->8959|13642->8981|13715->9026|13735->9036|13868->9146|13901->9151|14060->9282|14121->9320|14189->9361|14209->9371|14304->9444|14336->9448|14454->9538|14491->9553|14553->9587|14593->9605|14663->9648|14683->9658|14777->9730|14812->9737|14926->9823|14966->9841|15028->9875|15071->9896|15148->9945|15168->9955|15303->10067|15336->10072
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|35->9|39->175|39->175|41->175|42->176|42->176|42->176|42->176|42->176|43->177|43->177|43->177|43->177|44->180|44->180|46->180|47->181|47->181|47->181|47->181|47->181|49->183|49->183|49->183|49->183|51->11|51->11|51->13|51->13|53->18|53->18|53->19|53->19|53->21|53->21|60->31|60->31|70->47|70->47|70->48|70->48|70->49|70->49|77->57|77->57|87->70|87->70|87->71|87->71|87->72|87->72|101->88|101->88|115->106|115->106|115->107|115->107|131->126|131->126|131->127|131->127|131->128|131->128|144->144|144->144|144->145|144->145|144->146|144->146|155->158|155->158|170->9|172->11|174->15|176->17|177->18|178->19|180->28|183->41|188->46|189->47|190->48|191->56|192->67|194->69|195->70|196->71|197->86|199->102|202->105|203->106|204->123|206->125|207->126|208->127|209->141|211->143|212->144|213->145|214->157|215->172|218->178|220->185|223->188|223->188|223->188|225->190|227->192|227->192|228->193|232->197|232->197|233->198|237->202|237->202|237->202|237->202|238->203|238->203|239->204|240->205|245->210|245->210|245->210|245->210|247->212|247->212|247->212|248->213|252->217|252->217|252->217|252->217|254->219|254->219|254->219|255->220|260->225|260->225|260->225|260->225|262->227|262->227|263->228|264->229|268->233|268->233|268->233|269->234|271->236|271->236|271->236|273->238|275->240|275->240|276->241|280->245|280->245|281->246|285->250|285->250|285->250|285->250|287->252|287->252|288->253|289->254|294->259|294->259|296->261|296->261|296->261|297->262|302->267|302->267|302->267|302->267|304->269|304->269|304->269|305->270|310->275|310->275|310->275|310->275|312->277|312->277|313->278|314->279
                  -- GENERATED --
              */
          