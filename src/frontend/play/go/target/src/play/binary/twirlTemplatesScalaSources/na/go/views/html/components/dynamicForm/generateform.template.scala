
package na.go.views.html.components.dynamicForm

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import pt.ptinovacao.na.portal.webui.restful.commons.model.ElementTypes
/*2.2*/import na.go.views.html.components.dynamicForm.fields.textField
/*3.2*/import na.go.views.html.components.dynamicForm.fields.fileField
/*4.2*/import na.go.views.html.components.dynamicForm.fields.textArea
/*5.2*/import na.go.views.html.components.dynamicForm.fields.checkList
/*6.2*/import na.go.views.html.components.dynamicForm.fields.radioButtons
/*7.2*/import na.go.views.html.components.dynamicForm.fields.selectBox

object generateform extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[List[pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*9.2*/(mappings: List[pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*9.85*/("""

"""),_display_(/*11.2*/for(mapping <- mappings) yield /*11.26*/ {_display_(Seq[Any](format.raw/*11.28*/("""

    """),_display_(/*13.6*/ElementTypes/*13.18*/.getElementTypeByClass(mapping.getComponent.getClass.getName)/*13.79*/ match/*13.85*/ {/*14.9*/case ElementTypes.TEXT_FIELD =>/*14.40*/ {_display_(Seq[Any](format.raw/*14.42*/("""
            """),_display_(/*15.14*/textField/*15.23*/.render(mapping)),format.raw/*15.39*/("""
        """)))}/*17.9*/case ElementTypes.FILE_FIELD =>/*17.40*/ {_display_(Seq[Any](format.raw/*17.42*/("""
            """),_display_(/*18.14*/fileField/*18.23*/.render(mapping)),format.raw/*18.39*/("""
        """)))}/*20.9*/case ElementTypes.TEXT_AREA =>/*20.39*/ {_display_(Seq[Any](format.raw/*20.41*/("""
            """),_display_(/*21.14*/textArea/*21.22*/.render(mapping)),format.raw/*21.38*/("""
        """)))}/*23.9*/case ElementTypes.CHECK_LIST =>/*23.40*/ {_display_(Seq[Any](format.raw/*23.42*/("""
            """),_display_(/*24.14*/checkList/*24.23*/.render(mapping)),format.raw/*24.39*/("""
        """)))}/*26.9*/case ElementTypes.RADIO_BUTTON =>/*26.42*/ {_display_(Seq[Any](format.raw/*26.44*/("""
            """),_display_(/*27.14*/radioButtons/*27.26*/.render(mapping)),format.raw/*27.42*/("""
        """)))}/*29.9*/case ElementTypes.SELECT_BOX =>/*29.40*/ {_display_(Seq[Any](format.raw/*29.42*/("""
            """),_display_(/*30.14*/selectBox/*30.23*/.render(mapping)),format.raw/*30.39*/("""
        """)))}}),format.raw/*32.6*/("""

""")))}))
      }
    }
  }

  def render(mappings:List[pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping]): play.twirl.api.HtmlFormat.Appendable = apply(mappings)

  def f:((List[pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping]) => play.twirl.api.HtmlFormat.Appendable) = (mappings) => apply(mappings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:12 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/dynamicForm/generateform.scala.html
                  HASH: 2b66726da18922ab93d12c99bd50a2171343cd68
                  MATRIX: 680->1|759->74|830->139|901->204|971->268|1042->333|1116->401|1556->467|1734->550|1763->553|1803->577|1843->579|1876->586|1897->598|1967->659|1982->665|1992->676|2032->707|2072->709|2113->723|2131->732|2168->748|2196->767|2236->798|2276->800|2317->814|2335->823|2372->839|2400->858|2439->888|2479->890|2520->904|2537->912|2574->928|2602->947|2642->978|2682->980|2723->994|2741->1003|2778->1019|2806->1038|2848->1071|2888->1073|2929->1087|2950->1099|2987->1115|3015->1134|3055->1165|3095->1167|3136->1181|3154->1190|3191->1206|3232->1222
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|35->9|40->9|42->11|42->11|42->11|44->13|44->13|44->13|44->13|44->14|44->14|44->14|45->15|45->15|45->15|46->17|46->17|46->17|47->18|47->18|47->18|48->20|48->20|48->20|49->21|49->21|49->21|50->23|50->23|50->23|51->24|51->24|51->24|52->26|52->26|52->26|53->27|53->27|53->27|54->29|54->29|54->29|55->30|55->30|55->30|56->32
                  -- GENERATED --
              */
          