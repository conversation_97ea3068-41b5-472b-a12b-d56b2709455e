// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/conf/go.routes
// @DATE:Tue Jul 01 11:21:12 WEST 2025

package na.go.controllers;

import go.RoutesPrefix;

public class routes {
  
  public static final na.go.controllers.ReverseAssets Assets = new na.go.controllers.ReverseAssets(RoutesPrefix.byNamePrefix());
  public static final na.go.controllers.ReverseAdminOperation AdminOperation = new na.go.controllers.ReverseAdminOperation(RoutesPrefix.byNamePrefix());
  public static final na.go.controllers.ReverseOperation Operation = new na.go.controllers.ReverseOperation(RoutesPrefix.byNamePrefix());
  public static final na.go.controllers.ReverseModals Modals = new na.go.controllers.ReverseModals(RoutesPrefix.byNamePrefix());
  public static final na.go.controllers.ReverseApplication Application = new na.go.controllers.ReverseApplication(RoutesPrefix.byNamePrefix());
  public static final na.go.controllers.ReverseTableConfigs TableConfigs = new na.go.controllers.ReverseTableConfigs(RoutesPrefix.byNamePrefix());
  public static final na.go.controllers.ReverseSearch Search = new na.go.controllers.ReverseSearch(RoutesPrefix.byNamePrefix());
  public static final na.go.controllers.ReverseOperationDetails OperationDetails = new na.go.controllers.ReverseOperationDetails(RoutesPrefix.byNamePrefix());
  public static final na.go.controllers.ReverseOperationsTable OperationsTable = new na.go.controllers.ReverseOperationsTable(RoutesPrefix.byNamePrefix());
  public static final na.go.controllers.ReverseCatalog Catalog = new na.go.controllers.ReverseCatalog(RoutesPrefix.byNamePrefix());

  public static class javascript {
    
    public static final na.go.controllers.javascript.ReverseAssets Assets = new na.go.controllers.javascript.ReverseAssets(RoutesPrefix.byNamePrefix());
    public static final na.go.controllers.javascript.ReverseAdminOperation AdminOperation = new na.go.controllers.javascript.ReverseAdminOperation(RoutesPrefix.byNamePrefix());
    public static final na.go.controllers.javascript.ReverseOperation Operation = new na.go.controllers.javascript.ReverseOperation(RoutesPrefix.byNamePrefix());
    public static final na.go.controllers.javascript.ReverseModals Modals = new na.go.controllers.javascript.ReverseModals(RoutesPrefix.byNamePrefix());
    public static final na.go.controllers.javascript.ReverseApplication Application = new na.go.controllers.javascript.ReverseApplication(RoutesPrefix.byNamePrefix());
    public static final na.go.controllers.javascript.ReverseTableConfigs TableConfigs = new na.go.controllers.javascript.ReverseTableConfigs(RoutesPrefix.byNamePrefix());
    public static final na.go.controllers.javascript.ReverseSearch Search = new na.go.controllers.javascript.ReverseSearch(RoutesPrefix.byNamePrefix());
    public static final na.go.controllers.javascript.ReverseOperationDetails OperationDetails = new na.go.controllers.javascript.ReverseOperationDetails(RoutesPrefix.byNamePrefix());
    public static final na.go.controllers.javascript.ReverseOperationsTable OperationsTable = new na.go.controllers.javascript.ReverseOperationsTable(RoutesPrefix.byNamePrefix());
    public static final na.go.controllers.javascript.ReverseCatalog Catalog = new na.go.controllers.javascript.ReverseCatalog(RoutesPrefix.byNamePrefix());
  }

}
