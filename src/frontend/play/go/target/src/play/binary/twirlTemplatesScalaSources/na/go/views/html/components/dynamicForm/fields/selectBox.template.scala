
package na.go.views.html.components.dynamicForm.fields

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import scala.collection.mutable
/*2.2*/import scala.collection.immutable
/*3.2*/import na.naportalbase.settings.BaseMappings
/*4.2*/import na.naportalbase.views.html.components.selectsComp.dynamicSelectOptionComp
/*5.2*/import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.components.SelectBox
/*6.2*/import na.naportalbase.views.html.components.selectsComp.selectComp
/*7.2*/import na.naportalbase.views.tags.i18n

object selectBox extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*9.2*/(mapping: pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*11.2*/selectBox/*11.11*/ = {{mapping.getComponent.asInstanceOf[SelectBox]}};def /*12.2*/selectId/*12.10*/ = {{mapping.getTemplateMapping}};def /*13.2*/isRequired/*13.12*/ = {{selectBox.getRequired}};def /*14.2*/selectAttributes/*14.18*/ = {{

    var map = mutable.HashMap[String, String](
        "data-ng-model" -> ("model[\""+selectId+"\"]"),
        "data-model" -> selectId,
        "data-na-portal-select-box" -> null,
        "data-na-portal-go-dynamic-form-select-box" -> null,
        "name" -> selectId,
        "value" -> "",
        "id" -> selectId,
        "data-component" -> BaseMappings.dualMapper.valueToTree(selectBox),
        "class" -> "form-control input-sm"
    )
    if(isRequired){
        map.put("data-na-portal-required",null)
    }
    if(!selectBox.getEditable){
        map.put("disabled", null)
    }
    immutable.HashMap(map.toSeq:_*)

}};def /*37.2*/dynamicOptions/*37.16*/ = {{
    var stringBuilder = new StringBuilder
    for(option <- selectBox.getOptions){
        if (option.getVisible) {
            val selectOptionIndex = String.valueOf(option.getIndex)
            var optionAttributes = immutable.HashMap[String,String](
                "id" -> selectOptionIndex,
                "text" -> option.getLabel,
                "value" -> option.getValue
            )

            if(selectOptionIndex.equals(selectBox.getDefaultValue)){
                optionAttributes = immutable.HashMap[String,String](
                    "id" -> selectOptionIndex,
                    "text" -> option.getLabel,
                    "value" -> option.getValue,
                    "selected" -> null
                )
            }
            stringBuilder.append(dynamicSelectOptionComp.render(option.getLabel,optionAttributes))
        }
    }
    Html(stringBuilder.toString())
}};
Seq[Any](format.raw/*9.78*/("""

"""),format.raw/*11.61*/("""
"""),format.raw/*12.42*/("""
"""),format.raw/*13.39*/("""
"""),format.raw/*35.2*/("""

"""),format.raw/*60.2*/("""

"""),format.raw/*62.1*/("""<div class="form-group">
    <label for=""""),_display_(/*63.18*/mapping/*63.25*/.getLabel),format.raw/*63.34*/("""" class="col-sm-2 control-label """),_display_(/*63.67*/if(isRequired)/*63.81*/{_display_(Seq[Any](format.raw/*63.82*/("""fx-required""")))}),format.raw/*63.94*/("""" title=""""),_display_(/*63.104*/mapping/*63.111*/.getDescription),format.raw/*63.126*/("""">
        """),_display_(/*64.10*/mapping/*64.17*/.getLabel),_display_(/*64.27*/if(isRequired)/*64.41*/{_display_(Seq[Any](format.raw/*64.42*/("""<abbr title=""""),_display_(/*64.56*/i18n("na.basemodule.validations.mandatory")),format.raw/*64.99*/(""""></abbr>""")))}),format.raw/*64.109*/("""
    """),format.raw/*65.5*/("""</label>
    <div class="col-sm-10" data-na-portal-field-notification>
    """),_display_(/*67.6*/selectComp/*67.16*/.render(selectAttributes, null, null, dynamicOptions)),format.raw/*67.69*/("""
    """),format.raw/*68.5*/("""</div>
</div>"""))
      }
    }
  }

  def render(mapping:pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping): play.twirl.api.HtmlFormat.Appendable = apply(mapping)

  def f:((pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping) => play.twirl.api.HtmlFormat.Appendable) = (mapping) => apply(mapping)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:12 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/dynamicForm/fields/selectBox.scala.html
                  HASH: 2e3f4a8a0ebb24a6661a7d5df2774e4ea3315a5b
                  MATRIX: 687->1|726->34|767->69|819->115|907->197|1000->284|1075->353|1481->394|1636->473|1654->482|1718->534|1735->542|1781->576|1800->586|1841->615|1866->631|2516->1270|2539->1284|3474->470|3504->532|3533->574|3562->613|3590->1267|3619->2189|3648->2191|3717->2233|3733->2240|3763->2249|3823->2282|3846->2296|3885->2297|3928->2309|3966->2319|3983->2326|4020->2341|4059->2353|4075->2360|4105->2370|4128->2384|4167->2385|4208->2399|4272->2442|4314->2452|4346->2457|4448->2533|4467->2543|4541->2596|4573->2601
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|35->9|39->11|39->11|39->12|39->12|39->13|39->13|39->14|39->14|60->37|60->37|84->9|86->11|87->12|88->13|89->35|91->60|93->62|94->63|94->63|94->63|94->63|94->63|94->63|94->63|94->63|94->63|94->63|95->64|95->64|95->64|95->64|95->64|95->64|95->64|95->64|96->65|98->67|98->67|98->67|99->68
                  -- GENERATED --
              */
          