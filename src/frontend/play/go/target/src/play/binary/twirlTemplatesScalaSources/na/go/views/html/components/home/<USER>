
package na.go.views.html.components.home

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import scala.collection.mutable
/*2.2*/import scala.collection.immutable
/*3.2*/import na.naportalbase.views.tags.i18n
/*4.2*/import na.go.settings.GoConstants
/*5.2*/import na.go.controllers.routes.Search.getFilterNamesForSelect
/*6.2*/import na.naportalbase.views.html.components.selectsComp.selectInputComp
/*7.2*/import na.naportalbase.views.html.components.buttonsComp.caretDropdown
/*8.2*/import na.go.views.html.components.home.operationForm
/*9.2*/import na.naportalbase.views.html.components.buttonsComp.leftSideFormInputButtonsComp

object leftSideForm extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[na.go.models.general.OperCatFamilyList,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*11.2*/(groups : na.go.models.general.OperCatFamilyList):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*13.2*/caretTitle/*13.12*/ = {{i18n("na.buttons.caret.moreoptions")}};def /*14.2*/caretOptions/*14.14*/ = {{
    var map = mutable.LinkedHashMap[String,mutable.HashMap[String,mutable.HashMap[String,String]]]()

    var firstOptionTitle = i18n("na.modal.filters.title.editfilter")
    var secondOptionTitle = i18n("na.modal.filters.title.deletefilter")

    var firstOptionComponents = mutable.HashMap[String,mutable.HashMap[String,String]]()

    var firstOptionAattributes = mutable.HashMap[String,String]()
    firstOptionAattributes.put("id","editFilterButton")
    firstOptionAattributes.put("data-ng-click","operCtrl.openEditFilterModal()")

    var firstOptionIattributes = mutable.HashMap[String,String]()
    firstOptionIattributes.put("class","glyphicon glyphicon-pencil")

    firstOptionComponents.put("a",firstOptionAattributes)
    firstOptionComponents.put("i",firstOptionIattributes)

    var secondOptionComponents = mutable.HashMap[String,mutable.HashMap[String,String]]()

    var secondOptionAattributes = mutable.HashMap[String,String]()
    secondOptionAattributes.put("id","deleteFilterButton")
    secondOptionAattributes.put("data-ng-click","operCtrl.openDeleteFilterModal()")

    var secondOptionIattributes = mutable.HashMap[String,String]()
    secondOptionIattributes.put("class","glyphicon glyphicon-remove")

    secondOptionComponents.put("a",secondOptionAattributes)
    secondOptionComponents.put("i",secondOptionIattributes)

    map.put(firstOptionTitle,firstOptionComponents)
    map.put(secondOptionTitle,secondOptionComponents)

    map
}};def /*49.2*/selectAttributes/*49.18*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("class","input-sm fx-filter-input select2-offscreen")
    map.put("data-ng-model", "selectedFilter")
    map.put("name", "filters")
    map.put("data-na-portal-select-box",null)
    map.put("value", "")
    map.put("id", "rfsFilters")
    map.put("data-is-dynamic",null)
    map.put("data-context","filters")
    map.put("data-url",""+getFilterNamesForSelect(GoConstants.OPERATION_SEARCH_CONTEXT))
    map
}};def /*63.2*/form/*63.6*/ = {{
        operationForm.render("searchFormItems", immutable.HashMap[String,String](), groups, false)
}};def /*67.2*/searchButtonAttributes/*67.24*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-click", "operCtrl.submitSearchForm()")
    map.put("id", "searchButton")
    map.put("type","submit")
    map.put("value",i18n("na.buttons.search"))

    map
}};def /*77.2*/clearButtonAttributes/*77.23*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-click", "operCtrl.clearSearchFormItems()")
    map.put("id", "clearSearchButton")
    map.put("type","reset")
    map.put("value",i18n("na.buttons.clear"))

    map
}};
Seq[Any](format.raw/*11.51*/("""

"""),format.raw/*13.54*/("""
"""),format.raw/*48.2*/("""
"""),format.raw/*61.2*/("""

"""),format.raw/*65.2*/("""

"""),format.raw/*75.2*/("""

"""),format.raw/*85.2*/("""

"""),format.raw/*87.1*/("""<header class="fx-sidebar-header search-sidebar__header">
    <div class="fx-sidebar-header-inner">
        <p>
            <span class="fx-entity-header-icon">
                <i class="fa fa-search"></i>
            </span>
            <x-i18n key="na.general.filters"></x-i18n>
        </p>
    </div>
    <div class="form-group search-form--filter-select">
        <div class="search-form__selectbox">
            """),_display_(/*98.14*/selectInputComp/*98.29*/.render(selectAttributes)),format.raw/*98.54*/("""
        """),format.raw/*99.9*/("""</div>
        """),_display_(/*100.10*/caretDropdown/*100.23*/.render(caretTitle,caretOptions)),format.raw/*100.55*/("""
    """),format.raw/*101.5*/("""</div>
</header>
<div class="fx-sidebar-content-wrapper search-sidebar__field-list">
    <x-shadow-scroll vertical>
    """),_display_(/*105.6*/form),format.raw/*105.10*/("""
    """),format.raw/*106.5*/("""</x-shadow-scroll>
</div>
<div class="form-group search-sidebar__button-list">
    <div class="fx-splitter-sidebar-buttons">
        <div class="fx-splitter-sidebar-buttons-inner">
        """),_display_(/*111.10*/leftSideFormInputButtonsComp/*111.38*/.render(searchButtonAttributes,clearButtonAttributes)),format.raw/*111.91*/("""
        """),format.raw/*112.9*/("""</div>
    </div>
</div>
"""))
      }
    }
  }

  def render(groups:na.go.models.general.OperCatFamilyList): play.twirl.api.HtmlFormat.Appendable = apply(groups)

  def f:((na.go.models.general.OperCatFamilyList) => play.twirl.api.HtmlFormat.Appendable) = (groups) => apply(groups)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:21 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/home/<USER>
                  HASH: 0e762cf6ecf46914b3e0302be4f02d8dd269fd3b
                  MATRIX: 673->1|712->34|753->69|799->109|840->144|910->208|990->282|1068->354|1129->409|1559->497|1687->549|1706->559|1762->603|1783->615|3271->2091|3296->2107|3783->2583|3795->2587|3915->2696|3946->2718|4194->2955|4224->2976|4496->546|4526->601|4554->2089|4582->2580|4611->2693|4640->2952|4669->3217|4698->3219|5144->3638|5168->3653|5214->3678|5250->3687|5294->3703|5317->3716|5371->3748|5404->3753|5552->3874|5578->3878|5611->3883|5829->4073|5867->4101|5942->4154|5979->4163
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|32->9|37->11|41->13|41->13|41->14|41->14|75->49|75->49|87->63|87->63|89->67|89->67|97->77|97->77|106->11|108->13|109->48|110->61|112->65|114->75|116->85|118->87|129->98|129->98|129->98|130->99|131->100|131->100|131->100|132->101|136->105|136->105|137->106|142->111|142->111|142->111|143->112
                  -- GENERATED --
              */
          