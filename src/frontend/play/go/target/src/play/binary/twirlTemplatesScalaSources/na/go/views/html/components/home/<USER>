
package na.go.views.html.components.home

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import scala.collection.immutable
/*2.2*/import scala.collection.mutable
/*3.2*/import na.naportalbase.views.tags.i18n
/*4.2*/import na.naportalbase.views.html.components.inputsComp.sidebarInputTextComp
/*5.2*/import na.naportalbase.views.html.components.selectsComp.dynamicSelectOptionComp
/*6.2*/import pt.ptinovacao.na.portal.webui.restful.go.entities.States
/*7.2*/import na.go.views.components.home.OperationFormAttributes
/*8.2*/import na.naportalbase.views.html.components.selectsComp
/*9.2*/import na.naportalbase.views.html.components.inputsComp.modalInputTextComp
/*10.2*/import na.go.models.general.OperCatFamilyList
/*11.2*/import na.go.views.html.components.common.saveFilterButton

object operationForm extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[String,immutable.HashMap[String, String],OperCatFamilyList,Boolean,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*13.2*/(ngModelVariable: String, inputFieldIds: immutable.HashMap[String, String], groups : OperCatFamilyList, isModal: Boolean):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*170.2*/inputDateCompSidebar/*170.22*/(label: String, datePickerId: String, dateFormat: String):play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*170.83*/("""
    """),format.raw/*171.5*/("""<label for=""""),_display_(/*171.18*/datePickerId),format.raw/*171.30*/("""">"""),_display_(/*171.33*/label),format.raw/*171.38*/("""</label>
    <x-date-time-picker data-field=""""),_display_(/*172.38*/datePickerId),format.raw/*172.50*/("""" format=""""),_display_(/*172.61*/dateFormat),format.raw/*172.71*/(""""></x-date-time-picker>
""")))};def /*175.2*/inputDateCompModal/*175.20*/(label: String, datePickerId: String, dateFormat: String):play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*175.81*/("""
    """),format.raw/*176.5*/("""<label class="control-label col-sm-3" for=""""),_display_(/*176.49*/datePickerId),format.raw/*176.61*/("""">"""),_display_(/*176.64*/label),format.raw/*176.69*/("""</label>
    <div class="col-sm-9">
        <x-date-time-picker data-field=""""),_display_(/*178.42*/datePickerId),format.raw/*178.54*/("""" format=""""),_display_(/*178.65*/dateFormat),format.raw/*178.75*/(""""></x-date-time-picker>
    </div>
""")))};def /*15.2*/prefixId/*15.10*/ = {{if(isModal) "modal-" else ""}};def /*18.2*/selectAttributes/*18.18*/(attributesMap: immutable.HashMap[String, String]) = {{
    attributesMap + ("style" -> "width: 100%")
}};def /*23.2*/startDateConfig/*23.17*/ = {{OperationFormAttributes.OrderStartDate(
    id = inputFieldIds.getOrElse("orderStartDatePicker","orderStartDatePicker"),
    inputId = inputFieldIds.getOrElse("startDate","startDate"),
    formModel = ngModelVariable,isModal = isModal,
    pairWith = inputFieldIds.getOrElse("orderEndDatePicker","orderEndDatePicker")
)}};def /*30.2*/endDateConfig/*30.15*/ = {{OperationFormAttributes.OrderEndDate(
        id = inputFieldIds.getOrElse("orderEndDatePicker","orderEndDatePicker"),
        inputId = inputFieldIds.getOrElse("endDate","endDate"),
        formModel = ngModelVariable,isModal = isModal,
        pairWith = inputFieldIds.getOrElse("orderStartDatePicker","orderStartDatePicker")
    )}};def /*38.2*/orderLabel/*38.12*/ = {{i18n("na.portal.go.field.orderKey.label")}};def /*39.2*/orderPlaceHolder/*39.18*/ = {{i18n("na.portal.go.field.orderKey.placeholder")}};def /*40.2*/orderKeyInputId/*40.17*/ = {{inputFieldIds.getOrElse("orderKey","orderKey")}};def /*41.2*/orderKeyInputAttributes/*41.25*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-entity", orderKeyInputId)
    map.put("data-ng-model", ngModelVariable+"."+orderKeyInputId)
    map.put("name", orderKeyInputId)
    map.put("id", prefixId+orderKeyInputId)
    map
}};def /*53.2*/userLabel/*53.11*/ = {{i18n("na.portal.go.field.user.label")}};def /*54.2*/userPlaceHolder/*54.17*/ = {{i18n("na.portal.go.field.user.placeholder")}};def /*55.2*/userInputId/*55.13*/ = {{inputFieldIds.getOrElse("username","username")}};def /*56.2*/userInputAttributes/*56.21*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-model", ngModelVariable + "."+userInputId)
    map.put("name", "username")
    map.put("id", prefixId+userInputId)
    map
}};def /*67.2*/operationFamilyId/*67.19*/ = {{inputFieldIds.getOrElse("family-field","family-field")}};def /*68.2*/operationFamilyLabel/*68.22*/ = {{i18n("na.portal.go.family.field.label")}};def /*69.2*/operationFamilyInitialOption/*69.30*/ = {{i18n("na.portal.go.family.selects.empty.option")}};def /*70.2*/operationFamilySelectAttributes/*70.33*/ = {{
    immutable.HashMap[String, String](
        "data-ng-model" -> (ngModelVariable+".family"),
        "name" -> operationFamilyId,
        "data-na-portal-select-box" -> null,
        "data-na-portal-select-box-auto-dropdown-size" -> null,
        "placeholder" -> i18n("na.portal.go.family.selects.empty.option"),
        "value" -> "",
        "id" -> (prefixId+operationFamilyId),
        "class" -> "form-control input-sm",
        "data-context" -> "family",
        "allow-clear" -> null
        )
    }};def /*85.2*/operationFamilySelectData/*85.27*/ = {{
    var stringBuilder = new StringBuilder

   for(group <- groups.getEntries()){
      var optionAttributes = immutable.HashMap[String,String](
          "id" -> group.getName,
          "text" -> group.getName,
          "value" -> group.getName
      )
       stringBuilder.append(dynamicSelectOptionComp.render(group.getName,optionAttributes))

   }

    Html(stringBuilder.toString())
}};def /*103.2*/operationRfsId/*103.16*/ = {{inputFieldIds.getOrElse("selectRfsNames","selectRfsNames")}};def /*104.2*/operationRfsSelectAttributes/*104.30*/ = {{
    immutable.HashMap[String, String](
            "data-ng-model" -> (ngModelVariable+".rfs"),
            "name" -> operationRfsId,
            "data-na-portal-select-box" -> null,
            "data-na-portal-select-box-auto-dropdown-size" -> null,
            "placeholder" -> i18n("na.portal.go.rfs.selects.empty.option"),
            "id" -> (prefixId+operationRfsId),
            "data-is-dynamic" -> null,
            "value" -> "",
            "class" -> "form-control input-sm inherit-default-color",
            "data-context" -> "rfs",
            "allow-clear" -> null
        )
    }};def /*121.2*/operationOpId/*121.15*/ = {{inputFieldIds.getOrElse("operation","operation")}};def /*122.2*/operationOpLabel/*122.18*/ = {{i18n("na.portal.go.operation.field.label")}};def /*123.2*/operationOpSelectAttributes/*123.29*/ = {{
    immutable.HashMap[String, String](
    "data-ng-model" -> (ngModelVariable+".operationName"),
    "name" -> operationOpId,
    "data-na-portal-select-box" -> null,
    "data-na-portal-select-box-auto-dropdown-size" -> null,
    "value" -> "",
    "data-is-dynamic" -> null,
    "placeholder" -> i18n("na.portal.go.operation.selects.empty.option"),
    "id" -> (prefixId+operationOpId),
    "class" -> "form-control input-sm inherit-default-color",
    "data-context" -> "operation",
    "allow-clear" -> null
)}};def /*139.2*/operationStateId/*139.18*/ = {{inputFieldIds.getOrElse("executionState","executionState")}};def /*140.2*/operationStateLabel/*140.21*/ = {{i18n("na.portal.go.state.field.label")}};def /*141.2*/operationStateSelectAttributes/*141.32*/ = {{ immutable.HashMap[String, String](
    "data-ng-model" -> (ngModelVariable+".executionState"),
    "name" -> operationStateId,
    "data-na-portal-select-box" -> null,
    "placeholder" -> i18n("na.portal.go.state.selects.empty.option"),
    "value" -> "",
    "id" -> (prefixId+operationStateId),
    "class" -> "form-control input-sm",
    "data-context" -> "state",
    "allow-clear" -> null
 )}};def /*153.2*/operationStateSelectData/*153.26*/ = {{

    var list = States.values()
    var stringBuilder = new StringBuilder
    for(option <- list){
      var optionName = option.name()
      var optionAttributes = immutable.HashMap[String, String](
        "name" -> optionName,
        "id" -> optionName,
        "value" -> optionName
      )
      var text = i18n("na.portal.go.operation.state."+optionName+".label")
      stringBuilder.append(dynamicSelectOptionComp.render(text,optionAttributes))
  }
  Html(stringBuilder.toString())
}};
Seq[Any](format.raw/*13.123*/("""

"""),format.raw/*15.44*/("""


"""),format.raw/*20.2*/("""

"""),format.raw/*22.1*/("""<!-- START DATE CONFIG -->
"""),format.raw/*28.3*/("""
"""),format.raw/*29.1*/("""<!-- END DATE CONFIG -->
"""),format.raw/*35.7*/("""

"""),format.raw/*37.1*/("""<!-- ORDER KEY FIELD CONFIG -->
"""),format.raw/*38.59*/("""
"""),format.raw/*39.71*/("""
"""),format.raw/*40.69*/("""
"""),format.raw/*48.2*/("""



"""),format.raw/*52.1*/("""<!-- USER KEY FIELD -->
"""),format.raw/*53.54*/("""
"""),format.raw/*54.66*/("""
"""),format.raw/*55.65*/("""
"""),format.raw/*62.2*/("""


  
"""),format.raw/*66.1*/("""<!-- FAMILY FIELD CONFIG -->  
"""),format.raw/*67.93*/("""
"""),format.raw/*68.78*/("""
"""),format.raw/*69.87*/("""
"""),format.raw/*83.6*/("""

"""),format.raw/*99.2*/("""


"""),format.raw/*102.1*/("""<!-- RFS FIELD CONFIG -->  
"""),format.raw/*103.80*/("""
"""),format.raw/*118.6*/("""

"""),format.raw/*120.1*/("""<!-- OPERATION FIELD CONFIG -->  
"""),format.raw/*121.69*/("""
"""),format.raw/*122.66*/("""
"""),format.raw/*136.3*/("""
  
"""),format.raw/*138.1*/("""<!-- STATE FIELD CONFIG -->  
"""),format.raw/*139.82*/("""
"""),format.raw/*140.65*/("""
"""),format.raw/*151.4*/("""

"""),format.raw/*168.2*/("""

"""),format.raw/*173.2*/("""

"""),format.raw/*180.2*/("""



"""),_display_(/*184.2*/if(!isModal)/*184.14*/ {_display_(Seq[Any](format.raw/*184.16*/("""

"""),format.raw/*186.1*/("""<!-- SIDEBAR START DATE FIELD -->  
<div class="form-group" data-na-portal-field-notification data-na-portal-button-notification>
    """),_display_(/*188.6*/inputDateCompSidebar(startDateConfig.label, startDateConfig.inputId, "short")),format.raw/*188.83*/("""
"""),format.raw/*189.1*/("""</div>

<!-- SIDEBAR END DATE FIELD -->  
<div class="form-group" data-na-portal-field-notification data-na-portal-button-notification>
    """),_display_(/*193.6*/inputDateCompSidebar(endDateConfig.label, endDateConfig.inputId, "short")),format.raw/*193.79*/("""
"""),format.raw/*194.1*/("""</div>

<!-- SIDEBAR ORDER KEY FIELD -->  
<div class="form-group">
    """),_display_(/*198.6*/sidebarInputTextComp/*198.26*/.render(orderLabel, orderKeyInputId,orderPlaceHolder,orderKeyInputAttributes,false)),format.raw/*198.109*/("""
"""),format.raw/*199.1*/("""</div>

<!-- SIDEBAR USER FIELD -->
<div class="form-group">
    """),_display_(/*203.6*/sidebarInputTextComp/*203.26*/.render(userLabel, userInputId, userPlaceHolder, userInputAttributes, false)),format.raw/*203.102*/("""
"""),format.raw/*204.1*/("""</div>

<!-- SIDEBAR FAMILY FIELD -->  
<div class="form-group">
    <label for=""""),_display_(/*208.18*/{operationFamilyId}),format.raw/*208.37*/("""">"""),_display_(/*208.40*/operationFamilyLabel),format.raw/*208.60*/("""</label><br/>
        """),_display_(/*209.10*/selectsComp/*209.21*/.selectComp.render(
                selectAttributes(operationFamilySelectAttributes), null, null, operationFamilySelectData)),format.raw/*210.106*/("""
"""),format.raw/*211.1*/("""</div>


<!-- SIDEBAR RFS FIELD -->  
<div class="form-group">
        <label for=""""),_display_(/*216.22*/{operationRfsId}),format.raw/*216.38*/("""">"""),_display_(/*216.41*/i18n("na.portal.go.rfs.field.label")),format.raw/*216.77*/("""</label>
    <br/>
        """),_display_(/*218.10*/selectsComp/*218.21*/.selectComp.render(selectAttributes(operationRfsSelectAttributes), null, null, null)),format.raw/*218.105*/("""
"""),format.raw/*219.1*/("""</div>

<!-- SIDEBAR OPERATION FIELD -->  
<div class="form-group">
    <label for=""""),_display_(/*223.18*/{operationOpId}),format.raw/*223.33*/("""">"""),_display_(/*223.36*/{operationOpLabel}),format.raw/*223.54*/("""</label>
    <br/>
          """),_display_(/*225.12*/selectsComp/*225.23*/.selectComp.render(selectAttributes(operationOpSelectAttributes), null, null, null)),format.raw/*225.106*/("""
"""),format.raw/*226.1*/("""</div>


<!-- SIDEBAR STATE FIELD -->  
<div class="form-group">
    <label for=""""),_display_(/*231.18*/{operationStateId}),format.raw/*231.36*/("""">"""),_display_(/*231.39*/{operationStateLabel}),format.raw/*231.60*/("""</label>
    <br/>
    """),_display_(/*233.6*/selectsComp/*233.17*/.selectComp.render(
            selectAttributes(operationStateSelectAttributes), null, null, operationStateSelectData)),format.raw/*234.100*/("""
"""),format.raw/*235.1*/("""</div>

<!-- SAVE FILTER BUTTON -->
<div class="form-group search-form--save-filter-button">
    """),_display_(/*239.6*/saveFilterButton/*239.22*/.render("operCtrl.openSaveFilterModal()")),format.raw/*239.63*/("""
"""),format.raw/*240.1*/("""</div>


""")))}/*243.3*/else/*243.8*/{_display_(Seq[Any](format.raw/*243.9*/("""

"""),format.raw/*245.1*/("""<!-- MODAL START DATE FIELD -->  
<div class="form-group" data-na-portal-field-notification data-na-portal-button-notification>
    """),_display_(/*247.6*/inputDateCompModal(startDateConfig.label, startDateConfig.inputId, "short")),format.raw/*247.81*/("""
"""),format.raw/*248.1*/("""</div>

<!-- MODAL END DATE FIELD -->  
<div class="form-group" data-na-portal-field-notification data-na-portal-button-notification>
    """),_display_(/*252.6*/inputDateCompModal(endDateConfig.label, endDateConfig.inputId, "short")),format.raw/*252.77*/("""
"""),format.raw/*253.1*/("""</div>

<!-- MODAL ORDER KEY FIELD -->  
<div class="form-group">
	"""),_display_(/*257.3*/modalInputTextComp/*257.21*/.render(orderLabel, prefixId+orderKeyInputId,
	orderPlaceHolder,orderKeyInputAttributes)),format.raw/*258.43*/("""
"""),format.raw/*259.1*/("""</div>

<!-- MODAL USER FIELD -->
<div class="form-group">
    """),_display_(/*263.6*/modalInputTextComp/*263.24*/.render(userLabel, prefixId+userInputId,
        userPlaceHolder, userInputAttributes)),format.raw/*264.46*/("""
"""),format.raw/*265.1*/("""</div>

<!-- MODAL FAMILY FIELD -->  
<div class="form-group">
    <label for=""""),_display_(/*269.18*/{operationFamilyId}),format.raw/*269.37*/("""" class="control-label col-sm-3">"""),_display_(/*269.71*/{operationFamilyLabel}),format.raw/*269.93*/("""</label>
    <div class="col-sm-9">
        """),_display_(/*271.10*/selectsComp/*271.21*/.selectComp.render(
                selectAttributes(operationFamilySelectAttributes), null, null, operationFamilySelectData)),format.raw/*272.106*/("""
    """),format.raw/*273.5*/("""</div>
</div>

<!-- MODAL RFS FIELD -->  
<div class="form-group">
    <label for="selectRfsNames" class="control-label col-sm-3">"""),_display_(/*278.65*/{i18n("na.portal.go.rfs.field.label")}),format.raw/*278.103*/("""</label>
    <div class="col-sm-9">
        """),_display_(/*280.10*/selectsComp/*280.21*/.selectComp.render(selectAttributes(operationRfsSelectAttributes), null, null, null)),format.raw/*280.105*/("""
   """),format.raw/*281.4*/("""</div>
</div>

<!-- MODAL OPERATION FIELD -->  
<div class="form-group">
    <label for=""""),_display_(/*286.18*/{operationOpId}),format.raw/*286.33*/("""" class="control-label col-sm-3">"""),_display_(/*286.67*/{operationOpLabel}),format.raw/*286.85*/("""</label>
    <div class="col-sm-9">
          """),_display_(/*288.12*/selectsComp/*288.23*/.selectComp.render(selectAttributes(operationOpSelectAttributes), null, null, null)),format.raw/*288.106*/("""
      """),format.raw/*289.7*/("""</div>
</div>

<!-- MODAL STATE FIELD -->  
<div class="form-group">
    <label for=""""),_display_(/*294.18*/{operationStateId}),format.raw/*294.36*/("""" class="control-label col-sm-3">"""),_display_(/*294.70*/{operationStateLabel}),format.raw/*294.91*/("""</label>
    <div class="col-sm-9">
            """),_display_(/*296.14*/selectsComp/*296.25*/.selectComp.render(
                    selectAttributes(operationStateSelectAttributes), null, null, operationStateSelectData)),format.raw/*297.108*/("""
    """),format.raw/*298.5*/("""</div>
</div>

""")))}))
      }
    }
  }

  def render(ngModelVariable:String,inputFieldIds:immutable.HashMap[String, String],groups:OperCatFamilyList,isModal:Boolean): play.twirl.api.HtmlFormat.Appendable = apply(ngModelVariable,inputFieldIds,groups,isModal)

  def f:((String,immutable.HashMap[String, String],OperCatFamilyList,Boolean) => play.twirl.api.HtmlFormat.Appendable) = (ngModelVariable,inputFieldIds,groups,isModal) => apply(ngModelVariable,inputFieldIds,groups,isModal)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:12 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/home/<USER>
                  HASH: dfcdb0e1346eb2319a55ab682bbd088a7c2e95a6
                  MATRIX: 673->1|714->36|753->69|799->109|883->187|971->269|1042->334|1108->394|1172->452|1255->528|1309->575|1741->636|1942->6490|1972->6510|2111->6571|2144->6576|2185->6589|2219->6601|2250->6604|2277->6609|2351->6655|2385->6667|2424->6678|2456->6688|2505->6716|2533->6734|2672->6795|2705->6800|2777->6844|2811->6856|2842->6859|2869->6864|2974->6941|3008->6953|3047->6964|3079->6974|3138->760|3155->768|3203->806|3228->822|3346->956|3370->971|3709->1323|3731->1336|4084->1710|4103->1720|4164->1769|4189->1785|4256->1840|4280->1855|4346->1909|4378->1932|4646->2215|4664->2224|4721->2269|4745->2284|4808->2335|4828->2346|4894->2400|4922->2419|5135->2656|5161->2673|5235->2749|5264->2769|5323->2827|5360->2855|5428->2914|5468->2945|5998->3464|6032->3489|6443->3917|6467->3931|6546->3997|6584->4025|7201->4664|7224->4677|7293->4733|7319->4749|7382->4799|7419->4826|7955->5382|7981->5398|8060->5464|8089->5483|8148->5529|8188->5559|8607->5966|8641->5990|9170->757|9200->802|9230->926|9259->928|9313->1296|9341->1297|9393->1675|9422->1677|9482->1767|9511->1838|9540->1907|9568->2186|9599->2190|9651->2267|9680->2333|9709->2398|9737->2618|9770->2624|9829->2747|9858->2825|9887->2912|9915->3461|9944->3885|9975->3888|10032->3995|10061->4627|10091->4629|10154->4731|10184->4797|10213->5347|10245->5351|10304->5462|10334->5527|10363->5963|10393->6487|10423->6713|10453->7010|10485->7015|10507->7027|10548->7029|10578->7031|10740->7166|10839->7243|10868->7244|11036->7385|11131->7458|11160->7459|11260->7532|11290->7552|11396->7635|11425->7636|11518->7702|11548->7722|11647->7798|11676->7799|11786->7881|11827->7900|11858->7903|11900->7923|11951->7946|11972->7957|12120->8082|12149->8083|12261->8167|12299->8183|12330->8186|12388->8222|12444->8250|12465->8261|12572->8345|12601->8346|12714->8431|12751->8446|12782->8449|12822->8467|12880->8497|12901->8508|13007->8591|13036->8592|13146->8674|13186->8692|13217->8695|13260->8716|13311->8740|13332->8751|13474->8870|13503->8871|13628->8969|13654->8985|13717->9026|13746->9027|13775->9038|13788->9043|13827->9044|13857->9046|14017->9179|14114->9254|14143->9255|14309->9394|14402->9465|14431->9466|14526->9534|14554->9552|14664->9640|14693->9641|14784->9705|14812->9723|14920->9809|14949->9810|15057->9890|15098->9909|15160->9943|15204->9965|15277->10010|15298->10021|15446->10146|15479->10151|15638->10282|15699->10320|15772->10365|15793->10376|15900->10460|15932->10464|16050->10554|16087->10569|16149->10603|16189->10621|16264->10668|16285->10679|16391->10762|16426->10769|16540->10855|16580->10873|16642->10907|16685->10928|16762->10977|16783->10988|16933->11115|16966->11120
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|32->9|33->10|34->11|39->13|43->170|43->170|45->170|46->171|46->171|46->171|46->171|46->171|47->172|47->172|47->172|47->172|48->175|48->175|50->175|51->176|51->176|51->176|51->176|51->176|53->178|53->178|53->178|53->178|55->15|55->15|55->18|55->18|57->23|57->23|62->30|62->30|67->38|67->38|67->39|67->39|67->40|67->40|67->41|67->41|74->53|74->53|74->54|74->54|74->55|74->55|74->56|74->56|80->67|80->67|80->68|80->68|80->69|80->69|80->70|80->70|93->85|93->85|107->103|107->103|107->104|107->104|121->121|121->121|121->122|121->122|121->123|121->123|134->139|134->139|134->140|134->140|134->141|134->141|144->153|144->153|160->13|162->15|165->20|167->22|168->28|169->29|170->35|172->37|173->38|174->39|175->40|176->48|180->52|181->53|182->54|183->55|184->62|188->66|189->67|190->68|191->69|192->83|194->99|197->102|198->103|199->118|201->120|202->121|203->122|204->136|206->138|207->139|208->140|209->151|211->168|213->173|215->180|219->184|219->184|219->184|221->186|223->188|223->188|224->189|228->193|228->193|229->194|233->198|233->198|233->198|234->199|238->203|238->203|238->203|239->204|243->208|243->208|243->208|243->208|244->209|244->209|245->210|246->211|251->216|251->216|251->216|251->216|253->218|253->218|253->218|254->219|258->223|258->223|258->223|258->223|260->225|260->225|260->225|261->226|266->231|266->231|266->231|266->231|268->233|268->233|269->234|270->235|274->239|274->239|274->239|275->240|278->243|278->243|278->243|280->245|282->247|282->247|283->248|287->252|287->252|288->253|292->257|292->257|293->258|294->259|298->263|298->263|299->264|300->265|304->269|304->269|304->269|304->269|306->271|306->271|307->272|308->273|313->278|313->278|315->280|315->280|315->280|316->281|321->286|321->286|321->286|321->286|323->288|323->288|323->288|324->289|329->294|329->294|329->294|329->294|331->296|331->296|332->297|333->298
                  -- GENERATED --
              */
          