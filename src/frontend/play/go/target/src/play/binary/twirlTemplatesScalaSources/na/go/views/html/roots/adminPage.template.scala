
package na.go.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.go.views.html.skeletons.mainSkel
/*2.2*/import na.naportalbase.views.tags.i18n
/*3.2*/import na.go.settings.GoConstants.NavContext
/*4.2*/import na.go.views.html.components.adminpage.leftSideForm
/*5.2*/import na.go.views.html.components.common.lateralNav
/*6.2*/import na.go.controllers.routes.TableConfigs

object adminPage extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[na.go.models.general.OperCatFamilyList,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*8.2*/(groups : na.go.models.general.OperCatFamilyList):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*10.2*/leftSide/*10.10*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*10.14*/("""
    """),_display_(/*11.6*/leftSideForm/*11.18*/.render(groups)),format.raw/*11.33*/("""
""")))};def /*14.2*/rightSide/*14.11*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*14.15*/("""
    """),format.raw/*15.5*/("""<div class="fx-splitter-content-inner">
        <div class="fx-entity-header">
            <div class="fx-entity-header-info">
                <span class="fx-entity-header-icon">
                    <i class="fuxicons fuxicons-go-admin"></i>
                </span>
                <div class="fx-entity-header-title">
                    <h1>
                    """),_display_(/*23.22*/i18n("na.portal.go.admin.label.title")),format.raw/*23.60*/("""
                    """),format.raw/*24.21*/("""</h1>
                    <p class="fx-entity-header-details">
                    """),_display_(/*26.22*/i18n("na.portal.go.admin.label.subtitle")),format.raw/*26.63*/("""
                    """),format.raw/*27.21*/("""</p>
                </div>
            </div>
            <div class="fx-entity-header-actions">
                <button id="create-button" class="btn btn-primary fx-call-to-action" data-page-action="create-operation">
                    <i class="glyphicon glyphicon-plus"></i>
                    """),_display_(/*33.22*/i18n("na.portal.go.buttons.createoperation")),format.raw/*33.66*/("""
                """),format.raw/*34.17*/("""</button>
            </div>
        </div>
        <div id="entity-content" class="fx-entity-info">
            <x-shadow-scroll vertical>
                <div
                    data-na-portal-table-datatable
                    data-na-portal-table-load-using-ajax
                    data-config-url=""""),_display_(/*42.39*/TableConfigs/*42.51*/.templatesSearch()),format.raw/*42.69*/(""""
                    data-na-portal-go-templates-search-table>
                    <div data-na-portal-table-has-advanced-search></div>
                    <div data-na-portal-table-has-column-filter></div>
                </div>
            </x-shadow-scroll>
        </div>
    </div>
""")))};def /*52.2*/navBar/*52.8*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*52.12*/("""
    """),_display_(/*53.6*/lateralNav/*53.16*/.render(NavContext.ADMIN_TEMPLATES)),format.raw/*53.51*/("""
""")))};
Seq[Any](format.raw/*8.51*/("""

"""),format.raw/*12.2*/("""

"""),format.raw/*50.2*/("""

"""),format.raw/*54.2*/("""

"""),_display_(/*56.2*/mainSkel()/*56.12*/{_display_(Seq[Any](format.raw/*56.13*/("""
    """),format.raw/*57.5*/("""<div class="fx-push-footer page--go-search-page fx-full-height" data-na-portal-go-common data-na-portal-go-template-search-page>
        """),_display_(/*58.10*/navBar),format.raw/*58.16*/("""
        """),format.raw/*59.9*/("""<div data-fuxi-header></div>
        <x-splitter class="splitter splitter--go-admin-search">
            <form slot="left" class="search-sidebar">
            """),_display_(/*62.14*/leftSide),format.raw/*62.22*/("""
            """),format.raw/*63.13*/("""</form>
            <div id="fx-splitter-content" slot="right">
            """),_display_(/*65.14*/rightSide),format.raw/*65.23*/("""
            """),format.raw/*66.13*/("""</div>
            <div slot="collapsed-left">
                <div class="fx-info-sidebar-collapsed left">
                    <div class="fx-sidebar-header search-sidebar__header">
                        <span class="fx-entity-header-icon">
                            <i class="fa fa-search"></i>
                        </span>
                    </div>
                </div>
            </div>
        </x-splitter>
    </div>
""")))}))
      }
    }
  }

  def render(groups:na.go.models.general.OperCatFamilyList): play.twirl.api.HtmlFormat.Appendable = apply(groups)

  def f:((na.go.models.general.OperCatFamilyList) => play.twirl.api.HtmlFormat.Appendable) = (groups) => apply(groups)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:12 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/roots/adminPage.scala.html
                  HASH: d229e76abf51bb784fc2e450a52f18cfcc10e9b7
                  MATRIX: 663->1|713->45|759->85|811->131|876->190|936->244|1321->291|1449->343|1466->351|1547->355|1579->361|1600->373|1636->388|1661->393|1679->402|1760->406|1792->411|2185->777|2244->815|2293->836|2404->920|2466->961|2515->982|2844->1284|2909->1328|2954->1345|3288->1652|3309->1664|3348->1682|3660->1974|3674->1980|3755->1984|3787->1990|3806->2000|3862->2035|3903->340|3932->390|3961->1971|3990->2037|4019->2040|4038->2050|4077->2051|4109->2056|4274->2194|4301->2200|4337->2209|4524->2369|4553->2377|4594->2390|4698->2467|4728->2476|4769->2489
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|34->8|38->10|38->10|40->10|41->11|41->11|41->11|42->14|42->14|44->14|45->15|53->23|53->23|54->24|56->26|56->26|57->27|63->33|63->33|64->34|72->42|72->42|72->42|80->52|80->52|82->52|83->53|83->53|83->53|85->8|87->12|89->50|91->54|93->56|93->56|93->56|94->57|95->58|95->58|96->59|99->62|99->62|100->63|102->65|102->65|103->66
                  -- GENERATED --
              */
          