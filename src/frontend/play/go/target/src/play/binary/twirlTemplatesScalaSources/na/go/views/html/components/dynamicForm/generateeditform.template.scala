
package na.go.views.html.components.dynamicForm

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import pt.ptinovacao.na.portal.webui.restful.commons.model.ElementTypes
/*2.2*/import na.go.views.html.components.dynamicForm.fields.textField
/*3.2*/import na.go.views.html.components.dynamicForm.fields.fileField
/*4.2*/import na.go.views.html.components.dynamicForm.fields.textArea
/*5.2*/import na.go.views.html.components.dynamicForm.fields.checkList
/*6.2*/import na.go.views.html.components.dynamicForm.fields.radioButtons
/*7.2*/import na.go.views.html.components.dynamicForm.fields.selectBoxWithSwitch

object generateeditform extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[List[pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*9.2*/(mappings: List[pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*9.85*/("""

"""),_display_(/*11.2*/for(mapping <- mappings) yield /*11.26*/ {_display_(Seq[Any](format.raw/*11.28*/("""

    """),_display_(/*13.6*/ElementTypes/*13.18*/.getElementTypeByClass(mapping.getComponent.getClass.getName)/*13.79*/ match/*13.85*/ {/*14.9*/case ElementTypes.TEXT_FIELD =>/*14.40*/ {_display_(Seq[Any](format.raw/*14.42*/("""
            """),_display_(/*15.14*/textField/*15.23*/.render(mapping)),format.raw/*15.39*/("""
        """)))}/*17.9*/case ElementTypes.FILE_FIELD =>/*17.40*/ {_display_(Seq[Any](format.raw/*17.42*/("""
            """),_display_(/*18.14*/fileField/*18.23*/.render(mapping)),format.raw/*18.39*/("""
        """)))}/*20.9*/case ElementTypes.TEXT_AREA =>/*20.39*/ {_display_(Seq[Any](format.raw/*20.41*/("""
            """),_display_(/*21.14*/textArea/*21.22*/.render(mapping)),format.raw/*21.38*/("""
        """)))}/*23.9*/case ElementTypes.CHECK_LIST =>/*23.40*/ {_display_(Seq[Any](format.raw/*23.42*/("""
            """),_display_(/*24.14*/checkList/*24.23*/.render(mapping)),format.raw/*24.39*/("""
        """)))}/*26.9*/case ElementTypes.RADIO_BUTTON =>/*26.42*/ {_display_(Seq[Any](format.raw/*26.44*/("""
            """),_display_(/*27.14*/radioButtons/*27.26*/.render(mapping)),format.raw/*27.42*/("""
        """)))}/*29.9*/case ElementTypes.SELECT_BOX =>/*29.40*/ {_display_(Seq[Any](format.raw/*29.42*/("""
            """),_display_(/*30.14*/selectBoxWithSwitch/*30.33*/.render(mapping)),format.raw/*30.49*/("""
        """)))}}),format.raw/*32.6*/("""

""")))}))
      }
    }
  }

  def render(mappings:List[pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping]): play.twirl.api.HtmlFormat.Appendable = apply(mappings)

  def f:((List[pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping]) => play.twirl.api.HtmlFormat.Appendable) = (mappings) => apply(mappings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:21 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/dynamicForm/generateeditform.scala.html
                  HASH: dc348d7b8b044bd6a121c37116f4550996a80f30
                  MATRIX: 680->1|759->74|830->139|901->204|971->268|1042->333|1116->401|1570->477|1748->560|1777->563|1817->587|1857->589|1890->596|1911->608|1981->669|1996->675|2006->686|2046->717|2086->719|2127->733|2145->742|2182->758|2210->777|2250->808|2290->810|2331->824|2349->833|2386->849|2414->868|2453->898|2493->900|2534->914|2551->922|2588->938|2616->957|2656->988|2696->990|2737->1004|2755->1013|2792->1029|2820->1048|2862->1081|2902->1083|2943->1097|2964->1109|3001->1125|3029->1144|3069->1175|3109->1177|3150->1191|3178->1210|3215->1226|3256->1242
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|35->9|40->9|42->11|42->11|42->11|44->13|44->13|44->13|44->13|44->14|44->14|44->14|45->15|45->15|45->15|46->17|46->17|46->17|47->18|47->18|47->18|48->20|48->20|48->20|49->21|49->21|49->21|50->23|50->23|50->23|51->24|51->24|51->24|52->26|52->26|52->26|53->27|53->27|53->27|54->29|54->29|54->29|55->30|55->30|55->30|56->32
                  -- GENERATED --
              */
          