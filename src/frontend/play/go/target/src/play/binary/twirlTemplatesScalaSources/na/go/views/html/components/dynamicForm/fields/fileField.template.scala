
package na.go.views.html.components.dynamicForm.fields

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.settings.BaseMappings
/*2.2*/import scala.collection.mutable
/*3.2*/import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.components.FileField
/*4.2*/import na.naportalbase.views.html.components.buttonsComp.uploadButtonComp
/*5.2*/import na.naportalbase.views.tags.i18n

object fileField extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*7.2*/(mapping: pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*9.2*/fileFieldComponent/*9.20*/ = {{mapping.getComponent.asInstanceOf[FileField]}};def /*10.2*/inputLabel/*10.12*/ = {{if(mapping.getLabel != null) mapping.getLabel else "" }};def /*11.2*/inputId/*11.9*/ = {{mapping.getTemplateMapping}};def /*12.2*/placeHolder/*12.13*/ = {{fileFieldComponent.getMessage}};def /*13.2*/isRequired/*13.12*/ = {{fileFieldComponent.getRequired}};def /*14.2*/inputAttributes/*14.17*/ = {{
	var map = mutable.HashMap[String, String](
		"data-ng-model" -> ("model[\""+inputId+"\"]"),
		"data-model" -> inputId,
		"data-na-portal-go-dynamic-form-text-field" -> null,
		"name" -> inputId,
		"value" -> "",
		"id" -> inputId,
		"data-component" -> BaseMappings.dualMapper.valueToTree(fileFieldComponent),
		"class" -> "upload"
	)

	if(fileFieldComponent.getRegex != null){
		map.put("data-na-portal-regex-error",null)
		map.put("regex", fileFieldComponent.getRegex)
		map.put("notification-message", fileFieldComponent.getRegexErrorMessage)
	}
	if(fileFieldComponent.getMaxSize != null){
		map.put("maxlength", String.valueOf(fileFieldComponent.getMaxSize))
	}
	if(fileFieldComponent.getDefaultValue != null){
		map.put("value", fileFieldComponent.getDefaultValue)
	}
	if(isRequired){
		map.put("data-na-portal-required", null)
	}
	if(!fileFieldComponent.getEditable){
		map.put("disabled", null)
	}

	map
}};
Seq[Any](format.raw/*7.78*/("""

"""),format.raw/*9.70*/("""
"""),format.raw/*10.72*/("""
"""),format.raw/*11.41*/("""
"""),format.raw/*12.48*/("""
"""),format.raw/*13.48*/("""
"""),format.raw/*45.2*/("""

"""),format.raw/*47.1*/("""<div class="form-group">
	<label for=""""),_display_(/*48.15*/inputId),format.raw/*48.22*/("""" class="col-sm-2 control-label """),_display_(/*48.55*/if(isRequired)/*48.69*/{_display_(Seq[Any](format.raw/*48.70*/("""fx-required""")))}),format.raw/*48.82*/("""" title=""""),_display_(/*48.92*/mapping/*48.99*/.getDescription),format.raw/*48.114*/("""">
		"""),_display_(/*49.4*/inputLabel),_display_(/*49.15*/if(isRequired)/*49.29*/{_display_(Seq[Any](format.raw/*49.30*/("""<abbr title=""""),_display_(/*49.44*/i18n("na.basemodule.validations.mandatory")),format.raw/*49.87*/(""""></abbr>""")))}),format.raw/*49.97*/("""
	"""),format.raw/*50.2*/("""</label>
	<div class="col-sm-10" data-na-portal-button-notification data-na-portal-input-notification-hr>
	"""),_display_(/*52.3*/uploadButtonComp/*52.19*/.render(inputId, placeHolder, inputAttributes)),format.raw/*52.65*/("""
	"""),format.raw/*53.2*/("""</div>
</div>
"""))
      }
    }
  }

  def render(mapping:pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping): play.twirl.api.HtmlFormat.Appendable = apply(mapping)

  def f:((pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping) => play.twirl.api.HtmlFormat.Appendable) = (mapping) => apply(mapping)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:12 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/dynamicForm/fields/fileField.scala.html
                  HASH: aaccd2c8ab86e60a065f9c06ecbdf27265b537e4
                  MATRIX: 687->1|739->47|778->80|871->167|952->242|1358->283|1512->362|1538->380|1602->432|1621->442|1695->504|1710->511|1756->545|1776->556|1825->593|1844->603|1894->641|1918->656|2867->359|2896->430|2925->502|2954->543|2983->591|3012->639|3040->1575|3069->1577|3135->1616|3163->1623|3223->1656|3246->1670|3285->1671|3328->1683|3365->1693|3381->1700|3418->1715|3450->1721|3481->1732|3504->1746|3543->1747|3584->1761|3648->1804|3689->1814|3718->1816|3852->1924|3877->1940|3944->1986|3973->1988
                  LINES: 24->1|25->2|26->3|27->4|28->5|33->7|37->9|37->9|37->10|37->10|37->11|37->11|37->12|37->12|37->13|37->13|37->14|37->14|69->7|71->9|72->10|73->11|74->12|75->13|76->45|78->47|79->48|79->48|79->48|79->48|79->48|79->48|79->48|79->48|79->48|80->49|80->49|80->49|80->49|80->49|80->49|80->49|81->50|83->52|83->52|83->52|84->53
                  -- GENERATED --
              */
          