// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/conf/go.routes
// @DATE:Tue Jul 01 11:21:12 WEST 2025

package go

import play.core.routing._
import play.core.routing.HandlerInvokerFactory._

import play.api.mvc._

import _root_.controllers.Assets.Asset

class Routes(
  override val errorHandler: play.api.http.HttpErrorHandler, 
  // @LINE:2
  Application_5: na.go.controllers.Application,
  // @LINE:14
  Assets_8: na.go.controllers.Assets,
  // @LINE:17
  Search_2: na.go.controllers.Search,
  // @LINE:21
  OperationDetails_4: na.go.controllers.OperationDetails,
  // @LINE:24
  OperationsTable_1: na.go.controllers.OperationsTable,
  // @LINE:28
  Modals_9: na.go.controllers.Modals,
  // @LINE:37
  Operation_6: na.go.controllers.Operation,
  // @LINE:46
  Catalog_3: na.go.controllers.Catalog,
  // @LINE:52
  AdminOperation_0: na.go.controllers.AdminOperation,
  // @LINE:64
  TableConfigs_7: na.go.controllers.TableConfigs,
  // @LINE:72
  naportalbase_Routes_0: naportalbase.Routes,
  val prefix: String
) extends GeneratedRouter {

   @javax.inject.Inject()
   def this(errorHandler: play.api.http.HttpErrorHandler,
    // @LINE:2
    Application_5: na.go.controllers.Application,
    // @LINE:14
    Assets_8: na.go.controllers.Assets,
    // @LINE:17
    Search_2: na.go.controllers.Search,
    // @LINE:21
    OperationDetails_4: na.go.controllers.OperationDetails,
    // @LINE:24
    OperationsTable_1: na.go.controllers.OperationsTable,
    // @LINE:28
    Modals_9: na.go.controllers.Modals,
    // @LINE:37
    Operation_6: na.go.controllers.Operation,
    // @LINE:46
    Catalog_3: na.go.controllers.Catalog,
    // @LINE:52
    AdminOperation_0: na.go.controllers.AdminOperation,
    // @LINE:64
    TableConfigs_7: na.go.controllers.TableConfigs,
    // @LINE:72
    naportalbase_Routes_0: naportalbase.Routes
  ) = this(errorHandler, Application_5, Assets_8, Search_2, OperationDetails_4, OperationsTable_1, Modals_9, Operation_6, Catalog_3, AdminOperation_0, TableConfigs_7, naportalbase_Routes_0, "/")

  def withPrefix(prefix: String): Routes = {
    go.RoutesPrefix.setPrefix(prefix)
    new Routes(errorHandler, Application_5, Assets_8, Search_2, OperationDetails_4, OperationsTable_1, Modals_9, Operation_6, Catalog_3, AdminOperation_0, TableConfigs_7, naportalbase_Routes_0, prefix)
  }

  private[this] val defaultPrefix: String = {
    if (this.prefix.endsWith("/")) "" else "/"
  }

  def documentation = List(
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/home""", """na.go.controllers.Application.index"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/resume""", """na.go.controllers.Application.resume"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/assets/javascripts/routes""", """na.go.controllers.Application.javascriptRoutes"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/imports/jsscripts""", """na.go.controllers.Application.getGOModuleAngularScripts"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/assets/""" + "$" + """file<.+>""", """na.go.controllers.Assets.versioned(path:String = "/public", file:Asset)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/selectfilternames/""" + "$" + """context<[^/]+>""", """na.go.controllers.Search.getFilterNamesForSelect(context:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/getFieldsForFiltername/""" + "$" + """filterName<[^/]+>""", """na.go.controllers.Search.getFieldsForFiltername(filterName:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/operationdetails/view/""" + "$" + """id<[^/]+>/""" + "$" + """context<[^/]+>""", """na.go.controllers.OperationDetails.getOperationDetails(id:String, context:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/operationtabledata""", """na.go.controllers.OperationsTable.getTableDataforOperations"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/admin/operationtabledata""", """na.go.controllers.OperationsTable.getAdminTableDataforOperations"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/operation/savefiltermodal""", """na.go.controllers.Modals.getOperationSaveFilterModal"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/template/savefiltermodal""", """na.go.controllers.Modals.getTemplateSaveFilterModal"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/savefilter""", """na.go.controllers.Modals.saveFilter"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/editfiltermodal/""" + "$" + """filterName<[^/]+>""", """na.go.controllers.Modals.getEditFilterModal(filterName:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/editfilter/""" + "$" + """filterName<[^/]+>""", """na.go.controllers.Modals.editFilter(filterName:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/deletefiltermodal/""" + "$" + """filterName<[^/]+>""", """na.go.controllers.Modals.getDeleteFilterModal(filterName:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/deletefilter/""" + "$" + """filterName<[^/]+>""", """na.go.controllers.Modals.deleteFilter(filterName:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/create""", """na.go.controllers.Operation.createOperationPage"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/create""", """na.go.controllers.Operation.createOperation"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/create/""" + "$" + """id<[^/]+>""", """na.go.controllers.Operation.cloneOperationPage(id:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/create/tab/characteristics""", """na.go.controllers.Operation.getCharacteristicsTab"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/create/tab/operation""", """na.go.controllers.Operation.getOperationTab"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/upload/firmwarefile""", """na.go.controllers.Operation.uploadFirmwareFile"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/catalog/rfsNames""", """na.go.controllers.Catalog.getRfsOfFamily"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/catalog/distinctRfsNames""", """na.go.controllers.Catalog.getDistinctRfsOfFamily"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/catalog/operationTemplates""", """na.go.controllers.Catalog.getOperationTemplatesOfRfs"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/catalog/distinctOperationTemplates""", """na.go.controllers.Catalog.getDistinctOperationTemplates"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/admin""", """na.go.controllers.AdminOperation.index"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/admin/changeTemplateState""", """na.go.controllers.AdminOperation.changeTemplateState(templateId:Long, active:Boolean, family:String, rfs:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/admin/deleteTemplate""", """na.go.controllers.AdminOperation.deleteTemplate(templateId:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/admin/add/""" + "$" + """templateId<[^/]+>""", """na.go.controllers.AdminOperation.addTemplatePage(templateId:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/admin/edit/""" + "$" + """templateId<[^/]+>""", """na.go.controllers.AdminOperation.editTemplatePage(templateId:Long)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/admin/update""", """na.go.controllers.AdminOperation.updateTemplate"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/admin/create""", """na.go.controllers.AdminOperation.createTemplatePage"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/admin/create""", """na.go.controllers.AdminOperation.createTemplate"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/admin/create/tab/characteristics""", """na.go.controllers.AdminOperation.getCharacteristicsTab"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/admin/create/tab/operation""", """na.go.controllers.AdminOperation.getOperationTab"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/table-configs/operations-seach-table""", """na.go.controllers.TableConfigs.operationsSearch"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/table-configs/templates-seach-table""", """na.go.controllers.TableConfigs.templatesSearch"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """go/table-configs/lr-response""", """na.go.controllers.TableConfigs.lrResponse"""),
    prefixed_naportalbase_Routes_0_40.router.documentation,
    Nil
  ).foldLeft(List.empty[(String,String,String)]) { (s,e) => e.asInstanceOf[Any] match {
    case r @ (_,_,_) => s :+ r.asInstanceOf[(String,String,String)]
    case l => s ++ l.asInstanceOf[List[(String,String,String)]]
  }}


  // @LINE:2
  private[this] lazy val na_go_controllers_Application_index0_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/home")))
  )
  private[this] lazy val na_go_controllers_Application_index0_invoker = createInvoker(
    Application_5.index,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.Application",
      "index",
      Nil,
      "GET",
      this.prefix + """go/home""",
      """ Home page""",
      Seq()
    )
  )

  // @LINE:5
  private[this] lazy val na_go_controllers_Application_resume1_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/resume")))
  )
  private[this] lazy val na_go_controllers_Application_resume1_invoker = createInvoker(
    Application_5.resume,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.Application",
      "resume",
      Nil,
      "GET",
      this.prefix + """go/resume""",
      """Resume""",
      Seq()
    )
  )

  // @LINE:8
  private[this] lazy val na_go_controllers_Application_javascriptRoutes2_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/assets/javascripts/routes")))
  )
  private[this] lazy val na_go_controllers_Application_javascriptRoutes2_invoker = createInvoker(
    Application_5.javascriptRoutes,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.Application",
      "javascriptRoutes",
      Nil,
      "GET",
      this.prefix + """go/assets/javascripts/routes""",
      """Javascript Routing""",
      Seq()
    )
  )

  // @LINE:11
  private[this] lazy val na_go_controllers_Application_getGOModuleAngularScripts3_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/imports/jsscripts")))
  )
  private[this] lazy val na_go_controllers_Application_getGOModuleAngularScripts3_invoker = createInvoker(
    Application_5.getGOModuleAngularScripts,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.Application",
      "getGOModuleAngularScripts",
      Nil,
      "GET",
      this.prefix + """go/imports/jsscripts""",
      """Angular Scripts""",
      Seq()
    )
  )

  // @LINE:14
  private[this] lazy val na_go_controllers_Assets_versioned4_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/assets/"), DynamicPart("file", """.+""",false)))
  )
  private[this] lazy val na_go_controllers_Assets_versioned4_invoker = createInvoker(
    Assets_8.versioned(fakeValue[String], fakeValue[Asset]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.Assets",
      "versioned",
      Seq(classOf[String], classOf[Asset]),
      "GET",
      this.prefix + """go/assets/""" + "$" + """file<.+>""",
      """ Map static resources from the /public folder to the /assets URL path""",
      Seq()
    )
  )

  // @LINE:17
  private[this] lazy val na_go_controllers_Search_getFilterNamesForSelect5_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/selectfilternames/"), DynamicPart("context", """[^/]+""",true)))
  )
  private[this] lazy val na_go_controllers_Search_getFilterNamesForSelect5_invoker = createInvoker(
    Search_2.getFilterNamesForSelect(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.Search",
      "getFilterNamesForSelect",
      Seq(classOf[String]),
      "GET",
      this.prefix + """go/selectfilternames/""" + "$" + """context<[^/]+>""",
      """Search""",
      Seq()
    )
  )

  // @LINE:18
  private[this] lazy val na_go_controllers_Search_getFieldsForFiltername6_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/getFieldsForFiltername/"), DynamicPart("filterName", """[^/]+""",true)))
  )
  private[this] lazy val na_go_controllers_Search_getFieldsForFiltername6_invoker = createInvoker(
    Search_2.getFieldsForFiltername(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.Search",
      "getFieldsForFiltername",
      Seq(classOf[String]),
      "GET",
      this.prefix + """go/getFieldsForFiltername/""" + "$" + """filterName<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:21
  private[this] lazy val na_go_controllers_OperationDetails_getOperationDetails7_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/operationdetails/view/"), DynamicPart("id", """[^/]+""",true), StaticPart("/"), DynamicPart("context", """[^/]+""",true)))
  )
  private[this] lazy val na_go_controllers_OperationDetails_getOperationDetails7_invoker = createInvoker(
    OperationDetails_4.getOperationDetails(fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.OperationDetails",
      "getOperationDetails",
      Seq(classOf[String], classOf[String]),
      "GET",
      this.prefix + """go/operationdetails/view/""" + "$" + """id<[^/]+>/""" + "$" + """context<[^/]+>""",
      """OperationDetails""",
      Seq()
    )
  )

  // @LINE:24
  private[this] lazy val na_go_controllers_OperationsTable_getTableDataforOperations8_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/operationtabledata")))
  )
  private[this] lazy val na_go_controllers_OperationsTable_getTableDataforOperations8_invoker = createInvoker(
    OperationsTable_1.getTableDataforOperations,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.OperationsTable",
      "getTableDataforOperations",
      Nil,
      "POST",
      this.prefix + """go/operationtabledata""",
      """Tables""",
      Seq()
    )
  )

  // @LINE:25
  private[this] lazy val na_go_controllers_OperationsTable_getAdminTableDataforOperations9_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/admin/operationtabledata")))
  )
  private[this] lazy val na_go_controllers_OperationsTable_getAdminTableDataforOperations9_invoker = createInvoker(
    OperationsTable_1.getAdminTableDataforOperations,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.OperationsTable",
      "getAdminTableDataforOperations",
      Nil,
      "POST",
      this.prefix + """go/admin/operationtabledata""",
      """""",
      Seq()
    )
  )

  // @LINE:28
  private[this] lazy val na_go_controllers_Modals_getOperationSaveFilterModal10_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/operation/savefiltermodal")))
  )
  private[this] lazy val na_go_controllers_Modals_getOperationSaveFilterModal10_invoker = createInvoker(
    Modals_9.getOperationSaveFilterModal,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.Modals",
      "getOperationSaveFilterModal",
      Nil,
      "GET",
      this.prefix + """go/operation/savefiltermodal""",
      """Modals""",
      Seq()
    )
  )

  // @LINE:29
  private[this] lazy val na_go_controllers_Modals_getTemplateSaveFilterModal11_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/template/savefiltermodal")))
  )
  private[this] lazy val na_go_controllers_Modals_getTemplateSaveFilterModal11_invoker = createInvoker(
    Modals_9.getTemplateSaveFilterModal,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.Modals",
      "getTemplateSaveFilterModal",
      Nil,
      "GET",
      this.prefix + """go/template/savefiltermodal""",
      """""",
      Seq()
    )
  )

  // @LINE:30
  private[this] lazy val na_go_controllers_Modals_saveFilter12_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/savefilter")))
  )
  private[this] lazy val na_go_controllers_Modals_saveFilter12_invoker = createInvoker(
    Modals_9.saveFilter,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.Modals",
      "saveFilter",
      Nil,
      "POST",
      this.prefix + """go/savefilter""",
      """""",
      Seq()
    )
  )

  // @LINE:31
  private[this] lazy val na_go_controllers_Modals_getEditFilterModal13_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/editfiltermodal/"), DynamicPart("filterName", """[^/]+""",true)))
  )
  private[this] lazy val na_go_controllers_Modals_getEditFilterModal13_invoker = createInvoker(
    Modals_9.getEditFilterModal(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.Modals",
      "getEditFilterModal",
      Seq(classOf[String]),
      "GET",
      this.prefix + """go/editfiltermodal/""" + "$" + """filterName<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:32
  private[this] lazy val na_go_controllers_Modals_editFilter14_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/editfilter/"), DynamicPart("filterName", """[^/]+""",true)))
  )
  private[this] lazy val na_go_controllers_Modals_editFilter14_invoker = createInvoker(
    Modals_9.editFilter(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.Modals",
      "editFilter",
      Seq(classOf[String]),
      "POST",
      this.prefix + """go/editfilter/""" + "$" + """filterName<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:33
  private[this] lazy val na_go_controllers_Modals_getDeleteFilterModal15_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/deletefiltermodal/"), DynamicPart("filterName", """[^/]+""",true)))
  )
  private[this] lazy val na_go_controllers_Modals_getDeleteFilterModal15_invoker = createInvoker(
    Modals_9.getDeleteFilterModal(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.Modals",
      "getDeleteFilterModal",
      Seq(classOf[String]),
      "GET",
      this.prefix + """go/deletefiltermodal/""" + "$" + """filterName<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:34
  private[this] lazy val na_go_controllers_Modals_deleteFilter16_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/deletefilter/"), DynamicPart("filterName", """[^/]+""",true)))
  )
  private[this] lazy val na_go_controllers_Modals_deleteFilter16_invoker = createInvoker(
    Modals_9.deleteFilter(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.Modals",
      "deleteFilter",
      Seq(classOf[String]),
      "POST",
      this.prefix + """go/deletefilter/""" + "$" + """filterName<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:37
  private[this] lazy val na_go_controllers_Operation_createOperationPage17_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/create")))
  )
  private[this] lazy val na_go_controllers_Operation_createOperationPage17_invoker = createInvoker(
    Operation_6.createOperationPage,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.Operation",
      "createOperationPage",
      Nil,
      "GET",
      this.prefix + """go/create""",
      """Create operation""",
      Seq()
    )
  )

  // @LINE:38
  private[this] lazy val na_go_controllers_Operation_createOperation18_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/create")))
  )
  private[this] lazy val na_go_controllers_Operation_createOperation18_invoker = createInvoker(
    Operation_6.createOperation,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.Operation",
      "createOperation",
      Nil,
      "POST",
      this.prefix + """go/create""",
      """""",
      Seq()
    )
  )

  // @LINE:39
  private[this] lazy val na_go_controllers_Operation_cloneOperationPage19_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/create/"), DynamicPart("id", """[^/]+""",true)))
  )
  private[this] lazy val na_go_controllers_Operation_cloneOperationPage19_invoker = createInvoker(
    Operation_6.cloneOperationPage(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.Operation",
      "cloneOperationPage",
      Seq(classOf[String]),
      "GET",
      this.prefix + """go/create/""" + "$" + """id<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:40
  private[this] lazy val na_go_controllers_Operation_getCharacteristicsTab20_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/create/tab/characteristics")))
  )
  private[this] lazy val na_go_controllers_Operation_getCharacteristicsTab20_invoker = createInvoker(
    Operation_6.getCharacteristicsTab,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.Operation",
      "getCharacteristicsTab",
      Nil,
      "GET",
      this.prefix + """go/create/tab/characteristics""",
      """""",
      Seq()
    )
  )

  // @LINE:41
  private[this] lazy val na_go_controllers_Operation_getOperationTab21_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/create/tab/operation")))
  )
  private[this] lazy val na_go_controllers_Operation_getOperationTab21_invoker = createInvoker(
    Operation_6.getOperationTab,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.Operation",
      "getOperationTab",
      Nil,
      "GET",
      this.prefix + """go/create/tab/operation""",
      """""",
      Seq()
    )
  )

  // @LINE:42
  private[this] lazy val na_go_controllers_Operation_uploadFirmwareFile22_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/upload/firmwarefile")))
  )
  private[this] lazy val na_go_controllers_Operation_uploadFirmwareFile22_invoker = createInvoker(
    Operation_6.uploadFirmwareFile,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.Operation",
      "uploadFirmwareFile",
      Nil,
      "POST",
      this.prefix + """go/upload/firmwarefile""",
      """""",
      Seq()
    )
  )

  // @LINE:46
  private[this] lazy val na_go_controllers_Catalog_getRfsOfFamily23_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/catalog/rfsNames")))
  )
  private[this] lazy val na_go_controllers_Catalog_getRfsOfFamily23_invoker = createInvoker(
    Catalog_3.getRfsOfFamily,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.Catalog",
      "getRfsOfFamily",
      Nil,
      "GET",
      this.prefix + """go/catalog/rfsNames""",
      """Catalog SERVICE""",
      Seq()
    )
  )

  // @LINE:47
  private[this] lazy val na_go_controllers_Catalog_getDistinctRfsOfFamily24_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/catalog/distinctRfsNames")))
  )
  private[this] lazy val na_go_controllers_Catalog_getDistinctRfsOfFamily24_invoker = createInvoker(
    Catalog_3.getDistinctRfsOfFamily,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.Catalog",
      "getDistinctRfsOfFamily",
      Nil,
      "GET",
      this.prefix + """go/catalog/distinctRfsNames""",
      """""",
      Seq()
    )
  )

  // @LINE:48
  private[this] lazy val na_go_controllers_Catalog_getOperationTemplatesOfRfs25_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/catalog/operationTemplates")))
  )
  private[this] lazy val na_go_controllers_Catalog_getOperationTemplatesOfRfs25_invoker = createInvoker(
    Catalog_3.getOperationTemplatesOfRfs,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.Catalog",
      "getOperationTemplatesOfRfs",
      Nil,
      "GET",
      this.prefix + """go/catalog/operationTemplates""",
      """""",
      Seq()
    )
  )

  // @LINE:49
  private[this] lazy val na_go_controllers_Catalog_getDistinctOperationTemplates26_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/catalog/distinctOperationTemplates")))
  )
  private[this] lazy val na_go_controllers_Catalog_getDistinctOperationTemplates26_invoker = createInvoker(
    Catalog_3.getDistinctOperationTemplates,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.Catalog",
      "getDistinctOperationTemplates",
      Nil,
      "GET",
      this.prefix + """go/catalog/distinctOperationTemplates""",
      """""",
      Seq()
    )
  )

  // @LINE:52
  private[this] lazy val na_go_controllers_AdminOperation_index27_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/admin")))
  )
  private[this] lazy val na_go_controllers_AdminOperation_index27_invoker = createInvoker(
    AdminOperation_0.index,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.AdminOperation",
      "index",
      Nil,
      "GET",
      this.prefix + """go/admin""",
      """Create Template""",
      Seq()
    )
  )

  // @LINE:53
  private[this] lazy val na_go_controllers_AdminOperation_changeTemplateState28_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/admin/changeTemplateState")))
  )
  private[this] lazy val na_go_controllers_AdminOperation_changeTemplateState28_invoker = createInvoker(
    AdminOperation_0.changeTemplateState(fakeValue[Long], fakeValue[Boolean], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.AdminOperation",
      "changeTemplateState",
      Seq(classOf[Long], classOf[Boolean], classOf[String], classOf[String]),
      "POST",
      this.prefix + """go/admin/changeTemplateState""",
      """""",
      Seq()
    )
  )

  // @LINE:54
  private[this] lazy val na_go_controllers_AdminOperation_deleteTemplate29_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/admin/deleteTemplate")))
  )
  private[this] lazy val na_go_controllers_AdminOperation_deleteTemplate29_invoker = createInvoker(
    AdminOperation_0.deleteTemplate(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.AdminOperation",
      "deleteTemplate",
      Seq(classOf[Long]),
      "POST",
      this.prefix + """go/admin/deleteTemplate""",
      """""",
      Seq()
    )
  )

  // @LINE:55
  private[this] lazy val na_go_controllers_AdminOperation_addTemplatePage30_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/admin/add/"), DynamicPart("templateId", """[^/]+""",true)))
  )
  private[this] lazy val na_go_controllers_AdminOperation_addTemplatePage30_invoker = createInvoker(
    AdminOperation_0.addTemplatePage(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.AdminOperation",
      "addTemplatePage",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """go/admin/add/""" + "$" + """templateId<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:56
  private[this] lazy val na_go_controllers_AdminOperation_editTemplatePage31_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/admin/edit/"), DynamicPart("templateId", """[^/]+""",true)))
  )
  private[this] lazy val na_go_controllers_AdminOperation_editTemplatePage31_invoker = createInvoker(
    AdminOperation_0.editTemplatePage(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.AdminOperation",
      "editTemplatePage",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """go/admin/edit/""" + "$" + """templateId<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:57
  private[this] lazy val na_go_controllers_AdminOperation_updateTemplate32_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/admin/update")))
  )
  private[this] lazy val na_go_controllers_AdminOperation_updateTemplate32_invoker = createInvoker(
    AdminOperation_0.updateTemplate,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.AdminOperation",
      "updateTemplate",
      Nil,
      "POST",
      this.prefix + """go/admin/update""",
      """""",
      Seq()
    )
  )

  // @LINE:58
  private[this] lazy val na_go_controllers_AdminOperation_createTemplatePage33_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/admin/create")))
  )
  private[this] lazy val na_go_controllers_AdminOperation_createTemplatePage33_invoker = createInvoker(
    AdminOperation_0.createTemplatePage,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.AdminOperation",
      "createTemplatePage",
      Nil,
      "GET",
      this.prefix + """go/admin/create""",
      """""",
      Seq()
    )
  )

  // @LINE:59
  private[this] lazy val na_go_controllers_AdminOperation_createTemplate34_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/admin/create")))
  )
  private[this] lazy val na_go_controllers_AdminOperation_createTemplate34_invoker = createInvoker(
    AdminOperation_0.createTemplate,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.AdminOperation",
      "createTemplate",
      Nil,
      "POST",
      this.prefix + """go/admin/create""",
      """""",
      Seq()
    )
  )

  // @LINE:60
  private[this] lazy val na_go_controllers_AdminOperation_getCharacteristicsTab35_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/admin/create/tab/characteristics")))
  )
  private[this] lazy val na_go_controllers_AdminOperation_getCharacteristicsTab35_invoker = createInvoker(
    AdminOperation_0.getCharacteristicsTab,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.AdminOperation",
      "getCharacteristicsTab",
      Nil,
      "GET",
      this.prefix + """go/admin/create/tab/characteristics""",
      """""",
      Seq()
    )
  )

  // @LINE:61
  private[this] lazy val na_go_controllers_AdminOperation_getOperationTab36_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/admin/create/tab/operation")))
  )
  private[this] lazy val na_go_controllers_AdminOperation_getOperationTab36_invoker = createInvoker(
    AdminOperation_0.getOperationTab,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.AdminOperation",
      "getOperationTab",
      Nil,
      "GET",
      this.prefix + """go/admin/create/tab/operation""",
      """""",
      Seq()
    )
  )

  // @LINE:64
  private[this] lazy val na_go_controllers_TableConfigs_operationsSearch37_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/table-configs/operations-seach-table")))
  )
  private[this] lazy val na_go_controllers_TableConfigs_operationsSearch37_invoker = createInvoker(
    TableConfigs_7.operationsSearch,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.TableConfigs",
      "operationsSearch",
      Nil,
      "GET",
      this.prefix + """go/table-configs/operations-seach-table""",
      """ table configs""",
      Seq()
    )
  )

  // @LINE:65
  private[this] lazy val na_go_controllers_TableConfigs_templatesSearch38_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/table-configs/templates-seach-table")))
  )
  private[this] lazy val na_go_controllers_TableConfigs_templatesSearch38_invoker = createInvoker(
    TableConfigs_7.templatesSearch,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.TableConfigs",
      "templatesSearch",
      Nil,
      "GET",
      this.prefix + """go/table-configs/templates-seach-table""",
      """""",
      Seq()
    )
  )

  // @LINE:66
  private[this] lazy val na_go_controllers_TableConfigs_lrResponse39_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("go/table-configs/lr-response")))
  )
  private[this] lazy val na_go_controllers_TableConfigs_lrResponse39_invoker = createInvoker(
    TableConfigs_7.lrResponse,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "go",
      "na.go.controllers.TableConfigs",
      "lrResponse",
      Nil,
      "GET",
      this.prefix + """go/table-configs/lr-response""",
      """""",
      Seq()
    )
  )

  // @LINE:72
  private[this] val prefixed_naportalbase_Routes_0_40 = Include(naportalbase_Routes_0.withPrefix(this.prefix + (if (this.prefix.endsWith("/")) "" else "/") + "go"))


  def routes: PartialFunction[RequestHeader, Handler] = {
  
    // @LINE:2
    case na_go_controllers_Application_index0_route(params@_) =>
      call { 
        na_go_controllers_Application_index0_invoker.call(Application_5.index)
      }
  
    // @LINE:5
    case na_go_controllers_Application_resume1_route(params@_) =>
      call { 
        na_go_controllers_Application_resume1_invoker.call(Application_5.resume)
      }
  
    // @LINE:8
    case na_go_controllers_Application_javascriptRoutes2_route(params@_) =>
      call { 
        na_go_controllers_Application_javascriptRoutes2_invoker.call(Application_5.javascriptRoutes)
      }
  
    // @LINE:11
    case na_go_controllers_Application_getGOModuleAngularScripts3_route(params@_) =>
      call { 
        na_go_controllers_Application_getGOModuleAngularScripts3_invoker.call(Application_5.getGOModuleAngularScripts)
      }
  
    // @LINE:14
    case na_go_controllers_Assets_versioned4_route(params@_) =>
      call(Param[String]("path", Right("/public")), params.fromPath[Asset]("file", None)) { (path, file) =>
        na_go_controllers_Assets_versioned4_invoker.call(Assets_8.versioned(path, file))
      }
  
    // @LINE:17
    case na_go_controllers_Search_getFilterNamesForSelect5_route(params@_) =>
      call(params.fromPath[String]("context", None)) { (context) =>
        na_go_controllers_Search_getFilterNamesForSelect5_invoker.call(Search_2.getFilterNamesForSelect(context))
      }
  
    // @LINE:18
    case na_go_controllers_Search_getFieldsForFiltername6_route(params@_) =>
      call(params.fromPath[String]("filterName", None)) { (filterName) =>
        na_go_controllers_Search_getFieldsForFiltername6_invoker.call(Search_2.getFieldsForFiltername(filterName))
      }
  
    // @LINE:21
    case na_go_controllers_OperationDetails_getOperationDetails7_route(params@_) =>
      call(params.fromPath[String]("id", None), params.fromPath[String]("context", None)) { (id, context) =>
        na_go_controllers_OperationDetails_getOperationDetails7_invoker.call(OperationDetails_4.getOperationDetails(id, context))
      }
  
    // @LINE:24
    case na_go_controllers_OperationsTable_getTableDataforOperations8_route(params@_) =>
      call { 
        na_go_controllers_OperationsTable_getTableDataforOperations8_invoker.call(OperationsTable_1.getTableDataforOperations)
      }
  
    // @LINE:25
    case na_go_controllers_OperationsTable_getAdminTableDataforOperations9_route(params@_) =>
      call { 
        na_go_controllers_OperationsTable_getAdminTableDataforOperations9_invoker.call(OperationsTable_1.getAdminTableDataforOperations)
      }
  
    // @LINE:28
    case na_go_controllers_Modals_getOperationSaveFilterModal10_route(params@_) =>
      call { 
        na_go_controllers_Modals_getOperationSaveFilterModal10_invoker.call(Modals_9.getOperationSaveFilterModal)
      }
  
    // @LINE:29
    case na_go_controllers_Modals_getTemplateSaveFilterModal11_route(params@_) =>
      call { 
        na_go_controllers_Modals_getTemplateSaveFilterModal11_invoker.call(Modals_9.getTemplateSaveFilterModal)
      }
  
    // @LINE:30
    case na_go_controllers_Modals_saveFilter12_route(params@_) =>
      call { 
        na_go_controllers_Modals_saveFilter12_invoker.call(Modals_9.saveFilter)
      }
  
    // @LINE:31
    case na_go_controllers_Modals_getEditFilterModal13_route(params@_) =>
      call(params.fromPath[String]("filterName", None)) { (filterName) =>
        na_go_controllers_Modals_getEditFilterModal13_invoker.call(Modals_9.getEditFilterModal(filterName))
      }
  
    // @LINE:32
    case na_go_controllers_Modals_editFilter14_route(params@_) =>
      call(params.fromPath[String]("filterName", None)) { (filterName) =>
        na_go_controllers_Modals_editFilter14_invoker.call(Modals_9.editFilter(filterName))
      }
  
    // @LINE:33
    case na_go_controllers_Modals_getDeleteFilterModal15_route(params@_) =>
      call(params.fromPath[String]("filterName", None)) { (filterName) =>
        na_go_controllers_Modals_getDeleteFilterModal15_invoker.call(Modals_9.getDeleteFilterModal(filterName))
      }
  
    // @LINE:34
    case na_go_controllers_Modals_deleteFilter16_route(params@_) =>
      call(params.fromPath[String]("filterName", None)) { (filterName) =>
        na_go_controllers_Modals_deleteFilter16_invoker.call(Modals_9.deleteFilter(filterName))
      }
  
    // @LINE:37
    case na_go_controllers_Operation_createOperationPage17_route(params@_) =>
      call { 
        na_go_controllers_Operation_createOperationPage17_invoker.call(Operation_6.createOperationPage)
      }
  
    // @LINE:38
    case na_go_controllers_Operation_createOperation18_route(params@_) =>
      call { 
        na_go_controllers_Operation_createOperation18_invoker.call(Operation_6.createOperation)
      }
  
    // @LINE:39
    case na_go_controllers_Operation_cloneOperationPage19_route(params@_) =>
      call(params.fromPath[String]("id", None)) { (id) =>
        na_go_controllers_Operation_cloneOperationPage19_invoker.call(Operation_6.cloneOperationPage(id))
      }
  
    // @LINE:40
    case na_go_controllers_Operation_getCharacteristicsTab20_route(params@_) =>
      call { 
        na_go_controllers_Operation_getCharacteristicsTab20_invoker.call(Operation_6.getCharacteristicsTab)
      }
  
    // @LINE:41
    case na_go_controllers_Operation_getOperationTab21_route(params@_) =>
      call { 
        na_go_controllers_Operation_getOperationTab21_invoker.call(Operation_6.getOperationTab)
      }
  
    // @LINE:42
    case na_go_controllers_Operation_uploadFirmwareFile22_route(params@_) =>
      call { 
        na_go_controllers_Operation_uploadFirmwareFile22_invoker.call(Operation_6.uploadFirmwareFile)
      }
  
    // @LINE:46
    case na_go_controllers_Catalog_getRfsOfFamily23_route(params@_) =>
      call { 
        na_go_controllers_Catalog_getRfsOfFamily23_invoker.call(Catalog_3.getRfsOfFamily)
      }
  
    // @LINE:47
    case na_go_controllers_Catalog_getDistinctRfsOfFamily24_route(params@_) =>
      call { 
        na_go_controllers_Catalog_getDistinctRfsOfFamily24_invoker.call(Catalog_3.getDistinctRfsOfFamily)
      }
  
    // @LINE:48
    case na_go_controllers_Catalog_getOperationTemplatesOfRfs25_route(params@_) =>
      call { 
        na_go_controllers_Catalog_getOperationTemplatesOfRfs25_invoker.call(Catalog_3.getOperationTemplatesOfRfs)
      }
  
    // @LINE:49
    case na_go_controllers_Catalog_getDistinctOperationTemplates26_route(params@_) =>
      call { 
        na_go_controllers_Catalog_getDistinctOperationTemplates26_invoker.call(Catalog_3.getDistinctOperationTemplates)
      }
  
    // @LINE:52
    case na_go_controllers_AdminOperation_index27_route(params@_) =>
      call { 
        na_go_controllers_AdminOperation_index27_invoker.call(AdminOperation_0.index)
      }
  
    // @LINE:53
    case na_go_controllers_AdminOperation_changeTemplateState28_route(params@_) =>
      call(params.fromQuery[Long]("templateId", None), params.fromQuery[Boolean]("active", None), params.fromQuery[String]("family", None), params.fromQuery[String]("rfs", None)) { (templateId, active, family, rfs) =>
        na_go_controllers_AdminOperation_changeTemplateState28_invoker.call(AdminOperation_0.changeTemplateState(templateId, active, family, rfs))
      }
  
    // @LINE:54
    case na_go_controllers_AdminOperation_deleteTemplate29_route(params@_) =>
      call(params.fromQuery[Long]("templateId", None)) { (templateId) =>
        na_go_controllers_AdminOperation_deleteTemplate29_invoker.call(AdminOperation_0.deleteTemplate(templateId))
      }
  
    // @LINE:55
    case na_go_controllers_AdminOperation_addTemplatePage30_route(params@_) =>
      call(params.fromPath[Long]("templateId", None)) { (templateId) =>
        na_go_controllers_AdminOperation_addTemplatePage30_invoker.call(AdminOperation_0.addTemplatePage(templateId))
      }
  
    // @LINE:56
    case na_go_controllers_AdminOperation_editTemplatePage31_route(params@_) =>
      call(params.fromPath[Long]("templateId", None)) { (templateId) =>
        na_go_controllers_AdminOperation_editTemplatePage31_invoker.call(AdminOperation_0.editTemplatePage(templateId))
      }
  
    // @LINE:57
    case na_go_controllers_AdminOperation_updateTemplate32_route(params@_) =>
      call { 
        na_go_controllers_AdminOperation_updateTemplate32_invoker.call(AdminOperation_0.updateTemplate)
      }
  
    // @LINE:58
    case na_go_controllers_AdminOperation_createTemplatePage33_route(params@_) =>
      call { 
        na_go_controllers_AdminOperation_createTemplatePage33_invoker.call(AdminOperation_0.createTemplatePage)
      }
  
    // @LINE:59
    case na_go_controllers_AdminOperation_createTemplate34_route(params@_) =>
      call { 
        na_go_controllers_AdminOperation_createTemplate34_invoker.call(AdminOperation_0.createTemplate)
      }
  
    // @LINE:60
    case na_go_controllers_AdminOperation_getCharacteristicsTab35_route(params@_) =>
      call { 
        na_go_controllers_AdminOperation_getCharacteristicsTab35_invoker.call(AdminOperation_0.getCharacteristicsTab)
      }
  
    // @LINE:61
    case na_go_controllers_AdminOperation_getOperationTab36_route(params@_) =>
      call { 
        na_go_controllers_AdminOperation_getOperationTab36_invoker.call(AdminOperation_0.getOperationTab)
      }
  
    // @LINE:64
    case na_go_controllers_TableConfigs_operationsSearch37_route(params@_) =>
      call { 
        na_go_controllers_TableConfigs_operationsSearch37_invoker.call(TableConfigs_7.operationsSearch)
      }
  
    // @LINE:65
    case na_go_controllers_TableConfigs_templatesSearch38_route(params@_) =>
      call { 
        na_go_controllers_TableConfigs_templatesSearch38_invoker.call(TableConfigs_7.templatesSearch)
      }
  
    // @LINE:66
    case na_go_controllers_TableConfigs_lrResponse39_route(params@_) =>
      call { 
        na_go_controllers_TableConfigs_lrResponse39_invoker.call(TableConfigs_7.lrResponse)
      }
  
    // @LINE:72
    case prefixed_naportalbase_Routes_0_40(handler) => handler
  }
}
