
package na.go.views.html.components.admintemplatecreate

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import play.libs.Json
/*3.2*/import na.go.models.ui.UITemplate
/*4.2*/import scala.collection.mutable
/*5.2*/import na.naportalbase.views.html.components.inputsComp.inputTextComp
/*6.2*/import na.naportalbase.views.html.skeletons.datatables.naDatatablesSkeleton
/*7.2*/import na.naportalbase.views.html.components.wizard.wizardContentComp

object operationTab extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[UITemplate,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*9.2*/(template: UITemplate):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*25.2*/headContent/*25.13*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*25.17*/("""
	"""),format.raw/*26.2*/("""<p class="fx-required">
		<abbr title=""""),_display_(/*27.17*/i18n("na.basemodule.validations.mandatory")),format.raw/*27.60*/(""""></abbr>
		"""),_display_(/*28.4*/i18n("na.portal.go.operation.create.legend.requiredfields.label")),format.raw/*28.69*/("""
	"""),format.raw/*29.2*/("""</p>
	<p><strong>"""),_display_(/*30.14*/i18n("na.portal.go.adminoperation.create.tab.operation.description.title")),format.raw/*30.88*/("""</strong></p>
	<p>"""),_display_(/*31.6*/i18n("na.portal.go.adminoperation.create.tab.operation.description.text")),format.raw/*31.79*/("""</p><br>
""")))};def /*40.2*/blockContent/*40.14*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*40.18*/("""
	"""),format.raw/*41.2*/("""<form class="form-horizontal" data-na-portal-go-dynamic-form data-model="operationTabFormModel">
		<div class="form-group">
			<label for=""""),_display_(/*43.17*/inputId),format.raw/*43.24*/("""" class="col-sm-2 control-label fx-required">
				"""),_display_(/*44.6*/inputLabel),format.raw/*44.16*/("""<abbr title=""""),_display_(/*44.30*/i18n("na.basemodule.validations.mandatory")),format.raw/*44.73*/(""""></abbr>
			</label>
			<div class="col-sm-10" data-na-portal-field-notification data-na-portal-input-notification-hr>
			"""),_display_(/*47.5*/inputTextComp/*47.18*/.render(inputId, inputPlaceHolder, inputAttributes)),format.raw/*47.69*/("""
			"""),format.raw/*48.4*/("""</div>
		</div>
		<br>
		<p><strong>"""),_display_(/*51.15*/i18n("na.portal.go.adminoperation.create.tab.operation.fieldDefinition.title")),format.raw/*51.93*/("""</strong></p><br>
		<p>"""),_display_(/*52.7*/i18n("na.portal.go.adminoperation.create.tab.operation.fieldDefinition.text")),format.raw/*52.84*/("""</p>

		<div data-na-portal-go-template-operation=""""),_display_(/*54.47*/Json/*54.51*/.toJson(template)),format.raw/*54.68*/("""">
			<div id="fx-entity-content" class="fx-entity-info" data-ng-controller="NaPortalGoAdminTemplateParametersDatatableController">
			"""),_display_(/*56.5*/naDatatablesSkeleton/*56.25*/.render(adminOperationsTableAttributes)),format.raw/*56.64*/("""
			"""),format.raw/*57.4*/("""</div>
		</div>
    </form>
""")))};def /*12.2*/inputLabel/*12.12*/ = {{i18n("na.portal.go.field.operationName.label")}};def /*13.2*/inputPlaceHolder/*13.18*/ = {{i18n("na.portal.go.field.operationName.placeholder")}};def /*14.2*/inputId/*14.9*/ = {{"templateNameField"}};def /*15.2*/inputAttributes/*15.17*/ = {{
	var map = mutable.HashMap[String, String]()
	map.put("data-entity", inputId)
	map.put("data-ng-model", "staticFieldValues.name")
	map.put("name", inputId)
	map.put("id", inputId)
	map.put("data-na-portal-required",null)
	map
}};def /*34.2*/adminOperationsTableAttributes/*34.32*/ = {{
	var map = mutable.HashMap[String,String]()
	map.put("id","datatableMappingsTemplate")
	map
}};
Seq[Any](format.raw/*9.24*/("""

"""),format.raw/*11.1*/("""<!-- TEMPLATE NAME FIELD CONFIG -->
"""),format.raw/*12.64*/("""
"""),format.raw/*13.76*/("""
"""),format.raw/*14.34*/("""
"""),format.raw/*23.2*/("""

"""),format.raw/*32.2*/("""

"""),format.raw/*38.2*/("""

"""),format.raw/*60.2*/("""

"""),_display_(/*62.2*/wizardContentComp/*62.19*/.render(headContent,blockContent)))
      }
    }
  }

  def render(template:UITemplate): play.twirl.api.HtmlFormat.Appendable = apply(template)

  def f:((UITemplate) => play.twirl.api.HtmlFormat.Appendable) = (template) => apply(template)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:12 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/admintemplatecreate/operationTab.scala.html
                  HASH: 21cbc03db7f5813c12fae75704228322bca9ebe1
                  MATRIX: 688->1|734->41|763->64|804->99|843->132|920->203|1003->280|1388->352|1489->838|1509->849|1590->853|1619->855|1686->895|1750->938|1789->951|1875->1016|1904->1018|1949->1036|2044->1110|2089->1129|2183->1202|2216->1347|2237->1359|2318->1363|2347->1365|2514->1505|2542->1512|2619->1563|2650->1573|2691->1587|2755->1630|2905->1754|2927->1767|2999->1818|3030->1822|3094->1859|3193->1937|3243->1961|3341->2038|3420->2090|3433->2094|3471->2111|3633->2247|3662->2267|3722->2306|3753->2310|3805->413|3824->423|3890->477|3915->493|3987->553|4002->560|4041->587|4065->602|4312->1215|4351->1245|4480->374|4509->376|4573->475|4602->551|4631->585|4659->835|4688->1212|4717->1344|4746->2339|4775->2342|4801->2359
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|35->9|39->25|39->25|41->25|42->26|43->27|43->27|44->28|44->28|45->29|46->30|46->30|47->31|47->31|48->40|48->40|50->40|51->41|53->43|53->43|54->44|54->44|54->44|54->44|57->47|57->47|57->47|58->48|61->51|61->51|62->52|62->52|64->54|64->54|64->54|66->56|66->56|66->56|67->57|70->12|70->12|70->13|70->13|70->14|70->14|70->15|70->15|78->34|78->34|83->9|85->11|86->12|87->13|88->14|89->23|91->32|93->38|95->60|97->62|97->62
                  -- GENERATED --
              */
          