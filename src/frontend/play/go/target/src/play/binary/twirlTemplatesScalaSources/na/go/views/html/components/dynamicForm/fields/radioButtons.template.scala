
package na.go.views.html.components.dynamicForm.fields

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import scala.collection.mutable
/*2.2*/import scala.collection.immutable
/*3.2*/import na.naportalbase.settings.BaseMappings
/*4.2*/import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.components.RadioButton
/*5.2*/import na.naportalbase.utils.TemplateUtils
/*6.2*/import na.naportalbase.views.html.components.inputsComp.radioButtonComp
/*7.2*/import na.naportalbase.views.tags.i18n

object radioButtons extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*9.2*/(mapping: pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*11.2*/radioButtonComponent/*11.22*/ = {{mapping.getComponent.asInstanceOf[RadioButton]}};def /*12.2*/inputLabel/*12.12*/ = {{mapping.getLabel}};def /*13.2*/inputId/*13.9*/ = {{mapping.getTemplateMapping}};def /*14.2*/placeHolder/*14.13*/ = {{mapping.getLabel}};def /*15.2*/isRequired/*15.12*/ = {{radioButtonComponent.getRequired}};def /*16.2*/radioGroupAttributes/*16.22*/ = {{
    var map = mutable.HashMap[String, String](
        "data-na-portal-field-notification" -> null,
        "data-na-portal-button-notification" -> null,
        "data-ng-model" -> ("model[\""+inputId+"\"]"),
        "data-model" -> inputId,
        "id" -> inputId,
        "data-na-portal-go-dynamic-form-radio-button" -> null,
        "data-component" -> BaseMappings.dualMapper.valueToTree(radioButtonComponent),
        "name" -> inputId
    )
    if(isRequired){
        map.put("data-na-portal-required", null)
    }
    immutable.HashMap(map.toSeq:_*)
}};
Seq[Any](format.raw/*9.78*/("""

"""),format.raw/*11.74*/("""
"""),format.raw/*12.34*/("""
"""),format.raw/*13.41*/("""
"""),format.raw/*14.35*/("""
"""),format.raw/*15.50*/("""
"""),format.raw/*31.2*/("""

"""),format.raw/*33.1*/("""<div class="form-group">
    <label for=""""),_display_(/*34.18*/inputId),format.raw/*34.25*/("""" class="col-sm-2 control-label """),_display_(/*34.58*/if(isRequired)/*34.72*/{_display_(Seq[Any](format.raw/*34.73*/("""fx-required""")))}),format.raw/*34.85*/("""" title=""""),_display_(/*34.95*/mapping/*34.102*/.getDescription),format.raw/*34.117*/("""">
        """),_display_(/*35.10*/inputLabel),_display_(/*35.21*/if(isRequired)/*35.35*/{_display_(Seq[Any](format.raw/*35.36*/("""<abbr title=""""),_display_(/*35.50*/i18n("na.basemodule.validations.mandatory")),format.raw/*35.93*/(""""></abbr>""")))}),format.raw/*35.103*/("""
    """),format.raw/*36.5*/("""</label>
    <div data-na-portal-field-notification data-na-portal-button-notification """),_display_(/*37.80*/TemplateUtils/*37.93*/.dynamicElementsAttributes(radioGroupAttributes)),format.raw/*37.141*/(""">
        <ul class="list-unstyled">
        """),_display_(/*39.10*/for(option <- radioButtonComponent.getOptions) yield /*39.56*/ {_display_(Seq[Any](format.raw/*39.58*/("""
            """),_display_(/*40.14*/if(option.getVisible)/*40.35*/{_display_(Seq[Any](format.raw/*40.36*/("""
                """),format.raw/*41.17*/("""<li class="checkbox" style="padding-left: 0px">
                """),_display_(/*42.18*/{
                    val radioButtonIndex = String.valueOf(option.getIndex)
                    val radioButtonId = inputId + "_" + option.getValue
                    var radioAttributes = mutable.HashMap[String, String](
                        "data-ng-model" -> ("model[\""+inputId+"\"]"),
                        "data-model" -> inputId,
                        "name" -> inputId,
                        "value" -> option.getValue,
                        "id" -> inputId
                    )

                    if(radioButtonIndex.equals(radioButtonComponent.getDefaultValue)) {
                        radioAttributes.put("checked", null)
                    }

                    radioButtonComp.render(option.getLabel, radioButtonId, radioAttributes)
                }),format.raw/*58.18*/("""
                """),format.raw/*59.17*/("""</li>
            """)))}),format.raw/*60.14*/("""
        """)))}),format.raw/*61.10*/("""
        """),format.raw/*62.9*/("""</ul>
    </div>
</div>"""))
      }
    }
  }

  def render(mapping:pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping): play.twirl.api.HtmlFormat.Appendable = apply(mapping)

  def f:((pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping) => play.twirl.api.HtmlFormat.Appendable) = (mapping) => apply(mapping)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:21 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/dynamicForm/fields/radioButtons.scala.html
                  HASH: 2320d174a83c69c2c5083907868fedd1342ea26f
                  MATRIX: 687->1|726->34|767->69|819->115|914->204|964->248|1043->321|1452->362|1607->441|1636->461|1702->515|1721->525|1757->549|1772->556|1818->590|1838->601|1874->625|1893->635|1945->675|1974->695|2571->438|2601->513|2630->547|2659->588|2688->623|2717->673|2745->1262|2774->1264|2843->1306|2871->1313|2931->1346|2954->1360|2993->1361|3036->1373|3073->1383|3090->1390|3127->1405|3166->1417|3197->1428|3220->1442|3259->1443|3300->1457|3364->1500|3406->1510|3438->1515|3553->1603|3575->1616|3645->1664|3718->1710|3780->1756|3820->1758|3861->1772|3891->1793|3930->1794|3975->1811|4067->1876|4871->2659|4916->2676|4966->2695|5007->2705|5043->2714
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|35->9|39->11|39->11|39->12|39->12|39->13|39->13|39->14|39->14|39->15|39->15|39->16|39->16|55->9|57->11|58->12|59->13|60->14|61->15|62->31|64->33|65->34|65->34|65->34|65->34|65->34|65->34|65->34|65->34|65->34|66->35|66->35|66->35|66->35|66->35|66->35|66->35|67->36|68->37|68->37|68->37|70->39|70->39|70->39|71->40|71->40|71->40|72->41|73->42|89->58|90->59|91->60|92->61|93->62
                  -- GENERATED --
              */
          