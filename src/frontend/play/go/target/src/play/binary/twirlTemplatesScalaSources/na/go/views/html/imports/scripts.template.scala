
package na.go.views.html.imports

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object scripts extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.1*/("""<script src=""""),_display_(/*1.15*/na/*1.17*/.go.controllers.routes.Application.javascriptRoutes()),format.raw/*1.70*/(""""></script><script src='"""),_display_(/*1.95*/na/*1.97*/.go.controllers.routes.Assets.versioned("basemodule/runtime.ef4cdaceb8028d495c4b.min.js")),format.raw/*1.186*/("""'></script><script src='"""),_display_(/*1.211*/na/*1.213*/.go.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-fuxi.44e71963e917fd2be2ca.min.js")),format.raw/*1.321*/("""'></script><script src='"""),_display_(/*1.346*/na/*1.348*/.go.controllers.routes.Assets.versioned("na-portal-assets/base-styles.9e693254edb056a100ad.min.js")),format.raw/*1.447*/("""'></script><script src='"""),_display_(/*1.472*/na/*1.474*/.go.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-bootstrap.6f86c32db7bf4ccaf010.min.js")),format.raw/*1.587*/("""'></script><script src='"""),_display_(/*1.612*/na/*1.614*/.go.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-selects.c845a1da27d2bbccf773.min.js")),format.raw/*1.725*/("""'></script><script src='"""),_display_(/*1.750*/na/*1.752*/.go.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-std-utils.ab8ed0c92f51f5eb905e.min.js")),format.raw/*1.865*/("""'></script><script src='"""),_display_(/*1.890*/na/*1.892*/.go.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-tables.bc173ec447d65947d31a.min.js")),format.raw/*1.1002*/("""'></script><script src='"""),_display_(/*1.1027*/na/*1.1029*/.go.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-jquery.c69a6ab040df2ef632e6.min.js")),format.raw/*1.1139*/("""'></script><script src='"""),_display_(/*1.1164*/na/*1.1166*/.go.controllers.routes.Assets.versioned("na-portal-assets-vendors/common.a53001b54dbc3b115cc4.min.js")),format.raw/*1.1268*/("""'></script><script src='"""),_display_(/*1.1293*/na/*1.1295*/.go.controllers.routes.Assets.versioned("utils/utils.88e56ee512f1a3af972d.min.js")),format.raw/*1.1377*/("""'></script><script src='"""),_display_(/*1.1402*/na/*1.1404*/.go.controllers.routes.Assets.versioned("basemodule/basemodule.823a1b10758c828389cd.min.js")),format.raw/*1.1496*/("""'></script><script src='"""),_display_(/*1.1521*/na/*1.1523*/.go.controllers.routes.Assets.versioned("go/go.08d33486d1bb885819e1.min.js")),format.raw/*1.1599*/("""'></script>"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:12 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/imports/scripts.scala.html
                  HASH: bacec88a031994acee29b076f723614706e7b7bf
                  MATRIX: 1046->0|1086->14|1096->16|1169->69|1220->94|1230->96|1340->185|1392->210|1403->212|1532->320|1584->345|1595->347|1715->446|1767->471|1778->473|1912->586|1964->611|1975->613|2107->724|2159->749|2170->751|2304->864|2356->889|2367->891|2499->1001|2552->1026|2564->1028|2696->1138|2749->1163|2761->1165|2885->1267|2938->1292|2950->1294|3054->1376|3107->1401|3119->1403|3233->1495|3286->1520|3298->1522|3396->1598
                  LINES: 33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1
                  -- GENERATED --
              */
          