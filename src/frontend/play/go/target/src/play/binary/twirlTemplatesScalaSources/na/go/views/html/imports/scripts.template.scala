
package na.go.views.html.imports

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object scripts extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.1*/("""<script src=""""),_display_(/*1.15*/na/*1.17*/.go.controllers.routes.Application.javascriptRoutes()),format.raw/*1.70*/(""""></script>
<script src='"""),_display_(/*2.15*/na/*2.17*/.go.controllers.routes.Assets.versioned("basemodule/runtime.js")),format.raw/*2.81*/("""'></script>
<script src='"""),_display_(/*3.15*/na/*3.17*/.go.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-fuxi.js")),format.raw/*3.100*/("""'></script>
<script src='"""),_display_(/*4.15*/na/*4.17*/.go.controllers.routes.Assets.versioned("na-portal-assets/base-styles.js")),format.raw/*4.91*/("""'></script>
<script src='"""),_display_(/*5.15*/na/*5.17*/.go.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-bootstrap.js")),format.raw/*5.105*/("""'></script>
<script src='"""),_display_(/*6.15*/na/*6.17*/.go.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-selects.js")),format.raw/*6.103*/("""'></script>
<script src='"""),_display_(/*7.15*/na/*7.17*/.go.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-std-utils.js")),format.raw/*7.105*/("""'></script>
<script src='"""),_display_(/*8.15*/na/*8.17*/.go.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-tables.js")),format.raw/*8.102*/("""'></script>
<script src='"""),_display_(/*9.15*/na/*9.17*/.go.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-jquery.js")),format.raw/*9.102*/("""'></script>
<script src='"""),_display_(/*10.15*/na/*10.17*/.go.controllers.routes.Assets.versioned("na-portal-assets-vendors/common.js")),format.raw/*10.94*/("""'></script>
<script src='"""),_display_(/*11.15*/na/*11.17*/.go.controllers.routes.Assets.versioned("utils/utils.js")),format.raw/*11.74*/("""'></script>
<script src='"""),_display_(/*12.15*/na/*12.17*/.go.controllers.routes.Assets.versioned("basemodule/basemodule.js")),format.raw/*12.84*/("""'></script>
<script src='"""),_display_(/*13.15*/na/*13.17*/.go.controllers.routes.Assets.versioned("go/go.js")),format.raw/*13.68*/("""'></script>

"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:21 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/imports/scripts.scala.html
                  HASH: 913c546253e5d4d77ea3e749d120b1504a80d95d
                  MATRIX: 1046->0|1086->14|1096->16|1169->69|1221->95|1231->97|1315->161|1367->187|1377->189|1481->272|1533->298|1543->300|1637->374|1689->400|1699->402|1808->490|1860->516|1870->518|1977->604|2029->630|2039->632|2148->720|2200->746|2210->748|2316->833|2368->859|2378->861|2484->946|2537->972|2548->974|2646->1051|2699->1077|2710->1079|2788->1136|2841->1162|2852->1164|2940->1231|2993->1257|3004->1259|3076->1310
                  LINES: 33->1|33->1|33->1|33->1|34->2|34->2|34->2|35->3|35->3|35->3|36->4|36->4|36->4|37->5|37->5|37->5|38->6|38->6|38->6|39->7|39->7|39->7|40->8|40->8|40->8|41->9|41->9|41->9|42->10|42->10|42->10|43->11|43->11|43->11|44->12|44->12|44->12|45->13|45->13|45->13
                  -- GENERATED --
              */
          