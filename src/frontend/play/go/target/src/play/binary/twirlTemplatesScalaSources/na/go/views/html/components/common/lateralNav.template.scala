
package na.go.views.html.components.common

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.go.settings.GoConstants.NavContext;
/*2.2*/import scala.collection.mutable
/*3.2*/import na.naportalbase.views.html.components.lateralNavComp.lateralNavComp
/*4.2*/import na.naportalbase.models.lateralNav.LateralNavigationComponentItem
/*5.2*/import scala.collection.mutable.ArrayBuffer
/*6.2*/import na.naportalbase.views.tags.i18n

object lateralNav extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[NavContext,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*8.2*/(activeContext: NavContext):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*10.2*/navElements/*10.13*/ = {{

    var map = mutable.LinkedHashMap[String, mutable.HashMap[String,String]]()

    var goNavElementAttributes = mutable.HashMap[String,String]()
    goNavElementAttributes.put("data-ng-click", "searchGenericOperations()")
	goNavElementAttributes.put("data-tab-icon", "fuxicons fuxicons-go")
	goNavElementAttributes.put("link-class", "nav-generic-operations")
    map.put("na.portal.go.home.mainnav.go", goNavElementAttributes)

    var adminElementAttributes = mutable.HashMap[String,String]()
    adminElementAttributes.put("data-ng-click", "searchGenericOperationsAdmin()")
    adminElementAttributes.put("data-tab-icon", "fuxicons fuxicons-go-admin")
	adminElementAttributes.put("link-class", "nav-generic-admin-operations")
	map.put("na.portal.go.home.mainnav.admin", adminElementAttributes)

    activeContext match {
        case NavContext.ADMIN_TEMPLATES => adminElementAttributes.put("data-active-tab",null)
        case NavContext.OPERATIONS =>     goNavElementAttributes.put("data-active-tab",null)
    }

    map
}};def /*34.2*/linkClassAttr/*34.15*/(value:scala.collection.mutable.HashMap[String,String]) = {{
    var result = new ArrayBuffer[String]()
    if(value.contains("data-active-tab")){
        result += "fx-active"
    }
    if(value.contains("link-class")){
        result += value.get("link-class").get
    }

    if (result.nonEmpty) result.mkString(" ") else ""
}};
Seq[Any](format.raw/*8.29*/("""

"""),format.raw/*32.2*/("""

"""),format.raw/*44.2*/("""
"""),format.raw/*45.1*/("""<!-- data-enable-tooltip -->
<nav id="fx-page-main-nav" class="fx-ico-nav" data-ng-controller="NaPortalGoNavigationController">
    <ul class="list-unstyled">
    """),_display_(/*48.6*/for((key,value) <- navElements) yield /*48.37*/ {_display_(Seq[Any](format.raw/*48.39*/("""

        """),format.raw/*50.9*/("""<li class=""""),_display_(/*50.21*/linkClassAttr(value)),format.raw/*50.41*/("""" data-placement="right" data-toggle="tooltip" data-enable-tooltip
        data-original-title=""""),_display_(/*51.31*/i18n(key)),format.raw/*51.40*/("""">
            <a data-ng-click="""),_display_(/*52.31*/value/*52.36*/.get("data-ng-click")),format.raw/*52.57*/(""">
                <i class=""""),_display_(/*53.28*/value/*53.33*/.get("data-tab-icon")),format.raw/*53.54*/(""""></i>
            </a>
        </li>
    """)))}),format.raw/*56.6*/("""
    """),format.raw/*57.5*/("""</ul>
</nav>"""))
      }
    }
  }

  def render(activeContext:NavContext): play.twirl.api.HtmlFormat.Appendable = apply(activeContext)

  def f:((NavContext) => play.twirl.api.HtmlFormat.Appendable) = (activeContext) => apply(activeContext)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:21 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/common/lateralNav.scala.html
                  HASH: c477398219ceac6cf72120a46d9ef6f36f50af2a
                  MATRIX: 675->1|728->48|767->81|849->157|928->230|979->275|1331->316|1437->346|1457->357|2504->1393|2526->1406|2885->343|2914->1390|2943->1735|2971->1736|3161->1900|3208->1931|3248->1933|3285->1943|3324->1955|3365->1975|3489->2072|3519->2081|3579->2114|3593->2119|3635->2140|3691->2169|3705->2174|3747->2195|3820->2238|3852->2243
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|34->8|38->10|38->10|60->34|60->34|71->8|73->32|75->44|76->45|79->48|79->48|79->48|81->50|81->50|81->50|82->51|82->51|83->52|83->52|83->52|84->53|84->53|84->53|87->56|88->57
                  -- GENERATED --
              */
          