
package na.go.views.html.components.dynamicForm.fields

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import scala.collection.mutable
/*2.2*/import na.naportalbase.settings.BaseMappings
/*3.2*/import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.components.TextArea
/*4.2*/import na.naportalbase.views.html.components.inputsComp.inputTextAreaComp
/*5.2*/import na.naportalbase.views.tags.i18n

object textField extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*7.2*/(mapping: pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*9.2*/textAreaComponent/*9.19*/ = {{mapping.getComponent.asInstanceOf[TextArea]}};def /*10.2*/inputLabel/*10.12*/ = {{mapping.getLabel}};def /*11.2*/inputId/*11.9*/ = {{mapping.getTemplateMapping}};def /*12.2*/placeHolder/*12.13*/ = {{mapping.getLabel}};def /*13.2*/inputValue/*13.12*/ = {{ mapping.getComponent.getDefaultValue }};def /*14.2*/isRequired/*14.12*/ = {{textAreaComponent.getRequired}};def /*15.2*/inputAttributes/*15.17*/ = {{
    var map = mutable.HashMap[String, String](
        "data-ng-model" -> ("model[\""+inputId+"\"]"),
        "data-model" -> mapping.getTemplateMapping,
        "data-na-portal-go-dynamic-form-text-area" -> null,
        "name" -> inputId,
        "value" -> "",
        "rows" -> "3",
        "id" -> inputId,
        "data-component" -> BaseMappings.dualMapper.valueToTree(textAreaComponent),
        "class" -> "form-control input-sm pull-left"
    )

    if(textAreaComponent.getRegex){
        map.put("data-na-portal-regex-error",null)
        map.put("regex", textAreaComponent.getRegex)
        map.put("notification-message", textAreaComponent.getRegexErrorMessage)
    }
    if(textAreaComponent.getMaxSize != null){
        map.put("maxlength", String.valueOf(textAreaComponent.getMaxSize))
    }
    if(textAreaComponent.getHeight != null){
        map.put("rows", String.valueOf(textAreaComponent.getHeight))
    }
    if(textAreaComponent.getDefaultValue != null){
        map.put("value", textAreaComponent.getDefaultValue)
    }
    if(isRequired){
        map.put("data-na-portal-required", null)
    }
    if(!textAreaComponent.getEditable){
        map.put("disabled", null)
    }

    map
}};
Seq[Any](format.raw/*7.78*/("""

"""),format.raw/*9.68*/("""
"""),format.raw/*10.34*/("""
"""),format.raw/*11.41*/("""
"""),format.raw/*12.35*/("""
"""),format.raw/*13.56*/("""
"""),format.raw/*14.47*/("""
"""),format.raw/*50.2*/("""

"""),format.raw/*52.1*/("""<div class="form-group">
    <label for=""""),_display_(/*53.18*/inputId),format.raw/*53.25*/("""" class="col-sm-2 control-label """),_display_(/*53.58*/if(isRequired)/*53.72*/{_display_(Seq[Any](format.raw/*53.73*/("""fx-required""")))}),format.raw/*53.85*/("""" title=""""),_display_(/*53.95*/mapping/*53.102*/.getDescription),format.raw/*53.117*/("""">
        """),_display_(/*54.10*/inputLabel),_display_(/*54.21*/if(isRequired)/*54.35*/{_display_(Seq[Any](format.raw/*54.36*/("""<abbr title=""""),_display_(/*54.50*/i18n("na.basemodule.validations.mandatory")),format.raw/*54.93*/(""""></abbr>""")))}),format.raw/*54.103*/("""
    """),format.raw/*55.5*/("""</label>
    <div class="col-sm-10" data-na-portal-field-notification data-na-portal-button-notification>
    """),_display_(/*57.6*/inputTextAreaComp(inputId,inputAttributes,inputValue)),format.raw/*57.59*/("""
    """),format.raw/*58.5*/("""</div>
</div>"""))
      }
    }
  }

  def render(mapping:pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping): play.twirl.api.HtmlFormat.Appendable = apply(mapping)

  def f:((pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping) => play.twirl.api.HtmlFormat.Appendable) = (mapping) => apply(mapping)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:12 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/dynamicForm/fields/textField.scala.html
                  HASH: ee3bfcc683148288803bc109e83f6f0fb1793465
                  MATRIX: 687->1|726->34|778->80|870->166|951->241|1357->282|1511->361|1536->378|1599->429|1618->439|1654->463|1669->470|1715->504|1735->515|1771->539|1790->549|1848->595|1867->605|1916->642|1940->657|3187->358|3216->427|3245->461|3274->502|3303->537|3332->593|3361->640|3389->1874|3418->1876|3487->1918|3515->1925|3575->1958|3598->1972|3637->1973|3680->1985|3717->1995|3734->2002|3771->2017|3810->2029|3841->2040|3864->2054|3903->2055|3944->2069|4008->2112|4050->2122|4082->2127|4219->2238|4293->2291|4325->2296
                  LINES: 24->1|25->2|26->3|27->4|28->5|33->7|37->9|37->9|37->10|37->10|37->11|37->11|37->12|37->12|37->13|37->13|37->14|37->14|37->15|37->15|73->7|75->9|76->10|77->11|78->12|79->13|80->14|81->50|83->52|84->53|84->53|84->53|84->53|84->53|84->53|84->53|84->53|84->53|85->54|85->54|85->54|85->54|85->54|85->54|85->54|86->55|88->57|88->57|89->58
                  -- GENERATED --
              */
          