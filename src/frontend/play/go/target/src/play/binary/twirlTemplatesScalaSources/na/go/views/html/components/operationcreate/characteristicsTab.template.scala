
package na.go.views.html.components.operationcreate

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import scala.collection.immutable
/*2.2*/import na.naportalbase.views.tags.i18n
/*3.2*/import na.naportalbase.views.html.components.selectsComp.dynamicSelectOptionComp
/*4.2*/import na.naportalbase.views.html.components.selectsComp.selectComp
/*5.2*/import na.naportalbase.views.html.components.wizard.wizardContentComp
/*6.2*/import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Family

object characteristicsTab extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[String,List[Family],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*8.2*/(ngModelVariable: String, groups: List[Family]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*119.2*/headContent/*119.13*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*119.17*/("""
    """),format.raw/*120.5*/("""<p class="fx-required">
        <abbr title=""""),_display_(/*121.23*/i18n("na.basemodule.validations.mandatory")),format.raw/*121.66*/(""""></abbr>
        """),_display_(/*122.10*/i18n("na.portal.go.operation.create.legend.requiredfields.label")),format.raw/*122.75*/("""
    """),format.raw/*123.5*/("""</p>
    <p>
        <strong>
        """),_display_(/*126.10*/i18n("na.portal.go.operation.create.tab.characteristics.description.title")),format.raw/*126.85*/("""
        """),format.raw/*127.9*/("""</strong>
    </p>
    <p>"""),_display_(/*129.9*/i18n("na.portal.go.operation.create.tab.characteristics.description.text")),format.raw/*129.83*/("""</p>
""")))};def /*133.2*/blockContent/*133.14*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*133.18*/("""
        """),format.raw/*134.9*/("""<!-- FAMILY FIELD -->
    <div class="form-group">
        <label for="selectRfsNames" class="col-sm-2 control-label fx-required">
            """),_display_(/*137.14*/operationFamilyLabel),format.raw/*137.34*/("""<abbr title=""""),_display_(/*137.48*/i18n("na.basemodule.validations.mandatory")),format.raw/*137.91*/(""""></abbr>
        </label>
        <div class="col-sm-10">
        """),_display_(/*140.10*/selectComp/*140.20*/.render(
            operationFamilySelectAttributes, null, null, operationFamilySelectData)),format.raw/*141.84*/("""
        """),format.raw/*142.9*/("""</div>
    </div>

        <!-- RFS FIELD -->
    <div class="form-group">
        <label for=""""),_display_(/*147.22*/operationRfsId),format.raw/*147.36*/("""" class="col-sm-2 control-label fx-required">
            """),_display_(/*148.14*/operationRfsLabel),format.raw/*148.31*/("""<abbr title=""""),_display_(/*148.45*/i18n("na.basemodule.validations.mandatory")),format.raw/*148.88*/(""""></abbr>
        </label>
        <div class="col-sm-10">
        """),_display_(/*151.10*/selectComp/*151.20*/.render(
            operationRfsSelectAttributes, null, null, operationRfsSelectData)),format.raw/*152.78*/("""
        """),format.raw/*153.9*/("""</div>
    </div>

        <!-- OPERATION FIELD -->
    <div class="form-group">
        <label for=""""),_display_(/*158.22*/operationOpId),format.raw/*158.35*/("""" class="col-sm-2 control-label fx-required">
            """),_display_(/*159.14*/operationOpLabel),format.raw/*159.30*/("""<abbr title=""""),_display_(/*159.44*/i18n("na.basemodule.validations.mandatory")),format.raw/*159.87*/(""""></abbr>
        </label>
        <div class="col-sm-10">
        """),_display_(/*162.10*/selectComp/*162.20*/.render(operationOpSelectAttributes, null, null, operationOpSelectData)),format.raw/*162.91*/("""
        """),format.raw/*163.9*/("""</div>
    </div>
""")))};def /*10.2*/prefixId/*10.10*/ = {{
    ""
}};def /*15.2*/operationFamilyId/*15.19*/ = {{
    "family-field"
}};def /*18.2*/operationFamilyLabel/*18.22*/ = {{
    i18n("na.portal.go.family.field.label")
}};def /*21.2*/operationFamilySelectAttributes/*21.33*/ = {{
    var map = immutable.HashMap[String, String](
        "data-ng-model" -> (ngModelVariable + ".data.family"),
        "name" -> operationFamilyId,
        "data-na-portal-select-box" -> null,
        "data-na-portal-select-box-auto-dropdown-size" -> null,
        "data-na-portal-go-wizard-family-field" -> null,
        "value" -> "",
        "id" -> (prefixId + operationFamilyId),
        "class" -> "form-control input-sm",
        "data-context" -> "family",
        "full-width" -> "",
    )
    map
}};def /*37.2*/operationFamilySelectData/*37.27*/ = {{
    var stringBuilder = new StringBuilder
    for(group <- groups) {
        var optionAttributes = immutable.HashMap[String, String](
            "id" -> group.getName,
            "text" -> group.getName,
            "value" -> group.getName
        )
        stringBuilder.append(dynamicSelectOptionComp.render(group.getName, optionAttributes))
    }
    Html(stringBuilder.toString())
}};def /*52.2*/operationRfsId/*52.16*/ = {{
    "selectRfsNames"
}};def /*55.2*/operationRfsLabel/*55.19*/ = {{
    i18n("na.portal.go.rfs.field.label")
}};def /*59.2*/operationRfsSelectAttributes/*59.30*/ = {{
    var map = immutable.HashMap[String, String](
        "data-ng-model" -> (ngModelVariable + ".data.rfs"),
        "name" -> operationRfsId,
        "data-na-portal-select-box" -> null,
        "data-na-portal-select-box-auto-dropdown-size" -> null,
        "data-na-portal-go-wizard-rfs-field" -> null,
        "value" -> "",
        "id" -> (prefixId + operationRfsId),
        "class" -> "form-control input-sm",
        "data-context" -> "rfs",
        "full-width" -> "",
    )
    map
}};def /*75.2*/operationRfsSelectData/*75.24*/ = {{
    var stringBuilder = new StringBuilder
    Html(stringBuilder.toString())
}};def /*81.2*/operationOpId/*81.15*/ = {{
    "operation"
}};def /*84.2*/operationOpLabel/*84.18*/ = {{
    i18n("na.portal.go.operation.field.label")
}};def /*88.2*/operationOpSelectAttributes/*88.29*/ = {{
    immutable.HashMap[String, String](
        "data-ng-model" -> (ngModelVariable + ".data.operation"),
        "name" -> operationOpId,
        "data-na-portal-select-box" -> null,
        "data-na-portal-select-box-auto-dropdown-size" -> null,
        "data-na-portal-go-wizard-operation-field" -> null,
        "value" -> "",
        "id" -> (prefixId + operationOpId),
        "class" -> "form-control input-sm",
        "full-width" -> "",
    )
}};def /*102.2*/operationOpSelectData/*102.23*/ = {{
    var list = immutable.List[String](
        "getFisicosBits", "getClientInfoMox", "getFastZapping", "getUserStackedPortCounters", "getClients", "trDiagnostic"
    )
    var stringBuilder = new StringBuilder
    for(option <- list) {
        var optionAttributes = immutable.HashMap[String, String](
            "name" -> option,
            "id" -> option,
            "value" -> option
        )
        stringBuilder.append(dynamicSelectOptionComp.render(option, optionAttributes));
    }
    Html(stringBuilder.toString())
}};
Seq[Any](format.raw/*8.49*/("""

"""),format.raw/*12.2*/("""

    """),format.raw/*14.5*/("""<!-- FAMILY FIELD CONFIG -->
"""),format.raw/*17.2*/("""
"""),format.raw/*20.2*/("""
"""),format.raw/*35.2*/("""

"""),format.raw/*48.2*/("""


    """),format.raw/*51.5*/("""<!-- RFS FIELD CONFIG -->
"""),format.raw/*54.2*/("""
"""),format.raw/*57.2*/("""

"""),format.raw/*73.2*/("""

"""),format.raw/*78.2*/("""

    """),format.raw/*80.5*/("""<!-- OPERATION FIELD CONFIG -->
"""),format.raw/*83.2*/("""
"""),format.raw/*86.2*/("""

"""),format.raw/*100.2*/("""

"""),format.raw/*116.2*/("""


"""),format.raw/*130.2*/("""


"""),format.raw/*165.2*/("""


"""),_display_(/*168.2*/wizardContentComp/*168.19*/.render(headContent, blockContent)))
      }
    }
  }

  def render(ngModelVariable:String,groups:List[Family]): play.twirl.api.HtmlFormat.Appendable = apply(ngModelVariable,groups)

  def f:((String,List[Family]) => play.twirl.api.HtmlFormat.Appendable) = (ngModelVariable,groups) => apply(ngModelVariable,groups)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:12 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/operationcreate/characteristicsTab.scala.html
                  HASH: f2b6f1815398a3fa4479698fb873f383d531068e
                  MATRIX: 684->1|725->36|771->76|859->158|934->227|1011->298|1413->372|1540->3551|1561->3562|1643->3566|1676->3571|1750->3617|1815->3660|1862->3679|1949->3744|1982->3749|2049->3788|2146->3863|2183->3872|2237->3899|2333->3973|2363->3983|2385->3995|2467->3999|2504->4008|2676->4152|2718->4172|2760->4186|2825->4229|2921->4297|2941->4307|3055->4399|3092->4408|3216->4504|3252->4518|3339->4577|3378->4594|3420->4608|3485->4651|3581->4719|3601->4729|3709->4815|3746->4824|3876->4926|3911->4939|3998->4998|4036->5014|4078->5028|4143->5071|4239->5139|4259->5149|4352->5220|4389->5229|4431->422|4448->430|4476->480|4502->497|4542->525|4571->545|4636->598|4676->629|5205->1147|5239->1172|5649->1602|5672->1616|5714->1646|5740->1663|5802->1714|5839->1742|6353->2245|6384->2267|6482->2390|6504->2403|6541->2428|6566->2444|6634->2501|6670->2528|7144->2990|7175->3011|7741->419|7770->444|7803->450|7859->523|7887->596|7915->1144|7944->1568|7978->1575|8031->1644|8059->1711|8088->2242|8117->2351|8150->2357|8209->2426|8237->2498|8267->2987|8297->3547|8328->3979|8359->5248|8390->5252|8417->5269
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|34->8|38->119|38->119|40->119|41->120|42->121|42->121|43->122|43->122|44->123|47->126|47->126|48->127|50->129|50->129|51->133|51->133|53->133|54->134|57->137|57->137|57->137|57->137|60->140|60->140|61->141|62->142|67->147|67->147|68->148|68->148|68->148|68->148|71->151|71->151|72->152|73->153|78->158|78->158|79->159|79->159|79->159|79->159|82->162|82->162|82->162|83->163|85->10|85->10|87->15|87->15|89->18|89->18|91->21|91->21|105->37|105->37|116->52|116->52|118->55|118->55|120->59|120->59|134->75|134->75|137->81|137->81|139->84|139->84|141->88|141->88|153->102|153->102|168->8|170->12|172->14|173->17|174->20|175->35|177->48|180->51|181->54|182->57|184->73|186->78|188->80|189->83|190->86|192->100|194->116|197->130|200->165|203->168|203->168
                  -- GENERATED --
              */
          