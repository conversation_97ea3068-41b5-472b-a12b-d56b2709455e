// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/conf/go.routes
// @DATE:Tue Jul 01 11:21:12 WEST 2025

import play.api.routing.JavaScriptReverseRoute


import _root_.controllers.Assets.Asset

// @LINE:2
package na.go.controllers.javascript {

  // @LINE:14
  class ReverseAssets(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:14
    def versioned: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.Assets.versioned",
      """
        function(file1) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/assets/" + (""" + implicitly[play.api.mvc.PathBindable[Asset]].javascriptUnbind + """)("file", file1)})
        }
      """
    )
  
  }

  // @LINE:52
  class ReverseAdminOperation(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:57
    def updateTemplate: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.AdminOperation.updateTemplate",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "go/admin/update"})
        }
      """
    )
  
    // @LINE:59
    def createTemplate: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.AdminOperation.createTemplate",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "go/admin/create"})
        }
      """
    )
  
    // @LINE:55
    def addTemplatePage: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.AdminOperation.addTemplatePage",
      """
        function(templateId0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/admin/add/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("templateId", templateId0))})
        }
      """
    )
  
    // @LINE:58
    def createTemplatePage: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.AdminOperation.createTemplatePage",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/admin/create"})
        }
      """
    )
  
    // @LINE:60
    def getCharacteristicsTab: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.AdminOperation.getCharacteristicsTab",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/admin/create/tab/characteristics"})
        }
      """
    )
  
    // @LINE:61
    def getOperationTab: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.AdminOperation.getOperationTab",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/admin/create/tab/operation"})
        }
      """
    )
  
    // @LINE:53
    def changeTemplateState: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.AdminOperation.changeTemplateState",
      """
        function(templateId0,active1,family2,rfs3) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "go/admin/changeTemplateState" + _qS([(""" + implicitly[play.api.mvc.QueryStringBindable[Long]].javascriptUnbind + """)("templateId", templateId0), (""" + implicitly[play.api.mvc.QueryStringBindable[Boolean]].javascriptUnbind + """)("active", active1), (""" + implicitly[play.api.mvc.QueryStringBindable[String]].javascriptUnbind + """)("family", family2), (""" + implicitly[play.api.mvc.QueryStringBindable[String]].javascriptUnbind + """)("rfs", rfs3)])})
        }
      """
    )
  
    // @LINE:56
    def editTemplatePage: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.AdminOperation.editTemplatePage",
      """
        function(templateId0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/admin/edit/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("templateId", templateId0))})
        }
      """
    )
  
    // @LINE:52
    def index: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.AdminOperation.index",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/admin"})
        }
      """
    )
  
    // @LINE:54
    def deleteTemplate: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.AdminOperation.deleteTemplate",
      """
        function(templateId0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "go/admin/deleteTemplate" + _qS([(""" + implicitly[play.api.mvc.QueryStringBindable[Long]].javascriptUnbind + """)("templateId", templateId0)])})
        }
      """
    )
  
  }

  // @LINE:37
  class ReverseOperation(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:39
    def cloneOperationPage: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.Operation.cloneOperationPage",
      """
        function(id0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/create/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("id", id0))})
        }
      """
    )
  
    // @LINE:40
    def getCharacteristicsTab: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.Operation.getCharacteristicsTab",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/create/tab/characteristics"})
        }
      """
    )
  
    // @LINE:41
    def getOperationTab: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.Operation.getOperationTab",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/create/tab/operation"})
        }
      """
    )
  
    // @LINE:38
    def createOperation: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.Operation.createOperation",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "go/create"})
        }
      """
    )
  
    // @LINE:42
    def uploadFirmwareFile: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.Operation.uploadFirmwareFile",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "go/upload/firmwarefile"})
        }
      """
    )
  
    // @LINE:37
    def createOperationPage: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.Operation.createOperationPage",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/create"})
        }
      """
    )
  
  }

  // @LINE:28
  class ReverseModals(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:29
    def getTemplateSaveFilterModal: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.Modals.getTemplateSaveFilterModal",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/template/savefiltermodal"})
        }
      """
    )
  
    // @LINE:30
    def saveFilter: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.Modals.saveFilter",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "go/savefilter"})
        }
      """
    )
  
    // @LINE:33
    def getDeleteFilterModal: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.Modals.getDeleteFilterModal",
      """
        function(filterName0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/deletefiltermodal/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("filterName", filterName0))})
        }
      """
    )
  
    // @LINE:32
    def editFilter: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.Modals.editFilter",
      """
        function(filterName0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "go/editfilter/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("filterName", filterName0))})
        }
      """
    )
  
    // @LINE:28
    def getOperationSaveFilterModal: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.Modals.getOperationSaveFilterModal",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/operation/savefiltermodal"})
        }
      """
    )
  
    // @LINE:34
    def deleteFilter: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.Modals.deleteFilter",
      """
        function(filterName0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "go/deletefilter/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("filterName", filterName0))})
        }
      """
    )
  
    // @LINE:31
    def getEditFilterModal: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.Modals.getEditFilterModal",
      """
        function(filterName0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/editfiltermodal/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("filterName", filterName0))})
        }
      """
    )
  
  }

  // @LINE:2
  class ReverseApplication(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:2
    def index: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.Application.index",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/home"})
        }
      """
    )
  
    // @LINE:11
    def getGOModuleAngularScripts: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.Application.getGOModuleAngularScripts",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/imports/jsscripts"})
        }
      """
    )
  
    // @LINE:8
    def javascriptRoutes: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.Application.javascriptRoutes",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/assets/javascripts/routes"})
        }
      """
    )
  
    // @LINE:5
    def resume: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.Application.resume",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/resume"})
        }
      """
    )
  
  }

  // @LINE:64
  class ReverseTableConfigs(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:65
    def templatesSearch: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.TableConfigs.templatesSearch",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/table-configs/templates-seach-table"})
        }
      """
    )
  
    // @LINE:66
    def lrResponse: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.TableConfigs.lrResponse",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/table-configs/lr-response"})
        }
      """
    )
  
    // @LINE:64
    def operationsSearch: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.TableConfigs.operationsSearch",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/table-configs/operations-seach-table"})
        }
      """
    )
  
  }

  // @LINE:17
  class ReverseSearch(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:18
    def getFieldsForFiltername: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.Search.getFieldsForFiltername",
      """
        function(filterName0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/getFieldsForFiltername/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("filterName", filterName0))})
        }
      """
    )
  
    // @LINE:17
    def getFilterNamesForSelect: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.Search.getFilterNamesForSelect",
      """
        function(context0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/selectfilternames/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context0))})
        }
      """
    )
  
  }

  // @LINE:21
  class ReverseOperationDetails(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:21
    def getOperationDetails: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.OperationDetails.getOperationDetails",
      """
        function(id0,context1) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/operationdetails/view/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("id", id0)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("context", context1))})
        }
      """
    )
  
  }

  // @LINE:24
  class ReverseOperationsTable(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:24
    def getTableDataforOperations: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.OperationsTable.getTableDataforOperations",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "go/operationtabledata"})
        }
      """
    )
  
    // @LINE:25
    def getAdminTableDataforOperations: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.OperationsTable.getAdminTableDataforOperations",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "go/admin/operationtabledata"})
        }
      """
    )
  
  }

  // @LINE:46
  class ReverseCatalog(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:48
    def getOperationTemplatesOfRfs: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.Catalog.getOperationTemplatesOfRfs",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/catalog/operationTemplates"})
        }
      """
    )
  
    // @LINE:47
    def getDistinctRfsOfFamily: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.Catalog.getDistinctRfsOfFamily",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/catalog/distinctRfsNames"})
        }
      """
    )
  
    // @LINE:46
    def getRfsOfFamily: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.Catalog.getRfsOfFamily",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/catalog/rfsNames"})
        }
      """
    )
  
    // @LINE:49
    def getDistinctOperationTemplates: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.go.controllers.Catalog.getDistinctOperationTemplates",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "go/catalog/distinctOperationTemplates"})
        }
      """
    )
  
  }


}
