
package na.go.views.html.components.operationdetails

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import scala.collection.mutable
/*2.2*/import scala.collection.immutable
/*3.2*/import na.naportalbase.views.tags.i18n
/*4.2*/import na.go.controllers.routes.TableConfigs

object operationDetailsCharacteristicsTab extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*6.2*/operationdetailstableattributes/*6.33*/ = {{

    var map = mutable.HashMap[String,String]()

    map.put("id","datatableOperationDetails")
    map.put("data-module","go")
    map.put("data-entity","operations")
    map.put("data-context","operationDetails")

    map
}};def /*19.2*/operationDetailsLrTableAttributes/*19.35*/ = {{

    var map = mutable.HashMap[String,String]()

    map.put("id","datatableOperationDetailsLR")
    map.put("data-module","go")
    map.put("data-entity","operations")
    map.put("data-context","operationDetails")

    map
}};def /*31.2*/characteristicsTabAttributes/*31.30*/ = {{

    var map = immutable.HashMap[String, String]()

    map
}};
Seq[Any](format.raw/*16.2*/("""


"""),format.raw/*29.2*/("""

"""),format.raw/*36.2*/("""


"""),format.raw/*39.1*/("""<x-collapsible class="fx-section" header=""""),_display_(/*39.44*/{i18n("na.portal.go.operation.details.label.description")}),format.raw/*39.102*/("""">
    <div class="fx-form-content fx-expandable-form-blocks">
        <div class="form-horizontal">
            <div class="form-group">
                <label class="col-sm-2 control-label">"""),_display_(/*43.56*/i18n("na.portal.go.operation.details.label.family")),format.raw/*43.107*/("""</label>
                <div class="col-sm-10">
                    <p class="form-control-static" id="descriptionBlock-family" data-ng-bind="detailsCtrl.operation.family">
                    </p>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">"""),_display_(/*50.56*/i18n("na.portal.go.operation.details.label.rfs")),format.raw/*50.104*/("""</label>
                <div class="col-sm-10">
                    <p class="form-control-static" id="descriptionBlock-rfs" data-ng-bind="detailsCtrl.operation.rfs">
                    </p>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">"""),_display_(/*57.56*/i18n("na.portal.go.operation.details.label.operation")),format.raw/*57.110*/("""</label>
                <div class="col-sm-10">
                    <p class="form-control-static" id="descriptionBlock-operation" data-ng-bind="detailsCtrl.operation.operationName">
                    </p>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">"""),_display_(/*64.56*/i18n("na.portal.go.operation.details.label.username")),format.raw/*64.109*/("""</label>
                <div class="col-sm-10">
                    <p class="form-control-static" id="descriptionBlock-user" data-ng-bind="detailsCtrl.operation.username">
                    </p>
                </div>
            </div>
        </div>
    </div>
</x-collapsible>

<x-collapsible class="fx-section" header=""""),_display_(/*74.44*/{i18n("na.portal.go.operation.details.label.state")}),format.raw/*74.96*/("""">
    <div class="form-group">
        <div data-na-portal-go-operation-state-datatable>
            <div data-ng-controller="NaPortalDatatablesController">
                <table id="datatableOperationDetails" class="table table-striped table-hover no-footer"
                    data-na-portal-datatable
                    data-module="go"
                    data-entity="operations"
                    data-context="operationDetails"
                    data-dt-options="dtOptions"
                    data-dt-columns="dtColumns">
                </table>
            </div>
        </div>
    </div>
</x-collapsible>

<x-collapsible class="fx-section" header=""""),_display_(/*91.44*/{i18n("na.portal.go.operation.details.label.payload")}),format.raw/*91.98*/("""">
    <div data-pretty-print style="display: block">

        <x-collapsible header=""""),_display_(/*94.33*/i18n("na.portal.go.operation.details.label.request")),format.raw/*94.85*/("""">
            <div>
                <pre id="payload-request-content" class="prettyprint lang-xml" data-ng-bind="detailsCtrl.operation.request.payload">
                </pre>
            </div>
        </x-collapsible>

        <x-collapsible header=""""),_display_(/*101.33*/i18n("na.portal.go.operation.details.label.response")),format.raw/*101.86*/("""">
            <div class="in">
                <div data-ng-show="detailsCtrl.operation.response.payload != '-'">
                    <div data-na-portal-go-lr-response id="datatableLR" data-data-bind="operationDetailsLRTableData">
                        <div class="fx-bulk-actions clearfix">
                            <div class="pull-right">
                                <div class="btn-group" id="view-types-btn">
                                    <button class="btn btn-default btn-xs" data-ng-class=""""),format.raw/*108.91*/("""{"""),format.raw/*108.92*/("""active: view == 'table'"""),format.raw/*108.115*/("""}"""),format.raw/*108.116*/("""" title=""""),_display_(/*108.126*/i18n("na.portal.go.operation.details.see.table")),format.raw/*108.174*/("""" id="bt-table-view" data-ng-click="view = 'table'">
                                        <i class="fa fa-table"></i>
                                    </button>
                                    <button class="btn btn-default btn-xs" data-ng-class=""""),format.raw/*111.91*/("""{"""),format.raw/*111.92*/("""active: view == 'code'"""),format.raw/*111.114*/("""}"""),format.raw/*111.115*/("""" title=""""),_display_(/*111.125*/i18n("na.portal.go.operation.details.see.code")),format.raw/*111.172*/("""" id="bt-code-view" data-ng-click="view = 'code'">
                                        <i class="fa fa-code"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div data-ng-show="view == 'table'">
                            <div id="datatableLrResponse" class="table table-striped table-hover no-footer"
                                data-na-portal-table-datatable
                                data-na-portal-table-config-url=""""),_display_(/*120.67*/TableConfigs/*120.79*/.lrResponse()),format.raw/*120.92*/(""""
                                data-na-portal-go-lr-datatable>
                            </div>
                        </div>
                        <div data-ng-show="view == 'code'">
                            <pre id="payload-response-content" class="prettyprint lang-xml" data-ng-bind="detailsCtrl.operation.response.payload">
                            </pre>
                        </div>
                    </div>
                </div>
                <div data-ng-show="detailsCtrl.operation.response.payload == '-'">
                    <i>"""),_display_(/*131.25*/i18n("na.portal.go.operation.details.payload.empty")),format.raw/*131.77*/("""</i>
                </div>
            </div>
        </x-collapsible>
    </div>
</x-collapsible>

"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:12 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/operationdetails/operationDetailsCharacteristicsTab.scala.html
                  HASH: 0adf11808c5e7d8a77627d1cd53d47a6980bfe75
                  MATRIX: 685->1|724->34|765->69|811->109|1255->156|1294->187|1538->421|1580->454|1826->689|1863->717|1960->417|1990->686|2019->784|2049->787|2119->830|2199->888|2419->1081|2492->1132|2852->1465|2922->1513|3276->1840|3352->1894|3722->2237|3797->2290|4152->2618|4225->2670|4921->3339|4996->3393|5110->3480|5183->3532|5465->3786|5540->3839|6084->4354|6114->4355|6167->4378|6198->4379|6237->4389|6308->4437|6594->4694|6624->4695|6676->4717|6707->4718|6746->4728|6816->4775|7411->5342|7433->5354|7468->5367|8058->5929|8132->5981
                  LINES: 24->1|25->2|26->3|27->4|36->6|36->6|46->19|46->19|56->31|56->31|62->16|65->29|67->36|70->39|70->39|70->39|74->43|74->43|81->50|81->50|88->57|88->57|95->64|95->64|105->74|105->74|122->91|122->91|125->94|125->94|132->101|132->101|139->108|139->108|139->108|139->108|139->108|139->108|142->111|142->111|142->111|142->111|142->111|142->111|151->120|151->120|151->120|162->131|162->131
                  -- GENERATED --
              */
          