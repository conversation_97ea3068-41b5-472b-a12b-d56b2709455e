
package na.go.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import na.go.views.html.skeletons.mainSkel
/*3.2*/import na.go.settings.GoConstants.NavContext
/*4.2*/import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Family
/*5.2*/import na.go.models.ui.UITemplate
/*6.2*/import play.libs.Json
/*7.2*/import na.go.views.html.components.common.lateralNav
/*8.2*/import na.go.views.html.components.admintemplateedit.operationWizard

object adminTemplateEdit extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[List[Family],String,UITemplate,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*10.2*/(groups: List[Family], tab: String, template: UITemplate):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*10.59*/("""

"""),_display_(/*12.2*/mainSkel()/*12.12*/ {_display_(Seq[Any](format.raw/*12.14*/("""
	"""),format.raw/*13.2*/("""<div class="layout--page-with-navbar" data-na-portal-go-edit-template-breadcrumb>
		"""),_display_(/*14.4*/lateralNav/*14.14*/.render(NavContext.ADMIN_TEMPLATES)),format.raw/*14.49*/("""
		"""),format.raw/*15.3*/("""<div class="fx-main-content-wrapper go-wizard-page">
			<div class="fx-entity-header" data-template-loader data-context="template">
				<div class="fx-entity-header-info">
					<span class="fx-entity-header-icon">
						<i class="fuxicons fuxicons-go-admin"></i>
					</span>
					<div class="fx-entity-header-title">
						<h1>
						"""),_display_(/*23.8*/i18n("na.portal.go.admin.template.edit.title")),format.raw/*23.54*/("""
						"""),format.raw/*24.7*/("""</h1>
					</div>
				</div>
			</div>
			<div class="fx-entity-info" data-na-portal-go-common>
				<x-tab-container>
					<x-tab label=""""),_display_(/*30.21*/i18n("na.portal.go.operation.details.characteristics")),format.raw/*30.75*/("""">
					"""),_display_(/*31.7*/operationWizard/*31.22*/.render(groups, Json.toJson(template).toString, "edit-template")),format.raw/*31.86*/("""
					"""),format.raw/*32.6*/("""</x-tab>
				</x-tab-container>
			</div>
		</div>
	</div>
""")))}),format.raw/*37.2*/("""


"""))
      }
    }
  }

  def render(groups:List[Family],tab:String,template:UITemplate): play.twirl.api.HtmlFormat.Appendable = apply(groups,tab,template)

  def f:((List[Family],String,UITemplate) => play.twirl.api.HtmlFormat.Appendable) = (groups,tab,template) => apply(groups,tab,template)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:12 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/roots/adminTemplateEdit.scala.html
                  HASH: 69a391b58aa260c0b1cb353d9fee7a87937909c2
                  MATRIX: 663->1|709->41|759->85|811->131|890->204|931->239|960->262|1020->316|1430->387|1583->444|1612->447|1631->457|1671->459|1700->461|1811->546|1830->556|1886->591|1916->594|2278->930|2345->976|2379->983|2544->1121|2619->1175|2654->1184|2678->1199|2763->1263|2796->1269|2886->1329
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|36->10|41->10|43->12|43->12|43->12|44->13|45->14|45->14|45->14|46->15|54->23|54->23|55->24|61->30|61->30|62->31|62->31|62->31|63->32|68->37
                  -- GENERATED --
              */
          