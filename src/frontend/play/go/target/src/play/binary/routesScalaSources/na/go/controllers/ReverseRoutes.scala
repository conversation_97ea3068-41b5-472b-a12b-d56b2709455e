// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/conf/go.routes
// @DATE:Tue Jul 01 11:21:12 WEST 2025

import play.api.mvc.Call


import _root_.controllers.Assets.Asset

// @LINE:2
package na.go.controllers {

  // @LINE:14
  class ReverseAssets(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:14
    def versioned(file:Asset): Call = {
      implicit lazy val _rrc = new play.core.routing.ReverseRouteContext(Map(("path", "/public"))); _rrc
      Call("GET", _prefix + { _defaultPrefix } + "go/assets/" + implicitly[play.api.mvc.PathBindable[Asset]].unbind("file", file))
    }
  
  }

  // @LINE:52
  class ReverseAdminOperation(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:57
    def updateTemplate(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "go/admin/update")
    }
  
    // @LINE:59
    def createTemplate(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "go/admin/create")
    }
  
    // @LINE:55
    def addTemplatePage(templateId:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/admin/add/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("templateId", templateId)))
    }
  
    // @LINE:58
    def createTemplatePage(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/admin/create")
    }
  
    // @LINE:60
    def getCharacteristicsTab(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/admin/create/tab/characteristics")
    }
  
    // @LINE:61
    def getOperationTab(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/admin/create/tab/operation")
    }
  
    // @LINE:53
    def changeTemplateState(templateId:Long, active:Boolean, family:String, rfs:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "go/admin/changeTemplateState" + play.core.routing.queryString(List(Some(implicitly[play.api.mvc.QueryStringBindable[Long]].unbind("templateId", templateId)), Some(implicitly[play.api.mvc.QueryStringBindable[Boolean]].unbind("active", active)), Some(implicitly[play.api.mvc.QueryStringBindable[String]].unbind("family", family)), Some(implicitly[play.api.mvc.QueryStringBindable[String]].unbind("rfs", rfs)))))
    }
  
    // @LINE:56
    def editTemplatePage(templateId:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/admin/edit/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("templateId", templateId)))
    }
  
    // @LINE:52
    def index(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/admin")
    }
  
    // @LINE:54
    def deleteTemplate(templateId:Long): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "go/admin/deleteTemplate" + play.core.routing.queryString(List(Some(implicitly[play.api.mvc.QueryStringBindable[Long]].unbind("templateId", templateId)))))
    }
  
  }

  // @LINE:37
  class ReverseOperation(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:39
    def cloneOperationPage(id:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/create/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("id", id)))
    }
  
    // @LINE:40
    def getCharacteristicsTab(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/create/tab/characteristics")
    }
  
    // @LINE:41
    def getOperationTab(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/create/tab/operation")
    }
  
    // @LINE:38
    def createOperation(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "go/create")
    }
  
    // @LINE:42
    def uploadFirmwareFile(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "go/upload/firmwarefile")
    }
  
    // @LINE:37
    def createOperationPage(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/create")
    }
  
  }

  // @LINE:28
  class ReverseModals(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:29
    def getTemplateSaveFilterModal(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/template/savefiltermodal")
    }
  
    // @LINE:30
    def saveFilter(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "go/savefilter")
    }
  
    // @LINE:33
    def getDeleteFilterModal(filterName:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/deletefiltermodal/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("filterName", filterName)))
    }
  
    // @LINE:32
    def editFilter(filterName:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "go/editfilter/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("filterName", filterName)))
    }
  
    // @LINE:28
    def getOperationSaveFilterModal(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/operation/savefiltermodal")
    }
  
    // @LINE:34
    def deleteFilter(filterName:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "go/deletefilter/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("filterName", filterName)))
    }
  
    // @LINE:31
    def getEditFilterModal(filterName:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/editfiltermodal/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("filterName", filterName)))
    }
  
  }

  // @LINE:2
  class ReverseApplication(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:2
    def index(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/home")
    }
  
    // @LINE:11
    def getGOModuleAngularScripts(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/imports/jsscripts")
    }
  
    // @LINE:8
    def javascriptRoutes(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/assets/javascripts/routes")
    }
  
    // @LINE:5
    def resume(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/resume")
    }
  
  }

  // @LINE:64
  class ReverseTableConfigs(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:65
    def templatesSearch(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/table-configs/templates-seach-table")
    }
  
    // @LINE:66
    def lrResponse(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/table-configs/lr-response")
    }
  
    // @LINE:64
    def operationsSearch(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/table-configs/operations-seach-table")
    }
  
  }

  // @LINE:17
  class ReverseSearch(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:18
    def getFieldsForFiltername(filterName:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/getFieldsForFiltername/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("filterName", filterName)))
    }
  
    // @LINE:17
    def getFilterNamesForSelect(context:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/selectfilternames/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)))
    }
  
  }

  // @LINE:21
  class ReverseOperationDetails(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:21
    def getOperationDetails(id:String, context:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/operationdetails/view/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("id", id)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)))
    }
  
  }

  // @LINE:24
  class ReverseOperationsTable(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:24
    def getTableDataforOperations(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "go/operationtabledata")
    }
  
    // @LINE:25
    def getAdminTableDataforOperations(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "go/admin/operationtabledata")
    }
  
  }

  // @LINE:46
  class ReverseCatalog(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:48
    def getOperationTemplatesOfRfs(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/catalog/operationTemplates")
    }
  
    // @LINE:47
    def getDistinctRfsOfFamily(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/catalog/distinctRfsNames")
    }
  
    // @LINE:46
    def getRfsOfFamily(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/catalog/rfsNames")
    }
  
    // @LINE:49
    def getDistinctOperationTemplates(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "go/catalog/distinctOperationTemplates")
    }
  
  }


}
