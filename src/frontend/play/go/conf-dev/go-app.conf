na.go.backend.address = "naportal-ci.c.ptin.corppt.com"
na.go.backend.port = "8280"
na.portal.base.naem.ip.address = "naportal-ci.c.ptin.corppt.com"
na.portal.base.naem.address.port = "8180"

#history search delta
go.delta=10
#GO export limit
go.export.limit=5000
#filters top value
go.filters.top=20
#GO DATABASE FIELDS
#THE FIELDS OF INTEREST ARE ONLY THE ONES USED ON THE OPERATION SEARCH TABLE
# Left side = Datatable Column ID -> maps to mData property, and this property maps to the object field

# old 
#go.database.operation.fields =
#  {
#    "insertionDate" = "PARTITION_KEY", #INSERT_DATE
#    "sInsertionDate" = "PARTITION_KEY", #INSERT_DATE
#    "orderKey" = "ORDER_KEY",
#    "family" = "FAMILY",
#    "operation" = "OPERATION",
#    "rfs" = "RFS_ID",
#    "state" = "STATE",
#    "errorMessage" = "ERROR_MSGgo.database.operation.fields"
#  }

go.operation.base.upload.filesDir = "/tmp/firmwareFilesDir/"

go.mapping.for.operations.with.upload = { "createTemplateWithNewSoftwareVersion":
 	{
 	manufacurer = "manufacturer"
 	model = "modelName"
 	newVersion = "newVersion"
 	firmwareFilename = "firmwareFilename"
	}
 }

go.database.operation.fields =
  {
    "startDate" = "datetime", #INSERT_DATE
    "sInsertionDate" = "datetime", #INSERT_DATE
    "endDate" = "datetime", #INSERT_DATE
    "orderKey" = "orderKey",
    "username" = "username",
    "family" = "family",
    "operationName" = "operationName",
    "rfs" = "rfs",
    "executionState" = "executionState",
    "errorMessage" = "ERROR_MSG",
    "filterName" = "filterName"
  }

go.database.template.fields =
  {
    "startDate" = "lastModified", #INSERT_DATE
    "sInsertionDate" = "lastModified", #INSERT_DATE
    "endDate" = "lastModified", #INSERT_DATE
    "orderKey" = "orderKey",
    "family" = "familyName",
    "operationName" = "templateName",
    "rfs" = "rfsName",
    "templateState" = "active",
    "errorMessage" = "ERROR_MSG",
    "filterName" = "filterName"
  }

na.go.tables = { include "../conf/tables.conf" }