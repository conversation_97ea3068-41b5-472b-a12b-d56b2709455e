go operations search{
  route = "na.go.controllers.routes.OperationsTable.getTableDataforOperations"
  columns = [
    {
      name: sInsertionDate-col,
      title: na.portal.go.datatables.column.label.insertionDate,
      dataProperty: sInsertionDate,
      sortable: true,
      initialSortDirection: descendant,
      customRenderCallName: na.datatableTemplates.basemodule.datetimeSeconds
    }, {
      name: orderKey-col,
      title: na.portal.go.datatables.column.label.orderKey
      dataProperty: orderKey,
      sortable: true,
    }, {
      name: username-col,
      title: na.portal.go.datatables.column.label.username
      dataProperty: username,
      sortable: true,
    }, {
      name: family-col,
      title: na.portal.go.datatables.column.label.family
      dataProperty: family,
      sortable: true,
    }, {
      name: operation-col,
      title: na.portal.go.datatables.column.label.operation
      dataProperty: operationName,
      sortable: true,
    }, {
      name: rfs-col,
      title: na.portal.go.datatables.column.label.rfs
      dataProperty: rfs,
      sortable: true,
    }, {
      name: state-col,
      title: na.portal.go.datatables.column.label.state
      dataProperty: executionState,
      sortable: true,
      customRenderCallName: na.datatableTemplates.go.operationSearch.state
    }, {
      name: actions-col,
      title: na.portal.go.datatables.column.label.actions,
      sortable: false,
      customRenderCallName: na.datatableTemplates.go.operationSearch.actionRenderer,
      visibilityToggle: false
      columnWidth: 115px
    }
  ]
}


go templates search {
  route = "na.go.controllers.routes.OperationsTable.getAdminTableDataforOperations"
  columns = [
    {
      name: lastModified-col,
      title: na.portal.go.datatables.column.label.lastModification,
      dataProperty: sInsertionDate,
      sortable: true,
      initialSortDirection: descendant,
      customRenderCallName: na.datatableTemplates.basemodule.datetimeSeconds
    }, {
      name: username-col,
      title: na.portal.go.datatables.column.label.username
      dataProperty: username,
      sortable: true,
    }, {
      name: family-col,
      title: na.portal.go.datatables.column.label.family
      dataProperty: familyName,
      sortable: true,
    }, {
      name: operation-col,
      title: na.portal.go.datatables.column.label.operation
      dataProperty: operationName,
      sortable: true,
      customRenderCallName: na.datatableTemplates.go.templateSearch.operationName,
    }, {
      name: rfs-col,
      title: na.portal.go.datatables.column.label.rfs
      dataProperty: rfsName,
      sortable: true,
    }, {
      name: state-col,
      title: na.portal.go.datatables.column.label.stateAction
      sortable: true,
      customRenderCallName: na.datatableTemplates.go.templateSearch.state,
    }, {
      name: actions-col,
      title: na.portal.go.datatables.column.label.actions,
      sortable: false,
      customRenderCallName: na.datatableTemplates.go.templateSearch.actionRenderer,
      visibilityToggle: false
      columnWidth: 115px
    }
  ]
}

go lr response {
  columns = [
    {
      name: lrName-group,
      title: na.portal.go.operationName
      dataProperty: operationName,
      visible: false
    }, {
      name: status-col
      title: na.portal.go.operation.details.label.status
      customRenderCallName = na.datatableTemplates.go.operationDetails.columnStateRenderer
    }, {
      name: lr-name-col
      title: na.portal.go.operation.details.label.lrname
      dataProperty: lrName
    }, {
      name: id-resource-col
      title: na.portal.go.operation.details.label.lrid
      dataProperty: lrId
    }, {
      name: response-code-col
      title: na.portal.go.operation.details.label.responsecode
      customRenderCallName: na.datatableTemplates.go.operationDetails.columnResponseCodeRenderer
    }, {
      name: response-value-col
      title: na.portal.go.operation.details.label.responsevalue
      customRenderCallName: na.datatableTemplates.go.operationDetails.columnResponseValueRenderer
    }, {
      name: message-col
      title: na.portal.go.operation.details.label.message
      dataProperty: message
    }, {
      name: date-time-col
      title: na.portal.go.operation.details.label.descriptiontime
      customRenderCallName: na.datatableTemplates.go.operationDetails.columnDateHourRenderer
    }, {
      name: actions
      title: na.portal.go.datatables.column.label.actions
      sortable: false
      customRenderCallName: na.datatableTemplates.go.operationDetails.operationDetailsActionsRenderer
    }
  ]
  pagination = {
    pageLength: 5
    autoWidth: false
  }
  rowGroup = {
    groupByColumn: "lrName-group"
  }
}