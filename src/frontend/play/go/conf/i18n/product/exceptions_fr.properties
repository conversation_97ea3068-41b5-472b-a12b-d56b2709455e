
go.NA_GO_00001.user.message = Erreur lors de la recherche des familles.
go.NA_GO_00002.user.message = Erreur lors de la recherche de rfs.
go.NA_GO_00003.user.message = Erreur de lecture du mod\u00e8le.
go.NA_GO_00004.user.message = Erreur dans la recherche de mod\u00e8les.
go.NA_GO_00005.user.message = Erreur lors de la recherche d'op\u00e9rations g\u00e9n\u00e9riques.
go.NA_GO_00006.user.message = Erreur lors de la cr\u00e8ation de l'op\u00e9ration g\u00e8n\u00e8rique.
go.NA_GO_00007.user.message = Erreur lors de la suppression du mod\u00e8le.
go.NA_GO_00008.user.message = Erreur lors de la mise \u00e0 jour du mod\u00e8le.
go.NA_GO_00009.user.message = Erreur lors de la cr\u00e8ation du mod\u00e8le.
go.NA_GO_00010.user.message = Il existe d\u00e9j\u00e0 un autre mod\u00e8le du m\u00eame nom.
go.NA_GO_00011.user.message = Le fonctionnement n\u00e9cessite une validation. Le mod\u00e8le du p\u00e8re a \u00e9t\u00e9 mis \u00e0 jour.

# File Errors
go.NA_GO_00020.user.message = Erreur lors du t\u00e9l\u00e9chargement du fichier du micrologiciel