package na.go.settings;

import na.naportalbase.models.security.ResourcePermission;

/**
 * Created by omar on 09-10-2015.
 */
public class AAAPIResources {

    public final static String GenericOperations = "genericOperations";
    public final static String GenericOperationsAdmin = "genericOperationsAdmin";

    public static ResourcePermission genericOperations() {
        return new ResourcePermission(GenericOperations);
    }

    public static ResourcePermission genericOperationsAdmin() {
        return new ResourcePermission(GenericOperationsAdmin);
    }

    public static ResourcePermission resourceByContext(String context) {
        ResourcePermission resource = null;
        switch (context) {
            case GoConstants.TEMPLATE_SEARCH_CONTEXT : {
                resource = genericOperationsAdmin();
                break;
            }
            case GoConstants.OPERATION_SEARCH_CONTEXT : {
                resource = genericOperations();
                break;
            }
        }

        return resource;
    }


}
