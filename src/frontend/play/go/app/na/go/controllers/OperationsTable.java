package na.go.controllers;

import na.go.models.datatables.operations.ExtendedOperation;
import na.go.models.datatables.template.ExtendedTemplate;
import na.go.models.general.TemplateStates;
import na.go.settings.AAAPIResources;
import na.go.settings.GoConstants;
import na.go.utils.GOBackendRestAPI;
import na.go.utils.GoSecurityUtils;
import na.go.utils.RestAPIUrlBuilder;
import na.naportalbase.models.security.ResourcePermission;
import na.naportalbase.models.tables.query.TableQuery;
import na.naportalbase.models.tables.query.TableQuerySort;
import na.naportalbase.models.tables.response.TableResponse;
import na.naportalbase.settings.AAAPIAccess;
import na.naportalbase.utils.DatatablesUtils;
import na.naportalbase.utils.ODataBuilder;
import na.naportalbase.utils.ODataQueryParametersBuilder;
import na.naportalbase.utils.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import play.Logger;
import play.libs.Json;
import play.libs.concurrent.HttpExecutionContext;
import play.mvc.Controller;
import play.mvc.Result;
import pt.alticelabs.nossis.security.actions.Authenticated;
import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Template;
import pt.alticelabs.nossis.security.actions.Authorized;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

import static na.go.settings.GoConstants.FORM_DATA_OPERATIONS_ENDDATE;
import static na.go.settings.GoConstants.FORM_DATA_OPERATIONS_EXECUTIONSTATE;
import static na.go.settings.GoConstants.FORM_DATA_OPERATIONS_FAMILY;
import static na.go.settings.GoConstants.FORM_DATA_OPERATIONS_OPERATIONNAME;
import static na.go.settings.GoConstants.FORM_DATA_OPERATIONS_OPK;
import static na.go.settings.GoConstants.FORM_DATA_OPERATIONS_RFS;
import static na.go.settings.GoConstants.FORM_DATA_OPERATIONS_STARTDATE;
import static na.go.settings.GoConstants.FORM_DATA_OPERATIONS_TEMPLATESTATE;
import static na.go.settings.GoConstants.FORM_DATA_OPERATIONS_USERNAME;


/**
 * <AUTHOR> Castro <<EMAIL>>
 */
public class OperationsTable extends Controller {

    private final GOBackendRestAPI restAPI;
    private final HttpExecutionContext ec;
    private final RestAPIUrlBuilder restAPIUrlBuilder;
    private final DatatablesUtils datatablesUtils;

    @Inject
    public OperationsTable (GOBackendRestAPI restAPI, HttpExecutionContext ec, RestAPIUrlBuilder restAPIUrlBuilder, DatatablesUtils datatablesUtils) {
        this.restAPI = restAPI;
        this.ec = ec;
        this.restAPIUrlBuilder = restAPIUrlBuilder;
        this.datatablesUtils = datatablesUtils;
    }

    private static final Logger.ALogger goLogger = Logger.of("go");

    @Authenticated
    @Authorized({AAAPIResources.GenericOperations + AAAPIAccess.R})
    public CompletionStage<Result> getTableDataforOperations() {
        TableQuery<Map<String, Object>> query = TableQuery.fromJson(request().body().asJson());
        Map<String, Object> parameters = query.getParameters();

        final List<String> authorizedFamilies = GoSecurityUtils.getAuthorizedFamilies(AAAPIResources.genericOperations().read());
        boolean authorized = !authorizedFamilies.isEmpty();
        if (authorized) {
            if (parameters.containsKey(FORM_DATA_OPERATIONS_FAMILY)) {
                String familyNameFilter = parameters.get(FORM_DATA_OPERATIONS_FAMILY).toString();
                parameters.put(FORM_DATA_OPERATIONS_FAMILY, Collections.singletonList(familyNameFilter));
                if (!authorizedFamilies.contains(familyNameFilter)) {
                    authorized = false;
                    goLogger.debug("No authorization to selected familiy {}", familyNameFilter);
                }
            } else {
                // If no filter, bring it all
                parameters.put(FORM_DATA_OPERATIONS_FAMILY, authorizedFamilies);
            }
        }

        if (!authorized) {
            goLogger.debug("No authorization");
            return CompletableFuture.completedFuture(ok(datatablesUtils.genFormattedDataTableData(new ArrayList<ExtendedOperation>(), 0, 0)));
        }

        ODataQueryParametersBuilder oDataQueryParametersBuilder = ODataQueryParametersBuilder.get()
                .top(query)
                .skip(query)
                .filter(buildODataFilter(parameters, GoConstants.SearchContext.OPERATIONS))
                .orderBy(buildQuerySort(query, GoConstants.SearchContext.OPERATIONS))
                .inlinecount(ODataQueryParametersBuilder.InlineCount.ALL_PAGES);


        return restAPI.searchOperation(oDataQueryParametersBuilder.build()).thenApplyAsync(table -> {
            List<String> authorizedExFamilies = GoSecurityUtils.getAuthorizedFamilies(AAAPIResources.genericOperations().execute());
            List<String> authorizedExRfs = GoSecurityUtils.getAuthorizedRfs(AAAPIResources.genericOperations().execute());

            for (ExtendedOperation operationTableElement : table.getEntries()) {
                operationTableElement.setsInsertionDate(Long.toString(operationTableElement.getDatetime()));
                operationTableElement.setDt_RowId(operationTableElement.getId());
                String orderKey = operationTableElement.getOrderKey();
                if (StringUtils.isEmpty(orderKey)) {
                    operationTableElement.setOrderKey(null); // set orderKey to null if it's not valid
                }

                // User can clone an operation if has authorization to the operation's family and rfs, the catalog
                // template is not deleted and is active.
                String family = operationTableElement.getFamily();
                String rfs = operationTableElement.getRfs();
                if (authorizedExFamilies.contains(family)
                        && authorizedExRfs.contains(rfs)
                        && operationTableElement.getCatalogTemplateActive()
                        && !operationTableElement.getCatalogTemplateDeleted()
                        && !operationTableElement.getCatalogTemplateAttention()) {
                    operationTableElement.setClonePermission(true);
                }
            }

            return ok(Json.toJson(TableResponse.fromQueryAndResponse(query, table.getEntries(), table.getCount())));
        }, ec.current());
    }

    @Authenticated
    @Authorized({AAAPIResources.GenericOperationsAdmin + AAAPIAccess.R})
    public CompletionStage<Result> getAdminTableDataforOperations() {

        TableQuery<Map<String, Object>> query = TableQuery.fromJson(request().body().asJson());
        Map<String, Object> parameters = query.getParameters();


        ResourcePermission resource = AAAPIResources.genericOperationsAdmin().read();

        List<String> authorizedFamilies = GoSecurityUtils.getAuthorizedFamilies(resource);
        boolean authorized = !authorizedFamilies.isEmpty();
        if (authorized) {

            if (parameters.containsKey(FORM_DATA_OPERATIONS_FAMILY)) {
                String familyNameFilter = parameters.get(FORM_DATA_OPERATIONS_FAMILY).toString();
                parameters.put(FORM_DATA_OPERATIONS_FAMILY, Collections.singletonList(familyNameFilter));

                if (!authorizedFamilies.contains(familyNameFilter)) {
                    authorized = false;
                    goLogger.debug("No authorization to selected familiy {}", familyNameFilter);
                }
            } else {
                // If no filter, bring it all
                parameters.put(FORM_DATA_OPERATIONS_FAMILY, authorizedFamilies);
            }
        }

        if (parameters.containsKey(FORM_DATA_OPERATIONS_EXECUTIONSTATE)) {
            String state = parameters.get(FORM_DATA_OPERATIONS_EXECUTIONSTATE).toString();
            parameters.put(FORM_DATA_OPERATIONS_TEMPLATESTATE, Collections.singletonList(TemplateStates.valueOf(state).value()));
            parameters.remove(FORM_DATA_OPERATIONS_EXECUTIONSTATE);
        }

        if (!authorized) {
            goLogger.debug("No authorization");
            return CompletableFuture.completedFuture(ok(datatablesUtils.genFormattedDataTableData(new ArrayList<ExtendedOperation>(), 0, 0)));
        }

        ODataQueryParametersBuilder oDataQueryParametersBuilder = ODataQueryParametersBuilder.get()
                .top(query)
                .skip(query)
                .select(Template.ODATA_SELECTABLE_ELEMENTS.DEFINITION.getOdataSelect())
                .select(Template.ODATA_SELECTABLE_ELEMENTS.FAMILY.getOdataSelect())
                .select(Template.ODATA_SELECTABLE_ELEMENTS.RFS.getOdataSelect())
                .filter(buildODataFilter(parameters, GoConstants.SearchContext.TEMPLATES))
                .orderBy(buildQuerySort(query, GoConstants.SearchContext.TEMPLATES))
                .inlinecount(ODataQueryParametersBuilder.InlineCount.ALL_PAGES);


        return restAPI.searchTemplates(oDataQueryParametersBuilder.build())
                .thenApplyAsync(table -> {
                    ResourcePermission updateResource = AAAPIResources.genericOperationsAdmin().update();
                    ResourcePermission deleteResource = AAAPIResources.genericOperationsAdmin().delete();
                    ResourcePermission createResource = AAAPIResources.genericOperationsAdmin().create();

                    boolean hasUpdatePI = SecurityUtils.isAuthorized(updateResource);
                    boolean hasDeletePI = SecurityUtils.isAuthorized(deleteResource);
                    boolean hasAddPI = SecurityUtils.isAuthorized(createResource);

                    List<String> familiesPermCreate = GoSecurityUtils.getAuthorizedFamilies(createResource);
                    List<String> rfsPermCreate = GoSecurityUtils.getAuthorizedRfs(createResource);
                    List<String> familiesPermUpdate = GoSecurityUtils.getAuthorizedFamilies(updateResource);
                    List<String> rfsPermUpdate = GoSecurityUtils.getAuthorizedRfs(updateResource);
                    List<String> familiesPermDelete = GoSecurityUtils.getAuthorizedFamilies(deleteResource);
                    List<String> rfsPermDelete = GoSecurityUtils.getAuthorizedRfs(deleteResource);

                    for (ExtendedTemplate tableElement : table.getEntries()) {
                        pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Operation definition = tableElement.getDefinition();

                        tableElement.setsInsertionDate(Long.toString(definition.getLastModified().getTime()));
                        tableElement.setDt_RowId(String.valueOf(definition.getId()));
                        tableElement.setActive(definition.getActive());
                        tableElement.setUsername(definition.getUsername());

                        tableElement.setFamilyName(tableElement.getFamily().getName());
                        tableElement.setRfsName(tableElement.getRfs().getName());
                        tableElement.setOperationName(definition.getName());

                        boolean isChild = Boolean.TRUE.equals(definition.getIsChild());
                        boolean hasUpdatePermission = hasUpdatePI &&
                                familiesPermUpdate.contains(tableElement.getFamily().getName()) &&
                                rfsPermUpdate.contains(tableElement.getRfs().getName());

                        boolean hasDeletePermission = hasDeletePI &&
                                familiesPermDelete.contains(tableElement.getFamily().getName()) &&
                                rfsPermDelete.contains(tableElement.getRfs().getName());

                        boolean hasAddPermission = hasAddPI &&
                                familiesPermCreate.contains(tableElement.getFamily().getName()) &&
                                rfsPermCreate.contains(tableElement.getRfs().getName()) &&
                                !tableElement.getDefinition().getAttention();

                        tableElement.setChangeStatePermission(hasUpdatePermission);
                        tableElement.setEditPermission(isChild && hasUpdatePermission);
                        tableElement.setRemovePermission(isChild && hasDeletePermission);
                        tableElement.setAddPermission(hasAddPermission);
                    }

                    return ok(Json.toJson(TableResponse.fromQueryAndResponse(query, table.getEntries(), table.getCount())));
                }, ec.current());
    }


    private String buildODataFilter(Map<String, Object> filterMap, GoConstants.SearchContext context) {
        ODataBuilder odata = new ODataBuilder();
        Map<String, String> fieldMapper = restAPIUrlBuilder.getFieldMapper(context);
        for (String field : filterMap.keySet()) {
            Object value = filterMap.get(field);
            String mappedField = fieldMapper.get(field);

            // Add OData field filters
            switch (field) {
                case FORM_DATA_OPERATIONS_STARTDATE: {
                    odata.addDateField(mappedField, value.toString(), ODataBuilder.FilterOperation.GREATER_EQUAL);
                    break;
                }
                case FORM_DATA_OPERATIONS_ENDDATE: {
                    odata.addDateField(mappedField, value.toString(), ODataBuilder.FilterOperation.LESSER_EQUAL);
                    break;
                }
                case FORM_DATA_OPERATIONS_FAMILY: {
                    odata.addTextField(mappedField, (List<String>) value, ODataBuilder.FilterOperation.IN);
                    break;
                }
                case FORM_DATA_OPERATIONS_EXECUTIONSTATE: {
                    // Devia ser "EQUAL" mas no backend, aparentemente devido ao uso de Enums só funciona com a seguinte marosca:
                    odata.addTextField(mappedField, value.toString(), ODataBuilder.FilterOperation.STARTSWITH);
                    odata.addTextField(mappedField, value.toString(), ODataBuilder.FilterOperation.ENDSWITH);
                    break;
                }
                case FORM_DATA_OPERATIONS_TEMPLATESTATE: {
                    odata.addNumericField(mappedField, Short.parseShort(value.toString()), ODataBuilder.FilterOperation.EQUAL);
                    break;
                }
                case FORM_DATA_OPERATIONS_OPK:
                case FORM_DATA_OPERATIONS_OPERATIONNAME:
                case FORM_DATA_OPERATIONS_RFS: {
                    odata.addTextField(mappedField, value.toString(), ODataBuilder.FilterOperation.EQUAL);
                    break;
                }
                case FORM_DATA_OPERATIONS_USERNAME: {
                    odata.addTextFieldWithWildcard(mappedField, value.toString());
                    break;
                }
            }
        }
        return odata.build();
    }

    private TableQuerySort buildQuerySort(TableQuery<?> query, GoConstants.SearchContext context){
        TableQuerySort newSort = new TableQuerySort();
        Map<String, String> fieldMapper = restAPIUrlBuilder.getFieldMapper(context);
        query.getSorts().forEach((field, value) -> newSort.put(fieldMapper.getOrDefault(field, field), value));
        return newSort;
    }
}
