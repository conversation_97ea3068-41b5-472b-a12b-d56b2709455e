package na.go.models.general;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TemplateField {

    private String templateMapping;
    private String visibleValues;
    private String defaultValue;
    private boolean editable;


    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public String getTemplateMapping() {
        return templateMapping;
    }

    public void setTemplateMapping(String templateMapping) {
        this.templateMapping = templateMapping;
    }

    public String getVisibleValues() {
        return visibleValues;
    }

    public void setVisibleValues(String visibleValues) {
        this.visibleValues = visibleValues;
    }


    public boolean isEditable() {
        return editable;
    }

    public void setEditable(boolean editable) {
        this.editable = editable;
    }

}


