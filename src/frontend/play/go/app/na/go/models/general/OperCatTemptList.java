package na.go.models.general;

import na.go.models.common.BaseEntity;
import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Template;
import java.util.List;


/**
 * Created by hugo-f-bar<PERSON>as on 12/17/14.
 */
public class OperCatTemptList extends BaseEntity<Template> {
    public OperCatTemptList(){}
    public OperCatTemptList(List<Template> entries, int count) {
        super(count,entries,Template.class);
    }
}
