package na.go.models.datatables.operations;

import na.go.models.common.BaseEntity;

import java.util.List;

/**
 * Created by o<PERSON>-a-castro on 31/01/15.
 */
public class OperationWrapper extends BaseEntity<ExtendedOperation> {
    public OperationWrapper() {
    }
    public OperationWrapper(List<ExtendedOperation> entries, int count) {
        super(count, entries, ExtendedOperation.class);
    }
}
