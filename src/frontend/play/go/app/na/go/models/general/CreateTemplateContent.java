package na.go.models.general;

import pt.ptinovacao.na.portal.webui.restful.go.entities.CreateOperationContent;

import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>, 10-02-2016.
 */
public class CreateTemplateContent extends CreateOperationContent {

    private String name;
    private Long id;
    private Long parentId;
    private List<TemplateField> mappings;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CreateTemplateContent {");
        sb.append("name=\"" + name + "\"");
        sb.append(", id=\"" + id + "\"");
        //sb.append(", fields = TemplateField {\"" + id + "\"");
        sb.append("}");

        return sb.toString();
    }


    public List<TemplateField> getMappings() {
        return mappings;
    }

    public void setMappings(List<TemplateField> mappings) {
        this.mappings = mappings;
    }

    public Long getId(){
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }
}
