package na.go.serializers;

import com.fasterxml.jackson.databind.JsonNode;
import pt.ptinovacao.na.portal.web.ui.naem.schemas.ExtendedField;

import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

/**
 * Created by hugo-f-barrigas on 10/27/14.
 */
public class NAEMSerializer {

    /**
     * Create a List<ExtendedField> from a Json with name/value pair of data.
     *
     * @param jsonNode
     * @return
     */
    public static List<ExtendedField> buildExtendedFieldsFromJson(JsonNode jsonNode) {

        return Stream.of("startDate", "endDate", "orderKey", "family", "operationName", "executionState", "rfs")
                .filter(jsonNode::has)
                .map(fieldKey -> {
                    ExtendedField extendedField = new ExtendedField();
                    extendedField.setValue(jsonNode.get(fieldKey).asText());
                    extendedField.setName(fieldKey);
                    return extendedField;
                }).collect(Collectors.toList());
    }
}
