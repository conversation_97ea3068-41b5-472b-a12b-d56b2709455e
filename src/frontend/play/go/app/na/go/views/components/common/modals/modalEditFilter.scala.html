@import scala.collection.mutable
@import scala.collection.immutable
@import na.naportalbase.views.tags.i18n
@import na.naportalbase.views.html.components.inputsComp.modalInputTextComp
@import na.go.views.html.components.adminpage.{ operationForm => adminOperationForm }
@import na.go.views.html.components.home.{ operationForm => operationForm }
@import na.naportalbase.views.html.skeletons.search.horizontalFormSkel

@(filterName : String, groups : na.go.models.general.OperCatFamilyList, operationFormType: String)

<script>
    //https://github.com/select2/select2/issues/1436
    $.fn.modal.Constructor.prototype.enforceFocus = function() {};
</script>

@operationFormTemplate = {
    @if(operationFormType eq "operationForm") {
        @operationForm.render("tempSearchFormItems",immutable.HashMap[String,String](), groups, true)
    } else {
        @adminOperationForm.render("tempSearchFormItems",immutable.HashMap[String,String](), groups, true)
    }
}

<!-- FilterName FIELD -->
@filterNameLabel = @{i18n("na.modals.fields.name.label")}
@filterNamePlaceHolder = @{i18n("na.modals.fields.name.placeholder")}
@filterNameInputAttributes = @{
    var map = mutable.HashMap[String,String]()
    map.put("data-ng-model", "tempSearchFormItems.filterName")
    map.put("data-na-portal-required",null)
    map.put("name", "filterName")
    map.put("id", "filterName")
    map.put("maxlength", "64")
    map.put("disabled", "true")
    map
}

<!-- Filter Start Date -->
@filterOrderStartDateLabel = @{i18n("na.portal.go.field.beginDate.label")}
@datePickerAttributes = @{
    var map = mutable.HashMap[String, String]()
    map.put("id", "filterStartDatePicker")
    map.put("data-date-time-picker", null)
    map.put("data-date-time-picker-input", null)
    map.put("data-max-date","today")
    map
}

@inputAttributes = @{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-model", "tempSearchFormItems.startDate")
    map.put("data-na-portal-required",null)
    map.put("data-datapickerfield", null)
    map.put("data-scope-target","tempSearchFormItems")
    map.put("data-pair-condition","filterEndDatePicker")
    map.put("name", "startDate")
    map.put("id", "startDate")
    map
}

@formAttributes = @{
    var map = mutable.HashMap[String,String]()
    map.put("data-ng-controller","NaPortalGoOperationSaveModalController as rfsModalCtrl")
    map
}

@form = {
    <div id="generic-operation-modal-form">
        <div class="form-group" data-na-portal-field-notification>
            @modalInputTextComp.render(filterNameLabel, "filterName",filterNamePlaceHolder,filterNameInputAttributes)
        </div>

        <div class="fx-expand">
            @i18n("na.modals.filters.options")
        </div>

        <div class="clearfix">
            @operationFormTemplate
        </div>
    </div>
}

@horizontalFormSkel.render(formAttributes,form)
