@import na.naportalbase.settings.BaseMappings
@import scala.collection.mutable
@import scala.collection.immutable
@import com.google.common.base.Splitter
@import com.google.common.collect.Sets
@import na.naportalbase.utils.TemplateUtils
@import na.naportalbase.views.html.components.checkBoxComp.checkBoxComp
@import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.components.CheckList
@import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping
@import na.naportalbase.views.tags.i18n

@(mapping: Mapping)

@checkList = @{mapping.getComponent.asInstanceOf[CheckList]}
@checkListId = @{mapping.getTemplateMapping}
@isRequired = @{checkList.getRequired}

@checkLisAttributes = @{
    var map = mutable.HashMap[String, String](
        "data-ng-model" -> ("model[\""+mapping.getTemplateMapping+"\"]"),
        "data-model" -> mapping.getTemplateMapping,
        "data-na-portal-go-dynamic-form-check-list" -> null,
        "id" -> checkListId,
        "data-component" -> BaseMappings.dualMapper.valueToTree(checkList),
        "name" -> mapping.getTemplateMapping
    )
    if(isRequired){
        map.put("data-na-portal-required",null)
    }
    immutable.HashMap(map.toSeq:_*)
}

@dynamicOptions = @{
    var stringBuilder = new StringBuilder

    var values = Sets.newHashSet(Splitter.on(",").split(checkList.getDefaultValue))

    for(option <- checkList.getOptions){
        if (option.getVisible) {
            val checkboxIndex = String.valueOf(option.getIndex)
            var checkBoxId = checkListId+"_"+option.getValue;
            val optionAttributes = mutable.HashMap[String,String](
                "id" -> checkBoxId,
                "value" -> option.getValue,
                "name" -> mapping.getTemplateMapping,
                "data-component" -> BaseMappings.dualMapper.valueToTree(checkList)
            )
            if(values.contains(checkboxIndex)){
                optionAttributes.put("checked","checked")
            }
            val labelAttributes = mutable.HashMap[String,String](
                "class" -> "control-label"
            )
            stringBuilder.append(checkBoxComp.render(option.getLabel,checkBoxId,optionAttributes,labelAttributes))
        }
    }
    Html(stringBuilder.toString())
}

<div class="form-group">
    <label for="@mapping.getLabel" class="col-sm-2 control-label @if(isRequired){fx-required}" title="@mapping.getDescription">
        @mapping.getLabel@if(isRequired){<abbr title="@i18n("na.basemodule.validations.mandatory")"></abbr>}
    </label>
    <div class="col-sm-10" data-na-portal-field-notification >
        <div @TemplateUtils.dynamicElementsAttributes(checkLisAttributes)>
        @{dynamicOptions}
        </div>
    </div>
</div>