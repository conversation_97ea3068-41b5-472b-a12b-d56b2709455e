@import scala.collection.mutable
@import na.naportalbase.settings.BaseMappings
@import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.components.TextArea
@import na.naportalbase.views.html.components.inputsComp.inputTextAreaComp
@import na.naportalbase.views.tags.i18n

@(mapping: pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping)

@textAreaComponent = @{mapping.getComponent.asInstanceOf[TextArea]}
@inputLabel = @{mapping.getLabel}
@inputId = @{mapping.getTemplateMapping}
@placeHolder = @{mapping.getLabel}
@inputValue = @{ mapping.getComponent.getDefaultValue }
@isRequired = @{textAreaComponent.getRequired}
@inputAttributes = @{
    var map = mutable.HashMap[String, String](
        "data-ng-model" -> ("model[\""+inputId+"\"]"),
        "data-model" -> mapping.getTemplateMapping,
        "data-na-portal-go-dynamic-form-text-area" -> null,
        "name" -> inputId,
        "value" -> "",
        "rows" -> "3",
        "id" -> inputId,
        "data-component" -> BaseMappings.dualMapper.valueToTree(textAreaComponent),
        "class" -> "form-control input-sm pull-left"
    )

    if(textAreaComponent.getRegex){
        map.put("data-na-portal-regex-error",null)
        map.put("regex", textAreaComponent.getRegex)
        map.put("notification-message", textAreaComponent.getRegexErrorMessage)
    }
    if(textAreaComponent.getMaxSize != null){
        map.put("maxlength", String.valueOf(textAreaComponent.getMaxSize))
    }
    if(textAreaComponent.getHeight != null){
        map.put("rows", String.valueOf(textAreaComponent.getHeight))
    }
    if(textAreaComponent.getDefaultValue != null){
        map.put("value", textAreaComponent.getDefaultValue)
    }
    if(isRequired){
        map.put("data-na-portal-required", null)
    }
    if(!textAreaComponent.getEditable){
        map.put("disabled", null)
    }

    map
}

<div class="form-group">
    <label for="@inputId" class="col-sm-2 control-label @if(isRequired){fx-required}" title="@mapping.getDescription">
        @inputLabel@if(isRequired){<abbr title="@i18n("na.basemodule.validations.mandatory")"></abbr>}
    </label>
    <div class="col-sm-10" data-na-portal-field-notification data-na-portal-button-notification>
    @inputTextAreaComp(inputId,inputAttributes,inputValue)
    </div>
</div>