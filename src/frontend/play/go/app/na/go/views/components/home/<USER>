@import scala.collection.mutable
@import scala.collection.immutable
@import na.naportalbase.views.tags.i18n
@import na.go.settings.GoConstants
@import na.go.controllers.routes.Search.getFilterNamesForSelect
@import na.naportalbase.views.html.components.selectsComp.selectInputComp
@import na.naportalbase.views.html.components.buttonsComp.caretDropdown
@import na.go.views.html.components.home.operationForm
@import na.naportalbase.views.html.components.buttonsComp.leftSideFormInputButtonsComp

@(groups : na.go.models.general.OperCatFamilyList)

@caretTitle = @{i18n("na.buttons.caret.moreoptions")}
@caretOptions = @{
    var map = mutable.LinkedHashMap[String,mutable.HashMap[String,mutable.HashMap[String,String]]]()

    var firstOptionTitle = i18n("na.modal.filters.title.editfilter")
    var secondOptionTitle = i18n("na.modal.filters.title.deletefilter")

    var firstOptionComponents = mutable.HashMap[String,mutable.HashMap[String,String]]()

    var firstOptionAattributes = mutable.HashMap[String,String]()
    firstOptionAattributes.put("id","editFilterButton")
    firstOptionAattributes.put("data-ng-click","operCtrl.openEditFilterModal()")

    var firstOptionIattributes = mutable.HashMap[String,String]()
    firstOptionIattributes.put("class","glyphicon glyphicon-pencil")

    firstOptionComponents.put("a",firstOptionAattributes)
    firstOptionComponents.put("i",firstOptionIattributes)

    var secondOptionComponents = mutable.HashMap[String,mutable.HashMap[String,String]]()

    var secondOptionAattributes = mutable.HashMap[String,String]()
    secondOptionAattributes.put("id","deleteFilterButton")
    secondOptionAattributes.put("data-ng-click","operCtrl.openDeleteFilterModal()")

    var secondOptionIattributes = mutable.HashMap[String,String]()
    secondOptionIattributes.put("class","glyphicon glyphicon-remove")

    secondOptionComponents.put("a",secondOptionAattributes)
    secondOptionComponents.put("i",secondOptionIattributes)

    map.put(firstOptionTitle,firstOptionComponents)
    map.put(secondOptionTitle,secondOptionComponents)

    map
}
@selectAttributes = @{
    var map = mutable.HashMap[String, String]()
    map.put("class","input-sm fx-filter-input select2-offscreen")
    map.put("data-ng-model", "selectedFilter")
    map.put("name", "filters")
    map.put("data-na-portal-select-box",null)
    map.put("value", "")
    map.put("id", "rfsFilters")
    map.put("data-is-dynamic",null)
    map.put("data-context","filters")
    map.put("data-url",""+getFilterNamesForSelect(GoConstants.OPERATION_SEARCH_CONTEXT))
    map
}

@form = @{
        operationForm.render("searchFormItems", immutable.HashMap[String,String](), groups, false)
}

@searchButtonAttributes = @{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-click", "operCtrl.submitSearchForm()")
    map.put("id", "searchButton")
    map.put("type","submit")
    map.put("value",i18n("na.buttons.search"))

    map
}

@clearButtonAttributes = @{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-click", "operCtrl.clearSearchFormItems()")
    map.put("id", "clearSearchButton")
    map.put("type","reset")
    map.put("value",i18n("na.buttons.clear"))

    map
}

<header class="fx-sidebar-header search-sidebar__header">
    <div class="fx-sidebar-header-inner">
        <p>
            <span class="fx-entity-header-icon">
                <i class="fa fa-search"></i>
            </span>
            <x-i18n key="na.general.filters"></x-i18n>
        </p>
    </div>
    <div class="form-group search-form--filter-select">
        <div class="search-form__selectbox">
            @selectInputComp.render(selectAttributes)
        </div>
        @caretDropdown.render(caretTitle,caretOptions)
    </div>
</header>
<div class="fx-sidebar-content-wrapper search-sidebar__field-list">
    <x-shadow-scroll vertical>
    @form
    </x-shadow-scroll>
</div>
<div class="form-group search-sidebar__button-list">
    <div class="fx-splitter-sidebar-buttons">
        <div class="fx-splitter-sidebar-buttons-inner">
        @leftSideFormInputButtonsComp.render(searchButtonAttributes,clearButtonAttributes)
        </div>
    </div>
</div>
