@import pt.ptinovacao.na.portal.webui.restful.commons.model.ElementTypes
@import na.go.views.html.components.dynamicForm.fields.textField
@import na.go.views.html.components.dynamicForm.fields.fileField
@import na.go.views.html.components.dynamicForm.fields.textArea
@import na.go.views.html.components.dynamicForm.fields.checkList
@import na.go.views.html.components.dynamicForm.fields.radioButtons
@import na.go.views.html.components.dynamicForm.fields.selectBox

@(mappings: List[pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping])

@for(mapping <- mappings) {

    @ElementTypes.getElementTypeByClass(mapping.getComponent.getClass.getName) match {
        case ElementTypes.TEXT_FIELD => {
            @textField.render(mapping)
        }
        case ElementTypes.FILE_FIELD => {
            @fileField.render(mapping)
        }
        case ElementTypes.TEXT_AREA => {
            @textArea.render(mapping)
        }
        case ElementTypes.CHECK_LIST => {
            @checkList.render(mapping)
        }
        case ElementTypes.RADIO_BUTTON => {
            @radioButtons.render(mapping)
        }
        case ElementTypes.SELECT_BOX => {
            @selectBox.render(mapping)
        }
    }

}