@import na.naportalbase.views.tags.i18n
@import play.api.libs.json._
@import na.go.views.html.skeletons.mainSkel
@import na.go.settings.AAAPIResources
@import na.naportalbase.utils.SecurityUtils
@import na.go.settings.GoConstants.NavContext
@import na.go.views.html.components.common.lateralNav
@import na.go.views.html.components.operationdetails.operationDetailsCharacteristicsTab

@(orderKey: String,operation: String,rfs: String,jsonDetails: String,tab:String)

@parsedJsonDetails = @{
	val json = Json.parse(jsonDetails)
	json
}

@isAdmin = @{SecurityUtils.isAuthorized(AAAPIResources.genericOperationsAdmin().R())}

@mainSkel() {
<div class="layout--page-with-navbar">

	@if(isAdmin){
		@lateralNav.render(NavContext.OPERATIONS)
	}
	<div class="page__content--operation-details-view fx-main-content-wrapper fx-with-main-nav" data-order-key="@orderKey" data-na-portal-go-operation-details-breadcrumb>

		<div class="fx-entity-header">
			<div class="fx-entity-header-info">
				<span class="fx-entity-header-icon">
					<i class="fuxicons fuxicons-go"></i>
				</span>
				<div class="fx-entity-header-title">
					<h1>
						@orderKey
					</h1>
					<p class="fx-entity-header-details">
						<span class="fx-entity-header-details--label">@i18n("na.portal.go.operation.details.label.operation"):&nbsp</span>
						<span is="x-dynamic-span" data-truncated-at="middle">@operation</span>
						<span class="fx-entity-header-details--label">&nbsp/&nbsp@i18n("na.portal.go.operation.details.label.rfs"):&nbsp</span>
						<span is="x-dynamic-span" data-truncated-at="middle">@rfs</span>
					</p>
				</div>
			</div>
		</div>

		<div class="container--entity-content">
			<x-tab-container>
				<x-tab label="@i18n("na.portal.go.operation.create.tab.characteristics.label")">
					<x-shadow-scroll flex>
						<div class="tab-pane fx-form-vertical active" data-formatted-details-json="@parsedJsonDetails" data-na-portal-go-details-context data-context="operation">
							@operationDetailsCharacteristicsTab.render()
						</div>
					</x-shadow-scroll>
				</x-tab>
			</x-tab-container>
		</div>
	</div>
</div>
}




