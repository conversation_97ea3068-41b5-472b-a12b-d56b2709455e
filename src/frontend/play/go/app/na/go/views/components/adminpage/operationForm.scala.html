@import scala.collection.immutable
@import na.naportalbase.views.tags.i18n
@import na.go.models.general.{TemplateStates, OperCatFamilyList}
@import na.naportalbase.views.html.components.selectsComp.dynamicSelectOptionComp
@import na.naportalbase.views.html.components.selectsComp.selectComp
@import scala.collection.mutable
@import na.go.views.html.components.common.saveFilterButton

@(ngModelVariable: String, inputFieldIds: immutable.HashMap[String, String], groups : OperCatFamilyList, isModal: Boolean)

@prefixId = @{if(isModal) "modal-" else ""}

@selectAttributes(attributesMap: immutable.HashMap[String, String]) = @{
    attributesMap + ("style" -> "width: 100%")
}

<!-- START DATE CONFIG -->
@operationStartTimepickerInputId = @{inputFieldIds.getOrElse("startDate","startDate")}
@orderStartDateLabel = @{i18n("na.portal.go.field.modificationBeginDate.label")}

@datePickerAttributes = @{
    var map = mutable.HashMap[String, String]()
    map.put("data-date-time-picker", null)
    map.put("data-date-time-picker-input", null)
    map.put("data-max-date","today")

    map
}


@inputAttributes = @{
    var map = mutable.HashMap[String, String]()
    map.put("data-entity", operationStartTimepickerInputId)
    map.put("data-ng-model", ngModelVariable+"."+operationStartTimepickerInputId)
    map.put("data-datapickerfield", null)
    map.put("data-scope-target",ngModelVariable)
    map.put("name", operationStartTimepickerInputId)
    map.put("id", prefixId+operationStartTimepickerInputId)

    map
}




<!-- END DATE CONFIG -->
@orderEndTimepickerInputId = @{inputFieldIds.getOrElse("endDate","endDate")}
@orderEndDateLabel = @{i18n("na.portal.go.field.modificationEndDate.label")}
@endDatePickerAttributes = @{
    var map = mutable.HashMap[String, String]()
    map.put("data-date-time-picker", null)
    map.put("data-date-time-picker-input", null)
    map.put("data-max-date","today")

    map
}
@endDateinputAttributes = @{
    var map = mutable.HashMap[String, String]()
    map.put("data-entity", orderEndTimepickerInputId)
    map.put("data-ng-model", ngModelVariable+"."+orderEndTimepickerInputId)
    map.put("data-datapickerfield", null)
    map.put("data-scope-target",ngModelVariable)
    map.put("name", orderEndTimepickerInputId)
    map.put("id", prefixId+orderEndTimepickerInputId)

    map
}

<!-- FAMILY FIELD CONFIG -->
@operationFamilyId =               @{inputFieldIds.getOrElse("family-field","family-field")}
@operationFamilyLabel =            @{i18n("na.portal.go.family.field.label")}
@operationFamilySelectAttributes = @{
        var map = immutable.HashMap[String, String](
            "data-ng-model" -> (ngModelVariable+".family"),
            "name" -> operationFamilyId,
            "data-na-portal-select-box" -> null,
            "data-na-portal-select-box-auto-dropdown-size" -> null,
            "placeholder" -> i18n("na.portal.go.family.selects.empty.option"),
            "value" -> "",
            "id" -> (prefixId+operationFamilyId),
            "class" -> "form-control input-sm",
            "data-context" -> "family",
            "allow-clear" -> null
        )
        map
    }

@operationFamilySelectData = @{
    var stringBuilder = new StringBuilder

   for(group <- groups.getEntries()){
      var optionAttributes = immutable.HashMap[String,String](
          "id" -> group.getName,
          "text" -> group.getName,
          "value" -> group.getName
      )
       stringBuilder.append(dynamicSelectOptionComp.render(group.getName,optionAttributes))

   }

    Html(stringBuilder.toString())
}


<!-- RFS FIELD CONFIG -->  
@operationRfsId = @{inputFieldIds.getOrElse("selectRfsNames","selectRfsNames")}
@operationRfsSelectAttributes = @{
    var map = immutable.HashMap[String, String](
        "data-ng-model" -> (ngModelVariable+".rfs"),
        "style" -> "100%",
        "name" -> operationRfsId,
        "data-na-portal-select-box" -> null,
        "data-na-portal-select-box-auto-dropdown-size" -> null,
        "id" -> (prefixId+operationRfsId),
        "placeholder" -> i18n("na.portal.go.rfs.selects.empty.option"),
        "value" -> "",
        "data-is-dynamic" -> null,
        "class" -> "form-control input-sm inherit-default-color",
        "data-context" -> "rfs",
        "allow-clear" -> null
        )
        map
    }

<!-- OPERATION FIELD CONFIG -->  
@operationOpId = @{inputFieldIds.getOrElse("operation","operation")}
@operationOpLabel = @{i18n( "na.portal.go.operation.field.label")}
@operationOpSelectAttributes = @{ immutable.HashMap[String, String](
    "data-ng-model" -> (ngModelVariable+".operationName"),
    "name" -> operationOpId,
    "data-na-portal-select-box" -> null,
    "data-na-portal-select-box-auto-dropdown-size" -> null,
    "value" -> "",
    "data-is-dynamic" -> null,
    "placeholder" -> i18n("na.portal.go.operation.selects.empty.option"),
    "id" -> (prefixId+operationOpId),
    "class" -> "form-control input-sm inherit-default-color",
    "data-context" -> "operation",
    "style" -> "100%",
    "allow-clear" -> null
)}
  
<!-- STATE FIELD CONFIG -->  
@operationStateId = @{inputFieldIds.getOrElse("templateState","templateState")}
@operationStateLabel = @{i18n("na.portal.go.state.field.label")}
@operationStateSelectAttributes = @{ immutable.HashMap[String, String](
            "data-ng-model" -> (ngModelVariable+".templateState"),
            "name" -> operationStateId,
            "data-na-portal-select-box" -> null,
            "placeholder" -> i18n("na.portal.go.state.selects.empty.option"),
            "value" -> "",
            "id" -> (prefixId+operationStateId),
            "class" -> "form-control input-sm",
            "data-context" -> "state",
            "allow-clear" -> null
         )
     }
@operationStateSelectData = @{

    var list = TemplateStates.values()
    var stringBuilder = new StringBuilder
    for(option <- list){
      var optionAttributes = immutable.HashMap[String, String](
        "name" -> option.name(),
        "id" -> option.name(),
        "value" -> option.value()
      )
      var text = i18n("na.portal.go.adminoperation.state."+option.name()+".label")
      stringBuilder.append(dynamicSelectOptionComp.render(text,optionAttributes))
  }
  Html(stringBuilder.toString())
}


@inputDateCompSidebar(label: String, datePickerId: String, dateFormat: String) = {
    <label for="@datePickerId">@label</label>
    <x-date-time-picker data-field="@datePickerId" format="@dateFormat"></x-date-time-picker>
}

@inputDateCompModal(label: String, datePickerId: String, dateFormat: String) = {
    <label class="control-label col-sm-3" for="@datePickerId">@label</label>
    <div class="col-sm-9">
        <x-date-time-picker data-field="@datePickerId" format="@dateFormat"></x-date-time-picker>
    </div>
}


@if(!isModal) {

<!-- SIDEBAR START DATE FIELD -->  
<div class="form-group" data-na-portal-field-notification data-na-portal-button-notification>
    @inputDateCompSidebar(orderStartDateLabel, operationStartTimepickerInputId, "short")
</div>

<!-- SIDEBAR END DATE FIELD -->  
<div class="form-group" data-na-portal-field-notification data-na-portal-button-notification>
    @inputDateCompSidebar(orderEndDateLabel, orderEndTimepickerInputId, "short")
</div>

<!-- SIDEBAR FAMILY FIELD -->
<div class="form-group">
    <label for="@{operationFamilyId}">@operationFamilyLabel</label><br/>
        @selectComp.render(
            selectAttributes(operationFamilySelectAttributes), null, null, operationFamilySelectData)
</div>


<!-- SIDEBAR RFS FIELD -->  
<div class="form-group">
    <label for="@{operationRfsId}">@i18n("na.portal.go.rfs.field.label")</label>
    <br/>
    @selectComp.render(selectAttributes(operationRfsSelectAttributes), "", null, null)
</div>

<!-- SIDEBAR OPERATION FIELD -->  
<div class="form-group">
    <label for="@{operationOpId}">@{operationOpLabel}</label>
    <br/>
      @selectComp.render(selectAttributes(operationOpSelectAttributes), "", null, null)
</div>


<!-- SIDEBAR STATE FIELD -->  
<div class="form-group">
    <label for="@{operationStateId}">@{operationStateLabel}</label>
    <br/>
    @selectComp.render(
        operationStateSelectAttributes, null, null, operationStateSelectData)
</div>

<!-- SAVE FILTER BUTTON -->
<div class="form-group search-form--save-filter-button">
    @saveFilterButton.render("templateCtrl.openSaveFilterModal()")
</div>

} else {

<!-- MODAL START DATE FIELD -->  
<div class="form-group" data-na-portal-field-notification data-na-portal-button-notification>
    @inputDateCompModal(orderStartDateLabel, operationStartTimepickerInputId, "short")
</div>

<!-- MODAL END DATE FIELD -->  
<div class="form-group" data-na-portal-field-notification data-na-portal-button-notification>
	@inputDateCompModal(orderEndDateLabel, orderEndTimepickerInputId, "short")
</div>

<!-- MODAL FAMILY FIELD -->
<div class="form-group">
    <label for="@{operationFamilyId}" class="control-label col-sm-3">@{operationFamilyLabel}</label>
    <div class="col-sm-9">
        @selectComp.render(
            selectAttributes(operationFamilySelectAttributes), null, null, operationFamilySelectData)
    </div>
</div>

<!-- MODAL RFS FIELD -->  
<div class="form-group">
    <label for="selectRfsNames" class="control-label col-sm-3">@{i18n("na.portal.go.rfs.field.label")}</label>
    <div class="col-sm-9">
    @selectComp.render(selectAttributes(operationRfsSelectAttributes), null, null, null)
   </div>
</div>

<!-- MODAL OPERATION FIELD -->  
<div class="form-group">
    <label for="@{operationOpId}" class="control-label col-sm-3">@{operationOpLabel}</label>
    <div class="col-sm-9">
      @selectComp.render(selectAttributes(operationOpSelectAttributes), null, null, null)
      </div>
</div>

<!-- MODAL STATE FIELD -->  
<div class="form-group">
    <label for="@{operationStateId}" class="control-label col-sm-3">@{operationStateLabel}</label>
    <div class="col-sm-9">
            @selectComp.render(
                selectAttributes(operationStateSelectAttributes), null, null, operationStateSelectData)
    </div>
</div>

}