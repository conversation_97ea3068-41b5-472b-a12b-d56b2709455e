@import na.naportalbase.views.tags.i18n
@import na.naportalbase.views.tags.wizardStep
@import na.naportalbase.utils.TemplateUtils
@import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Family
@import na.go.models.datatables.operations.ExtendedOperation
@import na.go.views.html.components.operationcreate
@import play.libs.Json

@(groups: List[Family], operationToClone: ExtendedOperation)

@step2Content = {
    <div data-na-portal-wizard-template-loader></div>
}

@step1 = @{
    wizardStep(stepName = "characteristics",
    stepLabel = i18n("na.portal.go.operation.create.tab.characteristics.label")){
        operationcreate.characteristicsTab("createCtrl.characteristics", groups)
    }
}
@step2 = @{
    wizardStep(stepName = "operation",
    stepLabel = i18n("na.portal.go.operation.create.tab.operation.label")){
        step2Content
    }
}

@wizardAttributes = @{
    var map = new java.util.HashMap[String, String]()
    map.put("data-na-portal-go-wizard-context", "operation")
    map.put("data-ng-controller", "NaPortalGoCreateOperationController as createCtrl")
    if(operationToClone != null){
        map.put("data-na-portal-go-wizard-initial-tab", "operation")
        map.put("data-na-portal-go-clone-operation", Json.toJson(operationToClone).toString)
    } else {
        map.put("data-na-portal-go-wizard-initial-tab", "characteristics")
    }
    map
}

<div class="tab-pane active" id="tab-car" @TemplateUtils.dynamicElementsAttributes(wizardAttributes)>
    <x-wizard class="create-operation-wizard">
    @if(operationToClone != null){
        <x-wizard-step class="opened" data-tab-name="@i18n("na.portal.go.operation.create.tab.characteristics.label")">@step1.content</x-wizard-step>
        <x-wizard-step class="opened open" data-tab-name="@i18n("na.portal.go.operation.create.tab.operation.label")" ></x-wizard-step>
    } else {
        <x-wizard-step data-tab-name="@i18n("na.portal.go.operation.create.tab.characteristics.label")">@step1.content</x-wizard-step>
        <x-wizard-step data-tab-name="@i18n("na.portal.go.operation.create.tab.operation.label")"></x-wizard-step>
    }
    </x-wizard>
</div>
