@import na.naportalbase.views.tags.i18n
@import na.go.views.html.skeletons.mainSkel
@import na.go.settings.GoConstants.NavContext
@import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Family
@import na.go.views.html.components.common.lateralNav
@import na.go.views.html.components.admintemplatecreate.operationWizard

@(groups: List[Family], tab: String)

@mainSkel() {
	<div class="layout--page-with-navbar" data-na-portal-go-admin-add-template-breadcrumb>
		@lateralNav.render(NavContext.ADMIN_TEMPLATES)
		<div class="fx-main-content-wrapper go-wizard-page">
			<div class="fx-entity-header" data-template-loader data-context="template">
				<div class="fx-entity-header-info">
					<span class="fx-entity-header-icon">
						<i class="fuxicons fuxicons-go-admin"></i>
					</span>
					<div class="fx-entity-header-title">
						<h1>
						@i18n("na.portal.go.admin.operation.create.title")
						</h1>
					</div>
				</div>
			</div>
			<div class="fx-entity-info" data-na-portal-go-common>
				<x-tab-container>
					<x-tab label="@i18n("na.portal.go.operation.details.characteristics")">
					@operationWizard.render(groups, null, "create-template")
					</x-tab>
				</x-tab-container>
			</div>
		</div>
	</div>
}