
package na.mpt.views.html.components.bulkdetails

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.mpt.views.html.components.results.resultsTabForm
/*2.2*/import pt.ptinovacao.na.portal.webui.restful.mpt.commons.model.OperationHeadersListEntry

object resultsTab extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[OperationHeadersListEntry,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*4.2*/(headers : OperationHeadersListEntry):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*6.2*/leftSide/*6.10*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*6.14*/("""
    """),_display_(/*7.6*/resultsTabForm/*7.20*/.render(headers)),format.raw/*7.36*/("""
""")))};def /*10.2*/rightSide/*10.11*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*10.15*/("""
    """),format.raw/*11.5*/("""<div id="fx-splitter-content" class="fx-splitter-content fx-full-height">
        <div class="fx-splitter-content-inner" style="overflow-y: auto;">
            <div id="entity-content" data-ng-controller="NaPortalMptBulkDetailsResultsDatatableController">
                <div data-na-portal-datatables-toolbar class="fx-bulk-actions fx-table-actions clearfix"></div>
                <div data-na-portal-toolbar-advanced-search data-table-id="mptResultsDatatable"></div>
                <div id="loadingWrapper_mptResultsDatatable">
                    <div data-ng-controller="NaPortalDatatablesController">
                        <table class="table table-striped table-hover"
                        data-na-portal-datatable
                        data-dt-options="dtOptions"
                        data-dt-columns="dtColumns"
                        dt-disable-deep-watchers="true"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
""")))};
Seq[Any](format.raw/*4.39*/("""

"""),format.raw/*8.2*/("""

"""),format.raw/*28.2*/("""

"""),format.raw/*30.1*/("""<x-splitter class="splitter splitter--mpt-admin-search">
    <form class="search-sidebar" slot="left" data-na-portal-mpt-bulks-search-page data-ng-controller="NaPortalMptBulkDetailsResultsSearchController as resultsCtrl">
    """),_display_(/*32.6*/leftSide),format.raw/*32.14*/("""
    """),format.raw/*33.5*/("""</form>
    <div class="fx-splitter-content-inner" slot="right">
    """),_display_(/*35.6*/rightSide),format.raw/*35.15*/("""
    """),format.raw/*36.5*/("""</div>
    <div slot="collapsed-left">
        <div class="fx-info-sidebar-collapsed left">
            <div class="fx-sidebar-header search-sidebar__header">
                <span class="fx-entity-header-icon">
                    <i class="fa fa-search"></i>
                </span>
            </div>
        </div>
    </div>
</x-splitter>
"""))
      }
    }
  }

  def render(headers:OperationHeadersListEntry): play.twirl.api.HtmlFormat.Appendable = apply(headers)

  def f:((OperationHeadersListEntry) => play.twirl.api.HtmlFormat.Appendable) = (headers) => apply(headers)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 19:41:21 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/components/bulkdetails/resultsTab.scala.html
                  HASH: ae98e98d767a6655069106ec2d5d6ce5c7efb5b4
                  MATRIX: 681->1|747->61|1164->152|1279->192|1295->200|1375->204|1406->210|1428->224|1464->240|1489->245|1507->254|1588->258|1620->263|2653->189|2681->242|2710->1257|2739->1259|2992->1486|3021->1494|3053->1499|3149->1569|3179->1578|3211->1583
                  LINES: 24->1|25->2|30->4|34->6|34->6|36->6|37->7|37->7|37->7|38->10|38->10|40->10|41->11|59->4|61->8|63->28|65->30|67->32|67->32|68->33|70->35|70->35|71->36
                  -- GENERATED --
              */
          