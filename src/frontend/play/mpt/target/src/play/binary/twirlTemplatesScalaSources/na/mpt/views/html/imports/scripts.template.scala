
package na.mpt.views.html.imports

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object scripts extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.1*/("""<script type="text/javascript" src=""""),_display_(/*1.38*/na/*1.40*/.mpt.controllers.routes.Application.javascriptRoutes),format.raw/*1.92*/(""""></script>
<script src='"""),_display_(/*2.15*/na/*2.17*/.mpt.controllers.routes.Assets.versioned("basemodule/runtime.js")),format.raw/*2.82*/("""'></script>
<script src='"""),_display_(/*3.15*/na/*3.17*/.mpt.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-fuxi.js")),format.raw/*3.101*/("""'></script>
<script src='"""),_display_(/*4.15*/na/*4.17*/.mpt.controllers.routes.Assets.versioned("na-portal-assets/base-styles.js")),format.raw/*4.92*/("""'></script>
<script src='"""),_display_(/*5.15*/na/*5.17*/.mpt.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-bootstrap.js")),format.raw/*5.106*/("""'></script>
<script src='"""),_display_(/*6.15*/na/*6.17*/.mpt.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-selects.js")),format.raw/*6.104*/("""'></script>
<script src='"""),_display_(/*7.15*/na/*7.17*/.mpt.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-std-utils.js")),format.raw/*7.106*/("""'></script>
<script src='"""),_display_(/*8.15*/na/*8.17*/.mpt.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-tables.js")),format.raw/*8.103*/("""'></script>
<script src='"""),_display_(/*9.15*/na/*9.17*/.mpt.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-jquery.js")),format.raw/*9.103*/("""'></script>
<script src='"""),_display_(/*10.15*/na/*10.17*/.mpt.controllers.routes.Assets.versioned("na-portal-assets-vendors/common.js")),format.raw/*10.95*/("""'></script>
<script src='"""),_display_(/*11.15*/na/*11.17*/.mpt.controllers.routes.Assets.versioned("utils/utils.js")),format.raw/*11.75*/("""'></script>
<script src='"""),_display_(/*12.15*/na/*12.17*/.mpt.controllers.routes.Assets.versioned("basemodule/basemodule.js")),format.raw/*12.85*/("""'></script>
<script src='"""),_display_(/*13.15*/na/*13.17*/.mpt.controllers.routes.Assets.versioned("mpt/mpt.js")),format.raw/*13.71*/("""'></script>
"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:33 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/imports/scripts.scala.html
                  HASH: 2a8e33b425545a037d85016b79ccd8f701c8ed66
                  MATRIX: 1047->0|1110->37|1120->39|1192->91|1244->117|1254->119|1339->184|1391->210|1401->212|1506->296|1558->322|1568->324|1663->399|1715->425|1725->427|1835->516|1887->542|1897->544|2005->631|2057->657|2067->659|2177->748|2229->774|2239->776|2346->862|2398->888|2408->890|2515->976|2568->1002|2579->1004|2678->1082|2731->1108|2742->1110|2821->1168|2874->1194|2885->1196|2974->1264|3027->1290|3038->1292|3113->1346
                  LINES: 33->1|33->1|33->1|33->1|34->2|34->2|34->2|35->3|35->3|35->3|36->4|36->4|36->4|37->5|37->5|37->5|38->6|38->6|38->6|39->7|39->7|39->7|40->8|40->8|40->8|41->9|41->9|41->9|42->10|42->10|42->10|43->11|43->11|43->11|44->12|44->12|44->12|45->13|45->13|45->13
                  -- GENERATED --
              */
          