
package na.mpt.views.html.components.bulkWizard

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.mpt.settings.MptConstants
/*2.2*/import na.mpt.views.helpers.BuildSelectData
/*3.2*/import na.naportalbase.views.tags.i18n
/*4.2*/import pt.ptinovacao.na.portal.webui.restful.mpt.commons.model.FamilyEntry
/*5.2*/import scala.collection.immutable
/*6.2*/import scala.collection.mutable
/*7.2*/import na.naportalbase.views.html.components.selectsComp.selectComp
/*8.2*/import java.util
/*9.2*/import na.naportalbase.views.html.components.selectsComp.selectInputComp
/*10.2*/import na.naportalbase.views.html.components.inputsComp.wizardInputTextAreaComp
/*11.2*/import na.naportalbase.views.html.components.wizard.wizardContentComp

object characteristicsTab extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[util.List[FamilyEntry],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*13.2*/(allFamilies : util.List[FamilyEntry]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*15.2*/headContent/*15.13*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*15.17*/("""
    """),format.raw/*16.5*/("""<p class="fx-required">
        <abbr title=""""),_display_(/*17.23*/i18n("na.basemodule.validations.mandatory")),format.raw/*17.66*/(""""></abbr>
        """),_display_(/*18.10*/i18n("na.portal.mpt.messages.informative.mandatoryfields")),format.raw/*18.68*/("""
    """),format.raw/*19.5*/("""</p>
    <p>
        <strong>
        """),_display_(/*22.10*/i18n("na.portal.mpt.createoperation.label.provisiontype")),format.raw/*22.67*/("""
        """),format.raw/*23.9*/("""</strong>
    </p>
    <p>"""),_display_(/*25.9*/i18n("na.portal.mpt.createoperation.label.operation.identification")),format.raw/*25.77*/("""</p>
""")))};def /*90.2*/blockContent/*90.14*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*90.18*/("""
    """),format.raw/*91.5*/("""<div class="form-group">
        <label for="selectStates" class="col-sm-2 control-label fx-required-field">"""),_display_(/*92.85*/i18n( "na.portal.mpt.createoperation.label.family")),format.raw/*92.136*/("""</label>
        <div class="col-sm-10">
        """),_display_(/*94.10*/selectComp/*94.20*/.render(familySelectAttributes, null,null,BuildSelectData.buildFamiliesData(allFamilies))),format.raw/*94.109*/("""
        """),format.raw/*95.9*/("""</div>
    </div>
    <div class="form-group">
        <label for="s-oper" class="col-sm-2 control-label fx-required-field">"""),_display_(/*98.79*/i18n( "na.portal.mpt.createoperation.label.operation")),format.raw/*98.133*/("""</label>
        <div class="col-sm-10 mpt-input">
            <div class="mpt-input__input">
            """),_display_(/*101.14*/selectInputComp/*101.29*/.render(operationSelectAttributes)),format.raw/*101.63*/("""
            """),format.raw/*102.13*/("""</div>
            <div class="fx-context-help mpt-input__info-block" data-na-portal-mpt-create-operation-help-tooltip data-na-portal-mpt-display-mode></div>
        </div>
    </div>
    <div class="form-group">
    """),_display_(/*107.6*/wizardInputTextAreaComp/*107.29*/.render(i18n("na.portal.mpt.createoperation.label.description"),"operationDescriptionInput",
        descriptionInputAttributes, null, descriptionLabelAttributes)),format.raw/*108.70*/("""
    """),format.raw/*109.5*/("""</div>
""")))};def /*29.6*/familySelectAttributes/*29.28*/ = {{
        var map = immutable.HashMap[String, String](
            "data-ng-model" -> "createCtrl.operationData.selectedFamily",
            "name" -> "selectFamilies",
            "data-na-portal-select-box" -> null,
            "value" -> "",
            "id" -> "selectFamilies",
            "class" -> "form-control input-sm",
            "data-context" -> MptConstants.SELECT_FAMILIES_CONTEXT,
            "data-unlocks-select" -> "operationSelect",
            "data-na-portal-mpt-highlight-mandatory" -> "null",
            "data-na-portal-mpt-set-input-mode" -> "null",
            "full-width" -> ""
        )

        map
    }};def /*49.6*/operationSelectAttributes/*49.31*/ = {{

        var map = mutable.HashMap[String, String]()
        map.put("class","input-sm fx-filter-input select2-offscreen")
        map.put("data-ng-model", "createCtrl.operationData.operationId")
        map.put("name", "operations")
        map.put("data-na-portal-select-box",null)
        map.put("value", "")
        map.put("id", "operationSelect")
        map.put("data-is-dynamic", null)
        map.put("data-context", MptConstants.SELECT_OPERATIONS_CONTEXT)
        map.put("data-disable-select", null)
        map.put("full-width","")
        map.put("data-na-portal-mpt-highlight-mandatory", null)
        map.put("data-na-portal-mpt-check-select-disable", null)
        map.put("data-select-locker-id", "selectFamilies")
        map.put("data-na-portal-mpt-set-input-mode", "null")
        map

    }};def /*70.6*/descriptionLabelAttributes/*70.32*/ = {{
        var map = mutable.HashMap[String,String]()
        map.put("class","col-sm-2 control-label fx-field")

        map
    }};def /*77.6*/descriptionInputAttributes/*77.32*/ = {{
        var map = mutable.HashMap[String,String]()
        map.put("id","operationDescriptionInput")
        map.put("rows","6")
        map.put("class","form-control")
        map.put("data-ng-model","createCtrl.operationData.description")
        map.put("placeholder",i18n("na.portal.mpt.createoperation.label.description"))
        map.put("name","description")

        map
    }};
Seq[Any](format.raw/*13.40*/("""

"""),format.raw/*26.2*/("""

    """),format.raw/*28.5*/("""<!-- FAMILIES SELECT BOX-->
    """),format.raw/*45.6*/("""
    """),format.raw/*46.5*/("""<!-- END FAMILIES SELECT-->

    <!-- OPERATION SELECT -->
    """),format.raw/*68.6*/("""

    """),format.raw/*75.6*/("""

    """),format.raw/*87.6*/("""

    """),format.raw/*89.5*/("""<!-- END OPERATION SELECT-->
"""),format.raw/*110.2*/("""

"""),_display_(/*112.2*/wizardContentComp/*112.19*/.render(headContent,blockContent)))
      }
    }
  }

  def render(allFamilies:util.List[FamilyEntry]): play.twirl.api.HtmlFormat.Appendable = apply(allFamilies)

  def f:((util.List[FamilyEntry]) => play.twirl.api.HtmlFormat.Appendable) = (allFamilies) => apply(allFamilies)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:22 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/components/bulkWizard/characteristicsTab.scala.html
                  HASH: 5daf64eb2961ba6c038d54ca21605a47d35f7510
                  MATRIX: 680->1|723->38|774->83|820->123|902->199|943->234|982->267|1057->336|1081->354|1162->428|1250->509|1654->581|1771->622|1791->633|1872->637|1904->642|1977->688|2041->731|2087->750|2166->808|2198->813|2264->852|2342->909|2378->918|2431->945|2520->1013|2549->3259|2570->3271|2651->3275|2683->3280|2819->3389|2892->3440|2969->3490|2988->3500|3099->3589|3135->3598|3287->3723|3363->3777|3498->3884|3523->3899|3579->3933|3621->3946|3866->4164|3899->4187|4083->4349|4116->4354|4147->1058|4178->1080|4833->1790|4867->1815|5699->2640|5734->2666|5882->2807|5917->2833|6338->619|6367->1019|6400->1025|6459->1721|6491->1726|6581->2633|6614->2800|6647->3223|6680->3229|6737->4362|6767->4365|6794->4382
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|32->9|33->10|34->11|39->13|43->15|43->15|45->15|46->16|47->17|47->17|48->18|48->18|49->19|52->22|52->22|53->23|55->25|55->25|56->90|56->90|58->90|59->91|60->92|60->92|62->94|62->94|62->94|63->95|66->98|66->98|69->101|69->101|69->101|70->102|75->107|75->107|76->108|77->109|78->29|78->29|94->49|94->49|113->70|113->70|118->77|118->77|129->13|131->26|133->28|134->45|135->46|138->68|140->75|142->87|144->89|145->110|147->112|147->112
                  -- GENERATED --
              */
          