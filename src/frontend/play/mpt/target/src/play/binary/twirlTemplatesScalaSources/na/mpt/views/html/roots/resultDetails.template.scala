
package na.mpt.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.mpt.settings.MptConstants
/*2.2*/import na.naportalbase.views.tags.i18n
/*3.2*/import na.mpt.views.html.skeletons.mainSkel
/*4.2*/import na.mpt.views.html.components.resultsdetails.characteristicsTab

object resultDetails extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[String,String,com.fasterxml.jackson.databind.JsonNode,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*6.2*/(opk: String,operation:String, jsonDetails: com.fasterxml.jackson.databind.JsonNode):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*8.2*/conditionalOpk/*8.16*/ = {{
    var result = ""
    if(opk == null)
        result = "<i>sem opk</i>"
    else
        result = String.valueOf(opk)

    result
}};
Seq[Any](format.raw/*6.86*/("""

"""),format.raw/*16.2*/("""

"""),_display_(/*18.2*/mainSkel()/*18.12*/ {_display_(Seq[Any](format.raw/*18.14*/("""
    """),format.raw/*19.5*/("""<div id="mpt" class="page__content--results-details-view" data-na-portal-mpt-result-details-breadcrumb>

        <div class="page__content--result-details-view-header">
            <div class="fx-entity-header">
                <div class="fx-entity-header-info">
                    <span class="fx-entity-header-icon">
                        <i class="fuxicons fuxicons-mpt"></i>
                    </span>
                    <div class="fx-entity-header-title">
                        <h1>
                            """),_display_(/*29.30*/i18n("na.portal.mpt.result.details.title.opk")),format.raw/*29.76*/(""" """),_display_(/*29.78*/Html(conditionalOpk)),format.raw/*29.98*/("""
                        """),format.raw/*30.25*/("""</h1>
                        <p class="fx-entity-header-details">
                            """),_display_(/*32.30*/i18n("na.portal.mpt.result.details.subtitle.operation")),format.raw/*32.85*/(""" """),_display_(/*32.87*/operation),format.raw/*32.96*/("""
                        """),format.raw/*33.25*/("""</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="container--entity-content">
            <x-tab-container>
                <x-tab context=""""),_display_(/*41.34*/MptConstants/*41.46*/.MPT_BULK_DETAILS_RESULTS_TAB_CONTEXT),format.raw/*41.83*/("""" label="""),_display_(/*41.92*/i18n("na.portal.mpt.result.details.characteristics")),format.raw/*41.144*/(""">
                    <x-shadow-scroll flex>
                        <div class="tab-pane fx-form-vertical" id="mptResultDetailsCharacteristicsTab" data-formatted-details-json=""""),_display_(/*43.134*/jsonDetails),format.raw/*43.145*/("""" data-na-portal-mpt-result-details-page>
                            """),_display_(/*44.30*/characteristicsTab/*44.48*/.render()),format.raw/*44.57*/("""
                        """),format.raw/*45.25*/("""</div>
                    </x-shadow-scroll>
                </x-tab>
            </x-tab-container>
        </div>
    </div>
""")))}),format.raw/*51.2*/("""




"""))
      }
    }
  }

  def render(opk:String,operation:String,jsonDetails:com.fasterxml.jackson.databind.JsonNode): play.twirl.api.HtmlFormat.Appendable = apply(opk,operation,jsonDetails)

  def f:((String,String,com.fasterxml.jackson.databind.JsonNode) => play.twirl.api.HtmlFormat.Appendable) = (opk,operation,jsonDetails) => apply(opk,operation,jsonDetails)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:22 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/roots/resultDetails.scala.html
                  HASH: 7682c9dae75e582d94a43fb15a74017aaf85201e
                  MATRIX: 664->1|707->38|753->78|804->123|1233->195|1395->282|1417->296|1586->279|1615->435|1644->438|1663->448|1703->450|1735->455|2288->981|2355->1027|2384->1029|2425->1049|2478->1074|2601->1170|2677->1225|2706->1227|2736->1236|2789->1261|3017->1462|3038->1474|3096->1511|3132->1520|3206->1572|3412->1750|3445->1761|3543->1832|3570->1850|3600->1859|3653->1884|3812->2013
                  LINES: 24->1|25->2|26->3|27->4|32->6|36->8|36->8|45->6|47->16|49->18|49->18|49->18|50->19|60->29|60->29|60->29|60->29|61->30|63->32|63->32|63->32|63->32|64->33|72->41|72->41|72->41|72->41|72->41|74->43|74->43|75->44|75->44|75->44|76->45|82->51
                  -- GENERATED --
              */
          