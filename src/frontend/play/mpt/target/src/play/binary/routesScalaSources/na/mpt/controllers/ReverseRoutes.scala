// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/conf/mpt.routes
// @DATE:Tue Jul 01 11:21:22 WEST 2025

import play.api.mvc.Call


import _root_.controllers.Assets.Asset

// @LINE:2
package na.mpt.controllers {

  // @LINE:14
  class ReverseAssets(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:14
    def versioned(file:Asset): Call = {
      implicit lazy val _rrc = new play.core.routing.ReverseRouteContext(Map(("path", "/public"))); _rrc
      Call("GET", _prefix + { _defaultPrefix } + "mpt/assets/" + implicitly[play.api.mvc.PathBindable[Asset]].unbind("file", file))
    }
  
  }

  // @LINE:33
  class ReverseBulks(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:34
    def launchBulk(id:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/bulks/launch/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("id", id)))
    }
  
    // @LINE:36
    def suspendBulk(id:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/bulks/suspend/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("id", id)))
    }
  
    // @LINE:38
    def updateBulk(): Call = {
      
      Call("PUT", _prefix + { _defaultPrefix } + "mpt/bulks/update")
    }
  
    // @LINE:35
    def stopBulk(id:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/bulks/stop/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("id", id)))
    }
  
    // @LINE:33
    def BulksDatatableData(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "mpt/bulks/tabledata")
    }
  
    // @LINE:37
    def bulkEdit(bulkId:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/bulks/edit/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("bulkId", bulkId)))
    }
  
  }

  // @LINE:49
  class ReverseResultDetails(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:49
    def getResultDetails(operationId:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/results/details/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("operationId", operationId)))
    }
  
  }

  // @LINE:41
  class ReverseBulkDetails(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:42
    def getBulkDetailsResultsTab(operationId:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/bulks/details/results/tab/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("operationId", operationId)))
    }
  
    // @LINE:43
    def getBulkDetailsCharacteristicsTab(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/bulks/details/bulk")
    }
  
    // @LINE:45
    def getBulkOriginalFile(bulkId:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/bulks/export/original/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("bulkId", bulkId)))
    }
  
    // @LINE:44
    def getBulkResultFile(bulkId:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/bulks/export/results/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("bulkId", bulkId)))
    }
  
    // @LINE:41
    def getBulkDetails(bulkId:Long, tab:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/bulks/details/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("bulkId", bulkId)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("tab", tab)))
    }
  
  }

  // @LINE:2
  class ReverseApplication(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:17
    def getSettings(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/settings")
    }
  
    // @LINE:5
    def resume(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/resume")
    }
  
    // @LINE:11
    def getMptModuleAngularScripts(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/imports/jsscripts")
    }
  
    // @LINE:2
    def index(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/home")
    }
  
    // @LINE:8
    def javascriptRoutes(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/assets/javascripts/routes")
    }
  
  }

  // @LINE:21
  class ReverseCreateOperation(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:29
    def loadOperationHeaders(id:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/operation/create/operation/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)) + "/headers")
    }
  
    // @LINE:22
    def loadFilesTab(context:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/operation/create/files" + play.core.routing.queryString(List(Some(implicitly[play.api.mvc.QueryStringBindable[String]].unbind("context", context)))))
    }
  
    // @LINE:30
    def getEditScheduleModal(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/operation/edit/modals/editschedule")
    }
  
    // @LINE:25
    def validateSchedules(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "mpt/operation/create/validate")
    }
  
    // @LINE:26
    def loadScheduleTab(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/operation/create/schedule")
    }
  
    // @LINE:23
    def loadCharacteristicsTab(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/operation/create/characteristics")
    }
  
    // @LINE:21
    def loadCreateOperation(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/operation/create")
    }
  
    // @LINE:24
    def createNormalOperation(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "mpt/operation/create/uploadfiles")
    }
  
    // @LINE:28
    def getCreateScheduleModal(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/operation/create/modals/createschedule")
    }
  
    // @LINE:27
    def loadOperations(familyId:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/operation/create/families/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("familyId", familyId)))
    }
  
  }

  // @LINE:63
  class ReverseTableConfigs(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:63
    def bulkSearch(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/table-configs/bulks-search")
    }
  
  }

  // @LINE:48
  class ReverseBulkResults(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:48
    def resultsTableData(bulkId:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "mpt/results/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("bulkId", bulkId)) + "/tabledata")
    }
  
  }

  // @LINE:53
  class ReverseFilters(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:60
    def getFieldsForFiltername(filterName:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/filter/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("filterName", filterName)))
    }
  
    // @LINE:59
    def getFilterNamesForSelect(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/filter")
    }
  
    // @LINE:54
    def saveFilter(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "mpt/filter/persist")
    }
  
    // @LINE:58
    def getDeleteFilterModal(filterName:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/filter/delete/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("filterName", filterName)))
    }
  
    // @LINE:53
    def getSaveFilterModal(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/filter/save")
    }
  
    // @LINE:56
    def editFilter(filterName:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "mpt/filter/update/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("filterName", filterName)))
    }
  
    // @LINE:57
    def deleteFilter(filterName:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "mpt/filter/delete/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("filterName", filterName)))
    }
  
    // @LINE:55
    def getEditFilterModal(filterName:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "mpt/filter/edit/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("filterName", filterName)))
    }
  
  }


}
