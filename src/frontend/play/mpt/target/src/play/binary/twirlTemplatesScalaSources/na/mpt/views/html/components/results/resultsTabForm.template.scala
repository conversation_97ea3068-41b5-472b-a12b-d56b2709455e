
package na.mpt.views.html.components.results

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.mpt.views.helpers.AngularModelExpression
/*2.2*/import na.naportalbase.views.tags.i18n
/*3.2*/import na.naportalbase.views.html.components.inputsComp.sidebarInputTextComp
/*4.2*/import na.naportalbase.views.html.components.selectsComp.dynamicSelectOptionComp
/*5.2*/import pt.ptinovacao.na.portal.webui.restful.commons.model.OrderStatus
/*6.2*/import pt.ptinovacao.na.portal.webui.restful.commons.model.ExecutionStates
/*7.2*/import scala.collection.mutable
/*8.2*/import scala.collection.immutable
/*9.2*/import na.naportalbase.views.html.components.selectsComp.selectComp
/*10.2*/import na.naportalbase.views.html.components.buttonsComp.leftSideFormInputButtonsComp
/*11.2*/import pt.ptinovacao.na.portal.webui.restful.mpt.commons.model.OperationHeadersListEntry

object resultsTabForm extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[OperationHeadersListEntry,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*13.2*/(headers : OperationHeadersListEntry):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*187.2*/form/*187.6*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*187.10*/("""
    """),format.raw/*188.5*/("""<div class="form-group" data-na-portal-field-notification data-na-portal-button-notification>
        <label for="resultsStartDatePicker">"""),_display_(/*189.46*/{i18n("na.portal.mpt.results.fields.label.startDate")}),format.raw/*189.100*/("""</label>
        <x-date-time-picker data-field="startTime"></x-date-time-picker>
    </div>

    <div class="form-group" data-na-portal-field-notification data-na-portal-button-notification>
        <label for="resultsEndDatePicker">"""),_display_(/*194.44*/{i18n("na.portal.mpt.results.fields.label.endDate")}),format.raw/*194.96*/("""</label>
        <x-date-time-picker data-field="endTime"></x-date-time-picker>
    </div>

    <div class="form-group">
        """),_display_(/*199.10*/sidebarInputTextComp/*199.30*/.render(resultsOpkLabel, "opk",resultsOpkPlaceHolder,resultsOpkInputAttributes,false)),format.raw/*199.115*/("""
    """),format.raw/*200.5*/("""</div>

    <div class="form-group">
        """),_display_(/*203.10*/sidebarInputTextComp/*203.30*/.render(resultsKeyLabel, "appDn",resultsKeyPlaceHolder,resultsKeyInputAttributes,false)),format.raw/*203.117*/("""
    """),format.raw/*204.5*/("""</div>


    <div class="form-group">
        <label for="selectOrderStatus" class="control-label">"""),_display_(/*208.63*/i18n( "na.portal.mpt.results.fields.label.state")),format.raw/*208.112*/("""</label><br/>
        """),_display_(/*209.10*/selectComp/*209.20*/.render(resultsStateSelectAttributes, null, null, selectOrderStatusData)),format.raw/*209.92*/("""
    """),format.raw/*210.5*/("""</div>

    <div class="form-group">
        <label for="selectExecutionStates" class="control-label">"""),_display_(/*213.67*/i18n( "na.portal.mpt.results.fields.label.execution.state")),format.raw/*213.126*/("""</label><br/>
        """),_display_(/*214.10*/selectComp/*214.20*/.render(resultsExecutionStateSelectAttributes, null, null, selectExecutionStatesData)),format.raw/*214.105*/("""
    """),format.raw/*215.5*/("""</div>

    """),_display_(/*217.6*/for(header <- headers.getIn) yield /*217.34*/ {_display_(Seq[Any](format.raw/*217.36*/("""
        """),format.raw/*218.9*/("""<div class="form-group">
        """),_display_(/*219.10*/{
            var map = mutable.HashMap[String, String]()
            map.put("data-ng-model", AngularModelExpression("resultsCtrl","searchFormItems","inColumn"+header.getName))
            map.put("name", header.getName)
            map.put("id", header.getName)

            sidebarInputTextComp.render(header.getName, header.getName, header.getName, map, false)
        }),format.raw/*226.10*/("""
        """),format.raw/*227.9*/("""</div>
    """)))}),format.raw/*228.6*/("""

    """),_display_(/*230.6*/for(header <- headers.getOut) yield /*230.35*/ {_display_(Seq[Any](format.raw/*230.37*/("""
        """),format.raw/*231.9*/("""<div class="form-group">
        """),_display_(/*232.10*/{
            var map = mutable.HashMap[String, String]()
            map.put("data-ng-model", "resultsCtrl.searchFormItems.outColumn" + header.getName)
            map.put("name", header.getName)
            map.put("id", header.getName)

            sidebarInputTextComp.render(header.getName, header.getName, header.getName, map, false)
        }),format.raw/*239.10*/("""
        """),format.raw/*240.9*/("""</div>
    """)))}),format.raw/*241.6*/("""

""")))};def /*15.2*/caretTitle/*15.12*/ = {{i18n("na.buttons.caret.moreoptions")}};def /*16.2*/caretOptions/*16.14*/ = {{
    var map = mutable.LinkedHashMap[String,mutable.HashMap[String,mutable.HashMap[String,String]]]()

    var firstOptionTitle = i18n("na.modal.filters.title.editfilter")
    var secondOptionTitle = i18n("na.modal.filters.title.deletefilter")

    var firstOptionComponents = mutable.HashMap[String,mutable.HashMap[String,String]]()

    var firstOptionAattributes = mutable.HashMap[String,String]()
    firstOptionAattributes.put("id","editFilterButton")
    firstOptionAattributes.put("data-ng-click","resultsCtrl.openEditFilterModal()")

    var firstOptionIattributes = mutable.HashMap[String,String]()
    firstOptionIattributes.put("class","glyphicon glyphicon-pencil")

    firstOptionComponents.put("a",firstOptionAattributes)
    firstOptionComponents.put("i",firstOptionIattributes)

    var secondOptionComponents = mutable.HashMap[String,mutable.HashMap[String,String]]()

    var secondOptionAattributes = mutable.HashMap[String,String]()
    secondOptionAattributes.put("id","deleteFilterButton")
    secondOptionAattributes.put("data-ng-click","resultsCtrl.openDeleteFilterModal()")

    var secondOptionIattributes = mutable.HashMap[String,String]()
    secondOptionIattributes.put("class","glyphicon glyphicon-remove")

    secondOptionComponents.put("a",secondOptionAattributes)
    secondOptionComponents.put("i",secondOptionIattributes)

    map.put(firstOptionTitle,firstOptionComponents)
    map.put(secondOptionTitle,secondOptionComponents)

    map
}};def /*52.2*/selectAttributes/*52.18*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("class","input-sm fx-filter-input select2-offscreen")
    map.put("data-ng-model", "resultsCtrl.searchFormItems.filterName")
    map.put("name", "filters")
    map.put("data-na-portal-select-box",null)
    map.put("value", "")
    map.put("id", "resultsFilters")
    map.put("data-is-dynamic",null)
    map.put("data-context","filters")
    map.put("data-url","")

    map
}};def /*68.2*/datePickerAttributes/*68.22*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("id", "resultsStartDatePicker")
    map.put("data-date-time-picker", null)
    map.put("data-date-time-picker-input", null)
    map.put("data-max-date","today")

    map
}};def /*78.2*/inputAttributes/*78.17*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-model", "resultsCtrl.searchFormItems.startTime")
    map.put("data-datapickerfield", null)
    map.put("name", "startTime")
    map.put("id", "startTime")

    map
}};def /*89.2*/endDatePickerAttributes/*89.25*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("id", "resultsEndDatePicker")
    map.put("data-date-time-picker", null)
    map.put("data-date-time-picker-input", null)
    map.put("data-max-date","today")

    map
}};def /*98.2*/endDateinputAttributes/*98.24*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-datapickerfield", null)
    map.put("name", "endTime")
    map.put("id", "endTime")

    map
}};def /*108.2*/resultsOpkLabel/*108.17*/ = {{i18n( "na.portal.mpt.results.fields.label.opk")}};def /*109.2*/resultsOpkPlaceHolder/*109.23*/ = {{i18n( "na.portal.mpt.results.fields.label.opk")}};def /*110.2*/resultsOpkInputAttributes/*110.27*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-model", "resultsCtrl.searchFormItems.opk")
    map.put("name", "opk")
    map.put("id", "opk")
    map
}};def /*119.2*/resultsKeyLabel/*119.17*/ = {{i18n( "na.portal.mpt.results.fields.label.key")}};def /*120.2*/resultsKeyPlaceHolder/*120.23*/ = {{i18n( "na.portal.mpt.results.fields.label.key")}};def /*121.2*/resultsKeyInputAttributes/*121.27*/ = {{
    var map = mutable.HashMap[String,String]()
    map.put("data-ng-model", "resultsCtrl.searchFormItems.orderKeyRule")
    map.put("name", "orderKeyRule")
    map.put("id", "orderKeyRule")
    map
}};def /*129.2*/resultsStateSelectAttributes/*129.30*/ = {{
    var map = immutable.HashMap[String, String](
        "data-ng-model" -> "resultsCtrl.searchFormItems.status",
        "name" -> "selectOrderStatus",
        "data-na-portal-select-box" -> null,
        "value" -> "",
        "placeholder" -> i18n("na.portal.mpt.selects.states.empty.option"),
        "id" -> "selectOrderStatus",
        "class" -> "fx-working-context form-control input-sm pull-left select2",
        "style" -> "width: 100%;",
        "data-context" -> "states",
        "allow-clear" -> null
    )
    map
}};def /*145.2*/resultsExecutionStateSelectAttributes/*145.39*/ = {{
    var map = immutable.HashMap[String, String](
        "data-ng-model" -> "resultsCtrl.searchFormItems.executionState",
        "name" -> "selectExecutionStates",
        "data-na-portal-select-box" -> null,
        "value" -> "",
        "placeholder" -> i18n("na.portal.mpt.selects.states.empty.option"),
        "id" -> "selectExecutionStates",
        "class" -> "fx-working-context form-control input-sm pull-left select2",
        "style" -> "width: 100%;",
        "data-context" -> "states",
        "allow-clear" -> null
    )
    map
}};def /*161.2*/selectOrderStatusData/*161.23*/ = {{
    var stringBuilder = new StringBuilder
    for(entry <- OrderStatus.values){
        var optionAttributes = immutable.HashMap[String,String](
            "id" -> entry.name,
            "text" -> i18n("na.portal.mpt.bulks.order.status."+entry.name.toLowerCase),
            "value" -> entry.name
        )
        stringBuilder.append(dynamicSelectOptionComp.render(i18n("na.portal.mpt.bulks.order.status."+entry.name.toLowerCase),optionAttributes))
    }
    Html(stringBuilder.toString())
}};def /*174.2*/selectExecutionStatesData/*174.27*/ = {{
    var stringBuilder = new StringBuilder
    for(entry <- ExecutionStates.values){
        var optionAttributes = immutable.HashMap[String,String](
            "id" ->  entry.name,
            "text" -> i18n("na.portal.mpt.bulks.execution.states."+entry.name.toLowerCase),
            "value" -> entry.name
        )
        stringBuilder.append(dynamicSelectOptionComp.render(i18n("na.portal.mpt.bulks.execution.states."+entry.name.toLowerCase),optionAttributes))
    }
    Html(stringBuilder.toString())
}};def /*245.2*/searchButtonAttributes/*245.24*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-click", "resultsCtrl.submitResultsSearchForm()")
    map.put("id", "resultsSearchButton")
    map.put("type","submit")
    map.put("value",i18n("na.buttons.search"))

    map
}};def /*255.2*/clearButtonAttributes/*255.23*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-click", "resultsCtrl.clearSearchFormItems()")
    map.put("id", "clearSearchButton")
    map.put("type","reset")
    map.put("value",i18n("na.buttons.clear"))

    map
}};
Seq[Any](format.raw/*13.39*/("""

"""),format.raw/*15.54*/("""
"""),format.raw/*50.2*/("""

"""),format.raw/*65.2*/("""

"""),format.raw/*67.1*/("""<!-- Results Start Date -->
"""),format.raw/*76.2*/("""

"""),format.raw/*86.2*/("""

    """),format.raw/*88.5*/("""<!-- Results End Date -->
"""),format.raw/*97.2*/("""
"""),format.raw/*105.2*/("""

"""),format.raw/*107.1*/("""<!-- OPK FIELD -->
"""),format.raw/*108.70*/("""
"""),format.raw/*109.76*/("""
"""),format.raw/*116.2*/("""

"""),format.raw/*118.1*/("""<!-- KEY FIELD -->
"""),format.raw/*119.70*/("""
"""),format.raw/*120.76*/("""
"""),format.raw/*127.2*/("""

"""),format.raw/*143.2*/("""

"""),format.raw/*159.2*/("""

"""),format.raw/*172.2*/("""

"""),format.raw/*185.2*/("""

"""),format.raw/*243.2*/("""

"""),format.raw/*253.2*/("""

"""),format.raw/*263.2*/("""


"""),format.raw/*266.1*/("""<div class="fx-sidebar-content-wrapper search-sidebar__field-list">
    <x-shadow-scroll vertical>
    """),_display_(/*268.6*/form),format.raw/*268.10*/("""
    """),format.raw/*269.5*/("""</x-shadow-scroll>
</div>
<div class="form-group search-sidebar__button-list">
    <div class="fx-splitter-sidebar-buttons">
        <div class="fx-splitter-sidebar-buttons-inner">
        """),_display_(/*274.10*/leftSideFormInputButtonsComp/*274.38*/.render(searchButtonAttributes,clearButtonAttributes)),format.raw/*274.91*/("""
        """),format.raw/*275.9*/("""</div>
    </div>
</div>
"""))
      }
    }
  }

  def render(headers:OperationHeadersListEntry): play.twirl.api.HtmlFormat.Appendable = apply(headers)

  def f:((OperationHeadersListEntry) => play.twirl.api.HtmlFormat.Appendable) = (headers) => apply(headers)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:33 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/components/results/resultsTabForm.scala.html
                  HASH: 6d41f21ae566ea3b1336d6a10f54a3c52683b262
                  MATRIX: 677->1|735->53|781->93|865->171|953->253|1031->325|1113->401|1152->434|1193->469|1269->538|1363->625|1785->716|1902->6789|1915->6793|1997->6797|2030->6802|2197->6941|2274->6995|2537->7230|2611->7282|2769->7412|2799->7432|2907->7517|2940->7522|3014->7568|3044->7588|3154->7675|3187->7680|3315->7780|3387->7829|3438->7852|3458->7862|3552->7934|3585->7939|3716->8042|3798->8101|3849->8124|3869->8134|3977->8219|4010->8224|4050->8237|4095->8265|4136->8267|4173->8276|4235->8310|4631->8684|4668->8693|4711->8705|4745->8712|4791->8741|4832->8743|4869->8752|4931->8786|5302->9135|5339->9144|5382->9156|5408->756|5427->766|5483->810|5504->822|6998->2305|7023->2321|7477->2792|7506->2812|7757->3052|7781->3067|8035->3340|8067->3363|8316->3600|8347->3622|8528->3810|8553->3825|8621->3880|8652->3901|8720->3956|8755->3981|8949->4182|8974->4197|9042->4252|9073->4273|9141->4328|9176->4353|9396->4561|9434->4589|9986->5129|10033->5166|10601->5722|10632->5743|11148->6247|11183->6272|11712->9162|11744->9184|12010->9438|12041->9459|12316->753|12346->808|12374->2302|12403->2761|12432->2763|12487->3049|12516->3307|12549->3313|12602->3598|12631->3788|12661->3790|12709->3878|12739->3954|12768->4160|12798->4162|12846->4250|12876->4326|12905->4558|12935->5126|12965->5719|12995->6244|13025->6786|13055->9159|13085->9435|13115->9703|13146->9706|13277->9810|13303->9814|13336->9819|13554->10009|13592->10037|13667->10090|13704->10099
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|32->9|33->10|34->11|39->13|43->187|43->187|45->187|46->188|47->189|47->189|52->194|52->194|57->199|57->199|57->199|58->200|61->203|61->203|61->203|62->204|66->208|66->208|67->209|67->209|67->209|68->210|71->213|71->213|72->214|72->214|72->214|73->215|75->217|75->217|75->217|76->218|77->219|84->226|85->227|86->228|88->230|88->230|88->230|89->231|90->232|97->239|98->240|99->241|101->15|101->15|101->16|101->16|135->52|135->52|148->68|148->68|156->78|156->78|164->89|164->89|172->98|172->98|179->108|179->108|179->109|179->109|179->110|179->110|185->119|185->119|185->120|185->120|185->121|185->121|191->129|191->129|205->145|205->145|219->161|219->161|230->174|230->174|241->245|241->245|249->255|249->255|258->13|260->15|261->50|263->65|265->67|266->76|268->86|270->88|271->97|272->105|274->107|275->108|276->109|277->116|279->118|280->119|281->120|282->127|284->143|286->159|288->172|290->185|292->243|294->253|296->263|299->266|301->268|301->268|302->269|307->274|307->274|307->274|308->275
                  -- GENERATED --
              */
          