// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/conf/mpt.routes
// @DATE:Tue Jul 01 11:21:22 WEST 2025

package na.mpt.controllers;

import mpt.RoutesPrefix;

public class routes {
  
  public static final na.mpt.controllers.ReverseAssets Assets = new na.mpt.controllers.ReverseAssets(RoutesPrefix.byNamePrefix());
  public static final na.mpt.controllers.ReverseBulks Bulks = new na.mpt.controllers.ReverseBulks(RoutesPrefix.byNamePrefix());
  public static final na.mpt.controllers.ReverseResultDetails ResultDetails = new na.mpt.controllers.ReverseResultDetails(RoutesPrefix.byNamePrefix());
  public static final na.mpt.controllers.ReverseBulkDetails BulkDetails = new na.mpt.controllers.ReverseBulkDetails(RoutesPrefix.byNamePrefix());
  public static final na.mpt.controllers.ReverseApplication Application = new na.mpt.controllers.ReverseApplication(RoutesPrefix.byNamePrefix());
  public static final na.mpt.controllers.ReverseCreateOperation CreateOperation = new na.mpt.controllers.ReverseCreateOperation(RoutesPrefix.byNamePrefix());
  public static final na.mpt.controllers.ReverseTableConfigs TableConfigs = new na.mpt.controllers.ReverseTableConfigs(RoutesPrefix.byNamePrefix());
  public static final na.mpt.controllers.ReverseBulkResults BulkResults = new na.mpt.controllers.ReverseBulkResults(RoutesPrefix.byNamePrefix());
  public static final na.mpt.controllers.ReverseFilters Filters = new na.mpt.controllers.ReverseFilters(RoutesPrefix.byNamePrefix());

  public static class javascript {
    
    public static final na.mpt.controllers.javascript.ReverseAssets Assets = new na.mpt.controllers.javascript.ReverseAssets(RoutesPrefix.byNamePrefix());
    public static final na.mpt.controllers.javascript.ReverseBulks Bulks = new na.mpt.controllers.javascript.ReverseBulks(RoutesPrefix.byNamePrefix());
    public static final na.mpt.controllers.javascript.ReverseResultDetails ResultDetails = new na.mpt.controllers.javascript.ReverseResultDetails(RoutesPrefix.byNamePrefix());
    public static final na.mpt.controllers.javascript.ReverseBulkDetails BulkDetails = new na.mpt.controllers.javascript.ReverseBulkDetails(RoutesPrefix.byNamePrefix());
    public static final na.mpt.controllers.javascript.ReverseApplication Application = new na.mpt.controllers.javascript.ReverseApplication(RoutesPrefix.byNamePrefix());
    public static final na.mpt.controllers.javascript.ReverseCreateOperation CreateOperation = new na.mpt.controllers.javascript.ReverseCreateOperation(RoutesPrefix.byNamePrefix());
    public static final na.mpt.controllers.javascript.ReverseTableConfigs TableConfigs = new na.mpt.controllers.javascript.ReverseTableConfigs(RoutesPrefix.byNamePrefix());
    public static final na.mpt.controllers.javascript.ReverseBulkResults BulkResults = new na.mpt.controllers.javascript.ReverseBulkResults(RoutesPrefix.byNamePrefix());
    public static final na.mpt.controllers.javascript.ReverseFilters Filters = new na.mpt.controllers.javascript.ReverseFilters(RoutesPrefix.byNamePrefix());
  }

}
