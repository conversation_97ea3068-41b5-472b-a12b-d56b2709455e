
package na.mpt.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.mpt.views.html.components.bulkdetails.characteristicsTab
/*2.2*/import na.mpt.views.html.components.bulkdetails.resultsTab
/*3.2*/import na.mpt.settings.MptConstants
/*4.2*/import na.naportalbase.views.tags.i18n
/*5.2*/import com.fasterxml.jackson.databind.JsonNode
/*6.2*/import pt.ptinovacao.na.portal.webui.restful.mpt.commons.model.OperationHeadersListEntry
/*7.2*/import na.mpt.views.html.skeletons.mainSkel

object bulkDetails extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template7[Long,String,String,String,JsonNode,OperationHeadersListEntry,String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*9.2*/(bulkId: Long, tab: String, fileName: String, operationName: String, jsonInfo: JsonNode, headers: OperationHeadersListEntry, headersJson: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*9.147*/("""

"""),_display_(/*11.2*/mainSkel()/*11.12*/ {_display_(Seq[Any](format.raw/*11.14*/("""
    """),format.raw/*12.5*/("""<div id="mpt" class="page__content--bulk-details-view"
        data-ng-controller="NaPortalMptBulkDetailsController as detailsCtrl"
        data-na-portal-mpt-bulk-details-context
        data-formatted-details-json=""""),_display_(/*15.39*/jsonInfo),format.raw/*15.47*/(""""
        data-dynamic-fields=""""),_display_(/*16.31*/headersJson),format.raw/*16.42*/("""">

        <div class="page__content--bulk-details-view-header">
            <div class="fx-entity-header">
                <div class="fx-entity-header-info">
                    <span class="fx-entity-header-icon">
                        <i class="fuxicons fuxicons-mpt"></i>
                    </span>
                    <div class="fx-entity-header-title">
                        <h1>
                            <span is="x-dynamic-span" data-truncated-at="middle" data-ng-bind="detailsCtrl.bulkDetails.fileName"></span>
                        </h1>
                        <div class="fx-entity-header-details" style="display: flex">
                            <span style="min-width: fit-content">"""),_display_(/*29.67*/i18n("na.portal.mpt.field.operation.label")),format.raw/*29.110*/("""&nbsp:&nbsp</span>
                            <i><span is="x-dynamic-span" data-truncated-at="middle" data-ng-bind="detailsCtrl.bulkDetails.operationName"></span></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="btn-group fx-dropdown-reverse">
                <button id="exportButton"
                    type="button"
                    class="btn btn-default btn-sm dropdown-toggle"
                    title=""""),_display_(/*40.29*/i18n("na.button.label.export")),format.raw/*40.59*/(""""
                    data-toggle="dropdown"
                    aria-expanded="false"
                    >
                    <i class="glyphicon glyphicon-export"></i>
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu" role="menu">
                    <li>
                        <a id="originalfile" data-na-portal-mpt-bulk-export data-na-portal-mpt-bulk-export-file="original" href="#">
                        """),_display_(/*50.26*/i18n("na.portal.mpt.bulks.details.inputfile")),format.raw/*50.71*/("""
                        """),format.raw/*51.25*/("""</a>
                    </li>
                    <li>
                        <a id="resultfile" data-na-portal-mpt-bulk-export data-na-portal-mpt-bulk-export-file="results" href="#">
                        """),_display_(/*55.26*/i18n("na.portal.mpt.bulks.details.outputfile")),format.raw/*55.72*/("""
                        """),format.raw/*56.25*/("""</a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="container--entity-content" data-ng-controller="NaPortalMptCommonController" data-bulk-filename=""""),_display_(/*62.118*/fileName),format.raw/*62.126*/("""" data-na-portal-mpt-bulk-details-breadcrumb data-calc-height="true">
            <x-tab-container data-na-portal-mpt-details-tab-selector>
                <x-tab id=""""),_display_(/*64.29*/{MptConstants.MPT_BULK_DETAILS_TAB_CONTEXT}),format.raw/*64.72*/("""" context=""""),_display_(/*64.84*/{MptConstants.MPT_BULK_DETAILS_TAB_CONTEXT}),format.raw/*64.127*/("""" label=""""),_display_(/*64.137*/i18n("na.portal.mpt.bulk.details.characteristics")),format.raw/*64.187*/(""""
                    """),_display_(/*65.22*/("active".when(tab.equals(MptConstants.MPT_BULK_DETAILS_TAB_CONTEXT)))),format.raw/*65.92*/(""">
                    <x-shadow-scroll flex>
                        <div class="tab-pane fx-form-vertical" id="mptBulkDetailsCharacteristicsTab">
                            """),_display_(/*68.30*/characteristicsTab/*68.48*/.render()),format.raw/*68.57*/("""
                        """),format.raw/*69.25*/("""</div>
                    </x-shadow-scroll>
                </x-tab>
                <x-tab id=""""),_display_(/*72.29*/{MptConstants.MPT_BULK_DETAILS_RESULTS_TAB_CONTEXT}),format.raw/*72.80*/("""" context=""""),_display_(/*72.92*/{MptConstants.MPT_BULK_DETAILS_RESULTS_TAB_CONTEXT}),format.raw/*72.143*/("""" label=""""),_display_(/*72.153*/i18n("na.portal.mpt.bulk.details.results")),format.raw/*72.195*/(""""
                    """),_display_(/*73.22*/("active".when(tab.equals(MptConstants.MPT_BULK_DETAILS_RESULTS_TAB_CONTEXT)))),format.raw/*73.100*/(""">
                    <x-shadow-scroll flex>
                        <div class="tab-pane fx-form-vertical">
                            """),_display_(/*76.30*/resultsTab/*76.40*/.render(headers)),format.raw/*76.56*/("""
                        """),format.raw/*77.25*/("""</div>
                    </x-shadow-scroll>
                </x-tab>
            </x-tab-container>
        </div>
    </div>
""")))}))
      }
    }
  }

  def render(bulkId:Long,tab:String,fileName:String,operationName:String,jsonInfo:JsonNode,headers:OperationHeadersListEntry,headersJson:String): play.twirl.api.HtmlFormat.Appendable = apply(bulkId,tab,fileName,operationName,jsonInfo,headers,headersJson)

  def f:((Long,String,String,String,JsonNode,OperationHeadersListEntry,String) => play.twirl.api.HtmlFormat.Appendable) = (bulkId,tab,fileName,operationName,jsonInfo,headers,headersJson) => apply(bulkId,tab,fileName,operationName,jsonInfo,headers,headersJson)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:22 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/roots/bulkDetails.scala.html
                  HASH: 2599a350a467f2e5a3751fdd758662721fbba25a
                  MATRIX: 664->1|738->70|804->131|847->169|893->210|947->259|1043->350|1458->398|1699->543|1730->548|1749->558|1789->560|1822->566|2070->787|2099->795|2159->828|2191->839|2943->1564|3008->1607|3542->2114|3593->2144|4114->2638|4180->2683|4234->2709|4476->2924|4543->2970|4597->2996|4836->3207|4866->3215|5063->3385|5127->3428|5166->3440|5231->3483|5269->3493|5341->3543|5392->3567|5483->3637|5689->3816|5716->3834|5746->3843|5800->3869|5929->3971|6001->4022|6040->4034|6113->4085|6151->4095|6215->4137|6266->4161|6366->4239|6534->4380|6553->4390|6590->4406|6644->4432
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|35->9|40->9|42->11|42->11|42->11|43->12|46->15|46->15|47->16|47->16|60->29|60->29|71->40|71->40|81->50|81->50|82->51|86->55|86->55|87->56|93->62|93->62|95->64|95->64|95->64|95->64|95->64|95->64|96->65|96->65|99->68|99->68|99->68|100->69|103->72|103->72|103->72|103->72|103->72|103->72|104->73|104->73|107->76|107->76|107->76|108->77
                  -- GENERATED --
              */
          