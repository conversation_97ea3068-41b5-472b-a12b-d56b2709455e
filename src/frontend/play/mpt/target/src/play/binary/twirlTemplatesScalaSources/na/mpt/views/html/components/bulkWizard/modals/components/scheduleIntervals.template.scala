
package na.mpt.views.html.components.bulkWizard.modals.components

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import na.naportalbase.settings.DateFormats
/*3.2*/import na.naportalbase.views.html.components.selectsComp.selectComp
/*4.2*/import scala.collection.mutable
/*5.2*/import scala.collection.immutable

object scheduleIntervals extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*23.2*/hourSelectOptions/*23.19*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*23.23*/("""
    """),_display_(/*24.6*/for(hour <- 0 to 24) yield /*24.26*/ {_display_(Seq[Any](format.raw/*24.28*/("""
        """),format.raw/*25.9*/("""<option value=""""),_display_(/*25.25*/hour),format.raw/*25.29*/(""""><i class="fa fa-undo"></i>"""),_display_(/*25.58*/hour),format.raw/*25.62*/(""" """),_display_(/*25.64*/i18n("na.portal.mpt.createoperation.modals.scheduling.interval.hours")),format.raw/*25.134*/("""</option>
    """)))}),format.raw/*26.6*/("""
""")))};def /*44.2*/minutesSelectOptions/*44.22*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*44.26*/("""
    """),_display_(/*45.6*/for(interval <- 0 to 11) yield /*45.30*/ {_display_(Seq[Any](format.raw/*45.32*/("""
        """),format.raw/*46.9*/("""<option value=""""),_display_(/*46.25*/(interval*5)),format.raw/*46.37*/(""""><i class="fa fa-undo"></i>"""),_display_(/*46.66*/(interval*5)),format.raw/*46.78*/(""" """),_display_(/*46.80*/i18n("na.portal.mpt.createoperation.modals.scheduling.interval.minutes")),format.raw/*46.152*/("""</option>
    """)))}),format.raw/*47.6*/("""
""")))};def /*7.2*/timeFormat/*7.12*/ = {{DateFormats.TIME_SHORT.defaultFormat()}};def /*9.2*/elementHourAttr/*9.17*/ = {{
    var map = mutable.HashMap[String, String] (
        "id" -> "interval_{{interval.id}}_endHour",
        "name" -> "endHour",
        "data-ng-model" -> "interval.duration.hours",
        "data-na-portal-select-box" -> null,
        "data-show-icons" -> null,
        "class" -> "form-control modal-schedulers__interval-duration-hours",
        "select-width" -> "133px",
        "disable-search" -> "true"
    )
    immutable.HashMap(map.toSeq:_*)
}};def /*29.2*/elementMinutesAttr/*29.20*/ = {{
    var map = mutable.HashMap[String, String] (
        "id" -> "interval_{{interval.id}}_endMinutes",
        "name" -> "endMinutes",
        "data-ng-model" -> "interval.duration.minutes",
        "data-na-portal-select-box" -> null,
        "data-show-icons" -> null,
        "class" -> "form-control modal-schedulers__interval-duration-minutes",
        "select-width" -> "133px",
        "disable-search" -> "true",
        "data-ng-disabled" -> "interval.duration.hours == 24"
    )
    immutable.HashMap(map.toSeq:_*)
}};
Seq[Any](format.raw/*7.56*/("""

"""),format.raw/*21.2*/("""

"""),format.raw/*27.2*/("""

"""),format.raw/*42.2*/("""

"""),format.raw/*48.2*/("""

"""),format.raw/*50.1*/("""<div class="modal-schedulers__intervals">
    <strong>"""),_display_(/*51.14*/i18n("na.portal.mpt.createoperation.modals.chooseStartTimeAndDuration")),format.raw/*51.85*/("""</strong>
    <button id="schMod_addInt" data-ng-class=""""),format.raw/*52.47*/("""{"""),format.raw/*52.48*/("""disabled: scheduleModalCtrl.disableAddIntervalButton"""),format.raw/*52.100*/("""}"""),format.raw/*52.101*/("""" class="btn btn-sm btn-primary fx-call-to-action pull-right" data-ng-click="scheduleModalCtrl.addSchedule()">"""),_display_(/*52.212*/i18n("na.portal.mpt.createoperation.modals.addSchedule")),format.raw/*52.268*/("""</button>
    <ul class="clearfix list-unstyled">
        <li id="interval_"""),format.raw/*54.26*/("""{"""),format.raw/*54.27*/("""{"""),format.raw/*54.28*/("""interval.id"""),format.raw/*54.39*/("""}"""),format.raw/*54.40*/("""}"""),format.raw/*54.41*/("""" class="modal-schedulers__interval-line" data-na-portal-mpt-hight-light-bad-interval data-ng-repeat="interval in scheduleModalCtrl.intervals" data-interval-id=""""),format.raw/*54.202*/("""{"""),format.raw/*54.203*/("""{"""),format.raw/*54.204*/("""interval.id"""),format.raw/*54.215*/("""}"""),format.raw/*54.216*/("""}"""),format.raw/*54.217*/("""">
            <div class="col-sm-5">
                <label class="control-label date-form__label fx-required col-sm-2" for="interval_"""),format.raw/*56.98*/("""{"""),format.raw/*56.99*/("""{"""),format.raw/*56.100*/("""interval.id"""),format.raw/*56.111*/("""}"""),format.raw/*56.112*/("""}"""),format.raw/*56.113*/("""_startHour">
                """),_display_(/*57.18*/i18n("na.portal.mpt.createoperation.modals.scheduling.interval.startHour")),format.raw/*57.92*/("""<abbr title=""""),_display_(/*57.106*/i18n("na.basemodule.validations.mandatory")),format.raw/*57.149*/(""""></abbr>
                </label>
                <div class="col-sm-5" data-na-portal-field-notification data-na-portal-button-notification data-button-style="position:absolute|left:163px|top:0">
                    <x-date-time-picker value=""""),format.raw/*60.48*/("""{"""),format.raw/*60.49*/("""{"""),format.raw/*60.50*/("""interval.startHour"""),format.raw/*60.68*/("""}"""),format.raw/*60.69*/("""}"""),format.raw/*60.70*/("""" format="HH:mm" class="modal-schedulers__interval-startHour" utc></x-date-time-picker>
                </div>
            </div>
            <div class="col-sm-1">
                <label class="control-label date-form__label fx-required" for="interval_"""),format.raw/*64.89*/("""{"""),format.raw/*64.90*/("""{"""),format.raw/*64.91*/("""interval.id"""),format.raw/*64.102*/("""}"""),format.raw/*64.103*/("""}"""),format.raw/*64.104*/("""_endHour">
                """),_display_(/*65.18*/i18n("na.portal.mpt.createoperation.modals.scheduling.interval.duration")),format.raw/*65.91*/("""<abbr title=""""),_display_(/*65.105*/i18n("na.basemodule.validations.mandatory")),format.raw/*65.148*/(""""></abbr>
                </label>
            </div>
            <div class="col-sm-2">
                """),_display_(/*69.18*/selectComp/*69.28*/.render(elementHourAttr, null, null, hourSelectOptions)),format.raw/*69.83*/("""
            """),format.raw/*70.13*/("""</div>
            <div class="col-sm-2">
                """),_display_(/*72.18*/selectComp/*72.28*/.render(elementMinutesAttr, null, null, minutesSelectOptions)),format.raw/*72.89*/("""
            """),format.raw/*73.13*/("""</div>
            <div class="col-sm-2">
                <div class="scheduler-interval-button">
                    <button type="button" class="btn fx-btn-action rmInt pull-right" data-ng-click="scheduleModalCtrl.removeSchedule(interval.id)" data-ng-disabled="scheduleModalCtrl.disableIntervalRemoval">
                        <i class="glyphicon glyphicon-remove"></i>
                    </button>
                </div>
            </div>
        </li>
    </ul>
</div>"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:23 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/components/bulkWizard/modals/components/scheduleIntervals.scala.html
                  HASH: abd2ac011b14cfde2fc16862177f0f086619c082
                  MATRIX: 698->1|744->41|795->86|870->155|909->188|1326->758|1352->775|1433->779|1465->785|1501->805|1541->807|1577->816|1620->832|1645->836|1701->865|1726->869|1755->871|1847->941|1892->956|1917->1514|1946->1534|2027->1538|2059->1544|2099->1568|2139->1570|2175->1579|2218->1595|2251->1607|2307->1636|2340->1648|2369->1650|2463->1722|2508->1737|2532->224|2550->234|2607->281|2630->296|3103->961|3130->979|3692->278|3721->755|3750->958|3779->1511|3808->1739|3837->1741|3919->1796|4011->1867|4095->1923|4124->1924|4205->1976|4235->1977|4374->2088|4452->2144|4555->2219|4584->2220|4613->2221|4652->2232|4681->2233|4710->2234|4900->2395|4930->2396|4960->2397|5000->2408|5030->2409|5060->2410|5223->2545|5252->2546|5282->2547|5322->2558|5352->2559|5382->2560|5439->2590|5534->2664|5576->2678|5641->2721|5914->2966|5943->2967|5972->2968|6018->2986|6047->2987|6076->2988|6357->3241|6386->3242|6415->3243|6455->3254|6485->3255|6515->3256|6570->3284|6664->3357|6706->3371|6771->3414|6904->3520|6923->3530|6999->3585|7040->3598|7126->3657|7145->3667|7227->3728|7268->3741
                  LINES: 24->1|25->2|26->3|27->4|28->5|37->23|37->23|39->23|40->24|40->24|40->24|41->25|41->25|41->25|41->25|41->25|41->25|41->25|42->26|43->44|43->44|45->44|46->45|46->45|46->45|47->46|47->46|47->46|47->46|47->46|47->46|47->46|48->47|49->7|49->7|49->9|49->9|61->29|61->29|75->7|77->21|79->27|81->42|83->48|85->50|86->51|86->51|87->52|87->52|87->52|87->52|87->52|87->52|89->54|89->54|89->54|89->54|89->54|89->54|89->54|89->54|89->54|89->54|89->54|89->54|91->56|91->56|91->56|91->56|91->56|91->56|92->57|92->57|92->57|92->57|95->60|95->60|95->60|95->60|95->60|95->60|99->64|99->64|99->64|99->64|99->64|99->64|100->65|100->65|100->65|100->65|104->69|104->69|104->69|105->70|107->72|107->72|107->72|108->73
                  -- GENERATED --
              */
          