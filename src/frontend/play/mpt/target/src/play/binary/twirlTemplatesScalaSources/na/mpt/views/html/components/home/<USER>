
package na.mpt.views.html.components.home

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import scala.collection.immutable
/*3.2*/import scala.collection.mutable
/*4.2*/import na.naportalbase.views.html.components.selectsComp.selectComp
/*5.2*/import na.naportalbase.views.html.skeletons.search.horizontalFormSkel
/*6.2*/import na.mpt.controllers.routes.Filters

object modalDeleteFilter extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*8.2*/(filterName: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*46.2*/form/*46.6*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*46.10*/("""
    """),format.raw/*47.5*/("""<div class="form-group">
        <label class="control-label col-sm-3">"""),_display_(/*48.48*/i18n("na.modals.filters.select.label")),format.raw/*48.86*/("""</label>
        <div class="col-sm-9">
        """),_display_(/*50.10*/selectComp/*50.20*/.render(immutable.HashMap(filterSelectAttributes.toList: _*), filterName, filterSelectInitialOptionAttributes, null)),format.raw/*50.136*/("""
        """),format.raw/*51.9*/("""</div>
    </div>
""")))};def /*16.2*/filterSelectAttributes/*16.24*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("class","form-control input-sm")
    map.put("data-ng-model", "deleteModalCtrl.filterToDelete")
    map.put("name", "filters")
    map.put("data-na-portal-select-box",null)
    map.put("value", "")
    map.put("style", "width: 208px")
    map.put("id", "mptDeleteFilters")
    map.put("data-is-dynamic",null)
    map.put("data-context","filters")
    map.put("data-url",""+ Filters.getFilterNamesForSelect().url)

    map
}};def /*32.2*/filterSelectInitialOptionAttributes/*32.37*/ = {{
    var map = immutable.HashMap[String, String](
        "value" -> filterName
    )
    map
}};def /*39.2*/formAttributes/*39.16*/ = {{
    var map = mutable.HashMap[String,String]()
    map.put("data-ng-controller","NaPortalMptBulksDeleteFilterModalController as deleteModalCtrl")

    map
}};
Seq[Any](format.raw/*8.22*/("""

"""),format.raw/*10.1*/("""<script>
    //https://github.com/ivaynberg/select2/issues/1436
    $.fn.modal.Constructor.prototype.enforceFocus = function() """),format.raw/*12.64*/("""{"""),format.raw/*12.65*/("""}"""),format.raw/*12.66*/(""";
</script>

    <!--FILTER SELECT-->
"""),format.raw/*30.2*/("""

"""),format.raw/*37.2*/("""

"""),format.raw/*44.2*/("""

"""),format.raw/*53.2*/("""

"""),format.raw/*55.1*/("""<div class="fx-confirm-wrapper">
    <div class="alert alert-warning">
        <div class="fx-alert-icon">
        </div>
        <div class="fx-alert-message">
            <p>
                """),_display_(/*61.18*/i18n("na.modal.filters.info.deletefilter")),format.raw/*61.60*/("""
            """),format.raw/*62.13*/("""</p>
        </div>
    </div>
</div>

"""),_display_(/*67.2*/horizontalFormSkel/*67.20*/.render(formAttributes,form)))
      }
    }
  }

  def render(filterName:String): play.twirl.api.HtmlFormat.Appendable = apply(filterName)

  def f:((String) => play.twirl.api.HtmlFormat.Appendable) = (filterName) => apply(filterName)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:22 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/components/home/<USER>
                  HASH: a7a54eaa6b8e36c2dc371b9f022210e8efda84c5
                  MATRIX: 674->1|720->41|761->76|800->109|875->178|952->249|1309->292|1408->1313|1420->1317|1501->1321|1533->1326|1632->1398|1691->1436|1767->1485|1786->1495|1924->1611|1960->1620|2002->482|2033->504|2536->996|2580->1031|2694->1134|2717->1148|2909->312|2938->314|3093->441|3122->442|3151->443|3216->993|3245->1131|3274->1310|3303->1639|3332->1641|3553->1835|3616->1877|3657->1890|3723->1930|3750->1948
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|34->8|38->46|38->46|40->46|41->47|42->48|42->48|44->50|44->50|44->50|45->51|47->16|47->16|61->32|61->32|66->39|66->39|72->8|74->10|76->12|76->12|76->12|80->30|82->37|84->44|86->53|88->55|94->61|94->61|95->62|100->67|100->67
                  -- GENERATED --
              */
          