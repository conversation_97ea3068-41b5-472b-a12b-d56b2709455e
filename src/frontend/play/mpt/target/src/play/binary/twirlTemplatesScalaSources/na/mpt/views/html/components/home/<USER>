
package na.mpt.views.html.components.home

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.mpt.settings.MptServiceSettings
/*2.2*/import na.mpt.models.wrappers.FamilyWrapper
/*3.2*/import na.naportalbase.views.tags.i18n
/*4.2*/import na.naportalbase.views.html.components.inputsComp.sidebarInputTextComp
/*5.2*/import na.naportalbase.views.html.components.selectsComp.selectComp
/*6.2*/import na.naportalbase.views.html.components.selectsComp.dynamicSelectOptionComp
/*7.2*/import na.naportalbase.settings.DateFormats
/*8.2*/import scala.collection.mutable
/*9.2*/import na.mpt.controllers.routes.Filters
/*10.2*/import scala.collection.immutable
/*11.2*/import na.naportalbase.views.html.components.splitterComp.filterComp
/*12.2*/import na.naportalbase.views.html.components.selectsComp.selectInputComp
/*13.2*/import na.naportalbase.views.html.components.buttonsComp.caretDropdown
/*14.2*/import na.naportalbase.views.html.components.buttonsComp.leftSideFormInputButtonsComp
/*15.2*/import na.mpt.views.html.components.common.saveFilterButton
/*16.2*/import java.util

object leftSideForm extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[util.List[String],FamilyWrapper,MptServiceSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*18.2*/(states: util.List[String], families: FamilyWrapper, mptServiceSettings: MptServiceSettings):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*102.2*/headerContent/*102.15*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*102.19*/("""

        """),_display_(/*104.10*/filterComp/*104.20*/.render()),format.raw/*104.29*/("""
        """),format.raw/*105.9*/("""<div class="form-group search-form--filter-select">
            """),_display_(/*106.14*/selectInputComp/*106.29*/.render(selectAttributes)),format.raw/*106.54*/("""
            """),_display_(/*107.14*/caretDropdown/*107.27*/.render(caretTitle,caretOptions)),format.raw/*107.59*/("""
        """),format.raw/*108.9*/("""</div>
""")))};def /*221.2*/form/*221.6*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*221.10*/("""
    """),format.raw/*222.5*/("""<div class="form-group" data-na-portal-field-notification data-na-portal-button-notification>
        <span>
            <label>
                <x-i18n key="na.portal.mpt.field.startdate.label"></x-i18n>
            </label>
        </span>
        <x-date-time-picker data-field="startDate"></x-date-time-picker>
    </div>

    <div class="form-group" data-na-portal-field-notification data-na-portal-button-notification>
        <span>
            <label>
                <x-i18n key="na.portal.mpt.field.enddate.label"></x-i18n>
            </label>
        </span>
        <x-date-time-picker data-field="endDate"></x-date-time-picker>
    </div>

    <div class="form-group">
        """),_display_(/*241.10*/sidebarInputTextComp/*241.30*/.render(userLabel, "user",userPlaceHolder,userInputAttributes,false)),format.raw/*241.98*/("""
    """),format.raw/*242.5*/("""</div>

    <div class="form-group">
        <label for=""""),_display_(/*245.22*/familySelectAttributes/*245.44*/.get("id")),format.raw/*245.54*/("""">"""),_display_(/*245.57*/i18n("na.portal.mpt.field.family.label")),format.raw/*245.97*/("""</label><br>
        """),_display_(/*246.10*/selectComp/*246.20*/.render(familySelectAttributes, null, null, familySelectData)),format.raw/*246.81*/("""
    """),format.raw/*247.5*/("""</div>

    <div class="form-group">
        """),_display_(/*250.10*/sidebarInputTextComp/*250.30*/.render(operationLabel, "operation",operationPlaceHolder,operationInputAttributes,false)),format.raw/*250.118*/("""
    """),format.raw/*251.5*/("""</div>

    <div class="form-group">
        """),_display_(/*254.10*/sidebarInputTextComp/*254.30*/.render(fileLabel, "file",filePlaceHolder,fileInputAttributes,false)),format.raw/*254.98*/("""
    """),format.raw/*255.5*/("""</div>

    <div class="form-group">
        <label for="selectStates" class="control-label">"""),_display_(/*258.58*/i18n("na.portal.mpt.field.state.label")),format.raw/*258.97*/("""</label><br>
        """),_display_(/*259.10*/na/*259.12*/.naportalbase.views.html.components.selectsComp.selectComp.render(stateSelectAttributes, null, null, selectData)),format.raw/*259.124*/("""
    """),format.raw/*260.5*/("""</div>

    <div class="form-group search-form--save-filter-button">
        """),_display_(/*263.10*/saveFilterButton/*263.26*/.render("searchCtrl.openSaveFilterModal()")),format.raw/*263.69*/("""
    """),format.raw/*264.5*/("""</div>
""")))};def /*20.2*/caretTitle/*20.12*/ = {{i18n("na.buttons.caret.moreoptions")}};def /*21.2*/caretOptions/*21.14*/ = {{
    var map = mutable.LinkedHashMap[String,mutable.HashMap[String,mutable.HashMap[String,String]]]()

    var firstOptionTitle = i18n("na.modal.filters.title.editfilter")
    var secondOptionTitle = i18n("na.modal.filters.title.deletefilter")

    var firstOptionComponents = mutable.HashMap[String,mutable.HashMap[String,String]]()

    var firstOptionAattributes = mutable.HashMap[String,String]()
    firstOptionAattributes.put("id","editFilterButton")
    firstOptionAattributes.put("data-ng-click","searchCtrl.openEditFilterModal()")

    var firstOptionIattributes = mutable.HashMap[String,String]()
    firstOptionIattributes.put("class","glyphicon glyphicon-pencil")

    firstOptionComponents.put("a",firstOptionAattributes)
    firstOptionComponents.put("i",firstOptionIattributes)

    var secondOptionComponents = mutable.HashMap[String,mutable.HashMap[String,String]]()

    var secondOptionAattributes = mutable.HashMap[String,String]()
    secondOptionAattributes.put("id","deleteFilterButton")
    secondOptionAattributes.put("data-ng-click","searchCtrl.openDeleteFilterModal()")

    var secondOptionIattributes = mutable.HashMap[String,String]()
    secondOptionIattributes.put("class","glyphicon glyphicon-remove")

    secondOptionComponents.put("a",secondOptionAattributes)
    secondOptionComponents.put("i",secondOptionIattributes)

    map.put(firstOptionTitle,firstOptionComponents)
    map.put(secondOptionTitle,secondOptionComponents)

    map
}};def /*57.2*/selectAttributes/*57.18*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("class","input-sm fx-filter-input select2-offscreen")
    map.put("data-ng-model", "searchCtrl.selectedFilter")
    map.put("name", "filters")
    map.put("data-na-portal-select-box",null)
    map.put("value", "")
    map.put("id", "mptFilters")
    map.put("data-is-dynamic",null)
    map.put("data-context","filters")
    map.put("data-url", Filters.getFilterNamesForSelect().url)

    map
}};def /*73.2*/familySelectAttributes/*73.24*/ = {{

    var map = immutable.HashMap[String, String](
        "data-ng-model" -> "searchCtrl.searchFormItems.familyName",
        "name" -> "familyName",
        "data-na-portal-select-box" -> null,
        "value" -> "",
        "placeholder" -> i18n("na.portal.mpt.selects.option.allfamilies"),
        "id" -> "familyName",
        "class" -> "form-control input-sm",
        "style" -> "width:100%",
        "allow-clear" -> null
    )
    map
}};def /*89.2*/familySelectData/*89.18*/ = {{
    var stringBuilder = new StringBuilder
    for(family <- families.getEntries){
        var optionAttributes = immutable.HashMap[String,String](
            "id" -> family.getName,
            "text" -> family.getName,
            "value" -> family.getName
        )
        stringBuilder.append(dynamicSelectOptionComp.render(family.getName,optionAttributes))
    }
    Html(stringBuilder.toString())
}};def /*111.2*/startDateLabel/*111.16*/ = {{i18n("na.portal.mpt.field.startdate.label")}};def /*112.2*/datePickerAttributes/*112.22*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("id", "startDatePicker")
    map.put("data-date-time-picker", null)
    map.put("data-date-time-picker-input", null)
    map.put("data-max-date","today")

    map
}};def /*122.2*/inputAttributes/*122.17*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-model", "searchCtrl.searchFormItems.startDate")
    map.put("data-datapickerfield", null)
    map.put("name", "startDate")
    map.put("id", "startDate")

    map
}};def /*133.2*/endDateLabel/*133.14*/ = {{i18n("na.portal.mpt.field.enddate.label")}};def /*134.2*/endDatePickerAttributes/*134.25*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("id", "endDatePicker")
    map.put("data-date-time-picker", null)
    map.put("data-date-time-picker-input", null)
    map.put("data-max-date","today")

    map
}};def /*144.2*/endInputAttributes/*144.20*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-model", "searchCtrl.searchFormItems.endDate")
    map.put("data-datapickerfield", null)
    map.put("name", "endDate")
    map.put("id", "endDate")

    map
}};def /*155.2*/userLabel/*155.11*/ = {{i18n( "na.portal.mpt.field.user.label")}};def /*156.2*/userPlaceHolder/*156.17*/ = {{i18n( "na.portal.mpt.field.user.placeholder")}};def /*157.2*/userInputAttributes/*157.21*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-model", "searchCtrl.searchFormItems.username")
    map.put("name", "user")
    map.put("id", "user")
    map
}};def /*167.2*/operationLabel/*167.16*/ = {{i18n( "na.portal.mpt.field.operation.label")}};def /*168.2*/operationPlaceHolder/*168.22*/ = {{i18n( "na.portal.mpt.field.operation.placeholder")}};def /*169.2*/operationInputAttributes/*169.26*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-model", "searchCtrl.searchFormItems.operationName")
    map.put("name", "operation")
    map.put("id", "operation")
    map
}};def /*178.2*/fileLabel/*178.11*/ = {{i18n( "na.portal.mpt.field.file.label")}};def /*179.2*/filePlaceHolder/*179.17*/ = {{i18n( "na.portal.mpt.field.file.placeholder")}};def /*180.2*/fileInputAttributes/*180.21*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-model", "searchCtrl.searchFormItems.filename")
    map.put("name", "file")
    map.put("id", "file")
    map
}};def /*189.2*/stateSelectAttributes/*189.23*/ = {{
    var map = immutable.HashMap[String, String](
        "data-ng-model" -> "searchCtrl.searchFormItems.state",
        "name" -> "selectStates",
        "data-na-portal-select-box" -> null,
        "value" -> "",
        "placeholder" -> i18n("na.portal.mpt.selects.states.option.allstates"),
        "id" -> "selectStates",
        "class" -> "form-control input-sm",
        "style" -> "width:100%",
        "allow-clear" -> null
    )
    map
}};def /*205.2*/selectData/*205.12*/ = {{

    var stringBuilder = new StringBuilder

    for(entry <- states){
        var optionAttributes = immutable.HashMap[String,String](
            "id" -> entry,
            "text" -> i18n("na.portal.mpt.bulks.states."+mptServiceSettings.getBulkState(entry)),
            "value" -> entry
        )
        stringBuilder.append(dynamicSelectOptionComp.render(i18n("na.portal.mpt.bulks.states."+mptServiceSettings.getBulkState(entry)),optionAttributes))
    }

    Html(stringBuilder.toString())
}};def /*267.2*/searchButtonAttributes/*267.24*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-click", "searchCtrl.submitSearchForm($event)")
    map.put("id", "searchButton")
    map.put("type","submit")
    map.put("value",i18n("na.buttons.search"))
    map
}};def /*276.2*/clearButtonAttributes/*276.23*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-click", "searchCtrl.clearSearchForm($event)")
    map.put("id", "clearSearchButton")
    map.put("type","reset")
    map.put("value",i18n("na.buttons.clear"))
    map
}};
Seq[Any](format.raw/*18.94*/("""

"""),format.raw/*20.54*/("""
"""),format.raw/*55.2*/("""

"""),format.raw/*70.2*/("""

    """),format.raw/*72.5*/("""<!-- FAMILIES SELECT BOX-->
"""),format.raw/*87.2*/("""

"""),format.raw/*100.2*/("""

"""),format.raw/*109.2*/("""
"""),format.raw/*110.1*/("""<!-- Start Date -->
"""),format.raw/*111.65*/("""
"""),format.raw/*120.2*/("""

"""),format.raw/*130.2*/("""

"""),format.raw/*132.1*/("""<!-- End Date -->
"""),format.raw/*133.61*/("""
"""),format.raw/*142.2*/("""

"""),format.raw/*152.2*/("""

"""),format.raw/*154.1*/("""<!-- USER KEY FIELD -->
"""),format.raw/*155.56*/("""
"""),format.raw/*156.68*/("""
"""),format.raw/*163.2*/("""


"""),format.raw/*166.1*/("""<!-- Operation KEY FIELD -->
"""),format.raw/*167.66*/("""
"""),format.raw/*168.78*/("""
"""),format.raw/*175.2*/("""

"""),format.raw/*177.1*/("""<!-- File KEY FIELD -->
"""),format.raw/*178.56*/("""
"""),format.raw/*179.68*/("""
"""),format.raw/*186.2*/("""

"""),format.raw/*188.1*/("""<!-- state KEY FIELD -->
"""),format.raw/*202.2*/("""


"""),format.raw/*219.2*/("""

"""),format.raw/*265.2*/("""

"""),format.raw/*274.2*/("""

"""),format.raw/*283.2*/("""


"""),format.raw/*286.1*/("""<header class="fx-sidebar-header search-sidebar__header">
    <div class="fx-sidebar-header-inner">
        <p>
            <span class="fx-entity-header-icon">
                <i class="fa fa-search"></i>
            </span>
            <x-i18n key="na.general.filters"></x-i18n>
        </p>
    </div>
    <div class="form-group search-form--filter-select">
        <div class="search-form__selectbox">
            """),_display_(/*297.14*/selectInputComp/*297.29*/.render(selectAttributes)),format.raw/*297.54*/("""
        """),format.raw/*298.9*/("""</div>
        """),_display_(/*299.10*/caretDropdown/*299.23*/.render(caretTitle,caretOptions)),format.raw/*299.55*/("""
    """),format.raw/*300.5*/("""</div>
</header>
<div class="fx-sidebar-content-wrapper search-sidebar__field-list">
    <x-shadow-scroll vertical>
    """),_display_(/*304.6*/form),format.raw/*304.10*/("""
    """),format.raw/*305.5*/("""</x-shadow-scroll>
</div>
<div class="form-group search-sidebar__button-list">
    <div class="fx-splitter-sidebar-buttons">
        <div class="fx-splitter-sidebar-buttons-inner">
        """),_display_(/*310.10*/leftSideFormInputButtonsComp/*310.38*/.render(searchButtonAttributes,clearButtonAttributes)),format.raw/*310.91*/("""
        """),format.raw/*311.9*/("""</div>
    </div>
</div>
"""))
      }
    }
  }

  def render(states:util.List[String],families:FamilyWrapper,mptServiceSettings:MptServiceSettings): play.twirl.api.HtmlFormat.Appendable = apply(states,families,mptServiceSettings)

  def f:((util.List[String],FamilyWrapper,MptServiceSettings) => play.twirl.api.HtmlFormat.Appendable) = (states,families,mptServiceSettings) => apply(states,families,mptServiceSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:22 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/components/home/<USER>
                  HASH: 5b662b9b80a1872922224dd041d648e30b23be18
                  MATRIX: 674->1|723->44|774->89|820->129|904->207|979->276|1067->358|1118->403|1157->436|1206->478|1248->513|1325->583|1406->657|1485->729|1579->816|1647->877|2020->896|2192->3954|2215->3967|2297->3971|2336->3982|2356->3992|2387->4001|2424->4010|2517->4075|2542->4090|2589->4115|2631->4129|2654->4142|2708->4174|2745->4183|2777->7505|2790->7509|2872->7513|2905->7518|3625->8210|3655->8230|3745->8298|3778->8303|3864->8361|3896->8383|3928->8393|3959->8396|4021->8436|4071->8458|4091->8468|4174->8529|4207->8534|4281->8580|4311->8600|4422->8688|4455->8693|4529->8739|4559->8759|4649->8827|4682->8832|4804->8926|4865->8965|4915->8987|4927->8989|5062->9101|5095->9106|5201->9184|5227->9200|5292->9243|5325->9248|5356->991|5375->1001|5431->1045|5452->1057|6944->2538|6969->2554|7442->3048|7473->3070|7938->3524|7963->3540|8389->4213|8413->4227|8477->4278|8507->4298|8752->4531|8777->4546|9031->4806|9053->4818|9115->4867|9148->4890|9391->5121|9419->5139|9667->5399|9686->5408|9746->5455|9771->5470|9837->5523|9866->5542|10066->5760|10090->5774|10155->5826|10185->5846|10256->5904|10290->5928|10505->6155|10524->6164|10584->6211|10609->6226|10675->6279|10704->6298|10904->6511|10935->6532|11404->6990|11424->7000|11941->9259|11973->9281|12229->9525|12260->9546|12534->988|12564->1043|12592->2535|12621->3013|12654->3019|12709->3521|12739->3951|12769->4191|12798->4192|12847->4276|12876->4528|12906->4785|12936->4787|12983->4865|13012->5118|13042->5372|13072->5374|13125->5453|13155->5521|13184->5727|13215->5730|13273->5824|13303->5902|13332->6128|13362->6130|13415->6209|13445->6277|13474->6483|13504->6485|13557->6986|13588->7502|13618->9256|13648->9522|13678->9789|13709->9792|14156->10211|14181->10226|14228->10251|14265->10260|14309->10276|14332->10289|14386->10321|14419->10326|14567->10447|14593->10451|14626->10456|14844->10646|14882->10674|14957->10727|14994->10736
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|32->9|33->10|34->11|35->12|36->13|37->14|38->15|39->16|44->18|48->102|48->102|50->102|52->104|52->104|52->104|53->105|54->106|54->106|54->106|55->107|55->107|55->107|56->108|57->221|57->221|59->221|60->222|79->241|79->241|79->241|80->242|83->245|83->245|83->245|83->245|83->245|84->246|84->246|84->246|85->247|88->250|88->250|88->250|89->251|92->254|92->254|92->254|93->255|96->258|96->258|97->259|97->259|97->259|98->260|101->263|101->263|101->263|102->264|103->20|103->20|103->21|103->21|137->57|137->57|150->73|150->73|164->89|164->89|175->111|175->111|175->112|175->112|183->122|183->122|191->133|191->133|191->134|191->134|199->144|199->144|207->155|207->155|207->156|207->156|207->157|207->157|213->167|213->167|213->168|213->168|213->169|213->169|219->178|219->178|219->179|219->179|219->180|219->180|225->189|225->189|238->205|238->205|252->267|252->267|259->276|259->276|267->18|269->20|270->55|272->70|274->72|275->87|277->100|279->109|280->110|281->111|282->120|284->130|286->132|287->133|288->142|290->152|292->154|293->155|294->156|295->163|298->166|299->167|300->168|301->175|303->177|304->178|305->179|306->186|308->188|309->202|312->219|314->265|316->274|318->283|321->286|332->297|332->297|332->297|333->298|334->299|334->299|334->299|335->300|339->304|339->304|340->305|345->310|345->310|345->310|346->311
                  -- GENERATED --
              */
          