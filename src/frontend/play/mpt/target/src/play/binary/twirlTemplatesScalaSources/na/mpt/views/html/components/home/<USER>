
package na.mpt.views.html.components.home

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.mpt.settings.MptServiceSettings
/*2.2*/import na.mpt.models.wrappers.FamilyWrapper
/*3.2*/import na.naportalbase.views.tags.i18n
/*4.2*/import na.naportalbase.views.html.components.selectsComp.dynamicSelectOptionComp
/*5.2*/import na.mpt.settings.MptConstants
/*6.2*/import java.util
/*7.2*/import scala.collection.mutable
/*8.2*/import scala.collection.immutable
/*9.2*/import na.naportalbase.views.html.components.selectsComp.selectComp
/*10.2*/import na.naportalbase.views.html.components.inputsComp.modalInputTextComp
/*11.2*/import na.naportalbase.views.html.skeletons.search.horizontalFormSkel

object modalEditFilter extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[String,util.List[String],FamilyWrapper,MptServiceSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*13.2*/(filterName: String, states : util.List[String], families: FamilyWrapper, mptServiceSettings: MptServiceSettings):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*177.2*/form/*177.6*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*177.10*/("""
    """),format.raw/*178.5*/("""<div class="form-group" data-na-portal-field-notification>
        """),_display_(/*179.10*/modalInputTextComp/*179.28*/.render(filterFilterNameLabel, "filterName",filterFilterNamePlaceHolder,filterFilterNameInputAttributes)),format.raw/*179.132*/("""
    """),format.raw/*180.5*/("""</div>

    <div class="fx-expand">
        """),_display_(/*183.10*/i18n("na.modals.filters.options")),format.raw/*183.43*/("""
    """),format.raw/*184.5*/("""</div>

    <div class="clearfix">
        <div class="form-group" data-na-portal-field-notification data-na-portal-button-notification>
            <label class="control-label col-sm-3" for="filterStartDatePicker">"""),_display_(/*188.80*/{i18n("na.portal.mpt.field.startdate.label")}),format.raw/*188.125*/("""</label>
            <x-date-time-picker class="col-sm-9" data-field="startDate" format="short"></x-date-time-picker>
        </div>

        <div class="form-group" data-na-portal-field-notification data-na-portal-button-notification>
            <label class="control-label col-sm-3" for="filterEndDatePicker">"""),_display_(/*193.78*/{i18n("na.portal.mpt.field.enddate.label")}),format.raw/*193.121*/("""</label>
            <x-date-time-picker class="col-sm-9" data-field="endDate" format="short"></x-date-time-picker>
        </div>

        <div class="form-group">
            """),_display_(/*198.14*/modalInputTextComp/*198.32*/.render(filterUsernameLabel, "modalUsername",filterUsernamePlaceHolder,filterUsernameInputAttributes)),format.raw/*198.133*/("""
        """),format.raw/*199.9*/("""</div>

        <div class="form-group">
            <label for="modalStates" class="control-label col-sm-3">"""),_display_(/*202.70*/i18n("na.portal.mpt.field.family.label")),format.raw/*202.110*/("""</label>
            <div class="col-sm-9">
                """),_display_(/*204.18*/selectComp/*204.28*/.render(familySelectAttributes, null, null, familySelectData)),format.raw/*204.89*/("""
            """),format.raw/*205.13*/("""</div>
        </div>

        <div class="form-group">
            """),_display_(/*209.14*/modalInputTextComp/*209.32*/.render(filterOperationLabel, "modalOperation",filterOperationPlaceHolder,filterOperationInputAttributes)),format.raw/*209.137*/("""
        """),format.raw/*210.9*/("""</div>

        <div class="form-group">
            """),_display_(/*213.14*/modalInputTextComp/*213.32*/.render(filterFileLabel,"modalFileName" ,filterFilePlaceHolder,filterFileInputAttributes)),format.raw/*213.121*/("""
        """),format.raw/*214.9*/("""</div>

        <div class="form-group">
            <label for="modalStates" class="control-label col-sm-3">"""),_display_(/*217.70*/i18n( "na.portal.mpt.field.state.label")),format.raw/*217.110*/("""</label>
            <div class="col-sm-9">
                """),_display_(/*219.18*/selectComp/*219.28*/.render(stateSelectAttributes, null, null, selectData)),format.raw/*219.82*/("""
            """),format.raw/*220.13*/("""</div>
        </div>
    </div>
""")))};def /*20.2*/filterStartDateLabel/*20.22*/ = {{i18n("na.portal.mpt.field.startdate.label")}};def /*21.2*/datePickerAttributes/*21.22*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("id", "filterStartDatePicker")
    map.put("data-date-time-picker", null)
    map.put("data-date-time-picker-input", null)
    map.put("data-max-date","today")
    map
}};def /*30.2*/inputAttributes/*30.17*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-model", "editFilterCtrl.tempSearchFormItems.startDate")
    map.put("data-datapickerfield", null)
    map.put("name", "editModalStartDate")
    map.put("id", "editModalStartDate")
    map
}};def /*40.2*/filterEndDateLabel/*40.20*/ = {{i18n("na.portal.mpt.field.enddate.label")}};def /*41.2*/endDatePickerAttributes/*41.25*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("id", "filterEndDatePicker")
    map.put("data-date-time-picker", null)
    map.put("data-date-time-picker-input", null)
    map.put("data-max-date","today")
    map
}};def /*49.2*/endDateinputAttributes/*49.24*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-model", "editFilterCtrl.tempSearchFormItems.endDate")
    map.put("data-datapickerfield", null)
    map.put("name", "editModalEndDate")
    map.put("id", "editModalEndDate")
    map
}};def /*59.2*/filterUsernameLabel/*59.21*/ = {{i18n( "na.portal.mpt.field.user.label")}};def /*60.2*/filterUsernamePlaceHolder/*60.27*/ = {{i18n( "na.portal.mpt.field.user.placeholder")}};def /*61.2*/filterUsernameInputAttributes/*61.31*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-model", "editFilterCtrl.tempSearchFormItems.username")
    map.put("name", "editModalUsername")
    map.put("id", "editModalUsername")
    map
}};def /*70.2*/familySelectAttributes/*70.24*/ = {{
    var map = immutable.HashMap[String, String](
        "data-ng-model" -> "editFilterCtrl.tempSearchFormItems.familyName",
        "name" -> "modalFamily",
        "data-na-portal-select-box" -> null,
        "placeholder" -> i18n("na.portal.mpt.selects.option.allfamilies"),
        "value" -> "",
        "id" -> "modalFamily",
        "class" -> "form-control input-sm",
        "data-context" -> MptConstants.SELECT_FAMILIES_CONTEXT,
        "allow-clear" -> null
    )
    map
}};def /*85.2*/familySelectData/*85.18*/ = {{
    var stringBuilder = new StringBuilder
    for(family <- families.getEntries){
        var optionAttributes = immutable.HashMap[String,String](
            "id" -> family.getName,
            "text" -> family.getName,
            "value" -> family.getName
        )
        stringBuilder.append(dynamicSelectOptionComp.render(family.getName,optionAttributes))
    }
    Html(stringBuilder.toString())
}};def /*99.2*/filterOperationLabel/*99.22*/ = {{i18n( "na.portal.mpt.field.operation.label")}};def /*100.2*/filterOperationPlaceHolder/*100.28*/ = {{i18n( "na.portal.mpt.field.operation.label")}};def /*101.2*/filterOperationInputAttributes/*101.32*/ = {{
    var map = mutable.HashMap[String,String]()
    map.put("data-ng-model", "editFilterCtrl.tempSearchFormItems.operationName")
    map.put("name", "modalOperation")
    map.put("id", "modalOperation")
    map
}};def /*110.2*/filterFilterNameLabel/*110.23*/ = {{i18n( "na.portal.mpt.field.filtername.label")}};def /*111.2*/filterFilterNamePlaceHolder/*111.29*/ = {{i18n( "na.portal.mpt.field.filtername.placeholder")}};def /*112.2*/filterFilterNameInputAttributes/*112.33*/ = {{
    var map = mutable.HashMap[String,String]()
    map.put("data-na-portal-required",null)
    map.put("data-ng-model", "editFilterCtrl.tempSearchFormItems.filterName")
    map.put("name", "modalFilterName")
    map.put("id", "modalFilterName")
    map.put("maxlength", "64")
    map.put("disabled", "true")
    map
}};def /*124.2*/filterFileLabel/*124.17*/ = {{i18n( "na.portal.mpt.field.file.label")}};def /*125.2*/filterFilePlaceHolder/*125.23*/ = {{i18n( "na.portal.mpt.field.file.placeholder")}};def /*126.2*/filterFileInputAttributes/*126.27*/ = {{
    var map = mutable.HashMap[String,String]()
    map.put("data-ng-model", "editFilterCtrl.tempSearchFormItems.filename")
    map.put("name", "modalFileName")
    map.put("id", "modalFileName")
    map
}};def /*135.2*/stateSelectAttributes/*135.23*/ = {{
    var map = immutable.HashMap[String, String](
        "data-ng-model" -> "editFilterCtrl.tempSearchFormItems.state",
        "name" -> "modalStates",
        "data-na-portal-select-box" -> null,
        "value" -> "",
        "placeholder" -> i18n("na.portal.mpt.selects.states.option.allstates"),
        "id" -> "modalStates",
        "class" -> "form-control input-sm",
        "allow-clear" -> null
    )
    map
}};def /*149.2*/selectData/*149.12*/ = {{
    var stringBuilder = new StringBuilder

    for(entry <- states){
        var optionAttributes = immutable.HashMap[String,String](
            "id" -> entry,
            "text" -> i18n("na.portal.mpt.bulks.states."+mptServiceSettings.getBulkState(entry)),
            "value" -> entry
        )
        stringBuilder.append(dynamicSelectOptionComp.render(i18n("na.portal.mpt.bulks.states."+mptServiceSettings.getBulkState(entry)),optionAttributes))
    }

    Html(stringBuilder.toString())
}};def /*164.2*/formAttributes/*164.16*/ = {{
    var map = mutable.HashMap[String,String]()
    map.put("data-ng-controller","NaPortalMptBulksEditFilterModalController as editFilterCtrl")
    map
}};def /*170.2*/filterSelectInitialOptionAttributes/*170.37*/ = {{
    var map = immutable.HashMap[String, String](
        "value" -> filterName
    )
    map
}};
Seq[Any](format.raw/*13.115*/("""

"""),format.raw/*15.1*/("""<script>
    $.fn.modal.Constructor.prototype.enforceFocus = function() """),format.raw/*16.64*/("""{"""),format.raw/*16.65*/("""}"""),format.raw/*16.66*/(""";
</script>

<!-- Filter Start Date -->
"""),format.raw/*20.71*/("""
"""),format.raw/*28.2*/("""

"""),format.raw/*37.2*/("""

"""),format.raw/*39.1*/("""<!-- Filter End Date -->
"""),format.raw/*40.67*/("""
"""),format.raw/*48.2*/("""
"""),format.raw/*56.2*/("""

"""),format.raw/*58.1*/("""<!-- USERNAME FIELD -->
"""),format.raw/*59.66*/("""
"""),format.raw/*60.78*/("""
"""),format.raw/*67.2*/("""

"""),format.raw/*69.1*/("""<!-- FAMILIES SELECT BOX -->
"""),format.raw/*83.2*/("""

"""),format.raw/*96.2*/("""

"""),format.raw/*98.1*/("""<!-- OPERATION FIELD -->
"""),format.raw/*99.72*/("""
"""),format.raw/*100.78*/("""
"""),format.raw/*107.2*/("""

"""),format.raw/*109.1*/("""<!-- FILTERNAME FIELD -->
"""),format.raw/*110.74*/("""
"""),format.raw/*111.86*/("""
"""),format.raw/*121.2*/("""

"""),format.raw/*123.1*/("""<!-- FILENAME FIELD -->
"""),format.raw/*124.62*/("""
"""),format.raw/*125.74*/("""
"""),format.raw/*132.2*/("""

"""),format.raw/*134.1*/("""<!-- STATES KEY FIELD -->
"""),format.raw/*147.2*/("""

"""),format.raw/*162.2*/("""

"""),format.raw/*168.2*/("""

"""),format.raw/*175.2*/("""

"""),format.raw/*223.2*/("""

"""),_display_(/*225.2*/horizontalFormSkel/*225.20*/.render(formAttributes,form)),format.raw/*225.48*/("""
"""))
      }
    }
  }

  def render(filterName:String,states:util.List[String],families:FamilyWrapper,mptServiceSettings:MptServiceSettings): play.twirl.api.HtmlFormat.Appendable = apply(filterName,states,families,mptServiceSettings)

  def f:((String,util.List[String],FamilyWrapper,MptServiceSettings) => play.twirl.api.HtmlFormat.Appendable) = (filterName,states,families,mptServiceSettings) => apply(filterName,states,families,mptServiceSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:22 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/components/home/<USER>
                  HASH: b520aebd1cbdb8152753cd3d62e4261a1d017d55
                  MATRIX: 674->1|723->44|774->89|820->129|908->211|951->248|975->266|1014->299|1055->334|1131->403|1214->479|1650->551|1843->6089|1856->6093|1938->6097|1971->6102|2067->6170|2095->6188|2222->6292|2255->6297|2328->6342|2383->6375|2416->6380|2660->6596|2728->6641|3069->6954|3135->6997|3341->7175|3369->7193|3493->7294|3530->7303|3668->7413|3731->7453|3820->7514|3840->7524|3923->7585|3965->7598|4062->7667|4090->7685|4218->7790|4255->7799|4337->7853|4365->7871|4477->7960|4514->7969|4652->8079|4715->8119|4804->8180|4824->8190|4900->8244|4942->8257|4999->781|5028->801|5091->852|5120->872|5369->1110|5393->1125|5671->1417|5698->1435|5759->1484|5791->1507|6038->1742|6069->1764|6341->2049|6369->2068|6428->2115|6462->2140|6527->2193|6565->2222|6798->2473|6829->2495|7334->2989|7359->3005|7784->3444|7813->3464|7878->3516|7914->3542|7979->3594|8019->3624|8251->3870|8282->3891|8348->3944|8385->3971|8457->4030|8498->4061|8836->4411|8861->4426|8921->4473|8952->4494|9018->4547|9053->4572|9278->4811|9309->4832|9751->5262|9771->5272|10287->5776|10311->5790|10484->5951|10529->5986|10661->664|10690->666|10790->738|10819->739|10848->740|10916->850|10944->1107|10973->1389|11002->1391|11055->1482|11083->1740|11111->2022|11140->2024|11192->2113|11221->2191|11249->2441|11278->2443|11334->2986|11363->3416|11392->3418|11445->3514|11475->3592|11504->3841|11534->3843|11589->3942|11619->4028|11648->4384|11678->4386|11731->4471|11761->4545|11790->4782|11820->4784|11874->5259|11904->5773|11934->5948|11964->6086|11994->8291|12024->8294|12052->8312|12102->8340
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|32->9|33->10|34->11|39->13|43->177|43->177|45->177|46->178|47->179|47->179|47->179|48->180|51->183|51->183|52->184|56->188|56->188|61->193|61->193|66->198|66->198|66->198|67->199|70->202|70->202|72->204|72->204|72->204|73->205|77->209|77->209|77->209|78->210|81->213|81->213|81->213|82->214|85->217|85->217|87->219|87->219|87->219|88->220|91->20|91->20|91->21|91->21|98->30|98->30|105->40|105->40|105->41|105->41|112->49|112->49|119->59|119->59|119->60|119->60|119->61|119->61|125->70|125->70|138->85|138->85|149->99|149->99|149->100|149->100|149->101|149->101|155->110|155->110|155->111|155->111|155->112|155->112|164->124|164->124|164->125|164->125|164->126|164->126|170->135|170->135|182->149|182->149|195->164|195->164|199->170|199->170|205->13|207->15|208->16|208->16|208->16|212->20|213->28|215->37|217->39|218->40|219->48|220->56|222->58|223->59|224->60|225->67|227->69|228->83|230->96|232->98|233->99|234->100|235->107|237->109|238->110|239->111|240->121|242->123|243->124|244->125|245->132|247->134|248->147|250->162|252->168|254->175|256->223|258->225|258->225|258->225
                  -- GENERATED --
              */
          