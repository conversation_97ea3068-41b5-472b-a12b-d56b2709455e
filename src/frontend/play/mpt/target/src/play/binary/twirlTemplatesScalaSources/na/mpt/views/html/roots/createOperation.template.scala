
package na.mpt.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.mpt.views.html.components.createoperation.operationWizard
/*2.2*/import na.mpt.views.html.skeletons.mainSkel
/*3.2*/import na.naportalbase.views.tags.i18n
/*4.2*/import pt.ptinovacao.na.portal.webui.restful.mpt.commons.model.FamilyEntry
/*6.2*/import java.util.List

object createOperation extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[List[FamilyEntry],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*8.2*/(allFamilies: List[FamilyEntry]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*8.34*/("""

"""),_display_(/*10.2*/mainSkel()/*10.12*/ {_display_(Seq[Any](format.raw/*10.14*/("""
    """),format.raw/*11.5*/("""<div class="fx-main-content-wrapper mpt-wizard-page">
        <div class="fx-entity-header" data-na-portal-mpt-create-operation-breadcrumb>
            <div class="fx-entity-header-info">
                <span class="fx-entity-header-icon">
                    <i class="fuxicons fuxicons-mpt"></i>
                </span>
                <div class="fx-entity-header-title">
                    <h1>
                    """),_display_(/*19.22*/i18n("na.portal.mpt.createoperation.label.title")),format.raw/*19.71*/("""
                    """),format.raw/*20.21*/("""</h1>
                </div>
            </div>
        </div>
        <div class="fx-entity-info">
            <x-tab-container>
                <x-tab label=""""),_display_(/*26.32*/i18n("na.portal.mpt.createoperation.tab.label")),format.raw/*26.79*/("""">
                    <div class="tab-content fx-main-tab-content fx-full-height" data-template-loader data-ng-controller="NaPortalMptCommonController">
                    """),_display_(/*28.22*/{
                        operationWizard.render(allFamilies)
                    }),format.raw/*30.22*/("""
                    """),format.raw/*31.21*/("""</div>
                </x-tab>
            </x-tab-container>
        </div>
    </div>
""")))}))
      }
    }
  }

  def render(allFamilies:List[FamilyEntry]): play.twirl.api.HtmlFormat.Appendable = apply(allFamilies)

  def f:((List[FamilyEntry]) => play.twirl.api.HtmlFormat.Appendable) = (allFamilies) => apply(allFamilies)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:22 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/roots/createOperation.scala.html
                  HASH: ce94a96753c0992eb5b7230fc5eb9799bd78a4fb
                  MATRIX: 664->1|739->70|790->115|836->155|918->232|1265->256|1392->288|1421->291|1440->301|1480->303|1512->308|1961->730|2031->779|2080->800|2268->961|2336->1008|2538->1183|2642->1266|2691->1287
                  LINES: 24->1|25->2|26->3|27->4|28->6|33->8|38->8|40->10|40->10|40->10|41->11|49->19|49->19|50->20|56->26|56->26|58->28|60->30|61->31
                  -- GENERATED --
              */
          