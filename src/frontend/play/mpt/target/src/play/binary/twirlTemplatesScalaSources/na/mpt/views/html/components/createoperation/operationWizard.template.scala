
package na.mpt.views.html.components.createoperation

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import pt.ptinovacao.na.portal.webui.restful.mpt.commons.model.FamilyEntry
/*3.2*/import java.util
/*4.2*/import na.mpt.views.html.components.bulkWizard.characteristicsTab

object operationWizard extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[util.List[FamilyEntry],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*6.2*/(allFamilies : util.List[FamilyEntry]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*9.2*/initialContent/*9.16*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*9.20*/("""
"""),_display_(/*10.2*/characteristicsTab/*10.20*/.render(allFamilies)),format.raw/*10.40*/("""
""")))};
Seq[Any](format.raw/*6.40*/("""


"""),format.raw/*11.2*/("""

"""),format.raw/*13.1*/("""<div data-ng-controller="NaPortalMptCreateOperationController as createCtrl">
    <x-wizard>
        <x-wizard-step data-tab-name="""),_display_(/*15.39*/i18n("na.portal.mpt.createoperation.label.characteristics")),format.raw/*15.98*/(""">
            <div>
            """),_display_(/*17.14*/initialContent),format.raw/*17.28*/("""
            """),format.raw/*18.13*/("""</div>
        </x-wizard-step>
        <x-wizard-step data-tab-name="""),_display_(/*20.39*/i18n("na.portal.mpt.createoperation.label.files")),format.raw/*20.88*/("""></x-wizard-step>
        <x-wizard-step data-tab-name="""),_display_(/*21.39*/i18n("na.portal.mpt.createoperation.label.schedule")),format.raw/*21.91*/("""></x-wizard-step>
    </x-wizard>
</div>"""))
      }
    }
  }

  def render(allFamilies:util.List[FamilyEntry]): play.twirl.api.HtmlFormat.Appendable = apply(allFamilies)

  def f:((util.List[FamilyEntry]) => play.twirl.api.HtmlFormat.Appendable) = (allFamilies) => apply(allFamilies)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:33 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/components/createoperation/operationWizard.scala.html
                  HASH: 7072f4edc4cbf45818e6adf4797192740142c991
                  MATRIX: 685->1|731->41|813->117|837->135|1233->203|1349->245|1371->259|1451->263|1479->265|1506->283|1547->303|1588->241|1618->305|1647->307|1805->438|1885->497|1945->530|1980->544|2021->557|2118->627|2188->676|2271->732|2344->784
                  LINES: 24->1|25->2|26->3|27->4|32->6|36->9|36->9|38->9|39->10|39->10|39->10|41->6|44->11|46->13|48->15|48->15|50->17|50->17|51->18|53->20|53->20|54->21|54->21
                  -- GENERATED --
              */
          