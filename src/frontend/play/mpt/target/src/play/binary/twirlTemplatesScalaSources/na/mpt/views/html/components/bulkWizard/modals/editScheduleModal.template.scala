
package na.mpt.views.html.components.bulkWizard.modals

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.mpt.views.html.components.bulkWizard.modals.components.scheduleDateForm
/*2.2*/import na.mpt.views.html.components.bulkWizard.modals.components.schedulePeriod
/*3.2*/import na.mpt.views.html.components.bulkWizard.modals.components.scheduleIntervals

object editScheduleModal extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*5.1*/("""<div data-ng-controller="NaPortalMptCreateOperationSchedulesModalController as scheduleModalCtrl" id="schedule-modal">
    """),_display_(/*6.6*/scheduleDateForm/*6.22*/.render()),format.raw/*6.31*/("""
    """),_display_(/*7.6*/schedulePeriod/*7.20*/.render()),format.raw/*7.29*/("""
    """),_display_(/*8.6*/scheduleIntervals/*8.23*/.render()),format.raw/*8.32*/("""
"""),format.raw/*9.1*/("""</div>"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:23 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/components/bulkWizard/modals/editScheduleModal.scala.html
                  HASH: 7d8ebdc5740ae03440136ade7ebfa21f4d426797
                  MATRIX: 687->1|776->84|863->165|1344->249|1493->373|1517->389|1546->398|1577->404|1599->418|1628->427|1659->433|1684->450|1713->459|1740->460
                  LINES: 24->1|25->2|26->3|36->5|37->6|37->6|37->6|38->7|38->7|38->7|39->8|39->8|39->8|40->9
                  -- GENERATED --
              */
          