// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/conf/mpt.routes
// @DATE:Tue Jul 01 09:49:33 WEST 2025

import play.api.routing.JavaScriptReverseRoute


import _root_.controllers.Assets.Asset

// @LINE:2
package na.mpt.controllers.javascript {

  // @LINE:14
  class ReverseAssets(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:14
    def versioned: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.Assets.versioned",
      """
        function(file1) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/assets/" + (""" + implicitly[play.api.mvc.PathBindable[Asset]].javascriptUnbind + """)("file", file1)})
        }
      """
    )
  
  }

  // @LINE:33
  class ReverseBulks(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:34
    def launchBulk: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.Bulks.launchBulk",
      """
        function(id0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/bulks/launch/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("id", id0))})
        }
      """
    )
  
    // @LINE:36
    def suspendBulk: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.Bulks.suspendBulk",
      """
        function(id0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/bulks/suspend/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("id", id0))})
        }
      """
    )
  
    // @LINE:38
    def updateBulk: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.Bulks.updateBulk",
      """
        function() {
          return _wA({method:"PUT", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/bulks/update"})
        }
      """
    )
  
    // @LINE:35
    def stopBulk: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.Bulks.stopBulk",
      """
        function(id0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/bulks/stop/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("id", id0))})
        }
      """
    )
  
    // @LINE:33
    def BulksDatatableData: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.Bulks.BulksDatatableData",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/bulks/tabledata"})
        }
      """
    )
  
    // @LINE:37
    def bulkEdit: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.Bulks.bulkEdit",
      """
        function(bulkId0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/bulks/edit/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("bulkId", bulkId0))})
        }
      """
    )
  
  }

  // @LINE:49
  class ReverseResultDetails(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:49
    def getResultDetails: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.ResultDetails.getResultDetails",
      """
        function(operationId0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/results/details/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("operationId", operationId0))})
        }
      """
    )
  
  }

  // @LINE:41
  class ReverseBulkDetails(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:42
    def getBulkDetailsResultsTab: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.BulkDetails.getBulkDetailsResultsTab",
      """
        function(operationId0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/bulks/details/results/tab/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("operationId", operationId0))})
        }
      """
    )
  
    // @LINE:43
    def getBulkDetailsCharacteristicsTab: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.BulkDetails.getBulkDetailsCharacteristicsTab",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/bulks/details/bulk"})
        }
      """
    )
  
    // @LINE:45
    def getBulkOriginalFile: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.BulkDetails.getBulkOriginalFile",
      """
        function(bulkId0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/bulks/export/original/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("bulkId", bulkId0))})
        }
      """
    )
  
    // @LINE:44
    def getBulkResultFile: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.BulkDetails.getBulkResultFile",
      """
        function(bulkId0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/bulks/export/results/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("bulkId", bulkId0))})
        }
      """
    )
  
    // @LINE:41
    def getBulkDetails: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.BulkDetails.getBulkDetails",
      """
        function(bulkId0,tab1) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/bulks/details/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("bulkId", bulkId0)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("tab", tab1))})
        }
      """
    )
  
  }

  // @LINE:2
  class ReverseApplication(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:17
    def getSettings: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.Application.getSettings",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/settings"})
        }
      """
    )
  
    // @LINE:5
    def resume: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.Application.resume",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/resume"})
        }
      """
    )
  
    // @LINE:11
    def getMptModuleAngularScripts: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.Application.getMptModuleAngularScripts",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/imports/jsscripts"})
        }
      """
    )
  
    // @LINE:2
    def index: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.Application.index",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/home"})
        }
      """
    )
  
    // @LINE:8
    def javascriptRoutes: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.Application.javascriptRoutes",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/assets/javascripts/routes"})
        }
      """
    )
  
  }

  // @LINE:21
  class ReverseCreateOperation(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:29
    def loadOperationHeaders: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.CreateOperation.loadOperationHeaders",
      """
        function(id0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/operation/create/operation/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0)) + "/headers"})
        }
      """
    )
  
    // @LINE:22
    def loadFilesTab: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.CreateOperation.loadFilesTab",
      """
        function(context0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/operation/create/files" + _qS([(""" + implicitly[play.api.mvc.QueryStringBindable[String]].javascriptUnbind + """)("context", context0)])})
        }
      """
    )
  
    // @LINE:30
    def getEditScheduleModal: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.CreateOperation.getEditScheduleModal",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/operation/edit/modals/editschedule"})
        }
      """
    )
  
    // @LINE:25
    def validateSchedules: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.CreateOperation.validateSchedules",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/operation/create/validate"})
        }
      """
    )
  
    // @LINE:26
    def loadScheduleTab: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.CreateOperation.loadScheduleTab",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/operation/create/schedule"})
        }
      """
    )
  
    // @LINE:23
    def loadCharacteristicsTab: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.CreateOperation.loadCharacteristicsTab",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/operation/create/characteristics"})
        }
      """
    )
  
    // @LINE:21
    def loadCreateOperation: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.CreateOperation.loadCreateOperation",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/operation/create"})
        }
      """
    )
  
    // @LINE:24
    def createNormalOperation: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.CreateOperation.createNormalOperation",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/operation/create/uploadfiles"})
        }
      """
    )
  
    // @LINE:28
    def getCreateScheduleModal: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.CreateOperation.getCreateScheduleModal",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/operation/create/modals/createschedule"})
        }
      """
    )
  
    // @LINE:27
    def loadOperations: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.CreateOperation.loadOperations",
      """
        function(familyId0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/operation/create/families/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("familyId", familyId0))})
        }
      """
    )
  
  }

  // @LINE:63
  class ReverseTableConfigs(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:63
    def bulkSearch: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.TableConfigs.bulkSearch",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/table-configs/bulks-search"})
        }
      """
    )
  
  }

  // @LINE:48
  class ReverseBulkResults(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:48
    def resultsTableData: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.BulkResults.resultsTableData",
      """
        function(bulkId0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/results/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("bulkId", bulkId0)) + "/tabledata"})
        }
      """
    )
  
  }

  // @LINE:53
  class ReverseFilters(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:60
    def getFieldsForFiltername: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.Filters.getFieldsForFiltername",
      """
        function(filterName0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/filter/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("filterName", filterName0))})
        }
      """
    )
  
    // @LINE:59
    def getFilterNamesForSelect: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.Filters.getFilterNamesForSelect",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/filter"})
        }
      """
    )
  
    // @LINE:54
    def saveFilter: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.Filters.saveFilter",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/filter/persist"})
        }
      """
    )
  
    // @LINE:58
    def getDeleteFilterModal: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.Filters.getDeleteFilterModal",
      """
        function(filterName0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/filter/delete/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("filterName", filterName0))})
        }
      """
    )
  
    // @LINE:53
    def getSaveFilterModal: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.Filters.getSaveFilterModal",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/filter/save"})
        }
      """
    )
  
    // @LINE:56
    def editFilter: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.Filters.editFilter",
      """
        function(filterName0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/filter/update/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("filterName", filterName0))})
        }
      """
    )
  
    // @LINE:57
    def deleteFilter: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.Filters.deleteFilter",
      """
        function(filterName0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/filter/delete/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("filterName", filterName0))})
        }
      """
    )
  
    // @LINE:55
    def getEditFilterModal: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.mpt.controllers.Filters.getEditFilterModal",
      """
        function(filterName0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "mpt/filter/edit/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("filterName", filterName0))})
        }
      """
    )
  
  }


}
