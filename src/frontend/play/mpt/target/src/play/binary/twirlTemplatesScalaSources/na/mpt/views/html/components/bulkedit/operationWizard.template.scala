
package na.mpt.views.html.components.bulkedit

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import pt.ptinovacao.na.portal.webui.restful.mpt.commons.model.FamilyEntry
/*2.2*/import na.naportalbase.views.tags.i18n
/*3.2*/import java.util
/*4.2*/import na.mpt.views.html.components.bulkWizard.characteristicsTab

object operationWizard extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[util.List[FamilyEntry],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*6.2*/(allFamilies: util.List[FamilyEntry]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*8.2*/initialContent/*8.16*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*8.20*/("""
"""),_display_(/*9.2*/characteristicsTab/*9.20*/.render(allFamilies)),format.raw/*9.40*/("""
""")))};
Seq[Any](format.raw/*6.39*/("""

"""),format.raw/*10.2*/("""

"""),format.raw/*12.1*/("""<div data-ng-controller="NaPortalMptCreateOperationController as createCtrl">
    <x-wizard>
        <x-wizard-step class="open opened" data-tab-name="""),_display_(/*14.59*/i18n("na.portal.mpt.editbulk.label.characteristics")),format.raw/*14.111*/(""">
            <div>
            """),_display_(/*16.14*/initialContent),format.raw/*16.28*/("""
            """),format.raw/*17.13*/("""</div>
        </x-wizard-step>
        <x-wizard-step class="opened" data-tab-name="""),_display_(/*19.54*/i18n("na.portal.mpt.editbulk.label.files")),format.raw/*19.96*/("""></x-wizard-step>
        <x-wizard-step class="opened" data-tab-name="""),_display_(/*20.54*/i18n("na.portal.mpt.editbulk.label.schedule")),format.raw/*20.99*/("""></x-wizard-step>
    </x-wizard>
</div>


"""))
      }
    }
  }

  def render(allFamilies:util.List[FamilyEntry]): play.twirl.api.HtmlFormat.Appendable = apply(allFamilies)

  def f:((util.List[FamilyEntry]) => play.twirl.api.HtmlFormat.Appendable) = (allFamilies) => apply(allFamilies)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:33 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/components/bulkedit/operationWizard.scala.html
                  HASH: 59ac5c9021353c61812e6d9f718e39ff4bf84256
                  MATRIX: 678->1|760->77|806->117|830->135|1226->203|1341->243|1363->257|1443->261|1470->263|1496->281|1536->301|1577->240|1606->303|1635->305|1813->456|1887->508|1947->541|1982->555|2023->568|2135->653|2198->695|2296->766|2362->811
                  LINES: 24->1|25->2|26->3|27->4|32->6|36->8|36->8|38->8|39->9|39->9|39->9|41->6|43->10|45->12|47->14|47->14|49->16|49->16|50->17|52->19|52->19|53->20|53->20
                  -- GENERATED --
              */
          