
package na.mpt.views.html.components.bulkWizard

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import na.mpt.settings.MptConstants
/*3.2*/import play.filters.csrf.CSRF;
/*4.2*/import play.mvc.Http
/*5.2*/import scala.collection.mutable
/*6.2*/import na.naportalbase.views.html.skeletons.datatables.naDatatablesSkeleton
/*7.2*/import na.naportalbase.views.html.components.wizard.wizardContentComp

object schedulingTab extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*9.2*/headContent/*9.13*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*9.17*/("""
"""),format.raw/*10.1*/("""<p>
    <strong>
    """),_display_(/*12.6*/i18n("na.portal.mpt.createoperation.label.schedule.information")),format.raw/*12.70*/("""
    """),format.raw/*13.5*/("""</strong>
</p>
""")))};def /*36.2*/schedulingTable/*36.17*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*36.21*/("""
    """),format.raw/*37.5*/("""<div data-na-portal-datatables-toolbar class="fx-bulk-actions fx-table-actions clearfix"></div>
    """),_display_(/*38.6*/naDatatablesSkeleton/*38.26*/.render(schedulingtableattributes)),format.raw/*38.60*/("""
""")))};def /*41.2*/blockContent/*41.14*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*41.18*/("""
	"""),format.raw/*42.2*/("""<div class="form-group">
		<div id="scheduler-type" class="col-sm-offset-1 co-sm-11">
		    <input type="hidden" name="_token" id="csrf-token" value=""""),_display_(/*44.66*/token),format.raw/*44.71*/("""" />
		    <input id="radio-now" type="radio" name="rule-type" data-ng-change="createCtrl.scheduleSelection()" data-ng-model="createCtrl.operationData.scheduleType" value=""""),_display_(/*45.169*/MptConstants/*45.181*/.SCHEDULING_TYPE_IMMEDIATLY),format.raw/*45.208*/("""" checked>
		    <label for="radio-now">"""),_display_(/*46.31*/i18n("na.portal.mpt.createoperation.label.schedule.immediatly")),format.raw/*46.94*/("""</label><br/>

		    <input id="radio-later" type="radio" name="rule-type" data-ng-change="createCtrl.scheduleSelection()" data-ng-model="createCtrl.operationData.scheduleType" value=""""),_display_(/*48.171*/MptConstants/*48.183*/.SCHEDULING_TYPE_LATER),format.raw/*48.205*/("""">
		    <label for="radio-later">"""),_display_(/*49.33*/i18n("na.portal.mpt.createoperation.label.schedule.later")),format.raw/*49.91*/("""</label><br/>

		    <input id="radio-schedule" type="radio" name="rule-type" data-ng-change="createCtrl.scheduleSelection()" data-ng-model="createCtrl.operationData.scheduleType" value=""""),_display_(/*51.174*/MptConstants/*51.186*/.SCHEDULING_TYPE_SCHEDULING),format.raw/*51.213*/("""">
		    <label for="radio-schedule">"""),_display_(/*52.36*/i18n("na.portal.mpt.createoperation.label.schedule.scheduling")),format.raw/*52.99*/("""</label>
		</div>
	</div>
    <div class="scheduleOper_wiz" style="display: none;" data-na-portal-mpt-create-operation-control-table data-ng-controller="NaPortalMptSchedulingDatatableController" data-scheduling-type=""""),format.raw/*55.192*/("""{"""),format.raw/*55.193*/("""{"""),format.raw/*55.194*/("""createCtrl.operationData.scheduleType"""),format.raw/*55.231*/("""}"""),format.raw/*55.232*/("""}"""),format.raw/*55.233*/("""">
        """),_display_(/*56.10*/schedulingTable),format.raw/*56.25*/("""
    """),format.raw/*57.5*/("""</div>

""")))};def /*17.2*/schedulingtableattributes/*17.27*/ = {{

    var map = mutable.HashMap[String,String]()
    map.put("id","createOperationSchedulingDatatable")
    map.put("data-module","mpt")
    map.put("data-context","addSchedule")

    map
}};def /*27.2*/token/*27.7*/ = {{
	var token : CSRF.Token = CSRF.getToken(Http.Context.current()._requestHeader()).orNull
	if(token != null){
		token.value
	} else {
		""
	}
}};
Seq[Any](format.raw/*15.2*/("""

"""),format.raw/*25.2*/("""

"""),format.raw/*34.2*/("""

"""),format.raw/*39.2*/("""

"""),format.raw/*59.2*/("""

"""),_display_(/*61.2*/wizardContentComp/*61.19*/.render(headContent,blockContent)))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:33 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/components/bulkWizard/schedulingTab.scala.html
                  HASH: 611ffaeb3975af866e495bcc728bbae67c4ca4cd
                  MATRIX: 680->1|726->41|769->78|807->110|835->132|874->165|957->242|1405->314|1424->325|1504->329|1532->330|1580->352|1665->416|1697->421|1736->817|1760->832|1841->836|1873->841|2000->942|2029->962|2084->996|2109->1001|2130->1013|2211->1017|2240->1019|2418->1170|2444->1175|2645->1348|2667->1360|2716->1387|2784->1428|2868->1491|3081->1676|3103->1688|3147->1710|3209->1745|3288->1803|3504->1991|3526->2003|3575->2030|3640->2068|3724->2131|3970->2348|4000->2349|4030->2350|4096->2387|4126->2388|4156->2389|4195->2401|4231->2416|4263->2421|4295->440|4329->465|4537->662|4550->667|4727->437|4756->659|4785->814|4814->998|4843->2430|4872->2433|4898->2450
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|39->9|39->9|41->9|42->10|44->12|44->12|45->13|47->36|47->36|49->36|50->37|51->38|51->38|51->38|52->41|52->41|54->41|55->42|57->44|57->44|58->45|58->45|58->45|59->46|59->46|61->48|61->48|61->48|62->49|62->49|64->51|64->51|64->51|65->52|65->52|68->55|68->55|68->55|68->55|68->55|68->55|69->56|69->56|70->57|72->17|72->17|80->27|80->27|88->15|90->25|92->34|94->39|96->59|98->61|98->61
                  -- GENERATED --
              */
          