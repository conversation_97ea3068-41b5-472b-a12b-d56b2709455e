
package na.mpt.views.html.components.bulkWizard

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import scala.collection.mutable
/*3.2*/import na.naportalbase.views.html.components.wizard.wizardContentComp

object filesTab extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*5.2*/(context: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*14.6*/headContent/*14.17*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*14.21*/("""

        """),format.raw/*16.9*/("""<p>
            <strong>
            """),_display_(/*18.14*/i18n("na.portal.mpt.createoperation.label.selectfiles")),format.raw/*18.69*/("""
            """),format.raw/*19.13*/("""</strong>
        </p>
        <p><u>"""),_display_(/*21.16*/i18n("na.portal.mpt.createoperation.label.files.limit",maxFiles,maxFileSize)),format.raw/*21.92*/("""</u></p>
        <p>"""),_display_(/*22.13*/i18n("na.portal.mpt.createoperation.label.files.information")),format.raw/*22.74*/("""</p>
    """)))};def /*35.6*/blockContent/*35.18*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*35.22*/("""
        """),format.raw/*36.9*/("""<div class="form-horizontal">
            <ul id="sortFiles" class="list-unstyled """),_display_(/*37.54*/if(context == "create")/*37.77*/{_display_(Seq[Any](format.raw/*37.78*/("""ui-sortable""")))}),format.raw/*37.90*/("""">
                <li class="listFile mpt-file-item" ng-repeat="file in createCtrl.operationData.files">
                    <label ng-bind="file.name"></label>
                    <button type="button" """),_display_(/*40.44*/if(context == "create")/*40.67*/{_display_(Seq[Any](format.raw/*40.68*/("""ng-click="createCtrl.removeFile(file)"""")))}),_display_(/*40.108*/if(context == "edit")/*40.129*/{_display_(Seq[Any](format.raw/*40.130*/("""disabled""")))}),format.raw/*40.139*/(""" """),format.raw/*40.140*/("""class="btn fx-btn-action"><i class="glyphicon glyphicon-remove"></i></button>
                </li>
            </ul>
            <div class="fileUpload btn btn-primary fx-call-to-action pull-right">
                <i class="glyphicon glyphicon-plus"></i> """),_display_(/*44.59*/i18n("na.portal.mpt.buttons.addfile")),format.raw/*44.96*/("""
                """),format.raw/*45.17*/("""<input data-na-portal-mpt-set-input-mode
                data-na-portal-mpt-file-uploader
                data-number-of-files=""""),format.raw/*47.39*/("""{"""),format.raw/*47.40*/("""{"""),format.raw/*47.41*/("""createCtrl.operationData.files.length"""),format.raw/*47.78*/("""}"""),format.raw/*47.79*/("""}"""),format.raw/*47.80*/(""""
                file-model="files"
                type="file"
                class="upload"/> <!--multiple-->
            </div>
        </div>
    """)))};def /*7.6*/maxFiles/*7.14*/ = {{
        Html("{{createCtrl.maxFiles}}").toString()
    }};def /*10.6*/maxFileSize/*10.17*/ = {{
        Html("{{createCtrl.maxFileSize}}").toString()
    }};def /*25.6*/uploadButtonAttributes/*25.28*/ = {{
        var map = mutable.HashMap[String, String]()
        map.put("id", "uploadFileButton")
        map.put("class","btn btn-primary fx-call-to-action pull-right")
        map.put("role","button")
        map.put("type","file")

        map
    }};
Seq[Any](format.raw/*5.19*/("""

    """),format.raw/*9.6*/("""
    """),format.raw/*12.6*/("""

    """),format.raw/*23.6*/("""

    """),format.raw/*33.6*/("""

    """),format.raw/*53.6*/("""

    """),_display_(/*55.6*/wizardContentComp/*55.23*/.render(headContent,blockContent)))
      }
    }
  }

  def render(context:String): play.twirl.api.HtmlFormat.Appendable = apply(context)

  def f:((String) => play.twirl.api.HtmlFormat.Appendable) = (context) => apply(context)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:22 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/components/bulkWizard/filesTab.scala.html
                  HASH: d55d895c886df61b22ec0ee683773d3921100d3c
                  MATRIX: 680->1|726->41|765->74|1142->146|1238->329|1258->340|1339->344|1376->354|1441->392|1517->447|1558->460|1623->498|1720->574|1768->595|1850->656|1883->956|1904->968|1985->972|2021->981|2131->1064|2163->1087|2202->1088|2245->1100|2477->1305|2509->1328|2548->1329|2619->1369|2650->1390|2690->1391|2731->1400|2761->1401|3046->1659|3104->1696|3149->1713|3305->1841|3334->1842|3363->1843|3428->1880|3457->1881|3486->1882|3661->170|3677->178|3753->246|3773->257|3852->673|3883->695|4167->163|4199->240|4231->322|4264->666|4297->949|4330->2035|4363->2042|4389->2059
                  LINES: 24->1|25->2|26->3|31->5|35->14|35->14|37->14|39->16|41->18|41->18|42->19|44->21|44->21|45->22|45->22|46->35|46->35|48->35|49->36|50->37|50->37|50->37|50->37|53->40|53->40|53->40|53->40|53->40|53->40|53->40|53->40|57->44|57->44|58->45|60->47|60->47|60->47|60->47|60->47|60->47|66->7|66->7|68->10|68->10|70->25|70->25|79->5|81->9|82->12|84->23|86->33|88->53|90->55|90->55
                  -- GENERATED --
              */
          