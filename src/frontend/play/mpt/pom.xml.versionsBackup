<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>pt.ptinovacao.na-portal</groupId>
        <artifactId>na-portal-frontend-play</artifactId>
        <version>10.3.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>na-portal-frontend-mpt</artifactId>
    <packaging>jar</packaging>

    <properties>
        <skipGradleWrapper>true</skipGradleWrapper>
        <skipNpmTests>true</skipNpmTests>
    </properties>

    <!--
        These dependencies serve the purpose of quickly validating the frontend existing dependencies.
        It's important to note that the actual POM file that will be published to the Maven repository is generated by <PERSON><PERSON><PERSON>.
        You can find this POM file in the './target/publications/maven/pom-default.xml' directory.
        The dependencies listed in this POM file are derived from the generated POM file,
        which, in turn, is created based on the 'build.gradle' file where dependencies are explicitly declared.
        For consistency, it's essential to remember that whenever you add a dependency in Gradle,
        it must also be included in this POM file.
     -->
    <dependencies>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-frontend-basemodule</artifactId>
        </dependency>

        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-frontend-router</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-mpt-model</artifactId>
        </dependency>

        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-rest-commons</artifactId>
        </dependency>

        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-auditing-schemas</artifactId>
        </dependency>

        <dependency>
            <groupId>pt.ptinovacao.nossis-frmwrk</groupId>
            <artifactId>exceptions</artifactId>
        </dependency>

        <dependency>
            <groupId>pt.ptinovacao.nossis-ui</groupId>
            <artifactId>nossis-ui-security-adapter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.typesafe.play</groupId>
                    <artifactId>play-logback_${version.org.scala.base}</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>pt.ptinovacao.nossis-security</groupId>
            <artifactId>nossis-security-provider-iam</artifactId>
            <scope>runtime</scope>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.typesafe.play</groupId>
                    <artifactId>play-logback_${version.org.scala.base}</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
        </dependency>

        <dependency>
            <groupId>org.scala-lang.modules</groupId>
            <artifactId>scala-xml_${version.org.scala.base}</artifactId>
        </dependency>

        <dependency>
            <groupId>com.typesafe.akka</groupId>
            <artifactId>akka-actor_${version.org.scala.base}</artifactId>
        </dependency>

        <dependency>
            <groupId>com.typesafe.akka</groupId>
            <artifactId>akka-stream_${version.org.scala.base}</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.inject</groupId>
            <artifactId>javax.inject</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.inject</groupId>
            <artifactId>guice</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>com.typesafe</groupId>
            <artifactId>config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.typesafe.play</groupId>
            <artifactId>twirl-api_${version.org.scala.base}</artifactId>
        </dependency>

        <dependency>
            <groupId>com.typesafe.play</groupId>
            <artifactId>play-ws_${version.org.scala.base}</artifactId>
        </dependency>

        <dependency>
            <groupId>com.typesafe.play</groupId>
            <artifactId>play_${version.org.scala.base}</artifactId>
        </dependency>

        <dependency>
            <groupId>com.typesafe.play</groupId>
            <artifactId>filters-helpers_${version.org.scala.base}</artifactId>
        </dependency>

        <dependency>
            <groupId>com.typesafe.play</groupId>
            <artifactId>play-java_${version.org.scala.base}</artifactId>
        </dependency>

        <dependency>
            <groupId>com.typesafe.play</groupId>
            <artifactId>play-docs_${version.org.scala.base}</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mnode.ical4j</groupId>
            <artifactId>ical4j</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver-sync</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>bson</artifactId>
        </dependency>
    </dependencies>
</project>