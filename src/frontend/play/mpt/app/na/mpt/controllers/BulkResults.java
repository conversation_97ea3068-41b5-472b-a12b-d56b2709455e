package na.mpt.controllers;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import na.mpt.models.wrappers.OrderEntryWrapper;
import na.mpt.serializers.DatatablesSerializer;
import na.mpt.settings.AAAPIResources;
import na.mpt.settings.MptServiceSettings;
import na.mpt.utils.MptResponseMapper;
import na.mpt.utils.RestAPIUrlBuilder;
import na.naportalbase.services.ServicesHelper;
import na.naportalbase.services.ws.Request;
import na.naportalbase.services.ws.RequestBuilderFactory;
import na.naportalbase.settings.AAAPIAccess;
import na.naportalbase.settings.BaseConstants;
import na.naportalbase.utils.DatatablesUtils;
import na.naportalbase.utils.ODataBuilder;
import na.naportalbase.utils.RestAPIUtils;
import play.libs.concurrent.HttpExecutionContext;
import play.mvc.Controller;
import play.mvc.Result;
import pt.alticelabs.nossis.security.actions.Authenticated;
import pt.alticelabs.nossis.security.actions.Authorized;

import javax.inject.Inject;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletionStage;
import java.util.regex.Pattern;

import static na.mpt.settings.MptConstants.FORM_DATA_BULKS_BACKEND_BULKID;
import static na.mpt.settings.MptConstants.FORM_DATA_BULKS_RESULTS_ENDTIME;
import static na.mpt.settings.MptConstants.FORM_DATA_BULKS_RESULTS_EXECUTIONSTATE;
import static na.mpt.settings.MptConstants.FORM_DATA_BULKS_RESULTS_INCOLUMN;
import static na.mpt.settings.MptConstants.FORM_DATA_BULKS_RESULTS_OPK;
import static na.mpt.settings.MptConstants.FORM_DATA_BULKS_RESULTS_ORDERKEY;
import static na.mpt.settings.MptConstants.FORM_DATA_BULKS_RESULTS_OUTCOLUMN;
import static na.mpt.settings.MptConstants.FORM_DATA_BULKS_RESULTS_STARTTIME;
import static na.mpt.settings.MptConstants.FORM_DATA_BULKS_RESULTS_STATUS;
import static na.mpt.settings.MptConstants.MPT_RESULTS_SEARCH_CONTEXT;
import static na.naportalbase.utils.ODataBuilder.FilterOperation;

/**
 * Created by hugo-f-barrigas on 5/14/15.
 */
public class BulkResults extends Controller{

    private final ServicesHelper ws;
    private final HttpExecutionContext ec;
    private final DatatablesUtils datatablesUtils;
    private final MptServiceSettings mptServiceSettings;
    private final DatatablesSerializer datatablesSerializer;
    private final RequestBuilderFactory requestBuilderFactory;

    @Inject
    public BulkResults(ServicesHelper ws,
                       HttpExecutionContext ec,
                       DatatablesUtils datatablesUtils,
                       MptServiceSettings mptServiceSettings,
                       DatatablesSerializer datatablesSerializer,
                       RequestBuilderFactory requestBuilderFactory) {
        this.ws = ws;
        this.ec = ec;
        this.datatablesUtils = datatablesUtils;
        this.mptServiceSettings = mptServiceSettings;
        this.datatablesSerializer = datatablesSerializer;
        this.requestBuilderFactory = requestBuilderFactory;
    }

    @Authenticated
    @Authorized({AAAPIResources.OPERATIONS + AAAPIAccess.R})
    public CompletionStage<Result> resultsTableData(@NotNull final String bulkId){

        ObjectNode paramsPost = (ObjectNode)request().body().asJson().get(BaseConstants.DATATABLES_PARAMSPOST_PARAM);
        JsonNode jsonTableData = request().body().asJson().get(BaseConstants.DATATABLES_TABLEDATA_PARAM);

        Map<String, String> unprocessedTableParams = datatablesUtils.parseTableInfo(jsonTableData);
        final int draw = Integer.parseInt(unprocessedTableParams.get(BaseConstants.DATATABLES_INTERNAL_QUERY_PARAM_DRAW));
        unprocessedTableParams.remove(BaseConstants.DATATABLES_INTERNAL_QUERY_PARAM_DRAW);

        Request<OrderEntryWrapper> request = requestBuilderFactory.create()
                .setUrl(getResultsEndpoint())
                .responseAs(OrderEntryWrapper.class)
                .addQueryParameters(RestAPIUrlBuilder.processDatatableParams(unprocessedTableParams, buildODataFilter(RestAPIUtils.formToMap(paramsPost)), MPT_RESULTS_SEARCH_CONTEXT))
                .addQueryParameter(FORM_DATA_BULKS_BACKEND_BULKID, bulkId)
                .addQueryParameter(BaseConstants.DATATABLES_QUERY_PARAM_INLINECUNT, BaseConstants.DATATABLES_QUERY_PARAM_INLINECUNT_ALLPAGES)
                .setResponseMapper(new MptResponseMapper(MptResponseMapper.OperationType.search_bulks))
                .build();

        return ws.sendGet(request).thenApplyAsync(response -> {
            OrderEntryWrapper table = datatablesSerializer.serviceResponseToOrderEntryWrapper(response);
            ObjectNode object = datatablesUtils.genFormattedDataTableData(table.getEntries(), draw, table.getCount());
            return ok(object);
        }, ec.current());
    }


    private String getBaseEndpoint() {
        return mptServiceSettings.getBackendAddress() + "/na-portal-mpt-rest-interface"
                + BaseConstants.SERVICES_RESTFUL_SUFIX + BaseConstants.SERVICES_NAPORTAL_SUFIX + "/mpt";
    }

    private String getResultsEndpoint() {
        return getBaseEndpoint() + "/orders";
    }

    private static String buildODataFilter(Map<String, List<String>> filterMap) {
        ODataBuilder odata = new ODataBuilder();
        for (String field : filterMap.keySet()) {
            String value = filterMap.get(field).get(0);

            String fieldWrap = field;
            if (field.startsWith(FORM_DATA_BULKS_RESULTS_INCOLUMN)) {
                fieldWrap = FORM_DATA_BULKS_RESULTS_INCOLUMN;
            } else if (field.startsWith(FORM_DATA_BULKS_RESULTS_OUTCOLUMN)) {
                fieldWrap = FORM_DATA_BULKS_RESULTS_OUTCOLUMN;
            }

            // Add OData field filters
            switch (fieldWrap) {
                case FORM_DATA_BULKS_RESULTS_STARTTIME : {
                    odata.addDateField(field, value, FilterOperation.GREATER_EQUAL);
                    break;
                }
                case FORM_DATA_BULKS_RESULTS_ENDTIME : {
                    odata.addDateField(field, value, FilterOperation.LESSER_EQUAL);
                    break;
                }
                case FORM_DATA_BULKS_RESULTS_EXECUTIONSTATE :
                case FORM_DATA_BULKS_RESULTS_STATUS : {
                    // Devia ser "EQUAL" mas no backend, aparentemente devido ao uso de Enums no OGM só funciona com a seguinte marosca:
                    odata.addTextField(field, value, FilterOperation.STARTSWITH);
                    odata.addTextField(field, value, FilterOperation.ENDSWITH);
                    break;
                }
                case FORM_DATA_BULKS_RESULTS_ORDERKEY :
                case FORM_DATA_BULKS_RESULTS_OPK : {
                    odata.addTextFieldWithWildcard(fieldWrap, value);
                    break;
                }
                case FORM_DATA_BULKS_RESULTS_INCOLUMN :
                case FORM_DATA_BULKS_RESULTS_OUTCOLUMN : {

                    FilterOperation filterOperation = FilterOperation.EQUAL; // default operation
                    if (Pattern.compile("[\\*%]").matcher(value).find()) {
                        String defaultWildcard = "*";
                        value = value.replace("%", defaultWildcard); // simplify wildcards

                        if (value.startsWith(defaultWildcard)) {
                            if (value.endsWith(defaultWildcard)) {
                                // if starts and ends with a wildcard is a substring operation
                                filterOperation = FilterOperation.SUBSTRING;
                            } else {
                                // if starts with a wildcard is a endswith operation
                                filterOperation = FilterOperation.ENDSWITH;
                            }
                        } else if (value.endsWith(defaultWildcard)) {
                            filterOperation = FilterOperation.STARTSWITH;
                        }

                        value = value.replace(defaultWildcard, "");
                    }

                    odata.addTextField(fieldWrap, String.format("%s§§%s", field.substring(fieldWrap.length()), value), filterOperation);
                    break;
                }
            }
        }
        return odata.build();
    }

}
