package na.mpt.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import na.mpt.settings.MptConstants;
import na.mpt.settings.MptServiceSettings;
import na.naportalbase.exceptions.ExceptionCodes;
import na.naportalbase.utils.SecurityUtils;
import play.Logger;
import play.libs.Json;
import play.mvc.Http;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.entities.Request;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.entities.Schedule;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.model.BulkEntry;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.model.OperationEntry;
import pt.ptinovacao.nossis.exceptions.BusinessException;

import javax.inject.Inject;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static na.naportalbase.settings.BaseMappings.defaultObjectMapper;

/**
 * Created by hugo-f-barrigas on 3/23/15.
 */
public class MPTUtils {

    private static final Logger.ALogger LOGGER = Logger.of(MptConstants.LOGGER_NAME);
    private static final String MULTIPART_TEMP_FILE_REGEX = "^multipartBody(.*)TemporaryFile$";

    private final MptServiceSettings mptServiceSettings;

    @Inject
    public MPTUtils(MptServiceSettings mptServiceSettings) {
        this.mptServiceSettings = mptServiceSettings;
    }

    public ArrayNode buildOperationSelectData(List<OperationEntry> entries) {
        ArrayNode data = defaultObjectMapper.createArrayNode();
        for (OperationEntry entry : entries) {
            ObjectNode node = Json.newObject();
            node.put("id", entry.getId());
            node.put("text", entry.getName());
            node.put("value", entry.getId());
            data.add(node);
        }
        return data;
    }

    public Request buildCreateOperationRequest(Http.MultipartFormData<?> requestBody) {
        Map<String, String[]> urlEncoded = requestBody.asFormUrlEncoded();
        String[] data = urlEncoded.get(MptConstants.FORM_DATA_SCHEDULE_INFO);
        Set<Schedule> schedules = buildRequestSchedule(data.length > 0 ? data[0] : "");
        String family = urlEncoded.get(MptConstants.FORM_DATA_FAMILY_INFO).length > 0 ? urlEncoded.get(MptConstants.FORM_DATA_FAMILY_INFO)[0] : "";
        String operation = urlEncoded.get(MptConstants.FORM_DATA_OPERATION_INFO).length > 0 ? urlEncoded.get(MptConstants.FORM_DATA_OPERATION_INFO)[0] : "";
        String description = urlEncoded.get(MptConstants.FORM_DATA_DESCRIPTION_INFO).length > 0 ? urlEncoded.get(MptConstants.FORM_DATA_DESCRIPTION_INFO)[0] : "";
        String scheduleType = urlEncoded.get(MptConstants.FORM_DATA_SCHEDULE_TYPE).length > 0 ? urlEncoded.get(MptConstants.FORM_DATA_SCHEDULE_TYPE)[0] : "";

        if (family.isEmpty() || operation.isEmpty() || scheduleType.isEmpty()) {
            throw new BusinessException(ExceptionCodes.MALFORMED_REQUEST,
                    new Throwable("Missing fields on request! family - " + family + " operation - " + operation +
                            " scheduleType - " + scheduleType));
        }

        return OperationRequestBuilder
                .createSchedule(scheduleType)
                .withUsername(SecurityUtils.getUsername())
                .withDescription(description)
                .withDate(new Date())
                .withFamily(family)
                .withOperation(operation)
                .withSchedules(schedules)
                .build();
    }

    public Request buildUpdateBulkRequest(JsonNode requestBody) {
        Long bulkId = requestBody.get(MptConstants.FORM_DATA_BULKS_FRONTEND_BULKID).asLong();
        String description = requestBody.get(MptConstants.FORM_DATA_DESCRIPTION_INFO).asText();
        Set<Schedule> schedules = buildRequestSchedule(requestBody.get(MptConstants.FORM_DATA_SCHEDULE_INFO));
        String scheduleType = requestBody.get(MptConstants.FORM_DATA_SCHEDULE_TYPE).asText();

        return OperationRequestBuilder
                .updateSchedule(scheduleType)
                .withBulkId(bulkId)
                .withUsername(SecurityUtils.getUsername())
                .withDescription(description)
                .withSchedules(schedules)
                .build();
    }

    /**
     * Validates every FilePart regarding the number of lines declared in the configuration file.
     *
     * @param files
     */
    public void validateFiles(List<Http.MultipartFormData.FilePart<File>> files) {
        for (Http.MultipartFormData.FilePart<File> file : files) {
            if (!validateNumberOfLines(file.getFile())) {
                String fileName = file.getFilename();
                MPTUtils.deleteMultipartFiles(file);
                throw new BusinessException(MptExceptionCodes.INVALID_NUMBER_LINES_ERROR, new Throwable("Invalid file " + fileName + "! Number of lines exceed limit!"), "filename", fileName);
            }
        }
    }

    public Set<Schedule> buildRequestSchedule(JsonNode scheduleNode) {
        LOGGER.info("scheduleNode: " + scheduleNode);
        Set<Schedule> schedulesObject = new HashSet<>();
        Iterator<JsonNode> schedules = scheduleNode.elements();
        while (schedules.hasNext()) {
            JsonNode data = schedules.next();
            LOGGER.info("scheduleNodedata: " + data);

            Schedule scheduleObj = new Schedule();

            if (data.get(MptConstants.FORM_DATA_SCHEDULE_SCHEDULING_TYPE).asText().equals(MptConstants.SCHEDULING_TYPE_SCHEDULING_NOLIMIT)) {

                scheduleObj.setStartAtInMillis(data.get(MptConstants.FORM_DATA_SCHEDULE_INFO_STARTDATE).asLong());
                List<Schedule.Interval> intervalList = new ArrayList<>();
                Schedule.Interval intervalObj = new Schedule.Interval();
                intervalObj.setStartHourInMillis(data.get(MptConstants.FORM_DATA_SCHEDULE_INFO_INTERVALS_STARTHOUR).asLong());
                intervalObj.setDaysOfWeek(new HashSet<>());
                intervalList.add(intervalObj);
                scheduleObj.setIntervals(intervalList);
                schedulesObject.add(scheduleObj);

            } else if (data.get(MptConstants.FORM_DATA_SCHEDULE_SCHEDULING_TYPE).asText().equals(MptConstants.SCHEDULING_TYPE_SCHEDULING_INTERVALS)) {

                scheduleObj.setStartAtInMillis(data.get(MptConstants.FORM_DATA_SCHEDULE_INFO_STARTDATE).asLong());
                scheduleObj.setEndAtInMillis(data.get(MptConstants.FORM_DATA_SCHEDULE_INFO_ENDDATE).asLong());
                List<Schedule.Interval> intervalList = buildScheduleIntervals(data.get(MptConstants.FORM_DATA_SCHEDULE_INFO_INTERVALS));
                scheduleObj.setIntervals(intervalList);
                schedulesObject.add(scheduleObj);

            } else {
                throw new BusinessException(ExceptionCodes.MALFORMED_REQUEST, new Throwable("Unknown scheduling type!"));
            }
        }

        return schedulesObject;
    }

    private Set<Schedule> buildRequestSchedule(String scheduleData) {
        return buildRequestSchedule(Json.parse(scheduleData));
    }

    private Boolean validateNumberOfLines(File file) {
        int lineNumber = 0;
        boolean lastWasCR = false;
        try (FileInputStream fis = new FileInputStream(file);
             FileChannel fileChannel = fis.getChannel()) {

            long fileSize = fileChannel.size();
            MappedByteBuffer buffer = fileChannel.map(FileChannel.MapMode.READ_ONLY, 0, fileSize);
            for (int i = 0; i < fileSize; i++) {
                byte b = buffer.get(i);
                if (b == '\n' && !lastWasCR) {
                    lineNumber++;
                } else if (b == '\r') {
                    lineNumber++;
                    lastWasCR = true;
                } else if (lastWasCR) {
                    lastWasCR = false;
                }
            }

            buffer.clear();

            if (lineNumber > mptServiceSettings.getLineLimit()) {
                return false;
            }
        } catch (IOException ioe) {
            throw new BusinessException(MptExceptionCodes.FILE_ERROR_READING, ioe);
        }
        return true;
    }

    /**
     * Method that delete every file starting with "multipartBody" and ends with "TemporaryFile" in the FilePart directory.
     *
     * @param file
     */
    private static void deleteMultipartFiles(Http.MultipartFormData.FilePart file) {

        final File[] existingMultipartFiles = ((File) file.getFile()).getParentFile()
                .listFiles((dir, name) -> name.matches(MULTIPART_TEMP_FILE_REGEX));

        if (existingMultipartFiles != null) { //there are no multipartBody files
            for (File tempFile : existingMultipartFiles) {
                if (!tempFile.delete()) {
                    LOGGER.error("Unable to delete file - " + file.getFilename() + " from directory!");
                }
            }
        }
    }

    private static List<Schedule.Interval> buildScheduleIntervals(JsonNode intervals) {
        Iterator<JsonNode> intervalsIterator = intervals.elements();
        List<Schedule.Interval> intervalList = new ArrayList<>();
        while (intervalsIterator.hasNext()) {
            JsonNode interval = intervalsIterator.next();
            Schedule.Interval intervalObj = new Schedule.Interval();
            intervalObj.setStartHourInMillis(interval.get(MptConstants.FORM_DATA_SCHEDULE_INFO_INTERVALS_STARTHOUR).asLong());
            intervalObj.setDurationInMillis(interval.get(MptConstants.FORM_DATA_SCHEDULE_INFO_INTERVALS_DURATION).asLong());
            JsonNode weekDays = interval.get(MptConstants.FORM_DATA_SCHEDULE_INFO_INTERVALS_WEEKDAYS);
            Set<Integer> weekDaysObj = new HashSet<>();
            if (weekDays.isArray()) {
                for (final JsonNode objNode : weekDays) {
                    weekDaysObj.add(Integer.parseInt(objNode.asText()));
                }
            }
            intervalObj.setDaysOfWeek(weekDaysObj);
            intervalList.add(intervalObj);
        }
        return intervalList;
    }
}



class OperationRequestBuilder {
    Long bulkId;
    String description;
    String username;
    String scheduleType;
    Date date;
    BulkEntry.BulkActions action;
    Set<Schedule> schedules;
    String familyName;
    String operationName;

    private OperationRequestBuilder(BulkEntry.BulkActions action, String scheduleType) {
        this.action = action;
        this.scheduleType = scheduleType;
    }

    static OperationRequestBuilder createSchedule(String scheduleType) {
        return new OperationRequestBuilder(BulkEntry.BulkActions.CREATE, scheduleType);
    }

    static OperationRequestBuilder updateSchedule(String scheduleType) {
        return new OperationRequestBuilder(BulkEntry.BulkActions.EDIT, scheduleType);
    }

    OperationRequestBuilder withBulkId(Long bulkId) {
        this.bulkId = bulkId;
        return this;
    }

    OperationRequestBuilder withDescription(String description) {
        this.description = description;
        return this;
    }

    OperationRequestBuilder withUsername(String username) {
        this.username = username;
        return this;
    }

    OperationRequestBuilder withSchedules(Set<Schedule> schedules) {
        this.schedules = schedules;
        return this;
    }

    OperationRequestBuilder withDate(Date date) {
        this.date = date;
        return this;
    }

    OperationRequestBuilder withFamily(String familyName) {
        this.familyName = familyName;
        return this;
    }

    OperationRequestBuilder withOperation(String operationName) {
        this.operationName = operationName;
        return this;
    }

    public Request build(){
        Request request = new Request();
        request.setBulkId(bulkId);
        request.setUsername(username);
        request.setInputDescription(description);
        request.setAction(action);
        request.setDate(date);
        request.setOperationName(operationName);
        request.setFamilyName(familyName);

        switch (scheduleType) {
            case MptConstants.SCHEDULING_TYPE_IMMEDIATLY:
                request.setLaunch(true);
                break;
            case MptConstants.SCHEDULING_TYPE_LATER:
                request.setLaunch(false);
                break;
            case MptConstants.SCHEDULING_TYPE_SCHEDULING:
                request.setSchedules(schedules);
                break;
        }

        return request;
    }


}
