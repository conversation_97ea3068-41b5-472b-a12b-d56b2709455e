package na.mpt.dao;

import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import na.mpt.settings.MptConstants;
import na.mpt.settings.MptServiceSettings;
import org.bson.Document;

import javax.inject.Inject;
import java.util.Iterator;
import java.util.Map;

public class MongoDao {

    private static final play.Logger.ALogger LOGGER = play.Logger.of(MptConstants.LOGGER_NAME);

    private final MongoDatabase mongoDb;
    private final MptServiceSettings mptServiceSettings;

    @Inject
    public MongoDao(MptServiceSettings mptServiceSettings) {
        this.mptServiceSettings = mptServiceSettings;
        MongoClient client = MongoClients.create(mptServiceSettings.getMongoURI());
        this.mongoDb = client.getDatabase(mptServiceSettings.getMongoDatabaseForMptFiles());
    }

    public Iterator<Document> getAllByQuery(Map<String, Object> filter, Document projection) {
        MongoCollection<Document> mongoCollection = mongoDb.getCollection(mptServiceSettings.getMongoCollectionForMptFiles());

        Document filterDoc = (filter != null) ? new Document(filter) : new Document();

        FindIterable<Document> iterable = mongoCollection.find(filterDoc).projection(projection);
        iterable.noCursorTimeout(true);

        return iterable.iterator();

    }

    public long getAllByCount(Map<String, Object> filter) {
        long start = System.currentTimeMillis();
        MongoCollection<Document> mongoCollection = mongoDb.getCollection(mptServiceSettings.getMongoCollectionForMptFiles());

        Document filterDoc = (filter != null) ? new Document(filter) : new Document();

        LOGGER.debug("--- Query Init ---");
        long startQuery = System.currentTimeMillis();
        long size = mongoCollection.countDocuments(filterDoc);
        long endQuery = System.currentTimeMillis();
        LOGGER.debug("--- Query End ---");
        LOGGER.debug("Query took: " + (endQuery - startQuery));

        LOGGER.debug("Mongo getAll records " + size + " in " + (System.currentTimeMillis() - start) + " ms.");
        return size;
    }
}
