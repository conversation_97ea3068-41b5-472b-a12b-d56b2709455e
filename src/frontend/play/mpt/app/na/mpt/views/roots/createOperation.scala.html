@import na.mpt.views.html.components.createoperation.operationWizard
@import na.mpt.views.html.skeletons.mainSkel
@import na.naportalbase.views.tags.i18n
@import pt.ptinovacao.na.portal.webui.restful.mpt.commons.model.FamilyEntry

@import java.util.List

@(allFamilies: List[FamilyEntry])

@mainSkel() {
    <div class="fx-main-content-wrapper mpt-wizard-page">
        <div class="fx-entity-header" data-na-portal-mpt-create-operation-breadcrumb>
            <div class="fx-entity-header-info">
                <span class="fx-entity-header-icon">
                    <i class="fuxicons fuxicons-mpt"></i>
                </span>
                <div class="fx-entity-header-title">
                    <h1>
                    @i18n("na.portal.mpt.createoperation.label.title")
                    </h1>
                </div>
            </div>
        </div>
        <div class="fx-entity-info">
            <x-tab-container>
                <x-tab label="@i18n("na.portal.mpt.createoperation.tab.label")">
                    <div class="tab-content fx-main-tab-content fx-full-height" data-template-loader data-ng-controller="NaPortalMptCommonController">
                    @{
                        operationWizard.render(allFamilies)
                    }
                    </div>
                </x-tab>
            </x-tab-container>
        </div>
    </div>
}