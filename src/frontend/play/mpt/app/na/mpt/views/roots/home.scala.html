@import na.mpt.settings.MptServiceSettings
@import na.mpt.views.html.skeletons.mainSkel
@import na.naportalbase.views.tags.i18n
@import na.mpt.models.wrappers.FamilyWrapper
@import scala.collection.mutable
@import na.mpt.views.html.components.home.leftSideForm
@import na.mpt.controllers.routes.TableConfigs
@import na.naportalbase.views.html.components.lateralNavComp.lateralNavComp
@import na.mpt.settings.AAAPIResources
@import pt.alticelabs.nossis.security.views.html.authorized

@(states : List[String], families: FamilyWrapper, mptServiceSettings: MptServiceSettings)

<style>
#datatableMpt td>.progress {
    height: 12px;
    margin-top: 4px;
    margin-bottom: 0;
    background-color: #676566;
    -webkit-box-shadow: inset -1px 1px 5px -1px rgba(0,0,0,.25);
    -moz-box-shadow: inset -1px 1px 5px -1px rgba(0,0,0,.25);
    box-shadow: inset -1px 1px 5px -1px rgba(0,0,0,.25);
}

#datatableMpt .fx-table-actions>.btn.disabled>i {
color: #bbb;
}
</style>

@header = {
    <div class="fx-constraint two-rows">
        <div class="pull-left" data-na-portal-mpt-initial-breadcrumb>
            <span class="fx-info-icon">
                <i class="fuxicons fuxicons-mpt"></i>
            </span>
        </div>
        <div class="pull-left">
            <h1>
                @i18n( "na.portal.mpt.home.label.title")
            </h1>
            <p class="fx-entity-header-details">
                @i18n( "na.portal.mpt.home.label.subtitle")
            </p>
        </div>
    </div>
}

@suspendButtonAttributes = @{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-click", "headerCtrl.adminSuspendAll()")
    map.put("id", "adminSuspendButton")
    map.put("class","btn btn-default fx-call-to-action")
    map.put("role","button")
    map.put("style","margin-left:5px;")

    map
}

@suspendButtonValue = @{i18n( "na.portal.mpt.buttons.suspend")}

@suspendButtoniAttributes = @{
    var map = mutable.HashMap[String,String]()
    map
}

@navElements = @{
    var map = mutable.LinkedHashMap[String, mutable.HashMap[String,String]]()

    map
}

@leftSide = {
    @leftSideForm.render(states,families, mptServiceSettings)
}

@leftSideBarAttributes = @{
    var map = mutable.HashMap[String, String]()

    map.put("data-na-portal-mpt-bulks-search-page", "")

    map
}

@mpttableattributes = @{

    var map = mutable.HashMap[String,String]()
    map.put("id","datatableMpt")
    map.put("data-module","mpt")
    map.put("data-context","search")

    map
}

@rightSide = {
    <div class="fx-entity-header">
        <div class="fx-entity-header-info">
            <span class="fx-entity-header-icon">
                <i class="fuxicons fuxicons-mpt"></i>
            </span>
            <div class="fx-entity-header-title">
                <h1>
                @i18n( "na.portal.mpt.home.label.title")
                </h1>
                <p class="fx-entity-header-details">
                @i18n( "na.portal.mpt.home.label.subtitle")
                </p>
            </div>
        </div>
        @authorized(AAAPIResources.operations.X.toString()) {
            <div class="fx-entity-header-actions">
                <button id="create-button" class="btn btn-primary fx-call-to-action" data-page-action="create-operation">
                    <i class="glyphicon glyphicon-plus"></i>
                    @{
                        i18n("na.portal.mpt.buttons.createoperation")
                    }
                </button>
            </div>
        }
    </div>

    <x-shadow-scroll flex>
        <div id="entity-content" class="fx-entity-info">
            <div id="datatableMptBulk"
            data-na-portal-table-datatable
            data-na-portal-table-load-using-ajax
            data-na-portal-table-config-url="@TableConfigs.bulkSearch()"
            data-na-portal-mpt-bulks-search-table>
                <div data-na-portal-table-has-advanced-search></div>
                <div data-na-portal-table-has-column-filter></div>
            </div>
        </div>
    </x-shadow-scroll>

}

@mainSkel() {
    <div class="fx-push-footer .page--mpt-search-page fx-full-height">
        @if(navElements.size>1) {
            @lateralNavComp.render(navElements, "NaPortalMptNavigationController")
        }
        @*Later check if user is admin. If its not, we wont render the lateralNav and ommit the "fx-main-content-wraper fx-with-main-nav" div*@
        <div data-na-portal-mpt-bulks-dir data-ng-controller="NaPortalMptCommonController" class="fx-main-content-wrapper @{("fx-with-main-nav").when(navElements.size>1)}">
            <x-splitter class="splitter splitter--mpt-search">
                <form slot="left" class="search-sidebar" data-na-portal-mpt-bulks-search-page>
                @leftSide
                </form>
                <div id="fx-splitter-content" slot="right">
                @rightSide
                </div>
                <div slot="collapsed-left">
                    <div class="fx-info-sidebar-collapsed left">
                        <div class="fx-sidebar-header search-sidebar__header">
                            <span class="fx-entity-header-icon">
                                <i class="fa fa-search"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </x-splitter>
        </div>
    </div>
}

