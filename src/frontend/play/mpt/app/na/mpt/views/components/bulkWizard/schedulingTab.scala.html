@import na.naportalbase.views.tags.i18n
@import na.mpt.settings.MptConstants
@import play.filters.csrf.CSRF;
@import play.mvc.Http
@import scala.collection.mutable
@import na.naportalbase.views.html.skeletons.datatables.naDatatablesSkeleton
@import na.naportalbase.views.html.components.wizard.wizardContentComp

@headContent = {
<p>
    <strong>
    @i18n("na.portal.mpt.createoperation.label.schedule.information")
    </strong>
</p>
}

@schedulingtableattributes = @{

    var map = mutable.HashMap[String,String]()
    map.put("id","createOperationSchedulingDatatable")
    map.put("data-module","mpt")
    map.put("data-context","addSchedule")

    map
}

@token = @{
	var token : CSRF.Token = CSRF.getToken(Http.Context.current()._requestHeader()).orNull
	if(token != null){
		token.value
	} else {
		""
	}
}

@schedulingTable = {
    <div data-na-portal-datatables-toolbar class="fx-bulk-actions fx-table-actions clearfix"></div>
    @naDatatablesSkeleton.render(schedulingtableattributes)
}

@blockContent = {
	<div class="form-group">
		<div id="scheduler-type" class="col-sm-offset-1 co-sm-11">
		    <input type="hidden" name="_token" id="csrf-token" value="@token" />
		    <input id="radio-now" type="radio" name="rule-type" data-ng-change="createCtrl.scheduleSelection()" data-ng-model="createCtrl.operationData.scheduleType" value="@MptConstants.SCHEDULING_TYPE_IMMEDIATLY" checked>
		    <label for="radio-now">@i18n("na.portal.mpt.createoperation.label.schedule.immediatly")</label><br/>

		    <input id="radio-later" type="radio" name="rule-type" data-ng-change="createCtrl.scheduleSelection()" data-ng-model="createCtrl.operationData.scheduleType" value="@MptConstants.SCHEDULING_TYPE_LATER">
		    <label for="radio-later">@i18n("na.portal.mpt.createoperation.label.schedule.later")</label><br/>

		    <input id="radio-schedule" type="radio" name="rule-type" data-ng-change="createCtrl.scheduleSelection()" data-ng-model="createCtrl.operationData.scheduleType" value="@MptConstants.SCHEDULING_TYPE_SCHEDULING">
		    <label for="radio-schedule">@i18n("na.portal.mpt.createoperation.label.schedule.scheduling")</label>
		</div>
	</div>
    <div class="scheduleOper_wiz" style="display: none;" data-na-portal-mpt-create-operation-control-table data-ng-controller="NaPortalMptSchedulingDatatableController" data-scheduling-type="{{createCtrl.operationData.scheduleType}}">
        @schedulingTable
    </div>

}

@wizardContentComp.render(headContent,blockContent)