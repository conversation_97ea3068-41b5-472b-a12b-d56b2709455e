@import na.mpt.settings.MptConstants
@import na.mpt.views.helpers.BuildSelectData
@import na.naportalbase.views.tags.i18n
@import pt.ptinovacao.na.portal.webui.restful.mpt.commons.model.FamilyEntry
@import scala.collection.immutable
@import scala.collection.mutable
@import na.naportalbase.views.html.components.selectsComp.selectComp
@import java.util
@import na.naportalbase.views.html.components.selectsComp.selectInputComp
@import na.naportalbase.views.html.components.inputsComp.wizardInputTextAreaComp
@import na.naportalbase.views.html.components.wizard.wizardContentComp

@(allFamilies : util.List[FamilyEntry])

@headContent = {
    <p class="fx-required">
        <abbr title="@i18n("na.basemodule.validations.mandatory")"></abbr>
        @i18n("na.portal.mpt.messages.informative.mandatoryfields")
    </p>
    <p>
        <strong>
        @i18n("na.portal.mpt.createoperation.label.provisiontype")
        </strong>
    </p>
    <p>@i18n("na.portal.mpt.createoperation.label.operation.identification")</p>
}

    <!-- FAMILIES SELECT BOX-->
    @familySelectAttributes = @{
        var map = immutable.HashMap[String, String](
            "data-ng-model" -> "createCtrl.operationData.selectedFamily",
            "name" -> "selectFamilies",
            "data-na-portal-select-box" -> null,
            "value" -> "",
            "id" -> "selectFamilies",
            "class" -> "form-control input-sm",
            "data-context" -> MptConstants.SELECT_FAMILIES_CONTEXT,
            "data-unlocks-select" -> "operationSelect",
            "data-na-portal-mpt-highlight-mandatory" -> "null",
            "data-na-portal-mpt-set-input-mode" -> "null",
            "full-width" -> ""
        )

        map
    }
    <!-- END FAMILIES SELECT-->

    <!-- OPERATION SELECT -->
    @operationSelectAttributes = @{

        var map = mutable.HashMap[String, String]()
        map.put("class","input-sm fx-filter-input select2-offscreen")
        map.put("data-ng-model", "createCtrl.operationData.operationId")
        map.put("name", "operations")
        map.put("data-na-portal-select-box",null)
        map.put("value", "")
        map.put("id", "operationSelect")
        map.put("data-is-dynamic", null)
        map.put("data-context", MptConstants.SELECT_OPERATIONS_CONTEXT)
        map.put("data-disable-select", null)
        map.put("full-width","")
        map.put("data-na-portal-mpt-highlight-mandatory", null)
        map.put("data-na-portal-mpt-check-select-disable", null)
        map.put("data-select-locker-id", "selectFamilies")
        map.put("data-na-portal-mpt-set-input-mode", "null")
        map

    }

    @descriptionLabelAttributes = @{
        var map = mutable.HashMap[String,String]()
        map.put("class","col-sm-2 control-label fx-field")

        map
    }

    @descriptionInputAttributes = @{
        var map = mutable.HashMap[String,String]()
        map.put("id","operationDescriptionInput")
        map.put("rows","6")
        map.put("class","form-control")
        map.put("data-ng-model","createCtrl.operationData.description")
        map.put("placeholder",i18n("na.portal.mpt.createoperation.label.description"))
        map.put("name","description")

        map
    }

    <!-- END OPERATION SELECT-->
@blockContent = {
    <div class="form-group">
        <label for="selectStates" class="col-sm-2 control-label fx-required-field">@i18n( "na.portal.mpt.createoperation.label.family")</label>
        <div class="col-sm-10">
        @selectComp.render(familySelectAttributes, null,null,BuildSelectData.buildFamiliesData(allFamilies))
        </div>
    </div>
    <div class="form-group">
        <label for="s-oper" class="col-sm-2 control-label fx-required-field">@i18n( "na.portal.mpt.createoperation.label.operation")</label>
        <div class="col-sm-10 mpt-input">
            <div class="mpt-input__input">
            @selectInputComp.render(operationSelectAttributes)
            </div>
            <div class="fx-context-help mpt-input__info-block" data-na-portal-mpt-create-operation-help-tooltip data-na-portal-mpt-display-mode></div>
        </div>
    </div>
    <div class="form-group">
    @wizardInputTextAreaComp.render(i18n("na.portal.mpt.createoperation.label.description"),"operationDescriptionInput",
        descriptionInputAttributes, null, descriptionLabelAttributes)
    </div>
}

@wizardContentComp.render(headContent,blockContent)