@import na.mpt.views.html.components.bulkWizard.modals.components.scheduleDateForm
@import na.mpt.views.html.components.bulkWizard.modals.components.schedulePeriod
@import na.mpt.views.html.components.bulkWizard.modals.components.scheduleIntervals

<div data-ng-controller="NaPortalMptCreateOperationSchedulesModalController as scheduleModalCtrl" id="schedule-modal">
    @scheduleDateForm.render()
    @schedulePeriod.render()
    @scheduleIntervals.render()
</div>