@import na.mpt.settings.MptServiceSettings
@import na.mpt.models.wrappers.FamilyWrapper
@import na.naportalbase.views.tags.i18n
@import na.naportalbase.views.html.components.selectsComp.dynamicSelectOptionComp
@import na.mpt.settings.MptConstants
@import java.util
@import scala.collection.mutable
@import scala.collection.immutable
@import na.naportalbase.views.html.components.selectsComp.selectComp
@import na.naportalbase.views.html.components.inputsComp.modalInputTextComp
@import na.naportalbase.views.html.skeletons.search.horizontalFormSkel

@(filterName: String, states : util.List[String], families: FamilyWrapper, mptServiceSettings: MptServiceSettings)

<script>
    $.fn.modal.Constructor.prototype.enforceFocus = function() {};
</script>

<!-- Filter Start Date -->
@filterStartDateLabel = @{i18n("na.portal.mpt.field.startdate.label")}
@datePickerAttributes = @{
    var map = mutable.HashMap[String, String]()
    map.put("id", "filterStartDatePicker")
    map.put("data-date-time-picker", null)
    map.put("data-date-time-picker-input", null)
    map.put("data-max-date","today")
    map
}

@inputAttributes = @{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-model", "editFilterCtrl.tempSearchFormItems.startDate")
    map.put("data-datapickerfield", null)
    map.put("name", "editModalStartDate")
    map.put("id", "editModalStartDate")
    map
}

<!-- Filter End Date -->
@filterEndDateLabel = @{i18n("na.portal.mpt.field.enddate.label")}
@endDatePickerAttributes = @{
    var map = mutable.HashMap[String, String]()
    map.put("id", "filterEndDatePicker")
    map.put("data-date-time-picker", null)
    map.put("data-date-time-picker-input", null)
    map.put("data-max-date","today")
    map
}
@endDateinputAttributes = @{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-model", "editFilterCtrl.tempSearchFormItems.endDate")
    map.put("data-datapickerfield", null)
    map.put("name", "editModalEndDate")
    map.put("id", "editModalEndDate")
    map
}

<!-- USERNAME FIELD -->
@filterUsernameLabel = @{i18n( "na.portal.mpt.field.user.label")}
@filterUsernamePlaceHolder = @{i18n( "na.portal.mpt.field.user.placeholder")}
@filterUsernameInputAttributes = @{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-model", "editFilterCtrl.tempSearchFormItems.username")
    map.put("name", "editModalUsername")
    map.put("id", "editModalUsername")
    map
}

<!-- FAMILIES SELECT BOX -->
@familySelectAttributes = @{
    var map = immutable.HashMap[String, String](
        "data-ng-model" -> "editFilterCtrl.tempSearchFormItems.familyName",
        "name" -> "modalFamily",
        "data-na-portal-select-box" -> null,
        "placeholder" -> i18n("na.portal.mpt.selects.option.allfamilies"),
        "value" -> "",
        "id" -> "modalFamily",
        "class" -> "form-control input-sm",
        "data-context" -> MptConstants.SELECT_FAMILIES_CONTEXT,
        "allow-clear" -> null
    )
    map
}

@familySelectData = @{
    var stringBuilder = new StringBuilder
    for(family <- families.getEntries){
        var optionAttributes = immutable.HashMap[String,String](
            "id" -> family.getName,
            "text" -> family.getName,
            "value" -> family.getName
        )
        stringBuilder.append(dynamicSelectOptionComp.render(family.getName,optionAttributes))
    }
    Html(stringBuilder.toString())
}

<!-- OPERATION FIELD -->
@filterOperationLabel = @{i18n( "na.portal.mpt.field.operation.label")}
@filterOperationPlaceHolder = @{i18n( "na.portal.mpt.field.operation.label")}
@filterOperationInputAttributes = @{
    var map = mutable.HashMap[String,String]()
    map.put("data-ng-model", "editFilterCtrl.tempSearchFormItems.operationName")
    map.put("name", "modalOperation")
    map.put("id", "modalOperation")
    map
}

<!-- FILTERNAME FIELD -->
@filterFilterNameLabel = @{i18n( "na.portal.mpt.field.filtername.label")}
@filterFilterNamePlaceHolder = @{i18n( "na.portal.mpt.field.filtername.placeholder")}
@filterFilterNameInputAttributes = @{
    var map = mutable.HashMap[String,String]()
    map.put("data-na-portal-required",null)
    map.put("data-ng-model", "editFilterCtrl.tempSearchFormItems.filterName")
    map.put("name", "modalFilterName")
    map.put("id", "modalFilterName")
    map.put("maxlength", "64")
    map.put("disabled", "true")
    map
}

<!-- FILENAME FIELD -->
@filterFileLabel = @{i18n( "na.portal.mpt.field.file.label")}
@filterFilePlaceHolder = @{i18n( "na.portal.mpt.field.file.placeholder")}
@filterFileInputAttributes = @{
    var map = mutable.HashMap[String,String]()
    map.put("data-ng-model", "editFilterCtrl.tempSearchFormItems.filename")
    map.put("name", "modalFileName")
    map.put("id", "modalFileName")
    map
}

<!-- STATES KEY FIELD -->
@stateSelectAttributes = @{
    var map = immutable.HashMap[String, String](
        "data-ng-model" -> "editFilterCtrl.tempSearchFormItems.state",
        "name" -> "modalStates",
        "data-na-portal-select-box" -> null,
        "value" -> "",
        "placeholder" -> i18n("na.portal.mpt.selects.states.option.allstates"),
        "id" -> "modalStates",
        "class" -> "form-control input-sm",
        "allow-clear" -> null
    )
    map
}

@selectData = @{
    var stringBuilder = new StringBuilder

    for(entry <- states){
        var optionAttributes = immutable.HashMap[String,String](
            "id" -> entry,
            "text" -> i18n("na.portal.mpt.bulks.states."+mptServiceSettings.getBulkState(entry)),
            "value" -> entry
        )
        stringBuilder.append(dynamicSelectOptionComp.render(i18n("na.portal.mpt.bulks.states."+mptServiceSettings.getBulkState(entry)),optionAttributes))
    }

    Html(stringBuilder.toString())
}

@formAttributes = @{
    var map = mutable.HashMap[String,String]()
    map.put("data-ng-controller","NaPortalMptBulksEditFilterModalController as editFilterCtrl")
    map
}

@filterSelectInitialOptionAttributes = @{
    var map = immutable.HashMap[String, String](
        "value" -> filterName
    )
    map
}

@form = {
    <div class="form-group" data-na-portal-field-notification>
        @modalInputTextComp.render(filterFilterNameLabel, "filterName",filterFilterNamePlaceHolder,filterFilterNameInputAttributes)
    </div>

    <div class="fx-expand">
        @i18n("na.modals.filters.options")
    </div>

    <div class="clearfix">
        <div class="form-group" data-na-portal-field-notification data-na-portal-button-notification>
            <label class="control-label col-sm-3" for="filterStartDatePicker">@{i18n("na.portal.mpt.field.startdate.label")}</label>
            <x-date-time-picker class="col-sm-9" data-field="startDate" format="short"></x-date-time-picker>
        </div>

        <div class="form-group" data-na-portal-field-notification data-na-portal-button-notification>
            <label class="control-label col-sm-3" for="filterEndDatePicker">@{i18n("na.portal.mpt.field.enddate.label")}</label>
            <x-date-time-picker class="col-sm-9" data-field="endDate" format="short"></x-date-time-picker>
        </div>

        <div class="form-group">
            @modalInputTextComp.render(filterUsernameLabel, "modalUsername",filterUsernamePlaceHolder,filterUsernameInputAttributes)
        </div>

        <div class="form-group">
            <label for="modalStates" class="control-label col-sm-3">@i18n("na.portal.mpt.field.family.label")</label>
            <div class="col-sm-9">
                @selectComp.render(familySelectAttributes, null, null, familySelectData)
            </div>
        </div>

        <div class="form-group">
            @modalInputTextComp.render(filterOperationLabel, "modalOperation",filterOperationPlaceHolder,filterOperationInputAttributes)
        </div>

        <div class="form-group">
            @modalInputTextComp.render(filterFileLabel,"modalFileName" ,filterFilePlaceHolder,filterFileInputAttributes)
        </div>

        <div class="form-group">
            <label for="modalStates" class="control-label col-sm-3">@i18n( "na.portal.mpt.field.state.label")</label>
            <div class="col-sm-9">
                @selectComp.render(stateSelectAttributes, null, null, selectData)
            </div>
        </div>
    </div>
}

@horizontalFormSkel.render(formAttributes,form)
