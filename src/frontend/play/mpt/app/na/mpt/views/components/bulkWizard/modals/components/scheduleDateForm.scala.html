@import na.naportalbase.views.tags.i18n
@import na.mpt.settings.MptConstants

<div class="form-group mpt-wizard-modal__date-form">

    <div class="col-sm-6">
        <label class="control-label date-form__label col-sm-2 fx-required" for="startDate">
        @i18n("na.portal.mpt.createoperation.modals.scheduling.startDate")<abbr title="@i18n("na.basemodule.validations.mandatory")"></abbr>
        </label>
        <div class="col-sm-10">
                <x-date-time-picker
                class="schedule-date-form__date-time-picker"
                auto-select="start day"
                id="startDate"
                name="startDate"></x-date-time-picker>
        </div>
    </div>

    <div class="col-sm-6">
        <label id="schMod_end_label" class="control-label date-form__label col-sm-2 fx-required">
        @i18n("na.portal.mpt.createoperation.modals.scheduling.endDate")<abbr title="@i18n("na.basemodule.validations.mandatory")"></abbr>
        </label>
        <div id="schedule-term-type" class="col-sm-10">
            <div class="mpt-wizard-modal__date-input-option">
                <input
                id="term-type-@MptConstants.SCHEDULING_TYPE_SCHEDULING_INTERVALS"
                type="radio"
                name="term-type"
                checked
                value="@MptConstants.SCHEDULING_TYPE_SCHEDULING_INTERVALS">
                <label style="font-weight: normal" for="term-type-@MptConstants.SCHEDULING_TYPE_SCHEDULING_INTERVALS">@i18n("na.portal.mpt.createoperation.modals.scheduling.endBy")</label>
                <x-date-time-picker
                class="schedule-date-form__date-time-picker"
                id="endDate"
                name="endDate"
                auto-select="end day"></x-date-time-picker>
            </div>
            <div class="mpt-wizard-modal__date-input-option">
                <input id="term-type-@MptConstants.SCHEDULING_TYPE_SCHEDULING_NOLIMIT"
                type="radio"
                name="term-type"
                value="@MptConstants.SCHEDULING_TYPE_SCHEDULING_NOLIMIT">
                <label id="noLimitLabel" for="term-type-@MptConstants.SCHEDULING_TYPE_SCHEDULING_NOLIMIT">@i18n("na.portal.mpt.createoperation.modals.scheduling.noLimit")</label>
            </div>
        </div>
    </div>
</div>