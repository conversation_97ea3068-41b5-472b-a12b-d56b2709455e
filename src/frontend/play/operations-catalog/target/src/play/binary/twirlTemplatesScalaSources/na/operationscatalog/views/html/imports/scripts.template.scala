
package na.operationscatalog.views.html.imports

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object scripts extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.1*/("""<script src=""""),_display_(/*1.15*/na/*1.17*/.operationscatalog.controllers.routes.Application.javascriptRoutes()),format.raw/*1.85*/(""""></script>

    <script src='"""),_display_(/*3.19*/na/*3.21*/.operationscatalog.controllers.routes.Assets.versioned("basemodule/runtime.js")),format.raw/*3.100*/("""'></script>

    <script src='"""),_display_(/*5.19*/na/*5.21*/.operationscatalog.controllers.routes.Assets.versioned("na-portal-assets/base-styles.js")),format.raw/*5.110*/("""'></script>

    <script src='"""),_display_(/*7.19*/na/*7.21*/.operationscatalog.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-bootstrap.js")),format.raw/*7.124*/("""'></script>

    <script src='"""),_display_(/*9.19*/na/*9.21*/.operationscatalog.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-selects.js")),format.raw/*9.122*/("""'></script>

    <script src='"""),_display_(/*11.19*/na/*11.21*/.operationscatalog.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-std-utils.js")),format.raw/*11.124*/("""'></script>

    <script src='"""),_display_(/*13.19*/na/*13.21*/.operationscatalog.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-tables.js")),format.raw/*13.121*/("""'></script>

    <script src='"""),_display_(/*15.19*/na/*15.21*/.operationscatalog.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-jquery.js")),format.raw/*15.121*/("""'></script>

    <script src='"""),_display_(/*17.19*/na/*17.21*/.operationscatalog.controllers.routes.Assets.versioned("na-portal-assets-vendors/common.js")),format.raw/*17.113*/("""'></script>

    <script src='"""),_display_(/*19.19*/na/*19.21*/.operationscatalog.controllers.routes.Assets.versioned("utils/utils.js")),format.raw/*19.93*/("""'></script>

    <script src='"""),_display_(/*21.19*/na/*21.21*/.operationscatalog.controllers.routes.Assets.versioned("basemodule/basemodule.js")),format.raw/*21.103*/("""'></script>

    <script src='"""),_display_(/*23.19*/na/*23.21*/.operationscatalog.controllers.routes.Assets.versioned("catalog-basemodule/catalog-basemodule.js")),format.raw/*23.119*/("""'></script>

    <script src='"""),_display_(/*25.19*/na/*25.21*/.operationscatalog.controllers.routes.Assets.versioned("operations-catalog/operations-catalog.js")),format.raw/*25.119*/("""'></script>
"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:51 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/target/TwirlSource/na/operationscatalog/views/imports/scripts.scala.html
                  HASH: 88471cae347a948f19b6116f291f00516643f87a
                  MATRIX: 1061->0|1101->14|1111->16|1199->84|1256->115|1266->117|1366->196|1423->227|1433->229|1543->318|1600->349|1610->351|1734->454|1791->485|1801->487|1923->588|1981->619|1992->621|2117->724|2175->755|2186->757|2308->857|2366->888|2377->890|2499->990|2557->1021|2568->1023|2682->1115|2740->1146|2751->1148|2844->1220|2902->1251|2913->1253|3017->1335|3075->1366|3086->1368|3206->1466|3264->1497|3275->1499|3395->1597
                  LINES: 33->1|33->1|33->1|33->1|35->3|35->3|35->3|37->5|37->5|37->5|39->7|39->7|39->7|41->9|41->9|41->9|43->11|43->11|43->11|45->13|45->13|45->13|47->15|47->15|47->15|49->17|49->17|49->17|51->19|51->19|51->19|53->21|53->21|53->21|55->23|55->23|55->23|57->25|57->25|57->25
                  -- GENERATED --
              */
          