
package na.operationscatalog.views.html.imports

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object scripts extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.1*/("""<script src=""""),_display_(/*1.15*/na/*1.17*/.operationscatalog.controllers.routes.Application.javascriptRoutes()),format.raw/*1.85*/(""""></script><script src='"""),_display_(/*1.110*/na/*1.112*/.operationscatalog.controllers.routes.Assets.versioned("basemodule/runtime.ef4cdaceb8028d495c4b.min.js")),format.raw/*1.216*/("""'></script><script src='"""),_display_(/*1.241*/na/*1.243*/.operationscatalog.controllers.routes.Assets.versioned("na-portal-assets/base-styles.9e693254edb056a100ad.min.js")),format.raw/*1.357*/("""'></script><script src='"""),_display_(/*1.382*/na/*1.384*/.operationscatalog.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-bootstrap.6f86c32db7bf4ccaf010.min.js")),format.raw/*1.512*/("""'></script><script src='"""),_display_(/*1.537*/na/*1.539*/.operationscatalog.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-selects.c845a1da27d2bbccf773.min.js")),format.raw/*1.665*/("""'></script><script src='"""),_display_(/*1.690*/na/*1.692*/.operationscatalog.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-std-utils.ab8ed0c92f51f5eb905e.min.js")),format.raw/*1.820*/("""'></script><script src='"""),_display_(/*1.845*/na/*1.847*/.operationscatalog.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-tables.bc173ec447d65947d31a.min.js")),format.raw/*1.972*/("""'></script><script src='"""),_display_(/*1.997*/na/*1.999*/.operationscatalog.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-jquery.c69a6ab040df2ef632e6.min.js")),format.raw/*1.1124*/("""'></script><script src='"""),_display_(/*1.1149*/na/*1.1151*/.operationscatalog.controllers.routes.Assets.versioned("na-portal-assets-vendors/common.a53001b54dbc3b115cc4.min.js")),format.raw/*1.1268*/("""'></script><script src='"""),_display_(/*1.1293*/na/*1.1295*/.operationscatalog.controllers.routes.Assets.versioned("utils/utils.88e56ee512f1a3af972d.min.js")),format.raw/*1.1392*/("""'></script><script src='"""),_display_(/*1.1417*/na/*1.1419*/.operationscatalog.controllers.routes.Assets.versioned("basemodule/basemodule.823a1b10758c828389cd.min.js")),format.raw/*1.1526*/("""'></script><script src='"""),_display_(/*1.1551*/na/*1.1553*/.operationscatalog.controllers.routes.Assets.versioned("catalog-basemodule/catalog-basemodule.fab1781c4660b12ad1d0.min.js")),format.raw/*1.1676*/("""'></script><script src='"""),_display_(/*1.1701*/na/*1.1703*/.operationscatalog.controllers.routes.Assets.versioned("operations-catalog/operations-catalog.950e5314c81a59dd1cfd.min.js")),format.raw/*1.1826*/("""'></script>"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:43 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/target/TwirlSource/na/operationscatalog/views/imports/scripts.scala.html
                  HASH: 2c9aae54a63ef2d397d1ba29cc862c5dc9a413ed
                  MATRIX: 1061->0|1101->14|1111->16|1199->84|1251->109|1262->111|1387->215|1439->240|1450->242|1585->356|1637->381|1648->383|1797->511|1849->536|1860->538|2007->664|2059->689|2070->691|2219->819|2271->844|2282->846|2428->971|2480->996|2491->998|2638->1123|2691->1148|2703->1150|2842->1267|2895->1292|2907->1294|3026->1391|3079->1416|3091->1418|3220->1525|3273->1550|3285->1552|3430->1675|3483->1700|3495->1702|3640->1825
                  LINES: 33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1
                  -- GENERATED --
              */
          