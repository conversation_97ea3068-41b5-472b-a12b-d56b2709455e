
package na.operationscatalog.views.html.common

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.operationscatalog.settings.OperationsCatalogConstants
/*2.2*/import na.naportalbase.models.lateralNav.LateralNavigationComponentItem

object lateralNav extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*4.2*/(activeContext: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*6.2*/navElements/*6.13*/ = {{

    val map = scala.collection.mutable.LinkedHashMap[String, LateralNavigationComponentItem]()

    var versionNavElementAttributes = new LateralNavigationComponentItem()
          .setClickAction("version-home")
          .setTabIcon("fuxicons fuxicons-catalogue")

    var cfsNavElementAttributes = new LateralNavigationComponentItem()
            .setClickAction("cfs-home")
            .setTabIcon("fuxicons fuxicons-client-services")

    var rfsNavElementAttributes = new LateralNavigationComponentItem()
            .setClickAction("rfs-home")
            .setTabIcon("fuxicons fuxicons-network-configuration")

    var resourceNavElementAttributes = new LateralNavigationComponentItem()
            .setClickAction("resource-home")
            .setTabIcon("fuxicons fuxicons-resource")

    var responseCodeNavElementAttributes = new LateralNavigationComponentItem()
            .setClickAction("response-code-home")
            .setTabIcon("fa fa-comments")


    activeContext match {
        case OperationsCatalogConstants.VERSION_CONTEXT => versionNavElementAttributes.setActive(true)
        case OperationsCatalogConstants.CFS_CONTEXT => cfsNavElementAttributes.setActive(true)
        case OperationsCatalogConstants.RFS_CONTEXT => rfsNavElementAttributes.setActive(true)
        case OperationsCatalogConstants.RESOURCE_CONTEXT => resourceNavElementAttributes.setActive(true)
        case OperationsCatalogConstants.RESPONSE_CODE_CONTEXT => responseCodeNavElementAttributes.setActive(true)
    }

    map.put("na.portal.operationscatalog.nav.version", versionNavElementAttributes)
    map.put("na.portal.operationscatalog.nav.cfs", cfsNavElementAttributes)
    map.put("na.portal.operationscatalog.nav.rfs", rfsNavElementAttributes)
    map.put("na.portal.operationscatalog.nav.resource", resourceNavElementAttributes)
    map.put("na.portal.operationscatalog.nav.responseCode", responseCodeNavElementAttributes)

    map
}};
Seq[Any](format.raw/*4.25*/("""

"""),format.raw/*46.2*/("""

"""),_display_(/*48.2*/na/*48.4*/.naportalbase.views.html.components.lateralNavComp.lateralNavigationComp.render(navElements, "data-na-portal-operations-catalog-navigation-bar")))
      }
    }
  }

  def render(activeContext:String): play.twirl.api.HtmlFormat.Appendable = apply(activeContext)

  def f:((String) => play.twirl.api.HtmlFormat.Appendable) = (activeContext) => apply(activeContext)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:43 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/target/TwirlSource/na/operationscatalog/views/common/lateralNav.scala.html
                  HASH: 3b9a431cdd237eaaa72a75677e17cc85a89daaed
                  MATRIX: 679->1|750->66|1131->140|1232->166|1251->177|3228->163|3257->2124|3286->2127|3296->2129
                  LINES: 24->1|25->2|30->4|34->6|34->6|75->4|77->46|79->48|79->48
                  -- GENERATED --
              */
          