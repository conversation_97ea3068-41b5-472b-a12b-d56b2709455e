
package na.operationscatalog.views.html.resource

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.operationscatalog.services.OperationPermissionService

object view extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template5[Long,List[na.operationscatalog.models.statemachine.State],na.operationscatalog.models.operations.Operation,List[na.catalog.basemodule.ui.WebComponent],OperationPermissionService,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*3.2*/(
        operationId: Long,
        stateList: List[na.operationscatalog.models.statemachine.State],
        operation: na.operationscatalog.models.operations.Operation,
        elements: List[na.catalog.basemodule.ui.WebComponent],
        operationPermissionService: OperationPermissionService
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*11.2*/import na.operationscatalog.settings.OperationsCatalogConstants


Seq[Any](format.raw/*9.2*/("""

"""),format.raw/*12.1*/("""
"""),_display_(/*13.2*/na/*13.4*/.operationscatalog.views.html.operations.view.render(
    iconClass = "fuxicons fuxicons-resource",
    stateList = stateList,
    operationContext = OperationsCatalogConstants.RESOURCE_CONTEXT,
    operationId = operationId,
    storeDirective = "data-na-portal-operations-catalog-details-store-operation",
    viewPageDirective = "data-na-portal-operations-catalog-resource-view-page",
    operation = operation,
    elements = elements,
    operationPermissionService = operationPermissionService
)))
      }
    }
  }

  def render(operationId:Long,stateList:List[na.operationscatalog.models.statemachine.State],operation:na.operationscatalog.models.operations.Operation,elements:List[na.catalog.basemodule.ui.WebComponent],operationPermissionService:OperationPermissionService): play.twirl.api.HtmlFormat.Appendable = apply(operationId,stateList,operation,elements,operationPermissionService)

  def f:((Long,List[na.operationscatalog.models.statemachine.State],na.operationscatalog.models.operations.Operation,List[na.catalog.basemodule.ui.WebComponent],OperationPermissionService) => play.twirl.api.HtmlFormat.Appendable) = (operationId,stateList,operation,elements,operationPermissionService) => apply(operationId,stateList,operation,elements,operationPermissionService)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:51 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/target/TwirlSource/na/operationscatalog/views/resource/view.scala.html
                  HASH: da0aee0d8640484e65b32a692e607c518caf207a
                  MATRIX: 681->1|1219->67|1591->368|1683->365|1712->432|1740->434|1750->436
                  LINES: 24->1|29->3|38->11|41->9|43->12|44->13|44->13
                  -- GENERATED --
              */
          