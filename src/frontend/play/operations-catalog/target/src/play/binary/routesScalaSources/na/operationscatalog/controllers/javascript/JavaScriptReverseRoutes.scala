// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/conf/operationscatalog.routes
// @DATE:Tue Jul 01 11:21:43 WEST 2025

import play.api.routing.JavaScriptReverseRoute


import _root_.controllers.Assets.Asset

// @LINE:6
package na.operationscatalog.controllers.javascript {

  // @LINE:16
  class ReverseAssets(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:16
    def versioned: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.Assets.versioned",
      """
        function(file1) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/assets/" + (""" + implicitly[play.api.mvc.PathBindable[Asset]].javascriptUnbind + """)("file", file1)})
        }
      """
    )
  
  }

  // @LINE:106
  class ReverseResponseCodeController(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:108
    def responseCodesDataTableSearch: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.ResponseCodeController.responseCodesDataTableSearch",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/responsecodes/datatable/config"})
        }
      """
    )
  
    // @LINE:107
    def responseCodeSearch: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.ResponseCodeController.responseCodeSearch",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/responsecodes/search"})
        }
      """
    )
  
    // @LINE:106
    def home: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.ResponseCodeController.home",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/responsecodes/home"})
        }
      """
    )
  
    // @LINE:109
    def responseCodeTypes: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.ResponseCodeController.responseCodeTypes",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/responsecodes/types"})
        }
      """
    )
  
  }

  // @LINE:88
  class ReverseOperationController(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:89
    def delete: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.OperationController.delete",
      """
        function(id0) {
          return _wA({method:"DELETE", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/operation/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0))})
        }
      """
    )
  
    // @LINE:96
    def dataTableViewExecutionAttributes: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.OperationController.dataTableViewExecutionAttributes",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/operation/datatable/config/executionAttributes"})
        }
      """
    )
  
    // @LINE:98
    def dataTableEditableInputAttributes: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.OperationController.dataTableEditableInputAttributes",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/operation/datatable/config/editableInputAttributes"})
        }
      """
    )
  
    // @LINE:94
    def inputAttributeModal: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.OperationController.inputAttributeModal",
      """
        function(action0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/operation/modal/inputAttribute/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("action", action0))})
        }
      """
    )
  
    // @LINE:91
    def create: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.OperationController.create",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/operation/create"})
        }
      """
    )
  
    // @LINE:93
    def update: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.OperationController.update",
      """
        function(id0) {
          return _wA({method:"PUT", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/operation/update/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0))})
        }
      """
    )
  
    // @LINE:95
    def getEntityRelationTree: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.OperationController.getEntityRelationTree",
      """
        function(operationType0,versionName1,operationName2) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/operation/entities/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("versionName", versionName1)) + "/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("operationName", operationName2)) + "/relations" + _qS([(""" + implicitly[play.api.mvc.QueryStringBindable[String]].javascriptUnbind + """)("operationType", operationType0)])})
        }
      """
    )
  
    // @LINE:88
    def get: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.OperationController.get",
      """
        function(id0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/operation/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0))})
        }
      """
    )
  
    // @LINE:90
    def transitionState: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.OperationController.transitionState",
      """
        function(id0,stateId1) {
          return _wA({method:"PUT", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/operation/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0)) + "/state/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("stateId", stateId1))})
        }
      """
    )
  
    // @LINE:92
    def cloneOperation: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.OperationController.cloneOperation",
      """
        function(id0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/operation/clone/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0))})
        }
      """
    )
  
    // @LINE:97
    def dataTableViewInputAttributes: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.OperationController.dataTableViewInputAttributes",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/operation/datatable/config/inputAttributes"})
        }
      """
    )
  
    // @LINE:99
    def dataTableViewOutputAttributes: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.OperationController.dataTableViewOutputAttributes",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/operation/datatable/config/outputAttributes"})
        }
      """
    )
  
    // @LINE:100
    def dataTableCreateOrEditOutputAttributes: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.OperationController.dataTableCreateOrEditOutputAttributes",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/operation/datatable/config/outputAttributesCU"})
        }
      """
    )
  
  }

  // @LINE:6
  class ReverseApplication(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:129
    def getEditableVersionStates: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.Application.getEditableVersionStates",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/editableVersionStates"})
        }
      """
    )
  
    // @LINE:6
    def goToHome: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.Application.goToHome",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/home"})
        }
      """
    )
  
    // @LINE:12
    def javascriptRoutes: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.Application.javascriptRoutes",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/assets/javascripts/routes"})
        }
      """
    )
  
    // @LINE:9
    def resume: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.Application.resume",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/resume"})
        }
      """
    )
  
  }

  // @LINE:49
  class ReverseCfsController(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:53
    def getCreatePage: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.CfsController.getCreatePage",
      """
        function(versionId0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/cfs/create/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("versionId", versionId0))})
        }
      """
    )
  
    // @LINE:50
    def cfsSearch: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.CfsController.cfsSearch",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/cfs/search"})
        }
      """
    )
  
    // @LINE:56
    def entitiesByVersion: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.CfsController.entitiesByVersion",
      """
        function(entityVersion0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/cfs/entitiesByVersion/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("entityVersion", entityVersion0))})
        }
      """
    )
  
    // @LINE:54
    def cfsDataTableSearch: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.CfsController.cfsDataTableSearch",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/cfs/datatable/config/search"})
        }
      """
    )
  
    // @LINE:49
    def home: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.CfsController.home",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/cfs/home"})
        }
      """
    )
  
    // @LINE:55
    def entitiesSearch: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.CfsController.entitiesSearch",
      """
        function(version0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/cfs/entitiesCfs/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version0))})
        }
      """
    )
  
    // @LINE:51
    def view: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.CfsController.view",
      """
        function(id0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/cfs/view/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0))})
        }
      """
    )
  
    // @LINE:52
    def getEditPage: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.CfsController.getEditPage",
      """
        function(id0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/cfs/edit/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0))})
        }
      """
    )
  
  }

  // @LINE:62
  class ReverseRfsController(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:66
    def getCreatePage: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.RfsController.getCreatePage",
      """
        function(versionId0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/rfs/create/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("versionId", versionId0))})
        }
      """
    )
  
    // @LINE:67
    def rfsDataTableSearch: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.RfsController.rfsDataTableSearch",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/rfs/datatable/config/search"})
        }
      """
    )
  
    // @LINE:69
    def entitiesByVersion: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.RfsController.entitiesByVersion",
      """
        function(entityVersion0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/rfs/entitiesByVersion/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("entityVersion", entityVersion0))})
        }
      """
    )
  
    // @LINE:63
    def rfsSearch: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.RfsController.rfsSearch",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/rfs/search"})
        }
      """
    )
  
    // @LINE:62
    def home: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.RfsController.home",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/rfs/home"})
        }
      """
    )
  
    // @LINE:68
    def entitiesSearch: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.RfsController.entitiesSearch",
      """
        function(version0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/rfs/entitiesRfs/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version0))})
        }
      """
    )
  
    // @LINE:64
    def view: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.RfsController.view",
      """
        function(id0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/rfs/view/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0))})
        }
      """
    )
  
    // @LINE:65
    def getEditPage: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.RfsController.getEditPage",
      """
        function(id0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/rfs/edit/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0))})
        }
      """
    )
  
  }

  // @LINE:75
  class ReverseResourceController(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:76
    def resourceSearch: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.ResourceController.resourceSearch",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/resources/search"})
        }
      """
    )
  
    // @LINE:79
    def getCreatePage: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.ResourceController.getCreatePage",
      """
        function(versionId0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/resources/create/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("versionId", versionId0))})
        }
      """
    )
  
    // @LINE:80
    def resourcesDataTableSearch: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.ResourceController.resourcesDataTableSearch",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/resources/datatable/config/search"})
        }
      """
    )
  
    // @LINE:82
    def entitiesByVersion: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.ResourceController.entitiesByVersion",
      """
        function(entityVersion0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/resources/entitiesByVersion/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("entityVersion", entityVersion0))})
        }
      """
    )
  
    // @LINE:75
    def home: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.ResourceController.home",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/resources/home"})
        }
      """
    )
  
    // @LINE:81
    def entitiesSearch: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.ResourceController.entitiesSearch",
      """
        function(version0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/resources/entitiesResource/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version0))})
        }
      """
    )
  
    // @LINE:77
    def view: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.ResourceController.view",
      """
        function(id0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/resources/view/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0))})
        }
      """
    )
  
    // @LINE:78
    def getEditPage: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.ResourceController.getEditPage",
      """
        function(id0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/resources/edit/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0))})
        }
      """
    )
  
  }

  // @LINE:115
  class ReverseTagsController(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:115
    def getTagsForSelect: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.TagsController.getTagsForSelect",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/tags"})
        }
      """
    )
  
  }

  // @LINE:22
  class ReverseVersionController(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:26
    def delete: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.VersionController.delete",
      """
        function(id0) {
          return _wA({method:"DELETE", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/versions/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0))})
        }
      """
    )
  
    // @LINE:35
    def entitiesVersionsForSelect: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.VersionController.entitiesVersionsForSelect",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/versions/entitiesVersionsForSelect"})
        }
      """
    )
  
    // @LINE:24
    def create: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.VersionController.create",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/versions/create"})
        }
      """
    )
  
    // @LINE:33
    def getValidVersionsForSelect: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.VersionController.getValidVersionsForSelect",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/versions/validVersions"})
        }
      """
    )
  
    // @LINE:43
    def responseCodesSearch: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.VersionController.responseCodesSearch",
      """
        function(version0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/versions/datatable/search/responseCodes/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version0))})
        }
      """
    )
  
    // @LINE:30
    def cloneVersion: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.VersionController.cloneVersion",
      """
        function(id0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/versions/clone/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0))})
        }
      """
    )
  
    // @LINE:36
    def entityVersions: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.VersionController.entityVersions",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/versions/entityVersions"})
        }
      """
    )
  
    // @LINE:39
    def versionsDataTableSearch: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.VersionController.versionsDataTableSearch",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/versions/datatable/config"})
        }
      """
    )
  
    // @LINE:38
    def transitionVersionState: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.VersionController.transitionVersionState",
      """
        function(id0) {
          return _wA({method:"PUT", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/versions/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0)) + "/state"})
        }
      """
    )
  
    // @LINE:25
    def versionsSearch: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.VersionController.versionsSearch",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/versions/search"})
        }
      """
    )
  
    // @LINE:29
    def update: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.VersionController.update",
      """
        function(id0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/versions/edit/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0))})
        }
      """
    )
  
    // @LINE:31
    def versionsSearchForSelect: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.VersionController.versionsSearchForSelect",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/versions/context/search"})
        }
      """
    )
  
    // @LINE:34
    def versionEntitiesSearch: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.VersionController.versionEntitiesSearch",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/versions/entitiesVersions"})
        }
      """
    )
  
    // @LINE:32
    def versionByIdForSelect: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.VersionController.versionByIdForSelect",
      """
        function(id0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/versions/context/search/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0))})
        }
      """
    )
  
    // @LINE:37
    def versionEntitiesCache: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.VersionController.versionEntitiesCache",
      """
        function(name0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/versions/entities/cache/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("name", name0))})
        }
      """
    )
  
    // @LINE:40
    def viewOperationsDataTable: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.VersionController.viewOperationsDataTable",
      """
        function(version0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/versions/datatable/view/operations/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version0))})
        }
      """
    )
  
    // @LINE:41
    def viewResponseCodesDataTable: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.VersionController.viewResponseCodesDataTable",
      """
        function(version0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/versions/datatable/view/responseCodes/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version0))})
        }
      """
    )
  
    // @LINE:23
    def createPage: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.VersionController.createPage",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/versions/create"})
        }
      """
    )
  
    // @LINE:22
    def home: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.VersionController.home",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/versions/home"})
        }
      """
    )
  
    // @LINE:42
    def operationsSearch: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.VersionController.operationsSearch",
      """
        function(version0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/versions/datatable/search/operations/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("version", version0))})
        }
      """
    )
  
    // @LINE:27
    def view: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.VersionController.view",
      """
        function(id0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/versions/view/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0))})
        }
      """
    )
  
    // @LINE:28
    def getEditPage: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.VersionController.getEditPage",
      """
        function(id0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/versions/edit/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0))})
        }
      """
    )
  
  }

  // @LINE:122
  class ReverseCacheController(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:122
    def getCatEntity: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.CacheController.getCatEntity",
      """
        function(name0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/cache/catEntity/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("name", name0))})
        }
      """
    )
  
    // @LINE:123
    def invalidate: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.operationscatalog.controllers.CacheController.invalidate",
      """
        function() {
          return _wA({method:"PUT", url:"""" + _prefix + { _defaultPrefix } + """" + "operationscatalog/cache/invalidate"})
        }
      """
    )
  
  }


}
