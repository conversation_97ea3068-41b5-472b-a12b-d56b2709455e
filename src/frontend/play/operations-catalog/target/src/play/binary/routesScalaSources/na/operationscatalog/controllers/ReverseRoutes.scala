// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/conf/operationscatalog.routes
// @DATE:Tue Jul 01 11:21:43 WEST 2025

import play.api.mvc.Call


import _root_.controllers.Assets.Asset

// @LINE:6
package na.operationscatalog.controllers {

  // @LINE:16
  class ReverseAssets(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:16
    def versioned(file:Asset): Call = {
      implicit lazy val _rrc = new play.core.routing.ReverseRouteContext(Map(("path", "/public"))); _rrc
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/assets/" + implicitly[play.api.mvc.PathBindable[Asset]].unbind("file", file))
    }
  
  }

  // @LINE:106
  class ReverseResponseCodeController(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:108
    def responseCodesDataTableSearch(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/responsecodes/datatable/config")
    }
  
    // @LINE:107
    def responseCodeSearch(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "operationscatalog/responsecodes/search")
    }
  
    // @LINE:106
    def home(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/responsecodes/home")
    }
  
    // @LINE:109
    def responseCodeTypes(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/responsecodes/types")
    }
  
  }

  // @LINE:88
  class ReverseOperationController(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:89
    def delete(id:Long): Call = {
      
      Call("DELETE", _prefix + { _defaultPrefix } + "operationscatalog/operation/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)))
    }
  
    // @LINE:96
    def dataTableViewExecutionAttributes(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/operation/datatable/config/executionAttributes")
    }
  
    // @LINE:98
    def dataTableEditableInputAttributes(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/operation/datatable/config/editableInputAttributes")
    }
  
    // @LINE:94
    def inputAttributeModal(action:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/operation/modal/inputAttribute/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("action", action)))
    }
  
    // @LINE:91
    def create(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "operationscatalog/operation/create")
    }
  
    // @LINE:93
    def update(id:Long): Call = {
      
      Call("PUT", _prefix + { _defaultPrefix } + "operationscatalog/operation/update/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)))
    }
  
    // @LINE:95
    def getEntityRelationTree(operationType:String, versionName:String, operationName:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/operation/entities/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("versionName", versionName)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("operationName", operationName)) + "/relations" + play.core.routing.queryString(List(Some(implicitly[play.api.mvc.QueryStringBindable[String]].unbind("operationType", operationType)))))
    }
  
    // @LINE:88
    def get(id:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/operation/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)))
    }
  
    // @LINE:90
    def transitionState(id:Long, stateId:Long): Call = {
      
      Call("PUT", _prefix + { _defaultPrefix } + "operationscatalog/operation/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)) + "/state/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("stateId", stateId)))
    }
  
    // @LINE:92
    def cloneOperation(id:Long): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "operationscatalog/operation/clone/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)))
    }
  
    // @LINE:97
    def dataTableViewInputAttributes(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/operation/datatable/config/inputAttributes")
    }
  
    // @LINE:99
    def dataTableViewOutputAttributes(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/operation/datatable/config/outputAttributes")
    }
  
    // @LINE:100
    def dataTableCreateOrEditOutputAttributes(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/operation/datatable/config/outputAttributesCU")
    }
  
  }

  // @LINE:6
  class ReverseApplication(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:129
    def getEditableVersionStates(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/editableVersionStates")
    }
  
    // @LINE:6
    def goToHome(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/home")
    }
  
    // @LINE:12
    def javascriptRoutes(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/assets/javascripts/routes")
    }
  
    // @LINE:9
    def resume(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/resume")
    }
  
  }

  // @LINE:49
  class ReverseCfsController(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:53
    def getCreatePage(versionId:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/cfs/create/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("versionId", versionId)))
    }
  
    // @LINE:50
    def cfsSearch(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "operationscatalog/cfs/search")
    }
  
    // @LINE:56
    def entitiesByVersion(entityVersion:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/cfs/entitiesByVersion/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("entityVersion", entityVersion)))
    }
  
    // @LINE:54
    def cfsDataTableSearch(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/cfs/datatable/config/search")
    }
  
    // @LINE:49
    def home(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/cfs/home")
    }
  
    // @LINE:55
    def entitiesSearch(version:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/cfs/entitiesCfs/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)))
    }
  
    // @LINE:51
    def view(id:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/cfs/view/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)))
    }
  
    // @LINE:52
    def getEditPage(id:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/cfs/edit/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)))
    }
  
  }

  // @LINE:62
  class ReverseRfsController(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:66
    def getCreatePage(versionId:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/rfs/create/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("versionId", versionId)))
    }
  
    // @LINE:67
    def rfsDataTableSearch(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/rfs/datatable/config/search")
    }
  
    // @LINE:69
    def entitiesByVersion(entityVersion:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/rfs/entitiesByVersion/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("entityVersion", entityVersion)))
    }
  
    // @LINE:63
    def rfsSearch(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "operationscatalog/rfs/search")
    }
  
    // @LINE:62
    def home(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/rfs/home")
    }
  
    // @LINE:68
    def entitiesSearch(version:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/rfs/entitiesRfs/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)))
    }
  
    // @LINE:64
    def view(id:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/rfs/view/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)))
    }
  
    // @LINE:65
    def getEditPage(id:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/rfs/edit/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)))
    }
  
  }

  // @LINE:75
  class ReverseResourceController(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:76
    def resourceSearch(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "operationscatalog/resources/search")
    }
  
    // @LINE:79
    def getCreatePage(versionId:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/resources/create/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("versionId", versionId)))
    }
  
    // @LINE:80
    def resourcesDataTableSearch(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/resources/datatable/config/search")
    }
  
    // @LINE:82
    def entitiesByVersion(entityVersion:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/resources/entitiesByVersion/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("entityVersion", entityVersion)))
    }
  
    // @LINE:75
    def home(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/resources/home")
    }
  
    // @LINE:81
    def entitiesSearch(version:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/resources/entitiesResource/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)))
    }
  
    // @LINE:77
    def view(id:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/resources/view/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)))
    }
  
    // @LINE:78
    def getEditPage(id:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/resources/edit/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)))
    }
  
  }

  // @LINE:115
  class ReverseTagsController(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:115
    def getTagsForSelect(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/tags")
    }
  
  }

  // @LINE:22
  class ReverseVersionController(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:26
    def delete(id:Long): Call = {
      
      Call("DELETE", _prefix + { _defaultPrefix } + "operationscatalog/versions/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)))
    }
  
    // @LINE:35
    def entitiesVersionsForSelect(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/versions/entitiesVersionsForSelect")
    }
  
    // @LINE:24
    def create(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "operationscatalog/versions/create")
    }
  
    // @LINE:33
    def getValidVersionsForSelect(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/versions/validVersions")
    }
  
    // @LINE:43
    def responseCodesSearch(version:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "operationscatalog/versions/datatable/search/responseCodes/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)))
    }
  
    // @LINE:30
    def cloneVersion(id:Long): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "operationscatalog/versions/clone/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)))
    }
  
    // @LINE:36
    def entityVersions(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/versions/entityVersions")
    }
  
    // @LINE:39
    def versionsDataTableSearch(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/versions/datatable/config")
    }
  
    // @LINE:38
    def transitionVersionState(id:Long): Call = {
      
      Call("PUT", _prefix + { _defaultPrefix } + "operationscatalog/versions/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)) + "/state")
    }
  
    // @LINE:25
    def versionsSearch(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "operationscatalog/versions/search")
    }
  
    // @LINE:29
    def update(id:Long): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "operationscatalog/versions/edit/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)))
    }
  
    // @LINE:31
    def versionsSearchForSelect(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/versions/context/search")
    }
  
    // @LINE:34
    def versionEntitiesSearch(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/versions/entitiesVersions")
    }
  
    // @LINE:32
    def versionByIdForSelect(id:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/versions/context/search/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)))
    }
  
    // @LINE:37
    def versionEntitiesCache(name:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/versions/entities/cache/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("name", name)))
    }
  
    // @LINE:40
    def viewOperationsDataTable(version:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/versions/datatable/view/operations/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)))
    }
  
    // @LINE:41
    def viewResponseCodesDataTable(version:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/versions/datatable/view/responseCodes/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)))
    }
  
    // @LINE:23
    def createPage(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/versions/create")
    }
  
    // @LINE:22
    def home(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/versions/home")
    }
  
    // @LINE:42
    def operationsSearch(version:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "operationscatalog/versions/datatable/search/operations/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)))
    }
  
    // @LINE:27
    def view(id:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/versions/view/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)))
    }
  
    // @LINE:28
    def getEditPage(id:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/versions/edit/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)))
    }
  
  }

  // @LINE:122
  class ReverseCacheController(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:122
    def getCatEntity(name:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "operationscatalog/cache/catEntity/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("name", name)))
    }
  
    // @LINE:123
    def invalidate(): Call = {
      
      Call("PUT", _prefix + { _defaultPrefix } + "operationscatalog/cache/invalidate")
    }
  
  }


}
