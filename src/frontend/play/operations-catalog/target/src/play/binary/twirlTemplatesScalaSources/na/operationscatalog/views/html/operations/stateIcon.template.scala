
package na.operationscatalog.views.html.operations

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object stateIcon extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(state: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*3.2*/stateIcon/*3.11*/ = {{
    state match {
        case "BLOCKED" => "glyphicon glyphicon-ban-circle text-danger fx-icon"
        case "IN SERVICE" => "glyphicon glyphicon-ok text-success fx-icon"
        case "SIMULATED" => "glyphicon glyphicon-list-alt text-info fx-icon"
    }
}};
Seq[Any](format.raw/*1.17*/("""

"""),format.raw/*9.2*/("""

"""),format.raw/*11.1*/("""<i class=""""),_display_(/*11.12*/stateIcon),format.raw/*11.21*/(""""></i>"""))
      }
    }
  }

  def render(state:String): play.twirl.api.HtmlFormat.Appendable = apply(state)

  def f:((String) => play.twirl.api.HtmlFormat.Appendable) = (state) => apply(state)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:51 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/target/TwirlSource/na/operationscatalog/views/operations/stateIcon.scala.html
                  HASH: bb808c1718da9ee792f4e21ba89959418e8ed30b
                  MATRIX: 984->1|1077->19|1094->28|1386->16|1414->290|1443->292|1481->303|1511->312
                  LINES: 28->1|32->3|32->3|39->1|41->9|43->11|43->11|43->11
                  -- GENERATED --
              */
          