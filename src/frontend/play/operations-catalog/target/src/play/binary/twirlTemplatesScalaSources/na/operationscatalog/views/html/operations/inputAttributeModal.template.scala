
package na.operationscatalog.views.html.operations

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.catalog.basemodule.ui.Action

object inputAttributeModal extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[Action,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*3.2*/(action: Action):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*3.18*/("""
"""),format.raw/*4.1*/("""<x-i18n-attr-container>
<x-opercat-attribute-modal-form>
<form class="form-horizontal" role="form">
    <div id="operation-input-attribute-form">
        <div class="form-group">
            <label for="attribute-name" class="col-sm-2 control-label" is="x-label"><x-i18n key="na.portal.operationscatalog.modal.label.name"></x-i18n></label>
            <div id="attribute-entitySelect" class="col-sm-9">
                <x-opercat-input-attr-select id="attribute-name" required name="name" data-field="name" class="select-box--entity-attributes" data-i18n-placeholder="na.portal.operationscatalog.selectbox.placeholder"  """),_display_(/*11.219*/{"disabled edit-mode".when(action == Action.EDIT)}),format.raw/*11.269*/("""></x-opercat-input-attr-select>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label"><x-i18n key="na.portal.operationscatalog.modal.label.type"></x-i18n></label>
            <div class="form-control-static col-sm-10" id="attribute-type"></div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label"><x-i18n key="na.portal.operationscatalog.modal.label.entityRule"></x-i18n></label>
            <div class="form-control-static col-sm-10" id="attribute-entityConstraint"></div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label"><x-i18n key="na.portal.operationscatalog.modal.label.cardinality"></x-i18n></label>
            <div class="col-sm-1 radio">
                <label for="attribute-minMultiplicity" class="required" is="x-label"><x-i18n key="na.portal.operationscatalog.modal.label.cardinality.min"></x-i18n></label>
            </div>
            <div class="col-sm-2">
                <input id="attribute-minMultiplicity" type="number" name="min" min="0" size="4" class="form-control input-sm">
            </div>
            <div class="col-sm-1 radio">
                <label for="attribute-maxMultiplicity" class="required" is="x-label"><x-i18n key="na.portal.operationscatalog.modal.label.cardinality.max"></x-i18n></label>
            </div>
            <div class="col-sm-2">
                <input id="attribute-maxMultiplicity" type="number" name="max" min="1" size="4" class="form-control input-sm">
            </div>
        </div>

        <div class="form-group" style="display: none">
            <!-- error message container -->
            <div id="attribute-multiplicityValidation" class="col-sm-offset-2 col-sm-6" style="max-width: 100%"></div>
        </div>

        <!-- restrictions -->
        <div id="attribute-restrictions" style="display: none">

            <div class="form-group">
                <label class="col-sm-2 control-label"><x-i18n key="na.portal.operationscatalog.modal.label.restrictions"></x-i18n></label>
                    <!-- Restriction - Don't apply -->
                <div class="col-sm-4 form-control-static">
                    <input id="attribute-noRestrictions" type="radio" name="restriction" checked value="">
                    <label for="attribute-noRestrictions"><x-i18n key="na.portal.operationscatalog.modal.label.restrictions.doNotApply"></x-i18n></label><br/>
                </div>
            </div>

            <!-- Logic condition - SIZE -->
            <div id="attribute-logicConditionSize" data-restriction>
                <div class="form-group">
                    <div class="col-sm-4 col-sm-offset-2">
                        <input id="attribute-sizeRestriction" type="radio" name="restriction" value="SIZE">
                        <label for="attribute-sizeRestriction"><x-i18n key="na.portal.operationscatalog.modal.label.logicCondition"></x-i18n></label>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-sm-3 col-sm-offset-3 radio control-label">
                        <label for="attribute-inputMinSize" class="required" is="x-label"><x-i18n key="na.portal.operationscatalog.modal.label.logicalcondition.minSize"></x-i18n></label>
                    </div>
                    <div class="col-sm-2">
                        <input id="attribute-inputMinSize" class="form-control" type="number" min="0" max="" name="inputMinSize">
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-sm-3 col-sm-offset-3 radio control-label">
                        <label for="attribute-inputMaxSize" class="required" is="x-label"><x-i18n key="na.portal.operationscatalog.modal.label.logicalcondition.maxSize"></x-i18n></label>
                    </div>
                    <div class="col-sm-2">
                        <input id="attribute-inputMaxSize" class="form-control" type="number" min="0" max="" name="inputMaxSize">
                    </div>
                </div>
            </div>

            <!-- Logic condition - RANGE -->
            <div id="attribute-logicConditionRange" data-restriction>
                <div class="form-group">
                    <div class="col-sm-4 col-sm-offset-2">
                        <input id="attribute-rangeRestriction" type="radio" name="restriction" value="RANGE">
                        <label for="attribute-rangeRestriction"><x-i18n key="na.portal.operationscatalog.modal.label.logicCondition"></x-i18n></label>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-sm-2 col-sm-offset-3 radio control-label">
                        <label for="attribute-inputMinRange"><x-i18n key="na.portal.operationscatalog.modal.label.logicalcondition.minRange"></x-i18n></label>
                    </div>
                    <div class="col-sm-2">
                        <input id="attribute-inputMinRange" class="form-control" type="number" min="0" max="" name="inputMinRange">
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-sm-2 col-sm-offset-3 radio control-label">
                        <label for="attribute-inputMaxRange"><x-i18n key="na.portal.operationscatalog.modal.label.logicalcondition.maxRange"></x-i18n></label>
                    </div>
                    <div class="col-sm-2">
                        <input id="attribute-inputMaxRange" class="form-control" type="number" min="0" max="" name="inputMaxRange">
                    </div>
                </div>
            </div>

            <!-- Logic condition - FLOAT_RANGE -->
            <div id="attribute-logicConditionFloatRange" data-restriction>
                <div class="form-group">
                    <div class="col-sm-4 col-sm-offset-2">
                        <input type="radio" name="restriction" value="FLOAT_RANGE">
                        <label><x-i18n key="na.portal.operationscatalog.modal.label.logicCondition"></x-i18n></label>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-sm-2 col-sm-offset-3 radio control-label">
                        <label><x-i18n key="na.portal.operationscatalog.modal.label.logicalcondition.minRange"></x-i18n></label>
                    </div>
                    <div class="col-sm-2">
                        <input id="attribute-inputMinFloatRange" class="form-control" type="number" min="0" max="" name="inputMinFloatRange">
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-sm-2 col-sm-offset-3 radio control-label">
                        <label><x-i18n key="na.portal.operationscatalog.modal.label.logicalcondition.maxRange"></x-i18n></label>
                    </div>
                    <div class="col-sm-2">
                        <input id="attribute-inputMaxFloatRange" class="form-control" type="number" min="0" max="" name="inputMaxFloatRange">
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div id="attribute-conditionValidation" class="col-sm-6 col-sm-offset-3" style="max-width: 100%; display: none">
                    <p class="fx-form-validator fx-form-validator-danger">
                        <i class="fx-alert-icon fx-alert-icon-danger fx-icon"></i>
                        <span class="errorMessage"></span>
                    </p>
                </div>
            </div>

            <!-- Regex restriction -->
            <div id="attribute-regexRestriction" class="form-group" style="margin-bottom: 0;" data-restriction>
                <div class="col-sm-3 col-sm-offset-2 pull-left">
                    <input id="attribute-regex" type="radio" name="restriction" value="REGEXP">
                    <label for="attribute-regex" class="control-label"><x-i18n key="na.portal.operationscatalog.modal.label.regex"></x-i18n></label>
                </div>
                <div class="col-sm-4">
                    <div class="row form-group">
                        <input id="attribute-regexInput" class="form-control input-sm" type="text" name="regexInput" autocomplete="off">

                        <div id="attribute-regexValidation" style="max-width: 100%; display: none">
                            <p class="fx-form-validator fx-form-validator-danger">
                                <i class="fx-alert-icon fx-alert-icon-danger fx-icon"></i>
                                <span class="errorMessage"></span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enumeration restriction -->
            <div id="attribute-enumRestriction" class="form-group" data-restriction>
                <div class="col-sm-9 col-sm-offset-2">
                    <input type="radio" name="restriction" value="ENUMC">
                    <x-opercat-enum-restriction-multi-select id="attribute-enumeration" has-clear-button data-i18n-placeholder="na.portal.operationscatalog.selectbox.placeholder"></x-opercat-enum-restriction-multi-select>
                    <div id="attribute-enumerationValidation" style="max-width: 100%; display: none">
                        <p class="fx-form-validator fx-form-validator-danger">
                            <i class="fx-alert-icon fx-alert-icon-danger fx-icon"></i>
                            <span class="errorMessage"></span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
</x-opercat-attribute-modal-form>
</x-i18n-attr-container>"""))
      }
    }
  }

  def render(action:Action): play.twirl.api.HtmlFormat.Appendable = apply(action)

  def f:((Action) => play.twirl.api.HtmlFormat.Appendable) = (action) => apply(action)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:43 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/target/TwirlSource/na/operationscatalog/views/operations/inputAttributeModal.scala.html
                  HASH: 0de72e16633376274c7eefa9125fb0e9abf11905
                  MATRIX: 683->1|1040->42|1151->58|1178->59|1827->680|1899->730
                  LINES: 24->1|29->3|34->3|35->4|42->11|42->11
                  -- GENERATED --
              */
          