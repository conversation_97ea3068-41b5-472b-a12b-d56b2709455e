
package na.operationscatalog.views.html.version

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import na.operationscatalog.models.operations.Version
/*3.2*/import na.operationscatalog.models.statemachine.State
/*4.2*/import na.operationscatalog.services.VersionPermissionService
/*5.2*/import na.operationscatalog.settings.{AAAPIResources, OperationsCatalogConstants}
/*6.2*/import na.catalog.basemodule.ui.WebComponent
/*7.2*/import na.operationscatalog.views.html.skeletons.mainSkel
/*8.2*/import play.libs.Json
/*9.2*/import pt.alticelabs.nossis.security.views.html.authorized

object view extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template5[Version,List[State],List[WebComponent],Map[String, WebComponent],VersionPermissionService,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*11.2*/(
        version: Version,
        stateList: List[State],
        elements: List[WebComponent],
        headerComponents: Map[String, WebComponent],
        versionPermissionService: VersionPermissionService
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*19.2*/headerActions/*19.15*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*19.19*/("""
    """),format.raw/*20.5*/("""<div class="pull-right">
        """),_display_(/*21.10*/authorized(AAAPIResources.operationsCatalog().U().toString)/*21.69*/ {_display_(Seq[Any](format.raw/*21.71*/("""
            """),format.raw/*22.13*/("""<a class="btn btn-default btn-sm """),_display_(/*22.47*/{"disabled".when(!versionPermissionService.canEdit(version))}),format.raw/*22.108*/("""" title=""""),_display_(/*22.118*/i18n("na.details.title.edit")),format.raw/*22.147*/("""" id="version-edit-action" data-page-action="edit-version">
                <i class="glyphicon glyphicon-pencil"></i>
            </a>
        """)))}),format.raw/*25.10*/("""
        """),_display_(/*26.10*/authorized(AAAPIResources.operationsCatalog().C().toString)/*26.69*/ {_display_(Seq[Any](format.raw/*26.71*/("""
            """),format.raw/*27.13*/("""<a class="btn btn-default btn-sm """),_display_(/*27.47*/{"disabled".when(!versionPermissionService.canClone(version))}),format.raw/*27.109*/("""" title=""""),_display_(/*27.119*/i18n("na.details.title.clone")),format.raw/*27.149*/("""" id="version-clone-action" data-page-action="clone-version">
                <i class="fuxicons fuxicons-clone"></i>
            </a>
        """)))}),format.raw/*30.10*/("""
        """),_display_(/*31.10*/authorized(AAAPIResources.operationsCatalog().D().toString)/*31.69*/ {_display_(Seq[Any](format.raw/*31.71*/("""
            """),format.raw/*32.13*/("""<a class="btn btn-default btn-sm """),_display_(/*32.47*/{"disabled".when(!versionPermissionService.canDelete(version))}),format.raw/*32.110*/("""" title=""""),_display_(/*32.120*/i18n("na.details.title.delete")),format.raw/*32.151*/("""" id="version-delete-action" data-page-action="delete-version">
                <i class="glyphicon glyphicon-remove"></i>
            </a>
        """)))}),format.raw/*35.10*/("""
        """),_display_(/*36.10*/authorized(AAAPIResources.operationsCatalog().U().toString)/*36.69*/ {_display_(Seq[Any](format.raw/*36.71*/("""
            """),format.raw/*37.13*/("""<div class="btn-group" id="more_options">
                <a class="btn btn-default btn-sm" onclick="return false;" data-original-title=""""),_display_(/*38.97*/i18n("na.portal.operationscatalog.button.multiactions.title")),format.raw/*38.158*/("""" title=""""),_display_(/*38.168*/i18n("na.portal.operationscatalog.button.multiactions.title")),format.raw/*38.229*/("""" id="view_more_actions_button" data-toggle="dropdown">
                    <i class="glyphicon glyphicon-cog"></i>
                </a>
                <ul class="dropdown-menu fx-dropdown-open-to-left" role="menu" data-na-portal-operations-catalog-version-view-change-state data-version-name=""""),_display_(/*41.160*/version/*41.167*/.getName),format.raw/*41.175*/("""" data-version-id=""""),_display_(/*41.195*/version/*41.202*/.getId),format.raw/*41.208*/("""">
                    """),_display_(/*42.22*/if(stateList.isEmpty)/*42.43*/{_display_(Seq[Any](format.raw/*42.44*/("""
                        """),format.raw/*43.25*/("""<li style="white-space: nowrap;" class="dropdown-header"><b>"""),_display_(/*43.86*/i18n("na.portal.operationscatalog.datatable.actions.state.noFurtherStates")),format.raw/*43.161*/("""</b></li>
                    """)))}/*44.22*/else/*44.26*/{_display_(Seq[Any](format.raw/*44.27*/("""
                        """),format.raw/*45.25*/("""<li style="white-space: nowrap;" class="dropdown-header"><b>"""),_display_(/*45.86*/i18n("na.portal.operationscatalog.datatable.actions.state.changeStateTo")),format.raw/*45.159*/("""</b></li>
                        """),_display_(/*46.26*/for(state <- stateList) yield /*46.49*/{_display_(Seq[Any](format.raw/*46.50*/("""
                            """),format.raw/*47.29*/("""<li><a data-state-id=""""),_display_(/*47.52*/state/*47.57*/.getId),format.raw/*47.63*/("""" data-state-name=""""),_display_(/*47.83*/state/*47.88*/.getName),format.raw/*47.96*/("""" class="option"> """),_display_(/*47.115*/i18n("na.portal.operationscatalog.entities.state." + state.getName)/*47.182*/.toLowerCase),format.raw/*47.194*/("""</a></li>
                        """)))}),format.raw/*48.26*/("""
                    """)))}),format.raw/*49.22*/("""
                """),format.raw/*50.17*/("""</ul>
            </div>
        """)))}),format.raw/*52.10*/("""
    """),format.raw/*53.5*/("""</div>
""")))};def /*56.2*/header/*56.8*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*56.12*/("""
        """),format.raw/*57.9*/("""<span class="fx-entity-header-icon">
            <i class="fuxicons fuxicons-catalogue"></i>
        </span>
        <div class="fx-entity-header-title">
            <div style="display: table">
                <span style="display: table-cell">"""),_display_(/*62.52*/headerComponents("name")/*62.76*/.render()),format.raw/*62.85*/("""</span>
                <span style="display: table-cell">&nbsp;"""),_display_(/*63.58*/headerComponents("state")/*63.83*/.render()),format.raw/*63.92*/("""</span>
            </div>
            <p class="fx-entity-header-details">
                """),_display_(/*66.18*/headerComponents("description")/*66.49*/.render()),format.raw/*66.58*/("""
            """),format.raw/*67.13*/("""</p>
        </div>
""")))};def /*71.2*/tabs/*71.6*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*71.10*/("""
    """),_display_(/*72.6*/na/*72.8*/.naportalbase.views.html.skeletons.tabs.contentTabSkel.render/*72.69*/ {_display_(Seq[Any](format.raw/*72.71*/("""
        """),_display_(/*73.10*/na/*73.12*/.naportalbase.views.html.components.tabs.contentTabComp.render(
            isActive = true,
            tabLabel = i18n("na.portal.operationscatalog.tab.characteristics"),
            attributes = null
        )),format.raw/*77.10*/("""
    """)))}),format.raw/*78.6*/("""
""")))};
Seq[Any](format.raw/*17.2*/("""

"""),format.raw/*54.2*/("""

"""),format.raw/*69.2*/("""

"""),format.raw/*79.2*/("""

"""),_display_(/*81.2*/mainSkel(version.getName)/*81.27*/ {_display_(Seq[Any](format.raw/*81.29*/("""
    """),format.raw/*82.5*/("""<div class="page__container">
        """),_display_(/*83.10*/na/*83.12*/.operationscatalog.views.html.common.lateralNav.render(OperationsCatalogConstants.VERSION_CONTEXT)),format.raw/*83.110*/("""
        """),format.raw/*84.9*/("""<div class="page__content page__content--details-view"
            data-version-name=""""),_display_(/*85.33*/version/*85.40*/.getName),format.raw/*85.48*/(""""
            data-version-id=""""),_display_(/*86.31*/version/*86.38*/.getId),format.raw/*86.44*/(""""
            data-na-portal-operations-catalog-version-view-page>
            <script data-initial-store-content type="application/json">"""),_display_(/*88.73*/Json/*88.77*/.toJson(version)),format.raw/*88.93*/("""</script>
            <div class="fx-entity-header">
                <div class="fx-entity-header-info">"""),_display_(/*90.53*/header),format.raw/*90.59*/("""</div>
                <div class="fx-entity-header-actions">"""),_display_(/*91.56*/headerActions),format.raw/*91.69*/("""</div>
                """),_display_(/*92.18*/if(!version.isValid)/*92.38*/ {_display_(Seq[Any](format.raw/*92.40*/("""
                    """),format.raw/*93.21*/("""<div id="invalid-version-notification" class="invalid-notification invalid-version-notification alert alert-warning">
                        <div class="fx-alert-icon"></div>
                        <div id="description" class="fx-alert-message">
                            <x-i18n key="na.portal.operationscatalog.notification.this.version.is.invalid"></x-i18n>
                        </div>
                    </div>
                """)))}),format.raw/*99.18*/("""
            """),format.raw/*100.13*/("""</div>
            <div class="container--entity-content">
                <x-i18n-attr-container>
                    <x-tab-container>
                        <x-tab data-i18n-label="na.portal.operationscatalog.tab.characteristics">
                            <x-shadow-scroll flex>
                                <div class="tab-pane active">
                                    <div class="operations-catalog__details-page--form-content">
                                        <div class="form-horizontal">
                                            """),_display_(/*109.46*/characteristicsTabContent/*109.71*/.render(version, elements)),format.raw/*109.97*/("""
                                        """),format.raw/*110.41*/("""</div>
                                    </div>
                                </div>
                            </x-shadow-scroll>
                        </x-tab>
                    </x-tab-container>
                </x-i18n-attr-container>
            </div>
        </div>
    </div>
""")))}))
      }
    }
  }

  def render(version:Version,stateList:List[State],elements:List[WebComponent],headerComponents:Map[String, WebComponent],versionPermissionService:VersionPermissionService): play.twirl.api.HtmlFormat.Appendable = apply(version,stateList,elements,headerComponents,versionPermissionService)

  def f:((Version,List[State],List[WebComponent],Map[String, WebComponent],VersionPermissionService) => play.twirl.api.HtmlFormat.Appendable) = (version,stateList,elements,headerComponents,versionPermissionService) => apply(version,stateList,elements,headerComponents,versionPermissionService)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:43 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/target/TwirlSource/na/operationscatalog/views/version/view.scala.html
                  HASH: 9314b156423ba068b1fb1808e065bc53694b7d72
                  MATRIX: 680->1|726->41|787->96|848->151|917->214|1006->297|1058->343|1123->402|1152->425|1598->486|1888->700|1910->713|1991->717|2023->722|2084->756|2152->815|2192->817|2233->830|2294->864|2377->925|2415->935|2466->964|2642->1109|2679->1119|2747->1178|2787->1180|2828->1193|2889->1227|2973->1289|3011->1299|3063->1329|3238->1473|3275->1483|3343->1542|3383->1544|3424->1557|3485->1591|3570->1654|3608->1664|3661->1695|3841->1844|3878->1854|3946->1913|3986->1915|4027->1928|4192->2066|4275->2127|4313->2137|4396->2198|4720->2494|4737->2501|4767->2509|4815->2529|4832->2536|4860->2542|4911->2566|4941->2587|4980->2588|5033->2613|5121->2674|5218->2749|5268->2780|5281->2784|5320->2785|5373->2810|5461->2871|5556->2944|5618->2979|5657->3002|5696->3003|5753->3032|5803->3055|5817->3060|5844->3066|5891->3086|5905->3091|5934->3099|5981->3118|6058->3185|6092->3197|6158->3232|6211->3254|6256->3271|6321->3305|6353->3310|6384->3321|6398->3327|6479->3331|6515->3340|6788->3586|6821->3610|6851->3619|6943->3684|6977->3709|7007->3718|7127->3811|7167->3842|7197->3851|7238->3864|7282->3888|7294->3892|7375->3896|7407->3902|7417->3904|7487->3965|7527->3967|7564->3977|7575->3979|7808->4191|7844->4197|7885->697|7914->3318|7943->3885|7972->4199|8001->4202|8035->4227|8075->4229|8107->4234|8173->4273|8184->4275|8304->4373|8340->4382|8454->4469|8470->4476|8499->4484|8558->4516|8574->4523|8601->4529|8767->4668|8780->4672|8817->4688|8949->4793|8976->4799|9065->4861|9099->4874|9150->4898|9179->4918|9219->4920|9268->4941|9739->5381|9781->5394|10369->5954|10404->5979|10452->6005|10522->6046
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|32->9|37->11|47->19|47->19|49->19|50->20|51->21|51->21|51->21|52->22|52->22|52->22|52->22|52->22|55->25|56->26|56->26|56->26|57->27|57->27|57->27|57->27|57->27|60->30|61->31|61->31|61->31|62->32|62->32|62->32|62->32|62->32|65->35|66->36|66->36|66->36|67->37|68->38|68->38|68->38|68->38|71->41|71->41|71->41|71->41|71->41|71->41|72->42|72->42|72->42|73->43|73->43|73->43|74->44|74->44|74->44|75->45|75->45|75->45|76->46|76->46|76->46|77->47|77->47|77->47|77->47|77->47|77->47|77->47|77->47|77->47|77->47|78->48|79->49|80->50|82->52|83->53|84->56|84->56|86->56|87->57|92->62|92->62|92->62|93->63|93->63|93->63|96->66|96->66|96->66|97->67|99->71|99->71|101->71|102->72|102->72|102->72|102->72|103->73|103->73|107->77|108->78|110->17|112->54|114->69|116->79|118->81|118->81|118->81|119->82|120->83|120->83|120->83|121->84|122->85|122->85|122->85|123->86|123->86|123->86|125->88|125->88|125->88|127->90|127->90|128->91|128->91|129->92|129->92|129->92|130->93|136->99|137->100|146->109|146->109|146->109|147->110
                  -- GENERATED --
              */
          