
package na.operationscatalog.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.catalog.basemodule.ui.WebComponent
/*2.2*/import na.naportalbase.views.tags.i18n
/*3.2*/import na.operationscatalog.settings.{AAAPIResources, OperationsCatalogConstants}
/*4.2*/import pt.alticelabs.nossis.security.views.html.authorized
/*5.2*/import na.operationscatalog.views.html.skeletons.mainSkel

object responseCode extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[List[WebComponent],WebComponent,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*7.2*/(elements: List[WebComponent], versionComponent: WebComponent):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*26.2*/headerAction/*26.14*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*26.18*/("""
"""),_display_(/*27.2*/authorized(AAAPIResources.operationsCatalog.C.toString)/*27.57*/ {_display_(Seq[Any](format.raw/*27.59*/("""
    """),format.raw/*28.157*/("""
""")))}),format.raw/*29.2*/("""
""")))};def /*32.2*/pageHeader/*32.12*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*32.16*/("""
    """),format.raw/*33.5*/("""<div class="fx-entity-header page__search-results-header">
        <div class="fx-entity-header-info">
            <span class="fx-entity-header-icon">
                <i class="fa fa-comments"></i>
            </span>
            <div class="fx-entity-header-title">
                <h1>
                """),_display_(/*40.18*/i18n("na.portal.operationscatalog.nav.responseCode")),format.raw/*40.70*/("""
                """),format.raw/*41.17*/("""</h1>
                <span class="nav-context">("""),_display_(/*42.45*/i18n("na.portal.operationscatalog.nav.context")),format.raw/*42.92*/(""")</span>
            </div>
        </div>
        <div class="fx-entity-header-info">
        """),_display_(/*46.10*/versionComponent/*46.26*/.render()),format.raw/*46.35*/("""
        """),format.raw/*47.9*/("""</div>
        <div class="fx-entity-header-actions">
            """),_display_(/*49.14*/headerAction),format.raw/*49.26*/("""
        """),format.raw/*50.9*/("""</div>
    </div>
""")))};def /*55.2*/collapsedLeftSide/*55.19*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*55.23*/("""
    """),format.raw/*56.5*/("""<div slot="collapsed-left">
        <div class="fx-info-sidebar-collapsed left">
            <div class="fx-sidebar-header search-sidebar__header">
                <span class="fx-entity-header-icon">
                    <i class="fa fa-search"></i>
                </span>
            </div>
        </div>
    </div>
""")))};def /*67.2*/leftSide/*67.10*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*67.14*/("""
"""),format.raw/*68.1*/("""<form slot="left" id="fx-sidebar"
      class="fx-splitter-sidebar with-buttons search-sidebar search-sidebar--version"
      data-na-portal-operations-catalog-sidebar-response-code>
    """),_display_(/*71.6*/na/*71.8*/.operationscatalog.views.html.common.leftSide.render(elements)),format.raw/*71.70*/("""
"""),format.raw/*72.1*/("""</form>
""")))};def /*76.2*/rightSide/*76.11*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*76.15*/("""
    """),format.raw/*77.5*/("""<div id="fx-splitter-content" class="page__search-results" slot="right">
        """),_display_(/*78.10*/pageHeader),format.raw/*78.20*/("""
        """),format.raw/*79.9*/("""<x-shadow-scroll vertical class="page__search-results-table-container">
            <div id="fx-entity-content" class="page__search-results-table fx-entity-info">
                <div>
                    <table class="table table--search" id="response-codes-search-table"
                    data-na-portal-table-datatable
                    data-na-portal-table-load-using-ajax
                    data-na-portal-operations-catalog-response-code-datatable
                    data-config-url=""""),_display_(/*86.39*/dataTableConfigUrl),format.raw/*86.57*/(""""></table>
                </div>
            </div>
        </x-shadow-scroll>
    </div>
""")))};def /*9.2*/dataTableConfigUrl/*9.20*/ = {{
    na.operationscatalog.controllers.routes.ResponseCodeController.responseCodesDataTableSearch()
}};def /*13.2*/newButtonAttrs/*13.16*/ = {{
    val map = scala.collection.mutable.HashMap[String, String]()
    map.put("class", "btn btn-primary fx-call-to-action")
    map.put("id", "new-button")
    map
}};def /*20.2*/iconAttrs/*20.11*/ = {{
    val map = scala.collection.mutable.HashMap[String, String]()
    map.put("class", "glyphicon glyphicon-plus")
    map
}};
Seq[Any](format.raw/*7.64*/("""

"""),format.raw/*11.2*/("""

"""),format.raw/*18.2*/("""

"""),format.raw/*24.2*/("""

"""),format.raw/*30.2*/("""

"""),format.raw/*52.2*/("""


"""),format.raw/*65.2*/("""

"""),format.raw/*73.2*/("""


"""),format.raw/*91.2*/("""

"""),_display_(/*93.2*/mainSkel(i18n("na.portal.operationscatalog.nav.responseCode"))/*93.64*/ {_display_(Seq[Any](format.raw/*93.66*/("""
    """),format.raw/*94.5*/("""<div class="fx-push-footer page__container fx-full-height" data-na-portal-operations-catalog-response-code-search-page>
        <div data-fuxi-header></div>
        """),_display_(/*96.10*/na/*96.12*/.operationscatalog.views.html.common.lateralNav.render(OperationsCatalogConstants.RESPONSE_CODE_CONTEXT)),format.raw/*96.116*/("""
        """),format.raw/*97.9*/("""<div class="fx-splitter-area">
            <x-splitter style="position: relative">
                """),_display_(/*99.18*/collapsedLeftSide),format.raw/*99.35*/("""
                """),_display_(/*100.18*/leftSide),format.raw/*100.26*/("""
                """),_display_(/*101.18*/rightSide),format.raw/*101.27*/("""
            """),format.raw/*102.13*/("""</x-splitter>
        </div>
    </div>
""")))}))
      }
    }
  }

  def render(elements:List[WebComponent],versionComponent:WebComponent): play.twirl.api.HtmlFormat.Appendable = apply(elements,versionComponent)

  def f:((List[WebComponent],WebComponent) => play.twirl.api.HtmlFormat.Appendable) = (elements,versionComponent) => apply(elements,versionComponent)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 19:41:37 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/target/TwirlSource/na/operationscatalog/views/roots/responseCode.scala.html
                  HASH: 3973798d1002007042142dc5e21ff31ac9b59944
                  MATRIX: 678->1|730->47|776->87|865->170|931->230|1325->290|1466->809|1487->821|1568->825|1596->827|1660->882|1700->884|1734->1041|1766->1043|1791->1048|1810->1058|1891->1062|1923->1067|2256->1373|2329->1425|2374->1442|2451->1492|2519->1539|2642->1635|2667->1651|2697->1660|2733->1669|2827->1736|2860->1748|2896->1757|2938->1780|2964->1797|3045->1801|3077->1806|3420->2129|3437->2137|3518->2141|3546->2142|3760->2330|3770->2332|3853->2394|3881->2395|3913->2408|3931->2417|4012->2421|4044->2426|4153->2508|4184->2518|4220->2527|4744->3024|4783->3042|4897->355|4923->373|5042->481|5065->495|5249->668|5267->677|5426->352|5455->478|5484->665|5513->806|5542->1045|5571->1776|5601->2126|5630->2404|5660->3134|5689->3137|5760->3199|5800->3201|5832->3206|6025->3372|6036->3374|6162->3478|6198->3487|6325->3587|6363->3604|6409->3622|6439->3630|6485->3648|6516->3657|6558->3670
                  LINES: 24->1|25->2|26->3|27->4|28->5|33->7|37->26|37->26|39->26|40->27|40->27|40->27|41->28|42->29|43->32|43->32|45->32|46->33|53->40|53->40|54->41|55->42|55->42|59->46|59->46|59->46|60->47|62->49|62->49|63->50|65->55|65->55|67->55|68->56|77->67|77->67|79->67|80->68|83->71|83->71|83->71|84->72|85->76|85->76|87->76|88->77|89->78|89->78|90->79|97->86|97->86|102->9|102->9|104->13|104->13|109->20|109->20|114->7|116->11|118->18|120->24|122->30|124->52|127->65|129->73|132->91|134->93|134->93|134->93|135->94|137->96|137->96|137->96|138->97|140->99|140->99|141->100|141->100|142->101|142->101|143->102
                  -- GENERATED --
              */
          