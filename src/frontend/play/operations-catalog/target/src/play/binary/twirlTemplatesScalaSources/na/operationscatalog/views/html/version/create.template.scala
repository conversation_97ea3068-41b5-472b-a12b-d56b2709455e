
package na.operationscatalog.views.html.version

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import na.operationscatalog.settings.{AAAPIResources, OperationsCatalogConstants}
/*3.2*/import na.catalog.basemodule.ui.{I18n, Label, WebComponent}
/*4.2*/import na.operationscatalog.views.html.common.saveEntityButtons
/*5.2*/import na.operationscatalog.views.html.skeletons.mainSkel
/*6.2*/import scala.collection.immutable.ListMap

object create extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[Map[String, WebComponent],WebComponent,WebComponent,List[WebComponent],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*8.2*/(headerComponents: Map[String, WebComponent], entityVersionComponent: WebComponent,operationVersionComponent: WebComponent, elements: List[WebComponent]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*10.2*/headerActions/*10.15*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*10.19*/("""
    """),_display_(/*11.6*/saveEntityButtons/*11.23*/.render(
        Array(AAAPIResources.operationsCatalog.C.toString),
        ListMap("data-page-action" -> "submitEntity", "id" -> "forms_button_save", "class" -> "disabled"),
        ListMap("data-page-action" -> "cancel", "id" -> "forms_button_cancel")
    )),format.raw/*15.6*/("""
""")))};def /*18.2*/header/*18.8*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*18.12*/("""
    """),format.raw/*19.5*/("""<span class="fx-entity-header-icon">
        <i class="fuxicons fuxicons-catalogue"></i>
    </span>
    <div class="fx-entity-header-title">
        <div class="form-inline">
            """),_display_(/*24.14*/headerComponents("name")/*24.38*/.render()),format.raw/*24.47*/("""
            """),format.raw/*25.13*/("""<span> - """),_display_(/*25.23*/headerComponents("state")/*25.48*/.render()),format.raw/*25.57*/("""</span>
        </div>
        <p class="fx-entity-header-details">"""),_display_(/*27.46*/headerComponents("description")/*27.77*/.render()),format.raw/*27.86*/("""</p>
    </div>
""")))};def /*31.2*/tabs/*31.6*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*31.10*/("""
    """),_display_(/*32.6*/na/*32.8*/.naportalbase.views.html.skeletons.tabs.contentTabSkel.render/*32.69*/ {_display_(Seq[Any](format.raw/*32.71*/("""
        """),_display_(/*33.10*/na/*33.12*/.naportalbase.views.html.components.tabs.contentTabComp.render(
            isActive = true,
            tabLabel = i18n("na.portal.operationscatalog.tab.characteristics"),
            attributes = null
        )),format.raw/*37.10*/("""
    """)))}),format.raw/*38.6*/("""
""")))};def /*41.2*/helpMessagePlaceholder/*41.24*/(message: String):play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*41.45*/("""
    """),format.raw/*42.5*/("""<!-- help message placeholder -->
    <div class="fx-input-group-addon fx-input-group-feedback">
        <button
            class="fx-alert-button fx-alert-button-help"
            type="button"
            data-enable-popover
            data-toggle="popover"
            data-placement="right auto"
            data-content=""""),_display_(/*50.28*/i18n(message)),format.raw/*50.41*/(""""
        >
            <i class="fuxicons fuxicons-help"></i>
        </button>
    </div>
""")))};
Seq[Any](format.raw/*8.155*/("""

"""),format.raw/*16.2*/("""

"""),format.raw/*29.2*/("""

"""),format.raw/*39.2*/("""

"""),format.raw/*55.2*/("""

"""),_display_(/*57.2*/mainSkel()/*57.12*/ {_display_(Seq[Any](format.raw/*57.14*/("""
    """),format.raw/*58.5*/("""<div class="page__container">
        """),_display_(/*59.10*/na/*59.12*/.operationscatalog.views.html.common.lateralNav.render(OperationsCatalogConstants.VERSION_CONTEXT)),format.raw/*59.110*/("""
        """),format.raw/*60.9*/("""<div
            id="version-form"
            class="fx-main-content-wrapper fx-with-main-nav"
            data-na-portal-operations-catalog-version-create-page>
            """),_display_(/*64.14*/na/*64.16*/.naportalbase.views.html.skeletons.pageHeaderSkel.render(header, headerActions, null, tabs)),format.raw/*64.107*/("""
            """),format.raw/*65.13*/("""<div id="fx-entity-content">
                <div class="tab-content">
                    <div class="tab-pane active">
                        <div class="operations-catalog__details-page--form-content">
                            <div class="form-horizontal" id="create-version-form">

                                    <!-- create version from -->
                                    <div class="fx-form-block-controls">
                                        <div id="from-version" class="form-group">
                                                <!-- row 1 -->
                                            <div class="row">
                                                    <!-- create version label -->
                                                <div class="col-sm-2 control-label">
                                                """),_display_(/*78.50*/{
                                                    val label = new Label("", new I18n().setKey("na.portal.operationscatalog.version.create.label"))
                                                    label.addClass("fx-required-field")
                                                    label.render()
                                                }),format.raw/*82.50*/("""
                                                """),format.raw/*83.49*/("""</div>
                                                    <!-- from entities version -->
                                                <div class="col-sm-6">
                                                    <div class="form-group has-help">
                                                        <div class="col-sm-6 form-control-static">
                                                            <input id="from-entities-version" type="radio" name="createVersion" value="fromEntitiesVersion" disabled>
                                                            <label for="from-entities-version">"""),_display_(/*89.97*/i18n("na.portal.operationscatalog.version.fromEntitiesVersion.label")),format.raw/*89.166*/("""</label><br>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <div class="fx-input-group">
                                                                """),_display_(/*93.66*/entityVersionComponent/*93.88*/.render()),format.raw/*93.97*/("""
                                                                """),_display_(/*94.66*/helpMessagePlaceholder("na.portal.operationscatalog.version.create.noEntitiesVersions")),format.raw/*94.153*/("""
                                                            """),format.raw/*95.61*/("""</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- row 2 -->
                                            <div class="row">
                                                <!-- from operations version -->
                                                <div class="col-sm-6 col-sm-offset-2">
                                                    <div class="form-group has-help">
                                                        <div class="col-sm-6">
                                                            <input id="from-operations-version" type="radio" name="createVersion" value="fromOperationVersion" disabled>
                                                            <label for="from-operations-version">"""),_display_(/*107.99*/i18n("na.portal.operationscatalog.version.fromOperationsVersion.label")),format.raw/*107.170*/("""</label>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <div class="fx-input-group">
                                                                """),_display_(/*111.66*/operationVersionComponent/*111.91*/.render()),format.raw/*111.100*/("""
                                                                """),_display_(/*112.66*/helpMessagePlaceholder("na.portal.operationscatalog.version.create.noOperationsVersions")),format.raw/*112.155*/("""
                                                            """),format.raw/*113.61*/("""</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
""")))}))
      }
    }
  }

  def render(headerComponents:Map[String, WebComponent],entityVersionComponent:WebComponent,operationVersionComponent:WebComponent,elements:List[WebComponent]): play.twirl.api.HtmlFormat.Appendable = apply(headerComponents,entityVersionComponent,operationVersionComponent,elements)

  def f:((Map[String, WebComponent],WebComponent,WebComponent,List[WebComponent]) => play.twirl.api.HtmlFormat.Appendable) = (headerComponents,entityVersionComponent,operationVersionComponent,elements) => apply(headerComponents,entityVersionComponent,operationVersionComponent,elements)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:51 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/target/TwirlSource/na/operationscatalog/views/version/create.scala.html
                  HASH: 60550192058baa82b8c9e35e56be1d41a1714892
                  MATRIX: 680->1|726->41|815->124|882->185|953->250|1018->309|1429->353|1661->509|1683->522|1764->526|1796->532|1822->549|2102->809|2127->814|2141->820|2222->824|2254->829|2470->1018|2503->1042|2533->1051|2574->1064|2611->1074|2645->1099|2675->1108|2770->1176|2810->1207|2840->1216|2880->1236|2892->1240|2973->1244|3005->1250|3015->1252|3085->1313|3125->1315|3162->1325|3173->1327|3406->1539|3442->1545|3467->1550|3498->1572|3596->1593|3628->1598|3984->1927|4018->1940|4151->506|4180->811|4209->1233|4238->1547|4267->2033|4296->2036|4315->2046|4355->2048|4387->2053|4453->2092|4464->2094|4584->2192|4620->2201|4823->2377|4834->2379|4947->2470|4988->2483|5867->3335|6243->3690|6320->3739|6955->4347|7046->4416|7382->4725|7413->4747|7443->4756|7536->4822|7645->4909|7734->4970|8718->5926|8812->5997|9145->6302|9180->6327|9212->6336|9306->6402|9418->6491|9508->6552
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|34->8|38->10|38->10|40->10|41->11|41->11|45->15|46->18|46->18|48->18|49->19|54->24|54->24|54->24|55->25|55->25|55->25|55->25|57->27|57->27|57->27|59->31|59->31|61->31|62->32|62->32|62->32|62->32|63->33|63->33|67->37|68->38|69->41|69->41|71->41|72->42|80->50|80->50|86->8|88->16|90->29|92->39|94->55|96->57|96->57|96->57|97->58|98->59|98->59|98->59|99->60|103->64|103->64|103->64|104->65|117->78|121->82|122->83|128->89|128->89|132->93|132->93|132->93|133->94|133->94|134->95|146->107|146->107|150->111|150->111|150->111|151->112|151->112|152->113
                  -- GENERATED --
              */
          