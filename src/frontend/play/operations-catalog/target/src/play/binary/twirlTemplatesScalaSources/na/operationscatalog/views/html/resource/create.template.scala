
package na.operationscatalog.views.html.resource

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object create extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[List[na.catalog.basemodule.ui.WebComponent],Map[String, na.catalog.basemodule.ui.WebComponent],Long,na.operationscatalog.models.operations.Operation,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        elements: List[na.catalog.basemodule.ui.WebComponent],
        headerComponents: Map[String, na.catalog.basemodule.ui.WebComponent],
        typeId: Long,
        operation: na.operationscatalog.models.operations.Operation
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*8.2*/import na.operationscatalog.settings.{AAAPIResources, OperationsCatalogConstants}
/*9.2*/import pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext.Action


Seq[Any](format.raw/*6.2*/("""

"""),format.raw/*10.1*/("""
"""),_display_(/*11.2*/na/*11.4*/.operationscatalog.views.html.operations.createOrEdit.render(
    iconClass = "fuxicons fuxicons-resource",
    operationContext = OperationsCatalogConstants.RESOURCE_CONTEXT,
    pageDirective = "data-na-portal-operations-catalog-resource-create-page",
    elements = elements,
    headerComponents = headerComponents,
    permission = AAAPIResources.operationsCatalog.C.toString,
    action = Action.CREATE,
    typeId = typeId,
    operation = operation
)))
      }
    }
  }

  def render(elements:List[na.catalog.basemodule.ui.WebComponent],headerComponents:Map[String, na.catalog.basemodule.ui.WebComponent],typeId:Long,operation:na.operationscatalog.models.operations.Operation): play.twirl.api.HtmlFormat.Appendable = apply(elements,headerComponents,typeId,operation)

  def f:((List[na.catalog.basemodule.ui.WebComponent],Map[String, na.catalog.basemodule.ui.WebComponent],Long,na.operationscatalog.models.operations.Operation) => play.twirl.api.HtmlFormat.Appendable) = (elements,headerComponents,typeId,operation) => apply(elements,headerComponents,typeId,operation)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:51 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/target/TwirlSource/na/operationscatalog/views/resource/create.scala.html
                  HASH: b6c75c52a5a8e8b58f809d6a0ccd14f8bf3d4ff0
                  MATRIX: 1121->1|1428->238|1517->321|1618->235|1647->394|1675->396|1685->398
                  LINES: 28->1|36->8|37->9|40->6|42->10|43->11|43->11
                  -- GENERATED --
              */
          