// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/conf/operationscatalog.routes
// @DATE:Tue Jul 01 11:21:43 WEST 2025

package operationscatalog

import play.core.routing._
import play.core.routing.HandlerInvokerFactory._

import play.api.mvc._

import _root_.controllers.Assets.Asset

class Routes(
  override val errorHandler: play.api.http.HttpErrorHandler, 
  // @LINE:6
  Application_1: na.operationscatalog.controllers.Application,
  // @LINE:16
  Assets_2: na.operationscatalog.controllers.Assets,
  // @LINE:22
  VersionController_0: na.operationscatalog.controllers.VersionController,
  // @LINE:49
  CfsController_8: na.operationscatalog.controllers.CfsController,
  // @LINE:62
  RfsController_4: na.operationscatalog.controllers.RfsController,
  // @LINE:75
  ResourceController_5: na.operationscatalog.controllers.ResourceController,
  // @LINE:88
  OperationController_6: na.operationscatalog.controllers.OperationController,
  // @LINE:106
  ResponseCodeController_7: na.operationscatalog.controllers.ResponseCodeController,
  // @LINE:115
  TagsController_9: na.operationscatalog.controllers.TagsController,
  // @LINE:122
  CacheController_3: javax.inject.Provider[na.operationscatalog.controllers.CacheController],
  // @LINE:136
  naportalbase_Routes_0: naportalbase.Routes,
  val prefix: String
) extends GeneratedRouter {

   @javax.inject.Inject()
   def this(errorHandler: play.api.http.HttpErrorHandler,
    // @LINE:6
    Application_1: na.operationscatalog.controllers.Application,
    // @LINE:16
    Assets_2: na.operationscatalog.controllers.Assets,
    // @LINE:22
    VersionController_0: na.operationscatalog.controllers.VersionController,
    // @LINE:49
    CfsController_8: na.operationscatalog.controllers.CfsController,
    // @LINE:62
    RfsController_4: na.operationscatalog.controllers.RfsController,
    // @LINE:75
    ResourceController_5: na.operationscatalog.controllers.ResourceController,
    // @LINE:88
    OperationController_6: na.operationscatalog.controllers.OperationController,
    // @LINE:106
    ResponseCodeController_7: na.operationscatalog.controllers.ResponseCodeController,
    // @LINE:115
    TagsController_9: na.operationscatalog.controllers.TagsController,
    // @LINE:122
    CacheController_3: javax.inject.Provider[na.operationscatalog.controllers.CacheController],
    // @LINE:136
    naportalbase_Routes_0: naportalbase.Routes
  ) = this(errorHandler, Application_1, Assets_2, VersionController_0, CfsController_8, RfsController_4, ResourceController_5, OperationController_6, ResponseCodeController_7, TagsController_9, CacheController_3, naportalbase_Routes_0, "/")

  def withPrefix(prefix: String): Routes = {
    operationscatalog.RoutesPrefix.setPrefix(prefix)
    new Routes(errorHandler, Application_1, Assets_2, VersionController_0, CfsController_8, RfsController_4, ResourceController_5, OperationController_6, ResponseCodeController_7, TagsController_9, CacheController_3, naportalbase_Routes_0, prefix)
  }

  private[this] val defaultPrefix: String = {
    if (this.prefix.endsWith("/")) "" else "/"
  }

  def documentation = List(
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/home""", """na.operationscatalog.controllers.Application.goToHome()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/resume""", """na.operationscatalog.controllers.Application.resume()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/assets/javascripts/routes""", """na.operationscatalog.controllers.Application.javascriptRoutes()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/assets/""" + "$" + """file<.+>""", """na.operationscatalog.controllers.Assets.versioned(path:String = "/public", file:Asset)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/versions/home""", """na.operationscatalog.controllers.VersionController.home()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/versions/create""", """na.operationscatalog.controllers.VersionController.createPage()"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/versions/create""", """na.operationscatalog.controllers.VersionController.create()"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/versions/search""", """na.operationscatalog.controllers.VersionController.versionsSearch()"""),
    ("""DELETE""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/versions/""" + "$" + """id<[^/]+>""", """na.operationscatalog.controllers.VersionController.delete(id:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/versions/view/""" + "$" + """id<[^/]+>""", """na.operationscatalog.controllers.VersionController.view(id:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/versions/edit/""" + "$" + """id<[^/]+>""", """na.operationscatalog.controllers.VersionController.getEditPage(id:Long)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/versions/edit/""" + "$" + """id<[^/]+>""", """na.operationscatalog.controllers.VersionController.update(id:Long)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/versions/clone/""" + "$" + """id<[^/]+>""", """na.operationscatalog.controllers.VersionController.cloneVersion(id:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/versions/context/search""", """na.operationscatalog.controllers.VersionController.versionsSearchForSelect()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/versions/context/search/""" + "$" + """id<[^/]+>""", """na.operationscatalog.controllers.VersionController.versionByIdForSelect(id:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/versions/validVersions""", """na.operationscatalog.controllers.VersionController.getValidVersionsForSelect"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/versions/entitiesVersions""", """na.operationscatalog.controllers.VersionController.versionEntitiesSearch()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/versions/entitiesVersionsForSelect""", """na.operationscatalog.controllers.VersionController.entitiesVersionsForSelect()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/versions/entityVersions""", """na.operationscatalog.controllers.VersionController.entityVersions()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/versions/entities/cache/""" + "$" + """name<[^/]+>""", """na.operationscatalog.controllers.VersionController.versionEntitiesCache(name:String)"""),
    ("""PUT""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/versions/""" + "$" + """id<[^/]+>/state""", """na.operationscatalog.controllers.VersionController.transitionVersionState(id:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/versions/datatable/config""", """na.operationscatalog.controllers.VersionController.versionsDataTableSearch()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/versions/datatable/view/operations/""" + "$" + """version<[^/]+>""", """na.operationscatalog.controllers.VersionController.viewOperationsDataTable(version:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/versions/datatable/view/responseCodes/""" + "$" + """version<[^/]+>""", """na.operationscatalog.controllers.VersionController.viewResponseCodesDataTable(version:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/versions/datatable/search/operations/""" + "$" + """version<[^/]+>""", """na.operationscatalog.controllers.VersionController.operationsSearch(version:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/versions/datatable/search/responseCodes/""" + "$" + """version<[^/]+>""", """na.operationscatalog.controllers.VersionController.responseCodesSearch(version:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/cfs/home""", """na.operationscatalog.controllers.CfsController.home()"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/cfs/search""", """na.operationscatalog.controllers.CfsController.cfsSearch()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/cfs/view/""" + "$" + """id<[^/]+>""", """na.operationscatalog.controllers.CfsController.view(id:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/cfs/edit/""" + "$" + """id<[^/]+>""", """na.operationscatalog.controllers.CfsController.getEditPage(id:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/cfs/create/""" + "$" + """versionId<[^/]+>""", """na.operationscatalog.controllers.CfsController.getCreatePage(versionId:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/cfs/datatable/config/search""", """na.operationscatalog.controllers.CfsController.cfsDataTableSearch()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/cfs/entitiesCfs/""" + "$" + """version<[^/]+>""", """na.operationscatalog.controllers.CfsController.entitiesSearch(version:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/cfs/entitiesByVersion/""" + "$" + """entityVersion<[^/]+>""", """na.operationscatalog.controllers.CfsController.entitiesByVersion(entityVersion:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/rfs/home""", """na.operationscatalog.controllers.RfsController.home()"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/rfs/search""", """na.operationscatalog.controllers.RfsController.rfsSearch()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/rfs/view/""" + "$" + """id<[^/]+>""", """na.operationscatalog.controllers.RfsController.view(id:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/rfs/edit/""" + "$" + """id<[^/]+>""", """na.operationscatalog.controllers.RfsController.getEditPage(id:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/rfs/create/""" + "$" + """versionId<[^/]+>""", """na.operationscatalog.controllers.RfsController.getCreatePage(versionId:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/rfs/datatable/config/search""", """na.operationscatalog.controllers.RfsController.rfsDataTableSearch()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/rfs/entitiesRfs/""" + "$" + """version<[^/]+>""", """na.operationscatalog.controllers.RfsController.entitiesSearch(version:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/rfs/entitiesByVersion/""" + "$" + """entityVersion<[^/]+>""", """na.operationscatalog.controllers.RfsController.entitiesByVersion(entityVersion:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/resources/home""", """na.operationscatalog.controllers.ResourceController.home()"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/resources/search""", """na.operationscatalog.controllers.ResourceController.resourceSearch()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/resources/view/""" + "$" + """id<[^/]+>""", """na.operationscatalog.controllers.ResourceController.view(id:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/resources/edit/""" + "$" + """id<[^/]+>""", """na.operationscatalog.controllers.ResourceController.getEditPage(id:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/resources/create/""" + "$" + """versionId<[^/]+>""", """na.operationscatalog.controllers.ResourceController.getCreatePage(versionId:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/resources/datatable/config/search""", """na.operationscatalog.controllers.ResourceController.resourcesDataTableSearch()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/resources/entitiesResource/""" + "$" + """version<[^/]+>""", """na.operationscatalog.controllers.ResourceController.entitiesSearch(version:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/resources/entitiesByVersion/""" + "$" + """entityVersion<[^/]+>""", """na.operationscatalog.controllers.ResourceController.entitiesByVersion(entityVersion:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/operation/""" + "$" + """id<[^/]+>""", """na.operationscatalog.controllers.OperationController.get(id:Long)"""),
    ("""DELETE""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/operation/""" + "$" + """id<[^/]+>""", """na.operationscatalog.controllers.OperationController.delete(id:Long)"""),
    ("""PUT""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/operation/""" + "$" + """id<[^/]+>/state/""" + "$" + """stateId<[^/]+>""", """na.operationscatalog.controllers.OperationController.transitionState(id:Long, stateId:Long)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/operation/create""", """na.operationscatalog.controllers.OperationController.create()"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/operation/clone/""" + "$" + """id<[^/]+>""", """na.operationscatalog.controllers.OperationController.cloneOperation(id:Long)"""),
    ("""PUT""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/operation/update/""" + "$" + """id<[^/]+>""", """na.operationscatalog.controllers.OperationController.update(id:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/operation/modal/inputAttribute/""" + "$" + """action<[^/]+>""", """na.operationscatalog.controllers.OperationController.inputAttributeModal(action:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/operation/entities/""" + "$" + """versionName<[^/]+>/""" + "$" + """operationName<[^/]+>/relations""", """na.operationscatalog.controllers.OperationController.getEntityRelationTree(operationType:String, versionName:String, operationName:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/operation/datatable/config/executionAttributes""", """na.operationscatalog.controllers.OperationController.dataTableViewExecutionAttributes()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/operation/datatable/config/inputAttributes""", """na.operationscatalog.controllers.OperationController.dataTableViewInputAttributes()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/operation/datatable/config/editableInputAttributes""", """na.operationscatalog.controllers.OperationController.dataTableEditableInputAttributes()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/operation/datatable/config/outputAttributes""", """na.operationscatalog.controllers.OperationController.dataTableViewOutputAttributes()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/operation/datatable/config/outputAttributesCU""", """na.operationscatalog.controllers.OperationController.dataTableCreateOrEditOutputAttributes()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/responsecodes/home""", """na.operationscatalog.controllers.ResponseCodeController.home()"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/responsecodes/search""", """na.operationscatalog.controllers.ResponseCodeController.responseCodeSearch()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/responsecodes/datatable/config""", """na.operationscatalog.controllers.ResponseCodeController.responseCodesDataTableSearch()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/responsecodes/types""", """na.operationscatalog.controllers.ResponseCodeController.responseCodeTypes()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/tags""", """na.operationscatalog.controllers.TagsController.getTagsForSelect"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/cache/catEntity/""" + "$" + """name<[^/]+>""", """@na.operationscatalog.controllers.CacheController@.getCatEntity(name:String)"""),
    ("""PUT""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/cache/invalidate""", """@na.operationscatalog.controllers.CacheController@.invalidate"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """operationscatalog/editableVersionStates""", """na.operationscatalog.controllers.Application.getEditableVersionStates"""),
    prefixed_naportalbase_Routes_0_71.router.documentation,
    Nil
  ).foldLeft(List.empty[(String,String,String)]) { (s,e) => e.asInstanceOf[Any] match {
    case r @ (_,_,_) => s :+ r.asInstanceOf[(String,String,String)]
    case l => s ++ l.asInstanceOf[List[(String,String,String)]]
  }}


  // @LINE:6
  private[this] lazy val na_operationscatalog_controllers_Application_goToHome0_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/home")))
  )
  private[this] lazy val na_operationscatalog_controllers_Application_goToHome0_invoker = createInvoker(
    Application_1.goToHome(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.Application",
      "goToHome",
      Nil,
      "GET",
      this.prefix + """operationscatalog/home""",
      """ Home page""",
      Seq()
    )
  )

  // @LINE:9
  private[this] lazy val na_operationscatalog_controllers_Application_resume1_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/resume")))
  )
  private[this] lazy val na_operationscatalog_controllers_Application_resume1_invoker = createInvoker(
    Application_1.resume(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.Application",
      "resume",
      Nil,
      "GET",
      this.prefix + """operationscatalog/resume""",
      """ Resume""",
      Seq()
    )
  )

  // @LINE:12
  private[this] lazy val na_operationscatalog_controllers_Application_javascriptRoutes2_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/assets/javascripts/routes")))
  )
  private[this] lazy val na_operationscatalog_controllers_Application_javascriptRoutes2_invoker = createInvoker(
    Application_1.javascriptRoutes(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.Application",
      "javascriptRoutes",
      Nil,
      "GET",
      this.prefix + """operationscatalog/assets/javascripts/routes""",
      """ Javascript Routing""",
      Seq()
    )
  )

  // @LINE:16
  private[this] lazy val na_operationscatalog_controllers_Assets_versioned3_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/assets/"), DynamicPart("file", """.+""",false)))
  )
  private[this] lazy val na_operationscatalog_controllers_Assets_versioned3_invoker = createInvoker(
    Assets_2.versioned(fakeValue[String], fakeValue[Asset]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.Assets",
      "versioned",
      Seq(classOf[String], classOf[Asset]),
      "GET",
      this.prefix + """operationscatalog/assets/""" + "$" + """file<.+>""",
      """ Map static resources from the /public folder to the /assets URL path""",
      Seq()
    )
  )

  // @LINE:22
  private[this] lazy val na_operationscatalog_controllers_VersionController_home4_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/versions/home")))
  )
  private[this] lazy val na_operationscatalog_controllers_VersionController_home4_invoker = createInvoker(
    VersionController_0.home(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.VersionController",
      "home",
      Nil,
      "GET",
      this.prefix + """operationscatalog/versions/home""",
      """""",
      Seq()
    )
  )

  // @LINE:23
  private[this] lazy val na_operationscatalog_controllers_VersionController_createPage5_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/versions/create")))
  )
  private[this] lazy val na_operationscatalog_controllers_VersionController_createPage5_invoker = createInvoker(
    VersionController_0.createPage(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.VersionController",
      "createPage",
      Nil,
      "GET",
      this.prefix + """operationscatalog/versions/create""",
      """""",
      Seq()
    )
  )

  // @LINE:24
  private[this] lazy val na_operationscatalog_controllers_VersionController_create6_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/versions/create")))
  )
  private[this] lazy val na_operationscatalog_controllers_VersionController_create6_invoker = createInvoker(
    VersionController_0.create(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.VersionController",
      "create",
      Nil,
      "POST",
      this.prefix + """operationscatalog/versions/create""",
      """""",
      Seq()
    )
  )

  // @LINE:25
  private[this] lazy val na_operationscatalog_controllers_VersionController_versionsSearch7_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/versions/search")))
  )
  private[this] lazy val na_operationscatalog_controllers_VersionController_versionsSearch7_invoker = createInvoker(
    VersionController_0.versionsSearch(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.VersionController",
      "versionsSearch",
      Nil,
      "POST",
      this.prefix + """operationscatalog/versions/search""",
      """""",
      Seq()
    )
  )

  // @LINE:26
  private[this] lazy val na_operationscatalog_controllers_VersionController_delete8_route = Route("DELETE",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/versions/"), DynamicPart("id", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_VersionController_delete8_invoker = createInvoker(
    VersionController_0.delete(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.VersionController",
      "delete",
      Seq(classOf[Long]),
      "DELETE",
      this.prefix + """operationscatalog/versions/""" + "$" + """id<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:27
  private[this] lazy val na_operationscatalog_controllers_VersionController_view9_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/versions/view/"), DynamicPart("id", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_VersionController_view9_invoker = createInvoker(
    VersionController_0.view(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.VersionController",
      "view",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """operationscatalog/versions/view/""" + "$" + """id<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:28
  private[this] lazy val na_operationscatalog_controllers_VersionController_getEditPage10_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/versions/edit/"), DynamicPart("id", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_VersionController_getEditPage10_invoker = createInvoker(
    VersionController_0.getEditPage(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.VersionController",
      "getEditPage",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """operationscatalog/versions/edit/""" + "$" + """id<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:29
  private[this] lazy val na_operationscatalog_controllers_VersionController_update11_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/versions/edit/"), DynamicPart("id", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_VersionController_update11_invoker = createInvoker(
    VersionController_0.update(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.VersionController",
      "update",
      Seq(classOf[Long]),
      "POST",
      this.prefix + """operationscatalog/versions/edit/""" + "$" + """id<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:30
  private[this] lazy val na_operationscatalog_controllers_VersionController_cloneVersion12_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/versions/clone/"), DynamicPart("id", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_VersionController_cloneVersion12_invoker = createInvoker(
    VersionController_0.cloneVersion(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.VersionController",
      "cloneVersion",
      Seq(classOf[Long]),
      "POST",
      this.prefix + """operationscatalog/versions/clone/""" + "$" + """id<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:31
  private[this] lazy val na_operationscatalog_controllers_VersionController_versionsSearchForSelect13_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/versions/context/search")))
  )
  private[this] lazy val na_operationscatalog_controllers_VersionController_versionsSearchForSelect13_invoker = createInvoker(
    VersionController_0.versionsSearchForSelect(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.VersionController",
      "versionsSearchForSelect",
      Nil,
      "GET",
      this.prefix + """operationscatalog/versions/context/search""",
      """""",
      Seq()
    )
  )

  // @LINE:32
  private[this] lazy val na_operationscatalog_controllers_VersionController_versionByIdForSelect14_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/versions/context/search/"), DynamicPart("id", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_VersionController_versionByIdForSelect14_invoker = createInvoker(
    VersionController_0.versionByIdForSelect(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.VersionController",
      "versionByIdForSelect",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """operationscatalog/versions/context/search/""" + "$" + """id<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:33
  private[this] lazy val na_operationscatalog_controllers_VersionController_getValidVersionsForSelect15_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/versions/validVersions")))
  )
  private[this] lazy val na_operationscatalog_controllers_VersionController_getValidVersionsForSelect15_invoker = createInvoker(
    VersionController_0.getValidVersionsForSelect,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.VersionController",
      "getValidVersionsForSelect",
      Nil,
      "GET",
      this.prefix + """operationscatalog/versions/validVersions""",
      """""",
      Seq()
    )
  )

  // @LINE:34
  private[this] lazy val na_operationscatalog_controllers_VersionController_versionEntitiesSearch16_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/versions/entitiesVersions")))
  )
  private[this] lazy val na_operationscatalog_controllers_VersionController_versionEntitiesSearch16_invoker = createInvoker(
    VersionController_0.versionEntitiesSearch(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.VersionController",
      "versionEntitiesSearch",
      Nil,
      "GET",
      this.prefix + """operationscatalog/versions/entitiesVersions""",
      """""",
      Seq()
    )
  )

  // @LINE:35
  private[this] lazy val na_operationscatalog_controllers_VersionController_entitiesVersionsForSelect17_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/versions/entitiesVersionsForSelect")))
  )
  private[this] lazy val na_operationscatalog_controllers_VersionController_entitiesVersionsForSelect17_invoker = createInvoker(
    VersionController_0.entitiesVersionsForSelect(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.VersionController",
      "entitiesVersionsForSelect",
      Nil,
      "GET",
      this.prefix + """operationscatalog/versions/entitiesVersionsForSelect""",
      """""",
      Seq()
    )
  )

  // @LINE:36
  private[this] lazy val na_operationscatalog_controllers_VersionController_entityVersions18_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/versions/entityVersions")))
  )
  private[this] lazy val na_operationscatalog_controllers_VersionController_entityVersions18_invoker = createInvoker(
    VersionController_0.entityVersions(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.VersionController",
      "entityVersions",
      Nil,
      "GET",
      this.prefix + """operationscatalog/versions/entityVersions""",
      """""",
      Seq()
    )
  )

  // @LINE:37
  private[this] lazy val na_operationscatalog_controllers_VersionController_versionEntitiesCache19_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/versions/entities/cache/"), DynamicPart("name", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_VersionController_versionEntitiesCache19_invoker = createInvoker(
    VersionController_0.versionEntitiesCache(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.VersionController",
      "versionEntitiesCache",
      Seq(classOf[String]),
      "GET",
      this.prefix + """operationscatalog/versions/entities/cache/""" + "$" + """name<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:38
  private[this] lazy val na_operationscatalog_controllers_VersionController_transitionVersionState20_route = Route("PUT",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/versions/"), DynamicPart("id", """[^/]+""",true), StaticPart("/state")))
  )
  private[this] lazy val na_operationscatalog_controllers_VersionController_transitionVersionState20_invoker = createInvoker(
    VersionController_0.transitionVersionState(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.VersionController",
      "transitionVersionState",
      Seq(classOf[Long]),
      "PUT",
      this.prefix + """operationscatalog/versions/""" + "$" + """id<[^/]+>/state""",
      """""",
      Seq()
    )
  )

  // @LINE:39
  private[this] lazy val na_operationscatalog_controllers_VersionController_versionsDataTableSearch21_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/versions/datatable/config")))
  )
  private[this] lazy val na_operationscatalog_controllers_VersionController_versionsDataTableSearch21_invoker = createInvoker(
    VersionController_0.versionsDataTableSearch(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.VersionController",
      "versionsDataTableSearch",
      Nil,
      "GET",
      this.prefix + """operationscatalog/versions/datatable/config""",
      """""",
      Seq()
    )
  )

  // @LINE:40
  private[this] lazy val na_operationscatalog_controllers_VersionController_viewOperationsDataTable22_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/versions/datatable/view/operations/"), DynamicPart("version", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_VersionController_viewOperationsDataTable22_invoker = createInvoker(
    VersionController_0.viewOperationsDataTable(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.VersionController",
      "viewOperationsDataTable",
      Seq(classOf[String]),
      "GET",
      this.prefix + """operationscatalog/versions/datatable/view/operations/""" + "$" + """version<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:41
  private[this] lazy val na_operationscatalog_controllers_VersionController_viewResponseCodesDataTable23_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/versions/datatable/view/responseCodes/"), DynamicPart("version", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_VersionController_viewResponseCodesDataTable23_invoker = createInvoker(
    VersionController_0.viewResponseCodesDataTable(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.VersionController",
      "viewResponseCodesDataTable",
      Seq(classOf[String]),
      "GET",
      this.prefix + """operationscatalog/versions/datatable/view/responseCodes/""" + "$" + """version<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:42
  private[this] lazy val na_operationscatalog_controllers_VersionController_operationsSearch24_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/versions/datatable/search/operations/"), DynamicPart("version", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_VersionController_operationsSearch24_invoker = createInvoker(
    VersionController_0.operationsSearch(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.VersionController",
      "operationsSearch",
      Seq(classOf[String]),
      "POST",
      this.prefix + """operationscatalog/versions/datatable/search/operations/""" + "$" + """version<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:43
  private[this] lazy val na_operationscatalog_controllers_VersionController_responseCodesSearch25_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/versions/datatable/search/responseCodes/"), DynamicPart("version", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_VersionController_responseCodesSearch25_invoker = createInvoker(
    VersionController_0.responseCodesSearch(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.VersionController",
      "responseCodesSearch",
      Seq(classOf[String]),
      "POST",
      this.prefix + """operationscatalog/versions/datatable/search/responseCodes/""" + "$" + """version<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:49
  private[this] lazy val na_operationscatalog_controllers_CfsController_home26_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/cfs/home")))
  )
  private[this] lazy val na_operationscatalog_controllers_CfsController_home26_invoker = createInvoker(
    CfsController_8.home(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.CfsController",
      "home",
      Nil,
      "GET",
      this.prefix + """operationscatalog/cfs/home""",
      """""",
      Seq()
    )
  )

  // @LINE:50
  private[this] lazy val na_operationscatalog_controllers_CfsController_cfsSearch27_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/cfs/search")))
  )
  private[this] lazy val na_operationscatalog_controllers_CfsController_cfsSearch27_invoker = createInvoker(
    CfsController_8.cfsSearch(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.CfsController",
      "cfsSearch",
      Nil,
      "POST",
      this.prefix + """operationscatalog/cfs/search""",
      """""",
      Seq()
    )
  )

  // @LINE:51
  private[this] lazy val na_operationscatalog_controllers_CfsController_view28_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/cfs/view/"), DynamicPart("id", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_CfsController_view28_invoker = createInvoker(
    CfsController_8.view(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.CfsController",
      "view",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """operationscatalog/cfs/view/""" + "$" + """id<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:52
  private[this] lazy val na_operationscatalog_controllers_CfsController_getEditPage29_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/cfs/edit/"), DynamicPart("id", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_CfsController_getEditPage29_invoker = createInvoker(
    CfsController_8.getEditPage(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.CfsController",
      "getEditPage",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """operationscatalog/cfs/edit/""" + "$" + """id<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:53
  private[this] lazy val na_operationscatalog_controllers_CfsController_getCreatePage30_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/cfs/create/"), DynamicPart("versionId", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_CfsController_getCreatePage30_invoker = createInvoker(
    CfsController_8.getCreatePage(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.CfsController",
      "getCreatePage",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """operationscatalog/cfs/create/""" + "$" + """versionId<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:54
  private[this] lazy val na_operationscatalog_controllers_CfsController_cfsDataTableSearch31_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/cfs/datatable/config/search")))
  )
  private[this] lazy val na_operationscatalog_controllers_CfsController_cfsDataTableSearch31_invoker = createInvoker(
    CfsController_8.cfsDataTableSearch(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.CfsController",
      "cfsDataTableSearch",
      Nil,
      "GET",
      this.prefix + """operationscatalog/cfs/datatable/config/search""",
      """""",
      Seq()
    )
  )

  // @LINE:55
  private[this] lazy val na_operationscatalog_controllers_CfsController_entitiesSearch32_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/cfs/entitiesCfs/"), DynamicPart("version", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_CfsController_entitiesSearch32_invoker = createInvoker(
    CfsController_8.entitiesSearch(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.CfsController",
      "entitiesSearch",
      Seq(classOf[String]),
      "GET",
      this.prefix + """operationscatalog/cfs/entitiesCfs/""" + "$" + """version<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:56
  private[this] lazy val na_operationscatalog_controllers_CfsController_entitiesByVersion33_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/cfs/entitiesByVersion/"), DynamicPart("entityVersion", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_CfsController_entitiesByVersion33_invoker = createInvoker(
    CfsController_8.entitiesByVersion(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.CfsController",
      "entitiesByVersion",
      Seq(classOf[String]),
      "GET",
      this.prefix + """operationscatalog/cfs/entitiesByVersion/""" + "$" + """entityVersion<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:62
  private[this] lazy val na_operationscatalog_controllers_RfsController_home34_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/rfs/home")))
  )
  private[this] lazy val na_operationscatalog_controllers_RfsController_home34_invoker = createInvoker(
    RfsController_4.home(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.RfsController",
      "home",
      Nil,
      "GET",
      this.prefix + """operationscatalog/rfs/home""",
      """""",
      Seq()
    )
  )

  // @LINE:63
  private[this] lazy val na_operationscatalog_controllers_RfsController_rfsSearch35_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/rfs/search")))
  )
  private[this] lazy val na_operationscatalog_controllers_RfsController_rfsSearch35_invoker = createInvoker(
    RfsController_4.rfsSearch(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.RfsController",
      "rfsSearch",
      Nil,
      "POST",
      this.prefix + """operationscatalog/rfs/search""",
      """""",
      Seq()
    )
  )

  // @LINE:64
  private[this] lazy val na_operationscatalog_controllers_RfsController_view36_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/rfs/view/"), DynamicPart("id", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_RfsController_view36_invoker = createInvoker(
    RfsController_4.view(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.RfsController",
      "view",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """operationscatalog/rfs/view/""" + "$" + """id<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:65
  private[this] lazy val na_operationscatalog_controllers_RfsController_getEditPage37_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/rfs/edit/"), DynamicPart("id", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_RfsController_getEditPage37_invoker = createInvoker(
    RfsController_4.getEditPage(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.RfsController",
      "getEditPage",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """operationscatalog/rfs/edit/""" + "$" + """id<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:66
  private[this] lazy val na_operationscatalog_controllers_RfsController_getCreatePage38_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/rfs/create/"), DynamicPart("versionId", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_RfsController_getCreatePage38_invoker = createInvoker(
    RfsController_4.getCreatePage(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.RfsController",
      "getCreatePage",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """operationscatalog/rfs/create/""" + "$" + """versionId<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:67
  private[this] lazy val na_operationscatalog_controllers_RfsController_rfsDataTableSearch39_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/rfs/datatable/config/search")))
  )
  private[this] lazy val na_operationscatalog_controllers_RfsController_rfsDataTableSearch39_invoker = createInvoker(
    RfsController_4.rfsDataTableSearch(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.RfsController",
      "rfsDataTableSearch",
      Nil,
      "GET",
      this.prefix + """operationscatalog/rfs/datatable/config/search""",
      """""",
      Seq()
    )
  )

  // @LINE:68
  private[this] lazy val na_operationscatalog_controllers_RfsController_entitiesSearch40_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/rfs/entitiesRfs/"), DynamicPart("version", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_RfsController_entitiesSearch40_invoker = createInvoker(
    RfsController_4.entitiesSearch(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.RfsController",
      "entitiesSearch",
      Seq(classOf[String]),
      "GET",
      this.prefix + """operationscatalog/rfs/entitiesRfs/""" + "$" + """version<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:69
  private[this] lazy val na_operationscatalog_controllers_RfsController_entitiesByVersion41_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/rfs/entitiesByVersion/"), DynamicPart("entityVersion", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_RfsController_entitiesByVersion41_invoker = createInvoker(
    RfsController_4.entitiesByVersion(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.RfsController",
      "entitiesByVersion",
      Seq(classOf[String]),
      "GET",
      this.prefix + """operationscatalog/rfs/entitiesByVersion/""" + "$" + """entityVersion<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:75
  private[this] lazy val na_operationscatalog_controllers_ResourceController_home42_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/resources/home")))
  )
  private[this] lazy val na_operationscatalog_controllers_ResourceController_home42_invoker = createInvoker(
    ResourceController_5.home(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.ResourceController",
      "home",
      Nil,
      "GET",
      this.prefix + """operationscatalog/resources/home""",
      """""",
      Seq()
    )
  )

  // @LINE:76
  private[this] lazy val na_operationscatalog_controllers_ResourceController_resourceSearch43_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/resources/search")))
  )
  private[this] lazy val na_operationscatalog_controllers_ResourceController_resourceSearch43_invoker = createInvoker(
    ResourceController_5.resourceSearch(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.ResourceController",
      "resourceSearch",
      Nil,
      "POST",
      this.prefix + """operationscatalog/resources/search""",
      """""",
      Seq()
    )
  )

  // @LINE:77
  private[this] lazy val na_operationscatalog_controllers_ResourceController_view44_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/resources/view/"), DynamicPart("id", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_ResourceController_view44_invoker = createInvoker(
    ResourceController_5.view(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.ResourceController",
      "view",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """operationscatalog/resources/view/""" + "$" + """id<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:78
  private[this] lazy val na_operationscatalog_controllers_ResourceController_getEditPage45_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/resources/edit/"), DynamicPart("id", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_ResourceController_getEditPage45_invoker = createInvoker(
    ResourceController_5.getEditPage(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.ResourceController",
      "getEditPage",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """operationscatalog/resources/edit/""" + "$" + """id<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:79
  private[this] lazy val na_operationscatalog_controllers_ResourceController_getCreatePage46_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/resources/create/"), DynamicPart("versionId", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_ResourceController_getCreatePage46_invoker = createInvoker(
    ResourceController_5.getCreatePage(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.ResourceController",
      "getCreatePage",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """operationscatalog/resources/create/""" + "$" + """versionId<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:80
  private[this] lazy val na_operationscatalog_controllers_ResourceController_resourcesDataTableSearch47_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/resources/datatable/config/search")))
  )
  private[this] lazy val na_operationscatalog_controllers_ResourceController_resourcesDataTableSearch47_invoker = createInvoker(
    ResourceController_5.resourcesDataTableSearch(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.ResourceController",
      "resourcesDataTableSearch",
      Nil,
      "GET",
      this.prefix + """operationscatalog/resources/datatable/config/search""",
      """""",
      Seq()
    )
  )

  // @LINE:81
  private[this] lazy val na_operationscatalog_controllers_ResourceController_entitiesSearch48_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/resources/entitiesResource/"), DynamicPart("version", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_ResourceController_entitiesSearch48_invoker = createInvoker(
    ResourceController_5.entitiesSearch(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.ResourceController",
      "entitiesSearch",
      Seq(classOf[String]),
      "GET",
      this.prefix + """operationscatalog/resources/entitiesResource/""" + "$" + """version<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:82
  private[this] lazy val na_operationscatalog_controllers_ResourceController_entitiesByVersion49_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/resources/entitiesByVersion/"), DynamicPart("entityVersion", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_ResourceController_entitiesByVersion49_invoker = createInvoker(
    ResourceController_5.entitiesByVersion(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.ResourceController",
      "entitiesByVersion",
      Seq(classOf[String]),
      "GET",
      this.prefix + """operationscatalog/resources/entitiesByVersion/""" + "$" + """entityVersion<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:88
  private[this] lazy val na_operationscatalog_controllers_OperationController_get50_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/operation/"), DynamicPart("id", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_OperationController_get50_invoker = createInvoker(
    OperationController_6.get(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.OperationController",
      "get",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """operationscatalog/operation/""" + "$" + """id<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:89
  private[this] lazy val na_operationscatalog_controllers_OperationController_delete51_route = Route("DELETE",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/operation/"), DynamicPart("id", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_OperationController_delete51_invoker = createInvoker(
    OperationController_6.delete(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.OperationController",
      "delete",
      Seq(classOf[Long]),
      "DELETE",
      this.prefix + """operationscatalog/operation/""" + "$" + """id<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:90
  private[this] lazy val na_operationscatalog_controllers_OperationController_transitionState52_route = Route("PUT",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/operation/"), DynamicPart("id", """[^/]+""",true), StaticPart("/state/"), DynamicPart("stateId", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_OperationController_transitionState52_invoker = createInvoker(
    OperationController_6.transitionState(fakeValue[Long], fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.OperationController",
      "transitionState",
      Seq(classOf[Long], classOf[Long]),
      "PUT",
      this.prefix + """operationscatalog/operation/""" + "$" + """id<[^/]+>/state/""" + "$" + """stateId<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:91
  private[this] lazy val na_operationscatalog_controllers_OperationController_create53_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/operation/create")))
  )
  private[this] lazy val na_operationscatalog_controllers_OperationController_create53_invoker = createInvoker(
    OperationController_6.create(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.OperationController",
      "create",
      Nil,
      "POST",
      this.prefix + """operationscatalog/operation/create""",
      """""",
      Seq()
    )
  )

  // @LINE:92
  private[this] lazy val na_operationscatalog_controllers_OperationController_cloneOperation54_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/operation/clone/"), DynamicPart("id", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_OperationController_cloneOperation54_invoker = createInvoker(
    OperationController_6.cloneOperation(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.OperationController",
      "cloneOperation",
      Seq(classOf[Long]),
      "POST",
      this.prefix + """operationscatalog/operation/clone/""" + "$" + """id<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:93
  private[this] lazy val na_operationscatalog_controllers_OperationController_update55_route = Route("PUT",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/operation/update/"), DynamicPart("id", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_OperationController_update55_invoker = createInvoker(
    OperationController_6.update(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.OperationController",
      "update",
      Seq(classOf[Long]),
      "PUT",
      this.prefix + """operationscatalog/operation/update/""" + "$" + """id<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:94
  private[this] lazy val na_operationscatalog_controllers_OperationController_inputAttributeModal56_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/operation/modal/inputAttribute/"), DynamicPart("action", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_OperationController_inputAttributeModal56_invoker = createInvoker(
    OperationController_6.inputAttributeModal(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.OperationController",
      "inputAttributeModal",
      Seq(classOf[String]),
      "GET",
      this.prefix + """operationscatalog/operation/modal/inputAttribute/""" + "$" + """action<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:95
  private[this] lazy val na_operationscatalog_controllers_OperationController_getEntityRelationTree57_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/operation/entities/"), DynamicPart("versionName", """[^/]+""",true), StaticPart("/"), DynamicPart("operationName", """[^/]+""",true), StaticPart("/relations")))
  )
  private[this] lazy val na_operationscatalog_controllers_OperationController_getEntityRelationTree57_invoker = createInvoker(
    OperationController_6.getEntityRelationTree(fakeValue[String], fakeValue[String], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.OperationController",
      "getEntityRelationTree",
      Seq(classOf[String], classOf[String], classOf[String]),
      "GET",
      this.prefix + """operationscatalog/operation/entities/""" + "$" + """versionName<[^/]+>/""" + "$" + """operationName<[^/]+>/relations""",
      """""",
      Seq()
    )
  )

  // @LINE:96
  private[this] lazy val na_operationscatalog_controllers_OperationController_dataTableViewExecutionAttributes58_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/operation/datatable/config/executionAttributes")))
  )
  private[this] lazy val na_operationscatalog_controllers_OperationController_dataTableViewExecutionAttributes58_invoker = createInvoker(
    OperationController_6.dataTableViewExecutionAttributes(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.OperationController",
      "dataTableViewExecutionAttributes",
      Nil,
      "GET",
      this.prefix + """operationscatalog/operation/datatable/config/executionAttributes""",
      """""",
      Seq()
    )
  )

  // @LINE:97
  private[this] lazy val na_operationscatalog_controllers_OperationController_dataTableViewInputAttributes59_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/operation/datatable/config/inputAttributes")))
  )
  private[this] lazy val na_operationscatalog_controllers_OperationController_dataTableViewInputAttributes59_invoker = createInvoker(
    OperationController_6.dataTableViewInputAttributes(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.OperationController",
      "dataTableViewInputAttributes",
      Nil,
      "GET",
      this.prefix + """operationscatalog/operation/datatable/config/inputAttributes""",
      """""",
      Seq()
    )
  )

  // @LINE:98
  private[this] lazy val na_operationscatalog_controllers_OperationController_dataTableEditableInputAttributes60_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/operation/datatable/config/editableInputAttributes")))
  )
  private[this] lazy val na_operationscatalog_controllers_OperationController_dataTableEditableInputAttributes60_invoker = createInvoker(
    OperationController_6.dataTableEditableInputAttributes(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.OperationController",
      "dataTableEditableInputAttributes",
      Nil,
      "GET",
      this.prefix + """operationscatalog/operation/datatable/config/editableInputAttributes""",
      """""",
      Seq()
    )
  )

  // @LINE:99
  private[this] lazy val na_operationscatalog_controllers_OperationController_dataTableViewOutputAttributes61_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/operation/datatable/config/outputAttributes")))
  )
  private[this] lazy val na_operationscatalog_controllers_OperationController_dataTableViewOutputAttributes61_invoker = createInvoker(
    OperationController_6.dataTableViewOutputAttributes(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.OperationController",
      "dataTableViewOutputAttributes",
      Nil,
      "GET",
      this.prefix + """operationscatalog/operation/datatable/config/outputAttributes""",
      """""",
      Seq()
    )
  )

  // @LINE:100
  private[this] lazy val na_operationscatalog_controllers_OperationController_dataTableCreateOrEditOutputAttributes62_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/operation/datatable/config/outputAttributesCU")))
  )
  private[this] lazy val na_operationscatalog_controllers_OperationController_dataTableCreateOrEditOutputAttributes62_invoker = createInvoker(
    OperationController_6.dataTableCreateOrEditOutputAttributes(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.OperationController",
      "dataTableCreateOrEditOutputAttributes",
      Nil,
      "GET",
      this.prefix + """operationscatalog/operation/datatable/config/outputAttributesCU""",
      """""",
      Seq()
    )
  )

  // @LINE:106
  private[this] lazy val na_operationscatalog_controllers_ResponseCodeController_home63_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/responsecodes/home")))
  )
  private[this] lazy val na_operationscatalog_controllers_ResponseCodeController_home63_invoker = createInvoker(
    ResponseCodeController_7.home(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.ResponseCodeController",
      "home",
      Nil,
      "GET",
      this.prefix + """operationscatalog/responsecodes/home""",
      """""",
      Seq()
    )
  )

  // @LINE:107
  private[this] lazy val na_operationscatalog_controllers_ResponseCodeController_responseCodeSearch64_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/responsecodes/search")))
  )
  private[this] lazy val na_operationscatalog_controllers_ResponseCodeController_responseCodeSearch64_invoker = createInvoker(
    ResponseCodeController_7.responseCodeSearch(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.ResponseCodeController",
      "responseCodeSearch",
      Nil,
      "POST",
      this.prefix + """operationscatalog/responsecodes/search""",
      """""",
      Seq()
    )
  )

  // @LINE:108
  private[this] lazy val na_operationscatalog_controllers_ResponseCodeController_responseCodesDataTableSearch65_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/responsecodes/datatable/config")))
  )
  private[this] lazy val na_operationscatalog_controllers_ResponseCodeController_responseCodesDataTableSearch65_invoker = createInvoker(
    ResponseCodeController_7.responseCodesDataTableSearch(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.ResponseCodeController",
      "responseCodesDataTableSearch",
      Nil,
      "GET",
      this.prefix + """operationscatalog/responsecodes/datatable/config""",
      """""",
      Seq()
    )
  )

  // @LINE:109
  private[this] lazy val na_operationscatalog_controllers_ResponseCodeController_responseCodeTypes66_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/responsecodes/types")))
  )
  private[this] lazy val na_operationscatalog_controllers_ResponseCodeController_responseCodeTypes66_invoker = createInvoker(
    ResponseCodeController_7.responseCodeTypes(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.ResponseCodeController",
      "responseCodeTypes",
      Nil,
      "GET",
      this.prefix + """operationscatalog/responsecodes/types""",
      """""",
      Seq()
    )
  )

  // @LINE:115
  private[this] lazy val na_operationscatalog_controllers_TagsController_getTagsForSelect67_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/tags")))
  )
  private[this] lazy val na_operationscatalog_controllers_TagsController_getTagsForSelect67_invoker = createInvoker(
    TagsController_9.getTagsForSelect,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.TagsController",
      "getTagsForSelect",
      Nil,
      "GET",
      this.prefix + """operationscatalog/tags""",
      """""",
      Seq()
    )
  )

  // @LINE:122
  private[this] lazy val na_operationscatalog_controllers_CacheController_getCatEntity68_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/cache/catEntity/"), DynamicPart("name", """[^/]+""",true)))
  )
  private[this] lazy val na_operationscatalog_controllers_CacheController_getCatEntity68_invoker = createInvoker(
    CacheController_3.get.getCatEntity(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.CacheController",
      "getCatEntity",
      Seq(classOf[String]),
      "GET",
      this.prefix + """operationscatalog/cache/catEntity/""" + "$" + """name<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:123
  private[this] lazy val na_operationscatalog_controllers_CacheController_invalidate69_route = Route("PUT",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/cache/invalidate")))
  )
  private[this] lazy val na_operationscatalog_controllers_CacheController_invalidate69_invoker = createInvoker(
    CacheController_3.get.invalidate,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.CacheController",
      "invalidate",
      Nil,
      "PUT",
      this.prefix + """operationscatalog/cache/invalidate""",
      """""",
      Seq()
    )
  )

  // @LINE:129
  private[this] lazy val na_operationscatalog_controllers_Application_getEditableVersionStates70_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("operationscatalog/editableVersionStates")))
  )
  private[this] lazy val na_operationscatalog_controllers_Application_getEditableVersionStates70_invoker = createInvoker(
    Application_1.getEditableVersionStates,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "operationscatalog",
      "na.operationscatalog.controllers.Application",
      "getEditableVersionStates",
      Nil,
      "GET",
      this.prefix + """operationscatalog/editableVersionStates""",
      """""",
      Seq()
    )
  )

  // @LINE:136
  private[this] val prefixed_naportalbase_Routes_0_71 = Include(naportalbase_Routes_0.withPrefix(this.prefix + (if (this.prefix.endsWith("/")) "" else "/") + "operationscatalog"))


  def routes: PartialFunction[RequestHeader, Handler] = {
  
    // @LINE:6
    case na_operationscatalog_controllers_Application_goToHome0_route(params@_) =>
      call { 
        na_operationscatalog_controllers_Application_goToHome0_invoker.call(Application_1.goToHome())
      }
  
    // @LINE:9
    case na_operationscatalog_controllers_Application_resume1_route(params@_) =>
      call { 
        na_operationscatalog_controllers_Application_resume1_invoker.call(Application_1.resume())
      }
  
    // @LINE:12
    case na_operationscatalog_controllers_Application_javascriptRoutes2_route(params@_) =>
      call { 
        na_operationscatalog_controllers_Application_javascriptRoutes2_invoker.call(Application_1.javascriptRoutes())
      }
  
    // @LINE:16
    case na_operationscatalog_controllers_Assets_versioned3_route(params@_) =>
      call(Param[String]("path", Right("/public")), params.fromPath[Asset]("file", None)) { (path, file) =>
        na_operationscatalog_controllers_Assets_versioned3_invoker.call(Assets_2.versioned(path, file))
      }
  
    // @LINE:22
    case na_operationscatalog_controllers_VersionController_home4_route(params@_) =>
      call { 
        na_operationscatalog_controllers_VersionController_home4_invoker.call(VersionController_0.home())
      }
  
    // @LINE:23
    case na_operationscatalog_controllers_VersionController_createPage5_route(params@_) =>
      call { 
        na_operationscatalog_controllers_VersionController_createPage5_invoker.call(VersionController_0.createPage())
      }
  
    // @LINE:24
    case na_operationscatalog_controllers_VersionController_create6_route(params@_) =>
      call { 
        na_operationscatalog_controllers_VersionController_create6_invoker.call(VersionController_0.create())
      }
  
    // @LINE:25
    case na_operationscatalog_controllers_VersionController_versionsSearch7_route(params@_) =>
      call { 
        na_operationscatalog_controllers_VersionController_versionsSearch7_invoker.call(VersionController_0.versionsSearch())
      }
  
    // @LINE:26
    case na_operationscatalog_controllers_VersionController_delete8_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_operationscatalog_controllers_VersionController_delete8_invoker.call(VersionController_0.delete(id))
      }
  
    // @LINE:27
    case na_operationscatalog_controllers_VersionController_view9_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_operationscatalog_controllers_VersionController_view9_invoker.call(VersionController_0.view(id))
      }
  
    // @LINE:28
    case na_operationscatalog_controllers_VersionController_getEditPage10_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_operationscatalog_controllers_VersionController_getEditPage10_invoker.call(VersionController_0.getEditPage(id))
      }
  
    // @LINE:29
    case na_operationscatalog_controllers_VersionController_update11_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_operationscatalog_controllers_VersionController_update11_invoker.call(VersionController_0.update(id))
      }
  
    // @LINE:30
    case na_operationscatalog_controllers_VersionController_cloneVersion12_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_operationscatalog_controllers_VersionController_cloneVersion12_invoker.call(VersionController_0.cloneVersion(id))
      }
  
    // @LINE:31
    case na_operationscatalog_controllers_VersionController_versionsSearchForSelect13_route(params@_) =>
      call { 
        na_operationscatalog_controllers_VersionController_versionsSearchForSelect13_invoker.call(VersionController_0.versionsSearchForSelect())
      }
  
    // @LINE:32
    case na_operationscatalog_controllers_VersionController_versionByIdForSelect14_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_operationscatalog_controllers_VersionController_versionByIdForSelect14_invoker.call(VersionController_0.versionByIdForSelect(id))
      }
  
    // @LINE:33
    case na_operationscatalog_controllers_VersionController_getValidVersionsForSelect15_route(params@_) =>
      call { 
        na_operationscatalog_controllers_VersionController_getValidVersionsForSelect15_invoker.call(VersionController_0.getValidVersionsForSelect)
      }
  
    // @LINE:34
    case na_operationscatalog_controllers_VersionController_versionEntitiesSearch16_route(params@_) =>
      call { 
        na_operationscatalog_controllers_VersionController_versionEntitiesSearch16_invoker.call(VersionController_0.versionEntitiesSearch())
      }
  
    // @LINE:35
    case na_operationscatalog_controllers_VersionController_entitiesVersionsForSelect17_route(params@_) =>
      call { 
        na_operationscatalog_controllers_VersionController_entitiesVersionsForSelect17_invoker.call(VersionController_0.entitiesVersionsForSelect())
      }
  
    // @LINE:36
    case na_operationscatalog_controllers_VersionController_entityVersions18_route(params@_) =>
      call { 
        na_operationscatalog_controllers_VersionController_entityVersions18_invoker.call(VersionController_0.entityVersions())
      }
  
    // @LINE:37
    case na_operationscatalog_controllers_VersionController_versionEntitiesCache19_route(params@_) =>
      call(params.fromPath[String]("name", None)) { (name) =>
        na_operationscatalog_controllers_VersionController_versionEntitiesCache19_invoker.call(VersionController_0.versionEntitiesCache(name))
      }
  
    // @LINE:38
    case na_operationscatalog_controllers_VersionController_transitionVersionState20_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_operationscatalog_controllers_VersionController_transitionVersionState20_invoker.call(VersionController_0.transitionVersionState(id))
      }
  
    // @LINE:39
    case na_operationscatalog_controllers_VersionController_versionsDataTableSearch21_route(params@_) =>
      call { 
        na_operationscatalog_controllers_VersionController_versionsDataTableSearch21_invoker.call(VersionController_0.versionsDataTableSearch())
      }
  
    // @LINE:40
    case na_operationscatalog_controllers_VersionController_viewOperationsDataTable22_route(params@_) =>
      call(params.fromPath[String]("version", None)) { (version) =>
        na_operationscatalog_controllers_VersionController_viewOperationsDataTable22_invoker.call(VersionController_0.viewOperationsDataTable(version))
      }
  
    // @LINE:41
    case na_operationscatalog_controllers_VersionController_viewResponseCodesDataTable23_route(params@_) =>
      call(params.fromPath[String]("version", None)) { (version) =>
        na_operationscatalog_controllers_VersionController_viewResponseCodesDataTable23_invoker.call(VersionController_0.viewResponseCodesDataTable(version))
      }
  
    // @LINE:42
    case na_operationscatalog_controllers_VersionController_operationsSearch24_route(params@_) =>
      call(params.fromPath[String]("version", None)) { (version) =>
        na_operationscatalog_controllers_VersionController_operationsSearch24_invoker.call(VersionController_0.operationsSearch(version))
      }
  
    // @LINE:43
    case na_operationscatalog_controllers_VersionController_responseCodesSearch25_route(params@_) =>
      call(params.fromPath[String]("version", None)) { (version) =>
        na_operationscatalog_controllers_VersionController_responseCodesSearch25_invoker.call(VersionController_0.responseCodesSearch(version))
      }
  
    // @LINE:49
    case na_operationscatalog_controllers_CfsController_home26_route(params@_) =>
      call { 
        na_operationscatalog_controllers_CfsController_home26_invoker.call(CfsController_8.home())
      }
  
    // @LINE:50
    case na_operationscatalog_controllers_CfsController_cfsSearch27_route(params@_) =>
      call { 
        na_operationscatalog_controllers_CfsController_cfsSearch27_invoker.call(CfsController_8.cfsSearch())
      }
  
    // @LINE:51
    case na_operationscatalog_controllers_CfsController_view28_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_operationscatalog_controllers_CfsController_view28_invoker.call(CfsController_8.view(id))
      }
  
    // @LINE:52
    case na_operationscatalog_controllers_CfsController_getEditPage29_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_operationscatalog_controllers_CfsController_getEditPage29_invoker.call(CfsController_8.getEditPage(id))
      }
  
    // @LINE:53
    case na_operationscatalog_controllers_CfsController_getCreatePage30_route(params@_) =>
      call(params.fromPath[Long]("versionId", None)) { (versionId) =>
        na_operationscatalog_controllers_CfsController_getCreatePage30_invoker.call(CfsController_8.getCreatePage(versionId))
      }
  
    // @LINE:54
    case na_operationscatalog_controllers_CfsController_cfsDataTableSearch31_route(params@_) =>
      call { 
        na_operationscatalog_controllers_CfsController_cfsDataTableSearch31_invoker.call(CfsController_8.cfsDataTableSearch())
      }
  
    // @LINE:55
    case na_operationscatalog_controllers_CfsController_entitiesSearch32_route(params@_) =>
      call(params.fromPath[String]("version", None)) { (version) =>
        na_operationscatalog_controllers_CfsController_entitiesSearch32_invoker.call(CfsController_8.entitiesSearch(version))
      }
  
    // @LINE:56
    case na_operationscatalog_controllers_CfsController_entitiesByVersion33_route(params@_) =>
      call(params.fromPath[String]("entityVersion", None)) { (entityVersion) =>
        na_operationscatalog_controllers_CfsController_entitiesByVersion33_invoker.call(CfsController_8.entitiesByVersion(entityVersion))
      }
  
    // @LINE:62
    case na_operationscatalog_controllers_RfsController_home34_route(params@_) =>
      call { 
        na_operationscatalog_controllers_RfsController_home34_invoker.call(RfsController_4.home())
      }
  
    // @LINE:63
    case na_operationscatalog_controllers_RfsController_rfsSearch35_route(params@_) =>
      call { 
        na_operationscatalog_controllers_RfsController_rfsSearch35_invoker.call(RfsController_4.rfsSearch())
      }
  
    // @LINE:64
    case na_operationscatalog_controllers_RfsController_view36_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_operationscatalog_controllers_RfsController_view36_invoker.call(RfsController_4.view(id))
      }
  
    // @LINE:65
    case na_operationscatalog_controllers_RfsController_getEditPage37_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_operationscatalog_controllers_RfsController_getEditPage37_invoker.call(RfsController_4.getEditPage(id))
      }
  
    // @LINE:66
    case na_operationscatalog_controllers_RfsController_getCreatePage38_route(params@_) =>
      call(params.fromPath[Long]("versionId", None)) { (versionId) =>
        na_operationscatalog_controllers_RfsController_getCreatePage38_invoker.call(RfsController_4.getCreatePage(versionId))
      }
  
    // @LINE:67
    case na_operationscatalog_controllers_RfsController_rfsDataTableSearch39_route(params@_) =>
      call { 
        na_operationscatalog_controllers_RfsController_rfsDataTableSearch39_invoker.call(RfsController_4.rfsDataTableSearch())
      }
  
    // @LINE:68
    case na_operationscatalog_controllers_RfsController_entitiesSearch40_route(params@_) =>
      call(params.fromPath[String]("version", None)) { (version) =>
        na_operationscatalog_controllers_RfsController_entitiesSearch40_invoker.call(RfsController_4.entitiesSearch(version))
      }
  
    // @LINE:69
    case na_operationscatalog_controllers_RfsController_entitiesByVersion41_route(params@_) =>
      call(params.fromPath[String]("entityVersion", None)) { (entityVersion) =>
        na_operationscatalog_controllers_RfsController_entitiesByVersion41_invoker.call(RfsController_4.entitiesByVersion(entityVersion))
      }
  
    // @LINE:75
    case na_operationscatalog_controllers_ResourceController_home42_route(params@_) =>
      call { 
        na_operationscatalog_controllers_ResourceController_home42_invoker.call(ResourceController_5.home())
      }
  
    // @LINE:76
    case na_operationscatalog_controllers_ResourceController_resourceSearch43_route(params@_) =>
      call { 
        na_operationscatalog_controllers_ResourceController_resourceSearch43_invoker.call(ResourceController_5.resourceSearch())
      }
  
    // @LINE:77
    case na_operationscatalog_controllers_ResourceController_view44_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_operationscatalog_controllers_ResourceController_view44_invoker.call(ResourceController_5.view(id))
      }
  
    // @LINE:78
    case na_operationscatalog_controllers_ResourceController_getEditPage45_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_operationscatalog_controllers_ResourceController_getEditPage45_invoker.call(ResourceController_5.getEditPage(id))
      }
  
    // @LINE:79
    case na_operationscatalog_controllers_ResourceController_getCreatePage46_route(params@_) =>
      call(params.fromPath[Long]("versionId", None)) { (versionId) =>
        na_operationscatalog_controllers_ResourceController_getCreatePage46_invoker.call(ResourceController_5.getCreatePage(versionId))
      }
  
    // @LINE:80
    case na_operationscatalog_controllers_ResourceController_resourcesDataTableSearch47_route(params@_) =>
      call { 
        na_operationscatalog_controllers_ResourceController_resourcesDataTableSearch47_invoker.call(ResourceController_5.resourcesDataTableSearch())
      }
  
    // @LINE:81
    case na_operationscatalog_controllers_ResourceController_entitiesSearch48_route(params@_) =>
      call(params.fromPath[String]("version", None)) { (version) =>
        na_operationscatalog_controllers_ResourceController_entitiesSearch48_invoker.call(ResourceController_5.entitiesSearch(version))
      }
  
    // @LINE:82
    case na_operationscatalog_controllers_ResourceController_entitiesByVersion49_route(params@_) =>
      call(params.fromPath[String]("entityVersion", None)) { (entityVersion) =>
        na_operationscatalog_controllers_ResourceController_entitiesByVersion49_invoker.call(ResourceController_5.entitiesByVersion(entityVersion))
      }
  
    // @LINE:88
    case na_operationscatalog_controllers_OperationController_get50_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_operationscatalog_controllers_OperationController_get50_invoker.call(OperationController_6.get(id))
      }
  
    // @LINE:89
    case na_operationscatalog_controllers_OperationController_delete51_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_operationscatalog_controllers_OperationController_delete51_invoker.call(OperationController_6.delete(id))
      }
  
    // @LINE:90
    case na_operationscatalog_controllers_OperationController_transitionState52_route(params@_) =>
      call(params.fromPath[Long]("id", None), params.fromPath[Long]("stateId", None)) { (id, stateId) =>
        na_operationscatalog_controllers_OperationController_transitionState52_invoker.call(OperationController_6.transitionState(id, stateId))
      }
  
    // @LINE:91
    case na_operationscatalog_controllers_OperationController_create53_route(params@_) =>
      call { 
        na_operationscatalog_controllers_OperationController_create53_invoker.call(OperationController_6.create())
      }
  
    // @LINE:92
    case na_operationscatalog_controllers_OperationController_cloneOperation54_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_operationscatalog_controllers_OperationController_cloneOperation54_invoker.call(OperationController_6.cloneOperation(id))
      }
  
    // @LINE:93
    case na_operationscatalog_controllers_OperationController_update55_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_operationscatalog_controllers_OperationController_update55_invoker.call(OperationController_6.update(id))
      }
  
    // @LINE:94
    case na_operationscatalog_controllers_OperationController_inputAttributeModal56_route(params@_) =>
      call(params.fromPath[String]("action", None)) { (action) =>
        na_operationscatalog_controllers_OperationController_inputAttributeModal56_invoker.call(OperationController_6.inputAttributeModal(action))
      }
  
    // @LINE:95
    case na_operationscatalog_controllers_OperationController_getEntityRelationTree57_route(params@_) =>
      call(params.fromQuery[String]("operationType", None), params.fromPath[String]("versionName", None), params.fromPath[String]("operationName", None)) { (operationType, versionName, operationName) =>
        na_operationscatalog_controllers_OperationController_getEntityRelationTree57_invoker.call(OperationController_6.getEntityRelationTree(operationType, versionName, operationName))
      }
  
    // @LINE:96
    case na_operationscatalog_controllers_OperationController_dataTableViewExecutionAttributes58_route(params@_) =>
      call { 
        na_operationscatalog_controllers_OperationController_dataTableViewExecutionAttributes58_invoker.call(OperationController_6.dataTableViewExecutionAttributes())
      }
  
    // @LINE:97
    case na_operationscatalog_controllers_OperationController_dataTableViewInputAttributes59_route(params@_) =>
      call { 
        na_operationscatalog_controllers_OperationController_dataTableViewInputAttributes59_invoker.call(OperationController_6.dataTableViewInputAttributes())
      }
  
    // @LINE:98
    case na_operationscatalog_controllers_OperationController_dataTableEditableInputAttributes60_route(params@_) =>
      call { 
        na_operationscatalog_controllers_OperationController_dataTableEditableInputAttributes60_invoker.call(OperationController_6.dataTableEditableInputAttributes())
      }
  
    // @LINE:99
    case na_operationscatalog_controllers_OperationController_dataTableViewOutputAttributes61_route(params@_) =>
      call { 
        na_operationscatalog_controllers_OperationController_dataTableViewOutputAttributes61_invoker.call(OperationController_6.dataTableViewOutputAttributes())
      }
  
    // @LINE:100
    case na_operationscatalog_controllers_OperationController_dataTableCreateOrEditOutputAttributes62_route(params@_) =>
      call { 
        na_operationscatalog_controllers_OperationController_dataTableCreateOrEditOutputAttributes62_invoker.call(OperationController_6.dataTableCreateOrEditOutputAttributes())
      }
  
    // @LINE:106
    case na_operationscatalog_controllers_ResponseCodeController_home63_route(params@_) =>
      call { 
        na_operationscatalog_controllers_ResponseCodeController_home63_invoker.call(ResponseCodeController_7.home())
      }
  
    // @LINE:107
    case na_operationscatalog_controllers_ResponseCodeController_responseCodeSearch64_route(params@_) =>
      call { 
        na_operationscatalog_controllers_ResponseCodeController_responseCodeSearch64_invoker.call(ResponseCodeController_7.responseCodeSearch())
      }
  
    // @LINE:108
    case na_operationscatalog_controllers_ResponseCodeController_responseCodesDataTableSearch65_route(params@_) =>
      call { 
        na_operationscatalog_controllers_ResponseCodeController_responseCodesDataTableSearch65_invoker.call(ResponseCodeController_7.responseCodesDataTableSearch())
      }
  
    // @LINE:109
    case na_operationscatalog_controllers_ResponseCodeController_responseCodeTypes66_route(params@_) =>
      call { 
        na_operationscatalog_controllers_ResponseCodeController_responseCodeTypes66_invoker.call(ResponseCodeController_7.responseCodeTypes())
      }
  
    // @LINE:115
    case na_operationscatalog_controllers_TagsController_getTagsForSelect67_route(params@_) =>
      call { 
        na_operationscatalog_controllers_TagsController_getTagsForSelect67_invoker.call(TagsController_9.getTagsForSelect)
      }
  
    // @LINE:122
    case na_operationscatalog_controllers_CacheController_getCatEntity68_route(params@_) =>
      call(params.fromPath[String]("name", None)) { (name) =>
        na_operationscatalog_controllers_CacheController_getCatEntity68_invoker.call(CacheController_3.get.getCatEntity(name))
      }
  
    // @LINE:123
    case na_operationscatalog_controllers_CacheController_invalidate69_route(params@_) =>
      call { 
        na_operationscatalog_controllers_CacheController_invalidate69_invoker.call(CacheController_3.get.invalidate)
      }
  
    // @LINE:129
    case na_operationscatalog_controllers_Application_getEditableVersionStates70_route(params@_) =>
      call { 
        na_operationscatalog_controllers_Application_getEditableVersionStates70_invoker.call(Application_1.getEditableVersionStates)
      }
  
    // @LINE:136
    case prefixed_naportalbase_Routes_0_71(handler) => handler
  }
}
