// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/conf/operationscatalog.routes
// @DATE:Tue Jul 01 09:49:51 WEST 2025

package na.operationscatalog.controllers;

import operationscatalog.RoutesPrefix;

public class routes {
  
  public static final na.operationscatalog.controllers.ReverseAssets Assets = new na.operationscatalog.controllers.ReverseAssets(RoutesPrefix.byNamePrefix());
  public static final na.operationscatalog.controllers.ReverseResponseCodeController ResponseCodeController = new na.operationscatalog.controllers.ReverseResponseCodeController(RoutesPrefix.byNamePrefix());
  public static final na.operationscatalog.controllers.ReverseOperationController OperationController = new na.operationscatalog.controllers.ReverseOperationController(RoutesPrefix.byNamePrefix());
  public static final na.operationscatalog.controllers.ReverseApplication Application = new na.operationscatalog.controllers.ReverseApplication(RoutesPrefix.byNamePrefix());
  public static final na.operationscatalog.controllers.ReverseCfsController CfsController = new na.operationscatalog.controllers.ReverseCfsController(RoutesPrefix.byNamePrefix());
  public static final na.operationscatalog.controllers.ReverseRfsController RfsController = new na.operationscatalog.controllers.ReverseRfsController(RoutesPrefix.byNamePrefix());
  public static final na.operationscatalog.controllers.ReverseResourceController ResourceController = new na.operationscatalog.controllers.ReverseResourceController(RoutesPrefix.byNamePrefix());
  public static final na.operationscatalog.controllers.ReverseTagsController TagsController = new na.operationscatalog.controllers.ReverseTagsController(RoutesPrefix.byNamePrefix());
  public static final na.operationscatalog.controllers.ReverseVersionController VersionController = new na.operationscatalog.controllers.ReverseVersionController(RoutesPrefix.byNamePrefix());
  public static final na.operationscatalog.controllers.ReverseCacheController CacheController = new na.operationscatalog.controllers.ReverseCacheController(RoutesPrefix.byNamePrefix());

  public static class javascript {
    
    public static final na.operationscatalog.controllers.javascript.ReverseAssets Assets = new na.operationscatalog.controllers.javascript.ReverseAssets(RoutesPrefix.byNamePrefix());
    public static final na.operationscatalog.controllers.javascript.ReverseResponseCodeController ResponseCodeController = new na.operationscatalog.controllers.javascript.ReverseResponseCodeController(RoutesPrefix.byNamePrefix());
    public static final na.operationscatalog.controllers.javascript.ReverseOperationController OperationController = new na.operationscatalog.controllers.javascript.ReverseOperationController(RoutesPrefix.byNamePrefix());
    public static final na.operationscatalog.controllers.javascript.ReverseApplication Application = new na.operationscatalog.controllers.javascript.ReverseApplication(RoutesPrefix.byNamePrefix());
    public static final na.operationscatalog.controllers.javascript.ReverseCfsController CfsController = new na.operationscatalog.controllers.javascript.ReverseCfsController(RoutesPrefix.byNamePrefix());
    public static final na.operationscatalog.controllers.javascript.ReverseRfsController RfsController = new na.operationscatalog.controllers.javascript.ReverseRfsController(RoutesPrefix.byNamePrefix());
    public static final na.operationscatalog.controllers.javascript.ReverseResourceController ResourceController = new na.operationscatalog.controllers.javascript.ReverseResourceController(RoutesPrefix.byNamePrefix());
    public static final na.operationscatalog.controllers.javascript.ReverseTagsController TagsController = new na.operationscatalog.controllers.javascript.ReverseTagsController(RoutesPrefix.byNamePrefix());
    public static final na.operationscatalog.controllers.javascript.ReverseVersionController VersionController = new na.operationscatalog.controllers.javascript.ReverseVersionController(RoutesPrefix.byNamePrefix());
    public static final na.operationscatalog.controllers.javascript.ReverseCacheController CacheController = new na.operationscatalog.controllers.javascript.ReverseCacheController(RoutesPrefix.byNamePrefix());
  }

}
