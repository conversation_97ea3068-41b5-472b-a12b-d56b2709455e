
package na.operationscatalog.views.html.operations

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import na.operationscatalog.models.operations.Operation
/*3.2*/import na.operationscatalog.models.statemachine.State
/*4.2*/import na.operationscatalog.services.OperationPermissionService
/*5.2*/import na.operationscatalog.settings.AAAPIResources
/*6.2*/import na.catalog.basemodule.ui.{Label, WebComponent}
/*7.2*/import na.operationscatalog.views.html.skeletons.mainSkel
/*8.2*/import play.libs.Json
/*9.2*/import pt.alticelabs.nossis.security.views.html.authorized

object view extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template9[String,List[State],String,Long,String,String,Operation,List[WebComponent],OperationPermissionService,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*11.2*/(
        iconClass: String,
        stateList: List[State],
        operationContext: String,
        operationId: Long,
        storeDirective: String,
        viewPageDirective: String,
        operation: Operation,
        elements: List[WebComponent],
        operationPermissionService: OperationPermissionService
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*21.2*/("""

"""),_display_(/*23.2*/mainSkel(operation.getName)/*23.29*/ {_display_(Seq[Any](format.raw/*23.31*/("""
"""),format.raw/*24.1*/("""<x-i18n-attr-container>
    <div class="page__container">
        """),_display_(/*26.10*/na/*26.12*/.operationscatalog.views.html.common.lateralNav.render(operationContext)),format.raw/*26.84*/("""
        """),format.raw/*27.9*/("""<div class="page__content page__content--details-view" """),_display_(/*27.65*/storeDirective),format.raw/*27.79*/(""" """),_display_(/*27.81*/viewPageDirective),format.raw/*27.98*/(""" """),format.raw/*27.99*/("""data-operation-name=""""),_display_(/*27.121*/operation/*27.130*/.getName),format.raw/*27.138*/("""" data-operation-id=""""),_display_(/*27.160*/operation/*27.169*/.getId),format.raw/*27.175*/("""">
            <script data-initial-store-content type="application/json">
                """),_display_(/*29.18*/Json/*29.22*/.toJson(operation)),format.raw/*29.40*/("""
            """),format.raw/*30.13*/("""</script>
            <div class="fx-entity-header">
                <div class="fx-entity-header-info">
                    <span class="fx-entity-header-icon">
                        <i class=""""),_display_(/*34.36*/iconClass),format.raw/*34.45*/(""""></i>
                    </span>
                    <div class="fx-entity-header-title">
                        <h1><span id="name">"""),_display_(/*37.46*/operation/*37.55*/.getName),format.raw/*37.63*/("""</span> - <span id="entity">"""),_display_(/*37.92*/operation/*37.101*/.getEntityName),format.raw/*37.115*/("""</span> - <span id="stateIcon">"""),_display_(/*37.147*/stateIcon/*37.156*/.render(operation.getStateName)),format.raw/*37.187*/("""</span> <span id="stateName">"""),_display_(/*37.217*/operation/*37.226*/.getStateI18n),format.raw/*37.239*/("""</span></h1>
                        <p id="description">"""),_display_(/*38.46*/operation/*38.55*/.getDescription),format.raw/*38.70*/("""</p>
                    </div>
                </div>
                <div class="fx-entity-header-actions">
                    """),_display_(/*42.22*/authorized(AAAPIResources.operationsCatalog().U().toString)/*42.81*/{_display_(Seq[Any](format.raw/*42.82*/("""
                        """),format.raw/*43.25*/("""<a class="btn btn-default btn-sm """),_display_(/*43.59*/{"disabled".when(!operationPermissionService.canEdit(operation))}),format.raw/*43.124*/("""" data-i18n-title="na.details.title.edit" id="operation-edit-action" data-page-action="edit-operation">
                            <i class="glyphicon glyphicon-pencil"></i>
                        </a>
                    """)))}),format.raw/*46.22*/("""
                    """),_display_(/*47.22*/authorized(AAAPIResources.operationsCatalog().C().toString)/*47.81*/{_display_(Seq[Any](format.raw/*47.82*/("""
                        """),format.raw/*48.25*/("""<a class="btn btn-default btn-sm """),_display_(/*48.59*/{"disabled".when(!operationPermissionService.canClone(operation))}),format.raw/*48.125*/("""" data-i18n-title="na.details.title.clone" id="operation-clone-action" data-page-action="clone-operation">
                            <i class="fuxicons fuxicons-clone"></i>
                        </a>
                    """)))}),format.raw/*51.22*/("""
                    """),_display_(/*52.22*/authorized(AAAPIResources.operationsCatalog().D().toString)/*52.81*/{_display_(Seq[Any](format.raw/*52.82*/("""
                        """),format.raw/*53.25*/("""<a class="btn btn-default btn-sm """),_display_(/*53.59*/{"disabled".when(!operationPermissionService.canDelete(operation))}),format.raw/*53.126*/("""" data-i18n-title="na.details.title.delete" id="operation-delete-action" data-page-action="delete-operation">
                            <i class="glyphicon glyphicon-remove"></i>
                        </a>
                    """)))}),format.raw/*56.22*/("""
                    """),_display_(/*57.22*/authorized(AAAPIResources.operationsCatalog().U().toString)/*57.81*/ {_display_(Seq[Any](format.raw/*57.83*/("""
                        """),format.raw/*58.25*/("""<div class="btn-group" id="more_options">
                            <a class="btn btn-default btn-sm" onclick="return false;" data-i18n-title="na.portal.operationscatalog.button.multiactions.title" id="view_more_actions_button" data-toggle="dropdown">
                                <i class="glyphicon glyphicon-cog"></i>
                            </a>
                            <ul class="dropdown-menu fx-dropdown-open-to-left" role="menu">
                            """),_display_(/*63.30*/if(stateList.isEmpty)/*63.51*/{_display_(Seq[Any](format.raw/*63.52*/("""
                                """),format.raw/*64.33*/("""<li style="white-space: nowrap;" class="dropdown-header"><b>"""),_display_(/*64.94*/i18n("na.portal.operationscatalog.datatable.actions.state.noFurtherStates")),format.raw/*64.169*/("""</b></li>
                            """)))}/*65.30*/else/*65.34*/{_display_(Seq[Any](format.raw/*65.35*/("""
                                """),format.raw/*66.33*/("""<li style="white-space: nowrap;" class="dropdown-header"><b>"""),_display_(/*66.94*/i18n("na.portal.operationscatalog.datatable.actions.state.changeStateTo")),format.raw/*66.167*/("""</b></li>
                                """),_display_(/*67.34*/for(state <- stateList) yield /*67.57*/{_display_(Seq[Any](format.raw/*67.58*/("""
                                    """),format.raw/*68.37*/("""<li><a data-page-action="transition-to-state" data-state-id=""""),_display_(/*68.99*/state/*68.104*/.getId),format.raw/*68.110*/("""" data-state-name=""""),_display_(/*68.130*/state/*68.135*/.getName),format.raw/*68.143*/("""" data-state-i18n=""""),_display_(/*68.163*/state/*68.168*/.getI18n.toLowerCase),format.raw/*68.188*/("""" class="option"> """),_display_(/*68.207*/state/*68.212*/.getI18n.toLowerCase),format.raw/*68.232*/("""</a></li>
                                """)))}),format.raw/*69.34*/("""
                            """)))}),format.raw/*70.30*/("""
                            """),format.raw/*71.29*/("""</ul>
                        </div>
                    """)))}),format.raw/*73.22*/("""
                """),format.raw/*74.17*/("""</div>
                """),_display_(/*75.18*/if(!operation.isValid)/*75.40*/ {_display_(Seq[Any](format.raw/*75.42*/("""
                    """),format.raw/*76.21*/("""<div id="invalid-version-notification" class="invalid-notification invalid-version-notification alert alert-warning">
                        <div class="fx-alert-icon"></div>
                        <div id="description" class="fx-alert-message">
                            <x-i18n key="na.portal.operationscatalog.notification.this.operation.is.invalid"></x-i18n>
                        </div>
                    </div>
                """)))}),format.raw/*82.18*/("""
            """),format.raw/*83.13*/("""</div>
            <div class="container--entity-content">
                <x-tab-container>
                    <x-tab data-i18n-label="na.portal.operationscatalog.tab.characteristics">
                        <x-shadow-scroll flex>
                            <div id="na_catalog_section_caracterizacaoespecifica" class="tab-pane fx-form-vertical active">
                                <x-collapsible class="fx-section" data-i18n-header="na.portal.operationscatalog.tab.section.general.characteristics">
                                    <div class="fx-form-content fx-expandable-form-blocks">
                                        <div class="form-horizontal fx-toggle-form-vertical">
                                            <div class="fx-form-block-controls fx-first">
                                            """),_display_(/*93.46*/for(element <- elements) yield /*93.70*/ {_display_(Seq[Any](format.raw/*93.72*/("""
                                                """),format.raw/*94.49*/("""<div class="form-group">
                                                    <label class="col-sm-1 control-label" for="form-group-username">
                                                    """),_display_(/*96.54*/if(element.getI18n != null)/*96.81*/ {_display_(Seq[Any](format.raw/*96.83*/("""
                                                        """),_display_(/*97.58*/{new Label(element.getId, element.getI18n).render()}),format.raw/*97.110*/("""
                                                    """)))}),format.raw/*98.54*/("""
                                                    """),format.raw/*99.53*/("""</label>
                                                    <div class="col-sm-10">
                                                        <p class="form-control-static">
                                                        """),_display_(/*102.58*/element/*102.65*/.render()),format.raw/*102.74*/("""
                                                        """),format.raw/*103.57*/("""</p>
                                                    </div>
                                                </div>
                                            """)))}),format.raw/*106.46*/("""
                                            """),format.raw/*107.45*/("""</div>
                                        </div>
                                    </div>
                                </x-collapsible>

                                <x-collapsible class="fx-section" data-i18n-header="na.portal.operationscatalog.tab.section.input.attributes">
                                    <div class="fx-form-block-controls">
                                        <table
                                        class="table table-striped table-hover no-footer"
                                        id="operation-input-attributes-table"
                                        data-na-portal-table-datatable
                                        data-na-portal-operations-catalog-input-attributes-datatable
                                        data-config-url=""""),_display_(/*119.59*/na/*119.61*/.operationscatalog.controllers.routes.OperationController.dataTableViewInputAttributes()),format.raw/*119.149*/(""""></table>
                                    </div>
                                </x-collapsible>

                                <x-collapsible class="fx-section" data-i18n-header="na.portal.operationscatalog.tab.section.execution.attributes">
                                    <div class="fx-form-block-controls">
                                        <table
                                        class="table table-striped table-hover no-footer"
                                        id="operation-execution-attributes-table"
                                        data-na-portal-table-datatable
                                        data-na-portal-operations-catalog-execution-attributes-datatable
                                        data-config-url=""""),_display_(/*130.59*/na/*130.61*/.operationscatalog.controllers.routes.OperationController.dataTableViewExecutionAttributes()),format.raw/*130.153*/(""""></table>
                                    </div>
                                </x-collapsible>

                                <x-collapsible class="fx-section" data-i18n-header="na.portal.operationscatalog.tab.section.output.attributes">
                                    <div class="fx-form-block-controls fx-last-block">
                                        <table
                                        class="table table-striped table-hover no-footer"
                                        id="operation-output-attributes-table"
                                        data-na-portal-table-datatable
                                        data-na-portal-operations-catalog-output-attributes-datatable
                                        data-config-url=""""),_display_(/*141.59*/na/*141.61*/.operationscatalog.controllers.routes.OperationController.dataTableViewOutputAttributes()),format.raw/*141.150*/(""""></table>
                                    </div>
                                </x-collapsible>
                            </div>
                        </x-shadow-scroll>
                    </x-tab>
                </x-tab-container>
            </div>
        </div>
    </div>
</x-i18n-attr-container>

""")))}))
      }
    }
  }

  def render(iconClass:String,stateList:List[State],operationContext:String,operationId:Long,storeDirective:String,viewPageDirective:String,operation:Operation,elements:List[WebComponent],operationPermissionService:OperationPermissionService): play.twirl.api.HtmlFormat.Appendable = apply(iconClass,stateList,operationContext,operationId,storeDirective,viewPageDirective,operation,elements,operationPermissionService)

  def f:((String,List[State],String,Long,String,String,Operation,List[WebComponent],OperationPermissionService) => play.twirl.api.HtmlFormat.Appendable) = (iconClass,stateList,operationContext,operationId,storeDirective,viewPageDirective,operation,elements,operationPermissionService) => apply(iconClass,stateList,operationContext,operationId,storeDirective,viewPageDirective,operation,elements,operationPermissionService)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:43 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/target/TwirlSource/na/operationscatalog/views/operations/view.scala.html
                  HASH: 82183863fd24119b632d6349083e733fe9aad62b
                  MATRIX: 683->1|729->41|792->98|853->153|924->218|983->271|1044->326|1109->385|1138->408|1595->469|2011->790|2040->793|2076->820|2116->822|2144->823|2238->890|2249->892|2342->964|2378->973|2461->1029|2496->1043|2525->1045|2563->1062|2592->1063|2642->1085|2661->1094|2691->1102|2741->1124|2760->1133|2788->1139|2907->1231|2920->1235|2959->1253|3000->1266|3224->1463|3254->1472|3418->1609|3436->1618|3465->1626|3521->1655|3540->1664|3576->1678|3636->1710|3655->1719|3708->1750|3766->1780|3785->1789|3820->1802|3905->1860|3923->1869|3959->1884|4117->2015|4185->2074|4224->2075|4277->2100|4338->2134|4425->2199|4681->2424|4730->2446|4798->2505|4837->2506|4890->2531|4951->2565|5039->2631|5295->2856|5344->2878|5412->2937|5451->2938|5504->2963|5565->2997|5654->3064|5916->3295|5965->3317|6033->3376|6073->3378|6126->3403|6633->3883|6663->3904|6702->3905|6763->3938|6851->3999|6948->4074|7006->4113|7019->4117|7058->4118|7119->4151|7207->4212|7302->4285|7372->4328|7411->4351|7450->4352|7515->4389|7604->4451|7619->4456|7647->4462|7695->4482|7710->4487|7740->4495|7788->4515|7803->4520|7845->4540|7892->4559|7907->4564|7949->4584|8023->4627|8084->4657|8141->4686|8230->4744|8275->4761|8326->4785|8357->4807|8397->4809|8446->4830|8919->5272|8960->5285|9816->6114|9856->6138|9896->6140|9973->6189|10195->6384|10231->6411|10271->6413|10356->6471|10430->6523|10515->6577|10596->6630|10854->6860|10871->6867|10902->6876|10988->6933|11184->7097|11258->7142|12094->7950|12106->7952|12217->8040|13022->8817|13034->8819|13149->8911|13959->9693|13971->9695|14083->9784
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|32->9|37->11|52->21|54->23|54->23|54->23|55->24|57->26|57->26|57->26|58->27|58->27|58->27|58->27|58->27|58->27|58->27|58->27|58->27|58->27|58->27|58->27|60->29|60->29|60->29|61->30|65->34|65->34|68->37|68->37|68->37|68->37|68->37|68->37|68->37|68->37|68->37|68->37|68->37|68->37|69->38|69->38|69->38|73->42|73->42|73->42|74->43|74->43|74->43|77->46|78->47|78->47|78->47|79->48|79->48|79->48|82->51|83->52|83->52|83->52|84->53|84->53|84->53|87->56|88->57|88->57|88->57|89->58|94->63|94->63|94->63|95->64|95->64|95->64|96->65|96->65|96->65|97->66|97->66|97->66|98->67|98->67|98->67|99->68|99->68|99->68|99->68|99->68|99->68|99->68|99->68|99->68|99->68|99->68|99->68|99->68|100->69|101->70|102->71|104->73|105->74|106->75|106->75|106->75|107->76|113->82|114->83|124->93|124->93|124->93|125->94|127->96|127->96|127->96|128->97|128->97|129->98|130->99|133->102|133->102|133->102|134->103|137->106|138->107|150->119|150->119|150->119|161->130|161->130|161->130|172->141|172->141|172->141
                  -- GENERATED --
              */
          