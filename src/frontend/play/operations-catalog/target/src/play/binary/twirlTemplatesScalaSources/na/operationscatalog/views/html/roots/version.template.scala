
package na.operationscatalog.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.catalog.basemodule.ui.WebComponent
/*2.2*/import na.naportalbase.views.tags.i18n
/*3.2*/import na.operationscatalog.settings.{AAAPIResources, OperationsCatalogConstants}
/*4.2*/import na.operationscatalog.views.html.skeletons.mainSkel
/*5.2*/import pt.alticelabs.nossis.security.views.html.authorized

object version extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[java.util.List[WebComponent],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*7.2*/(elements: java.util.List[WebComponent]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*24.2*/headerAction/*24.14*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*24.18*/("""
"""),_display_(/*25.2*/authorized(AAAPIResources.operationsCatalog.X.toString)/*25.57*/ {_display_(Seq[Any](format.raw/*25.59*/("""
    """),_display_(/*26.6*/na/*26.8*/.naportalbase.views.html.components.buttonsComp.buttonComp.render(i18n("na.portal.operationscatalog.button.create"), newButtonAttrs, iconAttrs)),format.raw/*26.151*/("""
""")))}),format.raw/*27.2*/("""
""")))};def /*30.2*/pageHeader/*30.12*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*30.16*/("""
    """),format.raw/*31.5*/("""<div class="fx-entity-header page__search-results-header">
        <div class="fx-entity-header-info">
            <span class="fx-entity-header-icon">
                <i class="fuxicons fuxicons-catalogue"></i>
            </span>
            <div class="fx-entity-header-title">
                <h1>
                """),_display_(/*38.18*/i18n("na.portal.operationscatalog.nav.version")),format.raw/*38.65*/("""
                """),format.raw/*39.17*/("""</h1>
                <span class="nav-context">("""),_display_(/*40.45*/i18n("na.portal.operationscatalog.nav.context")),format.raw/*40.92*/(""")</span>
            </div>
        </div>
        <div class="fx-entity-header-actions">
            """),_display_(/*44.14*/headerAction),format.raw/*44.26*/("""
        """),format.raw/*45.9*/("""</div>
    </div>
""")))};def /*49.2*/rightSide/*49.11*/():play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*49.17*/("""
    """),format.raw/*50.5*/("""<div id="fx-splitter-content" class="page__search-results" slot="right">
        """),_display_(/*51.10*/pageHeader),format.raw/*51.20*/("""
        """),format.raw/*52.9*/("""<x-shadow-scroll vertical class="page__search-results-table-container">
            <div id="fx-entity-content" class="page__search-results-table fx-entity-info">
                <div>
                    <table id="version-search-table"
                    class="table table--search"
                    data-na-portal-table-datatable
                    data-na-portal-table-load-using-ajax
                    data-na-portal-operations-catalog-version-datatable
                    data-config-url=""""),_display_(/*60.39*/na/*60.41*/.operationscatalog.controllers.routes.VersionController.versionsDataTableSearch()),format.raw/*60.122*/(""""></table>
                </div>
            </div>
        </x-shadow-scroll>
    </div>
""")))};def /*9.2*/newButtonAttrs/*9.16*/ = {{
    val map = scala.collection.mutable.HashMap[String, String]()
    map.put("class", "btn btn-primary fx-call-to-action")
    map.put("id", "new-version-button")
    map.put("data-page-action", "create")
    map.put("data-na-portal-operations-catalog-version-create-button", null)
    map
}};def /*18.2*/iconAttrs/*18.11*/ = {{
    val map = scala.collection.mutable.HashMap[String, String]()
    map.put("class", "glyphicon glyphicon-plus")
    map
}};
Seq[Any](format.raw/*7.42*/("""

"""),format.raw/*16.2*/("""

"""),format.raw/*22.2*/("""

"""),format.raw/*28.2*/("""

"""),format.raw/*47.2*/("""

"""),format.raw/*65.2*/("""

"""),_display_(/*67.2*/mainSkel(i18n("na.portal.operationscatalog.nav.version"))/*67.59*/ {_display_(Seq[Any](format.raw/*67.61*/("""
    """),format.raw/*68.5*/("""<div class="fx-push-footer page__container fx-full-height" data-na-portal-operations-catalog-version-search-page>
        """),_display_(/*69.10*/na/*69.12*/.operationscatalog.views.html.common.lateralNav.render(OperationsCatalogConstants.VERSION_CONTEXT)),format.raw/*69.110*/("""
        """),format.raw/*70.9*/("""<x-splitter class="splitter splitter--operations-catalog-search" style="position: relative">
            <div slot="collapsed-left">
                <div class="fx-info-sidebar-collapsed left">
                    <div class="fx-sidebar-header search-sidebar__header">
                        <span class="fx-entity-header-icon">
                            <i class="fa fa-search"></i>
                        </span>
                    </div>
                </div>
            </div>
            <form slot="left" id="fx-sidebar"
            class="fx-splitter-sidebar with-buttons search-sidebar"
            data-na-portal-operations-catalog-sidebar
            data-na-portal-operations-catalog-sidebar-version
            >
            """),_display_(/*85.14*/na/*85.16*/.operationscatalog.views.html.common.leftSide.render(elements)),format.raw/*85.78*/("""
            """),format.raw/*86.13*/("""</form>
            """),_display_(/*87.14*/rightSide),format.raw/*87.23*/("""
        """),format.raw/*88.9*/("""</x-splitter>
    </div>
""")))}))
      }
    }
  }

  def render(elements:java.util.List[WebComponent]): play.twirl.api.HtmlFormat.Appendable = apply(elements)

  def f:((java.util.List[WebComponent]) => play.twirl.api.HtmlFormat.Appendable) = (elements) => apply(elements)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:43 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/target/TwirlSource/na/operationscatalog/views/roots/version.scala.html
                  HASH: a8898ac53be75adba5f28b5aef892f35351c588c
                  MATRIX: 678->1|730->47|776->87|865->170|930->229|1317->290|1436->788|1457->800|1538->804|1566->806|1630->861|1670->863|1702->869|1712->871|1877->1014|1909->1016|1934->1021|1953->1031|2034->1035|2066->1040|2412->1359|2480->1406|2525->1423|2602->1473|2670->1520|2800->1623|2833->1635|2869->1644|2911->1666|2929->1675|3012->1681|3044->1686|3153->1768|3184->1778|3220->1787|3751->2291|3762->2293|3865->2374|3979->333|4001->347|4312->647|4330->656|4489->330|4518->644|4547->785|4576->1018|4605->1663|4634->2466|4663->2469|4729->2526|4769->2528|4801->2533|4951->2656|4962->2658|5082->2756|5118->2765|5890->3510|5901->3512|5984->3574|6025->3587|6073->3608|6103->3617|6139->3626
                  LINES: 24->1|25->2|26->3|27->4|28->5|33->7|37->24|37->24|39->24|40->25|40->25|40->25|41->26|41->26|41->26|42->27|43->30|43->30|45->30|46->31|53->38|53->38|54->39|55->40|55->40|59->44|59->44|60->45|62->49|62->49|64->49|65->50|66->51|66->51|67->52|75->60|75->60|75->60|80->9|80->9|87->18|87->18|92->7|94->16|96->22|98->28|100->47|102->65|104->67|104->67|104->67|105->68|106->69|106->69|106->69|107->70|122->85|122->85|122->85|123->86|124->87|124->87|125->88
                  -- GENERATED --
              */
          