
package na.operationscatalog.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object operation extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template9[String,String,String,String,String,String,Call,java.util.List[na.catalog.basemodule.ui.WebComponent],na.catalog.basemodule.ui.WebComponent,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        headerTitleI18n: String,
        iconClass: String,
        searchContext: String,
        tableId: String,
        searchPageDirective: String,
        resultsTableDirective: String,
        dataTableConfigUrl: Call,
        elements: java.util.List[na.catalog.basemodule.ui.WebComponent],
        versionComponent: na.catalog.basemodule.ui.WebComponent
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*13.2*/import na.naportalbase.views.tags.i18n
/*14.2*/import na.operationscatalog.settings.AAAPIResources
/*15.2*/import na.operationscatalog.views.html.skeletons.mainSkel
/*16.2*/import pt.alticelabs.nossis.security.views.html.authorized

def /*18.2*/pageHeader/*18.12*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*18.16*/("""
    """),format.raw/*19.5*/("""<div class="fx-entity-header page__search-results-header">
        <div class="fx-entity-header-info">
            <span class="fx-entity-header-icon">
                <i class=""""),_display_(/*22.28*/iconClass),format.raw/*22.37*/(""""></i>
            </span>
            <div class="fx-entity-header-title">
                <h1>
                    <x-i18n key=""""),_display_(/*26.35*/headerTitleI18n),format.raw/*26.50*/(""""></x-i18n>
                </h1>
                <span class="nav-context">(<x-i18n key="na.portal.operationscatalog.nav.context"></x-i18n>)</span>
            </div>
        </div>
        <div class="fx-entity-header-info">
            """),_display_(/*32.14*/versionComponent/*32.30*/.render()),format.raw/*32.39*/("""
        """),format.raw/*33.9*/("""</div>
        <div class="fx-entity-header-actions">
            """),_display_(/*35.14*/authorized(AAAPIResources.operationsCatalog.C.toString)/*35.69*/ {_display_(Seq[Any](format.raw/*35.71*/("""
                """),format.raw/*36.17*/("""<button id="create-button" class="btn btn-primary fx-call-to-action" disabled data-na-portal-operations-catalog-operation-create-button data-page-action="create">
                    <i class="glyphicon glyphicon-plus"></i>
                    <x-i18n key="na.portal.operationscatalog.button.create"></x-i18n>
                </button>
            """)))}),format.raw/*40.14*/("""
        """),format.raw/*41.9*/("""</div>
    </div>
""")))};def /*46.2*/rightSide/*46.11*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*46.15*/("""
    """),format.raw/*47.5*/("""<div class="page__search-results" slot="right">
        """),_display_(/*48.10*/pageHeader),format.raw/*48.20*/("""
        """),format.raw/*49.9*/("""<x-shadow-scroll vertical class="page__search-results-table-container">
            <div id="fx-entity-content" class="page__search-results-table fx-entity-info">
                <div>
                    <table class="table table-striped table-hover table--search" id=""""),_display_(/*52.87*/tableId),format.raw/*52.94*/(""""
                    data-na-portal-table-datatable
                    data-na-portal-table-load-using-ajax
                    data-na-portal-operations-catalog-operation-state-dropdown
                        """),_display_(/*56.26*/resultsTableDirective),format.raw/*56.47*/("""
                    """),format.raw/*57.21*/("""data-operation-type=""""),_display_(/*57.43*/searchContext),format.raw/*57.56*/(""""
                    data-config-url=""""),_display_(/*58.39*/dataTableConfigUrl),format.raw/*58.57*/(""""></table>
                </div>
            </div>
        </x-shadow-scroll>
    </div>
""")))};
Seq[Any](format.raw/*11.2*/("""

"""),format.raw/*17.1*/("""
"""),format.raw/*43.2*/("""


"""),format.raw/*63.2*/("""

"""),_display_(/*65.2*/mainSkel(i18n(headerTitleI18n))/*65.33*/ {_display_(Seq[Any](format.raw/*65.35*/("""
    """),format.raw/*66.5*/("""<div class="fx-push-footer page__container fx-full-height" """),_display_(/*66.65*/searchPageDirective),format.raw/*66.84*/(""">
        """),_display_(/*67.10*/na/*67.12*/.operationscatalog.views.html.common.lateralNav.render(searchContext)),format.raw/*67.81*/("""
        """),format.raw/*68.9*/("""<x-splitter class="splitter splitter--operations-catalog-search">
            <div slot="collapsed-left">
                <div class="fx-info-sidebar-collapsed left">
                    <div class="fx-sidebar-header search-sidebar__header">
                        <span class="fx-entity-header-icon">
                            <i class="fa fa-search"></i>
                        </span>
                    </div>
                </div>
            </div>
            <form slot="left" id="fx-sidebar"
            class="fx-splitter-sidebar with-buttons search-sidebar"
            data-operation-type=""""),_display_(/*80.35*/searchContext),format.raw/*80.48*/(""""
            data-na-portal-operations-catalog-sidebar
            data-na-portal-operations-catalog-sidebar-operation
            >
            """),_display_(/*84.14*/na/*84.16*/.operationscatalog.views.html.common.leftSide.render(elements)),format.raw/*84.78*/("""
            """),format.raw/*85.13*/("""</form>
            <div id="fx-splitter-content" slot="right">
                """),_display_(/*87.18*/rightSide),format.raw/*87.27*/("""
            """),format.raw/*88.13*/("""</div>
        </x-splitter>
    </div>
""")))}))
      }
    }
  }

  def render(headerTitleI18n:String,iconClass:String,searchContext:String,tableId:String,searchPageDirective:String,resultsTableDirective:String,dataTableConfigUrl:Call,elements:java.util.List[na.catalog.basemodule.ui.WebComponent],versionComponent:na.catalog.basemodule.ui.WebComponent): play.twirl.api.HtmlFormat.Appendable = apply(headerTitleI18n,iconClass,searchContext,tableId,searchPageDirective,resultsTableDirective,dataTableConfigUrl,elements,versionComponent)

  def f:((String,String,String,String,String,String,Call,java.util.List[na.catalog.basemodule.ui.WebComponent],na.catalog.basemodule.ui.WebComponent) => play.twirl.api.HtmlFormat.Appendable) = (headerTitleI18n,iconClass,searchContext,tableId,searchPageDirective,resultsTableDirective,dataTableConfigUrl,elements,versionComponent) => apply(headerTitleI18n,iconClass,searchContext,tableId,searchPageDirective,resultsTableDirective,dataTableConfigUrl,elements,versionComponent)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:49:51 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/target/TwirlSource/na/operationscatalog/views/roots/operation.scala.html
                  HASH: 32f99d671a734a812490caf17a8778a67dc2137e
                  MATRIX: 1111->1|1551->370|1598->410|1658->463|1724->522|1796->583|1815->593|1896->597|1928->602|2134->781|2164->790|2322->921|2358->936|2625->1176|2650->1192|2680->1201|2716->1210|2810->1277|2874->1332|2914->1334|2959->1351|3339->1700|3375->1709|3417->1732|3435->1741|3516->1745|3548->1750|3632->1807|3663->1817|3699->1826|3997->2097|4025->2104|4266->2318|4308->2339|4357->2360|4406->2382|4440->2395|4507->2435|4546->2453|4677->367|4706->581|4734->1728|4764->2545|4793->2548|4833->2579|4873->2581|4905->2586|4992->2646|5032->2665|5070->2676|5081->2678|5171->2747|5207->2756|5843->3365|5877->3378|6051->3525|6062->3527|6145->3589|6186->3602|6294->3683|6324->3692|6365->3705
                  LINES: 28->1|41->13|42->14|43->15|44->16|46->18|46->18|48->18|49->19|52->22|52->22|56->26|56->26|62->32|62->32|62->32|63->33|65->35|65->35|65->35|66->36|70->40|71->41|73->46|73->46|75->46|76->47|77->48|77->48|78->49|81->52|81->52|85->56|85->56|86->57|86->57|86->57|87->58|87->58|93->11|95->17|96->43|99->63|101->65|101->65|101->65|102->66|102->66|102->66|103->67|103->67|103->67|104->68|116->80|116->80|120->84|120->84|120->84|121->85|123->87|123->87|124->88
                  -- GENERATED --
              */
          