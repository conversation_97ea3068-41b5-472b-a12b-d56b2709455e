
package na.operationscatalog.views.html.common

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.utils.TemplateUtils
/*2.2*/import pt.alticelabs.nossis.messages.api.Messages
/*3.2*/import pt.alticelabs.nossis.security.views.html.isAuthorized

object saveEntityButtons extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[Array[String],scala.collection.immutable.Map[String, String],scala.collection.immutable.Map[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*5.2*/(
    authorization: Array[String],
    saveAttributes: scala.collection.immutable.Map[String, String],
    cancelAttributes: scala.collection.immutable.Map[String, String]
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*11.2*/saveAdditionalClass/*11.21*/ = {{
    saveAttributes.getOrElse("class", "")
}};def /*14.2*/saveExtraAttributes/*14.21*/ = {{
    TemplateUtils.dynamicElementsAttributes(saveAttributes - "class")
}};def /*17.2*/cancelAdditionalClass/*17.23*/ = {{
    cancelAttributes.getOrElse("class", "")
}};def /*20.2*/cancelExtraAttributes/*20.23*/ = {{
    TemplateUtils.dynamicElementsAttributes(cancelAttributes - "class")
}};
Seq[Any](format.raw/*9.2*/("""

"""),format.raw/*13.2*/("""
"""),format.raw/*16.2*/("""
"""),format.raw/*19.2*/("""
"""),format.raw/*22.2*/("""

"""),_display_(/*24.2*/isAuthorized(authorization)/*24.29*/ {_display_(Seq[Any](format.raw/*24.31*/("""
    """),format.raw/*25.5*/("""<a href="javascript:void(0)" class="btn btn-primary """),_display_(/*25.58*/saveAdditionalClass),format.raw/*25.77*/("""" """),_display_(/*25.80*/saveExtraAttributes),format.raw/*25.99*/(""">
    """),_display_(/*26.6*/Messages/*26.14*/.get("na.button.label.save")),format.raw/*26.42*/("""
    """),format.raw/*27.5*/("""</a>
    <a href="javascript:void(0)" class="btn btn-default """),_display_(/*28.58*/cancelAdditionalClass),format.raw/*28.79*/("""" """),_display_(/*28.82*/cancelExtraAttributes),format.raw/*28.103*/(""">
    """),_display_(/*29.6*/Messages/*29.14*/.get("na.button.label.cancel")),format.raw/*29.44*/("""
    """),format.raw/*30.5*/("""</a>
""")))}))
      }
    }
  }

  def render(authorization:Array[String],saveAttributes:scala.collection.immutable.Map[String, String],cancelAttributes:scala.collection.immutable.Map[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(authorization,saveAttributes,cancelAttributes)

  def f:((Array[String],scala.collection.immutable.Map[String, String],scala.collection.immutable.Map[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (authorization,saveAttributes,cancelAttributes) => apply(authorization,saveAttributes,cancelAttributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:43 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/target/TwirlSource/na/operationscatalog/views/common/saveEntityButtons.scala.html
                  HASH: a698e9d67287bf2709c0d6328f7eca63d9211444
                  MATRIX: 679->1|729->45|786->96|1264->159|1517->336|1545->355|1608->406|1636->425|1727->504|1757->525|1822->578|1852->599|1960->333|1989->404|2017->502|2045->576|2073->678|2102->681|2138->708|2178->710|2210->715|2290->768|2330->787|2360->790|2400->809|2433->816|2450->824|2499->852|2531->857|2620->919|2662->940|2692->943|2735->964|2768->971|2785->979|2836->1009|2868->1014
                  LINES: 24->1|25->2|26->3|31->5|39->11|39->11|41->14|41->14|43->17|43->17|45->20|45->20|48->9|50->13|51->16|52->19|53->22|55->24|55->24|55->24|56->25|56->25|56->25|56->25|56->25|57->26|57->26|57->26|58->27|59->28|59->28|59->28|59->28|60->29|60->29|60->29|61->30
                  -- GENERATED --
              */
          