
package na.operationscatalog.views.html.cfs

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object edit extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[List[na.catalog.basemodule.ui.WebComponent],Map[String, na.catalog.basemodule.ui.WebComponent],na.operationscatalog.models.operations.Operation,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        elements: List[na.catalog.basemodule.ui.WebComponent],
        headerComponents: Map[String, na.catalog.basemodule.ui.WebComponent],
        operation: na.operationscatalog.models.operations.Operation
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*7.2*/import na.operationscatalog.settings.{AAAPIResources, OperationsCatalogConstants}
/*8.2*/import pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext.Action


Seq[Any](format.raw/*5.2*/("""

"""),format.raw/*9.1*/("""
"""),_display_(/*10.2*/na/*10.4*/.operationscatalog.views.html.operations.createOrEdit.render(
    iconClass = "fuxicons fuxicons-client-services",
    operationContext = OperationsCatalogConstants.CFS_CONTEXT,
    pageDirective = "data-na-portal-operations-catalog-cfs-edit-page",
    elements = elements,
    headerComponents = headerComponents,
    permission = AAAPIResources.operationsCatalog.U.toString,
    action = Action.EDIT,
    typeId = null,
    operation = operation
)))
      }
    }
  }

  def render(elements:List[na.catalog.basemodule.ui.WebComponent],headerComponents:Map[String, na.catalog.basemodule.ui.WebComponent],operation:na.operationscatalog.models.operations.Operation): play.twirl.api.HtmlFormat.Appendable = apply(elements,headerComponents,operation)

  def f:((List[na.catalog.basemodule.ui.WebComponent],Map[String, na.catalog.basemodule.ui.WebComponent],na.operationscatalog.models.operations.Operation) => play.twirl.api.HtmlFormat.Appendable) = (elements,headerComponents,operation) => apply(elements,headerComponents,operation)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:43 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/target/TwirlSource/na/operationscatalog/views/cfs/edit.scala.html
                  HASH: b8dbcd84474fd54c0a5893b43e5f6043dacc37db
                  MATRIX: 1109->1|1394->216|1483->299|1584->213|1612->372|1640->374|1650->376
                  LINES: 28->1|35->7|36->8|39->5|41->9|42->10|42->10
                  -- GENERATED --
              */
          