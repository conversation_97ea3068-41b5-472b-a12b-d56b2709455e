
package na.operationscatalog.views.html.cfs

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object create extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[List[na.catalog.basemodule.ui.WebComponent],Map[String, na.catalog.basemodule.ui.WebComponent],Long,na.operationscatalog.models.operations.Operation,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        elements: List[na.catalog.basemodule.ui.WebComponent],
        headerComponents: Map[String, na.catalog.basemodule.ui.WebComponent],
        typeId: Long,
        operation: na.operationscatalog.models.operations.Operation
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*8.2*/import na.operationscatalog.settings.{AAAPIResources, OperationsCatalogConstants}
/*9.2*/import pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext.Action


Seq[Any](format.raw/*6.2*/("""

"""),format.raw/*10.1*/("""
"""),_display_(/*11.2*/na/*11.4*/.operationscatalog.views.html.operations.createOrEdit.render(
    iconClass = "fuxicons fuxicons-client-services",
    operationContext = OperationsCatalogConstants.CFS_CONTEXT,
    pageDirective = "data-na-portal-operations-catalog-cfs-create-page",
    elements = elements,
    headerComponents = headerComponents,
    permission = AAAPIResources.operationsCatalog.C.toString,
    action = Action.CREATE,
    typeId = typeId,
    operation = operation
)))
      }
    }
  }

  def render(elements:List[na.catalog.basemodule.ui.WebComponent],headerComponents:Map[String, na.catalog.basemodule.ui.WebComponent],typeId:Long,operation:na.operationscatalog.models.operations.Operation): play.twirl.api.HtmlFormat.Appendable = apply(elements,headerComponents,typeId,operation)

  def f:((List[na.catalog.basemodule.ui.WebComponent],Map[String, na.catalog.basemodule.ui.WebComponent],Long,na.operationscatalog.models.operations.Operation) => play.twirl.api.HtmlFormat.Appendable) = (elements,headerComponents,typeId,operation) => apply(elements,headerComponents,typeId,operation)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:43 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/target/TwirlSource/na/operationscatalog/views/cfs/create.scala.html
                  HASH: 2909fc677af49271f847a782fe76277d9583c298
                  MATRIX: 1116->1|1423->238|1512->321|1613->235|1642->394|1670->396|1680->398
                  LINES: 28->1|36->8|37->9|40->6|42->10|43->11|43->11
                  -- GENERATED --
              */
          