package na.operationscatalog.settings;

import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;
import na.catalog.basemodule.models.module.CatalogModule;
import na.naportalbase.services.i18n.Lang;
import na.naportalbase.services.ws.RequestBuilderFactory;
import na.operationscatalog.services.EntitiesCatalogHttpClientImpl;
import na.operationscatalog.services.OperationsCatalogConfig;
import play.api.inject.ApplicationLifecycle;
import play.libs.ws.WSClient;
import pt.ptinovacao.asf.componentsmanager.ComponentsRegister;
import pt.ptinovacao.asf.configuration.ConfigPropertiesRegister;
import pt.ptinovacao.na.commons.cluster.service.locator.ServiceLocator;
import pt.ptinovacao.na.entities.cache.VersionCache;
import pt.ptinovacao.na.entities.cache.VersionCacheImpl;
import pt.ptinovacao.na.entities.cache.connector.HttpClient;
import pt.ptinovacao.na.entities.cache.connector.HttpConnector;
import pt.ptinovacao.na.entities.cache.connector.HttpConnectorImpl;

import javax.inject.Inject;
import javax.inject.Provider;
import javax.inject.Singleton;
import java.util.concurrent.CompletableFuture;

@Singleton
public final class OperationsCatalogModule extends CatalogModule {

    private static final play.Logger.ALogger LOGGER = play.Logger.of(OperationsCatalogConstants.LOGGER_NAME);

    @Inject
    private OperationsCatalogModule(final ApplicationLifecycle lifecycle,
                                    final RequestBuilderFactory requestBuilderFactory,
                                    final Lang lang,
                                    final OperationsCatalogConfig operationsCatalogConfig,
                                    final Provider<na.naportalbase.services.ServicesHelper> servicesHelperProvider,
                                    final WSClient wsClient) {

        // ---------------
        // -- ON START ---
        // ---------------

        LOGGER.info("STARTING OPERATIONS-CATALOG MODULE");

        final Config moduleConfigs = ConfigFactory.load();


        ConfigPropertiesRegister.put("entities.cache.eviction.time", moduleConfigs.getLong("na.operations.catalog.entities.eviction.time"));

        // Register Service Location Components and Cache Components

        ComponentsRegister.put(VersionCache.COMPONENT_NAME, new VersionCacheImpl());
        ComponentsRegister.put(HttpConnector.COMPONENT_NAME, new HttpConnectorImpl());
        ComponentsRegister.put(HttpClient.COMPONENT_NAME, new EntitiesCatalogHttpClientImpl(requestBuilderFactory, servicesHelperProvider));

        final String entitiesClusterConfigFile = moduleConfigs.hasPath("na.operations.catalog.entities.service.locator.configfile") ?
                moduleConfigs.getString("na.operations.catalog.entities.service.locator.configfile") :
                "/opt/alticelabs/na-portal/conf/cluster-entities-catalog.yml";
        final String entitiesClusterName = moduleConfigs.hasPath("na.operations.catalog.entities.service.locator.cluster-name") ?
                moduleConfigs.getString("na.operations.catalog.entities.service.locator.cluster-name") :
                "nossis-tsc-rest-backend-service@nossis-tsc-clusters";
        final String entitiesClusterServiceName = moduleConfigs.hasPath("na.operations.catalog.entities.service.locator.cluster-service") ?
                moduleConfigs.getString("na.operations.catalog.entities.service.locator.cluster-service") :
                "nossis-tsc-rest";
        ComponentsRegister.put(ServiceLocator.COMPONENT_NAME, new ServiceLocator(entitiesClusterConfigFile, entitiesClusterName, entitiesClusterServiceName));

        final String ocClusterConfigFile = moduleConfigs.hasPath("na.operations.catalog.service.locator.configfile") ?
                moduleConfigs.getString("na.operations.catalog.service.locator.configfile") :
                "/opt/alticelabs/na-portal/conf/cluster-operations-catalog.yml";
        final String ocClusterName = moduleConfigs.hasPath("na.operations.catalog.service.locator.cluster-name") ?
                moduleConfigs.getString("na.operations.catalog.service.locator.cluster-name") :
                "na-operations-catalog-rest-backend-service@na-operations-catalog-clusters";
        final String ocClusterServiceName = moduleConfigs.hasPath( "na.operations.catalog.service.locator.cluster-service") ?
                moduleConfigs.getString( "na.operations.catalog.service.locator.cluster-service") :
                "na-operations-catalog-rest";
        ComponentsRegister.put(OperationsCatalogConstants.OPERATIONS_CATALOG_SERVICE_LOCATOR_COMPONENT, new ServiceLocator(ocClusterConfigFile, ocClusterName, ocClusterServiceName));

        ComponentsRegister.initComponents(
                OperationsCatalogConstants.OPERATIONS_CATALOG_SERVICE_LOCATOR_COMPONENT,
                ServiceLocator.COMPONENT_NAME,
                VersionCache.COMPONENT_NAME,
                HttpConnector.COMPONENT_NAME,
                HttpClient.COMPONENT_NAME
        );

        // create catalog and api service instance
        configureCatalog(moduleConfigs, operationsCatalogConfig, lang, wsClient);

        // ---------------
        // --- ON STOP ---
        // ---------------

        lifecycle.addStopHook(() -> {
            LOGGER.info("OPERATIONS-CATALOG MODULE STOPPED");
            return CompletableFuture.completedFuture(null);
        });

        LOGGER.info("OPERATIONS-CATALOG MODULE STARTED");

    }
}