package na.operationscatalog.settings;


import pt.ptinovacao.na.commons.cluster.service.locator.ServiceLocator;

import javax.inject.Singleton;

@Singleton
public final class OperationsCatalogConstants {

    private OperationsCatalogConstants() {
    }

    public static final String OPERATIONS_CATALOG_SERVICE_LOCATOR_COMPONENT = "OC_" + ServiceLocator.COMPONENT_NAME;

    // settings
    public static final String LOGGER_NAME = "operations-catalog";
    public static final String CONF_FILE_PATH = "conf/operations-catalog-app.conf";
    public static final String WEB_COMPONENTS_CONFIG_PREFIX = "na.operationscatalog.webcomponent";

    // operations types
    public static final int CFS_TYPE = 10000;
    public static final int RFS_TYPE = 10010;
    public static final int RESOURCE_TYPE = 10020;

    // resource properties
    public static final String RESOURCE_TAG = "TAG";
    public static final String RESOURCE_VERSION = "VERSION";
    public static final String RESOURCE_OPERATION = "OPERATION";
    public static final String RESOURCE_ATTRIBUTE = "ATTRIBUTE";
    public static final String RESOURCE_RESPONSE_CODE = "RESPONSE_CODE";

    // resource attribute properties
    public static final String RESOURCE_ATTRIBUTE_TAGS = "tags";
    public static final String RESOURCE_ATTRIBUTE_VERSION = "version";
    public static final String RESOURCE_ATTRIBUTE_CONSTRAINTS = "operationAttributes/constraints/valueList";
    public static final String RESOURCE_ATTRIBUTE_ENTITY_CONSTRAINTS = "operationAttributes/entityConstraints/valueList";

    // contexts
    public static final String ENTITY_VERSION = "entityVersion";
    public static final String ENTITY_CFS = "entityCfs";
    public static final String ENTITY_RFS = "entityRfs";
    public static final String ENTITY_RESOURCE = "entityResource";

    public static final String ENTITY_LATERAL_CFS = "entityLateralCfs";
    public static final String ENTITY_LATERAL_RFS = "entityLateralRfs";
    public static final String ENTITY_LATERAL_RESOURCE = "entityLateralResource";

    public static final String VERSION_CONTEXT = "version";
    public static final String CFS_CONTEXT = "cfs";
    public static final String RFS_CONTEXT = "rfs";
    public static final String RESOURCE_CONTEXT = "resource";
    public static final String RESPONSE_CODE_CONTEXT = "responseCode";

    public static final String VERSION_NAME = "name";
    public static final String VERSION_DESCRIPTION = "description";
    public static final String VERSION_STATE = "state";
    public static final String VERSION_CAT_ENTITY_VERSION = "catEntityVersion";

    public static final String SERVICE_UI_CONTEXT = "uiContext";
    public static final String SERVICE_UI_ACTION = "uiAction";

    // attributes
    public static final String ENTITY_VERSION_NAME = "name";
    public static final String ID_ATTRIBUTE_NAME = "id";
    public static final String NAME_ATTRIBUTE_NAME = "name";
    public static final String VALID_ATTRIBUTE_NAME = "valid";
    public static final String VERSION_ATTRIBUTE_NAME = "version";
    public static final String TAGS_ATTRIBUTE_NAME = "tags";
    public static final String STATE_ATTRIBUTE_NAME = "state";
    public static final String OPERATION_TYPE_ATTRIBUTE_NAME = "type";
    public static final String OPERATION_ENTITY_NAME = "entityName";
}