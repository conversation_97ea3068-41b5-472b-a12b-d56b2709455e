package na.operationscatalog.models.operations;

import com.fasterxml.jackson.annotation.*;
import na.catalog.basemodule.services.CatEntityAPIServices;

import java.util.HashMap;
import java.util.Map;
import java.util.Stack;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Entity {

    private String id;
    private Long idCatEntity;
    private String catEntityName;
    private String name;
    private String description;
    private Boolean clonePermission;
    private Boolean deletePermission;
    private Boolean editPermission;

    private static Stack<CatEntityAPIServices> stack = new Stack<>();

    public static <T> T withCatEntityAPIService(CatEntityAPIServices service, Supplier<T> runnable) {
        stack.push(service);
        final T result = runnable.get();
        stack.pop();
        return result;
    }

    protected Entity(String name) {
        this.setCatEntityName(name);
        if (!stack.empty()) {
            this.setIdCatEntity(stack.peek().getCatEntityByName(name).getId());
        }
    }

    private final Map<String, Object> dynamicAttributes = new HashMap<>();

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public long getIdCatEntity() {
        return idCatEntity;
    }

    public void setIdCatEntity(long idCatEntity) {
        this.idCatEntity = idCatEntity;
    }

    public String getCatEntityName() {
        return catEntityName;
    }

    public void setCatEntityName(String catEntityName) {
        this.catEntityName = catEntityName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("hasClonePermission")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public Boolean hasClonePermission() {
        return clonePermission;
    }

    @JsonIgnore
    public void setClonePermission(boolean clonePermission) {
        this.clonePermission = clonePermission;
    }

    @JsonProperty("hasDeletePermission")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public Boolean hasDeletePermission() {
        return deletePermission;
    }

    @JsonIgnore
    public void setDeletePermission(boolean deletePermission) {
        this.deletePermission = deletePermission;
    }

    @JsonProperty("hasEditPermission")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public Boolean hasEditPermission() {
        return editPermission;
    }

    @JsonIgnore
    public void setEditPermission(boolean editPermission) {
        this.editPermission = editPermission;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @JsonAnyGetter
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public Map<String, Object> getDynamicAttributes() {
        // Filter out "null" values
        return dynamicAttributes.entrySet().stream()
                .filter(entry -> entry.getValue() != null)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    @JsonAnySetter
    public void setDynamicAttributes(String name, Object value) {
        // Filter out "@___"
        if (name.startsWith("@")) {
            return;
        }
        this.dynamicAttributes.put(name, value);
    }
}