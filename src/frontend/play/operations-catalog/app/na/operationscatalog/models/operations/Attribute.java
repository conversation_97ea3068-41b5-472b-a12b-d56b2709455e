package na.operationscatalog.models.operations;

import na.catalog.basemodule.models.EntityType;

import java.util.List;

public final class Attribute extends Entity {

    private List<Constraint> constraints;
    private List<Constraint> entityConstraints;

    public Attribute() {
        super(EntityType.ATTRIBUTE);
    }

    public List<Constraint> getConstraints() {
        return constraints;
    }

    public void setConstraints(List<Constraint> constraints) {
        this.constraints = constraints;
    }

    public List<Constraint> getEntityConstraints() {
        return entityConstraints;
    }

    public void setEntityConstraints(List<Constraint> entityConstraints) {
        this.entityConstraints = entityConstraints;
    }
}