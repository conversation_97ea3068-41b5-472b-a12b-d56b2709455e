package na.operationscatalog.models.responses;

import com.fasterxml.jackson.annotation.JsonProperty;
import na.operationscatalog.models.operations.Entity;

import java.util.List;

/**
 * Created by ruig on 29-09-2017.
 */
public abstract class EntityResponse<T extends Entity> {
    List<T> entries;
    private int count;

    public abstract void setEntries(List<T> entries);

    @JsonProperty("entries")
    public List<T> getEntries() {
        return entries;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }
}
