package na.operationscatalog.models.operations;

import na.catalog.basemodule.models.EntityType;

public final class ResponseCode extends TaggedEntityWithState {

    private String type;
    private String code;

    public ResponseCode() {
        super(EntityType.RESPONSE_CODE);
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}