package na.operationscatalog.models.responses;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import na.naportalbase.exceptions.ExceptionCodes;
import na.naportalbase.models.services.GenericResponse;
import na.naportalbase.services.DefaultErrorHandler;
import na.naportalbase.services.ResponseMapperIf;
import na.naportalbase.services.ServiceInvokeLogger;
import na.naportalbase.settings.ExpectedHttpResponseCodes;
import play.libs.ws.WSResponse;
import pt.ptinovacao.nossis.exceptions.BusinessException;
import pt.ptinovacao.nossis.exceptions.FaultException;

import java.util.concurrent.CompletionStage;

public class BooleanResponseMapper implements ResponseMapperIf {

    public BooleanResponseMapper() {
    }

    @Override
    public <T> CompletionStage<T> mapResponse(final Class<T> generic,
                                              final String requestType,
                                              final CompletionStage<WSResponse> mResponse,
                                              final String logId) {
        return mResponse.handle((response, throwable) -> {

            if (throwable != null && throwable.getCause() instanceof java.net.ConnectException) {
                throw new FaultException(ExceptionCodes.REST_API_UNAVAILABLE_EXCEPTION, throwable.getCause());
            }

            final GenericResponse<WSResponse> genericResponse = new GenericResponse<>(response);

            ServiceInvokeLogger.logServiceResponse(logId, response.getHeaders(), response.getStatus(), response.getBody(), requestType);

            if (ExpectedHttpResponseCodes.isExpectedSuccessCode(requestType, genericResponse.getStatus())) {
                return instantiateEmptyObject(generic);
            }
            new DefaultErrorHandler().handleBadResponse(requestType, genericResponse);
            return null;
        });
    }

    @SuppressWarnings("unchecked")
    private <T> T instantiateEmptyObject(final Class<T> generic) {
        try {
            if (generic == Boolean.class) {
                return (T) Boolean.TRUE;
            }
            if (generic == JsonNode.class) {
                return (T) new ObjectMapper().createObjectNode();
            }
            return generic.newInstance();
        } catch (Exception e) {
            throw new BusinessException(ExceptionCodes.UNKNOWN_EXCEPTION, new Throwable("Error while instantiating response object from rest service - " + e.getMessage()));
        }
    }
}