package na.operationscatalog.models.operations;

import com.fasterxml.jackson.annotation.JsonInclude;
import na.catalog.basemodule.models.EntityType;

import java.util.ArrayList;
import java.util.List;

public final class Operation extends TaggedEntityWithState {

    private Version version;
    private String entityName;
    private String type;
    private String workflow;
    private Boolean valid;
    private List<Attribute> operationAttributes = new ArrayList<>();

    public Operation() {
        super(EntityType.OPERATION);
    }

    public Version getVersion() {
        return version;
    }

    public void setVersion(Version version) {
        this.version = version;
    }

    public String getEntityName() {
        return entityName;
    }

    public void setEntityName(String entityName) {
        this.entityName = entityName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getWorkflow() {
        return workflow;
    }

    public void setWorkflow(String workflow) {
        this.workflow = workflow;
    }

    public Boolean isValid() {
        return valid;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public void setValid(Boolean valid) {
        this.valid = valid;
    }

    public List<Attribute> getOperationAttributes() {
        return operationAttributes;
    }

    public void setOperationAttributes(List<Attribute> operationAttributes) {
        if (operationAttributes != null) {
            this.operationAttributes = operationAttributes;
        }
    }
}