package na.operationscatalog.models.operations;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import na.catalog.basemodule.models.EntityType;
import na.operationscatalog.models.statemachine.State;

import java.util.List;

public final class OperationDTSearch extends TaggedEntityWithState {

    private Version version;
    private String entityName;
    private Boolean valid;
    private Boolean transitionStatePermission;
    private Boolean canEditOperation;
    private Boolean canCloneOperation;
    private Boolean canDeleteOperation;
    private Boolean canTransitionState;
    private List<State> nextStates;

    public OperationDTSearch() {
        super(EntityType.OPERATION);
    }

    public Version getVersion() {
        return version;
    }

    public void setVersion(Version version) {
        this.version = version;
    }

    public String getEntityName() {
        return entityName;
    }

    public void setEntityName(String entityName) {
        this.entityName = entityName;
    }

    @JsonProperty("hasTransitionStatePermission")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public Boolean hasTransitionStatePermission() {
        return transitionStatePermission;
    }

    @JsonIgnore
    public void setTransitionStatePermission(Boolean transitionStatePermission) {
        this.transitionStatePermission = transitionStatePermission;
    }

    @JsonProperty("canEdit")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public Boolean canEdit() {
        return canEditOperation;
    }

    @JsonIgnore
    public void setEditPossibility(boolean editPossibility) {
        this.canEditOperation = editPossibility;
    }

    @JsonProperty("canClone")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public Boolean canClone() {
        return canCloneOperation;
    }

    @JsonIgnore
    public void setClonePossibility(boolean clonePossibility) {
        this.canCloneOperation = clonePossibility;
    }

    @JsonProperty("canDelete")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public Boolean canDelete() {
        return canDeleteOperation;
    }

    @JsonIgnore
    public void setDeletePossibility(boolean deletePossibility) {
        this.canDeleteOperation = deletePossibility;
    }

    @JsonProperty("canTransitionState")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public Boolean canTransitionState() {
        return canTransitionState;
    }

    @JsonIgnore
    public void setTransitionStatePossibility(Boolean transitionStatePossibility) {
        this.canTransitionState = transitionStatePossibility;
    }

    @JsonProperty("nextStates")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public List<State> getNextStates() {
        return nextStates;
    }

    @JsonIgnore
    public void setNextStates(List<State> nextStates) {
        this.nextStates = nextStates;
    }

    public Boolean isValid() {
        return valid;
    }

    public void setValid(Boolean valid) {
        this.valid = valid;
    }
}