package na.operationscatalog.models;

import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>, 19-02-2018.
 */
public class CatEntitiesResponse {
    List<ExtendedCatEntityImpl> entities;
    Long count;

    public CatEntitiesResponse() {
    }

    public CatEntitiesResponse(List<ExtendedCatEntityImpl> entities, Long count) {
        this.entities = entities;
        this.count = count;
    }

    public List<ExtendedCatEntityImpl> getEntities() {
        return entities;
    }

    public void setEntities(List<ExtendedCatEntityImpl> entities) {
        this.entities = entities;
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }
}
