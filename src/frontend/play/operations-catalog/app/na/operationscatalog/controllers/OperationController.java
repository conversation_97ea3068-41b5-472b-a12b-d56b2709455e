package na.operationscatalog.controllers;

import com.fasterxml.jackson.databind.JsonNode;
import na.catalog.basemodule.services.CatEntityAPIServices;
import na.naportalbase.settings.AAAPIAccess;
import na.naportalbase.settings.BaseConstants;
import na.operationscatalog.models.operations.Entity;
import na.operationscatalog.models.operations.Operation;
import na.operationscatalog.models.tables.config.*;
import na.operationscatalog.services.OperationServices;
import na.operationscatalog.services.entities.EntitiesRelations;
import na.operationscatalog.settings.AAAPIResources;
import na.operationscatalog.settings.OperationsCatalogConstants;
import na.operationscatalog.settings.OperationsCatalogModule;
import na.catalog.basemodule.ui.Action;
import play.libs.Json;
import play.libs.concurrent.HttpExecutionContext;
import play.mvc.Controller;
import play.mvc.Result;
import play.mvc.Results;
import pt.alticelabs.nossis.messages.api.Messages;
import pt.alticelabs.nossis.security.actions.Authenticated;
import pt.alticelabs.nossis.security.actions.Authorized;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

@Singleton
public final class OperationController extends Controller {

    private final CatEntityAPIServices catEntityAPIServices;
    private final HttpExecutionContext ec;
    private final OperationServices operationServices;
    private final play.Logger.ALogger LOGGER;

    @Inject
    private OperationController(HttpExecutionContext ec,
                                OperationsCatalogModule operationsCatalogModule,
                                OperationServices operationServices) {
        this.catEntityAPIServices = operationsCatalogModule.getCatEntityApiServices();
        this.ec = ec;
        this.operationServices = operationServices;
        this.LOGGER = play.Logger.of(OperationsCatalogConstants.LOGGER_NAME);
    }

    @Authenticated
    @Authorized({AAAPIResources.OPERATIONS_CATALOG + AAAPIAccess.R})
    public CompletionStage<Result> get(Long operationId) {
        return operationServices.getOperationById(operationId).thenApply(Json::toJson).thenApply(Results::ok);
    }

    @Authenticated
    @Authorized({AAAPIResources.OPERATIONS_CATALOG + AAAPIAccess.C})
    public CompletionStage<Result> create() {
        LOGGER.info("Creating operation ...");
        response().setHeader(CACHE_CONTROL, BaseConstants.HEADER_NO_CACHE_NO_STORE_NO_TRANSFORM);
        final JsonNode operationData = request().body().asJson();
        return Entity.withCatEntityAPIService(catEntityAPIServices, () -> {
            final Operation operation = Json.fromJson(operationData, Operation.class);
            return operationServices.create(Json.toJson(operation)).thenApply(Results::ok);
        });
    }

    @Authenticated
    @Authorized({AAAPIResources.OPERATIONS_CATALOG + AAAPIAccess.U})
    public CompletionStage<Result> update(Long id) {
        LOGGER.info("Updating operation id=" + id + " ...");
        response().setHeader(CACHE_CONTROL, BaseConstants.HEADER_NO_CACHE_NO_STORE_NO_TRANSFORM);
        final JsonNode operationData = request().body().asJson();
        return Entity.withCatEntityAPIService(catEntityAPIServices, () -> {
            final Operation operation = Json.fromJson(operationData, Operation.class);
            return operationServices.update(id, Json.toJson(operation)).thenApply(Results::ok);
        });
    }

    @Authenticated
    @Authorized({AAAPIResources.OPERATIONS_CATALOG + AAAPIAccess.D})
    public CompletionStage<Result> delete(Long id) {
        LOGGER.info("Deleting operation id=" + id + " ...");
        response().setHeader(CACHE_CONTROL, BaseConstants.HEADER_NO_CACHE_NO_STORE_NO_TRANSFORM);
        return operationServices.delete(id).thenApplyAsync(deleted -> (deleted) ? ok() : noContent(), ec.current());
    }

    @Authenticated
    @Authorized(AAAPIResources.OPERATIONS_CATALOG + AAAPIAccess.C)
    public CompletionStage<Result> cloneOperation(Long id) {
        LOGGER.info("Cloning operation id=" + id + " ...");
        response().setHeader(CACHE_CONTROL, BaseConstants.HEADER_NO_CACHE_NO_STORE_NO_TRANSFORM);
        return operationServices.clone(id).thenApply(Results::created);
    }

    @Authenticated
    @Authorized({AAAPIResources.OPERATIONS_CATALOG + AAAPIAccess.R})
    public Result dataTableViewInputAttributes() {
        return ok(Json.toJson(operationServices.dataTableInputAttributes()));
    }

    /**
     * Get template to render input attribute's modal
     *
     * @return template
     */
    @Authenticated
    @Authorized({AAAPIResources.OPERATIONS_CATALOG + AAAPIAccess.R})
    public Result inputAttributeModal(String action) {
        return ok(na.operationscatalog.views.html.operations.inputAttributeModal.render(Action.valueOf(action.toUpperCase())));
    }

    @Authenticated
    @Authorized({AAAPIResources.OPERATIONS_CATALOG + AAAPIAccess.R})
    public Result dataTableEditableInputAttributes() {
        return ok(Json.toJson(operationServices.dataTableEditableInputAttributes()));
    }

    @Authenticated
    @Authorized({AAAPIResources.OPERATIONS_CATALOG + AAAPIAccess.R})
    public Result dataTableViewExecutionAttributes() {
        TableConfig config = new TableConfigBuilder()
                .pagination(Pagination.DISABLED)
                // name
                .addColumn(new Column()
                        .name("attribute")
                        .title(Messages.get("na.portal.operationscatalog.datatables.column.label.attribute"))
                        .dataProperty("attribute")
                        .sortable(false))
                // mandatory
                .addColumn(new Column()
                        .name("enrichmentRule")
                        .title(Messages.get("na.portal.operationscatalog.datatables.column.label.enrichmentRule"))
                        .dataProperty("entityName")
                        .sortable(false))
                // validationRule
                .addColumn(new Column()
                        .name("validationRule")
                        .title(Messages.get("na.portal.operationscatalog.datatables.column.label.validationRule"))
                        .dataProperty("validationRule")
                        .sortable(false))
                .build();
        return ok(Json.toJson(TableConfigResponse.from(config)));
    }

    @Authenticated
    @Authorized({AAAPIResources.OPERATIONS_CATALOG + AAAPIAccess.R})
    public Result dataTableViewOutputAttributes() {
        TableConfig config = new TableConfigBuilder()
                .pagination(Pagination.DISABLED)
                // attribute / entity
                .addColumn(new Column()
                        .name("attributes")
                        .title(Messages.get("na.portal.operationscatalog.datatables.column.label.attributes_entities"))
                        .dataProperty("attribute")
                        .sortable(true)
                        .initialSortDirection(SortDirection.ASCENDANT)
                        .customRenderCallName("na.datatableTemplates.operationscatalog.operationOutputAttributes"))
                .build();
        return ok(Json.toJson(TableConfigResponse.from(config)));
    }

    @Authenticated
    @Authorized({AAAPIResources.OPERATIONS_CATALOG + AAAPIAccess.R})
    public Result dataTableCreateOrEditOutputAttributes() {
        TableConfig config = new TableConfigBuilder()
                .pagination(Pagination.DISABLED)
                // attribute / entity
                .addColumn(new Column()
                        .name("attributes")
                        .title(Messages.get("na.portal.operationscatalog.datatables.column.label.attributes_entities"))
                        .sortable(true)
                        .initialSortDirection(SortDirection.ASCENDANT)
                        .customRenderCallName("na.datatableTemplates.operationscatalog.operationOutputAttributes"))
                // actions
                .addColumn(new Column()
                        .name("actions")
                        .title(Messages.get("na.portal.operationscatalog.datatables.column.label.actions"))
                        .sortable(false)
                        .customRenderCallName("na.datatableTemplates.operationscatalog.operationOutputAttributes.actions")
                        .columnWidth(new FixedColumnWidth("80px")))
                .build();
        return ok(Json.toJson(TableConfigResponse.from(config)));
    }

    @Authenticated
    @Authorized({AAAPIResources.OPERATIONS_CATALOG + AAAPIAccess.R})
    public CompletionStage<Result> getEntityRelationTree(String operationType, String versionName, String operationName) {
        return CompletableFuture.supplyAsync(() -> {
            switch (Integer.parseInt(operationType)) {
                case OperationsCatalogConstants.CFS_TYPE:
                    return ok(Json.toJson(EntitiesRelations.getEntitiesRelationTreeOfCfs(operationName, versionName)));
                case OperationsCatalogConstants.RFS_TYPE:
                    return ok(Json.toJson(EntitiesRelations.getEntitiesRelationTreeOfRfs(operationName, versionName)));
                case OperationsCatalogConstants.RESOURCE_TYPE:
                    return ok(Json.toJson(EntitiesRelations.getEntitiesRelationTreeOfResource(operationName, versionName)));
                default:
                    return notFound();
            }
        });
    }

    @Authenticated
    @Authorized({AAAPIResources.OPERATIONS_CATALOG + AAAPIAccess.X})
    public CompletionStage<Result> transitionState(Long operationId, Long stateId) {
        LOGGER.info("transitioning operation id=" + operationId + " state to stateId=" + stateId + " ...");
        response().setHeader(CACHE_CONTROL, BaseConstants.HEADER_NO_CACHE_NO_STORE_NO_TRANSFORM);
        return operationServices.transitionState(operationId, stateId).thenApply(Results::ok);
    }
}