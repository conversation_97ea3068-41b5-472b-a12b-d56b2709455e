package na.operationscatalog.services;

import com.fasterxml.jackson.databind.JsonNode;
import na.naportalbase.services.ServicesHelper;
import na.naportalbase.services.ws.RequestBuilderFactory;
import pt.ptinovacao.asf.componentsmanager.ComponentInit;
import pt.ptinovacao.na.entities.cache.connector.HttpClient;

import javax.inject.Inject;
import javax.inject.Provider;
import java.util.Optional;

public class EntitiesCatalogHttpClientImpl implements HttpClient, ComponentInit {

    private final RequestBuilderFactory requestBuilderFactory;
    private final Provider<ServicesHelper> servicesHelperProvider;

    @Inject
    public EntitiesCatalogHttpClientImpl(RequestBuilderFactory requestBuilderFactory,
                                         Provider<ServicesHelper> servicesHelperProvider) {
        this.requestBuilderFactory = requestBuilderFactory;
        this.servicesHelperProvider = servicesHelperProvider;
    }

    @Override
    public void init() {}

    @Override
    public Optional<JsonNode> get(String requestUrl) {
        try {
            return servicesHelperProvider.get()
                    .sendGet(requestBuilderFactory.create()
                            .setUrl(requestUrl)
                            .build()
                    )
                    .handle((jsonNode, throwable) -> Optional.ofNullable(jsonNode))
                    .toCompletableFuture()
                    .get();
        } catch (Exception e) {
            return Optional.empty();
        }
    }
}