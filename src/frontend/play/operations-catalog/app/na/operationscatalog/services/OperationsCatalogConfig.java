package na.operationscatalog.services;

import na.operationscatalog.settings.OperationsCatalogConstants;
import na.operationscatalog.settings.OperationsCatalogServiceSettings;
import na.catalog.basemodule.services.catalog.client.api.CatalogImplConfig;
import pt.ptinovacao.asf.componentsmanager.Components;
import pt.ptinovacao.asf.serviceregistry.api.ServiceInstance;
import pt.ptinovacao.asf.serviceregistry.api.ServiceInstanceGetter;
import pt.ptinovacao.na.commons.cluster.service.locator.ServiceLocator;

import javax.inject.Inject;

public final class OperationsCatalogConfig implements CatalogImplConfig {

    private final OperationsCatalogServiceSettings ocServiceSettings;

    @Inject
    private OperationsCatalogConfig(OperationsCatalogServiceSettings ocServiceSettings) {
        this.ocServiceSettings = ocServiceSettings;
    }

    @Override
    public String getURLServiceCatalog(Long id, Long version) {

        final ServiceLocator serviceLocator = Components.getMandatory(OperationsCatalogConstants.OPERATIONS_CATALOG_SERVICE_LOCATOR_COMPONENT, ServiceLocator.class);
        final ServiceInstanceGetter serviceInstanceGetter = serviceLocator.getServiceInstanceGetter();
        final ServiceInstance serviceInstance = serviceInstanceGetter.get();
        final StringBuilder url = new StringBuilder()
                .append("http://")
                .append(serviceInstance.ip())
                .append(":")
                .append(serviceInstance.port())
                .append(ocServiceSettings.getServicesSuffix())
                .append("/catalog/entities-full/");
        if (id != null) {
            url.append(id).append("/");
        }
        if (version != null) {
            url.append(version);
        }
        return url.toString();
    }
}