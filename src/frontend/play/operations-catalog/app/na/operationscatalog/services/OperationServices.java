package na.operationscatalog.services;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import na.catalog.basemodule.models.EntityType;
import na.catalog.basemodule.services.CatEntityAPIServices;
import na.catalog.basemodule.ui.Action;
import na.catalog.basemodule.ui.I18n;
import na.catalog.basemodule.ui.IconLabel;
import na.catalog.basemodule.ui.InputText;
import na.catalog.basemodule.ui.SelectBox;
import na.catalog.basemodule.ui.WebComponent;
import na.naportalbase.models.selectbox.SelectOption;
import na.naportalbase.models.selectbox.SelectOptionList;
import na.naportalbase.models.tables.query.TableQuery;
import na.naportalbase.services.ws.Request;
import na.naportalbase.services.ws.RequestBuilderFactory;
import na.naportalbase.utils.DatatablesUtils;
import na.naportalbase.utils.NossisInvODataQueryParametersBuilder;
import na.naportalbase.utils.SecurityUtils;
import na.operationscatalog.models.operations.Entity;
import na.operationscatalog.models.operations.Operation;
import na.operationscatalog.models.operations.OperationDTSearch;
import na.operationscatalog.models.operations.Version;
import na.operationscatalog.models.responses.BooleanResponseMapperWrapper;
import na.operationscatalog.models.responses.OperationResponse;
import na.operationscatalog.models.statemachine.State;
import na.operationscatalog.models.tables.config.Column;
import na.operationscatalog.models.tables.config.FixedColumnWidth;
import na.operationscatalog.models.tables.config.MinimalColumnWidth;
import na.operationscatalog.models.tables.config.Pagination;
import na.operationscatalog.models.tables.config.SortDirection;
import na.operationscatalog.models.tables.config.TableConfigBuilder;
import na.operationscatalog.models.tables.config.TableConfigResponse;
import na.operationscatalog.services.entities.EntitiesCatalogServices;
import na.operationscatalog.settings.AAAPIResources;
import na.operationscatalog.settings.OperationsCatalogConstants;
import na.operationscatalog.settings.OperationsCatalogModule;
import na.operationscatalog.settings.OperationsCatalogServiceSettings;
import na.operationscatalog.utils.SearchPayLoadUtils;
import play.api.mvc.Call;
import play.libs.Json;
import play.mvc.Http;
import play.mvc.Result;
import pt.alticelabs.nossis.messages.api.Messages;
import pt.ptinovacao.netwin.kernel.catalog.client.model.CatAttributeType;
import pt.ptinovacao.netwin.kernel.catalog.client.model.CatEntity;
import pt.ptinovacao.netwin.kernel.catalog.client.model.CatEnumeratedAttributeValue;
import pt.ptinovacao.netwin.kernel.catalog.client.model.CatInsAttribute;
import pt.ptinovacao.netwin.kernel.catalog.client.model.CatState;
import pt.ptinovacao.netwin.kernel.catalog.client.model.CatStateMachine;
import pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext;

import javax.inject.Inject;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

import static play.mvc.Results.ok;

public final class OperationServices {

    private static final play.Logger.ALogger LOGGER = play.Logger.of(OperationsCatalogConstants.LOGGER_NAME);

    private final BooleanResponseMapperWrapper booleanResponseMapperWrapper;
    private final CatEntityAPIServices catEntityAPIServices;
    private final DatatablesUtils datatablesUtils;
    private final EntitiesCatalogServices entitiesService;
    private final OperationPermissionService operationPermissionService;
    private final OperationsCatalogServiceHelper ocServiceHelper;
    private final OperationsCatalogServiceSettings ocServiceSettings;
    private final SearchPayLoadUtils searchPayLoadUtils;
    private final UIComponentsService uiComponentsService;
    private final RequestBuilderFactory requestBuilderFactory;

    @Inject
    private OperationServices(RequestBuilderFactory requestBuilderFactory,
                              BooleanResponseMapperWrapper booleanResponseMapperWrapper,
                              DatatablesUtils datatablesUtils,
                              EntitiesCatalogServices entitiesService,
                              OperationPermissionService operationPermissionService,
                              OperationsCatalogModule operationsCatalogModule,
                              OperationsCatalogServiceHelper ocServiceHelper,
                              OperationsCatalogServiceSettings ocServiceSettings,
                              SearchPayLoadUtils searchPayLoadUtils,
                              UIComponentsService uiComponentsService) {
        this.requestBuilderFactory = requestBuilderFactory;
        this.booleanResponseMapperWrapper = booleanResponseMapperWrapper;
        this.catEntityAPIServices = operationsCatalogModule.getCatEntityApiServices();
        this.datatablesUtils = datatablesUtils;
        this.entitiesService = entitiesService;
        this.operationPermissionService = operationPermissionService;
        this.ocServiceHelper = ocServiceHelper;
        this.ocServiceSettings = ocServiceSettings;
        this.searchPayLoadUtils = searchPayLoadUtils;
        this.uiComponentsService = uiComponentsService;
    }

    public CompletionStage<JsonNode> create(JsonNode operationNode) {
        final Request<JsonNode> request = requestBuilderFactory.create()
                .setUrl(ocServiceSettings.getServicesSuffix() + "/operations")
                .setPayloadAsJson(operationNode)
                .responseAs(JsonNode.class)
                .build();
        return ocServiceHelper.sendPost(request);
    }

    public CompletionStage<JsonNode> update(Long id, JsonNode operationNode) {
        final Request<JsonNode> request = requestBuilderFactory.create()
                .setUrl(ocServiceSettings.getServicesSuffix() + "/operations/" + id)
                .setPayloadAsJson(operationNode)
                .responseAs(JsonNode.class)
                .build();
        return ocServiceHelper.sendPut(request);
    }

    public CompletionStage<Boolean> delete(Long id) {
        final Request<JsonNode> request = requestBuilderFactory.create()
                .setUrl(ocServiceSettings.getServicesSuffix() + "/operations/" + id)
                .setResponseMapper(booleanResponseMapperWrapper.create())
                .build();
        return ocServiceHelper.sendDelete(request);
    }

    public CompletionStage<JsonNode> clone(Long id) {
        final Request<JsonNode> request = requestBuilderFactory.create()
                .setUrl(ocServiceSettings.getServicesSuffix() + "/opercat/operations/" + id + "/clone")
                .setPayloadAsJson(null)
                .build();
        return ocServiceHelper.sendPost(request);
    }

    public CompletionStage<JsonNode> transitionState(Long id, Long stateId) {
        final Request<JsonNode> request = requestBuilderFactory.create()
                .setUrl(ocServiceSettings.getServicesSuffix() + "/opercat/operations/state/" + id + "/" + stateId)
                .setPayloadAsJson(null)
                .build();
        return ocServiceHelper.sendPost(request);
    }

    /**
     * operations search
     *
     * @param oData search oData
     * @param query search query parameters
     * @return search results
     */
    public CompletionStage<OperationResponse> search(Map<String, String> oData, Map query) {

        LOGGER.info("Performing operations search with the following oData: " + oData.toString());

        final JsonNode parameters = new ObjectMapper().convertValue(query, JsonNode.class);
        final ObjectNode searchPayload = searchPayLoadUtils.buildSearchPayload(parameters, EntityType.OPERATION, true);
        return ocServiceHelper.sendPost(requestBuilderFactory.create()
                .setUrl(ocServiceSettings.getServicesSuffix() + "/search/operations")
                .addQueryParameters(oData)
                .setPayloadAsJson(searchPayload)
                .responseAs(OperationResponse.class)
                .build()
        );
    }

    /**
     * operations search
     *
     * @param oData search oData
     * @return search results
     */
    public CompletionStage<OperationResponse> search(Map<String, String> oData) {

        LOGGER.info("Performing operations search with the following oData: " + oData.toString());

        final CatStateMachine stateMachine = getStateCatStateMachine();
        final String language = Http.Context.current().lang().language();

        return ocServiceHelper.sendGet(requestBuilderFactory.create()
                .setUrl(ocServiceSettings.getServicesSuffix() + "/operations")
                .addQueryParameters(oData)
                .responseAs(OperationResponse.class)
                .build()
        ).thenApply(operationResponse -> {
            operationResponse.getEntries().forEach(operation -> {
                final CatState state = stateMachine.getState(operation.getStateId());
                operation.setStateI18n(state.getI18n(language));
                operation.setStateName(state.getName());
            });
            return operationResponse;
        });
    }

    /**
     * operations search
     *
     * @return search results
     */
    public CompletionStage<Operation> getOperationById(Long id) {
        final CatStateMachine stateMachine = getStateCatStateMachine();
        final String language = Http.Context.current().lang().language();
        final NossisInvODataQueryParametersBuilder oDataQueryParametersBuilder = (NossisInvODataQueryParametersBuilder) NossisInvODataQueryParametersBuilder
                .getBuilder()
                .expand(OperationsCatalogConstants.RESOURCE_ATTRIBUTE_CONSTRAINTS)
                .expand(OperationsCatalogConstants.RESOURCE_ATTRIBUTE_ENTITY_CONSTRAINTS)
                .expand(OperationsCatalogConstants.RESOURCE_ATTRIBUTE_TAGS)
                .expand(OperationsCatalogConstants.RESOURCE_ATTRIBUTE_VERSION);

        return ocServiceHelper.sendGet(requestBuilderFactory.create()
                .setUrl(ocServiceSettings.getServicesSuffix() + "/operations/" + id)
                .addQueryParameters(oDataQueryParametersBuilder.build())
                .responseAs(Operation.class)
                .build()
        ).thenApply(operation -> {
            final CatState state = stateMachine.getState(operation.getStateId());
            operation.updateTagsAsString();
            operation.setStateI18n(state.getI18n(language));
            operation.setStateName(state.getName());
            operation.getOperationAttributes().forEach(attribute -> {
                attribute.getConstraints().forEach(constraint -> constraint.setTypeI18n(catEntityAPIServices.getCatEntityById(constraint.getIdCatEntity()).getI18n(language)));
                attribute.getEntityConstraints().forEach(constraint -> constraint.setTypeI18n(catEntityAPIServices.getCatEntityById(constraint.getIdCatEntity()).getI18n(language)));
            });

            return operation;
        });
    }

    /**
     * get the list of distinct entity names for every operation of a type
     *
     * @param version       Operation's version name
     * @param operationType Operation type (cfs,rfs,resources)
     * @return list of entity names
     */
    public CompletionStage<Result> entitiesSearch(String version, String operationType, String uiContext, String entityNameFilter, Long pageNumber) {
        //TODO: make it configurable, maybe?
        long pageLength = 30;

        // build oData query
        final Map<String, String> queryParameters = NossisInvODataQueryParametersBuilder
                .getBuilder()
                .top(pageLength)
                .skip((pageNumber - 1) * pageLength)
                .select(OperationsCatalogConstants.OPERATION_ENTITY_NAME).build();

        // apply uiContext and uiAction filter
        queryParameters.put(OperationsCatalogConstants.SERVICE_UI_CONTEXT, uiContext);
        queryParameters.put(OperationsCatalogConstants.SERVICE_UI_ACTION, UIContext.Action.SEARCH.name());

        Map<String, Object> postParameters = new HashMap<>();
        postParameters.put(OperationsCatalogConstants.OPERATION_TYPE_ATTRIBUTE_NAME, String.valueOf(getOperationTypeIdByName(operationType)));
        postParameters.put(OperationsCatalogConstants.VERSION_ATTRIBUTE_NAME, version);
        if (entityNameFilter != null && !entityNameFilter.trim().isEmpty()) {
            postParameters.put(OperationsCatalogConstants.OPERATION_ENTITY_NAME, "*" + entityNameFilter.trim() + "*");
        }

        final JsonNode parameters = new ObjectMapper().convertValue(postParameters, JsonNode.class);
        final ObjectNode searchPayload = searchPayLoadUtils.buildSearchPayload(parameters, EntityType.OPERATION, true);

        return ocServiceHelper.sendPost(requestBuilderFactory.create()
                .setUrl(ocServiceSettings.getServicesSuffix() + "/search/operations")
                .addQueryParameters(queryParameters)
                .setPayloadAsJson(searchPayload)
                .responseAs(OperationResponse.class)
                .build()
        ).thenApply(operationResponse -> {
            final SelectOptionList optionList = new SelectOptionList();
            optionList.setCount(operationResponse.getCount());
            optionList.setEntries(operationResponse.getEntries().stream()
                    .map(OperationDTSearch::getEntityName)
                    .map(SelectOption::new)
                    .collect(Collectors.toList()));
            return ok(Json.toJson(optionList));
        });
    }

    /**
     * get the operation type id based on it's name
     *
     * @param operationType operation type name
     * @return operation type id
     */
    public Long getOperationTypeIdByName(String operationType) {
        final CatEntity catEntity = catEntityAPIServices.getCatEntityByName(OperationsCatalogConstants.RESOURCE_OPERATION);
        final CatInsAttribute attribute = catEntity.getAttribute("type");
        final CatAttributeType attributeType = attribute.getAttributeType();
        for (final CatEnumeratedAttributeValue attributeValue : attributeType.getCatEnumeratedAttributeValue()) {
            if (attributeValue.getValue().toLowerCase().equals(operationType.toLowerCase())) {
                return attributeValue.getId();
            }
        }
        return -1L;
    }

    public TableConfigResponse dataTableEditableInputAttributes() {
        TableConfigBuilder builder = dataTableInputAttributesBuilder()
                .addColumn(new Column()
                        .name("actions")
                        .title(Messages.get("na.portal.operationscatalog.datatables.column.label.actions"))
                        .customRenderCallName("na.datatableTemplates.operationscatalog.operationDetailsActions")
                        .columnWidth(new FixedColumnWidth("80px")));
        return TableConfigResponse.from(builder.build());
    }

    public TableConfigResponse dataTableInputAttributes() {
        return TableConfigResponse.from(dataTableInputAttributesBuilder().build());
    }

    private TableConfigBuilder dataTableInputAttributesBuilder() {
        return new TableConfigBuilder()
                .pagination(Pagination.DISABLED)
                // name
                .addColumn(new Column()
                        .name("attribute")
                        .title(Messages.get("na.portal.operationscatalog.datatables.column.label.attributes_entities"))
                        .dataProperty("attribute")
                        .sortable(true)
                        .initialSortDirection(SortDirection.ASCENDANT)
                        .customRenderCallName("na.datatableTemplates.operationscatalog.operationInputAttribute"))
                // mandatory
                .addColumn(new Column()
                        .name("cardinality")
                        .title(Messages.get("na.portal.operationscatalog.datatables.column.label.cardinality"))
                        .dataProperty("cardinality")
                        .sortable(false)
                        .columnWidth(new MinimalColumnWidth())
                )
                // validationRule
                .addColumn(new Column()
                        .name("validationRule")
                        .title(Messages.get("na.portal.operationscatalog.datatables.column.label.validationRule"))
                        .dataProperty("validationRule")
                        .sortable(false)
                        .customRenderCallName("na.datatableTemplates.operationscatalog.validationRule"));
    }

    public TableConfigResponse dataTableSearch(Call route) {
        return TableConfigResponse.from(TableConfigBuilder.fromRoute(route)
                // name
                .addColumn(new Column()
                        .name("name")
                        .title(Messages.get("na.portal.operationscatalog.datatables.column.label.operation"))
                        .dataProperty("name")
                        .sortable(true)
                        .customRenderCallName("na.datatableTemplates.operationscatalog.columnWithValidationWarning.operation"))
                // entity
                .addColumn(new Column()
                        .name("entity")
                        .title(Messages.get("na.portal.operationscatalog.datatables.column.label.entityName"))
                        .dataProperty("entityName")
                        .sortable(true))
                // state
                .addColumn(new Column()
                        .name("state")
                        .title(Messages.get("na.portal.operationscatalog.datatables.column.label.state"))
                        .dataProperty("state")
                        .sortable(true)
                        .columnWidth(new FixedColumnWidth("125px"))
                        .customRenderCallName("na.datatableTemplates.operationscatalog.operationSearchState"))
                // description
                .addColumn(new Column()
                        .name("description")
                        .title(Messages.get("na.portal.operationscatalog.datatables.column.label.description"))
                        .dataProperty("description"))
                // tags
                .addColumn(new Column()
                        .name("tags")
                        .title(Messages.get("na.portal.operationscatalog.datatables.column.label.tags"))
                        .dataProperty("tags")
                        .customRenderCallName("na.datatableTemplates.operationscatalog.operationSearchTags"))
                // actions
                .addColumn(new Column()
                        .name("actions")
                        .title(Messages.get("na.portal.operationscatalog.datatables.column.label.actions"))
                        .customRenderCallName("na.datatableTemplates.operationscatalog.operationSearchActions")
                        .columnWidth(new FixedColumnWidth("125px")))
                .build()
        );
    }

    public Operation createCfsPojo(final Version version, final String lang) {
        return createOperationPojo(version, lang, OperationsCatalogConstants.CFS_TYPE);
    }

    public Operation createRfsPojo(final Version version, final String lang) {
        return createOperationPojo(version, lang, OperationsCatalogConstants.RFS_TYPE);
    }

    public Operation createResourcePojo(final Version version, final String lang) {
        return createOperationPojo(version, lang, OperationsCatalogConstants.RESOURCE_TYPE);
    }


    private Operation createOperationPojo(final Version version, final String lang, final int type) {

        // initial state to a new operation
        final CatState initialState = getStateCatStateMachine().getInitialState();

        // Default values for dynamic web components
        final Operation operation = Entity.withCatEntityAPIService(catEntityAPIServices, Operation::new);
        operation.setWorkflow(ocServiceSettings.getWorkflowDefaultValue());
        operation.setVersion(version);
        operation.setType(String.valueOf(type));
        operation.setStateId(initialState.getId());
        operation.setStateName(initialState.getName());
        operation.setStateI18n(initialState.getI18n(lang));

        return operation;
    }

    @SuppressWarnings("unchecked")
    public CompletionStage<Result> operationSearch(String operationType, String context, JsonNode node) {

        final TableQuery<Map> query = TableQuery.fromJson(node, Map.class);

        // filter operations by type
        if (operationType != null && !operationType.isEmpty()) {
            query.getParameters().put(OperationsCatalogConstants.OPERATION_TYPE_ATTRIBUTE_NAME, String.valueOf(getOperationTypeIdByName(operationType)));
        }

        // build oData query
        final Map<String, String> oData = NossisInvODataQueryParametersBuilder
                .getBuilder()
                .top(query)
                .skip(query)
                .orderBy(query)
                .expand(OperationsCatalogConstants.TAGS_ATTRIBUTE_NAME)
                .expand(OperationsCatalogConstants.VERSION_ATTRIBUTE_NAME)
                .build();

        if (query.getSorts().isEmpty()) {
            oData.putAll(NossisInvODataQueryParametersBuilder.getBuilder().orderBy("updatedAt desc").build());
        }

        // apply uiContext and uiAction filter
        if (context != null && !context.isEmpty()) {
            oData.put(OperationsCatalogConstants.SERVICE_UI_CONTEXT, context);
        }
        oData.put(OperationsCatalogConstants.SERVICE_UI_ACTION, UIContext.Action.SEARCH.name());
        final boolean hasOperationCreatePermission = SecurityUtils.isAuthorized(AAAPIResources.operationsCatalog().C());
        final boolean hasOperationUpdatePermission = SecurityUtils.isAuthorized(AAAPIResources.operationsCatalog().U());
        final boolean hasOperationDeletePermission = SecurityUtils.isAuthorized(AAAPIResources.operationsCatalog().D());
        final boolean hasOperationExecutePermission = SecurityUtils.isAuthorized(AAAPIResources.operationsCatalog().X());
        final CatStateMachine stateMachine = getStateCatStateMachine();
        final String language = Http.Context.current().lang().language();

        final String catEntityVersionName = String.valueOf(query.getParameters().get(OperationsCatalogConstants.VERSION_CAT_ENTITY_VERSION));

        return search(oData, query.getParameters())
                .thenCombineAsync(entitiesService.getVersion(catEntityVersionName), (operationResponse, catEntityVersion) -> {
                    operationResponse.getEntries().forEach(operationDT -> {
                        operationDT.updateTagsAsString();
                        final CatState catState = stateMachine.getState(operationDT.getStateId());
                        operationDT.setStateI18n(catState.getI18n(language));
                        operationDT.setStateName(catState.getName());

                        operationDT.setEditPermission(hasOperationUpdatePermission);
                        operationDT.setClonePermission(hasOperationCreatePermission);
                        operationDT.setDeletePermission(hasOperationDeletePermission);
                        operationDT.setTransitionStatePermission(hasOperationExecutePermission);
                        if (operationDT.hasEditPermission()) {
                            operationDT.setEditPossibility(operationPermissionService.canEdit(operationDT));
                        }
                        if (operationDT.hasClonePermission()) {
                            operationDT.setClonePossibility(operationPermissionService.canClone(operationDT));
                        }
                        if (operationDT.hasDeletePermission()) {
                            operationDT.setDeletePossibility(operationPermissionService.canDelete(operationDT));
                        }
                        if (operationDT.hasTransitionStatePermission()) {
                            operationDT.setTransitionStatePossibility(operationPermissionService.canTransitionStates(operationDT));
                            if (operationDT.canTransitionState()) {
                                final List<State> stateList = stateMachine.getNextStates(operationDT.getStateId())
                                        .stream()
                                        .map(state -> new State(state.getId(), state.getName(), state.getI18n(language)))
                                        .filter(state -> operationPermissionService.canTransitionToState(operationDT, state))
                                        .collect(Collectors.toList());
                                operationDT.setNextStates(stateList);
                            }
                        }
                    });
                    return operationResponse;
                })
                .thenApply(operation -> ok(datatablesUtils.genFormattedDataTableData(operation.getEntries(), query.getVersion(), operation.getCount())));
    }

    public String getIconStateCss(String name) {
        switch (name) {
            case "BLOCKED":
                return "glyphicon glyphicon-ban-circle text-danger fx-icon";
            case "INSERVICE":
                return "glyphicon glyphicon-ok text-success fx-icon";
            case "SIMULATED":
                return "glyphicon glyphicon-list-alt text-info fx-icon";
        }
        return null;
    }

    /**
     * get the operation cat state machine
     *
     * @return operation cat state machine
     */
    public CatStateMachine getStateCatStateMachine() {
        final CatEntity catEntity = catEntityAPIServices.getCatEntityByName(EntityType.OPERATION);
        return catEntity.getAttribute(OperationsCatalogConstants.STATE_ATTRIBUTE_NAME).getAttributeType().getCatStateMachine();
    }

    public List<State> getNextStates(Operation operation) {
        final String language = Http.Context.current().lang().language();
        return getStateCatStateMachine().getNextStates(operation.getStateId())
                .stream()
                .map(catState -> new State(catState.getId(), catState.getName(), catState.getI18n(language)))
                .filter(state -> operationPermissionService.canTransitionToState(operation, state))
                .collect(Collectors.toList());
    }

    public Map<String, WebComponent> getHeaderComponentsToEdit(Operation operation, String context, String entitiesUrl) {
        Map<String, WebComponent> headerComponents = getHeaderComponents(operation, context, Action.EDIT, entitiesUrl);

        // Is not possible to change operation's entity after the operation has been created
        headerComponents.get(OperationsCatalogConstants.OPERATION_ENTITY_NAME).getDataAttributes().put("disabled", null);

        return headerComponents;
    }

    public Map<String, WebComponent> getHeaderComponentsToCreate(Operation operation, String context, String entitiesUrl) {
        return getHeaderComponents(operation, context, Action.CREATE, entitiesUrl);
    }

    /**
     * Get static web components for operation entity
     */
    private Map<String, WebComponent> getHeaderComponents(Operation operation, String context, Action action, String entitiesUrl) {

        final String NAME = "name";
        final String DESCRIPTION = "description";
        final String STATE = "state";

        final Map<String, WebComponent> components = new HashMap<>();

        // entity
        final SelectBox entitiesSelect = new SelectBox();
        entitiesSelect.setQueryUrl(entitiesUrl);
        entitiesSelect.setId(OperationsCatalogConstants.OPERATION_ENTITY_NAME);
        entitiesSelect.setDataField(OperationsCatalogConstants.OPERATION_ENTITY_NAME);
        entitiesSelect.setPlaceholder(uiComponentsService.getPlaceholderI18n(context, OperationsCatalogConstants.OPERATION_ENTITY_NAME));
        entitiesSelect.setAction(action);
        entitiesSelect.setValue(operation.getEntityName());
        entitiesSelect.addOption(operation.getEntityName());
        entitiesSelect.setRequired(true);
        entitiesSelect.setDisabled(action.equals(Action.EDIT));
        components.put(OperationsCatalogConstants.OPERATION_ENTITY_NAME, entitiesSelect);

        // name
        final InputText nameComponent = new InputText();
        nameComponent.setAction(action);
        nameComponent.setId(NAME);
        nameComponent.setDataField(NAME);
        nameComponent.setPlaceholder(uiComponentsService.getPlaceholderI18n(context, NAME));
        nameComponent.setValue(operation.getName());
        nameComponent.setRequired(true);
        components.put(NAME, nameComponent);

        // description
        final InputText descriptionComponent = new InputText();
        descriptionComponent.setAction(action);
        descriptionComponent.setId(DESCRIPTION);
        descriptionComponent.setDataField(DESCRIPTION);
        descriptionComponent.setValue(operation.getDescription());
        descriptionComponent.setPlaceholder(uiComponentsService.getPlaceholderI18n(context, DESCRIPTION));
        descriptionComponent.setDataAttributes(Collections.singletonMap("style", "width: 100%; max-width: 100%"));
        components.put(DESCRIPTION, descriptionComponent);

        // state icon
        final IconLabel iconLabelComponent = new IconLabel();
        iconLabelComponent.setTargetId(STATE);
        iconLabelComponent.setId(String.valueOf(operation.getStateId()));
        iconLabelComponent.setI18n(new I18n().setValue(operation.getStateI18n()));
        iconLabelComponent.setIconClass(getIconStateCss(operation.getStateName()));
        iconLabelComponent.setAction(action);
        components.put(STATE, iconLabelComponent);

        return components;
    }
}