@import na.catalog.basemodule.ui.WebComponent
@import na.operationscatalog.settings.OperationsCatalogConstants

@(elements: java.util.List[WebComponent], versionComponent: WebComponent)

@na.operationscatalog.views.html.roots.operation.render(
    headerTitleI18n = "na.portal.operationscatalog.nav.rfs",
    iconClass = "fuxicons fuxicons-network-configuration",
    searchContext = OperationsCatalogConstants.RFS_CONTEXT,
    tableId = "rfs-search-table",
    searchPageDirective = "data-na-portal-operations-catalog-rfs-search-page",
    resultsTableDirective = "data-na-portal-operations-catalog-rfs-datatable",
    dataTableConfigUrl = na.operationscatalog.controllers.routes.RfsController.rfsDataTableSearch(),
    elements = elements,
    versionComponent = versionComponent
)