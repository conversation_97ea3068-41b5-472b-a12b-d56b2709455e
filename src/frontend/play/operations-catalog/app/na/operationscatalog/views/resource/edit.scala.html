@(
        elements: List[na.catalog.basemodule.ui.WebComponent],
        headerComponents: Map[String, na.catalog.basemodule.ui.WebComponent],
        operation: na.operationscatalog.models.operations.Operation
)

@import na.operationscatalog.settings.{AAAPIResources, OperationsCatalogConstants}
@import pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext.Action

@na.operationscatalog.views.html.operations.createOrEdit.render(
    iconClass = "fuxicons fuxicons-resource",
    operationContext = OperationsCatalogConstants.RESOURCE_CONTEXT,
    pageDirective = "data-na-portal-operations-catalog-resource-edit-page",
    elements = elements,
    headerComponents = headerComponents,
    permission = AAAPIResources.operationsCatalog.U.toString,
    action = Action.EDIT,
    typeId = null,
    operation = operation
)