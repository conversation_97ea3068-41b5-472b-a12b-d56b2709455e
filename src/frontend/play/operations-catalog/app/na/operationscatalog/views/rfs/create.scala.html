@(
        elements: List[na.catalog.basemodule.ui.WebComponent],
        headerComponents: Map[String, na.catalog.basemodule.ui.WebComponent],
        typeId: Long,
        operation: na.operationscatalog.models.operations.Operation
)

@import na.operationscatalog.settings.{AAAPIResources, OperationsCatalogConstants}
@import pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext.Action

@na.operationscatalog.views.html.operations.createOrEdit.render(
    iconClass = "fuxicons fuxicons-network-configuration",
    operationContext = OperationsCatalogConstants.RFS_CONTEXT,
    pageDirective = "data-na-portal-operations-catalog-rfs-create-page",
    elements = elements,
    headerComponents = headerComponents,
    permission = AAAPIResources.operationsCatalog.C.toString,
    action = Action.CREATE,
    typeId = typeId,
    operation = operation
)