@import na.naportalbase.utils.TemplateUtils
@import pt.alticelabs.nossis.messages.api.Messages
@import pt.alticelabs.nossis.security.views.html.isAuthorized

@(
    authorization: Array[String],
    saveAttributes: scala.collection.immutable.Map[String, String],
    cancelAttributes: scala.collection.immutable.Map[String, String]
)

@saveAdditionalClass = @{
    saveAttributes.getOrElse("class", "")
}
@saveExtraAttributes = @{
    TemplateUtils.dynamicElementsAttributes(saveAttributes - "class")
}
@cancelAdditionalClass = @{
    cancelAttributes.getOrElse("class", "")
}
@cancelExtraAttributes = @{
    TemplateUtils.dynamicElementsAttributes(cancelAttributes - "class")
}

@isAuthorized(authorization) {
    <a href="javascript:void(0)" class="btn btn-primary @saveAdditionalClass" @saveExtraAttributes>
    @Messages.get("na.button.label.save")
    </a>
    <a href="javascript:void(0)" class="btn btn-default @cancelAdditionalClass" @cancelExtraAttributes>
    @Messages.get("na.button.label.cancel")
    </a>
}