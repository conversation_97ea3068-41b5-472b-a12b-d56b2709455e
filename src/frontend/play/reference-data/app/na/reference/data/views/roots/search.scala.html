@(
        entityName: String,
        element: na.catalog.basemodule.ui.WebComponent,
        headerSkeletonHelper: na.reference.data.utils.views.skeletons.HeaderSkeletonHelper
)

@import na.naportalbase.views.tags.i18n
@import na.reference.data.settings.{AAAPIResources, ReferenceDataConstants}
@import na.reference.data.utils.views.FormFieldContainerLocation
@import na.reference.data.views.html.components.leftSideNavigationBarComponents
@import na.reference.data.views.html.search.leftSideForm
@import na.reference.data.views.html.skeletons.mainSkeleton
@import pt.alticelabs.nossis.security.views.html.authorized

@toolbar = {
    <div class="fx-bulk-actions page__search-results-bulk-actions clearfix hidden" data-na-portal-reference-data-table-toolbar>
        @authorized(AAAPIResources.catalog.U.toString) {
            <button role="button" class="btn btn-default btn-xs" title="@i18n("na.datatables.action.label.edit")" data-action="edit">
                <i class="glyphicon glyphicon-pencil"></i>
                @i18n("na.datatables.action.label.edit")
            </button>
        }
        @authorized(AAAPIResources.catalog.C.toString) {
            <button role="button" class="btn btn-default btn-xs" title="@i18n("na.datatables.action.label.clone")" data-action="clone">
                <i class="fuxicons fuxicons-clone"></i>
                @i18n("na.datatables.action.label.clone")
            </button>
        }
        @authorized(AAAPIResources.catalog.D.toString) {
            <button role="button" class="btn btn-default btn-xs" title="@i18n("na.datatables.action.label.remove")" data-action="delete">
                <i class="glyphicon glyphicon-remove"></i>
                @i18n("na.datatables.action.label.remove")
            </button>
        }
    </div>
}


@mainSkeleton() {
    <div class="fx-push-footer fx-full-height" data-na-portal-reference-data-search-page>
        @leftSideNavigationBarComponents.render(ReferenceDataConstants.CONTEXT_CATALOG)
        <x-splitter class="splitter splitter--reference-data-search">
            <div slot="collapsed-left">
                <div class="fx-info-sidebar-collapsed collapsed-search left">
                    <div class="fx-sidebar-header">
                        <span class="fx-entity-header-icon">
                            <i class="fa fa-search"></i>
                        </span>
                    </div>
                </div>
            </div>

            <div slot="left"
              class="fx-splitter-sidebar with-buttons search-sidebar"
              data-na-portal-reference-data-entity-search-form
              data-na-portal-reference-data-entity-search-form-location="@FormFieldContainerLocation.SEARCH_SIDEBAR">
                @leftSideForm(entityName, element)
            </div>

            <div class="page__search-results" slot="right">
                <x-shadow-scroll class="page__search-results-table-container">
                    <div class="fx-entity-header page__search-results-header">
                        <div class="fx-entity-header-info">
                        <span class="fx-entity-header-icon">
                            <i class="fuxicons fuxicons-catalogue"></i>
                        </span>
                            <div class="fx-entity-header-title">
                                <h1>
                                    @entityName
                                </h1>
                            </div>
                        </div>
                        <div class="fx-entity-header-actions">
                            @headerSkeletonHelper.createEntityButtonComponent()
                        </div>
                    </div>
                    @toolbar
                    <div id="entity-content" class="page__search-results-table fx-entity-info">
                        <table id="catalogResults"
                        class="table fx-table no-footer dataTable"
                        data-na-portal-table-datatable
                        data-na-portal-reference-data-search-table
                        data-na-portal-reference-data-search-table-actions
                        data-na-portal-reference-data-search-table-config
                        ></table>
                    </div>
                </x-shadow-scroll>
            </div>
        </x-splitter>
    </div>
}