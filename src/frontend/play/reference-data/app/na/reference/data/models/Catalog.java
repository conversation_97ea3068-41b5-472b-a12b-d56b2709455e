package na.reference.data.models;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class Catalog {

    private long id;
    private long idCatEntity;
    private String catEntityName;
    private String name;
    private String icon;
    private String iconTitle;
    private String dtRowId;
    private boolean clonePermission = false;
    private boolean deletePermission = false;
    private boolean editPermission = false;
    @JsonIgnore
    private Set<String> instanceAttributes = new HashSet<>();
    private final Map<String, Object> dynamicAttributes = new HashMap<>();

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @JsonProperty("DT_RowId")
    public String getDtRowId() {
        return dtRowId;
    }

    public void setDtRowId(String dtRowId) {
        this.dtRowId = dtRowId;
    }

    public long getIdCatEntity() {
        return idCatEntity;
    }

    public void setIdCatEntity(long idCatEntity) {
        this.idCatEntity = idCatEntity;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCatEntityName() {
        return catEntityName;
    }

    public void setCatEntityName(String catEntityName) {
        this.catEntityName = catEntityName;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIconTitle() {
        return iconTitle;
    }

    public void setIconTitle(String iconTitle) {
        this.iconTitle = iconTitle;
    }

    @JsonIgnore
    public void setInstanceAttributes(Set<String> instanceAttributes) {
        this.instanceAttributes = instanceAttributes;
    }

    @JsonAnyGetter
    public Map<String, Object> any() {
        final Map<String, Object> result = new HashMap<>();
        for (Map.Entry<String, Object> entry : dynamicAttributes.entrySet()) {
            if (instanceAttributes.contains(entry.getKey())) {
                result.put(entry.getKey(), entry.getValue());
            }
        }
        return result;
    }

    @JsonAnySetter
    public void setdynamic(String name, Object value) {
        dynamicAttributes.put(name, value);
    }

    public String getClonePermission() {
        return Boolean.toString(clonePermission);
    }

    public void setClonePermission(boolean clonePermission) {
        this.clonePermission = clonePermission;
    }

    public String getDeletePermission() {
        return Boolean.toString(deletePermission);
    }

    public void setDeletePermission(boolean deletePermission) {
        this.deletePermission = deletePermission;
    }

    public String getEditPermission() {
        return Boolean.toString(editPermission);
    }

    public void setEditPermission(boolean editPermission) {
        this.editPermission = editPermission;
    }
}