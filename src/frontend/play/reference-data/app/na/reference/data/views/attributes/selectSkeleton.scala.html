@(
        uiContainer: na.catalog.basemodule.models.nossisinv.attributes.UIContainer,
        uiElement: pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,
        label: String = "",
        selectParameters: java.util.Map[String, String],
        selectBox: na.catalog.basemodule.ui.SelectBox
)

@import na.catalog.basemodule.utils.InputUtils

@action = @{
    uiContainer.getUiContext.getAction
}

@id = @{
    selectParameters.get("id")
}

@divCss = @{
    InputUtils.colClassForDiv(uiElement.getUIElementWidth, action)
}

@labelCss = @{
    InputUtils.colClassForLabel(uiElement.getUIElementWidth, action)
}

@required = @{
    InputUtils.requiredLabelClass(uiElement, action)
}

<label is="x-label" for="@id" class="@labelCss @required control-label">@label</label>

<div class="@divCss">@selectBox.render()</div>