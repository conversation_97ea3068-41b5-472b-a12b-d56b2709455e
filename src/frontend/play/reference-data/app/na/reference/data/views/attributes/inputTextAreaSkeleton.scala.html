@(
        inputId: String = "",
        uiContainer: na.catalog.basemodule.models.nossisinv.attributes.UIContainer,
        uiElement: pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,
        label: String = "",
        value: String = "",
        inputAttributes: java.util.Map[String, String]
)

@import na.naportalbase.utils.TemplateUtils
@import na.catalog.basemodule.utils.InputUtils

@action = @{
    uiContainer.getUiContext.getAction
}

@uiComponentWidth = @{
    uiElement.getUIComponentWidth
}

@uiElementWidth = @{
    uiElement.getUIElementWidth
}

@divCss = @{
    InputUtils.colClassForDiv(uiElementWidth, action)
}

@labelCss = @{
    InputUtils.colClassForLabel(uiElementWidth, action)
}

@required = @{
    InputUtils.requiredLabelClass(uiElement, action)
}

<label for="@inputId" is="x-label" class="@labelCss @required control-label">@label</label>

<div class="@divCss">
    <textarea
    id ="@inputId"
    class="form-control full-width @InputUtils.widthSizeForInput(uiComponentWidth)"
    @TemplateUtils.dynamicElementsAttributes(inputAttributes)
    >@value</textarea>
</div>