@(
        entityName: String,
        element: na.catalog.basemodule.ui.WebComponent
)

@import na.naportalbase.views.tags.i18n
@import scala.collection.mutable
@import java.util.HashMap
@import na.catalog.basemodule.ui.{Label, WebComponent}
@import na.naportalbase.views.html.components.buttonsComp.leftSideFormInputButtonsComp

@renderLabel(element: WebComponent) = @{
    if(element.getI18n != null) {

    val dataAttributes = new HashMap[String, String]()
    dataAttributes.put("class", "col-xs-12 control-label")

    val label = new Label(element.getId, element.getI18n)
    label.setDataAttributes(dataAttributes)
    label.render()
    }
}

@content = {
    @renderLabel(element)
    @container
}

@container = {
<div class="col-xs-12 field-container__slot">
    @element.render()
</div>
}

@searchButtonAttributes = @{
    val map = mutable.HashMap[String, String]()
    map.put("data-form-action", "submit")
    map.put("id", "submit_form")
    map.put("type", "submit")
    map.put("value", i18n("na.buttons.search"))
    map
}

@clearButtonAttributes = @{
    val map = mutable.HashMap[String, String]()
    map.put("data-form-action", "clear")
    map.put("id", "clean_form")
    map.put("type", "reset")
    map.put("value", i18n("na.buttons.clear"))
    map
}

@formButtons = {
    @leftSideFormInputButtonsComp.render(
        searchButtonAttributes,
        clearButtonAttributes
    )
}

@renderedButtons = {
    <div class="form-group search-sidebar__button-list">
        <div class="fx-splitter-sidebar-buttons">
            <div class="fx-splitter-sidebar-buttons-inner">
                @formButtons
            </div>
        </div>
    </div>
}

@renderedSaveFilterButton = {
    <br/>
}

<header class="fx-sidebar-header search-sidebar__header">
    <div class="fx-sidebar-header-inner">
        <p>
            <span class="fx-entity-header-icon">
                <i class="fa fa-search"></i>
            </span>
            <x-i18n key="na.basemodule.lateralSearch.title"></x-i18n>
        </p>
    </div>
</header>
<div class="fx-sidebar-content-wrapper search-sidebar__field-list">
    <form id="catalog-search-form" class="form form--sidebar">
        <div class="row form__header">
            <div class="fx-sidebar-nav-title">
                <h4>@entityName</h4>
            </div>
        </div>
        @if(element != null) {
        <div class="fx-sidebar-content-wrapper search-sidebar__field-list">
            <x-shadow-scroll inline>
                <div class="row form__fields-row" data-form-fields-container>
                    <!-- base search element -->
                    <div class="col-xs-12 field-container field-container--sidebar-form">
                        <div class="form-group">
                            @content
                        </div>
                    </div>
                </div>
            </x-shadow-scroll>
        </div>
        }
    </form>
</div>
<div class="form-group search-sidebar__button-list">
    <div class="fx-splitter-sidebar-buttons">
        <div class="fx-splitter-sidebar-buttons-inner">
            @renderedButtons
        </div>
    </div>
</div>