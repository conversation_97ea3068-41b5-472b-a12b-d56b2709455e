@(
        content: Html = Html(""),
        fieldLocation: na.reference.data.utils.views.FormFieldContainerLocation
)

@import na.reference.data.utils.views.FormFieldContainerLocation

@containerElementClasses = @{
    fieldLocation match {
        case FormFieldContainerLocation.DASHBOARD => "col-xs-12 col-md-4 field-container field-container--dashboard-form"
        case FormFieldContainerLocation.SEARCH_SIDEBAR => "col-xs-12 field-container field-container--sidebar-form"
        case _ => "col-xs-12 field-container"
    }
}

<div class="@containerElementClasses">
    <div class="form-group">
    @content
    </div>
</div>