package na.reference.data.services;

import com.fasterxml.jackson.databind.JsonNode;
import na.catalog.basemodule.exceptions.CatalogBaseModuleExceptions;
import na.catalog.basemodule.models.nossisinv.attributes.UICatInsAttribute;
import na.catalog.basemodule.models.nossisinv.attributes.UIContainer;
import na.catalog.basemodule.models.nossisinv.attributes.UIContainerClone;
import na.catalog.basemodule.services.CatEntityAPIServices;
import na.catalog.basemodule.services.parser.AttributeParser;
import na.naportalbase.services.parser.Parser;
import na.reference.data.settings.ReferenceDataConstants;
import na.reference.data.settings.ReferenceDataModule;
import play.libs.concurrent.HttpExecutionContext;
import play.mvc.Http;
import pt.alticelabs.nossis.messages.api.Messages;
import pt.ptinovacao.netwin.kernel.catalog.client.model.CatEntity;
import pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext;
import pt.ptinovacao.netwin.kernel.catalog.client.model.implementation.UIContextImpl;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

@Singleton
public final class UIServices {

    private final CatEntityAPIServices catEntityAPIServices;
    private final HttpExecutionContext httpExecutionContext;
    private final ReferenceDataRestApi referenceDataRestApi;

    @Inject
    private UIServices(final HttpExecutionContext httpExecutionContext,
                       final ReferenceDataModule referenceDataModule,
                       final ReferenceDataRestApi referenceDataRestApi) {
        this.catEntityAPIServices = referenceDataModule.getCatEntityApiServices();
        this.httpExecutionContext = httpExecutionContext;
        this.referenceDataRestApi = referenceDataRestApi;
    }

    public CompletionStage<UIContainer> getCatalogUi(final long idCatEntity, final UIContext.Action action) {
        return referenceDataRestApi.getCatalog(idCatEntity).thenComposeAsync(
                (JsonNode jsonNode) -> CompletableFuture.completedFuture(buildUIContainer(jsonNode, action)),
                httpExecutionContext.current()
        );
    }

    public CompletionStage<UIContainer> getCatalogToCloneUi(final long idCatEntity) {
        return getCatalogUi(idCatEntity, UIContext.Action.CREATE).thenApply(
                (UIContainer uiContainer) -> {
                    final UIContainerClone uiContainerClone = new UIContainerClone(uiContainer);
                    final UIContextImpl uiContext = (UIContextImpl) uiContainerClone.getUiContext();
                    uiContext.setAction(UIContext.Action.CLONE);
                    return uiContainerClone;
                });
    }

    public UIContainer getUIContainer(final Long id,
                                      final CatEntity catEntity,
                                      final Map<String, UICatInsAttribute<?>> uICatInsAttribute,
                                      final UIContext uiContext) {
        return new UIContainer(
                id,
                catEntity.getId(),
                ReferenceDataConstants.DOMAIN_NA_CATALOG,
                ReferenceDataConstants.FORM_ATTRIBUTES,
                catEntity.getI18n(Http.Context.current().lang().language()),
                catEntity,
                uiContext,
                uICatInsAttribute,
                new HashMap<>()
        );
    }

    public UIContainer buildUIContainer(final JsonNode jsonNode, final UIContext.Action action) {

        final Map<String, Object> entityAttributes = Parser.jsonAsMap(jsonNode);
        final Long idCatEntity = Parser.getLongValue(entityAttributes.get(ReferenceDataConstants.ID_CAT_ENTITY));

        if (idCatEntity == null) {
            throw CatalogBaseModuleExceptions.preConditionFailedException(
                    ReferenceDataConstants.ATTRIBUTE,
                    Messages.get("na.reference.data.notification.preconditionfailed.idcatentity")
            );
        }

        final CatEntity catEntity = catEntityAPIServices.getCatEntityById(idCatEntity);
        final Map<String, UICatInsAttribute<?>> uICatInsAttributes = AttributeParser.parseAttributesMapToAttributes(
                ReferenceDataConstants.ENTITY_UI_CONTEXT,
                action,
                catEntity,
                entityAttributes
        );

        return getUIContainer(
                Parser.getLongValue(entityAttributes, ReferenceDataConstants.ID),
                catEntity,
                uICatInsAttributes,
                catEntity.getUIContext(ReferenceDataConstants.ENTITY_UI_CONTEXT, action)
        );
    }
}