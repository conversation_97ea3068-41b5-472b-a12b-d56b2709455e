@(
        saveAttributes: java.util.Map[String, String],
        cancelAttributes: java.util.Map[String, String]
)

@import na.naportalbase.utils.TemplateUtils
@import na.naportalbase.views.tags.i18n

@* save *@
<button class="btn btn-primary" @TemplateUtils.dynamicElementsAttributes(saveAttributes)>
    <i class="fuxicons fuxicons-floppy-disk"></i> @i18n("na.button.label.save")
</button>
@* cancel *@
<button class="btn btn-default" @TemplateUtils.dynamicElementsAttributes(cancelAttributes)>
@i18n("na.button.label.cancel")
</button>