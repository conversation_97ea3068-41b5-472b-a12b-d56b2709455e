@(
        uiContainer: na.catalog.basemodule.models.nossisinv.attributes.UIContainer,
        element: na.catalog.basemodule.ui.WebComponent,
        catalogInstanceDetails: na.reference.data.utils.views.CatalogInstanceDetails,
        headerSkeletonHelper: na.reference.data.utils.views.skeletons.HeaderSkeletonHelper,
        uiAttributesService: na.reference.data.services.UIAttributesService
)

@import na.reference.data.views.html.skeletons.mainSkeleton

@mainSkeleton() {
    <div class="fx-push-footer fx-full-height">
    @na.reference.data.views.html.skeletons.detailsSkeleton.render(
        uiContainer = uiContainer,
        element = element,
        catalogInstanceDetails = catalogInstanceDetails,
        headerSkeletonHelper = headerSkeletonHelper,
        uiAttributesService = uiAttributesService
    )
    </div>
}