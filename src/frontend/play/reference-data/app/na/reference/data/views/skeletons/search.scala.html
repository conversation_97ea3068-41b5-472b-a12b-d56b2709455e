@(
        entityName: String,
        element: na.catalog.basemodule.ui.WebComponent
)

@import na.reference.data.utils.views.FormFieldContainerLocation
@import na.reference.data.views.html.search.submitFormButtons
@import na.reference.data.views.html.skeletons.searchFormSkeleton

@bottomButtons = {
    <div class="fx-main-searchbox-item-search clearfix">
        <div class="pull-right">
        @submitFormButtons.render()
        </div>
    </div>
}

@fieldLocation = @{
    FormFieldContainerLocation.DASHBOARD
}

<div id="fx-entity-content" class="fx-entity-info">
    <div class="row">
        <div class="col-xs-12 fx-tabbed-search-container-inner">
            <div
            class="fx-main-searchbox tab-content nav-tabs-stacked-left-content"
            data-na-portal-reference-data-entity-search-form
            data-na-portal-reference-data-entity-search-form-location="@fieldLocation"
            >
            @searchFormSkeleton.render(
                entityName = entityName,
                bottomButtons = bottomButtons,
                element = element,
                fieldLocation = fieldLocation
            )
            </div>
        </div>
    </div>
</div>