package na.reference.data.services;

import com.fasterxml.jackson.databind.JsonNode;
import na.naportalbase.exceptions.ExceptionCodes;
import na.naportalbase.models.services.GenericResponse;
import na.naportalbase.services.ErrorHandler;
import na.naportalbase.services.ServiceResponseHandler;
import na.naportalbase.settings.BaseMappings;
import na.naportalbase.settings.ExpectedHttpResponseCodes;
import play.mvc.Http;
import pt.ptinovacao.nossis.exceptions.BusinessException;
import pt.ptinovacao.nossis.exceptions.FaultException;

import java.io.IOException;
import java.util.Arrays;

final class ReferenceDataErrorHandler implements ErrorHandler {

    @Override
    public void handleBadResponse(final String requestType, final GenericResponse response) {
        if (ExpectedHttpResponseCodes.isExpectedErrorCode(requestType, response.getStatus())) {
            if (ServiceResponseHandler.isJsonContentType(response)) {
                final JsonNode body;
                try {
                    body = BaseMappings.dualMapper.readTree(response.getBody());
                } catch (IOException e) {
                    throw new BusinessException(ExceptionCodes.UNKNOWN_EXCEPTION, new Throwable("Error while serializing response body:\n" + e.getMessage()));
                }
                throw restException(body);
            } else {
                throw new BusinessException(ExceptionCodes.REST_API_UNAVAILABLE_EXCEPTION, new Throwable(response.getStatusText()));
            }
        } else if (Arrays.asList(Http.Status.BAD_GATEWAY, Http.Status.GATEWAY_TIMEOUT, Http.Status.REQUEST_TIMEOUT).contains(response.getStatus())) {
            throw new BusinessException(ExceptionCodes.REST_API_UNAVAILABLE_EXCEPTION, new Throwable(response.getStatusText()));
        } else {
            throw new BusinessException(ExceptionCodes.UNKNOWN_EXCEPTION, new Throwable(response.getStatusText()));
        }
    }

    private FaultException restException(final JsonNode response) {
        return new FaultException(ExceptionCodes.REST_EXCEPTION, "backendMessage", ServiceResponseHandler.getBackendUserMessage(response));
    }
}