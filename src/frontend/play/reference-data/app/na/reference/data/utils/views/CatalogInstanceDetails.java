package na.reference.data.utils.views;

import na.catalog.basemodule.ui.Action;
import na.reference.data.models.Catalog;
import play.twirl.api.Html;
import pt.ptinovacao.netwin.kernel.catalog.client.model.CatEntity;
import pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext;

/**
 * Used as template parameter to apply page directives as well as necessary DOM attributes on catalog instance details
 * templates
 *
 * <AUTHOR> <<EMAIL>>, 21-05-2019.
 */
public final class CatalogInstanceDetails {

    final private long catalogInstanceId;
    final private String catalogInstanceName;
    final private long catalogTypeId;
    final private String catalogTypeName;
    final private Action action;

    private CatalogInstanceDetails(final long catalogInstanceId,
                                   final String catalogInstanceName,
                                   final long catalogTypeId,
                                   final String catalogTypeName,
                                   final Action action) {
        this.catalogInstanceId = catalogInstanceId;
        this.catalogInstanceName = catalogInstanceName;
        this.catalogTypeId = catalogTypeId;
        this.catalogTypeName = catalogTypeName;
        this.action = action;
    }

    public static CatalogInstanceDetails fromAction(final Action action) {
        return new CatalogInstanceDetails(0, null, 0, null, action);
    }

    public static CatalogInstanceDetails fromAction(final UIContext.Action action) {
        return fromAction(adaptAction(action));
    }

    public static CatalogInstanceDetails fromInstanceWithContext(final Catalog catalog,
                                                                 final CatEntity catEntity,
                                                                 final Action action,
                                                                 final String language) {
        final long catalogInstanceId = catalog.getId();
        final String catalogInstanceName = catalog.getName();
        final long catalogTypeId = catEntity.getId();
        final String catalogTypeName = catEntity.getI18n(language);
        return new CatalogInstanceDetails(catalogInstanceId, catalogInstanceName, catalogTypeId, catalogTypeName, action);
    }

    public static CatalogInstanceDetails fromInstanceWithContext(final Catalog catalog,
                                                                 final CatEntity catEntity,
                                                                 final UIContext.Action action,
                                                                 final String language) {
        return fromInstanceWithContext(catalog, catEntity, adaptAction(action), language);
    }

    public static Action adaptAction(final UIContext.Action uiContextAction) {
        switch (uiContextAction) {
            case CREATE:
                return Action.CREATE;
            case DELETE:
                return Action.DELETE;
            case EDIT:
                return Action.EDIT;
            case SEARCH:
                return Action.SEARCH;
            case VIEW:
                return Action.VIEW;
            case CLONE:
                return Action.CLONE;
            default:
                return Action.VIEW;
        }
    }

    public Html getDomElementAttributes() {
        final StringBuilder builder = new StringBuilder();
        if (catalogInstanceId > 0) {
            builder.append("instance-id=\"").append(catalogInstanceId).append("\" ");
        }
        if (catalogInstanceName != null && !catalogInstanceName.isEmpty()) {
            builder.append("instance-name=\"").append(catalogInstanceName).append("\" ");
        }
        if (catalogTypeId > 0) {
            builder.append("type-id=\"").append(catalogTypeId).append("\" ");
        }
        if (catalogTypeName != null && !catalogTypeName.isEmpty()) {
            builder.append("type-name=\"").append(catalogTypeName).append("\" ");
        }
        return new Html(builder.toString());
    }

    public Html getDOMPageTag() {
        switch (action) {
            case CREATE:
                return new Html("x-refdata-create-page");
            case CLONE:
                return new Html("x-refdata-clone-page");
            case EDIT:
                return new Html("x-refdata-edit-page");
            case VIEW:
                return new Html("x-refdata-view-page");
            default:
                return new Html("div");
        }
    }

    public Html getDOMFormTag() {
        if (action == Action.VIEW) {
            return new Html("div");
        }
        return new Html("x-refdata-details-form");
    }
}