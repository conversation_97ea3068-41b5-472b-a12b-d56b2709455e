
package na.reference.data.views.html.attributes

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object selectSkeleton extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template5[na.catalog.basemodule.models.nossisinv.attributes.UIContainer,pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,String,java.util.Map[String, String],na.catalog.basemodule.ui.SelectBox,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        uiContainer: na.catalog.basemodule.models.nossisinv.attributes.UIContainer,
        uiElement: pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,
        label: String = "",
        selectParameters: java.util.Map[String, String],
        selectBox: na.catalog.basemodule.ui.SelectBox
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*9.2*/import na.catalog.basemodule.utils.InputUtils

def /*11.2*/action/*11.8*/ = {{
    uiContainer.getUiContext.getAction
}};def /*15.2*/id/*15.4*/ = {{
    selectParameters.get("id")
}};def /*19.2*/divCss/*19.8*/ = {{
    InputUtils.colClassForDiv(uiElement.getUIElementWidth, action)
}};def /*23.2*/labelCss/*23.10*/ = {{
    InputUtils.colClassForLabel(uiElement.getUIElementWidth, action)
}};def /*27.2*/required/*27.10*/ = {{
    InputUtils.requiredLabelClass(uiElement, action)
}};
Seq[Any](format.raw/*7.2*/("""

"""),format.raw/*10.1*/("""
"""),format.raw/*13.2*/("""

"""),format.raw/*17.2*/("""

"""),format.raw/*21.2*/("""

"""),format.raw/*25.2*/("""

"""),format.raw/*29.2*/("""

"""),format.raw/*31.1*/("""<label is="x-label" for=""""),_display_(/*31.27*/id),format.raw/*31.29*/("""" class=""""),_display_(/*31.39*/labelCss),format.raw/*31.47*/(""" """),_display_(/*31.49*/required),format.raw/*31.57*/(""" """),format.raw/*31.58*/("""control-label">"""),_display_(/*31.74*/label),format.raw/*31.79*/("""</label>

<div class=""""),_display_(/*33.14*/divCss),format.raw/*33.20*/("""">"""),_display_(/*33.23*/selectBox/*33.32*/.render()),format.raw/*33.41*/("""</div>"""))
      }
    }
  }

  def render(uiContainer:na.catalog.basemodule.models.nossisinv.attributes.UIContainer,uiElement:pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,label:String,selectParameters:java.util.Map[String, String],selectBox:na.catalog.basemodule.ui.SelectBox): play.twirl.api.HtmlFormat.Appendable = apply(uiContainer,uiElement,label,selectParameters,selectBox)

  def f:((na.catalog.basemodule.models.nossisinv.attributes.UIContainer,pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,String,java.util.Map[String, String],na.catalog.basemodule.ui.SelectBox) => play.twirl.api.HtmlFormat.Appendable) = (uiContainer,uiElement,label,selectParameters,selectBox) => apply(uiContainer,uiElement,label,selectParameters,selectBox)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:50:04 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/attributes/selectSkeleton.scala.html
                  HASH: f813a330c1522376a0e22b6b4b023761529f1a41
                  MATRIX: 1172->1|1550->309|1609->357|1623->363|1683->412|1693->414|1745->455|1759->461|1847->538|1864->546|1954->625|1971->633|2060->306|2089->355|2117->409|2146->452|2175->535|2204->622|2233->693|2262->695|2315->721|2338->723|2375->733|2404->741|2433->743|2462->751|2491->752|2534->768|2560->773|2610->796|2637->802|2667->805|2685->814|2715->823
                  LINES: 28->1|37->9|39->11|39->11|41->15|41->15|43->19|43->19|45->23|45->23|47->27|47->27|50->7|52->10|53->13|55->17|57->21|59->25|61->29|63->31|63->31|63->31|63->31|63->31|63->31|63->31|63->31|63->31|63->31|65->33|65->33|65->33|65->33|65->33
                  -- GENERATED --
              */
          