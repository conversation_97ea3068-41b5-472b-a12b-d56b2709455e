
package na.reference.data.views.html.attributes

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object paragraphSkeleton extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template6[String,java.util.Map[String, String],java.util.Map[String, String],String,pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext.Action,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        label: String = "",
        divAttributes: java.util.Map[String, String],
        inputAttributes: java.util.Map[String, String],
        inputValue: String = "",
        uiElement: pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,
        action: pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext.Action
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*10.2*/import na.catalog.basemodule.utils.InputUtils
/*11.2*/import na.naportalbase.utils.TemplateUtils

def /*13.2*/id/*13.4*/ = {{
    InputUtils.getElementId(uiElement)
}};def /*17.2*/labelClass/*17.12*/ = {{
    InputUtils.colClassForLabel(uiElement.getUIElementWidth, action)
}};def /*21.2*/divClass/*21.10*/ = {{
    InputUtils.colClassForDiv(uiElement.getUIElementWidth, action)
}};def /*25.2*/inputWidth/*25.12*/ = {{
    InputUtils.widthSizeForInput(uiElement.getUIComponentWidth)
}};
Seq[Any](format.raw/*8.2*/("""

"""),format.raw/*12.1*/("""
"""),format.raw/*15.2*/("""

"""),format.raw/*19.2*/("""

"""),format.raw/*23.2*/("""

"""),format.raw/*27.2*/("""

"""),format.raw/*29.1*/("""<label class=""""),_display_(/*29.16*/labelClass),format.raw/*29.26*/(""" """),format.raw/*29.27*/("""control-label">"""),_display_(/*29.43*/label),format.raw/*29.48*/("""</label>

<div class=""""),_display_(/*31.14*/divClass),format.raw/*31.22*/("""" """),_display_(/*31.25*/TemplateUtils/*31.38*/.dynamicElementsAttributes(divAttributes)),format.raw/*31.79*/(""">
    <p id=""""),_display_(/*32.13*/id),format.raw/*32.15*/("""" class="form-control-static """),_display_(/*32.45*/inputWidth),format.raw/*32.55*/("""" """),_display_(/*32.58*/TemplateUtils/*32.71*/.dynamicElementsAttributes(inputAttributes)),format.raw/*32.114*/(""">"""),_display_(/*32.116*/inputValue),format.raw/*32.126*/("""</p>
</div>"""))
      }
    }
  }

  def render(label:String,divAttributes:java.util.Map[String, String],inputAttributes:java.util.Map[String, String],inputValue:String,uiElement:pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,action:pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext.Action): play.twirl.api.HtmlFormat.Appendable = apply(label,divAttributes,inputAttributes,inputValue,uiElement,action)

  def f:((String,java.util.Map[String, String],java.util.Map[String, String],String,pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext.Action) => play.twirl.api.HtmlFormat.Appendable) = (label,divAttributes,inputAttributes,inputValue,uiElement,action) => apply(label,divAttributes,inputAttributes,inputValue,uiElement,action)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:55 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/attributes/paragraphSkeleton.scala.html
                  HASH: d5b4581b4decbde0c3c92005fdf461267ad49761
                  MATRIX: 1181->1|1590->339|1644->386|1700->431|1710->433|1770->482|1789->492|1879->571|1896->579|1984->656|2003->666|2103->336|2132->429|2160->479|2189->568|2218->653|2247->737|2276->739|2318->754|2349->764|2378->765|2421->781|2447->786|2497->809|2526->817|2556->820|2578->833|2640->874|2681->888|2704->890|2761->920|2792->930|2822->933|2844->946|2909->989|2939->991|2971->1001
                  LINES: 28->1|38->10|39->11|41->13|41->13|43->17|43->17|45->21|45->21|47->25|47->25|50->8|52->12|53->15|55->19|57->23|59->27|61->29|61->29|61->29|61->29|61->29|61->29|63->31|63->31|63->31|63->31|63->31|64->32|64->32|64->32|64->32|64->32|64->32|64->32|64->32|64->32
                  -- GENERATED --
              */
          