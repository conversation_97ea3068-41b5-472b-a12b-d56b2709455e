
package na.reference.data.views.html.search

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object leftSideForm extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[String,na.catalog.basemodule.ui.WebComponent,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        entityName: String,
        element: na.catalog.basemodule.ui.WebComponent
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*6.2*/import na.naportalbase.views.tags.i18n
/*7.2*/import scala.collection.mutable
/*8.2*/import java.util.HashMap
/*9.2*/import na.catalog.basemodule.ui.{Label, WebComponent}
/*10.2*/import na.naportalbase.views.html.components.buttonsComp.leftSideFormInputButtonsComp

def /*24.2*/content/*24.9*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*24.13*/("""
    """),_display_(/*25.6*/renderLabel(element)),format.raw/*25.26*/("""
    """),_display_(/*26.6*/container),format.raw/*26.15*/("""
""")))};def /*29.2*/container/*29.11*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*29.15*/("""
"""),format.raw/*30.1*/("""<div class="col-xs-12 field-container__slot">
    """),_display_(/*31.6*/element/*31.13*/.render()),format.raw/*31.22*/("""
"""),format.raw/*32.1*/("""</div>
""")))};def /*53.2*/formButtons/*53.13*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*53.17*/("""
    """),_display_(/*54.6*/leftSideFormInputButtonsComp/*54.34*/.render(
        searchButtonAttributes,
        clearButtonAttributes
    )),format.raw/*57.6*/("""
""")))};def /*60.2*/renderedButtons/*60.17*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*60.21*/("""
    """),format.raw/*61.5*/("""<div class="form-group search-sidebar__button-list">
        <div class="fx-splitter-sidebar-buttons">
            <div class="fx-splitter-sidebar-buttons-inner">
                """),_display_(/*64.18*/formButtons),format.raw/*64.29*/("""
            """),format.raw/*65.13*/("""</div>
        </div>
    </div>
""")))};def /*70.2*/renderedSaveFilterButton/*70.26*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*70.30*/("""
    """),format.raw/*71.5*/("""<br/>
""")))};def /*12.2*/renderLabel/*12.13*/(element: WebComponent) = {{
    if(element.getI18n != null) {

    val dataAttributes = new HashMap[String, String]()
    dataAttributes.put("class", "col-xs-12 control-label")

    val label = new Label(element.getId, element.getI18n)
    label.setDataAttributes(dataAttributes)
    label.render()
    }
}};def /*35.2*/searchButtonAttributes/*35.24*/ = {{
    val map = mutable.HashMap[String, String]()
    map.put("data-form-action", "submit")
    map.put("id", "submit_form")
    map.put("type", "submit")
    map.put("value", i18n("na.buttons.search"))
    map
}};def /*44.2*/clearButtonAttributes/*44.23*/ = {{
    val map = mutable.HashMap[String, String]()
    map.put("data-form-action", "clear")
    map.put("id", "clean_form")
    map.put("type", "reset")
    map.put("value", i18n("na.buttons.clear"))
    map
}};
Seq[Any](format.raw/*4.2*/("""

"""),format.raw/*11.1*/("""
"""),format.raw/*22.2*/("""

"""),format.raw/*27.2*/("""

"""),format.raw/*33.2*/("""

"""),format.raw/*42.2*/("""

"""),format.raw/*51.2*/("""

"""),format.raw/*58.2*/("""

"""),format.raw/*68.2*/("""

"""),format.raw/*72.2*/("""

"""),format.raw/*74.1*/("""<header class="fx-sidebar-header search-sidebar__header">
    <div class="fx-sidebar-header-inner">
        <p>
            <span class="fx-entity-header-icon">
                <i class="fa fa-search"></i>
            </span>
            <x-i18n key="na.basemodule.lateralSearch.title"></x-i18n>
        </p>
    </div>
</header>
<div class="fx-sidebar-content-wrapper search-sidebar__field-list">
    <form id="catalog-search-form" class="form form--sidebar">
        <div class="row form__header">
            <div class="fx-sidebar-nav-title">
                <h4>"""),_display_(/*88.22*/entityName),format.raw/*88.32*/("""</h4>
            </div>
        </div>
        """),_display_(/*91.10*/if(element != null)/*91.29*/ {_display_(Seq[Any](format.raw/*91.31*/("""
        """),format.raw/*92.9*/("""<div class="fx-sidebar-content-wrapper search-sidebar__field-list">
            <x-shadow-scroll inline>
                <div class="row form__fields-row" data-form-fields-container>
                    <!-- base search element -->
                    <div class="col-xs-12 field-container field-container--sidebar-form">
                        <div class="form-group">
                            """),_display_(/*98.30*/content),format.raw/*98.37*/("""
                        """),format.raw/*99.25*/("""</div>
                    </div>
                </div>
            </x-shadow-scroll>
        </div>
        """)))}),format.raw/*104.10*/("""
    """),format.raw/*105.5*/("""</form>
</div>
<div class="form-group search-sidebar__button-list">
    <div class="fx-splitter-sidebar-buttons">
        <div class="fx-splitter-sidebar-buttons-inner">
            """),_display_(/*110.14*/renderedButtons),format.raw/*110.29*/("""
        """),format.raw/*111.9*/("""</div>
    </div>
</div>"""))
      }
    }
  }

  def render(entityName:String,element:na.catalog.basemodule.ui.WebComponent): play.twirl.api.HtmlFormat.Appendable = apply(entityName,element)

  def f:((String,na.catalog.basemodule.ui.WebComponent) => play.twirl.api.HtmlFormat.Appendable) = (entityName,element) => apply(entityName,element)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:50:04 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/search/leftSideForm.scala.html
                  HASH: 073aa695f6092e2b1154d2a615cae5c61e0e6724
                  MATRIX: 1018->1|1177->90|1223->130|1262->163|1294->189|1356->244|1455->653|1470->660|1551->664|1583->670|1624->690|1656->696|1686->705|1711->710|1729->719|1810->723|1838->724|1915->775|1931->782|1961->791|1989->792|2020->1280|2040->1291|2121->1295|2153->1301|2190->1329|2286->1405|2311->1410|2335->1425|2416->1429|2448->1434|2655->1614|2687->1625|2728->1638|2785->1675|2818->1699|2899->1703|2931->1708|2961->332|2981->343|3302->803|3333->825|3563->1044|3593->1065|3834->87|3863->330|3891->650|3920->707|3949->800|3978->1041|4007->1277|4036->1407|4065->1672|4094->1715|4123->1717|4718->2285|4749->2295|4825->2344|4853->2363|4893->2365|4929->2374|5356->2774|5384->2781|5437->2806|5581->2918|5614->2923|5825->3106|5862->3121|5899->3130
                  LINES: 28->1|34->6|35->7|36->8|37->9|38->10|40->24|40->24|42->24|43->25|43->25|44->26|44->26|45->29|45->29|47->29|48->30|49->31|49->31|49->31|50->32|51->53|51->53|53->53|54->54|54->54|57->57|58->60|58->60|60->60|61->61|64->64|64->64|65->65|68->70|68->70|70->70|71->71|72->12|72->12|82->35|82->35|89->44|89->44|97->4|99->11|100->22|102->27|104->33|106->42|108->51|110->58|112->68|114->72|116->74|130->88|130->88|133->91|133->91|133->91|134->92|140->98|140->98|141->99|146->104|147->105|152->110|152->110|153->111
                  -- GENERATED --
              */
          