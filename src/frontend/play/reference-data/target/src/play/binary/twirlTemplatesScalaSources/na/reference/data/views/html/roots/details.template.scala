
package na.reference.data.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object details extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template5[na.catalog.basemodule.models.nossisinv.attributes.UIContainer,na.catalog.basemodule.ui.WebComponent,na.reference.data.utils.views.CatalogInstanceDetails,na.reference.data.utils.views.skeletons.HeaderSkeletonHelper,na.reference.data.services.UIAttributesService,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        uiContainer: na.catalog.basemodule.models.nossisinv.attributes.UIContainer,
        element: na.catalog.basemodule.ui.WebComponent,
        catalogInstanceDetails: na.reference.data.utils.views.CatalogInstanceDetails,
        headerSkeletonHelper: na.reference.data.utils.views.skeletons.HeaderSkeletonHelper,
        uiAttributesService: na.reference.data.services.UIAttributesService
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*9.2*/import na.reference.data.views.html.skeletons.mainSkeleton


Seq[Any](format.raw/*7.2*/("""

"""),format.raw/*10.1*/("""
"""),_display_(/*11.2*/mainSkeleton()/*11.16*/ {_display_(Seq[Any](format.raw/*11.18*/("""
    """),format.raw/*12.5*/("""<div class="fx-push-footer fx-full-height">
    """),_display_(/*13.6*/na/*13.8*/.reference.data.views.html.skeletons.detailsSkeleton.render(
        uiContainer = uiContainer,
        element = element,
        catalogInstanceDetails = catalogInstanceDetails,
        headerSkeletonHelper = headerSkeletonHelper,
        uiAttributesService = uiAttributesService
    )),format.raw/*19.6*/("""
    """),format.raw/*20.5*/("""</div>
""")))}))
      }
    }
  }

  def render(uiContainer:na.catalog.basemodule.models.nossisinv.attributes.UIContainer,element:na.catalog.basemodule.ui.WebComponent,catalogInstanceDetails:na.reference.data.utils.views.CatalogInstanceDetails,headerSkeletonHelper:na.reference.data.utils.views.skeletons.HeaderSkeletonHelper,uiAttributesService:na.reference.data.services.UIAttributesService): play.twirl.api.HtmlFormat.Appendable = apply(uiContainer,element,catalogInstanceDetails,headerSkeletonHelper,uiAttributesService)

  def f:((na.catalog.basemodule.models.nossisinv.attributes.UIContainer,na.catalog.basemodule.ui.WebComponent,na.reference.data.utils.views.CatalogInstanceDetails,na.reference.data.utils.views.skeletons.HeaderSkeletonHelper,na.reference.data.services.UIAttributesService) => play.twirl.api.HtmlFormat.Appendable) = (uiContainer,element,catalogInstanceDetails,headerSkeletonHelper,uiAttributesService) => apply(uiContainer,element,catalogInstanceDetails,headerSkeletonHelper,uiAttributesService)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:55 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/roots/details.scala.html
                  HASH: b423f32f8296b73f25ab92f890752ec22b429a0a
                  MATRIX: 1228->1|1698->401|1785->398|1814->460|1842->462|1865->476|1905->478|1937->483|2012->532|2022->534|2330->822|2362->827
                  LINES: 28->1|37->9|40->7|42->10|43->11|43->11|43->11|44->12|45->13|45->13|51->19|52->20
                  -- GENERATED --
              */
          