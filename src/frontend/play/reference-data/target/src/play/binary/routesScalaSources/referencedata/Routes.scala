// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/conf/referencedata.routes
// @DATE:Tue Jul 01 09:50:04 WEST 2025

package referencedata

import play.core.routing._
import play.core.routing.HandlerInvokerFactory._

import play.api.mvc._

import _root_.controllers.Assets.Asset

class Routes(
  override val errorHandler: play.api.http.HttpErrorHandler, 
  // @LINE:6
  Application_1: na.reference.data.controllers.Application,
  // @LINE:9
  Assets_0: na.reference.data.controllers.Assets,
  // @LINE:16
  SearchController_2: na.reference.data.controllers.SearchController,
  // @LINE:24
  CatalogController_3: na.reference.data.controllers.CatalogController,
  // @LINE:41
  naportalbase_Routes_0: naportalbase.Routes,
  val prefix: String
) extends GeneratedRouter {

   @javax.inject.Inject()
   def this(errorHandler: play.api.http.HttpErrorHandler,
    // @LINE:6
    Application_1: na.reference.data.controllers.Application,
    // @LINE:9
    Assets_0: na.reference.data.controllers.Assets,
    // @LINE:16
    SearchController_2: na.reference.data.controllers.SearchController,
    // @LINE:24
    CatalogController_3: na.reference.data.controllers.CatalogController,
    // @LINE:41
    naportalbase_Routes_0: naportalbase.Routes
  ) = this(errorHandler, Application_1, Assets_0, SearchController_2, CatalogController_3, naportalbase_Routes_0, "/")

  def withPrefix(prefix: String): Routes = {
    referencedata.RoutesPrefix.setPrefix(prefix)
    new Routes(errorHandler, Application_1, Assets_0, SearchController_2, CatalogController_3, naportalbase_Routes_0, prefix)
  }

  private[this] val defaultPrefix: String = {
    if (this.prefix.endsWith("/")) "" else "/"
  }

  def documentation = List(
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """referencedata/assets/javascripts/routes""", """na.reference.data.controllers.Application.javascriptRoutes()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """referencedata/assets/""" + "$" + """file<.+>""", """na.reference.data.controllers.Assets.versioned(path:String = "/public", file:Asset)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """referencedata/home""", """na.reference.data.controllers.Application.goToHome()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """referencedata/resume""", """na.reference.data.controllers.Application.catalogResume()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """referencedata/""" + "$" + """id<[^/]+>/dynamic-elements/""" + "$" + """location<[^/]+>""", """na.reference.data.controllers.SearchController.getDynamicElements(id:Long, location:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """referencedata/authorized-catalogs""", """na.reference.data.controllers.SearchController.getAuthorizedCatalogs"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """referencedata/dashboard""", """na.reference.data.controllers.SearchController.getDashboardPage"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """referencedata/search/inner""", """na.reference.data.controllers.SearchController.getSearchForm(id:Long, context:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """referencedata/search""", """na.reference.data.controllers.SearchController.getSearchPage"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """referencedata/search""", """na.reference.data.controllers.SearchController.getSearchResults"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """referencedata/create""", """na.reference.data.controllers.CatalogController.createCatalogInstance"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """referencedata/create""", """na.reference.data.controllers.CatalogController.getCatalogCreatePage"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """referencedata/""" + "$" + """id<[^/]+>/entity-tab""", """na.reference.data.controllers.CatalogController.generateEntityTab(id:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """referencedata/""" + "$" + """id<[^/]+>""", """na.reference.data.controllers.CatalogController.getCatalogViewPage(id:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """referencedata/""" + "$" + """id<[^/]+>/inner""", """na.reference.data.controllers.CatalogController.getCatalogInnerPage(id:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """referencedata/""" + "$" + """id<[^/]+>/edit""", """na.reference.data.controllers.CatalogController.getCatalogEditPage(id:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """referencedata/""" + "$" + """id<[^/]+>/edit-inner""", """na.reference.data.controllers.CatalogController.getCatalogEditInnerForm(id:Long)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """referencedata/""" + "$" + """id<[^/]+>/create-inner""", """na.reference.data.controllers.CatalogController.getCatalogCreateInnerForm(id:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """referencedata/""" + "$" + """id<[^/]+>/clone""", """na.reference.data.controllers.CatalogController.getCatalogCloneForm(id:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """referencedata/""" + "$" + """id<[^/]+>/clone-inner""", """na.reference.data.controllers.CatalogController.getCatalogCloneInnerForm(id:Long)"""),
    ("""PUT""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """referencedata/""" + "$" + """id<[^/]+>""", """na.reference.data.controllers.CatalogController.saveCatalog(id:Long)"""),
    ("""DELETE""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """referencedata/delete""", """na.reference.data.controllers.CatalogController.deleteCatalogInstances(ids:String ?= "")"""),
    prefixed_naportalbase_Routes_0_22.router.documentation,
    Nil
  ).foldLeft(List.empty[(String,String,String)]) { (s,e) => e.asInstanceOf[Any] match {
    case r @ (_,_,_) => s :+ r.asInstanceOf[(String,String,String)]
    case l => s ++ l.asInstanceOf[List[(String,String,String)]]
  }}


  // @LINE:6
  private[this] lazy val na_reference_data_controllers_Application_javascriptRoutes0_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("referencedata/assets/javascripts/routes")))
  )
  private[this] lazy val na_reference_data_controllers_Application_javascriptRoutes0_invoker = createInvoker(
    Application_1.javascriptRoutes(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "referencedata",
      "na.reference.data.controllers.Application",
      "javascriptRoutes",
      Nil,
      "GET",
      this.prefix + """referencedata/assets/javascripts/routes""",
      """ Javascript routing""",
      Seq()
    )
  )

  // @LINE:9
  private[this] lazy val na_reference_data_controllers_Assets_versioned1_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("referencedata/assets/"), DynamicPart("file", """.+""",false)))
  )
  private[this] lazy val na_reference_data_controllers_Assets_versioned1_invoker = createInvoker(
    Assets_0.versioned(fakeValue[String], fakeValue[Asset]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "referencedata",
      "na.reference.data.controllers.Assets",
      "versioned",
      Seq(classOf[String], classOf[Asset]),
      "GET",
      this.prefix + """referencedata/assets/""" + "$" + """file<.+>""",
      """ Map static resources from the /public folder to the /assets URL path""",
      Seq()
    )
  )

  // @LINE:12
  private[this] lazy val na_reference_data_controllers_Application_goToHome2_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("referencedata/home")))
  )
  private[this] lazy val na_reference_data_controllers_Application_goToHome2_invoker = createInvoker(
    Application_1.goToHome(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "referencedata",
      "na.reference.data.controllers.Application",
      "goToHome",
      Nil,
      "GET",
      this.prefix + """referencedata/home""",
      """ Defaults""",
      Seq()
    )
  )

  // @LINE:13
  private[this] lazy val na_reference_data_controllers_Application_catalogResume3_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("referencedata/resume")))
  )
  private[this] lazy val na_reference_data_controllers_Application_catalogResume3_invoker = createInvoker(
    Application_1.catalogResume(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "referencedata",
      "na.reference.data.controllers.Application",
      "catalogResume",
      Nil,
      "GET",
      this.prefix + """referencedata/resume""",
      """""",
      Seq()
    )
  )

  // @LINE:16
  private[this] lazy val na_reference_data_controllers_SearchController_getDynamicElements4_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("referencedata/"), DynamicPart("id", """[^/]+""",true), StaticPart("/dynamic-elements/"), DynamicPart("location", """[^/]+""",true)))
  )
  private[this] lazy val na_reference_data_controllers_SearchController_getDynamicElements4_invoker = createInvoker(
    SearchController_2.getDynamicElements(fakeValue[Long], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "referencedata",
      "na.reference.data.controllers.SearchController",
      "getDynamicElements",
      Seq(classOf[Long], classOf[String]),
      "GET",
      this.prefix + """referencedata/""" + "$" + """id<[^/]+>/dynamic-elements/""" + "$" + """location<[^/]+>""",
      """ Search""",
      Seq()
    )
  )

  // @LINE:17
  private[this] lazy val na_reference_data_controllers_SearchController_getAuthorizedCatalogs5_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("referencedata/authorized-catalogs")))
  )
  private[this] lazy val na_reference_data_controllers_SearchController_getAuthorizedCatalogs5_invoker = createInvoker(
    SearchController_2.getAuthorizedCatalogs,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "referencedata",
      "na.reference.data.controllers.SearchController",
      "getAuthorizedCatalogs",
      Nil,
      "GET",
      this.prefix + """referencedata/authorized-catalogs""",
      """""",
      Seq()
    )
  )

  // @LINE:18
  private[this] lazy val na_reference_data_controllers_SearchController_getDashboardPage6_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("referencedata/dashboard")))
  )
  private[this] lazy val na_reference_data_controllers_SearchController_getDashboardPage6_invoker = createInvoker(
    SearchController_2.getDashboardPage,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "referencedata",
      "na.reference.data.controllers.SearchController",
      "getDashboardPage",
      Nil,
      "GET",
      this.prefix + """referencedata/dashboard""",
      """""",
      Seq()
    )
  )

  // @LINE:19
  private[this] lazy val na_reference_data_controllers_SearchController_getSearchForm7_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("referencedata/search/inner")))
  )
  private[this] lazy val na_reference_data_controllers_SearchController_getSearchForm7_invoker = createInvoker(
    SearchController_2.getSearchForm(fakeValue[Long], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "referencedata",
      "na.reference.data.controllers.SearchController",
      "getSearchForm",
      Seq(classOf[Long], classOf[String]),
      "GET",
      this.prefix + """referencedata/search/inner""",
      """""",
      Seq()
    )
  )

  // @LINE:20
  private[this] lazy val na_reference_data_controllers_SearchController_getSearchPage8_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("referencedata/search")))
  )
  private[this] lazy val na_reference_data_controllers_SearchController_getSearchPage8_invoker = createInvoker(
    SearchController_2.getSearchPage,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "referencedata",
      "na.reference.data.controllers.SearchController",
      "getSearchPage",
      Nil,
      "GET",
      this.prefix + """referencedata/search""",
      """""",
      Seq()
    )
  )

  // @LINE:21
  private[this] lazy val na_reference_data_controllers_SearchController_getSearchResults9_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("referencedata/search")))
  )
  private[this] lazy val na_reference_data_controllers_SearchController_getSearchResults9_invoker = createInvoker(
    SearchController_2.getSearchResults,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "referencedata",
      "na.reference.data.controllers.SearchController",
      "getSearchResults",
      Nil,
      "POST",
      this.prefix + """referencedata/search""",
      """""",
      Seq()
    )
  )

  // @LINE:24
  private[this] lazy val na_reference_data_controllers_CatalogController_createCatalogInstance10_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("referencedata/create")))
  )
  private[this] lazy val na_reference_data_controllers_CatalogController_createCatalogInstance10_invoker = createInvoker(
    CatalogController_3.createCatalogInstance,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "referencedata",
      "na.reference.data.controllers.CatalogController",
      "createCatalogInstance",
      Nil,
      "POST",
      this.prefix + """referencedata/create""",
      """ Entity details""",
      Seq()
    )
  )

  // @LINE:25
  private[this] lazy val na_reference_data_controllers_CatalogController_getCatalogCreatePage11_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("referencedata/create")))
  )
  private[this] lazy val na_reference_data_controllers_CatalogController_getCatalogCreatePage11_invoker = createInvoker(
    CatalogController_3.getCatalogCreatePage,
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "referencedata",
      "na.reference.data.controllers.CatalogController",
      "getCatalogCreatePage",
      Nil,
      "GET",
      this.prefix + """referencedata/create""",
      """""",
      Seq()
    )
  )

  // @LINE:26
  private[this] lazy val na_reference_data_controllers_CatalogController_generateEntityTab12_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("referencedata/"), DynamicPart("id", """[^/]+""",true), StaticPart("/entity-tab")))
  )
  private[this] lazy val na_reference_data_controllers_CatalogController_generateEntityTab12_invoker = createInvoker(
    CatalogController_3.generateEntityTab(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "referencedata",
      "na.reference.data.controllers.CatalogController",
      "generateEntityTab",
      Seq(classOf[Long]),
      "POST",
      this.prefix + """referencedata/""" + "$" + """id<[^/]+>/entity-tab""",
      """""",
      Seq()
    )
  )

  // @LINE:27
  private[this] lazy val na_reference_data_controllers_CatalogController_getCatalogViewPage13_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("referencedata/"), DynamicPart("id", """[^/]+""",true)))
  )
  private[this] lazy val na_reference_data_controllers_CatalogController_getCatalogViewPage13_invoker = createInvoker(
    CatalogController_3.getCatalogViewPage(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "referencedata",
      "na.reference.data.controllers.CatalogController",
      "getCatalogViewPage",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """referencedata/""" + "$" + """id<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:28
  private[this] lazy val na_reference_data_controllers_CatalogController_getCatalogInnerPage14_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("referencedata/"), DynamicPart("id", """[^/]+""",true), StaticPart("/inner")))
  )
  private[this] lazy val na_reference_data_controllers_CatalogController_getCatalogInnerPage14_invoker = createInvoker(
    CatalogController_3.getCatalogInnerPage(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "referencedata",
      "na.reference.data.controllers.CatalogController",
      "getCatalogInnerPage",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """referencedata/""" + "$" + """id<[^/]+>/inner""",
      """""",
      Seq()
    )
  )

  // @LINE:29
  private[this] lazy val na_reference_data_controllers_CatalogController_getCatalogEditPage15_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("referencedata/"), DynamicPart("id", """[^/]+""",true), StaticPart("/edit")))
  )
  private[this] lazy val na_reference_data_controllers_CatalogController_getCatalogEditPage15_invoker = createInvoker(
    CatalogController_3.getCatalogEditPage(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "referencedata",
      "na.reference.data.controllers.CatalogController",
      "getCatalogEditPage",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """referencedata/""" + "$" + """id<[^/]+>/edit""",
      """""",
      Seq()
    )
  )

  // @LINE:30
  private[this] lazy val na_reference_data_controllers_CatalogController_getCatalogEditInnerForm16_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("referencedata/"), DynamicPart("id", """[^/]+""",true), StaticPart("/edit-inner")))
  )
  private[this] lazy val na_reference_data_controllers_CatalogController_getCatalogEditInnerForm16_invoker = createInvoker(
    CatalogController_3.getCatalogEditInnerForm(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "referencedata",
      "na.reference.data.controllers.CatalogController",
      "getCatalogEditInnerForm",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """referencedata/""" + "$" + """id<[^/]+>/edit-inner""",
      """""",
      Seq()
    )
  )

  // @LINE:31
  private[this] lazy val na_reference_data_controllers_CatalogController_getCatalogCreateInnerForm17_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("referencedata/"), DynamicPart("id", """[^/]+""",true), StaticPart("/create-inner")))
  )
  private[this] lazy val na_reference_data_controllers_CatalogController_getCatalogCreateInnerForm17_invoker = createInvoker(
    CatalogController_3.getCatalogCreateInnerForm(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "referencedata",
      "na.reference.data.controllers.CatalogController",
      "getCatalogCreateInnerForm",
      Seq(classOf[Long]),
      "POST",
      this.prefix + """referencedata/""" + "$" + """id<[^/]+>/create-inner""",
      """""",
      Seq()
    )
  )

  // @LINE:32
  private[this] lazy val na_reference_data_controllers_CatalogController_getCatalogCloneForm18_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("referencedata/"), DynamicPart("id", """[^/]+""",true), StaticPart("/clone")))
  )
  private[this] lazy val na_reference_data_controllers_CatalogController_getCatalogCloneForm18_invoker = createInvoker(
    CatalogController_3.getCatalogCloneForm(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "referencedata",
      "na.reference.data.controllers.CatalogController",
      "getCatalogCloneForm",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """referencedata/""" + "$" + """id<[^/]+>/clone""",
      """""",
      Seq()
    )
  )

  // @LINE:33
  private[this] lazy val na_reference_data_controllers_CatalogController_getCatalogCloneInnerForm19_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("referencedata/"), DynamicPart("id", """[^/]+""",true), StaticPart("/clone-inner")))
  )
  private[this] lazy val na_reference_data_controllers_CatalogController_getCatalogCloneInnerForm19_invoker = createInvoker(
    CatalogController_3.getCatalogCloneInnerForm(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "referencedata",
      "na.reference.data.controllers.CatalogController",
      "getCatalogCloneInnerForm",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """referencedata/""" + "$" + """id<[^/]+>/clone-inner""",
      """""",
      Seq()
    )
  )

  // @LINE:34
  private[this] lazy val na_reference_data_controllers_CatalogController_saveCatalog20_route = Route("PUT",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("referencedata/"), DynamicPart("id", """[^/]+""",true)))
  )
  private[this] lazy val na_reference_data_controllers_CatalogController_saveCatalog20_invoker = createInvoker(
    CatalogController_3.saveCatalog(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "referencedata",
      "na.reference.data.controllers.CatalogController",
      "saveCatalog",
      Seq(classOf[Long]),
      "PUT",
      this.prefix + """referencedata/""" + "$" + """id<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:35
  private[this] lazy val na_reference_data_controllers_CatalogController_deleteCatalogInstances21_route = Route("DELETE",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("referencedata/delete")))
  )
  private[this] lazy val na_reference_data_controllers_CatalogController_deleteCatalogInstances21_invoker = createInvoker(
    CatalogController_3.deleteCatalogInstances(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "referencedata",
      "na.reference.data.controllers.CatalogController",
      "deleteCatalogInstances",
      Seq(classOf[String]),
      "DELETE",
      this.prefix + """referencedata/delete""",
      """""",
      Seq()
    )
  )

  // @LINE:41
  private[this] val prefixed_naportalbase_Routes_0_22 = Include(naportalbase_Routes_0.withPrefix(this.prefix + (if (this.prefix.endsWith("/")) "" else "/") + "referencedata"))


  def routes: PartialFunction[RequestHeader, Handler] = {
  
    // @LINE:6
    case na_reference_data_controllers_Application_javascriptRoutes0_route(params@_) =>
      call { 
        na_reference_data_controllers_Application_javascriptRoutes0_invoker.call(Application_1.javascriptRoutes())
      }
  
    // @LINE:9
    case na_reference_data_controllers_Assets_versioned1_route(params@_) =>
      call(Param[String]("path", Right("/public")), params.fromPath[Asset]("file", None)) { (path, file) =>
        na_reference_data_controllers_Assets_versioned1_invoker.call(Assets_0.versioned(path, file))
      }
  
    // @LINE:12
    case na_reference_data_controllers_Application_goToHome2_route(params@_) =>
      call { 
        na_reference_data_controllers_Application_goToHome2_invoker.call(Application_1.goToHome())
      }
  
    // @LINE:13
    case na_reference_data_controllers_Application_catalogResume3_route(params@_) =>
      call { 
        na_reference_data_controllers_Application_catalogResume3_invoker.call(Application_1.catalogResume())
      }
  
    // @LINE:16
    case na_reference_data_controllers_SearchController_getDynamicElements4_route(params@_) =>
      call(params.fromPath[Long]("id", None), params.fromPath[String]("location", None)) { (id, location) =>
        na_reference_data_controllers_SearchController_getDynamicElements4_invoker.call(SearchController_2.getDynamicElements(id, location))
      }
  
    // @LINE:17
    case na_reference_data_controllers_SearchController_getAuthorizedCatalogs5_route(params@_) =>
      call { 
        na_reference_data_controllers_SearchController_getAuthorizedCatalogs5_invoker.call(SearchController_2.getAuthorizedCatalogs)
      }
  
    // @LINE:18
    case na_reference_data_controllers_SearchController_getDashboardPage6_route(params@_) =>
      call { 
        na_reference_data_controllers_SearchController_getDashboardPage6_invoker.call(SearchController_2.getDashboardPage)
      }
  
    // @LINE:19
    case na_reference_data_controllers_SearchController_getSearchForm7_route(params@_) =>
      call(params.fromQuery[Long]("id", None), params.fromQuery[String]("context", None)) { (id, context) =>
        na_reference_data_controllers_SearchController_getSearchForm7_invoker.call(SearchController_2.getSearchForm(id, context))
      }
  
    // @LINE:20
    case na_reference_data_controllers_SearchController_getSearchPage8_route(params@_) =>
      call { 
        na_reference_data_controllers_SearchController_getSearchPage8_invoker.call(SearchController_2.getSearchPage)
      }
  
    // @LINE:21
    case na_reference_data_controllers_SearchController_getSearchResults9_route(params@_) =>
      call { 
        na_reference_data_controllers_SearchController_getSearchResults9_invoker.call(SearchController_2.getSearchResults)
      }
  
    // @LINE:24
    case na_reference_data_controllers_CatalogController_createCatalogInstance10_route(params@_) =>
      call { 
        na_reference_data_controllers_CatalogController_createCatalogInstance10_invoker.call(CatalogController_3.createCatalogInstance)
      }
  
    // @LINE:25
    case na_reference_data_controllers_CatalogController_getCatalogCreatePage11_route(params@_) =>
      call { 
        na_reference_data_controllers_CatalogController_getCatalogCreatePage11_invoker.call(CatalogController_3.getCatalogCreatePage)
      }
  
    // @LINE:26
    case na_reference_data_controllers_CatalogController_generateEntityTab12_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_reference_data_controllers_CatalogController_generateEntityTab12_invoker.call(CatalogController_3.generateEntityTab(id))
      }
  
    // @LINE:27
    case na_reference_data_controllers_CatalogController_getCatalogViewPage13_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_reference_data_controllers_CatalogController_getCatalogViewPage13_invoker.call(CatalogController_3.getCatalogViewPage(id))
      }
  
    // @LINE:28
    case na_reference_data_controllers_CatalogController_getCatalogInnerPage14_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_reference_data_controllers_CatalogController_getCatalogInnerPage14_invoker.call(CatalogController_3.getCatalogInnerPage(id))
      }
  
    // @LINE:29
    case na_reference_data_controllers_CatalogController_getCatalogEditPage15_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_reference_data_controllers_CatalogController_getCatalogEditPage15_invoker.call(CatalogController_3.getCatalogEditPage(id))
      }
  
    // @LINE:30
    case na_reference_data_controllers_CatalogController_getCatalogEditInnerForm16_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_reference_data_controllers_CatalogController_getCatalogEditInnerForm16_invoker.call(CatalogController_3.getCatalogEditInnerForm(id))
      }
  
    // @LINE:31
    case na_reference_data_controllers_CatalogController_getCatalogCreateInnerForm17_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_reference_data_controllers_CatalogController_getCatalogCreateInnerForm17_invoker.call(CatalogController_3.getCatalogCreateInnerForm(id))
      }
  
    // @LINE:32
    case na_reference_data_controllers_CatalogController_getCatalogCloneForm18_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_reference_data_controllers_CatalogController_getCatalogCloneForm18_invoker.call(CatalogController_3.getCatalogCloneForm(id))
      }
  
    // @LINE:33
    case na_reference_data_controllers_CatalogController_getCatalogCloneInnerForm19_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_reference_data_controllers_CatalogController_getCatalogCloneInnerForm19_invoker.call(CatalogController_3.getCatalogCloneInnerForm(id))
      }
  
    // @LINE:34
    case na_reference_data_controllers_CatalogController_saveCatalog20_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_reference_data_controllers_CatalogController_saveCatalog20_invoker.call(CatalogController_3.saveCatalog(id))
      }
  
    // @LINE:35
    case na_reference_data_controllers_CatalogController_deleteCatalogInstances21_route(params@_) =>
      call(params.fromQuery[String]("ids", Some(""))) { (ids) =>
        na_reference_data_controllers_CatalogController_deleteCatalogInstances21_invoker.call(CatalogController_3.deleteCatalogInstances(ids))
      }
  
    // @LINE:41
    case prefixed_naportalbase_Routes_0_22(handler) => handler
  }
}
