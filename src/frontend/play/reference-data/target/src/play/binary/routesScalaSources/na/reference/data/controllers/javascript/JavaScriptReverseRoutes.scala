// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/conf/referencedata.routes
// @DATE:Tue Jul 01 11:21:55 WEST 2025

import play.api.routing.JavaScriptReverseRoute


import _root_.controllers.Assets.Asset

// @LINE:6
package na.reference.data.controllers.javascript {

  // @LINE:24
  class ReverseCatalogController(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:34
    def saveCatalog: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.reference.data.controllers.CatalogController.saveCatalog",
      """
        function(id0) {
          return _wA({method:"PUT", url:"""" + _prefix + { _defaultPrefix } + """" + "referencedata/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0))})
        }
      """
    )
  
    // @LINE:29
    def getCatalogEditPage: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.reference.data.controllers.CatalogController.getCatalogEditPage",
      """
        function(id0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "referencedata/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0)) + "/edit"})
        }
      """
    )
  
    // @LINE:35
    def deleteCatalogInstances: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.reference.data.controllers.CatalogController.deleteCatalogInstances",
      """
        function(ids0) {
          return _wA({method:"DELETE", url:"""" + _prefix + { _defaultPrefix } + """" + "referencedata/delete" + _qS([(ids0 == null ? null : (""" + implicitly[play.api.mvc.QueryStringBindable[String]].javascriptUnbind + """)("ids", ids0))])})
        }
      """
    )
  
    // @LINE:31
    def getCatalogCreateInnerForm: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.reference.data.controllers.CatalogController.getCatalogCreateInnerForm",
      """
        function(id0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "referencedata/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0)) + "/create-inner"})
        }
      """
    )
  
    // @LINE:24
    def createCatalogInstance: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.reference.data.controllers.CatalogController.createCatalogInstance",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "referencedata/create"})
        }
      """
    )
  
    // @LINE:26
    def generateEntityTab: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.reference.data.controllers.CatalogController.generateEntityTab",
      """
        function(id0) {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "referencedata/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0)) + "/entity-tab"})
        }
      """
    )
  
    // @LINE:30
    def getCatalogEditInnerForm: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.reference.data.controllers.CatalogController.getCatalogEditInnerForm",
      """
        function(id0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "referencedata/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0)) + "/edit-inner"})
        }
      """
    )
  
    // @LINE:32
    def getCatalogCloneForm: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.reference.data.controllers.CatalogController.getCatalogCloneForm",
      """
        function(id0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "referencedata/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0)) + "/clone"})
        }
      """
    )
  
    // @LINE:33
    def getCatalogCloneInnerForm: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.reference.data.controllers.CatalogController.getCatalogCloneInnerForm",
      """
        function(id0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "referencedata/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0)) + "/clone-inner"})
        }
      """
    )
  
    // @LINE:28
    def getCatalogInnerPage: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.reference.data.controllers.CatalogController.getCatalogInnerPage",
      """
        function(id0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "referencedata/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0)) + "/inner"})
        }
      """
    )
  
    // @LINE:25
    def getCatalogCreatePage: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.reference.data.controllers.CatalogController.getCatalogCreatePage",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "referencedata/create"})
        }
      """
    )
  
    // @LINE:27
    def getCatalogViewPage: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.reference.data.controllers.CatalogController.getCatalogViewPage",
      """
        function(id0) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "referencedata/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0))})
        }
      """
    )
  
  }

  // @LINE:9
  class ReverseAssets(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:9
    def versioned: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.reference.data.controllers.Assets.versioned",
      """
        function(file1) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "referencedata/assets/" + (""" + implicitly[play.api.mvc.PathBindable[Asset]].javascriptUnbind + """)("file", file1)})
        }
      """
    )
  
  }

  // @LINE:16
  class ReverseSearchController(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:16
    def getDynamicElements: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.reference.data.controllers.SearchController.getDynamicElements",
      """
        function(id0,location1) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "referencedata/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[Long]].javascriptUnbind + """)("id", id0)) + "/dynamic-elements/" + encodeURIComponent((""" + implicitly[play.api.mvc.PathBindable[String]].javascriptUnbind + """)("location", location1))})
        }
      """
    )
  
    // @LINE:18
    def getDashboardPage: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.reference.data.controllers.SearchController.getDashboardPage",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "referencedata/dashboard"})
        }
      """
    )
  
    // @LINE:17
    def getAuthorizedCatalogs: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.reference.data.controllers.SearchController.getAuthorizedCatalogs",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "referencedata/authorized-catalogs"})
        }
      """
    )
  
    // @LINE:21
    def getSearchResults: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.reference.data.controllers.SearchController.getSearchResults",
      """
        function() {
          return _wA({method:"POST", url:"""" + _prefix + { _defaultPrefix } + """" + "referencedata/search"})
        }
      """
    )
  
    // @LINE:20
    def getSearchPage: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.reference.data.controllers.SearchController.getSearchPage",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "referencedata/search"})
        }
      """
    )
  
    // @LINE:19
    def getSearchForm: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.reference.data.controllers.SearchController.getSearchForm",
      """
        function(id0,context1) {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "referencedata/search/inner" + _qS([(""" + implicitly[play.api.mvc.QueryStringBindable[Long]].javascriptUnbind + """)("id", id0), (""" + implicitly[play.api.mvc.QueryStringBindable[String]].javascriptUnbind + """)("context", context1)])})
        }
      """
    )
  
  }

  // @LINE:6
  class ReverseApplication(_prefix: => String) {

    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:13
    def catalogResume: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.reference.data.controllers.Application.catalogResume",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "referencedata/resume"})
        }
      """
    )
  
    // @LINE:12
    def goToHome: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.reference.data.controllers.Application.goToHome",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "referencedata/home"})
        }
      """
    )
  
    // @LINE:6
    def javascriptRoutes: JavaScriptReverseRoute = JavaScriptReverseRoute(
      "na.reference.data.controllers.Application.javascriptRoutes",
      """
        function() {
          return _wA({method:"GET", url:"""" + _prefix + { _defaultPrefix } + """" + "referencedata/assets/javascripts/routes"})
        }
      """
    )
  
  }


}
