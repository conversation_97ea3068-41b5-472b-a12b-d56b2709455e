
package na.reference.data.views.html.skeletons

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object mainSkeleton extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[String,Html,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(title: String = "")(content: Html):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*3.2*/scripts/*3.9*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*3.13*/("""
    """),_display_(/*4.6*/na/*4.8*/.reference.data.views.html.imports.scripts.render()),format.raw/*4.59*/("""
""")))};def /*7.2*/body/*7.6*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*7.10*/("""
    """),format.raw/*8.5*/("""<div
    id="catalog"
    class="page page--reference-data fx-full-height inherit"
    data-na-portal-page-container
    >"""),_display_(/*12.7*/content),format.raw/*12.14*/("""</div>
""")))};
Seq[Any](format.raw/*1.37*/("""

"""),format.raw/*5.2*/("""

"""),format.raw/*13.2*/("""

"""),_display_(/*15.2*/na/*15.4*/.naportalbase.views.html.skeletons.root.render(title, body, scripts)))
      }
    }
  }

  def render(title:String,content:Html): play.twirl.api.HtmlFormat.Appendable = apply(title)(content)

  def f:((String) => (Html) => play.twirl.api.HtmlFormat.Appendable) = (title) => (content) => apply(title)(content)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:55 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/skeletons/mainSkeleton.scala.html
                  HASH: b254015227ef7e5300a38de9eb267be847b58fef
                  MATRIX: 988->1|1101->39|1115->46|1195->50|1226->56|1235->58|1306->109|1330->114|1341->118|1421->122|1452->127|1601->250|1629->257|1676->36|1704->111|1733->265|1762->268|1772->270
                  LINES: 28->1|32->3|32->3|34->3|35->4|35->4|35->4|36->7|36->7|38->7|39->8|43->12|43->12|45->1|47->5|49->13|51->15|51->15
                  -- GENERATED --
              */
          