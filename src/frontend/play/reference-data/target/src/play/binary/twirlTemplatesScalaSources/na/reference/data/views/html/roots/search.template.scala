
package na.reference.data.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object search extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[String,na.catalog.basemodule.ui.WebComponent,na.reference.data.utils.views.skeletons.HeaderSkeletonHelper,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        entityName: String,
        element: na.catalog.basemodule.ui.WebComponent,
        headerSkeletonHelper: na.reference.data.utils.views.skeletons.HeaderSkeletonHelper
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*7.2*/import na.naportalbase.views.tags.i18n
/*8.2*/import na.reference.data.settings.{AAAPIResources, ReferenceDataConstants}
/*9.2*/import na.reference.data.utils.views.FormFieldContainerLocation
/*10.2*/import na.reference.data.views.html.components.leftSideNavigationBarComponents
/*11.2*/import na.reference.data.views.html.search.leftSideForm
/*12.2*/import na.reference.data.views.html.skeletons.mainSkeleton
/*13.2*/import pt.alticelabs.nossis.security.views.html.authorized

def /*15.2*/toolbar/*15.9*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*15.13*/("""
    """),format.raw/*16.5*/("""<div class="fx-bulk-actions page__search-results-bulk-actions clearfix hidden" data-na-portal-reference-data-table-toolbar>
        """),_display_(/*17.10*/authorized(AAAPIResources.catalog.U.toString)/*17.55*/ {_display_(Seq[Any](format.raw/*17.57*/("""
            """),format.raw/*18.13*/("""<button role="button" class="btn btn-default btn-xs" title=""""),_display_(/*18.74*/i18n("na.datatables.action.label.edit")),format.raw/*18.113*/("""" data-action="edit">
                <i class="glyphicon glyphicon-pencil"></i>
                """),_display_(/*20.18*/i18n("na.datatables.action.label.edit")),format.raw/*20.57*/("""
            """),format.raw/*21.13*/("""</button>
        """)))}),format.raw/*22.10*/("""
        """),_display_(/*23.10*/authorized(AAAPIResources.catalog.C.toString)/*23.55*/ {_display_(Seq[Any](format.raw/*23.57*/("""
            """),format.raw/*24.13*/("""<button role="button" class="btn btn-default btn-xs" title=""""),_display_(/*24.74*/i18n("na.datatables.action.label.clone")),format.raw/*24.114*/("""" data-action="clone">
                <i class="fuxicons fuxicons-clone"></i>
                """),_display_(/*26.18*/i18n("na.datatables.action.label.clone")),format.raw/*26.58*/("""
            """),format.raw/*27.13*/("""</button>
        """)))}),format.raw/*28.10*/("""
        """),_display_(/*29.10*/authorized(AAAPIResources.catalog.D.toString)/*29.55*/ {_display_(Seq[Any](format.raw/*29.57*/("""
            """),format.raw/*30.13*/("""<button role="button" class="btn btn-default btn-xs" title=""""),_display_(/*30.74*/i18n("na.datatables.action.label.remove")),format.raw/*30.115*/("""" data-action="delete">
                <i class="glyphicon glyphicon-remove"></i>
                """),_display_(/*32.18*/i18n("na.datatables.action.label.remove")),format.raw/*32.59*/("""
            """),format.raw/*33.13*/("""</button>
        """)))}),format.raw/*34.10*/("""
    """),format.raw/*35.5*/("""</div>
""")))};
Seq[Any](format.raw/*5.2*/("""

"""),format.raw/*14.1*/("""
"""),format.raw/*36.2*/("""


"""),_display_(/*39.2*/mainSkeleton()/*39.16*/ {_display_(Seq[Any](format.raw/*39.18*/("""
    """),format.raw/*40.5*/("""<div class="fx-push-footer fx-full-height" data-na-portal-reference-data-search-page>
        """),_display_(/*41.10*/leftSideNavigationBarComponents/*41.41*/.render(ReferenceDataConstants.CONTEXT_CATALOG)),format.raw/*41.88*/("""
        """),format.raw/*42.9*/("""<x-splitter class="splitter splitter--reference-data-search">
            <div slot="collapsed-left">
                <div class="fx-info-sidebar-collapsed collapsed-search left">
                    <div class="fx-sidebar-header">
                        <span class="fx-entity-header-icon">
                            <i class="fa fa-search"></i>
                        </span>
                    </div>
                </div>
            </div>

            <div slot="left"
              class="fx-splitter-sidebar with-buttons search-sidebar"
              data-na-portal-reference-data-entity-search-form
              data-na-portal-reference-data-entity-search-form-location=""""),_display_(/*56.75*/FormFieldContainerLocation/*56.101*/.SEARCH_SIDEBAR),format.raw/*56.116*/("""">
                """),_display_(/*57.18*/leftSideForm(entityName, element)),format.raw/*57.51*/("""
            """),format.raw/*58.13*/("""</div>

            <div class="page__search-results" slot="right">
                <x-shadow-scroll class="page__search-results-table-container">
                    <div class="fx-entity-header page__search-results-header">
                        <div class="fx-entity-header-info">
                        <span class="fx-entity-header-icon">
                            <i class="fuxicons fuxicons-catalogue"></i>
                        </span>
                            <div class="fx-entity-header-title">
                                <h1>
                                    """),_display_(/*69.38*/entityName),format.raw/*69.48*/("""
                                """),format.raw/*70.33*/("""</h1>
                            </div>
                        </div>
                        <div class="fx-entity-header-actions">
                            """),_display_(/*74.30*/headerSkeletonHelper/*74.50*/.createEntityButtonComponent()),format.raw/*74.80*/("""
                        """),format.raw/*75.25*/("""</div>
                    </div>
                    """),_display_(/*77.22*/toolbar),format.raw/*77.29*/("""
                    """),format.raw/*78.21*/("""<div id="entity-content" class="page__search-results-table fx-entity-info">
                        <table id="catalogResults"
                        class="table fx-table no-footer dataTable"
                        data-na-portal-table-datatable
                        data-na-portal-reference-data-search-table
                        data-na-portal-reference-data-search-table-actions
                        data-na-portal-reference-data-search-table-config
                        ></table>
                    </div>
                </x-shadow-scroll>
            </div>
        </x-splitter>
    </div>
""")))}))
      }
    }
  }

  def render(entityName:String,element:na.catalog.basemodule.ui.WebComponent,headerSkeletonHelper:na.reference.data.utils.views.skeletons.HeaderSkeletonHelper): play.twirl.api.HtmlFormat.Appendable = apply(entityName,element,headerSkeletonHelper)

  def f:((String,na.catalog.basemodule.ui.WebComponent,na.reference.data.utils.views.skeletons.HeaderSkeletonHelper) => play.twirl.api.HtmlFormat.Appendable) = (entityName,element,headerSkeletonHelper) => apply(entityName,element,headerSkeletonHelper)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:55 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/roots/search.scala.html
                  HASH: e4e527c9de10f3baaf2b929f79dc5d9f344b81b1
                  MATRIX: 1072->1|1323->182|1369->222|1451->298|1523->363|1610->443|1674->500|1741->560|1813->621|1828->628|1909->632|1941->637|2101->770|2155->815|2195->817|2236->830|2324->891|2385->930|2510->1028|2570->1067|2611->1080|2661->1099|2698->1109|2752->1154|2792->1156|2833->1169|2921->1230|2983->1270|3106->1366|3167->1406|3208->1419|3258->1438|3295->1448|3349->1493|3389->1495|3430->1508|3518->1569|3581->1610|3708->1710|3770->1751|3811->1764|3861->1783|3893->1788|3939->179|3968->619|3996->1796|4026->1800|4049->1814|4089->1816|4121->1821|4243->1916|4283->1947|4351->1994|4387->2003|5102->2691|5138->2717|5175->2732|5222->2752|5276->2785|5317->2798|5934->3388|5965->3398|6026->3431|6217->3595|6246->3615|6297->3645|6350->3670|6432->3725|6460->3732|6509->3753
                  LINES: 28->1|35->7|36->8|37->9|38->10|39->11|40->12|41->13|43->15|43->15|45->15|46->16|47->17|47->17|47->17|48->18|48->18|48->18|50->20|50->20|51->21|52->22|53->23|53->23|53->23|54->24|54->24|54->24|56->26|56->26|57->27|58->28|59->29|59->29|59->29|60->30|60->30|60->30|62->32|62->32|63->33|64->34|65->35|67->5|69->14|70->36|73->39|73->39|73->39|74->40|75->41|75->41|75->41|76->42|90->56|90->56|90->56|91->57|91->57|92->58|103->69|103->69|104->70|108->74|108->74|108->74|109->75|111->77|111->77|112->78
                  -- GENERATED --
              */
          