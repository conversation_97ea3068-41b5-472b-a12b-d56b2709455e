// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/conf/referencedata.routes
// @DATE:Tue Jul 01 09:50:04 WEST 2025

package na.reference.data.controllers;

import referencedata.RoutesPrefix;

public class routes {
  
  public static final na.reference.data.controllers.ReverseCatalogController CatalogController = new na.reference.data.controllers.ReverseCatalogController(RoutesPrefix.byNamePrefix());
  public static final na.reference.data.controllers.ReverseAssets Assets = new na.reference.data.controllers.ReverseAssets(RoutesPrefix.byNamePrefix());
  public static final na.reference.data.controllers.ReverseSearchController SearchController = new na.reference.data.controllers.ReverseSearchController(RoutesPrefix.byNamePrefix());
  public static final na.reference.data.controllers.ReverseApplication Application = new na.reference.data.controllers.ReverseApplication(RoutesPrefix.byNamePrefix());

  public static class javascript {
    
    public static final na.reference.data.controllers.javascript.ReverseCatalogController CatalogController = new na.reference.data.controllers.javascript.ReverseCatalogController(RoutesPrefix.byNamePrefix());
    public static final na.reference.data.controllers.javascript.ReverseAssets Assets = new na.reference.data.controllers.javascript.ReverseAssets(RoutesPrefix.byNamePrefix());
    public static final na.reference.data.controllers.javascript.ReverseSearchController SearchController = new na.reference.data.controllers.javascript.ReverseSearchController(RoutesPrefix.byNamePrefix());
    public static final na.reference.data.controllers.javascript.ReverseApplication Application = new na.reference.data.controllers.javascript.ReverseApplication(RoutesPrefix.byNamePrefix());
  }

}
