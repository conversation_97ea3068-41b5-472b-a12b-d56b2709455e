
package na.reference.data.views.html.components

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object saveEntityButtons extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[java.util.Map[String, String],java.util.Map[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        saveAttributes: java.util.Map[String, String],
        cancelAttributes: java.util.Map[String, String]
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*6.2*/import na.naportalbase.utils.TemplateUtils
/*7.2*/import na.naportalbase.views.tags.i18n


Seq[Any](format.raw/*4.2*/("""

"""),format.raw/*8.1*/("""
"""),format.raw/*9.11*/("""
"""),format.raw/*10.1*/("""<button class="btn btn-primary" """),_display_(/*10.34*/TemplateUtils/*10.47*/.dynamicElementsAttributes(saveAttributes)),format.raw/*10.89*/(""">
    <i class="fuxicons fuxicons-floppy-disk"></i> """),_display_(/*11.52*/i18n("na.button.label.save")),format.raw/*11.80*/("""
"""),format.raw/*12.1*/("""</button>
"""),format.raw/*13.13*/("""
"""),format.raw/*14.1*/("""<button class="btn btn-default" """),_display_(/*14.34*/TemplateUtils/*14.47*/.dynamicElementsAttributes(cancelAttributes)),format.raw/*14.91*/(""">
"""),_display_(/*15.2*/i18n("na.button.label.cancel")),format.raw/*15.32*/("""
"""),format.raw/*16.1*/("""</button>"""))
      }
    }
  }

  def render(saveAttributes:java.util.Map[String, String],cancelAttributes:java.util.Map[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(saveAttributes,cancelAttributes)

  def f:((java.util.Map[String, String],java.util.Map[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (saveAttributes,cancelAttributes) => apply(saveAttributes,cancelAttributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:50:04 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/components/saveEntityButtons.scala.html
                  HASH: 89ea183f61150a111398a4466a20e6600f3eaf65
                  MATRIX: 1042->1|1229->118|1279->162|1346->115|1374->201|1402->212|1430->213|1490->246|1512->259|1575->301|1655->354|1704->382|1732->383|1770->405|1798->406|1858->439|1880->452|1945->496|1974->499|2025->529|2053->530
                  LINES: 28->1|34->6|35->7|38->4|40->8|41->9|42->10|42->10|42->10|42->10|43->11|43->11|44->12|45->13|46->14|46->14|46->14|46->14|47->15|47->15|48->16
                  -- GENERATED --
              */
          