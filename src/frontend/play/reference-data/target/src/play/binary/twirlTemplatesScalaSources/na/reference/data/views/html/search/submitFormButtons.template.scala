
package na.reference.data.views.html.search

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n

object submitFormButtons extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*3.1*/("""<a id="submit_form" class="btn btn-sm btn-primary" data-form-action="submit">
    """),_display_(/*4.6*/i18n("na.buttons.search")),format.raw/*4.31*/("""
"""),format.raw/*5.1*/("""</a>

<a id="clean_form" class="btn btn-sm btn-default" data-form-action="clear">
    """),_display_(/*8.6*/i18n("na.buttons.clear")),format.raw/*8.30*/("""
"""),format.raw/*9.1*/("""</a>"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:50:04 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/search/submitFormButtons.scala.html
                  HASH: fb38ace4d921bdb0b763aa8b2782c409727b1f71
                  MATRIX: 676->1|1113->41|1221->124|1266->149|1293->150|1405->237|1449->261|1476->262
                  LINES: 24->1|34->3|35->4|35->4|36->5|39->8|39->8|40->9
                  -- GENERATED --
              */
          