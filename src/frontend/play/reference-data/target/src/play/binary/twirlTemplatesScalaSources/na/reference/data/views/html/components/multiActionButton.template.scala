
package na.reference.data.views.html.components

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object multiActionButton extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[java.util.Map[String, java.util.Map[String, String]],java.util.Map[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        attributes: java.util.Map[String, java.util.Map[String, String]],
        icons: java.util.Map[String, String]
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*6.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*4.2*/("""

"""),format.raw/*7.1*/("""
"""),_display_(/*8.2*/for((key, value) <- attributes) yield /*8.33*/ {_display_(Seq[Any](format.raw/*8.35*/("""
    """),format.raw/*9.5*/("""<button class="btn btn-default btn-sm" """),_display_(/*9.45*/TemplateUtils/*9.58*/.dynamicElementsAttributes(value)),format.raw/*9.91*/(""">
    """),_display_(/*10.6*/Html(icons.get(key))),format.raw/*10.26*/("""
    """),format.raw/*11.5*/("""</button>
""")))}))
      }
    }
  }

  def render(attributes:java.util.Map[String, java.util.Map[String, String]],icons:java.util.Map[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(attributes,icons)

  def f:((java.util.Map[String, java.util.Map[String, String]],java.util.Map[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (attributes,icons) => apply(attributes,icons)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:55 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/components/multiActionButton.scala.html
                  HASH: fa59a2acb69c0c7aca541f4df716038d88c33887
                  MATRIX: 1065->1|1260->126|1331->123|1359->169|1386->171|1432->202|1471->204|1502->209|1568->249|1589->262|1642->295|1675->302|1716->322|1748->327
                  LINES: 28->1|34->6|37->4|39->7|40->8|40->8|40->8|41->9|41->9|41->9|41->9|42->10|42->10|43->11
                  -- GENERATED --
              */
          