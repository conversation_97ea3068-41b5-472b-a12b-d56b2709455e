
package na.reference.data.views.html.skeletons

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object mainContentSkeleton extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[Html,Html,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        attributesTabContent: Html,
        specificAttributesTabContent: Html
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*6.2*/import na.naportalbase.views.tags.i18n


Seq[Any](format.raw/*4.2*/("""

"""),format.raw/*7.1*/("""
"""),format.raw/*8.1*/("""<div id="entity-content" class="fx-entity-info">
    <x-tab-container>
        <x-tab label=""""),_display_(/*10.24*/i18n("na.reference.data.tab.characteristics")),format.raw/*10.69*/("""">
            <x-shadow-scroll flex vertical>
                <div class="fx-form-content fx-expandable-form-blocks">
                    <div class="form-horizontal fx-toggle-form-vertical">
                        <x-collapsible header=""""),_display_(/*14.49*/i18n("na.reference.data.tab.attributes")),format.raw/*14.89*/(""""
                        class="collapsible-panel--form ref-data__attributes-section">
                        """),_display_(/*16.26*/attributesTabContent),format.raw/*16.46*/("""
                        """),format.raw/*17.25*/("""</x-collapsible>
                        <x-collapsible header=""""),_display_(/*18.49*/i18n("na.reference.data.tab.specific.attributes")),format.raw/*18.98*/(""""
                        class="collapsible-panel--form ref-data__attributes-section">
                        """),_display_(/*20.26*/specificAttributesTabContent),format.raw/*20.54*/("""
                        """),format.raw/*21.25*/("""</x-collapsible>
                    </div>
                </div>
            </x-shadow-scroll>
        </x-tab>
    </x-tab-container>
</div>"""))
      }
    }
  }

  def render(attributesTabContent:Html,specificAttributesTabContent:Html): play.twirl.api.HtmlFormat.Appendable = apply(attributesTabContent,specificAttributesTabContent)

  def f:((Html,Html) => play.twirl.api.HtmlFormat.Appendable) = (attributesTabContent,specificAttributesTabContent) => apply(attributesTabContent,specificAttributesTabContent)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:55 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/skeletons/mainContentSkeleton.scala.html
                  HASH: f9e5b0c33ef07922a45a83642d3999201ae9042c
                  MATRIX: 993->1|1148->86|1215->83|1243->125|1270->126|1391->220|1457->265|1725->506|1786->546|1926->659|1967->679|2020->704|2112->769|2182->818|2322->931|2371->959|2424->984
                  LINES: 28->1|34->6|37->4|39->7|40->8|42->10|42->10|46->14|46->14|48->16|48->16|49->17|50->18|50->18|52->20|52->20|53->21
                  -- GENERATED --
              */
          