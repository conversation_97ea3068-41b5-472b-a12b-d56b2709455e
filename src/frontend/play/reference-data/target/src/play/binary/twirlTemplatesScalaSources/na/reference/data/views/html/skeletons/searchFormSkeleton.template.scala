
package na.reference.data.views.html.skeletons

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object searchFormSkeleton extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[String,Html,na.catalog.basemodule.ui.WebComponent,na.reference.data.utils.views.FormFieldContainerLocation,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        entityName: String,
        bottomButtons: Html = Html(""),
        element: na.catalog.basemodule.ui.WebComponent,
        fieldLocation: na.reference.data.utils.views.FormFieldContainerLocation
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*8.2*/import na.catalog.basemodule.ui.{Label, WebComponent}
/*9.2*/import na.reference.data.utils.views.FormFieldContainerLocation
/*10.2*/import na.reference.data.views.html.search.searchFormFieldContainer

def /*39.2*/container/*39.11*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*39.15*/("""
    """),format.raw/*40.5*/("""<div class="col-xs-12 field-container__slot">
        """),_display_(/*41.10*/element/*41.17*/.render()),format.raw/*41.26*/("""
    """),format.raw/*42.5*/("""</div>
""")))};def /*45.2*/content/*45.9*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*45.13*/("""
    """),_display_(/*46.6*/renderLabel(element)),format.raw/*46.26*/("""
    """),_display_(/*47.6*/container),format.raw/*47.15*/("""
""")))};def /*12.2*/renderLabel/*12.13*/(element: WebComponent) = {{
    if(element.getI18n != null) {

        val dataAttributes = new java.util.HashMap[String, String]()
        dataAttributes.put("class", "col-xs-12 control-label")

        val label = new Label(element.getId, element.getI18n)
        label.setDataAttributes(dataAttributes)
        label.render()
    }
}};def /*24.2*/formElementClasses/*24.20*/ = {{
    fieldLocation match {
        case FormFieldContainerLocation.DASHBOARD => "form form--dashboard"
        case FormFieldContainerLocation.SEARCH_SIDEBAR => "form form--sidebar"
        case _ => "form"
    }
}};def /*32.2*/searchBoxItemClasses/*32.22*/ = {{
    fieldLocation match {
        case FormFieldContainerLocation.DASHBOARD => "fx-main-searchbox-item"
        case _ => ""
    }
}};
Seq[Any](format.raw/*6.2*/("""

"""),format.raw/*11.1*/("""
"""),format.raw/*22.2*/("""

"""),format.raw/*30.2*/("""

"""),format.raw/*37.2*/("""

"""),format.raw/*43.2*/("""

"""),format.raw/*48.2*/("""

"""),format.raw/*50.1*/("""<form id="catalog-search-form" class=""""),_display_(/*50.40*/formElementClasses),format.raw/*50.58*/("""">
    <div class="row form__header">
        <div class="fx-sidebar-nav-title">
            <h4>"""),_display_(/*53.18*/entityName),format.raw/*53.28*/("""</h4>
        </div>
    </div>
    <div class=""""),_display_(/*56.18*/searchBoxItemClasses),format.raw/*56.38*/("""">
    """),_display_(/*57.6*/if(element != null)/*57.25*/ {_display_(Seq[Any](format.raw/*57.27*/("""
        """),format.raw/*58.9*/("""<div class="row form__fields-row" data-form-fields-container>
            <!-- base search element -->
            """),_display_(/*60.14*/searchFormFieldContainer/*60.38*/.render(content, fieldLocation)),format.raw/*60.69*/("""
        """),format.raw/*61.9*/("""</div>
    """)))}),format.raw/*62.6*/("""
    """),format.raw/*63.5*/("""</div>
</form>

"""),_display_(/*66.2*/bottomButtons))
      }
    }
  }

  def render(entityName:String,bottomButtons:Html,element:na.catalog.basemodule.ui.WebComponent,fieldLocation:na.reference.data.utils.views.FormFieldContainerLocation): play.twirl.api.HtmlFormat.Appendable = apply(entityName,bottomButtons,element,fieldLocation)

  def f:((String,Html,na.catalog.basemodule.ui.WebComponent,na.reference.data.utils.views.FormFieldContainerLocation) => play.twirl.api.HtmlFormat.Appendable) = (entityName,bottomButtons,element,fieldLocation) => apply(entityName,bottomButtons,element,fieldLocation)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:50:04 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/skeletons/searchFormSkeleton.scala.html
                  HASH: 69c845743ff5cebeefca6dc11d5c41bf713628c6
                  MATRIX: 1089->1|1369->211|1430->266|1502->331|1583->1153|1601->1162|1682->1166|1714->1171|1796->1226|1812->1233|1842->1242|1874->1247|1905->1258|1920->1265|2001->1269|2033->1275|2074->1295|2106->1301|2136->1310|2161->401|2181->412|2532->752|2559->770|2792->992|2821->1012|2988->208|3017->399|3045->749|3074->989|3103->1150|3132->1255|3161->1312|3190->1314|3256->1353|3295->1371|3420->1469|3451->1479|3527->1528|3568->1548|3602->1556|3630->1575|3670->1577|3706->1586|3849->1702|3882->1726|3934->1757|3970->1766|4012->1778|4044->1783|4087->1800
                  LINES: 28->1|36->8|37->9|38->10|40->39|40->39|42->39|43->40|44->41|44->41|44->41|45->42|46->45|46->45|48->45|49->46|49->46|50->47|50->47|51->12|51->12|61->24|61->24|67->32|67->32|73->6|75->11|76->22|78->30|80->37|82->43|84->48|86->50|86->50|86->50|89->53|89->53|92->56|92->56|93->57|93->57|93->57|94->58|96->60|96->60|96->60|97->61|98->62|99->63|102->66
                  -- GENERATED --
              */
          