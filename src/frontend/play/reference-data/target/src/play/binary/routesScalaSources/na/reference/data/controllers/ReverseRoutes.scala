// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/conf/referencedata.routes
// @DATE:Tue Jul 01 09:50:04 WEST 2025

import play.api.mvc.Call


import _root_.controllers.Assets.Asset

// @LINE:6
package na.reference.data.controllers {

  // @LINE:24
  class ReverseCatalogController(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:34
    def saveCatalog(id:Long): Call = {
      
      Call("PUT", _prefix + { _defaultPrefix } + "referencedata/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)))
    }
  
    // @LINE:29
    def getCatalogEditPage(id:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "referencedata/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)) + "/edit")
    }
  
    // @LINE:35
    def deleteCatalogInstances(ids:String = ""): Call = {
      
      Call("DELETE", _prefix + { _defaultPrefix } + "referencedata/delete" + play.core.routing.queryString(List(if(ids == "") None else Some(implicitly[play.api.mvc.QueryStringBindable[String]].unbind("ids", ids)))))
    }
  
    // @LINE:31
    def getCatalogCreateInnerForm(id:Long): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "referencedata/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)) + "/create-inner")
    }
  
    // @LINE:24
    def createCatalogInstance(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "referencedata/create")
    }
  
    // @LINE:26
    def generateEntityTab(id:Long): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "referencedata/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)) + "/entity-tab")
    }
  
    // @LINE:30
    def getCatalogEditInnerForm(id:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "referencedata/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)) + "/edit-inner")
    }
  
    // @LINE:32
    def getCatalogCloneForm(id:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "referencedata/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)) + "/clone")
    }
  
    // @LINE:33
    def getCatalogCloneInnerForm(id:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "referencedata/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)) + "/clone-inner")
    }
  
    // @LINE:28
    def getCatalogInnerPage(id:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "referencedata/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)) + "/inner")
    }
  
    // @LINE:25
    def getCatalogCreatePage(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "referencedata/create")
    }
  
    // @LINE:27
    def getCatalogViewPage(id:Long): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "referencedata/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)))
    }
  
  }

  // @LINE:9
  class ReverseAssets(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:9
    def versioned(file:Asset): Call = {
      implicit lazy val _rrc = new play.core.routing.ReverseRouteContext(Map(("path", "/public"))); _rrc
      Call("GET", _prefix + { _defaultPrefix } + "referencedata/assets/" + implicitly[play.api.mvc.PathBindable[Asset]].unbind("file", file))
    }
  
  }

  // @LINE:16
  class ReverseSearchController(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:16
    def getDynamicElements(id:Long, location:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "referencedata/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[Long]].unbind("id", id)) + "/dynamic-elements/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("location", location)))
    }
  
    // @LINE:18
    def getDashboardPage(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "referencedata/dashboard")
    }
  
    // @LINE:17
    def getAuthorizedCatalogs(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "referencedata/authorized-catalogs")
    }
  
    // @LINE:21
    def getSearchResults(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "referencedata/search")
    }
  
    // @LINE:20
    def getSearchPage(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "referencedata/search")
    }
  
    // @LINE:19
    def getSearchForm(id:Long, context:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "referencedata/search/inner" + play.core.routing.queryString(List(Some(implicitly[play.api.mvc.QueryStringBindable[Long]].unbind("id", id)), Some(implicitly[play.api.mvc.QueryStringBindable[String]].unbind("context", context)))))
    }
  
  }

  // @LINE:6
  class ReverseApplication(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:13
    def catalogResume(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "referencedata/resume")
    }
  
    // @LINE:12
    def goToHome(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "referencedata/home")
    }
  
    // @LINE:6
    def javascriptRoutes(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "referencedata/assets/javascripts/routes")
    }
  
  }


}
