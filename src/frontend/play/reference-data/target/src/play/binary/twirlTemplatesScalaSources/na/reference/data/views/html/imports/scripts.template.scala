
package na.reference.data.views.html.imports

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object scripts extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.1*/("""<script src=""""),_display_(/*1.15*/na/*1.17*/.reference.data.controllers.routes.Application.javascriptRoutes()),format.raw/*1.82*/(""""></script><script src='"""),_display_(/*1.107*/na/*1.109*/.reference.data.controllers.routes.Assets.versioned("basemodule/runtime.ef4cdaceb8028d495c4b.min.js")),format.raw/*1.210*/("""'></script><script src='"""),_display_(/*1.235*/na/*1.237*/.reference.data.controllers.routes.Assets.versioned("na-portal-assets/base-styles.9e693254edb056a100ad.min.js")),format.raw/*1.348*/("""'></script><script src='"""),_display_(/*1.373*/na/*1.375*/.reference.data.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-bootstrap.6f86c32db7bf4ccaf010.min.js")),format.raw/*1.500*/("""'></script><script src='"""),_display_(/*1.525*/na/*1.527*/.reference.data.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-selects.c845a1da27d2bbccf773.min.js")),format.raw/*1.650*/("""'></script><script src='"""),_display_(/*1.675*/na/*1.677*/.reference.data.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-std-utils.ab8ed0c92f51f5eb905e.min.js")),format.raw/*1.802*/("""'></script><script src='"""),_display_(/*1.827*/na/*1.829*/.reference.data.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-tables.bc173ec447d65947d31a.min.js")),format.raw/*1.951*/("""'></script><script src='"""),_display_(/*1.976*/na/*1.978*/.reference.data.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-jquery.c69a6ab040df2ef632e6.min.js")),format.raw/*1.1100*/("""'></script><script src='"""),_display_(/*1.1125*/na/*1.1127*/.reference.data.controllers.routes.Assets.versioned("na-portal-assets-vendors/common.a53001b54dbc3b115cc4.min.js")),format.raw/*1.1241*/("""'></script><script src='"""),_display_(/*1.1266*/na/*1.1268*/.reference.data.controllers.routes.Assets.versioned("utils/utils.88e56ee512f1a3af972d.min.js")),format.raw/*1.1362*/("""'></script><script src='"""),_display_(/*1.1387*/na/*1.1389*/.reference.data.controllers.routes.Assets.versioned("basemodule/basemodule.823a1b10758c828389cd.min.js")),format.raw/*1.1493*/("""'></script><script src='"""),_display_(/*1.1518*/na/*1.1520*/.reference.data.controllers.routes.Assets.versioned("catalog-basemodule/catalog-basemodule.fab1781c4660b12ad1d0.min.js")),format.raw/*1.1640*/("""'></script><script src='"""),_display_(/*1.1665*/na/*1.1667*/.reference.data.controllers.routes.Assets.versioned("reference-data/reference-data.43be27b716a952ab37d1.min.js")),format.raw/*1.1779*/("""'></script>"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:55 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/imports/scripts.scala.html
                  HASH: 2784033e11df4752b25212329ee66237e6fdf7b5
                  MATRIX: 1058->0|1098->14|1108->16|1193->81|1245->106|1256->108|1378->209|1430->234|1441->236|1573->347|1625->372|1636->374|1782->499|1834->524|1845->526|1989->649|2041->674|2052->676|2198->801|2250->826|2261->828|2404->950|2456->975|2467->977|2611->1099|2664->1124|2676->1126|2812->1240|2865->1265|2877->1267|2993->1361|3046->1386|3058->1388|3184->1492|3237->1517|3249->1519|3391->1639|3444->1664|3456->1666|3590->1778
                  LINES: 33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1
                  -- GENERATED --
              */
          