
package na.reference.data.views.html.imports

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object scripts extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.1*/("""<script src=""""),_display_(/*1.15*/na/*1.17*/.reference.data.controllers.routes.Application.javascriptRoutes()),format.raw/*1.82*/(""""></script>

<script src='"""),_display_(/*3.15*/na/*3.17*/.reference.data.controllers.routes.Assets.versioned("basemodule/runtime.js")),format.raw/*3.93*/("""'></script>

<script src='"""),_display_(/*5.15*/na/*5.17*/.reference.data.controllers.routes.Assets.versioned("na-portal-assets/base-styles.js")),format.raw/*5.103*/("""'></script>

<script src='"""),_display_(/*7.15*/na/*7.17*/.reference.data.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-bootstrap.js")),format.raw/*7.117*/("""'></script>

<script src='"""),_display_(/*9.15*/na/*9.17*/.reference.data.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-selects.js")),format.raw/*9.115*/("""'></script>

<script src='"""),_display_(/*11.15*/na/*11.17*/.reference.data.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-std-utils.js")),format.raw/*11.117*/("""'></script>

<script src='"""),_display_(/*13.15*/na/*13.17*/.reference.data.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-tables.js")),format.raw/*13.114*/("""'></script>

<script src='"""),_display_(/*15.15*/na/*15.17*/.reference.data.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-jquery.js")),format.raw/*15.114*/("""'></script>

<script src='"""),_display_(/*17.15*/na/*17.17*/.reference.data.controllers.routes.Assets.versioned("na-portal-assets-vendors/common.js")),format.raw/*17.106*/("""'></script>

<script src='"""),_display_(/*19.15*/na/*19.17*/.reference.data.controllers.routes.Assets.versioned("utils/utils.js")),format.raw/*19.86*/("""'></script>

<script src='"""),_display_(/*21.15*/na/*21.17*/.reference.data.controllers.routes.Assets.versioned("basemodule/basemodule.js")),format.raw/*21.96*/("""'></script>

<script src='"""),_display_(/*23.15*/na/*23.17*/.reference.data.controllers.routes.Assets.versioned("catalog-basemodule/catalog-basemodule.js")),format.raw/*23.112*/("""'></script>

<script src='"""),_display_(/*25.15*/na/*25.17*/.reference.data.controllers.routes.Assets.versioned("reference-data/reference-data.js")),format.raw/*25.104*/("""'></script>
"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:50:04 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/imports/scripts.scala.html
                  HASH: a319bb1615de7dd70d2658d0d0e821a8ceb6bb8d
                  MATRIX: 1058->0|1098->14|1108->16|1193->81|1248->110|1258->112|1354->188|1409->217|1419->219|1526->305|1581->334|1591->336|1712->436|1767->465|1777->467|1896->565|1952->594|1963->596|2085->696|2141->725|2152->727|2271->824|2327->853|2338->855|2457->952|2513->981|2524->983|2635->1072|2691->1101|2702->1103|2792->1172|2848->1201|2859->1203|2959->1282|3015->1311|3026->1313|3143->1408|3199->1437|3210->1439|3319->1526
                  LINES: 33->1|33->1|33->1|33->1|35->3|35->3|35->3|37->5|37->5|37->5|39->7|39->7|39->7|41->9|41->9|41->9|43->11|43->11|43->11|45->13|45->13|45->13|47->15|47->15|47->15|49->17|49->17|49->17|51->19|51->19|51->19|53->21|53->21|53->21|55->23|55->23|55->23|57->25|57->25|57->25
                  -- GENERATED --
              */
          