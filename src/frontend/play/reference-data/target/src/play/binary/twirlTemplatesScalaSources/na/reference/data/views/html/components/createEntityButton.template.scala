
package na.reference.data.views.html.components

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object createEntityButton extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[String,java.util.Map[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        buttonValue: String,
        buttonAttributes: java.util.Map[String, String]
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*6.2*/import na.naportalbase.utils.TemplateUtils
/*7.2*/import na.naportalbase.views.tags.i18n


Seq[Any](format.raw/*4.2*/("""

"""),format.raw/*8.1*/("""
"""),format.raw/*9.1*/("""<button
class="btn btn-primary fx-call-to-action"
title=""""),_display_(/*11.9*/i18n(buttonValue)),format.raw/*11.26*/(""""
"""),_display_(/*12.2*/TemplateUtils/*12.15*/.dynamicElementsAttributes(buttonAttributes)),format.raw/*12.59*/("""
"""),format.raw/*13.1*/(""">
    <i class="glyphicon glyphicon-plus"></i>
    """),_display_(/*15.6*/i18n(buttonValue)),format.raw/*15.23*/("""
"""),format.raw/*16.1*/("""</button>"""))
      }
    }
  }

  def render(buttonValue:String,buttonAttributes:java.util.Map[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(buttonValue,buttonAttributes)

  def f:((String,java.util.Map[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (buttonValue,buttonAttributes) => apply(buttonValue,buttonAttributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:55 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/components/createEntityButton.scala.html
                  HASH: 4c2e28c223053b3d9b10d67a1ac7f6a7ffe74b72
                  MATRIX: 1020->1|1181->92|1231->136|1298->89|1326->175|1353->176|1437->234|1475->251|1504->254|1526->267|1591->311|1619->312|1697->364|1735->381|1763->382
                  LINES: 28->1|34->6|35->7|38->4|40->8|41->9|43->11|43->11|44->12|44->12|44->12|45->13|47->15|47->15|48->16
                  -- GENERATED --
              */
          