
package na.reference.data.views.html.entitydetails.header.skeleton

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object headerSkeleton extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[Html,Html,Html,Html,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        header: Html = new Html(""),
        headerActions: Html = new Html(""),
        additionalHeaderHtml1: Html = new Html(""),
        additionalHeaderHtml2: Html = new Html("")
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*6.2*/("""

"""),format.raw/*8.1*/("""<div class="fx-entity-header">
    <div class="fx-entity-header-inner">
        <div class="fx-entity-header-headings clearfix">
            """),_display_(/*11.14*/header),format.raw/*11.20*/("""
            """),format.raw/*12.13*/("""<div class="pull-right">
                """),_display_(/*13.18*/headerActions),format.raw/*13.31*/("""
            """),format.raw/*14.13*/("""</div>
        </div>
        """),_display_(/*16.10*/additionalHeaderHtml1),format.raw/*16.31*/("""
    """),format.raw/*17.5*/("""</div>
    """),_display_(/*18.6*/additionalHeaderHtml2),format.raw/*18.27*/("""
"""),format.raw/*19.1*/("""</div>"""))
      }
    }
  }

  def render(header:Html,headerActions:Html,additionalHeaderHtml1:Html,additionalHeaderHtml2:Html): play.twirl.api.HtmlFormat.Appendable = apply(header,headerActions,additionalHeaderHtml1,additionalHeaderHtml2)

  def f:((Html,Html,Html,Html) => play.twirl.api.HtmlFormat.Appendable) = (header,headerActions,additionalHeaderHtml1,additionalHeaderHtml2) => apply(header,headerActions,additionalHeaderHtml1,additionalHeaderHtml2)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:55 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/entitydetails/header/skeleton/headerSkeleton.scala.html
                  HASH: 1febe048bfb079c785e7f4b41a03aa67d02bc0d4
                  MATRIX: 1018->1|1299->188|1327->190|1496->332|1523->338|1564->351|1633->393|1667->406|1708->419|1766->450|1808->471|1840->476|1878->488|1920->509|1948->510
                  LINES: 28->1|38->6|40->8|43->11|43->11|44->12|45->13|45->13|46->14|48->16|48->16|49->17|50->18|50->18|51->19
                  -- GENERATED --
              */
          