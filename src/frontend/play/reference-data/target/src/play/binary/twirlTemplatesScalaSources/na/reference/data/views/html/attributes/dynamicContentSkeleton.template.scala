
package na.reference.data.views.html.attributes

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object dynamicContentSkeleton extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[na.catalog.basemodule.models.nossisinv.attributes.UIContainer,pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,na.catalog.basemodule.models.nossisinv.attributes.UICatAttribute[_$1] forSome { 
   type _$1
},play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        uiContainer: na.catalog.basemodule.models.nossisinv.attributes.UIContainer,
        uiElement: pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,
        uiAttribute: na.catalog.basemodule.models.nossisinv.attributes.UICatAttribute[_]
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*7.2*/import na.catalog.basemodule.utils.InputUtils
/*8.2*/import na.reference.data.utils.views.roots.HelperDrawContainersContents

def /*10.2*/action/*10.8*/ = {{
    uiContainer.getUiContext.getAction
}};def /*14.2*/divCss/*14.8*/ = {{
    InputUtils.colClassForDiv(uiElement.getUIElementWidth, action)
}};def /*18.2*/labelCss/*18.10*/ = {{
    InputUtils.colClassForLabel(uiElement.getUIElementWidth, action)
}};def /*22.2*/required/*22.10*/ = {{
    InputUtils.requiredLabelClass(uiElement, action)
}};def /*26.2*/label/*26.7*/ = {{
    InputUtils.getLabel(uiAttribute)
}};
Seq[Any](format.raw/*5.2*/("""

"""),format.raw/*9.1*/("""
"""),format.raw/*12.2*/("""

"""),format.raw/*16.2*/("""

"""),format.raw/*20.2*/("""

"""),format.raw/*24.2*/("""

"""),format.raw/*28.2*/("""

"""),format.raw/*30.1*/("""<label is="x-label" class=""""),_display_(/*30.29*/labelCss),format.raw/*30.37*/(""" """),_display_(/*30.39*/required),format.raw/*30.47*/(""" """),format.raw/*30.48*/("""control-label">"""),_display_(/*30.64*/label),format.raw/*30.69*/("""</label>

<div class=""""),_display_(/*32.14*/divCss),format.raw/*32.20*/("""">
"""),_display_(/*33.2*/{
    new HelperDrawContainersContents().renderStaticContent(uiAttribute, uiElement)
}),format.raw/*35.2*/("""
"""),format.raw/*36.1*/("""</div>"""))
      }
    }
  }

  def render(uiContainer:na.catalog.basemodule.models.nossisinv.attributes.UIContainer,uiElement:pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,uiAttribute:na.catalog.basemodule.models.nossisinv.attributes.UICatAttribute[_$1] forSome { 
   type _$1
}): play.twirl.api.HtmlFormat.Appendable = apply(uiContainer,uiElement,uiAttribute)

  def f:((na.catalog.basemodule.models.nossisinv.attributes.UIContainer,pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,na.catalog.basemodule.models.nossisinv.attributes.UICatAttribute[_$1] forSome { 
   type _$1
}) => play.twirl.api.HtmlFormat.Appendable) = (uiContainer,uiElement,uiAttribute) => apply(uiContainer,uiElement,uiAttribute)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:50:04 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/attributes/dynamicContentSkeleton.scala.html
                  HASH: 87d30aff4fdab6b13403f8b34c44bc298a79b7b9
                  MATRIX: 1203->1|1531->259|1584->306|1669->380|1683->386|1743->435|1757->441|1845->518|1862->526|1952->605|1969->613|2043->676|2056->681|2129->256|2157->378|2185->432|2214->515|2243->602|2272->673|2301->725|2330->727|2385->755|2414->763|2443->765|2472->773|2501->774|2544->790|2570->795|2620->818|2647->824|2677->828|2783->914|2811->915
                  LINES: 30->1|37->7|38->8|40->10|40->10|42->14|42->14|44->18|44->18|46->22|46->22|48->26|48->26|51->5|53->9|54->12|56->16|58->20|60->24|62->28|64->30|64->30|64->30|64->30|64->30|64->30|64->30|64->30|66->32|66->32|67->33|69->35|70->36
                  -- GENERATED --
              */
          