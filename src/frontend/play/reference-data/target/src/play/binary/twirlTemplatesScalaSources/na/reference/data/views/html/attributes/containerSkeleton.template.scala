
package na.reference.data.views.html.attributes

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object containerSkeleton extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[Long,Html,pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext.Action,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        columnQuantity: Long,
        content: Html,
        action: pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext.Action
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*7.2*/import na.catalog.basemodule.utils.InputUtils

def /*9.2*/divCss/*9.8*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*9.12*/("""
"""),_display_(/*10.2*/InputUtils/*10.12*/.formMainColDivClass(columnQuantity, action)),format.raw/*10.56*/(""" """),format.raw/*10.57*/("""form-group field-container field-container--details-form
""")))};
Seq[Any](format.raw/*5.2*/("""

"""),format.raw/*8.1*/("""
"""),format.raw/*11.2*/("""

"""),format.raw/*13.1*/("""<div class=""""),_display_(/*13.14*/divCss),format.raw/*13.20*/("""" >
    """),_display_(/*14.6*/content),format.raw/*14.13*/("""
"""),format.raw/*15.1*/("""</div>"""))
      }
    }
  }

  def render(columnQuantity:Long,content:Html,action:pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext.Action): play.twirl.api.HtmlFormat.Appendable = apply(columnQuantity,content,action)

  def f:((Long,Html,pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext.Action) => play.twirl.api.HtmlFormat.Appendable) = (columnQuantity,content,action) => apply(columnQuantity,content,action)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:50:04 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/attributes/containerSkeleton.scala.html
                  HASH: 357d1afe09b5696309e0c7c2f5b5a865329da323
                  MATRIX: 1058->1|1269->142|1327->190|1340->196|1420->200|1448->202|1467->212|1532->256|1561->257|1657->139|1685->188|1713->315|1742->317|1782->330|1809->336|1844->345|1872->352|1900->353
                  LINES: 28->1|35->7|37->9|37->9|39->9|40->10|40->10|40->10|40->10|42->5|44->8|45->11|47->13|47->13|47->13|48->14|48->14|49->15
                  -- GENERATED --
              */
          