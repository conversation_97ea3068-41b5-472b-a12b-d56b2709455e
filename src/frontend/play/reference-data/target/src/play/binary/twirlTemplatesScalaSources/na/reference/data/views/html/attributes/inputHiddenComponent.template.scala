
package na.reference.data.views.html.attributes

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object inputHiddenComponent extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[String,java.util.Map[String, String],String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        inputId: String,
        inputAttributes: java.util.Map[String, String],
        value: String = ""
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*7.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*5.2*/("""

"""),format.raw/*8.1*/("""
"""),format.raw/*9.1*/("""<input
type="hidden"
id=""""),_display_(/*11.6*/inputId),format.raw/*11.13*/(""""
class="form-control input-sm"
value=""""),_display_(/*13.9*/value),format.raw/*13.14*/(""""
"""),_display_(/*14.2*/TemplateUtils/*14.15*/.dynamicElementsAttributes(inputAttributes)),format.raw/*14.58*/("""
"""),format.raw/*15.1*/(""">"""))
      }
    }
  }

  def render(inputId:String,inputAttributes:java.util.Map[String, String],value:String): play.twirl.api.HtmlFormat.Appendable = apply(inputId,inputAttributes,value)

  def f:((String,java.util.Map[String, String],String) => play.twirl.api.HtmlFormat.Appendable) = (inputId,inputAttributes,value) => apply(inputId,inputAttributes,value)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:50:04 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/attributes/inputHiddenComponent.scala.html
                  HASH: 31ff6ec45d7f08d28158759c03526b15cf1fadd3
                  MATRIX: 1029->1|1213->115|1284->112|1312->158|1339->159|1391->185|1419->192|1485->232|1511->237|1540->240|1562->253|1626->296|1654->297
                  LINES: 28->1|35->7|38->5|40->8|41->9|43->11|43->11|45->13|45->13|46->14|46->14|46->14|47->15
                  -- GENERATED --
              */
          