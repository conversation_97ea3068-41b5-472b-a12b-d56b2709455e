
package na.reference.data.views.html.attributes

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object containerNotificationSkeleton extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[Html,pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext.Action,Long,Long,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        content: Html = Html(""),
        action: pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext.Action,
        columnQuantity: Long,
        elementWidth: Long
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*8.2*/import na.catalog.basemodule.utils.InputUtils

def /*10.2*/divCss/*10.8*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*10.12*/("""
"""),_display_(/*11.2*/InputUtils/*11.12*/.formMainColDivClass(columnQuantity, action)),format.raw/*11.56*/(""" """),format.raw/*11.57*/("""form-group field-container field-container--details-form field-container--with-notification
""")))};
Seq[Any](format.raw/*6.2*/("""

"""),format.raw/*9.1*/("""
"""),format.raw/*12.2*/("""

"""),format.raw/*14.1*/("""<div class=""""),_display_(/*14.14*/divCss),format.raw/*14.20*/("""">
    """),_display_(/*15.6*/content),format.raw/*15.13*/("""
"""),format.raw/*16.1*/("""</div>"""))
      }
    }
  }

  def render(content:Html,action:pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext.Action,columnQuantity:Long,elementWidth:Long): play.twirl.api.HtmlFormat.Appendable = apply(content,action,columnQuantity,elementWidth)

  def f:((Html,pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext.Action,Long,Long) => play.twirl.api.HtmlFormat.Appendable) = (content,action,columnQuantity,elementWidth) => apply(content,action,columnQuantity,elementWidth)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:55 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/attributes/containerNotificationSkeleton.scala.html
                  HASH: 42404c4e1360775f009c16356b44252cf6705cf9
                  MATRIX: 1075->1|1325->181|1384->229|1398->235|1479->239|1507->241|1526->251|1591->295|1620->296|1751->178|1779->227|1807->389|1836->391|1876->404|1903->410|1937->418|1965->425|1993->426
                  LINES: 28->1|36->8|38->10|38->10|40->10|41->11|41->11|41->11|41->11|43->6|45->9|46->12|48->14|48->14|48->14|49->15|49->15|50->16
                  -- GENERATED --
              */
          