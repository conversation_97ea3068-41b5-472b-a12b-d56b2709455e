
package na.reference.data.views.html.skeletons

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object detailsInnerSkeleton extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template5[na.catalog.basemodule.models.nossisinv.attributes.UIContainer,na.catalog.basemodule.ui.WebComponent,na.reference.data.utils.views.CatalogInstanceDetails,na.reference.data.utils.views.skeletons.HeaderSkeletonHelper,na.reference.data.services.UIAttributesService,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        uiContainer: na.catalog.basemodule.models.nossisinv.attributes.UIContainer,
        element: na.catalog.basemodule.ui.WebComponent,
        catalogInstanceDetails: na.reference.data.utils.views.CatalogInstanceDetails,
        headerSkeletonHelper: na.reference.data.utils.views.skeletons.HeaderSkeletonHelper,
        uiAttributesService: na.reference.data.services.UIAttributesService
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*9.2*/import na.catalog.basemodule.ui.Action
/*10.2*/import na.reference.data.settings.ReferenceDataConstants

def /*12.2*/characteristicsTabContent/*12.27*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*12.31*/("""
"""),_display_(/*13.2*/uiAttributesService/*13.21*/.drawEntityAttributes(
    uiContainer,
    uiContainer.getUiContext.getUIGroup(ReferenceDataConstants.GROUP_SPECIFIC_CHARACTERISTICS).getUISubGroup(ReferenceDataConstants.GROUP_BASE_CHARACTERISTICS)
)),format.raw/*16.2*/("""
""")))};def /*19.2*/specificCharacteristicsTabContent/*19.35*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*19.39*/("""
"""),_display_(/*20.2*/uiAttributesService/*20.21*/.drawEntityAttributes(
    uiContainer,
    uiContainer.getUiContext.getUIGroup(ReferenceDataConstants.GROUP_SPECIFIC_CHARACTERISTICS).getUISubGroup(ReferenceDataConstants.GROUP_SPECIFIC_CHARACTERISTICS)
)),format.raw/*23.2*/("""
""")))};def /*26.2*/contentForm/*26.13*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*26.17*/("""
"""),_display_(/*27.2*/na/*27.4*/.reference.data.views.html.skeletons.mainContentSkeleton.render(
    characteristicsTabContent,
    specificCharacteristicsTabContent
)),format.raw/*30.2*/("""
""")))};
Seq[Any](format.raw/*7.2*/("""

"""),format.raw/*11.1*/("""
"""),format.raw/*17.2*/("""

"""),format.raw/*24.2*/("""

"""),format.raw/*31.2*/("""

"""),format.raw/*33.1*/("""<"""),_display_(/*33.3*/catalogInstanceDetails/*33.25*/.getDOMFormTag),format.raw/*33.39*/(""" """),format.raw/*33.40*/("""class="wrapper__content">
    <div class="fx-entity-header">
        <div class="fx-entity-header-info">
            <span class="fx-entity-header-icon">
                <i class="fuxicons fuxicons-catalogue"></i>
            </span>
            """),_display_(/*39.14*/if(element.getAction.equals(Action.VIEW) || element.getAction.equals(Action.EDIT))/*39.96*/ {_display_(Seq[Any](format.raw/*39.98*/("""
                """),format.raw/*40.17*/("""<div class="fx-entity-header-title">
                    <h1 id="entity-title">"""),_display_(/*41.44*/element/*41.51*/.getValue),format.raw/*41.60*/("""</h1>
                </div>
            """)))}/*43.15*/else/*43.20*/{_display_(Seq[Any](format.raw/*43.21*/("""
                """),_display_(/*44.18*/element/*44.25*/.render()),format.raw/*44.34*/("""
            """)))}),format.raw/*45.14*/("""
        """),format.raw/*46.9*/("""</div>
        <div class="fx-entity-header-actions">
            """),_display_(/*48.14*/headerSkeletonHelper/*48.34*/.getActionsTemplate(uiContainer.getUiContext.getAction, uiContainer.getCatEntity.getName)),format.raw/*48.123*/("""
        """),format.raw/*49.9*/("""</div>
    </div>
"""),_display_(/*51.2*/contentForm),format.raw/*51.13*/("""
"""),format.raw/*52.1*/("""</"""),_display_(/*52.4*/catalogInstanceDetails/*52.26*/.getDOMFormTag),format.raw/*52.40*/(""">"""))
      }
    }
  }

  def render(uiContainer:na.catalog.basemodule.models.nossisinv.attributes.UIContainer,element:na.catalog.basemodule.ui.WebComponent,catalogInstanceDetails:na.reference.data.utils.views.CatalogInstanceDetails,headerSkeletonHelper:na.reference.data.utils.views.skeletons.HeaderSkeletonHelper,uiAttributesService:na.reference.data.services.UIAttributesService): play.twirl.api.HtmlFormat.Appendable = apply(uiContainer,element,catalogInstanceDetails,headerSkeletonHelper,uiAttributesService)

  def f:((na.catalog.basemodule.models.nossisinv.attributes.UIContainer,na.catalog.basemodule.ui.WebComponent,na.reference.data.utils.views.CatalogInstanceDetails,na.reference.data.utils.views.skeletons.HeaderSkeletonHelper,na.reference.data.services.UIAttributesService) => play.twirl.api.HtmlFormat.Appendable) = (uiContainer,element,catalogInstanceDetails,headerSkeletonHelper,uiAttributesService) => apply(uiContainer,element,catalogInstanceDetails,headerSkeletonHelper,uiAttributesService)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:50:04 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/skeletons/detailsInnerSkeleton.scala.html
                  HASH: f551d9945499b95f4bb7e85656e3acb9c87fd4db
                  MATRIX: 1245->1|1715->401|1762->441|1832->500|1866->525|1947->529|1975->531|2003->550|2224->751|2249->756|2291->789|2372->793|2400->795|2428->814|2653->1019|2678->1024|2698->1035|2779->1039|2807->1041|2817->1043|2972->1178|3012->398|3041->498|3069->753|3098->1021|3127->1180|3156->1182|3184->1184|3215->1206|3250->1220|3279->1221|3553->1468|3644->1550|3684->1552|3729->1569|3836->1649|3852->1656|3882->1665|3943->1708|3956->1713|3995->1714|4040->1732|4056->1739|4086->1748|4131->1762|4167->1771|4261->1838|4290->1858|4401->1947|4437->1956|4482->1975|4514->1986|4542->1987|4571->1990|4602->2012|4637->2026
                  LINES: 28->1|37->9|38->10|40->12|40->12|42->12|43->13|43->13|46->16|47->19|47->19|49->19|50->20|50->20|53->23|54->26|54->26|56->26|57->27|57->27|60->30|62->7|64->11|65->17|67->24|69->31|71->33|71->33|71->33|71->33|71->33|77->39|77->39|77->39|78->40|79->41|79->41|79->41|81->43|81->43|81->43|82->44|82->44|82->44|83->45|84->46|86->48|86->48|86->48|87->49|89->51|89->51|90->52|90->52|90->52|90->52
                  -- GENERATED --
              */
          