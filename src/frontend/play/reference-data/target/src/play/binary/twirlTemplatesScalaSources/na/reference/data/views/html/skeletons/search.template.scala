
package na.reference.data.views.html.skeletons

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object search extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[String,na.catalog.basemodule.ui.WebComponent,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        entityName: String,
        element: na.catalog.basemodule.ui.WebComponent
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*6.2*/import na.reference.data.utils.views.FormFieldContainerLocation
/*7.2*/import na.reference.data.views.html.search.submitFormButtons
/*8.2*/import na.reference.data.views.html.skeletons.searchFormSkeleton

def /*10.2*/bottomButtons/*10.15*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*10.19*/("""
    """),format.raw/*11.5*/("""<div class="fx-main-searchbox-item-search clearfix">
        <div class="pull-right">
        """),_display_(/*13.10*/submitFormButtons/*13.27*/.render()),format.raw/*13.36*/("""
        """),format.raw/*14.9*/("""</div>
    </div>
""")))};def /*18.2*/fieldLocation/*18.15*/ = {{
    FormFieldContainerLocation.DASHBOARD
}};
Seq[Any](format.raw/*4.2*/("""

"""),format.raw/*9.1*/("""
"""),format.raw/*16.2*/("""

"""),format.raw/*20.2*/("""

"""),format.raw/*22.1*/("""<div id="fx-entity-content" class="fx-entity-info">
    <div class="row">
        <div class="col-xs-12 fx-tabbed-search-container-inner">
            <div
            class="fx-main-searchbox tab-content nav-tabs-stacked-left-content"
            data-na-portal-reference-data-entity-search-form
            data-na-portal-reference-data-entity-search-form-location=""""),_display_(/*28.73*/fieldLocation),format.raw/*28.86*/(""""
            >
            """),_display_(/*30.14*/searchFormSkeleton/*30.32*/.render(
                entityName = entityName,
                bottomButtons = bottomButtons,
                element = element,
                fieldLocation = fieldLocation
            )),format.raw/*35.14*/("""
            """),format.raw/*36.13*/("""</div>
        </div>
    </div>
</div>"""))
      }
    }
  }

  def render(entityName:String,element:na.catalog.basemodule.ui.WebComponent): play.twirl.api.HtmlFormat.Appendable = apply(entityName,element)

  def f:((String,na.catalog.basemodule.ui.WebComponent) => play.twirl.api.HtmlFormat.Appendable) = (entityName,element) => apply(entityName,element)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 11:21:55 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/skeletons/search.scala.html
                  HASH: 48a915012b290dc93e03e5e7ce9b4bdc76181a8c
                  MATRIX: 1015->1|1174->90|1245->155|1313->217|1391->284|1413->297|1494->301|1526->306|1648->401|1674->418|1704->427|1740->436|1782->458|1804->471|1881->87|1909->282|1937->455|1966->519|1995->521|2391->890|2425->903|2481->932|2508->950|2720->1141|2761->1154
                  LINES: 28->1|34->6|35->7|36->8|38->10|38->10|40->10|41->11|43->13|43->13|43->13|44->14|46->18|46->18|49->4|51->9|52->16|54->20|56->22|62->28|62->28|64->30|64->30|69->35|70->36
                  -- GENERATED --
              */
          