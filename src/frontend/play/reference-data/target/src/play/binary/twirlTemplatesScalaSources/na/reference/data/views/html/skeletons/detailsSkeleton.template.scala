
package na.reference.data.views.html.skeletons

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object detailsSkeleton extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template5[na.catalog.basemodule.models.nossisinv.attributes.UIContainer,na.catalog.basemodule.ui.WebComponent,na.reference.data.utils.views.CatalogInstanceDetails,na.reference.data.utils.views.skeletons.HeaderSkeletonHelper,na.reference.data.services.UIAttributesService,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        uiContainer: na.catalog.basemodule.models.nossisinv.attributes.UIContainer,
        element: na.catalog.basemodule.ui.WebComponent,
        catalogInstanceDetails: na.reference.data.utils.views.CatalogInstanceDetails,
        headerSkeletonHelper: na.reference.data.utils.views.skeletons.HeaderSkeletonHelper,
        uiAttributesService: na.reference.data.services.UIAttributesService
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*9.2*/import na.reference.data.settings.ReferenceDataConstants
/*10.2*/import na.reference.data.views.html.components.leftSideNavigationBarComponents


Seq[Any](format.raw/*7.2*/("""

"""),format.raw/*11.1*/("""
"""),_display_(/*12.2*/leftSideNavigationBarComponents/*12.33*/.render(ReferenceDataConstants.CONTEXT_CATALOG)),format.raw/*12.80*/("""

"""),format.raw/*14.1*/("""<"""),_display_(/*14.3*/catalogInstanceDetails/*14.25*/.getDOMPageTag),format.raw/*14.39*/(""" """),format.raw/*14.40*/("""class="wrapper wrapper--details-page fx-main-content-wrapper fx-with-main-nav" """),_display_(/*14.120*/catalogInstanceDetails/*14.142*/.getDomElementAttributes),format.raw/*14.166*/(""">
    """),_display_(/*15.6*/na/*15.8*/.reference.data.views.html.skeletons.detailsInnerSkeleton.render(
        uiContainer = uiContainer,
        element = element,
        catalogInstanceDetails = catalogInstanceDetails,
        headerSkeletonHelper = headerSkeletonHelper,
        uiAttributesService = uiAttributesService
    )),format.raw/*21.6*/("""
"""),format.raw/*22.1*/("""</"""),_display_(/*22.4*/catalogInstanceDetails/*22.26*/.getDOMPageTag),format.raw/*22.40*/(""">"""))
      }
    }
  }

  def render(uiContainer:na.catalog.basemodule.models.nossisinv.attributes.UIContainer,element:na.catalog.basemodule.ui.WebComponent,catalogInstanceDetails:na.reference.data.utils.views.CatalogInstanceDetails,headerSkeletonHelper:na.reference.data.utils.views.skeletons.HeaderSkeletonHelper,uiAttributesService:na.reference.data.services.UIAttributesService): play.twirl.api.HtmlFormat.Appendable = apply(uiContainer,element,catalogInstanceDetails,headerSkeletonHelper,uiAttributesService)

  def f:((na.catalog.basemodule.models.nossisinv.attributes.UIContainer,na.catalog.basemodule.ui.WebComponent,na.reference.data.utils.views.CatalogInstanceDetails,na.reference.data.utils.views.skeletons.HeaderSkeletonHelper,na.reference.data.services.UIAttributesService) => play.twirl.api.HtmlFormat.Appendable) = (uiContainer,element,catalogInstanceDetails,headerSkeletonHelper,uiAttributesService) => apply(uiContainer,element,catalogInstanceDetails,headerSkeletonHelper,uiAttributesService)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:50:04 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/skeletons/detailsSkeleton.scala.html
                  HASH: 2a355da7ec7b9eb6925f2e4df51b7a60f06d8f27
                  MATRIX: 1240->1|1710->401|1775->459|1882->398|1911->538|1939->540|1979->571|2047->618|2076->620|2104->622|2135->644|2170->658|2199->659|2307->739|2339->761|2385->785|2418->792|2428->794|2741->1087|2769->1088|2798->1091|2829->1113|2864->1127
                  LINES: 28->1|37->9|38->10|41->7|43->11|44->12|44->12|44->12|46->14|46->14|46->14|46->14|46->14|46->14|46->14|46->14|47->15|47->15|53->21|54->22|54->22|54->22|54->22
                  -- GENERATED --
              */
          