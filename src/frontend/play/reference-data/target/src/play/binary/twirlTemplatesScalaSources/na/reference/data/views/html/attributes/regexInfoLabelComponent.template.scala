
package na.reference.data.views.html.attributes

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object regexInfoLabelComponent extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(regexLabel: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.22*/("""

"""),format.raw/*3.1*/("""<p class="fx-form-info-label">
    """),_display_(/*4.6*/regexLabel),format.raw/*4.16*/("""
"""),format.raw/*5.1*/("""</p>"""))
      }
    }
  }

  def render(regexLabel:String): play.twirl.api.HtmlFormat.Appendable = apply(regexLabel)

  def f:((String) => play.twirl.api.HtmlFormat.Appendable) = (regexLabel) => apply(regexLabel)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Tue Jul 01 09:50:04 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/attributes/regexInfoLabelComponent.scala.html
                  HASH: 0349be9a1dcb70924e8d152eb6650c3282348b30
                  MATRIX: 995->1|1110->21|1138->23|1199->59|1229->69|1256->70
                  LINES: 28->1|33->1|35->3|36->4|36->4|37->5
                  -- GENERATED --
              */
          