include 'na-portal-frontend-basemodule'
include 'na-portal-frontend-catalog-basemodule'
include 'na-portal-frontend-router'
include 'na-portal-frontend-portal'
include 'na-portal-frontend-operations-catalog'
include 'na-portal-frontend-reference-data'
include 'na-portal-frontend-monitoring'
include 'na-portal-frontend-mpt'
include 'na-portal-frontend-go'
include 'na-portal-frontend-nadm'
include 'na-portal-frontend-diagnostics'

rootProject.name = 'na-portal-frontend'

project(':na-portal-frontend-basemodule').projectDir = new File('basemodule')
project(':na-portal-frontend-catalog-basemodule').projectDir = new File('catalog-basemodule')
project(':na-portal-frontend-router').projectDir = new File('router')
project(':na-portal-frontend-portal').projectDir = new File('portal')
project(':na-portal-frontend-operations-catalog').projectDir = new File('operations-catalog')
project(':na-portal-frontend-reference-data').projectDir = new File('reference-data')
project(':na-portal-frontend-monitoring').projectDir = new File('monitoring')
project(':na-portal-frontend-mpt').projectDir = new File('mpt')
project(':na-portal-frontend-go').projectDir = new File('go')
project(':na-portal-frontend-nadm').projectDir = new File('nadm')
project(':na-portal-frontend-diagnostics').projectDir = new File('diagnostics')