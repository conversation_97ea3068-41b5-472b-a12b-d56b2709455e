<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>pt.ptinovacao.na-portal</groupId>
        <artifactId>na-portal-ref-data</artifactId>
        <version>100.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>na-portal-ref-data-core</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-ref-data-messaging-api</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.nossis-inv</groupId>
            <artifactId>nossis-inv-backend-core-http-api-rest</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.nossis-inv</groupId>
            <artifactId>nossis-inv-backend-core-coding</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.nossis-inv</groupId>
            <artifactId>nossis-inv-kernel-http-api-rest</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.nossis-inv</groupId>
            <artifactId>nossis-inv-backend-entities-catalog</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.nossis-inv</groupId>
            <artifactId>nossis-inv-kernel-monitoring</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.nossis-inv</groupId>
            <artifactId>nossis-inv-kernel-catalog-client-api</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.nossis-inv</groupId>
            <artifactId>nossis-inv-backend-core-persistence-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-commons</groupId>
            <artifactId>na-commons-interfaces-na-ws-api</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-commons</groupId>
            <artifactId>na-commons-na-cli-client</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na</groupId>
            <artifactId>na-core-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.asf</groupId>
            <artifactId>asf-components-manager</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.asf</groupId>
            <artifactId>asf-configuration</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.nossis-frmwrk</groupId>
            <artifactId>exceptions</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>xml-apis</groupId>
            <artifactId>xml-apis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.ws.rs</groupId>
            <artifactId>javax.ws.rs-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.glassfish.hk2.external</groupId>
            <artifactId>javax.inject</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.persistence</groupId>
            <artifactId>javax.persistence-api</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
        </dependency>

        <!-- TEST -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>
        <dependency>
            <groupId>xerces</groupId>
            <artifactId>xercesImpl</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
