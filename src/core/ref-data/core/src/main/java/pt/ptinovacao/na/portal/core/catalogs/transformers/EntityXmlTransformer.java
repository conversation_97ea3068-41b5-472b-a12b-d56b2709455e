package pt.ptinovacao.na.portal.core.catalogs.transformers;

import com.google.common.base.CharMatcher;
import com.google.common.base.Function;
import com.google.common.base.Predicates;
import com.google.common.base.Throwables;
import com.google.common.collect.FluentIterable;
import com.google.common.primitives.Longs;
import org.w3c.dom.DOMException;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import pt.ptinovacao.na.core.xml.XMLSerializer;
import pt.ptinovacao.na.portal.core.catalogs.exceptions.NaCatalogsException;
import pt.ptinovacao.netwin.backend.entities.abstractentities.AbstractBusinessEntityWithDynAttrEmbedFlat;
import pt.ptinovacao.netwin.kernel.catalog.client.model.CatAttributeType;
import pt.ptinovacao.netwin.kernel.catalog.client.model.CatEntity;
import pt.ptinovacao.netwin.kernel.catalog.client.model.CatInsAttribute;

import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Karamanos <<EMAIL>>
 */
public class EntityXmlTransformer<T extends AbstractBusinessEntityWithDynAttrEmbedFlat>
        implements EntityTransformerItf<T> {

    private final Document document;
    private final Element rootElement;
    private final CatEntity catEntity;
    private final Iterable<T> entitiesIterable;
    private final boolean declaration;

    public EntityXmlTransformer(CatEntity catEntity, Iterable<T> entitiesIterable, boolean declaration) throws NaCatalogsException {
        try {
            this.document = DocumentBuilderFactory.newInstance().newDocumentBuilder().newDocument();
            this.rootElement = this.document.createElement(catEntity.getName());
            this.catEntity = catEntity;
            this.document.appendChild(this.rootElement);
            this.entitiesIterable = entitiesIterable;
            this.declaration = declaration;
        } catch (ParserConfigurationException ex) {
            throw new NaCatalogsException(ex.getMessage());
        }
    }

    @Override
    public String getRootName() {
        return rootElement.getNodeName();
    }

    private Function<T, Element> transformerFunction
            = new Function<T, Element>() {
        @Override
        public Element apply(T input) {
            try {
                Element attrsElement = EntityXmlTransformer.this.document.createElement(input.getName());
                for (Map.Entry<String, String> en : input.getDynAttributesMap().entrySet()) {
                    Element e = EntityXmlTransformer.this.document.createElement(en.getKey());

                    String valueToExport = en.getValue();
                    CatInsAttribute catInsAttribute = EntityXmlTransformer.this.catEntity.getAttribute(en.getKey());
                    if (catInsAttribute != null) {
                        CatAttributeType catAttributeType = catInsAttribute.getAttributeType();
                        if (valueToExport != null && catAttributeType.canAddEnumerated() && Longs.tryParse(valueToExport) != null) {
                            valueToExport = catAttributeType.getCatEnumeratedAttributeValueById(Longs.tryParse(en.getValue())).getValue();
                        }
                    }

                    Node child = validateXMLChars(e, valueToExport);
                    if (child != null) {
                        attrsElement.appendChild(child);
                    }
                }

                return attrsElement;
            } catch (DOMException e) {
                LOGGER.error("Cannot generate XML child element for entity '"
                        + input.getName() + "' due to: " + e.getMessage(), e);
            }

            return null;
        }
    };

    private Node validateXMLChars(Element e, String value) {
        if (value != null) {
            if (CharMatcher.anyOf(value).matchesAnyOf("&<>\'\"")) {
                return e.appendChild(EntityXmlTransformer.this.document.createCDATASection(value)).getParentNode();
            } else {
                e.setTextContent(value);
                return e;
            }
        }
        return null;
    }

    @Override
    public String transform() throws NaCatalogsException {
        try {
            List<Element> elements = FluentIterable
                    .from(this.entitiesIterable)
                    .transform(this.transformerFunction)
                    .filter(Predicates.notNull())
                    .toList();

            for (Element element : elements) {
                this.rootElement.appendChild(element);
            }

            return XMLSerializer.serialize(this.rootElement, this.declaration);
        } catch (Throwable t) {
            LOGGER.error(t.getMessage(), t);
            throw new NaCatalogsException(Throwables.getRootCause(t).getMessage());
        }
    }
}
