package pt.ptinovacao.na.portal.core.catalogs.implementation.servicescomponents.validators;

import pt.ptinovacao.asf.componentsmanager.ComponentInit;
import pt.ptinovacao.na.portal.core.catalogs.entities.NaCatalog;
import pt.ptinovacao.na.portal.core.catalogs.exceptions.NaCatalogsExceptionCode;
import pt.ptinovacao.netwin.backend.core.coding.Coding;
import pt.ptinovacao.netwin.backend.core.persistence.interf.Persistence;
import pt.ptinovacao.netwin.backend.core.persistence.validator.CatalogValidator;
import pt.ptinovacao.netwin.backend.entities.abstractentities.AbstractEntity;
import pt.ptinovacao.nossis.exceptions.BusinessException;

/**
 * Created by 10049369 on 30/05/2016.
 */
public class NaCatalogValidator extends CatalogValidator implements ComponentInit {

    public static final String COMPONENT_NAME = "persistence.validator.NA_CATALOG";

    @Override
    public void init() {
    }

    @Override
    public <T extends AbstractEntity> void validateCreate(Persistence p, T entity) {
        super.validateCreate(p, entity);
        validateAttributes(p, entity);
    }

    @Override
    public <T extends AbstractEntity> void validateUpdate(Persistence p, T entity) {
        super.validateUpdate(p, entity);
        validateAttributes(p, entity);
    }

    private <T extends AbstractEntity> void validateAttributes(Persistence p, T entity) {
        if (entity instanceof  NaCatalog) {
            NaCatalog naCatalog = (NaCatalog) entity;
            try {
                Coding.validateAttributes(p, naCatalog);
            } catch (BusinessException e) {
                if (e.getExceptionParameters().length == 1 && "name".equals(e.getExceptionParameters()[0].getValue())) {
                    throw new BusinessException(NaCatalogsExceptionCode.NOT_UNIQUE_NAME, e, "name", naCatalog.getName());
                }
            }
        }
    }
}
