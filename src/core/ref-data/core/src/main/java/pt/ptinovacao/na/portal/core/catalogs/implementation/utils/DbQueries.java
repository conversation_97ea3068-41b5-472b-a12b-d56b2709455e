package pt.ptinovacao.na.portal.core.catalogs.implementation.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.ptinovacao.asf.configuration.ConfigProperties;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.ResourceBundle;

public class DbQueries {

    private static final Logger LOGGER = LoggerFactory.getLogger(DbQueries.class);
    private static volatile DbQueries INSTANCE = null;
    private static final Object MUTEX = new Object();

    public static DbQueries getInstance() {
        if (INSTANCE == null) {
            synchronized (MUTEX) {
                if (INSTANCE == null) {
                    INSTANCE = new DbQueries();
                }
            }
        }
        return INSTANCE;
    }

    private final Map<String, String> queries;

    private DbQueries() {
        Map<String, String> queries = new HashMap<>();
        String dbType = ConfigProperties.get("persistence.db.type", "oracle", String.class);
        ResourceBundle bundle = ResourceBundle.getBundle("nativequeries-" + dbType);
        for (String key : bundle.keySet()) {
            queries.put(key, bundle.getString(key));
        }
        this.queries = Collections.unmodifiableMap(queries);
    }

    public String getQuery(String name) {
        if (!queries.containsKey(name)) {
            throw new IllegalArgumentException("Invalid query '" + name + "'");
        }
        String query = queries.get(name);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(name + ": '" + query + "'");
        }
        return query;
    }
}
