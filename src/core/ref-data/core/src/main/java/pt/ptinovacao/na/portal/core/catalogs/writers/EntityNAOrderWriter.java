package pt.ptinovacao.na.portal.core.catalogs.writers;

import com.google.common.base.Throwables;

import java.util.Properties;
import java.util.concurrent.TimeUnit;

import pt.ptinovacao.na.client.ws.OrderData;

import static pt.ptinovacao.na.portal.core.catalogs.na.Commons.LR_NAME_KEY;
import static pt.ptinovacao.na.portal.core.catalogs.na.Commons.LR_TYPE_KEY;
import static pt.ptinovacao.na.portal.core.catalogs.na.Commons.LR_VERSION_KEY;
import static pt.ptinovacao.na.portal.core.catalogs.na.Commons.MANAGEMENT_CONTEXT_KEY;
import static pt.ptinovacao.na.portal.core.catalogs.na.Commons.MANAGEMENT_METHOD_KEY;
import static pt.ptinovacao.na.portal.core.catalogs.na.Commons.RFS_NAME_KEY;
import static pt.ptinovacao.na.portal.core.catalogs.na.Commons.RFS_TYPE_KEY;
import static pt.ptinovacao.na.portal.core.catalogs.na.Commons.RFS_VERSION_KEY;
import static pt.ptinovacao.na.portal.core.catalogs.na.Commons.BIND_ADDRESS_KEY;
import static pt.ptinovacao.na.portal.core.catalogs.na.Commons.TIMEOUT_KEY;
import static pt.ptinovacao.na.portal.core.catalogs.na.Commons.URL_KEY;
import static pt.ptinovacao.na.portal.core.catalogs.na.Commons.OPERATION_KEY;

import pt.ptinovacao.na.portal.core.catalogs.exceptions.NaCatalogsException;
import pt.ptinovacao.na.portal.core.catalogs.na.Commons;
import pt.ptinovacao.na.portal.core.catalogs.na.NAFactory;
import pt.ptinovacao.na.portal.core.catalogs.transformers.EntityTransformerItf;

import pt.ptinovacao.netwin.backend.entities.abstractentities.AbstractBusinessEntityWithDynAttrEmbedFlat;

/**
 *
 * <AUTHOR> Karamanos <<EMAIL>>
 */
public class EntityNAOrderWriter<T extends AbstractBusinessEntityWithDynAttrEmbedFlat> implements EntityWriterItf<T> {

    private static final int PRIORITY = 1;

    private OrderData reply;

    @Override
    public void write(EntityTransformerItf<T> entityTransformer, Properties properties) throws NaCatalogsException {
        try {
            String orderInfo = NAFactory.createWriteCatalogNAOrderInfo()
                    .setRfsName(properties.getProperty(RFS_NAME_KEY))
                    .setRfsType(properties.getProperty(RFS_TYPE_KEY))
                    .setRfsVersion(properties.getProperty(RFS_VERSION_KEY))
                    .setLrName(properties.getProperty(LR_NAME_KEY))
                    .setLrType(properties.getProperty(LR_TYPE_KEY))
                    .setLrVersion(properties.getProperty(LR_VERSION_KEY))
                    .setManagementContext(properties.getProperty(MANAGEMENT_CONTEXT_KEY))
                    .setManagementMethod(properties.getProperty(MANAGEMENT_METHOD_KEY))
                    .setInfoName(entityTransformer.getRootName())
                    .setInfoPayload(entityTransformer.transform().toString())
                    .build();

            LOGGER.info("Generated the following order info: \n" + orderInfo);

            // Sent to NA Client
            this.reply = NAFactory.createNaClient(properties.getProperty(BIND_ADDRESS_KEY))
                    .execute(properties.getProperty(URL_KEY), properties.getProperty(OPERATION_KEY), orderInfo,
                            this.getClass().getSimpleName() + "_" + entityTransformer.getRootName()
                            + "_" + System.currentTimeMillis(), PRIORITY, null, null, null);

            LOGGER.info("[" + this.getClass().getSimpleName() + "] [OPK "
                    + this.reply.getAsyncOrderKey().getPrimaryKey()
                    + "] Order sent ASYNC way...");

            // Retrieve the computation result
            this.reply.get(Long.parseLong(properties.getProperty(TIMEOUT_KEY)), TimeUnit.MILLISECONDS);

            // LOG the computation results with statistics
            LOGGER.info("[" + this.getClass().getSimpleName() + "] [OPK "
                    + this.reply.getAsyncOrderKey().getPrimaryKey()
                    + "] [Request time " + (System.currentTimeMillis() - this.reply.getRequestTime()) + " ms] [Response time "
                    + (System.currentTimeMillis() - this.reply.getResponseTime()) + " ms]- Order Result:\n"
                    + Commons.serialize(this.reply.getAsyncOrderResponse()));
        } catch (Exception e) {
            LOGGER.error(Throwables.getRootCause(e).getMessage(), e);
            throw new NaCatalogsException(Throwables.getRootCause(e).getMessage());
        }
    }

    /**
     * Releases the OrderData internal latch just in case.
     *
     * @throws Exception
     */
    @Override
    public void close() throws Exception {
        LOGGER.info("Closing a " + this.getClass().getSimpleName() + " instance");

        if (this.reply != null && !this.reply.isDone()) {
            this.reply.cancel(true);
        }
    }
}
