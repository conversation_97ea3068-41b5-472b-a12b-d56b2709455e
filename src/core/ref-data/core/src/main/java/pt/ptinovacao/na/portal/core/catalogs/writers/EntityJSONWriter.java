package pt.ptinovacao.na.portal.core.catalogs.writers;

import java.util.Properties;

import pt.ptinovacao.na.portal.core.catalogs.exceptions.NaCatalogsException;
import pt.ptinovacao.na.portal.core.catalogs.transformers.EntityTransformerItf;

import pt.ptinovacao.netwin.backend.entities.abstractentities.AbstractBusinessEntityWithDynAttrEmbedFlat;

/**
 * TODO.
 *
 * <AUTHOR> <<EMAIL>>
 */
public class EntityJSONWriter<T extends AbstractBusinessEntityWithDynAttrEmbedFlat> implements EntityWriterItf<T> {

    @Override
    public void write(EntityTransformerItf<T> entityTransformer, Properties properties) throws NaCatalogsException {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public void close() throws Exception {
        LOGGER.info("Closing a " + this.getClass().getSimpleName() + " instance");
    }
}
