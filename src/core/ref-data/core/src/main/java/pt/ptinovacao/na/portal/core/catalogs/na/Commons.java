package pt.ptinovacao.na.portal.core.catalogs.na;

import java.io.StringWriter;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBElement;
import javax.xml.bind.JAXBException;
import javax.xml.namespace.QName;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import pt.ptinovacao.agorang.na.ws.IllegalOrderException;
import pt.ptinovacao.agorang.na.ws.OrderValue;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
public final class Commons {

    private static final Logger LOGGER = LoggerFactory.getLogger(Commons.class);

    public static final String RFS_NAME_KEY = "rfsName";
    public static final String RFS_TYPE_KEY = "rfsType";
    public static final String RFS_VERSION_KEY = "rfsVersion";
    public static final String LR_NAME_KEY = "lrName";
    public static final String LR_TYPE_KEY = "lrType";
    public static final String LR_VERSION_KEY = "lrVersion";
    public static final String MANAGEMENT_METHOD_KEY = "managementMethod";
    public static final String MANAGEMENT_CONTEXT_KEY = "managementContext";
    public static final String BIND_ADDRESS_KEY = "bindAddress";
    public static final String URL_KEY = "url";
    public static final String TIMEOUT_KEY = "timeout";
    public static final String OPERATION_KEY = "operation";

    private static JAXBContext JAXB_CTX;

    static {
        try {
            JAXB_CTX = JAXBContext.newInstance(
                    OrderValue.class,
                    pt.ptinovacao.agorang.na.ws.response.client.OrderResponse.class,
                    pt.ptinovacao.agorang.na.ws.OrderResponse.class,
                    IllegalOrderException.class
            );
        } catch (JAXBException e) {
            LOGGER.error("JAXB initialization failed: " + e.getMessage(), e);
        }
    }

    private Commons() {
    }

    /**
     * Serialize an object so it can be represented in the log.
     *
     * @param object object instance to serialize
     * @return serialized object
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public static String serialize(Object object) {
        String output = null;
        if (object instanceof String) {
            output = (String) object;
        } else if (object instanceof OrderValue
                || object instanceof pt.ptinovacao.agorang.na.ws.response.client.OrderResponse
                || object instanceof pt.ptinovacao.agorang.na.ws.OrderResponse
                || object instanceof IllegalOrderException) {
            try {
                if (JAXB_CTX != null) {
                    StringWriter buffer = new StringWriter();
                    JAXB_CTX.createMarshaller().marshal(new JAXBElement(new QName("", object.getClass().getSimpleName()), object.getClass(), object), buffer);
                    output = buffer.toString();
                } else {
                    throw new JAXBException("JAXB context not initialized");
                }
            } catch (JAXBException e) {
                output = "object serialization failed: " + e.getMessage();
                LOGGER.error(output, e);
            }
        } else if (object != null) {
            output = String.valueOf(object);
        }

        return output;
    }
}
