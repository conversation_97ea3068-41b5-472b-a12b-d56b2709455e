<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>na-portal-ref-data</artifactId>
        <groupId>pt.ptinovacao.na-portal</groupId>
        <version>100.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>na-portal-ref-data-messaging-rabbitmq</artifactId>

    <dependencies>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-ref-data-messaging-api</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.nossis-inv</groupId>
            <artifactId>nossis-inv-backend-entities-catalog</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.asf</groupId>
            <artifactId>asf-components-manager</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.asf</groupId>
            <artifactId>asf-configuration</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.asf</groupId>
            <artifactId>asf-serializers</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.asf</groupId>
            <artifactId>asf-messaging-api</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-commons</groupId>
            <artifactId>na-commons-rabbitmq-channel-provider</artifactId>
        </dependency>
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
            <version>${version.org.scala-lang}</version>
        </dependency>
        <dependency>
            <groupId> org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.rabbitmq</groupId>
            <artifactId>amqp-client</artifactId>
        </dependency>
    </dependencies>

</project>