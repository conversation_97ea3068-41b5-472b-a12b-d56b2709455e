package pt.ptinovacao.na.portal.core.cache.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.net.UrlEscapers;
import pt.ptinovacao.asf.serializer.Serializer;
import pt.ptinovacao.na.portal.core.cache.api.HttpClient;

import javax.ws.rs.core.Response;
import java.util.Optional;

import static javax.ws.rs.core.Response.Status.fromStatusCode;

public class DefaultHttpClient implements HttpClient {
    private pt.ptinovacao.nacommons.http.client.HttpClient httpClient;

    public DefaultHttpClient() {
        this(Configs.getMaxCacheSize(),
                Configs.getMaxTotalConnections(),
                Configs.getMaxConnectionsPerHost(),
                Configs.getReadTimeout(),
                Configs.getConnectTimeout());
    }

    public DefaultHttpClient(int maxCacheSize, int maxTotalConnections, int maxConnectionsPerHost, int readTimeout, int connectTimeout) {
        this.httpClient = new pt.ptinovacao.nacommons.http.client.JerseyHttpClient(maxCacheSize,
                maxTotalConnections,
                maxConnectionsPerHost,
                readTimeout,
                connectTimeout);
    }

    @Override
    public Optional<JsonNode> get(String requestUrl) {
        Response response = null;
        try {
            response = httpClient.get(encodeURL(requestUrl));
            switch (fromStatusCode(response.getStatus())) {
                case OK:
                    return Optional.of(Serializer.fromJson(response.readEntity(String.class), JsonNode.class));
                default:
                    return Optional.empty();
            }
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    /**
     * URL Encoder: URL's must be encoded due to spaces and other special characters
     *
     * @param URL
     * @return encoded URL
     */
    private static String encodeURL(String URL) {
        return UrlEscapers.urlFragmentEscaper().escape(URL);
    }
}
