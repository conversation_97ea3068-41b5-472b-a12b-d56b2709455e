<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>na-portal-ref-data</artifactId>
        <groupId>pt.ptinovacao.na-portal</groupId>
        <version>10.55.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>na-portal-ref-data-cache-client</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-ref-data-cache-api</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-commons</groupId>
            <artifactId>na-commons-http-client-api</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.asf</groupId>
            <artifactId>asf-serializers</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-commons</groupId>
            <artifactId>na-commons-http-client-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>com.typesafe</groupId>
            <artifactId>config</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.ws.rs</groupId>
            <artifactId>javax.ws.rs-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
    </dependencies>

</project>