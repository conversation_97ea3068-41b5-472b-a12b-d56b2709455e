package pt.ptinovacao.na.portal.core.cache;

import pt.ptinovacao.na.portal.core.cache.connector.ZookeeperServiceInstance;
import pt.ptinovacao.na.portal.core.cache.connector.ZookeeperServiceInstanceInfo;

public class FakeZookeeperServiceInstance implements ZookeeperServiceInstance {

    final int port;

    public FakeZookeeperServiceInstance(final int port) {
        this.port = port;
    }

    @Override
    public ZookeeperServiceInstanceInfo getServiceInstance() {
        return new ZookeeperServiceInstanceInfo("http", "localhost", port);
    }
}