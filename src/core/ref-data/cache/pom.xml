<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>na-portal-ref-data</artifactId>
        <groupId>pt.ptinovacao.na-portal</groupId>
        <version>100.0.0</version>
    </parent>

    <artifactId>na-portal-ref-data-cache</artifactId>

    <dependencies>

        <!-- na-portal -->
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-ref-data-cache-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- ext -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.typesafe</groupId>
            <artifactId>config</artifactId>
            <version>${version.com.typesafe.config}</version>
        </dependency>

        <!--
        ****************************************************************************************************************
        *** Testing
        ****************************************************************************************************************
        -->

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>

        <!-- asf -->
        <dependency>
            <groupId>pt.ptinovacao.asf</groupId>
            <artifactId>asf-serializers</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- na-commons -->
        <dependency>
            <groupId>pt.ptinovacao.na-commons</groupId>
            <artifactId>na-commons-http-client-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-commons</groupId>
            <artifactId>na-commons-http-client-impl</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- na-portal -->
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-ref-data-cache-client</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- ext -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-simple</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.github.tomakehurst</groupId>
            <artifactId>wiremock-standalone</artifactId>
            <version>${version.com.github.tomakehurst}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-core</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>