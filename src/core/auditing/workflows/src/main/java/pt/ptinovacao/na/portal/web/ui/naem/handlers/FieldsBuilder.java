package pt.ptinovacao.na.portal.web.ui.naem.handlers;

import java.util.ArrayList;
import java.util.List;

import pt.ptinovacao.na.portal.web.ui.commons.Constants;

/**
 *
 * <AUTHOR> <michail-karama<PERSON>@telecom.pt>
 */
public final class FieldsBuilder {

  private FieldsBuilder() {
    //Hidden CTOR
  }

  public static List<String> filterName() {
    List<String> fields = new ArrayList<>();
    fields.add(Constants.VAR_FILTER_NAME);

    return fields;
  }
}
