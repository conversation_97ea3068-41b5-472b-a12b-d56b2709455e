package pt.ptinovacao.na.portal.web.ui.naem.workflows;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.ptinovacao.na.portal.web.ui.commons.Constants;
import pt.ptinovacao.na.portal.web.ui.naem.handlers.ExceptionHandler;
import pt.ptinovacao.na.portal.web.ui.naem.handlers.FieldsBuilder;
import pt.ptinovacao.na.portal.web.ui.naem.handlers.IntegrityCheck;
import pt.ptinovacao.na.portal.web.ui.naem.handlers.NullabilityCheck;
import pt.ptinovacao.na.portal.web.ui.naem.handlers.QueryBuilder;
import pt.ptinovacao.na.portal.web.ui.naem.schemas.WebUiRequest;
import pt.ptinovacao.na.portal.web.ui.naem.schemas.WebUiResponse;
import pt.ptinovacao.na.portal.web.ui.naem.schemas.WebUiResponse.Content;

import pt.ptinovacao.nacommons.plugins.dbmanager.core.DbManagerException;
import pt.ptinovacao.nacommons.plugins.dbmanager.core.RequestInfo;
import pt.ptinovacao.nacommons.plugins.dbmanager.core.connectors.DbProfile;
import pt.ptinovacao.nacommons.plugins.dbmanager.core.registries.DbRegistry;
import pt.ptinovacao.nacommons.plugins.dbmanager.core.workflow.ContextHandler;
import pt.ptinovacao.nacommons.utils.mongo.DbException;

import static pt.ptinovacao.na.portal.web.ui.naem.handlers.IntegrityCheck.checkUserFilterExistence;

/**
 * <AUTHOR> Karamanos <<EMAIL>>
 */
public class WebUiFilterContextHandler extends ContextHandler<WebUiRequest, WebUiResponse> {

    private static final Logger LOGGER = LoggerFactory.getLogger(WebUiFilterContextHandler.class);

    public WebUiFilterContextHandler() {
        super(Constants.CTX_FILTERS);
    }

    /**
     * Retrieve the database handler.
     *
     * @param properties
     * @return
     * @throws DbException
     */
    private DbProfile db(RequestInfo properties) throws DbException {
        return DbRegistry.getInstance().get(properties);
    }

    @Override
    public WebUiResponse delete(RequestInfo info, WebUiRequest request) {
        // create a filter response
        WebUiResponse response = WebUiCommons.FACTORY.createWebUiResponse();
        response.getContent().add(WebUiCommons.FACTORY.createWebUiResponseContent());
        response.getContent().get(0).setFiltername(request.getFiltername());
        response.setUsername(request.getUsername());
        response.setContext(request.getContext());

        // delete nullability check call. Also works as a method execution break...
        if (this.deleteNullabilityCheck(request, response, false)) {
            return response;
        }

        // Context integrity check
        if (!IntegrityCheck.checkContext(request, response, info).isPresent()) {
            return response;
        }

        Map<String, Object> query = QueryBuilder.filterByUserName_FilterName(
                request.getUsername(),
                request.getFiltername(),
                request.getContext().name());

        // Database delete
        try {
            this.db(info).delete(info, Constants.COLL_FILTERS, query);
        } catch (DbException ex) {
            try {
                return ExceptionHandler.responseWrapper(ex, request, response);
            } catch (Exception e) {
                LOGGER.error("Error while deleting filter: ", e);
                return ExceptionHandler.responseStatusFiller(
                        info,
                        response,
                        null,
                        ". ExceptionHandler. Reason: " + e.getMessage());
            }
        }

        // Status update and description
        return ExceptionHandler.responseStatusFiller(info, response, ExceptionHandler.OK, ". Filter deleted with success");
    }

    @Override
    public WebUiResponse update(RequestInfo info, WebUiRequest request) throws DbManagerException {
        // create a filter response
        WebUiResponse response = WebUiCommons.FACTORY.createWebUiResponse();
        WebUiRequest toUpdate = WebUiCommons.FACTORY.createWebUiRequest();

        // Context integrity check
        if (!IntegrityCheck.checkContext(request, response, info).isPresent()) {
            return response;
        }

        // update nullability check call. Also works as a method execution break...
        if (createNullabilityCheck(request, response)) {
            return response;
        }

        // Max length validation
        if (IntegrityCheck.exceedsMaxLength(request, response, info)) {
            return response;
        }

        // Database comm.
        try {
            Optional<Boolean> optional = checkUserFilterExistence(info, request, response);
            if (!optional.isPresent() || !optional.get()) {
                return ExceptionHandler.responseStatusFiller(info, response, ExceptionHandler.DOES_NOT_EXIST, null);
            }

            toUpdate.setContext(request.getContext());
            toUpdate.setUsername(request.getUsername());
            toUpdate.setFiltername(request.getFiltername());
            toUpdate.setGroup(request.getGroup());
            toUpdate.getFields().addAll(request.getFields());
            toUpdate.getMultiValueFields().addAll(request.getMultiValueFields());

            Map<String, Object> query = QueryBuilder.filterByUserName_FilterName(
                    toUpdate.getUsername(),
                    toUpdate.getFiltername(),
                    toUpdate.getContext().name());

            this.db(info).update(info, Constants.COLL_FILTERS, toUpdate, query);

        } catch (DbException ex) {
            try {
                return ExceptionHandler.responseWrapper(ex, request, response);
            } catch (Exception e) {
                LOGGER.error("Error while updating filter: ", e);
                return ExceptionHandler.responseStatusFiller(
                        info,
                        response,
                        null,
                        ". ExceptionHandler. Reason: " + e.getMessage());
            }
        }

        // Status update and description
        return ExceptionHandler.responseStatusFiller(info, response, ExceptionHandler.OK, ". Filter updated with success");
    }

    /**
     * READ OVERRIDE.
     *
     * @param info
     * @param request
     * @return
     * @throws DbManagerException
     */
    @Override
    public WebUiResponse read(RequestInfo info, WebUiRequest request) {
        // create a filter response
        WebUiResponse response = WebUiCommons.FACTORY.createWebUiResponse();
        response.setUsername(request.getUsername());
        response.setContext(request.getContext());

        // Context integrity check
        if (!IntegrityCheck.checkContext(request, response, info).isPresent()) {
            return response;
        }

        // Query parameters validation
        if (IntegrityCheck.hasInvalidQueryParams(info, response)) {
            return response;
        }

        // TODO: other parameters could be evaluated, such as pagination, etc...
        final List<String> qparam = info.getQueryParams().get(Constants.QUERY_PARAM_FILTERNAMES);
        if (qparam != null
                && !qparam.isEmpty()
                && !isStringNullOrEmpty(qparam.get(0))
                && Boolean.parseBoolean(qparam.get(0))) {

            info.logger().info("QUERYPARAM 'filternames' was found... Params passed:" + info.getQueryParams());

            Optional<List<WebUiRequest>> filternamesOpt;
            // Absent filter was returned so return the status and response to the caller
            if (!(filternamesOpt = this.readAllFilterNames(info, request, response)).isPresent()) {
                return response;
            }

            response.getContent().addAll(
                    filternamesOpt.get().stream()
                            .map(singleRequest -> {
                                WebUiResponse.Content content = WebUiCommons.FACTORY.createWebUiResponseContent();
                                content.setFiltername(singleRequest.getFiltername());

                                return content;
                            })
                            .collect(Collectors.toList())
            );

            return response;
        } else {
            // read nullability check call. Also works as a method execution break...
            if (this.readNullabilityCheck(request, response, false)) {
                info.logger().error("[readNullabilityCheck]");
                return response;
            }
        }

        Map<String, Object> query = QueryBuilder.filterByUserName_FilterName(
                request.getUsername(),
                request.getFiltername(),
                request.getContext().name());

        // Database read
        try {
            WebUiRequest toRead = this.db(info)
                    .readOne(info,
                            Constants.COLL_FILTERS,
                            WebUiRequest.class,
                            query);

            Content content = WebUiCommons.FACTORY.createWebUiResponseContent();
            content.getFields().addAll(toRead.getFields());
            content.getMultiValueFields().addAll(toRead.getMultiValueFields());
            content.setFiltername(toRead.getFiltername());
            content.setGroup(toRead.getGroup());
            response.getContent().add(content);
        } catch (DbException ex) {
            try {
                return ExceptionHandler.responseWrapper(ex, request, response);
            } catch (Exception e) {
                LOGGER.error("Error while reading filter: ", e);
                return ExceptionHandler.responseStatusFiller(
                        info,
                        response,
                        null,
                        ". ExceptionHandler. Reason: " + e.getMessage());
            }
        }

        // Status update and description
        return ExceptionHandler.responseStatusFiller(info, response, ExceptionHandler.OK, ". Filter read with success");
    }

    /**
     * Reads all the filtername for a given user and context.
     *
     * @param info
     * @param request
     * @param response
     * @return
     */
    public Optional<List<WebUiRequest>> readAllFilterNames(RequestInfo info, WebUiRequest request, WebUiResponse response) {
        // read nullability check call. Also works as a method execution break...
        if (this.readNullabilityCheck(request, response, true)) {
            return Optional.empty();
        }

        // Fetch all filters names and store them in the filters element of the response filter object
        List<WebUiRequest> filternames;
        try {
            if (info.getQueryParams().containsKey(Constants.VAR_FILTER_NAME)) {
                final List<String> qparam = info.getQueryParams().get(Constants.VAR_FILTER_NAME);
                // Query parameters content validation
                if (qparam == null
                        || qparam.isEmpty()
                        || isStringNullOrEmpty(qparam.get(0))) {
                    ExceptionHandler.responseStatusFiller(info, response, ExceptionHandler.INVALID_VALUES, " while parsing query parameters");
                    return Optional.empty();
                }

                Map<String, Object> query = QueryBuilder.queryByUserName_Filtername_Regex(
                        request.getUsername(),
                        request.getContext().name(),
                        qparam.get(0));

                // All went well so we do the thing yeahhh
                info.logger().warn("********************* USING REGEX QUERY PARAM **************************");
                filternames = this.db(info)
                        .readAll(info,
                                Constants.COLL_FILTERS,
                                WebUiRequest.class,
                                query,
                                FieldsBuilder.filterName());
            } else {
                Map<String, Object> query = QueryBuilder.queryByUserName(
                        request.getUsername(),
                        request.getContext().name());
                filternames = this.db(info)
                        .readAll(info,
                                Constants.COLL_FILTERS,
                                WebUiRequest.class,
                                query,
                                FieldsBuilder.filterName());
            }
        } catch (DbException ex) {
            try {
                ExceptionHandler.responseWrapper(ex, request, response);
            } catch (Exception e) {
                LOGGER.error("Error while reading all queried filters: ", e);
                ExceptionHandler.responseStatusFiller(
                        info,
                        response,
                        null,
                        ". ExceptionHandler. Reason: " + e.getMessage());
            }
            return Optional.empty();
        }

        return Optional.of(filternames);
    }

    /**
     * CREATION Override...
     *
     * @param info
     * @param request
     * @return
     * @throws DbManagerException
     */
    @Override
    public WebUiResponse create(RequestInfo info, WebUiRequest request) {
        // create a filter response
        WebUiResponse response = WebUiCommons.FACTORY.createWebUiResponse();
        response.setContext(request.getContext());
        response.setUsername(request.getUsername());
        response.getContent().add(WebUiCommons.FACTORY.createWebUiResponseContent());
        response.getContent().get(0).setFiltername(request.getFiltername());

        // Creation nullability check call. Also works as a method execution break...
        if (this.createNullabilityCheck(request, response)) {
            return response;
        }

        // Context integrity check
        if (!IntegrityCheck.checkContext(request, response, info).isPresent()) {
            return response;
        }

        // Max length validation
        if (IntegrityCheck.exceedsMaxLength(request, response, info)) {
            return response;
        }

        try {
            // Create a fresh filter because all went all
            this.db(info).create(info, Constants.COLL_FILTERS, request);
        } catch (DbException ex) {
            try {
                return ExceptionHandler.responseWrapper(ex, request, response);
            } catch (Exception e) {
                LOGGER.error("Error while creating filter: ", e);
                return ExceptionHandler.responseStatusFiller(
                        info,
                        response,
                        null,
                        ". ExceptionHandler. Reason: " + e.getMessage());
            }
        }

        // Status update and caller delegation
        return ExceptionHandler.responseStatusFiller(info, response, ExceptionHandler.OK, ". Filter inserted with success");
    }

    /**
     * Private method for a filter creation nullability check.
     *
     * @param request  filter data
     * @param response filter data
     * @return true if nullability violation was found, false otherwise
     */
    private boolean createNullabilityCheck(WebUiRequest request, WebUiResponse response) {

        boolean status = this.commonNullabilityCheck(request, response, false);

        // Fields validation, mandatory for creation
        if (!status) {
            status = NullabilityCheck.checkFieldsAndMultiValueFields(request, response);
        }

        return status;
    }

    /**
     * Private method for a filter read nullability check.
     *
     * @param request  filter data
     * @param response filter data
     * @return true if nullability violation was found, false otherwise
     */
    private boolean readNullabilityCheck(WebUiRequest request, WebUiResponse response, boolean hasQueryParams) {
        return this.commonNullabilityCheck(request, response, hasQueryParams);
    }

    /**
     * Private method for a filter delete nullability check.
     *
     * @param request  filter data
     * @param response filter data
     * @return true if nullability violation was found, false otherwise
     */
    private boolean deleteNullabilityCheck(WebUiRequest request, WebUiResponse response, boolean hasQueryParams) {
        return this.readNullabilityCheck(request, response, hasQueryParams);
    }

    /**
     * @param request
     * @param response
     * @param hasQueryParams
     * @return
     */
    private boolean commonNullabilityCheck(WebUiRequest request, WebUiResponse response, boolean hasQueryParams) {
        boolean status;

        // Filter context validation, mandatory for reading purposes
        status = NullabilityCheck.checkFilterContext(request.getContext(), response);

        // Usename validation, mandatory for reading purposes
        if (!status) {
            status = NullabilityCheck.checkNullOrEmptyValue(response, Constants.VAR_USER_NAME, request.getUsername());
        }

        // Filtername validation, mandatory for reading purposes. For query params enabled this evaluation is not needed...
        if (!hasQueryParams && !status) {
            status = NullabilityCheck.checkNullOrEmptyValue(response, Constants.VAR_FILTER_NAME, request.getFiltername());
        }

        return status;
    }

    private static boolean isStringNullOrEmpty(final String value) {
        return value == null || value.isEmpty();
    }
}
