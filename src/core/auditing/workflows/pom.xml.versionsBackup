<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>pt.ptinovacao.na-portal</groupId>
    <artifactId>na-portal-auditing</artifactId>
    <version>10.3.0-SNAPSHOT</version>
    <relativePath>../pom.xml</relativePath>
  </parent>

  <artifactId>na-portal-auditing-workflows</artifactId>

  <dependencies>
    <dependency>
      <groupId>javax</groupId>
      <artifactId>javaee-api</artifactId>
    </dependency>
    <!-- NA Portal dependencies -->
    <dependency>
      <groupId>pt.ptinovacao.na-portal</groupId>
      <artifactId>na-portal-auditing-schemas</artifactId>
    </dependency>
    <dependency>
      <groupId>pt.ptinovacao.na-portal</groupId>
      <artifactId>na-portal-error-commons</artifactId>
    </dependency>
    <dependency>
      <groupId>pt.ptinovacao.na-commons</groupId>
      <artifactId>na-commons-plugins-http-plugin</artifactId>
    </dependency>
    <!-- NA Commons dependencies -->
    <dependency>
      <groupId>pt.ptinovacao.na-commons</groupId>
      <artifactId>na-commons-plugins-dbmanager-schema</artifactId>
    </dependency>
    <dependency>
      <groupId>pt.ptinovacao.na-commons</groupId>
      <artifactId>na-commons-plugins-dbmanager-core</artifactId>
    </dependency>
    <dependency>
      <groupId>pt.ptinovacao.na-commons</groupId>
      <artifactId>na-commons-utils-mongo</artifactId>
    </dependency>
    <dependency>
      <groupId>pt.ptinovacao.na-commons</groupId>
      <artifactId>na-commons-utils-logging</artifactId>
    </dependency>
    <!-- WFE API -->
    <dependency>
      <groupId>pt.ptinovacao.na-commons</groupId>
      <artifactId>na-commons-wfe-api</artifactId>
    </dependency>
    <dependency>
      <groupId>pt.ptinovacao.na-commons</groupId>
      <artifactId>na-commons-wfe-adapters</artifactId>
    </dependency>
    <!-- Logging -->
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
    </dependency>

    <!-- Testing dependencies -->
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-all</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <configuration>
          <!-- These are transitive dependencies with scope provided, but we need them on compile time.
          This will be resolved in na-commons version 5.0.0 -->
          <usedDependencies>
            <dependency>pt.ptinovacao.na-commons:na-commons-wfe-adapters</dependency>
            <dependency>pt.ptinovacao.na-commons:na-commons-plugins-http-plugin</dependency>
          </usedDependencies>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
