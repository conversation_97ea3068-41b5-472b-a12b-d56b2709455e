<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>pt.ptinovacao.na-portal</groupId>
    <artifactId>na-portal-auditing</artifactId>
    <version>100.0.0</version>
    <relativePath>../pom.xml</relativePath>
  </parent>

  <artifactId>na-portal-auditing-schemas</artifactId>

  <dependencies>
    <dependency>
      <groupId>pt.ptinovacao.na-commons</groupId>
      <artifactId>na-commons-plugins-dbmanager-schema</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.jvnet.jaxb2_commons</groupId>
      <artifactId>jaxb2-basics-runtime</artifactId>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.jvnet.jaxb2.maven2</groupId>
        <artifactId>maven-jaxb2-plugin</artifactId>
        <executions>
          <execution>
            <id>datamodel-generate</id>
            <phase>generate-sources</phase>
            <configuration>
              <generatePackage>pt.ptinovacao.na.portal.web.ui.naem.schemas</generatePackage>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
