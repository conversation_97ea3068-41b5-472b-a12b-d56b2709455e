<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<jaxb:bindings xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" if-exists="true" version="2.1">
  <!--

This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.3.0 
See <a href="https://javaee.github.io/jaxb-v2/">https://javaee.github.io/jaxb-v2/</a> 
Any modifications to this file will be lost upon recompilation of the source schema. 
Generated on: 2025.07.01 at 11:20:48 AM WEST 

  -->
  <jaxb:bindings if-exists="true" scd="x-schema::">
    <jaxb:schemaBindings map="false">
      <jaxb:package name="pt.ptinovacao.na.portal.web.ui.naem.schemas"/>
    </jaxb:schemaBindings>
    <jaxb:bindings if-exists="true" scd="WebUiRequest">
      <jaxb:class ref="pt.ptinovacao.na.portal.web.ui.naem.schemas.WebUiRequest"/>
    </jaxb:bindings>
    <jaxb:bindings if-exists="true" scd="WebUiResponse">
      <jaxb:class ref="pt.ptinovacao.na.portal.web.ui.naem.schemas.WebUiResponse"/>
    </jaxb:bindings>
    <jaxb:bindings if-exists="true" scd="~ExtendedField">
      <jaxb:class ref="pt.ptinovacao.na.portal.web.ui.naem.schemas.ExtendedField"/>
    </jaxb:bindings>
    <jaxb:bindings if-exists="true" scd="~ExtendedMultiValueField">
      <jaxb:class ref="pt.ptinovacao.na.portal.web.ui.naem.schemas.ExtendedMultiValueField"/>
    </jaxb:bindings>
    <jaxb:bindings if-exists="true" scd="~BaseField">
      <jaxb:class ref="pt.ptinovacao.na.portal.web.ui.naem.schemas.BaseField"/>
    </jaxb:bindings>
    <jaxb:bindings if-exists="true" scd="~Context">
      <jaxb:typesafeEnumClass ref="pt.ptinovacao.na.portal.web.ui.naem.schemas.Context"/>
    </jaxb:bindings>
    <jaxb:bindings if-exists="true" scd="~ResponseStatus">
      <jaxb:typesafeEnumClass ref="pt.ptinovacao.na.portal.web.ui.naem.schemas.ResponseStatus"/>
    </jaxb:bindings>
  </jaxb:bindings>
</jaxb:bindings>
