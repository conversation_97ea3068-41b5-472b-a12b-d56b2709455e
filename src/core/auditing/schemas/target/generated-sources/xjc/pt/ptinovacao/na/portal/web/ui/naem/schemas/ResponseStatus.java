//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.3.0 
// See <a href="https://javaee.github.io/jaxb-v2/">https://javaee.github.io/jaxb-v2/</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2025.07.01 at 11:20:48 AM WEST 
//


package pt.ptinovacao.na.portal.web.ui.naem.schemas;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ResponseStatus.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="ResponseStatus"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="OK"/&gt;
 *     &lt;enumeration value="NOT_FOUND"/&gt;
 *     &lt;enumeration value="ALREADY_EXISTS"/&gt;
 *     &lt;enumeration value="MALFORMED_REQUEST"/&gt;
 *     &lt;enumeration value="OTHER_ERROR"/&gt;
 *     &lt;enumeration value="INTEGRITY_HAS_NULL_VALUES"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "ResponseStatus")
@XmlEnum
public enum ResponseStatus {

    OK,
    NOT_FOUND,
    ALREADY_EXISTS,
    MALFORMED_REQUEST,
    OTHER_ERROR,
    INTEGRITY_HAS_NULL_VALUES;

    public String value() {
        return name();
    }

    public static ResponseStatus fromValue(String v) {
        return valueOf(v);
    }

}
