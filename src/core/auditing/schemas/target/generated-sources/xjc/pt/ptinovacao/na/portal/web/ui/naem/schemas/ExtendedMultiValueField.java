//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.3.0 
// See <a href="https://javaee.github.io/jaxb-v2/">https://javaee.github.io/jaxb-v2/</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2025.07.01 at 11:20:48 AM WEST 
//


package pt.ptinovacao.na.portal.web.ui.naem.schemas;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import org.jvnet.jaxb2_commons.lang.Equals2;
import org.jvnet.jaxb2_commons.lang.EqualsStrategy2;
import org.jvnet.jaxb2_commons.lang.HashCode2;
import org.jvnet.jaxb2_commons.lang.HashCodeStrategy2;
import org.jvnet.jaxb2_commons.lang.JAXBEqualsStrategy;
import org.jvnet.jaxb2_commons.lang.JAXBHashCodeStrategy;
import org.jvnet.jaxb2_commons.lang.JAXBToStringStrategy;
import org.jvnet.jaxb2_commons.lang.ToString2;
import org.jvnet.jaxb2_commons.lang.ToStringStrategy2;
import org.jvnet.jaxb2_commons.locator.ObjectLocator;
import org.jvnet.jaxb2_commons.locator.util.LocatorUtils;


/**
 * <p>Java class for ExtendedMultiValueField complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ExtendedMultiValueField"&gt;
 *   &lt;complexContent&gt;
 *     &lt;extension base="{}BaseField"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="multiValue" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/extension&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ExtendedMultiValueField", propOrder = {
    "multiValue"
})
public class ExtendedMultiValueField
    extends BaseField
    implements Equals2, HashCode2, ToString2
{

    @XmlElement(required = true)
    protected List<String> multiValue;

    /**
     * Gets the value of the multiValue property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the multiValue property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getMultiValue().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     * 
     * 
     */
    public List<String> getMultiValue() {
        if (multiValue == null) {
            multiValue = new ArrayList<String>();
        }
        return this.multiValue;
    }

    public String toString() {
        final ToStringStrategy2 strategy = JAXBToStringStrategy.INSTANCE2;
        final StringBuilder buffer = new StringBuilder();
        append(null, buffer, strategy);
        return buffer.toString();
    }

    public StringBuilder append(ObjectLocator locator, StringBuilder buffer, ToStringStrategy2 strategy) {
        strategy.appendStart(locator, this, buffer);
        appendFields(locator, buffer, strategy);
        strategy.appendEnd(locator, this, buffer);
        return buffer;
    }

    public StringBuilder appendFields(ObjectLocator locator, StringBuilder buffer, ToStringStrategy2 strategy) {
        super.appendFields(locator, buffer, strategy);
        {
            List<String> theMultiValue;
            theMultiValue = (((this.multiValue!= null)&&(!this.multiValue.isEmpty()))?this.getMultiValue():null);
            strategy.appendField(locator, this, "multiValue", buffer, theMultiValue, ((this.multiValue!= null)&&(!this.multiValue.isEmpty())));
        }
        return buffer;
    }

    public boolean equals(ObjectLocator thisLocator, ObjectLocator thatLocator, Object object, EqualsStrategy2 strategy) {
        if ((object == null)||(this.getClass()!= object.getClass())) {
            return false;
        }
        if (this == object) {
            return true;
        }
        if (!super.equals(thisLocator, thatLocator, object, strategy)) {
            return false;
        }
        final ExtendedMultiValueField that = ((ExtendedMultiValueField) object);
        {
            List<String> lhsMultiValue;
            lhsMultiValue = (((this.multiValue!= null)&&(!this.multiValue.isEmpty()))?this.getMultiValue():null);
            List<String> rhsMultiValue;
            rhsMultiValue = (((that.multiValue!= null)&&(!that.multiValue.isEmpty()))?that.getMultiValue():null);
            if (!strategy.equals(LocatorUtils.property(thisLocator, "multiValue", lhsMultiValue), LocatorUtils.property(thatLocator, "multiValue", rhsMultiValue), lhsMultiValue, rhsMultiValue, ((this.multiValue!= null)&&(!this.multiValue.isEmpty())), ((that.multiValue!= null)&&(!that.multiValue.isEmpty())))) {
                return false;
            }
        }
        return true;
    }

    public boolean equals(Object object) {
        final EqualsStrategy2 strategy = JAXBEqualsStrategy.INSTANCE2;
        return equals(null, null, object, strategy);
    }

    public int hashCode(ObjectLocator locator, HashCodeStrategy2 strategy) {
        int currentHashCode = super.hashCode(locator, strategy);
        {
            List<String> theMultiValue;
            theMultiValue = (((this.multiValue!= null)&&(!this.multiValue.isEmpty()))?this.getMultiValue():null);
            currentHashCode = strategy.hashCode(LocatorUtils.property(locator, "multiValue", theMultiValue), currentHashCode, theMultiValue, ((this.multiValue!= null)&&(!this.multiValue.isEmpty())));
        }
        return currentHashCode;
    }

    public int hashCode() {
        final HashCodeStrategy2 strategy = JAXBHashCodeStrategy.INSTANCE2;
        return this.hashCode(null, strategy);
    }

}
