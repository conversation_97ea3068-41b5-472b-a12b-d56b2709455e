//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.3.0 
// See <a href="https://javaee.github.io/jaxb-v2/">https://javaee.github.io/jaxb-v2/</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2025.07.01 at 11:20:48 AM WEST 
//


package pt.ptinovacao.na.portal.web.ui.naem.schemas;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the pt.ptinovacao.na.portal.web.ui.naem.schemas package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: pt.ptinovacao.na.portal.web.ui.naem.schemas
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link WebUiRequest }
     * 
     */
    public WebUiRequest createWebUiRequest() {
        return new WebUiRequest();
    }

    /**
     * Create an instance of {@link WebUiResponse }
     * 
     */
    public WebUiResponse createWebUiResponse() {
        return new WebUiResponse();
    }

    /**
     * Create an instance of {@link WebUiRequest.Group }
     * 
     */
    public WebUiRequest.Group createWebUiRequestGroup() {
        return new WebUiRequest.Group();
    }

    /**
     * Create an instance of {@link ExtendedField }
     * 
     */
    public ExtendedField createExtendedField() {
        return new ExtendedField();
    }

    /**
     * Create an instance of {@link ExtendedMultiValueField }
     * 
     */
    public ExtendedMultiValueField createExtendedMultiValueField() {
        return new ExtendedMultiValueField();
    }

    /**
     * Create an instance of {@link WebUiResponse.Content }
     * 
     */
    public WebUiResponse.Content createWebUiResponseContent() {
        return new WebUiResponse.Content();
    }

    /**
     * Create an instance of {@link BaseField }
     * 
     */
    public BaseField createBaseField() {
        return new BaseField();
    }

    /**
     * Create an instance of {@link WebUiRequest.Group.Fields }
     * 
     */
    public WebUiRequest.Group.Fields createWebUiRequestGroupFields() {
        return new WebUiRequest.Group.Fields();
    }

}
