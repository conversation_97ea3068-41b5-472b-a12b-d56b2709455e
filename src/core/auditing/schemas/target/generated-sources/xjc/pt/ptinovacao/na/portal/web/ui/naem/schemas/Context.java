//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.3.0 
// See <a href="https://javaee.github.io/jaxb-v2/">https://javaee.github.io/jaxb-v2/</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2025.07.01 at 11:20:48 AM WEST 
//


package pt.ptinovacao.na.portal.web.ui.naem.schemas;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for Context.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="Context"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="NA_EQUIPMENTS"/&gt;
 *     &lt;enumeration value="NA_GLOBALEQUIPMENTS"/&gt;
 *     &lt;enumeration value="NA_SERVICE"/&gt;
 *     &lt;enumeration value="MPT"/&gt;
 *     &lt;enumeration value="GO"/&gt;
 *     &lt;enumeration value="ADMIN_GO"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "Context")
@XmlEnum
public enum Context {

    NA_EQUIPMENTS,
    NA_GLOBALEQUIPMENTS,
    NA_SERVICE,
    MPT,
    GO,
    ADMIN_GO;

    public String value() {
        return name();
    }

    public static Context fromValue(String v) {
        return valueOf(v);
    }

}
