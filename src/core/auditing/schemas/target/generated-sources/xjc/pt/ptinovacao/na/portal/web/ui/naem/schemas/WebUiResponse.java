//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.3.0 
// See <a href="https://javaee.github.io/jaxb-v2/">https://javaee.github.io/jaxb-v2/</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2025.07.01 at 11:20:48 AM WEST 
//


package pt.ptinovacao.na.portal.web.ui.naem.schemas;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import org.jvnet.jaxb2_commons.lang.Equals2;
import org.jvnet.jaxb2_commons.lang.EqualsStrategy2;
import org.jvnet.jaxb2_commons.lang.HashCode2;
import org.jvnet.jaxb2_commons.lang.HashCodeStrategy2;
import org.jvnet.jaxb2_commons.lang.JAXBEqualsStrategy;
import org.jvnet.jaxb2_commons.lang.JAXBHashCodeStrategy;
import org.jvnet.jaxb2_commons.lang.JAXBToStringStrategy;
import org.jvnet.jaxb2_commons.lang.ToString2;
import org.jvnet.jaxb2_commons.lang.ToStringStrategy2;
import org.jvnet.jaxb2_commons.locator.ObjectLocator;
import org.jvnet.jaxb2_commons.locator.util.LocatorUtils;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="username" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="status" type="{}ResponseStatus"/&gt;
 *         &lt;element name="description" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="content" maxOccurs="unbounded" minOccurs="0"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="fields" type="{}ExtendedField" maxOccurs="unbounded" minOccurs="0"/&gt;
 *                   &lt;element name="multiValueFields" type="{}ExtendedMultiValueField" maxOccurs="unbounded" minOccurs="0"/&gt;
 *                   &lt;choice&gt;
 *                     &lt;group ref="{}FilterInfo"/&gt;
 *                     &lt;group ref="{}AuditInfo"/&gt;
 *                   &lt;/choice&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *       &lt;/sequence&gt;
 *       &lt;attGroup ref="{}InfoPropertiesGroup"/&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "username",
    "status",
    "description",
    "content"
})
@XmlRootElement(name = "WebUiResponse")
public class WebUiResponse implements Equals2, HashCode2, ToString2
{

    @XmlElement(required = true)
    protected String username;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected ResponseStatus status;
    protected String description;
    protected List<WebUiResponse.Content> content;
    @XmlAttribute(name = "context", required = true)
    protected Context context;

    /**
     * Gets the value of the username property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUsername() {
        return username;
    }

    /**
     * Sets the value of the username property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUsername(String value) {
        this.username = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link ResponseStatus }
     *     
     */
    public ResponseStatus getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link ResponseStatus }
     *     
     */
    public void setStatus(ResponseStatus value) {
        this.status = value;
    }

    /**
     * Gets the value of the description property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescription() {
        return description;
    }

    /**
     * Sets the value of the description property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescription(String value) {
        this.description = value;
    }

    /**
     * Gets the value of the content property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the content property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getContent().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link WebUiResponse.Content }
     * 
     * 
     */
    public List<WebUiResponse.Content> getContent() {
        if (content == null) {
            content = new ArrayList<WebUiResponse.Content>();
        }
        return this.content;
    }

    /**
     * Gets the value of the context property.
     * 
     * @return
     *     possible object is
     *     {@link Context }
     *     
     */
    public Context getContext() {
        return context;
    }

    /**
     * Sets the value of the context property.
     * 
     * @param value
     *     allowed object is
     *     {@link Context }
     *     
     */
    public void setContext(Context value) {
        this.context = value;
    }

    public String toString() {
        final ToStringStrategy2 strategy = JAXBToStringStrategy.INSTANCE2;
        final StringBuilder buffer = new StringBuilder();
        append(null, buffer, strategy);
        return buffer.toString();
    }

    public StringBuilder append(ObjectLocator locator, StringBuilder buffer, ToStringStrategy2 strategy) {
        strategy.appendStart(locator, this, buffer);
        appendFields(locator, buffer, strategy);
        strategy.appendEnd(locator, this, buffer);
        return buffer;
    }

    public StringBuilder appendFields(ObjectLocator locator, StringBuilder buffer, ToStringStrategy2 strategy) {
        {
            String theUsername;
            theUsername = this.getUsername();
            strategy.appendField(locator, this, "username", buffer, theUsername, (this.username!= null));
        }
        {
            ResponseStatus theStatus;
            theStatus = this.getStatus();
            strategy.appendField(locator, this, "status", buffer, theStatus, (this.status!= null));
        }
        {
            String theDescription;
            theDescription = this.getDescription();
            strategy.appendField(locator, this, "description", buffer, theDescription, (this.description!= null));
        }
        {
            List<WebUiResponse.Content> theContent;
            theContent = (((this.content!= null)&&(!this.content.isEmpty()))?this.getContent():null);
            strategy.appendField(locator, this, "content", buffer, theContent, ((this.content!= null)&&(!this.content.isEmpty())));
        }
        {
            Context theContext;
            theContext = this.getContext();
            strategy.appendField(locator, this, "context", buffer, theContext, (this.context!= null));
        }
        return buffer;
    }

    public boolean equals(ObjectLocator thisLocator, ObjectLocator thatLocator, Object object, EqualsStrategy2 strategy) {
        if ((object == null)||(this.getClass()!= object.getClass())) {
            return false;
        }
        if (this == object) {
            return true;
        }
        final WebUiResponse that = ((WebUiResponse) object);
        {
            String lhsUsername;
            lhsUsername = this.getUsername();
            String rhsUsername;
            rhsUsername = that.getUsername();
            if (!strategy.equals(LocatorUtils.property(thisLocator, "username", lhsUsername), LocatorUtils.property(thatLocator, "username", rhsUsername), lhsUsername, rhsUsername, (this.username!= null), (that.username!= null))) {
                return false;
            }
        }
        {
            ResponseStatus lhsStatus;
            lhsStatus = this.getStatus();
            ResponseStatus rhsStatus;
            rhsStatus = that.getStatus();
            if (!strategy.equals(LocatorUtils.property(thisLocator, "status", lhsStatus), LocatorUtils.property(thatLocator, "status", rhsStatus), lhsStatus, rhsStatus, (this.status!= null), (that.status!= null))) {
                return false;
            }
        }
        {
            String lhsDescription;
            lhsDescription = this.getDescription();
            String rhsDescription;
            rhsDescription = that.getDescription();
            if (!strategy.equals(LocatorUtils.property(thisLocator, "description", lhsDescription), LocatorUtils.property(thatLocator, "description", rhsDescription), lhsDescription, rhsDescription, (this.description!= null), (that.description!= null))) {
                return false;
            }
        }
        {
            List<WebUiResponse.Content> lhsContent;
            lhsContent = (((this.content!= null)&&(!this.content.isEmpty()))?this.getContent():null);
            List<WebUiResponse.Content> rhsContent;
            rhsContent = (((that.content!= null)&&(!that.content.isEmpty()))?that.getContent():null);
            if (!strategy.equals(LocatorUtils.property(thisLocator, "content", lhsContent), LocatorUtils.property(thatLocator, "content", rhsContent), lhsContent, rhsContent, ((this.content!= null)&&(!this.content.isEmpty())), ((that.content!= null)&&(!that.content.isEmpty())))) {
                return false;
            }
        }
        {
            Context lhsContext;
            lhsContext = this.getContext();
            Context rhsContext;
            rhsContext = that.getContext();
            if (!strategy.equals(LocatorUtils.property(thisLocator, "context", lhsContext), LocatorUtils.property(thatLocator, "context", rhsContext), lhsContext, rhsContext, (this.context!= null), (that.context!= null))) {
                return false;
            }
        }
        return true;
    }

    public boolean equals(Object object) {
        final EqualsStrategy2 strategy = JAXBEqualsStrategy.INSTANCE2;
        return equals(null, null, object, strategy);
    }

    public int hashCode(ObjectLocator locator, HashCodeStrategy2 strategy) {
        int currentHashCode = 1;
        {
            String theUsername;
            theUsername = this.getUsername();
            currentHashCode = strategy.hashCode(LocatorUtils.property(locator, "username", theUsername), currentHashCode, theUsername, (this.username!= null));
        }
        {
            ResponseStatus theStatus;
            theStatus = this.getStatus();
            currentHashCode = strategy.hashCode(LocatorUtils.property(locator, "status", theStatus), currentHashCode, theStatus, (this.status!= null));
        }
        {
            String theDescription;
            theDescription = this.getDescription();
            currentHashCode = strategy.hashCode(LocatorUtils.property(locator, "description", theDescription), currentHashCode, theDescription, (this.description!= null));
        }
        {
            List<WebUiResponse.Content> theContent;
            theContent = (((this.content!= null)&&(!this.content.isEmpty()))?this.getContent():null);
            currentHashCode = strategy.hashCode(LocatorUtils.property(locator, "content", theContent), currentHashCode, theContent, ((this.content!= null)&&(!this.content.isEmpty())));
        }
        {
            Context theContext;
            theContext = this.getContext();
            currentHashCode = strategy.hashCode(LocatorUtils.property(locator, "context", theContext), currentHashCode, theContext, (this.context!= null));
        }
        return currentHashCode;
    }

    public int hashCode() {
        final HashCodeStrategy2 strategy = JAXBHashCodeStrategy.INSTANCE2;
        return this.hashCode(null, strategy);
    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * &lt;complexType&gt;
     *   &lt;complexContent&gt;
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
     *       &lt;sequence&gt;
     *         &lt;element name="fields" type="{}ExtendedField" maxOccurs="unbounded" minOccurs="0"/&gt;
     *         &lt;element name="multiValueFields" type="{}ExtendedMultiValueField" maxOccurs="unbounded" minOccurs="0"/&gt;
     *         &lt;choice&gt;
     *           &lt;group ref="{}FilterInfo"/&gt;
     *           &lt;group ref="{}AuditInfo"/&gt;
     *         &lt;/choice&gt;
     *       &lt;/sequence&gt;
     *     &lt;/restriction&gt;
     *   &lt;/complexContent&gt;
     * &lt;/complexType&gt;
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "fields",
        "multiValueFields",
        "filtername",
        "group",
        "date"
    })
    public static class Content implements Equals2, HashCode2, ToString2
    {

        protected List<ExtendedField> fields;
        protected List<ExtendedMultiValueField> multiValueFields;
        protected String filtername;
        protected pt.ptinovacao.na.portal.web.ui.naem.schemas.WebUiRequest.Group group;
        protected Long date;

        /**
         * Gets the value of the fields property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the fields property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getFields().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link ExtendedField }
         * 
         * 
         */
        public List<ExtendedField> getFields() {
            if (fields == null) {
                fields = new ArrayList<ExtendedField>();
            }
            return this.fields;
        }

        /**
         * Gets the value of the multiValueFields property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the multiValueFields property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getMultiValueFields().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link ExtendedMultiValueField }
         * 
         * 
         */
        public List<ExtendedMultiValueField> getMultiValueFields() {
            if (multiValueFields == null) {
                multiValueFields = new ArrayList<ExtendedMultiValueField>();
            }
            return this.multiValueFields;
        }

        /**
         * Gets the value of the filtername property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getFiltername() {
            return filtername;
        }

        /**
         * Sets the value of the filtername property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setFiltername(String value) {
            this.filtername = value;
        }

        /**
         * Gets the value of the group property.
         * 
         * @return
         *     possible object is
         *     {@link pt.ptinovacao.na.portal.web.ui.naem.schemas.WebUiRequest.Group }
         *     
         */
        public pt.ptinovacao.na.portal.web.ui.naem.schemas.WebUiRequest.Group getGroup() {
            return group;
        }

        /**
         * Sets the value of the group property.
         * 
         * @param value
         *     allowed object is
         *     {@link pt.ptinovacao.na.portal.web.ui.naem.schemas.WebUiRequest.Group }
         *     
         */
        public void setGroup(pt.ptinovacao.na.portal.web.ui.naem.schemas.WebUiRequest.Group value) {
            this.group = value;
        }

        /**
         * Gets the value of the date property.
         * 
         * @return
         *     possible object is
         *     {@link Long }
         *     
         */
        public Long getDate() {
            return date;
        }

        /**
         * Sets the value of the date property.
         * 
         * @param value
         *     allowed object is
         *     {@link Long }
         *     
         */
        public void setDate(Long value) {
            this.date = value;
        }

        public String toString() {
            final ToStringStrategy2 strategy = JAXBToStringStrategy.INSTANCE2;
            final StringBuilder buffer = new StringBuilder();
            append(null, buffer, strategy);
            return buffer.toString();
        }

        public StringBuilder append(ObjectLocator locator, StringBuilder buffer, ToStringStrategy2 strategy) {
            strategy.appendStart(locator, this, buffer);
            appendFields(locator, buffer, strategy);
            strategy.appendEnd(locator, this, buffer);
            return buffer;
        }

        public StringBuilder appendFields(ObjectLocator locator, StringBuilder buffer, ToStringStrategy2 strategy) {
            {
                List<ExtendedField> theFields;
                theFields = (((this.fields!= null)&&(!this.fields.isEmpty()))?this.getFields():null);
                strategy.appendField(locator, this, "fields", buffer, theFields, ((this.fields!= null)&&(!this.fields.isEmpty())));
            }
            {
                List<ExtendedMultiValueField> theMultiValueFields;
                theMultiValueFields = (((this.multiValueFields!= null)&&(!this.multiValueFields.isEmpty()))?this.getMultiValueFields():null);
                strategy.appendField(locator, this, "multiValueFields", buffer, theMultiValueFields, ((this.multiValueFields!= null)&&(!this.multiValueFields.isEmpty())));
            }
            {
                String theFiltername;
                theFiltername = this.getFiltername();
                strategy.appendField(locator, this, "filtername", buffer, theFiltername, (this.filtername!= null));
            }
            {
                pt.ptinovacao.na.portal.web.ui.naem.schemas.WebUiRequest.Group theGroup;
                theGroup = this.getGroup();
                strategy.appendField(locator, this, "group", buffer, theGroup, (this.group!= null));
            }
            {
                Long theDate;
                theDate = this.getDate();
                strategy.appendField(locator, this, "date", buffer, theDate, (this.date!= null));
            }
            return buffer;
        }

        public boolean equals(ObjectLocator thisLocator, ObjectLocator thatLocator, Object object, EqualsStrategy2 strategy) {
            if ((object == null)||(this.getClass()!= object.getClass())) {
                return false;
            }
            if (this == object) {
                return true;
            }
            final WebUiResponse.Content that = ((WebUiResponse.Content) object);
            {
                List<ExtendedField> lhsFields;
                lhsFields = (((this.fields!= null)&&(!this.fields.isEmpty()))?this.getFields():null);
                List<ExtendedField> rhsFields;
                rhsFields = (((that.fields!= null)&&(!that.fields.isEmpty()))?that.getFields():null);
                if (!strategy.equals(LocatorUtils.property(thisLocator, "fields", lhsFields), LocatorUtils.property(thatLocator, "fields", rhsFields), lhsFields, rhsFields, ((this.fields!= null)&&(!this.fields.isEmpty())), ((that.fields!= null)&&(!that.fields.isEmpty())))) {
                    return false;
                }
            }
            {
                List<ExtendedMultiValueField> lhsMultiValueFields;
                lhsMultiValueFields = (((this.multiValueFields!= null)&&(!this.multiValueFields.isEmpty()))?this.getMultiValueFields():null);
                List<ExtendedMultiValueField> rhsMultiValueFields;
                rhsMultiValueFields = (((that.multiValueFields!= null)&&(!that.multiValueFields.isEmpty()))?that.getMultiValueFields():null);
                if (!strategy.equals(LocatorUtils.property(thisLocator, "multiValueFields", lhsMultiValueFields), LocatorUtils.property(thatLocator, "multiValueFields", rhsMultiValueFields), lhsMultiValueFields, rhsMultiValueFields, ((this.multiValueFields!= null)&&(!this.multiValueFields.isEmpty())), ((that.multiValueFields!= null)&&(!that.multiValueFields.isEmpty())))) {
                    return false;
                }
            }
            {
                String lhsFiltername;
                lhsFiltername = this.getFiltername();
                String rhsFiltername;
                rhsFiltername = that.getFiltername();
                if (!strategy.equals(LocatorUtils.property(thisLocator, "filtername", lhsFiltername), LocatorUtils.property(thatLocator, "filtername", rhsFiltername), lhsFiltername, rhsFiltername, (this.filtername!= null), (that.filtername!= null))) {
                    return false;
                }
            }
            {
                pt.ptinovacao.na.portal.web.ui.naem.schemas.WebUiRequest.Group lhsGroup;
                lhsGroup = this.getGroup();
                pt.ptinovacao.na.portal.web.ui.naem.schemas.WebUiRequest.Group rhsGroup;
                rhsGroup = that.getGroup();
                if (!strategy.equals(LocatorUtils.property(thisLocator, "group", lhsGroup), LocatorUtils.property(thatLocator, "group", rhsGroup), lhsGroup, rhsGroup, (this.group!= null), (that.group!= null))) {
                    return false;
                }
            }
            {
                Long lhsDate;
                lhsDate = this.getDate();
                Long rhsDate;
                rhsDate = that.getDate();
                if (!strategy.equals(LocatorUtils.property(thisLocator, "date", lhsDate), LocatorUtils.property(thatLocator, "date", rhsDate), lhsDate, rhsDate, (this.date!= null), (that.date!= null))) {
                    return false;
                }
            }
            return true;
        }

        public boolean equals(Object object) {
            final EqualsStrategy2 strategy = JAXBEqualsStrategy.INSTANCE2;
            return equals(null, null, object, strategy);
        }

        public int hashCode(ObjectLocator locator, HashCodeStrategy2 strategy) {
            int currentHashCode = 1;
            {
                List<ExtendedField> theFields;
                theFields = (((this.fields!= null)&&(!this.fields.isEmpty()))?this.getFields():null);
                currentHashCode = strategy.hashCode(LocatorUtils.property(locator, "fields", theFields), currentHashCode, theFields, ((this.fields!= null)&&(!this.fields.isEmpty())));
            }
            {
                List<ExtendedMultiValueField> theMultiValueFields;
                theMultiValueFields = (((this.multiValueFields!= null)&&(!this.multiValueFields.isEmpty()))?this.getMultiValueFields():null);
                currentHashCode = strategy.hashCode(LocatorUtils.property(locator, "multiValueFields", theMultiValueFields), currentHashCode, theMultiValueFields, ((this.multiValueFields!= null)&&(!this.multiValueFields.isEmpty())));
            }
            {
                String theFiltername;
                theFiltername = this.getFiltername();
                currentHashCode = strategy.hashCode(LocatorUtils.property(locator, "filtername", theFiltername), currentHashCode, theFiltername, (this.filtername!= null));
            }
            {
                pt.ptinovacao.na.portal.web.ui.naem.schemas.WebUiRequest.Group theGroup;
                theGroup = this.getGroup();
                currentHashCode = strategy.hashCode(LocatorUtils.property(locator, "group", theGroup), currentHashCode, theGroup, (this.group!= null));
            }
            {
                Long theDate;
                theDate = this.getDate();
                currentHashCode = strategy.hashCode(LocatorUtils.property(locator, "date", theDate), currentHashCode, theDate, (this.date!= null));
            }
            return currentHashCode;
        }

        public int hashCode() {
            final HashCodeStrategy2 strategy = JAXBHashCodeStrategy.INSTANCE2;
            return this.hashCode(null, strategy);
        }

    }

}
