//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.3.0 
// See <a href="https://javaee.github.io/jaxb-v2/">https://javaee.github.io/jaxb-v2/</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2025.07.01 at 11:20:48 AM WEST 
//


package pt.ptinovacao.na.portal.web.ui.naem.schemas;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import org.jvnet.jaxb2_commons.lang.Equals2;
import org.jvnet.jaxb2_commons.lang.EqualsStrategy2;
import org.jvnet.jaxb2_commons.lang.HashCode2;
import org.jvnet.jaxb2_commons.lang.HashCodeStrategy2;
import org.jvnet.jaxb2_commons.lang.JAXBEqualsStrategy;
import org.jvnet.jaxb2_commons.lang.JAXBHashCodeStrategy;
import org.jvnet.jaxb2_commons.lang.JAXBToStringStrategy;
import org.jvnet.jaxb2_commons.lang.ToString2;
import org.jvnet.jaxb2_commons.lang.ToStringStrategy2;
import org.jvnet.jaxb2_commons.locator.ObjectLocator;
import org.jvnet.jaxb2_commons.locator.util.LocatorUtils;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="username" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="fields" type="{}ExtendedField" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="multiValueFields" type="{}ExtendedMultiValueField" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;choice&gt;
 *           &lt;group ref="{}FilterInfo"/&gt;
 *           &lt;group ref="{}AuditInfo"/&gt;
 *         &lt;/choice&gt;
 *       &lt;/sequence&gt;
 *       &lt;attGroup ref="{}InfoPropertiesGroup"/&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "username",
    "fields",
    "multiValueFields",
    "filtername",
    "group",
    "date"
})
@XmlRootElement(name = "WebUiRequest")
public class WebUiRequest implements Equals2, HashCode2, ToString2
{

    @XmlElement(required = true)
    protected String username;
    protected List<ExtendedField> fields;
    protected List<ExtendedMultiValueField> multiValueFields;
    protected String filtername;
    protected WebUiRequest.Group group;
    protected Long date;
    @XmlAttribute(name = "context", required = true)
    protected Context context;

    /**
     * Gets the value of the username property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUsername() {
        return username;
    }

    /**
     * Sets the value of the username property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUsername(String value) {
        this.username = value;
    }

    /**
     * Gets the value of the fields property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the fields property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getFields().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ExtendedField }
     * 
     * 
     */
    public List<ExtendedField> getFields() {
        if (fields == null) {
            fields = new ArrayList<ExtendedField>();
        }
        return this.fields;
    }

    /**
     * Gets the value of the multiValueFields property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the multiValueFields property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getMultiValueFields().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ExtendedMultiValueField }
     * 
     * 
     */
    public List<ExtendedMultiValueField> getMultiValueFields() {
        if (multiValueFields == null) {
            multiValueFields = new ArrayList<ExtendedMultiValueField>();
        }
        return this.multiValueFields;
    }

    /**
     * Gets the value of the filtername property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFiltername() {
        return filtername;
    }

    /**
     * Sets the value of the filtername property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFiltername(String value) {
        this.filtername = value;
    }

    /**
     * Gets the value of the group property.
     * 
     * @return
     *     possible object is
     *     {@link WebUiRequest.Group }
     *     
     */
    public WebUiRequest.Group getGroup() {
        return group;
    }

    /**
     * Sets the value of the group property.
     * 
     * @param value
     *     allowed object is
     *     {@link WebUiRequest.Group }
     *     
     */
    public void setGroup(WebUiRequest.Group value) {
        this.group = value;
    }

    /**
     * Gets the value of the date property.
     * 
     * @return
     *     possible object is
     *     {@link Long }
     *     
     */
    public Long getDate() {
        return date;
    }

    /**
     * Sets the value of the date property.
     * 
     * @param value
     *     allowed object is
     *     {@link Long }
     *     
     */
    public void setDate(Long value) {
        this.date = value;
    }

    /**
     * Gets the value of the context property.
     * 
     * @return
     *     possible object is
     *     {@link Context }
     *     
     */
    public Context getContext() {
        return context;
    }

    /**
     * Sets the value of the context property.
     * 
     * @param value
     *     allowed object is
     *     {@link Context }
     *     
     */
    public void setContext(Context value) {
        this.context = value;
    }

    public String toString() {
        final ToStringStrategy2 strategy = JAXBToStringStrategy.INSTANCE2;
        final StringBuilder buffer = new StringBuilder();
        append(null, buffer, strategy);
        return buffer.toString();
    }

    public StringBuilder append(ObjectLocator locator, StringBuilder buffer, ToStringStrategy2 strategy) {
        strategy.appendStart(locator, this, buffer);
        appendFields(locator, buffer, strategy);
        strategy.appendEnd(locator, this, buffer);
        return buffer;
    }

    public StringBuilder appendFields(ObjectLocator locator, StringBuilder buffer, ToStringStrategy2 strategy) {
        {
            String theUsername;
            theUsername = this.getUsername();
            strategy.appendField(locator, this, "username", buffer, theUsername, (this.username!= null));
        }
        {
            List<ExtendedField> theFields;
            theFields = (((this.fields!= null)&&(!this.fields.isEmpty()))?this.getFields():null);
            strategy.appendField(locator, this, "fields", buffer, theFields, ((this.fields!= null)&&(!this.fields.isEmpty())));
        }
        {
            List<ExtendedMultiValueField> theMultiValueFields;
            theMultiValueFields = (((this.multiValueFields!= null)&&(!this.multiValueFields.isEmpty()))?this.getMultiValueFields():null);
            strategy.appendField(locator, this, "multiValueFields", buffer, theMultiValueFields, ((this.multiValueFields!= null)&&(!this.multiValueFields.isEmpty())));
        }
        {
            String theFiltername;
            theFiltername = this.getFiltername();
            strategy.appendField(locator, this, "filtername", buffer, theFiltername, (this.filtername!= null));
        }
        {
            WebUiRequest.Group theGroup;
            theGroup = this.getGroup();
            strategy.appendField(locator, this, "group", buffer, theGroup, (this.group!= null));
        }
        {
            Long theDate;
            theDate = this.getDate();
            strategy.appendField(locator, this, "date", buffer, theDate, (this.date!= null));
        }
        {
            Context theContext;
            theContext = this.getContext();
            strategy.appendField(locator, this, "context", buffer, theContext, (this.context!= null));
        }
        return buffer;
    }

    public boolean equals(ObjectLocator thisLocator, ObjectLocator thatLocator, Object object, EqualsStrategy2 strategy) {
        if ((object == null)||(this.getClass()!= object.getClass())) {
            return false;
        }
        if (this == object) {
            return true;
        }
        final WebUiRequest that = ((WebUiRequest) object);
        {
            String lhsUsername;
            lhsUsername = this.getUsername();
            String rhsUsername;
            rhsUsername = that.getUsername();
            if (!strategy.equals(LocatorUtils.property(thisLocator, "username", lhsUsername), LocatorUtils.property(thatLocator, "username", rhsUsername), lhsUsername, rhsUsername, (this.username!= null), (that.username!= null))) {
                return false;
            }
        }
        {
            List<ExtendedField> lhsFields;
            lhsFields = (((this.fields!= null)&&(!this.fields.isEmpty()))?this.getFields():null);
            List<ExtendedField> rhsFields;
            rhsFields = (((that.fields!= null)&&(!that.fields.isEmpty()))?that.getFields():null);
            if (!strategy.equals(LocatorUtils.property(thisLocator, "fields", lhsFields), LocatorUtils.property(thatLocator, "fields", rhsFields), lhsFields, rhsFields, ((this.fields!= null)&&(!this.fields.isEmpty())), ((that.fields!= null)&&(!that.fields.isEmpty())))) {
                return false;
            }
        }
        {
            List<ExtendedMultiValueField> lhsMultiValueFields;
            lhsMultiValueFields = (((this.multiValueFields!= null)&&(!this.multiValueFields.isEmpty()))?this.getMultiValueFields():null);
            List<ExtendedMultiValueField> rhsMultiValueFields;
            rhsMultiValueFields = (((that.multiValueFields!= null)&&(!that.multiValueFields.isEmpty()))?that.getMultiValueFields():null);
            if (!strategy.equals(LocatorUtils.property(thisLocator, "multiValueFields", lhsMultiValueFields), LocatorUtils.property(thatLocator, "multiValueFields", rhsMultiValueFields), lhsMultiValueFields, rhsMultiValueFields, ((this.multiValueFields!= null)&&(!this.multiValueFields.isEmpty())), ((that.multiValueFields!= null)&&(!that.multiValueFields.isEmpty())))) {
                return false;
            }
        }
        {
            String lhsFiltername;
            lhsFiltername = this.getFiltername();
            String rhsFiltername;
            rhsFiltername = that.getFiltername();
            if (!strategy.equals(LocatorUtils.property(thisLocator, "filtername", lhsFiltername), LocatorUtils.property(thatLocator, "filtername", rhsFiltername), lhsFiltername, rhsFiltername, (this.filtername!= null), (that.filtername!= null))) {
                return false;
            }
        }
        {
            WebUiRequest.Group lhsGroup;
            lhsGroup = this.getGroup();
            WebUiRequest.Group rhsGroup;
            rhsGroup = that.getGroup();
            if (!strategy.equals(LocatorUtils.property(thisLocator, "group", lhsGroup), LocatorUtils.property(thatLocator, "group", rhsGroup), lhsGroup, rhsGroup, (this.group!= null), (that.group!= null))) {
                return false;
            }
        }
        {
            Long lhsDate;
            lhsDate = this.getDate();
            Long rhsDate;
            rhsDate = that.getDate();
            if (!strategy.equals(LocatorUtils.property(thisLocator, "date", lhsDate), LocatorUtils.property(thatLocator, "date", rhsDate), lhsDate, rhsDate, (this.date!= null), (that.date!= null))) {
                return false;
            }
        }
        {
            Context lhsContext;
            lhsContext = this.getContext();
            Context rhsContext;
            rhsContext = that.getContext();
            if (!strategy.equals(LocatorUtils.property(thisLocator, "context", lhsContext), LocatorUtils.property(thatLocator, "context", rhsContext), lhsContext, rhsContext, (this.context!= null), (that.context!= null))) {
                return false;
            }
        }
        return true;
    }

    public boolean equals(Object object) {
        final EqualsStrategy2 strategy = JAXBEqualsStrategy.INSTANCE2;
        return equals(null, null, object, strategy);
    }

    public int hashCode(ObjectLocator locator, HashCodeStrategy2 strategy) {
        int currentHashCode = 1;
        {
            String theUsername;
            theUsername = this.getUsername();
            currentHashCode = strategy.hashCode(LocatorUtils.property(locator, "username", theUsername), currentHashCode, theUsername, (this.username!= null));
        }
        {
            List<ExtendedField> theFields;
            theFields = (((this.fields!= null)&&(!this.fields.isEmpty()))?this.getFields():null);
            currentHashCode = strategy.hashCode(LocatorUtils.property(locator, "fields", theFields), currentHashCode, theFields, ((this.fields!= null)&&(!this.fields.isEmpty())));
        }
        {
            List<ExtendedMultiValueField> theMultiValueFields;
            theMultiValueFields = (((this.multiValueFields!= null)&&(!this.multiValueFields.isEmpty()))?this.getMultiValueFields():null);
            currentHashCode = strategy.hashCode(LocatorUtils.property(locator, "multiValueFields", theMultiValueFields), currentHashCode, theMultiValueFields, ((this.multiValueFields!= null)&&(!this.multiValueFields.isEmpty())));
        }
        {
            String theFiltername;
            theFiltername = this.getFiltername();
            currentHashCode = strategy.hashCode(LocatorUtils.property(locator, "filtername", theFiltername), currentHashCode, theFiltername, (this.filtername!= null));
        }
        {
            WebUiRequest.Group theGroup;
            theGroup = this.getGroup();
            currentHashCode = strategy.hashCode(LocatorUtils.property(locator, "group", theGroup), currentHashCode, theGroup, (this.group!= null));
        }
        {
            Long theDate;
            theDate = this.getDate();
            currentHashCode = strategy.hashCode(LocatorUtils.property(locator, "date", theDate), currentHashCode, theDate, (this.date!= null));
        }
        {
            Context theContext;
            theContext = this.getContext();
            currentHashCode = strategy.hashCode(LocatorUtils.property(locator, "context", theContext), currentHashCode, theContext, (this.context!= null));
        }
        return currentHashCode;
    }

    public int hashCode() {
        final HashCodeStrategy2 strategy = JAXBHashCodeStrategy.INSTANCE2;
        return this.hashCode(null, strategy);
    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * &lt;complexType&gt;
     *   &lt;complexContent&gt;
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
     *       &lt;sequence&gt;
     *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
     *         &lt;element name="fields" maxOccurs="unbounded"&gt;
     *           &lt;complexType&gt;
     *             &lt;complexContent&gt;
     *               &lt;extension base="{}BaseField"&gt;
     *                 &lt;attribute name="isSelected" use="required" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
     *               &lt;/extension&gt;
     *             &lt;/complexContent&gt;
     *           &lt;/complexType&gt;
     *         &lt;/element&gt;
     *       &lt;/sequence&gt;
     *       &lt;attribute name="isSelected" use="required" type="{http://www.w3.org/2001/XMLSchema}boolean" fixed="true" /&gt;
     *     &lt;/restriction&gt;
     *   &lt;/complexContent&gt;
     * &lt;/complexType&gt;
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "name",
        "fields"
    })
    public static class Group implements Equals2, HashCode2, ToString2
    {

        @XmlElement(required = true)
        protected String name;
        @XmlElement(required = true)
        protected List<WebUiRequest.Group.Fields> fields;
        @XmlAttribute(name = "isSelected", required = true)
        protected boolean isSelected;

        /**
         * Gets the value of the name property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getName() {
            return name;
        }

        /**
         * Sets the value of the name property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setName(String value) {
            this.name = value;
        }

        /**
         * Gets the value of the fields property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the fields property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getFields().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link WebUiRequest.Group.Fields }
         * 
         * 
         */
        public List<WebUiRequest.Group.Fields> getFields() {
            if (fields == null) {
                fields = new ArrayList<WebUiRequest.Group.Fields>();
            }
            return this.fields;
        }

        /**
         * Gets the value of the isSelected property.
         * 
         */
        public boolean isIsSelected() {
            return isSelected;
        }

        /**
         * Sets the value of the isSelected property.
         * 
         */
        public void setIsSelected(boolean value) {
            this.isSelected = value;
        }

        public String toString() {
            final ToStringStrategy2 strategy = JAXBToStringStrategy.INSTANCE2;
            final StringBuilder buffer = new StringBuilder();
            append(null, buffer, strategy);
            return buffer.toString();
        }

        public StringBuilder append(ObjectLocator locator, StringBuilder buffer, ToStringStrategy2 strategy) {
            strategy.appendStart(locator, this, buffer);
            appendFields(locator, buffer, strategy);
            strategy.appendEnd(locator, this, buffer);
            return buffer;
        }

        public StringBuilder appendFields(ObjectLocator locator, StringBuilder buffer, ToStringStrategy2 strategy) {
            {
                String theName;
                theName = this.getName();
                strategy.appendField(locator, this, "name", buffer, theName, (this.name!= null));
            }
            {
                List<WebUiRequest.Group.Fields> theFields;
                theFields = (((this.fields!= null)&&(!this.fields.isEmpty()))?this.getFields():null);
                strategy.appendField(locator, this, "fields", buffer, theFields, ((this.fields!= null)&&(!this.fields.isEmpty())));
            }
            {
                boolean theIsSelected;
                theIsSelected = this.isIsSelected();
                strategy.appendField(locator, this, "isSelected", buffer, theIsSelected, true);
            }
            return buffer;
        }

        public boolean equals(ObjectLocator thisLocator, ObjectLocator thatLocator, Object object, EqualsStrategy2 strategy) {
            if ((object == null)||(this.getClass()!= object.getClass())) {
                return false;
            }
            if (this == object) {
                return true;
            }
            final WebUiRequest.Group that = ((WebUiRequest.Group) object);
            {
                String lhsName;
                lhsName = this.getName();
                String rhsName;
                rhsName = that.getName();
                if (!strategy.equals(LocatorUtils.property(thisLocator, "name", lhsName), LocatorUtils.property(thatLocator, "name", rhsName), lhsName, rhsName, (this.name!= null), (that.name!= null))) {
                    return false;
                }
            }
            {
                List<WebUiRequest.Group.Fields> lhsFields;
                lhsFields = (((this.fields!= null)&&(!this.fields.isEmpty()))?this.getFields():null);
                List<WebUiRequest.Group.Fields> rhsFields;
                rhsFields = (((that.fields!= null)&&(!that.fields.isEmpty()))?that.getFields():null);
                if (!strategy.equals(LocatorUtils.property(thisLocator, "fields", lhsFields), LocatorUtils.property(thatLocator, "fields", rhsFields), lhsFields, rhsFields, ((this.fields!= null)&&(!this.fields.isEmpty())), ((that.fields!= null)&&(!that.fields.isEmpty())))) {
                    return false;
                }
            }
            {
                boolean lhsIsSelected;
                lhsIsSelected = this.isIsSelected();
                boolean rhsIsSelected;
                rhsIsSelected = that.isIsSelected();
                if (!strategy.equals(LocatorUtils.property(thisLocator, "isSelected", lhsIsSelected), LocatorUtils.property(thatLocator, "isSelected", rhsIsSelected), lhsIsSelected, rhsIsSelected, true, true)) {
                    return false;
                }
            }
            return true;
        }

        public boolean equals(Object object) {
            final EqualsStrategy2 strategy = JAXBEqualsStrategy.INSTANCE2;
            return equals(null, null, object, strategy);
        }

        public int hashCode(ObjectLocator locator, HashCodeStrategy2 strategy) {
            int currentHashCode = 1;
            {
                String theName;
                theName = this.getName();
                currentHashCode = strategy.hashCode(LocatorUtils.property(locator, "name", theName), currentHashCode, theName, (this.name!= null));
            }
            {
                List<WebUiRequest.Group.Fields> theFields;
                theFields = (((this.fields!= null)&&(!this.fields.isEmpty()))?this.getFields():null);
                currentHashCode = strategy.hashCode(LocatorUtils.property(locator, "fields", theFields), currentHashCode, theFields, ((this.fields!= null)&&(!this.fields.isEmpty())));
            }
            {
                boolean theIsSelected;
                theIsSelected = this.isIsSelected();
                currentHashCode = strategy.hashCode(LocatorUtils.property(locator, "isSelected", theIsSelected), currentHashCode, theIsSelected, true);
            }
            return currentHashCode;
        }

        public int hashCode() {
            final HashCodeStrategy2 strategy = JAXBHashCodeStrategy.INSTANCE2;
            return this.hashCode(null, strategy);
        }


        /**
         * <p>Java class for anonymous complex type.
         * 
         * <p>The following schema fragment specifies the expected content contained within this class.
         * 
         * <pre>
         * &lt;complexType&gt;
         *   &lt;complexContent&gt;
         *     &lt;extension base="{}BaseField"&gt;
         *       &lt;attribute name="isSelected" use="required" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
         *     &lt;/extension&gt;
         *   &lt;/complexContent&gt;
         * &lt;/complexType&gt;
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "")
        public static class Fields
            extends BaseField
            implements Equals2, HashCode2, ToString2
        {

            @XmlAttribute(name = "isSelected", required = true)
            protected boolean isSelected;

            /**
             * Gets the value of the isSelected property.
             * 
             */
            public boolean isIsSelected() {
                return isSelected;
            }

            /**
             * Sets the value of the isSelected property.
             * 
             */
            public void setIsSelected(boolean value) {
                this.isSelected = value;
            }

            public String toString() {
                final ToStringStrategy2 strategy = JAXBToStringStrategy.INSTANCE2;
                final StringBuilder buffer = new StringBuilder();
                append(null, buffer, strategy);
                return buffer.toString();
            }

            public StringBuilder append(ObjectLocator locator, StringBuilder buffer, ToStringStrategy2 strategy) {
                strategy.appendStart(locator, this, buffer);
                appendFields(locator, buffer, strategy);
                strategy.appendEnd(locator, this, buffer);
                return buffer;
            }

            public StringBuilder appendFields(ObjectLocator locator, StringBuilder buffer, ToStringStrategy2 strategy) {
                super.appendFields(locator, buffer, strategy);
                {
                    boolean theIsSelected;
                    theIsSelected = this.isIsSelected();
                    strategy.appendField(locator, this, "isSelected", buffer, theIsSelected, true);
                }
                return buffer;
            }

            public boolean equals(ObjectLocator thisLocator, ObjectLocator thatLocator, Object object, EqualsStrategy2 strategy) {
                if ((object == null)||(this.getClass()!= object.getClass())) {
                    return false;
                }
                if (this == object) {
                    return true;
                }
                if (!super.equals(thisLocator, thatLocator, object, strategy)) {
                    return false;
                }
                final WebUiRequest.Group.Fields that = ((WebUiRequest.Group.Fields) object);
                {
                    boolean lhsIsSelected;
                    lhsIsSelected = this.isIsSelected();
                    boolean rhsIsSelected;
                    rhsIsSelected = that.isIsSelected();
                    if (!strategy.equals(LocatorUtils.property(thisLocator, "isSelected", lhsIsSelected), LocatorUtils.property(thatLocator, "isSelected", rhsIsSelected), lhsIsSelected, rhsIsSelected, true, true)) {
                        return false;
                    }
                }
                return true;
            }

            public boolean equals(Object object) {
                final EqualsStrategy2 strategy = JAXBEqualsStrategy.INSTANCE2;
                return equals(null, null, object, strategy);
            }

            public int hashCode(ObjectLocator locator, HashCodeStrategy2 strategy) {
                int currentHashCode = super.hashCode(locator, strategy);
                {
                    boolean theIsSelected;
                    theIsSelected = this.isIsSelected();
                    currentHashCode = strategy.hashCode(LocatorUtils.property(locator, "isSelected", theIsSelected), currentHashCode, theIsSelected, true);
                }
                return currentHashCode;
            }

            public int hashCode() {
                final HashCodeStrategy2 strategy = JAXBHashCodeStrategy.INSTANCE2;
                return this.hashCode(null, strategy);
            }

        }

    }

}
