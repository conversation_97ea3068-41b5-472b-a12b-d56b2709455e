package pt.ptinovacao.na.portal.webui.restful.commons.db.odata;

import com.google.common.base.Optional;
import org.jboss.logmanager.Logger;
import org.odata4j.expression.BoolCommonExpression;
import org.odata4j.expression.EntitySimpleProperty;
import org.odata4j.expression.OrderByExpression;
import org.odata4j.producer.InlineCount;
import org.odata4j.producer.QueryInfo;
import org.odata4j.producer.resources.OptionsQueryParser;

import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.UriInfo;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

/**
 * <AUTHOR> <<EMAIL>>
 */
public final class ODataUtils {

    private static final Logger LOGGER = Logger.getLogger(ODataUtils.class.getName());

    private ODataUtils() {
        // Hidden CTOR
    }

    /**
     * <b>
     * Query Options In addition to the value and the count, OData defines other URL options to
     * customize the output of your query.</b>
     * <p>
     * For example, $orderby allows you to order a query result by one or more properties in ascending
     * or descending order: http://services.odata.org/V4/TripPinServiceRW/People?$orderby=FirstName
     * desc In this case, we’re ordering by the FirstName property of each person in descending order.
     * If we want to combine query options, we can do so using the standard “&” query string operator:
     * http://services.odata.org/V4/TripPinServiceRW/People?$orderby=FirstName desc&$top=4 In this
     * case, we’re shorting and then pulling off the top 4.</p>
     * <p>
     * <p>
     * Table shows the full set of query options.</p>
     * <p>
     * <p>
     * <table border="1">
     * <thead>
     * <tr><th>Query Option</th><th>Description</th></tr>
     * </thead>
     * <tbody>
     * <tr><td>$filter</td><td>A Boolean expression for whether a particular entry should be included
     * in the feed, e.g. Categories?$filter=CategoryName eq 'Produce'. The Query Expression section
     * describes OData expressions.</td>
     * </tr>
     * <tr><td>$expand</td><td>Expand related resources in line with the retrieved resources, e.g.
     * Categories/$expand=Products would expand Product data in line with each Category
     * entry.</td></tr>
     * <tr><td>$select</td><td>Limit the properties on each entry to just those requested, e.g.
     * Categories?$select=CategoryName,Description.</td></tr>
     * <tr><td>$orderby</td><td>One or more comma-separated expressions with an optional “asc” (the
     * default) or “desc” depending on the order you’d like the values sorted, e.g.
     * Categories?$orderby=CategoryName desc.</td></tr>
     * <tr><td>$top</td><td>Return entries from the top of the feed, e.g. Categories?$top=4.</td></tr>
     * <tr><td>$skip</td><td>How many entries you’d like to skip, e.g. Categories?$skip=4.</td></tr>
     * <tr><td>$count</td><td>A Boolean value of true or false request a count of the matching
     * resources included with the resources in the response, e.g. Categories?$count=true</td></tr>
     * <tr><td>$search</td><td>A free-text search expressionxi to match for whether a particular entry
     * should be included in the feed, e.g. Categories?$search=blue OR green</td></tr>
     * <tr><td>$format</td><td>Return the response in particular formatxii without access to request
     * headers for standard content-type negotiation</td></tr>
     * </tbody>
     * </table>
     * </p>
     *
     * @param uriInfo
     * @return QueryInfo parsed object
     */
    public static QueryInfo queryInfoParser(final UriInfo uriInfo) {
        MultivaluedMap<String, String> qparams = uriInfo.getQueryParameters();
        QueryInfo.Builder builder = QueryInfo.newBuilder();

        for (Map.Entry<String, List<String>> entrySet : qparams.entrySet()) {
            String key = entrySet.getKey();
            List<String> value = entrySet.getValue();
            if (value != null && !value.isEmpty()) {
                switch (key) {
                    case "$inlinecount":
                        builder.setInlineCount(OptionsQueryParser.parseInlineCount(value.get(0)));
                        break;
                    case "$top":
                        builder.setTop(OptionsQueryParser.parseTop(value.get(0)));
                        break;
                    case "$skip":
                        builder.setSkip(OptionsQueryParser.parseSkip(value.get(0)));
                        break;
                    case "$filter":
                        builder.setFilter(OptionsQueryParser.parseFilter(value.get(0)));
                        break;
                    case "$orderby":
                        builder.setOrderBy(OptionsQueryParser.parseOrderBy(value.get(0)));
                        break;
                    case "$skiptoken":
                        builder.setSkipToken(OptionsQueryParser.parseSkipToken(value.get(0)));
                        break;
                    case "$expand":
                        builder.setExpand(OptionsQueryParser.parseExpand(value.get(0)));
                        break;
                    case "$select":
                        builder.setSelect(OptionsQueryParser.parseSelect(value.get(0)));
                        break;
                    default:
                        builder.setCustomOptions(OptionsQueryParser.parseCustomOptions(uriInfo));
                        break;
                }
            }
        }

        return builder.build();
    }

    public static Optional<Map<String, OrderByExpression.Direction>> orderByProcessor(QueryInfo queryInfo) {
        if (queryInfo.orderBy == null) {
            return Optional.absent();
        }
        Map<String, OrderByExpression.Direction> orderBys = new HashMap<>(0);

        for (OrderByExpression ob : queryInfo.orderBy) {
            if (!(ob.getExpression() instanceof EntitySimpleProperty)) {
                throw new UnsupportedOperationException("Query engine only supports simple property expressions");
            }
            String propName = ((EntitySimpleProperty) ob.getExpression()).getPropertyName();
            LOGGER.log(Level.INFO, "OData orderby property was found: {0} with direction: {1}", new Object[]{propName, ob.getDirection()});
            orderBys.put(propName, ob.getDirection());
        }

        if (orderBys.isEmpty()) {
            return Optional.absent();
        } else {
            return Optional.of(orderBys);
        }
    }
    
    public static Optional<Integer> topProcessor(QueryInfo queryInfo) {
        if (queryInfo.top != null && !queryInfo.top.toString().isEmpty()) {
            return Optional.of(queryInfo.top);
        } else {
            return Optional.absent();
        }
    }

    /**
     * Parses only an odata 'skip' entry.
     *
     * @param queryInfo
     * @return optional object
     */
    public static Optional<Integer> skipProcessor(QueryInfo queryInfo) {
        if (queryInfo.skip != null && !queryInfo.skip.toString().isEmpty()) {
            return Optional.of(queryInfo.skip);
        } else {
            return Optional.absent();
        }
    }

    /**
     * Parses only an odata 'inlineCount' entry.
     *
     * @param queryInfo
     * @return optional object
     */
    public static Optional<InlineCount> inlineCntProcessor(QueryInfo queryInfo) {
        if (queryInfo.inlineCount != null && queryInfo.inlineCount.equals(InlineCount.ALLPAGES)) {
            return Optional.of(queryInfo.inlineCount);
        } else {
            return Optional.absent();
        }
    }

    /**
     * Validates the existence of a odata 'filter' entry.
     *
     * @param queryInfo
     * @return optional object
     */
    public static Optional<BoolCommonExpression> filterEval(QueryInfo queryInfo) {
        if (queryInfo.filter != null) {
            return Optional.of(queryInfo.filter);
        } else {
            return Optional.absent();
        }
    }
}
