package pt.ptinovacao.na.portal.webui.restful.commons.audit;

import javax.ws.rs.core.MultivaluedMap;
import javax.xml.bind.DatatypeConverter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;

public class AuditContext {

    public static final String CORRELATION_ID_HEADER_NAME = "CorrelationId";
    public static final String REQUEST_ID_HEADER_NAME = "RequestId";
    public static final String REQUEST_PAYLOAD = "payload";

    private static final int ID_LENGTH = 22;

    private static final ThreadLocal<AuditContext> THREAD_CONTEXT = new ThreadLocal<AuditContext>() {
        @Override
        protected AuditContext initialValue() {
            return new AuditContext(null, null, 0L, null, true, null);
        }
    };

    private final String requestId;
    private final String correlationId;
    private final long startTime;
    private final Locale locale;
    private final Map<String, Object> values;
    private final boolean isDefault;
    private final MultivaluedMap<String, String> requestHttpHeaders;

    private long executionStartTime;
    private Throwable exception;

    private AuditContext(String requestId, String correlationId, long startTime, Locale locale, boolean isDefault, MultivaluedMap<String, String> requestHttpHeaders) {
        this.requestId = requestId == null ? createUniqueIdentifier() : requestId;
        this.correlationId = correlationId == null ? createUniqueIdentifier() : correlationId;
        this.startTime = startTime == 0L ? System.currentTimeMillis() : startTime;
        this.locale = locale == null ? Locale.ROOT : locale;
        this.values = new HashMap<>();
        this.executionStartTime = 0L;
        this.isDefault = isDefault;
        this.requestHttpHeaders = requestHttpHeaders;
    }

    public static AuditContext create(String correlationId, Locale locale, MultivaluedMap<String, String> httpHeaders) {
        if (!isDefault()) {
            String errorMessage = String.format("A context (Correlation-ID '%s' and Request-ID '%s') already exists for the current thread",
                    current().requestId,
                    current().correlationId);

            throw new IllegalStateException(errorMessage);
        }

        AuditContext context = new AuditContext(current().requestId, correlationId, current().startTime, locale, false, httpHeaders);

        THREAD_CONTEXT.set(context);

        return context;
    }

    public static Locale locale() {
        return current().locale;
    }

    public static String requestId() {
        return current().requestId;
    }

    public static String correlationId() {
        return current().correlationId;
    }

    public static long startTime() {
        return current().startTime;
    }

    public static long getTotalElapsedTime() {
        return currentTimeDiference(current().startTime);
    }

    public static long startExecutionTimer() {
        if (current().executionStartTime != 0L) {
            String errorMessage = String.format("Execution timer was already started for this context (Request-ID = '%s').", current().requestId);

            throw new IllegalStateException(errorMessage);
        }

        Date now = new Date();

        current().executionStartTime = now.getTime();

        return current().executionStartTime;
    }

    public static long getCurrentExecutionTime() {
        return current().executionStartTime == 0L ? 0L : currentTimeDiference(current().executionStartTime);
    }

    public static List<String> getHttpHeader(String header) {
        return current().requestHttpHeaders.get(header);
    }

    public static String getFirstHttpHeaderOrEmpty(String header) {
        if (current().requestHttpHeaders.get(header) != null) {
            return current().requestHttpHeaders.get(header).get(0);
        } else {
            return "";
        }
    }

    public static Throwable exception() {
        return current().exception;
    }

    public static void setException(Throwable t) {
        current().exception = t;
    }

    public static <T> void addValue(String name, T value) {
        if (name == null || name.isEmpty()) {
            throw new IllegalArgumentException("Cannot add a context value using a NULL or empty key.");
        }

        current().values.put(name, value);
    }

    public static <T> T getValue(String name, Class<T> valueType) {
        if (name == null || name.isEmpty() || valueType == null) {
            throw new IllegalArgumentException("Cannot search a context value using a NULL or empty key or an NULL type.");
        }

        Object value = current().values.get(name);
        if (value != null && !valueType.isAssignableFrom(value.getClass())) {
            String errorMessage = String.format("Value of property '%s' is instance of class '%s' but the expected type is '%s'.",
                    name,
                    value.getClass().getName(),
                    valueType.getName());

            throw new ClassCastException(errorMessage);
        }

        return (T) value;
    }

    public static AuditContext current() {
        return THREAD_CONTEXT.get();
    }

    public static void clear() {
        THREAD_CONTEXT.remove();
    }

    public static boolean exists() {
        return THREAD_CONTEXT.get() != null;
    }

    private static boolean isDefault() {
        return current().isDefault;
    }

    private static long currentTimeDiference(long start) {
        Date now = new Date();

        return now.getTime() - start;
    }

    private static String createUniqueIdentifier() {
        return DatatypeConverter.printBase64Binary(asByteArray(UUID.randomUUID())).replace('+', '-').replace('/', '_').substring(0, ID_LENGTH);
    }

    private static byte[] asByteArray(UUID uuid) {
        byte[] bUUID = new byte[16];

        long msb = uuid.getMostSignificantBits();
        for (int i = 0; i < 8; i++) {
            bUUID[i] = (byte) (msb >>> 8 * (7 - i));
        }

        long lsb = uuid.getLeastSignificantBits();
        for (int i = 8; i < 16; i++) {
            bUUID[i] = (byte) (lsb >>> 8 * (7 - i));
        }

        return bUUID;
    }

    @Override
    public String toString() {
        return String.format("%s[requestId=%s, correlationId=%s, startTime=%d, locale=%s, values=%s]",
                this.getClass().getSimpleName(),
                requestId,
                correlationId,
                startTime,
                locale,
                values);
    }
}