package pt.ptinovacao.na.portal.webui.restful.commons.db.odata;

import com.google.common.base.Throwables;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.RemovalListener;
import com.google.common.cache.RemovalNotification;
import org.jboss.logmanager.Level;
import org.jboss.logmanager.Logger;
import org.odata4j.producer.jdbc.JdbcModel;

import java.io.Serializable;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;


/**
 * <AUTHOR> <<EMAIL>>
 */
public class MetaModelLoaderSingleton implements Serializable {

    private static final Logger LOGGER = Logger.getLogger(MetaModelLoaderSingleton.class.getName());

    private static final long serialVersionUID = 5741846653861739771L;

    private boolean isLoaded = false;

    private final Cache<String, List<JdbcModel.JdbcColumn>> metaModelCache = CacheBuilder.newBuilder()
            .initialCapacity(0)
            .concurrencyLevel(Runtime.getRuntime().availableProcessors())
            .softValues() // Memory eager storage
            .recordStats()// Enable's statistics
            .removalListener(new RemovalListener<String, List<JdbcModel.JdbcColumn>>() {

                @Override
                public final void onRemoval(final RemovalNotification<String, List<JdbcModel.JdbcColumn>> notification) {
                    try {
                        LOGGER.log(Level.INFO, "LRU Cache removal [{0}]. Cause: {1}", new Object[]{notification.toString(), notification.getCause()});
                    } catch (Throwable t) {//NOSONAR
                        LOGGER.log(Level.SEVERE, "Metamodel loader onRemoval() error", Throwables.getRootCause(t));
                    }
                }
            }).build();

    private MetaModelLoaderSingleton() {
    }

    public static MetaModelLoaderSingleton getInstance() {
        return MetaModelLoaderSingletonHolder.INSTANCE;
    }

    private static class MetaModelLoaderSingletonHolder {

        private static final MetaModelLoaderSingleton INSTANCE = new MetaModelLoaderSingleton();
    }

    /**
     * DOCUMENT ME.
     *
     * @return
     */
    public MetaModelLoaderSingleton loadMetaModelToCache() {
        if (!this.isLoaded) {
            // All went well and loading flag is going to active now....
            this.isLoaded = true;
        }

        return this;
    }

    /**
     * DOCUMENT ME.
     *
     * @param tableName
     * @return
     * @throws ExecutionException
     */
    public List<JdbcModel.JdbcColumn> getColumnsListFromTable(final String tableName, final Connection connection) throws ExecutionException {
        if (!this.isLoaded) {
            throw new IllegalStateException("loadMetaModelToCache method invocation is needed for proper metamodel initialization!");
        }

        return this.metaModelCache.get(tableName, new Callable<List<JdbcModel.JdbcColumn>>() {

            @Override
            public List<JdbcModel.JdbcColumn> call() throws Exception {
                List<JdbcModel.JdbcColumn> jdbcColumns = new ArrayList<>(0);
                try {
                    String tableNameCaseByDBType = (connection.getMetaData().getURL().contains("oracle") == true ? tableName : tableName.toLowerCase());
                    try (ResultSet columns = connection.getMetaData().getColumns(null, null, tableNameCaseByDBType, null)) {
                        JdbcModel jdbcModel = new JdbcModel();

                        while (columns.next()) {
                            String schemaName = columns.getString("TABLE_SCHEM");
                            jdbcModel.getOrCreateSchema(schemaName);
                            String tableName = columns.getString("TABLE_NAME");
                            jdbcModel.getOrCreateTable(schemaName, tableName);
                            String columnName = columns.getString("COLUMN_NAME");
                            jdbcColumns.add(jdbcModel.getOrCreateColumn(schemaName, tableName, columnName));
                        }

                        LOGGER.log(Level.INFO, "Successfully loaded metamodel for table ''{0}''. Columns loaded: {1}",
                                new Object[]{tableName, jdbcColumns.size()});
                    }
                } catch (Throwable t) {//NOSONAR
                    throw new ExecutionException(Throwables.getRootCause(t));
                }

                // Since we cannot return null, an error is thrown as an execution exception...
                if (jdbcColumns.isEmpty()) {
                    throw new ExecutionException(new Throwable("No columns for tablename '"
                            + tableName
                            + "'"));
                }

                return jdbcColumns;
            }
        });
    }
}
