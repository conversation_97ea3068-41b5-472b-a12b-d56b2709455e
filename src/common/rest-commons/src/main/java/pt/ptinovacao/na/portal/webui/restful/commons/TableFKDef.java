/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package pt.ptinovacao.na.portal.webui.restful.commons;

/**
 *
 * <AUTHOR>
 * 
 * alias != null, for overriding table alias
 * literal == false, to use bind variable on query
 * literal == true, to use literal object on query
 */
public class TableFKDef {

    private String alias = null;
    private String field = null;
    private boolean literal = Boolean.FALSE;
    
    public TableFKDef(String field) {
        this.field = field;
    }

    public TableFKDef(String alias, String field) {
        this(field);
        this.alias = alias;
    }
    public TableFKDef(String alias, String field, boolean literal) {
        this(alias, field);
        this.literal = literal;
    }

    public String getAlias() {
        return alias;
    }

    public String getField() {
        return field;
    }

    public boolean isLiteral() {
        return literal;
    }
 
}
