package pt.ptinovacao.naportal.nadm.model;

import pt.ptinovacao.naportal.nadm.NadmMapToXML;

import java.util.Map;
import java.util.HashMap;

public abstract class NadmResource {

    private String type;

    public NadmResource(String type){
        this.type = type;
    }

    private Map<String,String> values = new HashMap<>();

    public String getType(){
        return this.type;
    }

    public void setValues(Map<String, String> values){
        this.values.putAll(values);
    }

    public  Map<String,String> getValues(){
        return values;
    }

    public String toXmlString() {
        return NadmMapToXML.printPayload(this.getValues());
    }

    public String getValue(String key){
        return values.get(key);
    }

}

