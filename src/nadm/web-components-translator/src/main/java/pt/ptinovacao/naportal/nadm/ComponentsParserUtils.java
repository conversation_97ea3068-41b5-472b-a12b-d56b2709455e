package pt.ptinovacao.naportal.nadm;

import com.google.common.base.Strings;
import com.google.common.primitives.Ints;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import pt.ptinovacao.naportal.components.*;

import java.util.ArrayList;
import java.util.List;

final class ComponentsParserUtils {

    private static final String OPTIONS = "options";
    private static final String SELECT_BOX = "selectbox";
    private static final String CHECKBOX = "checkbox";
    private static final String RADIO_BUTTON = "radiobutton";
    private static final String TEXT_AREA = "textarea";
    private static final String TYPE = "type";
    private static final String TIME = "time";
    private static final String DATE = "date";
    private static final String TRUE = "true";
    private static final String ID = "id";
    private static final String STRING = "string";
    private static final String PASSWORD = "password";
    private static final String INT = "int";
    private static final String INTEGER = "integer";
    private static final String DATETIME = "datetime";
    private static final String REGEX_LABEL = "regexLabel";
    private static final String REGEX_KEY = "regexKey";
    private static final String PLACEHOLDER_LABEL = "placeholderLabel";
    private static final String PLACE_HOLDER_KEY = "placeHolderKey";
    private static final String MAX_SIZE = "maxSize";
    private static final String DEFAULT = "default";
    private static final String HEIGHT = "height";
    private static final String LABEL = "label";
    private static final String LABEL_KEY = "labelKey";
    private static final String DEFAULT_VALUE_INDEX = "defaultValueIndex";
    private static final String REQUIRED = "required";
    private static final String VISIBLE = "visible";
    private static final String REGEX = "regex";
    private static final String OPT = "opt";
    private static final String ENABLE = "enable";


    private ComponentsParserUtils(){
        //
    }

    protected static UiComponent parseUiComponent(String type, Element element, boolean ignoreIds, boolean isCreate ){
        switch (type.toLowerCase()) {
            case ID:
                if (ignoreIds) {
                    break;
                }
            case STRING:
            case PASSWORD:
            case INT:
            case INTEGER:
            case TEXT_AREA:
                return parseTextUiComponent(element, isCreate);
            case OPTIONS:
            case SELECT_BOX:
            case CHECKBOX:
            case RADIO_BUTTON:
                return parseSelectUiComponent(element, isCreate);
            case TIME:
            case DATE:
            case DATETIME:
                return parseDateUiComponent(element, isCreate);
            default:
                return null;

        }
        return null;
    }

    /**
     *
     * @param element
     * @return
     */
    public static GroupComponent parseGroupComponent(Element element) {
        GroupComponent groupComponent = new GroupComponent();
        groupComponent.setElementId(Utils.getElementPath(element));
        ComponentsParserUtils.parseComponentParameters(groupComponent, element);
        return groupComponent;
    }

    /**
     *
     * @param element
     * @param isCreate
     * @return
     */
    private static TextUiComponent parseTextUiComponent(Element element, boolean isCreate) {
        TextUiComponent textUiComponent = new TextUiComponent();

        String type = element.getAttribute(TYPE).toLowerCase().trim();
        switch (type) {
            case ID:
            case STRING:
                textUiComponent.setType(TextUiComponent.Type.textField);
                break;
            case PASSWORD:
                textUiComponent.setType(TextUiComponent.Type.passwordField);
                break;
            case INT:
            case INTEGER:
                textUiComponent.setType(TextUiComponent.Type.numberField);
                break;
            case TEXT_AREA:
                textUiComponent.setType(TextUiComponent.Type.textArea);
                break;
            default:
                textUiComponent.setType(TextUiComponent.Type.textField);
                break;
        }
        textUiComponent.setElementId(Utils.getElementPath(element));
        parseComponentParameters(textUiComponent, element);
        parseUiComponentParameters(textUiComponent, element, isCreate);
        textUiComponent.setRegex(parseRegex(element, REGEX_LABEL, REGEX_KEY));
        textUiComponent.setPlaceholder(getI18n(element, PLACEHOLDER_LABEL, PLACE_HOLDER_KEY, false));
        textUiComponent.setMaxSize(Ints.tryParse(element.getAttribute(MAX_SIZE)));
        textUiComponent.setDefaultValue(Strings.emptyToNull(element.getAttribute(DEFAULT)));
        textUiComponent.setHeight(Ints.tryParse(element.getAttribute(HEIGHT)));

        if (elementIsEnabled(element)) {
            return textUiComponent;
        }
        return null;
    }

    /**
     * Utility method to parse SelectableUiComponents {@link SelectableUiComponent}
     * @param element The element containg the SelectableUiComponent
     * @param isCreate
     * @return {@link SelectableUiComponent}
     */
    private  static SelectableUiComponent parseSelectUiComponent(Element element, boolean isCreate) {
        SelectableUiComponent selectableUiComponent = new SelectableUiComponent();
        String type = element.getAttribute(TYPE).toLowerCase().trim();
        switch (type) {
            case OPTIONS:
            case SELECT_BOX:
                selectableUiComponent.setType(SelectableUiComponent.Type.selectBox);
                break;
            case CHECKBOX:
                selectableUiComponent.setType(SelectableUiComponent.Type.checkBox);
                break;
            case RADIO_BUTTON:
                selectableUiComponent.setType(SelectableUiComponent.Type.radioButton);
                break;
            default:
                selectableUiComponent.setType(SelectableUiComponent.Type.selectBox);
                break;
        }
        selectableUiComponent.setElementId(Utils.getElementPath(element));
        parseComponentParameters(selectableUiComponent, element);
        parseUiComponentParameters(selectableUiComponent, element, isCreate);
        selectableUiComponent.setOptions(parseOptions(element));
        selectableUiComponent.setDefaultValueIndex(Ints.tryParse(element.getAttribute(DEFAULT_VALUE_INDEX)));

        if (elementIsEnabled(element)) {
            return selectableUiComponent;
        }
        return null;
    }

    /**
     * Utility method to parse DateUiComponents
     * @param element Element containing DateUiComponents
     * @param isCreate
     * @return {@link DateUiComponent}
     */
    private  static DateUiComponent parseDateUiComponent(Element element, boolean isCreate) {
        DateUiComponent dateUiComponent = new DateUiComponent();
        String type = element.getAttribute(TYPE).toLowerCase().trim();
        switch (type) {
            case TIME:
                dateUiComponent.setType(DateUiComponent.Type.TimePicker);
                break;
            case DATE:
                dateUiComponent.setType(DateUiComponent.Type.DatePicker);
                break;
            default:
                dateUiComponent.setType(DateUiComponent.Type.DateTimePicker);
                break;
        }
        dateUiComponent.setElementId(Utils.getElementPath(element));
        parseComponentParameters(dateUiComponent, element);
        parseUiComponentParameters(dateUiComponent, element , isCreate);

        if (elementIsEnabled(element)) {
            return dateUiComponent;
        }
        return null;
    }

    /**
     * Utility method to parse Component Parameters
     * @param component Element containing the ComponentParameters
     * @param element
     */
    private  static void parseComponentParameters(Component component, Element element) {
        component.setI18n(getI18n(element, LABEL, LABEL_KEY, true));
        component.setRoles(parseRoles(element));
    }

    /**
     * Utility method that parses an {@link Element} and gets its {@link I18n} definition
     * @param element the element to be parsed
     * @param labelName the element name
     * @param labelKeyName the label key name
     * @param useDefault to use the default label
     * @return {@link I18n}
     */
    private static I18n getI18n(org.w3c.dom.Element element, String labelName, String labelKeyName, boolean useDefault) {
        String label = element.getAttribute(labelName);
        String key = element.getAttribute(labelKeyName);

        if ("".equals(label) && "".equals(key)) {
            if (useDefault) {
                return new I18n(element.getLocalName().trim(), key);
            }

            return null;
        }

        return new I18n(label, key);
    }

    /**
     *
     * @param element
     * @param regexLabel
     * @param regexKey
     * @return
     */
    private static Regex parseRegex(org.w3c.dom.Element element, String regexLabel, String regexKey) {
        String regexStr = element.getAttribute(REGEX);
        if (!"".equals(regexStr)) {
            Regex regex = new Regex();
            regex.setExpression(regexStr);
            regex.setI18n(getI18n(element, regexLabel, regexKey, false));
            return regex;
        }
        return null;
    }

    /**
     *
     * @param element
     * @return
     */
    private static Roles parseRoles(org.w3c.dom.Element element) {
        NodeList childList = element.getChildNodes();
        for (int i = 0; i < childList.getLength(); i++) {
            Node childNode = childList.item(i);
            Roles roles = new Roles();
            if (childNode != null && childNode.getNodeType() == Node.ELEMENT_NODE) {
                if ("roles".equals(element.getLocalName().toLowerCase())) {
                    for (int j = 0; j < childList.getLength(); j++) {
                        Node roleNode = childList.item(j);
                        if (roleNode != null && childNode.getNodeType() == Node.ELEMENT_NODE) {
                            roles.addRole(element.getLocalName(), element.getTextContent());
                        }
                    }
                    return roles;
                }
            }
        }
        return null;
    }

    /**
     *
     * @param uiComponent
     * @param element
     */
    private static void parseUiComponentParameters(UiComponent uiComponent, Element element, boolean isCreate) {
        String required = element.getAttribute(REQUIRED);
        String type = element.getAttribute(TYPE);
        if (TRUE.equals(required)) {
            uiComponent.setRequired(Boolean.TRUE);
        }

        if (ID.equals(type) && !isCreate) {
            uiComponent.setEditable(Boolean.FALSE);
        } else {
            uiComponent.setEditable(Boolean.TRUE);
        }
    }

    /**
     *
     * @param element
     * @return
     */
    private static List<Option> parseOptions(Element element) {
        List<Option> options = new ArrayList<>();
        NodeList childList = element.getChildNodes();
        int optionIndex = 0;
        for (int i = 0; i < childList.getLength(); i++) {
            Node childNode = childList.item(i);
            if (childNode != null && childNode.getNodeType() == Node.ELEMENT_NODE) {
                if (OPT.equals(childNode.getLocalName().trim().toLowerCase())) {
                    Option option = new Option();
                    org.w3c.dom.Element opt = (org.w3c.dom.Element) childNode;
                    option.setI18n(getI18n(opt, LABEL, LABEL_KEY, false));
                    option.setIndex(optionIndex++);
                    option.setValue(opt.getTextContent().trim());
                    String visible = opt.getAttribute(VISIBLE);
                    if (Boolean.getBoolean(visible) || "".equals(visible)) {
                        option.setVisible(Boolean.TRUE);
                    }
                    if (elementIsEnabled(opt)) {
                        options.add(option);
                    }
                }
            }
        }
        if (options.isEmpty()) {
            return null;
        }
        return options;
    }

    /**
     * Checks if element is enabled.
     *
     * @param element xml element.
     * @return true if element is enabled, false otherwise.
     */
    static boolean elementIsEnabled(Element element){
        String enabled = element.getAttribute(ENABLE);
        if(enabled == null || enabled.trim().equalsIgnoreCase(TRUE) || enabled.equalsIgnoreCase("")) {
            return true;
        }
        return false;
    }

}
