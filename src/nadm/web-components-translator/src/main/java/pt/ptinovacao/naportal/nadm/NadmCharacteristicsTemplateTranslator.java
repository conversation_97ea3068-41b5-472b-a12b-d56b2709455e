package pt.ptinovacao.naportal.nadm;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import pt.ptinovacao.naportal.components.FormContainer;
import pt.ptinovacao.naportal.components.GroupComponent;
import pt.ptinovacao.naportal.components.UiComponent;
import pt.ptinovacao.naportal.nadm.exceptions.NadmConfigurationExceptionCode;
import pt.ptinovacao.nossis.exceptions.BusinessException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static pt.ptinovacao.nossis.exceptions.ErrorCode.PRECONDITION_FAILED;


public class NadmCharacteristicsTemplateTranslator implements CharacteristicsTranslatorIf {

    private static final String TYPE = "type";
    private static final String TRUE = "true";
    private static final String GROUP = "group";


    private static final String CHARACTERISTICS = "characteristics";
    private static final String SERVICES = "services";
    protected static final String INSTANCE_GROUP = "instanceGroup";
    private static final String NADM_AP_00052 = "NADM_AP_00052";

    private final Map<String, FormContainer> formContainer;
    private final List<String> instanceGroupContainers = new ArrayList<>();

    private final List<String> groupIds;
    private final boolean ignoreIds;
    private final boolean isCreate;

    NadmCharacteristicsTemplateTranslator() {
        this(false, true);
    }

    /**
     * NADM Template parser - parses all the templates to a given model
     *
     * @param ignoreIds ignore id field types
     * @param isCreate identify if Operation type is create
     */
    NadmCharacteristicsTemplateTranslator(boolean ignoreIds, boolean isCreate) {
        this.ignoreIds = ignoreIds;
        this.isCreate = isCreate;
        this.formContainer = new HashMap<>();
        this.groupIds = new ArrayList<>();
        this.formContainer.put(CHARACTERISTICS, new FormContainer());
    }


    @Override
    public void translate(String template) {
        if (template == null) {
            throw new BusinessException(new NadmConfigurationExceptionCode(NADM_AP_00052, PRECONDITION_FAILED));
        }
        try {
            reader(template);
        } catch (Exception e) {
            throw new BusinessException(new NadmConfigurationExceptionCode(NADM_AP_00052, PRECONDITION_FAILED), e);
        }
    }

    @Override
    public void translate(Element template) {
        try {
            if (template == null) {
                throw new BusinessException(new NadmConfigurationExceptionCode(NADM_AP_00052, PRECONDITION_FAILED));
            }

            Element services = Utils.getElements(SERVICES, template);
            if (services != null) {
                services.getParentNode().removeChild(services);
            }

            recurse(template, null, formContainer.get(CHARACTERISTICS));
        } catch (Exception e) {
            throw new BusinessException(new NadmConfigurationExceptionCode(NADM_AP_00052, PRECONDITION_FAILED), e);
        }
    }

    @Override
    public FormContainer getCharacteristics() {
        return formContainer.get(CHARACTERISTICS);
    }

    List<String> getGroupIds() {
        return this.groupIds;
    }

    List<String> getInstanceGroupContainers() {
        return instanceGroupContainers;
    }

    /**
     * @param template
     * @throws Exception
     */
    private void reader(String template) throws Exception {
        Document templateDoc = Utils.loadXMLFromString(template);
        translate(templateDoc.getDocumentElement());
    }

    /**
     * @param el
     * @param current
     * @param curFormContainer
     * @throws Exception
     */
    private void recurse(Element el, GroupComponent current, FormContainer curFormContainer) throws Exception {
        if (el.hasChildNodes()) {
            NodeList childList = el.getChildNodes();
            for (int i = 0; i < childList.getLength(); i++) {
                Node childNode = childList.item(i);
                if (childNode != null && childNode.getNodeType() == Node.ELEMENT_NODE) {
                    Element element = (Element) childNode;
                    String group = element.getAttribute(GROUP);
                    String type = element.getAttribute(TYPE).trim();
                    if (TRUE.equalsIgnoreCase(group) || type.equalsIgnoreCase(GROUP)) {
                        GroupComponent groupComponent = ComponentsParserUtils.parseGroupComponent(element);
                        groupIds.add(groupComponent.getElementId());
                        if (ComponentsParserUtils.elementIsEnabled(element)) {
                            curFormContainer.addComponent(groupComponent);
                        }
                        recurse(element, groupComponent, curFormContainer);
                    } else if (INSTANCE_GROUP.equalsIgnoreCase(type)) {
                        Element igroupEl = Utils.getElementAndRemoveFromDocument(element.getLocalName(), el);
                        instanceGroupContainers.add(Utils.getElementPath(igroupEl));
                    } else if (!"".equals(type)) {
                        UiComponent uiComponent = ComponentsParserUtils.parseUiComponent(type, element, this.ignoreIds, this.isCreate);
                        if (uiComponent != null) {
                            if (current != null) {
                                current.addUiComponent(uiComponent);
                            } else {
                                curFormContainer.addComponent(uiComponent);
                            }
                        }
                        recurse(element, current, curFormContainer);
                    } else {
                        recurse(element, null, curFormContainer);
                    }
                }
            }
        }
    }
}


