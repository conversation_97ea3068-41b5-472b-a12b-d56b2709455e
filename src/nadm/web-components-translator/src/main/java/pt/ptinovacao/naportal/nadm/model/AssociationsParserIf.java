package pt.ptinovacao.naportal.nadm.model;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import pt.ptinovacao.naportal.nadm.request.Request;

/**
 * The Associations generic parser Interface
 */
public interface AssociationsParserIf  {

    /**
     * Parses and full equipment resource configuration response and returns
     * the Resource Configuration that represents the equipment resource allocations
     * @param xml The full equipment response
     * @return {@link ResourceConfigurationIf}
     */
    ResourceConfigurationIf parse(String xml);


    /**
     * Generates the {@link Request} to associate the resources of an given Resource
     * @return  {@link Request} representation of the associations Payload
     */
    Request getAssociationsRequest(String database, String context, String equipmentId, JsonNode nodes, final ObjectMapper mapper);
}
