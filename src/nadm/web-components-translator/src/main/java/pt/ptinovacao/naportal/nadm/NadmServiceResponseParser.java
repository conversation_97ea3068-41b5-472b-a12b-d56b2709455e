package pt.ptinovacao.naportal.nadm;

import org.w3c.dom.Element;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class NadmServiceResponseParser {

    private final ServiceConfigurations serviceConfigurations = new ServiceConfigurations();
    private final boolean isCreate;
    private final List<String> serviceIds;

    private NadmServiceResponseParser() {
        this(Collections.emptyList(), false);
    }

    public NadmServiceResponseParser(List<String> serviceTypes, boolean isCreate) {
        this.isCreate = isCreate;
        this.serviceIds = new ArrayList<>();
        for (String serviceType : serviceTypes) {
            this.serviceIds.add("services.service." + serviceType);
        }
    }

    public ServiceConfigurations parse(String response) {
        Element resDoc = Utils.loadXMLFromString(response).getDocumentElement();
        Utils.evaluateResponseStatus(resDoc);
        if (!isCreate) {
            Utils.getAllValues(Utils.getElements("services", resDoc), serviceConfigurations.getValues(), null);
        }
        Utils.recurseForm(Utils.getElements("template", resDoc), null, serviceConfigurations.getForm(), this.isCreate);
        //NAPORTAL-8249 => failsafe for empty services: { "name" : "n", "clientName" : "c", "serviceData" : { "voiceProfile" : {  } } }
        for (String serviceId : serviceIds) {
            serviceConfigurations.getValues().remove(serviceId);
        }
        return serviceConfigurations;
    }

}
