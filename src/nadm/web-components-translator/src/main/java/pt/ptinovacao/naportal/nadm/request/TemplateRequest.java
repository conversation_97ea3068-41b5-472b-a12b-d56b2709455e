package pt.ptinovacao.naportal.nadm.request;

import pt.ptinovacao.naportal.nadm.NadmMapToXML;
import pt.ptinovacao.naportal.nadm.Utils;

import java.util.Map;

/**
 * Created by filipe-m-tavares on 30-03-2020.
 */
public class TemplateRequest extends AbsRequest {

    private static final String TEMPLATE_REQUEST =
            "<equipments>\n" +
                    "   <equipment>\n" +
                    "       <equipmentData>\n" +
                    "           <deviceInfo>\n" +
                    "               <manufacturer>$manufacturer</manufacturer>\n" +
                    "               <modelName>$model</modelName>\n" +
                    "               <softwareVersion>$version</softwareVersion>\n" +
                    "               <firmwareFilename>$firmwareFilename</firmwareFilename>\n" +
                    "           </deviceInfo>\n" +
                    "       </equipmentData>\n" +
                    "   </equipment>\n" +
                    " </equipments>\n";
    private static final String PAYLOAD = "$payload";
    private static final String DESCRIPTION = "$description";
    private static final String MANUFACTURER = "$manufacturer";
    private static final String MODEL = "$model";
    private static final String VERSION = "$version";
    private static final String FIRMWARE_FILENAME = "$firmwareFilename";
    private static final String EQUIPMENTS_EQUIPMENT_EQUIPMENT_DATA_DEVICE_INFO_MANUFACTURER = "equipments.equipment.equipmentData.deviceInfo.manufacturer";
    private static final String EQUIPMENTS_EQUIPMENT_EQUIPMENT_DATA_DEVICE_INFO_MODEL_NAME = "equipments.equipment.equipmentData.deviceInfo.modelName";
    private static final String EQUIPMENTS_EQUIPMENT_EQUIPMENT_DATA_DEVICE_INFO_SOFTWARE_VERSION = "equipments.equipment.equipmentData.deviceInfo.softwareVersion";
    private static final String EQUIPMENTS_EQUIPMENT_EQUIPMENT_DATA_DEVICE_INFO_FIRMWARE_FILENAME = "equipments.equipment.equipmentData.deviceInfo.firmwareFilename";

    private final String manufacturer, model, version, firmwareFilename;


    public TemplateRequest(String database, String context, String manufacturer, String model, String version, String firmwareFilename) {
        super(database, context);
        this.manufacturer = Utils.validateXMLChars(manufacturer);
        this.model = Utils.validateXMLChars(model);
        this.version = Utils.validateXMLChars(version);
        this.firmwareFilename = Utils.validateXMLChars(firmwareFilename) ;
    }

    @Override
    public Request getRead() {
        return new Request(baseUri() + "naacs/read/template",
                BASE_REQUEST.replace(PAYLOAD,
                        TEMPLATE_REQUEST
                                .replace(MANUFACTURER, manufacturer)
                                .replace(MODEL, model)
                                .replace(VERSION, version)
                                .replace(FIRMWARE_FILENAME, firmwareFilename))
                        .replace(DESCRIPTION, "na-portal - read-template"));
    }

    public Request getReadAll() {
        return new Request(baseUri() + "naacs/read/templates",
                BASE_REQUEST.replace(PAYLOAD,"")
                        .replace(DESCRIPTION, "na-portal - read-all-templates"));
    }

    @Override
    public Request getCreate(Map<String, String> params) {
        fillMMV(params);

        return new Request(baseUri()+"naacs/create/templates", BASE_REQUEST
                .replace(PAYLOAD, NadmMapToXML.printPayload(params))
                .replace(DESCRIPTION, "na-portal - create-template"));
    }

    private void fillMMV(Map<String, String> params) {
        params.put(EQUIPMENTS_EQUIPMENT_EQUIPMENT_DATA_DEVICE_INFO_MANUFACTURER, manufacturer);
        params.put(EQUIPMENTS_EQUIPMENT_EQUIPMENT_DATA_DEVICE_INFO_MODEL_NAME, model);
        params.put(EQUIPMENTS_EQUIPMENT_EQUIPMENT_DATA_DEVICE_INFO_SOFTWARE_VERSION, version);
        params.put(EQUIPMENTS_EQUIPMENT_EQUIPMENT_DATA_DEVICE_INFO_FIRMWARE_FILENAME, firmwareFilename);
    }


    @Override
    public Request getUpdate(Map<String, String> params) {
        fillMMV(params);

        return new Request(baseUri()+"naacs/update/template:firmwareFilename", BASE_REQUEST
                .replace(PAYLOAD, NadmMapToXML.printPayload(params))
                .replace(DESCRIPTION, "na-portal - update-template"));
    }

    @Override
    public Request getDelete() {
        return new Request(baseUri() + "naacs/delete/templates",
                BASE_REQUEST.replace(PAYLOAD,
                        TEMPLATE_REQUEST
                                .replace(MANUFACTURER, manufacturer))
                        .replace(MODEL, model)
                        .replace(VERSION, version)
                        .replace(FIRMWARE_FILENAME, firmwareFilename)
                        .replace(DESCRIPTION, "na-portal - delete-template"));
    }
}
