<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ns2:templateHolder xmlns:ns2="http://nadm.ptinovacao.pt">
    <clients>
        <client>
            <name required="true" type="id"/>
        </client>
    </clients>
    <equipments>
        <equipment label="Equipment" labelKey="NADM.Equipment" type="group">
            <name label="MAC Address" labelKey="NADM.name" regex="^([a-fA-F0-9]{2}(:)){5}[a-fA-F0-9]{2}$"
                  required="true" type="id"/>
            <equipmentData>
                <deviceInfo>
                    <time label="Time" labelKey="NADM.deviceInfo.time" type="group">
                        <dateformat label="Date format" labelKey="NADM.deviceInfo.time.dateformat" type="options">
                            <opt>WWW MMM DD</opt>
                            <opt>DD-MMM-YY</opt>
                            <opt>YYYY-MM-DD</opt>
                            <opt>DD/MM/YYYY</opt>
                            <opt>MM/DD/YY</opt>
                            <opt>MM/DD/YY</opt>
                            <opt>DD MMM YYYY</opt>
                            <opt>WWW DD MMM</opt>
                        </dateformat>
                        <timeformat label="Time format" labelKey="NADM.deviceInfo.time.timeformat" type="options">
                            <opt>24hr</opt>
                            <opt>12hr</opt>
                        </timeformat>
                        <timezone label="Timezone" labelKey="NADM.deviceInfo.time.timezone" type="options">
                            <opt>-11:00 Samoa</opt>
                            <opt>-10:00 United States-Hawaii-Aleutian</opt>
                            <opt>-10:00 United States-Alaska-Aleutian</opt>
                            <opt>-09:00 United States-Alaska Time</opt>
                            <opt>-08:00 Canada(Vancouver,Whitehorse)</opt>
                            <opt>-08:00 Mexico(Tijuana,Mexicali)</opt>
                            <opt>-08:00 United States-Pacific Time</opt>
                            <opt>-07:00 Canada(Edmonton,Calgary)</opt>
                            <opt>-07:00 Mexico(Mazatlan,Chihuahua)</opt>
                            <opt>-07:00 United States-Mountain Time</opt>
                            <opt>-07:00 United States-MST no DST</opt>
                            <opt>-06:00 Canada-Manitoba(Winnipeg)</opt>
                            <opt>-06:00 Chile(Easter Islands)</opt>
                            <opt>-06:00 Mexico(Mexico City,Acapulco)</opt>
                            <opt>-06:00 United States-Central Time</opt>
                            <opt>-05:00 Bahamas(Nassau)</opt>
                            <opt>-05:00 Canada(Montreal,Ottawa,Quebec)</opt>
                            <opt>-05:00 Cuba(Havana)</opt>
                            <opt>-05:00 United States-Eastern Time</opt>
                            <opt>-04:30 Venezuela(Caracas)</opt>
                            <opt>-04:00 Canada(Halifax,Saint John)</opt>
                            <opt>-04:00 Chile(Santiago)</opt>
                            <opt>-04:00 Paraguay(Asuncion)</opt>
                            <opt>-04:00 United Kingdom-Bermuda(Bermuda)</opt>
                            <opt>-04:00 United Kingdom(Falkland Islands)</opt>
                            <opt>-04:00 Trinidad and Tobago</opt>
                            <opt>-03:30 Canada-New Foundland(St.Johns)</opt>
                            <opt>-03:00 Denmark-Greenland(Nuuk)</opt>
                            <opt>-03:00 Argentina(Buenos Aires)</opt>
                            <opt>-03:00 Brazil(no DST)</opt>
                            <opt>-03:00 Brazil(DST)</opt>
                            <opt>-02:00 Brazil(no DST)</opt>
                            <opt>-01:00 Portugal(Azores)</opt>
                            <opt>0 GMT</opt>
                            <opt>0 Greenland</opt>
                            <opt>0 Denmark-Faroe Islands(Torshavn)</opt>
                            <opt>0 Ireland(Dublin)</opt>
                            <opt>0 Portugal(Lisboa,Porto,Funchal)</opt>
                            <opt>0 Spain-Canary Islands(Las Palmas)</opt>
                            <opt>0 United Kingdom(London)</opt>
                            <opt>0 Morocco</opt>
                            <opt>+01:00 Albania(Tirane)</opt>
                            <opt>+01:00 Austria(Vienna)</opt>
                            <opt>+01:00 Belgium(Brussels)</opt>
                            <opt>+01:00 Chad</opt>
                            <opt>+01:00 Croatia(Zagreb)</opt>
                            <opt>+01:00 Czech Republic(Prague)</opt>
                            <opt>+01:00 Denmark(Kopenhagen)</opt>
                            <opt>+01:00 France(Paris)</opt>
                            <opt>+01:00 Germany(Berlin)</opt>
                            <opt>+01:00 Hungary(Budapest)</opt>
                            <opt>+01:00 Italy(Rome)</opt>
                            <opt>+01:00 Luxembourg(Luxembourg)</opt>
                            <opt>+01:00 Macedonia(Skopje)</opt>
                            <opt>+01:00 Netherlands(Amsterdam)</opt>
                            <opt>+01:00 Namibia(Windhoek)</opt>
                            <opt>+02:00 Estonia(Tallinn)</opt>
                            <opt>+02:00 Finland(Helsinki)</opt>
                            <opt>+02:00 Gaza Strip(Gaza)</opt>
                            <opt>+02:00 Greece(Athens)</opt>
                            <opt>+02:00 Israel(Tel Aviv)</opt>
                            <opt>+02:00 Jordan(Amman)</opt>
                            <opt>+02:00 Latvia(Riga)</opt>
                            <opt>+02:00 Lebanon(Beirut)</opt>
                            <opt>+02:00 Moldova(Kishinev)</opt>
                            <opt>+02:00 Russia(Kaliningrad)</opt>
                            <opt>+02:00 Romania(Bucharest)</opt>
                            <opt>+02:00 Syria(Damascus)</opt>
                            <opt>+02:00 Turkey(Ankara)</opt>
                            <opt>+02:00 Ukraine(Kyiv,Odessa)</opt>
                            <opt>+03:00 East Africa Time</opt>
                            <opt>+03:00 Iraq(Baghdad)</opt>
                            <opt>+03:00 Russia(Moscow)</opt>
                            <opt>+03:30 Iran(Teheran)</opt>
                            <opt>+04:00 Armenia(Yerevan)</opt>
                            <opt>+04:00 Azerbaijan(Baku)</opt>
                            <opt>+04:00 Georgia(Tbilisi)</opt>
                            <opt>+04:00 Kazakhstan(Aktau)</opt>
                            <opt>+04:00 Russia(Samara)</opt>
                            <opt>+04:30 Afghanistan</opt>
                            <opt>+05:00 Kazakhstan(Aqtobe)</opt>
                            <opt>+05:00 Kyrgyzstan(Bishkek)</opt>
                            <opt>+05:00 Pakistan(Islamabad)</opt>
                            <opt>+05:00 Russia(Chelyabinsk)</opt>
                            <opt>+05:30 India(Calcutta)</opt>
                            <opt>+06:00 Kazakhstan(Astana,Almaty)</opt>
                            <opt>+06:00 Russia(Novosibirsk,Omsk)</opt>
                            <opt>+07:00 Russia(Krasnoyarsk)</opt>
                            <opt>+07:00 Thailand(Bangkok)</opt>
                            <opt>+08:00 China(Beijing)</opt>
                            <opt>+08:00 Singapore(Singapore)</opt>
                            <opt>+08:00 Australia(Perth)</opt>
                            <opt>+09:00 Japan(Tokyo)</opt>
                            <opt>+09:00 Korea(Seoul)</opt>
                            <opt>+09:30 Australia(Darwin)</opt>
                            <opt>+09:30 Australia(Adelaide)</opt>
                            <opt>+10:00 Russia(Vladivostok)</opt>
                            <opt>+10:00 Australia(Hobart)</opt>
                            <opt>+10:00 Australia(Brisbane)</opt>
                            <opt>+10:00 Australia(Sydney,Melbourne,Canberra)</opt>
                            <opt>+10:30 Australia(Lord Howe Islands)</opt>
                            <opt>+11:00 New Caledonia(Noumea)</opt>
                            <opt>+12:00 New Zealand(Wellington,Auckland)</opt>
                            <opt>+12:45 New Zealand(Chatham Islands)</opt>
                            <opt>+13:00 Tonga(Nukualofa)</opt>
                        </timezone>
                    </time>
                    <daylightSavingTime label="Daylight Saving Time" labelKey="NADM.deviceInfo.daylightSavingTime"
                                        type="group">
                        <enable label="Enable" labelKey="NADM.deviceInfo.daylightSavingTime.enable" type="options">
                            <opt>Manual</opt>
                            <opt>Disabled</opt>
                            <opt>Automatic</opt>
                        </enable>
                        <type label="Type" labelKey="NADM.deviceInfo.daylightSavingTime.type" type="options">
                            <opt>By Date</opt>
                            <opt>By Weekday</opt>
                        </type>
                        <startWeekDay label="Start Week Day" labelKey="NADM.deviceInfo.daylightSavingTime.startWeekDay"
                                      type="options">
                            <opt>Monday</opt>
                            <opt>Tuesday</opt>
                            <opt>Wednesday</opt>
                            <opt>Thursday</opt>
                            <opt>Friday</opt>
                            <opt>Saturday</opt>
                            <opt>Sunday</opt>
                        </startWeekDay>
                        <startWeekDayOcurrence label="Start Week Day Ocurrence"
                                               labelKey="NADM.deviceInfo.daylightSavingTime.startWeekDayOcurrence"
                                               type="options">
                            <opt>1st occurrence of the day-of-week in the month</opt>
                            <opt>2nd occurrence of the day-of-week in the month</opt>
                            <opt>3rd occurrence of the day-of-week in the month</opt>
                            <opt>4th occurrence of the day-of-week in the month</opt>
                            <opt>Last occurrence of the day-of-week in the month</opt>
                        </startWeekDayOcurrence>
                        <startMonth label="Start Month" labelKey="NADM.deviceInfo.daylightSavingTime.startMonth"
                                    type="options">
                            <opt>1</opt>
                            <opt>2</opt>
                            <opt>3</opt>
                            <opt>4</opt>
                            <opt>5</opt>
                            <opt>6</opt>
                            <opt>7</opt>
                            <opt>8</opt>
                            <opt>9</opt>
                            <opt>10</opt>
                            <opt>11</opt>
                            <opt>12</opt>
                        </startMonth>
                        <startDay label="Start Day" labelKey="NADM.deviceInfo.daylightSavingTime.startDay"
                                  type="options">
                            <opt>1</opt>
                            <opt>2</opt>
                            <opt>3</opt>
                            <opt>4</opt>
                            <opt>5</opt>
                            <opt>6</opt>
                            <opt>7</opt>
                            <opt>8</opt>
                            <opt>9</opt>
                            <opt>10</opt>
                            <opt>11</opt>
                            <opt>12</opt>
                            <opt>13</opt>
                            <opt>14</opt>
                            <opt>15</opt>
                            <opt>16</opt>
                            <opt>17</opt>
                            <opt>18</opt>
                            <opt>19</opt>
                            <opt>20</opt>
                            <opt>21</opt>
                            <opt>22</opt>
                            <opt>23</opt>
                            <opt>24</opt>
                            <opt>25</opt>
                            <opt>26</opt>
                            <opt>27</opt>
                            <opt>28</opt>
                            <opt>29</opt>
                            <opt>30</opt>
                            <opt>31</opt>
                        </startDay>
                        <startHour label="Start Hour" labelKey="NADM.deviceInfo.daylightSavingTime.startHour"
                                   type="options">
                            <opt>00h</opt>
                            <opt>01h</opt>
                            <opt>02h</opt>
                            <opt>03h</opt>
                            <opt>04h</opt>
                            <opt>05h</opt>
                            <opt>06h</opt>
                            <opt>07h</opt>
                            <opt>08h</opt>
                            <opt>09h</opt>
                            <opt>10h</opt>
                            <opt>11h</opt>
                            <opt>12h</opt>
                            <opt>13h</opt>
                            <opt>14h</opt>
                            <opt>15h</opt>
                            <opt>16h</opt>
                            <opt>17h</opt>
                            <opt>18h</opt>
                            <opt>19h</opt>
                            <opt>20h</opt>
                            <opt>21h</opt>
                            <opt>22h</opt>
                            <opt>23h</opt>
                        </startHour>
                        <endWeekDay label="End Week Day" labelKey="NADM.deviceInfo.daylightSavingTime.endWeekDay"
                                    type="options">
                            <opt>Monday</opt>
                            <opt>Tuesday</opt>
                            <opt>Wednesday</opt>
                            <opt>Thursday</opt>
                            <opt>Friday</opt>
                            <opt>Saturday</opt>
                            <opt>Sunday</opt>
                        </endWeekDay>
                        <endWeekDayOcurrence label="End Week Day Ocurrence"
                                             labelKey="NADM.deviceInfo.daylightSavingTime.endWeekDayOcurrence"
                                             type="options">
                            <opt>1st occurrence of the day-of-week in the month</opt>
                            <opt>2nd occurrence of the day-of-week in the month</opt>
                            <opt>3rd occurrence of the day-of-week in the month</opt>
                            <opt>4th occurrence of the day-of-week in the month</opt>
                            <opt>Last occurrence of the day-of-week in the month</opt>
                        </endWeekDayOcurrence>
                        <endMonth label="End Month" labelKey="NADM.deviceInfo.daylightSavingTime.endMonth"
                                  type="options">
                            <opt>1</opt>
                            <opt>2</opt>
                            <opt>3</opt>
                            <opt>4</opt>
                            <opt>5</opt>
                            <opt>6</opt>
                            <opt>7</opt>
                            <opt>8</opt>
                            <opt>9</opt>
                            <opt>10</opt>
                            <opt>11</opt>
                            <opt>12</opt>
                        </endMonth>
                        <endDay label="End Day" labelKey="NADM.deviceInfo.daylightSavingTime.endDay" type="options">
                            <opt>1</opt>
                            <opt>2</opt>
                            <opt>3</opt>
                            <opt>4</opt>
                            <opt>5</opt>
                            <opt>6</opt>
                            <opt>7</opt>
                            <opt>8</opt>
                            <opt>9</opt>
                            <opt>10</opt>
                            <opt>11</opt>
                            <opt>12</opt>
                            <opt>13</opt>
                            <opt>14</opt>
                            <opt>15</opt>
                            <opt>16</opt>
                            <opt>17</opt>
                            <opt>18</opt>
                            <opt>19</opt>
                            <opt>20</opt>
                            <opt>21</opt>
                            <opt>22</opt>
                            <opt>23</opt>
                            <opt>24</opt>
                            <opt>25</opt>
                            <opt>26</opt>
                            <opt>27</opt>
                            <opt>28</opt>
                            <opt>29</opt>
                            <opt>30</opt>
                            <opt>31</opt>
                        </endDay>
                        <endHour label="End Hour" labelKey="NADM.deviceInfo.daylightSavingTime.endHour" type="options">
                            <opt>00h</opt>
                            <opt>01h</opt>
                            <opt>02h</opt>
                            <opt>03h</opt>
                            <opt>04h</opt>
                            <opt>05h</opt>
                            <opt>06h</opt>
                            <opt>07h</opt>
                            <opt>08h</opt>
                            <opt>09h</opt>
                            <opt>10h</opt>
                            <opt>11h</opt>
                            <opt>12h</opt>
                            <opt>13h</opt>
                            <opt>14h</opt>
                            <opt>15h</opt>
                            <opt>16h</opt>
                            <opt>17h</opt>
                            <opt>18h</opt>
                            <opt>19h</opt>
                            <opt>20h</opt>
                            <opt>21h</opt>
                            <opt>22h</opt>
                            <opt>23h</opt>
                        </endHour>
                        <offset label="Offset" labelKey="NADM.deviceInfo.daylightSavingTime.offset" type="options">
                            <opt>30 min</opt>
                            <opt>60 min</opt>
                            <opt>90 min</opt>
                            <opt>120 min</opt>
                            <opt>150 min</opt>
                            <opt>180 min</opt>
                            <opt>210 min</opt>
                            <opt>240 min</opt>
                            <opt>270 min</opt>
                            <opt>300 min</opt>
                        </offset>
                    </daylightSavingTime>
                    <DNS label="DNS" labelKey="NADM.deviceInfo.DNS" type="group">
                        <primary label="Primary DNS" labelKey="NADM.deviceInfo.DNS.primary"
                                 regex="^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
                                 type="string"/>
                        <secondary label="Secondary DNS" labelKey="NADM.deviceInfo.DNS.secondary"
                                   regex="^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
                                   type="string"/>
                    </DNS>
                    <NTP label="NTP" labelKey="NADM.deviceInfo.NTP" type="group">
                        <primary label="Primary NTP" labelKey="NADM.deviceInfo.NTP.primary" type="string"/>
                        <secondary label="Secondary NTP" labelKey="NADM.deviceInfo.NTP.secondary" type="string"/>
                    </NTP>
                    <lan label="Lan" labelKey="NADM.deviceInfo.lan" type="group">
                        <addressingType label="Addressing Type" labelKey="NADM.deviceInfo.lan.addressingType"
                                        type="options">
                            <opt>DHCP</opt>
                            <opt>Static IP</opt>
                        </addressingType>
                        <ipAddress label="IP Address" labelKey="NADM.deviceInfo.lan.ipAddress"
                                   regex="^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
                                   type="string"/>
                        <subnetMask label="Subnet Mask" labelKey="NADM.deviceInfo.lan.subnetMask"
                                    regex="^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
                                    type="string"/>
                        <defaultGateway label="Default Gateway" labelKey="NADM.deviceInfo.lan.defaultGateway"
                                        regex="^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
                                        type="string"/>
                    </lan>
                    <ldap label="LDAP" labelKey="NADM.deviceInfo.ldap" type="group">
                        <enable label="Enable" labelKey="NADM.deviceInfo.ldap.enable" type="options">
                            <opt>true</opt>
                            <opt>false</opt>
                        </enable>
                        <host label="Host" labelKey="NADM.deviceInfo.ldap.host" type="string"/>
                        <port label="Port" labelKey="NADM.deviceInfo.ldap.port" type="int"/>
                        <username label="Username" labelKey="NADM.deviceInfo.ldap.username" type="string"/>
                        <password label="Password" labelKey="NADM.deviceInfo.ldap.password" type="password"/>
                        <base label="Base" labelKey="NADM.deviceInfo.ldap.base" type="string"/>
                        <sort label="Sort" labelKey="NADM.deviceInfo.ldap.sort" type="options">
                            <opt>true</opt>
                            <opt>false</opt>
                        </sort>
                        <nameFilter label="Name Filter" labelKey="NADM.deviceInfo.ldap.nameFilter" type="string"/>
                        <numberFilter label="Number Filter" labelKey="NADM.deviceInfo.ldap.numberFilter" type="string"/>
                        <displayNameAttributes label="Display Name Attributes"
                                               labelKey="NADM.deviceInfo.ldap.displayNameAttributes" type="string"/>
                        <displayNumberAttributes label="Display Number Attributes"
                                                 labelKey="NADM.deviceInfo.ldap.displayNumberAttributes" type="string"/>
                        <displayName label="Display Name" labelKey="NADM.deviceInfo.ldap.displayName" type="string"/>
                        <lookup label="Lookup" labelKey="NADM.deviceInfo.ldap.lookup" type="options">
                            <opt>Disabled</opt>
                            <opt>Incoming Calls</opt>
                            <opt>Outgoing Calls</opt>
                            <opt>Incoming and Outgoing Calls</opt>
                        </lookup>
                        <mode label="Mode" labelKey="NADM.deviceInfo.ldap.mode" type="options">
                            <opt>LDAP</opt>
                        </mode>
                        <version label="Version" labelKey="NADM.deviceInfo.ldap.version" type="options">
                            <opt>v2</opt>
                            <opt>v3</opt>
                        </version>
                        <maxHits label="Max Hits" labelKey="NADM.deviceInfo.ldap.maxHits" type="int"/>
                    </ldap>
                </deviceInfo>
                <genericPhone label="Generic Phone" labelKey="NADM.genericPhone" type="group">
                    <language label="Language" labelKey="NADM.genericPhone.language" type="options">
                        <opt>English</opt>
                        <opt>Chinese(Ch)</opt>
                        <opt>Chinese Traditional</opt>
                        <opt>French</opt>
                        <opt>German</opt>
                        <opt>Italian</opt>
                        <opt>Polish</opt>
                        <opt>Portuguese</opt>
                        <opt>Spanish</opt>
                        <opt>Turkish</opt>
                    </language>
                    <voiceVlanId label="Voice Vlan Id" labelKey="NADM.genericPhone.voiceVlanId" type="integer"/>
                    <dataVlanId label="Data Vlan Id" labelKey="NADM.genericPhone.dataVlanId" type="integer"/>
                    <userPassword label="User Password" labelKey="NADM.genericPhone.userPassword" type="password"/>
                    <adminPassword label="Admin Password" labelKey="NADM.genericPhone.adminPassword" type="password"/>
                    <backgroundImageURL label="Background Image URL" labelKey="NADM.genericPhone.backgroundImageURL"
                                        type="string"/>
                    <backgroundImageSelect label="Background Image Select"
                                           labelKey="NADM.genericPhone.backgroundImageSelect" type="options">
                        <opt>Disabled</opt>
                        <opt>Yealink Logo</opt>
                        <opt>Custom Logo</opt>
                    </backgroundImageSelect>
                    <callPickupCode label="CallPickup Code" labelKey="NADM.genericPhone.callPickupCode" type="string"/>
                    <callPickupAudioNotification label="Call Pickup Audio Notification"
                                                 labelKey="NADM.genericPhone.callPickupAudioNotification"
                                                 type="options">
                        <opt>Enabled</opt>
                        <opt>Disabled</opt>
                    </callPickupAudioNotification>
                    <blfSubscriptionTimeout label="BLF Subscription Time Out"
                                            labelKey="NADM.genericPhone.blfSubscriptionTimeout" type="string"/>
                    <customKeys label="Custom Keys" labelKey="NADM.genericPhone.customKeys">
                        <customKey label="Custom Key" labelKey="NADM.genericPhone.customKeys.customKey"
                                   maxInstances="29" type="instance">
                            <keyId label="Key ID" labelKey="NADM.genericPhone.customKeys.customKey.keyId"
                                   required="true" type="options">
                                <opt>1</opt>
                                <opt>2</opt>
                                <opt>3</opt>
                                <opt>4</opt>
                                <opt>5</opt>
                                <opt>6</opt>
                                <opt>7</opt>
                                <opt>8</opt>
                                <opt>9</opt>
                                <opt>10</opt>
                                <opt>11</opt>
                                <opt label="1st key of the screen center"
                                     labelKey="customKey.keyId.1stKeyOfTheScreenCenter">12
                                </opt>
                                <opt>13</opt>
                                <opt>14</opt>
                                <opt>15</opt>
                                <opt>16</opt>
                                <opt>17</opt>
                                <opt>18</opt>
                                <opt>19</opt>
                                <opt>20</opt>
                                <opt>21</opt>
                                <opt>22</opt>
                                <opt>23</opt>
                                <opt>24</opt>
                                <opt>25</opt>
                                <opt>26</opt>
                                <opt>27</opt>
                                <opt>28</opt>
                                <opt>29</opt>
                            </keyId>
                            <line label="Line" labelKey="NADM.genericPhone.customKeys.customKey.line" required="true"
                                  type="options">
                                <opt>1</opt>
                                <opt>2</opt>
                                <opt>3</opt>
                                <opt>4</opt>
                                <opt>5</opt>
                                <opt>6</opt>
                            </line>
                            <name label="Name" labelKey="NADM.genericPhone.customKeys.customKey.name" required="true"
                                  type="string"/>
                            <monitor label="Monitor" labelKey="NADM.genericPhone.customKeys.customKey.monitor"
                                     required="true" type="string"/>
                            <type label="Custom key type" labelKey="NADM.genericPhone.customKeys.customKey.type"
                                  required="true" type="options">
                                <opt label="Speed Dial" labelKey="customKey.type.opt.SpeedDial">speedDial</opt>
                                <opt label="Call Pickup" labelKey="customKey.type.opt.CallPickup">callPickup</opt>
                                <opt label="BLF with speedDial" labelKey="customKey.type.opt.blfWithSpeedDial">
                                    speedDial+blf
                                </opt>
                                <opt label="BLF with Call Pickup and Speed Dial"
                                     labelKey="customKey.type.opt.blfWithCallPickupAndSpeedDial">
                                    callPickup+speedDial+blf
                                </opt>
                            </type>
                        </customKey>
                    </customKeys>
                    <dialplan label="Dial Plan" labelKey="NADM.genericPhone.dialplan" type="group">
                        <rule label="Rule" labelKey="NADM.genericPhone.dialplan.rule" type="string"/>
                        <areaCode label="Area Code" labelKey="NADM.genericPhone.dialplan.areaCode" type="group">
                            <code label="Code" labelKey="NADM.genericPhone.dialplan.areaCode.code" type="string"/>
                            <minLength label="Min Length" labelKey="NADM.genericPhone.dialplan.areaCode.minLength"
                                       type="string"/>
                            <maxLength label="Max Length" labelKey="NADM.genericPhone.dialplan.areaCode.maxLength"
                                       type="string"/>
                        </areaCode>
                        <blockOut label="Block Out" labelKey="NADM.genericPhone.dialplan.blockOut" type="string"/>
                        <dialNow label="Dial Now" labelKey="NADM.genericPhone.dialplan.dialNow" type="string"/>
                    </dialplan>
                </genericPhone>
                <firmwareUpgrade label="Firmware Upgrade" labelKey="NADM.firmwareUpgrade" type="group">
                    <roles>
                        <oper>view</oper>
                        <user>block</user>
                    </roles>
                    <enable label="Enable" labelKey="NADM.firmwareUpgrade.enable" required="true" type="options">
                        <opt>true</opt>
                        <opt>false</opt>
                    </enable>
                    <expectedVersion label="Expected Version" labelKey="NADM.firmwareUpgrade.expectedVersion"
                                     required="true" type="options">
                        <opt>35.72.0.30</opt>
                        <opt>35.80.0.95</opt>
                    </expectedVersion>
                    <migrateConfig label="Migrate Config" labelKey="NADM.firmwareUpgrade.migrateConfig" required="true"
                                   type="options">
                        <opt>true</opt>
                        <opt>false</opt>
                    </migrateConfig>
                    <scheduleEnabled label="Schedule Enabled" labelKey="NADM.firmwareUpgrade.scheduleEnabled"
                                     required="true" type="options">
                        <opt>true</opt>
                        <opt>false</opt>
                    </scheduleEnabled>
                    <scheduleStartTime label="Schedule Start Time" labelKey="NADM.firmwareUpgrade.scheduleStartTime"
                                       type="time"/>
                    <scheduleEndTime label="Schedule End Time" labelKey="NADM.firmwareUpgrade.scheduleEndTime"
                                     type="time"/>
                    <file label="Firmware file" labelKey="NADM.firmwareUpgrade.file" type="string"/>
                    <customUrl label="Custom URL" labelKey="NADM.firmwareUpgrade.customUrl" type="string"/>
                </firmwareUpgrade>
            </equipmentData>
            <services label="Services" labelKey="NADM.services">
                <service label="Service" labelKey="NADM.services.service" maxInstances="6" type="instance">
                    <name label="Name" labelKey="NADM.services.service.name" required="true" type="id"/>
                    <serviceData label="Service Data" labelKey="NADM.services.service.serviceData">
                        <voiceProfile label="Voice Profile" labelKey="NADM.services.service.voiceProfile"
                                      maxInstances="6" type="subInstance">
                            <line label="Line" labelKey="NADM.services.service.voiceProfile.line" required="true"
                                  type="integer"/>
                            <password label="Password" labelKey="NADM.services.service.voiceProfile.password"
                                      type="password"/>
                            <codecs label="Codecs" labelKey="NADM.services.service.voiceProfile.codecs" type="options">
                                <opt>G711u</opt>
                                <opt>G711a</opt>
                                <opt>G729</opt>
                                <opt>G722</opt>
                            </codecs>
                            <dialplan label="Dial Plan" labelKey="NADM.service.service.voiceProfile.dialplan"
                                      type="string"/>
                            <proxy label="Proxy" labelKey="NADM.services.service.voiceProfile.proxy" type="string"/>
                            <outboundproxy label="OutboundProxy"
                                           labelKey="NADM.services.service.voiceProfile.outboundproxy" type="string"/>
                        </voiceProfile>
                    </serviceData>
                </service>
            </services>
            <advancedData label="Advanced Data" labelKey="NADM.advancedData" type="textArea "/>
        </equipment>
    </equipments>
</ns2:templateHolder>