<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<response>
    <uuid>d77fcef0-ef4d-41f4-bb06-b46ef27731e4</uuid>
    <status>NOK</status>
    <code>NADM_CL_10019</code>
    <data>
        <NadmResponse>
            <equipments>
                <equipment>
                    <name>A4:4C:11:9F:68:84</name>
                    <clientName>99988</clientName>
                    <equipmentData>
                        <deviceInfo>
                            <manufacturer>cisco</manufacturer>
                            <modelName>SPA502G</modelName>
                            <softwareVersion>7.5.2b</softwareVersion>
                            <firstContactTime>1396014528277</firstContactTime>
                            <lastContactTime>1448579327317</lastContactTime>
                            <lastConfigurationTime>1396013273782</lastConfigurationTime>
                        </deviceInfo>
                        <genericPhone/>
                    </equipmentData>
                    <services>
                        <service>
                            <name><EMAIL></name>
                            <clientName>99988</clientName>
                            <serviceData>
                                <voiceProfile>
                                    <line>1</line>
                                    <codecs>G729</codecs>
                                    <dialplan></dialplan>
                                    <proxy>service.unify.telecom.pt</proxy>
                                    <outboundproxy>***********</outboundproxy>
                                    <displayName>Loja 451</displayName>
                                    <password>dummyPwd</password>
                                </voiceProfile>
                            </serviceData>
                        </service>
                    </services>
                </equipment>
                <clientEquipment>
                    <clientName>99988</clientName>
                    <equipmentData>
                        <deviceInfo>
                            <manufacturer>cisco</manufacturer>
                            <modelName>SPA502G</modelName>
                            <softwareVersion>7.5.2b</softwareVersion>
                            <time>
                                <dateformat>day/month</dateformat>
                                <timeformat>24hr</timeformat>
                                <timezone>GMT</timezone>
                            </time>
                            <DNS>
                                <primary>************</primary>
                                <secondary>************</secondary>
                            </DNS>
                            <NTP>
                                <primary>************</primary>
                                <secondary>************</secondary>
                            </NTP>
                            <lan>
                                <addressingType>DHCP</addressingType>
                                <ipAddress></ipAddress>
                                <subnetMask></subnetMask>
                                <defaultGateway></defaultGateway>
                            </lan>
                        </deviceInfo>
                        <genericPhone>
                            <language>Portuguese</language>
                            <speakerGain>0</speakerGain>
                            <handsetGain>0</handsetGain>
                            <headsetGain>0</headsetGain>
                        </genericPhone>
                    </equipmentData>
                    <advancedData> </advancedData>
                </clientEquipment>
                <globalEquipment>
                    <equipmentData>
                        <deviceInfo>
                            <manufacturer>cisco</manufacturer>
                            <modelName>SPA502G</modelName>
                            <softwareVersion>7.5.2b</softwareVersion>
                        </deviceInfo>
                    </equipmentData>
                </globalEquipment>
            </equipments>
            <template>
                <ns2:templateHolder xmlns:ns2="http://nadm.ptinovacao.pt">
                    <clients>
                        <client>
                            <name required="true" type="id"/>
                        </client>
                    </clients>
                    <equipments>
                        <equipment group="true" label="Equipment" labelKey="NADM.Equipment">
                            <name label="MAC Address" labelKey="NADM.name" regex="^([a-fA-F0-9]\{2\}(:|\-))\{5\}[a-fA-F0-9]\{2\}$" required="true" type="id"/>
                            <equipmentData>
                                <deviceInfo>
                                    <time group="true" label="Time" labelKey="NADM.deviceInfo.time">
                                        <dateformat label="Date format" labelKey="NADM.deviceInfo.time.dateformat" type="options">
                                            <opt>day/month</opt>
                                            <opt>month/day</opt>
                                        </dateformat>
                                        <timeformat label="Time format" labelKey="NADM.deviceInfo.time.timeformat" type="options">
                                            <opt>24hr</opt>
                                            <opt>12hr</opt>
                                        </timeformat>
                                        <timezone label="Timezone" labelKey="NADM.deviceInfo.time.timezone" type="options">
                                            <opt>GMT-12:00</opt>
                                            <opt>GMT-11:00</opt>
                                            <opt>GMT-10:00</opt>
                                            <opt>GMT-09:00</opt>
                                            <opt>GMT-08:00</opt>
                                            <opt>GMT-07:00</opt>
                                            <opt>GMT-06:00</opt>
                                            <opt>GMT-05:00</opt>
                                            <opt>GMT-04:00</opt>
                                            <opt>GMT-03:30</opt>
                                            <opt>GMT-03:00</opt>
                                            <opt>GMT-02:00</opt>
                                            <opt>GMT-01:00</opt>
                                            <opt>GMT</opt>
                                            <opt>GMT+01:00</opt>
                                            <opt>GMT+02:00</opt>
                                            <opt>GMT+03:00</opt>
                                            <opt>GMT+03:30</opt>
                                            <opt>GMT+04:00</opt>
                                            <opt>GMT+05:00</opt>
                                            <opt>GMT+05:30</opt>
                                            <opt>GMT+05:45</opt>
                                            <opt>GMT+06:00</opt>
                                            <opt>GMT+06:30</opt>
                                            <opt>GMT+07:00</opt>
                                            <opt>GMT+08:00</opt>
                                            <opt>GMT+09:00</opt>
                                            <opt>GMT+09:30</opt>
                                            <opt>GMT+10:00</opt>
                                            <opt>GMT+11:00</opt>
                                            <opt>GMT+12:00</opt>
                                            <opt>GMT+13:00</opt>
                                        </timezone>
                                    </time>
                                    <daylightSavingTime group="true" label="Daylight Saving Time" labelKey="NADM.deviceInfo.daylightSavingTime">
                                        <enable label="Enable" labelKey="NADM.deviceInfo.daylightSavingTime.enable" type="options">
                                            <opt>Manual</opt>
                                            <opt>Disabled</opt>
                                        </enable>
                                        <type label="Type" labelKey="NADM.deviceInfo.daylightSavingTime.type" type="options">
                                            <opt>By Date</opt>
                                            <opt>By Weekday</opt>
                                        </type>
                                        <startWeekDay label="Start Week Day" labelKey="NADM.deviceInfo.daylightSavingTime.startWeekDay" type="options">
                                            <opt>Monday</opt>
                                            <opt>Tuesday</opt>
                                            <opt>Wednesday</opt>
                                            <opt>Thursday</opt>
                                            <opt>Friday</opt>
                                            <opt>Saturday</opt>
                                            <opt>Sunday</opt>
                                        </startWeekDay>
                                        <startWeekDayOcurrence label="Start Week Day Ocurrence" labelKey="NADM.deviceInfo.daylightSavingTime.startWeekDayOcurrence" type="options">
                                            <opt>1st occurrence of the day-of-week in the month</opt>
                                            <opt>2nd occurrence of the day-of-week in the month</opt>
                                            <opt>3rd occurrence of the day-of-week in the month</opt>
                                            <opt>4th occurrence of the day-of-week in the month</opt>
                                            <opt>Last occurrence of the day-of-week in the month</opt>
                                        </startWeekDayOcurrence>
                                        <startMonth label="Start Month" labelKey="NADM.deviceInfo.daylightSavingTime.startMonth" type="options">
                                            <opt>1</opt>
                                            <opt>2</opt>
                                            <opt>3</opt>
                                            <opt>4</opt>
                                            <opt>5</opt>
                                            <opt>6</opt>
                                            <opt>7</opt>
                                            <opt>8</opt>
                                            <opt>9</opt>
                                            <opt>10</opt>
                                            <opt>11</opt>
                                            <opt>12</opt>
                                        </startMonth>
                                        <startDay label="Start Day" labelKey="NADM.deviceInfo.daylightSavingTime.startDay" type="options">
                                            <opt>1</opt>
                                            <opt>2</opt>
                                            <opt>3</opt>
                                            <opt>4</opt>
                                            <opt>5</opt>
                                            <opt>6</opt>
                                            <opt>7</opt>
                                            <opt>8</opt>
                                            <opt>9</opt>
                                            <opt>10</opt>
                                            <opt>11</opt>
                                            <opt>12</opt>
                                            <opt>13</opt>
                                            <opt>14</opt>
                                            <opt>15</opt>
                                            <opt>16</opt>
                                            <opt>17</opt>
                                            <opt>18</opt>
                                            <opt>19</opt>
                                            <opt>20</opt>
                                            <opt>21</opt>
                                            <opt>22</opt>
                                            <opt>23</opt>
                                            <opt>24</opt>
                                            <opt>25</opt>
                                            <opt>26</opt>
                                            <opt>27</opt>
                                            <opt>28</opt>
                                            <opt>29</opt>
                                            <opt>30</opt>
                                            <opt>31</opt>
                                        </startDay>
                                        <startHour label="Start Hour" labelKey="NADM.deviceInfo.daylightSavingTime.startHour" type="options">
                                            <opt>00h</opt>
                                            <opt>01h</opt>
                                            <opt>02h</opt>
                                            <opt>03h</opt>
                                            <opt>04h</opt>
                                            <opt>05h</opt>
                                            <opt>06h</opt>
                                            <opt>07h</opt>
                                            <opt>08h</opt>
                                            <opt>09h</opt>
                                            <opt>10h</opt>
                                            <opt>11h</opt>
                                            <opt>12h</opt>
                                            <opt>13h</opt>
                                            <opt>14h</opt>
                                            <opt>15h</opt>
                                            <opt>16h</opt>
                                            <opt>17h</opt>
                                            <opt>18h</opt>
                                            <opt>19h</opt>
                                            <opt>20h</opt>
                                            <opt>21h</opt>
                                            <opt>22h</opt>
                                            <opt>23h</opt>
                                        </startHour>
                                        <endWeekDay label="End Week Day" labelKey="NADM.deviceInfo.daylightSavingTime.endWeekDay" type="options">
                                            <opt>Monday</opt>
                                            <opt>Tuesday</opt>
                                            <opt>Wednesday</opt>
                                            <opt>Thursday</opt>
                                            <opt>Friday</opt>
                                            <opt>Saturday</opt>
                                            <opt>Sunday</opt>
                                        </endWeekDay>
                                        <endWeekDayOcurrence label="End Week Day Ocurrence" labelKey="NADM.deviceInfo.daylightSavingTime.endWeekDayOcurrence" type="options">
                                            <opt>1st occurrence of the day-of-week in the month</opt>
                                            <opt>2nd occurrence of the day-of-week in the month</opt>
                                            <opt>3rd occurrence of the day-of-week in the month</opt>
                                            <opt>4th occurrence of the day-of-week in the month</opt>
                                            <opt>Last occurrence of the day-of-week in the month</opt>
                                        </endWeekDayOcurrence>
                                        <endMonth label="End Month" labelKey="NADM.deviceInfo.daylightSavingTime.endMonth" type="options">
                                            <opt>1</opt>
                                            <opt>2</opt>
                                            <opt>3</opt>
                                            <opt>4</opt>
                                            <opt>5</opt>
                                            <opt>6</opt>
                                            <opt>7</opt>
                                            <opt>8</opt>
                                            <opt>9</opt>
                                            <opt>10</opt>
                                            <opt>11</opt>
                                            <opt>12</opt>
                                        </endMonth>
                                        <endDay label="End Day" labelKey="NADM.deviceInfo.daylightSavingTime.endDay" type="options">
                                            <opt>1</opt>
                                            <opt>2</opt>
                                            <opt>3</opt>
                                            <opt>4</opt>
                                            <opt>5</opt>
                                            <opt>6</opt>
                                            <opt>7</opt>
                                            <opt>8</opt>
                                            <opt>9</opt>
                                            <opt>10</opt>
                                            <opt>11</opt>
                                            <opt>12</opt>
                                            <opt>13</opt>
                                            <opt>14</opt>
                                            <opt>15</opt>
                                            <opt>16</opt>
                                            <opt>17</opt>
                                            <opt>18</opt>
                                            <opt>19</opt>
                                            <opt>20</opt>
                                            <opt>21</opt>
                                            <opt>22</opt>
                                            <opt>23</opt>
                                            <opt>24</opt>
                                            <opt>25</opt>
                                            <opt>26</opt>
                                            <opt>27</opt>
                                            <opt>28</opt>
                                            <opt>29</opt>
                                            <opt>30</opt>
                                            <opt>31</opt>
                                        </endDay>
                                        <endHour label="End Hour" labelKey="NADM.deviceInfo.daylightSavingTime.endHour" type="options">
                                            <opt>00h</opt>
                                            <opt>01h</opt>
                                            <opt>02h</opt>
                                            <opt>03h</opt>
                                            <opt>04h</opt>
                                            <opt>05h</opt>
                                            <opt>06h</opt>
                                            <opt>07h</opt>
                                            <opt>08h</opt>
                                            <opt>09h</opt>
                                            <opt>10h</opt>
                                            <opt>11h</opt>
                                            <opt>12h</opt>
                                            <opt>13h</opt>
                                            <opt>14h</opt>
                                            <opt>15h</opt>
                                            <opt>16h</opt>
                                            <opt>17h</opt>
                                            <opt>18h</opt>
                                            <opt>19h</opt>
                                            <opt>20h</opt>
                                            <opt>21h</opt>
                                            <opt>22h</opt>
                                            <opt>23h</opt>
                                        </endHour>
                                        <offset label="Offset" labelKey="NADM.deviceInfo.daylightSavingTime.offset" type="options">
                                            <opt>30 min</opt>
                                            <opt>60 min</opt>
                                            <opt>90 min</opt>
                                            <opt>120 min</opt>
                                            <opt>150 min</opt>
                                            <opt>180 min</opt>
                                            <opt>210 min</opt>
                                            <opt>240 min</opt>
                                            <opt>270 min</opt>
                                            <opt>300 min</opt>
                                        </offset>
                                    </daylightSavingTime>
                                    <lan group="true" label="Lan" labelKey="NADM.deviceInfo.lan">
                                        <addressingType label="Addressing Type" labelKey="NADM.deviceInfo.lan.addressingType" type="options">
                                            <opt>DHCP</opt>
                                            <opt>Static IP</opt>
                                        </addressingType>
                                        <ipAddress label="IP Address" labelKey="NADM.deviceInfo.lan.ipAddress" regex="^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$" type="string"/>
                                        <subnetMask label="Subnet Mask" labelKey="NADM.deviceInfo.lan.subnetMask" regex="^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$" type="string"/>
                                        <defaultGateway label="Default Gateway" labelKey="NADM.deviceInfo.lan.defaultGateway" regex="^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$" type="string"/>
                                    </lan>
                                    <DNS group="true" label="DNS" labelKey="NADM.deviceInfo.DNS">
                                        <primary label="primary" labelKey="NADM.deviceInfo.DNS.primary" regex="^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$" type="string"/>
                                        <secondary label="secondary" labelKey="NADM.deviceInfo.DNS.secondary" regex="^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$" type="string"/>
                                    </DNS>
                                    <NTP group="true" label="NTP" labelKey="NADM.deviceInfo.NTP">
                                        <primary label="primary" labelKey="NADM.deviceInfo.NTP." type="string"/>
                                        <secondary label="secondary" labelKey="NADM.deviceInfo.NTP." type="string"/>
                                    </NTP>
                                    <ldap group="true" label="LDAP" labelKey="NADM.deviceInfo.ldap">
                                        <enable label="Enable" labelKey="NADM.deviceInfo.ldap.enable" type="options">
                                            <opt>true</opt>
                                            <opt>false</opt>
                                        </enable>
                                        <host label="Host" labelKey="NADM.deviceInfo.ldap.host" type="string"/>
                                        <port label="Port" labelKey="NADM.deviceInfo.ldap.port" type="int"/>
                                        <username label="Username" labelKey="NADM.deviceInfo.ldap.username" type="string"/>
                                        <password label="Password" labelKey="NADM.deviceInfo.ldap.password" type="password"/>
                                        <base label="Base" labelKey="NADM.deviceInfo.ldap.base" type="string"/>
                                        <clientDn label="clientDn" labelKey="NADM.deviceInfo.ldap.clientDn" type="string"/>
                                        <firstNameFilter label="First Name Filter" labelKey="NADM.deviceInfo.ldap.firstNameFilter" type="string"/>
                                        <lastNameFilter label="Last Name Filter" labelKey="NADM.deviceInfo.ldap.lastNameFilter" type="string"/>
                                        <searchItem3 label="searchItem3" labelKey="NADM.deviceInfo.ldap.searchItem3" type="string"/>
                                        <searchFilter3 label="searchFilter3" labelKey="NADM.deviceInfo.ldap.searchFilter3" type="string"/>
                                        <searchItem4 label="searchItem4" labelKey="NADM.deviceInfo.ldap.searchItem4" type="string"/>
                                        <searchFilter4 label="searchFilter4" labelKey="NADM.deviceInfo.ldap.searchFilter4" type="string"/>
                                        <displayAttributes label="Display Attributes" labelKey="NADM.deviceInfo.ldap.displayAttributes" type="string"/>
                                        <mode label="Mode" labelKey="NADM.deviceInfo.ldap.mode" type="options">
                                            <opt>LDAP</opt>
                                            <opt>LDAPS</opt>
                                        </mode>
                                        <version label="Version" labelKey="NADM.deviceInfo.ldap.version" type="options">
                                            <opt>v2</opt>
                                            <opt>v3</opt>
                                        </version>
                                        <authMethod label="Auth Method" labelKey="NADM.deviceInfo.ldap.authMethod" type="options">
                                            <opt>None</opt>
                                            <opt>Simple</opt>
                                            <opt>MD5-Digest</opt>
                                        </authMethod>
                                        <numberMapping label="Number Mapping" labelKey="NADM.deviceInfo.ldap.numberMapping" type="string"/>
                                        <corpDirName label="Corp Dir Name" labelKey="NADM.deviceInfo.ldap.corpDirName" type="string"/>
                                    </ldap>
                                </deviceInfo>
                                <genericPhone group="true" label="Generic Phone" labelKey="NADM.genericPhone">
                                    <language label="Language" labelKey="NADM.genericPhone.language" type="options">
                                        <opt>Portuguese</opt>
                                        <opt>Danish</opt>
                                        <opt>Dutch</opt>
                                        <opt>English</opt>
                                        <opt>French</opt>
                                        <opt>German</opt>
                                        <opt>Italian</opt>
                                        <opt>Norwegian</opt>
                                        <opt>Spanish</opt>
                                        <opt>Swedish</opt>
                                    </language>
                                    <voiceVlanId label="Voice Vlan Id" labelKey="NADM.genericPhone.voiceVlanId" type="integer"/>
                                    <dataVlanId label="Data Vlan Id" labelKey="NADM.genericPhone.dataVlanId" type="integer"/>
                                    <speakerGain label="Speaker Gain" labelKey="NADM.genericPhone.speakerGain" type="options">
                                        <opt>-6</opt>
                                        <opt>0</opt>
                                        <opt>6</opt>
                                    </speakerGain>
                                    <handsetGain label="Handset Gain" labelKey="NADM.genericPhone.handsetGain" type="options">
                                        <opt>-6</opt>
                                        <opt>0</opt>
                                        <opt>6</opt>
                                    </handsetGain>
                                    <headsetGain label="Headset Gain" labelKey="NADM.genericPhone.headsetGain" type="options">
                                        <opt>-6</opt>
                                        <opt>0</opt>
                                        <opt>6</opt>
                                        <opt>12</opt>
                                        <opt>-12</opt>
                                        <opt>-18</opt>
                                    </headsetGain>
                                    <userPassword label="User Password" labelKey="NADM.genericPhone.userPassword" type="password"/>
                                    <adminPassword label="Admin Password" labelKey="NADM.genericPhone.adminPassword" type="password"/>
                                    <screensaverEnable label="Screensaver Enable" labelKey="NADM.genericPhone.screensaverEnable" type="options">
                                        <opt>Yes</opt>
                                        <opt>No</opt>
                                    </screensaverEnable>
                                    <screensaverWaitTime label="Screensaver WaitTime" labelKey="NADM.genericPhone.screensaverWaitTime" type="integer"/>
                                    <screensaverImageSelect label="Screensaver ImageSelect" labelKey="NADM.genericPhone.screensaverImageSelect" type="options">
                                        <opt>Station Time</opt>
                                        <opt>Lock</opt>
                                        <opt>Phone</opt>
                                        <opt>DateTime</opt>
                                        <opt>Background Picture</opt>
                                    </screensaverImageSelect>
                                    <backgroundImageURL label="Background Image URL" labelKey="NADM.genericPhone.backgroundImageURL" type="string"/>
                                    <backgroundImageText label="Background Text" labelKey="NADM.genericPhone.backgroundImageText" type="string"/>
                                    <backgroundImageSelect label="Background Image Select" labelKey="NADM.genericPhone.backgroundImageSelect" type="options">
                                        <opt>None</opt>
                                        <opt>BMP Picture</opt>
                                        <opt>Text Logo</opt>
                                    </backgroundImageSelect>
                                    <callPickupCode label="CallPickup Code" labelKey="NADM.genericPhone.callPickupCode" type="string"/>
                                    <callPickupAudioNotification label="Call Pickup Audio Notification" labelKey="NADM.genericPhone.callPickupAudioNotification" type="options">
                                        <opt>Enabled</opt>
                                        <opt>Disabled</opt>
                                    </callPickupAudioNotification>
                                    <callPickupTone label="callPickup Tone" labelKey="NADM.genericPhone.callPickupTone" type="string"/>
                                    <blfSubscriptionTimeout label="BLF Subscription Time Out" labelKey="NADM.genericPhone.blfSubscriptionTimeout" type="string"/>
                                    <customKeys label="customKeys" labelKey="NADM.genericPhone.customKeys">
                                        <customKey label="Custom Key" labelKey="NADM.genericPhone.customKeys.customKey" maxInstances="1" type="instance">
                                            <keyId label="Key ID" labelKey="NADM.genericPhone.customKeys.customKey.keyId" required="true" type="options">
                                                <opt>1</opt>
                                            </keyId>
                                            <line label="Line" labelKey="NADM.genericPhone.customKeys.customKey.line" required="true" type="options">
                                                <opt>1</opt>
                                            </line>
                                            <name label="Name" labelKey="NADM.genericPhone.customKeys.customKey.name" required="true" type="string"/>
                                            <monitor label="Monitor" labelKey="NADM.genericPhone.customKey.monitor" required="true" type="string"/>
                                            <type label="Custom key type" labelKey="NADM.genericPhone.customKeys.customKey.type" required="true" type="options">
                                                <opt label="Speed Dial">speedDial</opt>
                                                <opt label="BLF with speedDial">speedDial+blf</opt>
                                                <opt label="BLF with callPickup">callPickup+blf</opt>
                                                <opt label="BLF with Call Pickup and Speed Dial">callPickup+speedDial+blf</opt>
                                                <opt label="BLF">blf</opt>
                                            </type>
                                        </customKey>
                                    </customKeys>
                                    <stationName label="Station Name" labelKey="NADM.genericPhone.stationName" type="string"/>
                                    <dialplan group="true" label="Dial Plan" labelKey="NADM.genericPhone.dialplan">
                                        <rule label="Rule" labelKey="NADM.genericPhone.dialplan.rule" type="string"/>
                                    </dialplan>
                                </genericPhone>
                                <firmwareUpgrade group="true" label="Firmware Upgrade" labelKey="NADM.firmwareUpgrade">
                                    <roles>
                                        <oper>view</oper>
                                        <user>block</user>
                                    </roles>
                                    <enable label="Enable" labelKey="NADM.firmwareUpgrade.enable" required="true" type="options">
                                        <opt>true</opt>
                                        <opt>false</opt>
                                    </enable>
                                    <expectedVersion label="Expected Version" labelKey="NADM.firmwareUpgrade.expectedVersion" required="true" type="options">
                                        <opt>7.5.2b</opt>
                                        <opt>7.5.6</opt>
                                    </expectedVersion>
                                    <migrateConfig label="Migrate Config" labelKey="NADM.firmwareUpgrade.migrateConfig" required="true" type="options">
                                        <opt>true</opt>
                                        <opt>false</opt>
                                    </migrateConfig>
                                    <scheduleEnabled label="Schedule Enabled" labelKey="NADM.firmwareUpgrade.scheduleEnabled" required="true" type="options">
                                        <opt>true</opt>
                                        <opt>false</opt>
                                    </scheduleEnabled>
                                    <scheduleStartTime label="Schedule Start Time" labelKey="NADM.firmwareUpgrade.scheduleStartTime" type="time"/>
                                    <scheduleEndTime label="Schedule End Time" labelKey="NADM.firmwareUpgrade.scheduleEndTime" type="time"/>
                                    <file label="Firmware file" labelKey="NADM.firmwareUpgrade.file" type="string"/>
                                    <customUrl label="Custum URL" labelKey="NADM.firmwareUpgrade.customUrl" type="string"/>
                                </firmwareUpgrade>
                            </equipmentData>
                            <services label="Services" labelKey="NADM.services">
                                <service label="Service" labelKey="NADM.services.service" maxInstances="1" type="instance">
                                    <name label="Name" labelKey="NADM.services.service.name" required="true" type="id"/>
                                    <serviceData label="Service Data" labelKey="NADM.services.service.serviceData">
                                        <voiceProfile label="Voice Profile" labelKey="NADM.services.service.voiceProfile" maxInstances="1" type="subInstance">
                                            <line label="Line" labelKey="NADM.services.service.voiceProfile.line" required="true" type="integer"/>
                                            <password label="Password" labelKey="NADM.services.service.voiceProfile.password" type="password"/>
                                            <codecs label="Codecs" labelKey="NADM.services.service.voiceProfile.codecs" type="options">
                                                <opt>G729</opt>
                                                <opt>G711u</opt>
                                                <opt>G711a</opt>
                                                <opt>G722</opt>
                                            </codecs>
                                            <dialplan label="Dial Plan" labelKey="NADM.service.service.voiceProfile.dialplan" type="string"/>
                                            <proxy label="proxy" labelKey="NADM.services.service.voiceProfile.proxy" type="string"/>
                                            <outboundproxy label="OutboundProxy" labelKey="NADM.services.service.voiceProfile.outboundproxy" type="string"/>
                                        </voiceProfile>
                                    </serviceData>
                                </service>
                            </services>
                            <advancedData label="Advanced Data" labelKey="NADM.advancedData" type="string"/>
                        </equipment>
                    </equipments>
                </ns2:templateHolder>
            </template>
        </NadmResponse>
    </data>
</response>