<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<response>
    <uuid>331eba77-49a4-4c0e-8c88-a41ae1e6e1a2</uuid>
    <description>read-service (success test)</description>
    <status>OK</status>
    <data>
        <NadmResponse>
            <template>
                <ns2:templateHolder xmlns:ns2="http://nadm.ptinovacao.pt">
                    <services label="Services" labelKey="NADM.services">
                        <service label="Service" labelKey="NADM.services.service" type="instance">
                            <name label="Name" labelKey="NADM.services.service.name" required="true" type="id"/>
                            <clientName label="Client" labelKey="NADM.services.service.name" required="true" type ="id"/>
                            <serviceData label="Service Data" labelKey="NADM.services.service.serviceData">
                                <voiceProfile label="Voice Profile" labelKey="NADM.services.service.voiceProfile" type="subInstance">
                                    <displayName label="Display Name" labelKey="NADM.services.service.voiceProfile.displayName" type="string"/>
                                    <password label="Password" labelKey="NADM.services.service.voiceProfile.password" type="password"/>
                                    <proxy label="Proxy" labelKey="NADM.services.service.voiceProfile.proxy" type="string"/>
                                    <outboundproxy label="OutboundProxy" labelKey="NADM.services.service.voiceProfile.outboundproxy" type="string"/>
                                </voiceProfile>
                            </serviceData>
                        </service>
                    </services>
                </ns2:templateHolder>
            </template>
        </NadmResponse>
    </data>
</response>