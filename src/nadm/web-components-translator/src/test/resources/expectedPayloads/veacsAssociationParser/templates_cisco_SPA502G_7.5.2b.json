{"serviceData.voiceProfile": {"i18n": {"label": "Voice Profile", "i18nKey": "NADM.services.service.voiceProfile"}, "template": {"componentList": [{"@type": "TextUiComponent", "elementId": "service.name", "i18n": {"label": "Name", "i18nKey": "NADM.services.service.name"}, "required": true, "editable": false, "type": "textField"}, {"@type": "SelectableUiComponent", "elementId": "service.serviceData.voiceProfile.line", "i18n": {"label": "Line", "i18nKey": "NADM.services.service.voiceProfile.line"}, "required": true, "editable": true, "type": "selectBox", "options": [{"index": 0, "value": "1"}]}, {"@type": "TextUiComponent", "elementId": "service.serviceData.voiceProfile.displayName", "i18n": {"label": "Display Name", "i18nKey": "NADM.services.service.voiceProfile.displayName"}, "editable": true, "type": "textField"}, {"@type": "TextUiComponent", "elementId": "service.serviceData.voiceProfile.password", "i18n": {"label": "Password", "i18nKey": "NADM.services.service.voiceProfile.password"}, "editable": true, "type": "passwordField"}, {"@type": "TextUiComponent", "elementId": "service.serviceData.voiceProfile.proxy", "i18n": {"label": "Proxy", "i18nKey": "NADM.services.service.voiceProfile.proxy"}, "editable": true, "type": "textField"}, {"@type": "TextUiComponent", "elementId": "service.serviceData.voiceProfile.outboundproxy", "i18n": {"label": "OutboundProxy", "i18nKey": "NADM.services.service.voiceProfile.outboundproxy"}, "editable": true, "type": "textField"}]}, "resourceName": "service.name", "resourceKey": "service.serviceData.voiceProfile.line", "service": true, "maxInstances": 1}}