package pt.ptinovacao.naportal.nadm.request;

import org.junit.Assert;
import org.junit.Test;
import org.w3c.dom.Element;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.fail;
import static pt.ptinovacao.naportal.nadm.TestUtils.assertXmlElementsChildsAreEqual;
import static pt.ptinovacao.naportal.nadm.TestUtils.loadXmlFromPayloadAndGoToElement;
import static pt.ptinovacao.naportal.nadm.TestUtils.readTestFileFromResources;

/**
 * Created by hen<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 9/27/16.
 * Unit tests for the equipment request class
 */
public class EquipmentRequestTest {

    private static final String BASE_PATH = "src/test/resources/expectedPayloads/";
    private static final String DATABASE = "test";
    private static final String CONTEXT = "test";
    private static final String BASE_URI = "/management-nadm/" + DATABASE + "/nadm/" + CONTEXT + "/naacs/";
    private static final String CLIENT_NAME = "nadm";
    private static final String EQUIPMENT_NAME = "1A:2B:3C:4D:5E:6F";
    private static final EquipmentRequest REQUEST = new EquipmentRequest(DATABASE, CONTEXT, CLIENT_NAME, EQUIPMENT_NAME);

    @Test
    public void testGetCreate_success() {
        try {
            // prepare expectations
            final String uri = BASE_URI + "create/equipment";
            final String payload = readTestFileFromResources(BASE_PATH + "equipment/create.xml")
                    .replace("$clientName", CLIENT_NAME)
                    .replace("$equipmentName", EQUIPMENT_NAME);
            Request expectedResponse = new Request(uri, payload);

            // prepare params
            Map<String, String> params = new HashMap<>();
            params.put("equipments.equipment.name", EQUIPMENT_NAME);

            // run test
            Request response = REQUEST.getCreate(params);
            Element actualResponseXml = loadXmlFromPayloadAndGoToElement(response.getPayload(),
                    "data/NadmRequest/equipments/equipment");
            Element expectedResponseXml = loadXmlFromPayloadAndGoToElement(expectedResponse.getPayload(),
                    "data/NadmRequest/equipments/equipment");

            Assert.assertEquals(expectedResponse.getUri(), response.getUri());

            assertXmlElementsChildsAreEqual(actualResponseXml, expectedResponseXml, new String [] {
                    "clientName",
                    "name"
            });
        } catch (Exception e) {
            fail(e.getMessage());
        }
    }

    @Test
    public void testGetRead_success() {
        try {
            // prepare expectations
            final String uri = BASE_URI + "read/equipmentAndServices?configClient=true&configGlobal=true";
            final String payload = readTestFileFromResources(BASE_PATH + "equipment/read.xml")
                    .replace("$equipmentName", EQUIPMENT_NAME);
            Request expectedResponse = new Request(uri, payload);

            // run test
            Request response = REQUEST.getRead();
            Element actualResponseXml = loadXmlFromPayloadAndGoToElement(response.getPayload(),
                    "data/NadmRequest/equipments/equipment");
            Element expectedResponseXml = loadXmlFromPayloadAndGoToElement(expectedResponse.getPayload(),
                    "data/NadmRequest/equipments/equipment");

            Assert.assertEquals(expectedResponse.getUri(), response.getUri());
            assertXmlElementsChildsAreEqual(actualResponseXml, expectedResponseXml, new String [] {
                    "name"
            });
        } catch (Exception e) {
            fail(e.getMessage());
        }
    }

    @Test
    public void testGetUpdate_success() {
        try {
            // prepare expectations
            final String uri = BASE_URI + "update/equipment?updateResources=false";
            final String dateFormat = "day/month";
            final String timeFormat = "12h";
            final String timezone = "GMT+1";
            final String firstNameFilter = "cn:(|(cn=$VALUE*)(givenName=$VALUE*))";
            final String payload = readTestFileFromResources(BASE_PATH + "equipment/update.xml")
                    .replace("$clientName", CLIENT_NAME)
                    .replace("$equipmentName", EQUIPMENT_NAME)
                    .replace("$dateFormat", dateFormat)
                    .replace("$timeFormat", timeFormat)
                    .replace("$timezone", timezone)
                    .replace("$firstNameFilter", firstNameFilter);
            Request expectedResponse = new Request(uri, payload);

            // prepare params
            Map<String, String> params = new HashMap<>();
            params.put("equipments.equipment.equipmentData.deviceInfo.time.dateformat", dateFormat);
            params.put("equipments.equipment.equipmentData.deviceInfo.time.timeformat", timeFormat);
            params.put("equipments.equipment.equipmentData.deviceInfo.time.timezone", timezone);
            params.put("equipments.equipment.equipmentData.deviceInfo.ldap.firstNameFilter", firstNameFilter);

            // run test
            Request response = REQUEST.getUpdate(params);

            Element actualResponseXml = loadXmlFromPayloadAndGoToElement(response.getPayload(),
                    "data/NadmRequest/equipments/equipment");
            Element expectedResponseXml = loadXmlFromPayloadAndGoToElement(expectedResponse.getPayload(),
                    "data/NadmRequest/equipments/equipment");

            Assert.assertEquals(expectedResponse.getUri(), response.getUri());
            assertXmlElementsChildsAreEqual(actualResponseXml, expectedResponseXml, new String [] {
                    "name",
                    "clientName",
                    "equipmentData/deviceInfo/time/timezone",
                    "equipmentData/deviceInfo/time/dateformat",
                    "equipmentData/deviceInfo/time/timeformat",
                    "equipmentData/deviceInfo/ldap/firstNameFilter"
            });
        } catch (Exception e) {
            fail(e.getMessage());
        }
    }

    @Test
    public void deleteTest_success() {
        try {
            // prepare expectations
            final String uri = BASE_URI + "delete/equipment";
            final String payload = readTestFileFromResources(BASE_PATH + "equipment/delete.xml")
                    .replace("$equipmentName", EQUIPMENT_NAME);
            Request expectedResponse = new Request(uri, payload);

            // run test
            Request response = REQUEST.getDelete();
            Element actualResponseXml = loadXmlFromPayloadAndGoToElement(response.getPayload(),
                    "data/NadmRequest/equipments/equipment");
            Element expectedResponseXml = loadXmlFromPayloadAndGoToElement(expectedResponse.getPayload(),
                    "data/NadmRequest/equipments/equipment");

            Assert.assertEquals(expectedResponse.getUri(), response.getUri());
            assertXmlElementsChildsAreEqual(actualResponseXml, expectedResponseXml, new String [] {
                    "name"
            });
        } catch (Exception e) {
            fail(e.getMessage());
        }
    }
}
