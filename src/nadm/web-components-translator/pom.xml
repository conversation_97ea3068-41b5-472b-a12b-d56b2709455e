<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>pt.ptinovacao.na-portal</groupId>
        <artifactId>na-portal-nadm</artifactId>
        <version>10.55.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>na-portal-nadm-web-components-translator</artifactId>
    <packaging>jar</packaging>


    <dependencies>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <!-- internal -->

        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-web-components</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.nossis-frmwrk</groupId>
            <artifactId>exceptions</artifactId>
        </dependency>

        <!-- test dependencies -->
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>

    </dependencies>
</project>