/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package pt.ptinovacao.na.portal.webui.restful.go.entities.catalog;

import java.util.ArrayList;
import java.util.List;
import javax.validation.Validation;
import javax.validation.Validator;
import org.junit.Test;
import static org.junit.Assert.*;
import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.components.SelectBox;
import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.components.TextField;

/**
 *
 * <AUTHOR>
 */
public class TemplateTest {
    
        public static final String TEMPLATE = "  <?xml version=\"1.0\" encoding=\"UTF-8\"?>\n "
            + "  <service-descriptor>\n" +
"            <rfsGponDiagnostics rfsType=\"type1\" rfsName=\"rfsGponDiagnostics\" rfsVersion=\"v1.0\" operation=\"checkOnt\">\n" +
"                <info>\n" +
"                    <lrList>\n" +
"                        <ont lrVersion=\"v1.0\" lrType=\"type1\" lrName=\"ont\">\n" +
"                            <managementInfo>\n" +
"                                <managementMethod>genericPlugin</managementMethod>\n" +
"                                <managementContext>$olt_vendor/$olt_model/$olt_version</managementContext>\n" +
"                                <managementProxy>\n" +
"                                    <mediaInterface>\n" +
"                                        <logicalDeviceId>\n" +
"                                            <logicalDeviceType></logicalDeviceType>\n" +
"                                            <logicalDeviceName>$olt_name</logicalDeviceName>\n" +
"                                        </logicalDeviceId>\n" +
"                                        <mediaInterfaceId>\n" +
"                                            <mediaInterfaceType></mediaInterfaceType>\n" +
"                                            <mediaInterfaceName>$pon</mediaInterfaceName>\n" +
"                                        </mediaInterfaceId>\n" +
"                                    </mediaInterface>\n" +
"                                </managementProxy>\n" +
"                            </managementInfo>\n" +
"                            <info>\n" +
"                                <logicalDeviceId>\n" +
"                                    <logicalDeviceType>ONT</logicalDeviceType>\n" +
"                                    <logicalDeviceName>$ontid</logicalDeviceName>\n" +
"                                </logicalDeviceId>\n" +
"                                <otherInfo/>\n" +
"                            </info>\n" +
"                        </ont>\n" +
"                    </lrList>\n" +
"                </info>\n" +
"            </rfsGponDiagnostics>\n" +
"        </service-descriptor>";
  
    /**
     * Test of getFamily method, of class Template.
     */
    @Test
    public void testValidation_success_NoValidationError() {
 
         
        Template t = new Template();
        Family family = new Family();
        family.setName("Gpon");
        Rfs rfs = new Rfs();
        rfs.setName("rfsGponDiagnostics");
        t.setFamily(family);
        t.setRfs(rfs);
        Operation oper = new Operation();
        oper.setName("estadoONT");
        oper.setEndpointName("NA");
        oper.setPayloadTemplate(TEMPLATE);
        oper.setTimeout(600);
        oper.setActive(true);
        oper.setIsChild(Boolean.FALSE);
        oper.setUsername("SYSTEM");
        
        List<Mapping> mappings = new ArrayList<>();
        Mapping mapping = new Mapping();
        mapping.setLabel("OLT Vendor");
        mapping.setPosition(0);
        mapping.setDescription("Olt Vendor Name");
        mapping.setTemplateMapping("$olt_vendor");
        
        SelectBox selectBox = new SelectBox();
        selectBox.setDefaultValue("1");
        selectBox.setMessage("Select Olt Vendor Model and Version");
        selectBox.setRequired(Boolean.TRUE);
        selectBox.addOption(new Option(1, "ALCATEL ISAM7342 4.7.13", "ALCATEL/ISAM7342/4.7.13"));
        selectBox.addOption(new Option(2, "Huawei MA5600T V800R005C33B118", "HUAWEI/MA5600T/V800R005C33B118"));
        selectBox.addOption(new Option(3, "PT Inovacão OLT7_8CH 1.11.12", "PTINOVACAO/OLT7_8CH/1.11.12"));
        
        mapping.setComponent(selectBox);
        mapping.setStatus(Mapping.Status.OK);
        mappings.add(mapping);
        
        mapping = new Mapping();
        mapping.setLabel("OLT Name");
        mapping.setPosition(1);
        mapping.setDescription("OLT Catalog Name");
        mapping.setTemplateMapping("$olt_name");
        
        TextField textField = new TextField();
        textField.setMessage("Selecione o nome do OLT identificado em cadastro");
        textField.setRequired(Boolean.TRUE);
        mapping.setComponent(textField);
        mapping.setStatus(Mapping.Status.OK);
        mappings.add(mapping);
        
        mapping = new Mapping();
        mapping.setLabel("PON Id");
        mapping.setPosition(1);
        mapping.setDescription("O Id do PON a utilizar");
        mapping.setTemplateMapping("$pon");
        
        textField = new TextField();
        textField.setMessage("Selecione o PON ID");
        textField.setRequired(Boolean.TRUE);
        mapping.setComponent(textField);
        mapping.setStatus(Mapping.Status.OK);
        mappings.add(mapping);
        
        mapping = new Mapping();
        mapping.setLabel("ONT Id");
        mapping.setPosition(2);
        mapping.setDescription("O Id do ONT a utilizar");
        mapping.setTemplateMapping("$ontid");
        mapping.setStatus(Mapping.Status.OK);
        
        textField = new TextField();
        textField.setMessage("Selecione o ONT ID");
        textField.setRequired(Boolean.TRUE);
        textField.setRegex("^\\d+$");
        textField.setRegexErrorMessage("O ONT ID é UM INTEIRO PAH!!!!");
        mapping.setComponent(textField);
        mapping.setStatus(Mapping.Status.OK);
        mappings.add(mapping);
        
        oper.setMapping(mappings);
        
        t.setDefinition(oper);
                
        Validator v = Validation.buildDefaultValidatorFactory().getValidator();
        
        assertTrue(v.validate(t).isEmpty());
        
    }

 
    @Test
    public void testValidation_failure_WithOptionValidationError() {
 
         
        pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Template t = new pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Template();
        Family family = new Family();
        family.setName("Gpon");
        Rfs rfs = new Rfs();
        rfs.setName("rfsGponDiagnostics");
        t.setFamily(family);
        t.setRfs(rfs);
        Operation oper = new Operation();
        oper.setName("estadoONT");
        oper.setEndpointName("NA");
        oper.setPayloadTemplate(TEMPLATE);
        oper.setTimeout(600);
        
        List<Mapping> mappings = new ArrayList<>();
        Mapping mapping = new Mapping();
        mapping.setLabel("OLT Vendor");
        mapping.setPosition(0);
        mapping.setDescription("Olt Vendor Name");
        mapping.setTemplateMapping("$olt_vendor");
        
        SelectBox selectBox = new SelectBox();
        selectBox.setDefaultValue("1");
        selectBox.setMessage("Select Olt Vendor Model and Version");
        selectBox.setRequired(Boolean.TRUE);
        selectBox.addOption(new Option(1, null, "ALCATEL/ISAM7342/4.7.13"));
        selectBox.addOption(new Option(2, "Huawei MA5600T V800R005C33B118", "HUAWEI/MA5600T/V800R005C33B118"));
        selectBox.addOption(new Option(3, "T Inovacão OLT7_8CH 1.11.12", "PTINOVACAO/OLT7_8CH/1.11.12"));
        
        mapping.setComponent(selectBox);
        mappings.add(mapping);
        
        mapping = new Mapping();
        mapping.setLabel("OLT Name");
        mapping.setPosition(1);
        mapping.setDescription("OLT Catalog Name");
        mapping.setTemplateMapping("$olt_name");
        
        TextField textField = new TextField();
        textField.setMessage("Selecione o nome do OLT identificado em cadastro");
        textField.setRequired(Boolean.TRUE);
        mapping.setComponent(textField);
        mappings.add(mapping);
        
        mapping = new Mapping();
        mapping.setLabel("PON Id");
        mapping.setPosition(1);
        mapping.setDescription("O Id do PON a utilizar");
        mapping.setTemplateMapping("$pon");
        
        textField = new TextField();
        textField.setMessage("Selecione o PON ID");
        textField.setRequired(Boolean.TRUE);
        mapping.setComponent(textField);
        mappings.add(mapping);
        
        mapping = new Mapping();
        mapping.setLabel("ONT Id");
        mapping.setPosition(2);
        mapping.setDescription("O Id do ONT a utilizar");
        mapping.setTemplateMapping("$ontid");
        
        textField = new TextField();
        textField.setMessage("Selecione o ONT ID");
        textField.setRequired(Boolean.TRUE);
        textField.setRegex("^\\d+$");
        textField.setRegexErrorMessage("O ONT ID é UM INTEIRO PAH!!!!");
        mapping.setComponent(textField);
        mappings.add(mapping);
        
        oper.setMapping(mappings);
        
        t.setDefinition(oper);
                
        Validator v = Validation.buildDefaultValidatorFactory().getValidator();
        
        assertFalse(v.validate(t).isEmpty());
        
    }
    
    
}
