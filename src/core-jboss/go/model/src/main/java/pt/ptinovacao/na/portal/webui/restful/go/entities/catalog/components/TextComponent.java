/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.components;

import com.google.common.base.Strings;
import java.util.Map;
import javax.validation.constraints.Size;

/**
 *
 * <AUTHOR>
 */
public class TextComponent extends Component {

    @Size(max = 256)
    private String regex;
    @Size(max = 256)
    private String regexErrorMessage;

    private Integer maxSize;

    public String getRegex() {
        return regex;
    }

    public void setRegex(String regex) {
        this.regex = regex;
    }

    public String getRegexErrorMessage() {
        return regexErrorMessage;
    }

    public void setRegexErrorMessage(String regexErrorMessage) {
        this.regexErrorMessage = regexErrorMessage;
    }

    public Integer getMaxSize() {
        return maxSize;
    }

    public void setMaxSize(Integer maxSize) {
        this.maxSize = maxSize;
    }

    @Override
    public void buildUp(String key, String value) {
        switch (key) {
            case "regex":
                this.setRegex(value);
                break;
            case "regexErrorMessage":
                this.setRegexErrorMessage(value);
                break;
            case "maxSize":
                this.setMaxSize(Integer.valueOf(value));
                break;
            default:
                super.buildUp(key, value);
                break;
        }
    }

    @Override
    public Map<String, String> getAttributes() {
        Map<String, String> map = super.getAttributes();

        if (!Strings.isNullOrEmpty(this.getRegex())) {
            map.put("regex", this.getRegex());
        }
        if (!Strings.isNullOrEmpty(this.getRegexErrorMessage())) {
            map.put("regexErrorMessage", this.getRegexErrorMessage());
        }
        if (this.getMaxSize() != null) {
            map.put("maxSize", this.getMaxSize().toString());
        }
        return map;
    }

}
