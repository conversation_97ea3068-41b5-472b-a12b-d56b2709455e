/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package pt.ptinovacao.na.portal.webui.restful.go.entities.catalog;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Objects;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Template {

    public enum ODATA_SELECTABLE_ELEMENTS {

        FAMILY("family"),
        RFS("rfs"),
        DEFINITION("definition"),
        PAYLOADTEMPLATE("payloadTemplate"),
        MAPPING("mapping");

        private final String odataSelect;

        ODATA_SELECTABLE_ELEMENTS(String odataSelect) {
            this.odataSelect = odataSelect;
        }

        public String getOdataSelect() {
            return this.odataSelect;
        }
    }

    @NotNull
    @Valid
    private Family family;

    @NotNull
    @Valid
    private Rfs rfs;

    @NotNull
    @Valid
    private Operation definition;

    public Template() {
    }

    public Template(Family family, Rfs rfs, Operation definition) {
        this.family = family;
        this.rfs = rfs;
        this.definition = definition;
    }

    public Family getFamily() {
        return family;
    }

    public void setFamily(Family family) {
        this.family = family;
    }

    public Rfs getRfs() {
        return rfs;
    }

    public void setRfs(Rfs rfs) {
        this.rfs = rfs;
    }

    public Operation getDefinition() {
        return definition;
    }

    public void setDefinition(Operation definition) {
        this.definition = definition;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 53 * hash + Objects.hashCode(this.family.getName());
        hash = 53 * hash + Objects.hashCode(this.rfs.getName());
        hash = 53 * hash + Objects.hashCode(this.definition.getName());
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Template other = (Template) obj;
        if (!Objects.equals(this.family.getName(), other.family.getName())) {
            return false;
        }
        if (!Objects.equals(this.rfs.getName(), other.rfs.getName())) {
            return false;
        }
        if (!Objects.equals(this.definition.getName(), other.definition.getName())) {
            return false;
        }
        return true;
    }

}
