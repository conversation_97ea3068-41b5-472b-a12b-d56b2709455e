/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.components;

import pt.ptinovacao.na.portal.db.mpt.orm.entities.go.OperCatAttrs;
import pt.ptinovacao.na.portal.db.mpt.orm.entities.go.OperCatChars;

/**
 *
 * <AUTHOR>
 */
public class ComponentFactory {

    private ComponentFactory() {

    }

    public static Component getComponent(OperCatAttrs attrs) throws ClassNotFoundException, InstantiationException, IllegalAccessException {
        Component component = (Component) Class.forName(attrs.getElementType().getElementClassName()).newInstance();
        for (OperCatChars chars : attrs.getOperCatCharsCollection()) {
            component.buildUp(chars.getKey(), chars.getValue());
        }
        return component;
    }
}
