<?xml version="1.0" encoding="UTF-8"?>
<web-app version="3.0" xmlns="http://java.sun.com/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd">

    <display-name>Core GO RESTEasy Interface</display-name>

    <!-- Auto scan REST service -->
<!--    <context-param>
        <param-name>resteasy.scan</param-name>
        <param-value>true</param-value>
    </context-param>-->

    <context-param>
        <param-name>resteasy.resources</param-name>
        <param-value>
            pt.ptinovacao.na.portal.webui.restful.go.resteasy.ModelService,
            pt.ptinovacao.na.portal.webui.restful.go.resteasy.OperationService,
            pt.ptinovacao.na.portal.webui.restful.go.resteasy.TemplateService
        </param-value>
    </context-param>

    <!-- this should be the same URL pattern as the servlet-mapping property -->
    <context-param>
        <param-name>resteasy.servlet.mapping.prefix</param-name>
        <param-value>/restful</param-value>
    </context-param>

    <listener>
        <listener-class>org.jboss.resteasy.plugins.server.servlet.ResteasyBootstrap</listener-class>
    </listener>

    <servlet>
        <servlet-name>go-resteasy-servlet</servlet-name>
        <servlet-class>org.jboss.resteasy.plugins.server.servlet.HttpServletDispatcher</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <servlet-mapping>
        <servlet-name>go-resteasy-servlet</servlet-name>
        <url-pattern>/restful/*</url-pattern>
    </servlet-mapping>

    <context-param>
        <param-name>resteasy.providers</param-name>
        <param-value>
            pt.ptinovacao.na.portal.webui.restful.go.exception.GOExceptionProvider,
            pt.ptinovacao.na.portal.webui.restful.commons.audit.AuditLoggingFilter
        </param-value>
    </context-param>

    <session-config>
        <session-timeout>360</session-timeout>
    </session-config>

    <!-- To have your HTTP session state replicated across the cluster ;) -->
    <distributable/>
</web-app>
