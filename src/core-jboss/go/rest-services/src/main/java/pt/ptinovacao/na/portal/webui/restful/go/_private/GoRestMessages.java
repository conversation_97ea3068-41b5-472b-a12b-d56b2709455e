package pt.ptinovacao.na.portal.webui.restful.go._private;

import org.jboss.logging.Messages;
import org.jboss.logging.annotations.Cause;
import org.jboss.logging.annotations.Message;
import org.jboss.logging.annotations.MessageBundle;
import org.jboss.logging.annotations.ValidIdRange;
import pt.ptinovacao.na.portal.db.commons.persistence.CommonDataManagerException;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
@MessageBundle(projectCode = "GOSERVICE")
@ValidIdRange(min = 100, max = 200)
public interface GoRestMessages {

    GoRestMessages MESSAGES = Messages.getBundle(GoRestMessages.class);

    @Message(id = 101, value = "Failed to find template [%s]")
    CommonDataManagerException TemplateNotFound(@Cause Throwable e, String template);

    @Message(id = 102, value = "Failed to find xml from template [%s]")
    CommonDataManagerException xmlTemplateAbsent(@Cause Throwable e, String template);

    @Message(id = 103, value = "Received invalid order key [%s], aborting operation...")
    Exception receivedInvalidOrderkey(@Cause Throwable e, String orderkey);


}
