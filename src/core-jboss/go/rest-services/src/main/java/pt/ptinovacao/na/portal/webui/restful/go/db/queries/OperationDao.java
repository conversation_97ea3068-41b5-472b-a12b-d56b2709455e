package pt.ptinovacao.na.portal.webui.restful.go.db.queries;

import org.odata4j.producer.QueryInfo;
import pt.ptinovacao.na.portal.db.commons.persistence.resources.qualifiers.Audited;
import pt.ptinovacao.na.portal.db.mpt.entities.contants.NamedQueriesOGM;
import pt.ptinovacao.na.portal.db.mpt.ogm.entities.go.Operation;
import pt.ptinovacao.na.portal.webui.restful.commons.model.RawListContainer;
import pt.ptinovacao.na.portal.webui.restful.go.commons.NamedTableFKFieldsCat;

import javax.enterprise.context.RequestScoped;
import javax.inject.Named;
import org.bson.types.ObjectId;
import pt.ptinovacao.na.portal.webui.restful.go.converter.EntityConverter;
import pt.ptinovacao.na.portal.webui.restful.go.entities.GOOperation;

/**
 * <AUTHOR> <<EMAIL>>, 05-01-2016.
 */
@RequestScoped
@Named
@Audited(logToFile = true)
public class OperationDao extends GoDao {

    /**
     *
     * @param queryInfo
     * @return
     */
    public RawListContainer<GOOperation> searchOperations(QueryInfo queryInfo) throws Exception {
        RawListContainer<Operation> operation = executeQuery(NamedQueriesOGM.GO_OPERATION_FINDALL_QUERY, queryInfo, "o", NamedTableFKFieldsCat.OPERATION_FKS, Operation.class.getSimpleName(), this.ogmStore.getEntityManager());

        return EntityConverter.convertToGOOperation(operation, queryInfo);
    }

    /**
     *
     * @param operationId
     * @return
     */
    public GOOperation getOperationDetails(String operationId) throws Exception {
        Operation operation = this.ogmStore.findSingleOrNull(NamedQueriesOGM.GO_OPERATION_FIND_BY_ID, Operation.class, "id", new ObjectId(operationId));
        if (operation == null) {
            return null;
        }
        return EntityConverter.convertToGOOperation(operation);
    }

}
