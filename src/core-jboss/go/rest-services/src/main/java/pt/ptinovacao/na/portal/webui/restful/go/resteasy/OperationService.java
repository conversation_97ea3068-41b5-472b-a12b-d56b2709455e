package pt.ptinovacao.na.portal.webui.restful.go.resteasy;

import com.google.common.base.Charsets;
import org.odata4j.producer.QueryInfo;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.w3c.dom.ls.DOMImplementationLS;
import org.w3c.dom.ls.LSSerializer;
import org.xml.sax.SAXException;
import pt.ptinovacao.na.portal.core.mpt.dispatcher.EventDispatcherEJB;
import pt.ptinovacao.na.portal.db.mpt.orm.entities.go.OperCatTempt;
import pt.ptinovacao.na.portal.webui.restful.commons.db.odata.ODataUtils;
import pt.ptinovacao.na.portal.webui.restful.commons.model.RawListContainer;
import pt.ptinovacao.na.portal.webui.restful.go._private.GoRestLogger;
import pt.ptinovacao.na.portal.webui.restful.go._private.GoRestMessages;
import pt.ptinovacao.na.portal.webui.restful.go.commons.Consts;
import pt.ptinovacao.na.portal.webui.restful.go.converter.EntityConverter;
import pt.ptinovacao.na.portal.webui.restful.go.db.queries.CatDao;
import pt.ptinovacao.na.portal.webui.restful.go.db.queries.OperationDao;
import pt.ptinovacao.na.portal.webui.restful.go.dispatcher.GoOrderHandlerEJB;
import pt.ptinovacao.na.portal.webui.restful.go.dispatcher.GoOrderProcessor;
import pt.ptinovacao.na.portal.webui.restful.go.entities.CreateOperationContent;
import pt.ptinovacao.na.portal.webui.restful.go.entities.GOOperation;
import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.*;
import pt.ptinovacao.na.portal.webui.restful.go.exception.GOExceptionProvider;
import pt.ptinovacao.na.portal.webui.restful.go.exception.GOResteasyException;
import pt.ptinovacao.na.portal.webui.restful.go.exception.Utils;
import pt.ptinovacao.na.portal.webui.restful.go.templateparser.OperationParser;

import javax.inject.Inject;
import javax.persistence.EntityNotFoundException;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriInfo;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Castro <<EMAIL>>
 */
@Path(Consts.GO_OPERATION_PATH)
public class OperationService {

    @Inject
    private EventDispatcherEJB dispatcher;

    @Inject
    private GoOrderHandlerEJB orderHandler;

    @Inject
    private OperationDao operationDao;

    @Inject
    private CatDao catDao;

    @Inject
    private GoOrderProcessor goProcessor;

    /**
     * Creates a generic operation
     * <p>
     * <p>
     * steps:
     * <ol>
     * <li> parse request ( grab form information )
     * <li> getTemplatesDetails templateXML from database ( OracleDB )
     * <li> modify templateXML with the formdata
     * <li> create operation in the mongoDB
     * <li> wrap templateXML into a request and send to NA Core
     * <li> redirecto to go main page
     * </ol>
     *
     * @param content - information necessary to create operation
     * @return the created operation
     * @throws Exception
     */
    @POST
    @Path(Consts.GO_SEARCH_PATH)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response createOperation(CreateOperationContent content) {
        final String LIGHT_BOLD_BLUE = "\u001B[1m\u001B[37m\u001B[34m";
        final String RESET_ANSI = "\u001B[0m";
        final String prefixInfo = LIGHT_BOLD_BLUE + "[[createOperation]]" + RESET_ANSI + " ";

        try {
            // Validations
            Template template = EntityConverter.convertFromCreateOperationContentToTemplate(content);
            OperCatTempt tempt = this.catDao.getTemplate(template);
            if (tempt == null) {
                GoRestLogger.LOGGER.templateNotFound(content.getOperation(), content.getFamily(), content.getRfs());
                throw GoRestMessages.MESSAGES.TemplateNotFound(new EntityNotFoundException(), content.getOperation());
            }
            String templateXml = tempt.getTemplateXml();

            Operation operation = EntityConverter.convertToOperation(tempt);
            checkRequiredAttributesAreAllPresent(operation, content);

            if (templateXml == null) {
                GoRestLogger.LOGGER.xmlTemplateNotFound(tempt.getTemplateName(), content.getFamily(), content.getRfs());
                throw GoRestMessages.MESSAGES.xmlTemplateAbsent(new EntityNotFoundException(), tempt.getTemplateName());
            }

            // BuildUp Request
            GoRestLogger.LOGGER.info(prefixInfo + " got XML :\n" + templateXml);
            template.getDefinition().setPayloadTemplate(templateXml);
            template.getDefinition().setEndpointName(tempt.getEndpointName());
            template.getDefinition().setTimeout(Long.valueOf(tempt.getTimeout()).intValue());

            String mappedXml = OperationParser.buildRequestPayload(content, templateXml);
            // remove xml elements which values are "${...}"
            mappedXml = normalizePayload(mappedXml);

            GoRestLogger.LOGGER.info(prefixInfo + " mappedXml: \n" + mappedXml);

            // Fire request process, runs asynchronously
            final pt.ptinovacao.na.portal.db.mpt.ogm.entities.go.Operation processingOperation = this.goProcessor.process(template, content.getAttributes(), mappedXml).get();

            HashMap<String, String> response = new HashMap<>();
            response.put("status", "ok");
            response.put("operationId", processingOperation.getId());

            return Response.ok().entity(response).build();

        } catch (Exception ex) {
            return Utils.handleException(ex);
        }
    }

    /**
     * steps:
     * <ol>
     * <li> parse request ( grab search form information )
     * <li> query from MongoDB
     * <li> send query result
     * </ol>
     *
     * @param uriInfo
     * @return
     */
    @GET
    @Path(Consts.GO_SEARCH_PATH)
    @Produces(MediaType.APPLICATION_JSON)
    public Response search(@Context UriInfo uriInfo) {
        try {
            GoRestLogger.LOGGER2.receivedRequestUriInfo("search GO operations", uriInfo);
            QueryInfo queryInfo = ODataUtils.queryInfoParser(uriInfo);
            RawListContainer<GOOperation> rawListContainer = this.operationDao.searchOperations(queryInfo);

            // Get Catalog Properties
            Map<Template, pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Operation> catalog
                    = this.catDao.getTemplatesDetails(getListOfTemplatesFromGOOperations(rawListContainer));

            return Response.ok().entity(enrichWithCatalogProperties(rawListContainer, catalog)).build();
        } catch (Exception ex) {
            return Utils.handleException(ex);
        }
    }

    /**
     * steps:
     * <ol>
     * <li> parse request ( grab search form information )
     * <li> query from MongoDB
     * <li> send query result
     * </ol>
     *
     * @param operationId
     * @return
     */
    @GET
    @Path(Consts.GO_OPERATION_DETAILS_PATH)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getOperationDetails(@PathParam("id") String operationId, @Context UriInfo uriInfo) {
        try {
            GoRestLogger.LOGGER2.receivedRequestUriInfo("getOperationDetails", uriInfo);
            GOOperation operation = this.operationDao.getOperationDetails(operationId);
            if (operation == null) {
                GOResteasyException goResteasyException = new GOResteasyException(Response.Status.NOT_FOUND.getStatusCode(), "DEV_MSG", "USR_MSG", "MO INFO");
                GoRestLogger.LOGGER2.exceptionThrown(goResteasyException);
                return new GOExceptionProvider().toResponse(goResteasyException);
            }
            return Response.ok().entity(operation).build();
        } catch (Exception ex) {
            return Utils.handleException(ex);
        }
    }

    /**
     * Add Properties from Template Catalog to GOOperation
     *
     * @param rawListContainer
     * @param catalog
     * @return
     */
    private RawListContainer<GOOperation> enrichWithCatalogProperties(RawListContainer<GOOperation> rawListContainer, Map<Template, pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Operation> catalog) {
        for (GOOperation oper : rawListContainer.getEntries()) {
            pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Operation o = catalog.get(getTemplateFromGOOperation(oper));
            if (o == null) {
                oper.setCatalogTemplateActive(Boolean.FALSE);
                oper.setCatalogTemplateAttention(Boolean.TRUE);
                oper.setCatalogTemplateDeleted(Boolean.TRUE);
            } else {
                oper.setCatalogTemplateId(o.getId());
                oper.setCatalogTemplateActive(o.getActive());
                oper.setCatalogTemplateAttention(o.getAttention());
                oper.setCatalogTemplateDeleted(o.getDeleted());
            }
        }
        return rawListContainer;
    }

    /**
     * Returns a list of CatalogTemplates from a list of Operations
     *
     * @param operation
     * @return
     */
    private List<Template> getListOfTemplatesFromGOOperations(RawListContainer<GOOperation> operation) {
        List<Template> catalog = new ArrayList<>();
        for (GOOperation oper : operation.getEntries()) {
            Template tplt = getTemplateFromGOOperation(oper);
            catalog.add(tplt);
        }
        return catalog;
    }

    private Template getTemplateFromGOOperation(GOOperation oper) {
        Template tplt = new Template();
        Family family = new Family();
        family.setName(oper.getFamily());
        tplt.setFamily(family);
        Rfs rfs = new Rfs();
        rfs.setName(oper.getRfs());
        tplt.setRfs(rfs);
        pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Operation definition = new pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Operation();
        definition.setName(oper.getOperationName());
        tplt.setDefinition(definition);
        return tplt;
    }

    private void checkRequiredAttributesAreAllPresent(Operation operation, CreateOperationContent content) {
        Collection<Mapping> required = operation.getMapping().stream().filter(m -> m.getComponent().getRequired()).collect(Collectors.toSet());
        for (Mapping mapping : required) {
            if (content.getAttributes().stream().noneMatch(oa -> oa.getName().equals(mapping.getTemplateMapping()))) {
                throw new RuntimeException(String.format("Attribute %s is required.", mapping.getLabel()));
            }
        }
    }

    /**
     *
     * Removes elements in template in which values have not been set.
     *
     * @param xml
     * @return
     * @throws ParserConfigurationException
     * @throws IOException
     * @throws SAXException
     */
    private String normalizePayload(String xml) throws ParserConfigurationException, IOException, SAXException {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document document = builder.parse(new ByteArrayInputStream(xml.getBytes(Charsets.UTF_8)));
        Element element = document.getDocumentElement();
        normalizeElement(element);
        DOMImplementationLS lsImpl = (DOMImplementationLS) element.getOwnerDocument().getImplementation().getFeature("LS", "3.0");
        LSSerializer serializer = lsImpl.createLSSerializer();
        serializer.getDomConfig().setParameter("xml-declaration", false); //by default its true, so set it to false to get String without xml-declaration
        return serializer.writeToString(element);
    }

    private boolean normalizeElement(Node parent) {
        boolean remove = false;
        NodeList nodeList = parent.getChildNodes();
        Set<Node> toRemove = new HashSet<>();
        for (int i = 0; i < nodeList.getLength(); i++) {
            Node child = nodeList.item(i);
            if (child.getNodeType() == Node.ELEMENT_NODE) {
                if (normalizeElement(child)) toRemove.add(child);
            } else if (child.getNodeType() == Node.TEXT_NODE) {
                String value = child.getNodeValue();
                if (value.startsWith("${"))
                    remove = true;
            }
        }
        toRemove.forEach(parent::removeChild);
        return remove;
    }
}
