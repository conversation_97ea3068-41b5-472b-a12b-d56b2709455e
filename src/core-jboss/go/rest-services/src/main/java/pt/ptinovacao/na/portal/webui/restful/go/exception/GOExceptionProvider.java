package pt.ptinovacao.na.portal.webui.restful.go.exception;

import javax.ws.rs.core.Response;
import javax.ws.rs.ext.ExceptionMapper;
import javax.ws.rs.ext.Provider;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
@Provider
public class GOExceptionProvider implements ExceptionMapper<Exception> {
    
    @Override
    public Response toResponse(Exception exception) {
        
        if (exception instanceof GOResteasyException) {
            return Response.status(((GOResteasyException) exception).getErrorCode()).entity(exception).build();
        } else {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(new GOResteasyException(exception)).build();
        }
    }
}
