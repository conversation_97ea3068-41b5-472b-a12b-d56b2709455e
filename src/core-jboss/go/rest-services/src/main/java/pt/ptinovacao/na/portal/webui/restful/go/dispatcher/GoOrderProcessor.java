package pt.ptinovacao.na.portal.webui.restful.go.dispatcher;

import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import pt.ptinovacao.na.portal.core.mpt.dispatcher.EventDispatcherEJB;
import pt.ptinovacao.na.portal.core.mpt.dispatcher.events.OrderEventPayload;
import pt.ptinovacao.na.portal.core.mpt.dispatcher.events.qualifiers.GO;
import pt.ptinovacao.na.portal.core.request.engine.model.request.Request;
import pt.ptinovacao.na.portal.db.commons.persistence.CommonDataManagerException;
import pt.ptinovacao.na.portal.db.mpt.ogm.entities.Configuration;
import pt.ptinovacao.na.portal.db.mpt.ogm.entities.go.Details;
import pt.ptinovacao.na.portal.db.mpt.ogm.entities.go.MessageTypes;
import pt.ptinovacao.na.portal.db.mpt.ogm.entities.go.Operation;
import pt.ptinovacao.na.portal.db.mpt.ogm.entities.go.Status;
import pt.ptinovacao.na.portal.db.mpt.persistence.PersistenceOGMBMTStore;
import pt.ptinovacao.na.portal.db.mpt.queries.CommonQueryEngine;
import pt.ptinovacao.na.portal.webui.restful.commons.model.ExecutionStates;
import pt.ptinovacao.na.portal.webui.restful.go._private.GoRestLogger;
import pt.ptinovacao.na.portal.webui.restful.go.converter.EntityConverter;
import pt.ptinovacao.na.portal.webui.restful.go.entities.OperationAttribute;
import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Template;

import javax.ejb.Asynchronous;
import javax.ejb.Stateless;
import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.enterprise.event.Event;
import javax.enterprise.inject.Any;
import javax.enterprise.util.AnnotationLiteral;
import javax.inject.Inject;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class GoOrderProcessor {

    @Inject
    private PersistenceOGMBMTStore ogmStore;

    @Inject
    private EventDispatcherEJB dispatcher;

    @Inject
    @Any
    private Event<OrderEventPayload> event;

    @Asynchronous
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public Future<Operation> process(Template template, List<OperationAttribute> attributes, String requestPayload)
            throws CommonDataManagerException, InterruptedException {
        Operation oper = persistOperation(template, attributes, requestPayload);
        Details requestDetails = oper.getDetails().get(0);

        try {
            UUID clientId = UUID.randomUUID();
            GoRestLogger.LOGGER.info("Generated ClientId: " + clientId);

            Request request = buildRequest(template, clientId, requestPayload);

            // Publish it
            GoRestLogger.LOGGER.publishingRequestToNA("[createOperation]", request.getClientId(), request.getClientURL(),
                    request.getTimeout(), request.getDescription(), request.getPayload());

            OrderEventPayload orderEventPayload = this.dispatcher.dispatchAsyncCDI(request, event, new AnnotationLiteral<GO>() {});
            if (orderEventPayload.getOrderKey() == null) {
                throw new AssertionError("received null orderKey");
            }

            // Store the updated results the OGM database
            oper.setNodeType(request.getNodeType());
            oper.setOrderKey(orderEventPayload.getOrderKey());
            oper.setExecutionState(ExecutionStates.STARTED);
            requestDetails.setStatus(Status.OK);

            ogmStore.getDataManager().refresh(oper);
        } catch (Throwable t) {
            GoRestLogger.LOGGER.error(Throwables.getRootCause(t));
            requestDetails.setStatus(Status.NOK);
            oper.setExecutionState(ExecutionStates.ERROR);
        }

        return CompletableFuture.completedFuture(oper);

    }

    private Operation persistOperation(Template template, List<OperationAttribute> attributes, String requestPayload) throws CommonDataManagerException {
        Details requestDetails = new Details();
        requestDetails.setDatetime(new Date());
        requestDetails.setPayload(requestPayload);
        requestDetails.setMessageType(MessageTypes.REQUEST);

        Operation oper = new Operation();
        oper.setDatetime(new Date());
        oper.setDetails(Lists.newArrayList(requestDetails));
        oper.setOperationName(template.getDefinition().getName());
        oper.setFamily(template.getFamily().getName());
        oper.setRfs(template.getRfs().getName());
        oper.setUsername(template.getDefinition().getUsername());
        oper.setExecutionState(ExecutionStates.NOT_STARTED);
        oper.setAttributes(EntityConverter.convertToOGMOperationAttributes(attributes));
        oper.setOrderKey("");

        ogmStore.persist(oper);
        return oper;
    }

    private Request buildRequest(Template template, UUID clientId, String requestPayload) {

        Configuration conf = new CommonQueryEngine(ogmStore, 0).getRequestNodeConfiguration(template.getDefinition().getEndpointName());

        Request request = new Request();
        request.setClientId(clientId.toString());
        request.setPayload(requestPayload);
        request.setTimeout(TimeUnit.SECONDS.toMillis(template.getDefinition().getTimeout()));
        request.setDescription("[OrderProcessor] " + clientId + " " + System.currentTimeMillis());

        request.setClientURL(conf.getUrl());
        request.setAppDn(conf.getApplicationDN());
        request.setPassword(conf.getPassword());
        request.setNodeType(conf.getType());
        request.setDomain(conf.getDomain());

        return request;
    }
}
