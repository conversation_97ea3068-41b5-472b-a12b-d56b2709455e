[{"version": 19, "templateXml": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<service-descriptor xmlns:fn=\"http://www.w3.org/2005/xpath-functions\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n<rfsBbrasClient operation=\"modify\" rfsName=\"rfsBbrasClient\" rfsType=\"ADSLService\" rfsVersion=\"1.0\">\n\t<info>\n\t\t<lrList>\n\t\t\t<subInterfaceVlanEthernet lrName=\"subInterfaceVlanEthernet\" lrType=\"BBRAS\" lrVersion=\"v1.0\">\n\t\t\t\t<managementInfo>\n\t\t\t\t\t<managementMethod>genericPlugin</managementMethod>\n\t\t\t\t\t<managementContext>JUNIPER/E320/12-3-2</managementContext>\n\t\t\t\t</managementInfo>\n\t\t\t\t<info>\n\t\t\t\t\t<logicalDeviceId>\n\t\t\t\t\t\t<logicalDeviceType>BBRAS</logicalDeviceType>\n\t\t\t\t\t\t<logicalDeviceName>$logicalDeviceName</logicalDeviceName>\n\t\t\t\t\t</logicalDeviceId>\n\t\t\t\t\t<interfaceDescription>$interfaceDesc</interfaceDescription>\n\t\t\t\t\t<logicalInterfaceId>\n\t\t\t\t\t\t<logicalInterfaceType>SubVLAN</logicalInterfaceType>\n\t\t\t\t\t\t<logicalInterfaceName>$logicalIntName</logicalInterfaceName>\n\t\t\t\t\t</logicalInterfaceId>\n\t\t\t\t\t<mediaInterfaceId>\n\t\t\t\t\t\t<mediaInterfaceType>$mediaInterfaceType</mediaInterfaceType>\n\t\t\t\t\t\t<mediaInterfaceName>$mediaInterfaceName</mediaInterfaceName>\n\t\t\t\t\t</mediaInterfaceId>\n\t\t\t\t\t<layer2SVlanId>\n\t\t\t\t\t\t<oVlan>$oVlan</oVlan>\n\t\t\t\t\t\t<iVlan>$iVlan</iVlan>\n\t\t\t\t\t</layer2SVlanId>\n\t\t\t\t\t<layer2Encapsulation>\n\t\t\t\t\t\t<pppoE>\n\t\t\t\t\t\t\t<sessions>$ppoeSessions</sessions>\n\t\t\t\t\t\t</pppoE>\n\t\t\t\t\t</layer2Encapsulation>\n\t\t\t\t\t<otherInfo>\n\t\t\t\t\t\t<doFullDiagnose>$doFullDiagnose</doFullDiagnose>\n\t\t\t\t\t\t<operador>$operator</operador>\n\t\t\t\t\t\t<classe>$classe</classe>\n\t\t\t\t\t\t<ipFixo>$ipFixo</ipFixo>\n\t\t\t\t\t\t<PTR1.Caract>$ptrCaract</PTR1.Caract>\n\t\t\t\t\t\t<classeTecnica>$classeTecnica</classeTecnica>\n\t\t\t\t\t\t<classeInternet>$classeInternet</classeInternet>\n\t\t\t\t\t\t<qos>$qos</qos>\n\t\t\t\t\t\t<telefone>$telefone</telefone>\n\t\t\t\t\t\t<pedido>$pedido</pedido>\n\t\t\t\t\t\t<versaoIp>$versaoIp</versaoIp>\n\t\t\t\t\t\t<tipoEnderecamento>$tipoEnderecamento</tipoEnderecamento>\n\t\t\t\t\t</otherInfo>\n\t\t\t\t</info>\n\t\t\t</subInterfaceVlanEthernet>\n\t\t</lrList>\n\t</info>\n</rfsBbrasClient>\n        </service-descriptor>", "templateName": "modifyLrVlan", "endpointName": "DEFAULT", "active": 1}, {"version": 4, "templateXml": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<service-descriptor xmlns:fn=\"http://www.w3.org/2005/xpath-functions\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n<rfsBbrasClient operation=\"modify\" rfsName=\"rfsBbrasClient\" rfsType=\"ADSLService\" rfsVersion=\"1.0\">\n\t<info>\n\t\t<lrList>\n\t\t\t<subInterfaceVcAtm lrName=\"subInterfaceVcAtm\" lrType=\"BBRAS\" lrVersion=\"v1.0\">\n\t\t\t\t<managementInfo>\n\t\t\t\t\t<managementMethod>genericPlugin</managementMethod>\n\t\t\t\t\t<managementContext>JUNIPER/E320/12-3-2</managementContext>\n\t\t\t\t</managementInfo>\n\t\t\t\t<info>\n\t\t\t\t\t<logicalDeviceId>\n\t\t\t\t\t\t<logicalDeviceType>BBRAS</logicalDeviceType>\n\t\t\t\t\t\t<logicalDeviceName>$logicalDeviceName</logicalDeviceName>\n\t\t\t\t\t</logicalDeviceId>\n\t\t\t\t\t<interfaceDescription>$interfaceDesc</interfaceDescription>\n\t\t\t\t\t<logicalInterfaceId>\n\t\t\t\t\t\t<logicalInterfaceType>SubVLAN</logicalInterfaceType>\n\t\t\t\t\t\t<logicalInterfaceName>$logicalIntName</logicalInterfaceName>\n\t\t\t\t\t</logicalInterfaceId>\n\t\t\t\t\t<mediaInterfaceId>\n\t\t\t\t\t\t<mediaInterfaceType>Atm</mediaInterfaceType>\n\t\t\t\t\t\t<mediaInterfaceName>$mediaInterfaceName</mediaInterfaceName>\n\t\t\t\t\t</mediaInterfaceId>\n\t\t\t\t\t<layer2AtmId>\n\t\t\t\t\t\t<vpiId>$vpiId</vpiId>\n\t\t\t\t\t\t<vciId>$vciId</vciId>\n\t\t\t\t\t</layer2AtmId>\n\t\t\t\t\t<layer2Encapsulation>\n\t\t\t\t\t\t<pppoE>\n\t\t\t\t\t\t\t<sessions>$ppoeSessions</sessions>\n\t\t\t\t\t\t</pppoE>\n\t\t\t\t\t</layer2Encapsulation>\n\t\t\t\t\t<otherInfo>\n\t\t\t\t\t\t<doFullDiagnose>$doFullDiagnose</doFullDiagnose>\n\t\t\t\t\t\t<operador>$operator</operador>\n\t\t\t\t\t\t<classe>$classe</classe>\n\t\t\t\t\t\t<ipFixo>$ipFixo</ipFixo>\n\t\t\t\t\t\t<PTR1.Caract>$ptrCaract</PTR1.Caract>\n\t\t\t\t\t\t<classeTecnica>$classeTecnica</classeTecnica>\n\t\t\t\t\t\t<classeInternet>$classeInternet</classeInternet>\n\t\t\t\t\t\t<qos>$qos</qos>\n\t\t\t\t\t\t<telefone>$telefone</telefone>\n\t\t\t\t\t\t<pedido>$pedido</pedido>\n\t\t\t\t\t\t<versaoIp>$versaoIp</versaoIp>\n\t\t\t\t\t\t<tipoEnderecamento>$tipoEnderecamento</tipoEnderecamento>\n\t\t\t\t\t</otherInfo>\n\t\t\t\t</info>\n\t\t\t</subInterfaceVcAtm>\n\t\t</lrList>\n\t</info>\n</rfsBbrasClient>\n        </service-descriptor>", "templateName": "modifyLrVcAtm", "endpointName": "NA", "active": 1}, {"version": 1, "templateXml": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<service-descriptor xmlns:fn=\"http://www.w3.org/2005/xpath-functions\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n  <rfsBbrasClient rfsName=\"rfsBbrasClient\" rfsType=\"ADSLService\" rfsVersion=\"1.0\" rfsId=\"3705581\" bbrasname=\"$logicalDeviceName\" operation=\"consulta\">\n    <info>\n      <lrList>\n        <subInterfaceVlanEthernet lrName=\"subInterfaceVlanEthernet\" lrType=\"BBRAS\" lrVersion=\"v1.0\">\n          <managementInfo>\n            <managementMethod>genericPlugin</managementMethod>\n            <managementContext>juniper/ERX1410/v4.0</managementContext>\n          </managementInfo>\n          <info>\n            <logicalDeviceId>\n              <logicalDeviceType>BBRAS</logicalDeviceType>\n              <logicalDeviceName>$logicalDeviceName</logicalDeviceName>\n            </logicalDeviceId>\n            <interfaceDescription>NULL</interfaceDescription>\n            <logicalInterfaceId>\n              <logicalInterfaceType>SubVLAN</logicalInterfaceType>\n              <logicalInterfaceName>$iVlan0$oVlan</logicalInterfaceName>\n            </logicalInterfaceId>\n            <mediaInterfaceId>\n              <mediaInterfaceType>$mediaInterfaceType</mediaInterfaceType>\n              <mediaInterfaceName>$mediaInterfaceName</mediaInterfaceName>\n            </mediaInterfaceId>\n            <layer2SVlanId>\n              <oVlan>$oVlan</oVlan>\n              <iVlan>$iVlan</iVlan>\n            </layer2SVlanId>\n            <layer2Encapsulation>\n              <pppoE>\n                <sessions>1</sessions>\n              </pppoE>\n            </layer2Encapsulation>\n            <otherInfo>\n            </otherInfo>\n          </info>\n        </subInterfaceVlanEthernet>\n      </lrList>\n    </info>\n  </rfsBbrasClient>\n</service-descriptor>", "templateName": "consulta", "endpointName": "NA", "active": 1}, {"version": 1, "templateXml": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<service-descriptor xmlns:fn=\"http://www.w3.org/2005/xpath-functions\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n  <rfsBbrasClient rfsName=\"rfsBbrasClient\" rfsType=\"ADSLService\" rfsVersion=\"1.0\" rfsId=\"3705581\" bbrasname=\"$logicalDeviceName\" operation=\"consulta\">\n    <info>\n      <lrList>\n        <subInterfaceVlanEthernet lrName=\"subInterfaceVlanEthernet\" lrType=\"BBRAS\" lrVersion=\"v1.0\">\n          <managementInfo>\n            <managementMethod>genericPlugin</managementMethod>\n            <managementContext>juniper/ERX1410/v4.0</managementContext>\n          </managementInfo>\n          <info>\n            <logicalDeviceId>\n              <logicalDeviceType>BBRAS</logicalDeviceType>\n              <logicalDeviceName>$logicalDeviceName</logicalDeviceName>\n            </logicalDeviceId>\n            <interfaceDescription>NULL</interfaceDescription>\n            <logicalInterfaceId>\n              <logicalInterfaceType>SubVLAN</logicalInterfaceType>\n              <logicalInterfaceName>$iVlan0$oVlan</logicalInterfaceName>\n            </logicalInterfaceId>\n            <mediaInterfaceId>\n              <mediaInterfaceType>$mediaInterfaceType</mediaInterfaceType>\n              <mediaInterfaceName>$mediaInterfaceName</mediaInterfaceName>\n            </mediaInterfaceId>\n            <layer2SVlanId>\n              <oVlan>$oVlan</oVlan>\n              <iVlan>$iVlan</iVlan>\n            </layer2SVlanId>\n            <layer2Encapsulation>\n              <pppoE>\n                <sessions>1</sessions>\n              </pppoE>\n            </layer2Encapsulation>\n            <otherInfo>\n            </otherInfo>\n          </info>\n        </subInterfaceVlanEthernet>\n      </lrList>\n    </info>\n  </rfsBbrasClient>\n</service-descriptor>", "templateName": "consulta2", "endpointName": "NA", "active": 1}, {"version": 1, "templateXml": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<service-descriptor xmlns:fn=\"http://www.w3.org/2005/xpath-functions\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">\n  <rfsBbrasClient rfsName=\"rfsBbrasClient\" rfsType=\"ADSLService\" rfsVersion=\"1.0\" rfsId=\"3705581\" bbrasname=\"$logicalDeviceName\" operation=\"consulta\">\n    <info>\n      <lrList>\n        <subInterfaceVlanEthernet lrName=\"subInterfaceVlanEthernet\" lrType=\"BBRAS\" lrVersion=\"v1.0\">\n          <managementInfo>\n            <managementMethod>genericPlugin</managementMethod>\n            <managementContext>juniper/ERX1410/v4.0</managementContext>\n          </managementInfo>\n          <info>\n            <logicalDeviceId>\n              <logicalDeviceType>BBRAS</logicalDeviceType>\n              <logicalDeviceName>$logicalDeviceName</logicalDeviceName>\n            </logicalDeviceId>\n            <interfaceDescription>NULL</interfaceDescription>\n            <logicalInterfaceId>\n              <logicalInterfaceType>SubVLAN</logicalInterfaceType>\n              <logicalInterfaceName>$iVlan0$oVlan</logicalInterfaceName>\n            </logicalInterfaceId>\n            <mediaInterfaceId>\n              <mediaInterfaceType>$mediaInterfaceType</mediaInterfaceType>\n              <mediaInterfaceName>$mediaInterfaceName</mediaInterfaceName>\n            </mediaInterfaceId>\n            <layer2SVlanId>\n              <oVlan>$oVlan</oVlan>\n              <iVlan>$iVlan</iVlan>\n            </layer2SVlanId>\n            <layer2Encapsulation>\n              <pppoE>\n                <sessions>1</sessions>\n              </pppoE>\n            </layer2Encapsulation>\n            <otherInfo>\n            </otherInfo>\n          </info>\n        </subInterfaceVlanEthernet>\n      </lrList>\n    </info>\n  </rfsBbrasClient>\n</service-descriptor>", "templateName": "consulta3", "endpointName": "NA", "active": 1}, {"version": 1, "templateXml": "<?xml version=\"1.0\" encoding=\"utf-8\"?><templates>  <template>    <area>strArea</area>    <managedDomain>strManagedDomain</managedDomain>    <rfsName>strRFS</rfsName>    <templateName>strTemplateName</templateName>    <templateXML>strTemplateXML</templateXML>    <mappings>      <mapping>        <labelGUI>strLABEL</labelGUI>        <description>strDescription</description>        <templateMapping>strTemplateMapping</templateMapping>        <mandatory>strMandatory</mandatory>        <regex>strRegex</regex>      </mapping>    </mappings>  </template></templates>", "templateName": "JAVA", "endpointName": "NA", "active": 1}, {"version": 1, "templateXml": "<?xml version=\"1.0\" encoding=\"utf-8\"?><templates>  <template>    <area>strArea</area>    <managedDomain>strManagedDomain</managedDomain>    <rfsName>strRFS</rfsName>    <templateName>strTemplateName</templateName>    <templateXML>strTemplateXML</templateXML>    <mappings>      <mapping>        <labelGUI>strLABEL</labelGUI>        <description>strDescription</description>        <templateMapping>strTemplateMapping</templateMapping>        <mandatory>strMandatory</mandatory>        <regex>strRegex</regex>      </mapping>    </mappings>  </template></templates>", "templateName": "JAVA1", "endpointName": "NA", "active": 1}, {"version": 1, "templateXml": "<?xml version=\"1.0\" encoding=\"utf-8\"?><templates>  <template>    <area>strArea</area>    <managedDomain>strManagedDomain</managedDomain>    <rfsName>strRFS</rfsName>    <templateName>strTemplateName</templateName>    <templateXML>strTemplateXML</templateXML>    <mappings>      <mapping>        <labelGUI>strLABEL</labelGUI>        <description>strDescription</description>        <templateMapping>strTemplateMapping</templateMapping>        <mandatory>strMandatory</mandatory>        <regex>strRegex</regex>      </mapping>    </mappings>  </template></templates>", "templateName": "JAVA2", "endpointName": "CI_TEST_NA", "active": 1}]