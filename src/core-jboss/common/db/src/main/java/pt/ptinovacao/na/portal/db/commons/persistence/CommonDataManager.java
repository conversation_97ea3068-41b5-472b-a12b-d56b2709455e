package pt.ptinovacao.na.portal.db.commons.persistence;

import com.google.common.base.Stopwatch;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;

import java.lang.reflect.InvocationTargetException;

import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import javax.persistence.EntityManager;
import javax.persistence.LockModeType;
import javax.persistence.Query;
import javax.persistence.criteria.CriteriaBuilder;

import org.jboss.logmanager.Level;
import org.jboss.logmanager.Logger;

/**
 *
 * <AUTHOR> <michail-karama<PERSON>@telecom.pt>
 */
@SuppressWarnings("unchecked")
public abstract class CommonDataManager {

    private static final Logger LOGGER = Logger.getLogger(CommonDataManager.class.getName());

    @Inject
    private int commitSize;

    @Inject
    private BatchTxPersistence txPersistence;

    public abstract EntityManager getEntityManager();

    public <T> void bulkPersist(T... entities) throws CommonDataManagerException {
        int elementCnt = 0;

        try {
            for (List<T> partitionedList : Lists.partition(Lists.newArrayList(entities), this.commitSize)) {
                LOGGER.log(org.jboss.logmanager.Level.INFO, "[CommitSize={0}] Persisting {1} of {2} MPT entities",
                        new Object[]{this.commitSize, partitionedList.size(), entities.length
                            - elementCnt});
                this.txPersistence.txBulkPersist(this, (T[]) partitionedList.toArray());
                elementCnt += partitionedList.size();
            }
        } catch (Throwable t) {//NOSONAR
            throw new CommonDataManagerException(Throwables.getRootCause(t));
        }
    }

    public <T> void bulkRefresh(T... entities) throws CommonDataManagerException {
        int elementCnt = 0;

        try {
            for (List<T> partitionedList : Lists.partition(Lists.newArrayList(entities), this.commitSize)) {
                LOGGER.log(org.jboss.logmanager.Level.INFO, "[CommitSize={0}] Updating {1} of {2} MPT entities",
                        new Object[]{this.commitSize, partitionedList.size(), entities.length
                            - elementCnt});
                this.txPersistence.txBulkRefresh(this, (T[]) partitionedList.toArray());
                elementCnt += partitionedList.size();
            }
        } catch (Throwable t) {//NOSONAR
            throw new CommonDataManagerException(Throwables.getRootCause(t));
        }
    }

    public <T> void bulkRemove(T... entities) throws CommonDataManagerException {
        int elementCnt = 0;

        try {
            for (List<T> partitionedList : Lists.partition(Lists.newArrayList(entities), this.commitSize)) {
                LOGGER.log(org.jboss.logmanager.Level.INFO, "[CommitSize={0}] Removing {1} of {2} MPT entities",
                        new Object[]{this.commitSize, partitionedList.size(), entities.length
                            - elementCnt});
                this.txPersistence.txBulkRemove(this, (T[]) partitionedList.toArray());
                elementCnt += partitionedList.size();
            }
        } catch (Throwable t) {//NOSONAR
            throw new CommonDataManagerException(Throwables.getRootCause(t));
        }
    }

    /**
     * Finds and retrieves an object from the persistence context.
     *
     * @param <T> return type
     * @param type class of the object instance to find
     * @param id identifier of the object instance to find
     * @param lockMode lock mode
     * @return the located object instance or null if none was found
     * @throws CommonDataManagerException if an invalid id is provided or an error occurs during the
     * operation
     */
    public <T> T find(Class<T> type, Object id, LockModeType lockMode) throws CommonDataManagerException {
        return this.getEntityManager().find(type, id, lockMode);
    }

    /**
     * Persists the provided entity list and returns the last persisted entity. The transaction is
     * committed after the method call ends. Please note that additional exceptions may be thrown at
     * that time.
     *
     * @param <T> return type
     * @param entities entities to persist
     * @return last entity on the list
     * @throws CommonDataManagerException if an error occurs during the operation
     */
    public <T> void persist(T... entities) throws CommonDataManagerException {
        this.joinEmToBeanTransaction();
        final Stopwatch ticker = Stopwatch.createStarted();
        boolean persisted = false;

        try {
            for (T entity : entities) {
                try {
                    this.getEntityManager().persist(entity);
                    persisted = true;
                } catch (Throwable t) {//NOSONAR
                    throw new CommonDataManagerException("persisting entity "
                            + entity.getClass().getSimpleName() + ": "
                            + Throwables.getRootCause(t).getClass().getSimpleName() + ": "
                            + Throwables.getRootCause(t).getMessage(), t);
                }

                if (LOGGER.isLoggable(Level.FINE)) {
                    try {
                        // Ouch!
                        LOGGER.log(Level.FINE, "Persisted {0} entity id: {1}", new Object[]{entity.getClass().getSimpleName(), this.keyFinder(entity)});

                    } catch (Throwable t) { //NOSONAR
                        throw new CommonDataManagerException("No method found while persisting entity..."
                                + ": " + Throwables.getRootCause(t).getMessage(), Throwables.getRootCause(t));
                    }
                }
            }
        } finally {
            if (persisted) {
                this.getEntityManager().flush();
                LOGGER.log(Level.INFO, "Persisted {0} entities in [{1}ms]", new Object[]{entities.length, ticker.stop().elapsed(TimeUnit.MILLISECONDS)});
            }
        }
    }

    /**
     * Removes the provided entity list and returns the last removed entity. The transaction is
     * committed after the method call ends. Please note that additional exceptions may be thrown at
     * that time.
     *
     * @param <T> return type
     * @param lockMode lock mode
     * @param entities entities to remove
     * @return last entity on the list
     * @throws CommonDataManagerException if an error occurs during the operation
     */
    public <T> void remove(LockModeType lockMode, T... entities) throws CommonDataManagerException {
        this.joinEmToBeanTransaction();
        final Stopwatch ticker = Stopwatch.createStarted();
        boolean removed = false;

        try {
            for (T entity : entities) {
                try {
                    if (!this.getEntityManager().contains(entity)) {
                        Class<T> type = (Class<T>) entity.getClass(); // So maybe generics were not the best option in this scenario (Mario you are dam right).
                        entity = this.getEntityManager().find(type, this.keyFinder(entity), lockMode); // worse cenario using reflection... Jesus forgive me...
                    }
                    this.getEntityManager().remove(entity);
                    removed = true;
                } catch (Throwable t) {//NOSONAR
                    throw new CommonDataManagerException("removing entity "
                            + entity.getClass().getSimpleName() + ": "
                            + Throwables.getRootCause(t).getClass().getSimpleName() + ": "
                            + Throwables.getRootCause(t).getMessage(), t);
                }

                if (LOGGER.isLoggable(Level.FINE)) {
                    // Ouch!
                    LOGGER.log(Level.FINE, "Removed {0} entity", entity.getClass().getSimpleName());
                }
            }
        } finally {
            if (removed) {
                this.getEntityManager().flush();
                LOGGER.log(Level.INFO, "Removed {0} entities in [{1}ms]", new Object[]{entities.length, ticker.stop().elapsed(TimeUnit.MILLISECONDS)});
            }
        }
    }

    /**
     * Refresh the provided entity list and returns the last refreshed entity. The transaction is
     * committed after the method call ends. Please note that additional exceptions may be thrown at
     * that time.
     *
     * @param <T> return type
     * @param entities entities to be refreshed
     * @return last entity on the list
     * @throws CommonDataManagerException if an error occurs during the operation
     */
    public <T> void refresh(T... entities) throws CommonDataManagerException {
        this.joinEmToBeanTransaction();
        final Stopwatch ticker = Stopwatch.createStarted();
        boolean refreshed = false;

        try {

            for (T entity : entities) {
                try {
                    this.getEntityManager().merge(entity);
                    refreshed = true;
                } catch (Throwable t) {//NOSONAR
                    throw new CommonDataManagerException("refreshing entity "
                            + entity.getClass().getSimpleName() + ": "
                            + Throwables.getRootCause(t).getClass().getSimpleName() + ": "
                            + Throwables.getRootCause(t).getMessage(), t);
                }

                if (LOGGER.isLoggable(Level.FINE)) {
                    try {
                        // Ouch!
                        LOGGER.log(Level.FINE, "Refreshed {0} entity id: {1}", new Object[]{entity.getClass().getSimpleName(), this.keyFinder(entity)});
                    } catch (Throwable t) {//NOSONAR
                        throw new CommonDataManagerException("No method found while refreshing entity..."
                                + ": " + Throwables.getRootCause(t).getMessage(), t);
                    }
                }
            }

        } finally {
            if (refreshed) {
                this.getEntityManager().flush();
                LOGGER.log(Level.INFO, "Refreshed {0} entities in [{1}ms]", new Object[]{entities.length, ticker.stop().elapsed(TimeUnit.MILLISECONDS)});
            }
        }
    }

    /**
     * Find a list of entities given a certain query.
     *
     * @param queryStr string to execute
     * @param parameters needed for the query execution
     * @param isNamedQuery use several parameters for a certain named query
     * @param isNativeSql to indicate that the query string is native sql
     * @param resultClass the class of the resulting instance(s) in case of a native sql query
     * @return a List of the query results
     * @throws CommonDataManagerException if an error occurs during the operation
     */
    public <T> List<T> findByQuery(String queryStr, Map<String, Object> parameters, boolean isNamedQuery,
            boolean isNativeSql, Class<T> resultClass) throws CommonDataManagerException {
        final Stopwatch ticker = Stopwatch.createStarted();
        Query query;

        try {
            // Alternative to native sql
            if (isNativeSql) {
                query = this.getEntityManager().createNativeQuery(queryStr, resultClass);
            } else {
                query = isNamedQuery ? this.getEntityManager().createNamedQuery(queryStr)
                        : this.getEntityManager().createQuery(queryStr);
            }
            // Paramaters eval
            if (parameters != null) {
                for (Entry<String, Object> parameter : parameters.entrySet()) {
                    switch (parameter.getKey()) {
                        case Commons.FIRST_RESULT:
                            query.setFirstResult(Integer.valueOf(parameter.getValue().toString()));
                            break;
                        case Commons.MAX_RESULT:
                            query.setMaxResults(Integer.valueOf(parameter.getValue().toString()));
                            break;
                        default:
                            query.setParameter(parameter.getKey(), parameter.getValue());
                            break;
                    }
                }
            }

            LOGGER.log(Level.INFO, "Query [{0}] executed in [{1}ms]", new Object[]{queryStr, ticker.stop().elapsed(TimeUnit.MILLISECONDS)});

            return query.getResultList();

        } catch (Throwable t) {//NOSONAR
            throw new CommonDataManagerException("executing query: '" + queryStr
                    + "': "
                    + Throwables.getRootCause(t).getClass().getSimpleName() + ": "
                    + Throwables.getRootCause(t).getMessage(), t);
        }
    }

    /**
     * Find a list of entities given a certain named query.
     *
     * @param namedQueryTag string representing a named query tag name
     * @param parameterKey parameter of the query
     * @param parameterValue value for the query
     * @return a List of the query results
     * @throws CommonDataManagerException if an error occurs during the operation
     */
    public <T> List<T> findByNamedQueryTag(String namedQueryTag, String parameterKey, Object parameterValue)
            throws CommonDataManagerException {
        try {
            if (parameterKey == null && parameterValue == null) {
                return this.getEntityManager().createNamedQuery(namedQueryTag).getResultList();
            } else {
                return this.getEntityManager().createNamedQuery(namedQueryTag).setParameter(parameterKey, parameterValue).getResultList();
            }
        } catch (Throwable t) {//NOSONAR
            throw new CommonDataManagerException("Named query: " + namedQueryTag
                    + " returned error: " + Throwables.getRootCause(t).getClass().getSimpleName()
                    + ": "
                    + Throwables.getRootCause(t).getMessage(), t);
        }
    }

    /**
     * Needs to be extended by the child data managers.
     *
     * @param <T>
     * @param entity
     * @return
     * @throws NoSuchMethodException
     * @throws IllegalAccessException
     * @throws InvocationTargetException
     */
    public abstract <T> Object keyFinder(T entity) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException;

    public CriteriaBuilder getCriteriaBuilder() {
        return this.getEntityManager().getCriteriaBuilder();
    }

    private void joinEmToBeanTransaction() {
        if (!this.getEntityManager().isJoinedToTransaction()) {
            this.getEntityManager().joinTransaction();
        }
    }
}
