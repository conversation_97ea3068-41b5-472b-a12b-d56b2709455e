package pt.ptinovacao.na.portal.db.commons.persistence.resources.qualifiers;

import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.lang.annotation.Inherited;

import javax.enterprise.util.Nonbinding;

import javax.interceptor.InterceptorBinding;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
@Inherited
@InterceptorBinding
@Target({METHOD, TYPE})
@Retention(RUNTIME)
public @interface Audited {

    @Nonbinding
    boolean logToFile() default false;
}
