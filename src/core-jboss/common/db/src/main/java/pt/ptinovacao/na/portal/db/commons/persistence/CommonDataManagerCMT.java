package pt.ptinovacao.na.portal.db.commons.persistence;

import java.lang.reflect.InvocationTargetException;

import java.util.List;
import java.util.Map;

import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;

import javax.persistence.LockModeType;

import pt.ptinovacao.na.portal.db.commons.persistence.resources.qualifiers.Audited;


/**
 * Will be extended to provide some extra transactional control. By default, the value of the
 * transaction attribute for a method of a bean with container-managed transaction demarcation is
 * the REQUIRED transaction attribute, and the transaction attribute does not need to be explicitly
 * specified in this case.
 *
 * <AUTHOR> <<EMAIL>>
 */
@SuppressWarnings("unchecked")
@TransactionAttribute(TransactionAttributeType.REQUIRED)
public abstract class CommonDataManagerCMT extends CommonDataManager {

    @Override
    @SuppressWarnings("unchecked")
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    @Audited(logToFile = true)
    public <T> void bulkRefresh(T... entities) throws CommonDataManagerException {
        super.bulkRefresh(entities);
    }

    @Override
    @SuppressWarnings("unchecked")
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    @Audited(logToFile = true)
    public <T> void bulkRemove(T... entities) throws CommonDataManagerException {
        super.bulkRemove(entities);
    }

    @SuppressWarnings("unchecked")
    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    @Audited(logToFile = true)
    public <T> void bulkPersist(T... entities) throws CommonDataManagerException {
        super.bulkPersist(entities);
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.NOT_SUPPORTED)
    public <T> Object keyFinder(final T entity) throws NoSuchMethodException,
            IllegalAccessException, InvocationTargetException {
        return entity.getClass().getDeclaredMethod("getId").invoke(entity, new Object[0]);
    }

    @Override
    @Audited(logToFile = true)
    @TransactionAttribute(TransactionAttributeType.NOT_SUPPORTED)
    public <T> List<T> findByNamedQueryTag(String namedQueryTag, String parameterKey, Object parameterValue) throws CommonDataManagerException {
        return super.findByNamedQueryTag(namedQueryTag, parameterKey, parameterValue);
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.NOT_SUPPORTED)
    @Audited(logToFile = true)
    public <T> List<T> findByQuery(String queryStr, Map<String, Object> parameters, boolean isNamedQuery, boolean isNativeSql, Class<T> resultClass) throws CommonDataManagerException {
        return super.findByQuery(queryStr, parameters, isNamedQuery, isNativeSql, resultClass);
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    @Audited(logToFile = true)
    public <T> void refresh(T... entities) throws CommonDataManagerException {
        super.refresh(entities);
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    @Audited(logToFile = true)
    public <T> void remove(LockModeType lockMode, T... entities) throws CommonDataManagerException {
        super.remove(lockMode, entities);
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    @Audited(logToFile = true)
    public <T> void persist(T... entities) throws CommonDataManagerException {
        super.persist(entities);
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.NOT_SUPPORTED)
    @Audited(logToFile = true)
    public <T> T find(Class<T> type, Object id, LockModeType lockMode) throws CommonDataManagerException {
        return super.find(type, id, lockMode);
    }
}
