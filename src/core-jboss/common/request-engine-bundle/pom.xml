<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>pt.ptinovacao.na-portal</groupId>
        <artifactId>na-portal-core-jboss-commons</artifactId>
        <version>10.55.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>na-portal-core-request-engine-bundle</artifactId>
    <packaging>war</packaging>

    <dependencies>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jboss.logging</groupId>
            <artifactId>jboss-logging</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jboss.logging</groupId>
            <artifactId>jboss-logging-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-core-request-engine</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-commons</groupId>
            <artifactId>na-commons-interfaces-na-ws-api</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na</groupId>
            <artifactId>na-smart2m-wsdl</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <configuration>
                    <outputFileNameMapping>@{artifactId}@.@{extension}@</outputFileNameMapping>
                    <archive>
                        <manifest>
                            <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                        </manifest>
                        <manifestEntries>
                            <Implementation-Build>${project.version}</Implementation-Build>
                            <Dependencies>deployment.na-portal-commons-model.jar export</Dependencies>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
