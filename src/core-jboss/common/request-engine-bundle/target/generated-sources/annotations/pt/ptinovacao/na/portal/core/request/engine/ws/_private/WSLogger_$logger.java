package pt.ptinovacao.na.portal.core.request.engine.ws._private;

import java.util.Locale;
import java.lang.Integer;
import java.io.Serializable;
import javax.annotation.Generated;
import org.jboss.logging.DelegatingBasicLogger;
import org.jboss.logging.BasicLogger;
import java.lang.Long;
import java.lang.Throwable;
import java.lang.String;
import org.jboss.logging.Logger;


import static org.jboss.logging.Logger.Level.ERROR;
import static org.jboss.logging.Logger.Level.INFO;
import static org.jboss.logging.Logger.Level.DEBUG;

/**
 * Warning this class consists of generated code.
 */
@Generated(value = "org.jboss.logging.processor.generator.model.MessageLoggerImplementor", date = "2025-07-01T11:20:41+0100")
public class WSLogger_$logger extends DelegatingBasicLogger implements WSLogger, BasicLogger, Serializable {
    private static final long serialVersionUID = 1L;
    private static final String FQCN = WSLogger_$logger.class.getName();
    public WSLogger_$logger(final Logger log) {
        super(log);
    }
    private static final Locale LOCALE = Locale.ROOT;
    protected Locale getLoggingLocale() {
        return LOCALE;
    }
    @Override
    public final void opkResponseContent(final Long opk, final String info, final String message, final String clientID, final String description, final String operation, final Integer priority, final String schedule, final String state, final String domain, final Long primaryKey, final String applicationDN, final String url, final String hostName) {
        super.log.logf(FQCN, DEBUG, null, opkResponseContent$str(), opk, info, message, clientID, description, operation, priority, schedule, state, domain, primaryKey, applicationDN, url, hostName);
    }
    private static final String opkResponseContent = "NAPORTALWS001001: [OPK %s] order response content:\n=============================== order response payload ===============================\n - Info: %s\n - Message: %s\n - Header:\n\t * clientID: %s\n\t * description: %s\n\t * operation: %s\n\t * priority: %d\n\t * schedule: %s\n\t * state: %s\n\t * domain: %s\n\t - OrderKey: \n\t\t * primaryKey: %s\n\t\t * applicationDN: %s\n\t\t - ApplicationContext: \n\t\t\t * url: %s\n\t\t\t * hostName: %s\n======================================================================================";
    protected String opkResponseContent$str() {
        return opkResponseContent;
    }
    @Override
    public final void opkReceived(final Long opk, final String clientId) {
        super.log.logf(FQCN, INFO, null, opkReceived$str(), opk, clientId);
    }
    private static final String opkReceived = "NAPORTALWS001002: [OPK %s] received order response from client %s";
    protected String opkReceived$str() {
        return opkReceived;
    }
    @Override
    public final void unregisterTimeoutRegistryPointException(final Throwable t, final Long opk, final String clientId) {
        super.log.logf(FQCN, ERROR, t, unregisterTimeoutRegistryPointException$str(), opk, clientId);
    }
    private static final String unregisterTimeoutRegistryPointException = "NAPORTALWS001003: [OPK %s] failed to unregister client '%s' from timeout registry point...";
    protected String unregisterTimeoutRegistryPointException$str() {
        return unregisterTimeoutRegistryPointException;
    }
    @Override
    public final void unregisterWSRegistryPointException(final Throwable t, final Long opk, final String clientId) {
        super.log.logf(FQCN, ERROR, t, unregisterWSRegistryPointException$str(), opk, clientId);
    }
    private static final String unregisterWSRegistryPointException = "NAPORTALWS001004: [OPK %s] failed to unregister client '%s' from webservice registry point...";
    protected String unregisterWSRegistryPointException$str() {
        return unregisterWSRegistryPointException;
    }
    @Override
    public final void wsGenericFailure(final Throwable t, final Long opk, final String cause) {
        super.log.logf(FQCN, ERROR, t, wsGenericFailure$str(), opk, cause);
    }
    private static final String wsGenericFailure = "NAPORTALWS001005: [OPK %s] WebService order response processing failure: %s";
    protected String wsGenericFailure$str() {
        return wsGenericFailure;
    }
}
