package pt.ptinovacao.na.portal.core.request.engine.registry;

import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;


/**
 * <AUTHOR> <<EMAIL>> Apr 9, 2012
 * @param <T>
 */
public abstract class CheckedRegistryPoint<T> implements RegistryPoint<T> {

    private final String name;
    private final ConcurrentMap<String, T> map = new ConcurrentHashMap<>();

    @SuppressWarnings("LeakingThisInConstructor")
    public CheckedRegistryPoint() {
	this.name = getClass().getCanonicalName();
	MainRegistry.getInstance().register(name, this);
    }

    CheckedRegistryPoint(String name) {
	this.name = name;
    }

    /**
     * Maps an instance of {@link T} for a given key.
     *
     * @param key
     * @param object
     * @return true if the object was registered.
     * @throws pt.ptinovacao.na.portal.core.request.engine.registry.RegistryPointException
     */
    @Override
    public T registerIfAbsent(String key, T object) throws RegistryPointException {
	return map.putIfAbsent(validateKey(key), object);
    }

    @Override
    public T register(String key, T object) throws RegistryPointException {
	return map.put(validateKey(key), object);
    }

    @Override
    public T safeRegister(String key, T object) throws RegistryPointException {
	if (map.containsKey(key)) {
	    throw new RegistryPointException(RegistryPointException.ALREADY_REGISTERED, key);
	}
	return map.put(key, object);
    }

    @Override
    public T unregister(String key) throws RegistryPointException {
	T object = map.remove(validateKey(key));
	if (object == null) {
	    throw new RegistryPointException(RegistryPointException.NOT_REGISTERED, key);
	}
	return object;
    }

    @Override
    public T get(String key) {
	return map.get(key);
    }

    /**
     * Doesn't do anything. Override if necessary.
     *
     * @param key
     * @return key
     * @throws pt.ptinovacao.na.portal.core.request.engine.registry.RegistryPointException
     */
    protected String validateKey(String key) {
	//Do some integrity check!
	return key;
    }

    @Override
    public Set<Entry<String, T>> getEntrySet() {
	return map.entrySet();
    }

    @Override
    public boolean containsKey(String key) {
	return map.containsKey(key);
    }
}
