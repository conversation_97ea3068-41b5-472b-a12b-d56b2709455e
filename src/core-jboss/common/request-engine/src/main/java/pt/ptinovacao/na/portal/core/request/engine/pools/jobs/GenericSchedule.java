/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package pt.ptinovacao.na.portal.core.request.engine.pools.jobs;

/**
 * <AUTHOR> <<EMAIL>>
 * Apr 23, 2012
 */
public abstract class GenericSchedule extends RootTask implements Runnable {

    public GenericSchedule(Class clazz, short type) {
	super(clazz, type);
    }

    @Override
    protected void preProcess() {
	//Don't measure stuff
    }

    @Override
    protected void postProcess() {
	//Don't measure stuff
    }
    
    public abstract void doJob();
    
    @Override
    public void run() {
        preProcess();
        try { doJob(); }
        catch(Exception e) { //TODO
                }
        finally { postProcess(); }
    }
    
    
}
