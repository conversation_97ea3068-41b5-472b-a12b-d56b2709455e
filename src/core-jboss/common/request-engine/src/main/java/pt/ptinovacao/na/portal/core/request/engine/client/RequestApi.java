package pt.ptinovacao.na.portal.core.request.engine.client;

import com.google.common.base.Stopwatch;
import com.google.common.base.Throwables;
import pt.ptinovacao.na.portal.core.request.engine._private.RELogger;
import pt.ptinovacao.na.portal.core.request.engine._private.WS_IO_Logger;
import pt.ptinovacao.na.portal.core.request.engine.cleaner.Timeout;
import pt.ptinovacao.na.portal.core.request.engine.cleaner.TimeoutRegistry;
import pt.ptinovacao.na.portal.core.request.engine.model.request.Request;
import pt.ptinovacao.na.portal.core.request.engine.registry.RegistryPointException;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR> <<EMAIL>> Apr 16, 2012
 */
public final class RequestApi {

    private RequestApi() {
    }

    public static RequestApi getInstance() {
        return RequestApiHolder.INSTANCE;
    }

    private static class RequestApiHolder {

        private static final RequestApi INSTANCE = new RequestApi();
    }

    private static final SoapWebServiceRequest NA_CLIENT = new SoapWebServiceRequest();
    private static final AtomicLong TICKETS = new AtomicLong(0);

    public String callWebService(Request request, ResponseHandler responseHandler) {
        String opk = "ERROR", ticket = getTicket(), errorMsg = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        RELogger.LOGGER.requestApiCallWebService(ticket, request.getClientId(), request.getOperation());

        try {
            ClientRegistry naTicket = new ClientRegistry(ticket, responseHandler);
            WebServiceClientRegistryPoint.getInstance().safeRegister(request.getClientId(), naTicket);
            Timeout timeoutTask = new Timeout(naTicket, request.getTimeout());
            TimeoutRegistry.getInstance().register(request.getClientId(), timeoutTask);
            opk = NA_CLIENT.callSoapWebService(request, naTicket);

            RELogger.LOGGER.requestApiCallWebService(opk, request.getAppDn(), request.getPassword(),
                    request.getOperation(), request.getClientId(), request.getDescription(),
                    request.getClientURL(), request.getResponseURL(), request.getTimeout(),
                    request.getNodeType(), request.getPayload());

        } catch (RegistryPointException ex) {
            errorMsg = ex.getMessage();
            responseHandler.handleException(new RequestException(ex));
            RELogger.LOGGER.requestApiCallWebServiceFailure(ex, ticket, request.getClientId(),
                    Throwables.getRootCause(ex).getMessage());
        } catch (Throwable t) {
            errorMsg = t.getMessage();
            RELogger.LOGGER.requestApiCallWebServiceFailure(t, ticket, request.getClientId(),
                    Throwables.getRootCause(t).getMessage());
        } finally {
            String state = (errorMsg != null || (opk != null && "ERROR".equals(opk))) ? "NOK" : "OK";
            WS_IO_Logger.LOGGER.webserviceIO(WS_IO_Logger.REGISTRY_TYPE.ASYNC_RECEPTION,
                    request.getAppDn(), request.getClientId(), opk, request.getOperation(),
                    request.getResponseURL(), state, null, errorMsg, request.getClientURL(),
                    request.getPayload());
            RELogger.LOGGER.requestApiCallWebServiceEnd(opk, request.getClientId(),
                    stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
        }

        return opk;
    }

    private String getTicket() {
        return "NA_PORTAL" + TICKETS.getAndIncrement();
    }
}
