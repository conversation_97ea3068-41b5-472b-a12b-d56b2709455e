package pt.ptinovacao.na.portal.core.request.engine._private;

import java.util.Locale;
import java.io.Serializable;
import javax.annotation.Generated;
import org.jboss.logging.DelegatingBasicLogger;
import org.jboss.logging.BasicLogger;
import pt.ptinovacao.na.portal.commons.model.NodeType;
import java.lang.Long;
import java.lang.Throwable;
import java.lang.String;
import org.jboss.logging.Logger;


import static org.jboss.logging.Logger.Level.ERROR;
import static org.jboss.logging.Logger.Level.INFO;
import static org.jboss.logging.Logger.Level.DEBUG;
import static org.jboss.logging.Logger.Level.WARN;

/**
 * Warning this class consists of generated code.
 */
@Generated(value = "org.jboss.logging.processor.generator.model.MessageLoggerImplementor", date = "2025-07-01T11:20:41+0100")
public class RELogger_$logger extends DelegatingBasicLogger implements RELogger, BasicLogger, Serializable {
    private static final long serialVersionUID = 1L;
    private static final String FQCN = RELogger_$logger.class.getName();
    public RELogger_$logger(final Logger log) {
        super(log);
    }
    private static final Locale LOCALE = Locale.ROOT;
    protected Locale getLoggingLocale() {
        return LOCALE;
    }
    @Override
    public final void requestApiCallWebService(final String ticket, final String clientId, final String operation) {
        super.log.logf(FQCN, INFO, null, requestApiCallWebService3$str(), ticket, clientId, operation);
    }
    private static final String requestApiCallWebService3 = "REQUESTENGINE000001: [Ticket %s] RequestAPI received a new call to webservice for clientId '%s' for operation %s";
    protected String requestApiCallWebService3$str() {
        return requestApiCallWebService3;
    }
    @Override
    public final void requestApiCallWebServiceFailure(final Throwable t, final String ticket, final String clientId, final String cause) {
        super.log.logf(FQCN, ERROR, t, requestApiCallWebServiceFailure$str(), ticket, clientId, cause);
    }
    private static final String requestApiCallWebServiceFailure = "REQUESTENGINE000002: [Ticket %s] RequestAPI failed to call webservice for clientId '%s' due to: %s";
    protected String requestApiCallWebServiceFailure$str() {
        return requestApiCallWebServiceFailure;
    }
    @Override
    public final void requestApiCallWebServiceEnd(final String opk, final String clientId, final long milis) {
        super.log.logf(FQCN, INFO, null, requestApiCallWebServiceEnd$str(), opk, clientId, milis);
    }
    private static final String requestApiCallWebServiceEnd = "REQUESTENGINE000003: [OPK %s] RequestAPI finished calling webservice for clientId '%s' and took %d ms";
    protected String requestApiCallWebServiceEnd$str() {
        return requestApiCallWebServiceEnd;
    }
    @Override
    public final void soapWebServiceFailure(final Throwable t, final String ticket, final String opk, final String type, final String clientId, final String cause) {
        super.log.logf(FQCN, ERROR, t, soapWebServiceFailure$str(), ticket, opk, type, clientId, cause);
    }
    private static final String soapWebServiceFailure = "REQUESTENGINE000004: [Ticket/OPK %s/%s] Failed to send soap request of type '%s' for clientId '%s' due to: %s";
    protected String soapWebServiceFailure$str() {
        return soapWebServiceFailure;
    }
    @Override
    public final void soapWebServiceUnregisterTimeoutFailure(final Throwable t, final String ticket, final String opk, final String clientId, final String cause) {
        super.log.logf(FQCN, ERROR, t, soapWebServiceUnregisterTimeoutFailure$str(), ticket, opk, clientId, cause);
    }
    private static final String soapWebServiceUnregisterTimeoutFailure = "REQUESTENGINE000005: [Ticket/OPK %s/%s] Failed to unregister timeout from registry for clientId '%s' due to: %s";
    protected String soapWebServiceUnregisterTimeoutFailure$str() {
        return soapWebServiceUnregisterTimeoutFailure;
    }
    @Override
    public final void soapWebServiceUnregisterRegistryFailure(final Throwable t, final String ticket, final String opk, final String clientId, final String cause) {
        super.log.logf(FQCN, ERROR, t, soapWebServiceUnregisterRegistryFailure$str(), ticket, opk, clientId, cause);
    }
    private static final String soapWebServiceUnregisterRegistryFailure = "REQUESTENGINE000006: [Ticket/OPK %s/%s] Failed to unregister request from registry for clientId '%s' due to: %s";
    protected String soapWebServiceUnregisterRegistryFailure$str() {
        return soapWebServiceUnregisterRegistryFailure;
    }
    @Override
    public final void naResponseParserParse(final String payload) {
        super.log.logf(FQCN, INFO, null, naResponseParserParse$str(), payload);
    }
    private static final String naResponseParserParse = "REQUESTENGINE000007: NA response parser is going to parse the following payload %s";
    protected String naResponseParserParse$str() {
        return naResponseParserParse;
    }
    @Override
    public final void naResponseParserParseEnd(final long milis) {
        super.log.logf(FQCN, INFO, null, naResponseParserParseEnd$str(), milis);
    }
    private static final String naResponseParserParseEnd = "REQUESTENGINE000008: NA response parser took %d ms to parse payload...";
    protected String naResponseParserParseEnd$str() {
        return naResponseParserParseEnd;
    }
    @Override
    public final void naResponseParserParseFailure(final Throwable t, final String cause) {
        super.log.logf(FQCN, ERROR, t, naResponseParserParseFailure$str(), cause);
    }
    private static final String naResponseParserParseFailure = "REQUESTENGINE000009: NA response parser failed due to: %s";
    protected String naResponseParserParseFailure$str() {
        return naResponseParserParseFailure;
    }
    @Override
    public final void sooResponseParserParse(final String payload) {
        super.log.logf(FQCN, INFO, null, sooResponseParserParse$str(), payload);
    }
    private static final String sooResponseParserParse = "REQUESTENGINE000010: NA response parser is going to parse the following payload %s";
    protected String sooResponseParserParse$str() {
        return sooResponseParserParse;
    }
    @Override
    public final void sooResponseParserParseEnd(final long milis) {
        super.log.logf(FQCN, INFO, null, sooResponseParserParseEnd$str(), milis);
    }
    private static final String sooResponseParserParseEnd = "REQUESTENGINE000011: NA response parser took %d ms to parse payload...";
    protected String sooResponseParserParseEnd$str() {
        return sooResponseParserParseEnd;
    }
    @Override
    public final void sooResponseParserParseFailure(final Throwable t, final String cause) {
        super.log.logf(FQCN, ERROR, t, sooResponseParserParseFailure$str(), cause);
    }
    private static final String sooResponseParserParseFailure = "REQUESTENGINE000012: NA response parser failed due to: %s";
    protected String sooResponseParserParseFailure$str() {
        return sooResponseParserParseFailure;
    }
    @Override
    public final void timeoutScheduleUnregisterFail(final Throwable t, final String key, final String cause) {
        super.log.logf(FQCN, WARN, t, timeoutScheduleUnregisterFail$str(), key, cause);
    }
    private static final String timeoutScheduleUnregisterFail = "REQUESTENGINE000013: Timeout schedule failed to unregister from registry with key %s due to: %s";
    protected String timeoutScheduleUnregisterFail$str() {
        return timeoutScheduleUnregisterFail;
    }
    @Override
    public final void responseParserJAXBInitError(final Throwable t, final String cause) {
        super.log.logf(FQCN, ERROR, t, responseParserJAXBInitError$str(), cause);
    }
    private static final String responseParserJAXBInitError = "REQUESTENGINE000014: Response parser factory failed JAXB initialization due to: %s";
    protected String responseParserJAXBInitError$str() {
        return responseParserJAXBInitError;
    }
    @Override
    public final void responseParserJAXBSerializationFail(final Throwable t, final String cause) {
        super.log.logf(FQCN, ERROR, t, responseParserJAXBSerializationFail$str(), cause);
    }
    private static final String responseParserJAXBSerializationFail = "REQUESTENGINE000015: Response parser factory JAXB serialization failure due to: %s";
    protected String responseParserJAXBSerializationFail$str() {
        return responseParserJAXBSerializationFail;
    }
    @Override
    public final void requestApiCallWebService(final String opk, final String appDn, final String password, final String operation, final String clientId, final String description, final String clientURL, final String responseURL, final Long timeout, final NodeType nodeType, final String payload) {
        super.log.logf(FQCN, DEBUG, null, requestApiCallWebService11$str(), opk, appDn, password, operation, clientId, description, clientURL, responseURL, timeout, nodeType, payload);
    }
    private static final String requestApiCallWebService11 = "REQUESTENGINE000016: [OPK %s] RequestAPI WS order request content:\n=============================== order request payload ===============================\n - ApplicationDN: %s\n - Password: %s\n - Operation: %s\n - Client Id: %s\n - Description: %s\n - Client URL: %s\n - Response URL: %s\n - Timeout: %d ms\n - Node type: %s\n - Payload: \n %s \n=====================================================================================";
    protected String requestApiCallWebService11$str() {
        return requestApiCallWebService11;
    }
}
