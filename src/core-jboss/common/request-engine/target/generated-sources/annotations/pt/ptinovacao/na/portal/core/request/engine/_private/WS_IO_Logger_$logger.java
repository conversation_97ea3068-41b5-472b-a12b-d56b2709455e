package pt.ptinovacao.na.portal.core.request.engine._private;

import java.util.Locale;
import java.lang.Integer;
import java.io.Serializable;
import javax.annotation.Generated;
import org.jboss.logging.DelegatingBasicLogger;
import org.jboss.logging.BasicLogger;
import pt.ptinovacao.na.portal.core.request.engine._private.WS_IO_Logger.REGISTRY_TYPE;
import java.lang.String;
import org.jboss.logging.Logger;


import static org.jboss.logging.Logger.Level.INFO;

/**
 * Warning this class consists of generated code.
 */
@Generated(value = "org.jboss.logging.processor.generator.model.MessageLoggerImplementor", date = "2025-07-01T11:20:41+0100")
public class WS_IO_Logger_$logger extends DelegatingBasicLogger implements WS_IO_Logger, BasicLogger, Serializable {
    private static final long serialVersionUID = 1L;
    private static final String FQCN = WS_IO_Logger_$logger.class.getName();
    public WS_IO_Logger_$logger(final Logger log) {
        super(log);
    }
    private static final Locale LOCALE = Locale.ROOT;
    protected Locale getLoggingLocale() {
        return LOCALE;
    }
    @Override
    public final void webserviceIO(final REGISTRY_TYPE type, final String appDn, final String clientId, final String orderKey, final String operation, final String url, final String state, final Integer retry, final String errorMessage, final String clientIp, final String message) {
        super.log.logf(FQCN, INFO, null, webserviceIO$str(), type, appDn, clientId, orderKey, operation, url, state, retry, errorMessage, clientIp, message);
    }
    private static final String webserviceIO = "%s;%s;%s;%s;%s;%s;%s;%d;%s;%s;%s";
    protected String webserviceIO$str() {
        return webserviceIO;
    }
}
