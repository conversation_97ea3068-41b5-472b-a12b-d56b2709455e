package pt.ptinovacao.na.portal.core.request.engine._private;

import java.util.Locale;
import java.io.Serializable;
import javax.annotation.Generated;
import javax.xml.bind.JAXBException;
import java.lang.String;
import org.jboss.logging.Logger;
import java.util.Arrays;
import java.lang.Exception;

/**
 * Warning this class consists of generated code.
 */
@Generated(value = "org.jboss.logging.processor.generator.model.MessageLoggerImplementor", date = "2025-07-01T11:20:41+0100")
public class REMessages_$logger implements REMessages, Serializable {
    private static final long serialVersionUID = 1L;
    private static final String FQCN = REMessages_$logger.class.getName();
    public REMessages_$logger(final Logger log) {
        this.log = log;
    }
    protected final Logger log;
    private static final Locale LOCALE = Locale.ROOT;
    protected Locale getLoggingLocale() {
        return LOCALE;
    }
    private static final String jaxbContextNotInitialzied = "REQUESTENGINE000100: JAXB context not properly initialized";
    protected String jaxbContextNotInitialzied$str() {
        return jaxbContextNotInitialzied;
    }
    @Override
    public final JAXBException jaxbContextNotInitialzied() {
        final JAXBException result = new JAXBException(String.format(getLoggingLocale(), jaxbContextNotInitialzied$str()));
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String callNaWebServiceUrlMissing = "REQUESTENGINE000101: Missing response URL for request with clientId '%s'";
    protected String callNaWebServiceUrlMissing$str() {
        return callNaWebServiceUrlMissing;
    }
    @Override
    public final Exception callNaWebServiceUrlMissing(final String clientId) {
        final Exception result = new Exception(String.format(getLoggingLocale(), callNaWebServiceUrlMissing$str(), clientId));
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
}
