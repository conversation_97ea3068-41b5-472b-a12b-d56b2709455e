/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package pt.ptinovacao.na.portal.mpt.client.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;
import org.junit.Test;
import static org.junit.Assert.*;
import org.junit.Before;
import org.junit.Rule;
import org.junit.rules.ExpectedException;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.entities.Schedule;

/**
 *
 * <AUTHOR>
 */
public class YamlSchedulesReaderTest {
    private static final SimpleDateFormat DATE_FORMATER = new SimpleDateFormat("yyyy-MM-dd");
    
    public YamlSchedulesReaderTest() {
    }

    @Before
    public void initializeTests(){
          System.setProperty("java.util.logging.manager", "org.jboss.logmanager.LogManager");
    }
    
    @Rule  
    public ExpectedException thrown = ExpectedException.none();
    
    @Test
    public void testParse_MultipleSchedule_success() throws ParseException {
        String scheduleYamlDescription = 
                  "- schedule:\n"
                + "  daysOfWeek: [ALL]\n"
                + "  endAt: \"2015-12-30\"\n"
                + "  intervals:\n"
                + "  - {duration: \"00:18\", start: \"02:00\"}\n"
                + "  - {duration: \"02:00\", start: \"10:00\"}\n"
                + "  startAt: \"2015-11-25\"\n"
                + "- schedule:\n"
                + "  daysOfWeek: [ALL]\n"
                + "  endAt: \"2015-12-15\"\n"
                + "  intervals:\n"
                + "  - {duration: \"01:50\", start: \"02:10\"}\n"
                + "  - {start: \"21:00\", duration: \"02:59\"}\n"
                + "  startAt: \"2015-11-26\"\n";
    
        Set<Schedule> schedules = new YamlSchedulesReader().parse(scheduleYamlDescription);
        
        Set<Integer> daysOfweek = new TreeSet<>();
        for (int i = 1; i <= 7; i++) {
            daysOfweek.add(i);
        }
        
        List<Schedule.Interval> intervals = new ArrayList<>();
        intervals.add(new Schedule.Interval(7200000L,1080000L, daysOfweek));
        intervals.add(new Schedule.Interval(36000000L,7200000L, daysOfweek));
        Schedule schedule = new Schedule(DATE_FORMATER.parse("2015-11-25").getTime(),DATE_FORMATER.parse("2015-12-30").getTime(),intervals);
        
        assertEquals(schedules.size(), 2);
        assertTrue(schedules.remove(schedule));
    }
 
    @Test
    public void testParse_MultipleScheduleInvalidDateFormat_failure() throws ParseException {
        String scheduleYamlDescription = 
                  "- schedule:\n"
                + "  daysOfWeek: [ALL]\n"
                + "  endAt: \"2015-122-30\"\n"
                + "  intervals:\n"
                + "  - {duration: \"00:18\", start: \"02:00\"}\n"
                + "  - {duration: \"02:00\", start: \"10:00\"}\n"
                + "  startAt: \"2015-11-25\"\n"
                + "- schedule:\n"
                + "  daysOfWeek: [ALL]\n"
                + "  endAt: \"2015-12-15\"\n"
                + "  intervals:\n"
                + "  - {duration: \"01:50\", start: \"02:10\"}\n"
                + "  - {start: \"21:00\", duration: \"02:59\"}\n"
                + "  startAt: \"2015-11-26\"\n";
        
        thrown.expect(ScheduleParserException.class);
        thrown.expectMessage("Unparseable date: \"2015-122-30\"");

       new YamlSchedulesReader().parse(scheduleYamlDescription);
        
         
      }
    
    @Test
    public void testParse_MultipleScheduleInvalidDaysOfWeekFormat_failure() throws ParseException {
        String scheduleYamlDescription
                = "- schedule:\n"
                + "  daysOfWeek: [WEG]\n"
                + "  endAt: \"2015-12-30\"\n"
                + "  intervals:\n"
                + "  - {duration: \"00:18\", start: \"02:00\"}\n"
                + "  - {duration: \"02:00\", start: \"10:00\"}\n"
                + "  startAt: \"2015-11-25\"\n"
                + "- schedule:\n"
                + "  daysOfWeek: [ALL]\n"
                + "  endAt: \"2015-12-15\"\n"
                + "  intervals:\n"
                + "  - {duration: \"01:50\", start: \"02:10\"}\n"
                + "  - {start: \"21:00\", duration: \"02:59\"}\n"
                + "  startAt: \"2015-11-26\"\n";

        thrown.expect(ScheduleParserException.class);
        thrown.expectMessage("Unknown Day of Week: WEG");

         new YamlSchedulesReader().parse(scheduleYamlDescription);
    }
    
    @Test
    public void testParse_MultipleScheduleInvalidEndHoursFormat_failure() throws ParseException {
        String scheduleYamlDescription
                = "- schedule:\n"
                + "  daysOfWeek: [ALL]\n" 
                + "  endAt: \"2015-12-30\"\n"
                + "  intervals:\n"
                + "  - {duration: \"000:18\", start: \"02:00\"}\n"
                + "  - {duration: \"02:00\", start: \"10:00\"}\n"
                + "  startAt: \"2015-11-25\"\n"
                + "- schedule:\n"
                + "  daysOfWeek: [ALL]\n"
                + "  endAt: \"2015-12-15\"\n"
                + "  intervals:\n"
                + "  - {duration: \"01:50\", start: \"02:10\"}\n"
                + "  - {start: \"21:00\", duration: \"02:59\"}\n"
                + "  startAt: \"2015-11-26\"\n";

        thrown.expect(ScheduleParserException.class);
        thrown.expectMessage("Interval duration format is Invalid or Null: 000:18");

         new YamlSchedulesReader().parse(scheduleYamlDescription);
    }
    
    @Test
    public void testParse_MultipleScheduleInvalidStartHoursFormat_failure() throws ParseException {
        String scheduleYamlDescription
                = "- schedule:\n"
                + "  daysOfWeek: [ALL]\n" 
                + "  endAt: \"2015-12-30\"\n"
                + "  intervals:\n"
                + "  - {duration: \"00:18\", start: \"0A2:00\"}\n"
                + "  - {duration: \"02:00\", start: \"10:00\"}\n"
                + "  startAt: \"2015-11-25\"\n"
                + "- schedule:\n"
                + "  daysOfWeek: [ALL]\n"
                + "  endAt: \"2015-12-15\"\n"
                + "  intervals:\n"
                + "  - {duration: \"01:50\", start: \"02:10\"}\n"
                + "  - {start: \"21:00\", duration: \"02:59\"}\n"
                + "  startAt: \"2015-11-26\"\n";

        thrown.expect(ScheduleParserException.class);
        thrown.expectMessage("Interval Start Date format is Invalid or Null: 0A2:00");
        
        new YamlSchedulesReader().parse(scheduleYamlDescription);
   
    }
    
    @Test
    public void testParse_MultipleScheduleInvalidStartHoursNullFormat_failures() throws ParseException {
        String scheduleYamlDescription
                = "- schedule:\n"
                + "  intervals:\n"
                + "  - {start: \"10:00\"}\n";
                
        thrown.expect(ScheduleParserException.class);
        thrown.expectMessage("startAt can't be null");
        
         new YamlSchedulesReader().parse(scheduleYamlDescription);
   
    }
   
    @Test
    public void testParse_MultipleScheduleWithNoLimit_failures() throws ParseException {
        String scheduleYamlDescription
                = "- schedule:\n"
                + "  daysOfWeek: [ALL]\n" 
                + "  endAt: \"2015-12-30\"\n"
                + "  intervals:\n"
                + "  - {duration: \"00:18\", start: \"02:00\"}\n"
                + "  - {duration: \"02:00\", start: \"10:00\"}\n"
                + "  startAt: \"2015-11-25\"\n"
                +  "- schedule:\n"
                + "  intervals:\n"
                + "  - {start: \"10:00\"}\n"
                + "  startAt: \"2015-11-25\"\n"; 
                
        thrown.expect(ScheduleParserException.class);
        thrown.expectMessage("There is an no limit schedule defined. Only one schedule can be defined in that case");
        
         new YamlSchedulesReader().parse(scheduleYamlDescription);
   
    }

    @Test
    public void testParse_MultipleScheduleWithInvalidEndDateInInterval_failures() throws ParseException {
        String scheduleYamlDescription
                = "- schedule:\n"
                + "  daysOfWeek: [ALL]\n" 
                + "  endAt: \"2015-12-30\"\n"
                + "  intervals:\n"
                + "  - {start: \"02:00\"}\n"
                + "  - {duration: \"02:00\", start: \"10:00\"}\n"
                + "  startAt: \"2015-11-25\"\n";
                
        thrown.expect(ScheduleParserException.class);
        thrown.expectMessage("Interval duration format is Invalid or Null: null");
        
         new YamlSchedulesReader().parse(scheduleYamlDescription);
   
    }
   
    
    @Test
    public void testParse_ScheduleWithoutDaysOfWeek_failures() throws ParseException {
        String scheduleYamlDescription
                = "- schedule:\n"
                + "  endAt: \"2015-12-30\"\n"
                + "  intervals:\n"
                + "  - {duration: \"00:18\", start: \"02:00\"}\n"
                + "  - {duration: \"02:00\", start: \"10:00\"}\n"
                + "  startAt: \"2015-11-25\"\n"; 
                
        thrown.expect(ScheduleParserException.class);
        thrown.expectMessage("Invalid Days of Week, it can't be null or empty");
        
         new YamlSchedulesReader().parse(scheduleYamlDescription);
   
    }

}