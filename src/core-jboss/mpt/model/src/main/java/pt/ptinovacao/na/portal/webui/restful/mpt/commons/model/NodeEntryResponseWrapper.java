/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package pt.ptinovacao.na.portal.webui.restful.mpt.commons.model;

/**
 *
 * <AUTHOR>
 */
public class NodeEntryResponseWrapper {
 
    public enum STATUS {
        OK,
        NOT_OK,
        INVALID_NODE_ENTRY,
        NOT_MODIFIED,
        NOT_FOUND
    }
     
    private STATUS status;

    private NodeEntry node;

    public NodeEntryResponseWrapper(){
    }
    
    public NodeEntryResponseWrapper(NodeEntry node) {
        this.node = node;
    }

    public NodeEntry getNode() {
        return node;
    }

    public void setNode(NodeEntry node) {
        this.node = node;
    }
    
    public STATUS getStatus() {
        return status;
    }

    public void setStatus(STATUS status) {
        this.status = status;
    }
}
