/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package pt.ptinovacao.na.portal.webui.restful.mpt.commons.model;

import java.util.Objects;

/**
 * Represents an Endpoint Entry Object
 * <AUTHOR>
 */
public class NodeEntry {

   
    
    private String endpointName;

    private String applicationDN;

    private String password;
    
    private Long connectionTimeout;
    
    private String nodeType;

    private String domain;
    
    private String url;
  
    public NodeEntry() {
    }

    /**
     * Models the Node Entry described cataloged in the system
     * @param endpointName
     * @param applicationDN
     * @param password
     * @param nodeType
     * @param connectionTimeout
     * @param url
     * @param domain
     */
    public NodeEntry(String endpointName, String applicationDN, String password, String nodeType, Long connectionTimeout, String url, String domain) {
        this.endpointName = endpointName;
        this.applicationDN = applicationDN;
        this.password = password;
        this.nodeType = nodeType;
        this.connectionTimeout = connectionTimeout;
        this.url = url;
        this.domain = domain;
    }

    public String getEndpointName() {
        return endpointName;
    }

    public void setEndpointName(String endpointName) {
        this.endpointName = endpointName;
    }

    public String getApplicationDN() {
        return applicationDN;
    }

    public void setApplicationDN(String applicationDN) {
        this.applicationDN = applicationDN;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }

    public Long getConnectionTimeout() {
        return connectionTimeout;
    }

    public void setConnectionTimeout(Long connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
   
    @Override
    public int hashCode() {
        int hash = 7;
        hash = 37 * hash + Objects.hashCode(this.endpointName);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        
        return Objects.equals(this.endpointName, ((NodeEntry) obj).getEndpointName());
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }
}
