package pt.ptinovacao.na.portal.webui.restful.mpt.commons.entities;

import com.google.common.base.MoreObjects;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.io.Files;

import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonSerialize(include = JsonSerialize.Inclusion.NON_EMPTY)
public class Customization implements Serializable {

    private static final long serialVersionUID = 8899854250996780031L;
    private static final String ERROR_MSG_TEMPLATE = "Property %s is not valid. Format is [%s] Ex: [%s]";

    private enum VALID_TYPES {

        Family(2), Operation(2), Header(3), ResponseHeader(3);

        private final int size;

        VALID_TYPES(int size) {
            this.size = size;
        }

    }

    private Map<Integer, Object> header = new TreeMap<>();
    private Map<Integer, Object> responseHeader = new TreeMap<>();
    private Map<String, Object> family = new HashMap<>();
    private Map<String, Object> operation = new HashMap<>();
    private String xsltinString;
    private String xsltoutString;
    
    private boolean updateIfExists = false;



    public Customization(Properties properties, String xsltinString, String xsltoutString) throws IOException {
        this(properties);
        this.xsltinString = xsltinString;
        this.xsltoutString = xsltoutString;
    }
    
    public Customization(Properties properties, String xsltinString, String xsltoutString, boolean updateIfExists) throws IOException {
        this(properties, xsltinString, xsltoutString);
        this.updateIfExists = updateIfExists;
    }

    public Customization(Properties properties) throws IOException {
        Preconditions.checkNotNull(properties, "Null customization properties!");

        for (Map.Entry<Object, Object> entrySet : properties.entrySet()) {
            List<String> propsNameDef = Splitter.on(".").omitEmptyStrings().trimResults().splitToList(entrySet.getKey().toString());

            Preconditions.checkNotNull(propsNameDef, "Customization property nullability error");

            switch (VALID_TYPES.valueOf(propsNameDef.get(0))) {
                case Family:
                    preconditionCheck(propsNameDef, VALID_TYPES.Family, entrySet.getValue(), "TYPE.NAME=VALUE", "Family.maxconcurrentopers=10");
                    this.family.put(propsNameDef.get(1), entrySet.getValue());
                    break;
                case Operation:
                    preconditionCheck(propsNameDef, VALID_TYPES.Operation, entrySet.getValue(), "TYPE.NAME=VALUE", "Operation.name=pdbf_sync_provision");
                    // Evaluate the file existence and transform it as a string! This is one way of doing things...
                    if (propsNameDef.get(1).startsWith("transform")
                            && Files.isFile().apply(new File(entrySet.getValue().toString()))) {
                        this.operation.put(propsNameDef.get(1), Files.toString(new File(entrySet.getValue().toString()), Charset.defaultCharset()));
                    } else {
                        this.operation.put(propsNameDef.get(1), entrySet.getValue());
                    }
                    break;
                case Header:
                    preconditionCheck(propsNameDef, VALID_TYPES.Header, entrySet.getValue(), "TYPE.NAME.INDEX=VALUE;VALUE", "Header.position.0=OPERATION;SEARCHABLE");
                    this.header.put(Integer.valueOf(propsNameDef.get(2)), entrySet.getValue());
                    break;
                case ResponseHeader:
                    preconditionCheck(propsNameDef, VALID_TYPES.ResponseHeader, entrySet.getValue(), "TYPE.NAME.INDEX=VALUE", "ResponseHeader.position.0=OPERATION");
                    this.responseHeader.put(Integer.valueOf(propsNameDef.get(2)), entrySet.getValue());
                    break;
                default:
                    throw new NoSuchFieldError("Unknown property while parsing properties");
            }
        }
    }

    /**
     * Auxiliary method to help the input properties validation.
     *
     * @param propsNameDef previous parsed properties
     * @param type type of evaluation to be made
     * @param value property value to check for
     * @param strFormatArgs custom messages to be thrown if an error is found
     */
    private void preconditionCheck(List<String> propsNameDef, VALID_TYPES type, Object value, String... strFormatArgs) {

        Preconditions.checkArgument(propsNameDef.size() == type.size,
                String.format(ERROR_MSG_TEMPLATE, "key", strFormatArgs[0],
                        strFormatArgs[1] + " size of type " + type.size + " sizeofList" + propsNameDef.size() + " " + propsNameDef.toString()));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(propsNameDef.get(1)), String.format(ERROR_MSG_TEMPLATE, "key", strFormatArgs[0], strFormatArgs[1]));

        if (type.equals(VALID_TYPES.Header)) {
            Preconditions.checkArgument(!Strings.isNullOrEmpty(propsNameDef.get(2)), String.format(ERROR_MSG_TEMPLATE, "key", strFormatArgs[0], strFormatArgs[1]));
        }

        Preconditions.checkArgument(!Strings.isNullOrEmpty(value.toString()), String.format(ERROR_MSG_TEMPLATE, "value", strFormatArgs[0], strFormatArgs[1]));
    }

    

    public Map<Integer, Object> getHeader() {
        return header;
    }
    
    public Map<String, Object> getFamily() {
        return family;
    }
    
    public Map<String, Object> getOperation() {
        return operation;
    }
    
    public Map<Integer, Object> getResponseHeader() {
        return responseHeader;
    }
    
    public String getXsltinString() {
        return xsltinString;
    }
    
    public String getXsltoutString() {
        return xsltoutString;
    }
    
    public void setXsltinString(String xsltinString) {
        this.xsltinString = xsltinString;
    }
    
    public void setXsltoutString(String xsltoutString) {
        this.xsltoutString = xsltoutString;
    }

    /*
     * this setters (setHeader,setFamily,setOperation) are used only for json
     * serialization purpose, they must be populated only with constructor, so
     * be careful if use them at your own.
     */
    
    public Customization() {
    }

    public void setHeader(Map<Integer, Object> header) {
        this.header = header;
    }

    public void setFamily(Map<String, Object> family) {
        this.family = family;
    }

    public void setOperation(Map<String, Object> operation) {
        this.operation = operation;
    }
    
    public void setResponseHeader(Map<Integer, Object> responseHeader) {
        this.responseHeader = responseHeader;
    }
    
    public boolean isUpdateIfExists() {
        return updateIfExists;
    }
    
    public void setUpdateIfExists(boolean updateIfExists) {
        this.updateIfExists = updateIfExists;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .omitNullValues()
                .add("header", this.header)
                .add("responseHeader", this.responseHeader)
                .add("family", this.family)
                .add("operation", this.operation)
                .add("xsltinString", this.xsltinString)
                .add("xsltoutString", this.xsltoutString)
                .add("updateIfExists", this.updateIfExists)
                .toString();
    }
}
