package pt.ptinovacao.na.portal.webui.restful.mpt.commons.entities;

import com.google.common.base.MoreObjects;
import org.jboss.resteasy.annotations.providers.multipart.PartType;
import pt.ptinovacao.na.portal.webui.restful.commons.JsonUtils;

import javax.ws.rs.FormParam;
import javax.ws.rs.core.MediaType;

/**
 * <AUTHOR> <<EMAIL>>
 */
public class StringFileInputForm extends FileInputForm {

    /**
     * Holds request specification
     */
    @FormParam("request")
    @PartType(MediaType.TEXT_PLAIN)
    private String request;

    public void setRequest(String request) {
        this.request = request;
    }

    public String getRequest() {
        return request;
    }

    public RequestFileInputForm convertToRequest() {
        Request req = JsonUtils.jsonToJava(Request.class, request);
        if (req != null) {
            RequestFileInputForm converted = new RequestFileInputForm();
            converted.setFilename(getFilename());
            converted.setInputStream(getInputStream());
            converted.setRequest(req);
            return converted;
        }
        return null;
    }
}
