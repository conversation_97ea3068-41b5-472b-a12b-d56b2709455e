package pt.ptinovacao.na.portal.webui.restful.mpt.commons.model;

import java.util.List;
import java.util.Objects;

import pt.ptinovacao.na.portal.webui.restful.commons.model.ExecutionStates;
import pt.ptinovacao.na.portal.webui.restful.commons.model.OrderStatus;


/**
 * POJO that represents the Details of an given Order 
 **/
public class OrderDetailsEntry extends OrderEntry {
    private String filename;
    private String family;
    private Long familyId;
    private String operation;
    private Long operationId;
    private String username;
    private String statusMessage;
    
    public OrderDetailsEntry() {
    }
    
    public OrderDetailsEntry(String id, Long bulkId, Long startTime, Long endTime, String opk, String keyRule, Integer lineNum,
            OrderStatus status, ExecutionStates executionState, List<ColumnEntry> inputLine, List<ColumnEntry> outputLine,
            String filename, String family, Long familyId, String operation, Long operationId, String username, String statusMessage) {
        super(id, bulkId, startTime, endTime, opk, keyRule, lineNum, status, executionState, inputLine, outputLine);
        this.filename = filename;
        this.family = family;
        this.operation = operation;
        this.username = username;
        this.statusMessage = statusMessage;
        this.operationId = operationId;
        this.familyId = familyId;
    }
    
    public Long getFamilyId() {
        return familyId;
    }
    
    public void setFamilyId(Long familyId) {
        this.familyId = familyId;
    }
    
    public Long getOperationId() {
        return operationId;
    }
    
    public void setOperationId(Long operationId) {
        this.operationId = operationId;
    }

    public void setStatusMessage(String statusMessage) {
        this.statusMessage = statusMessage;
    }
    
    public String getStatusMessage() {
        return statusMessage;
    }

    public String getFilename() {
        return filename;
    }
    
    public void setFilename(String filename) {
        this.filename = filename;
    }
    
    public String getFamily() {
        return family;
    }
    
    public void setFamily(String family) {
        this.family = family;
    }
    
    public String getOperation() {
        return operation;
    }
    
    public void setOperation(String operation) {
        this.operation = operation;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 41 * hash + Objects.hashCode(this.filename);
        hash = 41 * hash + Objects.hashCode(this.family);
        hash = 41 * hash + Objects.hashCode(this.familyId);
        hash = 41 * hash + Objects.hashCode(this.operation);
        hash = 41 * hash + Objects.hashCode(this.operationId);
        hash = 41 * hash + Objects.hashCode(this.username);
        hash = 41 * hash + Objects.hashCode(this.statusMessage);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final OrderDetailsEntry other = (OrderDetailsEntry) obj;
        if (!Objects.equals(this.filename, other.getFilename())) {
            return false;
        }
        if (!Objects.equals(this.family, other.getFamily())) {
            return false;
        }
        if (!Objects.equals(this.familyId, other.getFamilyId())) {
            return false;
        }
        if (!Objects.equals(this.operation, other.getOperation())) {
            return false;
        }
        if (!Objects.equals(this.operationId, other.getOperationId())) {
            return false;
        }
        if (!Objects.equals(this.username, other.getUsername())) {
            return false;
        }
        return Objects.equals(this.statusMessage, other.getStatusMessage());
    }

    @Override
    public String toString() {
        return "OrderDetailsEntry{" + "filename=" + filename + ", family=" + family + ", familyId=" + familyId + ", operation=" + operation + ", operationId=" + operationId + ", username=" + username + ", statusMessage=" + statusMessage + '}';
    }
}
