/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package pt.ptinovacao.na.portal.webui.restful.mpt.commons.schedules;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import org.junit.Test;
import static org.junit.Assert.*;
import org.junit.Rule;
import org.junit.rules.ExpectedException;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.entities.Schedule;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.entities.Schedule.Interval;

/**
 *
 * <AUTHOR>
 */
@SuppressWarnings("unchecked")
public class ScheduleEventsParserTest {

    private static final SimpleDateFormat DATE_FORMATER = new SimpleDateFormat("yyyy-MM-dd");
    private static final SimpleDateFormat DATE_FORMATER_HH_MM = new SimpleDateFormat("yyyy-MM-dd HH:mm");

    public ScheduleEventsParserTest() {
    }

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @Test
    public void testValidateAndGetTransitions_MultipleSchedule_success() throws ParseException {
        Set<Schedule> sc = new HashSet<>();
        List<Interval> intervals = new ArrayList<>();

        Set<Integer> weekDays = new HashSet<>(Arrays.asList(Calendar.SATURDAY, Calendar.SUNDAY, Calendar.MONDAY));
        intervals.add(new Interval(36000000L, 7200000L, weekDays));
        intervals.add(new Interval(46800000L, 14400000L, weekDays));
        Schedule s = new Schedule(DATE_FORMATER.parse("2015-12-25").getTime(), DATE_FORMATER.parse("2015-12-31").getTime(), intervals);
        sc.add(s);

        ScheduleEventsParser sep = new ScheduleEventsParser(sc);
        assertTrue(sep.validate().isValid());

        List<ScheduleEvent> expectedScheduleEvents = new ArrayList<>();
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-26 10:00"), ScheduleEvent.ACTION.START));
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-26 12:00"), ScheduleEvent.ACTION.STOP));
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-26 13:00"), ScheduleEvent.ACTION.START));
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-26 17:00"), ScheduleEvent.ACTION.STOP));
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-27 10:00"), ScheduleEvent.ACTION.START));
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-27 12:00"), ScheduleEvent.ACTION.STOP));
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-27 13:00"), ScheduleEvent.ACTION.START));
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-27 17:00"), ScheduleEvent.ACTION.STOP));
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-28 10:00"), ScheduleEvent.ACTION.START));
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-28 12:00"), ScheduleEvent.ACTION.STOP));
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-28 13:00"), ScheduleEvent.ACTION.START));
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-28 17:00"), ScheduleEvent.ACTION.STOP));

        List<ScheduleEvent> scheduleEvents = new ArrayList<>(sep.getTransitionsEvents());
        assertEquals(scheduleEvents, expectedScheduleEvents);
    }

    @Test
    public void testValidateAndGetTransitions_MultipleSchedule_SingleDay_success() throws ParseException {
        Set<Schedule> sc = new HashSet<>();
        List<Interval> intervals = new ArrayList<>();

        Set<Integer> weekDays = new HashSet<>(Collections.singletonList(Calendar.SUNDAY));
        intervals.add(new Interval(36000000L /* 10:00 */, 7200000L /* 2 hours */, weekDays));
        intervals.add(new Interval(46800000L /* 13:00 */, 14400000L /* 4 hours */, weekDays));
        Schedule s = new Schedule(
                DATE_FORMATER_HH_MM.parse( "2015-12-25 00:00" ).getTime(),
                DATE_FORMATER_HH_MM.parse( "2015-12-27 23:59" ).getTime(),
                intervals);
        sc.add(s);

        ScheduleEventsParser sep = new ScheduleEventsParser(sc);
        assertTrue(sep.validate().isValid());

        List<ScheduleEvent> expectedScheduleEvents = new ArrayList<>();
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-27 10:00"), ScheduleEvent.ACTION.START));
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-27 12:00"), ScheduleEvent.ACTION.STOP));
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-27 13:00"), ScheduleEvent.ACTION.START));
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-27 17:00"), ScheduleEvent.ACTION.STOP));
        List<ScheduleEvent> scheduleEvents = new ArrayList<>(sep.getTransitionsEvents());
        assertEquals(scheduleEvents, expectedScheduleEvents);
    }

    @Test
    public void testValidateAndGetTransitions_MultipleSchedule_SingleDay_DurationShiftsDay_success() throws ParseException {
        Set<Schedule> sc = new HashSet<>();
        List<Interval> intervals = new ArrayList<>();

        Set<Integer> weekDays = new HashSet<>(Collections.singletonList(Calendar.MONDAY));
        intervals.add(new Interval(82800000L /* 23:00 */, 3600000L /* 1 hour */, weekDays));
        Schedule s = new Schedule(
                DATE_FORMATER_HH_MM.parse( "2019-08-25 23:00" ).getTime(),
                DATE_FORMATER_HH_MM.parse( "2019-08-29 22:59" ).getTime(),
                intervals
        );
        sc.add(s);

        ScheduleEventsParser sep = new ScheduleEventsParser(sc);
        assertTrue(sep.validate().isValid());

        List<ScheduleEvent> expectedScheduleEvents = new ArrayList<>();
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2019-08-26 23:00"), ScheduleEvent.ACTION.START));
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2019-08-27 00:00"), ScheduleEvent.ACTION.STOP));
        List<ScheduleEvent> scheduleEvents = new ArrayList<>(sep.getTransitionsEvents());
        assertEquals(expectedScheduleEvents, scheduleEvents);
    }

    @Test
    public void testValidate_NoAvailablePeriods_failure() throws ParseException {
        Set<Schedule> sc = new HashSet<>();
        List<Interval> intervals = new ArrayList<>();

        Set<Integer> weekDays = new HashSet<>(Collections.singletonList(Calendar.THURSDAY));
        // 36000000L = 10:00  --- 7200000L = 12:00
        intervals.add(new Interval(36000000L, 7200000L, weekDays));
        // 46800000L = 13:00 --- 14400000L = 17:00
        intervals.add(new Interval(46800000L, 14400000L, weekDays));
        Schedule s = new Schedule(DATE_FORMATER.parse("2015-12-25").getTime(), DATE_FORMATER.parse("2015-12-27").getTime(), intervals);
        sc.add(s);

        ScheduleEventsParser.ValidationResult vr = new ScheduleEventsParser(sc).validate();
        assertFalse(vr.isValid());
        assertEquals(vr.getDescription(), ScheduleEventsParser.ValidationResult.Description.NO_PERIODS);
    }

    @Test
    public void testValidate_OverlappedPeriods_failure() throws ParseException {
        Set<Schedule> sc = new HashSet<>();

        List<Interval> intervals = new ArrayList<>();
        Set<Integer> weekDays = new HashSet<>(Arrays.asList(Calendar.MONDAY, Calendar.THURSDAY));
        intervals.add(new Interval(36000000L, 7200000L, weekDays));
        intervals.add(new Interval(46800000L, 14400000L, weekDays));
        Schedule s = new Schedule(DATE_FORMATER.parse("2015-12-25").getTime(), DATE_FORMATER.parse("2016-01-25").getTime(), intervals);
        sc.add(s);

        Set<Integer> weekDays2 = new HashSet<>(Arrays.asList(Calendar.MONDAY, Calendar.THURSDAY));
        intervals.add(new Interval(36000000L, 7200000L, weekDays2));
        Schedule s2 = new Schedule(DATE_FORMATER.parse("2015-12-20").getTime(), DATE_FORMATER.parse("2016-01-01").getTime(), intervals);
        sc.add(s2);

        ScheduleEventsParser.ValidationResult vr = new ScheduleEventsParser(sc).validate();
        assertFalse(vr.isValid());
        assertEquals(vr.getDescription(), ScheduleEventsParser.ValidationResult.Description.OVERLAPS);
    }

    @Test
    public void testValidate_OverlappedStartEnd_differentHoursOfDay_success() throws ParseException {
        Set<Schedule> sc = new HashSet<>();

        List<Interval> intervals = new ArrayList<>();
        Set<Integer> weekDays = new HashSet<>(Arrays.asList(Calendar.MONDAY, Calendar.THURSDAY));
        intervals.add(new Interval(36000000L, 7200000L, weekDays));
        intervals.add(new Interval(46800000L, 14400000L, weekDays));
        Schedule s = new Schedule(DATE_FORMATER.parse("2015-12-25").getTime(), DATE_FORMATER.parse("2016-01-25").getTime(), intervals);
        sc.add(s);

        List<Interval> intervals2 = new ArrayList<>();
        Set<Integer> weekDays2 = new HashSet<>(Arrays.asList(Calendar.MONDAY, Calendar.THURSDAY));
        intervals2.add(new Interval(72000000L, 3600000L, weekDays2));
        Schedule s2 = new Schedule(DATE_FORMATER.parse("2015-12-20").getTime(), DATE_FORMATER.parse("2016-01-01").getTime(), intervals2);
        sc.add(s2);

        List<Interval> intervals3 = new ArrayList<>();
        Set<Integer> weekDays3 = new HashSet<>(Arrays.asList(Calendar.TUESDAY, Calendar.WEDNESDAY));
        intervals3.add(new Interval(72000000L, 3600000L, weekDays3));
        Schedule s3 = new Schedule(DATE_FORMATER.parse("2015-12-20").getTime(), DATE_FORMATER.parse("2016-01-01").getTime(), intervals3);
        sc.add(s3);

        ScheduleEventsParser.ValidationResult vr = new ScheduleEventsParser(sc).validate();
        assertTrue(vr.isValid());
    }

    // Invalid test. For a valid test, we need to change the value of schedulerNormalizerDelta (value in standalone.xml) to 5.
    // With changes made in NAPORTAL-11107, this is not valid.
    public void testValidateAndGetTransitions_MultipleSchedule_JoinPeriods_success() throws ParseException {
        Set<Schedule> sc = new HashSet<>();
        List<Interval> intervals = new ArrayList<>();

        Set<Integer> weekDays = new HashSet<>(Arrays.asList(Calendar.SATURDAY, Calendar.SUNDAY, Calendar.MONDAY));
        intervals.add(new Interval(36000000L, 7200000L, weekDays));
        intervals.add(new Interval(46800000L, 14400000L, weekDays));
        Schedule s = new Schedule(DATE_FORMATER.parse("2015-12-25").getTime(), DATE_FORMATER.parse("2015-12-31").getTime(), intervals);
        sc.add(s);

        List<Interval> intervals2 = new ArrayList<>();
        Set<Integer> weekDays2 = new HashSet<>(Arrays.asList(Calendar.SATURDAY, Calendar.SUNDAY, Calendar.MONDAY));
        intervals2.add(new Interval(43500000L, 3000000L, weekDays2));
        Schedule s2 = new Schedule(DATE_FORMATER.parse("2015-12-27").getTime(), DATE_FORMATER.parse("2015-12-28").getTime(), intervals2);
        sc.add(s2);

        ScheduleEventsParser sep = new ScheduleEventsParser(sc);
        assertTrue(sep.validate().isValid());

        List<ScheduleEvent> expectedScheduleEvents = new ArrayList<>();
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-26 10:00"), ScheduleEvent.ACTION.START));
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-26 12:00"), ScheduleEvent.ACTION.STOP));
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-26 13:00"), ScheduleEvent.ACTION.START));
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-26 17:00"), ScheduleEvent.ACTION.STOP));
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-27 10:00"), ScheduleEvent.ACTION.START));
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-27 17:00"), ScheduleEvent.ACTION.STOP));
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-28 10:00"), ScheduleEvent.ACTION.START));
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-28 17:00"), ScheduleEvent.ACTION.STOP));

        List<ScheduleEvent> scheduleEvents = new ArrayList<>(sep.getTransitionsEvents());
        assertEquals(scheduleEvents, expectedScheduleEvents);
    }


    @Test
    public void testValidateAndGetTransitions_SingleScheduleNoLimit_success() throws ParseException {
        Set<Schedule> sc = new HashSet<>();
        List<Interval> intervals = new ArrayList<>();
        intervals.add(new Interval(36000000L, null, null));
        Schedule s = new Schedule(DATE_FORMATER.parse("2015-12-25").getTime(), null, intervals);
        sc.add(s);

        ScheduleEventsParser sep = new ScheduleEventsParser(sc);
        assertTrue(sep.validate().isValid());

        List<ScheduleEvent> expectedScheduleEvents = new ArrayList<>();
        expectedScheduleEvents.add(new ScheduleEvent(DATE_FORMATER_HH_MM.parse("2015-12-25 10:00"), ScheduleEvent.ACTION.START));

        List<ScheduleEvent> scheduleEvents = new ArrayList<>(sep.getTransitionsEvents());
        assertEquals(scheduleEvents, expectedScheduleEvents);
    }


    @Test
    public void testValidate_SingleScheduleNoLimit_failure() throws ParseException {
        Set<Schedule> sc = new HashSet<>();
        List<Interval> intervals = new ArrayList<>();
        intervals.add(new Interval(36000000L, null, null));
        intervals.add(new Interval(39600000L, null, null));
        Schedule s = new Schedule(DATE_FORMATER.parse("2015-12-25").getTime(), null, intervals);
        sc.add(s);

        ScheduleEventsParser sep = new ScheduleEventsParser(sc);
        assertFalse(sep.validate().isValid());

        sc.clear();
        intervals.clear();
        intervals.add(new Interval(36000000L, null, null));
        sc.add(s);
        sc.add(new Schedule(DATE_FORMATER.parse("2015-12-26").getTime(), null, intervals));
        sep = new ScheduleEventsParser(sc);
        assertFalse(sep.validate().isValid());

    }

}
