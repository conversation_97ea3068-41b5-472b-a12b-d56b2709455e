package pt.ptinovacao.na.portal.core.mpt.resourcemanager.wildfly;

import java.io.IOException;

import java.security.AccessController;
import java.security.PrivilegedAction;

import java.util.LinkedList;
import java.util.List;

import org.jboss.dmr.ModelNode;

import org.jboss.as.controller.client.ModelControllerClient;

import static org.jboss.as.controller.descriptions.ModelDescriptionConstants.ADD;
import static org.jboss.as.controller.descriptions.ModelDescriptionConstants.NAME;
import static org.jboss.as.controller.descriptions.ModelDescriptionConstants.OUTCOME;
import static org.jboss.as.controller.descriptions.ModelDescriptionConstants.READ_ATTRIBUTE_OPERATION;
import static org.jboss.as.controller.descriptions.ModelDescriptionConstants.REMOVE;
import static org.jboss.as.controller.descriptions.ModelDescriptionConstants.SUCCESS;
import static org.jboss.as.controller.descriptions.ModelDescriptionConstants.VALUE;
import static org.jboss.as.controller.descriptions.ModelDescriptionConstants.WRITE_ATTRIBUTE_OPERATION;

import pt.ptinovacao.na.portal.core.mpt.resourcemanager._private.ResourceManagerLogger;
import pt.ptinovacao.na.portal.core.mpt.resourcemanager._private.ResourceManagerMessages;

/**
 *
 * <AUTHOR> Karamanos <<EMAIL>>
 */
public final class WildflyModelController {

    private WildflyModelController() {
    }

    public static String getSystemProperty(final String key) {
        if (System.getSecurityManager() == null) {
            return System.getProperty(key);
        }
        return AccessController.doPrivileged(new PrivilegedAction<String>() {
            @Override
            public String run() {
                return System.getProperty(key);
            }
        });
    }

    public static void setProperty(String name, String value, ModelControllerClient client) {
        ModelNode modelNode = createOpNode("system-property=" + name, ADD);

        modelNode.get(VALUE).set(value);
        ModelNode result = executeOp(modelNode, client);

        ResourceManagerLogger.LOGGER.setPropertyWildflyModelControllerClient(name, result);
    }

    public static void removeProperty(String name, ModelControllerClient client) {
        ModelNode modelNode = createOpNode("system-property=" + name, REMOVE);
        ModelNode result = executeOp(modelNode, client);

        ResourceManagerLogger.LOGGER.removePropertyWildflyModelControllerClient(name, result);
    }

    public static void redefineProperty(String name, String value, ModelControllerClient client) {
        ModelNode modelNode = createOpNode("system-property=" + name, WRITE_ATTRIBUTE_OPERATION);
        modelNode.get(NAME).set(VALUE);
        modelNode.get(VALUE).set(value);
        ModelNode result = executeOp(modelNode, client);

        ResourceManagerLogger.LOGGER.redefinePropertyWildflyModelControllerClient(name, result);
    }

    public static ModelNode executeOp(ModelNode op, ModelControllerClient client) {
        ModelNode modelNodeResult;
        try {
            modelNodeResult = client.execute(op);
        } catch (IOException ioe) {
            throw ResourceManagerMessages.MESSAGES.errorExecutingWildflyModelControllerClient(ioe, op.toJSONString(false));
        }

        if (!SUCCESS.equals(modelNodeResult.get(OUTCOME).asString())) {
            throw ResourceManagerMessages.MESSAGES.wildflyModelControllerClientCmdFailure(op, modelNodeResult);
        }

        return modelNodeResult;
    }

    public static String getProperty(String name, ModelControllerClient client) {
        ModelNode modelNode = createOpNode("system-property=" + name, READ_ATTRIBUTE_OPERATION);
        modelNode.get(NAME).set(VALUE);
        ModelNode result = executeOp(modelNode, client);
        ModelNode resolvedResult = result.resolve();

        ResourceManagerLogger.LOGGER.getPropertyWildflyModelControllerClient(name, resolvedResult);

        return resolvedResult.get("result").asString();
    }

    public static List<String> modelNodeAsStingList(ModelNode node) {
        List<String> ret = new LinkedList<>();
        for (ModelNode n : node.asList()) {
            ret.add(n.asString());
        }
        return ret;
    }

    public static ModelNode createCompositeNode(ModelNode... steps) {
        ModelNode comp = new ModelNode();
        comp.get("operation").set("composite");
        for (ModelNode step : steps) {
            comp.get("steps").add(step);
        }
        return comp;
    }

    public static ModelNode createOpNode(String address, String operation) {
        ModelNode op = new ModelNode();

        // set address
        ModelNode list = op.get("address").setEmptyList();
        if (address != null) {
            String[] pathSegments = address.split("/");
            for (String segment : pathSegments) {
                String[] elements = segment.split("=");
                list.add(elements[0], elements[1]);
            }
        }
        op.get("operation").set(operation);
        return op;
    }

    public static List<String> modelNodeAsStringList(ModelNode node) {
        List<String> ret = new LinkedList<>();
        for (ModelNode n : node.asList()) {
            ret.add(n.asString());
        }
        return ret;
    }

    private static boolean getOperationResult(ModelNode node) {
        boolean success = "success".equalsIgnoreCase(node.get("outcome").asString());
        if (!success) {
            ResourceManagerLogger.LOGGER.getResultFailWildflyModelControllerClient(node.toJSONString(false));
        }

        return success;
    }

    public static boolean execute(ModelControllerClient client, ModelNode operation) {
        try {
            ModelNode result = client.execute(operation);
            return getOperationResult(result);
        } catch (IOException e) {
            ResourceManagerLogger.LOGGER.executeFailWildflyModelControllerClient(e, operation.toJSONString(false));
            return false;
        }
    }
}
