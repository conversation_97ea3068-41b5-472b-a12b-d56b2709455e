package pt.ptinovacao.na.portal.core.mpt.resourcemanager;

import com.google.common.base.MoreObjects;

import java.io.Serializable;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Class to hold a replicated cache entry containing all the relevant contention information around
 * the batch engine.
 *
 * <AUTHOR> <<EMAIL>>
 */
public class BatchCacheEntry implements Serializable {

    private static final long serialVersionUID = 4404041529840260188L;

    private final AtomicInteger actualBatchFileThreads = new AtomicInteger(0);
    private final AtomicInteger actualBatchGlobalThreads = new AtomicInteger(0);

    public int incActualBatchFileThreads() {
        return this.actualBatchFileThreads.incrementAndGet();
    }

    public int incActualBatchGlobalThreads() {
        return this.actualBatchGlobalThreads.incrementAndGet();
    }

    public int decActualBatchFileThreads() {
        return this.actualBatchFileThreads.decrementAndGet();
    }

    public int decActualBatchGlobalThreads() {
        return this.actualBatchGlobalThreads.decrementAndGet();
    }

    public int getActualBatchFileThreads() {
        return this.actualBatchFileThreads.get();
    }

    public int getActualBatchGlobalThreads() {
        return this.actualBatchGlobalThreads.get();
    }

    @Override
    public String toString() {
        return MoreObjects
                .toStringHelper(this)
                .omitNullValues()
                .add("ActualBatchFileThreads", this.actualBatchFileThreads)
                .add("actualBatchGlobalThreads", this.actualBatchGlobalThreads)
                .toString();
    }
}
