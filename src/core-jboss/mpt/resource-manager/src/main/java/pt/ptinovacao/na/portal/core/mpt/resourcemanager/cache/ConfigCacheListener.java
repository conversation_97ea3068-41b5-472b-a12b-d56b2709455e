package pt.ptinovacao.na.portal.core.mpt.resourcemanager.cache;

import com.google.common.base.Strings;

import javax.inject.Inject;

import org.hibernate.ogm.cfg.OgmProperties;
import org.hibernate.ogm.datastore.mongodb.MongoDBProperties;

import org.infinispan.notifications.Listener;
import org.infinispan.notifications.cachelistener.annotation.CacheEntryCreated;
import org.infinispan.notifications.cachelistener.annotation.CacheEntryModified;
import org.infinispan.notifications.cachelistener.annotation.CacheEntryRemoved;
import org.infinispan.notifications.cachelistener.event.CacheEntryCreatedEvent;
import org.infinispan.notifications.cachelistener.event.CacheEntryModifiedEvent;
import org.infinispan.notifications.cachelistener.event.CacheEntryRemovedEvent;

import org.jboss.as.controller.client.ModelControllerClient;

import pt.ptinovacao.na.portal.core.mpt.resourcemanager.BatchResources;
import pt.ptinovacao.na.portal.core.mpt.resourcemanager._private.ResourceManagerLogger;
import pt.ptinovacao.na.portal.core.mpt.resourcemanager.wildfly.WildflyModelController;

/**
 * No long running task are needed to be performed, so it's recommended to use se same thread.
 * <p>
 * Only the node who made the modification should trigger the event! Cache-related events are
 * triggered twicw by Infinispan, once before and once after the event happens. We use the isPre
 * method on the event instance to detect which of the two events has taken place.
 *
 * <AUTHOR> Karamanos <<EMAIL>>
 */
@Listener(primaryOnly = true, sync = false)
public class ConfigCacheListener {

    @Inject
    private ModelControllerClient controllerClient;

    @CacheEntryCreated
    public void observeAdd(CacheEntryCreatedEvent<String, String> event) {
        if (event.isPre() || event.getKey().equals(BatchResources.BATCH_RESOURCES_CACHE_KEY)) {
            return;
        }

        this.modelControllerDispatcher(event.getKey(), event.getValue());
        ResourceManagerLogger.LOGGER.configCacheListenerArrival(event.getCache().getName(), event.getType(), event.getKey(), event.getValue());
    }

    @CacheEntryModified
    public void observeUpdate(CacheEntryModifiedEvent<String, String> event) {
        if (event.isPre() || event.getKey().equals(BatchResources.BATCH_RESOURCES_CACHE_KEY)) {
            return;
        }
        this.modelControllerDispatcher(event.getKey(), event.getValue());
        ResourceManagerLogger.LOGGER.configCacheListenerArrival(event.getCache().getName(), event.getType(), event.getKey(), event.getValue());
    }

    @CacheEntryRemoved
    public void observeRemove(CacheEntryRemovedEvent<String, String> event) {
        if (event.isPre() || event.getKey().equals(BatchResources.BATCH_RESOURCES_CACHE_KEY)) {
            return;
        }
        WildflyModelController.removeProperty(event.getKey(), this.controllerClient);
        ResourceManagerLogger.LOGGER.configCacheListenerArrival(event.getCache().getName(), event.getType(), event.getKey(), event.getValue());
    }

    private void modelControllerDispatcher(String key, String value) {
        String localKey = key;

        if (!Strings.isNullOrEmpty(localKey) && !Strings.isNullOrEmpty(value)) {
            // hibernate ogm properties are treated differently
            if (localKey.startsWith("ogm")) {
                localKey = ogmPropertyKeyTransformer(key);
            }

            if (!Strings.isNullOrEmpty(WildflyModelController.getSystemProperty(localKey))) {
                if (!WildflyModelController.getSystemProperty(localKey).equals(value)) {
                    WildflyModelController.redefineProperty(localKey, value, this.controllerClient);
                } else {
                    ResourceManagerLogger.LOGGER.ignoringSysPropToWildflyModelControllerClient(key, value);
                }
            } else {
                ResourceManagerLogger.LOGGER.sendingSysPropToWildflyModelControllerClient(key, value);
                WildflyModelController.setProperty(localKey, value, this.controllerClient);
            }
        }
    }

    private String ogmPropertyKeyTransformer(String key) {
        switch (key) {
            case "ogmHost":
                return OgmProperties.HOST;
            case "ogmProvider":
                return OgmProperties.DATASTORE_PROVIDER;
            case "ogmDatabase":
                return OgmProperties.DATABASE;
            case "ogmCreateDatabase":
                return OgmProperties.CREATE_DATABASE;
            case "ogmConnectionTimeout":
                return MongoDBProperties.MONGO_DRIVER_SETTINGS_PREFIX + ".connectTimeout";
            default:
                return "";
        }
    }
}
