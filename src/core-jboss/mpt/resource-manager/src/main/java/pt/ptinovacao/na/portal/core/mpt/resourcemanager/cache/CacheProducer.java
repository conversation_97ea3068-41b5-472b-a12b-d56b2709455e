package pt.ptinovacao.na.portal.core.mpt.resourcemanager.cache;

import javax.annotation.ManagedBean;
import javax.annotation.Resource;

import javax.enterprise.context.ApplicationScoped;
import javax.enterprise.inject.Produces;

import org.infinispan.Cache;
import org.infinispan.manager.EmbeddedCacheManager;

import pt.ptinovacao.na.portal.core.mpt.resourcemanager.FamilyResources;
import pt.ptinovacao.na.portal.core.mpt.resourcemanager._private.ResourceManagerLogger;
import pt.ptinovacao.na.portal.core.mpt.resourcemanager.cache.qualifiers.OrderKeySafeTimeCache;
import pt.ptinovacao.na.portal.core.mpt.resourcemanager.cache.qualifiers.RequestCache;
import pt.ptinovacao.na.portal.core.mpt.resourcemanager.cache.qualifiers.ConfigCache;
import pt.ptinovacao.na.portal.core.mpt.resourcemanager.cache.qualifiers.FamilyResourcesCache;
import pt.ptinovacao.na.portal.core.mpt.resourcemanager.cache.qualifiers.SchedulerCache;

import pt.ptinovacao.na.portal.webui.restful.mpt.commons.schedules.ScheduleEventList;

/**
 * {@link CacheContainerProvider}'s implementation creating a DefaultCacheManager which is
 * configured programmatically. Infinispan's libraries need to be bundled with the application -
 * this is called "library" mode.
 *
 * <AUTHOR> Karamanos <<EMAIL>>
 */
@ApplicationScoped
@ManagedBean
public class CacheProducer {

    @Resource(lookup = "java:jboss/infinispan/container/mpt")
    @Produces
    private EmbeddedCacheManager mptCacheManager;

    @Resource(lookup = "java:jboss/infinispan/container/na")
    @Produces
    private EmbeddedCacheManager naCacheManager;

    @Produces
    @RequestCache // This is the cache qualifier.
    @ApplicationScoped
    public Cache<Long, Object> produceRequestCache() {
        Cache<Long, Object> cache = this.mptCacheManager.getCache(RequestCache.REQUEST_CACHE_NAME);

        if (cache.getStatus().isTerminated()) {
            cache.start();
        }

        ResourceManagerLogger.LOGGER.cacheProducerCache(cache.getName());

        return cache;
    }

    @Produces
    @ConfigCache // This is the cache qualifier.
    @ApplicationScoped
    public Cache<String, Object> produceConfigCache() {
        Cache<String, Object> cache = this.mptCacheManager.getCache(ConfigCache.CONFIG_CACHE_NAME);

        if (cache.getStatus().isTerminated()) {
            cache.start();
        }

        ResourceManagerLogger.LOGGER.cacheProducerCache(cache.getName());

        return cache;
    }

    @Produces
    @OrderKeySafeTimeCache // This is the cache qualifier.
    @ApplicationScoped
    public Cache<String, String> produceOrderKeySafeTimeCache() {
        Cache<String, String> cache = this.naCacheManager.getCache(OrderKeySafeTimeCache.ORDER_KEY_SAFETIME_CACHE_NAME);

        if (cache.getStatus().isTerminated()) {
            cache.start();
        }

        ResourceManagerLogger.LOGGER.cacheProducerCache(cache.getName());

        return cache;
    }

    @Produces
    @FamilyResourcesCache // This is the cache qualifier.
    @ApplicationScoped
    public Cache<String, FamilyResources> produceFamilyResourcesCache() {
        Cache<String, FamilyResources> cache = this.mptCacheManager.getCache(FamilyResourcesCache.FAMILY_RESOURCES_CACHE_NAME);

        if (cache.getStatus().isTerminated()) {
            cache.start();
        }

        ResourceManagerLogger.LOGGER.cacheProducerCache(cache.getName());

        return cache;
    }

    @Produces
    @SchedulerCache // This is the cache qualifier.
    @ApplicationScoped
    public Cache<Long, ScheduleEventList> produceSchedulerCache() {
        Cache<Long, ScheduleEventList> cache = this.mptCacheManager.getCache(SchedulerCache.SCHEDULER_CACHE_NAME);

        if (cache.getStatus().isTerminated()) {
            cache.start();
        }

        ResourceManagerLogger.LOGGER.cacheProducerCache(cache.getName());

        return cache;
    }
}
