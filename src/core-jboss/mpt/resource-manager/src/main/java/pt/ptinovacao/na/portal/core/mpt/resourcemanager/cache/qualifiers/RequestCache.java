package pt.ptinovacao.na.portal.core.mpt.resourcemanager.cache.qualifiers;

import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.lang.annotation.Documented;

import javax.inject.Qualifier;

/**
 *
 * <AUTHOR> <micha<PERSON>-<PERSON>@telecom.pt>
 */
@Qualifier
@Retention(RUNTIME)
@Target({METHOD, FIELD, PARAMETER, TYPE})
@Documented
public @interface RequestCache {

    String REQUEST_CACHE_NAME = "RequestCache";
}
