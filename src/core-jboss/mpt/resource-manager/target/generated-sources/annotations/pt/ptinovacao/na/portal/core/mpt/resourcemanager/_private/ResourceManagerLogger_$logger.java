package pt.ptinovacao.na.portal.core.mpt.resourcemanager._private;

import java.util.Locale;
import java.io.Serializable;
import org.infinispan.notifications.cachelistener.event.Event.Type;
import javax.annotation.Generated;
import org.jboss.logging.DelegatingBasicLogger;
import org.jboss.logging.BasicLogger;
import java.lang.Throwable;
import java.lang.String;
import org.jboss.logging.Logger;
import org.jboss.dmr.ModelNode;


import static org.jboss.logging.Logger.Level.TRACE;
import static org.jboss.logging.Logger.Level.ERROR;
import static org.jboss.logging.Logger.Level.INFO;
import static org.jboss.logging.Logger.Level.DEBUG;
import static org.jboss.logging.Logger.Level.FATAL;
import static org.jboss.logging.Logger.Level.WARN;

/**
 * Warning this class consists of generated code.
 */
@Generated(value = "org.jboss.logging.processor.generator.model.MessageLoggerImplementor", date = "2025-07-01T11:20:43+0100")
public class ResourceManagerLogger_$logger extends DelegatingBasicLogger implements ResourceManagerLogger, BasicLogger, Serializable {
    private static final long serialVersionUID = 1L;
    private static final String FQCN = ResourceManagerLogger_$logger.class.getName();
    public ResourceManagerLogger_$logger(final Logger log) {
        super(log);
    }
    private static final Locale LOCALE = Locale.ROOT;
    protected Locale getLoggingLocale() {
        return LOCALE;
    }
    @Override
    public final void bulkRegister(final long bulkId, final String family, final java.util.Set<Long> registeredBulks) {
        super.log.logf(FQCN, INFO, null, bulkRegister$str(), bulkId, family, registeredBulks);
    }
    private static final String bulkRegister = "MPTRESOURCEMANAGER000001: Registered bulk [%s]. Actual registered bulks for family '%s': %s";
    protected String bulkRegister$str() {
        return bulkRegister;
    }
    @Override
    public final void bulkUnregister(final long bulkId, final String family, final java.util.Set<Long> registeredBulks) {
        super.log.logf(FQCN, INFO, null, bulkUnregister$str(), bulkId, family, registeredBulks);
    }
    private static final String bulkUnregister = "MPTRESOURCEMANAGER000002: Unregistered bulk [%s]. Actual registered bulks for family '%s': %s";
    protected String bulkUnregister$str() {
        return bulkUnregister;
    }
    @Override
    public final void bulkRegistrationFailure(final long bulkId, final String family, final int size) {
        super.log.logf(FQCN, ERROR, null, bulkRegistrationFailure$str(), bulkId, family, size);
    }
    private static final String bulkRegistrationFailure = "MPTRESOURCEMANAGER000003: Failed registering bulk [%s]. Actual number of registered bulks for family '%s': %s";
    protected String bulkRegistrationFailure$str() {
        return bulkRegistrationFailure;
    }
    @Override
    public final void bulkUnregistrationFailure(final long bulkId, final String family, final int size) {
        super.log.logf(FQCN, ERROR, null, bulkUnregistrationFailure$str(), bulkId, family, size);
    }
    private static final String bulkUnregistrationFailure = "MPTRESOURCEMANAGER000004: Failed unregistering bulk [%s]. Actual number of registered bulks for family '%s': %s";
    protected String bulkUnregistrationFailure$str() {
        return bulkUnregistrationFailure;
    }
    @Override
    public final void familyResourcesCheck(final String family, final long actual, final long maxSize) {
        super.log.logf(FQCN, DEBUG, null, familyResourcesCheck$str(), family, actual, maxSize);
    }
    private static final String familyResourcesCheck = "MPTRESOURCEMANAGER000005: Checking if family '%s' has resources to process any orders. Actual/Maximum [%s/%s]";
    protected String familyResourcesCheck$str() {
        return familyResourcesCheck;
    }
    @Override
    public final void registeredOrderToFamily(final String family, final long actual, final long maxSize) {
        super.log.logf(FQCN, INFO, null, registeredOrderToFamily$str(), family, actual, maxSize);
    }
    private static final String registeredOrderToFamily = "MPTRESOURCEMANAGER000006: Registered order for family '%s'. Slots [%s/%s]";
    protected String registeredOrderToFamily$str() {
        return registeredOrderToFamily;
    }
    @Override
    public final void unregisteredOrderToFamily(final String family, final long actual) {
        super.log.logf(FQCN, INFO, null, unregisteredOrderToFamily$str(), family, actual);
    }
    private static final String unregisteredOrderToFamily = "MPTRESOURCEMANAGER000007: Unregistered order for family '%s'. Actual registered orders [%s]";
    protected String unregisteredOrderToFamily$str() {
        return unregisteredOrderToFamily;
    }
    @Override
    public final void waitForResourcesSlotAvailable(final String family) {
        super.log.logf(FQCN, INFO, null, waitForResourcesSlotAvailable$str(), family);
    }
    private static final String waitForResourcesSlotAvailable = "MPTRESOURCEMANAGER000008: Waiting for resources... There's a slot available on family '%s'";
    protected String waitForResourcesSlotAvailable$str() {
        return waitForResourcesSlotAvailable;
    }
    @Override
    public final void waitForResourcesLeaveSafeCondMonitor(final String family) {
        super.log.logf(FQCN, INFO, null, waitForResourcesLeaveSafeCondMonitor$str(), family);
    }
    private static final String waitForResourcesLeaveSafeCondMonitor = "MPTRESOURCEMANAGER000009: Waiting for resources... Leaving safe condition monitor of family '%s'";
    protected String waitForResourcesLeaveSafeCondMonitor$str() {
        return waitForResourcesLeaveSafeCondMonitor;
    }
    @Override
    public final void waitForResourcesSkipSafeCondGuard(final String family) {
        super.log.logf(FQCN, INFO, null, waitForResourcesSkipSafeCondGuard$str(), family);
    }
    private static final String waitForResourcesSkipSafeCondGuard = "MPTRESOURCEMANAGER000010: Waiting for resources... Family '%s' did not enter on safeCondition Guard!!! Interrupt SIG arrival?!";
    protected String waitForResourcesSkipSafeCondGuard$str() {
        return waitForResourcesSkipSafeCondGuard;
    }
    @Override
    public final void waitForResourcesLeaveCondGuard(final String family) {
        super.log.logf(FQCN, INFO, null, waitForResourcesLeaveCondGuard$str(), family);
    }
    private static final String waitForResourcesLeaveCondGuard = "MPTRESOURCEMANAGER000011: Waiting for resources... Leaving guard condition monitor of family '%s'";
    protected String waitForResourcesLeaveCondGuard$str() {
        return waitForResourcesLeaveCondGuard;
    }
    @Override
    public final void initConfService() {
        super.log.logf(FQCN, DEBUG, null, initConfService$str());
    }
    private static final String initConfService = "MPTRESOURCEMANAGER000012: [PostConstruct] Initializing ResourceManager Configuration Service...";
    protected String initConfService$str() {
        return initConfService;
    }
    @Override
    public final void confServiceCacheListenerFound(final String name) {
        super.log.logf(FQCN, TRACE, null, confServiceCacheListenerFound$str(), name);
    }
    private static final String confServiceCacheListenerFound = "MPTRESOURCEMANAGER000013: [PostConstruct] Found a registered Configuration Service Cache listener [%s]";
    protected String confServiceCacheListenerFound$str() {
        return confServiceCacheListenerFound;
    }
    @Override
    public final void confServiceCacheListenerRemoval() {
        super.log.logf(FQCN, DEBUG, null, confServiceCacheListenerRemoval$str());
    }
    private static final String confServiceCacheListenerRemoval = "MPTRESOURCEMANAGER000014: [PostConstruct] Found a previous registered Configuration Service Cache listener. Removing it...";
    protected String confServiceCacheListenerRemoval$str() {
        return confServiceCacheListenerRemoval;
    }
    @Override
    public final void confServiceCacheListenerRegister() {
        super.log.logf(FQCN, DEBUG, null, confServiceCacheListenerRegister$str());
    }
    private static final String confServiceCacheListenerRegister = "MPTRESOURCEMANAGER000015: [PostConstruct] Registered a fresh Configuration Service Cache listener";
    protected String confServiceCacheListenerRegister$str() {
        return confServiceCacheListenerRegister;
    }
    @Override
    public final void confServiceCacheStorage(final String mapAsString) {
        super.log.logf(FQCN, INFO, null, confServiceCacheStorage$str(), mapAsString);
    }
    private static final String confServiceCacheStorage = "MPTRESOURCEMANAGER000016: [PostConstruct] Storing Configuration Service Cache configurations to infinispan cache. Mapped configurations: %s";
    protected String confServiceCacheStorage$str() {
        return confServiceCacheStorage;
    }
    @Override
    public final void initConfServiceEnd(final String cacheAsString) {
        super.log.logf(FQCN, DEBUG, null, initConfServiceEnd$str(), cacheAsString);
    }
    private static final String initConfServiceEnd = "MPTRESOURCEMANAGER000017: [PostConstruct] Initialized ResourceManager Configuration Service. Total mapped configurations: %s";
    protected String initConfServiceEnd$str() {
        return initConfServiceEnd;
    }
    @Override
    public final void destroyConfService() {
        super.log.logf(FQCN, TRACE, null, destroyConfService$str());
    }
    private static final String destroyConfService = "MPTRESOURCEMANAGER000018: [PostDestroy] Disposing ResourceManager Configuration Service...";
    protected String destroyConfService$str() {
        return destroyConfService;
    }
    @Override
    public final void producingWildflyModelControllerClient(final String user, final String password) {
        super.log.logf(FQCN, INFO, null, producingWildflyModelControllerClient$str(), user, password);
    }
    private static final String producingWildflyModelControllerClient = "MPTRESOURCEMANAGER000019: Producing Wildfly model controller client. The used security settings were: User/Password [%s/%s]";
    protected String producingWildflyModelControllerClient$str() {
        return producingWildflyModelControllerClient;
    }
    @Override
    public final void producedWildflyModelControllerClient(final String ip, final int port) {
        super.log.logf(FQCN, INFO, null, producedWildflyModelControllerClient$str(), ip, port);
    }
    private static final String producedWildflyModelControllerClient = "MPTRESOURCEMANAGER000020: Produced Wildfly model controller client. ModelControllerClient address [%s:%s]";
    protected String producedWildflyModelControllerClient$str() {
        return producedWildflyModelControllerClient;
    }
    @Override
    public final void disposingWildflyModelControllerClient() {
        super.log.logf(FQCN, INFO, null, disposingWildflyModelControllerClient$str());
    }
    private static final String disposingWildflyModelControllerClient = "MPTRESOURCEMANAGER000021: Disposing Wildfly ModelControllerClient";
    protected String disposingWildflyModelControllerClient$str() {
        return disposingWildflyModelControllerClient;
    }
    @Override
    public final void cacheProducerCache(final String cacheName) {
        super.log.logf(FQCN, INFO, null, cacheProducerCache$str(), cacheName);
    }
    private static final String cacheProducerCache = "MPTRESOURCEMANAGER000022: Cache producer produced the cache [%s] with CDI @ApplicationScope scope";
    protected String cacheProducerCache$str() {
        return cacheProducerCache;
    }
    @Override
    public final void configCacheListenerArrival(final String cacheName, final Type type, final String key, final String value) {
        super.log.logf(FQCN, WARN, null, configCacheListenerArrival$str(), cacheName, type, key, value);
    }
    private static final String configCacheListenerArrival = "MPTRESOURCEMANAGER000023: Cache [%s] - New configuration of type [%s] arrived into configuration cache listener! Key/Value [%s/%s]";
    protected String configCacheListenerArrival$str() {
        return configCacheListenerArrival;
    }
    @Override
    public final void sendingSysPropToWildflyModelControllerClient(final String key, final String value) {
        super.log.logf(FQCN, INFO, null, sendingSysPropToWildflyModelControllerClient$str(), key, value);
    }
    private static final String sendingSysPropToWildflyModelControllerClient = "MPTRESOURCEMANAGER000024: Sending a configuration system property command to Wildfly! Key/Value [%s/%s]";
    protected String sendingSysPropToWildflyModelControllerClient$str() {
        return sendingSysPropToWildflyModelControllerClient;
    }
    @Override
    public final void ignoringSysPropToWildflyModelControllerClient(final String key, final String value) {
        super.log.logf(FQCN, WARN, null, ignoringSysPropToWildflyModelControllerClient$str(), key, value);
    }
    private static final String ignoringSysPropToWildflyModelControllerClient = "MPTRESOURCEMANAGER000025: Ignoring Wildfly configuration system property because is the same as before! Key/Value [%s/%s]";
    protected String ignoringSysPropToWildflyModelControllerClient$str() {
        return ignoringSysPropToWildflyModelControllerClient;
    }
    @Override
    public final void setPropertyWildflyModelControllerClient(final String key, final ModelNode result) {
        super.log.logf(FQCN, INFO, null, setPropertyWildflyModelControllerClient$str(), key, result);
    }
    private static final String setPropertyWildflyModelControllerClient = "MPTRESOURCEMANAGER000026: Setting property '%s' via WildflyModelControllerClient! Controller result: %s";
    protected String setPropertyWildflyModelControllerClient$str() {
        return setPropertyWildflyModelControllerClient;
    }
    @Override
    public final void removePropertyWildflyModelControllerClient(final String key, final ModelNode result) {
        super.log.logf(FQCN, INFO, null, removePropertyWildflyModelControllerClient$str(), key, result);
    }
    private static final String removePropertyWildflyModelControllerClient = "MPTRESOURCEMANAGER000027: Removing property '%s' via WildflyModelControllerClient! Controller result: %s";
    protected String removePropertyWildflyModelControllerClient$str() {
        return removePropertyWildflyModelControllerClient;
    }
    @Override
    public final void redefinePropertyWildflyModelControllerClient(final String key, final ModelNode result) {
        super.log.logf(FQCN, INFO, null, redefinePropertyWildflyModelControllerClient$str(), key, result);
    }
    private static final String redefinePropertyWildflyModelControllerClient = "MPTRESOURCEMANAGER000028: Redefining property '%s' via WildflyModelControllerClient! Controller result: %s";
    protected String redefinePropertyWildflyModelControllerClient$str() {
        return redefinePropertyWildflyModelControllerClient;
    }
    @Override
    public final void getPropertyWildflyModelControllerClient(final String key, final ModelNode result) {
        super.log.logf(FQCN, INFO, null, getPropertyWildflyModelControllerClient$str(), key, result);
    }
    private static final String getPropertyWildflyModelControllerClient = "MPTRESOURCEMANAGER000029: Getting property '%s' via WildflyModelControllerClient! Controller result: %s";
    protected String getPropertyWildflyModelControllerClient$str() {
        return getPropertyWildflyModelControllerClient;
    }
    @Override
    public final void getResultFailWildflyModelControllerClient(final String result) {
        super.log.logf(FQCN, WARN, null, getResultFailWildflyModelControllerClient$str(), result);
    }
    private static final String getResultFailWildflyModelControllerClient = "MPTRESOURCEMANAGER000030: Operation failed with \n %s";
    protected String getResultFailWildflyModelControllerClient$str() {
        return getResultFailWildflyModelControllerClient;
    }
    @Override
    public final void executeFailWildflyModelControllerClient(final Throwable t, final String result) {
        super.log.logf(FQCN, FATAL, t, executeFailWildflyModelControllerClient$str(), result);
    }
    private static final String executeFailWildflyModelControllerClient = "MPTRESOURCEMANAGER000031: Failed execution of operation \n %s";
    protected String executeFailWildflyModelControllerClient$str() {
        return executeFailWildflyModelControllerClient;
    }
    @Override
    public final void signalNextMonitorThread(final String familyName) {
        super.log.logf(FQCN, INFO, null, signalNextMonitorThread$str(), familyName);
    }
    private static final String signalNextMonitorThread = "MPTRESOURCEMANAGER000032: Signalled next waiting thread monitor for family '%s'...";
    protected String signalNextMonitorThread$str() {
        return signalNextMonitorThread;
    }
}
