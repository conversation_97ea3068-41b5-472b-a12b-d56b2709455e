package pt.ptinovacao.na.portal.core.mpt.resourcemanager._private;

import java.util.Locale;
import java.lang.IllegalStateException;
import java.io.Serializable;
import javax.annotation.Generated;
import java.lang.RuntimeException;
import java.lang.Throwable;
import java.lang.String;
import pt.ptinovacao.na.portal.core.mpt.resourcemanager.exception.ResourceManagerException;
import java.util.Arrays;
import org.jboss.dmr.ModelNode;
import java.lang.IllegalArgumentException;

/**
 * Warning this class consists of generated code.
 */
@Generated(value = "org.jboss.logging.processor.generator.model.MessageBundleImplementor", date = "2025-07-01T11:20:43+0100")
public class ResourceManagerMessages_$bundle implements ResourceManagerMessages, Serializable {
    private static final long serialVersionUID = 1L;
    protected ResourceManagerMessages_$bundle() {}
    public static final ResourceManagerMessages_$bundle INSTANCE = new ResourceManagerMessages_$bundle();
    protected Object readResolve() {
        return INSTANCE;
    }
    private static final Locale LOCALE = Locale.ROOT;
    protected Locale getLoggingLocale() {
        return LOCALE;
    }
    private static final String errorProducingWildflyModelControllerClient = "MPTRESOURCEMANAGER000200: Could not produce a ModelControllerClient. Domain username/password/host/port might be missing???";
    protected String errorProducingWildflyModelControllerClient$str() {
        return errorProducingWildflyModelControllerClient;
    }
    @Override
    public final IllegalStateException errorProducingWildflyModelControllerClient(final Throwable t) {
        final IllegalStateException result = new IllegalStateException(String.format(getLoggingLocale(), errorProducingWildflyModelControllerClient$str()), t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String errorWildflyPropertyInjector = "MPTRESOURCEMANAGER000201: Wildfly system property %s is missing from injection point in class %s";
    protected String errorWildflyPropertyInjector$str() {
        return errorWildflyPropertyInjector;
    }
    @Override
    public final IllegalArgumentException errorWildflyPropertyInjector(final String propertyName, final String className) {
        final IllegalArgumentException result = new IllegalArgumentException(String.format(getLoggingLocale(), errorWildflyPropertyInjector$str(), propertyName, className));
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String errorExecutingWildflyModelControllerClient = "MPTRESOURCEMANAGER000202: WildflyModelControllerClient failed executing operation %s";
    protected String errorExecutingWildflyModelControllerClient$str() {
        return errorExecutingWildflyModelControllerClient;
    }
    @Override
    public final RuntimeException errorExecutingWildflyModelControllerClient(final Throwable t, final String operation) {
        final RuntimeException result = new RuntimeException(String.format(getLoggingLocale(), errorExecutingWildflyModelControllerClient$str(), operation), t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String wildflyModelControllerClientCmdFailure = "MPTRESOURCEMANAGER000203: WildflyModelControllerClient return error while executing management operation %s. Returned the following result: %s";
    protected String wildflyModelControllerClientCmdFailure$str() {
        return wildflyModelControllerClientCmdFailure;
    }
    @Override
    public final RuntimeException wildflyModelControllerClientCmdFailure(final ModelNode operation, final ModelNode resultOp) {
        final RuntimeException result = new RuntimeException(String.format(getLoggingLocale(), wildflyModelControllerClientCmdFailure$str(), operation, resultOp));
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String hasOrderResourcesFailure = "MPTRESOURCEMANAGER000204: Could not check if family has order resources";
    protected String hasOrderResourcesFailure$str() {
        return hasOrderResourcesFailure;
    }
    @Override
    public final ResourceManagerException hasOrderResourcesFailure(final Throwable t) {
        final ResourceManagerException result = new ResourceManagerException(String.format(getLoggingLocale(), hasOrderResourcesFailure$str()), t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String hasFamilyOtherRegisteredBulksFailure = "MPTRESOURCEMANAGER000205: Could not check if family has other registered bulks";
    protected String hasFamilyOtherRegisteredBulksFailure$str() {
        return hasFamilyOtherRegisteredBulksFailure;
    }
    @Override
    public final ResourceManagerException hasFamilyOtherRegisteredBulksFailure(final Throwable t) {
        final ResourceManagerException result = new ResourceManagerException(String.format(getLoggingLocale(), hasFamilyOtherRegisteredBulksFailure$str()), t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String getFamilyOtherRegisteredBulksFailure = "MPTRESOURCEMANAGER000206: Could not retrieve other family registered bulks";
    protected String getFamilyOtherRegisteredBulksFailure$str() {
        return getFamilyOtherRegisteredBulksFailure;
    }
    @Override
    public final ResourceManagerException getFamilyOtherRegisteredBulksFailure(final Throwable t) {
        final ResourceManagerException result = new ResourceManagerException(String.format(getLoggingLocale(), getFamilyOtherRegisteredBulksFailure$str()), t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String getFamilyMonitorFailure = "MPTRESOURCEMANAGER000207: Could not retrieve family monitor";
    protected String getFamilyMonitorFailure$str() {
        return getFamilyMonitorFailure;
    }
    @Override
    public final ResourceManagerException getFamilyMonitorFailure(final Throwable t) {
        final ResourceManagerException result = new ResourceManagerException(String.format(getLoggingLocale(), getFamilyMonitorFailure$str()), t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String registerFamilyFailure = "MPTRESOURCEMANAGER000208: Could not register family monitor";
    protected String registerFamilyFailure$str() {
        return registerFamilyFailure;
    }
    @Override
    public final ResourceManagerException registerFamilyFailure(final Throwable t) {
        final ResourceManagerException result = new ResourceManagerException(String.format(getLoggingLocale(), registerFamilyFailure$str()), t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String registerOrderFailure = "MPTRESOURCEMANAGER000209: Could not register order";
    protected String registerOrderFailure$str() {
        return registerOrderFailure;
    }
    @Override
    public final ResourceManagerException registerOrderFailure(final Throwable t) {
        final ResourceManagerException result = new ResourceManagerException(String.format(getLoggingLocale(), registerOrderFailure$str()), t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String unregisterOrderFailure = "MPTRESOURCEMANAGER000210: Could not unregister order";
    protected String unregisterOrderFailure$str() {
        return unregisterOrderFailure;
    }
    @Override
    public final ResourceManagerException unregisterOrderFailure(final Throwable t) {
        final ResourceManagerException result = new ResourceManagerException(String.format(getLoggingLocale(), unregisterOrderFailure$str()), t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String registerBulkThreadFailure = "MPTRESOURCEMANAGER000211: Could not register bulk thread";
    protected String registerBulkThreadFailure$str() {
        return registerBulkThreadFailure;
    }
    @Override
    public final ResourceManagerException registerBulkThreadFailure(final Throwable t) {
        final ResourceManagerException result = new ResourceManagerException(String.format(getLoggingLocale(), registerBulkThreadFailure$str()), t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String unregisterBulkThreadFailure = "MPTRESOURCEMANAGER000212: Could not unregister bulk thread";
    protected String unregisterBulkThreadFailure$str() {
        return unregisterBulkThreadFailure;
    }
    @Override
    public final ResourceManagerException unregisterBulkThreadFailure(final Throwable t) {
        final ResourceManagerException result = new ResourceManagerException(String.format(getLoggingLocale(), unregisterBulkThreadFailure$str()), t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String isBulkThreadRegisteredFailure = "MPTRESOURCEMANAGER000213: Could check if thread bulk is registered";
    protected String isBulkThreadRegisteredFailure$str() {
        return isBulkThreadRegisteredFailure;
    }
    @Override
    public final ResourceManagerException isBulkThreadRegisteredFailure(final Throwable t) {
        final ResourceManagerException result = new ResourceManagerException(String.format(getLoggingLocale(), isBulkThreadRegisteredFailure$str()), t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String signalNextFamilyMonitorWaitingThreadsFailure = "MPTRESOURCEMANAGER000214: Couldn't signal next family monitor waiting threads";
    protected String signalNextFamilyMonitorWaitingThreadsFailure$str() {
        return signalNextFamilyMonitorWaitingThreadsFailure;
    }
    @Override
    public final ResourceManagerException signalNextFamilyMonitorWaitingThreadsFailure(final Throwable t) {
        final ResourceManagerException result = new ResourceManagerException(String.format(getLoggingLocale(), signalNextFamilyMonitorWaitingThreadsFailure$str()), t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String doesRequestHasSameActionFailure = "MPTRESOURCEMANAGER000215: Couldn't check if request exists and if it has the same action";
    protected String doesRequestHasSameActionFailure$str() {
        return doesRequestHasSameActionFailure;
    }
    @Override
    public final ResourceManagerException doesRequestHasSameActionFailure(final Throwable t) {
        final ResourceManagerException result = new ResourceManagerException(String.format(getLoggingLocale(), doesRequestHasSameActionFailure$str()), t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String deleteRequestFailure = "MPTRESOURCEMANAGER000216: Couldn't delete request";
    protected String deleteRequestFailure$str() {
        return deleteRequestFailure;
    }
    @Override
    public final ResourceManagerException deleteRequestFailure(final Throwable t) {
        final ResourceManagerException result = new ResourceManagerException(String.format(getLoggingLocale(), deleteRequestFailure$str()), t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String addOrUpdateRequestFailure = "MPTRESOURCEMANAGER000217: Couldn't insert or update request";
    protected String addOrUpdateRequestFailure$str() {
        return addOrUpdateRequestFailure;
    }
    @Override
    public final ResourceManagerException addOrUpdateRequestFailure(final Throwable t) {
        final ResourceManagerException result = new ResourceManagerException(String.format(getLoggingLocale(), addOrUpdateRequestFailure$str()), t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String deleteAndSanitizeRequestFailure = "MPTRESOURCEMANAGER000218: Couldn't delete and sanitize request";
    protected String deleteAndSanitizeRequestFailure$str() {
        return deleteAndSanitizeRequestFailure;
    }
    @Override
    public final ResourceManagerException deleteAndSanitizeRequestFailure(final Throwable t) {
        final ResourceManagerException result = new ResourceManagerException(String.format(getLoggingLocale(), deleteAndSanitizeRequestFailure$str()), t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String getRequestFailure = "MPTRESOURCEMANAGER000219: Couldn't fetch request";
    protected String getRequestFailure$str() {
        return getRequestFailure;
    }
    @Override
    public final ResourceManagerException getRequestFailure(final Throwable t) {
        final ResourceManagerException result = new ResourceManagerException(String.format(getLoggingLocale(), getRequestFailure$str()), t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
}
