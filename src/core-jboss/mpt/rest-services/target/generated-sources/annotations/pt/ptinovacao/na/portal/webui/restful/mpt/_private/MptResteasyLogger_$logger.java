package pt.ptinovacao.na.portal.webui.restful.mpt._private;

import java.util.Locale;
import java.io.Serializable;
import javax.annotation.Generated;
import org.jboss.logging.DelegatingBasicLogger;
import org.jboss.logging.BasicLogger;
import java.lang.Long;
import java.lang.String;
import org.jboss.logging.Logger;


import static org.jboss.logging.Logger.Level.INFO;

/**
 * Warning this class consists of generated code.
 */
@Generated(value = "org.jboss.logging.processor.generator.model.MessageLoggerImplementor", date = "2025-07-01T11:20:45+0100")
public class MptResteasyLogger_$logger extends DelegatingBasicLogger implements MptResteasyLogger, BasicLogger, Serializable {
    private static final long serialVersionUID = 1L;
    private static final String FQCN = MptResteasyLogger_$logger.class.getName();
    public MptResteasyLogger_$logger(final Logger log) {
        super(log);
    }
    private static final Locale LOCALE = Locale.ROOT;
    protected Locale getLoggingLocale() {
        return LOCALE;
    }
    @Override
    public final void agentExecuteFileOperation(final String request, final String filename) {
        super.log.logf(FQCN, INFO, null, agentExecuteFileOperation$str(), request, filename);
    }
    private static final String agentExecuteFileOperation = "MPTSERVICE000001: An AGENT CORE [execute file] operation was received: %s for file '%s'";
    protected String agentExecuteFileOperation$str() {
        return agentExecuteFileOperation;
    }
    @Override
    public final void agentConsultOperation(final String request) {
        super.log.logf(FQCN, INFO, null, agentConsultOperation$str(), request);
    }
    private static final String agentConsultOperation = "MPTSERVICE000002: An AGENT CORE [consult] operation was received: %s";
    protected String agentConsultOperation$str() {
        return agentConsultOperation;
    }
    @Override
    public final void agentExportFileOperation(final String request) {
        super.log.logf(FQCN, INFO, null, agentExportFileOperation$str(), request);
    }
    private static final String agentExportFileOperation = "MPTSERVICE000003: An AGENT CORE [export] operation was received: %s";
    protected String agentExportFileOperation$str() {
        return agentExportFileOperation;
    }
    @Override
    public final void agentInstallOperation(final String customization) {
        super.log.logf(FQCN, INFO, null, agentInstallOperation$str(), customization);
    }
    private static final String agentInstallOperation = "MPTSERVICE000004: An AGENT CORE [customization] install was received: %s";
    protected String agentInstallOperation$str() {
        return agentInstallOperation;
    }
    @Override
    public final void agentInstallFamilyOperation(final String customization) {
        super.log.logf(FQCN, INFO, null, agentInstallFamilyOperation$str(), customization);
    }
    private static final String agentInstallFamilyOperation = "MPTSERVICE000005: An AGENT CORE [family customization] was received: %s";
    protected String agentInstallFamilyOperation$str() {
        return agentInstallFamilyOperation;
    }
    @Override
    public final void agentPing() {
        super.log.logf(FQCN, INFO, null, agentPing$str());
    }
    private static final String agentPing = "MPTSERVICE000006: An AGENT CORE [ping] was received";
    protected String agentPing$str() {
        return agentPing;
    }
    @Override
    public final void bulkCreate(final String request, final String filename) {
        super.log.logf(FQCN, INFO, null, bulkCreate$str(), request, filename);
    }
    private static final String bulkCreate = "MPTSERVICE000007: A BULK [Create] request %s was received for file '%s'";
    protected String bulkCreate$str() {
        return bulkCreate;
    }
    @Override
    public final void bulkResume(final String request) {
        super.log.logf(FQCN, INFO, null, bulkResume$str(), request);
    }
    private static final String bulkResume = "MPTSERVICE000008: A BULK [Resume] request %s was received";
    protected String bulkResume$str() {
        return bulkResume;
    }
    @Override
    public final void bulkSuspend(final String request) {
        super.log.logf(FQCN, INFO, null, bulkSuspend$str(), request);
    }
    private static final String bulkSuspend = "MPTSERVICE000009: A BULK [Suspend] request %s was received";
    protected String bulkSuspend$str() {
        return bulkSuspend;
    }
    @Override
    public final void bulkAbort(final String request) {
        super.log.logf(FQCN, INFO, null, bulkAbort$str(), request);
    }
    private static final String bulkAbort = "MPTSERVICE000010: A BULK [Abort] request %s was received";
    protected String bulkAbort$str() {
        return bulkAbort;
    }
    @Override
    public final void bulkSuspendAdmin(final String request) {
        super.log.logf(FQCN, INFO, null, bulkSuspendAdmin$str(), request);
    }
    private static final String bulkSuspendAdmin = "MPTSERVICE000011: A BULK [SuspendAdmin] request %s was received";
    protected String bulkSuspendAdmin$str() {
        return bulkSuspendAdmin;
    }
    @Override
    public final void bulkUpdate(final String request) {
        super.log.logf(FQCN, INFO, null, bulkUpdate$str(), request);
    }
    private static final String bulkUpdate = "MPTSERVICE000012: A BULK [Update] request %s was received";
    protected String bulkUpdate$str() {
        return bulkUpdate;
    }
    @Override
    public final void getBulkFile(final Long bulkId, final String type) {
        super.log.logf(FQCN, INFO, null, getBulkFile$str(), bulkId, type);
    }
    private static final String getBulkFile = "MPTSERVICE000013: A BULK [Get File] request was received for bulk id '%s' and type '%s'";
    protected String getBulkFile$str() {
        return getBulkFile;
    }
}
