package pt.ptinovacao.na.portal.webui.restful.mpt._private;

import java.util.Locale;
import java.io.Serializable;
import javax.annotation.Generated;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.exceptions.MptResteasyException;
import java.lang.Throwable;
import java.lang.String;
import java.util.Arrays;

/**
 * Warning this class consists of generated code.
 */
@Generated(value = "org.jboss.logging.processor.generator.model.MessageBundleImplementor", date = "2025-07-01T11:20:45+0100")
public class MptResteasyMessages_$bundle implements MptResteasyMessages, Serializable {
    private static final long serialVersionUID = 1L;
    protected MptResteasyMessages_$bundle() {}
    public static final MptResteasyMessages_$bundle INSTANCE = new MptResteasyMessages_$bundle();
    protected Object readResolve() {
        return INSTANCE;
    }
    private static final Locale LOCALE = Locale.ROOT;
    protected Locale getLoggingLocale() {
        return LOCALE;
    }
    private static final String createBulkFailure = "MPTSERVICE000101: Could not create bulk for client '%s' and for request %s. Filename '%s'";
    protected String createBulkFailure$str() {
        return createBulkFailure;
    }
    @Override
    public final MptResteasyException createBulkFailure(final Throwable t, final String client, final String request, final String filename) {
        final MptResteasyException result = new MptResteasyException(String.format(getLoggingLocale(), createBulkFailure$str(), client, request, filename), t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String resumeBulkFailure = "MPTSERVICE000102: Could not resume bulk for client '%s' and for request %s";
    protected String resumeBulkFailure$str() {
        return resumeBulkFailure;
    }
    @Override
    public final MptResteasyException resumeBulkFailure(final Throwable t, final String client, final String request) {
        final MptResteasyException result = new MptResteasyException(String.format(getLoggingLocale(), resumeBulkFailure$str(), client, request), t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String abortBulkFailure = "MPTSERVICE000103: Could not abort bulk for client '%s' and for request %s";
    protected String abortBulkFailure$str() {
        return abortBulkFailure;
    }
    @Override
    public final MptResteasyException abortBulkFailure(final Throwable t, final String client, final String request) {
        final MptResteasyException result = new MptResteasyException(String.format(getLoggingLocale(), abortBulkFailure$str(), client, request), t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String updateBulkFailure = "MPTSERVICE000104: Could not update bulk for client '%s' and for request %s";
    protected String updateBulkFailure$str() {
        return updateBulkFailure;
    }
    @Override
    public final MptResteasyException updateBulkFailure(final Throwable t, final String client, final String request) {
        final MptResteasyException result = new MptResteasyException(String.format(getLoggingLocale(), updateBulkFailure$str(), client, request), t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String handleExportFileFailure = "MPTSERVICE000105: Could not handle an export file operation";
    protected String handleExportFileFailure$str() {
        return handleExportFileFailure;
    }
    @Override
    public final MptResteasyException handleExportFileFailure(final Throwable t) {
        final MptResteasyException result = new MptResteasyException(String.format(getLoggingLocale(), handleExportFileFailure$str()), t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String handleStartBulkFailure = "MPTSERVICE000106: Could not handle start bulk operation for request %s";
    protected String handleStartBulkFailure$str() {
        return handleStartBulkFailure;
    }
    @Override
    public final MptResteasyException handleStartBulkFailure(final Throwable t, final String request) {
        final MptResteasyException result = new MptResteasyException(String.format(getLoggingLocale(), handleStartBulkFailure$str(), request), t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
}
