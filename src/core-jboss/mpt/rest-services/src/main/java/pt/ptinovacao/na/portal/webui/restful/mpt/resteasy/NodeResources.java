/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package pt.ptinovacao.na.portal.webui.restful.mpt.resteasy;

import com.google.common.base.Strings;
import java.util.ArrayList;
import java.util.List;
import javax.inject.Inject;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriInfo;
import org.odata4j.producer.QueryInfo;
import pt.ptinovacao.na.portal.db.commons.persistence.CommonDataManagerException;
import pt.ptinovacao.na.portal.webui.restful.commons.model.RawListContainer;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.Consts;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.model.NodeEntry;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.model.NodeEntryResponseWrapper;
import pt.ptinovacao.na.portal.webui.restful.mpt.db.queries.NodeDao;
import pt.ptinovacao.na.portal.webui.restful.mpt.util.ClassConverter;

@Path(Consts.NA_PORTAL_ENDPOINTS)
public class NodeResources {

    @Inject
    private NodeDao nodeDao;

    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response searchNodes(@Context UriInfo uriInfo) {
        QueryInfo queryInfo = ClassConverter.getQueryInfo(uriInfo);
        RawListContainer<NodeEntry> nodes = this.nodeDao.searchNodes(queryInfo);
        return Response.ok(nodes).build();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response createNodes(List<NodeEntry> nodeEntries) {
        return createOrUpdateNodes(nodeEntries);
    }

    @PUT
    @Path(Consts.NA_PORTAL_ENDPOINTS_NODE_NAME)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response updateNode(@PathParam("nodeName") String nodeName, NodeEntry node) {
        NodeEntryResponseWrapper response = new NodeEntryResponseWrapper();
        if (Strings.isNullOrEmpty(nodeName) || !isValid(node)) {
            response.setStatus(NodeEntryResponseWrapper.STATUS.INVALID_NODE_ENTRY);
            return Response.status(Response.Status.BAD_REQUEST).entity(response).build();
        }

        try {
            if (this.nodeDao.getNodeConfiguration(nodeName) == null) {
                response.setStatus(NodeEntryResponseWrapper.STATUS.NOT_FOUND);
                return Response.status(Response.Status.NOT_FOUND).entity(response).build();
            }

            if (this.nodeDao.getNodeConfiguration(node.getEndpointName()) != null) {
                response.setStatus(NodeEntryResponseWrapper.STATUS.INVALID_NODE_ENTRY);
                return Response.status(Response.Status.CONFLICT).entity(response).build();
            }

            this.nodeDao.createOrUpdateNode(node, nodeName);
            response.setStatus(NodeEntryResponseWrapper.STATUS.OK);
        } catch (CommonDataManagerException e) {
            response.setStatus(NodeEntryResponseWrapper.STATUS.NOT_OK);
            return Response.notModified().entity(node).build();
        }

        return Response.ok().entity(response).build();
    }

    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response updateNodes(List<NodeEntry> nodeEntries) {
        return createOrUpdateNodes(nodeEntries);
    }

    @DELETE
    @Path(Consts.NA_PORTAL_ENDPOINTS_NODE_NAME)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response deleteNode(@PathParam("nodeName") String nodeName) {

        if (Strings.isNullOrEmpty(nodeName)) {
            return Response.status(Response.Status.BAD_REQUEST).build();
        }

        try {
            if (!this.nodeDao.deleteNode(nodeName)) {
                return Response.status(Response.Status.NOT_FOUND).build();
            }

        } catch (CommonDataManagerException e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).build();
        }

        return Response.ok().build();
    }

    @DELETE
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response deleteNodes(List<String> nodeNames) {
        List<String> removedNodes = new ArrayList<>();
        for (String nodeName : nodeNames) {
            try {
                if (this.nodeDao.deleteNode(nodeName)) {
                    removedNodes.add(nodeName);
                }
            } catch (CommonDataManagerException e) {
                return Response.status(Response.Status.INTERNAL_SERVER_ERROR).build();
            }
        }

        return Response.ok().entity(removedNodes).build();
    }

    /**
     * Creates or updates Nodes
     *
     * @param nodeEntries
     * @return
     */
    private Response createOrUpdateNodes(List<NodeEntry> nodeEntries) {
        boolean isListValid = Boolean.TRUE;
        List<NodeEntryResponseWrapper> responses = new ArrayList<>();
        for (NodeEntry node : nodeEntries) {
            NodeEntryResponseWrapper resp = new NodeEntryResponseWrapper(node);
            if (!isValid(node)) {
                isListValid = Boolean.FALSE;
                resp.setStatus(NodeEntryResponseWrapper.STATUS.INVALID_NODE_ENTRY);
            } else {
                resp.setStatus(NodeEntryResponseWrapper.STATUS.OK);
            }
            responses.add(resp);
        }

        if (!isListValid) {
            return Response.status(Response.Status.BAD_REQUEST).entity(new RawListContainer<>(responses, responses.size())).build();
        } else {
            responses.clear();
        }

        for (NodeEntry node : nodeEntries) {
            NodeEntryResponseWrapper resp = new NodeEntryResponseWrapper(node);
            try {
                this.nodeDao.createOrUpdateNode(node);
                resp.setStatus(NodeEntryResponseWrapper.STATUS.OK);
            } catch (CommonDataManagerException e) {
                resp.setStatus(NodeEntryResponseWrapper.STATUS.NOT_OK);
            }
            responses.add(resp);
        }

        return Response.ok(new RawListContainer<>(responses, responses.size())).build();
    }

    public boolean isValid(NodeEntry nodeEntry) {
        return !(Strings.isNullOrEmpty(nodeEntry.getEndpointName()) || Strings.isNullOrEmpty(nodeEntry.getApplicationDN())
                || Strings.isNullOrEmpty(nodeEntry.getNodeType()) || Strings.isNullOrEmpty(nodeEntry.getUrl()));
    }
}
