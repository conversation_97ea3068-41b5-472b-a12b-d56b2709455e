package pt.ptinovacao.na.portal.webui.restful.mpt.resteasy;

import com.google.common.base.Splitter;
import com.google.common.primitives.Ints;
import pt.ptinovacao.na.portal.core.mpt.engine.BulkEngineEJB;
import pt.ptinovacao.na.portal.core.mpt.engine.CustomizationEngine;
import pt.ptinovacao.na.portal.core.mpt.resourcemanager.ResourceManagerEJB;
import pt.ptinovacao.na.portal.webui.restful.mpt._private.MptResteasyLogger;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.Consts;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.entities.Customization;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.entities.Request;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.entities.RequestFileInputForm;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.entities.ResponseFileInputForm;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.exceptions.MptResteasyException;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.model.BulkDetailsEntry;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.proxies.AgentProxy;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.schedules.ScheduleEventsParser;
import pt.ptinovacao.na.portal.webui.restful.mpt.db.queries.BulkDao;

import javax.ejb.EJB;
import javax.inject.Inject;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;
import java.util.Date;
import java.util.Set;
import java.util.stream.StreamSupport;

/**
 *
 * <AUTHOR> Karamanos <<EMAIL>>
 */
@Path(Consts.AGENT_CORE_PATH)
public class AgentResources implements AgentProxy {

    @EJB
    private BulkEngineEJB engine;

    @Inject
    private BulkDao bulkDao;

    @Inject
    private ResourceManagerEJB resourceManager;

    @Inject
    private CustomizationEngine customizationEngine;

    @Inject
    private String tmpOrderFilesDir;

    @Override
    public Response executeFileOperation(RequestFileInputForm formDataInput)
            throws MptResteasyException {
        Request request = formDataInput.getRequest();
        MptResteasyLogger.LOGGER.agentExecuteFileOperation(request.toString(), formDataInput.getFilename());

        // Validate schedules
        if (formDataInput.getRequest().hasSchedules()) {
            ScheduleEventsParser scheduleEventParser = new ScheduleEventsParser(formDataInput.getRequest().getSchedules());
            ScheduleEventsParser.ValidationResult validationResult = scheduleEventParser.validate();
            if (!validationResult.isValid()) {
                MptResteasyLogger.LOGGER.infof("invalid schedule (overlapped periods): %s", formDataInput.getRequest().getSchedules());
                return Response.status(Status.BAD_REQUEST).entity(validationResult).build();
            }
        }
        pt.ptinovacao.na.portal.webui.restful.mpt.commons.entities.Response response = new pt.ptinovacao.na.portal.webui.restful.mpt.commons.entities.Response();
        try {
            long execId = new CommonExecutionResources(this.bulkDao, this.resourceManager, this.engine, this.tmpOrderFilesDir).handleStartBulk(formDataInput);
            MptResteasyLogger.LOGGER.infof("BatchAPI execId={%s}", execId);

            response.setState("acepted");
            response.setStartedAt(new Date());
            response.setBulkId(formDataInput.getRequest().getBulkId());

        } catch (MptResteasyException e) {
            return Response.status(Status.INTERNAL_SERVER_ERROR).entity(e).build();
        }

        return Response.accepted().entity(response).build();
    }

    @Override
    public Response consultOperation(Request request) {
        MptResteasyLogger.LOGGER.agentConsultOperation(request.toString());

        if (request.getBulkId() == null) {
            final String mandatoryParam = "Parameter bulkId is mandatory.";
            return Response.status(Status.BAD_REQUEST).entity(mandatoryParam).build();
        }

        BulkDetailsEntry detailsEntry = this.bulkDao.getBulkDetails(request.getBulkId());
        if (detailsEntry.getId() == null) {
            return Response.status(Status.NOT_FOUND).build();
        }

        return Response.ok(detailsEntry).build();
    }

    @Override
    public Response exportFileOperation(Request request) throws MptResteasyException {
        MptResteasyLogger.LOGGER.agentExportFileOperation(tmpOrderFilesDir);

        return new CommonExecutionResources(this.bulkDao, this.resourceManager, this.engine, this.tmpOrderFilesDir)
                .handleExportFile(request.getBulkId(), false);
    }

    @Override
    public Response installOperation(Customization customization) {
        MptResteasyLogger.LOGGER.agentInstallOperation(customization.toString());
        final Set<Integer> headerKeys = customization.getHeader().keySet();
        if (customization.getOperation().containsKey(Consts.COSTUMIZATION_OPERATION_ORDERKEYCONSTRUCTION)) {
            String orderKeyConstructor = customization.getOperation().get(Consts.COSTUMIZATION_OPERATION_ORDERKEYCONSTRUCTION).toString();
            if (!StreamSupport.stream(Splitter.on(";").trimResults().split(orderKeyConstructor).spliterator(), false)
                    .allMatch(input -> Ints.tryParse(input) != null && headerKeys.contains(Ints.tryParse(input)))) {

                MptResteasyLogger.LOGGER.infof("invalid costumization, Malformed orderKeyConstructor: %s", orderKeyConstructor);
                return Response.status(Status.BAD_REQUEST).entity("Malformed orderKeyConstructor: "
                        + orderKeyConstructor).build();
            }
        }

        return customizationEngine.executeInstall(customization);

    }

    @Override
    public Response installFamilyOperation(Customization customization) {
        MptResteasyLogger.LOGGER.agentInstallFamilyOperation(customization.toString());

        return customizationEngine.executeFamilyInstall(customization);

    }

    @Override
    public Response ping() {
        MptResteasyLogger.LOGGER.agentPing();
        return Response.ok().entity("CORE PONG [" + new Date().toString() + "]").build();
    }

    @Override
    public Response asyncExportOperation(ResponseFileInputForm fileInputForm) {
        return Response.status(Status.NOT_IMPLEMENTED).build();
    }
}
