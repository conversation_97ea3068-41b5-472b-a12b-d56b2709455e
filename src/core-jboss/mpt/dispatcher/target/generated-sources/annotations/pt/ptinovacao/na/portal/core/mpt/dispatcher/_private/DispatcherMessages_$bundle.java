package pt.ptinovacao.na.portal.core.mpt.dispatcher._private;

import java.util.Locale;
import java.io.Serializable;
import javax.annotation.Generated;
import pt.ptinovacao.na.portal.core.request.engine.model.request.Request;
import java.lang.Long;
import pt.ptinovacao.na.portal.core.mpt.dispatcher.exception.DispatcherException;
import java.lang.Throwable;
import java.lang.String;
import java.util.Arrays;
import pt.ptinovacao.na.portal.core.mpt.dispatcher._private.DispatcherLogger.ASYNCH_TYPES;

/**
 * Warning this class consists of generated code.
 */
@Generated(value = "org.jboss.logging.processor.generator.model.MessageBundleImplementor", date = "2025-07-01T11:20:44+0100")
public class DispatcherMessages_$bundle implements DispatcherMessages, Serializable {
    private static final long serialVersionUID = 1L;
    protected DispatcherMessages_$bundle() {}
    public static final DispatcherMessages_$bundle INSTANCE = new DispatcherMessages_$bundle();
    protected Object readResolve() {
        return INSTANCE;
    }
    private static final Locale LOCALE = Locale.ROOT;
    protected Locale getLoggingLocale() {
        return LOCALE;
    }
    private static final String dispatchingAsynchFailure = "MPTDISPATCHER000100: Failed dispatching order event type %s for request %s due to: %s";
    protected String dispatchingAsynchFailure$str() {
        return dispatchingAsynchFailure;
    }
    @Override
    public final DispatcherException dispatchingAsynchFailure(final Throwable e, final ASYNCH_TYPES type, final Request request, final String reason) {
        final DispatcherException result = new DispatcherException(String.format(getLoggingLocale(), dispatchingAsynchFailure$str(), type, request, reason), e);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String processOrderFailure = "MPTDISPATCHER000101: Failed to process order id '%s' due to: %s";
    protected String processOrderFailure$str() {
        return processOrderFailure;
    }
    @Override
    public final DispatcherException processOrderFailure(final Throwable e, final String orderId, final String reason) {
        final DispatcherException result = new DispatcherException(String.format(getLoggingLocale(), processOrderFailure$str(), orderId, reason), e);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String mptAdapterXmlRequestGenerationFailure = "MPTDISPATCHER000102: MPT adapter failed generating request XML for order id '%s' from bulk '%d' due to: %s";
    protected String mptAdapterXmlRequestGenerationFailure$str() {
        return mptAdapterXmlRequestGenerationFailure;
    }
    @Override
    public final DispatcherException mptAdapterXmlRequestGenerationFailure(final Throwable e, final String orderId, final Long bulkId, final String reason) {
        final DispatcherException result = new DispatcherException(String.format(getLoggingLocale(), mptAdapterXmlRequestGenerationFailure$str(), orderId, bulkId, reason), e);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String orderHandlerFailure = "MPTDISPATCHER000103: Order handler failed handling order id '%s' due to: %s";
    protected String orderHandlerFailure$str() {
        return orderHandlerFailure;
    }
    @Override
    public final DispatcherException orderHandlerFailure(final Throwable e, final String orderId, final String cause) {
        final DispatcherException result = new DispatcherException(String.format(getLoggingLocale(), orderHandlerFailure$str(), orderId, cause), e);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String orderProviderHolderExclusiveKeyRemovalFailure = "MPTDISPATCHER000104: Order provider holder failed removing exclusive key for order id '%s' due to: %s";
    protected String orderProviderHolderExclusiveKeyRemovalFailure$str() {
        return orderProviderHolderExclusiveKeyRemovalFailure;
    }
    @Override
    public final DispatcherException orderProviderHolderExclusiveKeyRemovalFailure(final Throwable e, final String orderId, final String cause) {
        final DispatcherException result = new DispatcherException(String.format(getLoggingLocale(), orderProviderHolderExclusiveKeyRemovalFailure$str(), orderId, cause), e);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String orderProviderNextOrderFailure = "MPTDISPATCHER000105: Order provider failed fetching next order for bulk id '%d' due to: %s";
    protected String orderProviderNextOrderFailure$str() {
        return orderProviderNextOrderFailure;
    }
    @Override
    public final DispatcherException orderProviderNextOrderFailure(final Throwable e, final Long bulkId, final String cause) {
        final DispatcherException result = new DispatcherException(String.format(getLoggingLocale(), orderProviderNextOrderFailure$str(), bulkId, cause), e);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
}
