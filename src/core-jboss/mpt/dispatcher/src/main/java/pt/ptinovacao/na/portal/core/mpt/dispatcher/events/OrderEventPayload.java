package pt.ptinovacao.na.portal.core.mpt.dispatcher.events;

import java.util.Date;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import pt.ptinovacao.na.portal.core.request.engine.model.response.Response;
import pt.ptinovacao.na.portal.core.request.engine.client.RequestException;
import pt.ptinovacao.na.portal.commons.model.NodeType;

/**
 *
 * <AUTHOR> <michail-karama<PERSON>@telecom.pt>
 */
public class OrderEventPayload {

    private NodeType nodeType;
    private String orderKey;
    private Response orderResult;
    private String clientId;
    private Date dateCreated;
    private String orderResultString;
    private RequestException networkActivatorException;
    private int retries = 0;
    private final CountDownLatch latch = new CountDownLatch(1);
    private Predicate p;

    public NodeType getNodeType() {
        return nodeType;
    }

    public void setNodeType(NodeType nodeType) {
        this.nodeType = nodeType;
    }

    public void setOrderResultString(String orderResultString) {
        this.orderResultString = orderResultString;
    }

    public String getOrderResultString() {
        return orderResultString;
    }

    public void setRequestException(RequestException networkActivatorException) {
        this.networkActivatorException = networkActivatorException;
    }

    public RequestException getRequestException() {
        return networkActivatorException;
    }
    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientId() {
        return clientId;
    }

    public String getOrderKey() {
        return orderKey;
    }

    public void setOrderKey(String orderKey) {
        this.orderKey = orderKey;
    }

    public Response getOrderResult() {
        return orderResult;
    }

    public void setOrderResult(Response orderResult) {
        this.orderResult = orderResult;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public int getRetries() {
        return this.retries;
    }

    public int incAndGetRetries() {
        return ++this.retries;
    }

/*    public boolean waitCompletion(long timeout, TimeUnit unit) throws InterruptedException {
        return this.latch.await(timeout, unit);
    }*/

/*    public void notifyCompletion() {
        this.latch.countDown();
    } */

    public void setPredicate(Predicate p) {
        this.p = p;
    }

    public void executePredicate() {
        p.run();
    }

    @Override
    public String toString() {
        return "[OrderEvent] " + getOrderKey();
    }
}
