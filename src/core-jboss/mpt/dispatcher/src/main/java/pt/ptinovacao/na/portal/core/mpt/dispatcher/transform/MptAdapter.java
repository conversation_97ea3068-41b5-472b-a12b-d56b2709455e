package pt.ptinovacao.na.portal.core.mpt.dispatcher.transform;

import com.google.common.base.Stopwatch;
import com.google.common.base.Throwables;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.StringReader;
import java.io.UnsupportedEncodingException;

import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.xml.transform.Source;
import javax.xml.transform.TransformerException;
import javax.xml.transform.stream.StreamSource;

import pt.ptinovacao.na.portal.core.mpt.dispatcher._private.DispatcherLogger;
import pt.ptinovacao.na.portal.core.mpt.dispatcher._private.DispatcherMessages;
import pt.ptinovacao.na.portal.core.mpt.dispatcher.exception.DispatcherException;

import pt.ptinovacao.na.portal.db.mpt.ogm.entities.Column;
import pt.ptinovacao.na.portal.db.mpt.ogm.entities.Order;
import pt.ptinovacao.na.portal.db.mpt.orm.entities.Bulk;
import pt.ptinovacao.na.portal.db.mpt.orm.entities.OperCat;
import pt.ptinovacao.na.portal.db.mpt.orm.entities.go.OperCatTempt;

/**
 *
 * <AUTHOR> Laranjeira <<EMAIL>>
 */
public class MptAdapter {

    public String generateRequestXml(Order order, Bulk bulk) throws DispatcherException {
        DispatcherLogger.LOGGER.mptAdapterXmlGenerate(order.getId().toString(), bulk.getId());
        String xmlRequest;
        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            if (isOperationFromCatTemplate(bulk)) {
                xmlRequest = getOperationTemplateXmlRequest(bulk.getFkOperCat().getFkOperCatTempt(), order.getInputLine());
            } else {
                xmlRequest = requestTransform(order.getInputLine(), bulk.getFkOperCat());
            }

            if (DispatcherLogger.LOGGER.isDebugEnabled()) {
                DispatcherLogger.LOGGER.mptAdapterXmlGenerateEnd(order.getId().toString(), bulk.getId(),
                        stopwatch.stop().elapsed(TimeUnit.MILLISECONDS), xmlRequest);
            } else {
                DispatcherLogger.LOGGER.mptAdapterXmlGenerateEnd(order.getId().toString(), bulk.getId(),
                        stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
            }

            return xmlRequest;

        } catch (Throwable t) {
            throw DispatcherMessages.MESSAGES.mptAdapterXmlRequestGenerationFailure(t,
                    order.getId().toString(), bulk.getId(), Throwables.getRootCause(t).getMessage());
        }
    }

    private String getOperationTemplateXmlRequest(OperCatTempt operCatTempt, List<Column> columns) {
        String operCatTemplateXml = operCatTempt.getTemplateXml();

        for (Column column : columns) {
            operCatTemplateXml = operCatTemplateXml.replace(column.getColumnName(), column.getColumnValue());
        }

        return operCatTemplateXml;
    }

    private String requestTransform(List<Column> columns, OperCat operCat)
            throws UnsupportedEncodingException, TransformerException {
        String xmlPedidoNa, paramsXml = transformParamsXml(columns);
        Source source = new StreamSource(new StringReader(paramsXml));
        InputStream is = new ByteArrayInputStream(operCat.getTransformin().getBytes("UTF-8"));

        xmlPedidoNa = XmlTransformer.getTransformationAsString(operCat.getName(), operCat.getFkFamilyCat().getName(), true, source, is);

        return xmlPedidoNa;
    }

    /**
     * @param columns
     * @return
     */
    private String transformParamsXml(List<Column> columns) {
        StringBuilder paramsXml = new StringBuilder("<requestNA>\n");

        for (int i = 0; i < columns.size(); i++) {
            Column column = columns.get(i);
            if (column.getColumnValue() != null) {
                paramsXml.append("<param name=\"").append(column.getColumnName())
                        .append("\" position=\"").append(i).append("\">")
                        .append(column.getColumnValue()).append("</param>\n");
            } else {
                paramsXml.append("<param name=\"").append(column.getColumnName())
                        .append("\" position=\"").append(i).append("\"></param>\n");
            }
        }

        return paramsXml.append("</requestNA>").toString();
    }

    public String transformResponseNatoXML(List<Column> columns, String xmlResposta) {
        DispatcherLogger.LOGGER.mptAdapterNAResponseXmlGeneration();
        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            StringBuilder responseXml = new StringBuilder("<responseNA>\n")
                    .append(transformParamsXml(columns));

            if (xmlResposta.trim().startsWith("<?xml")) {
                xmlResposta = xmlResposta.substring(xmlResposta.indexOf("?>") + 2);
            }

            return responseXml.append("<info>").append(xmlResposta).append("</info></responseNA>")
                    .toString();
        } finally {
            DispatcherLogger.LOGGER.mptAdapterNAResponseXmlGenerationEnd(stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
        }
    }

    /**
     * returns true if the operation is configured by template
     *
     * @return
     */
    private boolean isOperationFromCatTemplate(Bulk bulk) {
        return (bulk.getFkOperCat().getFkOperCatTempt() != null);
    }
}
