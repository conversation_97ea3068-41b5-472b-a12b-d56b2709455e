package pt.ptinovacao.na.portal.core.mpt.dispatcher.model;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR> <luis-f-la<PERSON><PERSON><PERSON>@telecom.pt>
 */

public class OrderResult {
    List<OrderResultColumn> columns = new ArrayList<>();

    private String statusExecution;
    private String statusMessage;

    public OrderResult() {
    }

    public void addOrderResultColumn(OrderResultColumn column) {
        this.columns.add(column);
    }

    public List<OrderResultColumn> getColumns() {
        return columns;
    }

    public void setColumns(List<OrderResultColumn> columns) {
        this.columns = columns;
    }

    public String getStatusExecution() {
        return statusExecution;
    }

    public void setStatusExecution(String statusExecution) {
        this.statusExecution = statusExecution;
    }

    public String getStatusMessage() {
        return statusMessage;
    }

    public void setStatusMessage(String statusMessage) {
        this.statusMessage = statusMessage;
    }

    @Override
    public String toString() {
        StringBuilder str = new StringBuilder()
                .append("OrderResult [statusExecution=")
                .append(statusExecution)
                .append(", statusMessage=")
                .append(statusMessage)
                .append(", columns=\n");
        for (OrderResultColumn column : columns) {
            str.append(column.toString()).append("\n");
        }
        str.append("]");
        return str.toString();
    }

}
