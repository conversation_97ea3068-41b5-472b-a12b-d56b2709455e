package pt.ptinovacao.na.portal.core.mpt.dispatcher.provider;

import java.util.concurrent.TimeUnit;

import javax.persistence.Query;
import javax.persistence.NoResultException;

import org.bson.types.ObjectId;

import pt.ptinovacao.na.portal.db.commons.persistence.CommonDataManagerException;
import pt.ptinovacao.na.portal.db.mpt.entities.contants.NamedQueriesCat;
import pt.ptinovacao.na.portal.db.mpt.entities.contants.NamedQueriesOGM;
import pt.ptinovacao.na.portal.db.mpt.ogm.entities.Order;
import pt.ptinovacao.na.portal.db.mpt.ogm.entities.OrderTuple;
import pt.ptinovacao.na.portal.db.mpt.orm.entities.OperCat;
import pt.ptinovacao.na.portal.db.mpt.orm.entities.OrderKeyRule;
import pt.ptinovacao.na.portal.db.mpt.persistence.PersistenceOGMStore;
import pt.ptinovacao.na.portal.db.mpt.persistence.PersistenceORMStore;
import pt.ptinovacao.na.portal.db.mpt.persistence.utils.QueryIterator;
import pt.ptinovacao.na.portal.core.mpt.dispatcher._private.DispatcherLogger;
import pt.ptinovacao.na.portal.webui.restful.commons.model.ExecutionStates;

import com.google.common.util.concurrent.Monitor;
import com.google.common.base.Stopwatch;
import com.google.common.base.Throwables;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import org.infinispan.Cache;
import pt.ptinovacao.na.portal.core.mpt.dispatcher._private.DispatcherMessages;
import pt.ptinovacao.na.portal.core.mpt.dispatcher.exception.DispatcherException;

/**
 * Provides orders to the caller based mainly by a bulk identifier. Several instances may be cached
 * and build by the {@link OrderProviderHolder} witch acts as a singleton since it has a CDI
 * application scope..
 * <p>
 * Contains business logic to deal with orders keys. Its a critical business rule and not an easy
 * one by the way...
 *
 * <AUTHOR> Laranjeira <<EMAIL>>
 * <AUTHOR> Karamanos <<EMAIL>>
 */
public class OrderProvider {

    final PersistenceOGMStore ogmStore;
    final Cache<String, String> orderKeySafeTimeCache;
    final ConcurrentMap<String, OrderKeyOrders> ordersKeysOrdersMap = new ConcurrentHashMap<>();
    final long operCatId;
    private final boolean exclusive;
    private final Monitor monitor;

    public OrderProvider(long operCatId, PersistenceOGMStore ogmStore, PersistenceORMStore ormStore,
            Cache<String, String> orderKeySafeTimeCache, Monitor exclusiveMonitor) throws CommonDataManagerException {
        this.ogmStore = ogmStore;
        this.orderKeySafeTimeCache = orderKeySafeTimeCache;
        this.operCatId = operCatId;

        OperCat operCat = ormStore.findSingleOrNull(NamedQueriesCat.OPER_CAT_FIND_BY_ID, OperCat.class, "id", this.operCatId);

        this.exclusive = operCat.getOrderkeyrule() == OrderKeyRule.Exclusive;
        this.monitor = exclusiveMonitor;
    }

    /**
     * Check whether this order provider deals with exclusive orders or not. Relevant to occupy
     * resources while executing a certain bulk!
     *
     * @return true if indeed this family has the exclusive rule active, false otherwise
     */
    public boolean isExclusive() {
        return this.exclusive;
    }

    /**
     * Computes the maximum simultaneous orders that a bulk can process without restrictions. It is
     * relevant in two scenarios:
     * <ul>
     * <li>Scenario with exclusivity: Only one order key order can be processed simultaneously. So
     * it is 1*N orderKeys max</li>
     * <li>Scenario without exclusivity: Several order keys orders can be processed simultaneously.
     * So it is N*N orderKeys max</li>
     * </ul>
     *
     * Obviously they will be limited by the bulk family resources configuration!
     *
     * @param bulkId to calculate the maximum value from
     * @return the number of maximum simultaneous orders at a certain time
     */
    public int getMaxSimultaneousOrdersToProcess(long bulkId) {
        int count = 0;
        for (OrderKeyOrders value : this.ordersKeysOrdersMap.values()) {
            if (value.containsBulk(bulkId)) {
                if (this.exclusive) {
                    count++;
                } else {
                    count += value.getTotalOrdersByBulk(bulkId);
                }
            }
        }

        return count;
    }

    /**
     * Helper to check if bulk was already added to cache. The provider holder already contains a
     * local reference to this provider. Obviously, the cache is relevant to avoid resource wastage.
     *
     * @param bulkId to look for in the cache
     * @return true if any reference found, false otherwise
     */
    public boolean isAlreadyBulkAdded(long bulkId) {
        for (OrderKeyOrders value : this.ordersKeysOrdersMap.values()) {
            if (value.containsBulk(bulkId)) {
                return true;
            }
        }

        return false;
    }

    public synchronized boolean removeBulk(long bulkId) {
        boolean removed = false;

        for (OrderKeyOrders value : this.ordersKeysOrdersMap.values()) {
            if (value.containsBulk(bulkId)) {
                if (removed = value.removeBulk(bulkId)) {
                    // Also remove orderKeysOrders from this provider instance
                    if (this.ordersKeysOrdersMap.get(value.getOrderKey()).isEmpty()) {
                        this.ordersKeysOrdersMap.remove(value.getOrderKey());
                        DispatcherLogger.LOGGER.orderProviderRemoveBulk(value.getOrderKey(), bulkId);
                    }
                }
            }
        }

        return removed;
    }

    /**
     * Adds a bulk to orders key cache and batch loads all not yet started orders to it, via custom
     * iterator. Checks if the desired bulk was already inserted into this provider reference,
     * returning always the amount of not yet started orders.
     *
     * @param bulkId identifier from the ORM model
     * @param batchSize to fetch the orders by batch, important due to performance issues
     * @return the number of orders fetched by this order provider
     */
    public void addBulk(long bulkId, int batchSize) {
        DispatcherLogger.LOGGER.orderProviderAddBulk(bulkId, batchSize);

        if (!isAlreadyBulkAdded(bulkId)) {
            Stopwatch loadingTime = Stopwatch.createStarted();
            final String queryStringFormat = "db.mpt_orders.%s({ 'bulkId' : "
                    + bulkId + " , 'executionState' : '"
                    + ExecutionStates.NOT_STARTED + "'}", findNativeQuery = String.format(queryStringFormat, "find")
                    + ", { 'orderKeyRule' : 1})";
            final Query query = this.ogmStore.getEntityManager().createNativeQuery(findNativeQuery, "orderTuple"),
                    countQuery = this.ogmStore.getEntityManager().createNativeQuery(String.format(queryStringFormat, "count")
                            + ")");
            final QueryIterator<Object[]> queryIterator = new QueryIterator<>(query, batchSize,
                    ((Long) countQuery.getSingleResult()).intValue());
            int i = 0;

            while (queryIterator.hasNext()) {
                OrderTuple orderTuple = new OrderTuple(queryIterator.next());
                OrderKeyOrders oko = this.ordersKeysOrdersMap.get(orderTuple.getOrderKeyRule());

                if (oko == null) {
                    oko = new OrderKeyOrders(orderTuple.getOrderKeyRule());
                }

                oko.addOrder(orderTuple.getOrderId().toString(), bulkId);
                i++;

                // XXX: Review due to orders cache triggers: @CacheEntryModified vs @CacheEntryCreated
                this.ordersKeysOrdersMap.put(orderTuple.getOrderKeyRule(), oko);
            }
            DispatcherLogger.LOGGER.orderProviderAddBulk(i, bulkId, loadingTime.stop().elapsed(TimeUnit.MILLISECONDS));
        } else {
            DispatcherLogger.LOGGER.orderProviderAlreadyAddedBulk(bulkId);
        }
    }

    /**
     * TODO: Only makes sense in a clustered environment.
     */
    public void clusterProcessedOrdersSanitizer() {
    }

    /**
     * Searches all orders keys stored in the distributed cache, and if it has a order returns true.
     *
     * @param bulkId to search in the in mem maps
     * @return true if has an order, false otherwise
     */
    public boolean hasNext(long bulkId) {
        for (String orderKey : this.ordersKeysOrdersMap.keySet()) {
            if (this.ordersKeysOrdersMap.get(orderKey).hasNextOrder(bulkId)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Fetches the next available order given timeout to elapse, throwing InterruptedException if
     * interrupted while waiting, or returning immediately the intended.
     *
     * @param bulkId
     * @return the next available order
     * @throws DispatcherException if error occurs
     */
    public synchronized Order next(long bulkId) throws DispatcherException {
        DispatcherLogger.LOGGER.orderProviderNextOrderForBulk(bulkId);
        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            if (hasNext(bulkId)) {
                String orderId;
                if (this.exclusive) {
                    orderId = getNextOrderExclusive(bulkId);
                } else {
                    orderId = getNextOrder(bulkId);
                }

                // Fetch the order in real time to check if it is not started and if it was started by other cluster node...
                if (orderId != null) {
                    Order order = this.ogmStore.findSingleOrNull(NamedQueriesOGM.ORDER_FIND_BY_ID, Order.class, "id", new ObjectId(orderId));

                    if (!order.getExecutionState().equals(ExecutionStates.NOT_STARTED)) {
                        DispatcherLogger.LOGGER.orderProviderNextOrderForBulkMismatch(orderId);
                        OrderKeyOrders orderKeyOrders = this.ordersKeysOrdersMap.get(order.getOrderKeyRule());
                        orderKeyOrders.removeOrder(orderId, bulkId);
//                    // TODO: implement sanitizer. Only makes sense in a cluster environment
//                    this.clusterProcessedOrdersSanitizer();
                        return null;
                    }
                    return order;
                }
            }

            return null;
        } catch (Throwable t) {
            throw DispatcherMessages.MESSAGES.orderProviderNextOrderFailure(t, bulkId, Throwables.getRootCause(t).getMessage());
        } finally {
            DispatcherLogger.LOGGER.orderProviderNextOrderForBulk(bulkId, stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
        }
    }

    /**
     *
     */
    class ExclusiveOrderFetcherGuard extends Monitor.Guard {

        private final long bulkId;
        public String orderId = null;

        protected ExclusiveOrderFetcherGuard(Monitor monitor, long bulkId) {
            super(monitor);
            this.bulkId = bulkId;
        }

        @Override
        public boolean isSatisfied() {
            // Iterate all actual orders keys loaded maps and find a match
            for (String orderKeyRule : OrderProvider.this.ordersKeysOrdersMap.keySet()) {
                // Distributed cache check in case other cluster node takes the order first! Seems redundant and complex but
                // suites for multi cluster and standalone environments
                if (!OrderProvider.this.orderKeySafeTimeCache.containsKey(OrderProvider.this.operCatId
                        + orderKeyRule)
                        && OrderProvider.this.ordersKeysOrdersMap.get(orderKeyRule).hasNextOrder(this.bulkId)) {
                    Order order = null;

                    try {
                        // Database re-check that no other order is running within the same operation for the same orderkey rule
                        String queryString = "db.mpt_orders.find({ 'operCatId' : "
                                + OrderProvider.this.operCatId + " , 'executionState' : '"
                                + ExecutionStates.STARTED + "' , 'orderKeyRule' : '"
                                + orderKeyRule + "' , 'bulkId' : '"
                                + this.bulkId
                                + "'})";
                        Query query = OrderProvider.this.ogmStore.getEntityManager().createNativeQuery(queryString, Order.class);

                        try {
                            order = (Order) query.getSingleResult();
                        } catch (NoResultException e) {
                            DispatcherLogger.LOGGER.orderProviderExclusiveGuardNoResult(queryString, e.getMessage());
                        }

                        // If no order is found, means that we are ready to retrieve a valid order from the provider
                        if (order == null) {
                            this.orderId = OrderProvider.this.getNextOrder(this.bulkId);
                            OrderProvider.this.orderKeySafeTimeCache.put(OrderProvider.this.operCatId
                                    + orderKeyRule, this.orderId, 2, TimeUnit.SECONDS);
                            DispatcherLogger.LOGGER.orderProviderExclusiveGuardResult(this.bulkId, this.orderId);

                            return true;
                        }
                    } catch (Throwable t) {
                        DispatcherLogger.LOGGER.orderProviderExclusiveGuardFailure(t, Throwables.getRootCause(t).getMessage());
                    }
                }
            }

            // Falling here means that no order was found or that other order is running with the same order key rule, within
            // the same operation of course...
            DispatcherLogger.LOGGER.orderProviderExclusiveGuardNoResult(this.bulkId);

            return false;
        }
    }

    /**
     * by calling before hasNext(bulk), we know that are orders to return, but if exclusivity is
     * true we have to verify witch order can we return.
     *
     * This method validates it by checking orders already running and its orderKeys
     *
     * If there is no Order to return at the moment, it will waits the period defined for some other
     * order to finish, if that dosn't happened, InterrupedExceptioon will be Throw.
     *
     * @param bulkId
     * @return
     * @throws CommonDataManagerException
     */
    private String getNextOrderExclusive(final long bulkId) throws InterruptedException {
        ExclusiveOrderFetcherGuard hasNextOrder = new ExclusiveOrderFetcherGuard(this.monitor, bulkId);

        // TODO: needs to be reviewed in next sprint
        if (this.monitor.enterWhen(hasNextOrder, 1, TimeUnit.SECONDS)) {
            try {
                return hasNextOrder.orderId;
            } finally {
                this.monitor.leave();
            }
        } else {
            return null;
        }
    }

    private String getNextOrder(long bulkId) {

        for (String orderKey : this.ordersKeysOrdersMap.keySet()) {
            if (this.ordersKeysOrdersMap.get(orderKey).hasNextOrder(bulkId)) {
                // Check for no more orderkeysOrders
                String orderId = this.ordersKeysOrdersMap.get(orderKey).getNextOrder(bulkId);
                if (this.ordersKeysOrdersMap.get(orderKey).isEmpty()) {
                    this.ordersKeysOrdersMap.remove(orderKey);
                }

                return orderId;
            }
        }

        return null;
    }
}
