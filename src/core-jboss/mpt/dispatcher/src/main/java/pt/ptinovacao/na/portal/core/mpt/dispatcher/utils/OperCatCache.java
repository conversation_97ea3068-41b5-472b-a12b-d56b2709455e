package pt.ptinovacao.na.portal.core.mpt.dispatcher.utils;

import pt.ptinovacao.na.portal.db.commons.persistence.CommonDataManagerException;
import pt.ptinovacao.na.portal.db.mpt.entities.contants.NamedQueriesCat;
import pt.ptinovacao.na.portal.db.mpt.orm.entities.OperCat;
import pt.ptinovacao.na.portal.db.mpt.persistence.PersistenceORMStore;

import javax.ejb.Singleton;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;

@Singleton
public class OperCatCache {

    /**
     * How much time should the entry be valid in cache in milliseconds
     */
    private final Long expirationTimeMs = 600000L;

    private final Map<Long, OperCatEntry> entries = new HashMap<>();

    private final ReentrantLock lock = new ReentrantLock();

    public OperCat getOperCat(Long id, PersistenceORMStore ormStore) {
        lock.lock();
        try {
            if (!checkIfValidEntry(id)) {
                updateCache(ormStore, id);
            }
            return entries.containsKey(id) ? entries.get(id).getOperCat() : null;
        } finally {
            lock.unlock();
        }
    }

    private boolean checkIfValidEntry(Long key) {
        return entries.containsKey(key) && (System.currentTimeMillis() - entries.get(key).getEntryTimeMs() < expirationTimeMs);
    }

    // Only add the current entry because getting all database opercat entries can take some time...
    private void updateCache(PersistenceORMStore ormStore, Long id) {
        try {
            OperCat operCat = ormStore.findSingle(NamedQueriesCat.OPER_CAT_FIND_BY_ID, OperCat.class, "id", id);
            entries.put(id, new OperCatEntry(operCat, System.currentTimeMillis()));
        } catch (CommonDataManagerException e) {
            // Something wrong happened while connecting to database, refreshing entry time to use cache again
            if(entries.containsKey(id)) entries.get(id).setEntryTimeMs(System.currentTimeMillis());
        }
    }

    private static class OperCatEntry {
        private final OperCat operCat;
        private Long entryTimeMs;

        public OperCatEntry(OperCat operCat, Long entryTimeMs) {
            this.operCat = operCat;
            this.entryTimeMs = entryTimeMs;
        }

        public OperCat getOperCat() {
            return operCat;
        }

        public Long getEntryTimeMs() {
            return entryTimeMs;
        }

        public void setEntryTimeMs(Long entryTimeMs) {
            this.entryTimeMs = entryTimeMs;
        }
    }
}
