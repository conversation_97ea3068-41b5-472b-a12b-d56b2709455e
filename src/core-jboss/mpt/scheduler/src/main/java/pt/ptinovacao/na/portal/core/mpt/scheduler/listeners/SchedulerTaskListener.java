package pt.ptinovacao.na.portal.core.mpt.scheduler.listeners;

import com.google.common.base.Optional;

import java.util.concurrent.Future;

import javax.enterprise.concurrent.ManagedExecutorService;
import javax.enterprise.concurrent.ManagedTaskListener;
import javax.enterprise.event.Event;

import javax.inject.Inject;
import javax.inject.Named;

import pt.ptinovacao.na.portal.core.mpt.scheduler.AbstractSchedulerTask;
import pt.ptinovacao.na.portal.core.mpt.scheduler.TaskLifecycle;
import pt.ptinovacao.na.portal.core.mpt.scheduler._private.SchedulerLogger;
import pt.ptinovacao.na.portal.core.mpt.scheduler.event.LifecycleEvent;

/**
 * If replicated scheduler cache is somehow modified then, then a CDI event should arrive here to
 * allow the task safe cancellation.
 *
 * <AUTHOR> <<EMAIL>>
 * @see ManagedTaskListener
 */
@Named
public class SchedulerTaskListener implements ManagedTaskListener {

    @Inject
    private Event<LifecycleEvent> updateTaskLifecycleEv;

    @Override
    public void taskSubmitted(Future<?> future, ManagedExecutorService executor, Object task) {
        SchedulerLogger.LOGGER.schedulerTaskListenerSubmitted(task.toString());

        this.registerStateTransition(TaskLifecycle.TaskStatus.SUBMITTED, task, future);
    }

    @Override
    public void taskAborted(Future<?> future, ManagedExecutorService executor, Object task, Throwable exception) {
        SchedulerLogger.LOGGER.schedulerTaskListenerAborted(task.toString());

        this.registerStateTransition(TaskLifecycle.TaskStatus.ABORTED, task, future);
    }

    @Override
    public void taskDone(Future<?> future, ManagedExecutorService executor,
            Object task, Throwable exception) {
        SchedulerLogger.LOGGER.schedulerTaskListenerDone(task.toString());

        this.registerStateTransition(TaskLifecycle.TaskStatus.DONE, task, future);
    }

    @Override
    public void taskStarting(Future<?> future, ManagedExecutorService executor, Object task) {
        SchedulerLogger.LOGGER.schedulerTaskListenerStarting(task.toString());

        this.registerStateTransition(TaskLifecycle.TaskStatus.STARTING, task, future);
    }

    private Optional<Long> fetchBulkIdFromTask(Object task) {
        if (task instanceof AbstractSchedulerTask) {
            return Optional.fromNullable(AbstractSchedulerTask.class.cast(task).getBulkId());
        }

        return Optional.absent();
    }

    private void registerStateTransition(TaskLifecycle.TaskStatus status, Object task, Future<?> future) {
        if (this.fetchBulkIdFromTask(task).isPresent()) {
            this.updateTaskLifecycleEv.fire(new LifecycleEvent(this.fetchBulkIdFromTask(task).get(), status, future, task));
        }
    }
}
