package pt.ptinovacao.na.portal.core.mpt.scheduler.event;

import com.google.common.base.MoreObjects;

import pt.ptinovacao.na.portal.webui.restful.mpt.commons.schedules.ScheduleEventList;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
public class ScheduleEvent {

    private final Long bulkId;
    private ScheduleEventList scheduleEventList;

    public ScheduleEvent(Long bulkId) {
        this.bulkId = bulkId;
    }

    public ScheduleEvent(Long bulkId, ScheduleEventList scheduleEventList) {
        this.bulkId = bulkId;
        this.scheduleEventList = scheduleEventList;
    }

    public Long getBulkId() {
        return bulkId;
    }

    public ScheduleEventList getScheduleEventList() {
        return scheduleEventList;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .omitNullValues()
                .addValue(this.bulkId)
                .addValue(this.scheduleEventList)
                .toString();
    }
}
