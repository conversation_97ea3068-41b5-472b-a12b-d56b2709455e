package pt.ptinovacao.na.portal.core.mpt.scheduler._private;

import java.util.Locale;
import java.io.Serializable;
import javax.annotation.Generated;
import pt.ptinovacao.na.portal.core.mpt.scheduler.exceptions.SchedulerException;
import java.lang.Throwable;
import java.lang.String;
import java.util.Arrays;

/**
 * Warning this class consists of generated code.
 */
@Generated(value = "org.jboss.logging.processor.generator.model.MessageBundleImplementor", date = "2025-07-01T11:20:44+0100")
public class SchedulerMessages_$bundle implements SchedulerMessages, Serializable {
    private static final long serialVersionUID = 1L;
    protected SchedulerMessages_$bundle() {}
    public static final SchedulerMessages_$bundle INSTANCE = new SchedulerMessages_$bundle();
    protected Object readResolve() {
        return INSTANCE;
    }
    private static final Locale LOCALE = Locale.ROOT;
    protected Locale getLoggingLocale() {
        return LOCALE;
    }
    private static final String schedulerToRangeWrapperEmptyError = "MPTSCHEDULER000101: No scheduling events found from schedule event list iterator";
    protected String schedulerToRangeWrapperEmptyError$str() {
        return schedulerToRangeWrapperEmptyError;
    }
    @Override
    public final SchedulerException schedulerToRangeWrapperEmptyError() {
        final SchedulerException result = new SchedulerException(String.format(getLoggingLocale(), schedulerToRangeWrapperEmptyError$str()));
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String schedulerToRangeWrapperMismatch = "MPTSCHEDULER000102: Iterator Mismatch... Expected STOP event while iterating schedule event list";
    protected String schedulerToRangeWrapperMismatch$str() {
        return schedulerToRangeWrapperMismatch;
    }
    @Override
    public final SchedulerException schedulerToRangeWrapperMismatch() {
        final SchedulerException result = new SchedulerException(String.format(getLoggingLocale(), schedulerToRangeWrapperMismatch$str()));
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String failedGetScheduleEventListFromRequest = "MPTSCHEDULER000103: Failed to get and parse schedule event list from request: %s";
    protected String failedGetScheduleEventListFromRequest$str() {
        return failedGetScheduleEventListFromRequest;
    }
    @Override
    public final SchedulerException failedGetScheduleEventListFromRequest(final Throwable t, final String request) {
        final SchedulerException result = new SchedulerException(String.format(getLoggingLocale(), failedGetScheduleEventListFromRequest$str(), request));
        result.initCause(t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String failedSchedulingRequest = "MPTSCHEDULER000104: Failed to schedule request: %s";
    protected String failedSchedulingRequest$str() {
        return failedSchedulingRequest;
    }
    @Override
    public final SchedulerException failedSchedulingRequest(final Throwable t, final String request) {
        final SchedulerException result = new SchedulerException(String.format(getLoggingLocale(), failedSchedulingRequest$str(), request));
        result.initCause(t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String failedReschedulingRequest = "MPTSCHEDULER000105: Failed to REschedule request: %s";
    protected String failedReschedulingRequest$str() {
        return failedReschedulingRequest;
    }
    @Override
    public final SchedulerException failedReschedulingRequest(final Throwable t, final String request) {
        final SchedulerException result = new SchedulerException(String.format(getLoggingLocale(), failedReschedulingRequest$str(), request));
        result.initCause(t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String failedCancellingSchedulesFromRequest = "MPTSCHEDULER000106: Failed to cancel schedules from request: %s";
    protected String failedCancellingSchedulesFromRequest$str() {
        return failedCancellingSchedulesFromRequest;
    }
    @Override
    public final SchedulerException failedCancellingSchedulesFromRequest(final Throwable t, final String request) {
        final SchedulerException result = new SchedulerException(String.format(getLoggingLocale(), failedCancellingSchedulesFromRequest$str(), request));
        result.initCause(t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String failedRegisteringLifeCycle = "MPTSCHEDULER000107: Failed registering scheduler lifecycle for task: %s";
    protected String failedRegisteringLifeCycle$str() {
        return failedRegisteringLifeCycle;
    }
    @Override
    public final SchedulerException failedRegisteringLifeCycle(final Throwable t, final String task) {
        final SchedulerException result = new SchedulerException(String.format(getLoggingLocale(), failedRegisteringLifeCycle$str(), task));
        result.initCause(t);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String failedIteratingEventList = "MPTSCHEDULER000108: Iterator Mismatch... Expected STOP event while iterating schedule event list";
    protected String failedIteratingEventList$str() {
        return failedIteratingEventList;
    }
    @Override
    public final SchedulerException failedIteratingEventList() {
        final SchedulerException result = new SchedulerException(String.format(getLoggingLocale(), failedIteratingEventList$str()));
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String failedEventIteratorEmpty = "MPTSCHEDULER000109: No scheduling events found from schedule event list iterator";
    protected String failedEventIteratorEmpty$str() {
        return failedEventIteratorEmpty;
    }
    @Override
    public final SchedulerException failedEventIteratorEmpty() {
        final SchedulerException result = new SchedulerException(String.format(getLoggingLocale(), failedEventIteratorEmpty$str()));
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
}
