package pt.ptinovacao.na.portal.core.mpt.scheduler._private;

import java.util.Locale;
import java.io.Serializable;
import javax.annotation.Generated;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.schedules.ScheduleEvent.ACTION;
import org.jboss.logging.DelegatingBasicLogger;
import org.jboss.logging.BasicLogger;
import java.lang.Long;
import java.lang.String;
import org.jboss.logging.Logger;
import java.util.Date;


import static org.jboss.logging.Logger.Level.ERROR;
import static org.jboss.logging.Logger.Level.INFO;
import static org.jboss.logging.Logger.Level.DEBUG;
import static org.jboss.logging.Logger.Level.WARN;

/**
 * Warning this class consists of generated code.
 */
@Generated(value = "org.jboss.logging.processor.generator.model.MessageLoggerImplementor", date = "2025-07-01T11:20:44+0100")
public class SchedulerLogger_$logger extends DelegatingBasicLogger implements SchedulerLogger, BasicLogger, Serializable {
    private static final long serialVersionUID = 1L;
    private static final String FQCN = SchedulerLogger_$logger.class.getName();
    public SchedulerLogger_$logger(final Logger log) {
        super(log);
    }
    private static final Locale LOCALE = Locale.ROOT;
    protected Locale getLoggingLocale() {
        return LOCALE;
    }
    @Override
    public final void triggerGenerated(final Long bulkId, final Date date, final ACTION action) {
        super.log.logf(FQCN, INFO, null, triggerGenerated$str(), bulkId, date, action);
    }
    private static final String triggerGenerated = "MPTSCHEDULER000001: Trigger for bulk '%s' will calculate next run time [%s]. Action type: %s";
    protected String triggerGenerated$str() {
        return triggerGenerated;
    }
    @Override
    public final void triggerLastExecutionInfo(final String lastTriggerExecutionInfo) {
        super.log.logf(FQCN, INFO, null, triggerLastExecutionInfo$str(), lastTriggerExecutionInfo);
    }
    private static final String triggerLastExecutionInfo = "MPTSCHEDULER000002: Trigger Last execution: %s";
    protected String triggerLastExecutionInfo$str() {
        return triggerLastExecutionInfo;
    }
    @Override
    public final void triggerHasNotRunYet(final Long bulkId, final String scheduleEventListString) {
        super.log.logf(FQCN, INFO, null, triggerHasNotRunYet$str(), bulkId, scheduleEventListString);
    }
    private static final String triggerHasNotRunYet = "MPTSCHEDULER000003: The task for bulkId '%s' has not run yet. It is in scheduled state: %s";
    protected String triggerHasNotRunYet$str() {
        return triggerHasNotRunYet;
    }
    @Override
    public final void triggerHadRun(final Long bulkId, final String dateString) {
        super.log.logf(FQCN, INFO, null, triggerHadRun$str(), bulkId, dateString);
    }
    private static final String triggerHadRun = "MPTSCHEDULER000004: The task for bulkId '%s' had a previous run and finished at %s...";
    protected String triggerHadRun$str() {
        return triggerHadRun;
    }
    @Override
    public final void startingSchedulerSingleton(final String typeName) {
        super.log.logf(FQCN, INFO, null, startingSchedulerSingleton$str(), typeName);
    }
    private static final String startingSchedulerSingleton = "MPTSCHEDULER000005: Starting up [MPT SCHEDULER] Singleton... Building a Scheduler in %s mode!";
    protected String startingSchedulerSingleton$str() {
        return startingSchedulerSingleton;
    }
    @Override
    public final void stoppingSchedulerSingleton() {
        super.log.logf(FQCN, INFO, null, stoppingSchedulerSingleton$str());
    }
    private static final String stoppingSchedulerSingleton = "MPTSCHEDULER000006: Stopping [SCHEDULER] Singleton...";
    protected String stoppingSchedulerSingleton$str() {
        return stoppingSchedulerSingleton;
    }
    @Override
    public final void initializingSchedulerCacheListener() {
        super.log.logf(FQCN, INFO, null, initializingSchedulerCacheListener$str());
    }
    private static final String initializingSchedulerCacheListener = "MPTSCHEDULER000007: Initializing [SCHEDULER Cache Listener]";
    protected String initializingSchedulerCacheListener$str() {
        return initializingSchedulerCacheListener;
    }
    @Override
    public final void initializedSchedulerCacheListener() {
        super.log.logf(FQCN, INFO, null, initializedSchedulerCacheListener$str());
    }
    private static final String initializedSchedulerCacheListener = "MPTSCHEDULER000008: Initialized [SCHEDULER Cache Listener]";
    protected String initializedSchedulerCacheListener$str() {
        return initializedSchedulerCacheListener;
    }
    @Override
    public final void addedSchedulerCacheListener() {
        super.log.logf(FQCN, INFO, null, addedSchedulerCacheListener$str());
    }
    private static final String addedSchedulerCacheListener = "MPTSCHEDULER000009: Fresh instance of a Scheduler Cache Listener was added to Scheduler Cache";
    protected String addedSchedulerCacheListener$str() {
        return addedSchedulerCacheListener;
    }
    @Override
    public final void triggerWindowAbsence(final Long bulkId) {
        super.log.logf(FQCN, WARN, null, triggerWindowAbsence$str(), bulkId);
    }
    private static final String triggerWindowAbsence = "MPTSCHEDULER000010: First run with no future windows for bulkId '%s'! This is a abnormal situation and should be investigated...";
    protected String triggerWindowAbsence$str() {
        return triggerWindowAbsence;
    }
    @Override
    public final void noMoreTriggers(final Long bulkId) {
        super.log.logf(FQCN, WARN, null, noMoreTriggers$str(), bulkId);
    }
    private static final String noMoreTriggers = "MPTSCHEDULER000011: No more triggers found!!! Trigger for bulkId '%s' will return null, meaning task end of execution...";
    protected String noMoreTriggers$str() {
        return noMoreTriggers;
    }
    @Override
    public final void previousTaskAbnormalError(final Long bulkId) {
        super.log.logf(FQCN, ERROR, null, previousTaskAbnormalError$str(), bulkId);
    }
    private static final String previousTaskAbnormalError = "MPTSCHEDULER000012: Something went wrong with the previous execution for bulkId '%s'. Task was found in a abnormal STATE!";
    protected String previousTaskAbnormalError$str() {
        return previousTaskAbnormalError;
    }
    @Override
    public final void cacheListenerModificationTrigger(final Long bulkId, final java.util.Set<?> scheduleEvents) {
        super.log.logf(FQCN, INFO, null, cacheListenerModificationTrigger$str(), bulkId, scheduleEvents);
    }
    private static final String cacheListenerModificationTrigger = "MPTSCHEDULER000013: Scheduler listener cache detected modifications for bulkId '%s'. Canceling all schedules on all nodes... Cancelation returned the following scheduled events: %s";
    protected String cacheListenerModificationTrigger$str() {
        return cacheListenerModificationTrigger;
    }
    @Override
    public final void registeringTaskLifecycle(final String task, final String trigger) {
        super.log.logf(FQCN, INFO, null, registeringTaskLifecycle$str(), task, trigger);
    }
    private static final String registeringTaskLifecycle = "MPTSCHEDULER000014: Registering lifecycle task to scheduler: TASK [%s], TRIGGER [%s]";
    protected String registeringTaskLifecycle$str() {
        return registeringTaskLifecycle;
    }
    @Override
    public final void updatingTaskLifecycle(final Long bulkId, final String status, final String task) {
        super.log.logf(FQCN, INFO, null, updatingTaskLifecycle$str(), bulkId, status, task);
    }
    private static final String updatingTaskLifecycle = "MPTSCHEDULER000015: Updating task lifecycle from bulk '%s' to a status of %s. Task info [%s]";
    protected String updatingTaskLifecycle$str() {
        return updatingTaskLifecycle;
    }
    @Override
    public final void schedulingTriggerTask(final String task, final String trigger) {
        super.log.logf(FQCN, INFO, null, schedulingTriggerTask$str(), task, trigger);
    }
    private static final String schedulingTriggerTask = "MPTSCHEDULER000016: Scheduling TASK [%s] with the following associated TRIGGER [%s]";
    protected String schedulingTriggerTask$str() {
        return schedulingTriggerTask;
    }
    @Override
    public final void schedulingTriggerTaskRunAt(final Long bulkId, final String date) {
        super.log.logf(FQCN, INFO, null, schedulingTriggerTaskRunAt$str(), bulkId, date);
    }
    private static final String schedulingTriggerTaskRunAt = "MPTSCHEDULER000017: Trigger for BulkId '%s' was scheduled to run at '%s'";
    protected String schedulingTriggerTaskRunAt$str() {
        return schedulingTriggerTaskRunAt;
    }
    @Override
    public final void schedulingTriggerSkip(final Long bulkId, final boolean skip) {
        super.log.logf(FQCN, INFO, null, schedulingTriggerSkip$str(), bulkId, skip);
    }
    private static final String schedulingTriggerSkip = "MPTSCHEDULER000018: Trigger for BulkId '%s' in skipRun() returned '%s'";
    protected String schedulingTriggerSkip$str() {
        return schedulingTriggerSkip;
    }
    @Override
    public final void schedulerCacheListener(final Long bulkId, final String type) {
        super.log.logf(FQCN, INFO, null, schedulerCacheListener$str(), bulkId, type);
    }
    private static final String schedulerCacheListener = "MPTSCHEDULER000019: Scheduler cache listener triggered by a CACHE MODIFICATION from bulk %s of type %s";
    protected String schedulerCacheListener$str() {
        return schedulerCacheListener;
    }
    @Override
    public final void schedulerCacheListenerFire(final Long bulkId) {
        super.log.logf(FQCN, INFO, null, schedulerCacheListenerFire$str(), bulkId);
    }
    private static final String schedulerCacheListenerFire = "MPTSCHEDULER000020: Scheduler cache listener fired a scheduler event from bulk %s";
    protected String schedulerCacheListenerFire$str() {
        return schedulerCacheListenerFire;
    }
    @Override
    public final void schedulerTaskListenerSubmitted(final String task) {
        super.log.logf(FQCN, INFO, null, schedulerTaskListenerSubmitted$str(), task);
    }
    private static final String schedulerTaskListenerSubmitted = "MPTSCHEDULER000021: Scheduler task listener listened the following submitted task: %s";
    protected String schedulerTaskListenerSubmitted$str() {
        return schedulerTaskListenerSubmitted;
    }
    @Override
    public final void schedulerTaskListenerAborted(final String task) {
        super.log.logf(FQCN, INFO, null, schedulerTaskListenerAborted$str(), task);
    }
    private static final String schedulerTaskListenerAborted = "MPTSCHEDULER000022: Scheduler task listener listened the following aborted task: %s";
    protected String schedulerTaskListenerAborted$str() {
        return schedulerTaskListenerAborted;
    }
    @Override
    public final void schedulerTaskListenerDone(final String task) {
        super.log.logf(FQCN, INFO, null, schedulerTaskListenerDone$str(), task);
    }
    private static final String schedulerTaskListenerDone = "MPTSCHEDULER000023: Scheduler task listener listened the following done task: %s";
    protected String schedulerTaskListenerDone$str() {
        return schedulerTaskListenerDone;
    }
    @Override
    public final void schedulerTaskListenerStarting(final String task) {
        super.log.logf(FQCN, INFO, null, schedulerTaskListenerStarting$str(), task);
    }
    private static final String schedulerTaskListenerStarting = "MPTSCHEDULER000024: Scheduler task listener listened the following starting task: %s";
    protected String schedulerTaskListenerStarting$str() {
        return schedulerTaskListenerStarting;
    }
    @Override
    public final void utilsRangeContainsDate(final String a, final String b, final String c, final String d, final boolean result) {
        super.log.logf(FQCN, INFO, null, utilsRangeContainsDate$str(), a, b, c, d, result);
    }
    private static final String utilsRangeContainsDate = "MPTSCHEDULER000025: Checking if range contains date %s >= %s AND %s <= %s == %s";
    protected String utilsRangeContainsDate$str() {
        return utilsRangeContainsDate;
    }
    @Override
    public final void utilsRangeContainsDateLowerCheck(final boolean result) {
        super.log.logf(FQCN, DEBUG, null, utilsRangeContainsDateLowerCheck$str(), result);
    }
    private static final String utilsRangeContainsDateLowerCheck = "MPTSCHEDULER000026: Checking if range contains date lowerbound dateToCheck >= rangeToCheck.lowerEndpoint() = %s";
    protected String utilsRangeContainsDateLowerCheck$str() {
        return utilsRangeContainsDateLowerCheck;
    }
    @Override
    public final void utilsRangeContainsDateUpperCheck(final boolean result) {
        super.log.logf(FQCN, DEBUG, null, utilsRangeContainsDateUpperCheck$str(), result);
    }
    private static final String utilsRangeContainsDateUpperCheck = "MPTSCHEDULER000027: Checking if range contains date upperbound dateToCheck <= rangeToCheck.upperEndpoint() = %s";
    protected String utilsRangeContainsDateUpperCheck$str() {
        return utilsRangeContainsDateUpperCheck;
    }
    @Override
    public final void utilsRangeContainsDateNoUpperBound() {
        super.log.logf(FQCN, WARN, null, utilsRangeContainsDateNoUpperBound$str());
    }
    private static final String utilsRangeContainsDateNoUpperBound = "MPTSCHEDULER000028: Checking if range contains date found no UpperBound!!!";
    protected String utilsRangeContainsDateNoUpperBound$str() {
        return utilsRangeContainsDateNoUpperBound;
    }
    @Override
    public final void utilsRangeContainsDateResult(final String date, final String range, final boolean result) {
        super.log.logf(FQCN, INFO, null, utilsRangeContainsDateResult$str(), date, range, result);
    }
    private static final String utilsRangeContainsDateResult = "MPTSCHEDULER000029: Checking if range contains date returned that date %s is inside range %s = %s";
    protected String utilsRangeContainsDateResult$str() {
        return utilsRangeContainsDateResult;
    }
    @Override
    public final void utilsFetchNextDateRangeFirstTime() {
        super.log.logf(FQCN, INFO, null, utilsFetchNextDateRangeFirstTime$str());
    }
    private static final String utilsFetchNextDateRangeFirstTime = "MPTSCHEDULER000030: Fetch next date range found that its the first time window definition... Wrapping range...";
    protected String utilsFetchNextDateRangeFirstTime$str() {
        return utilsFetchNextDateRangeFirstTime;
    }
    @Override
    public final void utilsFetchNextDateRangeFirstTimeWrappedRange(final String range) {
        super.log.logf(FQCN, INFO, null, utilsFetchNextDateRangeFirstTimeWrappedRange$str(), range);
    }
    private static final String utilsFetchNextDateRangeFirstTimeWrappedRange = "MPTSCHEDULER000031: Fetch next date range wrappep range: %s";
    protected String utilsFetchNextDateRangeFirstTimeWrappedRange$str() {
        return utilsFetchNextDateRangeFirstTimeWrappedRange;
    }
    @Override
    public final void utilsFetchNextDateRangePeek(final String action) {
        super.log.logf(FQCN, DEBUG, null, utilsFetchNextDateRangePeek$str(), action);
    }
    private static final String utilsFetchNextDateRangePeek = "MPTSCHEDULER000032: Fetch next date range is peeking the following action [%s]";
    protected String utilsFetchNextDateRangePeek$str() {
        return utilsFetchNextDateRangePeek;
    }
    @Override
    public final void utilsFetchNextDateRangeWrapping() {
        super.log.logf(FQCN, INFO, null, utilsFetchNextDateRangeWrapping$str());
    }
    private static final String utilsFetchNextDateRangeWrapping = "MPTSCHEDULER000033: Fetch next date range evaluated that start EVENT is not present inside previous known window... Wrapping range...";
    protected String utilsFetchNextDateRangeWrapping$str() {
        return utilsFetchNextDateRangeWrapping;
    }
    @Override
    public final void utilsFetchNextDateRangeWrapped(final String range) {
        super.log.logf(FQCN, INFO, null, utilsFetchNextDateRangeWrapped$str(), range);
    }
    private static final String utilsFetchNextDateRangeWrapped = "MPTSCHEDULER000034: Fetch next date range wrapped range %s";
    protected String utilsFetchNextDateRangeWrapped$str() {
        return utilsFetchNextDateRangeWrapped;
    }
    @Override
    public final void utilsFetchNextDateRangePeeking() {
        super.log.logf(FQCN, DEBUG, null, utilsFetchNextDateRangePeeking$str());
    }
    private static final String utilsFetchNextDateRangePeeking = "MPTSCHEDULER000035: Fetch next date range is peeking next event...";
    protected String utilsFetchNextDateRangePeeking$str() {
        return utilsFetchNextDateRangePeeking;
    }
}
