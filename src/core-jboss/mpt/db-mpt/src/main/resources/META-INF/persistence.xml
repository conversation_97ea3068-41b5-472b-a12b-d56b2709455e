<?xml version="1.0" encoding="UTF-8"?>
<persistence version="2.1" xmlns="http://xmlns.jcp.org/xml/ns/persistence"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/persistence http://xmlns.jcp.org/xml/ns/persistence/persistence_2_1.xsd">
  <persistence-unit name="MPT-ORM-PU" transaction-type="JTA">
    <provider>org.hibernate.jpa.HibernatePersistenceProvider</provider>
    <jta-data-source>java:/jdbc/NAPortalMPTDS</jta-data-source>
    <!-- FOR HIBERNATE OGM UNCOMMENT -->
    <!--<provider>org.hibernate.ogm.jpa.HibernateOgmPersistence</provider>-->
    <class>pt.ptinovacao.na.portal.db.mpt.orm.entities.Bulk</class>
    <class>pt.ptinovacao.na.portal.db.mpt.orm.entities.FamilyCat</class>
    <class>pt.ptinovacao.na.portal.db.mpt.orm.entities.BulkTrans</class>
    <class>pt.ptinovacao.na.portal.db.mpt.orm.entities.HdrCat</class>
    <class>pt.ptinovacao.na.portal.db.mpt.orm.entities.OperCat</class>
    <class>pt.ptinovacao.na.portal.db.mpt.orm.entities.RespHdrCat</class>
    <class>pt.ptinovacao.na.portal.db.mpt.orm.entities.go.OperCatChars</class>
    <class>pt.ptinovacao.na.portal.db.mpt.orm.entities.go.OperCatTempt</class>
    <class>pt.ptinovacao.na.portal.db.mpt.orm.entities.go.OperCatRfs</class>
    <class>pt.ptinovacao.na.portal.db.mpt.orm.entities.go.OperCatArea</class>
    <class>pt.ptinovacao.na.portal.db.mpt.orm.entities.go.OperCatAttrs</class>
    <exclude-unlisted-classes>true</exclude-unlisted-classes>
    <shared-cache-mode>ENABLE_SELECTIVE</shared-cache-mode>
    <properties>
      <!--<property name="javax.persistence.jdbc.url" value="*********************************************"/>
            <property name="javax.persistence.jdbc.password" value="mpt"/>
            <property name="javax.persistence.jdbc.driver" value="oracle.jdbc.OracleDriver"/>
            <property name="javax.persistence.jdbc.user" value="mpt"/>-->
      <property name="hibernate.hbm2ddl.auto" value="${hibernate.hbm2ddl.auto:validate}"/>
      <!--<property name="hibernate.generate_statistics" value="true"/>-->
      <!--<property name="hibernate.show_sql" value="true"/>-->
      <!--<property name="hibernate.use_sql_comments" value="true"/>-->
      <!--- ************************* Caching properties  ************************************** -->
      <!--<property name="hibernate.cache.region.factory_class" value="org.jboss.as.jpa.hibernate4.infinispan.InfinispanRegionFactory"/>-->
      <!--<property name="hibernate.cache.infinispan.cachemanager" value="java:jboss/infinispan/container/hibernate"/>-->
      <!-- datasource for Hibernate or JPA property is not needed -->
      <!--<property name="hibernate.transaction.manager_lookup_class" value="org.hibernate.transaction.JBossTransactionManagerLookup"/>-->
      <property name="hibernate.cache.use_second_level_cache" value="true"/>
      <property name="hibernate.cache.use_query_cache" value="true"/>
      <!--<property name="hibernate.cache.use_minimal_puts" value="true"/>-->
      <!--- ************************* FOR HIBERNATE OGM UNCOMMENT ****************************** -->
      <!--      <property name="jboss.as.jpa.classtransformer" value="false" />
            <property name="jboss.as.jpa.adapterModule" value="org.jboss.as.jpa.hibernate:4"/>
            Check: https://docs.jboss.org/author/display/WFLY8/JPA+Reference+Guide#JPAReferenceGuide-UpdateyourPersistence.xmlforHibernate4.3.0
            -->
    </properties>
  </persistence-unit>
  <persistence-unit name="MPT-OGM-PU" transaction-type="JTA">
    <provider>org.hibernate.ogm.jpa.HibernateOgmPersistence</provider>
    <class>pt.ptinovacao.na.portal.db.mpt.ogm.entities.Order</class>
    <class>pt.ptinovacao.na.portal.db.mpt.ogm.entities.Column</class>
    <class>pt.ptinovacao.na.portal.db.mpt.ogm.entities.Configuration</class>
    <class>pt.ptinovacao.na.portal.db.mpt.ogm.entities.go.Details</class>
    <class>pt.ptinovacao.na.portal.db.mpt.ogm.entities.go.Operation</class>
    <exclude-unlisted-classes>true</exclude-unlisted-classes>
    <!--<shared-cache-mode>ENABLE_SELECTIVE</shared-cache-mode>-->
    <properties>
      <!--- ************************* Caching properties MANDATORY FOR OGM!!! ************************************** -->
      <!--            <property name="hibernate.cache.region.factory_class" value="org.jboss.as.jpa.hibernate4.infinispan.InfinispanRegionFactory"/>
            <property name="hibernate.cache.infinispan.cachemanager" value="java:jboss/infinispan/container/hibernate"/>-->
      <!-- MONGODB stuff -->
      <!--            <property name="hibernate.ogm.datastore.host" value="*************" />
            <property name="hibernate.ogm.datastore.port" value="27017" />
            <property name="hibernate.ogm.datastore.provider" value="mongodb" />
            <property name="hibernate.ogm.datastore.database" value="naportal.mpt" />
            <property name="hibernate.ogm.datastore.create_database" value="true" />-->
      <!-- Defines the timeout used by the driver when the connection to the MongoDB instance is initiated. This configuration is expressed in milliseconds. The default value is 5000. -->
      <!--<property name="hibernate.ogm.mongodb.connection_timeout" value="6000" />-->
      <!-- STATS -->
      <property name="hibernate.hbm2ddl.auto" value="none"/>
      <property name="hibernate.generate_statistics" value="false"/>
      <!--<property name="hibernate.cache.use_second_level_cache" value="true"/>-->
      <!--<property name="hibernate.cache.use_query_cache" value="true"/>-->
      <!--<property name="hibernate.cache.use_minimal_puts" value="true"/>-->
      <!--- ************************* FOR HIBERNATE OGM UNCOMMENT ****************************** -->
      <!--<property name="hibernate.transaction.manager_lookup_class" value="org.hibernate.transaction.JTATransactionFactory"/>-->
      <!--<property name="hibernate.transaction.jta.platform" value="org.hibernate.service.jta.platform.internal.JBossAppServerJtaPlatform" />-->
      <property name="jboss.as.jpa.classtransformer" value="false"/>
      <property name="jboss.as.jpa.providerModule" value="org.hibernate.ogm"/>
      <!-- ERROR in WILDFLY deployment. Works on JBOSS 7.x! -->
      <!--<property name="jboss.as.jpa.adapterModule" value="org.jboss.as.jpa.hibernate:4"/>-->
    </properties>
  </persistence-unit>
</persistence>
