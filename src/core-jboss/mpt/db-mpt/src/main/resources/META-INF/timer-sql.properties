create-table=CREATE TABLE JBOSS_EJB_TIMER (ID VARCHAR PRIMARY KEY NOT NULL, TIMED_OBJECT_ID VARCHAR NOT NULL, INITIAL_DATE TIMESTAMP, REPEAT_INTERVAL BIGINT, NEXT_DATE TIMESTAMP, PREVIOUS_RUN TIMESTAMP, PRIMARY_KEY VARCHAR, INFO VARCHAR, TIMER_STATE VARCHAR, SCHEDULE_EXPR_SECOND VARCHAR, SCHEDULE_EXPR_MINUTE VARCHAR, SCHEDULE_EXPR_HOUR VARCHAR,SCHEDULE_EXPR_DAY_OF_WEEK VARCHAR, SCHEDULE_EXPR_DAY_OF_MONTH VARCHAR, SCHEDULE_EXPR_MONTH VARCHAR, SCHEDULE_EXPR_YEAR VARCHAR, SCHEDULE_EXPR_START_DATE VARCHAR, SCHEDULE_EXPR_END_DATE VARCHAR, SCHEDULE_EXPR_TIMEZONE VARCHAR, AUTO_TIMER BOOLEAN, TIMEOUT_METHOD_DECLARING_CLASS VARCHAR, TIMEOUT_METHOD_NAME VARCHAR, TIMEOUT_METHOD_DESCRIPTOR VARCHAR, CALENDAR_TIMER BOOLEAN, PARTITION_NAME VARCHAR NOT NULL, NODE_NAME VARCHAR)
create-timer=INSERT INTO JBOSS_EJB_TIMER (ID, TIMED_OBJECT_ID, INITIAL_DATE, REPEAT_INTERVAL, NEXT_DATE, PREVIOUS_RUN, PRIMARY_KEY, INFO, TIMER_STATE, SCHEDULE_EXPR_SECOND, SCHEDULE_EXPR_MINUTE, SCHEDULE_EXPR_HOUR, SCHEDULE_EXPR_DAY_OF_WEEK, SCHEDULE_EXPR_DAY_OF_MONTH, SCHEDULE_EXPR_MONTH, SCHEDULE_EXPR_YEAR, SCHEDULE_EXPR_START_DATE, SCHEDULE_EXPR_END_DATE, SCHEDULE_EXPR_TIMEZONE, AUTO_TIMER, TIMEOUT_METHOD_DECLARING_CLASS, TIMEOUT_METHOD_NAME, TIMEOUT_METHOD_DESCRIPTOR, CALENDAR_TIMER, PARTITION_NAME, NODE_NAME) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
update-timer=UPDATE JBOSS_EJB_TIMER SET NEXT_DATE=?, PREVIOUS_RUN=?, TIMER_STATE=?, NODE_NAME=? WHERE TIMED_OBJECT_ID=? and ID=? AND PARTITION_NAME=? AND (NODE_NAME IS NULL OR NODE_NAME=?)
delete-timer=DELETE FROM JBOSS_EJB_TIMER WHERE TIMED_OBJECT_ID=? and ID=? AND PARTITION_NAME=?
load-all-timers=SELECT ID, TIMED_OBJECT_ID, INITIAL_DATE, REPEAT_INTERVAL, NEXT_DATE, PREVIOUS_RUN, PRIMARY_KEY, INFO, TIMER_STATE, SCHEDULE_EXPR_SECOND, SCHEDULE_EXPR_MINUTE, SCHEDULE_EXPR_HOUR, SCHEDULE_EXPR_DAY_OF_WEEK, SCHEDULE_EXPR_DAY_OF_MONTH, SCHEDULE_EXPR_MONTH, SCHEDULE_EXPR_YEAR, SCHEDULE_EXPR_START_DATE, SCHEDULE_EXPR_END_DATE, SCHEDULE_EXPR_TIMEZONE, AUTO_TIMER, TIMEOUT_METHOD_DECLARING_CLASS, TIMEOUT_METHOD_NAME, TIMEOUT_METHOD_DESCRIPTOR, CALENDAR_TIMER FROM JBOSS_EJB_TIMER WHERE TIMED_OBJECT_ID=? AND PARTITION_NAME=?
load-timer=SELECT ID, TIMED_OBJECT_ID, INITIAL_DATE, REPEAT_INTERVAL, NEXT_DATE, PREVIOUS_RUN, PRIMARY_KEY, INFO, TIMER_STATE, SCHEDULE_EXPR_SECOND, SCHEDULE_EXPR_MINUTE, SCHEDULE_EXPR_HOUR, SCHEDULE_EXPR_DAY_OF_WEEK, SCHEDULE_EXPR_DAY_OF_MONTH, SCHEDULE_EXPR_MONTH, SCHEDULE_EXPR_YEAR, SCHEDULE_EXPR_START_DATE, SCHEDULE_EXPR_END_DATE, SCHEDULE_EXPR_TIMEZONE, AUTO_TIMER, TIMEOUT_METHOD_DECLARING_CLASS, TIMEOUT_METHOD_NAME, TIMEOUT_METHOD_DESCRIPTOR, CALENDAR_TIMER FROM JBOSS_EJB_TIMER WHERE TIMED_OBJECT_ID=? and ID=? AND PARTITION_NAME=?
create-table.hsql=CREATE TABLE JBOSS_EJB_TIMER (ID VARCHAR PRIMARY KEY NOT NULL, TIMED_OBJECT_ID VARCHAR NOT NULL, INITIAL_DATE TIMESTAMP, REPEAT_INTERVAL BIGINT, NEXT_DATE TIMESTAMP, PREVIOUS_RUN TIMESTAMP, PRIMARY_KEY VARCHAR, INFO VARCHAR, TIMER_STATE VARCHAR, SCHEDULE_EXPR_SECOND VARCHAR, SCHEDULE_EXPR_MINUTE VARCHAR, SCHEDULE_EXPR_HOUR VARCHAR,SCHEDULE_EXPR_DAY_OF_WEEK VARCHAR, SCHEDULE_EXPR_DAY_OF_MONTH VARCHAR, SCHEDULE_EXPR_MONTH VARCHAR, SCHEDULE_EXPR_YEAR VARCHAR, SCHEDULE_EXPR_START_DATE VARCHAR, SCHEDULE_EXPR_END_DATE VARCHAR, SCHEDULE_EXPR_TIMEZONE VARCHAR, AUTO_TIMER BOOLEAN, TIMEOUT_METHOD_DECLARING_CLASS VARCHAR, TIMEOUT_METHOD_NAME VARCHAR, TIMEOUT_METHOD_DESCRIPTOR VARCHAR, CALENDAR_TIMER BOOLEAN, PARTITION_NAME VARCHAR NOT NULL, NODE_NAME VARCHAR);CREATE INDEX JBOSS_EJB_TIMER_IDENX ON JBOSS_EJB_TIMER (PARTITION_NAME, TIMED_OBJECT_ID)
create-table.postgresql=CREATE TABLE JBOSS_EJB_TIMER (ID VARCHAR PRIMARY KEY NOT NULL, TIMED_OBJECT_ID VARCHAR NOT NULL, INITIAL_DATE TIMESTAMP, REPEAT_INTERVAL BIGINT, NEXT_DATE TIMESTAMP, PREVIOUS_RUN TIMESTAMP, PRIMARY_KEY VARCHAR, INFO TEXT, TIMER_STATE VARCHAR, SCHEDULE_EXPR_SECOND VARCHAR, SCHEDULE_EXPR_MINUTE VARCHAR, SCHEDULE_EXPR_HOUR VARCHAR,SCHEDULE_EXPR_DAY_OF_WEEK VARCHAR, SCHEDULE_EXPR_DAY_OF_MONTH VARCHAR, SCHEDULE_EXPR_MONTH VARCHAR, SCHEDULE_EXPR_YEAR VARCHAR, SCHEDULE_EXPR_START_DATE VARCHAR, SCHEDULE_EXPR_END_DATE VARCHAR, SCHEDULE_EXPR_TIMEZONE VARCHAR, AUTO_TIMER BOOLEAN, TIMEOUT_METHOD_DECLARING_CLASS VARCHAR, TIMEOUT_METHOD_NAME VARCHAR, TIMEOUT_METHOD_DESCRIPTOR VARCHAR, CALENDAR_TIMER BOOLEAN, PARTITION_NAME VARCHAR NOT NULL, NODE_NAME VARCHAR);CREATE INDEX JBOSS_EJB_TIMER_IDENX ON JBOSS_EJB_TIMER (PARTITION_NAME, TIMED_OBJECT_ID)
create-table.mysql=CREATE TABLE JBOSS_EJB_TIMER (ID VARCHAR(255) PRIMARY KEY NOT NULL, TIMED_OBJECT_ID VARCHAR(255) NOT NULL, INITIAL_DATE DATETIME, REPEAT_INTERVAL BIGINT, NEXT_DATE DATETIME, PREVIOUS_RUN DATETIME, PRIMARY_KEY VARCHAR(255), INFO TEXT, TIMER_STATE VARCHAR(32), SCHEDULE_EXPR_SECOND VARCHAR(100), SCHEDULE_EXPR_MINUTE VARCHAR(100), SCHEDULE_EXPR_HOUR VARCHAR(100),SCHEDULE_EXPR_DAY_OF_WEEK VARCHAR(100), SCHEDULE_EXPR_DAY_OF_MONTH VARCHAR(100), SCHEDULE_EXPR_MONTH VARCHAR(100), SCHEDULE_EXPR_YEAR VARCHAR(100), SCHEDULE_EXPR_START_DATE VARCHAR(100), SCHEDULE_EXPR_END_DATE VARCHAR(100), SCHEDULE_EXPR_TIMEZONE VARCHAR(100), AUTO_TIMER BOOLEAN, TIMEOUT_METHOD_NAME VARCHAR(100), TIMEOUT_METHOD_DECLARING_CLASS VARCHAR(255), TIMEOUT_METHOD_DESCRIPTOR VARCHAR(255), CALENDAR_TIMER BOOLEAN, PARTITION_NAME VARCHAR(100) NOT NULL, NODE_NAME VARCHAR(255));CREATE INDEX JBOSS_EJB_TIMER_IDENX ON JBOSS_EJB_TIMER (PARTITION_NAME, TIMED_OBJECT_ID)
create-table.oracle=CREATE TABLE JBOSS_EJB_TIMER (ID VARCHAR2(255) PRIMARY KEY NOT NULL, TIMED_OBJECT_ID VARCHAR2(255) NOT NULL, INITIAL_DATE TIMESTAMP, REPEAT_INTERVAL NUMBER(20), NEXT_DATE TIMESTAMP, PREVIOUS_RUN TIMESTAMP, PRIMARY_KEY VARCHAR2(255), INFO CLOB, TIMER_STATE VARCHAR2(32), SCHEDULE_EXPR_SECOND VARCHAR2(100), SCHEDULE_EXPR_MINUTE VARCHAR2(100), SCHEDULE_EXPR_HOUR VARCHAR2(100),SCHEDULE_EXPR_DAY_OF_WEEK VARCHAR2(100), SCHEDULE_EXPR_DAY_OF_MONTH VARCHAR2(100), SCHEDULE_EXPR_MONTH VARCHAR2(100), SCHEDULE_EXPR_YEAR VARCHAR2(100), SCHEDULE_EXPR_START_DATE VARCHAR2(100), SCHEDULE_EXPR_END_DATE VARCHAR(100), SCHEDULE_EXPR_TIMEZONE VARCHAR2(100), AUTO_TIMER NUMBER(1), TIMEOUT_METHOD_NAME VARCHAR2(100), TIMEOUT_METHOD_DECLARING_CLASS VARCHAR2(255), TIMEOUT_METHOD_DESCRIPTOR VARCHAR2(255), CALENDAR_TIMER NUMBER(1), PARTITION_NAME VARCHAR2(100), NODE_NAME VARCHAR2(255)) TABLESPACE TS_DATA_MPT;CREATE INDEX JBOSS_EJB_TIMER_IDENX ON JBOSS_EJB_TIMER (PARTITION_NAME, TIMED_OBJECT_ID) TABLESPACE TS_INDEX_MPT
create-table.db2=CREATE TABLE JBOSS_EJB_TIMER (ID VARCHAR(255) PRIMARY KEY NOT NULL, TIMED_OBJECT_ID VARCHAR(255) NOT NULL, INITIAL_DATE TIMESTAMP, REPEAT_INTERVAL BIGINT, NEXT_DATE TIMESTAMP, PREVIOUS_RUN TIMESTAMP, PRIMARY_KEY VARCHAR(255), INFO CLOB, TIMER_STATE VARCHAR(32), SCHEDULE_EXPR_SECOND VARCHAR(100), SCHEDULE_EXPR_MINUTE VARCHAR(100), SCHEDULE_EXPR_HOUR VARCHAR(100),SCHEDULE_EXPR_DAY_OF_WEEK VARCHAR(100), SCHEDULE_EXPR_DAY_OF_MONTH VARCHAR(100), SCHEDULE_EXPR_MONTH VARCHAR(100), SCHEDULE_EXPR_YEAR VARCHAR(100), SCHEDULE_EXPR_START_DATE VARCHAR(100), SCHEDULE_EXPR_END_DATE VARCHAR(100), SCHEDULE_EXPR_TIMEZONE VARCHAR(100), AUTO_TIMER INT, TIMEOUT_METHOD_NAME VARCHAR(100), TIMEOUT_METHOD_DECLARING_CLASS VARCHAR(255), TIMEOUT_METHOD_DESCRIPTOR VARCHAR(255), CALENDAR_TIMER INT, PARTITION_NAME VARCHAR(100) NOT NULL, NODE_NAME VARCHAR(255));CREATE INDEX JBOSS_EJB_TIMER_IDENX ON JBOSS_EJB_TIMER (PARTITION_NAME, TIMED_OBJECT_ID)
create-table.mssql=CREATE TABLE JBOSS_EJB_TIMER (ID VARCHAR(255) PRIMARY KEY NOT NULL, TIMED_OBJECT_ID VARCHAR(255) NOT NULL, INITIAL_DATE DATETIME, REPEAT_INTERVAL BIGINT, NEXT_DATE DATETIME, PREVIOUS_RUN DATETIME, PRIMARY_KEY VARCHAR(255), INFO VARCHAR(8000), TIMER_STATE VARCHAR(255), SCHEDULE_EXPR_SECOND VARCHAR(255), SCHEDULE_EXPR_MINUTE VARCHAR(255), SCHEDULE_EXPR_HOUR VARCHAR(255),SCHEDULE_EXPR_DAY_OF_WEEK VARCHAR(255), SCHEDULE_EXPR_DAY_OF_MONTH VARCHAR(255), SCHEDULE_EXPR_MONTH VARCHAR(255), SCHEDULE_EXPR_YEAR VARCHAR(255), SCHEDULE_EXPR_START_DATE VARCHAR(255), SCHEDULE_EXPR_END_DATE VARCHAR(255), SCHEDULE_EXPR_TIMEZONE VARCHAR(255), AUTO_TIMER SMALLINT, TIMEOUT_METHOD_DECLARING_CLASS VARCHAR(255), TIMEOUT_METHOD_NAME VARCHAR(255), TIMEOUT_METHOD_DESCRIPTOR VARCHAR(255), CALENDAR_TIMER SMALLINT, PARTITION_NAME VARCHAR(100) NOT NULL, NODE_NAME VARCHAR(255));CREATE INDEX JBOSS_EJB_TIMER_IDENX ON JBOSS_EJB_TIMER (PARTITION_NAME, TIMED_OBJECT_ID)
create-table.sybase=CREATE TABLE JBOSS_EJB_TIMER (ID VARCHAR(255) PRIMARY KEY NOT NULL, TIMED_OBJECT_ID VARCHAR(255) NOT NULL, INITIAL_DATE DATETIME, REPEAT_INTERVAL BIGINT, NEXT_DATE DATETIME, PREVIOUS_RUN DATETIME, PRIMARY_KEY VARCHAR(255), INFO VARCHAR(16384), TIMER_STATE VARCHAR(255), SCHEDULE_EXPR_SECOND VARCHAR(255), SCHEDULE_EXPR_MINUTE VARCHAR(255), SCHEDULE_EXPR_HOUR VARCHAR(255),SCHEDULE_EXPR_DAY_OF_WEEK VARCHAR(255), SCHEDULE_EXPR_DAY_OF_MONTH VARCHAR(255), SCHEDULE_EXPR_MONTH VARCHAR(255), SCHEDULE_EXPR_YEAR VARCHAR(255), SCHEDULE_EXPR_START_DATE VARCHAR(255), SCHEDULE_EXPR_END_DATE VARCHAR(255), SCHEDULE_EXPR_TIMEZONE VARCHAR(255), AUTO_TIMER SMALLINT, TIMEOUT_METHOD_DECLARING_CLASS VARCHAR(255), TIMEOUT_METHOD_NAME VARCHAR(255), TIMEOUT_METHOD_DESCRIPTOR VARCHAR(255), CALENDAR_TIMER SMALLINT, PARTITION_NAME VARCHAR(100) NOT NULL, NODE_NAME VARCHAR(255));CREATE INDEX JBOSS_EJB_TIMER_IDENX ON JBOSS_EJB_TIMER (PARTITION_NAME, TIMED_OBJECT_ID)
update-running=UPDATE JBOSS_EJB_TIMER SET TIMER_STATE=?, NODE_NAME=? WHERE ID=? AND TIMER_STATE<>? AND TIMER_STATE<>? AND NEXT_DATE=?
