package pt.ptinovacao.na.portal.db.mpt.persistence;


import javax.inject.Inject;
import javax.inject.Named;

import javax.persistence.EntityManager;

import javax.enterprise.context.RequestScoped;

import pt.ptinovacao.na.portal.db.commons.persistence.CommonDataManagerBMT;
import pt.ptinovacao.na.portal.db.commons.persistence.resources.qualifiers.OGMDataStore;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
@Named("DataManagerOGMBMT")
@RequestScoped
public class DataManagerOGMBMT extends CommonDataManagerBMT {

    @Inject
    @OGMDataStore
    private EntityManager em;

    @Override
    public EntityManager getEntityManager() {
       return this.em;
    }
}