package pt.ptinovacao.na.portal.db.mpt.orm.entities;

import java.io.Serializable;

import java.util.Collection;
import java.util.Date;
import java.util.Set;

import javax.enterprise.inject.Vetoed;

import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import org.hibernate.annotations.Type;
import pt.ptinovacao.na.portal.db.mpt.entities.contants.NamedQueriesCat;
import pt.ptinovacao.na.portal.webui.restful.commons.JsonUtils;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.entities.Schedule;

/**
 *
 * <AUTHOR> Karamanos <<EMAIL>>
 */
@Vetoed
@Entity
@Table(name = "MPT_BULK")
@NamedQueries({
    @NamedQuery(name = NamedQueriesCat.BULK_FINDALL, query = "SELECT b FROM Bulk b"),
    @NamedQuery(name = NamedQueriesCat.BULK_FIND_BY_ID, query = "SELECT b FROM Bulk b WHERE b.id = :id"),
    @NamedQuery(name = NamedQueriesCat.BULK_FIND_BY_INSERT_DATE, query = "SELECT b FROM Bulk b WHERE b.insertDate = :insertDate"),
    @NamedQuery(name = NamedQueriesCat.BULK_IDS_FINDALL_BY_INSERT_DATE_LIMIT, query = NamedQueriesCat.BULK_IDS_FINDALL_BY_INSERT_DATE_LIMIT_QUERY),
    @NamedQuery(name = NamedQueriesCat.BULK_FIND_BY_DESCRIPTION, query = "SELECT b FROM Bulk b WHERE b.description = :description"),
    @NamedQuery(name = NamedQueriesCat.BULK_FIND_BY_FILENAME, query = "SELECT b FROM Bulk b WHERE b.filename = :filename"),
    @NamedQuery(name = NamedQueriesCat.BULK_FIND_BY_FLAG_EXECUTION, query = "SELECT b FROM Bulk b WHERE b.flagexecution = :flagexecution"),
    @NamedQuery(name = NamedQueriesCat.BULK_FIND_BY_STARTED_AT, query = "SELECT b FROM Bulk b WHERE b.startedat = :startedat"),
    @NamedQuery(name = NamedQueriesCat.BULK_FIND_BY_FINISHED_AT, query = "SELECT b FROM Bulk b WHERE b.finisheddat = :finisheddat"),
    @NamedQuery(name = NamedQueriesCat.BULK_FIND_BY_TOTAL_LINES, query = "SELECT b FROM Bulk b WHERE b.totalLines = :totalLines"),
    @NamedQuery(name = NamedQueriesCat.BULK_FIND_BY_TOTAL_LINES_OK, query = "SELECT b FROM Bulk b WHERE b.totalLinesOk = :totalLinesOk"),
    @NamedQuery(name = NamedQueriesCat.BULK_FIND_BY_TOTAL_LINES_NOK, query = "SELECT b FROM Bulk b WHERE b.totalLinesNok = :totalLinesNok"),
    @NamedQuery(name = NamedQueriesCat.BULK_FIND_BY_TOTAL_LINES_REJECTED, query = "SELECT b FROM Bulk b WHERE b.totalLinesRejected = :totalLinesRejected"),
    @NamedQuery(name = NamedQueriesCat.BULK_FIND_BY_USERNAME, query = "SELECT b FROM Bulk b WHERE b.username = :username"),
    @NamedQuery(name = NamedQueriesCat.BULK_FINDALL_BY_LAST_NOT_IN_STATES, query = NamedQueriesCat.BULK_FINDALL_BY_LAST_NOT_IN_STATES_QUERY),
    @NamedQuery(name = NamedQueriesCat.BULK_FINDALL_BY_LAST_STATE, query = NamedQueriesCat.BULK_FINDALL_BY_LAST_STATE_QUERY),
    @NamedQuery(name = NamedQueriesCat.BULK_FINDALL_TO_SCHEDULE, query = NamedQueriesCat.BULK_FINDALL_TO_SCHEDULE_QUERY)
    })
public class Bulk implements Serializable {

    private static final long serialVersionUID = 5197405772383825307L;

    @Id
    @SequenceGenerator(name = "SEQ_MPT_BULK", sequenceName = "SEQ_MPT_BULK", initialValue = 1, allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SEQ_MPT_BULK")
    @Basic(optional = false)
    @NotNull
    private Long id;
    @Basic(optional = false)
    @NotNull
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "INSERT_DATE")
    private Date insertDate;
    @Size(max = 1024)
    private String description;
    @Basic(optional = false)
    @NotNull
    @Size(min = 1, max = 256)
    private String filename;
    @Basic(optional = false)
    @NotNull
    private Short flagexecution;
    @Temporal(TemporalType.TIMESTAMP)
    private Date startedat;
    @Temporal(TemporalType.TIMESTAMP)
    private Date finisheddat;
    @Column(name = "TOTAL_LINES_REJECTED")
    private Long totalLinesRejected;
    @Basic(optional = false)
    @NotNull
    @Column(name = "TOTAL_LINES")
    private Long totalLines;
    @Basic(optional = false)
    @NotNull
    @Column(name = "TOTAL_LINES_OK")
    private Long totalLinesOk;
    @Basic(optional = false)
    @NotNull
    @Column(name = "TOTAL_LINES_NOK")
    private Long totalLinesNok;
    @Size(max = 256)
    private String username;
    @JoinColumn(name = "FK_OPER_CAT", referencedColumnName = "ID")
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private OperCat fkOperCat;
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "fkBulk", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("startdate DESC")
    private Collection<BulkTrans> bulkTransCollection;
    @Basic(optional = true, fetch = FetchType.EAGER)
    @Lob
    @Type(type = "org.hibernate.type.TextType")
    private String schedules;

    public Bulk() {
    }

    public Bulk(Long id) {
        this.id = id;
        this.totalLines = 0L;
        this.totalLinesNok = 0L;
        this.totalLinesRejected = 0L;
    }

    public Bulk(Long id, Date date, String filename, Short flagexecution, Long totalLines, Long totalLinesOk, Long totalLinesNok) {
        this.id = id;
        this.insertDate = date;
        this.filename = filename;
        this.flagexecution = flagexecution;
        this.totalLines = totalLines;
        this.totalLinesOk = totalLinesOk;
        this.totalLinesNok = totalLinesNok;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getInsertDate() {
        return insertDate;
    }

    public void setInsertDate(Date insertDate) {
        this.insertDate = insertDate;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public short getFlagexecution() {
        return flagexecution;
    }

    public void setFlagexecution(short flagexecution) {
        this.flagexecution = flagexecution;
    }

    public Date getStartdate() {
        return startedat;
    }

    public void setStartedat(Date startedat) {
        this.startedat = startedat;
    }

    public Date getFinisheddat() {
        return finisheddat;
    }

    public void setFinisheddat(Date finisheddat) {
        this.finisheddat = finisheddat;
    }

    public Long getTotalLines() {
        return totalLines;
    }

    public void setTotalLines(Long totalLines) {
        this.totalLines = totalLines;
    }

    public Long getTotalLinesOk() {
        return totalLinesOk;
    }

    public void setTotalLinesOk(Long totalLinesOk) {
        this.totalLinesOk = totalLinesOk;
    }

    public Long getTotalLinesNok() {
        return totalLinesNok;
    }

    public void setTotalLinesNok(Long totalLinesNok) {
        this.totalLinesNok = totalLinesNok;
    }

    public Long getTotalLinesRejected() {
        return totalLinesRejected;
    }

    public void setTotalLinesRejected(Long totalLinesRejected) {
        this.totalLinesRejected = totalLinesRejected;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public OperCat getFkOperCat() {
        return fkOperCat;
    }

    public void setFkOperCat(OperCat fkOperCat) {
        this.fkOperCat = fkOperCat;
    }

    public Collection<BulkTrans> getBulkTransCollection() {
        return bulkTransCollection;
    }

    public void setBulkTransCollection(Collection<BulkTrans> bulkTransCollection) {
        this.bulkTransCollection = bulkTransCollection;
    }

    public Set<Schedule> getSchedules() {
        return JsonUtils.jsonToJava(Set.class, Schedule.class, this.schedules);
    }

    public void setSchedules(Set<Schedule> schedules) {
        this.schedules = JsonUtils.javaToJSON(schedules);
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Bulk)) {
            return false;
        }
        Bulk other = (Bulk) object;
        return !((this.id == null && other.id != null) || (this.id != null
                && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "pt.ptinovacao.na.portal.db.mpt.orm.entities.Bulk[ id=" + id + " ]";
    }

}
