package pt.ptinovacao.na.portal.db.mpt.ogm.entities;

import pt.ptinovacao.na.portal.db.mpt.persistence.NativeObjectConverter;

import com.google.common.base.MoreObjects;
import com.google.common.base.Strings;

import com.mongodb.BasicDBObject;

import java.io.Serializable;
import java.util.ArrayList;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.enterprise.inject.Vetoed;
import javax.persistence.Cacheable;
import javax.persistence.ColumnResult;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OrderColumn;
import javax.persistence.SqlResultSetMapping;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.Transient;
import javax.persistence.Version;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Past;
import javax.validation.constraints.Size;

import org.bson.types.ObjectId;

import static pt.ptinovacao.na.portal.db.mpt.entities.contants.NamedQueriesOGM.ORDER_FINDALL;
import static pt.ptinovacao.na.portal.db.mpt.entities.contants.NamedQueriesOGM.ORDER_FIND_BY_BULKID_BY_STATES;
import static pt.ptinovacao.na.portal.db.mpt.entities.contants.NamedQueriesOGM.ORDER_FIND_BY_BULKID_BY_STATES_QUERY;
import static pt.ptinovacao.na.portal.db.mpt.entities.contants.NamedQueriesOGM.ORDER_FIND_BY_BULKID_NOT_STARTED;
import static pt.ptinovacao.na.portal.db.mpt.entities.contants.NamedQueriesOGM.ORDER_FIND_BY_ID;
import static pt.ptinovacao.na.portal.db.mpt.entities.contants.NamedQueriesOGM.ORDER_FIND_BY_OPK;
import static pt.ptinovacao.na.portal.db.mpt.entities.contants.NamedQueriesOGM.ORDER_FIND_DISTINCT_ORDERKEYS_BY_BULKID_NOT_STARTED;
import static pt.ptinovacao.na.portal.db.mpt.entities.contants.NamedQueriesOGM.ORDER_FIND_BY_IN_OUT;
import static pt.ptinovacao.na.portal.db.mpt.entities.contants.NamedQueriesOGM.ORDER_FIND_BY_IN_OUT_QUERY;

import pt.ptinovacao.na.portal.commons.model.NodeType;
import pt.ptinovacao.na.portal.webui.restful.commons.model.ExecutionStates;
import pt.ptinovacao.na.portal.webui.restful.commons.model.OrderStatus;

/**
 *
 * <AUTHOR> Karamanos <<EMAIL>>
 */
@Vetoed
@Entity
// Disable cache for performance issues. There will be a lot of these entities!!!
@Cacheable(value = false)
@Table(name = "mpt_orders")
@NamedQueries({
    @NamedQuery(name = ORDER_FINDALL, query = "SELECT o FROM Order o"),
    @NamedQuery(name = ORDER_FIND_BY_ID, query = "SELECT o FROM Order o WHERE o.id = :id"),
    @NamedQuery(name = ORDER_FIND_BY_OPK, query = "SELECT o FROM Order o WHERE o.opk = :opk"),
    @NamedQuery(name = ORDER_FIND_DISTINCT_ORDERKEYS_BY_BULKID_NOT_STARTED, query = "SELECT DISTINCT(o.orderKeyRule) FROM Order o WHERE o.bulkId = :bulkId and o.executionState = 'NOT_STARTED'"),
    @NamedQuery(name = ORDER_FIND_BY_BULKID_NOT_STARTED, query = "SELECT o FROM Order o WHERE o.bulkId = :bulkId and o.executionState = 'NOT_STARTED'"),
    @NamedQuery(name = ORDER_FIND_BY_BULKID_BY_STATES, query = ORDER_FIND_BY_BULKID_BY_STATES_QUERY),
    @NamedQuery(name = ORDER_FIND_BY_IN_OUT, query = ORDER_FIND_BY_IN_OUT_QUERY),})
@SqlResultSetMapping(
        name = "orderTuple",
        columns = {
            @ColumnResult(name = "_id"),
            @ColumnResult(name = "orderKeyRule")
        }
)
@SuppressWarnings("unchecked")
public class Order extends NativeObjectConverter implements Serializable {

    private static final long serialVersionUID = -7507404559275923266L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private ObjectId id;

    /**
     * if two changes from two different sessions (for example) are applied to
     * the same document a <code>org.hibernate.StaleObjectStateException</code>
     * is thrown.
     */
    @Version
    private Integer version;

    /**
     * Correlation id between ORM and OGM MPT model.
     */
    @NotNull
    @Min(value = 0)
    @javax.persistence.Column(name = "bulkId")
    private long bulkId;

    /**
     * Correlation id between OGM and ORM 2nd level.
     */
    @NotNull
    @Min(value = 0)
    private long operCatId;

    /**
     * NodeType
     */
    @Enumerated(EnumType.STRING)
    private NodeType nodeType;

    /**
     * Line number to represent the input file index.
     */
    @NotNull
    @Min(value = 0)
    private Integer lineNum;

    @Past(message = "Start date cannot be in the future")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    private Date startTime;

    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    private Date endTime;

    /**
     * Network activator order processing key upon submission.
     */
    private String opk;

    /**
     * Order key rule for line processor.
     */
    private String orderKeyRule;

    /**
     * Line status. Possible states are defined in the enumeration {@link
     * OrderStatus}.
     */
    @Enumerated(EnumType.STRING)
    private OrderStatus status;

    /**
     * Calculated by the XSLT customization.
     */
    private String statusMessage;

    /**
     * NA Order valid execution states.
     */
    @Enumerated(EnumType.STRING)
    private ExecutionStates executionState;

    /**
     * Input line is designed as a list of columns. Some data is fetched from
     * the Header Catalog customization. Collection is ordered by its position.
     *
     * @see Column
     */
    @ElementCollection(fetch = FetchType.EAGER)
    @OrderColumn(name = "position")
    @Size(min = 1)
    private List<Column> inputLine;

    /**
     * Output line is designed as a list of columns. Some data is fetched from
     * the Header Catalog customization. Collection is ordered by its position.
     *
     * @see Column
     */
    @ElementCollection(fetch = FetchType.EAGER)
    @OrderColumn(name = "position")
    @Size(min = 1)
    private List<Column> outputLine;

    /**
     * Network activator response in case of a generic operation to MPT
     * template.
     */
    @Transient
    private String responseNA;

    public Order() {
    }

    public ObjectId getId() {
        return id;
    }

    public void setId(ObjectId id) {
        this.id = id;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getBulkId() {
        return bulkId;
    }

    public void setBulkId(Long bulkId) {
        this.bulkId = bulkId;
    }

    public Integer getLineNum() {
        return lineNum;
    }

    public void setLineNum(Integer lineNum) {
        this.lineNum = lineNum;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getOpk() {
        return opk;
    }

    public void setOpk(String opk) {
        this.opk = opk;
    }

    public String getOrderKeyRule() {
        return orderKeyRule;
    }

    public void setOrderKeyRule(String keyRule) {
        this.orderKeyRule = keyRule;
    }

    public OrderStatus getStatus() {
        return status;
    }

    public void setStatus(OrderStatus status) {
        this.status = status;
    }

    public String getStatusMessage() {
        return statusMessage;
    }

    public void setStatusMessage(String statusMessage) {
        this.statusMessage = statusMessage;
    }

    public ExecutionStates getExecutionState() {
        return executionState;
    }

    public void setExecutionState(ExecutionStates executionState) {
        this.executionState = executionState;
    }

    public List<Column> getInputLine() {
        return inputLine;
    }

    public void setInputLine(List<Column> inputLine) {
        this.inputLine = inputLine;
    }

    public List<Column> getOutputLine() {
        return outputLine;
    }

    public void setOutputLine(List<Column> outputLine) {
        this.outputLine = outputLine;
    }

    public String getResponseNA() {
        return responseNA;
    }

    public void setResponseNA(String responseNA) {
        this.responseNA = responseNA;
    }

    public void setOperCatId(long operCatId) {
        this.operCatId = operCatId;
    }

    public long getOperCatId() {
        return operCatId;
    }

    public NodeType getNodeType() {
        return nodeType;
    }

    public void setNodeType(NodeType nodeType) {
        this.nodeType = nodeType;
    }

    @Override
    public BasicDBObject toMongoDBNativeObject() {
        Map m = new HashMap();
        m.put("version", this.version);
        m.put("bulkId", this.bulkId);
        m.put("operCatId", this.operCatId);
        m.put("nodeType", this.nodeType);
        m.put("lineNum", this.lineNum);
        m.put("startTime", this.startTime);
        m.put("endTime", this.endTime);
        m.put("opk", Strings.nullToEmpty(this.opk));
        m.put("orderKeyRule", Strings.nullToEmpty(this.orderKeyRule));
        m.put("status", this.status == null ? null : this.status.toString());
        m.put("statusMessage", this.statusMessage);
        m.put("executionState", this.executionState == null ? null : this.executionState.toString());
        m.put("inputLine", getBasicDBObjectList(this.inputLine));
        m.put("outputLine", getBasicDBObjectList(this.outputLine));
        m.put("responseNA", Strings.nullToEmpty(this.responseNA));
        return new BasicDBObject(m);
    }

    private List<BasicDBObject> getBasicDBObjectList(List<Column> list) {
        List<BasicDBObject> l = new ArrayList<>();
        if (list != null) {
            int i = 0;
            for (Column c : list) {
                Map m = new HashMap();
                m.put("columnName", Strings.nullToEmpty(c.getColumnName()));
                m.put("columnValue", Strings.nullToEmpty(c.getColumnValue()));
                m.put("column", Strings.nullToEmpty(c.getColumn()));
                m.put("position", i);
                l.add(new BasicDBObject(m));
                i++;
            }
        }
        return l;
    }

    @Override
    public String toString() {
        return MoreObjects
                .toStringHelper(this)
                .omitNullValues()
                .add("id", this.id)
                .add("version", this.version)
                .add("bulkId", this.bulkId)
                .add("operCatId", this.operCatId)
                .add("nodeType", this.nodeType)
                .add("lineNum", this.lineNum)
                .add("startTime", this.startTime)
                .add("endTime", this.endTime)
                .add("opk", this.opk)
                .add("orderKeyRule", this.orderKeyRule)
                .add("status", this.status)
                .add("statusMessage", this.statusMessage)
                .add("executionState", this.executionState)
                .add("inputLine", this.inputLine)
                .add("outputLine", this.outputLine)
                .add("responseNA", this.responseNA)
                .toString();
    }
}
