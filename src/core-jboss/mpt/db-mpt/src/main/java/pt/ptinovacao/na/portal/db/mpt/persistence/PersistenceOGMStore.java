package pt.ptinovacao.na.portal.db.mpt.persistence;

import com.google.common.base.Stopwatch;
import com.google.common.base.Throwables;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.enterprise.context.RequestScoped;

import javax.persistence.EntityManager;
import javax.persistence.LockModeType;
import javax.persistence.TypedQuery;
import javax.persistence.Table;

import com.mongodb.BasicDBObject;
import com.mongodb.BulkWriteOperation;
import com.mongodb.BulkWriteResult;
import com.mongodb.DBCollection;

import java.util.concurrent.TimeUnit;

import org.hibernate.engine.spi.SessionFactoryImplementor;
import org.hibernate.ogm.OgmSession;
import org.hibernate.ogm.datastore.mongodb.impl.MongoDBDatastoreProvider;
import org.hibernate.ogm.datastore.spi.DatastoreProvider;

import org.jboss.logmanager.Level;
import org.jboss.logmanager.Logger;

import pt.ptinovacao.na.portal.db.commons.persistence.CommonDataManager;
import pt.ptinovacao.na.portal.db.commons.persistence.CommonDataManagerException;

/**
 *
 * <AUTHOR> Laranjeira <<EMAIL>>
 */
@RequestScoped
@SuppressWarnings("unchecked")
public abstract class PersistenceOGMStore {

    private static final Logger LOGGER = Logger.getLogger(PersistenceOGMStore.class.getName());

    public abstract CommonDataManager getDataManager();

    public EntityManager getEntityManager() {
        return this.getDataManager().getEntityManager();
    }

    public <T> List<T> find(String namedQueryTag) throws CommonDataManagerException {
        return (List<T>) this.getDataManager().findByNamedQueryTag(namedQueryTag, null, null);
    }

    public <T> List<T> find(String namedQueryTag, String parameterKey, Object parameterValue) throws CommonDataManagerException {

        return (List<T>) this.getDataManager().findByNamedQueryTag(namedQueryTag, parameterKey, parameterValue);
    }

    public <T> List<T> find(String query, Map<String, Object> parameters) throws CommonDataManagerException {
        return this.getDataManager().findByQuery(query, parameters, true, false, null);
    }

    public <T> void persist(T... entities) throws CommonDataManagerException {
        this.getDataManager().persist(entities);
    }

    public <T> void persist(List<T> entities) throws CommonDataManagerException {
        T[] tList = (T[]) entities.toArray();

        this.persist(tList);
    }

    public <T> void refresh(T entity) throws CommonDataManagerException {
        this.getDataManager().refresh(entity);
    }

    public <T> void refresh(T... entities) throws CommonDataManagerException {
        this.getDataManager().refresh(entities);
    }

    public <T> void refresh(List<T> entities) throws CommonDataManagerException {
        T[] tList = (T[]) entities.toArray();

        this.refresh(tList);
    }

    public <T> void remove(LockModeType lockmode, List<T> entities) throws CommonDataManagerException {
        T[] tList = (T[]) entities.toArray();
        this.remove(lockmode, tList);
    }

    public <T> void remove(LockModeType lockmode, T... entities) throws CommonDataManagerException {
        this.getDataManager().remove(lockmode, entities);
    }

    public <T> T findSingleOrNull(String namedQueryTag, Class<T> type, String parameterKey, Object parameterValue) throws CommonDataManagerException {
        Map<String, Object> map = new HashMap<>();
        map.put(parameterKey, parameterValue);
        return findSingleOrNull(namedQueryTag, type, map);
    }

    public <T> T findSingleOrNull(String namedQueryTag, Class<T> type, Map<String, Object> map) throws CommonDataManagerException {
        try {
            TypedQuery<T> query = this.getEntityManager()
                    .createNamedQuery(namedQueryTag, type)
                    .setMaxResults(1);

            if (map != null && !map.isEmpty()) {
                for (Map.Entry<String, Object> entry : map.entrySet()) {
                    query.setParameter(entry.getKey(), entry.getValue());
                }
            }

            List<T> result = query.getResultList();
            if (result.size() > 0) {
                return result.get(0);
            } else {
                return null;
            }

        } catch (Throwable t) {//NOSONAR
            throw new CommonDataManagerException("Named query: " + namedQueryTag
                    + " returned error: " + Throwables.getRootCause(t).getClass().getSimpleName()
                    + ": "
                    + Throwables.getRootCause(t).getMessage(), t);
        }
    }

    /**
     * Native bulk persist on MongoDB via internal driver framework.
     *
     * @param <T>
     * @param entities
     * @throws CommonDataManagerException
     */
    public <T extends NativeObjectConverter> void bulkPersistNative(List<T> entities)
            throws CommonDataManagerException {
        final Stopwatch ticker = Stopwatch.createStarted();
        if (entities.size() > 0) {
            try {
                Class clazz = entities.get(0).getClass();
                BulkWriteOperation bulkWriteOperation = getBulkWriteOperation(clazz);

                for (T o : entities) {
                    bulkWriteOperation.insert(o.toMongoDBNativeObject());
                }
                bulkWriteOperation.execute();
            } catch (Throwable t) {//NOSONAR
                throw new CommonDataManagerException("persisting entity "
                        + Throwables.getRootCause(t).getClass().getSimpleName() + ": "
                        + Throwables.getRootCause(t).getMessage(), t);

            }
        }
        LOGGER.log(Level.INFO, "Persisted {0} entities in [{1}ms]", new Object[]{entities.size(),
            ticker.stop().elapsed(TimeUnit.MILLISECONDS)});
    }

    /**
     * Native bulk remove on MongoDB via internal driver framework.
     *
     * @param entityQuery
     * @param entityClass
     * @throws CommonDataManagerException
     */
    public BulkWriteResult bulkDeleteNative(Map entityQuery, Class entityClass)
            throws CommonDataManagerException {
        final Stopwatch ticker = Stopwatch.createStarted();
        BulkWriteResult removeResult;

        try {
            final BulkWriteOperation bulkWriteOperation = getBulkWriteOperation(entityClass);

            bulkWriteOperation.find(new BasicDBObject(entityQuery)).remove();
            removeResult = bulkWriteOperation.execute();
        } catch (Throwable t) {
            throw new CommonDataManagerException("Native OGM bulk remove for entit(y/ies) of type '"
                    + entityClass.getSimpleName() + "' using a filter query '"
                    + entityQuery + "'", Throwables.getRootCause(t));
        }

        LOGGER.log(Level.INFO, "Bulk Remove Native returned {0} and took [{1}ms]",
                new Object[]{removeResult, ticker.stop().elapsed(TimeUnit.MILLISECONDS)});

        return removeResult;
    }

    /**
     * Native bulk update on MongoDB via internal driver framework.
     *
     * @param entityQuery
     * @param updateQuery
     * @param entityClass
     * @throws CommonDataManagerException
     */
    public BulkWriteResult bulkUpdateNative(Map entityQuery, Map updateQuery, Class entityClass)
            throws CommonDataManagerException {
        final Stopwatch ticker = Stopwatch.createStarted();
        BulkWriteResult updateResult;

        try {
            final BulkWriteOperation bulkWriteOperation = getBulkWriteOperation(entityClass);

            bulkWriteOperation.find(new BasicDBObject(entityQuery)).update(new BasicDBObject("$set",
                    new BasicDBObject(updateQuery)));
            updateResult = bulkWriteOperation.execute();
        } catch (Throwable t) {
            throw new CommonDataManagerException("Native OGM bulk update for entit(y/ies) of type '"
                    + entityClass.getSimpleName() + "' using a filter query '"
                    + entityQuery + "' and as an update query '" + updateQuery + "'", Throwables.getRootCause(t));
        }

        LOGGER.log(Level.INFO, "Bulk Update Native returned {0} and took [{1}ms]",
                new Object[]{updateResult, ticker.stop().elapsed(TimeUnit.MILLISECONDS)});

        return updateResult;
    }

    /**
     * Helper to unwrap the hibernate session and fetch the bulk write operation of a entity class
     * vi its collection.
     *
     * @param entityClass to retrieve the collection from
     * @return BulkWriteOperation to manipulate by the caller
     */
    private BulkWriteOperation getBulkWriteOperation(Class entityClass) {
        OgmSession session = this.getEntityManager().unwrap(OgmSession.class);
        DatastoreProvider provider = ((SessionFactoryImplementor) session.getSessionFactory())
                .getServiceRegistry().getService(DatastoreProvider.class);
        MongoDBDatastoreProvider mongoDBDatastoreProvider = MongoDBDatastoreProvider.class.cast(provider);
        String coll = ((Table) entityClass.getAnnotation(Table.class)).name();
        DBCollection collection = mongoDBDatastoreProvider.getDatabase().getCollection(coll);
        BulkWriteOperation bulkWriteOperation = collection.initializeUnorderedBulkOperation();

        return bulkWriteOperation;
    }

}
