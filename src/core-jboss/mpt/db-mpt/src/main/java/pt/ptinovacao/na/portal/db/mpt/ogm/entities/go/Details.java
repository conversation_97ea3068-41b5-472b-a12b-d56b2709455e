package pt.ptinovacao.na.portal.db.mpt.ogm.entities.go;

import java.io.Serializable;

import java.util.Date;

import javax.enterprise.inject.Vetoed;
import javax.persistence.Embeddable;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Temporal;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Past;
import javax.validation.constraints.Max;

@Embeddable
@Vetoed
public class Details implements Serializable {

    private static final long serialVersionUID = 5142721004716000599L;

    public Details() {
    }

    public Details(Date datetime, int messageType, Status status, String description, String payload) {
        this.datetime = datetime;
        this.messageType = messageType;
        this.status = status;
        this.description = description;
        this.payload = payload;
    }

    /**
     * Timestamp of created operation
     */
    @Past(message = "date cannot be in the future")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    private Date datetime;

    /**
     * Corresponding order messageType, request or response
     */
    @NotNull
    @Min(value = 0, message = "type should be 0 (request) or 1 (response)")
    @Max(value = 1, message = "type should be 0 (request) or 1 (response)")
    private int messageType;

    @NotNull
    @Enumerated(EnumType.STRING)
    private Status status;

    private String description;

    /**
     * Corresponding order messageType, request or response
     */
    @NotNull
    private String payload;

    /**
     * @return the datetime
     */
    public Date getDatetime() {
        return datetime;
    }

    /**
     * @param datetime the datetime to set
     */
    public void setDatetime(Date datetime) {
        this.datetime = datetime;
    }

    /**
     * @return the messageType
     */
    public int getMessageType() {
        return messageType;
    }

    /**
     * @param messageType the messageType to set
     */
    public void setMessageType(int messageType) {
        this.messageType = messageType;
    }

    /**
     * @return the description
     */
    public String getDescription() {
        return description;
    }

    /**
     * @param description the description to set
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * @return the payload
     */
    public String getPayload() {
        return payload;
    }

    /**
     * @param payload the payload to set
     */
    public void setPayload(String payload) {
        this.payload = payload;
    }

    /**
     * @return the status
     */
    public Status getStatus() {
        return status;
    }

    /**
     * @param status the status to set
     */
    public void setStatus(Status status) {
        this.status = status;
    }
}
