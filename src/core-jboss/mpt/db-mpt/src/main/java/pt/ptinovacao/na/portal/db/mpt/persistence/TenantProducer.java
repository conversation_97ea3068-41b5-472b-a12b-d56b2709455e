package pt.ptinovacao.na.portal.db.mpt.persistence;

import javax.enterprise.inject.Produces;
import javax.enterprise.inject.spi.InjectionPoint;
import javax.enterprise.inject.spi.Bean;
import javax.enterprise.inject.spi.BeanManager;
import javax.enterprise.context.spi.CreationalContext;

import org.jboss.logmanager.Level;
import org.jboss.logmanager.Logger;

import pt.ptinovacao.na.portal.db.commons.persistence.CommonDataManager;
import pt.ptinovacao.na.portal.db.commons.persistence.resources.qualifiers.Tenant;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
@SuppressWarnings("unchecked")
public class TenantProducer {

    private static final String LOG_TEMPLATE = "- Producing {0} DataManager -";

    private static final Logger LOGGER = Logger.getLogger(TenantProducer.class.getName());

    /**
     * Ask the container to inject an Entity Manager (EM). As a consequence the EM will be
     * automatically enlisted into any new transactions started by the managed component. We specify
     * a persistence unit name (the application interacts with multiple databases!)
     * @param ip
     * @param bm
     * @return
     */
    @Produces
    @Tenant
    public CommonDataManager produceDataManager(InjectionPoint ip, BeanManager bm) {
        if (ip.getAnnotated().isAnnotationPresent(Tenant.class)) {
            Tenant tenantAnnotation = ip.getAnnotated().getAnnotation(Tenant.class);

            switch (tenantAnnotation.value()) {
                case OGM_BMT:
                    LOGGER.log(Level.DEBUG, LOG_TEMPLATE, Tenant.TenantType.OGM_BMT.name());
                    return (CommonDataManager) this.getBeanByName(DataManagerOGMBMT.class.getSimpleName(), bm);
                case OGM_CMT:
                    LOGGER.log(Level.DEBUG, LOG_TEMPLATE, Tenant.TenantType.OGM_CMT.name());
                     return (CommonDataManager) this.getBeanByName(DataManagerOGMCMT.class.getSimpleName(), bm);
                case ORM_BMT:
                    LOGGER.log(Level.DEBUG, LOG_TEMPLATE, Tenant.TenantType.ORM_BMT.name());
                     return (CommonDataManager) this.getBeanByName(DataManagerORMBMT.class.getSimpleName(), bm);
                case ORM_CMT:
                    LOGGER.log(Level.DEBUG, LOG_TEMPLATE, Tenant.TenantType.ORM_CMT.name());
                     return (CommonDataManager) this.getBeanByName(DataManagerORMCMT.class.getSimpleName(), bm);
                default:
                    LOGGER.severe("No valid tenant provided");
                    return null;
            }
        }

        return null;
    }

    public Object getBeanByName(String name, BeanManager bm) { // eg. name=batatas{
        LOGGER.log(Level.TRACE, "Fetching the following bean -> {0}", name);
        for (Bean b : bm.getBeans(name)) {
            LOGGER.log(Level.TRACE, "---- {0}", b.getName());
        }
        Bean bean = bm.getBeans(name).iterator().next();
        CreationalContext ctx = bm.createCreationalContext(bean); // could be inlined below
        Object o = bm.getReference(bean, bean.getBeanClass(), ctx); // could be inlined with return
        LOGGER.log(Level.TRACE, "~~~~~~ Fetched bean ~~~~~~");

        return o;
    }
}
