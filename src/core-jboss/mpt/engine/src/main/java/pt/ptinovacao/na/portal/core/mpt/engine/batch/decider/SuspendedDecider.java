package pt.ptinovacao.na.portal.core.mpt.engine.batch.decider;

import com.google.common.collect.ImmutableList;

import javax.batch.api.Decider;
import javax.batch.runtime.StepExecution;
import javax.inject.Inject;
import javax.inject.Named;

import pt.ptinovacao.na.portal.core.mpt._private.EngineLogger;
import pt.ptinovacao.na.portal.core.mpt.engine.batch.AbstractTransientRequest;
import pt.ptinovacao.na.portal.core.mpt.engine.batch.WorkflowStates;
import pt.ptinovacao.na.portal.core.mpt.scheduler.SchedulerEJB;

import pt.ptinovacao.na.portal.webui.restful.mpt.commons.model.BulkEntry;

@Named("SuspendedDecider")
public class SuspendedDecider extends AbstractTransientRequest implements Decider {

    @Inject
    private SchedulerEJB scheduler;

    /**
     * Possible decisions:
     * <pre>
     * {@code
     *
     * <next on="RESUME_WITH_SCHEDULE" to="Scheduled"/>
     * <next on="RESUME_IMMEDIATE" to="Waiting"/>
     * <next on="SUSPENDED_ABORTED" to="Aborting"/>
     * <stop on="SUSPEND" exit-status="SUSPEND" restart="ResumeDecider"/>
     * <next on="*" to="Error"/>
     *
     * }
     * </pre>
     *
     * @param executions
     * @return
     * @throws Exception
     */
    @Override
    public String decide(StepExecution[] executions) throws Exception {
        String decision;

        EngineLogger.LOGGER.runningInsideDecider(this.getClass().getSimpleName());

        switch (this.request.getAction()) {
            case SUSPEND:
                decision = BulkEntry.BulkActions.SUSPEND.name();
                break;
            case RESUME:
                if (this.request.hasSchedules()) {
                    decision = WorkflowStates.RESUME_WITH_SCHEDULE.name();
                } else {
                    decision = WorkflowStates.RESUME_IMMEDIATE.name();
                }
                break;
            case ABORT:
                decision = WorkflowStates.SUSPENDED_ABORTED.name();
                break;
            default:
                decision = WorkflowStates.ERROR.name();
                break;
        }

        // Evaluate if triggers are available and disable them off from the scheduler
        if (super.request.hasSchedules()
                && ImmutableList.of(BulkEntry.BulkActions.SUSPEND.name(), BulkEntry.BulkActions.ABORT.name()).contains(decision)) {
            EngineLogger.LOGGER.requestSchedulerCancel(this.getClass().getSimpleName(), super.request);
            this.scheduler.cancelSchedulesFromRequest(super.request);
        }

        EngineLogger.LOGGER.deciderFinished(this.getClass().getSimpleName(), decision);

        return decision;
    }
}
