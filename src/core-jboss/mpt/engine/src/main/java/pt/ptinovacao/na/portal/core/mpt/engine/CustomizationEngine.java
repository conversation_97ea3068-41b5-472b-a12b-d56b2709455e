package pt.ptinovacao.na.portal.core.mpt.engine;

import java.util.*;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.inject.Named;

import javax.persistence.LockModeType;
import javax.ws.rs.core.Response.Status;

import pt.ptinovacao.na.portal.core.mpt._private.EngineLogger;
import pt.ptinovacao.na.portal.core.mpt.resourcemanager.FamilyResources;
import pt.ptinovacao.na.portal.core.mpt.resourcemanager.ResourceManagerEJB;
import pt.ptinovacao.na.portal.core.mpt.resourcemanager.exception.ResourceManagerException;
import pt.ptinovacao.na.portal.core.mpt.utils.ObjectMapper;

import pt.ptinovacao.na.portal.db.commons.persistence.CommonDataManagerException;
import pt.ptinovacao.na.portal.db.commons.persistence.resources.qualifiers.Audited;
import pt.ptinovacao.na.portal.db.mpt.entities.contants.NamedQueriesCat;
import pt.ptinovacao.na.portal.db.mpt.orm.entities.FamilyCat;
import pt.ptinovacao.na.portal.db.mpt.orm.entities.HdrCat;
import pt.ptinovacao.na.portal.db.mpt.orm.entities.OperCat;
import pt.ptinovacao.na.portal.db.mpt.orm.entities.RespHdrCat;
import pt.ptinovacao.na.portal.db.mpt.persistence.PersistenceORMStore;

import pt.ptinovacao.na.portal.webui.restful.mpt.commons.Consts;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.entities.Customization;

/**
 *
 * <AUTHOR> Karamanos <<EMAIL>>
 */
@RequestScoped
@Named
@Audited(logToFile = true)
public class CustomizationEngine {

    @Inject
    private PersistenceORMStore ormStore;

    @Inject
    private ResourceManagerEJB resourceManager;

    /**
     * validate customization and add it to database if it not exists or update if already exists
     *
     * @param customization
     * @return Response with status of request process
     */
    public javax.ws.rs.core.Response executeFamilyInstall(Customization customization) {
        try {
            insertOrUpdateFamily(customization);
        } catch (IllegalArgumentException e) {
            return javax.ws.rs.core.Response.status(Status.BAD_REQUEST).entity(e.getMessage()).build();
        } catch (CommonDataManagerException | ResourceManagerException e) {
            javax.ws.rs.core.Response.status(Status.INTERNAL_SERVER_ERROR).entity(e.getMessage()).build();
        }

        return javax.ws.rs.core.Response.ok().build();
    }

    /**
     * validate customization and add it to database if it not exists or update if already exists
     *
     * @param customization
     * @return true if inserted or updated successfully, false otherwise.
     */
    public javax.ws.rs.core.Response executeInstall(Customization customization) {
        try {
            insertOrUpdateOperationAndHdrs(customization);
        } catch (IllegalArgumentException e) {
            return javax.ws.rs.core.Response.status(Status.BAD_REQUEST).entity(e.getMessage()).build();
        } catch (CommonDataManagerException | ResourceManagerException e) {
            return javax.ws.rs.core.Response.status(Status.INTERNAL_SERVER_ERROR).entity(e.getMessage()).build();
        }

        return javax.ws.rs.core.Response.ok().build();
    }

    /**
     * Inserts/updates a family into the system respecting the recommendation of
     * {@link pt.ptinovacao.na.portal.core.mpt.engine.Resources#init()}.
     *
     * @param customization
     * @return
     * @throws CommonDataManagerException
     */
    private FamilyCat insertOrUpdateFamily(Customization customization)
            throws CommonDataManagerException, ResourceManagerException {
        if (customization.getFamily().get(Consts.COSTUMIZATION_FAMILY_NAME) == null) {
            throw new IllegalArgumentException("Missing family name parameter");
        }

        FamilyCat familyCat = ormStore.findSingleOrNull(NamedQueriesCat.FAMILY_CAT_FIND_BY_NAME,
                FamilyCat.class, Consts.COSTUMIZATION_FAMILY_NAME, customization.getFamily().get(Consts.COSTUMIZATION_FAMILY_NAME));

        if (familyCat != null) {
            if (customization.isUpdateIfExists()) {
                familyCat = ObjectMapper.updateFamilyCat(customization, familyCat);
                this.ormStore.refresh(familyCat);
            }
        } else {
            validateFamilyParameters(customization);
            familyCat = ObjectMapper.populateFamilyCat(customization);
            ormStore.persist(familyCat);
        }

        EngineLogger.LOGGER.infof("Registering/Updating familyCat '%s' into the resource manager... ", familyCat);
        EngineLogger.LOGGER.infof("%s familyCat '%s' into the resource manager... ",
                this.resourceManager.registerFamily(familyCat.getName(),
                        new FamilyResources(familyCat.getName())) ? "REGISTERED" : "UPDATED", familyCat);

        return familyCat;
    }

    private OperCat insertOrUpdateOperationAndHdrs(Customization customization)
            throws CommonDataManagerException, ResourceManagerException {
        FamilyCat familyCat = insertOrUpdateFamily(customization);
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("name", customization.getOperation().get(Consts.COSTUMIZATION_OPERATION_NAME));
        parameters.put("fkFamilyCat", familyCat.getId());
        OperCat operCat = ormStore.findSingleOrNull(NamedQueriesCat.OPER_CAT_FIND_BY_NAME_AND_FKFAMILYCAT, OperCat.class, parameters);

        if (operCat != null) {
            if (customization.isUpdateIfExists()) {

                List<HdrCat> hdrCatToRemove = ObjectMapper.getHdrsToRemove(customization.getHeader(), operCat.getHdrCatCollection());
                operCat.getHdrCatCollection().removeAll(hdrCatToRemove);
                this.ormStore.remove(LockModeType.OPTIMISTIC_FORCE_INCREMENT, hdrCatToRemove);

                List<RespHdrCat> respHdrCatToDelete = ObjectMapper.getHdrsToRemove(customization.getResponseHeader(), operCat.getRespHdrCatCollection());
                operCat.getRespHdrCatCollection().removeAll(respHdrCatToDelete);
                this.ormStore.remove(LockModeType.OPTIMISTIC_FORCE_INCREMENT, respHdrCatToDelete);

                operCat = ObjectMapper.updateOperCatAndHdrs(operCat, customization);
                this.ormStore.refresh(operCat);
            }
        } else {
            validateOperationParameters(customization);
            operCat = ObjectMapper.populateOperCat(customization, familyCat);
            List<HdrCat> hdrCats = ObjectMapper.populateHdrCat(customization, operCat);
            List<RespHdrCat> respHdrCats = ObjectMapper.populateRespHdrCat(customization, operCat);

            operCat.setHdrCatCollection(hdrCats);
            operCat.setRespHdrCatCollection(respHdrCats);

            this.ormStore.persist(operCat);
        }
        EngineLogger.LOGGER.info("operCat: " + operCat);

        return operCat;
    }

    private void validateFamilyParameters(Customization customization) {
        Map<String, Object> family = customization.getFamily();
        //Verifying all non null parameters to add to DB

        //family parameters
        checkFieldsInMap(
                family, Consts.COSTUMIZATION_FAMILY, ObjectMapper.getNotNullFieldsIgnoleID(FamilyCat.class));
    }

    private void validateOperationParameters(Customization customization) {
        //Verifying all non null parameters to add to DB

        //operation parameters
        checkFieldsInMap(
                customization.getOperation(), Consts.COSTUMIZATION_OPERATION, ObjectMapper.getNotNullFieldsIgnoleID(OperCat.class));

        //headers
        if (customization.getHeader() == null || customization.getHeader().isEmpty()) {
            throw new IllegalArgumentException("Missing headers parameters");
        }

        //Response Headers
        if (customization.getResponseHeader() == null || customization.getResponseHeader().isEmpty()) {
            throw new IllegalArgumentException("Missing Response headers parameters");
        }

        customization.getOperation();

    }

    private void checkFieldsInMap(Map<String, Object> map, String type, List<String> fields) {
        for (String parameter : fields) {
            if (map.get(parameter) == null) {
                throw new IllegalArgumentException("Missing " + type + " " + parameter
                        + " parameter");
            }
        }
    }

}
