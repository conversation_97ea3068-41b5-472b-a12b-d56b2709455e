package pt.ptinovacao.na.portal.core.mpt.engine.events;

import pt.ptinovacao.na.portal.webui.restful.commons.model.TransitionStates;

/**
 *
 * <AUTHOR> <michail-karama<PERSON>@telecom.pt>
 */
public class RecoverEvent {

    public enum RecoverStrategy {
        SUSPEND_STRATEGY, LAST_VALID_STRATEGY, CUSTOM_DEFINED_STRATEGY, FAST_STRATEGY
    }

    private final Long bulkId;
    private RecoverStrategy strategy;
    private TransitionStates transitionState;
    private String exitStatus;
    private String restartPosition;

    public RecoverEvent(Long bulkId, RecoverStrategy strategy) {
        this.bulkId = bulkId;
        this.strategy = strategy;
    }

    public RecoverEvent(Long bulkId, RecoverStrategy strategy, TransitionStates transitionState,
            String exitStatus, String restartPosition) {
        this(bulkId, strategy);
        this.transitionState = transitionState;
        this.exitStatus = exitStatus;
        this.restartPosition = restartPosition;
    }

    public Long getBulkId() {
        return bulkId;
    }

    public RecoverStrategy getStrategy() {
        return strategy;
    }

    public void setStrategy(RecoverStrategy strategy) {
        this.strategy = strategy;
    }

    public TransitionStates getTransitionState() {
        return transitionState;
    }

    public void setTransitionState(TransitionStates transitionState) {
        this.transitionState = transitionState;
    }

    public String getExitStatus() {
        return exitStatus;
    }

    public void setExitStatus(String exitStatus) {
        this.exitStatus = exitStatus;
    }

    public String getRestartPosition() {
        return restartPosition;
    }

    public void setRestartPosition(String restartPosition) {
        this.restartPosition = restartPosition;
    }
}
