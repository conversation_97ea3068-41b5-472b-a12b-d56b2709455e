package pt.ptinovacao.na.portal.core.mpt.engine.batch.decider;

import com.google.common.collect.FluentIterable;

import java.util.Date;

import javax.batch.api.Decider;

import javax.batch.runtime.BatchStatus;
import javax.batch.runtime.StepExecution;
import javax.inject.Inject;
import javax.inject.Named;

import pt.ptinovacao.na.portal.core.mpt._private.EngineLogger;
import pt.ptinovacao.na.portal.core.mpt.engine.BulkEngineEJB;
import pt.ptinovacao.na.portal.core.mpt.engine.batch.AbstractTransientRequest;
import pt.ptinovacao.na.portal.core.mpt.engine.batch.WorkflowStates;
import pt.ptinovacao.na.portal.db.mpt.orm.entities.Bulk;

@Named("ValidatorDecider")
public class ValidatorDecider extends AbstractTransientRequest implements Decider {

    @Inject
    private BulkEngineEJB engine;



    /**
     * Possible decisions:
     * <pre>
     * {@code
     *
     * <next on="OK_PENDING" to="Pending"/>
     * <next on="NOK" to="Rejected"/>
     * <next on="OK_IMMEDIATE" to="Waiting"/>
     * <next on="*" to="Error"/>
     *
     * }
     * </pre>
     *
     * @param executions
     * @return
     * @throws Exception
     */
    @Override
    public String decide(StepExecution[] executions) throws Exception {
        // Fall in error batchlet by default...
        String decision = WorkflowStates.ERROR.name();

        EngineLogger.LOGGER.runningInsideDecider(this.getClass().getSimpleName());

        Bulk bulk = this.engine.getBulk(super.request.getBulkId());

        if (bulk.getStartdate() == null) {
            bulk.setStartedat(new Date());

            this.engine.persistBulk(bulk, true);
        }

        if (executions != null) {
            final FluentIterable<StepExecution> iterable = FluentIterable.of(executions);

            if (!iterable.isEmpty() && iterable.first().isPresent()
                    && !iterable.first().get().getExitStatus().equals(BatchStatus.FAILED.name())) {
                // 1 to 1 therefore evaluate the first execution step... should not fail by spec definition
                if (iterable.first().get().getBatchStatus() != BatchStatus.COMPLETED
                        || iterable.first().get().getExitStatus().equals(WorkflowStates.REJECTED.name())) {
                    decision = WorkflowStates.NOK.name();
                } else if (this.request.hasSchedules()
                        || iterable.first().get().getBatchStatus().equals(BatchStatus.COMPLETED)
                        && !this.request.getLaunch()) {
                    decision = WorkflowStates.OK_PENDING.name();
                } else if (this.request.getLaunch()) {
                    decision = WorkflowStates.OK_IMMEDIATE.name();
                }
            }
        }

        EngineLogger.LOGGER.deciderFinished(this.getClass().getSimpleName(), decision);

        return decision;
    }

}
