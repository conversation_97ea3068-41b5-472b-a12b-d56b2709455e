package pt.ptinovacao.na.portal.core.mpt.engine.batch.batchlet;

import java.util.Date;
import javax.batch.api.Batchlet;
import javax.batch.runtime.BatchStatus;
import javax.batch.runtime.context.StepContext;

import javax.inject.Inject;
import javax.inject.Named;

import pt.ptinovacao.na.portal.core.mpt._private.EngineLogger;
import pt.ptinovacao.na.portal.core.mpt.engine.BulkEngineEJB;
import pt.ptinovacao.na.portal.core.mpt.engine.batch.AbstractTransientRequest;

import pt.ptinovacao.na.portal.db.commons.persistence.CommonDataManagerException;
import pt.ptinovacao.na.portal.db.mpt.orm.entities.Bulk;

import pt.ptinovacao.na.portal.webui.restful.commons.model.TransitionStates;

@Named("AbortedBatchlet")
public class AbortedBatchlet extends AbstractTransientRequest implements Batchlet {

    @Inject
    private StepContext stepContext;

    @Inject
    private BulkEngineEJB engine;

    @Override
    public String process() throws CommonDataManagerException {
        EngineLogger.LOGGER.runningInsideBatchlet(this.getClass().getName());

        Bulk bulk = this.engine.persistBulkTrans(this.request, this.stepContext.getStepName(), TransitionStates.ABORTED);
        bulk.setFinisheddat(new Date());

        this.engine.persistBulk(bulk, true);

        return BatchStatus.COMPLETED.name();
    }

    @Override
    public void stop() throws Exception {
    }
}
