package pt.ptinovacao.na.portal.core.mpt.engine.batch.batchlet;

import java.io.File;

import java.util.Properties;

import javax.batch.api.Batchlet;
import javax.batch.runtime.BatchRuntime;
import javax.batch.runtime.BatchStatus;
import javax.batch.runtime.context.JobContext;

import javax.inject.Inject;
import javax.inject.Named;

import pt.ptinovacao.na.portal.core.mpt._private.EngineLogger;
import pt.ptinovacao.na.portal.core.mpt.engine.batch.AbstractTransientRequest;


@Named("LocalFileStoreBatchlet")
public class LocalFileStoreBatchlet extends AbstractTransientRequest implements Batchlet {

    @Inject
    private JobContext jobCtx;

    @Inject
    private String tmpArchivedOrderFilesDir;

    @Override
    public String process() throws Exception {
        EngineLogger.LOGGER.runningInsideBatchlet(this.getClass().getSimpleName());

        Properties jobParameters = BatchRuntime.getJobOperator().getParameters(this.jobCtx.getExecutionId());

        EngineLogger.LOGGER.localFileStoreJobParams(jobParameters);

        String tmpGzipDir = this.tmpArchivedOrderFilesDir + File.separator + "job" + File.separator
                + this.request.getBulkId();

        EngineLogger.LOGGER.localFileStoreCreateGzipDir(tmpGzipDir, new File(tmpGzipDir).mkdirs());

        return BatchStatus.COMPLETED.name();
    }

    @Override
    public void stop() throws Exception {
    }
}
