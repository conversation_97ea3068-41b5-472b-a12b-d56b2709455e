package pt.ptinovacao.na.portal.core.mpt.utils;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotNull;

import com.google.common.base.MoreObjects;
import pt.ptinovacao.na.portal.db.mpt.orm.entities.Bulk;
import pt.ptinovacao.na.portal.db.mpt.orm.entities.BulkTrans;
import pt.ptinovacao.na.portal.db.mpt.orm.entities.FamilyCat;
import pt.ptinovacao.na.portal.db.mpt.orm.entities.HdrCat;
import pt.ptinovacao.na.portal.db.mpt.orm.entities.JobTypes;
import pt.ptinovacao.na.portal.db.mpt.orm.entities.OperCat;
import pt.ptinovacao.na.portal.db.mpt.orm.entities.OrderKeyRule;
import pt.ptinovacao.na.portal.db.mpt.orm.entities.RespHdrCat;
import pt.ptinovacao.na.portal.webui.restful.commons.model.TransitionStates;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.Consts;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.entities.Customization;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.entities.Request;

/**
 *
 * <AUTHOR> Laranjeira <<EMAIL>>
 */
public final class ObjectMapper {

    private ObjectMapper() {
    }

    public static List<String> getNotNullFieldsIgnoleID(Class<?> clazz) {
        List<String> fields = new ArrayList<>();
        for (Field field : clazz.getDeclaredFields()) {
            if (!field.getName().equalsIgnoreCase("id")) {
                for (Annotation annotation : field.getAnnotations()) {
                    if (annotation.annotationType().equals(NotNull.class)) {
                        fields.add(field.getName());
                        break;
                    }
                }
            }
        }
        return fields;
    }

    public static OperCat updateOperCatAndHdrs(OperCat operCat, Customization customization) {
        Map<String, Object> operation = customization.getOperation();
        if (operation != null && !operation.isEmpty()) {
            if (operation.get(Consts.COSTUMIZATION_OPERATION_NAME) != null) {
                operCat.setName(operation.get(Consts.COSTUMIZATION_OPERATION_NAME).toString());
            }
            if (operation.get(Consts.COSTUMIZATION_OPERATION_ACTIVE) != null) {
                operCat.setActive(Short.parseShort(operation.get(Consts.COSTUMIZATION_OPERATION_ACTIVE).toString()));
            }
            if (operation.get(Consts.COSTUMIZATION_OPERATION_DESCRIPTION) != null) {
                operCat.setDescription(operation.get(Consts.COSTUMIZATION_OPERATION_DESCRIPTION).toString());
            }
            if (operation.get(Consts.COSTUMIZATION_OPERATION_ORDERKEYCONSTRUCTION) != null) {
                operCat.setOrderkeyconstruction(operation.get(Consts.COSTUMIZATION_OPERATION_ORDERKEYCONSTRUCTION).toString());
            }
            if (operation.get(Consts.COSTUMIZATION_OPERATION_ORDERKEYRULE) != null) {
                operCat.setOrderkeyrule(OrderKeyRule.valueOf(operation.get(Consts.COSTUMIZATION_OPERATION_ORDERKEYRULE).toString()));
            }
            if (customization.getXsltinString() != null) {
                operCat.setTransformin(customization.getXsltinString());
            }
            if (customization.getXsltoutString() != null) {
                operCat.setTransformout(customization.getXsltoutString());
            }
            if (operation.get(Consts.COSTUMIZATION_OPERATION_TIMEOUT) != null) {
                operCat.setTimeout(Long.parseLong(operation.get(Consts.COSTUMIZATION_OPERATION_TIMEOUT).toString()));
            }
            if (operation.get(Consts.COSTUMIZATION_OPERATION_WRITREQUESTLINE) != null) {
                operCat.setWriterequestline(operation.get(Consts.COSTUMIZATION_OPERATION_WRITREQUESTLINE).toString().charAt(0));
            }
            operCat.setNodeName(objectToStringOrNull(operation.get(Consts.COSTUMIZATION_OPERATION_NODENAME)));
            if (operation.get(Consts.COSTUMIZATION_OPERATION_REQUIRES_ATTENTION) != null) {
                operCat.setRequiresAttention(operation.get(Consts.COSTUMIZATION_OPERATION).toString());
            }
        }
        Map<Integer, Object> header = customization.getHeader();
        if (header != null && !header.isEmpty()) {
            updateHeader(header, operCat.getHdrCatCollection(), operCat);
        }

        Map<Integer, Object> responseHeader = customization.getResponseHeader();
        if (responseHeader != null && !responseHeader.isEmpty()) {
            updateResponseHeader(responseHeader, operCat.getRespHdrCatCollection(), operCat);
        }

        return operCat;
    }

    private static Collection<HdrCat> updateHeader(Map<Integer, Object> headers, Collection<HdrCat> hdrCatCollection, OperCat operCat) {
        if (hdrCatCollection == null) {
            hdrCatCollection = new ArrayList<>();
        }

        for (Map.Entry<Integer, Object> entry : headers.entrySet()) {
            String[] params = entry.getValue().toString().split(";");

            boolean changed = false;
            for (HdrCat cat : hdrCatCollection) {
                if (entry.getKey() == cat.getPosition().longValue()) {
                    cat.setName(params[0]);
                    cat.setSearchable(params.length > 1 ? Short.parseShort(params[1]) : 0);
                    changed = true;
                    break;
                }
            }
            if (!changed) {
                HdrCat hdrCat = new HdrCat();
                hdrCat.setPosition(entry.getKey().longValue());
                hdrCat.setName(params[0]);
                hdrCat.setSearchable(params.length > 1 ? Short.parseShort(params[1]) : 0);
                hdrCat.setFkOperCat(operCat);
                hdrCatCollection.add(hdrCat);
            }
        }

        return hdrCatCollection;
    }

    private static Collection<RespHdrCat> updateResponseHeader(Map<Integer, Object> headers, Collection<RespHdrCat> hdrCatCollection, OperCat operCat) {
        if (hdrCatCollection == null) {
            hdrCatCollection = new ArrayList<>();
        }

        for (Map.Entry<Integer, Object> entry : headers.entrySet()) {
            String name = entry.getValue().toString();
            boolean changed = false;
            for (RespHdrCat cat : hdrCatCollection) {
                if (entry.getKey() == cat.getPosition().longValue()) {
                    cat.setName(name);
                    changed = true;
                    break;
                }
            }
            if (!changed) {
                RespHdrCat hdrCat = new RespHdrCat();
                hdrCat.setPosition(entry.getKey().longValue());
                hdrCat.setName(name);
                hdrCat.setFkOperCat(operCat);
                hdrCatCollection.add(hdrCat);
            }
        }

        return hdrCatCollection;
    }

    public static <T> List<T> getHdrsToRemove(Map<Integer, Object> headers, Collection<T> hdrs) {
        List<T> toRemove = new ArrayList<>();
        for (T resp : hdrs) {
            Long pos = -1L;
            String name = "";
            if (resp instanceof  HdrCat){
                pos = ((HdrCat)resp).getPosition();
                name =((HdrCat)resp).getName();
            } else if (resp instanceof RespHdrCat){
                pos = ((RespHdrCat)resp).getPosition();
                name =((RespHdrCat)resp).getName();
            }
            if (!name.equals(MoreObjects.firstNonNull((String)headers.get(pos.intValue()),"").replaceFirst(";.*",""))) {
                toRemove.add(resp);
            }
        }
        return toRemove;
    }

    public static FamilyCat populateFamilyCat(Customization customization) {
        Map<String, Object> family = customization.getFamily();

        FamilyCat familyCat = new FamilyCat();
        familyCat.setName(family.get(Consts.COSTUMIZATION_FAMILY_NAME).toString());
        familyCat.setDescription(family.get(Consts.COSTUMIZATION_FAMILY_DESCRIPTION).toString());
        familyCat.setActive(Short.parseShort(family.get(Consts.COSTUMIZATION_FAMILY_ACTIVE).toString()));
        familyCat.setMaxconcurrentopers(Long.parseLong(family.get(Consts.COSTUMIZATION_FAMILY_MAXCONCURRENTOPERS).toString()));

        return familyCat;
    }

    public static FamilyCat updateFamilyCat(Customization customization, FamilyCat familyCat) {
        Map<String, Object> family = customization.getFamily();

        familyCat.setName(family.get(Consts.COSTUMIZATION_FAMILY_NAME) != null ? family.get(Consts.COSTUMIZATION_FAMILY_NAME).toString()
                : familyCat.getName());
        familyCat.setDescription(family.get(Consts.COSTUMIZATION_FAMILY_DESCRIPTION) != null
                ? family.get(Consts.COSTUMIZATION_FAMILY_DESCRIPTION).toString() : familyCat.getDescription());
        familyCat.setActive(family.get(Consts.COSTUMIZATION_FAMILY_ACTIVE) != null
                ? Short.parseShort(family.get(Consts.COSTUMIZATION_FAMILY_ACTIVE).toString()) : familyCat.getActive());
        familyCat.setMaxconcurrentopers(family.get(Consts.COSTUMIZATION_FAMILY_MAXCONCURRENTOPERS) != null
                ? Long.parseLong(family.get(Consts.COSTUMIZATION_FAMILY_MAXCONCURRENTOPERS).toString())
                : familyCat.getMaxconcurrentopers());

        return familyCat;
    }

    public static OperCat populateOperCat(Customization customization, FamilyCat familyCat) {
        Map<String, Object> operation = customization.getOperation();

        OperCat operCat = new OperCat();
        operCat.setName(operation.get(Consts.COSTUMIZATION_OPERATION_NAME).toString());
        operCat.setActive(Short.parseShort(operation.get(Consts.COSTUMIZATION_OPERATION_ACTIVE).toString()));
        operCat.setDescription(operation.get(Consts.COSTUMIZATION_OPERATION_DESCRIPTION).toString());
        operCat.setOrderkeyconstruction(operation.get(Consts.COSTUMIZATION_OPERATION_ORDERKEYCONSTRUCTION).toString());
        operCat.setOrderkeyrule(OrderKeyRule.valueOf(operation.get(Consts.COSTUMIZATION_OPERATION_ORDERKEYRULE).toString()));
        operCat.setTransformin(customization.getXsltinString());
        operCat.setTransformout(customization.getXsltoutString());
        operCat.setTimeout(Long.parseLong(operation.get(Consts.COSTUMIZATION_OPERATION_TIMEOUT).toString()));
        operCat.setWriterequestline(operation.get(Consts.COSTUMIZATION_OPERATION_WRITREQUESTLINE) != null ? operation.get(Consts.COSTUMIZATION_OPERATION_WRITREQUESTLINE).toString().charAt(0) : null);
        operCat.setNodeName(objectToStringOrNull(operation.get(Consts.COSTUMIZATION_OPERATION_NODENAME)));
        operCat.setRequiresAttention(objectToStringOrNull(operation.get(Consts.COSTUMIZATION_OPERATION_REQUIRES_ATTENTION)));

        operCat.setFkFamilyCat(familyCat);

        return operCat;
    }

    public static List<HdrCat> populateHdrCat(Customization customization, OperCat operCat) {
        List<HdrCat> hdrCats = new ArrayList<>();
        Map<Integer, Object> header = customization.getHeader();

        for (Map.Entry<Integer, Object> position : header.entrySet()) {
            String[] params = position.getValue().toString().split(";");

            HdrCat hdrCat = new HdrCat();
            hdrCat.setPosition(position.getKey().longValue());
            hdrCat.setName(params[0]);
            hdrCat.setSearchable(params.length > 1 ? Short.parseShort(params[1]) : 0);
            hdrCat.setFkOperCat(operCat);

            hdrCats.add(hdrCat);
        }

        return hdrCats;
    }

    public static List<RespHdrCat> populateRespHdrCat(Customization customization, OperCat operCat) {
        List<RespHdrCat> respHdrCats = new ArrayList<>();
        Map<Integer, Object> header = customization.getResponseHeader();

        for (Map.Entry<Integer, Object> position : header.entrySet()) {

            RespHdrCat hdrCat = new RespHdrCat();
            hdrCat.setPosition(position.getKey().longValue());
            hdrCat.setName(position.getValue().toString());
            hdrCat.setFkOperCat(operCat);

            respHdrCats.add(hdrCat);
        }

        return respHdrCats;
    }

    private static String objectToStringOrNull(Object o) {
        return o != null ? o.toString() : null;
    }

    public static BulkTrans getNewBulkTransBased(BulkTrans bulkTrans) {
        BulkTrans transNew = new BulkTrans();
        transNew.setFkBulk(bulkTrans.getFkBulk());
        transNew.setJobExecutionId(bulkTrans.getJobExecutionId());
        transNew.setJobType(bulkTrans.getJobType());

        return transNew;
    }

    public static void eagerMergeRequestWithBulkData(Request request, final Bulk bulk) {
        if (bulk.getBulkTransCollection() != null
                && !bulk.getBulkTransCollection().isEmpty()) {
            // Maybe merging here the batch execution is not a good idea... Review later
            request.setBatchExecutionId(bulk.getBulkTransCollection().iterator().next().getJobExecutionId());
        }
        request.setBulkId(bulk.getId());
        request.setFamilyName(bulk.getFkOperCat().getFkFamilyCat().getName());
        request.setInputDescription(bulk.getDescription());
        request.setLaunch(bulk.getFlagexecution() == 0 ? Boolean.FALSE : Boolean.TRUE);
        request.setOperationName(bulk.getFkOperCat().getName());
        // The rest of the data is fetched by the http headers... Username, Action, Date, Origin and File export path
    }

    /**
     * Generate a bulk transition.
     *
     * @param request
     * @param bulk
     * @param transitionState
     * @return
     */
    public static BulkTrans generateBulkTrans(Request request, Bulk bulk, TransitionStates transitionState) {
        BulkTrans bulkTrans = new BulkTrans();
        bulkTrans.setStartdate(new Date());

        String description = request.getInputDescription();
        if (description != null && description.length() > BulkTrans.MAX_DESCRIPTION){
            description = description.substring(0, BulkTrans.MAX_DESCRIPTION);
        }

        bulkTrans.setDescription(description);
        bulkTrans.setState(transitionState);
        bulkTrans.setOrigin(request.getOrigin());
        bulkTrans.setJobExecutionId(request.getBatchExecutionId() == null ? -1 : request.getBatchExecutionId());
        bulkTrans.setCorestartdate(bulk.getStartdate());
        bulkTrans.setName(request.getUsername());
        bulkTrans.setJobType(request.hasSchedules() ? JobTypes.SCHEDULED : JobTypes.MANUAL);
        bulkTrans.setFkBulk(bulk);

        return bulkTrans;
    }
}
