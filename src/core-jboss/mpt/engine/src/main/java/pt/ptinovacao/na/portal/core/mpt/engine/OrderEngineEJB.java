package pt.ptinovacao.na.portal.core.mpt.engine;

import com.mongodb.QueryBuilder;
import org.hibernate.annotations.QueryHints;
import pt.ptinovacao.na.portal.core.mpt._private.EngineLogger;
import pt.ptinovacao.na.portal.core.mpt.dispatcher.exception.DispatcherException;
import pt.ptinovacao.na.portal.core.mpt.dispatcher.processor.OrderProcessor;
import pt.ptinovacao.na.portal.core.mpt.dispatcher.provider.OrderProvider;
import pt.ptinovacao.na.portal.core.mpt.dispatcher.provider.OrderProviderHolderEJB;
import pt.ptinovacao.na.portal.core.mpt.dispatcher.transform.OrdersToFile;
import pt.ptinovacao.na.portal.db.commons.persistence.CommonDataManagerException;
import pt.ptinovacao.na.portal.db.mpt.entities.contants.NamedQueriesCat;
import pt.ptinovacao.na.portal.db.mpt.entities.contants.NamedQueriesOGM;
import pt.ptinovacao.na.portal.db.mpt.ogm.OgmBootstrapEJB;
import pt.ptinovacao.na.portal.db.mpt.ogm.entities.Order;
import pt.ptinovacao.na.portal.db.mpt.orm.entities.OperCat;
import pt.ptinovacao.na.portal.db.mpt.persistence.NativeObjectConverter;
import pt.ptinovacao.na.portal.db.mpt.persistence.PersistenceOGMCMTStore;
import pt.ptinovacao.na.portal.db.mpt.persistence.PersistenceORMStore;
import pt.ptinovacao.na.portal.db.mpt.persistence.utils.QueryIterator;
import pt.ptinovacao.na.portal.db.mpt.queries.CommonQueryEngine;
import pt.ptinovacao.na.portal.webui.restful.commons.model.ExecutionStates;
import pt.ptinovacao.na.portal.webui.restful.commons.model.OrderStatus;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.entities.Request;

import javax.ejb.Asynchronous;
import javax.ejb.Stateless;
import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.enterprise.inject.Instance;
import javax.inject.Inject;
import java.io.File;
import java.io.IOException;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR> Karamanos <<EMAIL>>
 */
@Stateless
@SuppressWarnings("unchecked")
public class OrderEngineEJB {

    @Inject
    private OrderProviderHolderEJB orderProviderHolder;

    @Inject
    private OrderProcessor orderProcessor;

    @Inject
    private PersistenceOGMCMTStore ogmStore;

    @Inject
    private PersistenceORMStore ormStore;

    @Inject
    private Instance<Integer> commitSize;

    @Inject
    private OgmBootstrapEJB bootStrap;

    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public void writeBulkOrders(List<?> items) throws CommonDataManagerException {
        this.ogmStore.bulkPersistNative((List<NativeObjectConverter>) (List<?>) items);
    }

    @TransactionAttribute(TransactionAttributeType.NOT_SUPPORTED)
    public Long getOperCatalogIdFromRequest(final Request request) throws CommonDataManagerException {
        Map<String, Object> params = new HashMap<>();
        params.put("name", request.getOperationName());
        params.put("fkFamilyCatName", request.getFamilyName());
        return this.ormStore.findSingle(NamedQueriesCat.OPER_CAT_FIND_BY_NAME_AND_FKFAMILYCATNAME, OperCat.class, params).getId();
    }

    @TransactionAttribute(TransactionAttributeType.NOT_SUPPORTED)
    public OrderProvider getOrderProviderForRequest(final Request request) throws CommonDataManagerException {
        Long operationId = this.getOperCatalogIdFromRequest(request);
        OrderProvider orderProvider = this.orderProviderHolder.getOrderProvider(operationId, true);

        orderProvider.addBulk(request.getBulkId(), this.commitSize.get());

        return orderProvider;
    }

    @TransactionAttribute(TransactionAttributeType.NOT_SUPPORTED)
    public boolean removeOrderProviderIfExists(final Request request) {
        boolean removed = false;

        try {
            Long operationId = this.getOperCatalogIdFromRequest(request);

            removed = this.orderProviderHolder.removeOrderProvider(operationId);
        } catch (Exception e) {
            EngineLogger.LOGGER.error("Error removing order provider", e);
        }

        return removed;
    }

    @TransactionAttribute(TransactionAttributeType.NOT_SUPPORTED)
    public boolean removeBulkFromOrderProvider(final Request request) {
        boolean removed = false;

        try {
            Long operationId = this.getOperCatalogIdFromRequest(request);
            // TODO: review
            if (operationId != null) {
                OrderProvider orderProvider = this.orderProviderHolder.getOrderProvider(operationId, false);

                if (orderProvider != null) {
                    removed = orderProvider.removeBulk(request.getBulkId());
                }
            }
        } catch (Exception e) {
            EngineLogger.LOGGER.error("Error removing bulk order provider", e);
        }

        return removed;
    }

    /**
     * Computes the maximum simultaneous orders that a bulk can process without restrictions. It is
     * relevant in two scenarios:
     * <ul>
     * <li>Scenario with exclusivity: Only one order key order can be processed simultaneously. So
     * it is 1*N orderKeys max</li>
     * <li>Scenario without exclusivity: Several order keys orders can be processed simultaneously.
     * So it is N*N orderKeys max</li>
     * </ul>
     *
     * Obviously they will be limited by the bulk family resources configuration!
     *
     * @param bulkId to calculate the maximum value from
     * @return the number of maximum simultaneous orders at a certain time
     */
    @TransactionAttribute(TransactionAttributeType.NOT_SUPPORTED)
    public int getMaxSimultaneousOrdersToProcess(boolean exclusive, long bulkId) {
        String nativeQueryName;

        if (!exclusive) {
            nativeQueryName = "db.mpt_orders.count({ 'bulkId' : " + bulkId
                    + " , 'executionState' : '"
                    + ExecutionStates.NOT_STARTED + "'})";
            return ((Number) this.ogmStore.getEntityManager().createNativeQuery(nativeQueryName)
                    .setHint(QueryHints.READ_ONLY, Boolean.TRUE.toString())
                    .getSingleResult()).intValue();
        } else {
            return this.bootStrap.collectionDistinctCount("mpt_orders", "orderKeyRule",
                    QueryBuilder.start("bulkId").is(bulkId).and("executionState").is(ExecutionStates.NOT_STARTED.name()).get());
        }

    }

    @Asynchronous
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public void processOrder(final Order order) throws DispatcherException {
        EngineLogger.LOGGER.processingOrder(order.getId().toString(), order.getBulkId());

        Order processedOrder = this.orderProcessor.process(order.getId().toString());

        EngineLogger.LOGGER.processedOrder(processedOrder.getId().toString(),
                processedOrder.getBulkId(), processedOrder.getExecutionState().name());
    }

    @TransactionAttribute(TransactionAttributeType.NOT_SUPPORTED)
    public boolean checkIfOrderProviderHasNext(final OrderProvider provider, final Request request) {
        return provider.hasNext(request.getBulkId());
    }

    @TransactionAttribute(TransactionAttributeType.NOT_SUPPORTED)
    public Order getNextOrderFromProvider(OrderProvider provider, Request request)
            throws DispatcherException {

        return provider.next(request.getBulkId());
    }

    @Asynchronous
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public void updateBulkMetrics(Long bulkId, OrderStatus orderStatus) {
        try {
            this.ormStore.getEntityManager().joinTransaction(); // Join this transaction

            if (orderStatus.equals(OrderStatus.OK)) {
                this.ormStore.getEntityManager()
                        .createQuery("UPDATE Bulk b SET b.totalLinesOk = b.totalLinesOk + 1 WHERE b.id=:bulkId")
                        .setParameter("bulkId", bulkId)
                        .executeUpdate();
            } else {
                this.ormStore.getEntityManager()
                        .createQuery("UPDATE Bulk b SET b.totalLinesNok = b.totalLinesNok + 1 WHERE b.id=:bulkId")
                        .setParameter("bulkId", bulkId)
                        .executeUpdate();
            }

            this.ormStore.getEntityManager().flush(); // Commit for web visibility purposes... On method exit will issue a commit, but we need it before!
        } catch (Exception e) {
            EngineLogger.LOGGER.errorf(e, "Could not update metrics for bulk [%s/%s]", bulkId, orderStatus);
        }
    }

    @TransactionAttribute(TransactionAttributeType.NOT_SUPPORTED)
    public Long countOrdersByBulkIdAndState(Long bulkId, String executionState) {
        return new CommonQueryEngine(this.ogmStore, this.commitSize.get()).countOrdersByBulkIdAndState(bulkId, executionState);
    }

    @TransactionAttribute(TransactionAttributeType.NOT_SUPPORTED)
    public QueryIterator<Order> getOrdersIteratorByBulkIdAndState(Long bulkId, String executionState, Long ordersCount) {
        return new CommonQueryEngine(this.ogmStore, this.commitSize.get()).getOrdersIteratorByBulkIdAndState(bulkId, executionState, ordersCount);
    }

    @TransactionAttribute(TransactionAttributeType.NOT_SUPPORTED)
    public File writeTmpFileFromOrders(Long bulkId, String executionState, Long ordersCount, String tmpFilePath) throws IOException {
        QueryIterator<Order> queryIterator = new CommonQueryEngine(this.ogmStore, this.commitSize.get()).getOrdersIteratorByBulkIdAndState(bulkId, executionState, ordersCount);
        return OrdersToFile.getOrdersFile(queryIterator, false, tmpFilePath);
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public boolean hasRemainingOrdersToProcess(Long bulkId) throws CommonDataManagerException {
        Map<String, Object> param = new HashMap();
        param.put("bulkId", bulkId);
        param.put("states", EnumSet.of(ExecutionStates.STARTED, ExecutionStates.NOT_STARTED));
        param.put("max", 1);

        // Avoid dirty reads on high concurrency scenarios - orders dont come updated from last commit (hibernate cache maybe???)
        this.ogmStore.getEntityManager().flush();
        List<Order> orders = this.ogmStore.find(NamedQueriesOGM.ORDER_FIND_BY_BULKID_BY_STATES, param);

        if (orders != null) {
            EngineLogger.LOGGER.ordersRemaining(bulkId, orders.size());
        }

        return orders != null && !orders.isEmpty();
    }
}
