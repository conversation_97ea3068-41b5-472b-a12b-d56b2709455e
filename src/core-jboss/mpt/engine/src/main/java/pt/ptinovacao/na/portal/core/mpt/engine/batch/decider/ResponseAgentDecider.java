/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package pt.ptinovacao.na.portal.core.mpt.engine.batch.decider;

import com.google.common.base.Strings;
import javax.annotation.PostConstruct;
import javax.batch.api.Decider;
import javax.batch.runtime.BatchRuntime;
import javax.batch.runtime.StepExecution;
import javax.batch.runtime.context.JobContext;
import javax.inject.Inject;
import javax.inject.Named;
import pt.ptinovacao.na.portal.core.mpt._private.EngineLogger;
import pt.ptinovacao.na.portal.core.mpt.engine.batch.WorkflowStates;

/**
 *
 * <AUTHOR>
 */
@Named("ResponseAgentDecider")
public class ResponseAgentDecider implements Decider {

    @Inject
    private JobContext jobCtx;

    private String fileExportPath;

    @PostConstruct
    public void init() {

        fileExportPath = BatchRuntime.getJobOperator().getJobExecution(this.jobCtx.getExecutionId()).getJobParameters().getProperty("fileExportPath");

    }

    @Override
    public String decide(StepExecution[] executions) throws Exception {
        EngineLogger.LOGGER.runningInsideDecider(this.getClass().getSimpleName());

        return Strings.isNullOrEmpty(this.fileExportPath) ? WorkflowStates.NO.name()
                : WorkflowStates.YES.name();
    }

}
