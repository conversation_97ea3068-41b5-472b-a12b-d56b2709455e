package pt.ptinovacao.na.portal.core.mpt.engine.batch.batchlet;

import com.google.common.base.MoreObjects;
import com.google.common.base.Stopwatch;
import com.google.common.collect.ImmutableSet;
import com.google.common.util.concurrent.Monitor;

import java.util.concurrent.TimeUnit;

import javax.batch.runtime.BatchStatus;
import javax.enterprise.inject.Instance;

import javax.inject.Inject;
import javax.inject.Named;

import pt.ptinovacao.na.portal.db.commons.persistence.CommonDataManagerException;
import pt.ptinovacao.na.portal.db.mpt.ogm.entities.Order;

import pt.ptinovacao.na.portal.core.mpt._private.EngineLogger;
import pt.ptinovacao.na.portal.core.mpt.dispatcher.exception.DispatcherException;
import pt.ptinovacao.na.portal.core.mpt.engine.batch.Tools;
import pt.ptinovacao.na.portal.core.mpt.dispatcher.provider.OrderProvider;
import pt.ptinovacao.na.portal.core.mpt.engine.BulkEngineEJB;
import pt.ptinovacao.na.portal.core.mpt.engine.OrderEngineEJB;
import pt.ptinovacao.na.portal.core.mpt.engine.events.MPTEvent;
import pt.ptinovacao.na.portal.core.mpt.engine.exceptions.MptEngineException;
import pt.ptinovacao.na.portal.core.mpt.resourcemanager.ResourceManagerEJB;
import pt.ptinovacao.na.portal.core.mpt.resourcemanager.exception.ResourceManagerException;

import pt.ptinovacao.na.portal.webui.restful.commons.model.TransitionStates;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.model.BulkEntry;

/**
 *
 * <AUTHOR> Karamanos <<EMAIL>>
 */
@Named("ExecutingBatchlet")
public class ExecutingBatchlet extends MPTBatchlet {

    @Inject
    ResourceManagerEJB resourceManager;

    @Inject
    OrdersObserverEJB ordersObserver;

    @Inject
    private OrderEngineEJB orderEngine;

    @Inject
    private BulkEngineEJB engine;

    @Inject
    private Instance<Long> batchGlobalTimeoutMilis;

    private int ordersProcessed = 0;

    @Override
    public String process() throws CommonDataManagerException, MptEngineException,
            ResourceManagerException, DispatcherException {
        EngineLogger.LOGGER.runningInsideBatchlet(this.getClass().getSimpleName());
        Stopwatch stopwatch = Stopwatch.createStarted();

        this.engine.persistBulkTrans(super.getRequest(), this.stepCtx.getStepName(),
                TransitionStates.EXECUTING);

        OrderProvider orderProvider = this.orderEngine.getOrderProviderForRequest(super.getRequest());

        // Loop while it has orders to process and terminates as soon as interrupt signal arrives
        while (!super.isInterrupted()
                && this.orderEngine.checkIfOrderProviderHasNext(orderProvider, super.getRequest())) {
            final Long maxConcurrentOrders = this.engine.getMaxConcurrentOrdersFromFamily(
                    super.getRequest().getFamilyName());

            if (OrdersObserverEJB.getMONITOR().enterWhenUninterruptibly(
                    this.buildWaitForFamilyResources(maxConcurrentOrders), this.batchGlobalTimeoutMilis.get(), TimeUnit.MILLISECONDS)) {
                try {
                    EngineLogger.LOGGER.resourcesAreNowAvailable(super.getRequest().getFamilyName());
                } finally {
                    OrdersObserverEJB.getMONITOR().leave();
                }
                // Monitor pos sync point... Executes only if entered the monitor...
                this.orderExecutor(maxConcurrentOrders, orderProvider);
            } else {
                EngineLogger.LOGGER.globalTimeoutWasReached("executing and waiting for family "
                        + "resources", this.batchGlobalTimeoutMilis.get());
            }
        }

        // Avoid monitor infinit lock in case of absent orders or some concurrent interrupt flag fast change in previous while loop
        if (super.isInterrupted()
                && (!this.ordersObserver.isBulkObserved(super.getRequest().getBulkId())
                || this.ordersProcessed == 0)) {
            return super.getRequest().getAction().name();
        }

        // WAIT FOR ORDERS... Monitor will be waked up on every concurrent monitor enter by the bottom CDI Orders Observer
        if (OrdersObserverEJB.getMONITOR().enterWhenUninterruptibly(this.buildCheckIfRemainBulkOrdersGuard(),
                this.batchGlobalTimeoutMilis.get(), TimeUnit.MILLISECONDS)) {
            try {
                EngineLogger.LOGGER.executingSummary(super.getRequest().getBulkId(),
                        this.ordersProcessed, stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
            } finally {
                OrdersObserverEJB.getMONITOR().leave();
            }
        } else {
            EngineLogger.LOGGER.globalTimeoutWasReached("executing and waiting for orders to finish",
                    this.batchGlobalTimeoutMilis.get());
            if (this.ordersObserver.isBulkObserved(super.getRequest().getBulkId())) {
                for (int i = 0; i
                        < this.ordersObserver.getOrdersProcessedFromBulk(super.getRequest().getBulkId()).size(); i++) {
                    this.resourceManager.unregisterOrder(super.getRequest().getFamilyName());
                }
            }
        }

        this.ordersObserver.unobserveBulk(super.getRequest().getBulkId());

        if (!super.isInterrupted()) {
            return BatchStatus.COMPLETED.name();
        } else {
            return super.getRequest().getAction().name();
        }
    }

    /**
     *
     * Guard condition to proceed as soon as resources are available, optimizing resources usage
     * such as CPU and excessive calls to resource manager.
     *
     * @param maxConcurrentOrders
     * @return the intended guard condition
     */
    private Monitor.Guard buildWaitForFamilyResources(final Long maxConcurrentOrders) {
        return new Monitor.Guard(OrdersObserverEJB.getMONITOR()) {

            @Override
            public boolean isSatisfied() {
                try {
                    return resourceManager.hasOrderResources(
                            getRequest().getFamilyName(), maxConcurrentOrders) || isInterrupted();
                } catch (ResourceManagerException | MptEngineException ex) {
                    EngineLogger.LOGGER.waitForFamilyResourcesFailure(ex);
                }

                return false;
            }
        };
    }

    /**
     * Creates a guard condition to check if remain orders from a bulk.
     *
     * @return the intended guard condition
     */
    private Monitor.Guard buildCheckIfRemainBulkOrdersGuard() {
        return new Monitor.Guard(OrdersObserverEJB.getMONITOR()) {

            @Override
            public boolean isSatisfied() {
                try {
                    return ordersObserver.checkIfRemainBulkOrders(getRequest().getBulkId());
                } catch (Exception e) {
                    EngineLogger.LOGGER.checkingIfRemainBulkOrdersGuardFailure(e);
                }

                return false;
            }
        };
    }

    /**
     * Tries to unregister a order from family resources.
     *
     * @param order
     */
    private void unregisterOrderHelper(String orderId) throws ResourceManagerException,
            MptEngineException {
        if (!this.resourceManager.unregisterOrder(super.getRequest().getFamilyName())) {
            EngineLogger.LOGGER.failureUnregisteringOrderFromFamily(
                    MoreObjects.firstNonNull(orderId, "Unknown"),
                    super.getRequest().getFamilyName(),
                    super.getRequest().getBulkId());
        }
    }

    @Override
    protected ImmutableSet<BulkEntry.BulkActions> getValidInterruptedStates() {
        return Tools.END_TIME_WINDOW_INTERRUPTED_STATES;
    }

    /**
     * Make sure that the monitor changes protected region data and that all threads have visibility
     * on those changes.
     *
     * @param event received from the guava signaling system
     */
    @Override
    public void mptEventListener(MPTEvent event) {
        OrdersObserverEJB.getMONITOR().enter();
        try {
            super.mptEventListener(event);
        } finally {
            OrdersObserverEJB.getMONITOR().leave();
        }
    }

    /**
     * Registers to a family and validates before sending do process next available order.
     * Unregistered in OrderProcessor in case o immediate error, on OrderHandlerEJB as a "normal
     * workflow", and here in case of interruption while fetching next order.
     *
     * @param maxConcurrentOrders availabelk
     * @param orderProvider
     * @throws CommonDataManagerException
     */
    private void orderExecutor(Long maxConcurrentOrders, OrderProvider orderProvider)
            throws CommonDataManagerException, MptEngineException, ResourceManagerException,
            DispatcherException {
        if (!super.isInterrupted()
                && this.resourceManager.registerOrder(super.getRequest().getFamilyName(), maxConcurrentOrders)) {
            EngineLogger.LOGGER.executingProcessingOrder(super.getRequest().getBulkId());
            Order order = this.orderEngine.getNextOrderFromProvider(orderProvider, super.getRequest());

            if (order == null) {
                EngineLogger.LOGGER.executingNullOrder();
                this.unregisterOrderHelper(null);
                return;
            }

            this.ordersObserver.addOrderToProcessedList(order.getBulkId(), order.getId().toString());
            this.orderEngine.processOrder(order);
            this.ordersProcessed++;
        } else {
            EngineLogger.LOGGER.failureRegisteringOrderToFamily(super.getRequest().getFamilyName(),
                    super.getRequest().getBulkId());
        }
    }
}
