package pt.ptinovacao.na.portal.core.mpt._private;

import java.util.List;
import java.util.Properties;

import org.jboss.logging.BasicLogger;
import org.jboss.logging.Logger;
import org.jboss.logging.annotations.Cause;
import org.jboss.logging.annotations.LogMessage;
import org.jboss.logging.annotations.Message;
import org.jboss.logging.annotations.MessageLogger;
import org.jboss.logging.annotations.ValidIdRange;
import pt.ptinovacao.na.portal.core.mpt.engine.batch.WorkflowStates;

import pt.ptinovacao.na.portal.webui.restful.commons.model.TransitionStates;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.Consts;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.entities.Request;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
@MessageLogger(projectCode = Consts.MPT_ENGINE_ORIGIN)
@ValidIdRange(min = 1, max = 199)
public interface EngineLogger extends BasicLogger {

    EngineLogger LOGGER = Logger.getMessageLogger(EngineLogger.class, "pt.ptinovacao.na.portal.core.mpt.engine");

    @LogMessage(level = Logger.Level.ERROR)
    @Message(id = 1, value = "Failed to read order line from batch job %s. The problematic line number was %s present on file [%s]")
    void failToReadOrderLine(@Cause Throwable e, String jobName, int lineNumber, String fileName);

    @LogMessage(level = Logger.Level.WARN)
    @Message(id = 2, value = "Order parameter does not exist or was not defined for index '%s'. Catalogued key construction is [%s]")
    void orderParameterAbsent(@Cause Throwable e, int index, String keyConstruction);

    @LogMessage(level = Logger.Level.WARN)
    @Message(id = 3, value = "Line order processor error with cause [%s] at line index [%s]. Line original content: [%s]. ")
    void failToProcessLine(String cause, int index, String line);

    @LogMessage(level = Logger.Level.WARN)
    @Message(id = 4, value = "Orders writer error with cause [%s]. Transient chunk data content [%s]. Lines not writen [%s]")
    void failToWriteOrders(String cause, String transientData, List<Object> lines);

    @LogMessage(level = Logger.Level.FATAL)
    @Message(id = 5, value = "Invalid %s found on state '%s' with family '%s' and operation '%s' from user '%s'")
    void failBatchletExecution(@Cause Throwable e, String context, TransitionStates state, String family, String operation, String user);

    @LogMessage(level = Logger.Level.WARN)
    @Message(id = 6, value = "Transient data is missing from job properties while executing batch artifact named '%s'")
    void transientDataMissing(String artifact);

    @LogMessage(level = Logger.Level.FATAL)
    @Message(id = 7, value = "Persistence data is missing from job properties while executing batch artifact named '%s'")
    void persistenceDataMissing(String artifact);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 8, value = "Batch Context Info %n %s %n")
    void batchContextInfo(String contextInfo);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 9, value = "Batch Execution Info %n %s %n")
    void batchExecutionInfo(String executionInfo);

    @LogMessage(level = Logger.Level.WARN)
    @Message(id = 10, value = "Failed to unregister bulk batch thread for BULK [%s]")
    void batchThreadUnregisterFailure(long bulkId);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 11, value = "%s SCHEDULED BULK Arrival - [%s/%s/%s]")
    void waitingScheduleArrival(TransitionStates state, long bulkId, String description, String filename);

    @LogMessage(level = Logger.Level.WARN)
    @Message(id = 12, value = "Timeout between orders was reached! Bulk [%s] will retry if resources available...")
    void timeoutBetweenOrderWasReached(@Cause Throwable e, long bulkId);

    @LogMessage(level = Logger.Level.ERROR)
    @Message(id = 13, value = "Database connectivity problem. %s ")
    void databaseConnectivityFailure(@Cause Throwable e, String description);

    @LogMessage(level = Logger.Level.WARN)
    @Message(id = 14, value = "ResourceManager failed to unregister order [%s] family/bulkId [%s/%s]")
    void failureUnregisteringOrderFromFamily(String orderId, String family, long bulkId);

    @LogMessage(level = Logger.Level.WARN)
    @Message(id = 15, value = "ResourceManager failed to register an order to family/bulkId [%s/%s]. Too much family concurrency found on the cluster!")
    void failureRegisteringOrderToFamily(String family, long bulkId);

    @LogMessage(level = Logger.Level.WARN)
    @Message(id = 16, value = "Unsufficient resources from family [%s] of ResourceManager Module. Too much family concurrency! Retrying...")
    void resourcesExausted(String family);

    @LogMessage(level = Logger.Level.WARN)
    @Message(id = 17, value = "Timeout while waiting for resources was reached! The defined timeout is [%s/%s]. Bulk [%s]. Going to suspend this batch bulk...")
    void timeoutWhileWaitingForResources(@Cause Throwable e, long l, String timeUnit, long bulkId);

    @LogMessage(level = Logger.Level.WARN)
    @Message(id = 18, value = "Timeout while waiting for bulk[%s] termination/suspension.")
    void timeoutWhileWaitingForBulk(Long bulkId);

    @LogMessage(level = Logger.Level.ERROR)
    @Message(id = 19, value = "Internal MPT batch engine error while executing '%s' artifact")
    void internalEngineError(@Cause Throwable e, Class clazz);

    @LogMessage(level = Logger.Level.DEBUG)
    @Message(id = 20, value = "Running inside a batchlet : %s")
    void runningInsideBatchlet(String artifactName);

    @LogMessage(level = Logger.Level.DEBUG)
    @Message(id = 21, value = "Running inside decider : %s")
    void runningInsideDecider(String artifactName);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 22, value = "Decider will transition to the following artifact: %s")
    void deciderTransition(String artifactName);

    @LogMessage(level = Logger.Level.DEBUG)
    @Message(id = 23, value = "Batchlet will return the following workflow state - [%s]")
    void batchletWorkflowTransition(WorkflowStates state);

    @LogMessage(level = Logger.Level.WARN)
    @Message(id = 24, value = "%s will cancel all schedules from request [%s]")
    void requestSchedulerCancel(String entity, Request request);

    @LogMessage(level = Logger.Level.ERROR)
    @Message(id = 25, value = "Global batch timeout was reached while %s. Value 'batchGlobalTimeoutMilis' should be increased via configuration service from '%s' milis to a higher value!!!")
    void globalTimeoutWasReached(String description, Long timeoutValue);

    @LogMessage(level = Logger.Level.ERROR)
    @Message(id = 26, value = "Internal engine guava subscriber error")
    void mptEventListenerError(@Cause Throwable e);

    @LogMessage(level = Logger.Level.ERROR)
    @Message(id = 27, value = "Failure while sending event to batch engine (if running)")
    void sendEventToBatchEngineIfRunningFailure(@Cause Throwable e);

    @LogMessage(level = Logger.Level.ERROR)
    @Message(id = 28, value = "Orders observer monitor wait for family resources guard failure")
    void waitForFamilyResourcesFailure(@Cause Throwable e);

    @LogMessage(level = Logger.Level.ERROR)
    @Message(id = 29, value = "Orders observer monitor checking if remain bulk orders guard failure")
    void checkingIfRemainBulkOrdersGuardFailure(@Cause Throwable e);

    @LogMessage(level = Logger.Level.ERROR)
    @Message(id = 30, value = "Failure while initializing waiting batchlet data")
    void initWaitingBatchletFailure(@Cause Throwable e);

    @LogMessage(level = Logger.Level.ERROR)
    @Message(id = 31, value = "Failure in interrupted guard condition")
    void interruptedGuardFailure(@Cause Throwable e);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 32, value = "Resources for family '%s' are now available... Proceeding...")
    void resourcesAreNowAvailable(String familyName);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 33, value = "Finished processing orders for bulk id [%s] inside ExecutingBatchlet. Processed %s orders in %s ms")
    void executingSummary(Long bulkId, int orders, Long elapsedMilis);

    @LogMessage(level = Logger.Level.DEBUG)
    @Message(id = 34, value = "ExecutingBatchlet is processing order for bulk id [%s]")
    void executingProcessingOrder(Long bulkId);

    @LogMessage(level = Logger.Level.WARN)
    @Message(id = 35, value = "ExecutingBatchlet retrieved a null/empty order from OrderProvider. Too many order keys for the same operation maybe!")
    void executingNullOrder();

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 36, value = "Going to process order with id '%s' from bulk id '%s'")
    void processingOrder(String orderId, Long bulkId);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 37, value = "Processed order with id '%s' from bulk id '%s' ended with an execution status of [%s]")
    void processedOrder(String orderId, Long bulkId, String status);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 38, value = "Bulk with id '%s' has [%s] remaining orders to process")
    void ordersRemaining(Long bulkId, int size);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 39, value = "Finished waiting for previous processed orders for bulk id [%s]. Waiting took %s ms")
    void waitingSummary(Long bulkId, Long elapsedMilis);

    @LogMessage(level = Logger.Level.TRACE)
    @Message(id = 40, value = "Unregistered Thread[%s] to Guava EventBus!")
    void eventBusThreadUnregister(String name);

    @LogMessage(level = Logger.Level.TRACE)
    @Message(id = 41, value = "Registered Thread[%s] to Guava EventBus!")
    void eventBusThreadRegister(String name);

    @LogMessage(level = Logger.Level.WARN)
    @Message(id = 42, value = "Received an undelivered engine event... EVENT[%s/%s]")
    void eventBusUndeliver(Object event, Object source);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 43, value = "Received an engine event on class '%s' living in Thread '%s'. EVENT[%s]")
    void eventBusReception(String className, String threadName, String eventPayload);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 44, value = "Batch listener thread initiliazed with the following properties context: %s")
    void batchListenerInitProps(String properties);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 45, value = "Batch listener thread for job '%s' is initializing at before job phase for bulk with id '%s'... Request -> %s")
    void batchListenerBeforeJob(String jobName, Long bulkId, String requestPayload);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 46, value = "Batch listener thread is starting step '%s' for bulk id '%s'")
    void batchListenerBeforeStep(String jobName, Long bulkId);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 47, value = "Batch listener thread is ending step '%s' for bulk id '%s'")
    void batchListenerAfterStep(String jobName, Long bulkId);

    @LogMessage(level = Logger.Level.ERROR)
    @Message(id = 48, value = "Processed orders observer received twice a same order with id %s!!!")
    void ordersObserverTwiceError(String orderId);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 49, value = "Processed orders observer received a processed order event %s")
    void ordersObserverEventArrival(String orderEventString);

    @LogMessage(level = Logger.Level.DEBUG)
    @Message(id = 50, value = "Processed orders observer removed order '%s' from list -> %s. [Total] - [%s]")
    void ordersObserverOrderRemoval(String orderId, String ordersList, int total);

    @LogMessage(level = Logger.Level.WARN)
    @Message(id = 51, value = "Processed orders observer received unknown processed order '%s' from bulk %s!!!")
    void ordersObserverUnknownOrderArrival(String orderId, Long bulkId);

    @LogMessage(level = Logger.Level.FATAL)
    @Message(id = 52, value = "Processed orders observer severe error for event: %s")
    void ordersObserverError(@Cause Throwable e, String event);

    @LogMessage(level = Logger.Level.DEBUG)
    @Message(id = 53, value = "Building BatchOperations bean...")
    void batchOperationsInit();

    @LogMessage(level = Logger.Level.WARN)
    @Message(id = 54, value = "Batch operations bean flushed all batch in memory jobs from job operator and took %s ms")
    void batchOperationsMemJobsFlush(Long milis);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 55, value = "Job with execution id '%s' was found on a BatchState of '%s'")
    void batchOperationsJobState(Long id, String state);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 56, value = "Batch job with execution id '%s' took '%s' ms to complete...")
    void batchJobExecutionTookXMilis(Long id, Long milis);

    @LogMessage(level = Logger.Level.DEBUG)
    @Message(id = 57, value = "Local file store will use the following job parameters: %s")
    void localFileStoreJobParams(Properties properties);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 58, value = "Clean local file store will remove directory '%s'")
    void cleanLocalFileStoreRemoveDir(String dirName);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 59, value = "File compressor will use the following locations: - download directory [%s] - output gzip file [%s]")
    void fileCompressorDirs(String downloadDir, String gzipFile);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 60, value = "File compressor will compress the file '%s' to gzip as '%s'")
    void fileCompressorGzip(String fileName, String gzipFileName);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 61, value = "File compressor is done with creating the file archive")
    void fileCompressorGzip();

    @LogMessage(level = Logger.Level.ERROR)
    @Message(id = 62, value = "File compressor failed while creating the file archive due to: %s")
    void fileCompressorGzip(@Cause Throwable e, String cause);

    @LogMessage(level = Logger.Level.DEBUG)
    @Message(id = 63, value = "File compressor found the following files while generating file list: %s")
    void fileCompressorGenerateFileList(String list);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 64, value = "Local file store created the following temporary output gzip directory '%s'. Creation of dirs returned '%s'")
    void localFileStoreCreateGzipDir(String dir, boolean created);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 65, value = "Checking if request has more schedules of event list '%s'")
    void checkIfRequestHasSchedules(String list);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 66, value = "While checking if request has more schedules it found the last element of the schedule list -> %s")
    void checkIfRequestHasSchedulesFoundLast(String last);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 67, value = "Going to load orders from order provider to bulk '%s'...")
    void loadingOrdersFromProvider(Long bulkId);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 68, value = "Wait for resources guard found no other family running bulk orders")
    void waitForResourcesGuardNoOther();

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 69, value = "Wait for resources guard found other family running bulk orders")
    void waitForResourcesGuardOther();

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 70, value = "Wait for resources guard found the max allowed orders per bulk list -> %s")
    void waitForResourcesGuardMaxOrders(String list);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 71, value = "Wait for resources guard compared [maxSimultaneousOrders %s < maxConcurrentOrders %s]. Comparison took %s ms")
    void waitForResourcesGuardMaxOrdersCmp(int maxSimultaneousOrders, int maxConcurrentOrders, long milis);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 72, value = "Decider '%s' will exit with the following state '%s'")
    void deciderFinished(String name, String exitStatus);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 73, value = "Previous executions were found on decider '%s' meaning that it's not a batch resume...")
    void deciderPreviousExecsFound(String name);

    @LogMessage(level = Logger.Level.ERROR)
    @Message(id = 74, value = "Engine schedule event observer could not schedule event %s")
    void engineScheduleEventObserver(@Cause Throwable e, String event);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 75, value = "Engine resources is initializing [RESOURCE MANAGER] Family Resources cache")
    void resourcesInitFamilyCache();

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 76, value = "Engine resources initialized [RESOURCE MANAGER] Family Resources cache successfully")
    void resourcesInitFamilyCacheSuccess();

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 77, value = "Engine resources is initializing [BATCH ENGINE REQUEST CACHE Listener]")
    void resourcesInitCacheListener();

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 78, value = "Engine resources found a fresh instance of a Batch Listener bound it to Request Cache")
    void resourcesInitCacheListenerBound();

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 79, value = "Engine resources initialized [BATCH ENGINE REQUEST CACHE Listener] successfully")
    void resourcesInitCacheListenerSuccess();

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 80, value = "Engine resources is shutting down... Removing [BATCH ENGINE REQUEST CACHE Listener] and flushing cache...")
    void resourcesDestroy();

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 81, value = "Engine resources removed [BATCH ENGINE REQUEST CACHE Listener] and flushed cache successfully")
    void resourcesDestroySuccess();

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 82, value = "Engine resources removed a [BATCH ENGINE REQUEST CACHE Listener] instance")
    void resourcesDestroyListenerRemoval();

    @LogMessage(level = Logger.Level.DEBUG)
    @Message(id = 83, value = "Destroying BatchOperations bean...")
    void batchOperationsDestroy();

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 84, value = "Engine scheduler task is initializing scheduler task %s...")
    void engineSchedulerTaskInit(String taskName);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 85, value = "Engine scheduler task for bulk id '%s' from family [%s] is running... Going to send a end time window event"
            + " to the batch engine and wait until batch ends!")
    void engineSchedulerTaskEndWindowEvent(Long bulkId, String familyName);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 86, value = "Engine scheduler task for bulk id '%s' from family [%s] is not running... "
                            + "Going to restart batch engine!")
    void engineSchedulerTaskRestart(Long bulkId, String familyName);

    @LogMessage(level = Logger.Level.WARN)
    @Message(id = 87, value = "Engine scheduler task found a bulk id '%s' not elegible to start/resume. Batch workflow execution id[%s]")
    void engineSchedulerTaskInelegibleBulkFailure(Long bulkId, Long batchExecutionId);

    @LogMessage(level = Logger.Level.ERROR)
    @Message(id = 88, value = "Engine scheduler task did not found bulk transition for bulkId '%s' from family [%s]. Bulk is in an invalid state....")
    void engineSchedulerTaskInvalidBulk(Long bulkId, String familyName);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 89, value = "Engine scheduler task '%s' finished! The computed scheduled ACTION is '%s'")
    void engineSchedulerTaskFinished(String taskName, String result);

    @LogMessage(level = Logger.Level.ERROR)
    @Message(id = 90, value = "Engine scheduler task internal error")
    void engineSchedulerTaskError(@Cause Throwable e);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 91, value = "Recover engine is initializing... Going to sanitize bulks and their schedules...")
    void recoverEngineInit();

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 92, value = "Recover engine is processing bulk id %s for recover. Last known bulk transition status was '%s'...")
    void recoverEngineBulkFound(Long bulkId, String transition);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 93, value = "Recover engine for Bulk id %s calculated its next state as '%s' (May change if batch engine runs ok)")
    void recoverEngineBulkFoundNextState(Long bulkId, String transition);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 94, value = "Recover engine returned the following request from recover -> %s using the strategy '%s'")
    void recoverEngineResultStrategy(String request, String strategy);

    @LogMessage(level = Logger.Level.WARN)
    @Message(id = 95, value = "Recover engine failed using '%s' strategy")
    void recoverEngineStrategyFail(String strategy);

    @LogMessage(level = Logger.Level.WARN)
    @Message(id = 96, value = "Recover engine found no bulk transitions for bulkId '%s' or bulk was found in valid state... discarding...")
    void recoverEngineNoTransOrValid(Long bulkId);

    @LogMessage(level = Logger.Level.WARN)
    @Message(id = 97, value = "Recover engine found that BatchStatus is not in a valid state to check if fixed...")
    void recoverEngineBatchStatusInvalid();

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 98, value = "Recover engine will start rebuilding and sanitizing bulk schedules")
    void recoverEngineStartScheduling();

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 99, value = "Recover engine found a valid bulk to recover: id %s at state %s")
    void recoverEngineSchedulingBulk(Long bulkId, String state);

    @LogMessage(level = Logger.Level.ERROR)
    @Message(id = 100, value = "Recover engine failed while rebuilding and sanitizing bulk schedules...")
    void recoverEngineSchedulingFailure(@Cause Throwable e);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 101, value = "Recover engine finished rebuilding and sanitizing bulk schedules")
    void recoverEngineFinishedScheduling();

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 102, value = "Recover engine finished and recovered %s bulks and %s bulk schedules in %s ms")
    void recoverEngineFinishedSummary(int bulks, int schedules, long milis);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 103, value = "Recover engine will purge a job type '%s'. List of ids to purge: %s")
    void recoverEngineJobPurge(String jobType, String ids);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 104, value = "Recover engine purged job [%s] and took %s ms to conclude...")
    void recoverEngineJobPurgeMetric(String jobType, long milis);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 105, value = "Recover engine going to abandon job execution %s for bulk %s")
    void recoverEngineJobAbandon(Long execId, Long bulkId);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 106, value = "Recover engine updated %d orders from bulkId %s to ERROR execution state and NOK status. BulkWriteResult -> %s")
    void recoverEngineUpdatedOrders(int orders, Long bulkId, String rawResult);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 107, value = "Recover engine going to recover to the closest valid previous batch execution. Request: %s")
    void recoverEngineClosestValidStep(String request);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 108, value = "Recover engine restoring request from step %s with following persistent data: %s")
    void recoverEnginePersistentDataRecover(String step, String data);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 109, value = "Recover engine going to recover to the previous defined by [%s/%s/%s] from batch with execution id %s. Request: %s")
    void recoverEnginePreviousDefinedStep(String fallState, String exitStatus, String restartPosition, Long excecId, String request);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 110, value = "Recover engine is updating step execution name %s with id %s - BatchStatus %s -> %s | ExitStatus %s -> %s")
    void recoverEngineStepExecUpdate(String stepName, Long execId, String statusBefore, String statusAfter, String exitStatusBefore, String exitStatusAfter);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 111, value = "Recover engine going to purge the not most recent execution...")
    void recoverEngineStartPurgeNotMostRecentJob();

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 112, value = "Recover engine going to generate a request from last bulk...")
    void recoverEngineStartGenerateRequest();

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 113, value = "Recover engine sanitized previous generated request: %s")
    void recoverEngineStartGenerateRequestSanitize(String request);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 114, value = "Recover engine event observer is going to process a RECOVER EVENT: %s")
    void recoverEngineEventObserver(String event);

    @LogMessage(level = Logger.Level.ERROR)
    @Message(id = 115, value = "Recover engine event observer found no bulk transitions for bulkId '%s'. Discarding...")
    void recoverEngineEventObserverInvalidEvent(Long bulkId);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 116, value = "Pending decider is resuming from previous execution. Request content -> %s")
    void pendingDeciderResuming(String request);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 117, value = "Pending decider has no previous execution. Resuming from STOPPED job. Request content -> %s. Is interrupted %s")
    void pendingDeciderResumingFromStop(String request, boolean interrupted);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 118, value = "Pending decider is returning a '%s' decision. Request content -> %s. Is interrupted %s")
    void pendingDeciderDecision(String decision, String request, boolean interrupted);

    @LogMessage(level = Logger.Level.DEBUG)
    @Message(id = 119, value = "Order item reader injected file for reading bulk orders = %s")
    void chunkReaderFileInjection(String fileName);

    @LogMessage(level = Logger.Level.TRACE)
    @Message(id = 120, value = "Order item reader is reading line [%s]")
    void chunkReaderLine(String line);

    @LogMessage(level = Logger.Level.TRACE)
    @Message(id = 121, value = "Order item reader processed the following transient data content %s")
    void chunkReaderTransientContent(String data);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 122, value = "Order item reader finished and will close reader with a total of '%s' rejected lines...")
    void chunkReaderFinish(int rejectedLines);

    @LogMessage(level = Logger.Level.DEBUG)
    @Message(id = 123, value = "Order item processor is processing line [%s]")
    void chunkProcessorLine(String line);

    @LogMessage(level = Logger.Level.DEBUG)
    @Message(id = 124, value = "Order item processor is processing the following transient data content %s")
    void chunkProcessorTransientContent(String transientData);

    @LogMessage(level = Logger.Level.INFO)
    @Message(id = 125, value = "Engine checkpoint algorithm activation... Commiting %s MPT Orders. [ChunkTransientData] [%s]")
    void chunkCheckpointOk(int size, String transientData);

    @LogMessage(level = Logger.Level.DEBUG)
    @Message(id = 126, value = "Engine checkpoint algorithm not yet ready... Activation will be done on '%s' value...")
    void chunkCheckpointNok(int commitSize);
}
