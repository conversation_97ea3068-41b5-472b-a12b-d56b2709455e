package pt.ptinovacao.na.portal.core.mpt.engine.batch;

import com.google.common.collect.ImmutableSet;
import javax.batch.runtime.context.JobContext;
import pt.ptinovacao.na.portal.core.mpt._private.EngineLogger;
import pt.ptinovacao.na.portal.core.mpt.engine.events.MPTEvent;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.entities.Request;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.model.BulkEntry;

/**
 * Some lazy useful tools.
 *
 * <AUTHOR> <michail-karama<PERSON>@telecom.pt>
 */
public final class Tools {

    /**
     * Helper set that defines all the available interruption states. ABORT, SUSPEND, SUSPEND_ADMIN
     * and END_TIME_WINDOW.
     */
    public static final ImmutableSet<BulkEntry.BulkActions> ALL_INTERRUPTED_STATES = ImmutableSet.of(
            BulkEntry.BulkActions.ABORT,
            BulkEntry.BulkActions.SUSPEND,
            BulkEntry.BulkActions.SUSPEND_ADMIN,
            BulkEntry.BulkActions.END_TIME_WINDOW);

    /**
     * Helper set used to define the available abort interruption states. ABORT solely.
     */
    public static final ImmutableSet<BulkEntry.BulkActions> CAN_ABORT_INTERRUPTED_STATES = ImmutableSet.of(
            BulkEntry.BulkActions.ABORT);

    /**
     * Helper set used to define the interruption states that can be suspended. ABORT and SUSPEND.
     */
    public static final ImmutableSet<BulkEntry.BulkActions> CAN_SUSPEND_INTERRUPTED_STATES = ImmutableSet
            .<BulkEntry.BulkActions>builder()
            .addAll(CAN_ABORT_INTERRUPTED_STATES)
            .add(BulkEntry.BulkActions.SUSPEND)
            .build();

    /**
     * Helper set used to define the interruption states that can be suspended administratively.
     * ABORT, SUSPEND and SUSPEND_ADMIN.
     */
    public static final ImmutableSet<BulkEntry.BulkActions> CAN_SUSPEND_ADMIN_INTERRUPTED_STATES = ImmutableSet
            .<BulkEntry.BulkActions>builder()
            .addAll(CAN_SUSPEND_INTERRUPTED_STATES)
            .add(BulkEntry.BulkActions.SUSPEND_ADMIN)
            .build();

    /**
     * Helper set used to define the interruption states valid for the end time window. ALL THE
     * PREVOIS ONES are available..
     */
    public static final ImmutableSet<BulkEntry.BulkActions> END_TIME_WINDOW_INTERRUPTED_STATES = ImmutableSet
            .<BulkEntry.BulkActions>builder()
            .addAll(ALL_INTERRUPTED_STATES)
            .build();

    private Tools() {
    }

    public static Request getAndValidateTransientData(JobContext context, String batchletName) {
        Request request = (Request) context.getTransientUserData();

        if (request == null) {
            EngineLogger.LOGGER.transientDataMissing(batchletName);
        }

        return request;
    }

    public static boolean eventEval(MPTEvent event, Request request) {
        if (event != null && event.getBulkId() != null && request != null && request.getBulkId() != null) {
            return request.getBulkId().equals(event.getBulkId());
        } else {
            return false;
        }
    }
}
