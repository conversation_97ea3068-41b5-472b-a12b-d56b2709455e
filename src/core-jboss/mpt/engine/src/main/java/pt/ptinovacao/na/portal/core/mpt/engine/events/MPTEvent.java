package pt.ptinovacao.na.portal.core.mpt.engine.events;

import com.google.common.base.MoreObjects;

import pt.ptinovacao.na.portal.webui.restful.mpt.commons.model.BulkEntry;

/**
 * Since CDI doesn't allow Dependent beans to be conditional observers we will use the Guava event
 * bus approach.
 *
 * <AUTHOR> <<EMAIL>>
 */
public class MPTEvent {

    private final Long bulkId;
    private final BulkEntry.BulkActions eventType;

    public MPTEvent(Long bulkId, BulkEntry.BulkActions eventType) {
        this.bulkId = bulkId;
        this.eventType = eventType;
    }

    public Long getBulkId() {
        return bulkId;
    }

    public BulkEntry.BulkActions getEventType() {
        return eventType;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .omitNullValues()
                .add("bulkId", this.bulkId)
                .add("eventType", this.eventType)
                .toString();
    }
}
