package pt.ptinovacao.na.portal.core.mpt.engine.batch.chunk;

import com.google.common.base.MoreObjects;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
public class ChunkLine {

    private final int index;
    private String line;

    public ChunkLine(int index) {
        this.index = index;
    }

    public ChunkLine(int index, String line) {
        this.index = index;
        this.line = line;
    }

    public int getIndex() {
        return index;
    }

    public String getLine() {
        return line;
    }

    public boolean isValid() {
        return this.line != null && !this.line.isEmpty();
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .omitNullValues()
                .add("index", this.index)
                .add("line", this.line)
                .add("isValid", this.isValid())
                .toString();
    }
}
