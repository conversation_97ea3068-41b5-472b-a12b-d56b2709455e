package pt.ptinovacao.na.portal.core.mpt._private;

import java.util.Locale;
import java.io.Serializable;
import javax.annotation.Generated;
import org.jboss.logging.DelegatingBasicLogger;
import pt.ptinovacao.na.portal.webui.restful.mpt.commons.entities.Request;
import pt.ptinovacao.na.portal.webui.restful.commons.model.TransitionStates;
import java.lang.String;
import org.jboss.logging.Logger;
import java.util.Properties;
import org.jboss.logging.BasicLogger;
import java.lang.Long;
import java.lang.Throwable;
import java.lang.Class;
import java.lang.Object;
import pt.ptinovacao.na.portal.core.mpt.engine.batch.WorkflowStates;


import static org.jboss.logging.Logger.Level.TRACE;
import static org.jboss.logging.Logger.Level.ERROR;
import static org.jboss.logging.Logger.Level.INFO;
import static org.jboss.logging.Logger.Level.FATAL;
import static org.jboss.logging.Logger.Level.DEBUG;
import static org.jboss.logging.Logger.Level.WARN;

/**
 * Warning this class consists of generated code.
 */
@Generated(value = "org.jboss.logging.processor.generator.model.MessageLoggerImplementor", date = "2025-07-01T11:20:44+0100")
public class EngineLogger_$logger extends DelegatingBasicLogger implements EngineLogger, BasicLogger, Serializable {
    private static final long serialVersionUID = 1L;
    private static final String FQCN = EngineLogger_$logger.class.getName();
    public EngineLogger_$logger(final Logger log) {
        super(log);
    }
    private static final Locale LOCALE = Locale.ROOT;
    protected Locale getLoggingLocale() {
        return LOCALE;
    }
    @Override
    public final void failToReadOrderLine(final Throwable e, final String jobName, final int lineNumber, final String fileName) {
        super.log.logf(FQCN, ERROR, e, failToReadOrderLine$str(), jobName, lineNumber, fileName);
    }
    private static final String failToReadOrderLine = "MPTENGINE000001: Failed to read order line from batch job %s. The problematic line number was %s present on file [%s]";
    protected String failToReadOrderLine$str() {
        return failToReadOrderLine;
    }
    @Override
    public final void orderParameterAbsent(final Throwable e, final int index, final String keyConstruction) {
        super.log.logf(FQCN, WARN, e, orderParameterAbsent$str(), index, keyConstruction);
    }
    private static final String orderParameterAbsent = "MPTENGINE000002: Order parameter does not exist or was not defined for index '%s'. Catalogued key construction is [%s]";
    protected String orderParameterAbsent$str() {
        return orderParameterAbsent;
    }
    @Override
    public final void failToProcessLine(final String cause, final int index, final String line) {
        super.log.logf(FQCN, WARN, null, failToProcessLine$str(), cause, index, line);
    }
    private static final String failToProcessLine = "MPTENGINE000003: Line order processor error with cause [%s] at line index [%s]. Line original content: [%s]. ";
    protected String failToProcessLine$str() {
        return failToProcessLine;
    }
    @Override
    public final void failToWriteOrders(final String cause, final String transientData, final java.util.List<Object> lines) {
        super.log.logf(FQCN, WARN, null, failToWriteOrders$str(), cause, transientData, lines);
    }
    private static final String failToWriteOrders = "MPTENGINE000004: Orders writer error with cause [%s]. Transient chunk data content [%s]. Lines not writen [%s]";
    protected String failToWriteOrders$str() {
        return failToWriteOrders;
    }
    @Override
    public final void failBatchletExecution(final Throwable e, final String context, final TransitionStates state, final String family, final String operation, final String user) {
        super.log.logf(FQCN, FATAL, e, failBatchletExecution$str(), context, state, family, operation, user);
    }
    private static final String failBatchletExecution = "MPTENGINE000005: Invalid %s found on state '%s' with family '%s' and operation '%s' from user '%s'";
    protected String failBatchletExecution$str() {
        return failBatchletExecution;
    }
    @Override
    public final void transientDataMissing(final String artifact) {
        super.log.logf(FQCN, WARN, null, transientDataMissing$str(), artifact);
    }
    private static final String transientDataMissing = "MPTENGINE000006: Transient data is missing from job properties while executing batch artifact named '%s'";
    protected String transientDataMissing$str() {
        return transientDataMissing;
    }
    @Override
    public final void persistenceDataMissing(final String artifact) {
        super.log.logf(FQCN, FATAL, null, persistenceDataMissing$str(), artifact);
    }
    private static final String persistenceDataMissing = "MPTENGINE000007: Persistence data is missing from job properties while executing batch artifact named '%s'";
    protected String persistenceDataMissing$str() {
        return persistenceDataMissing;
    }
    @Override
    public final void batchContextInfo(final String contextInfo) {
        super.log.logf(FQCN, INFO, null, batchContextInfo$str(), contextInfo);
    }
    private static final String batchContextInfo = "MPTENGINE000008: Batch Context Info %n %s %n";
    protected String batchContextInfo$str() {
        return batchContextInfo;
    }
    @Override
    public final void batchExecutionInfo(final String executionInfo) {
        super.log.logf(FQCN, INFO, null, batchExecutionInfo$str(), executionInfo);
    }
    private static final String batchExecutionInfo = "MPTENGINE000009: Batch Execution Info %n %s %n";
    protected String batchExecutionInfo$str() {
        return batchExecutionInfo;
    }
    @Override
    public final void batchThreadUnregisterFailure(final long bulkId) {
        super.log.logf(FQCN, WARN, null, batchThreadUnregisterFailure$str(), bulkId);
    }
    private static final String batchThreadUnregisterFailure = "MPTENGINE000010: Failed to unregister bulk batch thread for BULK [%s]";
    protected String batchThreadUnregisterFailure$str() {
        return batchThreadUnregisterFailure;
    }
    @Override
    public final void waitingScheduleArrival(final TransitionStates state, final long bulkId, final String description, final String filename) {
        super.log.logf(FQCN, INFO, null, waitingScheduleArrival$str(), state, bulkId, description, filename);
    }
    private static final String waitingScheduleArrival = "MPTENGINE000011: %s SCHEDULED BULK Arrival - [%s/%s/%s]";
    protected String waitingScheduleArrival$str() {
        return waitingScheduleArrival;
    }
    @Override
    public final void timeoutBetweenOrderWasReached(final Throwable e, final long bulkId) {
        super.log.logf(FQCN, WARN, e, timeoutBetweenOrderWasReached$str(), bulkId);
    }
    private static final String timeoutBetweenOrderWasReached = "MPTENGINE000012: Timeout between orders was reached! Bulk [%s] will retry if resources available...";
    protected String timeoutBetweenOrderWasReached$str() {
        return timeoutBetweenOrderWasReached;
    }
    @Override
    public final void databaseConnectivityFailure(final Throwable e, final String description) {
        super.log.logf(FQCN, ERROR, e, databaseConnectivityFailure$str(), description);
    }
    private static final String databaseConnectivityFailure = "MPTENGINE000013: Database connectivity problem. %s ";
    protected String databaseConnectivityFailure$str() {
        return databaseConnectivityFailure;
    }
    @Override
    public final void failureUnregisteringOrderFromFamily(final String orderId, final String family, final long bulkId) {
        super.log.logf(FQCN, WARN, null, failureUnregisteringOrderFromFamily$str(), orderId, family, bulkId);
    }
    private static final String failureUnregisteringOrderFromFamily = "MPTENGINE000014: ResourceManager failed to unregister order [%s] family/bulkId [%s/%s]";
    protected String failureUnregisteringOrderFromFamily$str() {
        return failureUnregisteringOrderFromFamily;
    }
    @Override
    public final void failureRegisteringOrderToFamily(final String family, final long bulkId) {
        super.log.logf(FQCN, WARN, null, failureRegisteringOrderToFamily$str(), family, bulkId);
    }
    private static final String failureRegisteringOrderToFamily = "MPTENGINE000015: ResourceManager failed to register an order to family/bulkId [%s/%s]. Too much family concurrency found on the cluster!";
    protected String failureRegisteringOrderToFamily$str() {
        return failureRegisteringOrderToFamily;
    }
    @Override
    public final void resourcesExausted(final String family) {
        super.log.logf(FQCN, WARN, null, resourcesExausted$str(), family);
    }
    private static final String resourcesExausted = "MPTENGINE000016: Unsufficient resources from family [%s] of ResourceManager Module. Too much family concurrency! Retrying...";
    protected String resourcesExausted$str() {
        return resourcesExausted;
    }
    @Override
    public final void timeoutWhileWaitingForResources(final Throwable e, final long l, final String timeUnit, final long bulkId) {
        super.log.logf(FQCN, WARN, e, timeoutWhileWaitingForResources$str(), l, timeUnit, bulkId);
    }
    private static final String timeoutWhileWaitingForResources = "MPTENGINE000017: Timeout while waiting for resources was reached! The defined timeout is [%s/%s]. Bulk [%s]. Going to suspend this batch bulk...";
    protected String timeoutWhileWaitingForResources$str() {
        return timeoutWhileWaitingForResources;
    }
    @Override
    public final void timeoutWhileWaitingForBulk(final Long bulkId) {
        super.log.logf(FQCN, WARN, null, timeoutWhileWaitingForBulk$str(), bulkId);
    }
    private static final String timeoutWhileWaitingForBulk = "MPTENGINE000018: Timeout while waiting for bulk[%s] termination/suspension.";
    protected String timeoutWhileWaitingForBulk$str() {
        return timeoutWhileWaitingForBulk;
    }
    @Override
    public final void internalEngineError(final Throwable e, final Class clazz) {
        super.log.logf(FQCN, ERROR, e, internalEngineError$str(), clazz);
    }
    private static final String internalEngineError = "MPTENGINE000019: Internal MPT batch engine error while executing '%s' artifact";
    protected String internalEngineError$str() {
        return internalEngineError;
    }
    @Override
    public final void runningInsideBatchlet(final String artifactName) {
        super.log.logf(FQCN, DEBUG, null, runningInsideBatchlet$str(), artifactName);
    }
    private static final String runningInsideBatchlet = "MPTENGINE000020: Running inside a batchlet : %s";
    protected String runningInsideBatchlet$str() {
        return runningInsideBatchlet;
    }
    @Override
    public final void runningInsideDecider(final String artifactName) {
        super.log.logf(FQCN, DEBUG, null, runningInsideDecider$str(), artifactName);
    }
    private static final String runningInsideDecider = "MPTENGINE000021: Running inside decider : %s";
    protected String runningInsideDecider$str() {
        return runningInsideDecider;
    }
    @Override
    public final void deciderTransition(final String artifactName) {
        super.log.logf(FQCN, INFO, null, deciderTransition$str(), artifactName);
    }
    private static final String deciderTransition = "MPTENGINE000022: Decider will transition to the following artifact: %s";
    protected String deciderTransition$str() {
        return deciderTransition;
    }
    @Override
    public final void batchletWorkflowTransition(final WorkflowStates state) {
        super.log.logf(FQCN, DEBUG, null, batchletWorkflowTransition$str(), state);
    }
    private static final String batchletWorkflowTransition = "MPTENGINE000023: Batchlet will return the following workflow state - [%s]";
    protected String batchletWorkflowTransition$str() {
        return batchletWorkflowTransition;
    }
    @Override
    public final void requestSchedulerCancel(final String entity, final Request request) {
        super.log.logf(FQCN, WARN, null, requestSchedulerCancel$str(), entity, request);
    }
    private static final String requestSchedulerCancel = "MPTENGINE000024: %s will cancel all schedules from request [%s]";
    protected String requestSchedulerCancel$str() {
        return requestSchedulerCancel;
    }
    @Override
    public final void globalTimeoutWasReached(final String description, final Long timeoutValue) {
        super.log.logf(FQCN, ERROR, null, globalTimeoutWasReached$str(), description, timeoutValue);
    }
    private static final String globalTimeoutWasReached = "MPTENGINE000025: Global batch timeout was reached while %s. Value 'batchGlobalTimeoutMilis' should be increased via configuration service from '%s' milis to a higher value!!!";
    protected String globalTimeoutWasReached$str() {
        return globalTimeoutWasReached;
    }
    @Override
    public final void mptEventListenerError(final Throwable e) {
        super.log.logf(FQCN, ERROR, e, mptEventListenerError$str());
    }
    private static final String mptEventListenerError = "MPTENGINE000026: Internal engine guava subscriber error";
    protected String mptEventListenerError$str() {
        return mptEventListenerError;
    }
    @Override
    public final void sendEventToBatchEngineIfRunningFailure(final Throwable e) {
        super.log.logf(FQCN, ERROR, e, sendEventToBatchEngineIfRunningFailure$str());
    }
    private static final String sendEventToBatchEngineIfRunningFailure = "MPTENGINE000027: Failure while sending event to batch engine (if running)";
    protected String sendEventToBatchEngineIfRunningFailure$str() {
        return sendEventToBatchEngineIfRunningFailure;
    }
    @Override
    public final void waitForFamilyResourcesFailure(final Throwable e) {
        super.log.logf(FQCN, ERROR, e, waitForFamilyResourcesFailure$str());
    }
    private static final String waitForFamilyResourcesFailure = "MPTENGINE000028: Orders observer monitor wait for family resources guard failure";
    protected String waitForFamilyResourcesFailure$str() {
        return waitForFamilyResourcesFailure;
    }
    @Override
    public final void checkingIfRemainBulkOrdersGuardFailure(final Throwable e) {
        super.log.logf(FQCN, ERROR, e, checkingIfRemainBulkOrdersGuardFailure$str());
    }
    private static final String checkingIfRemainBulkOrdersGuardFailure = "MPTENGINE000029: Orders observer monitor checking if remain bulk orders guard failure";
    protected String checkingIfRemainBulkOrdersGuardFailure$str() {
        return checkingIfRemainBulkOrdersGuardFailure;
    }
    @Override
    public final void initWaitingBatchletFailure(final Throwable e) {
        super.log.logf(FQCN, ERROR, e, initWaitingBatchletFailure$str());
    }
    private static final String initWaitingBatchletFailure = "MPTENGINE000030: Failure while initializing waiting batchlet data";
    protected String initWaitingBatchletFailure$str() {
        return initWaitingBatchletFailure;
    }
    @Override
    public final void interruptedGuardFailure(final Throwable e) {
        super.log.logf(FQCN, ERROR, e, interruptedGuardFailure$str());
    }
    private static final String interruptedGuardFailure = "MPTENGINE000031: Failure in interrupted guard condition";
    protected String interruptedGuardFailure$str() {
        return interruptedGuardFailure;
    }
    @Override
    public final void resourcesAreNowAvailable(final String familyName) {
        super.log.logf(FQCN, INFO, null, resourcesAreNowAvailable$str(), familyName);
    }
    private static final String resourcesAreNowAvailable = "MPTENGINE000032: Resources for family '%s' are now available... Proceeding...";
    protected String resourcesAreNowAvailable$str() {
        return resourcesAreNowAvailable;
    }
    @Override
    public final void executingSummary(final Long bulkId, final int orders, final Long elapsedMilis) {
        super.log.logf(FQCN, INFO, null, executingSummary$str(), bulkId, orders, elapsedMilis);
    }
    private static final String executingSummary = "MPTENGINE000033: Finished processing orders for bulk id [%s] inside ExecutingBatchlet. Processed %s orders in %s ms";
    protected String executingSummary$str() {
        return executingSummary;
    }
    @Override
    public final void executingProcessingOrder(final Long bulkId) {
        super.log.logf(FQCN, DEBUG, null, executingProcessingOrder$str(), bulkId);
    }
    private static final String executingProcessingOrder = "MPTENGINE000034: ExecutingBatchlet is processing order for bulk id [%s]";
    protected String executingProcessingOrder$str() {
        return executingProcessingOrder;
    }
    @Override
    public final void executingNullOrder() {
        super.log.logf(FQCN, WARN, null, executingNullOrder$str());
    }
    private static final String executingNullOrder = "MPTENGINE000035: ExecutingBatchlet retrieved a null/empty order from OrderProvider. Too many order keys for the same operation maybe!";
    protected String executingNullOrder$str() {
        return executingNullOrder;
    }
    @Override
    public final void processingOrder(final String orderId, final Long bulkId) {
        super.log.logf(FQCN, INFO, null, processingOrder$str(), orderId, bulkId);
    }
    private static final String processingOrder = "MPTENGINE000036: Going to process order with id '%s' from bulk id '%s'";
    protected String processingOrder$str() {
        return processingOrder;
    }
    @Override
    public final void processedOrder(final String orderId, final Long bulkId, final String status) {
        super.log.logf(FQCN, INFO, null, processedOrder$str(), orderId, bulkId, status);
    }
    private static final String processedOrder = "MPTENGINE000037: Processed order with id '%s' from bulk id '%s' ended with an execution status of [%s]";
    protected String processedOrder$str() {
        return processedOrder;
    }
    @Override
    public final void ordersRemaining(final Long bulkId, final int size) {
        super.log.logf(FQCN, INFO, null, ordersRemaining$str(), bulkId, size);
    }
    private static final String ordersRemaining = "MPTENGINE000038: Bulk with id '%s' has [%s] remaining orders to process";
    protected String ordersRemaining$str() {
        return ordersRemaining;
    }
    @Override
    public final void waitingSummary(final Long bulkId, final Long elapsedMilis) {
        super.log.logf(FQCN, INFO, null, waitingSummary$str(), bulkId, elapsedMilis);
    }
    private static final String waitingSummary = "MPTENGINE000039: Finished waiting for previous processed orders for bulk id [%s]. Waiting took %s ms";
    protected String waitingSummary$str() {
        return waitingSummary;
    }
    @Override
    public final void eventBusThreadUnregister(final String name) {
        super.log.logf(FQCN, TRACE, null, eventBusThreadUnregister$str(), name);
    }
    private static final String eventBusThreadUnregister = "MPTENGINE000040: Unregistered Thread[%s] to Guava EventBus!";
    protected String eventBusThreadUnregister$str() {
        return eventBusThreadUnregister;
    }
    @Override
    public final void eventBusThreadRegister(final String name) {
        super.log.logf(FQCN, TRACE, null, eventBusThreadRegister$str(), name);
    }
    private static final String eventBusThreadRegister = "MPTENGINE000041: Registered Thread[%s] to Guava EventBus!";
    protected String eventBusThreadRegister$str() {
        return eventBusThreadRegister;
    }
    @Override
    public final void eventBusUndeliver(final Object event, final Object source) {
        super.log.logf(FQCN, WARN, null, eventBusUndeliver$str(), event, source);
    }
    private static final String eventBusUndeliver = "MPTENGINE000042: Received an undelivered engine event... EVENT[%s/%s]";
    protected String eventBusUndeliver$str() {
        return eventBusUndeliver;
    }
    @Override
    public final void eventBusReception(final String className, final String threadName, final String eventPayload) {
        super.log.logf(FQCN, INFO, null, eventBusReception$str(), className, threadName, eventPayload);
    }
    private static final String eventBusReception = "MPTENGINE000043: Received an engine event on class '%s' living in Thread '%s'. EVENT[%s]";
    protected String eventBusReception$str() {
        return eventBusReception;
    }
    @Override
    public final void batchListenerInitProps(final String properties) {
        super.log.logf(FQCN, INFO, null, batchListenerInitProps$str(), properties);
    }
    private static final String batchListenerInitProps = "MPTENGINE000044: Batch listener thread initiliazed with the following properties context: %s";
    protected String batchListenerInitProps$str() {
        return batchListenerInitProps;
    }
    @Override
    public final void batchListenerBeforeJob(final String jobName, final Long bulkId, final String requestPayload) {
        super.log.logf(FQCN, INFO, null, batchListenerBeforeJob$str(), jobName, bulkId, requestPayload);
    }
    private static final String batchListenerBeforeJob = "MPTENGINE000045: Batch listener thread for job '%s' is initializing at before job phase for bulk with id '%s'... Request -> %s";
    protected String batchListenerBeforeJob$str() {
        return batchListenerBeforeJob;
    }
    @Override
    public final void batchListenerBeforeStep(final String jobName, final Long bulkId) {
        super.log.logf(FQCN, INFO, null, batchListenerBeforeStep$str(), jobName, bulkId);
    }
    private static final String batchListenerBeforeStep = "MPTENGINE000046: Batch listener thread is starting step '%s' for bulk id '%s'";
    protected String batchListenerBeforeStep$str() {
        return batchListenerBeforeStep;
    }
    @Override
    public final void batchListenerAfterStep(final String jobName, final Long bulkId) {
        super.log.logf(FQCN, INFO, null, batchListenerAfterStep$str(), jobName, bulkId);
    }
    private static final String batchListenerAfterStep = "MPTENGINE000047: Batch listener thread is ending step '%s' for bulk id '%s'";
    protected String batchListenerAfterStep$str() {
        return batchListenerAfterStep;
    }
    @Override
    public final void ordersObserverTwiceError(final String orderId) {
        super.log.logf(FQCN, ERROR, null, ordersObserverTwiceError$str(), orderId);
    }
    private static final String ordersObserverTwiceError = "MPTENGINE000048: Processed orders observer received twice a same order with id %s!!!";
    protected String ordersObserverTwiceError$str() {
        return ordersObserverTwiceError;
    }
    @Override
    public final void ordersObserverEventArrival(final String orderEventString) {
        super.log.logf(FQCN, INFO, null, ordersObserverEventArrival$str(), orderEventString);
    }
    private static final String ordersObserverEventArrival = "MPTENGINE000049: Processed orders observer received a processed order event %s";
    protected String ordersObserverEventArrival$str() {
        return ordersObserverEventArrival;
    }
    @Override
    public final void ordersObserverOrderRemoval(final String orderId, final String ordersList, final int total) {
        super.log.logf(FQCN, DEBUG, null, ordersObserverOrderRemoval$str(), orderId, ordersList, total);
    }
    private static final String ordersObserverOrderRemoval = "MPTENGINE000050: Processed orders observer removed order '%s' from list -> %s. [Total] - [%s]";
    protected String ordersObserverOrderRemoval$str() {
        return ordersObserverOrderRemoval;
    }
    @Override
    public final void ordersObserverUnknownOrderArrival(final String orderId, final Long bulkId) {
        super.log.logf(FQCN, WARN, null, ordersObserverUnknownOrderArrival$str(), orderId, bulkId);
    }
    private static final String ordersObserverUnknownOrderArrival = "MPTENGINE000051: Processed orders observer received unknown processed order '%s' from bulk %s!!!";
    protected String ordersObserverUnknownOrderArrival$str() {
        return ordersObserverUnknownOrderArrival;
    }
    @Override
    public final void ordersObserverError(final Throwable e, final String event) {
        super.log.logf(FQCN, FATAL, e, ordersObserverError$str(), event);
    }
    private static final String ordersObserverError = "MPTENGINE000052: Processed orders observer severe error for event: %s";
    protected String ordersObserverError$str() {
        return ordersObserverError;
    }
    @Override
    public final void batchOperationsInit() {
        super.log.logf(FQCN, DEBUG, null, batchOperationsInit$str());
    }
    private static final String batchOperationsInit = "MPTENGINE000053: Building BatchOperations bean...";
    protected String batchOperationsInit$str() {
        return batchOperationsInit;
    }
    @Override
    public final void batchOperationsMemJobsFlush(final Long milis) {
        super.log.logf(FQCN, WARN, null, batchOperationsMemJobsFlush$str(), milis);
    }
    private static final String batchOperationsMemJobsFlush = "MPTENGINE000054: Batch operations bean flushed all batch in memory jobs from job operator and took %s ms";
    protected String batchOperationsMemJobsFlush$str() {
        return batchOperationsMemJobsFlush;
    }
    @Override
    public final void batchOperationsJobState(final Long id, final String state) {
        super.log.logf(FQCN, INFO, null, batchOperationsJobState$str(), id, state);
    }
    private static final String batchOperationsJobState = "MPTENGINE000055: Job with execution id '%s' was found on a BatchState of '%s'";
    protected String batchOperationsJobState$str() {
        return batchOperationsJobState;
    }
    @Override
    public final void batchJobExecutionTookXMilis(final Long id, final Long milis) {
        super.log.logf(FQCN, INFO, null, batchJobExecutionTookXMilis$str(), id, milis);
    }
    private static final String batchJobExecutionTookXMilis = "MPTENGINE000056: Batch job with execution id '%s' took '%s' ms to complete...";
    protected String batchJobExecutionTookXMilis$str() {
        return batchJobExecutionTookXMilis;
    }
    @Override
    public final void localFileStoreJobParams(final Properties properties) {
        super.log.logf(FQCN, DEBUG, null, localFileStoreJobParams$str(), properties);
    }
    private static final String localFileStoreJobParams = "MPTENGINE000057: Local file store will use the following job parameters: %s";
    protected String localFileStoreJobParams$str() {
        return localFileStoreJobParams;
    }
    @Override
    public final void cleanLocalFileStoreRemoveDir(final String dirName) {
        super.log.logf(FQCN, INFO, null, cleanLocalFileStoreRemoveDir$str(), dirName);
    }
    private static final String cleanLocalFileStoreRemoveDir = "MPTENGINE000058: Clean local file store will remove directory '%s'";
    protected String cleanLocalFileStoreRemoveDir$str() {
        return cleanLocalFileStoreRemoveDir;
    }
    @Override
    public final void fileCompressorDirs(final String downloadDir, final String gzipFile) {
        super.log.logf(FQCN, INFO, null, fileCompressorDirs$str(), downloadDir, gzipFile);
    }
    private static final String fileCompressorDirs = "MPTENGINE000059: File compressor will use the following locations: - download directory [%s] - output gzip file [%s]";
    protected String fileCompressorDirs$str() {
        return fileCompressorDirs;
    }
    @Override
    public final void fileCompressorGzip(final String fileName, final String gzipFileName) {
        super.log.logf(FQCN, INFO, null, fileCompressorGzip2$str(), fileName, gzipFileName);
    }
    private static final String fileCompressorGzip2 = "MPTENGINE000060: File compressor will compress the file '%s' to gzip as '%s'";
    protected String fileCompressorGzip2$str() {
        return fileCompressorGzip2;
    }
    @Override
    public final void fileCompressorGzip() {
        super.log.logf(FQCN, INFO, null, fileCompressorGzip0$str());
    }
    private static final String fileCompressorGzip0 = "MPTENGINE000061: File compressor is done with creating the file archive";
    protected String fileCompressorGzip0$str() {
        return fileCompressorGzip0;
    }
    @Override
    public final void fileCompressorGzip(final Throwable e, final String cause) {
        super.log.logf(FQCN, ERROR, e, fileCompressorGzip1$str(), cause);
    }
    private static final String fileCompressorGzip1 = "MPTENGINE000062: File compressor failed while creating the file archive due to: %s";
    protected String fileCompressorGzip1$str() {
        return fileCompressorGzip1;
    }
    @Override
    public final void fileCompressorGenerateFileList(final String list) {
        super.log.logf(FQCN, DEBUG, null, fileCompressorGenerateFileList$str(), list);
    }
    private static final String fileCompressorGenerateFileList = "MPTENGINE000063: File compressor found the following files while generating file list: %s";
    protected String fileCompressorGenerateFileList$str() {
        return fileCompressorGenerateFileList;
    }
    @Override
    public final void localFileStoreCreateGzipDir(final String dir, final boolean created) {
        super.log.logf(FQCN, INFO, null, localFileStoreCreateGzipDir$str(), dir, created);
    }
    private static final String localFileStoreCreateGzipDir = "MPTENGINE000064: Local file store created the following temporary output gzip directory '%s'. Creation of dirs returned '%s'";
    protected String localFileStoreCreateGzipDir$str() {
        return localFileStoreCreateGzipDir;
    }
    @Override
    public final void checkIfRequestHasSchedules(final String list) {
        super.log.logf(FQCN, INFO, null, checkIfRequestHasSchedules$str(), list);
    }
    private static final String checkIfRequestHasSchedules = "MPTENGINE000065: Checking if request has more schedules of event list '%s'";
    protected String checkIfRequestHasSchedules$str() {
        return checkIfRequestHasSchedules;
    }
    @Override
    public final void checkIfRequestHasSchedulesFoundLast(final String last) {
        super.log.logf(FQCN, INFO, null, checkIfRequestHasSchedulesFoundLast$str(), last);
    }
    private static final String checkIfRequestHasSchedulesFoundLast = "MPTENGINE000066: While checking if request has more schedules it found the last element of the schedule list -> %s";
    protected String checkIfRequestHasSchedulesFoundLast$str() {
        return checkIfRequestHasSchedulesFoundLast;
    }
    @Override
    public final void loadingOrdersFromProvider(final Long bulkId) {
        super.log.logf(FQCN, INFO, null, loadingOrdersFromProvider$str(), bulkId);
    }
    private static final String loadingOrdersFromProvider = "MPTENGINE000067: Going to load orders from order provider to bulk '%s'...";
    protected String loadingOrdersFromProvider$str() {
        return loadingOrdersFromProvider;
    }
    @Override
    public final void waitForResourcesGuardNoOther() {
        super.log.logf(FQCN, INFO, null, waitForResourcesGuardNoOther$str());
    }
    private static final String waitForResourcesGuardNoOther = "MPTENGINE000068: Wait for resources guard found no other family running bulk orders";
    protected String waitForResourcesGuardNoOther$str() {
        return waitForResourcesGuardNoOther;
    }
    @Override
    public final void waitForResourcesGuardOther() {
        super.log.logf(FQCN, INFO, null, waitForResourcesGuardOther$str());
    }
    private static final String waitForResourcesGuardOther = "MPTENGINE000069: Wait for resources guard found other family running bulk orders";
    protected String waitForResourcesGuardOther$str() {
        return waitForResourcesGuardOther;
    }
    @Override
    public final void waitForResourcesGuardMaxOrders(final String list) {
        super.log.logf(FQCN, INFO, null, waitForResourcesGuardMaxOrders$str(), list);
    }
    private static final String waitForResourcesGuardMaxOrders = "MPTENGINE000070: Wait for resources guard found the max allowed orders per bulk list -> %s";
    protected String waitForResourcesGuardMaxOrders$str() {
        return waitForResourcesGuardMaxOrders;
    }
    @Override
    public final void waitForResourcesGuardMaxOrdersCmp(final int maxSimultaneousOrders, final int maxConcurrentOrders, final long milis) {
        super.log.logf(FQCN, INFO, null, waitForResourcesGuardMaxOrdersCmp$str(), maxSimultaneousOrders, maxConcurrentOrders, milis);
    }
    private static final String waitForResourcesGuardMaxOrdersCmp = "MPTENGINE000071: Wait for resources guard compared [maxSimultaneousOrders %s < maxConcurrentOrders %s]. Comparison took %s ms";
    protected String waitForResourcesGuardMaxOrdersCmp$str() {
        return waitForResourcesGuardMaxOrdersCmp;
    }
    @Override
    public final void deciderFinished(final String name, final String exitStatus) {
        super.log.logf(FQCN, INFO, null, deciderFinished$str(), name, exitStatus);
    }
    private static final String deciderFinished = "MPTENGINE000072: Decider '%s' will exit with the following state '%s'";
    protected String deciderFinished$str() {
        return deciderFinished;
    }
    @Override
    public final void deciderPreviousExecsFound(final String name) {
        super.log.logf(FQCN, INFO, null, deciderPreviousExecsFound$str(), name);
    }
    private static final String deciderPreviousExecsFound = "MPTENGINE000073: Previous executions were found on decider '%s' meaning that it's not a batch resume...";
    protected String deciderPreviousExecsFound$str() {
        return deciderPreviousExecsFound;
    }
    @Override
    public final void engineScheduleEventObserver(final Throwable e, final String event) {
        super.log.logf(FQCN, ERROR, e, engineScheduleEventObserver$str(), event);
    }
    private static final String engineScheduleEventObserver = "MPTENGINE000074: Engine schedule event observer could not schedule event %s";
    protected String engineScheduleEventObserver$str() {
        return engineScheduleEventObserver;
    }
    @Override
    public final void resourcesInitFamilyCache() {
        super.log.logf(FQCN, INFO, null, resourcesInitFamilyCache$str());
    }
    private static final String resourcesInitFamilyCache = "MPTENGINE000075: Engine resources is initializing [RESOURCE MANAGER] Family Resources cache";
    protected String resourcesInitFamilyCache$str() {
        return resourcesInitFamilyCache;
    }
    @Override
    public final void resourcesInitFamilyCacheSuccess() {
        super.log.logf(FQCN, INFO, null, resourcesInitFamilyCacheSuccess$str());
    }
    private static final String resourcesInitFamilyCacheSuccess = "MPTENGINE000076: Engine resources initialized [RESOURCE MANAGER] Family Resources cache successfully";
    protected String resourcesInitFamilyCacheSuccess$str() {
        return resourcesInitFamilyCacheSuccess;
    }
    @Override
    public final void resourcesInitCacheListener() {
        super.log.logf(FQCN, INFO, null, resourcesInitCacheListener$str());
    }
    private static final String resourcesInitCacheListener = "MPTENGINE000077: Engine resources is initializing [BATCH ENGINE REQUEST CACHE Listener]";
    protected String resourcesInitCacheListener$str() {
        return resourcesInitCacheListener;
    }
    @Override
    public final void resourcesInitCacheListenerBound() {
        super.log.logf(FQCN, INFO, null, resourcesInitCacheListenerBound$str());
    }
    private static final String resourcesInitCacheListenerBound = "MPTENGINE000078: Engine resources found a fresh instance of a Batch Listener bound it to Request Cache";
    protected String resourcesInitCacheListenerBound$str() {
        return resourcesInitCacheListenerBound;
    }
    @Override
    public final void resourcesInitCacheListenerSuccess() {
        super.log.logf(FQCN, INFO, null, resourcesInitCacheListenerSuccess$str());
    }
    private static final String resourcesInitCacheListenerSuccess = "MPTENGINE000079: Engine resources initialized [BATCH ENGINE REQUEST CACHE Listener] successfully";
    protected String resourcesInitCacheListenerSuccess$str() {
        return resourcesInitCacheListenerSuccess;
    }
    @Override
    public final void resourcesDestroy() {
        super.log.logf(FQCN, INFO, null, resourcesDestroy$str());
    }
    private static final String resourcesDestroy = "MPTENGINE000080: Engine resources is shutting down... Removing [BATCH ENGINE REQUEST CACHE Listener] and flushing cache...";
    protected String resourcesDestroy$str() {
        return resourcesDestroy;
    }
    @Override
    public final void resourcesDestroySuccess() {
        super.log.logf(FQCN, INFO, null, resourcesDestroySuccess$str());
    }
    private static final String resourcesDestroySuccess = "MPTENGINE000081: Engine resources removed [BATCH ENGINE REQUEST CACHE Listener] and flushed cache successfully";
    protected String resourcesDestroySuccess$str() {
        return resourcesDestroySuccess;
    }
    @Override
    public final void resourcesDestroyListenerRemoval() {
        super.log.logf(FQCN, INFO, null, resourcesDestroyListenerRemoval$str());
    }
    private static final String resourcesDestroyListenerRemoval = "MPTENGINE000082: Engine resources removed a [BATCH ENGINE REQUEST CACHE Listener] instance";
    protected String resourcesDestroyListenerRemoval$str() {
        return resourcesDestroyListenerRemoval;
    }
    @Override
    public final void batchOperationsDestroy() {
        super.log.logf(FQCN, DEBUG, null, batchOperationsDestroy$str());
    }
    private static final String batchOperationsDestroy = "MPTENGINE000083: Destroying BatchOperations bean...";
    protected String batchOperationsDestroy$str() {
        return batchOperationsDestroy;
    }
    @Override
    public final void engineSchedulerTaskInit(final String taskName) {
        super.log.logf(FQCN, INFO, null, engineSchedulerTaskInit$str(), taskName);
    }
    private static final String engineSchedulerTaskInit = "MPTENGINE000084: Engine scheduler task is initializing scheduler task %s...";
    protected String engineSchedulerTaskInit$str() {
        return engineSchedulerTaskInit;
    }
    @Override
    public final void engineSchedulerTaskEndWindowEvent(final Long bulkId, final String familyName) {
        super.log.logf(FQCN, INFO, null, engineSchedulerTaskEndWindowEvent$str(), bulkId, familyName);
    }
    private static final String engineSchedulerTaskEndWindowEvent = "MPTENGINE000085: Engine scheduler task for bulk id '%s' from family [%s] is running... Going to send a end time window event to the batch engine and wait until batch ends!";
    protected String engineSchedulerTaskEndWindowEvent$str() {
        return engineSchedulerTaskEndWindowEvent;
    }
    @Override
    public final void engineSchedulerTaskRestart(final Long bulkId, final String familyName) {
        super.log.logf(FQCN, INFO, null, engineSchedulerTaskRestart$str(), bulkId, familyName);
    }
    private static final String engineSchedulerTaskRestart = "MPTENGINE000086: Engine scheduler task for bulk id '%s' from family [%s] is not running... Going to restart batch engine!";
    protected String engineSchedulerTaskRestart$str() {
        return engineSchedulerTaskRestart;
    }
    @Override
    public final void engineSchedulerTaskInelegibleBulkFailure(final Long bulkId, final Long batchExecutionId) {
        super.log.logf(FQCN, WARN, null, engineSchedulerTaskInelegibleBulkFailure$str(), bulkId, batchExecutionId);
    }
    private static final String engineSchedulerTaskInelegibleBulkFailure = "MPTENGINE000087: Engine scheduler task found a bulk id '%s' not elegible to start/resume. Batch workflow execution id[%s]";
    protected String engineSchedulerTaskInelegibleBulkFailure$str() {
        return engineSchedulerTaskInelegibleBulkFailure;
    }
    @Override
    public final void engineSchedulerTaskInvalidBulk(final Long bulkId, final String familyName) {
        super.log.logf(FQCN, ERROR, null, engineSchedulerTaskInvalidBulk$str(), bulkId, familyName);
    }
    private static final String engineSchedulerTaskInvalidBulk = "MPTENGINE000088: Engine scheduler task did not found bulk transition for bulkId '%s' from family [%s]. Bulk is in an invalid state....";
    protected String engineSchedulerTaskInvalidBulk$str() {
        return engineSchedulerTaskInvalidBulk;
    }
    @Override
    public final void engineSchedulerTaskFinished(final String taskName, final String result) {
        super.log.logf(FQCN, INFO, null, engineSchedulerTaskFinished$str(), taskName, result);
    }
    private static final String engineSchedulerTaskFinished = "MPTENGINE000089: Engine scheduler task '%s' finished! The computed scheduled ACTION is '%s'";
    protected String engineSchedulerTaskFinished$str() {
        return engineSchedulerTaskFinished;
    }
    @Override
    public final void engineSchedulerTaskError(final Throwable e) {
        super.log.logf(FQCN, ERROR, e, engineSchedulerTaskError$str());
    }
    private static final String engineSchedulerTaskError = "MPTENGINE000090: Engine scheduler task internal error";
    protected String engineSchedulerTaskError$str() {
        return engineSchedulerTaskError;
    }
    @Override
    public final void recoverEngineInit() {
        super.log.logf(FQCN, INFO, null, recoverEngineInit$str());
    }
    private static final String recoverEngineInit = "MPTENGINE000091: Recover engine is initializing... Going to sanitize bulks and their schedules...";
    protected String recoverEngineInit$str() {
        return recoverEngineInit;
    }
    @Override
    public final void recoverEngineBulkFound(final Long bulkId, final String transition) {
        super.log.logf(FQCN, INFO, null, recoverEngineBulkFound$str(), bulkId, transition);
    }
    private static final String recoverEngineBulkFound = "MPTENGINE000092: Recover engine is processing bulk id %s for recover. Last known bulk transition status was '%s'...";
    protected String recoverEngineBulkFound$str() {
        return recoverEngineBulkFound;
    }
    @Override
    public final void recoverEngineBulkFoundNextState(final Long bulkId, final String transition) {
        super.log.logf(FQCN, INFO, null, recoverEngineBulkFoundNextState$str(), bulkId, transition);
    }
    private static final String recoverEngineBulkFoundNextState = "MPTENGINE000093: Recover engine for Bulk id %s calculated its next state as '%s' (May change if batch engine runs ok)";
    protected String recoverEngineBulkFoundNextState$str() {
        return recoverEngineBulkFoundNextState;
    }
    @Override
    public final void recoverEngineResultStrategy(final String request, final String strategy) {
        super.log.logf(FQCN, INFO, null, recoverEngineResultStrategy$str(), request, strategy);
    }
    private static final String recoverEngineResultStrategy = "MPTENGINE000094: Recover engine returned the following request from recover -> %s using the strategy '%s'";
    protected String recoverEngineResultStrategy$str() {
        return recoverEngineResultStrategy;
    }
    @Override
    public final void recoverEngineStrategyFail(final String strategy) {
        super.log.logf(FQCN, WARN, null, recoverEngineStrategyFail$str(), strategy);
    }
    private static final String recoverEngineStrategyFail = "MPTENGINE000095: Recover engine failed using '%s' strategy";
    protected String recoverEngineStrategyFail$str() {
        return recoverEngineStrategyFail;
    }
    @Override
    public final void recoverEngineNoTransOrValid(final Long bulkId) {
        super.log.logf(FQCN, WARN, null, recoverEngineNoTransOrValid$str(), bulkId);
    }
    private static final String recoverEngineNoTransOrValid = "MPTENGINE000096: Recover engine found no bulk transitions for bulkId '%s' or bulk was found in valid state... discarding...";
    protected String recoverEngineNoTransOrValid$str() {
        return recoverEngineNoTransOrValid;
    }
    @Override
    public final void recoverEngineBatchStatusInvalid() {
        super.log.logf(FQCN, WARN, null, recoverEngineBatchStatusInvalid$str());
    }
    private static final String recoverEngineBatchStatusInvalid = "MPTENGINE000097: Recover engine found that BatchStatus is not in a valid state to check if fixed...";
    protected String recoverEngineBatchStatusInvalid$str() {
        return recoverEngineBatchStatusInvalid;
    }
    @Override
    public final void recoverEngineStartScheduling() {
        super.log.logf(FQCN, INFO, null, recoverEngineStartScheduling$str());
    }
    private static final String recoverEngineStartScheduling = "MPTENGINE000098: Recover engine will start rebuilding and sanitizing bulk schedules";
    protected String recoverEngineStartScheduling$str() {
        return recoverEngineStartScheduling;
    }
    @Override
    public final void recoverEngineSchedulingBulk(final Long bulkId, final String state) {
        super.log.logf(FQCN, INFO, null, recoverEngineSchedulingBulk$str(), bulkId, state);
    }
    private static final String recoverEngineSchedulingBulk = "MPTENGINE000099: Recover engine found a valid bulk to recover: id %s at state %s";
    protected String recoverEngineSchedulingBulk$str() {
        return recoverEngineSchedulingBulk;
    }
    @Override
    public final void recoverEngineSchedulingFailure(final Throwable e) {
        super.log.logf(FQCN, ERROR, e, recoverEngineSchedulingFailure$str());
    }
    private static final String recoverEngineSchedulingFailure = "MPTENGINE000100: Recover engine failed while rebuilding and sanitizing bulk schedules...";
    protected String recoverEngineSchedulingFailure$str() {
        return recoverEngineSchedulingFailure;
    }
    @Override
    public final void recoverEngineFinishedScheduling() {
        super.log.logf(FQCN, INFO, null, recoverEngineFinishedScheduling$str());
    }
    private static final String recoverEngineFinishedScheduling = "MPTENGINE000101: Recover engine finished rebuilding and sanitizing bulk schedules";
    protected String recoverEngineFinishedScheduling$str() {
        return recoverEngineFinishedScheduling;
    }
    @Override
    public final void recoverEngineFinishedSummary(final int bulks, final int schedules, final long milis) {
        super.log.logf(FQCN, INFO, null, recoverEngineFinishedSummary$str(), bulks, schedules, milis);
    }
    private static final String recoverEngineFinishedSummary = "MPTENGINE000102: Recover engine finished and recovered %s bulks and %s bulk schedules in %s ms";
    protected String recoverEngineFinishedSummary$str() {
        return recoverEngineFinishedSummary;
    }
    @Override
    public final void recoverEngineJobPurge(final String jobType, final String ids) {
        super.log.logf(FQCN, INFO, null, recoverEngineJobPurge$str(), jobType, ids);
    }
    private static final String recoverEngineJobPurge = "MPTENGINE000103: Recover engine will purge a job type '%s'. List of ids to purge: %s";
    protected String recoverEngineJobPurge$str() {
        return recoverEngineJobPurge;
    }
    @Override
    public final void recoverEngineJobPurgeMetric(final String jobType, final long milis) {
        super.log.logf(FQCN, INFO, null, recoverEngineJobPurgeMetric$str(), jobType, milis);
    }
    private static final String recoverEngineJobPurgeMetric = "MPTENGINE000104: Recover engine purged job [%s] and took %s ms to conclude...";
    protected String recoverEngineJobPurgeMetric$str() {
        return recoverEngineJobPurgeMetric;
    }
    @Override
    public final void recoverEngineJobAbandon(final Long execId, final Long bulkId) {
        super.log.logf(FQCN, INFO, null, recoverEngineJobAbandon$str(), execId, bulkId);
    }
    private static final String recoverEngineJobAbandon = "MPTENGINE000105: Recover engine going to abandon job execution %s for bulk %s";
    protected String recoverEngineJobAbandon$str() {
        return recoverEngineJobAbandon;
    }
    @Override
    public final void recoverEngineUpdatedOrders(final int orders, final Long bulkId, final String rawResult) {
        super.log.logf(FQCN, INFO, null, recoverEngineUpdatedOrders$str(), orders, bulkId, rawResult);
    }
    private static final String recoverEngineUpdatedOrders = "MPTENGINE000106: Recover engine updated %d orders from bulkId %s to ERROR execution state and NOK status. BulkWriteResult -> %s";
    protected String recoverEngineUpdatedOrders$str() {
        return recoverEngineUpdatedOrders;
    }
    @Override
    public final void recoverEngineClosestValidStep(final String request) {
        super.log.logf(FQCN, INFO, null, recoverEngineClosestValidStep$str(), request);
    }
    private static final String recoverEngineClosestValidStep = "MPTENGINE000107: Recover engine going to recover to the closest valid previous batch execution. Request: %s";
    protected String recoverEngineClosestValidStep$str() {
        return recoverEngineClosestValidStep;
    }
    @Override
    public final void recoverEnginePersistentDataRecover(final String step, final String data) {
        super.log.logf(FQCN, INFO, null, recoverEnginePersistentDataRecover$str(), step, data);
    }
    private static final String recoverEnginePersistentDataRecover = "MPTENGINE000108: Recover engine restoring request from step %s with following persistent data: %s";
    protected String recoverEnginePersistentDataRecover$str() {
        return recoverEnginePersistentDataRecover;
    }
    @Override
    public final void recoverEnginePreviousDefinedStep(final String fallState, final String exitStatus, final String restartPosition, final Long excecId, final String request) {
        super.log.logf(FQCN, INFO, null, recoverEnginePreviousDefinedStep$str(), fallState, exitStatus, restartPosition, excecId, request);
    }
    private static final String recoverEnginePreviousDefinedStep = "MPTENGINE000109: Recover engine going to recover to the previous defined by [%s/%s/%s] from batch with execution id %s. Request: %s";
    protected String recoverEnginePreviousDefinedStep$str() {
        return recoverEnginePreviousDefinedStep;
    }
    @Override
    public final void recoverEngineStepExecUpdate(final String stepName, final Long execId, final String statusBefore, final String statusAfter, final String exitStatusBefore, final String exitStatusAfter) {
        super.log.logf(FQCN, INFO, null, recoverEngineStepExecUpdate$str(), stepName, execId, statusBefore, statusAfter, exitStatusBefore, exitStatusAfter);
    }
    private static final String recoverEngineStepExecUpdate = "MPTENGINE000110: Recover engine is updating step execution name %s with id %s - BatchStatus %s -> %s | ExitStatus %s -> %s";
    protected String recoverEngineStepExecUpdate$str() {
        return recoverEngineStepExecUpdate;
    }
    @Override
    public final void recoverEngineStartPurgeNotMostRecentJob() {
        super.log.logf(FQCN, INFO, null, recoverEngineStartPurgeNotMostRecentJob$str());
    }
    private static final String recoverEngineStartPurgeNotMostRecentJob = "MPTENGINE000111: Recover engine going to purge the not most recent execution...";
    protected String recoverEngineStartPurgeNotMostRecentJob$str() {
        return recoverEngineStartPurgeNotMostRecentJob;
    }
    @Override
    public final void recoverEngineStartGenerateRequest() {
        super.log.logf(FQCN, INFO, null, recoverEngineStartGenerateRequest$str());
    }
    private static final String recoverEngineStartGenerateRequest = "MPTENGINE000112: Recover engine going to generate a request from last bulk...";
    protected String recoverEngineStartGenerateRequest$str() {
        return recoverEngineStartGenerateRequest;
    }
    @Override
    public final void recoverEngineStartGenerateRequestSanitize(final String request) {
        super.log.logf(FQCN, INFO, null, recoverEngineStartGenerateRequestSanitize$str(), request);
    }
    private static final String recoverEngineStartGenerateRequestSanitize = "MPTENGINE000113: Recover engine sanitized previous generated request: %s";
    protected String recoverEngineStartGenerateRequestSanitize$str() {
        return recoverEngineStartGenerateRequestSanitize;
    }
    @Override
    public final void recoverEngineEventObserver(final String event) {
        super.log.logf(FQCN, INFO, null, recoverEngineEventObserver$str(), event);
    }
    private static final String recoverEngineEventObserver = "MPTENGINE000114: Recover engine event observer is going to process a RECOVER EVENT: %s";
    protected String recoverEngineEventObserver$str() {
        return recoverEngineEventObserver;
    }
    @Override
    public final void recoverEngineEventObserverInvalidEvent(final Long bulkId) {
        super.log.logf(FQCN, ERROR, null, recoverEngineEventObserverInvalidEvent$str(), bulkId);
    }
    private static final String recoverEngineEventObserverInvalidEvent = "MPTENGINE000115: Recover engine event observer found no bulk transitions for bulkId '%s'. Discarding...";
    protected String recoverEngineEventObserverInvalidEvent$str() {
        return recoverEngineEventObserverInvalidEvent;
    }
    @Override
    public final void pendingDeciderResuming(final String request) {
        super.log.logf(FQCN, INFO, null, pendingDeciderResuming$str(), request);
    }
    private static final String pendingDeciderResuming = "MPTENGINE000116: Pending decider is resuming from previous execution. Request content -> %s";
    protected String pendingDeciderResuming$str() {
        return pendingDeciderResuming;
    }
    @Override
    public final void pendingDeciderResumingFromStop(final String request, final boolean interrupted) {
        super.log.logf(FQCN, INFO, null, pendingDeciderResumingFromStop$str(), request, interrupted);
    }
    private static final String pendingDeciderResumingFromStop = "MPTENGINE000117: Pending decider has no previous execution. Resuming from STOPPED job. Request content -> %s. Is interrupted %s";
    protected String pendingDeciderResumingFromStop$str() {
        return pendingDeciderResumingFromStop;
    }
    @Override
    public final void pendingDeciderDecision(final String decision, final String request, final boolean interrupted) {
        super.log.logf(FQCN, INFO, null, pendingDeciderDecision$str(), decision, request, interrupted);
    }
    private static final String pendingDeciderDecision = "MPTENGINE000118: Pending decider is returning a '%s' decision. Request content -> %s. Is interrupted %s";
    protected String pendingDeciderDecision$str() {
        return pendingDeciderDecision;
    }
    @Override
    public final void chunkReaderFileInjection(final String fileName) {
        super.log.logf(FQCN, DEBUG, null, chunkReaderFileInjection$str(), fileName);
    }
    private static final String chunkReaderFileInjection = "MPTENGINE000119: Order item reader injected file for reading bulk orders = %s";
    protected String chunkReaderFileInjection$str() {
        return chunkReaderFileInjection;
    }
    @Override
    public final void chunkReaderLine(final String line) {
        super.log.logf(FQCN, TRACE, null, chunkReaderLine$str(), line);
    }
    private static final String chunkReaderLine = "MPTENGINE000120: Order item reader is reading line [%s]";
    protected String chunkReaderLine$str() {
        return chunkReaderLine;
    }
    @Override
    public final void chunkReaderTransientContent(final String data) {
        super.log.logf(FQCN, TRACE, null, chunkReaderTransientContent$str(), data);
    }
    private static final String chunkReaderTransientContent = "MPTENGINE000121: Order item reader processed the following transient data content %s";
    protected String chunkReaderTransientContent$str() {
        return chunkReaderTransientContent;
    }
    @Override
    public final void chunkReaderFinish(final int rejectedLines) {
        super.log.logf(FQCN, INFO, null, chunkReaderFinish$str(), rejectedLines);
    }
    private static final String chunkReaderFinish = "MPTENGINE000122: Order item reader finished and will close reader with a total of '%s' rejected lines...";
    protected String chunkReaderFinish$str() {
        return chunkReaderFinish;
    }
    @Override
    public final void chunkProcessorLine(final String line) {
        super.log.logf(FQCN, DEBUG, null, chunkProcessorLine$str(), line);
    }
    private static final String chunkProcessorLine = "MPTENGINE000123: Order item processor is processing line [%s]";
    protected String chunkProcessorLine$str() {
        return chunkProcessorLine;
    }
    @Override
    public final void chunkProcessorTransientContent(final String transientData) {
        super.log.logf(FQCN, DEBUG, null, chunkProcessorTransientContent$str(), transientData);
    }
    private static final String chunkProcessorTransientContent = "MPTENGINE000124: Order item processor is processing the following transient data content %s";
    protected String chunkProcessorTransientContent$str() {
        return chunkProcessorTransientContent;
    }
    @Override
    public final void chunkCheckpointOk(final int size, final String transientData) {
        super.log.logf(FQCN, INFO, null, chunkCheckpointOk$str(), size, transientData);
    }
    private static final String chunkCheckpointOk = "MPTENGINE000125: Engine checkpoint algorithm activation... Commiting %s MPT Orders. [ChunkTransientData] [%s]";
    protected String chunkCheckpointOk$str() {
        return chunkCheckpointOk;
    }
    @Override
    public final void chunkCheckpointNok(final int commitSize) {
        super.log.logf(FQCN, DEBUG, null, chunkCheckpointNok$str(), commitSize);
    }
    private static final String chunkCheckpointNok = "MPTENGINE000126: Engine checkpoint algorithm not yet ready... Activation will be done on '%s' value...";
    protected String chunkCheckpointNok$str() {
        return chunkCheckpointNok;
    }
}
