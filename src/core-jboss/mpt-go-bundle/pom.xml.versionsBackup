<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>pt.ptinovacao.na-portal</groupId>
        <artifactId>na-portal-core-jboss</artifactId>
        <version>10.3.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>na-portal-mpt-go-bundle</artifactId>
    <packaging>ear</packaging>

    <dependencies>
        <dependency>
            <groupId>net.sf.saxon</groupId>
            <artifactId>saxon</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-health-check-rest-services</artifactId>
            <type>war</type>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-mpt-resource-manager</artifactId>
            <type>ejb</type>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-mpt-engine</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>antlr</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
            <type>ejb</type>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-db-commons</artifactId>
            <type>ejb</type>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-db-mpt</artifactId>
            <type>ejb</type>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-mpt-model</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-mpt-rest-services</artifactId>
            <type>war</type>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-go-model</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-go-rest-services</artifactId>
            <type>war</type>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-mpt-dispatcher</artifactId>
            <type>ejb</type>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-mpt-scheduler</artifactId>
            <type>ejb</type>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-portal</groupId>
            <artifactId>na-portal-rest-commons</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <scope>runtime</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-ear-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-ear</id>
                        <phase>package</phase>
                        <goals>
                            <goal>generate-application-xml</goal>
                            <goal>ear</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/MPT</outputDirectory>
                            <displayName>Network Activator Portal MPT EAR</displayName>
                            <description>Network Activator Portal MPT modules bundle for Wildfly AS</description>
                            <defaultLibBundleDir>lib/</defaultLibBundleDir>
                            <!--<generateModuleId>true</generateModuleId>-->
                            <skinnyWars>true</skinnyWars>
                            <!-- Don't append version to archive name -->
                            <fileNameMapping>no-version</fileNameMapping>
                            <!-- Tell Maven we are using Java EE 7 -->
                            <version>7</version>
                            <!--<initializeInOrder>true</initializeInOrder>-->
                            <packagingExcludes>**/*-go-*.jar,**/*-go-*.war</packagingExcludes>
                            <workDirectory>${project.build.directory}/work/MPT</workDirectory>
                            <tempFolder>${project.build.directory}/temp/MPT</tempFolder>
                            <generatedDescriptorLocation>${project.build.directory}/temp/MPT</generatedDescriptorLocation>
                            <generateApplicationXml>true</generateApplicationXml>

                            <archive>
                                <manifest>
                                    <addClasspath>false</addClasspath>
                                    <classpathPrefix>lib/</classpathPrefix>
                                </manifest>
                            </archive>
                            <modules>
                                <ejbModule>
                                    <groupId>pt.ptinovacao.na-portal</groupId>
                                    <artifactId>na-portal-mpt-resource-manager</artifactId>
                                </ejbModule>
                                <ejbModule>
                                    <groupId>pt.ptinovacao.na-portal</groupId>
                                    <artifactId>na-portal-db-commons</artifactId>
                                </ejbModule>
                                <ejbModule>
                                    <groupId>pt.ptinovacao.na-portal</groupId>
                                    <artifactId>na-portal-db-mpt</artifactId>
                                </ejbModule>
                                <ejbModule>
                                    <groupId>pt.ptinovacao.na-portal</groupId>
                                    <artifactId>na-portal-mpt-scheduler</artifactId>
                                </ejbModule>
                                <ejbModule>
                                    <groupId>pt.ptinovacao.na-portal</groupId>
                                    <artifactId>na-portal-mpt-dispatcher</artifactId>
                                </ejbModule>
                                <ejbModule>
                                    <groupId>pt.ptinovacao.na-portal</groupId>
                                    <artifactId>na-portal-mpt-engine</artifactId>
                                </ejbModule>
                                <webModule>
                                    <groupId>pt.ptinovacao.na-portal</groupId>
                                    <artifactId>na-portal-health-check-rest-services</artifactId>
                                    <contextRoot>/health</contextRoot>
                                </webModule>
                                <webModule>
                                    <groupId>pt.ptinovacao.na-portal</groupId>
                                    <artifactId>na-portal-mpt-rest-services</artifactId>
                                    <contextRoot>/na-portal-mpt-rest-interface</contextRoot>
                                </webModule>
                                <webModule>
                                    <groupId>pt.ptinovacao.na-portal</groupId>
                                    <artifactId>na-portal-go-rest-services</artifactId>
                                    <excluded>true</excluded>
                                </webModule>
                            </modules>
                        </configuration>
                    </execution>
                    <execution>
                        <id>go-ear</id>
                        <phase>package</phase>
                        <goals>
                            <goal>generate-application-xml</goal>
                            <goal>ear</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/MPT-GO</outputDirectory>
                            <displayName>Network Activator Portal MPT-GO EAR</displayName>
                            <defaultLibBundleDir>lib/</defaultLibBundleDir>
                            <!--<generateModuleId>true</generateModuleId>-->
                            <skinnyWars>true</skinnyWars>
                            <!-- Don't append version to archive name -->
                            <fileNameMapping>no-version</fileNameMapping>
                            <!-- Tell Maven we are using Java EE 7 -->
                            <version>7</version>
                            <!--<initializeInOrder>true</initializeInOrder>-->
                            <workDirectory>${project.build.directory}/work/MPT-GO</workDirectory>
                            <tempFolder>${project.build.directory}/temp/MPT-GO</tempFolder>
                            <generatedDescriptorLocation>${project.build.directory}/temp/MPT-GO</generatedDescriptorLocation>
                            <generateApplicationXml>true</generateApplicationXml>

                            <archive>
                                <manifest>
                                    <addClasspath>false</addClasspath>
                                    <classpathPrefix>lib/</classpathPrefix>
                                </manifest>
                            </archive>
                            <modules>
                                <ejbModule>
                                    <groupId>pt.ptinovacao.na-portal</groupId>
                                    <artifactId>na-portal-mpt-resource-manager</artifactId>
                                </ejbModule>
                                <ejbModule>
                                    <groupId>pt.ptinovacao.na-portal</groupId>
                                    <artifactId>na-portal-db-commons</artifactId>
                                </ejbModule>
                                <ejbModule>
                                    <groupId>pt.ptinovacao.na-portal</groupId>
                                    <artifactId>na-portal-db-mpt</artifactId>
                                </ejbModule>
                                <ejbModule>
                                    <groupId>pt.ptinovacao.na-portal</groupId>
                                    <artifactId>na-portal-mpt-dispatcher</artifactId>
                                </ejbModule>
                                <ejbModule>
                                    <groupId>pt.ptinovacao.na-portal</groupId>
                                    <artifactId>na-portal-mpt-engine</artifactId>
                                </ejbModule>
                                <webModule>
                                    <groupId>pt.ptinovacao.na-portal</groupId>
                                    <artifactId>na-portal-health-check-rest-services</artifactId>
                                    <contextRoot>/health</contextRoot>
                                </webModule>
                                <webModule>
                                    <groupId>pt.ptinovacao.na-portal</groupId>
                                    <artifactId>na-portal-mpt-rest-services</artifactId>
                                    <contextRoot>/na-portal-mpt-rest-interface</contextRoot>
                                </webModule>
                                <webModule>
                                    <groupId>pt.ptinovacao.na-portal</groupId>
                                    <artifactId>na-portal-go-rest-services</artifactId>
                                    <contextRoot>/na-portal-go-rest-interface</contextRoot>
                                </webModule>
                            </modules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
