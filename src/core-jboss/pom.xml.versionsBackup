<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>pt.ptinovacao.na-portal</groupId>
        <artifactId>na-portal</artifactId>
        <version>10.3.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>na-portal-core-jboss</artifactId>
    <packaging>pom</packaging>

    <properties>
        <!-- JBoss 10 ships with these dependencies versions -->
        <version.com.google.guava>18.0</version.com.google.guava>
        <version.com.fasterxml.jackson>2.5.4</version.com.fasterxml.jackson>
        <!-- force hibernate older versions - coming from jbossas rpm -->
        <version.org.hibernate.entitymanager>5.0.11.Final</version.org.hibernate.entitymanager>
        <version.org.hibernate.hibernate-core>5.0.11.Final</version.org.hibernate.hibernate-core>
        <version.org.hibernate.hibernate-validator>5.2.4.Final</version.org.hibernate.hibernate-validator>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${version.com.google.guava}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-servlet-initializer</artifactId>
                <version>${version.org.jboss.resteasy}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-jaxrs</artifactId>
                <version>${version.org.jboss.resteasy}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-multipart-provider</artifactId>
                <version>${version.org.jboss.resteasy}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-client</artifactId>
                <version>${version.org.jboss.resteasy}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.jboss.logmanager</groupId>
                <artifactId>jboss-logmanager</artifactId>
                <version>${version.org.jboss.logmanager}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.jboss.ejb3</groupId>
                <artifactId>jboss-ejb3-ext-api</artifactId>
                <version>${version.org.jboss.ejb3.jboss-ejb3-ext-api}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.jboss.spec.javax.ejb</groupId>
                <artifactId>jboss-ejb-api_3.2_spec</artifactId>
                <version>${version.org.jboss.spec}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.jboss.spec.javax.transaction</groupId>
                <artifactId>jboss-transaction-api_1.2_spec</artifactId>
                <version>${version.org.jboss.spec}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.jboss.spec.javax.ws.rs</groupId>
                <artifactId>jboss-jaxrs-api_2.0_spec</artifactId>
                <version>${version.org.jboss-jaxrs-api_2.0_spec}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.jboss.spec.javax.annotation</groupId>
                <artifactId>jboss-annotations-api_1.2_spec</artifactId>
                <version>${version.org.jboss.spec}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.jboss.logging</groupId>
                <artifactId>jboss-logging</artifactId>
                <version>${version.org.jboss.logging.jboss-logging}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.jboss.logging</groupId>
                <artifactId>jboss-logging-annotations</artifactId>
                <version>${version.org.jboss.jboss-logging-annotations}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.jboss.logging</groupId>
                <artifactId>jboss-logging-processor</artifactId>
                <version>${version.org.jboss.jboss-logging-processor}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.jboss.spec.javax.batch</groupId>
                <artifactId>jboss-batch-api_1.0_spec</artifactId>
                <version>${version.org.jboss.spec}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.jboss</groupId>
                <artifactId>jboss-dmr</artifactId>
                <version>${version.org.jboss.dmr}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.jboss.weld</groupId>
                <artifactId>weld-api</artifactId>
                <version>${version.org.jboss.weld}</version>
                <scope>provided</scope>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.hibernate.ogm</groupId>
                <artifactId>hibernate-ogm-mongodb</artifactId>
                <version>${version.org.hibernate.ogm}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.hibernate.ogm</groupId>
                <artifactId>hibernate-ogm-core</artifactId>
                <version>${version.org.hibernate.ogm}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-core</artifactId>
                <version>${version.org.hibernate.hibernate-core}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${version.org.hibernate.hibernate-validator}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-entitymanager</artifactId>
                <version>${version.org.hibernate.entitymanager}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.hibernate.javax.persistence</groupId>
                <artifactId>hibernate-jpa-2.1-api</artifactId>
                <version>${version.org.hibernate.javax.persistence.hibernate-jpa-2.1-api}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongo-java-driver</artifactId>
                <version>${version.org.mongodb.mongo-java-driver}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-core</artifactId>
                <version>${version.org.infinispan}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.jberet</groupId>
                <artifactId>jberet-core</artifactId>
                <version>${version.org.jberet}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.wildfly.security</groupId>
                <artifactId>wildfly-security-manager</artifactId>
                <version>${version.org.wildfly.security}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.wildfly.core</groupId>
                <artifactId>wildfly-controller</artifactId>
                <version>${version.org.wildfly.core}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.wildfly.core</groupId>
                <artifactId>wildfly-controller-client</artifactId>
                <version>${version.org.wildfly.core}</version>
                <scope>provided</scope>
            </dependency>
            <!-- Import the CDI API, we use provided scope as the API is included in JBoss WildFly -->
            <dependency>
                <groupId>javax.enterprise</groupId>
                <artifactId>cdi-api</artifactId>
                <version>${version.javax.enterprise.cdi-api}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>javax.interceptor</groupId>
                <artifactId>javax.interceptor-api</artifactId>
                <version>${version.javax.interceptor}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>javax.inject</groupId>
                <artifactId>javax.inject</artifactId>
                <version>${version.javax.inject}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>javax</groupId>
                <artifactId>javaee-api</artifactId>
                <version>${version.javax.javee-api}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>javax.el</groupId>
                <artifactId>javax.el-api</artifactId>
                <version>${version.javax.el-api}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${version.javax.validation}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${version.com.fasterxml.jackson}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${version.com.fasterxml.jackson}</version>
                <scope>provided</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- DO NOT REMOVE THIS... -->
        <dependency>
            <groupId>org.jboss.logging</groupId>
            <artifactId>jboss-logging-processor</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <modules>
        <module>common</module>
        <module>go</module>
        <module>health-check</module>
        <module>mpt</module>
        <module>mpt-go-bundle</module>
    </modules>
</project>
