---

mpt_client_sidecar_docker_run: >-
  docker run
  -v /opt/alticelabs/na-portal/conf/na-portal-mpt-client-sidecar/config/all.yml:/config/all.yml
  {% if na_portal_mpt_client_mpt_configs_dir %}-v {{ na_portal_mpt_client_mpt_configs_dir }}:/docker-entrypoint-scripts.d/mpt/ {% endif %}
  {% if na_portal_mpt_client_go_configs_dir %}-v {{ na_portal_mpt_client_go_configs_dir }}:/docker-entrypoint-scripts.d/go/ {% endif %}
  {% if na_portal_mpt_client_migrate_scripts_dir %}-v {{ na_portal_mpt_client_migrate_scripts_dir }}:/docker-entrypoint-scripts.d/sh/ {% endif %}
  {{ na_portal_mpt_client_sidecar_docker_opts }} {{ na_portal_mpt_client_sidecar_docker_image }}

na_portal_mpt_client_sidecar_docker_image: "{{ docker_registry_host }}/na-portal{% if '${project.version}'.endswith('-SNAPSHOT') %}-tst{% endif %}/na-portal-mpt-client-sidecar:${ud.version}"
na_portal_mpt_client_sidecar_docker_opts: ""
docker_registry_host: "rdocker.ptin.corppt.com"