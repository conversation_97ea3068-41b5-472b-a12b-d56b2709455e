---

docker_registry_host: "rdocker.ptin.corppt.com"
na_portal_web_balancer_docker_opts: ""
na_portal_web_balancer_docker_command: ""
na_portal_web_balancer_image: "{{ docker_registry_host }}/na-portal{% if '${project.version}'.endswith('-SNAPSHOT') %}-tst{% endif %}/na-portal-web-balancer:${ud.version}"

nginx_copy_cert: False
na_portal_web_balancer_host_be_port: 8080
na_portal_web_balancer_host_tls_port: 443

na_portal_web_balancer_logs_dir: "/var/log/alticelabs/na-portal/web-balancer/"
na_portal_web_balancer_host_user: 2016
na_portal_web_balancer_host_group: 2016

web_balancer_opts: >-
  -v {{ na_portal_web_balancer_logs_dir }}:/var/log/nginx
  -v /opt/ptin/na-portal/conf/{{ daemon }}/config/all.yml:/config/all.yml
  {% if na_portal_web_balancer_private_key_path is defined and na_portal_web_balancer_public_certificate_path is defined %}
  -v {{ na_portal_web_balancer_private_key_path }}:/certificates/private_key.key
  -v {{ na_portal_web_balancer_public_certificate_path }}:/certificates/public_cert.crt
  {% endif %}
  -p {{ na_portal_web_balancer_host_be_port }}:8090
  -p {{ na_portal_web_balancer_host_tls_port }}:8443
