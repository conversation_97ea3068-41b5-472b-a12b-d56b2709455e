---

- name: "{{ role_name }} - create {{ daemon }} systemd file"
  template:
    src: na-portal-web-balancer.SAMPLE
    dest: /etc/sysconfig/{{ daemon }}
    group: root
    owner: root

- name: "{{ role_name }} - create mapped logs volume folder"
  file:
    path: "{{ na_portal_web_balancer_logs_dir }}"
    state: directory
    mode: 0755
    owner: "{{ na_portal_web_balancer_host_user }}"
    group: "{{ na_portal_web_balancer_host_group }}"
    recurse: yes

- name: "{{ role_name }} - create mapped volume folder"
  file:
    path: /opt/alticelabs/na-portal/conf/{{ daemon }}/config/
    state: directory
    owner: "{{ app_user }}"
    group: "{{ app_group }}"

- name: "{{ role_name }} - template inventory file to mapped volume folder"
  template:
    src: all.yml.SAMPLE
    dest: /opt/alticelabs/na-portal/conf/{{ daemon }}/config/all.yml
    owner: "{{ app_user }}"
    group: "{{ app_group }}"