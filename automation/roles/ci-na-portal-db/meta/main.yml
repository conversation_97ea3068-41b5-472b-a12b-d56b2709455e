---

dependencies:
  - role: '/opt/alticelabs/automation/na-commons/roles/docker-postgres'
    vars:
      postgres_pdocker_image: "{{ ci_postgres_pdocker_image }}"
      postgres_pdocker_command: "{{ ci_postgres_pdocker_command }}"
      postgres_pdocker_opts: "{{ ci_postgres_pdocker_opts }}"
      postgres_docker_data_dir: "{{ ci_postgres_docker_data_dir }}"
  - role: '/opt/alticelabs/automation/na-portal/roles/docker-na-portal-db-configuration'
    vars:
      na_portal_db_pdocker_image: "{{ ci_na_portal_db_pdocker_image }}"
      na_portal_db_pdocker_opts: "{{ ci_na_portal_db_pdocker_opts }}"
      na_portal_db_pdocker_command: "{{ ci_na_portal_db_pdocker_command }}"
      pgsql_user: "{{ ci_pgsql_user }}"
      pgsql_group: "{{ ci_pgsql_group }}"
      pgsql_version: "{{ ci_pgsql_version }}"
      na_portal_db_optional_variables:
        - param: "pgsql_version"
          value: "{{ ci_pgsql_version }}"
