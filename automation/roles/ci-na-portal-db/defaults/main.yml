---

#pgsql
ci_postgres_pdocker_image: 'postgres:16'
ci_postgres_pdocker_command: ''
ci_postgres_pdocker_opts: '-e LC_CTYPE=en_US.UTF-8 -e LC_ALL=en_US.UTF-8 -e POSTGRES_USER={{na_portal_db_dba_user}} -e POSTGRES_PASSWORD={{na_portal_db_dba_password}} -e POSTGRES_DB=postgres -p 5432:5432 -e PGDATA=/var/lib/pgsql/data'
ci_pgsql_user: "999"
ci_pgsql_group: "{{ ci_pgsql_user }}"
ci_pgsql_version: 16
ci_postgres_docker_data_dir: "/var/lib/pgsql/16/"

#db-conf
ud_version: '${ud.version}'
ci_na_portal_db_pdocker_image: "rdocker.ptin.corppt.com/na-portal-tst/na-portal-pgsql12-db:{{ ud_version|replace('-SNAPSHOT','')}}"
ci_na_portal_db_pdocker_command: "--create-or-update --mpt-go --catalog"
ci_na_portal_db_pdocker_opts: '-e DB_NAME={{ na_portal_db_name }} -e DB_HOST={{ na_portal_db_host }} -e DB_DBA_USER={{na_portal_db_dba_user}} -e DB_DBA_PWD={{na_portal_db_dba_password}}'

