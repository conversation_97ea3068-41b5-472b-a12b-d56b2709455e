---

- name: "{{ role_name }} - create {{ daemon }} systemd file"
  template:
    src: "na-portal-frontend-portal.SAMPLE"
    dest: /etc/sysconfig/{{ daemon }}
    group: "root"
    owner: "root"
    mode: 0664

- name: "{{ role_name }} - create mapped volume folder"
  file:
    path: /opt/alticelabs/na-portal/conf/{{ daemon }}/config/
    state: directory
    owner: "{{ na_portal_frontend_portal_host_user }}"
    group: "{{ na_portal_frontend_portal_host_group }}"

- name: "{{ role_name }} - create {{ daemon }} log mount volume"
  file:
    path: "{{ na_portal_frontend_portal_logs_dir }}"
    state: directory
    owner: "{{ na_portal_frontend_portal_host_user }}"
    group: "{{ na_portal_frontend_portal_host_group }}"
    mode: 0755
    recurse: yes

- name: "{{ role_name }} - template inventory sample file to mapped volume config file"
  template:
    src: "all.yml.SAMPLE"
    dest: "/opt/alticelabs/na-portal//conf/{{ daemon }}/config/all.yml"
    owner: "{{ na_portal_frontend_portal_host_user }}"
    group: "{{ na_portal_frontend_portal_host_group }}"

