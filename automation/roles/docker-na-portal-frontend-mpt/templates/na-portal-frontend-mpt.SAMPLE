DOCKER_IMAGE='{{ na_portal_frontend_mpt_docker_image }}'
DOCKER_COMMAND='{{ na_portal_frontend_mpt_docker_command }}'
DOCKER_OPTS='-p {{ na_portal_frontend_mpt_exposed_port }}:19006 {% if na_portal_frontend_i18n_customization_dir_path is defined %}-v {{ na_portal_frontend_i18n_customization_dir_path }}:/opt/alticelabs/na-portal/conf/i18n/customization{% endif %} {% if use_provided_iam_conn_cert_file %}-v {{ iam_source_host_cert_file }}:/certificates/iam_conn.pem{% endif %} -v {{ na_portal_frontend_mpt_logs_dir }}:/var/log/alticelabs/na-portal/web/ -v {{ na_portal_iam_host_cert_path }}:/certificates/na-portal-iam-service-public-cert.pem -v /opt/alticelabs/na-portal/conf/{{ daemon }}/config/all.yml:/config/all.yml:ro {{ na_portal_frontend_mpt_docker_opts }}'
