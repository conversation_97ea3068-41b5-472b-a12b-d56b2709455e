
---

- name: "{{ role_name }} - set daemon name"
  set_fact:
    daemon: na-portal-frontend-mpt

- name: "{{ role_name }} - check if {{ daemon }} service exists"
  stat:
    path: /etc/sysconfig/{{ daemon }}
  register: daemon_status

- name: "{{ role_name }} - pulling docker image"
  command: docker pull {{ na_portal_frontend_mpt_docker_image }}

- name: "{{ role_name }} - stop {{ daemon }} daemon if running"
  systemd:
    name: pdocker@{{ daemon }}
    state: stopped
  when: daemon_status.stat.exists

- name: "{{ role_name }} - install configuration"
  include: install_configuration.yml
  tags: configuration

- name: "{{ role_name }} - start {{ daemon }} daemon"
  systemd:
    name: pdocker@{{ daemon }}
    enabled: yes
    state: started
