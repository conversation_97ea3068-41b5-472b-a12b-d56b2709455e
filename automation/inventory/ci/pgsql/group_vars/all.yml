#----------------------------------------------------------------------------------------------------------------------#
#| Ansible Variables                                                                                                  |#
#----------------------------------------------------------------------------------------------------------------------#

ansible_user: ptin_admin
ansible_ssh_pass: ptin_admin
ansible_become: true
ansible_become_pass: ptin_admin
ansible_become_method: sudo


#----------------------------------------------------------------------------------------------------------------------#
#| General Variables                                                                                                  |#
#----------------------------------------------------------------------------------------------------------------------#

app_user: na
app_group: ptin

naportal_ci_host: "naportal-ci.c.ptin.corppt.com"

#----------------------------------------------------------------------------------------------------------------------#
#| IAM Variables                                                                                                      |#
#----------------------------------------------------------------------------------------------------------------------#

# USED_BY: na-commons-iam-configurator
iam_db_addr: naportal-iam-test.c.ptin.corppt.com
iam_IDP_API_method: http
iam_IDP_API_addr: naportal-iam-test.c.ptin.corppt.com
iam_IDP_API_port: 8080
iam_IDP_API_base_url: '{{ iam_IDP_API_method }}://{{ iam_IDP_API_addr | add_url_port(iam_IDP_API_port) }}'
iam_PortalAddr_host: 'iam.sso.local'
use_provided_csvs: false
use_supplied_public_certificate: True
iam_host_public_cert_path: "/opt/alticelabs/na-portal-service-public-cert.pem"
iam_db_dbname: iam
iam_db_user: iam
iam_db_password: iam
iam_login_url: "https://{{ naportal_ci_host }}/na/loginPost"
iam_logout_url: "https://{{ naportal_ci_host }}/na/logoutPost"

iam_configurator_api_public_cert_file: "{{ iam_host_public_cert_path }}"
iam_configurator_api_client_id: wsiFo9JHRV2_0y0gU923kQ
iam_configurator_api_client_secret: MOLIeOGlTeG12YN0JMyWqA
iam_configurator_api_address: https://naportal-iam-test.c.ptin.corppt.com

#----------------------------------------------------------------------------------------------------------------------#
#| NAPORTAL Variables                                                                                                 |#
#----------------------------------------------------------------------------------------------------------------------#

# USED_BY: na-portal-naem-dbmanager; na-portal-frontend
naem_nbi_host: "{{ naportal_ci_host }}"
naem_nbi_port: 8180
naem_db_profile: mongolocal

# USED_BY: na-portal-mpt-client; na-portal-mpt-client-sidecar; na-portal-web-balancer; na-portal-frontend; install_full playbook
na_portal_be_nbi_host: "{{ naportal_ci_host }}"
na_portal_be_nbi_port: 8280

# USED_BY: na-portal-jboss-mpt-go; na-portal-core-reference-data; ci-na-portal-db; install_full/load_scenario playbook
na_portal_db_name: naportal
na_portal_db_host: "{{ naportal_ci_host }}"
na_portal_db_port: 5432

# USED_BY: ci-na-portal-db ; install_full/load_scenario playbook
na_portal_db_dba_user: ptin_admin
na_portal_db_dba_password: PTIN_ADMIN

# USED_BY: na-portal-frontend-*
na_portal_iam_host: 'naportal-iam-test.c.ptin.corppt.com:443'
na_portal_iam_keystore_secret: 123456

# USED_BY: na-portal-frontend-*; na-portal-iam-configurator
na_portal_iam_oauth_client: MSAH4HH9QF3Ln3pgTrU7zt
na_portal_iam_oauth_secret: zk0wdE3BeCQrrLdm8pl3ja
na_portal_iam_credential_signature: "NzCcNb+xrUOyNhjeuLS0+/+IumfvPkQFYQPMwFaPlqhsqLISSOXWxXIGp+kYXPy52d25icf+4mfD6q7xZ/xU9v/0XKBLia4eOrzp6t6GGB0m57TxdUINjTgcSqJjDmArNmx8Wf07eanh8k+mGHAmK76ozgl0DkSP43+otYRRyD7hCt94Eud3UiZHIwnsVwJEV14hgI+X1rxpKlMcaMb8cegDIbPDoPiU7TEZZZFJ6ldcoB0gxMQWSm+ooMGshGZiuEmD8tcOysYgbiKJ+jga6wUWLlRg6FiMuif5Fn0j1+/KNztehGkAtR6j2Q/WKH6I8HPrm7uOy02a+gXUzqGxqiDjgV5bWYADDy7e8kPtqUWe0G1rtDkr58prEsHWN60d/1oQzKryoWy4pCBWNXi+iYv/aTgNn12D4Sxc850R2qPe5bm6Hgi7Y9smyzcP5KE4kr2R40OkiQqViYIpaTYaZNg715ddrcC8l+JXsvCRzVGwpYZyjQOHYZh3EmnCaLRj4Xvg2U3/CEK7xZ2cxEzUW6PNt9Lwmv6DjLGyyQGbhORB1V4YSDPt/0pVE+VxBgpOwbNrHY0Sz6EECahW/vsC3S5BSP6sx9UJe5/QwTn0pL+EP0gHlP1TrnKHZZIcLwHOKoETN5RZdAPEidtNeDtfRptxs3xdyhHLc3dBviz1dF8="
na_portal_iam_login_url: "https://{{ naportal_ci_host }}/na/loginPost"
na_portal_iam_logout_url: "https://{{ naportal_ci_host }}/na/logoutPost"

#USED_BY: nossis-tsc-frontend-entities-catalog
nossis_tsc_iam_oauth_client: MSAH4HH9QF3Ln3pgTrU7zt
nossis_tsc_iam_oauth_secret: zk0wdE3BeCQrrLdm8pl3ja
nossis_tsc_iam_service_secret: 123456
nossis_tsc_iam_host: "{{ na_portal_iam_host }}"
nossis_tsc_access_management_endpoint: "https://{{ nossis_tsc_iam_host }}/idp/login"
nossis_tsc_iam_public_cert_path: "/opt/alticelabs/na-portal/conf/na-portal-frontend/certificates/na-portal-iam-service-public-cert.pem"
PORTAL_LANGS: '"pt","en","fr"'
nossis_tsc_iam_login_url: "https://{{ naportal_ci_host }}/na/loginPost"

# USED_BY: load_scenario playbook;
version_env_postgresql: '12*'

# USED_BY: install_full playbook
na_portal_be_bind_ip: 0.0.0.0

# USED_BY: na-portal-jboss-mpt-go;
na_portal_be_mdb_datastore_url: "{{ naportal_ci_host }}:27017"

# USED_BY: install_full playbook
na_portal_be_backend_port: 9015

# USED_BY: na-portal-naem-dbmanager; na-portal-frontend-mpt; na-portal-core-reference-data
mongodb_uri: "mongodb://naportal:naportal@{{ naportal_ci_host }}:27017/naportal"

# USED_BY: na-portal-frontend-(operations-catalog and monitoring); na-portal-core-reference-data
ci_zookeeperNodes: "{{ naportal_ci_host }}:32181"

# USED_BY: na-portal-core-reference-data
zkClient_zookeeperNodes: "{{ ci_zookeeperNodes }}"

# USED_BY: na-portal-frontend ( Go and Veacs modules )
na_portal_fe_mapping_opers_with_firmware_upload: |
    { 
      "createTemplateWithNewSoftwareVersion": {
          manufacurer = "manufacturer"
          model = "modelName"
          newVersion = "newVersion"
          firmwareFilename = "firmwareFilename"
      }
    }

# USED_BY: na-portal-fe-monitoring
na_portal_monitoring_order_followup_backend_host: "{{ naportal_ci_host }}"
na_portal_monitoring_order_followup_backend_port: 39007
na_portal_monitoring_swe_backend_host: "{{ naportal_ci_host }}"
na_portal_monitoring_swe_backend_port: 39004
na_portal_monitoring_external_systems_whitelist: |
  ["NA", "OM", "user_DEVELOPMENT_WITH_SUBSCRIBER"]
na_portal_monitoring_requester_callback_base_url: http://naportal-ci.c.ptin.corppt.com:19001/
na_portal_monitoring_feature_whitelist: '["order-create", "operation-attributes-view"]'
na_portal_monitoring_zkClient_zookeeperNodes: "{{ ci_zookeeperNodes }}"

# USED_BY: na-portal-web-balancer
na_portal_fe_portal_host: "{{ naportal_ci_host }}"
nginx_host: localhost

# USED_BY: na-portal-core-reference-data
naportal_catalog_rabbitmq_uri: "{{ naportal_ci_host }}:5672"
naportal_catalog_rabbitmq_username: "admin"
naportal_catalog_rabbitmq_password: "1234"

#USED_BY: na-portal-frontend-operations-catalog
operations_catalog_zkClient_zookeeperNodes: "{{ ci_zookeeperNodes }}"
entities_catalog_zkClient_zookeeperNodes: "{{ ci_zookeeperNodes }}"

#USED_BY: na-portal-frontend-reference-data
reference_data_zkClient_zookeeperNodes: "{{ ci_zookeeperNodes }}"

#USED_BY: na-portal-frontend-go; na-portal-frontend-mpt
na_portal_be_mpt_go_host: "{{ naportal_ci_host }}"
na_portal_be_mpt_go_port: 8090

#----------------------------------------------------------------------------------------------------------------------#
#| Docker Variables                                                                                                   |#
#----------------------------------------------------------------------------------------------------------------------#

docker_registry_host: "rdocker.ptin.corppt.com"

# na-portal-naem-dbmanager
na_portal_naem_dbmanager_docker_opts: "--hostname {{ ansible_hostname }} -p 8180:8180 -p 5559:5555"
na_portal_naem_dbmanager_optional_variables:
  - param: ansible_hostname
    value: "{{ ansible_hostname }}"
  - param: naem_nbi_port
    value: "{{ naem_nbi_port }}"
  - param: naem_java_opts
    value: "-Xmx256M -Xms256M -Xss256K"
  - param: log4j2
    value: "{{ naem_dbmanager_log4j2 | to_json }}"

# na-portal-core-reference-data
na_portal_core_reference_data_docker_opts: "--net=host -v /var/tmp/na-portal/catalogs:/var/tmp/na-portal/catalogs -v /tmp/catalogs:/tmp"
na_portal_core_reference_data_docker_image: "{{ docker_registry_host }}/na-portal{% if '${project.version}'.endswith('-SNAPSHOT') %}-tst{% endif %}/na-portal-core-reference-data:${ud.version}"
na_portal_core_reference_data_optional_variables:
  - param: catalogs_dir
    value: '/var/tmp/na-portal/catalogs'
  - param: na_portal_catalogs_notifier_to_xml_enabled
    value: True
  - param: na_portal_catalogs_notifier_to_msg_bus_enabled
    value: True
  - param: log4j2
    value: "{{ core_reference_data_log4j2 | to_json }}"

# na-portal-mpt-client
na_portal_mpt_client_logs_dir: "/var/log/alticelabs/na-portal/mpt-client/"
na_portal_mpt_client_docker_opts: "--net=host"
na_portal_mpt_client_optional_variables:
  - param: log4j2
    value: "{{ mpt_client_log4j2 | to_json }}"
  - param: application_environment
    value: "CI"

# na-portal-frontend-portal
na_portal_frontend_portal_docker_opts: "-p 5557:5555"
na_portal_frontend_portal_optional_variables:
  - param: na_portal_install_nadm_module
    value: True
  - param: na_portal_nadm_context
    value: "veacs"
  - param: na_portal_install_diagnostics_module
    value: True
  ###### LOGGING ######
  - param: log4j2
    value: "{{ na_portal_frontend_log4j2 | to_json }}"
  - param: application_environment
    value: "dev"

# na-portal-frontend-monitoring
na_portal_frontend_monitoring_optional_variables:
  - param: na_portal_monitoring_external_systems_whitelist
    value: "{{ na_portal_monitoring_external_systems_whitelist | to_json }}"
  - param: na_portal_monitoring_feature_whitelist
    value: "{{ na_portal_monitoring_feature_whitelist | to_json }}"
  ###### LOGGING ######
  - param: log4j2
    value: "{{ na_portal_frontend_log4j2 | to_json }}"
  - param: application_environment
    value: "dev"

# na-portal-frontend-operations-catalog
na_portal_frontend_operations_catalog_optional_variables:
  - param: na_portal_monitoring_external_systems_whitelist
    value: "{{ na_portal_monitoring_external_systems_whitelist | to_json }}"
  ###### LOGGING ######
  - param: log4j2
    value: "{{ na_portal_frontend_log4j2 | to_json }}"
  - param: application_environment
    value: "dev"

# na-portal-frontend-reference-data
na_portal_frontend_reference_data_optional_variables:
  - param: na_portal_monitoring_external_systems_whitelist
    value: "{{ na_portal_monitoring_external_systems_whitelist | to_json }}"
  ###### LOGGING ######
  - param: log4j2
    value: "{{ na_portal_frontend_log4j2 | to_json }}"
  - param: application_environment
    value: "dev"

#nossis-tsc-frontend-entities-catalog
docker_nossis_tsc_version: "${version.pt.ptinovacao.nossis-tsc}"
docker_nossis_tsc_repository: "nossis-tsc{% if docker_nossis_tsc_version is string_endingwith '-SNAPSHOT' %}-tst{% endif %}"
nossis_tsc_frontend_entities_catalog_image: "{{ docker_registry_host }}/{{ docker_nossis_tsc_repository }}/nossis-tsc-frontend-entities-catalog:{{ docker_nossis_tsc_version | replace('-SNAPSHOT', '')}}"
nossis_tsc_frontend_entities_catalog_docker_opts: ""
nossis_tsc_frontend_entities_catalog_optional_variables:
  - param: entities_catalog_portal_context
    value: "/na"
  ###### LOGGING ######
  - param: log4j2
    value: "{{ nossis_tsc_frontend_entities_catalog_log4j2 | to_json }}"
  - param: application_environment
    value: "dev"

#na-portal-frontend-go
na_portal_frontend_go_docker_opts: "-v /tmp/firmwareFilesDir/:/tmp/firmwareFilesDir/"
na_portal_frontend_go_optional_variables:
  - param: na_portal_fe_mapping_opers_with_firmware_upload
    value: "{{ na_portal_fe_mapping_opers_with_firmware_upload | to_json }}"
  ###### LOGGING ######
  - param: log4j2
    value: "{{ na_portal_frontend_log4j2 | to_json }}"
  - param: application_environment
    value: "dev"

#na-portal-frontend-mpt
na_portal_frontend_mpt_optional_variables:
  ###### LOGGING ######
  - param: log4j2
    value: "{{ na_portal_frontend_log4j2 | to_json }}"
  - param: application_environment
    value: "dev"

#na-portal-frontend-nadm
na_portal_frontend_nadm_docker_image: "{{ docker_registry_host }}/na-portal{% if '${project.version}'.endswith('-SNAPSHOT') %}-tst{% endif %}/na-portal-frontend-veacs:${ud.version}"
na_portal_frontend_nadm_docker_opts: "-v /tmp/firmwareFilesDir/:/tmp/firmwareFilesDir/"
na_portal_frontend_nadm_optional_variables:
  - param: na_portal_veacs_be_go_mpt_host
    value: "{{ na_portal_be_nbi_host }}"
  - param: na_portal_veacs_be_go_mpt_port
    value: "{{ na_portal_be_nbi_port }}"
  - param: na_portal_veacs_be_host
    value: "{{ naportal_ci_host }}"
  - param: na_portal_veacs_be_port
    value: 8090
  - param: na_portal_fe_mapping_opers_with_firmware_upload
    value: "{{ na_portal_fe_mapping_opers_with_firmware_upload | to_json }}"
  - param: na_portal_fe_nadm_integrate_diagnostics
    value: True
  ###### LOGGING ######
  - param: log4j2
    value: "{{ na_portal_frontend_log4j2 | to_json }}"
  - param: application_environment
    value: "dev"
  - param: naem_nbi_host
    value: "{{ naem_nbi_host }}"
  - param: naem_nbi_port
    value: "{{ naem_nbi_port }}"

#na-portal-frontend-diagnostics
na_portal_frontend_diagnostics_optional_variables:
  ###### LOGGING ######
  - param: log4j2
    value: "{{ na_portal_frontend_log4j2 | to_json }}"
  - param: application_environment
    value: "dev"

# na-portal-web-balancer
na_portal_web_balancer_docker_image: "{{ docker_registry_host }}/na-portal{% if '${project.version}'.endswith('-SNAPSHOT') %}-tst{% endif %}/na-portal-web-balancer:${ud.version}"
na_portal_web_balancer_docker_opts: ""
na_portal_web_balancer_optional_variables:
  # mpt/go
  - param: na_portal_install_mpt_go_module
    value: True
  - param: na_portal_mpt_go_host
    value: "{{ na_portal_be_nbi_host }}"
  - param: na_portal_mpt_go_port
    value: "{{ na_portal_be_nbi_port }}"
  # data management
  - param: na_portal_install_catalog_module
    value: True
  # nadm
  - param: na_portal_install_nadm_module
    value: True
  - param: nadm_nbi_host
    value: "{{ naportal_ci_host }}"
  - param: nadm_nbi_port
    value: 9090
  - param: na_portal_nadm_context
    value: "veacs"
  # entities catalog
  - param: na_portal_install_entities_catalog_module
    value: True
  # operations catalog
  - param: na_portal_install_operations_catalog_module
    value: True
  # monitoring
  - param: na_portal_install_monitoring_module
    value: True
  # diagnostics
  - param: na_portal_install_diagnostics_module
    value: True
  - param: na_portal_nginx_diagnostics_service_upstream_server
    value: *************
na_portal_web_balancer_host_be_port: 8090
na_portal_web_balancer_host_tls_port: 443

# na-portal-jboss-mpt-go
na_portal_jboss_mpt_go_docker_image: "{{ docker_registry_host }}/na-portal{% if '${project.version}'.endswith('-SNAPSHOT') %}-tst{% endif %}/na-portal-jboss-mpt-go:${ud.version}"
na_portal_jboss_mpt_go_docker_command: ""
na_portal_jboss_mpt_go_docker_opts: "-p 8280:8280 -p 15005:5005"
na_portal_jboss_mpt_go_optional_variables:
  - param: na_portal_be_jboss_origin_ip
    value: "{{ naportal_ci_host }}"
  - param: housekeeping_delta_days
    value: 7
  - param: na_portal_be_mdb_auth_user
    value: "naportal"
  - param: na_portal_be_mdb_auth_password
    value: "naportal"
  - param: na_portal_be_mdb_auth_db
    value: "naportal"
  - param: na_portal_be_jmx_statistics_binding_port
    value: 5556
  - param: jboss_server_opts
    value: "-Djboss.host.name={{ ansible_hostname }}"
  - param: log4j
    value: "{{ jboss_mpt_go_log4j | to_json }}"

# na-commons-iam-configurator
iam_configurator_api_pdocker_image: "{{ docker_registry_host }}/na-portal{% if '${project.version}'.endswith('-SNAPSHOT') %}-tst{% endif %}/na-portal-iam-configurator-api:${ud.version}"
iam_configurator_api_pdocker_opts: "-e USE_UNSECURE=true"
iam_configurator_api_optional_variables:
  - param: na_portal_iam_configurator_api_base_folder
    value: 'v4/testing_csv'
  - param: na_portal_iam_oauth_client
    value: '{{ na_portal_iam_oauth_client }}'
  - param: na_portal_iam_oauth_secret
    value: '{{ na_portal_iam_oauth_secret }}'
  - param: na_portal_iam_credential_signature
    value: '{{ na_portal_iam_credential_signature }}'
  - param: na_portal_iam_login_url
    value: '{{ na_portal_iam_login_url }}'
  - param: na_portal_iam_logout_url
    value: '{{ na_portal_iam_logout_url }}'

iam_configurator_pdocker_image: "{{ docker_registry_host }}/na-portal{% if '${project.version}'.endswith('-SNAPSHOT') %}-tst{% endif %}/na-portal-iam-configurator:${ud.version}"
iam_configurator_pdocker_opts: ""
iam_configurator_optional_variables:
  - param: iam_base_csvs_folder
    value: '/opt/alticelabs/na-portal/conf/na-portal-iam-csvs/iam_v4/testing_csv'
  - param: na_portal_iam_login_url
    value: "{{ na_portal_iam_login_url }}"
  - param: na_portal_iam_logout_url
    value: '{{ na_portal_iam_logout_url }}'
  - param: na_portal_iam_oauth_client
    value: "{{ na_portal_iam_oauth_client }}"
  - param: na_portal_iam_oauth_secret
    value: "{{ na_portal_iam_oauth_secret }}"
  # signature generated from https://wiki.ptin.corppt.com/display/IAM/Kubernetes+Friendly+Client+Credentials+Import
  - param: na_portal_iam_credential_signature
    value: "aFRSOf9cgrjzRRxHfg5Q65/ETY84wiKfN6WGpN/Po3ul/RV2OrSX+N8w2wk8yU4Y2McX0R5gipEf2CXkZWCCETKHoq6XqVpkqxxn35+WrthicuQ9ciHBUv/EzvVS6kgTfcwUyO2o+DHcdhDF6+G4mRG5SYOOAjYWlj4SSbeQA3h5qE1We6w5u3A1ivOGoVNWdaILP0UGZ4W/JH09V/6kFTzVsfpx5MvbrdL8nr9HmsQXlvw+j9roW0jux4o2zreyrHSgtoJpN1UUtqYCFwnamJW7Dj8VD8Q89fFgkcVHkIMK2aLmqyaetDtqwWTUmZJGD/4bUZjxK33rDYQS+OSptzOQ+yvf9x8lZF4kUbQQqM9Q4AM6cL18j0/05rpLhWKP/hDFlb13HAz9oCY6s0GyY28ZtvnXjcwBAP9sRGDkRQnInzOomEPCd1fvjEe9Ygko2jQa+CJ3dMFtyhr0tcljvumYKFrKoMl/xUGCw6jhobDwCNyp90gwFkjkkoeNmkbIAeMc8kfbBMq/PtMcGMG+tkNt/uqyoAw7XEDjf+UyF7rRUhlB9SmRZxMKgCeiJ35coOBnRZWd9+v5F6g7tdhn4HbXPCEAJAgLASUv1FgUjC8FgBTruIZvs2+VgJtXRu58MPmqgQufYMMgt+uBNR3bNiv12YhcKp5AmsF9DvY9f3s="



#----------------------------------------------------------------------------------------------------------------------#
#| Logger Variables                                                                                                   |#
#----------------------------------------------------------------------------------------------------------------------#
jboss_mpt_go_log4j:
  RollingFile:
    FILE:
      file: "server.log"
      threshold: "DEBUG"
      maxFileSize: "50m"
      maxBackupIndex: "10"
      conversionPattern: "%d{yyyy-MM-dd HH:mm:ss,SSSS} %-5p [%c]-[%t] %s%e%n"
    REST_COMMONS:
      file: "restfulCommons.log"
      maxFileSize: "50m"
      maxBackupIndex: "10"
      threshold: "INFO"
      conversionPattern: "%d{yyyy-MM-dd HH:mm:ss,SSSS} %-5p [%c]-[%t] %s%e%n"
    REST_AUDIT:
      file: "audit.log"
      maxFileSize: "50m"
      maxBackupIndex: "10"
      threshold: "DEBUG"
      conversionPattern: "%d %m%n"
    DISPATCHER:
      file: "mpt/dispatcher.log"
      maxFileSize: "50m"
      maxBackupIndex: "10"
      threshold: "DEBUG"
      conversionPattern: "%d{yyyy-MM-dd HH:mm:ss,SSSS} %-5p [%c]-[%t] %s%e%n"
    RESOURCEMANAGER:
      file: "mpt/resourceManager.log"
      maxFileSize: "50m"
      maxBackupIndex: "10"
      threshold: "DEBUG"
      conversionPattern: "%d{yyyy-MM-dd HH:mm:ss,SSSS} %-5p [%c]-[%t] %s%e%n"
    ENGINE:
      file: "mpt/engine.log"
      maxFileSize: "50m"
      maxBackupIndex: "10"
      threshold: "DEBUG"
      conversionPattern: "%d{yyyy-MM-dd HH:mm:ss,SSSS} %-5p [%c]-[%t] %s%e%n"
    SCHEDULER:
      file: "mpt/scheduler.log"
      maxFileSize: "50m"
      maxBackupIndex: "10"
      threshold: "DEBUG"
      conversionPattern: "%d{yyyy-MM-dd HH:mm:ss,SSSS} %-5p [%c]-[%t] %s%e%n"
    REST_MPT:
      file: "mpt/restful.log"
      maxFileSize: "50m"
      maxBackupIndex: "10"
      threshold: "DEBUG"
      conversionPattern: "%d{yyyy-MM-dd HH:mm:ss,SSSS} %-5p [%c]-[%t] %s%e%n"
    REST_GO:
      file: "go/restful.log"
      maxFileSize: "50m"
      maxBackupIndex: "10"
      threshold: "DEBUG"
      conversionPattern: "%d{yyyy-MM-dd HH:mm:ss,SSSS} %-5p [%c]-[%t] %s%e%n"
    DB:
      file: "db.log"
      maxFileSize: "50m"
      maxBackupIndex: "10"
      threshold: "DEBUG"
      conversionPattern: "%d{yyyy-MM-dd HH:mm:ss,SSSS} %-5p  (%t) %s%E%n"
    HOUSEKEEPING:
      file: "mpt/housekeeping.log"
      maxFileSize: "50m"
      maxBackupIndex: "10"
      threshold: "DEBUG"
      conversionPattern: "%d{yyyy-MM-dd HH:mm:ss,SSSS} %-5p [%c]-[%t] %s%e%n"
    REQUESTENGINE:
      file: "requestEngine.log"
      maxFileSize: "50m"
      maxBackupIndex: "10"
      threshold: "DEBUG"
      conversionPattern: "%d{yyyy-MM-dd HH:mm:ss,SSSS} %-5p [%c]-[%t] %s%e%n"
    REQUESTENGINE_WS_IO:
      file: "webservice_io.log"
      maxFileSize: "50m"
      maxBackupIndex: "10"
      threshold: "DEBUG"
      conversionPattern: "%d{yyyy-MM-dd HH:mm:ss,SSSS} %-5p [%c]-[%t] %s%e%n"
  loggers:
    - { name: "pt.ptinovacao.na.portal.core.mpt.dispatcher", refs: ["DISPATCHER"], level: "INFO", additivity: "false" }
    - { name: "pt.ptinovacao.na.portal.core.mpt.resourcemanager", refs: ["RESOURCEMANAGER"], level: "INFO", additivity: "false" }
    - { name: "pt.ptinovacao.na.portal.core.mpt.engine", refs: ["ENGINE"], level: "INFO", additivity: "false" }
    - { name: "pt.ptinovacao.na.portal.core.mpt.scheduler", refs: ["SCHEDULER"], level: "INFO", additivity: "false" }
    - { name: "pt.ptinovacao.na.portal.webui.restful.mpt", refs: ["REST_MPT"], level: "INFO", additivity: "false" }
    - { name: "pt.ptinovacao.na.portal.webui.restful.go", refs: ["REST_GO"], level: "INFO", additivity: "false" }
    - { name: "pt.ptinovacao.na.portal.db", refs: ["DB"], level: "INFO", additivity: "false" }
    - { name: "org.jberet", refs: ["ENGINE"], level: "INFO", additivity: "false" }
    - { name: "pt.ptinovacao.na.portal.core.mpt.housekeeping", refs: ["HOUSEKEEPING"], level: "INFO", additivity: "false" }
    - { name: "pt.ptinovacao.na.portal.core.request.engine", refs: ["REQUESTENGINE"], level: "INFO", additivity: "false" }
    - { name: "pt.ptinovacao.na.portal.core.request.engine.ws_io", refs: ["REQUESTENGINE_WS_IO"], level: "DEBUG", additivity: "false" }
    - { name: "pt.ptinovacao.na.portal.webui.restful.commons.audit.AuditLoggingFilter", refs: ["REST_AUDIT"], level: "INFO", additivity: "false" }
    - { name: "pt.ptinovacao.na.portal.webui.restful.commons", refs: ["REST_COMMONS"], level: "INFO", additivity: "false" }
    - { name: "io.undertow", refs: ["REST_COMMONS"], level: "INFO", additivity: "false" }
    - { name: "jacorb.config", refs: ["FILE"], level: "ERROR", additivity: "false" }
    - { name: "jacorb", refs: ["FILE"], level: "WARN", additivity: "false" }
    - { name: "sun.rmi", refs: ["FILE"], level: "WARN", additivity: "false" }
    - { name: "org.jboss.as.config", refs: ["FILE"], level: "DEBUG", additivity: "false" }
    - { name: "org.apache.tomcat.util.modeler", refs: ["FILE"], level: "WARN", additivity: "false" }
    - { name: "com.arjuna", refs: ["FILE"], level: "WARN", additivity: "false" }
  root:
    level: "INFO"
    refs: ["FILE"]

naem_dbmanager_log4j2:
  rollingFile:
    SERVER:
      file: "/var/log/alticelabs/naem/server.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    AUDIT-INTERACTIONS:
      file: "/var/log/alticelabs/naem/audit-interactions.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    AUDIT-PAYLOAD:
      file: "/var/log/alticelabs/naem/audit-payload.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    LIB-EXCEPTION:
      file: "/var/log/alticelabs/naem/exceptions.lib.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    BOOTSTRAP:
      file: "/var/log/alticelabs/naem/bootstrap.log"
      maxFileSize: "100MB"
      maxBackupIndex: "1"
    LOGGING-EVENTDISPATCHER:
      file: "/var/log/alticelabs/naem/eventdispatcher.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    LOGGING-EVENTS:
      file: "/var/log/alticelabs/naem/events.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    LOGGING-ENVELOPES:
      file: "/var/log/alticelabs/naem/envelopes.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    LOGGING-COMMUNICATOR-OUT:
      file: "/var/log/alticelabs/naem/communicator-out.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    NOSSIS:
      file: "/var/log/alticelabs/naem/nossis.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    LIBS:
      file: "/var/log/alticelabs/naem/lib.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    ASF:
      file: "/var/log/alticelabs/naem/asf.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    STATISTICS-RESOURCES:
      file: "/var/log/alticelabs/naem/statistics.resources.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    EXCEPTIONS-SERVER:
      file: "/var/log/alticelabs/naem/exceptions.server.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    CORE-PLUGIN-ESPER:
      file: "/var/log/alticelabs/naem/core-plugin-esper.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    CORE-PLUGIN-DBMANAGER-MONGO:
      file: "/var/log/alticelabs/naem/core-plugin-dbmanager-mongodb.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    CORE-PLUGIN-NA:
      file: "/var/log/alticelabs/naem/core-plugin-na.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    WF-NAEM-CORE-FILTERUPDATER:
      file: "/var/log/alticelabs/naem/wf-naem-core-filterupdater.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    WF-NAEM-CORE-EVENTDISPATCHER:
      file: "/var/log/alticelabs/naem/wf-naem-core-eventdispatcher.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    WF-NAEM-MANAGEMENT-FILTERING:
      file: "/var/log/alticelabs/naem/wf-naem-management-filtering.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    EXCEPTIONS-WORKFLOWS:
      file: "/var/log/alticelabs/naem/exceptions.workflows.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    LOGGING-WEB-SERVICE:
      file: "/var/log/alticelabs/naem/webservice-io.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
  loggers:
    - { name: "pt.ptinovacao.asf", level: "info", refs: [ "ASF", "EXCEPTIONS-SERVER:error" ], additivity: "false" }
    - { name: "org.quartz", level: "info", refs: [ "ASF", "EXCEPTIONS-SERVER:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.nacommons.audit.core.AuditWriter.interaction", level: "info", refs: [ "AUDIT-INTERACTIONS", "LIB-EXCEPTION:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.nacommons.audit.core.AuditWriter.payload", level: "info", refs: [ "AUDIT-PAYLOAD", "LIB-EXCEPTION:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.naem.commons.core.boot", level: "info", refs: [ "BOOTSTRAP", "EXCEPTIONS-SERVER:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.nacommons.plugins.esper.EsperPlugin", level: "info", refs: [ "CORE-PLUGIN-ESPER", "EXCEPTIONS-SERVER:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.nacommons.plugins.dbmanager.core.connectors.mongo.MongoDao", level: "info", refs: [ "CORE-PLUGIN-DBMANAGER-MONGO", "EXCEPTIONS-SERVER:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.nacommons.naclient", level: "info", refs: [ "CORE-PLUGIN-NA", "EXCEPTIONS-SERVER:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.naem.workflows.filtering.FilterWorkflow", level: "warn", refs: [ "WF-NAEM-CORE-EVENTDISPATCHER", "EXCEPTIONS-WORKFLOWS:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.naem.workflows.filtering.FilterUpdater", level: "info", refs: [ "WF-NAEM-CORE-FILTERUPDATER", "EXCEPTIONS-WORKFLOWS:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.naem.workflows.filtering.manager.FilterManager", level: "info", refs: [ "WF-NAEM-MANAGEMENT-FILTERING", "EXCEPTIONS-WORKFLOWS:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.naem.workflows.filtering.manager.FilterManagerHttpAdapter", level: "info", refs: [ "WF-NAEM-MANAGEMENT-FILTERING", "EXCEPTIONS-WORKFLOWS:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.naem.workflows", level: "info", refs: [ "SERVER", "EXCEPTIONS-WORKFLOWS:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.nossisfrmwrk", level: "info", refs: [ "NOSSIS", "EXCEPTIONS-SERVER:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.asf.resourcemanagement.ResourceManagerPlugin.allocations", level: "info", refs: [ "STATISTICS-RESOURCES", "EXCEPTIONS-SERVER:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.naem.commons.core.kernel.broker.FilteringLogger", level: "info", refs: [ "LOGGING-EVENTDISPATCHER" ], additivity: "false" }
    - { name: "httpclient.wire", level: "info", refs: [ "LIBS", "LIB-EXCEPTION:error" ], additivity: "false" }
    - { name: "com", level: "info", refs: [ "LIBS", "LIB-EXCEPTION:error" ], additivity: "false" }
    - { name: "org", level: "info", refs: [ "LIBS", "LIB-EXCEPTION:error" ], additivity: "false" }
    - { name: "pt.ptinovacao.naem.commons.core.kernel.broker.EventLogger", level: "info", refs: [ "LOGGING-EVENTS" ], additivity: "false" }
    - { name: "pt.ptinovacao.naem.commons.core.kernel.broker.EnvelopeLogger", level: "info", refs: [ "LOGGING-ENVELOPES" ], additivity: "false" }
    - { name: "pt.ptinovacao.nacommons.plugins.http.client.HttpPlugin", level: "info", refs: [ "LOGGING-WEB-SERVICE" ], additivity: "false" }
    - { name: "pt.ptinovacao.nacommons.plugins.http.webservice.io", level: "info", refs: [ "LOGGING-WEB-SERVICE" ], additivity: "false" }
    - { name: "pt.ptinovacao.nacommons.plugins.http.collector.HttpCollectorPlugin.webserviceio", level: "info", refs: [ "LOGGING-WEB-SERVICE" ], additivity: "false" }
    - { name: "pt.ptinovacao.nacommons.plugins.http.client.HttpPluginMediator", level: "info", refs: [ "LOGGING-WEB-SERVICE" ], additivity: "false" }
    - { name: "pt.ptinovacao.naem.plugins.communicator.CommunicatorPlugin", level: "info", refs: [ "LOGGING-COMMUNICATOR-OUT" ], additivity: "false" }
  root:
    level: "info"
    refs: [ "SERVER", "EXCEPTIONS-SERVER:error" ]

core_reference_data_log4j2:
  rollingFile:
    SERVER:
      file: "/var/log/alticelabs/na-portal/core-catalogs/backend.log"
      maxFileSize: "100MB"
      maxBackupIndex: "5"
      layout: "%d{yyyy-MM-dd'T'HH:mm:ss.SSS}{GMT} %-5p [%t] [CorrelationID=%X{correlationId}, RequestID=%X{requestId}] [%c{3}] %m%n"
    CONFIG_PROPERTY:
      file: "/var/log/alticelabs/na-portal/core-catalogs/configProperty.log"
      maxFileSize: "100MB"
      maxBackupIndex: "5"
      layout: "%d{yyyy-MM-dd'T'HH:mm:ss.SSS}{GMT} %-5p [%t] [CorrelationID=%X{correlationId}, RequestID=%X{requestId}] [%c{3}] %m%n"
    AUDITING:
      file: "/var/log/alticelabs/na-portal/core-catalogs/auditing.log"
      maxFileSize: "100MB"
      maxBackupIndex: "13"
      layout: "%d{yyyy-MM-dd'T'HH:mm:ss.SSS}{GMT} %-5p [%t] [CorrelationID=%X{correlationId}, RequestID=%X{requestId}] [%c{3}] %m%n"
  loggers:
    - { name: "pt.ptinovacao.asf.configuration", level: "info", refs: [ "CONFIG_PROPERTY" ], additivity: "false" }
    - { name: "pt.ptinovacao.netwin.auditing", level: "debug", refs: [ "AUDITING" ], additivity: "false" }
    - { name: "pt.ptinovacao", level: "debug", refs: [ "SERVER" ], additivity: "false" }
    - { name: "org.hibernate", level: "info", refs: [ "SERVER" ], additivity: "false" }
  root:
    level: "info"
    refs: [ "SERVER" ]

mpt_client_log4j2:
  rollingFile:
    APPLICATION:
      file: "/var/log/alticelabs/na-portal/mpt-client/application.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
      layout: "%d{yyyy-MM-dd'T'HH:mm:ss.SSS} [%t] %-5p %c - %m%n"
    CLIENT:
      file: "/var/log/alticelabs/na-portal/mpt-client/mpt-client.log"
      maxFileSize: "50MB"
      maxBackupIndex: "5"
      layout: "%d{yyyy-MM-dd'T'HH:mm:ss.SSS} [%t] %-5p %c - %m%n"
      thresholdFilter: "debug"
    AGENT:
      file: "/var/log/alticelabs/na-portal/mpt-client/mpt-agent.log"
      maxFileSize: "50MB"
      maxBackupIndex: "5"
      layout: "%d{yyyy-MM-dd'T'HH:mm:ss.SSS} [%t] %-5p %c - %m%n"
    CLIENT_EXCEPTIONS:
      file: "/var/log/alticelabs/na-portal/mpt-client/mpt-client-exceptions.log"
      maxFileSize: "50MB"
      maxBackupIndex: "5"
      layout: "%d{yyyy-MM-dd'T'HH:mm:ss.SSS} [%t] %-5p %c - %m%n"
    AGENT_EXCEPTIONS:
      file: "/var/log/alticelabs/na-portal/mpt-client/mpt-agent-exceptions.log"
      maxFileSize: "50MB"
      maxBackupIndex: "5"
      layout: "%d{yyyy-MM-dd'T'HH:mm:ss.SSS} [%t] %-5p %c - %m%n"
  loggers:
    - { name: "pt.ptinovacao.na.portal.mpt.client", level: "debug", refs: [ "CLIENT" ], additivity: "false" }
    - { name: "pt.ptinovacao.na.portal.mpt.agent", level: "debug", refs: [ "AGENT" ], additivity: "false" }
    - { name: "ExceptionClient", level: "debug", refs: [ "CLIENT_EXCEPTIONS" ], additivity: "false" }
    - { name: "ExceptionAgent", level: "debug", refs: [ "AGENT_EXCEPTIONS" ], additivity: "false" }
  root:
    level: "info"
    refs: [ "APPLICATION" ]

na_portal_frontend_log4j2:
  rollingFile:
    APPLICATION:
      file: "/var/log/alticelabs/na-portal/web/application.log"
      maxFileSize: "100MB"
      maxBackupIndex: "2"
    BASEMODULE:
      file: "/var/log/alticelabs/na-portal/web/base.log"
      maxFileSize: "100MB"
      maxBackupIndex: "2"
    MPT:
      file: "/var/log/alticelabs/na-portal/web/mpt.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    GO:
      file: "/var/log/alticelabs/na-portal/web/go.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    CATALOG:
      file: "/var/log/alticelabs/na-portal/web/catalog.log"
      maxFileSize: "50MB"
      maxBackupIndex: "1"
    OPERATIONS-CATALOG:
      file: "/var/log/alticelabs/na-portal/web/operations-catalog.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    MONITORING:
      file: "/var/log/alticelabs/na-portal/web/monitoring.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    NADM:
      file: "/var/log/alticelabs/na-portal/web/nadm.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
    DIAGNOSTICS:
      file: "/var/log/alticelabs/na-portal/web/diagnostics.log"
      maxFileSize: "50MB"
      maxBackupIndex: "2"
  loggers:
    - { name: "base", level: "info", refs: [ "BASEMODULE" ], additivity: "false" }
    - { name: "mpt", level: "info", refs: [ "MPT" ], additivity: "false" }
    - { name: "go", level: "info", refs: [ "GO" ], additivity: "false" }
    - { name: "data-management", level: "info", refs: [ "CATALOG" ], additivity: "false" }
    - { name: "operations-catalog", level: "info", refs: [ "OPERATIONS-CATALOG" ], additivity: "false" }
    - { name: "monitoring", level: "info", refs: [ "MONITORING" ], additivity: "false" }
    - { name: "nadm", level: "info", refs: [ "NADM" ], additivity: "false" }
    - { name: "diagnostics", level: "info", refs: [ "DIAGNOSTICS" ], additivity: "false" }
  root:
    level: "info"
    refs: [ "APPLICATION" ]

nossis_tsc_frontend_entities_catalog_log4j2:
  rollingFile:
    APPLICATION:
      file: "/var/log/alticelabs/nossis-tsc/web/application.log"
      maxFileSize: "100MB"
      maxBackupIndex: "2"
    BASEMODULE:
      file: "/var/log/alticelabs/nossis-tsc/web/base/base.log"
      maxFileSize: "100MB"
      maxBackupIndex: "2"
    CATALOG:
      file: "/var/log/alticelabs/nossis-tsc/web/catalog/catalog.log"
      maxFileSize: "100MB"
      maxBackupIndex: "2"
  loggers:
    - { name: "base", level: "info", refs: [ "BASEMODULE" ], additivity: "false" }
    - { name: "catalog", level: "info", refs: [ "CATALOG" ], additivity: "false" }
  root:
    level: "info"
    refs: [ "APPLICATION" ]
