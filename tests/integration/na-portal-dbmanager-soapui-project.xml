<?xml version="1.0" encoding="UTF-8"?>
<con:soapui-project activeEnvironment="Default" name="na-portal-dbmanager" resourceRoot="" soapui-version="5.0.0" abortOnError="false" runType="SEQUENTIAL" xmlns:con="http://eviware.com/soapui/config"><con:settings/><con:testSuite name="naem-management-na-portal"><con:settings/><con:runType>SEQUENTIAL</con:runType><con:testCase failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="web-ui-filter" searchProperties="true"><con:settings/><con:testStep type="httprequest" name="create-filter"><con:settings/><con:config method="POST" xsi:type="con:HttpRequest" name="create-filter" postQueryString="false" mediaType="application/xml" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>http://*************:8080/management-na-portal-webui/mongonaportal/na-portal-webui/naportal/naportal/create/filters</con:endpoint><con:request><![CDATA[<request>
   <description>na_portal_web_ui_create_filter</description>
   <data>
      <WebUiRequest context="HISTORY_ORDER">
         <filtername>10</filtername>
         <username>netwin</username>
         <fields>
         <name>filter_name</name>
         <value>filter_value</value>
         </fields>
         <group>
         <name>default_group</name>
        <fields>
         <name>filter_name</name>
         </fields>
         </group>
      </WebUiRequest>
   </data>
</request>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:config></con:testStep><con:testStep type="httprequest" name="read-filter"><con:settings/><con:config method="POST" xsi:type="con:HttpRequest" name="read-filter" postQueryString="false" mediaType="application/xml" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>http://*************:8080/management-na-portal-webui/mongonaportal/na-portal-webui/naportal/naportal/read/filters</con:endpoint><con:request><![CDATA[<request>
   <description>na_portal_web_ui_read_filter</description>
   <data>
      <WebUiRequest context="HISTORY_ORDER">
         <filtername>10</filtername>
         <username>netwin</username>
      </WebUiRequest>
   </data>
</request>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:config></con:testStep><con:testStep type="httprequest" name="read-filter-json"><con:settings/><con:config method="POST" xsi:type="con:HttpRequest" name="read-filter-json" postQueryString="false" mediaType="application/json" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>http://*************:8080/management-na-portal-webui/mongonaportal/na-portal-webui/naportal/naportal/read/filters?filternames=false</con:endpoint><con:request>{"description":"Requesting all filters for username: netwin","data":{"any":{"filtername":"1","username":"netwin","context":"HISTORY_ORDER"}}}</con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:config></con:testStep><con:testStep type="httprequest" name="read-all-filters"><con:settings/><con:config method="POST" xsi:type="con:HttpRequest" name="read-all-filters" postQueryString="false" mediaType="application/xml" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>http://*************:8080/management-na-portal-webui/mongonaportal/na-portal-webui/naportal/naportal/read/filters</con:endpoint><con:request><![CDATA[<request>
   <description>na_portal_web_ui_read_filter</description>
   <data>
      <WebUiRequest context="HISTORY_ORDER">
         <username>nossis</username>
      </WebUiRequest>
   </data>
</request>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><con:parameter><con:name>filternames</con:name><con:value>true</con:value><con:style>QUERY</con:style><con:default>true</con:default><con:path xsi:nil="true"/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>dbLimit</con:name><con:value>5</con:value><con:style>QUERY</con:style></con:parameter><con:parameter><con:name>filtername</con:name><con:value/><con:style>QUERY</con:style></con:parameter></con:parameters></con:config></con:testStep><con:testStep type="httprequest" name="update-filter"><con:settings/><con:config method="POST" xsi:type="con:HttpRequest" name="update-filter" postQueryString="false" mediaType="application/xml" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>http://*************:8080/management-na-portal-webui/mongonaportal/na-portal-webui/naportal/naportal/update/filters</con:endpoint><con:request><![CDATA[<request>
   <description>na_portal_web_ui_create_filter</description>
   <data>
      <WebUiRequest context="HISTORY_ORDER">
         <filtername>10</filtername>
         <username>netwin</username>
         <fields>
         <name>new_filter_name</name>
         <value>new_filter_value</value>
         </fields>
         <group isSelected="true">
         <name>new_default_group</name>
        <fields isSelected="true">
         <name>new_filter_name</name>
         </fields>
         </group>
      </WebUiRequest>
   </data>
</request>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:config></con:testStep><con:properties/></con:testCase><con:testCase failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="web-ui-audit" searchProperties="true"><con:settings/><con:testStep type="httprequest" name="create-audit"><con:settings/><con:config method="POST" xsi:type="con:HttpRequest" name="create-audit" postQueryString="false" mediaType="application/xml" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>http://*************:8080/management-na-portal-webui/mongonaportal/na-portal-webui/naportal/naportal/create/audits</con:endpoint><con:request><![CDATA[<request>
   <description>na_portal_web_ui_create_audit</description>
   <data>
      <WebUiRequest context="HISTORY_ORDER">
         <date>crazy date hahahaha</date>
         <username>netwin</username>
         <fields>
         <name>audit_name</name>
         <value>audit_value</value>
         </fields>
         <fields>
         <name>audit_name1</name>
         <value>audit_value2</value>
         </fields>
      </WebUiRequest>
   </data>
</request>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:config></con:testStep><con:testStep type="httprequest" name="read-all-audits"><con:settings/><con:config method="POST" xsi:type="con:HttpRequest" name="read-all-audits" postQueryString="false" mediaType="application/xml" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>http://*************:8080/management-na-portal-webui/mongonaportal/na-portal-webui/naportal/naportal/read/audits</con:endpoint><con:request><![CDATA[<request>
   <description>na_portal_web_ui_create_audit</description>
   <data>
      <WebUiRequest context="HISTORY_ORDER">
         <date>crazy date hahahaha</date>
         <username>netwin</username>
      </WebUiRequest>
   </data>
</request>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:config></con:testStep><con:properties/></con:testCase><con:testCase failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="LoadBalance" searchProperties="true"><con:settings/><con:testStep type="httprequest" name="HTTP Test Request"><con:settings/><con:config method="GET" xsi:type="con:HttpRequest" name="HTTP Test Request" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>http://*************/na-portal-history-rest-interface/restful/naportal/history/orders/last5</con:endpoint><con:request/><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:config></con:testStep><con:loadTest name="LoadTest"><con:settings><con:setting id="HttpSettings@close-connections">false</con:setting></con:settings><con:threadCount>15</con:threadCount><con:startDelay>0</con:startDelay><con:sampleInterval>250</con:sampleInterval><con:calculateTPSOnTimePassed>true</con:calculateTPSOnTimePassed><con:resetStatisticsOnThreadCountChange>true</con:resetStatisticsOnThreadCountChange><con:historyLimit>-1</con:historyLimit><con:testLimit>60</con:testLimit><con:limitType>TIME</con:limitType><con:loadStrategy><con:type>Simple</con:type><con:config><testDelay>1000</testDelay><randomFactor>0.5</randomFactor></con:config></con:loadStrategy><con:assertion type="Step Status" name="Step Status"/><con:maxAssertionErrors>100</con:maxAssertionErrors><con:cancelExcessiveThreads>true</con:cancelExcessiveThreads><con:strategyInterval>500</con:strategyInterval></con:loadTest><con:properties/></con:testCase><con:properties/></con:testSuite><con:properties/><con:wssContainer/><con:oAuth2ProfileContainer/><con:sensitiveInformation/></con:soapui-project>