<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>pt.ptinovacao.na-portal.it</groupId>
        <artifactId>na-portal-integration-tests</artifactId>
        <version>100.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>na-portal-integration-tests-web-data-snapshots</artifactId>
    <packaging>jar</packaging>


    <!-- dependencies -->
    <dependencies>
        <dependency>
            <groupId>pt.ptinovacao.na</groupId>
            <artifactId>na-swe-order-model</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver-core</artifactId>
        </dependency>
        <dependency>
            <groupId> pt.ptinovacao.na-commons</groupId>
            <artifactId>na-commons-model</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na-commons</groupId>
            <artifactId>na-commons-test-api</artifactId>
            <version>${version.pt.ptinovacao.na-commons}</version>
        </dependency>
        <dependency>
            <groupId>javax.ws.rs</groupId>
            <artifactId>javax.ws.rs-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-http</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver-sync</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>bson</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.asf</groupId>
            <artifactId>asf-serializers</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na</groupId>
            <artifactId>na-swe-order-followup-persistence</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na</groupId>
            <artifactId>na-swe-async-notifications-handling-model</artifactId>
        </dependency>
        <dependency>
            <groupId>org.glassfish.hk2</groupId>
            <artifactId>hk2-utils</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.glassfish.hk2</groupId>
            <artifactId>hk2-api</artifactId>
            <scope>runtime</scope>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <mainClass>pt.ptinovacao.na.portal.it.data.snapshots.Main</mainClass>
                        </manifest>
                    </archive>
                    <descriptorRefs>
                        <descriptorRef>jar-with-dependencies</descriptorRef>
                    </descriptorRefs>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
