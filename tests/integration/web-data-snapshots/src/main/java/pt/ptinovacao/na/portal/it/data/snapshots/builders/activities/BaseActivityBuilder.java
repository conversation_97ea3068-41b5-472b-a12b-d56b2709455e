package pt.ptinovacao.na.portal.it.data.snapshots.builders.activities;

import pt.ptinovacao.na.followup.order.model.workflow.activities.BaseActivity;
import pt.ptinovacao.na.followup.order.model.workflow.activities.details.ActivityDetail;

public class BaseActivityBuilder extends ActiviyBuilder<BaseActivity, BaseActivityBuilder> {

    private BaseActivityBuilder(String id) {
        this.instance = this;
        this.id = id;
    }

    public static BaseActivityBuilder withId(String id){
        return new BaseActivityBuilder(id);
    }

    public BaseActivity build(){
        return buildBaseActivity(new BaseActivity(), new ActivityDetail());
    }
}
