package pt.ptinovacao.na.portal.it.data.snapshots;

import pt.ptinovacao.na.followup.order.model.OrderFollowUp;
import pt.ptinovacao.na.portal.it.data.snapshots.builders.OrderBuilder;
import pt.ptinovacao.na.portal.it.data.snapshots.builders.OrderFollowUpBuilder;
import pt.ptinovacao.na.portal.it.data.snapshots.builders.OrderProcessBuilder;
import pt.ptinovacao.na.portal.it.data.snapshots.builders.RelatedPartyBuilder;
import pt.ptinovacao.na.swe.order.model.State;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;
import java.util.UUID;

public class MockedOrders {
    private final String sweTestCallbackUrl;

    public MockedOrders(String sweTestCallbackUrl) {
        this.sweTestCallbackUrl = sweTestCallbackUrl;
    }

    private final static int MINUTES = 60000;

    private static String mockRequestValidationText(String orderId, String externalOrderId, String dateRequired) {
        return "{" +
                "\"description\" : \"Workflow Engine Test - Execution of simulated operation\"," +
                "\"category\" : \"CFS SUM Test - notification\"," +
                "\"orderDate\" : \"" + dateRequired + "\"," +
                "\"requestedCompletionDate\" : \"" + dateRequired + "\"," +
                "\"requesterCallback\" : \"http://*************:60048/callback\"," +
                "\"orderItem\" : [ {" +
                "\"id\" : \"CFS Sum - manual action activity\"," +
                "\"action\" : \"manual-retry-new-request\"," +
                "\"serviceSpecification\" : {" +
                "\"id\" : \"CFS.SUM\"" +
                "}," +
                "\"requestedCompletionDate\" : \"2020-03-24T15:59:31.857Z\"," +
                "\"service\" : {" +
                "\"id\" : \"123\"," +
                "\"category\" : \"CFS\"," +
                "\"description\" : \"Workflow Engine Test - non-simulated operation\"," +
                "\"serviceCharacteristic\" : [ {" +
                "\"name\" : \"N_SUMS\"," +
                "\"value\" : \"3\"" +
                "}]" +
                "}" +
                "} ]," +
                "\"id\" : \"" + orderId + "\"," +
                "\"externalId\" : \"" + externalOrderId + "\"" +
                "}";
    }

    private static String timeToDate(int timeBefore) {
        long startTime = System.currentTimeMillis() - timeBefore * MINUTES;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:sss'Z'");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
        Date resultDate = new Date(startTime);
        return resultDate.toString();
    }

    public OrderFollowUp orderFollowUp() {
        final int STARTED_MINUTES_BEFORE = 27;
        final String ORDER_ID = "70a74483-623e-5ad3-a553-1140ae0afdac2";
        final String EXTERNAL_ID = "4abdbc19-ba61-4030-989e-82301b76c0a3";
        final String START_DATE = timeToDate(STARTED_MINUTES_BEFORE);
        return OrderFollowUpBuilder
                .withOrderId(ORDER_ID)
                .withExternalId(EXTERNAL_ID)
                .withNotificationId("5b4f0091-f6fe-4985-a60f-9e9db89715b3")
                .withSystem("user_TESTING")
                .withOrigin("**********")
                .withOperationsCatalogVersion("SWE_SELFNET_TESTING")
                .withRequesterCallback(sweTestCallbackUrl)
                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                .durationInMinutes(13)
                .withReceived(mockRequestValidationText(ORDER_ID, EXTERNAL_ID, START_DATE))
                .withSyncResponse("{\"externalId\": \"4abdbc19-ba61-4032-989e-82301b76c0a3\", \"description\": \"description\", \"category\": \"SALES_ORDER\", \"orderDate\": \"2018-02-02T13:37:16\", \"requestedCompletionDate\": \"2018-02-03T13:37:16\", \"requesterCallback\": \"http://*************:60048/callback\", \"orderItem\": [ { \"id\": \"cfsIm\", \"action\": \"soapivalidOrder\", \"serviceSpecification\": { \"id\": \"CFS.IM\" }, \"service\": { \"id\": \"123456\", \"category\": \"CFS\", \"serviceCharacteristic\": [ { \"name\": \"dstPort\", \"value\": \"10000\" }, { \"name\": \"botnetReal\", \"value\": \"true\" }, { \"name\": \"dstIpAddress\", \"value\": \"***************\" }, { \"name\": \"frequency\", \"value\": \"49.99\" } ], \"component\": [ { \"name\": \"componentType1\", \"component\": { \"type\": \"componentType1\", \"component\": [ { \"name\": \"componentType2\", \"component\": { \"type\": \"componentType2\", \"property\": [ { \"name\": \"host\", \"value\": \"***************\" }, { \"name\": \"port\", \"value\": \"8500\" } ] } } ] } } ] }, \"state\": \"Acknowledged\" }, { \"id\" : \"rfsFlowT\", \"action\" : \"soapivalidOrder\", \"requestedCompletionDate\" : \"2018-02-02T13:37:18\", \"orderItemRelationship\" : [ { \"id\" : \"cfsIm\", \"type\" : \"bundled\" } ], \"serviceSpecification\" : { \"id\" : \"RFS.FLOWT\" }, \"service\" : { \"id\" : \"1234567\", \"category\" : \"RFS\", \"serviceCharacteristic\" : [ { \"name\" : \"dstIpAddress\", \"value\" : \"***************\" }, { \"name\" : \"srcIpAddress\", \"value\" : \"***************\" } ], \"resource\" : [ { \"name\" : \"LR.FLOWT\", \"resource\" : { \"id\" : \"lrFlowT\", \"type\" : \"Resource\", \"property\" : [ { \"name\" : \"dstIpAddress\", \"value\" : \"***************\" } ] } } ] }, \"state\" : \"Acknowledged\" }, { \"id\" : \"rfsSnort\", \"action\" : \"soapivalidOrder\", \"requestedCompletionDate\" : \"2018-02-02T13:37:18\", \"orderItemRelationship\" : [ { \"id\" : \"cfsIm\", \"type\" : \"bundled\" } ], \"serviceSpecification\" : { \"id\" : \"RFS.SNORT\" }, \"service\" : { \"id\" : \"565\", \"category\" : \"RFS\", \"serviceCharacteristic\" : [ { \"name\" : \"startInspection\", \"value\" : \"2018-07-12T10:21:10.979Z\" } ] }, \"state\" : \"Acknowledged\" } ], \"id\": \"70a74483-623e-4ed3-a553-1140ae0afdac2\", \"state\": \"Acknowledged\", \"href\": \"http://**********:9004/ptinovacao/serviceOrderingManagement/serviceOrder/70a74483-623e-4ed3-a553-1140ae0afdac2\"}")
                .withAsyncResponse("{\"eventId\": \"e418cfc2-4e7d-47ed-93c0-39ec30f313d0-2\", \"eventTime\": \"2018-07-12T10:21:21.627+00:00\", \"eventType\": \"ServiceOrderStateChangeNotification\", \"event\": { \"serviceOrder\": {   \"externalId\": \"4abdbc19-ba61-4032-989e-82301b76c0a3\", \"description\": \"description\", \"category\": \"SALES_ORDER\", \"orderDate\": \"2018-02-02T13:37:16\", \"requestedCompletionDate\": \"2018-02-03T13:37:16\", \"requesterCallback\": \"http://*************:60048/callback\", \"orderItem\": [     {       \"id\": \"cfsIm\", \"action\": \"soapivalidOrder\", \"serviceSpecification\": {       \"id\": \"CFS.IM\"     }, \"service\": {       \"id\": \"123456\", \"category\": \"CFS\", \"serviceCharacteristic\": [         {           \"name\": \"dstPort\", \"value\": \"10000\"         },         {           \"name\": \"botnetReal\", \"value\": \"true\"         },         {           \"name\": \"dstIpAddress\", \"value\": \"***************\"         },         {           \"name\": \"frequency\", \"value\": \"49.99\"         }       ], \"component\": [         {           \"name\": \"componentType1\", \"component\": {           \"type\": \"componentType1\", \"component\": [             {               \"name\": \"componentType2\", \"component\": {               \"type\": \"componentType2\", \"property\": [                 {                   \"name\": \"host\", \"value\": \"***************\"                 },                 {                   \"name\": \"port\", \"value\": \"8500\"                 }               ]             }             }           ]         }         }       ]     }, \"state\": \"Failed\", \"statusMessage\": {       \"code\": \"CL_02001-NOK-NOK\", \"message\": \"Operation not found\"     }     }   ], \"id\": \"70a74483-623e-4ed3-a553-1140ae0afdac2\", \"state\": \"Failed\", \"href\": \"http://**********:9004/ptinovacao/serviceOrderingManagement/serviceOrder/70a74483-623e-4ed3-a553-1140ae0afdac2\" }}}")
                .isPayloadReceivedValid(true)
                .withInternal(OrderBuilder
                        .withId(ORDER_ID)
                        .withExternalId(EXTERNAL_ID)
                        .withDescription("description")
                        .withCategory("SALES_ORDER")
                        .withOrderDate("2018-04-06T11:57:11")
                        .withRequestedCompletionDate("2018-04-06T11:57:11")
                        .withState(State.COMPLETED)
                        .withRequesterCallback("https://som.idahs.com/alt/nossis/notification")
                        .addRelatedParties(RelatedPartyBuilder
                                .withId("65946422")
                                .withRole("customer")
                                .addSimpleAttribute("type", "SOHO")
                                .addSimpleAttribute("first-name", "FTTH")
                                .addSimpleAttribute("last-name", "FT2")
                                .build())
                        .addOrderProcesse(OrderProcessBuilder
                                .withId("88D9360C960E42C7824F687062F23AA7")
                                .withAction("add")
                                .withName("CFS.3PGPON.BUNDLE")
                                .withServiceId("0780157053910_3GPONBundle")
                                .withCategory("CFS")
                                .withDescription("3P GPON Bundle")
                                .withState(State.COMPLETED)
                                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                                .durationInMinutes(13)
                                .withWorkflowName("WorkflowCompleted")
                                .withVersion("v1")
                                .addCfs(OrderProcessBuilder
                                        .withId("BE76A40030CC4FF4B3AC81E482E518BC")
                                        .withAction("add")
                                        .withName("CFS.HSI")
                                        .withServiceId("0780157053910_Data")
                                        .withCategory("CFS")
                                        .withDescription("HSI")
                                        .withState(State.ACKNOWLEDGED)
                                        .addRelationship("88D9360C960E42C7824F687062F23AA7", "bundled")
                                        .buildCfs()
                                )
                                .addCfs(OrderProcessBuilder
                                        .withId("FA09351BF38D4AD982B3C5736BE525C9")
                                        .withAction("add")
                                        .withName("CFS.GPONACCESS")
                                        .withServiceId("0780157053910_FiberGateway")
                                        .withCategory("CFS")
                                        .withDescription("GPON Access")
                                        .withState(State.ACKNOWLEDGED)
                                        .addRelationship("88D9360C960E42C7824F687062F23AA7", "bundled")
                                        .addSimpleAttribute("reserveId", "********")
                                        .addRelationship("88D9360C960E42C7824F687062F23AA7", "bundled")
                                        .addResource(OrderProcessBuilder
                                                .withId("0780157053910_E76A7624A5594056B9777FC764B8A17F")
                                                .withName("accessNetworkAccount")
                                                .addSimpleAttribute("ipVersion", "ipv4")
                                                .addSimpleAttribute("action", "add")
                                                .addSimpleAttribute("description", "GPON Account")
                                                .addSimpleAttribute("category", "Account")
                                                .addSimpleAttribute("ipType", "public")
                                                .buildResource()
                                        )
                                        .addResource(OrderProcessBuilder
                                                .withId("0780157053910_36F729E5C2A7462EB927204799BE1BA1")
                                                .withName("cpeOntRg")
                                                .addSimpleAttribute("serialNumber", "5054494E912C0DEF")
                                                .addSimpleAttribute("regime", "rent")
                                                .addSimpleAttribute("vendor", "ALTICELABS")
                                                .addSimpleAttribute("context", "1")
                                                .addSimpleAttribute("action", "add")
                                                .addSimpleAttribute("description", "RG and ONT")
                                                .addSimpleAttribute("model", "GR241AG")
                                                .addSimpleAttribute("category", "Equipment")
                                                .addSimpleAttribute("version", "1.0")
                                                .addSimpleAttribute("mac", "0006912C0DEF")
                                                .buildResource()
                                        )
                                        .buildCfs()
                                )
                                .buildCfsProcess()
                        )
                        .build())
                .build();
    }

    public OrderFollowUp acknowledgedOrder() {
        final int STARTED_MINUTES_BEFORE = 60; // 1 hour ago
        final String ORDER_ID = "31eb8b3a-9d12-43dc-9366-a32a9bd04d9f";
        final String EXTERNAL_ID = "07fe025b-4cf1-4d0a-bc03-f40ed767bfa4";
        final String START_DATE = timeToDate(STARTED_MINUTES_BEFORE);
        return OrderFollowUpBuilder
                .withOrderId(ORDER_ID)
                .withExternalId(EXTERNAL_ID)
                .withNotificationId("f87d5ceb-d640-4b7d-97fb-38a437315dba")
                .withSystem("user_TESTING")
                .withOrigin("**********")
                .withOperationsCatalogVersion("SWE_SELFNET_TESTING")
                .withRequesterCallback(sweTestCallbackUrl)
                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                .durationInMinutes(1)
                .withReceived(mockRequestValidationText(ORDER_ID, EXTERNAL_ID, START_DATE))
                .withSyncResponse("{ \"externalId\" : \"4abdbc19-ba61-4032-989e-82301b76c0a3\", \"description\" : \"description\", \"category\" : \"DUMMY\", \"orderDate\" : \"2018-02-02T13:37:16\", \"requestedCompletionDate\" : \"2018-02-03T13:37:16\", \"requesterCallback\" : \"http://*************:60048/callback\", \"orderItem\" : [{ \"id\" : \"cfsIm\",        \"action\" : \"soapivalidOrder\",        \"serviceSpecification\" : {          \"id\" : \"CFS.IM\"        },        \"service\" : {          \"id\" : \"123456\",          \"category\" : \"CFS\",          \"serviceCharacteristic\" : [            {              \"name\" : \"dstPort\",              \"value\" : \"10000\"            },            {              \"name\" : \"botnetReal\",              \"value\" : \"true\"            },            {              \"name\" : \"dstIpAddress\",              \"value\" : \"***************\"            },            {              \"name\" : \"frequency\",              \"value\" : \"49.99\"            }          ],          \"component\" : [            {              \"name\" : \"componentType1\",              \"component\" : {                \"type\" : \"componentType1\",                \"component\" : [                  {                    \"name\" : \"componentType2\",                    \"component\" : {                      \"type\" : \"componentType2\",                      \"property\" : [                        {                          \"name\" : \"host\",                          \"value\" : \"***************\"                        },                        {                          \"name\" : \"port\",                          \"value\" : \"8500\"                        }                      ]                    }                  }                ]              }            }          ]        },        \"state\" : \"Acknowledged\"      },      {        \"id\" : \"rfsFlowT\",        \"action\" : \"soapivalidOrder\",        \"requestedCompletionDate\" : \"2018-02-02T13:37:18\",        \"orderItemRelationship\" : [          {            \"id\" : \"cfsIm\",            \"type\" : \"bundled\"          }        ],        \"serviceSpecification\" : {          \"id\" : \"RFS.FLOWT\"        },        \"service\" : {          \"id\" : \"1234567\",          \"category\" : \"RFS\",          \"serviceCharacteristic\" : [            {              \"name\" : \"dstIpAddress\",              \"value\" : \"***************\"            },            {              \"name\" : \"srcIpAddress\",              \"value\" : \"***************\"            }          ],          \"resource\" : [            {              \"name\" : \"LR.FLOWT\",              \"resource\" : {                \"id\" : \"lrFlowT\",                \"type\" : \"Resource\",                \"property\" : [                  {                    \"name\" : \"dstIpAddress\",                    \"value\" : \"***************\"                  }                ]              }            }          ]        },        \"state\" : \"Acknowledged\"      },      {        \"id\" : \"rfsSnort\",        \"action\" : \"soapivalidOrder\",        \"requestedCompletionDate\" : \"2018-02-02T13:37:18\",        \"orderItemRelationship\" : [          {            \"id\" : \"cfsIm\",            \"type\" : \"bundled\"          }        ],        \"serviceSpecification\" : {          \"id\" : \"RFS.SNORT\"        },        \"service\" : {          \"id\" : \"565\",          \"category\" : \"RFS\",          \"serviceCharacteristic\" : [            {              \"name\" : \"startInspection\",              \"value\" : \"2018-07-12T10:21:10.979Z\"            }          ]        },        \"state\" : \"Acknowledged\"      }    ],    \"id\" : \"70a74483-623e-4ed3-a553-1140ae0afdac2\",    \"state\" : \"Acknowledged\",    \"href\" : \"http://**********:9004/ptinovacao/serviceOrderingManagement/serviceOrder/70a74483-623e-4ed3-a553-1140ae0afdac2\"  }")
                .withAsyncResponse("{    \"eventId\" : \"e418cfc2-4e7d-47ed-93c0-39ec30f313d0-2\",    \"eventTime\" : \"2018-07-12T10:21:21.627+00:00\",    \"eventType\" : \"ServiceOrderStateChangeNotification\",    \"event\" : {      \"serviceOrder\" : {        \"externalId\" : \"4abdbc19-ba61-4032-989e-82301b76c0a3\",        \"description\" : \"description\",        \"category\" : \"DUMMY\",        \"orderDate\" : \"2018-02-02T13:37:16\",        \"requestedCompletionDate\" : \"2018-02-03T13:37:16\",        \"requesterCallback\" : \"http://*************:60048/callback\",        \"orderItem\" : [          {            \"id\" : \"cfsIm\",            \"action\" : \"soapivalidOrder\",            \"serviceSpecification\" : {              \"id\" : \"CFS.IM\"            },            \"service\" : {              \"id\" : \"123456\",              \"category\" : \"CFS\",              \"serviceCharacteristic\" : [                {                  \"name\" : \"dstPort\",                  \"value\" : \"10000\"                },                {                  \"name\" : \"botnetReal\",                  \"value\" : \"true\"                },                {                  \"name\" : \"dstIpAddress\",                  \"value\" : \"***************\"                },                {                  \"name\" : \"frequency\",                  \"value\" : \"49.99\"                }              ],              \"component\" : [                {                  \"name\" : \"componentType1\",                  \"component\" : {                    \"type\" : \"componentType1\",                    \"component\" : [                      {                        \"name\" : \"componentType2\",                        \"component\" : {                          \"type\" : \"componentType2\",                          \"property\" : [                            {                              \"name\" : \"host\",                              \"value\" : \"***************\"                            },                            {                              \"name\" : \"port\",                              \"value\" : \"8500\"                            }                          ]                        }                      }                    ]                  }                }              ]            },            \"state\" : \"Failed\",            \"statusMessage\" : {              \"code\" : \"CL_02001-NOK\",              \"message\" : \"Operation not found\"            }          }        ],        \"id\" : \"70a74483-623e-4ed3-a553-1140ae0afdac2\",        \"state\" : \"Failed\",        \"href\" : \"http://**********:9004/ptinovacao/serviceOrderingManagement/serviceOrder/70a74483-623e-4ed3-a553-1140ae0afdac2\"      }    }  }")
                .isPayloadReceivedValid(true)
                .withInternal(OrderBuilder
                        .withId(ORDER_ID)
                        .withExternalId(EXTERNAL_ID)
                        .withDescription("description")
                        .withCategory("DUMMY")
                        .withOrderDate("2018-07-12T06:21:20.995-04:00")
                        .withRequestedCompletionDate("2018-02-03T13:37:16")
                        .withState(State.ACKNOWLEDGED)
                        .withRequesterCallback("http://*************:60048/callback")
                        .addRelatedParties(RelatedPartyBuilder
                                .withId("65946422")
                                .withRole("customer")
                                .addSimpleAttribute("type", "residential")
                                .addSimpleAttribute("first-name", "FTTH")
                                .addSimpleAttribute("last-name", "FT2")
                                .build())
                        .addOrderProcesse(OrderProcessBuilder
                                .withId("cfsIm")
                                .withAction("soapivalidOrder")
                                .withName("CFS.IM")
                                .withServiceId("123456")
                                .withCategory("CFS")
                                .withState(State.ACKNOWLEDGED)
                                .withWorkflowName("workflow1")
                                .withVersion("v1")
                                .addSimpleAttribute("dstPort", "10000")
                                .addSimpleAttribute("botnetReal", "true")
                                .addSimpleAttribute("dstIpAddress", "***************")
                                .addSimpleAttribute("frequency", "49.99")
                                .addRfs(OrderProcessBuilder
                                        .withId("rfsSnort")
                                        .withAction("soapivalidOrder")
                                        .withName("RFS.SNORT")
                                        .withRequestedCompletionDate("2018-02-02T13:37:18")
                                        .withServiceId("565")
                                        .withCategory("RFS")
                                        .withState(State.ACKNOWLEDGED)
                                        .addSimpleAttribute("startInspection", "2018-07-12T10:21:10.979Z")
                                        .addRelationship("cfsIm", "bundled")
                                        .buildRfs())
                                .addRfs(OrderProcessBuilder
                                        .withId("rfsFlowT")
                                        .withAction("soapivalidOrder")
                                        .withName("RFS.FLOWT")
                                        .withRequestedCompletionDate("2018-02-02T13:37:18")
                                        .withServiceId("1234567")
                                        .withCategory("RFS")
                                        .withState(State.ACKNOWLEDGED)
                                        .addResource(OrderProcessBuilder
                                                .withId("lrFlowT")
                                                .withName("LR.FLOWT")
                                                .addSimpleAttribute("dstIpAddress", "***************")
                                                .buildResource())
                                        .addSimpleAttribute("attributeType", "***************")
                                        .addSimpleAttribute("srcIpAddress", "***************")
                                        .addRelationship("cfsIm", "bundled")
                                        .buildRfs())
                                .buildCfsProcess()
                        ).build())
                .build();
    }

    public OrderFollowUp oldAcknowledgedOrder() {
        final int STARTED_MINUTES_BEFORE = 4320; // 3 days ago today 5:00 am
        final String ORDER_ID = "85a52c69-c83f-4bb0-8ca7-74d78b72f9b6";
        final String EXTERNAL_ID = "02d5414d-2271-415f-a195-bb516dde0dec";
        final String START_DATE = timeToDate(STARTED_MINUTES_BEFORE);
        return OrderFollowUpBuilder
                .withOrderId(ORDER_ID)
                .withExternalId(EXTERNAL_ID)
                .withNotificationId("306269d8-3fea-4b81-9b2c-1655a813d7e6")
                .withSystem("user_TESTING")
                .withOrigin("**********")
                .withOperationsCatalogVersion("SWE_SELFNET_TESTING")
                .withRequesterCallback(sweTestCallbackUrl)
                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE) // 3 days ago today 5:00 am
                .durationInMinutes(10)
                .withReceived(mockRequestValidationText(ORDER_ID, EXTERNAL_ID, START_DATE))
                .isPayloadReceivedValid(true)
                .withSyncResponse("{ \"externalId\" : \"4abdbc19-ba61-4032-989e-82301b76c0a3\", \"description\" : \"description\", \"category\" : \"DUMMY\", \"orderDate\" : \"2018-02-02T13:37:16\", \"requestedCompletionDate\" : \"2018-02-03T13:37:16\", \"requesterCallback\" : \"http://*************:60048/callback\", \"orderItem\" : [{ \"id\" : \"cfsIm\",        \"action\" : \"soapivalidOrder\",        \"serviceSpecification\" : {          \"id\" : \"CFS.IM\"        },        \"service\" : {          \"id\" : \"123456\",          \"category\" : \"CFS\",          \"serviceCharacteristic\" : [            {              \"name\" : \"dstPort\",              \"value\" : \"10000\"            },            {              \"name\" : \"botnetReal\",              \"value\" : \"true\"            },            {              \"name\" : \"dstIpAddress\",              \"value\" : \"***************\"            },            {              \"name\" : \"frequency\",              \"value\" : \"49.99\"            }          ],          \"component\" : [            {              \"name\" : \"componentType1\",              \"component\" : {                \"type\" : \"componentType1\",                \"component\" : [                  {                    \"name\" : \"componentType2\",                    \"component\" : {                      \"type\" : \"componentType2\",                      \"property\" : [                        {                          \"name\" : \"host\",                          \"value\" : \"***************\"                        },                        {                          \"name\" : \"port\",                          \"value\" : \"8500\"                        }                      ]                    }                  }                ]              }            }          ]        },        \"state\" : \"Acknowledged\"      },      {        \"id\" : \"rfsFlowT\",        \"action\" : \"soapivalidOrder\",        \"requestedCompletionDate\" : \"2018-02-02T13:37:18\",        \"orderItemRelationship\" : [          {            \"id\" : \"cfsIm\",            \"type\" : \"bundled\"          }        ],        \"serviceSpecification\" : {          \"id\" : \"RFS.FLOWT\"        },        \"service\" : {          \"id\" : \"1234567\",          \"category\" : \"RFS\",          \"serviceCharacteristic\" : [            {              \"name\" : \"dstIpAddress\",              \"value\" : \"***************\"            },            {              \"name\" : \"srcIpAddress\",              \"value\" : \"***************\"            }          ],          \"resource\" : [            {              \"name\" : \"LR.FLOWT\",              \"resource\" : {                \"id\" : \"lrFlowT\",                \"type\" : \"Resource\",                \"property\" : [                  {                    \"name\" : \"dstIpAddress\",                    \"value\" : \"***************\"                  }                ]              }            }          ]        },        \"state\" : \"Acknowledged\"      },      {        \"id\" : \"rfsSnort\",        \"action\" : \"soapivalidOrder\",        \"requestedCompletionDate\" : \"2018-02-02T13:37:18\",        \"orderItemRelationship\" : [          {            \"id\" : \"cfsIm\",            \"type\" : \"bundled\"          }        ],        \"serviceSpecification\" : {          \"id\" : \"RFS.SNORT\"        },        \"service\" : {          \"id\" : \"565\",          \"category\" : \"RFS\",          \"serviceCharacteristic\" : [            {              \"name\" : \"startInspection\",              \"value\" : \"2018-07-12T10:21:10.979Z\"            }          ]        },        \"state\" : \"Acknowledged\"      }    ],    \"id\" : \"70a74483-623e-4ed3-a553-1140ae0afdac2\",    \"state\" : \"Acknowledged\",    \"href\" : \"http://**********:9004/ptinovacao/serviceOrderingManagement/serviceOrder/70a74483-623e-4ed3-a553-1140ae0afdac2\"  }")
                .withAsyncResponse("{    \"eventId\" : \"e418cfc2-4e7d-47ed-93c0-39ec30f313d0-2\",    \"eventTime\" : \"2018-07-12T10:21:21.627+00:00\",    \"eventType\" : \"ServiceOrderStateChangeNotification\",    \"event\" : {      \"serviceOrder\" : {        \"externalId\" : \"4abdbc19-ba61-4032-989e-82301b76c0a3\",        \"description\" : \"description\",        \"category\" : \"DUMMY\",        \"orderDate\" : \"2018-02-02T13:37:16\",        \"requestedCompletionDate\" : \"2018-02-03T13:37:16\",        \"requesterCallback\" : \"http://*************:60048/callback\",        \"orderItem\" : [          {            \"id\" : \"cfsIm\",            \"action\" : \"soapivalidOrder\",            \"serviceSpecification\" : {              \"id\" : \"CFS.IM\"            },            \"service\" : {              \"id\" : \"123456\",              \"category\" : \"CFS\",              \"serviceCharacteristic\" : [                {                  \"name\" : \"dstPort\",                  \"value\" : \"10000\"                },                {                  \"name\" : \"botnetReal\",                  \"value\" : \"true\"                },                {                  \"name\" : \"dstIpAddress\",                  \"value\" : \"***************\"                },                {                  \"name\" : \"frequency\",                  \"value\" : \"49.99\"                }              ],              \"component\" : [                {                  \"name\" : \"componentType1\",                  \"component\" : {                    \"type\" : \"componentType1\",                    \"component\" : [                      {                        \"name\" : \"componentType2\",                        \"component\" : {                          \"type\" : \"componentType2\",                          \"property\" : [                            {                              \"name\" : \"host\",                              \"value\" : \"***************\"                            },                            {                              \"name\" : \"port\",                              \"value\" : \"8500\"                            }                          ]                        }                      }                    ]                  }                }              ]            },            \"state\" : \"Failed\",            \"statusMessage\" : {              \"code\" : \"CL_02001-NOK\",              \"message\" : \"Operation not found\"            }          }        ],        \"id\" : \"70a74483-623e-4ed3-a553-1140ae0afdac2\",        \"state\" : \"Failed\",        \"href\" : \"http://**********:9004/ptinovacao/serviceOrderingManagement/serviceOrder/70a74483-623e-4ed3-a553-1140ae0afdac2\"      }    }  }")
                .withInternal(OrderBuilder
                        .withId(ORDER_ID)
                        .withExternalId(EXTERNAL_ID)
                        .withDescription("description")
                        .withCategory("DUMMY")
                        .withOrderDate("2018-07-12T06:21:20.995-04:00")
                        .withRequestedCompletionDate("2018-02-03T13:37:16")
                        .withState(State.ACKNOWLEDGED)
                        .withRequesterCallback("http://*************:60048/callback")
                        .addRelatedParties(RelatedPartyBuilder
                                .withId("65946422")
                                .withRole("customer")
                                .addSimpleAttribute("type", "enterprise")
                                .addSimpleAttribute("first-name", "FTTH")
                                .addSimpleAttribute("last-name", "FT2")
                                .build())
                        .addOrderProcesse(OrderProcessBuilder
                                .withId("cfsIm")
                                .withAction("soapivalidOrder")
                                .withName("CFS.IM")
                                .withServiceId("123456")
                                .withCategory("CFS")
                                .withState(State.ACKNOWLEDGED)
                                .withWorkflowName("workflow1")
                                .withVersion("v1")
                                .addSimpleAttribute("dstPort", "10000")
                                .addSimpleAttribute("botnetReal", "true")
                                .addSimpleAttribute("dstIpAddress", "***************")
                                .addSimpleAttribute("frequency", "49.99")
                                .addRfs(OrderProcessBuilder
                                        .withId("rfsSnort")
                                        .withAction("soapivalidOrder")
                                        .withName("RFS.SNORT")
                                        .withRequestedCompletionDate("2018-02-02T13:37:18")
                                        .withServiceId("565")
                                        .withCategory("RFS")
                                        .withState(State.ACKNOWLEDGED)
                                        .addSimpleAttribute("startInspection", "2018-07-12T10:21:10.979Z")
                                        .addRelationship("cfsIm", "bundled")
                                        .buildRfs())
                                .addRfs(OrderProcessBuilder
                                        .withId("rfsFlowT")
                                        .withAction("soapivalidOrder")
                                        .withName("RFS.FLOWT")
                                        .withRequestedCompletionDate("2018-02-02T13:37:18")
                                        .withServiceId("1234567")
                                        .withCategory("RFS")
                                        .withState(State.ACKNOWLEDGED)
                                        .addResource(OrderProcessBuilder
                                                .withId("lrFlowT")
                                                .withName("LR.FLOWT")
                                                .addSimpleAttribute("dstIpAddress", "***************")
                                                .buildResource())
                                        .addSimpleAttribute("attributeType", "***************")
                                        .addSimpleAttribute("srcIpAddress", "***************")
                                        .addRelationship("cfsIm", "bundled")
                                        .buildRfs())
                                .buildCfsProcess()
                        ).build())
                .build();
    }

    public OrderFollowUp heldOrder() {
        final int STARTED_MINUTES_BEFORE = 55;
        final String ORDER_ID = "70a74483-623e-4ed3-a553-1140ae0afdec2";
        final String EXTERNAL_ID = "4abdbc19-ba61-4032-989e-82301b7660a3";
        final String START_DATE = timeToDate(STARTED_MINUTES_BEFORE);
        return OrderFollowUpBuilder
                .withOrderId(ORDER_ID)
                .withExternalId(EXTERNAL_ID)
                .withNotificationId("87571376-16ff-42ba-b815-7636ff3ba43b")
                .withSystem("user_TESTING")
                .withOrigin("**********")
                .withOperationsCatalogVersion("SWE_SELFNET_TESTING")
                .withRequesterCallback(sweTestCallbackUrl)
                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                .durationInMinutes(5)
                .withReceived(mockRequestValidationText(ORDER_ID, EXTERNAL_ID, START_DATE))
                .isPayloadReceivedValid(true)
                .withSyncResponse("{ \"externalId\" : \"4abdbc19-ba61-4032-989e-82301b76c0a3\", \"description\" : \"description\"," + "    \"category\" : \"DUMMY\"," + "    \"orderDate\" : \"2018-02-02T13:37:16\"," + "    \"requestedCompletionDate\" : \"2018-02-03T13:37:16\"," + "    \"requesterCallback\" : \"http://*************:60048/callback\"," + "    \"orderItem\" : [" + "      {" + "        \"id\" : \"cfsIm\"," + "        \"action\" : \"soapivalidOrder\"," + "        \"serviceSpecification\" : {" + "          \"id\" : \"CFS.IM\"" + "        }," + "        \"service\" : {" + "          \"id\" : \"123456\"," + "          \"category\" : \"CFS\"," + "          \"serviceCharacteristic\" : [" + "            {" + "              \"name\" : \"dstPort\"," + "              \"value\" : \"10000\"" + "            }," + "            {" + "              \"name\" : \"botnetReal\"," + "              \"value\" : \"true\"" + "            }," + "            {" + "              \"name\" : \"dstIpAddress\"," + "              \"value\" : \"***************\"" + "            }," + "            {" + "              \"name\" : \"frequency\"," + "              \"value\" : \"49.99\"" + "            }" + "          ]," + "          \"component\" : [" + "            {" + "              \"name\" : \"componentType1\"," + "              \"component\" : {" + "                \"type\" : \"componentType1\"," + "                \"component\" : [" + "                  {" + "                    \"name\" : \"componentType2\"," + "                    \"component\" : {" + "                      \"type\" : \"componentType2\"," + "                      \"property\" : [" + "                        {" + "                          \"name\" : \"host\"," + "                          \"value\" : \"***************\"" + "                        }," + "                        {" + "                          \"name\" : \"port\"," + "                          \"value\" : \"8500\"" + "                        }" + "                      ]" + "                    }" + "                  }" + "                ]" + "              }" + "            }" + "          ]" + "        }," + "        \"state\" : \"Acknowledged\"" + "      }," + "      {" + "        \"id\" : \"rfsFlowT\"," + "        \"action\" : \"soapivalidOrder\"," + "        \"requestedCompletionDate\" : \"2018-02-02T13:37:18\"," + "        \"orderItemRelationship\" : [" + "          {" + "            \"id\" : \"cfsIm\"," + "            \"type\" : \"bundled\"" + "          }" + "        ]," + "        \"serviceSpecification\" : {" + "          \"id\" : \"RFS.FLOWT\"" + "        }," + "        \"service\" : {" + "          \"id\" : \"1234567\"," + "          \"category\" : \"RFS\"," + "          \"serviceCharacteristic\" : [" + "            {" + "              \"name\" : \"dstIpAddress\"," + "              \"value\" : \"***************\"" + "            }," + "            {" + "              \"name\" : \"srcIpAddress\"," + "              \"value\" : \"***************\"" + "            }" + "          ]," + "          \"resource\" : [" + "            {" + "              \"name\" : \"LR.FLOWT\"," + "              \"resource\" : {" + "                \"id\" : \"lrFlowT\"," + "                \"type\" : \"Resource\"," + "                \"property\" : [" + "                  {" + "                    \"name\" : \"dstIpAddress\"," + "                    \"value\" : \"***************\"" + "                  }" + "                ]" + "              }" + "            }" + "          ]" + "        }," + "        \"state\" : \"Acknowledged\"" + "      }," + "      {" + "        \"id\" : \"rfsSnort\"," + "        \"action\" : \"soapivalidOrder\"," + "        \"requestedCompletionDate\" : \"2018-02-02T13:37:18\"," + "        \"orderItemRelationship\" : [" + "          {" + "            \"id\" : \"cfsIm\"," + "            \"type\" : \"bundled\"" + "          }" + "        ]," + "        \"serviceSpecification\" : {" + "          \"id\" : \"RFS.SNORT\"" + "        }," + "        \"service\" : {" + "          \"id\" : \"565\"," + "          \"category\" : \"RFS\"," + "          \"serviceCharacteristic\" : [" + "            {" + "              \"name\" : \"startInspection\"," + "              \"value\" : \"2018-07-12T10:21:10.979Z\"" + "            }" + "          ]" + "        }," + "        \"state\" : \"Acknowledged\"" + "      }" + "    ]," + "    \"id\" : \"70a74483-623e-4ed3-a553-1140ae0afdac2\"," + "    \"state\" : \"Acknowledged\"," + "    \"href\" : \"http://**********:9004/ptinovacao/serviceOrderingManagement/serviceOrder/70a74483-623e-4ed3-a553-1140ae0afdac2\"" + "  }")
                .withInternal(OrderBuilder
                        .withId(ORDER_ID)
                        .withExternalId(EXTERNAL_ID)
                        .withCategory("DUMMY")
                        .withState(State.HELD)
                        .addOrderProcesse(OrderProcessBuilder
                                .withId("cfsIm")
                                .withAction("soapivalidOrder")
                                .withName("CFS.IM")
                                .withServiceId("123456")
                                .withCategory("CFS")
                                .withState(State.HELD)
                                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                                .durationInMinutes(22)
                                .withWorkflowName("workflow1")
                                .withVersion("v1")
                                .addSimpleAttribute("dstPort", "10000")
                                .addSimpleAttribute("botnetReal", "true")
                                .addSimpleAttribute("dstIpAddress", "***************")
                                .addSimpleAttribute("frequency", "49.99")
                                .addRfs(OrderProcessBuilder
                                        .withId("rfsSnort")
                                        .withAction("soapivalidOrder")
                                        .withName("RFS.SNORT")
                                        .withRequestedCompletionDate("2018-02-02T13:37:18")
                                        .withServiceId("565")
                                        .withCategory("RFS")
                                        .withState(State.ACKNOWLEDGED)
                                        .addSimpleAttribute("startInspection", "2018-07-12T10:21:10.979Z")
                                        .addRelationship("cfsIm", "bundled")
                                        .buildRfs()
                                )
                                .addRfs(OrderProcessBuilder
                                        .withId("rfsFlowT")
                                        .withAction("soapivalidOrder")
                                        .withName("RFS.FLOWT")
                                        .withRequestedCompletionDate("2018-02-02T13:37:18")
                                        .withServiceId("5656")
                                        .withCategory("RFS")
                                        .withState(State.ACKNOWLEDGED)
                                        .addSimpleAttribute("dstIpAddress", "***************")
                                        .addSimpleAttribute("srcIpAddress", "***************")
                                        .addRelationship("cfsIm", "bundled")
                                        .addResource(OrderProcessBuilder
                                                .withId("lrFlowT")
                                                .withName("LR.FLOWT")
                                                .addSimpleAttribute("dstIpAddress", "***************")
                                                .buildResource()
                                        )
                                        .buildRfs()
                                )
                                .addStatusMessage("CL_02001-NOK-NOK", "Operation not found")
                                .buildCfsProcess()
                        )
                        .build()
                )
                .build();
    }

    public OrderFollowUp cancelledOrder() {
        final int STARTED_MINUTES_BEFORE = 72; // 1 hour and 12 minutes ago
        final String ORDER_ID = "70a74483-623e-4ed3-a553-1140ae0afdac2";
        final String EXTERNAL_ID = "31eb8b3a-9d12-43dc-9366-a32a9bd04d9f";
        final String START_DATE = timeToDate(STARTED_MINUTES_BEFORE);
        return OrderFollowUpBuilder
                .withOrderId(ORDER_ID)
                .withExternalId(EXTERNAL_ID)
                .withNotificationId("875b4670-21f9-42e2-b0e8-49caae96bc4c")
                .withSystem("OM")
                .withOrigin("**********")
                .withOperationsCatalogVersion("SWE_SELFNET_TESTING")
                .withRequesterCallback(sweTestCallbackUrl)
                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                .durationInMinutes(22)
                .withReceived(mockRequestValidationText(ORDER_ID, EXTERNAL_ID, START_DATE))
                .isPayloadReceivedValid(true)
                .withSyncResponse("{" + "    \"externalId\" : \"31eb8b3a-9d12-43dc-9366-a32a9bd04d9f\"," + "    \"description\" : \"description\"," + "    \"category\" : \"DUMMY\"," + "    \"orderDate\" : \"2018-02-02T13:37:16\"," + "    \"requestedCompletionDate\" : \"2018-02-03T13:37:16\"," + "    \"requesterCallback\" : \"http://*************:60048/callback\"," + "    \"orderItem\" : [" + "      {" + "        \"id\" : \"cfsIm\"," + "        \"action\" : \"soapivalidOrder\"," + "        \"serviceSpecification\" : {" + "          \"id\" : \"CFS.IM\"" + "        }," + "        \"service\" : {" + "          \"id\" : \"123456\"," + "          \"category\" : \"CFS\"," + "          \"serviceCharacteristic\" : [" + "            {" + "              \"name\" : \"dstPort\"," + "              \"value\" : \"10000\"" + "            }," + "            {" + "              \"name\" : \"botnetReal\"," + "              \"value\" : \"true\"" + "            }," + "            {" + "              \"name\" : \"dstIpAddress\"," + "              \"value\" : \"***************\"" + "            }," + "            {" + "              \"name\" : \"frequency\"," + "              \"value\" : \"49.99\"" + "            }" + "          ]," + "          \"component\" : [" + "            {" + "              \"name\" : \"componentType1\"," + "              \"component\" : {" + "                \"type\" : \"componentType1\"," + "                \"component\" : [" + "                  {" + "                    \"name\" : \"componentType2\"," + "                    \"component\" : {" + "                      \"type\" : \"componentType2\"," + "                      \"property\" : [" + "                        {" + "                          \"name\" : \"host\"," + "                          \"value\" : \"***************\"" + "                        }," + "                        {" + "                          \"name\" : \"port\"," + "                          \"value\" : \"8500\"" + "                        }" + "                      ]" + "                    }" + "                  }" + "                ]" + "              }" + "            }" + "          ]" + "        }," + "        \"state\" : \"Acknowledged\"" + "      }," + "      {" + "        \"id\" : \"rfsFlowT\"," + "        \"action\" : \"soapivalidOrder\"," + "        \"requestedCompletionDate\" : \"2018-02-02T13:37:18\"," + "        \"orderItemRelationship\" : [" + "          {" + "            \"id\" : \"cfsIm\"," + "            \"type\" : \"bundled\"" + "          }" + "        ]," + "        \"serviceSpecification\" : {" + "          \"id\" : \"RFS.FLOWT\"" + "        }," + "        \"service\" : {" + "          \"id\" : \"1234567\"," + "          \"category\" : \"RFS\"," + "          \"serviceCharacteristic\" : [" + "            {" + "              \"name\" : \"dstIpAddress\"," + "              \"value\" : \"***************\"" + "            }," + "            {" + "              \"name\" : \"srcIpAddress\"," + "              \"value\" : \"***************\"" + "            }" + "          ]," + "          \"resource\" : [" + "            {" + "              \"name\" : \"LR.FLOWT\"," + "              \"resource\" : {" + "                \"id\" : \"lrFlowT\"," + "                \"type\" : \"Resource\"," + "                \"property\" : [" + "                  {" + "                    \"name\" : \"dstIpAddress\"," + "                    \"value\" : \"***************\"" + "                  }" + "                ]" + "              }" + "            }" + "          ]" + "        }," + "        \"state\" : \"Acknowledged\"" + "      }," + "      {" + "        \"id\" : \"rfsSnort\"," + "        \"action\" : \"soapivalidOrder\"," + "        \"requestedCompletionDate\" : \"2018-02-02T13:37:18\"," + "        \"orderItemRelationship\" : [" + "          {" + "            \"id\" : \"cfsIm\"," + "            \"type\" : \"bundled\"" + "          }" + "        ]," + "        \"serviceSpecification\" : {" + "          \"id\" : \"RFS.SNORT\"" + "        }," + "        \"service\" : {" + "          \"id\" : \"565\"," + "          \"category\" : \"RFS\"," + "          \"serviceCharacteristic\" : [" + "            {" + "              \"name\" : \"startInspection\"," + "              \"value\" : \"2018-07-12T10:21:10.979Z\"" + "            }" + "          ]" + "        }," + "        \"state\" : \"Acknowledged\"" + "      }" + "    ]," + "    \"id\" : \"70a74483-623e-4ed3-a553-1140ae0afdac2\"," + "    \"state\" : \"Acknowledged\"," + "    \"href\" : \"http://**********:9004/ptinovacao/serviceOrderingManagement/serviceOrder/70a74483-623e-4ed3-a553-1140ae0afdac2\"" + "  }")
                .withAsyncResponse("{" + "    \"eventId\" : \"e418cfc2-4e7d-47ed-93c0-39ec30f313d0-2\"," + "    \"eventTime\" : \"2018-07-12T10:21:21.627+00:00\"," + "    \"eventType\" : \"ServiceOrderStateChangeNotification\"," + "    \"event\" : {" + "      \"serviceOrder\" : {" + "        \"externalId\" : \"31eb8b3a-9d12-43dc-9366-a32a9bd04d9f\"," + "        \"description\" : \"description\"," + "        \"category\" : \"DUMMY\"," + "        \"orderDate\" : \"2018-02-02T13:37:16\"," + "        \"requestedCompletionDate\" : \"2018-02-03T13:37:16\"," + "        \"requesterCallback\" : \"http://*************:60048/callback\"," + "        \"orderItem\" : [" + "          {" + "            \"id\" : \"cfsIm\"," + "            \"action\" : \"soapivalidOrder\"," + "            \"serviceSpecification\" : {" + "              \"id\" : \"CFS.IM\"" + "            }," + "            \"service\" : {" + "              \"id\" : \"123456\"," + "              \"category\" : \"CFS\"," + "              \"serviceCharacteristic\" : [" + "                {" + "                  \"name\" : \"dstPort\"," + "                  \"value\" : \"10000\"" + "                }," + "                {" + "                  \"name\" : \"botnetReal\"," + "                  \"value\" : \"true\"" + "                }," + "                {" + "                  \"name\" : \"dstIpAddress\"," + "                  \"value\" : \"***************\"" + "                }," + "                {" + "                  \"name\" : \"frequency\"," + "                  \"value\" : \"49.99\"" + "                }" + "              ]," + "              \"component\" : [" + "                {" + "                  \"name\" : \"componentType1\"," + "                  \"component\" : {" + "                    \"type\" : \"componentType1\"," + "                    \"component\" : [" + "                      {" + "                        \"name\" : \"componentType2\"," + "                        \"component\" : {" + "                          \"type\" : \"componentType2\"," + "                          \"property\" : [" + "                            {" + "                              \"name\" : \"host\"," + "                              \"value\" : \"***************\"" + "                            }," + "                            {" + "                              \"name\" : \"port\"," + "                              \"value\" : \"8500\"" + "                            }" + "                          ]" + "                        }" + "                      }" + "                    ]" + "                  }" + "                }" + "              ]" + "            }," + "            \"state\" : \"Failed\"," + "            \"statusMessage\" : {" + "              \"code\" : \"CL_02001-NOK\"," + "              \"message\" : \"Operation not found\"" + "            }" + "          }" + "        ]," + "        \"id\" : \"70a74483-623e-4ed3-a553-1140ae0afdac2\"," + "        \"state\" : \"Failed\"," + "        \"href\" : \"http://**********:9004/ptinovacao/serviceOrderingManagement/serviceOrder/70a74483-623e-4ed3-a553-1140ae0afdac2\"" + "      }" + "    }" + "  }")
                .withInternal(OrderBuilder
                        .withId(ORDER_ID)
                        .withExternalId(EXTERNAL_ID)
                        .withCategory("DUMMY")
                        .withDescription("description")
                        .withState(State.CANCELLED)
                        .addOrderProcesse(OrderProcessBuilder
                                .withId("cfsIm")
                                .withAction("soapivalidOrder")
                                .withName("CFS.IM")
                                .withServiceId("123456")
                                .withCategory("CFS")
                                .withState(State.CANCELLED)
                                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                                .durationInMinutes(22)
                                .withWorkflowName("workflow1")
                                .withVersion("v1")
                                .addSimpleAttribute("dstPort", "10000")
                                .addSimpleAttribute("botnetReal", "true")
                                .addSimpleAttribute("dstIpAddress", "***************")
                                .addSimpleAttribute("frequency", "49.99")
                                .addStatusMessage("CL_371337-NOK", "canceled for some reason")
                                .addRfs(OrderProcessBuilder
                                        .withId("rfsSnort")
                                        .withAction("soapivalidOrder")
                                        .withName("RFS.SNORT")
                                        .withRequestedCompletionDate("2018-02-02T13:37:18")
                                        .withServiceId("5656")
                                        .withCategory("RFS")
                                        .withState(State.ACKNOWLEDGED)
                                        .addSimpleAttribute("startInspection", "2018-07-12T10:21:10.979Z")
                                        .addRelationship("cfsIm", "bundled")
                                        .buildRfs()
                                )
                                .addRfs(OrderProcessBuilder
                                        .withId("rfsFlowT")
                                        .withAction("soapivalidOrder")
                                        .withName("RFS.FLOWT")
                                        .withRequestedCompletionDate("2018-02-02T13:37:18")
                                        .withServiceId("1234567")
                                        .withCategory("RFS")
                                        .withState(State.ACKNOWLEDGED)
                                        .addSimpleAttribute("dstIpAddress", "***************")
                                        .addSimpleAttribute("srcIpAddress", "***************")
                                        .addRelationship("cfsIm", "bundled")
                                        .addResource(OrderProcessBuilder
                                                .withId("lrFlowT")
                                                .withName("LR.FLOWT")
                                                .addSimpleAttribute("dstIpAddress", "***************")
                                                .buildResource()
                                        )
                                        .buildRfs()
                                )
                                .buildCfsProcess()
                        )
                        .addOrderProcesse(OrderProcessBuilder
                                .withId("cfsB2b")
                                .withAction("soapivalidOrder")
                                .withName("CFS.B2B.HSI")
                                .withServiceId("1111111")
                                .withCategory("CFS")
                                .withState(State.CANCELLED)
                                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                                .durationInMinutes(22)
                                .withWorkflowName("workflow1")
                                .withVersion("v1")
                                .addSimpleAttribute("dstPort", "10000")
                                .addSimpleAttribute("botnetReal", "true")
                                .addSimpleAttribute("dstIpAddress", "***************")
                                .addSimpleAttribute("frequency", "49.99")
                                .addStatusMessage("CL_371337-NOK", "canceled for some reason")
                                .buildCfsProcess()
                        )
                        .addRelatedParties(RelatedPartyBuilder
                                        .withId("65946422")
                                        .withRole("customer")
                                        .addSimpleAttribute("type", "residential")
                                        .addSimpleAttribute("first-name", "FTTH")
                                        .addSimpleAttribute("last-name", "FT2")
                                        .addSimpleAttribute("frequency", "49.99")
                                        .build(),
                                RelatedPartyBuilder
                                        .withId("54645217")
                                        .withRole("technician")
                                        .addSimpleAttribute("first-name", "John")
                                        .addSimpleAttribute("last-name", "Cena")
                                        .build()
                        )
                        .build()
                )
                .build();
    }

    public OrderFollowUp invalidOrder() {
        final int STARTED_MINUTES_BEFORE = 135; // 2 hours and 15 minutes ago
        final String ORDER_ID = "4786a4c4-6006-40da-8b85-67345156545a14";
        final String EXTERNAL_ID = "4786a4c4-6006-40da-8b85-67345156545a14";
        final String START_DATE = timeToDate(STARTED_MINUTES_BEFORE);
        return OrderFollowUpBuilder
                .withOrderId(ORDER_ID)
                .withExternalId(EXTERNAL_ID)
                .withSystem("user_no_version")
                .withOrigin("************")
                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                .withReceived(mockRequestValidationText(ORDER_ID, EXTERNAL_ID, START_DATE))
                .withSyncResponse("{    \"code\" : \"CL_01000\",    \"description\" : \"Order has invalid or missing parameters (see details)\",    \"details\" : [      {        \"code\" : \"CL_01002\",        \"message\" : \"Invalid or missing mandatory parameter\",        \"description\" : \"Order Category is missing\"      },      {        \"code\" : \"CL_01002\",        \"message\" : \"Invalid or missing mandatory parameter\",        \"description\" : \"Requester Callback is missing\"      },      {        \"code\" : \"CL_01002\",        \"message\" : \"Invalid or missing mandatory parameter\",        \"description\" : \"Order OrderItem is empty\"      }    ],    \"message\" : \"Invalid order structure\"  }")
                .isPayloadReceivedValid(true)
                .withInternal(OrderBuilder
                        .withId(ORDER_ID)
                        .withExternalId(EXTERNAL_ID)
                        .withCategory("SALES_ORDER")
                        .withState(State.REJECTED)
                        .addRelatedParties()
                        .build()
                )
                .build();
    }

    public OrderFollowUp blockedOrder() {
        final int STARTED_MINUTES_BEFORE = 171; // 2 hours and 51 minutes ago
        final String ORDER_ID = "4786a4c4-6006-40da-8b85-67345156545a29";
        final String EXTERNAL_ID = "4786a4c4-6006-40da-8b85-67345156545a29";
        final String START_DATE = timeToDate(STARTED_MINUTES_BEFORE);
        return OrderFollowUpBuilder
                .withOrderId(ORDER_ID)
                .withExternalId(EXTERNAL_ID)
                .withSystem("user_PRODUCTION")
                .withOrigin("************")
                .withOperationsCatalogVersion("SWE_SELFNET_PRODUCTION")
                .withRequesterCallback(sweTestCallbackUrl)
                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                .isPayloadReceivedValid(true)
                .withReceived(mockRequestValidationText(ORDER_ID, EXTERNAL_ID, START_DATE))
                .withSyncResponse("{    \"code\" : \"AP_00001\",    \"description\" : \"Internal error occurred (see details)\",    \"details\" : [      {        \"code\" : \"AP_02003\",        \"message\" : \"Operation is in blocked state\",        \"description\" : \"The operation 'add' is in an invalid state: 'BLOCKED'.\"      }    ],    \"message\" : \"Internal server error\"  }")
                .withInternal(OrderBuilder
                        .withId(ORDER_ID)
                        .withExternalId(EXTERNAL_ID)
                        .withCategory("SALES_ORDER")
                        .withState(State.REJECTED)
                        .build()
                )
                .build();
    }

    public OrderFollowUp failedOrder() {
        final int STARTED_MINUTES_BEFORE = 100; // 1 hour and 40 minutes and 4 seconds ago
        final String ORDER_ID = "79ac9f07-ed0d-4483-954f-2e8a00ba741c13";
        final String EXTERNAL_ID = "4abdbc19-ba61-4032-989e-82301b76c0a3";
        final String START_DATE = timeToDate(STARTED_MINUTES_BEFORE);
        return OrderFollowUpBuilder
                .withOrderId(ORDER_ID)
                .withExternalId(EXTERNAL_ID)
                .withNotificationId("5eab6f69-0333-4343-93a9-2123eebf539d")
                .withSystem("user_no_version")
                .withOrigin("***********")
                .withOperationsCatalogVersion("SWE_SELFNET_PRODUCTION")
                .withRequesterCallback(sweTestCallbackUrl)
                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                .durationInMinutes(STARTED_MINUTES_BEFORE - 1)
                .isPayloadReceivedValid(true)
                .withReceived(mockRequestValidationText(ORDER_ID, EXTERNAL_ID, START_DATE))
                .withInternal(OrderBuilder
                        .withId(ORDER_ID)
                        .withExternalId(EXTERNAL_ID)
                        .withDescription("description")
                        .withCategory("DUMMY")
                        .withOrderDate("2018-02-02T13:37:16")
                        .withRequestedCompletionDate("2018-02-03T13:37:16")
                        .withRequesterCallback("http://***********:60048/callback")
                        .withState(State.FAILED)
                        .addRelatedParties()
                        .addOrderProcesse(OrderProcessBuilder
                                .withId("ed45c559-fd68-4092-9da9-7393dc542552")
                                .withAction("add")
                                .withName("CFS.VOICE")
                                .withServiceId("MOBILE-VOICE-002")
                                .withCategory("CFS")
                                .withDescription("Mobile Voice Service")
                                .withState(State.FAILED)
                                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                                .addSimpleAttribute("profile", "melhor ainda")
                                .addSimpleAttribute("imsi", "123456789012346")
                                .addSimpleAttribute("msisdn", "1234567891")
                                .buildCfsProcess()
                        )
                        .build()
                )
                .withSyncResponse("{ \"externalId\" : \"4abdbc19-ba61-4032-989e-82301b76c0a3\", \"description\" : \"description\"," + "    \"category\" : \"DUMMY\"," + "    \"orderDate\" : \"2018-02-02T13:37:16\"," + "    \"requestedCompletionDate\" : \"2018-02-03T13:37:16\"," + "    \"requesterCallback\" : \"http://*************:60048/callback\"," + "    \"orderItem\" : [" + "      {" + "        \"id\" : \"cfsIm\"," + "        \"action\" : \"soapivalidOrder\"," + "        \"serviceSpecification\" : {" + "          \"id\" : \"CFS.IM\"" + "        }," + "        \"service\" : {" + "          \"id\" : \"123456\"," + "          \"category\" : \"CFS\"," + "          \"serviceCharacteristic\" : [" + "            {" + "              \"name\" : \"dstPort\"," + "              \"value\" : \"10000\"" + "            }," + "            {" + "              \"name\" : \"botnetReal\"," + "              \"value\" : \"true\"" + "            }," + "            {" + "              \"name\" : \"dstIpAddress\"," + "              \"value\" : \"***************\"" + "            }," + "            {" + "              \"name\" : \"frequency\"," + "              \"value\" : \"49.99\"" + "            }" + "          ]," + "          \"component\" : [" + "            {" + "              \"name\" : \"componentType1\"," + "              \"component\" : {" + "                \"type\" : \"componentType1\"," + "                \"component\" : [" + "                  {" + "                    \"name\" : \"componentType2\"," + "                    \"component\" : {" + "                      \"type\" : \"componentType2\"," + "                      \"property\" : [" + "                        {" + "                          \"name\" : \"host\"," + "                          \"value\" : \"***************\"" + "                        }," + "                        {" + "                          \"name\" : \"port\"," + "                          \"value\" : \"8500\"" + "                        }" + "                      ]" + "                    }" + "                  }" + "                ]" + "              }" + "            }" + "          ]" + "        }," + "        \"state\" : \"Acknowledged\"" + "      }," + "      {" + "        \"id\" : \"rfsFlowT\"," + "        \"action\" : \"soapivalidOrder\"," + "        \"requestedCompletionDate\" : \"2018-02-02T13:37:18\"," + "        \"orderItemRelationship\" : [" + "          {" + "            \"id\" : \"cfsIm\"," + "            \"type\" : \"bundled\"" + "          }" + "        ]," + "        \"serviceSpecification\" : {" + "          \"id\" : \"RFS.FLOWT\"" + "        }," + "        \"service\" : {" + "          \"id\" : \"1234567\"," + "          \"category\" : \"RFS\"," + "          \"serviceCharacteristic\" : [" + "            {" + "              \"name\" : \"dstIpAddress\"," + "              \"value\" : \"***************\"" + "            }," + "            {" + "              \"name\" : \"srcIpAddress\"," + "              \"value\" : \"***************\"" + "            }" + "          ]," + "          \"resource\" : [" + "            {" + "              \"name\" : \"LR.FLOWT\"," + "              \"resource\" : {" + "                \"id\" : \"lrFlowT\"," + "                \"type\" : \"Resource\"," + "                \"property\" : [" + "                  {" + "                    \"name\" : \"dstIpAddress\"," + "                    \"value\" : \"***************\"" + "                  }" + "                ]" + "              }" + "            }" + "          ]" + "        }," + "        \"state\" : \"Acknowledged\"" + "      }," + "      {" + "        \"id\" : \"rfsSnort\"," + "        \"action\" : \"soapivalidOrder\"," + "        \"requestedCompletionDate\" : \"2018-02-02T13:37:18\"," + "        \"orderItemRelationship\" : [" + "          {" + "            \"id\" : \"cfsIm\"," + "            \"type\" : \"bundled\"" + "          }" + "        ]," + "        \"serviceSpecification\" : {" + "          \"id\" : \"RFS.SNORT\"" + "        }," + "        \"service\" : {" + "          \"id\" : \"565\"," + "          \"category\" : \"RFS\"," + "          \"serviceCharacteristic\" : [" + "            {" + "              \"name\" : \"startInspection\"," + "              \"value\" : \"2018-07-12T10:21:10.979Z\"" + "            }" + "          ]" + "        }," + "        \"state\" : \"Acknowledged\"" + "      }" + "    ]," + "    \"id\" : \"70a74483-623e-4ed3-a553-1140ae0afdac2\"," + "    \"state\" : \"Acknowledged\"," + "    \"href\" : \"http://**********:9004/ptinovacao/serviceOrderingManagement/serviceOrder/70a74483-623e-4ed3-a553-1140ae0afdac2\"" + "  }")
                .withAsyncResponse("{    \"eventId\" : \"e418cfc2-4e7d-47ed-93c0-39ec30f313d0-2\",    \"eventTime\" : \"2018-07-12T10:21:21.627+00:00\",    \"eventType\" : \"ServiceOrderStateChangeNotification\",    \"event\" : {      \"serviceOrder\" : {        \"externalId\" : \"4abdbc19-ba61-4032-989e-82301b76c0a3\",        \"description\" : \"description\",        \"category\" : \"DUMMY\",        \"orderDate\" : \"2018-02-02T13:37:16\",        \"requestedCompletionDate\" : \"2018-02-03T13:37:16\",        \"requesterCallback\" : \"http://*************:60048/callback\",        \"orderItem\" : [          {            \"id\" : \"cfsIm\",            \"action\" : \"soapivalidOrder\",            \"serviceSpecification\" : {              \"id\" : \"CFS.IM\"            },            \"service\" : {              \"id\" : \"123456\",              \"category\" : \"CFS\",              \"serviceCharacteristic\" : [                {                  \"name\" : \"dstPort\",                  \"value\" : \"10000\"                },                {                  \"name\" : \"botnetReal\",                  \"value\" : \"true\"                },                {                  \"name\" : \"dstIpAddress\",                  \"value\" : \"***************\"                },                {                  \"name\" : \"frequency\",                  \"value\" : \"49.99\"                }              ],              \"component\" : [                {                  \"name\" : \"componentType1\",                  \"component\" : {                    \"type\" : \"componentType1\",                    \"component\" : [                      {                        \"name\" : \"componentType2\",                        \"component\" : {                          \"type\" : \"componentType2\",                          \"property\" : [                            {                              \"name\" : \"host\",                              \"value\" : \"***************\"                            },                            {                              \"name\" : \"port\",                              \"value\" : \"8500\"                            }                          ]                        }                      }                    ]                  }                }              ]            },            \"state\" : \"Failed\",            \"statusMessage\" : {              \"code\" : \"CL_02001-NOK\",              \"message\" : \"Operation not found\"            }          }        ],        \"id\" : \"70a74483-623e-4ed3-a553-1140ae0afdac2\",        \"state\" : \"Failed\",        \"href\" : \"http://**********:9004/ptinovacao/serviceOrderingManagement/serviceOrder/70a74483-623e-4ed3-a553-1140ae0afdac2\"      }    }  }")
                .build();
    }

    public OrderFollowUp inProgressMvnoOrder() {
        final int STARTED_MINUTES_BEFORE = 70; // 1 hour ago 10 minutes ago 4 seconds ago
        final String ORDER_ID = "49199b7a-61e5-48b4-93f2-c8e4a89f9c872";
        final String EXTERNAL_ID = "2d2459d6-4ad5-4104-8ece-087c2c0c7e98";
        final String START_DATE = timeToDate(STARTED_MINUTES_BEFORE);
        final String REQUESTER_CALLBACK = "http://**************:8998/client/listener";
        return OrderFollowUpBuilder
                .withOrderId(ORDER_ID)
                .withExternalId(EXTERNAL_ID)
                .withSystem("ommvno")
                .withOrigin("10.112.208.216")
                .withOperationsCatalogVersion("MVNO_OPERATIONS")
                .withRequesterCallback(REQUESTER_CALLBACK)
                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                .durationInMinutes(STARTED_MINUTES_BEFORE - 1)
                .isPayloadReceivedValid(true)
                .withReceived(mockRequestValidationText(ORDER_ID, EXTERNAL_ID, START_DATE))
                .withInternal(OrderBuilder
                        .withId(ORDER_ID)
                        .withExternalId(EXTERNAL_ID)
                        .withDescription("Mobile Service Order")
                        .withCategory("MVNO")
                        .withOrderDate("2018-08-07T13:23:18.370Z")
                        .withRequesterCallback(REQUESTER_CALLBACK)
                        .withState(State.IN_PROGRESS)
                        .addOrderProcesse(OrderProcessBuilder
                                .withId("ed45c559-fd68-4092-9da9-7393dc542552")
                                .withAction("add")
                                .withName("CFS.VOICE")
                                .withServiceId("MOBILE-VOICE-002")
                                .withCategory("CFS")
                                .withDescription("Mobile Voice Service")
                                .withState(State.IN_PROGRESS)
                                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                                .addSimpleAttribute("profile", "melhor ainda")
                                .addSimpleAttribute("imsi", "123456789012346")
                                .addSimpleAttribute("msisdn", "1234567891")
                                .buildCfsProcess()
                        )
                        .addOrderProcesse(OrderProcessBuilder
                                .withId("0b00ac4a-aa42-456d-9ddd-77553d2a400d")
                                .withAction("add")
                                .withName("CFS.VOICE")
                                .withServiceId("MOBILE-VOICE-001")
                                .withCategory("CFS")
                                .withDescription("Mobile Voice Service")
                                .withState(State.PENDING)
                                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                                .durationInMinutes(1)
                                .addSimpleAttribute("profile", "mt bom")
                                .addSimpleAttribute("imsi", "123456789012345")
                                .addSimpleAttribute("msisdn", "1234567890")
                                .buildCfsProcess()
                        )
                        .build()
                )
                .withSyncResponse("{    \"externalId\": \"2d2459d6-4ad5-4104-8ece-087c2c0c7e98\",    \"description\": \"Mobile Service Order\",    \"category\": \"MVNO\",    \"requesterCallback\": \"http://**************:8998/client/listener\",    \"orderItem\": [      {        \"id\": \"0b00ac4a-aa42-456d-9ddd-77553d2a400d\",        \"action\": \"add\",        \"serviceSpecification\": {          \"id\": \"CFS.VOICE\"        },        \"service\": {          \"category\": \"CFS\",          \"id\": \"MOBILE-VOICE-001\",          \"description\": \"Mobile Voice Service\",          \"serviceCharacteristic\": [            {              \"name\": \"msisdn\",              \"value\": \"1234567890\"            },            {              \"name\": \"imsi\",              \"value\": \"123456789012345\"            },            {              \"name\": \"profile\",              \"value\": \"mt bom\"            }          ]        },        \"state\": \"Acknowledged\"      },      {        \"id\": \"ed45c559-fd68-4092-9da9-7393dc542552\",        \"action\": \"add\",        \"serviceSpecification\": {          \"id\": \"CFS.VOICE\"        },        \"service\": {          \"category\": \"CFS\",          \"id\": \"MOBILE-VOICE-002\",          \"description\": \"Mobile Voice Service\",          \"serviceCharacteristic\": [            {              \"name\": \"msisdn\",              \"value\": \"1234567891\"            },            {              \"name\": \"imsi\",              \"value\": \"123456789012346\"            },            {              \"name\": \"profile\",              \"value\": \"melhor ainda\"            }          ]        },        \"state\": \"Acknowledged\"      }    ],    \"id\": \"49199b7a-61e5-48b4-93f2-c8e4a89f9c872\",    \"state\": \"Acknowledged\",    \"href\": \"http://ommvnoaus-dev-evl-swe.c.ptin.corppt.com:9004/ptinovacao/serviceOrderingManagement/serviceOrder/49199b7a-61e5-48b4-93f2-c8e4a89f9c872\",    \"orderDate\": \"2018-08-07T13:23:18.370Z\"  }")
                .build();
    }

    public OrderFollowUp invalidOrderNoCategory() {
        final int STARTED_MINUTES_BEFORE = 135; // 2 hour ago 15 minutes ago
        final String ORDER_ID = "05a85ef2-04d1-4751-9209-b64da00467cf";
        final String EXTERNAL_ID = "e58f5d95-9fcd-4234-8ff4-59172811f9a2";
        final String START_DATE = timeToDate(STARTED_MINUTES_BEFORE);
        return OrderFollowUpBuilder
                .withOrderId(ORDER_ID)
                .withExternalId(EXTERNAL_ID)
                .withSystem("user_no_category")
                .withOrigin("***********0")
                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                .isPayloadReceivedValid(true)
                .withReceived(mockRequestValidationText(ORDER_ID, EXTERNAL_ID, START_DATE))
                .withInternal(OrderBuilder
                        .withId(ORDER_ID)
                        .withExternalId(EXTERNAL_ID)
                        .withState(State.REJECTED)
                        .build()
                )
                .withSyncResponse("{    \"code\" : \"CL_01000\",    \"description\" : \"Order has invalid or missing parameters (see details)\",    \"details\" : [      {        \"code\" : \"CL_01002\",        \"message\" : \"Invalid or missing mandatory parameter\",        \"description\" : \"Order Category is missing\"      },      {        \"code\" : \"CL_01002\",        \"message\" : \"Invalid or missing mandatory parameter\",        \"description\" : \"Requester Callback is missing\"      },      {        \"code\" : \"CL_01002\",        \"message\" : \"Invalid or missing mandatory parameter\",        \"description\" : \"Order OrderItem is empty\"      }    ],    \"message\" : \"Invalid order structure\"  }")
                .build();
    }

    public OrderFollowUp orderFollowUpEqExtId1() {
        final int STARTED_MINUTES_BEFORE = 87; // 1 hour ago 27 minutes ago
        final String ORDER_ID = "30468833-d90c-4351-8caa-48efb1449a07";
        final String EXTERNAL_ID = "2b34334d-cd7b-40c5-9fcc-666690016911";
        final String START_DATE = timeToDate(STARTED_MINUTES_BEFORE);
        return OrderFollowUpBuilder
                .withOrderId(ORDER_ID)
                .withExternalId(EXTERNAL_ID)
                .withNotificationId("ed743a28-5e8d-4c6e-842b-e0c6b4ec6db0")
                .withSystem("user_TESTING_EXAMPLE_1")
                .withOrigin("**********")
                .withOperationsCatalogVersion("SWE_SELFNET_TESTING")
                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                .durationInMinutes(11)
                .withRequesterCallback(sweTestCallbackUrl)
                .isPayloadReceivedValid(true)
                .withReceived(mockRequestValidationText(ORDER_ID, EXTERNAL_ID, START_DATE))
                .withInternal(OrderBuilder
                        .withId(ORDER_ID)
                        .withExternalId(EXTERNAL_ID)
                        .withDescription("first order with duplicate external id, but different system")
                        .withCategory("SALES_ORDER")
                        .withOrderDate("2018-04-06T11:57:11")
                        .withRequestedCompletionDate("2018-04-06T11:57:11")
                        .withRequesterCallback("https://som.idahs.com/alt/nossis/notification")
                        .withState(State.COMPLETED)
                        .addRelatedParties(RelatedPartyBuilder
                                .withId("65946422")
                                .withRole("customer")
                                .addSimpleAttribute("type", "SOHO")
                                .addSimpleAttribute("first-name", "FTTH")
                                .addSimpleAttribute("last-name", "FT2")
                                .build())
                        .addOrderProcesse(OrderProcessBuilder
                                .withId("88D9360C960E42C7824F687062F23AA7")
                                .withAction("add")
                                .withName("CFS.3PGPON.BUNDLE")
                                .withServiceId("0780157053910_3GPONBundle")
                                .withCategory("CFS")
                                .withDescription("3P GPON Bundle")
                                .withState(State.COMPLETED)
                                .startedMinutesBeforeNow(27)
                                .durationInMinutes(13)
                                .addCfs(OrderProcessBuilder
                                        .withId("BE76A40030CC4FF4B3AC81E482E518BC")
                                        .withAction("add")
                                        .withName("CFS.HSI")
                                        .withServiceId("0780157053910_Data")
                                        .withCategory("CFS")
                                        .withDescription("HSI")
                                        .withState(State.ACKNOWLEDGED)
                                        .addRelationship("88D9360C960E42C7824F687062F23AA7", "bundled")
                                        .buildCfs()
                                ).buildCfsProcess()
                        )
                        .addOrderProcesse(OrderProcessBuilder
                                .withId("FA09351BF38D4AD982B3C5736BE525C9")
                                .withAction("add")
                                .withName("CFS.GPONACCESS")
                                .withServiceId("0780157053910_FiberGateway")
                                .withCategory("CFS")
                                .withDescription("GPON Access")
                                .withState(State.ACKNOWLEDGED)
                                .startedMinutesBeforeNow(27)
                                .durationInMinutes(13)
                                .addCfs(OrderProcessBuilder
                                        .withId("BE76A40030CC4FF4B3AC81E482E518BC")
                                        .withAction("add")
                                        .withName("CFS.HSI")
                                        .withServiceId("0780157053911_Data")
                                        .withCategory("CFS")
                                        .withDescription("HSI")
                                        .withState(State.ACKNOWLEDGED)
                                        .addRelationship("88D9360C960E42C7824F687062F23AA7", "bundled")
                                        .buildCfs()
                                ).buildCfsProcess()
                        )
                        .build()
                )
                .withSyncResponse("{ \"externalId\" : \"4abdbc19-ba61-4032-989e-82301b76c0a3\",    \"description\" : \"description\",    \"category\" : \"SALES_ORDER\",    \"orderDate\" : \"2018-02-02T13:37:16\",    \"requestedCompletionDate\" : \"2018-02-03T13:37:16\",    \"requesterCallback\" : \"http://*************:60048/callback\",    \"orderItem\" : [      {        \"id\" : \"cfsIm\",        \"action\" : \"soapivalidOrder\",        \"serviceSpecification\" : {          \"id\" : \"CFS.IM\"        },        \"service\" : {          \"id\" : \"123456\",          \"category\" : \"CFS\",          \"serviceCharacteristic\" : [            {              \"name\" : \"dstPort\",              \"value\" : \"10000\"            },            {              \"name\" : \"botnetReal\",              \"value\" : \"true\"            },            {              \"name\" : \"dstIpAddress\",              \"value\" : \"***************\"            },            {              \"name\" : \"frequency\",              \"value\" : \"49.99\"            }          ],          \"component\" : [            {              \"name\" : \"componentType1\",              \"component\" : {                \"type\" : \"componentType1\",                \"component\" : [                  {                    \"name\" : \"componentType2\",                    \"component\" : {                      \"type\" : \"componentType2\",                      \"property\" : [                        {                          \"name\" : \"host\",                          \"value\" : \"***************\"                        },                        {                          \"name\" : \"port\",                          \"value\" : \"8500\"                        }                      ]                    }                  }                ]              }            }          ]        },        \"state\" : \"Acknowledged\"      },      {        \"id\" : \"rfsFlowT\",        \"action\" : \"soapivalidOrder\",        \"requestedCompletionDate\" : \"2018-02-02T13:37:18\",        \"orderItemRelationship\" : [          {            \"id\" : \"cfsIm\",            \"type\" : \"bundled\"          }        ],        \"serviceSpecification\" : {          \"id\" : \"RFS.FLOWT\"        },        \"service\" : {          \"id\" : \"1234567\",          \"category\" : \"RFS\",          \"serviceCharacteristic\" : [            {              \"name\" : \"dstIpAddress\",              \"value\" : \"***************\"            },            {              \"name\" : \"srcIpAddress\",              \"value\" : \"***************\"            }          ],          \"resource\" : [            {              \"name\" : \"LR.FLOWT\",              \"resource\" : {                \"id\" : \"lrFlowT\",                \"type\" : \"Resource\",                \"property\" : [                  {                    \"name\" : \"dstIpAddress\",                    \"value\" : \"***************\"                  }                ]              }            }          ]        },        \"state\" : \"Acknowledged\"      },      {        \"id\" : \"rfsSnort\",        \"action\" : \"soapivalidOrder\",        \"requestedCompletionDate\" : \"2018-02-02T13:37:18\",        \"orderItemRelationship\" : [          {            \"id\" : \"cfsIm\",            \"type\" : \"bundled\"          }        ],        \"serviceSpecification\" : {          \"id\" : \"RFS.SNORT\"        },        \"service\" : {          \"id\" : \"565\",          \"category\" : \"RFS\",          \"serviceCharacteristic\" : [            {              \"name\" : \"startInspection\",              \"value\" : \"2018-07-12T10:21:10.979Z\"            }          ]        },        \"state\" : \"Acknowledged\"      }    ],    \"id\" : \"70a74483-623e-4ed3-a553-1140ae0afdac2\",    \"state\" : \"Acknowledged\",    \"href\" : \"http://**********:9004/ptinovacao/serviceOrderingManagement/serviceOrder/70a74483-623e-4ed3-a553-1140ae0afdac2\"  }")
                .withAsyncResponse("{ \"eventId\" : \"e418cfc2-4e7d-47ed-93c0-39ec30f313d0-2\",    \"eventTime\" : \"2018-07-12T10:21:21.627+00:00\",    \"eventType\" : \"ServiceOrderStateChangeNotification\",    \"event\" : {      \"serviceOrder\" : {        \"externalId\" : \"4abdbc19-ba61-4032-989e-82301b76c0a3\",        \"description\" : \"description\",        \"category\" : \"SALES_ORDER\",        \"orderDate\" : \"2018-02-02T13:37:16\",        \"requestedCompletionDate\" : \"2018-02-03T13:37:16\",        \"requesterCallback\" : \"http://*************:60048/callback\",        \"orderItem\" : [          {            \"id\" : \"cfsIm\",            \"action\" : \"soapivalidOrder\",            \"serviceSpecification\" : {              \"id\" : \"CFS.IM\"            },            \"service\" : {              \"id\" : \"123456\",              \"category\" : \"CFS\",              \"serviceCharacteristic\" : [                {                  \"name\" : \"dstPort\",                  \"value\" : \"10000\"                },                {                  \"name\" : \"botnetReal\",                  \"value\" : \"true\"                },                {                  \"name\" : \"dstIpAddress\",                  \"value\" : \"***************\"                },                {                  \"name\" : \"frequency\",                  \"value\" : \"49.99\"                }              ],              \"component\" : [                {                  \"name\" : \"componentType1\",                  \"component\" : {                    \"type\" : \"componentType1\",                    \"component\" : [                      {                        \"name\" : \"componentType2\",                        \"component\" : {                          \"type\" : \"componentType2\",                          \"property\" : [                            {                              \"name\" : \"host\",                              \"value\" : \"***************\"                            },                            {                              \"name\" : \"port\",                              \"value\" : \"8500\"                            }                          ]                        }                      }                    ]                  }                }              ]            },            \"state\" : \"Failed\",            \"statusMessage\" : {              \"code\" : \"CL_02001-NOK\",              \"message\" : \"Operation not found\"            }          }        ],        \"id\" : \"70a74483-623e-4ed3-a553-1140ae0afdac2\",        \"state\" : \"Failed\",        \"href\" : \"http://**********:9004/ptinovacao/serviceOrderingManagement/serviceOrder/70a74483-623e-4ed3-a553-1140ae0afdac2\"      }    }  }")
                .build();
    }

    public OrderFollowUp orderFollowUpEqExtId2() {
        final int STARTED_MINUTES_BEFORE = 88; // 1 hour ago 28 minutes ago
        final String ORDER_ID = "bb9c3f3c-a1a7-4435-a93e-35c30a67dce6";
        final String EXTERNAL_ID = "2b34334d-cd7b-40c5-9fcc-666690016911";
        final String START_DATE = timeToDate(STARTED_MINUTES_BEFORE);
        return OrderFollowUpBuilder
                .withOrderId(ORDER_ID)
                .withExternalId(EXTERNAL_ID)
                .withSystem("user_TESTING_EXAMPLE_2")
                .withOrigin("**********")
                .withRequesterCallback(sweTestCallbackUrl)
                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                .durationInMinutes(11)
                .isPayloadReceivedValid(true)
                .withReceived(mockRequestValidationText(ORDER_ID, EXTERNAL_ID, START_DATE))
                .withInternal(OrderBuilder
                        .withId(ORDER_ID)
                        .withExternalId(EXTERNAL_ID)
                        .withDescription("second order with duplicate external id, but different system")
                        .withState(State.COMPLETED)
                        .withCategory("SALES_ORDER")
                        .addRelatedParties(RelatedPartyBuilder
                                .withId("65946422")
                                .withRole("customer")
                                .addSimpleAttribute("type", "SOHO")
                                .addSimpleAttribute("first-name", "FTTH")
                                .addSimpleAttribute("last-name", "FT2")
                                .build())
                        .build()
                )
                .withSyncResponse("{ \"externalId\" : \"4abdbc19-ba61-4032-989e-82301b76c0a3\",    \"description\" : \"description\",    \"category\" : \"SALES_ORDER\",    \"orderDate\" : \"2018-02-02T13:37:16\",    \"requestedCompletionDate\" : \"2018-02-03T13:37:16\",    \"requesterCallback\" : \"http://*************:60048/callback\",    \"orderItem\" : [      {        \"id\" : \"cfsIm\",        \"action\" : \"soapivalidOrder\",        \"serviceSpecification\" : {          \"id\" : \"CFS.IM\"        },        \"service\" : {          \"id\" : \"123456\",          \"category\" : \"CFS\",          \"serviceCharacteristic\" : [            {              \"name\" : \"dstPort\",              \"value\" : \"10000\"            },            {              \"name\" : \"botnetReal\",              \"value\" : \"true\"            },            {              \"name\" : \"dstIpAddress\",              \"value\" : \"***************\"            },            {              \"name\" : \"frequency\",              \"value\" : \"49.99\"            }          ],          \"component\" : [            {              \"name\" : \"componentType1\",              \"component\" : {                \"type\" : \"componentType1\",                \"component\" : [                  {                    \"name\" : \"componentType2\",                    \"component\" : {                      \"type\" : \"componentType2\",                      \"property\" : [                        {                          \"name\" : \"host\",                          \"value\" : \"***************\"                        },                        {                          \"name\" : \"port\",                          \"value\" : \"8500\"                        }                      ]                    }                  }                ]              }            }          ]        },        \"state\" : \"Acknowledged\"      },      {        \"id\" : \"rfsFlowT\",        \"action\" : \"soapivalidOrder\",        \"requestedCompletionDate\" : \"2018-02-02T13:37:18\",        \"orderItemRelationship\" : [          {            \"id\" : \"cfsIm\",            \"type\" : \"bundled\"          }        ],        \"serviceSpecification\" : {          \"id\" : \"RFS.FLOWT\"        },        \"service\" : {          \"id\" : \"1234567\",          \"category\" : \"RFS\",          \"serviceCharacteristic\" : [            {              \"name\" : \"dstIpAddress\",              \"value\" : \"***************\"            },            {              \"name\" : \"srcIpAddress\",              \"value\" : \"***************\"            }          ],          \"resource\" : [            {              \"name\" : \"LR.FLOWT\",              \"resource\" : {                \"id\" : \"lrFlowT\",                \"type\" : \"Resource\",                \"property\" : [                  {                    \"name\" : \"dstIpAddress\",                    \"value\" : \"***************\"                  }                ]              }            }          ]        },        \"state\" : \"Acknowledged\"      },      {        \"id\" : \"rfsSnort\",        \"action\" : \"soapivalidOrder\",        \"requestedCompletionDate\" : \"2018-02-02T13:37:18\",        \"orderItemRelationship\" : [          {            \"id\" : \"cfsIm\",            \"type\" : \"bundled\"          }        ],        \"serviceSpecification\" : {          \"id\" : \"RFS.SNORT\"        },        \"service\" : {          \"id\" : \"565\",          \"category\" : \"RFS\",          \"serviceCharacteristic\" : [            {              \"name\" : \"startInspection\",              \"value\" : \"2018-07-12T10:21:10.979Z\"            }          ]        },        \"state\" : \"Acknowledged\"      }    ],    \"id\" : \"70a74483-623e-4ed3-a553-1140ae0afdac2\",    \"state\" : \"Acknowledged\",    \"href\" : \"http://**********:9004/ptinovacao/serviceOrderingManagement/serviceOrder/70a74483-623e-4ed3-a553-1140ae0afdac2\"  }")
                .withAsyncResponse("{ \"eventId\" : \"e418cfc2-4e7d-47ed-93c0-39ec30f313d0-2\",    \"eventTime\" : \"2018-07-12T10:21:21.627+00:00\",    \"eventType\" : \"ServiceOrderStateChangeNotification\",    \"event\" : {      \"serviceOrder\" : {        \"externalId\" : \"4abdbc19-ba61-4032-989e-82301b76c0a3\",        \"description\" : \"description\",        \"category\" : \"SALES_ORDER\",        \"orderDate\" : \"2018-02-02T13:37:16\",        \"requestedCompletionDate\" : \"2018-02-03T13:37:16\",        \"requesterCallback\" : \"http://*************:60048/callback\",        \"orderItem\" : [          {            \"id\" : \"cfsIm\",            \"action\" : \"soapivalidOrder\",            \"serviceSpecification\" : {              \"id\" : \"CFS.IM\"            },            \"service\" : {              \"id\" : \"123456\",              \"category\" : \"CFS\",              \"serviceCharacteristic\" : [                {                  \"name\" : \"dstPort\",                  \"value\" : \"10000\"                },                {                  \"name\" : \"botnetReal\",                  \"value\" : \"true\"                },                {                  \"name\" : \"dstIpAddress\",                  \"value\" : \"***************\"                },                {                  \"name\" : \"frequency\",                  \"value\" : \"49.99\"                }              ],              \"component\" : [                {                  \"name\" : \"componentType1\",                  \"component\" : {                    \"type\" : \"componentType1\",                    \"component\" : [                      {                        \"name\" : \"componentType2\",                        \"component\" : {                          \"type\" : \"componentType2\",                          \"property\" : [                            {                              \"name\" : \"host\",                              \"value\" : \"***************\"                            },                            {                              \"name\" : \"port\",                              \"value\" : \"8500\"                            }                          ]                        }                      }                    ]                  }                }              ]            },            \"state\" : \"Failed\",            \"statusMessage\" : {              \"code\" : \"CL_02001-NOK\",              \"message\" : \"Operation not found\"            }          }        ],        \"id\" : \"70a74483-623e-4ed3-a553-1140ae0afdac2\",        \"state\" : \"Failed\",        \"href\" : \"http://**********:9004/ptinovacao/serviceOrderingManagement/serviceOrder/70a74483-623e-4ed3-a553-1140ae0afdac2\"      }    }  }")
                .build();
    }

    public OrderFollowUp oldOrderFollow() {
        final int STARTED_MINUTES_BEFORE = 25947; // 18 days ago 27 minutes ago
        final String ORDER_ID = "28c00eb9-f40a-4dc2-ad5e-1238ef3e6596";
        final String EXTERNAL_ID = "1ef1f05f-9977-4063-b7d0-9462aecc7f4";
        final String START_DATE = timeToDate(STARTED_MINUTES_BEFORE);
        return OrderFollowUpBuilder
                .withOrderId(ORDER_ID)
                .withExternalId(EXTERNAL_ID)
                .withSystem("user_TESTING")
                .withOrigin("**********")
                .withOperationsCatalogVersion("SWE_SELFNET_TESTING")
                .withServiceIds("400705")
                .withRequesterCallback(sweTestCallbackUrl)
                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                .durationInMinutes(STARTED_MINUTES_BEFORE - 13) // 2 days ago 14 minutes ago
                .isPayloadReceivedValid(true)
                .withReceived(mockRequestValidationText(ORDER_ID, EXTERNAL_ID, START_DATE))
                .withInternal(OrderBuilder
                        .withId(ORDER_ID)
                        .withExternalId(EXTERNAL_ID)
                        .withDescription("description")
                        .withCategory("SALES_ORDER")
                        .withOrderDate("2018-04-06T11:57:11")
                        .withRequestedCompletionDate("2018-04-06T11:57:11")
                        .withRequesterCallback(sweTestCallbackUrl)
                        .withState(State.ACKNOWLEDGED)
                        .addRelatedParties()
                        .build()
                )
                .withSyncResponse("{}")
                .build();
    }

    public OrderFollowUp oldOrderRejected() {
        final int STARTED_MINUTES_BEFORE = 24615; // 17 days ago2 hour ago 15 minutes ago
        final String ORDER_ID = "6bef1638-c5e4-4fee-8959-017469534cd9";
        final String EXTERNAL_ID = "e6e41eb0-ae58-43d8-bbab-6357557c0bf4";
        final String START_DATE = timeToDate(STARTED_MINUTES_BEFORE);
        return OrderFollowUpBuilder
                .withOrderId(ORDER_ID)
                .withExternalId(EXTERNAL_ID)
                .withSystem("user_no_category")
                .withOrigin("***********0")
                .withOperationsCatalogVersion("SWE_SELFNET_TESTING")
                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                .withReceived(mockRequestValidationText(ORDER_ID, EXTERNAL_ID, START_DATE))
                .withSyncResponse("{    \"code\" : \"CL_01000\",    \"description\" : \"Order has invalid or missing parameters (see details)\",    \"details\" : [      {        \"code\" : \"CL_01002\",        \"message\" : \"Invalid or missing mandatory parameter\",        \"description\" : \"Order Category is missing\"      },      {        \"code\" : \"CL_01002\",        \"message\" : \"Invalid or missing mandatory parameter\",        \"description\" : \"Requester Callback is missing\"      },      {        \"code\" : \"CL_01002\",        \"message\" : \"Invalid or missing mandatory parameter\",        \"description\" : \"Order OrderItem is empty\"      }    ],    \"message\" : \"Invalid order structure\"  }")
                .isPayloadReceivedValid(true)
                .withInternal(OrderBuilder
                        .withId(ORDER_ID)
                        .withExternalId(EXTERNAL_ID)
                        .withState(State.REJECTED)
                        .addRelatedParties()
                        .build()
                )
                .build();
    }

    public OrderFollowUp oldFailedOrder() {
        final int STARTED_MINUTES_BEFORE = 28909; // 20 days ago 1 hour ago 40 minutes ago 4 seconds ag
        final String ORDER_ID = "55b10a62-27e6-48a1-95ff-50b59f1d1cfa";
        final String EXTERNAL_ID = "2ecb684f-32ce-408b-8a7c-172f4eca2eec";
        final String START_DATE = timeToDate(STARTED_MINUTES_BEFORE);
        return OrderFollowUpBuilder
                .withOrderId(ORDER_ID)
                .withExternalId(EXTERNAL_ID)
                .withSystem("user_no_version")
                .withOrigin("***********")
                .withOperationsCatalogVersion("SWE_SELFNET_PRODUCTION")
                .withRequesterCallback(sweTestCallbackUrl)
                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                .durationInMinutes(STARTED_MINUTES_BEFORE - 1) // 2 days ago 14 minutes ago
                .isPayloadReceivedValid(true)
                .withReceived(mockRequestValidationText(ORDER_ID, EXTERNAL_ID, START_DATE))
                .withInternal(OrderBuilder
                        .withId(ORDER_ID)
                        .withExternalId(EXTERNAL_ID)
                        .withDescription("description")
                        .withCategory("DUMMY")
                        .withOrderDate("2018-02-02T13:37:16")
                        .withRequestedCompletionDate("2018-02-03T13:37:16")
                        .withRequesterCallback(sweTestCallbackUrl)
                        .withState(State.FAILED)
                        .addRelatedParties()
                        .build()
                )
                .withSyncResponse("{}")
                .build();
    }

    public OrderFollowUp completedOmvnoOrder() {
        final int STARTED_MINUTES_BEFORE = 27430; // 19 days ago 1 hour ago 10 minutes ago 4 seconds ag
        final String ORDER_ID = "fcb8708c-7773-4bfd-b267-5f72894100a3";
        final String EXTERNAL_ID = "e89b346c-0bef-4749-8bdc-c20af4dfc8c2";
        final String START_DATE = timeToDate(STARTED_MINUTES_BEFORE);
        return OrderFollowUpBuilder
                .withOrderId(ORDER_ID)
                .withExternalId(EXTERNAL_ID)
                .withSystem("ommvno")
                .withOrigin("10.112.208.216")
                .withOperationsCatalogVersion("MVNO_OPERATIONS")
                .withRequesterCallback(sweTestCallbackUrl)
                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                .durationInMinutes(5760)
                .isPayloadReceivedValid(true)
                .withReceived(mockRequestValidationText(ORDER_ID, EXTERNAL_ID, START_DATE))
                .withInternal(OrderBuilder
                        .withId(ORDER_ID)
                        .withExternalId(EXTERNAL_ID)
                        .withDescription("Mobile Service Order")
                        .withCategory("MVNO")
                        .withOrderDate("2018-08-07T13:23:18.370Z")
                        .withRequesterCallback(sweTestCallbackUrl)
                        .withState(State.COMPLETED)
                        .addRelatedParties()
                        .addOrderProcesse(OrderProcessBuilder
                                .withId("ed45c559-fd68-4092-9da9-7393dc542552")
                                .withAction("add")
                                .withName("CFS.VOICE")
                                .withServiceId("MOBILE-VOICE-002")
                                .withCategory("CFS")
                                .withDescription("Mobile Voice Service")
                                .withState(State.COMPLETED)
                                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                                .withWorkflowName("workflowWithAllTypeOfNodes")
                                .addSimpleAttribute("profile", "melhor ainda")
                                .addSimpleAttribute("imsi", "123456789012346")
                                .addSimpleAttribute("msisdn", "1234567891")
                                .buildCfsProcess())
                        .build()
                )
                .withSyncResponse("{\"externalId\":\"e89b346c-0bef-4749-8bdc-c20af4dfc8c2\",\"description\":\"Mobile Service Order\",\"category\":\"MVNO\",\"requesterCallback\":\"http://**************:8998/client/listener\",\"orderItem\":[{\"id\":\"0b00ac4a-aa42-456d-9ddd-77553d2a400d\",\"action\": \"add\",        \"serviceSpecification\": {          \"id\": \"CFS.VOICE\"        },        \"service\": {\"category\": \"CFS\",\"id\": \"MOBILE-VOICE-001\",\"description\": \"Mobile Voice Service\",\"serviceCharacteristic\": [            {              \"name\": \"msisdn\",              \"value\": \"1234567890\"            },            {              \"name\": \"imsi\",              \"value\": \"123456789012345\"            },            {              \"name\": \"profile\",              \"value\": \"mt bom\"            }          ]        },        \"state\": \"Acknowledged\"      },      {        \"id\": \"ed45c559-fd68-4092-9da9-7393dc542552\",        \"action\": \"add\",        \"serviceSpecification\": {          \"id\": \"CFS.VOICE\"        },        \"service\": {          \"category\": \"CFS\",          \"id\": \"MOBILE-VOICE-002\",          \"description\": \"Mobile Voice Service\",          \"serviceCharacteristic\": [            {              \"name\": \"msisdn\",              \"value\": \"1234567891\"            },            {              \"name\": \"imsi\",              \"value\": \"123456789012346\"            },            {              \"name\": \"profile\",              \"value\": \"melhor ainda\"            }          ]        },        \"state\": \"Acknowledged\"      }    ],    \"id\": \"fcb8708c-7773-4bfd-b267-5f72894100a3\",    \"state\": \"Acknowledged\",    \"href\": \"http://ommvnoaus-dev-evl-swe.c.ptin.corppt.com:9004/ptinovacao/serviceOrderingManagement/serviceOrder/fcb8708c-7773-4bfd-b267-5f72894100a3\",    \"orderDate\": \"2018-08-07T13:23:18.370Z\"  }")
                .build();
    }

    public static OrderFollowUp otherOldPartialMvnoOrder() {
        final int STARTED_MINUTES_BEFORE = 33147; // 23 days ago 27 minutes ago
        final String ORDER_ID = "e6ca9ae9-08af-4cdf-9e2a-65c9c3cbe916";
        final String EXTERNAL_ID = "bcc370a5-ea7a-45a9-95f4-64d7429e15e5";
        final String START_DATE = timeToDate(STARTED_MINUTES_BEFORE);
        final String REQUESTER_CALLBACK = "https://som.idahs.com/alt/nossis/notification";
        return OrderFollowUpBuilder
                .withOrderId(ORDER_ID)
                .withExternalId(EXTERNAL_ID)
                .withSystem("user_TESTING")
                .withOrigin("**********")
                .withOperationsCatalogVersion("SWE_SELFNET_TESTING")
                .withRequesterCallback(REQUESTER_CALLBACK)
                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                .durationInMinutes(STARTED_MINUTES_BEFORE - 1)
                .isPayloadReceivedValid(true)
                .withReceived(mockRequestValidationText(ORDER_ID, EXTERNAL_ID, START_DATE))
                .withInternal(OrderBuilder
                        .withId(ORDER_ID)
                        .withExternalId(EXTERNAL_ID)
                        .withDescription("description")
                        .withCategory("SALES_ORDER")
                        .withOrderDate("2018-04-06T11:57:11")
                        .withRequestedCompletionDate("2018-04-06T11:57:1")
                        .withRequesterCallback(REQUESTER_CALLBACK)
                        .withState(State.COMPLETED)
                        .build()
                )
                .withSyncResponse("{}")
                .build();
    }


    public OrderFollowUp completedMVNODelete() {
        final int STARTED_MINUTES_BEFORE = 13 * 60;
        final String ORDER_ID = "9f0c8539-cb9e-4be3-8b86-79cc6a3d3ae84";
        final String EXTERNAL_ID = "bdbe3762-8a29-4731-984e-1a962a27819f"; // DELETE_DV_IST_00476

        return OrderFollowUpBuilder
                .withOrderId(ORDER_ID)
                .withExternalId(EXTERNAL_ID)
                .withNotificationId("bf3f7fe8-75cf-45a2-a42b-42d4823645f0")
                .withOrigin("***********")
                .withSystem("ommvno")
                .withOperationsCatalogVersion("MVNO_OPERATIONS_V2.00")
                .withRequesterCallback(sweTestCallbackUrl)
                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                .durationInMinutes(STARTED_MINUTES_BEFORE - 1)
                .isPayloadReceivedValid(true)
                .withInternal(OrderBuilder
                        .withId(ORDER_ID)
                        .withExternalId(EXTERNAL_ID)
                        .withDescription("MVNO Delete")
                        .withCategory("MVNO")
                        .withOrderDate("2019-07-09T16:30:31.401+01:00")
                        .withRequesterCallback(sweTestCallbackUrl)
                        .withState(State.COMPLETED)
                        .addOrderProcesse(OrderProcessBuilder
                                .withId("oiVoice-BUNDLE")
                                .withAction("delete")
                                .withName("CFS.BUNDLE")
                                .withServiceId("sBundle")
                                .withCategory("CFS")
                                .withState(State.COMPLETED)
                                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                                .withWorkflowName("CFS.CFS.BUNDLE.delete")
                                .isSimulated(false)
                                .isVirtual(true)
                                .withVersion("MVNO_OPERATIONS_V2.00")
                                .addStatusMessage("OK", "")
                                .addCfs(OrderProcessBuilder
                                        .withId("oiSms")
                                        .withAction("delete")
                                        .withName("CFS.SMS")
                                        .withServiceId("sSms_00476")
                                        .withCategory("CFS")
                                        .withState(State.COMPLETED)
                                        .addStatusMessage("OK", "")
                                        .isVirtual(false)
                                        .isSimulated(false)
                                        .buildCfs())
                                .addCfs(OrderProcessBuilder
                                        .withId("oiData")
                                        .withAction("delete")
                                        .withName("CFS.DATA")
                                        .withServiceId("sData_00476")
                                        .withCategory("CFS")
                                        .withState(State.COMPLETED)
                                        .addStatusMessage("OK", "")
                                        .isVirtual(false)
                                        .isSimulated(false)
                                        .buildCfs())
                                .addCfs(OrderProcessBuilder
                                        .withId("oiVoice")
                                        .withAction("delete")
                                        .withName("CFS.VOICE")
                                        .withServiceId("sVoice_00476")
                                        .withCategory("CFS")
                                        .withState(State.COMPLETED)
                                        .addStatusMessage("OK", "")
                                        .isVirtual(false)
                                        .isSimulated(false)
                                        .buildCfs())
                                .buildCfsProcess())
                        .build()
                )
                .withReceived("{\n  \"category\" : \"MVNO\",\n  \"externalId\" : \"DELETE_DV_IST_00476\",\n  \"description\" : \"MVNO Delete\",\n  \"orderDate\" : \"2018-08-10T14:05:52.842Z\",\n  \"orderItem\" : [ {\n    \"action\" : \"delete\",\n    \"id\" : \"oiSms\",\n    \"service\" : {\n      \"category\" : \"CFS\",\n      \"component\" : [ ],\n      \"id\" : \"sSms_00476\",\n      \"serviceCharacteristic\" : [ ]\n    },\n    \"serviceSpecification\" : {\n      \"id\" : \"CFS.SMS\"\n    }\n  }, {\n    \"action\" : \"delete\",\n    \"id\" : \"oiData\",\n    \"service\" : {\n      \"category\" : \"CFS\",\n      \"component\" : [ ],\n      \"id\" : \"sData_00476\",\n      \"serviceCharacteristic\" : [ ]\n    },\n    \"serviceSpecification\" : {\n      \"id\" : \"CFS.DATA\"\n    }\n  }, {\n    \"action\" : \"delete\",\n    \"id\" : \"oiVoice\",\n    \"service\" : {\n      \"category\" : \"CFS\",\n      \"component\" : [ ],\n      \"id\" : \"sVoice_00476\",\n      \"serviceCharacteristic\" : [ ]\n    },\n    \"serviceSpecification\" : {\n      \"id\" : \"CFS.VOICE\"\n    }\n  } ],\n  \"requesterCallback\" : \"http://ommvnoaus-ist-evl-swe.c.ptin.corppt.com:8585/requesterCallback\",\n  \"id\" : \"2bf12262-4c95-4d30-bd68-84346b88a5b14\"\n}")
                .withSyncResponse("{\"payload\":{\"category\":\"MVNO\",\"externalId\":\"DELETE_DV_IST_00476\",\"description\":\"MVNO Delete\",\"orderItem\":[{\"state\":\"Acknowledged\",\"serviceSpecification\":{\"id\":\"CFS.SMS\"},\"service\":{\"category\":\"CFS\",\"component\":[],\"id\":\"sSms_00476\",\"serviceCharacteristic\":[]},\"id\":\"oiSms\",\"action\":\"delete\"},{\"state\":\"Acknowledged\",\"serviceSpecification\":{\"id\":\"CFS.DATA\"},\"service\":{\"category\":\"CFS\",\"component\":[],\"id\":\"sData_00476\",\"serviceCharacteristic\":[]},\"id\":\"oiData\",\"action\":\"delete\"},{\"state\":\"Acknowledged\",\"serviceSpecification\":{\"id\":\"CFS.VOICE\"},\"service\":{\"category\":\"CFS\",\"component\":[],\"id\":\"sVoice_00476\",\"serviceCharacteristic\":[]},\"id\":\"oiVoice\",\"action\":\"delete\"}],\"requesterCallback\":\"http://ommvnoaus-ist-evl-swe.c.ptin.corppt.com:8585/requesterCallback\",\"id\":\"2bf12262-4c95-4d30-bd68-84346b88a5b14\",\"href\":\"http://ommvnoaus-ist-evl-swe.c.ptin.corppt.com:9004/ptinovacao/serviceOrderingManagement/serviceOrder/2bf12262-4c95-4d30-bd68-84346b88a5b14\",\"orderDate\":\"2019-07-09T16:30:31.401+01:00\",\"state\":\"Acknowledged\"}}")
                .withAsyncResponse("{\"payload\":{\"eventId\":\"d10cb5ca-4f7c-49a9-b7a6-3d0fa0f6f500-5\",\"eventTime\":\"2019-07-09T15:30:40.574Z\",\"eventType\":\"ServiceOrderStateChangeNotification\",\"event\":{\"serviceOrder\":{\"orderItem\":[{\"state\":\"Completed\",\"serviceSpecification\":{\"id\":\"CFS.SMS\"},\"service\":{\"id\":\"sSms_00476\",\"resource\":[],\"category\":\"CFS\",\"place\":[],\"serviceCharacteristic\":[],\"component\":[]},\"id\":\"oiSms\",\"statusMessage\":{\"code\":\"OK\",\"message\":\"\",\"description\":\"\",\"details\":[]},\"action\":\"delete\"},{\"state\":\"Completed\",\"serviceSpecification\":{\"id\":\"CFS.DATA\"},\"service\":{\"id\":\"sData_00476\",\"resource\":[],\"category\":\"CFS\",\"place\":[],\"serviceCharacteristic\":[],\"component\":[]},\"id\":\"oiData\",\"statusMessage\":{\"code\":\"OK\",\"message\":\"\",\"description\":\"\",\"details\":[]},\"action\":\"delete\"},{\"state\":\"Completed\",\"serviceSpecification\":{\"id\":\"CFS.VOICE\"},\"service\":{\"id\":\"sVoice_00476\",\"resource\":[],\"category\":\"CFS\",\"place\":[],\"serviceCharacteristic\":[],\"component\":[]},\"id\":\"oiVoice\",\"statusMessage\":{\"code\":\"OK\",\"message\":\"\",\"description\":\"\",\"details\":[]},\"action\":\"delete\"}],\"externalId\":\"DELETE_DV_IST_00476\",\"orderDate\":\"2019-07-09T16:30:31.401+01:00\",\"state\":\"Completed\",\"description\":\"MVNO Delete\",\"requesterCallback\":\"http://ommvnoaus-ist-evl-swe.c.ptin.corppt.com:8585/requesterCallback\",\"id\":\"2bf12262-4c95-4d30-bd68-84346b88a5b14\",\"category\":\"MVNO\",\"relatedParty\":[]}}},\"attempts\":[{\"time\":1562686240627,\"state\":\"OK\",\"statusCode\":200,\"message\":\"OK\"}]}")
                .build();
    }


    public OrderFollowUp rejectedMVNODelete() {
        final int STARTED_MINUTES_BEFORE = 13 * 60 + 5;
        final String ORDER_ID = "9f0c8539-cb9e-4be3-8b86-79cc6a3d3ae86";
        final String EXTERNAL_ID = "bdbe3762-8a29-4731-984e-1a962a27819f";
        final String START_DATE = timeToDate(STARTED_MINUTES_BEFORE);

        return OrderFollowUpBuilder
                .withOrderId(ORDER_ID)
                .withExternalId(EXTERNAL_ID)
                .withOrigin("***********")
                .withSystem("ommvno")
                .withOperationsCatalogVersion("MVNO_OPERATIONS_V2.00")
                .withRequesterCallback(sweTestCallbackUrl)
                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                .durationInMinutes(STARTED_MINUTES_BEFORE - 1)
                .isPayloadReceivedValid(true)
                .withReceived(mockRequestValidationText(ORDER_ID, EXTERNAL_ID, START_DATE))
                .withSyncResponse("{    \"code\" : \"AP_00001\",    \"description\" : \"Internal error occurred (see details)\",    \"details\" : [      {        \"code\" : \"AP_02003\",        \"message\" : \"Operation is in blocked state\",        \"description\" : \"The operation 'add' is in an invalid state: 'BLOCKED'.\"      }    ],    \"message\" : \"Internal server error\"  }")
                .withInternal(OrderBuilder
                        .withId(ORDER_ID)
                        .withExternalId(EXTERNAL_ID)
                        .withState(State.REJECTED)
                        .build()
                )
                .build();
    }


    public OrderFollowUp validatingDiscoveryOrder() {
        final int STARTED_MINUTES_BEFORE = 64; // 1 hour ago 4 minutes ago
        final String ORDER_ID = "efac85e8-7b7b-4e0d-a3f7-5ecc2085077a1";
        final String EXTERNAL_ID = "DISCOVERY 9a07ba60-ac98-4d8f-abbe-f85e9f39571d";
        final String START_DATE = timeToDate(STARTED_MINUTES_BEFORE);

        return OrderFollowUpBuilder
                .withOrderId(ORDER_ID)
                .withExternalId(EXTERNAL_ID)
                .withOrigin("**********")
                .withSystem("discovery")
                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                .withOperationsCatalogVersion("MVNO_OPERATIONS_V2.00")
                .withRequesterCallback(sweTestCallbackUrl)
                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                .durationInMinutes(STARTED_MINUTES_BEFORE - 1)
                .isPayloadReceivedValid(true)
                .withReceived("{\n  \"externalId\" : \"" + EXTERNAL_ID + "\",\n  \"category\" : \"DISCOVERY.ORDER\",\n  \"orderDate\" : \"" + START_DATE + "\",\n  \"requesterCallback\" : \"" + sweTestCallbackUrl + "\",\n  \"relatedParty\" : [ ],\n  \"orderItem\" : [ {\n    \"id\" : \"discovery\",\n    \"action\" : \"add\",\n    \"serviceSpecification\" : {\n      \"id\" : \"RFS.DISCOVERY\"\n    },\n    \"service\" : {\n      \"category\" : \"RFS\",\n      \"serviceCharacteristic\" : [ {\n        \"name\" : \"omc\",\n        \"value\" : \"HUAWEI\"\n      }, {\n        \"name\" : \"entityType\",\n        \"value\" : \"Equipment\"\n      } ],\n      \"component\" : [ {\n        \"name\" : \"searchCriteria\",\n        \"component\" : {\n          \"property\" : [ {\n            \"name\" : \"criteria\",\n            \"value\" : \"oltName\"\n          }, {\n            \"name\" : \"param\",\n            \"value\" : \"qualquer\"\n          } ]\n        }\n      } ]\n    }\n  } ],\n  \"id\" : \"" + ORDER_ID + "\"\n}")
                .build();
    }

    public OrderFollowUp notificationOrder(String orderId, String externalId) {
        final int STARTED_MINUTES_BEFORE = 55;
        final String START_DATE = timeToDate(STARTED_MINUTES_BEFORE);
        return OrderFollowUpBuilder
                .withOrderId(orderId)
                .withExternalId(externalId)
                .withNotificationId(UUID.randomUUID().toString())
                .withSystem("user_TESTING")
                .withOrigin("**********")
                .withOperationsCatalogVersion("SWE_SELFNET_TESTING")
                .withRequesterCallback(sweTestCallbackUrl)
                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                .durationInMinutes(5)
                .withReceived(mockRequestValidationText(orderId, externalId, START_DATE))
                .isPayloadReceivedValid(true)
                .withSyncResponse("{ \"externalId\" : \"4abdbc19-ba61-4032-989e-82301b76c0a3\", \"description\" : \"description\"," + "    \"category\" : \"DUMMY\"," + "    \"orderDate\" : \"2018-02-02T13:37:16\"," + "    \"requestedCompletionDate\" : \"2018-02-03T13:37:16\"," + "    \"requesterCallback\" : \"http://*************:60048/callback\"," + "    \"orderItem\" : [" + "      {" + "        \"id\" : \"cfsIm\"," + "        \"action\" : \"soapivalidOrder\"," + "        \"serviceSpecification\" : {" + "          \"id\" : \"CFS.IM\"" + "        }," + "        \"service\" : {" + "          \"id\" : \"123456\"," + "          \"category\" : \"CFS\"," + "          \"serviceCharacteristic\" : [" + "            {" + "              \"name\" : \"dstPort\"," + "              \"value\" : \"10000\"" + "            }," + "            {" + "              \"name\" : \"botnetReal\"," + "              \"value\" : \"true\"" + "            }," + "            {" + "              \"name\" : \"dstIpAddress\"," + "              \"value\" : \"***************\"" + "            }," + "            {" + "              \"name\" : \"frequency\"," + "              \"value\" : \"49.99\"" + "            }" + "          ]," + "          \"component\" : [" + "            {" + "              \"name\" : \"componentType1\"," + "              \"component\" : {" + "                \"type\" : \"componentType1\"," + "                \"component\" : [" + "                  {" + "                    \"name\" : \"componentType2\"," + "                    \"component\" : {" + "                      \"type\" : \"componentType2\"," + "                      \"property\" : [" + "                        {" + "                          \"name\" : \"host\"," + "                          \"value\" : \"***************\"" + "                        }," + "                        {" + "                          \"name\" : \"port\"," + "                          \"value\" : \"8500\"" + "                        }" + "                      ]" + "                    }" + "                  }" + "                ]" + "              }" + "            }" + "          ]" + "        }," + "        \"state\" : \"Acknowledged\"" + "      }," + "      {" + "        \"id\" : \"rfsFlowT\"," + "        \"action\" : \"soapivalidOrder\"," + "        \"requestedCompletionDate\" : \"2018-02-02T13:37:18\"," + "        \"orderItemRelationship\" : [" + "          {" + "            \"id\" : \"cfsIm\"," + "            \"type\" : \"bundled\"" + "          }" + "        ]," + "        \"serviceSpecification\" : {" + "          \"id\" : \"RFS.FLOWT\"" + "        }," + "        \"service\" : {" + "          \"id\" : \"1234567\"," + "          \"category\" : \"RFS\"," + "          \"serviceCharacteristic\" : [" + "            {" + "              \"name\" : \"dstIpAddress\"," + "              \"value\" : \"***************\"" + "            }," + "            {" + "              \"name\" : \"srcIpAddress\"," + "              \"value\" : \"***************\"" + "            }" + "          ]," + "          \"resource\" : [" + "            {" + "              \"name\" : \"LR.FLOWT\"," + "              \"resource\" : {" + "                \"id\" : \"lrFlowT\"," + "                \"type\" : \"Resource\"," + "                \"property\" : [" + "                  {" + "                    \"name\" : \"dstIpAddress\"," + "                    \"value\" : \"***************\"" + "                  }" + "                ]" + "              }" + "            }" + "          ]" + "        }," + "        \"state\" : \"Acknowledged\"" + "      }," + "      {" + "        \"id\" : \"rfsSnort\"," + "        \"action\" : \"soapivalidOrder\"," + "        \"requestedCompletionDate\" : \"2018-02-02T13:37:18\"," + "        \"orderItemRelationship\" : [" + "          {" + "            \"id\" : \"cfsIm\"," + "            \"type\" : \"bundled\"" + "          }" + "        ]," + "        \"serviceSpecification\" : {" + "          \"id\" : \"RFS.SNORT\"" + "        }," + "        \"service\" : {" + "          \"id\" : \"565\"," + "          \"category\" : \"RFS\"," + "          \"serviceCharacteristic\" : [" + "            {" + "              \"name\" : \"startInspection\"," + "              \"value\" : \"2018-07-12T10:21:10.979Z\"" + "            }" + "          ]" + "        }," + "        \"state\" : \"Acknowledged\"" + "      }" + "    ]," + "    \"id\" : \"70a74483-623e-4ed3-a553-1140ae0afdac2\"," + "    \"state\" : \"Acknowledged\"," + "    \"href\" : \"http://**********:9004/ptinovacao/serviceOrderingManagement/serviceOrder/70a74483-623e-4ed3-a553-1140ae0afdac2\"" + "  }")
                .withInternal(OrderBuilder
                        .withId(orderId)
                        .withExternalId(externalId)
                        .withCategory("DUMMY")
                        .withState(State.HELD)
                        .addOrderProcesse(OrderProcessBuilder
                                .withId("cfsIm")
                                .withAction("soapivalidOrder")
                                .withName("CFS.IM")
                                .withServiceId("123456")
                                .withCategory("CFS")
                                .withState(State.HELD)
                                .startedMinutesBeforeNow(STARTED_MINUTES_BEFORE)
                                .durationInMinutes(22)
                                .withWorkflowName("workflow1")
                                .withVersion("v1")
                                .addSimpleAttribute("dstPort", "10000")
                                .addSimpleAttribute("botnetReal", "true")
                                .addSimpleAttribute("dstIpAddress", "***************")
                                .addSimpleAttribute("frequency", "49.99")
                                .addRfs(OrderProcessBuilder
                                        .withId("rfsSnort")
                                        .withAction("soapivalidOrder")
                                        .withName("RFS.SNORT")
                                        .withRequestedCompletionDate("2018-02-02T13:37:18")
                                        .withServiceId("565")
                                        .withCategory("RFS")
                                        .withState(State.ACKNOWLEDGED)
                                        .addSimpleAttribute("startInspection", "2018-07-12T10:21:10.979Z")
                                        .addRelationship("cfsIm", "bundled")
                                        .buildRfs()
                                )
                                .addRfs(OrderProcessBuilder
                                        .withId("rfsFlowT")
                                        .withAction("soapivalidOrder")
                                        .withName("RFS.FLOWT")
                                        .withRequestedCompletionDate("2018-02-02T13:37:18")
                                        .withServiceId("5656")
                                        .withCategory("RFS")
                                        .withState(State.ACKNOWLEDGED)
                                        .addSimpleAttribute("dstIpAddress", "***************")
                                        .addSimpleAttribute("srcIpAddress", "***************")
                                        .addRelationship("cfsIm", "bundled")
                                        .addResource(OrderProcessBuilder
                                                .withId("lrFlowT")
                                                .withName("LR.FLOWT")
                                                .addSimpleAttribute("dstIpAddress", "***************")
                                                .buildResource()
                                        )
                                        .buildRfs()
                                )
                                .addStatusMessage("CL_02001-NOK-NOK", "Operation not found")
                                .buildCfsProcess()
                        )
                        .build()
                )
                .build();
    }

}