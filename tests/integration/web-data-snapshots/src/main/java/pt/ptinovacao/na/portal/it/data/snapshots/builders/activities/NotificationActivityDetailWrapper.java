package pt.ptinovacao.na.portal.it.data.snapshots.builders.activities;

import pt.ptinovacao.na.followup.order.model.workflow.activities.details.NotificationActivityDetail;
import pt.ptinovacao.na.swe.async.notifications.handling.model.AsyncNotification;

/**
 * This class is used to wrap notification in an activity detail
 * <p>
 * Simply subclassing the NotificationActivityDetail is not enough since the classname
 * property saved in the document will be of this class instead of the parent class
 * </p>
 * <p>
 * For that reason, this class is created to replace NotificationActivityDetail when creating NotificationActivity
 * on workflows. And on {@link pt.ptinovacao.na.portal.it.data.snapshots.builders.OrderFollowupScenarioBuilder#addWorkflow(pt.ptinovacao.na.followup.order.model.workflow.Workflow)}
 * the notification will be added to the database and this instance is replaced by the original
 * NotificationActivityDetail. This way the NotificationActivityDetail will be saved correctly.
 * </p>
 */
public class NotificationActivityDetailWrapper extends NotificationActivityDetail {
    AsyncNotification notification;
    NotificationActivityDetail detail;

    public NotificationActivityDetailWrapper() {
    }

    public AsyncNotification getNotification() {
        return notification;
    }

    public void setNotification(AsyncNotification notification) {
        this.notification = notification;
    }

    public void setDetail(NotificationActivityDetail detail) {
        this.detail = detail;
    }

    public NotificationActivityDetail getDetail() {
        return detail;
    }
}
