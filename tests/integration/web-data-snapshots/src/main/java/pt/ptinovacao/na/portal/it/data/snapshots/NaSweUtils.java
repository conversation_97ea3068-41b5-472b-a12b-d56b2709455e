package pt.ptinovacao.na.portal.it.data.snapshots;

import com.google.common.base.Strings;
import pt.ptinovacao.nacommons.model.MapModel;
import pt.ptinovacao.nacommons.model.Model;
import pt.ptinovacao.nacommons.model.StringModel;
import pt.ptinovacao.nacommons.testapi.HttpClient;
import pt.ptinovacao.nacommons.testapi.HttpRequest;
import pt.ptinovacao.nacommons.testapi.ResponseResult;

import javax.ws.rs.core.MediaType;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

import static org.junit.Assert.assertEquals;

/**
 * <AUTHOR> <<EMAIL>>, 25/06/20.
 */

public final class NaSweUtils {

    private String user;
    private String password;
    private int sweTestCallbackPort;

    private NaSweUtils() {
    }

    private NaSweUtils(String user, String password, int sweCallbackPort) {
        this.user = user;
        this.password = password;
        this.sweTestCallbackPort = sweCallbackPort;
    }

    public static NaSweUtils init(String user, String password, int sweCallbackPort){
        return new NaSweUtils(user, password, sweCallbackPort);
    }


    public Model<?> getEntity(String url, int expectedStatus) {
        return sendRequest(url, expectedStatus, "GET", null);
    }

    public Model<?> executeOperation(String url, int expectedStatus, Model<?> request) {
        return sendRequest(url, expectedStatus, "POST", request);
    }

    public Model<?> sendRequest(String url, int expectedStatus, String method, Model<?> request) {
        return sendRequest(url, expectedStatus, method, request, MediaType.APPLICATION_JSON_TYPE);
    }

    public Model<?> sendRequest(String url, int expectedStatus, String method, Model<?> request, MediaType mediaType) {
        return sendRequest(url, expectedStatus, method, request, mediaType,  0);
    }

    public Model<?> sendRequest(String url, int expectedStatus, String method, Model<?> request, MediaType mediaType, int numOfExpectedCallbacks) {
        List<ResponseResult> results = sendHttpRequest(url, expectedStatus, method, request, mediaType, numOfExpectedCallbacks);
        if (Strings.isNullOrEmpty(results.get(results.size() - 1).getBody())) {
            return new StringModel("");
        }
        return new MapModel(results.get(results.size() - 1).getBody());
    }

    private List<ResponseResult> sendHttpRequest(String url, int expectedStatus, String method, Model<?> request, MediaType mediaType, int expectedCallbacks) {
        HttpRequest httpRequest = new HttpRequest()
            .header("Authorization", "Basic " + baseAuthenticationValue())
            .soTimeout(60000)
            .connectTimeout(60000)
            .expectedStatus(expectedStatus);
        switch (method.toUpperCase()) {
            case "POST":
                httpRequest.post(url).mediaType(mediaType);
                break;
            case "GET":
                httpRequest.get(url);
                break;
            case "DELETE":
                httpRequest.delete(url).mediaType(mediaType);
                break;
        }
        if (request != null) {
            httpRequest.payload(request.toString());
        }
        List<ResponseResult> results = new ArrayList<>();

        HttpClient client = new HttpClient().callbackPort(sweTestCallbackPort)
            .expectedCallbacks(expectedCallbacks)
            .notificationsTimeout(60)
            .request(httpRequest);
        results.add(client.run());
        if (expectedCallbacks > 0){
            assertEquals(expectedCallbacks, client.getHttpCallbackResult().size());
            results.addAll(client.getHttpCallbackResult());
        }
        return results;
    }

    private String baseAuthenticationValue() {
        return Base64.getEncoder().encodeToString((user + ":" + password).getBytes());
    }
}
