{"externalId": "#externalId", "description": "Workflow Na Portal Engine Test - Execution of operation", "category": "CFS SUM Na Portal Test - Decision Workflow", "orderDate": "$orderDate", "requestedCompletionDate": "$requestedCompletionDate", "requesterCallback": "$requesterCallback", "orderItem": [{"id": "CFS Sum - notification decision", "action": "decision-workflow-na-portal", "serviceSpecification": {"id": "CFS.SUM"}, "requestedCompletionDate": "$requestedCompletionDate", "service": {"id": "123-NA-PORTAL-DECISION", "category": "CFS", "description": "Workflow Na Portal Engine Test", "serviceCharacteristic": [{"name": "inAttr", "value": "1000"}]}}]}