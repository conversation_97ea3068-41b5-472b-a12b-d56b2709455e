{"externalId": "#externalId", "description": "Workflow Na Portal Engine Test - Execution of operation", "category": "CFS SUM Na Portal Test - Forked Workflow", "orderDate": "$orderDate", "requestedCompletionDate": "$requestedCompletionDate", "requesterCallback": "$requesterCallback", "orderItem": [{"id": "CFS Sum - notification", "action": "fork-workflow-na-portal", "serviceSpecification": {"id": "CFS.SUM"}, "requestedCompletionDate": "$requestedCompletionDate", "service": {"id": "123-NA-PORTAL-FORK", "category": "CFS", "description": "Workflow Na Portal Engine Test", "serviceCharacteristic": [{"name": "inAttr", "value": "1000"}]}}]}