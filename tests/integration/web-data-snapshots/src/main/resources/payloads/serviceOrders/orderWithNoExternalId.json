{"externalId": "", "description": "Workflow Engine Test - Order with no external id", "category": "Order with no external id", "orderDate": "$orderDate", "requestedCompletionDate": "$requestedCompletionDate", "requesterCallback": "$requesterCallback", "orderItem": [{"id": "CFS Sum - non-simulated", "action": "sum", "serviceSpecification": {"id": "CFS.SUM"}, "requestedCompletionDate": "$requestedCompletionDate", "service": {"id": "123", "category": "CFS", "description": "Workflow Engine Test - Order with no external id", "serviceCharacteristic": [{"name": "N_SUMS", "value": "3"}]}}, {"id": "CFS Sum - simulated", "action": "sum-simulated", "serviceSpecification": {"id": "CFS.SUM"}, "requestedCompletionDate": "$requestedCompletionDate", "service": {"id": "123", "category": "CFS", "description": "Workflow Engine Test - Order with no external id", "serviceCharacteristic": [{"name": "N_SUMS", "value": "3"}]}}]}