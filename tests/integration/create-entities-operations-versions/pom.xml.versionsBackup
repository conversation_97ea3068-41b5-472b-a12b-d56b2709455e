<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>pt.ptinovacao.na-portal.it</groupId>
        <artifactId>na-portal-integration-tests</artifactId>
        <version>10.3.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>na-portal-integration-tests-create-entities-operations-versions</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>pt.ptinovacao.na</groupId>
            <artifactId>na-operations-tests-utils</artifactId>
            <version>${version.pt.ptinovacao.na}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>pt.ptinovacao.na</groupId>
            <artifactId>na-operations-catalog-versions-manager</artifactId>
            <version>${version.pt.ptinovacao.na}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>${version.log4j2}</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>${version.log4j2}</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
            <version>${version.log4j2}</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${version.org.slf4j}</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project>