package pt.ptinovacao.na.portal.it.create.entities.operations.versions;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.ptinovacao.na.operations.catalog.versions.manager.scenario.ScenarioBuilder;
import pt.ptinovacao.na.operations.catalog.versions.manager.scenario.ec.EntityVersionState;
import pt.ptinovacao.na.operations.catalog.versions.manager.scenario.oc.OCVersionState;
import pt.ptinovacao.na.scenarios.ec.SelfnetTscScenarioBuilder;
import pt.ptinovacao.na.scenarios.oc.SelfnetOcDummyScenarioBuilder;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

public class NaPortalScenarioBuilder implements ScenarioBuilder {
    private static final Logger LOGGER = LoggerFactory.getLogger(NaPortalScenarioBuilder.class);

    private static final String OC_HOST;
    private static final int OC_PORT;
    private static final boolean OC_LOAD;
    private static final String EC_HOST;
    private static final int EC_PORT;
    private static final boolean EC_LOAD;


    static {
        final Map<String,String> ENV_VARIABLES = System.getenv();
        OC_HOST = ENV_VARIABLES.getOrDefault("OC_HOST", "naportal-ci.c.ptin.corppt.com");
        OC_PORT = Integer.parseInt(ENV_VARIABLES.getOrDefault("OC_PORT", "39001"));
        EC_HOST = ENV_VARIABLES.getOrDefault("EC_HOST", "naportal-ci.c.ptin.corppt.com");
        EC_PORT = Integer.parseInt(ENV_VARIABLES.getOrDefault("EC_PORT", "39000"));
        OC_LOAD = Boolean.parseBoolean(ENV_VARIABLES.getOrDefault("OC_LOAD", "true"));
        EC_LOAD = Boolean.parseBoolean(ENV_VARIABLES.getOrDefault("EC_LOAD", "true"));

    }
    public void buildScenario(List<String> argsList) {
        String ecVersion = "SELFNET-TESTES-1.0.0";
        if(EC_LOAD) {
            LOGGER.info("CREATING EC VERSION {}", ecVersion);
            SelfnetTscScenarioBuilder scenarioBuilder = new SelfnetTscScenarioBuilder(EC_HOST, EC_PORT);
            scenarioBuilder.buildScenario(ecVersion);
            scenarioBuilder.getHelper().updateVersionState(scenarioBuilder.getVersionId(), ecVersion, EntityVersionState.PRODUCTION);
            LOGGER.info("Finished EC VERSION {} ", ecVersion);

            String ecVersionDev = "SELFNET-TESTES-1.0.0-DEV";
            LOGGER.info("CREATING EC VERSION {}", ecVersion);
            scenarioBuilder.buildScenario(ecVersionDev);
            LOGGER.info("Finished EC VERSION # ");


        }
        if(OC_LOAD) {
            final int sleep = 1000;
            try {
                Thread.sleep(sleep);
            } catch (InterruptedException e) {
                LOGGER.error("Error doing a sleep of {}", sleep);
            }

            final AtomicInteger versionCounter = new AtomicInteger(0);
            Arrays.asList(OCVersionState.values()).forEach(versionState -> {
                String ocVersion = "SELFNET-TESTES-1.0." + versionCounter.getAndIncrement();
                LOGGER.info("CREATING OC VERSION {} with {}", ocVersion, ecVersion);
                new SelfnetOcDummyScenarioBuilder(OC_HOST, OC_PORT).buildScenario(ocVersion, ecVersion, versionState);
                LOGGER.info("Finished OC VERSION # ");
            });
        }
        LOGGER.info("Operations and Entities Catalogs Scenario loading complete");
    }
}
