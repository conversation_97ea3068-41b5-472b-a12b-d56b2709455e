package pt.ptinovacao.na.portal.it.web.utils.models.actors.table;

import com.google.common.base.Preconditions;
import pt.ptinovacao.na.portal.it.web.utils.models.data.table_configs.TableDefinition;

import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>, 29-02-2016.
 */
public class TableInteraction {

    private static Map<String, TableDefinition> loadedTables;

    public static void loadTables(Map<String, TableDefinition> tablesToLoad){
        Preconditions.checkArgument( tablesToLoad != null , "trying to load a null Map<String, TableDefinition> tablesToLoad");
        loadedTables = tablesToLoad;
    }

    public static TableDefinition getTableDefinition(String tableName){
        Preconditions.checkNotNull(loadedTables, "loadedTables must be loaded before getting definitions");
        TableDefinition result = loadedTables.get(tableName);
        if(result == null){
            String errTpl = "table \"%s\" is not defined, defined tables: %s";
            String errorMsg = String.format(errTpl, tableName, loadedTables.keySet());
            throw new RuntimeException(errorMsg);
        }
        return result;
    }

}
