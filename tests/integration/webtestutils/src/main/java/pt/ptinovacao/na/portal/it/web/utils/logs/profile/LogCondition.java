package pt.ptinovacao.na.portal.it.web.utils.logs.profile;


import com.google.common.base.Joiner;

import java.util.stream.Stream;

/**
 * <AUTHOR> <o<PERSON>-<EMAIL>>, 18-10-2016.
 */
public enum LogCondition {
    SUCCESS,
    FAILURE,
    ANY;

    private static final String validValue = Joiner.on(", ").join(LogCondition.values());

    public static LogCondition fromValue(String value) {
        return Stream.of(LogCondition.values())
                .filter(condition -> condition.name().compareToIgnoreCase(value) == 0)
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Could not get enum LogCondition with value \"" + value + '\"' + ", valid values: " + validValue));
    }
}
