package pt.ptinovacao.na.portal.it.web.utils.models.data;

import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR> <<EMAIL>>, 18-05-2016.
 */
public class UserProfile {
    private final String name;
    private final String loginUsername;
    private final String loginPassword;
    private final Locale i18n;
    private final List<String> modulesAccess;
    private final boolean accessToAllModules;

    public UserProfile(String name, String loginUsername, String loginPassword, String i18n, List<String> modulesAccess, boolean accessToAllModules) {
        this.name = name;
        this.loginUsername = loginUsername;
        this.loginPassword = loginPassword;
        this.i18n = Locale.forLanguageTag(i18n);
        this.modulesAccess = modulesAccess;
        this.accessToAllModules = accessToAllModules;
    }

    public String getName() {
        return name;
    }

    public String getLoginUsername() {
        return loginUsername;
    }

    public String getLoginPassword() {
        return loginPassword;
    }

    public Locale getI18n() {
        return i18n;
    }

    public boolean hasAccessToSingleModule(){ return !hasAccessToAllModule() && modulesAccess.size() == 1; }

    public boolean hasAccessToAllModule(){ return accessToAllModules; }

    @Override
    public String toString() {
        return "UserProfile{" +
                "name='" + name + '\'' +
                ", loginUsername='" + loginUsername + '\'' +
                ", loginPassword='" + loginPassword + '\'' +
                ", i18n=" + i18n +
                ", modulesAccess=" + modulesAccess +
                ", accessToAllModules=" + accessToAllModules +
                '}';
    }
}
