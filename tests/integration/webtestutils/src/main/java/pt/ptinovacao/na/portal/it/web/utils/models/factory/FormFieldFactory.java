package pt.ptinovacao.na.portal.it.web.utils.models.factory;

import org.slf4j.Logger;
import pt.ptinovacao.na.portal.it.web.utils.logger.ITLogger;
import pt.ptinovacao.na.portal.it.web.utils.models.actors.formHelper.field.interactions.*;
import pt.ptinovacao.na.portal.it.web.utils.models.data.common.UUIDInsert;
import pt.ptinovacao.na.portal.it.web.utils.uuid.UUID_Manager;

import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>, 13-01-2016.
 * <AUTHOR>
 */
public class FormFieldFactory {

    protected static final Logger LOGGER = ITLogger.getLogger(FormFieldFactory.class);

    static FormField fromConfig(String fieldName, Map<String, Object> config) {
        String elementId = config.get(ConfigKeys.FIELD_ELEMENT_ID).toString();
        FieldType fieldType = FieldType.valueOf(config.get(ConfigKeys.FIELD_TYPE).toString().toUpperCase());

        return fieldType.buildFormField(config, fieldName, elementId, getUUIDInsert(config), getUUIDContext(config));
    }

    private static UUID_Manager.UUIDContext getUUIDContext(Map<String, Object> config) {
        UUID_Manager.UUIDContext uuidContext = null;

        if (config.containsKey(ConfigKeys.FIELD_UUID_CONTEXT_OPTION)) {
            uuidContext = UUID_Manager.UUIDContext.valueOf(config.get(ConfigKeys.FIELD_UUID_CONTEXT_OPTION).toString().toUpperCase());
        }
        return uuidContext;
    }

    private static UUIDInsert getUUIDInsert(Map<String, Object> config) {
        UUIDInsert uuidInsert = UUIDInsert.NO_UUID;
        if (config.containsKey(ConfigKeys.FIELD_UUID_OPTION)) {

            switch (config.get(ConfigKeys.FIELD_UUID_OPTION).toString().toUpperCase()) {
                case "APPEND":
                    uuidInsert = UUIDInsert.APPEND;
                    break;
                case "ALPHANUMERIC":
                    uuidInsert = UUIDInsert.CONVERSION_TO_ALPHANUMERIC;
                    break;
                case "NO":
                case "NONE":
                    break;
                default:
                    throw new RuntimeException("unknown uuid option " + config.get(ConfigKeys.FIELD_UUID_OPTION).toString());
            }
        }
        return uuidInsert;
    }

    private enum FieldType {
        TEXT {
            @Override
            public FormField buildFormField(Map<String, Object> config, String fieldName, String elementId, UUIDInsert uuidInsert, UUID_Manager.UUIDContext uuidContext) {
                return TextFieldBuilder.builder(fieldName, elementId).withUUID(uuidInsert).withUUIDContext(uuidContext).build();
            }
        },
        TEXT_AREA {
            @Override
            public FormField buildFormField(Map<String, Object> config, String fieldName, String elementId, UUIDInsert uuidInsert, UUID_Manager.UUIDContext uuidContext) {
                return TextAreaFieldBuilder.builder(fieldName, elementId).withUUID(uuidInsert).withUUIDContext(uuidContext).build();
            }
        },
        DATE {
            @Override
            public FormField buildFormField(Map<String, Object> config, String fieldName, String elementId, UUIDInsert uuidInsert, UUID_Manager.UUIDContext uuidContext) {
                return DateFieldBuilder.builder(fieldName, elementId)
                        .withDateFormat(config.get(ConfigKeys.DATE_FORMAT).toString())
                        .withUUID(uuidInsert).withUUIDContext(uuidContext).build();
            }
        },
        SELECT {
            @Override
            public FormField buildFormField(Map<String, Object> config, String fieldName, String elementId, UUIDInsert uuidInsert, UUID_Manager.UUIDContext uuidContext) {
                SelectFieldBuilder builder = SelectFieldBuilder.builder(fieldName, elementId).withUUID(uuidInsert).withUUIDContext(uuidContext);
                if (config.containsKey(ConfigKeys.SELECT2_COMBO_BOX_TEXT_ELEMENT)) {
                    builder.checkBoxWithElement(config.get(ConfigKeys.SELECT2_COMBO_BOX_TEXT_ELEMENT).toString());
                }
                return builder.build();
            }
        },
        MULTI_SELECT {
            @Override
            public FormField buildFormField(Map<String, Object> config, String fieldName, String elementId, UUIDInsert uuidInsert, UUID_Manager.UUIDContext uuidContext) {
                MultiSelectFieldBuilder builder = MultiSelectFieldBuilder.builder(fieldName, elementId);
                if (config.containsKey(ConfigKeys.SELECT2_TAGS)) {
                    builder.acceptTags(config.get(ConfigKeys.SELECT2_TAGS).toString());
                }
                return builder.withUUID(uuidInsert).withUUIDContext(uuidContext).build();
            }
        },
        SELECT_TREE {
            @Override
            public FormField buildFormField(Map<String, Object> config, String fieldName, String elementId, UUIDInsert uuidInsert, UUID_Manager.UUIDContext uuidContext) {
                return SelectTreeFieldBuilder.builder(fieldName, elementId).withUUID(uuidInsert).withUUIDContext(uuidContext).build();
            }
        },
        RADIO {
            @Override
            public FormField buildFormField(Map<String, Object> config, String fieldName, String elementId, UUIDInsert uuidInsert, UUID_Manager.UUIDContext uuidContext) {
                return RadioFieldBuilder.builder(fieldName, elementId).withUUID(uuidInsert).withUUIDContext(uuidContext).build();
            }
        },
        CHECKBOX {
            @Override
            public FormField buildFormField(Map<String, Object> config, String fieldName, String elementId, UUIDInsert uuidInsert, UUID_Manager.UUIDContext uuidContext) {
                return CheckboxFieldBuilder.builder(fieldName, elementId).withUUID(uuidInsert).withUUIDContext(uuidContext).build();
            }
        };

        public abstract  FormField buildFormField(Map<String, Object> config, String fieldName, String elementId, UUIDInsert uuidInsert, UUID_Manager.UUIDContext uuidContext);
    }

    private static class ConfigKeys {
        static final String DATE_FORMAT = "dateFormat";
        static final String FIELD_ELEMENT_ID = "id";
        static final String FIELD_TYPE = "type";
        static final String FIELD_UUID_OPTION = "uuid";
        static final String FIELD_UUID_CONTEXT_OPTION = "uuid context";
        static final String SELECT2_OPTION_TEXT_ELEMENT = "option text element";
        static final String SELECT2_COMBO_BOX_TEXT_ELEMENT = "combo box text element";
        static final String SELECT2_TAGS = "tags";
    }
}
