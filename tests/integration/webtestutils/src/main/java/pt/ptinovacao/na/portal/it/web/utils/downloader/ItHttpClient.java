package pt.ptinovacao.na.portal.it.web.utils.downloader;

import org.apache.http.HttpHost;
import org.apache.http.HttpRequest;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.cookie.Cookie;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHttpRequest;
import org.apache.http.ssl.SSLContextBuilder;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import java.net.URL;

public class ItHttpClient {

    private HttpClient httpClient;

    public ItHttpClient(Cookie[] cookies){
        try {
            httpClient = buildHttpClient(cookies);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * Build http client
     *
     * @param cookies
     * @return http client
     * @throws Exception
     */
    private HttpClient buildHttpClient(Cookie[] cookies) throws Exception {
        HttpClientBuilder b = HttpClientBuilder.create();
        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, (TrustStrategy) (arg0, arg1) -> true).build();
        b.setSSLContext(sslContext);

        // or SSLConnectionSocketFactory.getDefaultHostnameVerifier(), if you don't want to weaken
        HostnameVerifier hostnameVerifier = SSLConnectionSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER;

        SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(sslContext, hostnameVerifier);

        Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("https", sslSocketFactory)
                .build();

        // allows multi-threaded use
        PoolingHttpClientConnectionManager connMgr = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
        b.setConnectionManager( connMgr);

        // Set cookies
        BasicCookieStore cookieStore = new BasicCookieStore();
        cookieStore.addCookies(cookies);
        b.setDefaultCookieStore(cookieStore);

        return b.build();

    }

    /**
     * Send a request 'GET' to frontend server.
     *
     * @param url contains target uri, server host and port
     * @return
     * @throws Exception
     */
    public HttpResponse sendRequest(URL url) throws Exception {
        HttpRequest httpRequest = new BasicHttpRequest("GET", url.getPath());
        HttpHost httpHost = new HttpHost(url.getHost(), url.getPort(), url.getProtocol());
        return httpClient.execute(httpHost, httpRequest);
    }

}
