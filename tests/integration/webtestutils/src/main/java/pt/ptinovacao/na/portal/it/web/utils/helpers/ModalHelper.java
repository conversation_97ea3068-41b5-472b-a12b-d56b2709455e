package pt.ptinovacao.na.portal.it.web.utils.helpers;

import pt.ptinovacao.na.portal.it.web.utils.driver.ItWebDriver;
import pt.ptinovacao.na.portal.it.web.utils.config.loader.ConfigFile;
import pt.ptinovacao.na.portal.it.web.utils.config.loader.ConfigNode;
import pt.ptinovacao.na.portal.it.web.utils.config.loader.ConfigTree;
import pt.ptinovacao.na.portal.it.web.utils.models.data.UserProfile;

import java.util.List;
import java.util.Locale;


public class ModalHelper {
	private ItWebDriver driver;
    private static final ConfigFile filename = ConfigFile.CONFIRM_MODAL_CONTENTS;

	private ModalHelper(){ }


	private ModalHelper(ItWebDriver driver){
		this.driver = driver;
	}

	public static ModalHelper using(ItWebDriver driver){
		return new ModalHelper(driver);
	}
	

	public ModalHelper openModal(String modalName){
        // Click button by javascript because this way, there's no need to scroll if necessary
        driver.waitAndClickAfterBackdropFadesById("modal open " + modalName);
        return this;
	}
	
	public ModalHelper setModalFormValues(List<FormHelper.FormElems> values){
    	FormHelper.using(driver).setForm("modal").setFields(values);
		return this;
	}

	public ModalHelper setModalFormValues(String formName, List<FormHelper.FormElems> values){
		FormHelper.using(driver).setForm(formName).setFields(values);
		return this;
	}

	public ModalHelper waitModalDisappears(){
		driver.waitElementHidden("modal");
		return this;
	}

	public ModalHelper waitModalVisible(){
		driver.waitElementVisible("modal");
		return this;
	}

    public ModalHelper checkConfirmModal(String confirmationID){
        final UserProfile currentUser = LoginHelper.getCurrentUser();
        final Locale currentUserI18n = currentUser.getI18n();
        final String i18nString = currentUserI18n.toString();
        final String langOnly = currentUserI18n.getLanguage();
        final ConfigNode configNode = ConfigTree.instance.get(filename, confirmationID);

        if (!configNode.isValid()) {
            throw new RuntimeException("no confirmation named '" + confirmationID + "' found in config file");
        }

        ConfigNode node = configNode.get(currentUserI18n.toLanguageTag());
        if (!node.isValid()) {
            node = configNode.get(langOnly);
        }

        if (!node.isValid()) {
            throw new RuntimeException("no lang " + i18nString + " or " + langOnly + " of '" + confirmationID + "' found in config file");
        }

        final String title = node.getString("title");
        final String content = node.getString("content");
        int timeout = Math.max(7, driver.getDefaultTimeout()); //the timeout is visible at some seconds
        driver.waitElementVisible(timeout, "modal");
        driver.assertThat.element("modal title").containsText(title);
        driver.assertThat.element("modal body").containsText(content);


        // Click button by javascript because this way, there's no need to scroll if necessary

        return this;
    }

	public ModalHelper checkSystemDetailsModal(String notificationType, String systemName){
		final String title = notificationType + " (" + systemName + ")";
		int timeout = Math.max(7, driver.getDefaultTimeout()); //the timeout is visible at some seconds
		driver.waitElementVisible(timeout, "modal");
		driver.assertThat.element("modal title").containsText(title, false);
		return this;
	}

	public ModalHelper ok(){
		driver.click("modal close ok");
		return waitLoadingFades();
	}

	public ModalHelper ok(String okButton){
		driver.click(okButton);
		return waitLoadingFades();
	}


	public ModalHelper cancel(){
		driver.click("modal close cancel");
		return waitLoadingFades();
	}
	
	public ModalHelper close(){
		driver.click("modal close");
		return waitLoadingFades();
	}
	
	private ModalHelper waitLoadingFades(){
		driver.waitLoadingFades();
		return this;
	}
	
	
}
	
	