package pt.ptinovacao.na.portal.it.web.utils.helpers.select;

import org.slf4j.Logger;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedCondition;
import pt.ptinovacao.na.portal.it.web.utils.Retry;
import pt.ptinovacao.na.portal.it.web.utils.driver.ItWebDriver;
import pt.ptinovacao.na.portal.it.web.utils.logger.ITLogger;
import pt.ptinovacao.na.portal.it.web.utils.driver.WebElementReference;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * Helper functions that interacts with the select tree box
 *
 */
@SuppressWarnings("UnusedReturnValue")
public class SelectTree {

	private WebElement select2;
    private final WebElementReference select2Reference;

    @SuppressWarnings("FieldCanBeLocal")
    private boolean isMultiple = false;
	private final ItWebDriver driver;

    private static final Logger LOGGER = ITLogger.getLogger(SelectTree.class);


	public SelectTree(ItWebDriver driver, WebElement el){
		this.driver = driver;
		select2 = el;
        select2Reference = null;
	}

    public SelectTree(ItWebDriver driver, WebElementReference el){
        this.driver = driver;
        select2Reference = el;
    }

    public WebElement getWebElement(){
        if(select2Reference != null){
            return select2Reference.queryElement();
        } else {
            return select2;
        }
    }


	public static SelectTree fromField(ItWebDriver driver, String fieldName){
		return new SelectTree(driver, driver.element(Elem.BUTTON_FROM_ID, fieldName));
	}

    public static SelectTree fromElementName(ItWebDriver driver, String elementName){
        return new SelectTree(driver, new WebElementReference(driver, elementName, Elem.BUTTON_FROM_ELEMENT));
    }
	

    /* ******************************
				Public methods
	 ******************************* */
	
	public SelectTree click(){
        if (select2Reference != null) {
            select2 = driver.click(select2Reference);
        } else {
			driver.click(select2);
        }
		return this;
	}


	/** opens the select tree */
	public SelectTree open() {
		Retry.call(() -> {
			click();
			driver.waitElementVisible(driver.getDefaultTimeout() / Retry.getDefaultMaxNumOfRetries(), Elem.TREE_DROPDOWN);
		},(Retry.RetryError e) -> {
			//ignore
		}, 2);
		return this;
	}

	/** closes the select tree */
	public SelectTree close() {
		Retry.call(() -> {
			click();
			driver.waitElementHidden(driver.getDefaultTimeout() / Retry.getDefaultMaxNumOfRetries(), Elem.TREE_DROPDOWN);
		},(Retry.RetryError e) -> {
			//ignore
		}, 2);
		return this;
	}

	/** selects the option with the following {{val}} value, remember that the select tree must be already open
	 *  with the {@link SelectTree#open } method */
	public SelectTree selectVal(String val){
		Retry.call(() -> {
			List<WebElement> results = getResultsFromSearch(val);

			LOGGER.info(String.format("found %d select tree results", results.size()));

			List<WebElement> match = new ArrayList<>();
			for (WebElement el : results) {
				String elementText = el.getText();
				LOGGER.info(String.format("found select tree result result \"%s\"", elementText));
				if (val.equals(elementText)) {
					match.add(el);
				}
			}

			checkResults(match, val);
			driver.click(match.get(0));
		},(Retry.RetryError e) -> {
			//ignore
		}, 2);

		return this;
	}


	/** check if the select2 contains the option with the following {{value}}, remember that the
	 *  select2 must be already open with the {@link SelectTree#open } method */
	public SelectTree checkValueExists(String value){
		List<WebElement> results = getResultsFromSearch(value);
		checkResults(results, value);
		return this;
	}

	/** check if the select2 contains all options with the following {{values}}, remember that the
	 *  select2 must be already open with the {@link SelectTree#open } method */
	public SelectTree checkValuesExist(List<String> values){
		for(String val : values){
			checkValueExists(val);
		}
		return this;
	}

	/** check if the select2 contains aa defined number of options, remember that the
	 *  select2 must be already open with the {@link SelectTree#open } method */
	public SelectTree checkNumOfOptionsEquals(int expectedNumOfOptions){
		int numOfOptions = getResultsFromSearch().size();
		if(numOfOptions != expectedNumOfOptions){
			throw new RuntimeException("select contains " + numOfOptions + " options, expected  " +
					expectedNumOfOptions + " options");
		}
		return this;
	}

	/** check if the select2 contains aa defined number of options, remember that the
	 *  select2 must be already open with the {@link SelectTree#open } method */
	public SelectTree checkValuesEquals(List<String> values){
		List<String> selectValues = selectValuesAsStringList(getResultsFromSearch());
		Collections.sort(values);
		Collections.sort(selectValues);
		if(!selectValues.equals(values)){
			throw new RuntimeException("list " + values + " does not equals "+ selectValues);
		}
		return this;
	}

	/** check if the select2 contains aa defined number of options, remember that the
	 *  select2 must be already open with the {@link SelectTree#open } method */
	public SelectTree checkValueNotExists(String val){
		try{
			checkValueExists(val);
		}catch( EmptyResultsException | NonEqualResultException e){
			return this;
		}
		throw new EqualResultException(val);
	}

	private List<String> selectValuesAsStringList(List<WebElement> selectValues){
		List<String> result = new ArrayList<>();
		for (WebElement selectValue : selectValues){
			result.add(selectValue.getText());
		}
		return result;
	}

	/** get all options from the select box */
	public List<WebElement> getResultsFromSearch(){
		WebElement select2Drop = driver.waitElementVisible(Elem.TREE_DROPDOWN);
		return driver.elements(select2Drop, Elem.RESULTS);
	}



    /* ******************************
				Private methods
	 ******************************* */


	/** filter select values by {{val}} and get the results */
	private void checkResults(List<WebElement> results, String val){
		if(results.isEmpty()){ throw new EmptyResultsException(val); }
		String text = results.get(0).getText();
		if(!text.equals(val)){ throw new NonEqualResultException(val, text); }
	}

    /** filter select values by {{val}} and get the results */
	private List<WebElement> getResultsFromSearch(String val){
        final WebElement input;
		final WebElement select2Drop = driver.waitElementVisible(Elem.TREE_DROPDOWN);


        if(isMultiple){
            input = driver.element(select2, Elem.INPUT);
        } else {
            input = driver.element(select2Drop, Elem.INPUT);
        }

		ExpectedCondition<Boolean> noResults = (WebDriver arg0) -> driver.elements(select2Drop, Elem.SEARCH_RESULTS).isEmpty();
		ExpectedCondition<Boolean> inputActive = (WebDriver arg0) -> input.getAttribute("class").contains("active");

		input.clear();
		input.sendKeys(" ");
		driver.waitUntil(noResults);
		input.clear();
		input.sendKeys(val);
		driver.waitUntilNot(noResults);
		driver.waitUntilNot(inputActive);
        driver.waitElementVisible(select2Drop, Elem.SEARCH_RESULTS);
		return driver.elements(select2Drop, Elem.SEARCH_RESULTS);
	}

    /* ******************************
				Element keys
	 ******************************* */


    private static class Elem {
        static final String TREE_DROPDOWN =  "select box tree";
        static final String RESULTS =  "select box tree results";
        static final String SEARCH_RESULTS =  "select box tree search results";
        static final String INPUT =  "select box tree input";
        static final String BUTTON_FROM_ID =  "select box tree button from id";
        static final String BUTTON_FROM_ELEMENT =  "select box tree button from element";

    }


	/* ***********************************
                Exceptions
    ************************************ */


    /**
     * Empty Results Exception
     *
     * This Exception is thrown when the select2 doesn't show any result when searching for a value
     */
	public static class EmptyResultsException extends RuntimeException{
		private static final long serialVersionUID = 1L;
		public final String select2Value;
		EmptyResultsException(String value){
			super("No select2 value like \""+ value +"\" exists");
			select2Value = value;
		}
	}

	/**
	 * Non Equal Result Exception
	 *
	 * This Exception is thrown when the select2 doesn't show any results that are equal to the query
	 */
	public static class NonEqualResultException extends RuntimeException{
		private static final long serialVersionUID = 1L;
		public final String select2Value;
		public final String closestVal;
		NonEqualResultException(String value, String closestResult){
			super("The closest select2 value to \""+value +"\" is \""+ closestResult +"\"");
			select2Value = value;
			closestVal = closestResult;
		}
	}

	/**
	 * Equal Result Exception
	 *
	 * Opposite of {@link NonEqualResultException}, This exception is thrown when
	 * the test expects that a selects doesn't have a value (e.g. validate that a search filter
	 * has been successfully deleted).
	 */
	public static class EqualResultException extends RuntimeException{
		private static final long serialVersionUID = 1L;
		public final String select2Value;
		EqualResultException(String value){
			super("The select2 results contains the value \""+value +"\"");
			select2Value = value;
		}
	}
	
	
}

