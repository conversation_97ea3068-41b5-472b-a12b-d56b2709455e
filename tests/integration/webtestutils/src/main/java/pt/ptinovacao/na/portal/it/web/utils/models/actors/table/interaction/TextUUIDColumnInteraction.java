package pt.ptinovacao.na.portal.it.web.utils.models.actors.table.interaction;

import org.openqa.selenium.WebElement;
import pt.ptinovacao.na.portal.it.web.utils.models.data.table_configs.TableColumnDefinition;
import pt.ptinovacao.na.portal.it.web.utils.uuid.UUID_Manager;

/**
 * <AUTHOR> <<EMAIL>>, 29-02-2016.
 */
public class TextUUIDColumnInteraction extends TextColumnInteraction {


    @Override
    protected boolean verifyCellMatch(WebElement cellElement, String cellTextToMatch, TableColumnDefinition columnDefinition) {
        return super.verifyCellMatch(cellElement, UUID_Manager.getUniqueNameOrName(cellTextToMatch), columnDefinition);
    }
}
