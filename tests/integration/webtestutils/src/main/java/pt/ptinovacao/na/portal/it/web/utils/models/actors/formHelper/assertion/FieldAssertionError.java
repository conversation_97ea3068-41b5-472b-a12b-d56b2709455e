package pt.ptinovacao.na.portal.it.web.utils.models.actors.formHelper.assertion;

/**
 * Field Assertion Error
 *
 * Error thrown when the value field did not match the expected one
 */
public class FieldAssertionError extends AssertionError{
    private static final String MessageFormat = "field '%s' value is '%s', different from expected value '%s'.";

    public FieldAssertionError(String fieldName, String fieldValue, String expectedValue){
        super(String.format(MessageFormat, fieldName, fieldValue, expectedValue));
    }
}