package pt.ptinovacao.na.portal.it.web.utils.models.actors.formHelper.field.interactions;

import java.util.List;

public interface FormFieldInteraction extends ContextInteraction {

    String getElementIdSelector();

    String setFieldValue(String value);

    String getFieldValue();

    void checkValueEquals(String expectedValue);

    void checkValueEquals(List<String> expectedValue);

    void checkEnabled(Boolean isEnabled);

    void checkValueExists(String value);

    void checkValueExists(List<String> value);

    void checkValueNotExists(String value);

}
