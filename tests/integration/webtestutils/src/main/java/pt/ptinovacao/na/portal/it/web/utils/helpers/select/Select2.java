package pt.ptinovacao.na.portal.it.web.utils.helpers.select;

import org.openqa.selenium.WebElement;
import pt.ptinovacao.na.portal.it.web.utils.Retry;
import pt.ptinovacao.na.portal.it.web.utils.driver.ItWebDriver;
import pt.ptinovacao.na.portal.it.web.utils.driver.WebElementReference;

import java.util.ArrayList;
import java.util.List;

/**
 * Helper functions that interacts with the select2 element
 */
public class Select2 extends AbstractSelect {
    private String optionResultElement;

    private Select2(ItWebDriver driver, WebElementReference el) {
        this.driver = driver;
        this.select2Reference = el;
    }

    public static Select2 fromField(ItWebDriver driver, String fieldName) {
        return new Select2(driver, new WebElementReference(driver, Elem.BUTTON, fieldName));
    }

    public static Select2 fromField(ItWebDriver driver, WebElementReference select2ParentReference) {
        return new Select2(driver, new WebElementReference(select2ParentReference, Elem.SELECTION_CONTAINER));
    }

    public static Select2 filterInput(ItWebDriver driver) {
        return new Select2(driver, new WebElementReference(driver, Elem.FILTER_BUTTON));
    }

	  /* ******************************
                Public methods
	 ******************************* */

    public Select2 withResultElement(String result) {
        optionResultElement = result;
        return this;
    }

    /**
     * opens the select2
     */
    public Select2 open() {
        Retry.call(() -> {
            click();
            waitSelect2DropVisible(driver.getDefaultTimeout() / Retry.getDefaultMaxNumOfRetries());
        }, (Retry.RetryError e) -> {
            //ignore
        });
        return this;
    }

    /**
     * closes the select2
     */
    public Select2 close() {
        Retry.call(() -> {
            click();
            waitSelect2DropHidden(driver.getDefaultTimeout() / Retry.getDefaultMaxNumOfRetries());
        }, (Retry.RetryError e) -> {
            //ignore
        });
        return this;
    }

    public Select2 selectValue(String value) {
        value = value.trim();
        List<WebElement> options = getResults();
        if (clickOption(options, value)) {
            return this;
        }
        throw new NonEqualResultException(value, options.get(0).getText().trim());
    }


    /**
     * selects the option with the following {{val}} value, remember that the select2 must be already open
     * with the {@link Select2#open } method
     */
    public Select2 searchAndSelectVal(String value) {
        value = value.trim();
        List<WebElement> options = getResultsFromSearch(value);
        if (clickOption(options, value)) {
            return this;
        }
        throw new NonEqualResultException(value, options.get(0).getText().trim());
    }


    private boolean clickOption(List<WebElement> options, String value) {
        for (WebElement option : options) {
            WebElement resultOption = option;
            if (optionResultElement != null) {
                resultOption = driver.waitElementVisible(option, optionResultElement);
            }

            String optionText = resultOption.getText().trim();
            if (value.equals(optionText)) {
                driver.click(option);
                return true;
            }
        }
        return false;
    }

    public Select2 selectValOnSearch(String value, String searchText) {
        value = value.trim();
        List<WebElement> options = getResultsFromSearch(searchText);
        if (clickOption(options, value)) {
            return this;
        }
        throw new NonEqualResultException(value, options.get(0).getText().trim());
    }

    public String getSelectedValue() {
        return select2Reference.queryElement().getAttribute("title").trim();
    }

    /**
     * check if the select2 contains the option with the following {{value}}, remember that the
     * select2 must be already open with the {@link Select2#open } method
     */
    public Select2 checkValueExists(String value) {
        List<WebElement> results = getResultsFromSearch(value);
        checkResults(results, value);
        return this;
    }

    /**
     * check if the select2 contains all options with the following {{values}}, remember that the
     * select2 must be already open with the {@link Select2#open } method
     */
    public Select2 checkValuesExist(List<String> values) {
        for (String val : values) {
            checkValueExists(val);
        }
        return this;
    }

    /**
     * check if the select2 contains a defined number of options, remember that the
     * select2 must be already open with the {@link Select2#open } method
     */
    public Select2 checkValuesEquals(List<String> expectedValues) {
        checkValuesAreEqual(expectedValues, getOptions());
        return this;
    }

    /**
     * check if the select2 contains aa defined number of options, remember that the
     * select2 must be already open with the {@link Select2#open } method
     */
    public Select2 checkValueNotExists(String val) {
        try {
            checkValueExists(val);
        } catch (EmptyResultsException | NonEqualResultException e) {
            return this;
        }
        throw new EqualResultException(val);
    }


	/* ******************************
				Private methods
	 ******************************* */

    /**
     * filter select values by {{val}} and get the results
     */
    private void checkResults(List<WebElement> results, String value) {
        checkIfThereAreNoResults(results, value);

        List<String> optionsValue = new ArrayList<>();
        for (WebElement elementOption : results) {
            WebElement resultOption = elementOption;
            if (optionResultElement != null) {
                resultOption = driver.element(elementOption, optionResultElement);
            }
            optionsValue.add(resultOption.getText().trim());

        }

        if (!optionsValue.contains(value)) {
            throw new NonEqualResultException(value, optionsValue.get(0));
        }
    }

    /**
     * filter select values by {{val}} and get the results
     */
    private List<String> getOptions() {
        return getOptionValues(getResults());
    }

    /**
     * filter select values by {{val}} and get the results
     */
    private List<WebElement> getResultsFromSearch(String value) {
        WebElement select2Drop = waitSelect2DropVisible();
        final WebElement input = driver.element(select2Drop, Elem.INPUT);
        sendValueToSearchInput(value, input);
        return Retry.call(this::getResults);
    }

}

