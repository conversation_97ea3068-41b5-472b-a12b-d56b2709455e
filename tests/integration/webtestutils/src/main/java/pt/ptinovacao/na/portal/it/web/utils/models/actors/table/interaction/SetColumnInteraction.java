package pt.ptinovacao.na.portal.it.web.utils.models.actors.table.interaction;

import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedCondition;
import pt.ptinovacao.na.portal.it.web.utils.driver.ItWebDriver;
import pt.ptinovacao.na.portal.it.web.utils.models.actors.table.ColumnInteraction;
import pt.ptinovacao.na.portal.it.web.utils.models.data.table_configs.TableColumnDefinition;

import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Column interaction where the expected content is a list of non-duplicated values separated with a comma
 */
public class SetColumnInteraction extends ColumnInteraction {

    @Override
    protected boolean verifyCellMatch(WebElement cellElement, String cellTextToMatch, TableColumnDefinition columnDefinition) {
        return ItWebDriver.current().waitUntil(new ConditionElementTextMatches(cellElement,cellTextToMatch,columnDefinition));
    }

    private class ConditionElementTextMatches implements ExpectedCondition<Boolean> {

        private final WebElement cellElement;
        private final Pattern patternToMatch;
        private final TableColumnDefinition columnDefinition;
        private Set<String> toMatch;

        ConditionElementTextMatches(WebElement cellElement, String patternToMatch, TableColumnDefinition columnDefinition) {
            this.cellElement = cellElement;
            this.patternToMatch = Pattern.compile(patternToMatch);
            this.columnDefinition = columnDefinition;
            this.toMatch = Stream.of(patternToMatch.split(",")).map(String::trim).collect(Collectors.toSet());
        }

        @Override
        public Boolean apply(WebDriver webDriver) {
            final String text = ItWebDriver.grabText(cellElement);
            Set<String> values = Stream.of(text.split(",")).map(String::trim).collect(Collectors.toSet());
            boolean result = values.containsAll(toMatch);
            LOGGER.info(String.format("TextColumnInteraction - do cell content '%s' match set %s? %s.",text,toMatch,(result) ? "yes" : "no"));
            return result;
        }

        @Override
        public String toString() {
            return String.format("cell text of column '%s' and table '%s' to match set '%s'",
                    columnDefinition.getKey(),columnDefinition.getTable().getTableName(),patternToMatch
            );
        }
    }
}
