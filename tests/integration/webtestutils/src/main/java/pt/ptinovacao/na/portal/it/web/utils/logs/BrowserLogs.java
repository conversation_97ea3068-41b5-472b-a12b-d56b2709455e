package pt.ptinovacao.na.portal.it.web.utils.logs;

import com.google.common.collect.ImmutableMap;
import org.openqa.selenium.logging.LogEntries;
import org.openqa.selenium.logging.LogEntry;
import org.openqa.selenium.logging.LogLevelMapping;
import org.openqa.selenium.logging.LogType;
import org.openqa.selenium.support.Color;
import pt.ptinovacao.na.portal.it.web.utils.driver.ItWebDriver;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Date;
import java.util.Map;
import java.util.logging.Level;

/**
 * <AUTHOR> <<EMAIL>>, 14-04-2016.
 */
public class BrowserLogs {

    private BrowserLogs() {
    }

    /**
     * Grabs the browser log as HTML to write on the reports
     *
     * @param driver - webdriver to get the browser logs
     * @return browser log in HTML format.
     */
    public static String grabDriverLogs(ItWebDriver driver) {
        StringBuilder builder = new StringBuilder();
        try {
            LogEntries logEntries = driver.originalDriver().manage().logs().get(LogType.BROWSER);

            for (LogEntry entry : logEntries) {
                Date timestamp = new Date(entry.getTimestamp());
                Level level = entry.getLevel();
                String logMessage = entry.getMessage();
                Level normalized = LogLevelMapping.toLevel(LogLevelMapping.getName(level));
                String message = String.format("<span class=\"%s\">%s %s %s</span>", normalized, timestamp, level, logMessage);
                builder.append(message);
                if (!logMessage.endsWith("\n")) {
                    builder.append('\n');
                }

            }
        } catch (Throwable e) {
            builder.append("Unable to get browser logs due to exception:\n");
            StringWriter errors = new StringWriter();
            e.printStackTrace(new PrintWriter(errors));
            builder.append(errors);
        }

        return "<span class=\"" + BROWSER_LOG_CSS_CLASS + "\"><style>" + getCssContent() + "</style>" + builder.toString() + "</span>";

    }

    private static String getCssContent() {
        StringBuilder styleHtml = new StringBuilder("<style>");
        for (Map.Entry<Level, Color> entries : browserLogLevelColorMap.entrySet()) {
            String logLevelClass = entries.getKey().getName();
            String bgColor = entries.getValue().asRgb();
            styleHtml.append(String.format("." + BROWSER_LOG_CSS_CLASS + " .%s {border-left:.5em solid %s; padding-left:.5em}", logLevelClass, bgColor));
        }
        return styleHtml.toString();
    }

    private static final String BROWSER_LOG_CSS_CLASS = "browser-log";

    private static final Map<Level, Color> browserLogLevelColorMap = ImmutableMap.<Level, Color>builder()
            .put(Level.FINE, Color.fromString("rgb(100,255,100)"))
            .put(Level.INFO, Color.fromString("rgb(100,100,255)"))
            .put(Level.WARNING, Color.fromString("yellow"))
            .put(Level.SEVERE, Color.fromString("red"))
            .build();


}
