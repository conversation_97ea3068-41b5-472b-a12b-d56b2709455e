package pt.ptinovacao.na.portal.it.web.utils.models.actors.table.interaction;

import com.google.common.base.Splitter;
import org.apache.commons.lang3.StringUtils;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriverException;
import org.openqa.selenium.WebElement;
import pt.ptinovacao.na.portal.it.web.utils.Retry;
import pt.ptinovacao.na.portal.it.web.utils.driver.ItWebDriver;
import pt.ptinovacao.na.portal.it.web.utils.models.actors.table.ColumnInteraction;
import pt.ptinovacao.na.portal.it.web.utils.models.data.table_configs.TableColumnDefinition;
import pt.ptinovacao.na.portal.it.web.utils.models.data.table_configs.metas.ActionColumnMeta;
import pt.ptinovacao.na.portal.it.web.utils.models.data.table_configs.metas.ActionColumnMetaEntry;

import java.util.List;
import java.util.function.Supplier;

/**
 * <AUTHOR> <<EMAIL>>, 29-02-2016.
 */
public class ActionColumnInteraction extends ColumnInteraction {

    private static final Splitter matchSplitter = Splitter.on(",").trimResults().omitEmptyStrings();
    private static final Splitter actionStateSplitter = Splitter.on("(").trimResults().omitEmptyStrings();


    public static void clickAction(Supplier<WebElement> tableElement, String actionName, int rowIndex, TableColumnDefinition columnDefinition){
        Retry.call(() -> clickActionButton(tableElement.get(),actionName,rowIndex,columnDefinition),
            (Retry.RetryError e) -> {
                //ignore
             }, 4);
    }


    @Override
    protected boolean verifyCellMatch(WebElement cellElement, String cellTextToMatch, TableColumnDefinition columnDefinition) {
        LOGGER.trace("ActionColumnInteraction.verifyCellMatch(<element>,{},{})", cellTextToMatch,columnDefinition);
        Iterable<String> actionsToMatch = matchSplitter.split(cellTextToMatch);
        LOGGER.trace("actionsToMatch = {}", actionsToMatch);
        for (String stringAction: actionsToMatch){
            ActionToCheck actionToCheck = parseToActionToCheck(stringAction);
            String actionName = actionToCheck.actionName;
            ActionColumnMetaEntry entry = getAction(actionName, columnDefinition);
            if(StringUtils.isNotBlank(entry.getSubActionOf())){
                clickActionButton(cellElement, entry.getSubActionOf(), columnDefinition);
            }
            if(!actionToCheck.checkState || actionToCheck.state != ActionState.HIDDEN){
                WebElement actionElement = cellElement.findElement(By.cssSelector(entry.getCss()));
                if(actionToCheck.checkState){
                    String elementInfo = actionName + " table action";
                    switch (actionToCheck.state){
                        case ENABLED:
                            LOGGER.trace("checking if action {} is enabled", actionName);
                            ItWebDriver.current().assertThat.element(actionElement, elementInfo).notContainsClass("disabled");
                            LOGGER.trace("action {} is enabled", actionName);
                            break;
                        case DISABLED:
                            LOGGER.trace("checking if action {} is disabled", actionName);
                            ItWebDriver.current().assertThat.element(actionElement, elementInfo).containsClass("disabled");
                            LOGGER.trace("action {} is disabled", actionName);
                            break;
                    }
                }
            } else { //actionToCheck.checkState && actionToCheck.state == ActionState.HIDDEN
                try{
                    WebElement actionElement = cellElement.findElement(By.cssSelector(entry.getCss()));
                    if(actionElement.isDisplayed()){
                        throw new RuntimeException("action "+stringAction+"is visible, expected not visible");
                    }
                } catch (WebDriverException e){
                    //ignore
                }
            }
            if(StringUtils.isNotBlank(entry.getSubActionOf())){
                clickRootActionButton(cellElement, entry.getSubActionOf(), columnDefinition);
            }
        }
        return true;
    }

    private static ActionColumnMetaEntry getAction(String actionName, TableColumnDefinition columnDefinition){
        try{
            return ((ActionColumnMeta) columnDefinition.getMeta()).getAction(actionName);
        } catch (ActionColumnMeta.UndefinedActionException e){
            e.setColumnDefinition(columnDefinition);
            throw e;
        }
    }

    private ActionToCheck parseToActionToCheck(String stringToMatch){
        if(stringToMatch.matches("[^\\(]+\\(disabled?\\)")){
            return new ActionToCheck(actionStateSplitter.split(stringToMatch).iterator().next(), ActionState.DISABLED);
        } else if(stringToMatch.matches("[^\\(]+\\(enabled?\\)")){
            return new ActionToCheck(actionStateSplitter.split(stringToMatch).iterator().next(), ActionState.ENABLED);
        } else if(stringToMatch.matches("[^\\(]+\\(hidden?\\)")){
            return new ActionToCheck(actionStateSplitter.split(stringToMatch).iterator().next(), ActionState.HIDDEN);
        } else if(stringToMatch.matches("[^\\(]+\\(visible?\\)")){
            return new ActionToCheck(actionStateSplitter.split(stringToMatch).iterator().next(), ActionState.VISIBLE);
        }  else {
            return new ActionToCheck(stringToMatch.trim());
        }
    }

    private static void clickActionButton(WebElement cellTarget, String actionName, TableColumnDefinition columnDefinition){
        ActionColumnMetaEntry entry = getAction(actionName, columnDefinition);
        if(StringUtils.isNotBlank(entry.getSubActionOf())){
            clickActionButton(cellTarget, entry.getSubActionOf(), columnDefinition);
        }
        String cssSelector = entry.getCss();
        WebElement actionElement = cellTarget.findElement(By.cssSelector(cssSelector));
        final ItWebDriver driver = ItWebDriver.current();
        driver.scrollToElementIfNeeded(actionElement);
        driver.click(actionElement);
    }

    private static void clickRootActionButton(WebElement cellTarget, String actionName, TableColumnDefinition columnDefinition){
        ActionColumnMetaEntry entry = getAction(actionName, columnDefinition);
        if(StringUtils.isNotBlank(entry.getSubActionOf())){
            clickRootActionButton(cellTarget, entry.getSubActionOf(), columnDefinition);
        } else {
            WebElement actionElement = cellTarget.findElement(By.cssSelector(entry.getCss()));
            ItWebDriver.current().click(actionElement);
        }
    }

    private static void clickActionButton(WebElement tableElement, String actionName, int rowIndex, TableColumnDefinition columnDefinition){
        List<WebElement> cells = getCells(tableElement, columnDefinition);
        WebElement cellTarget = cells.get(rowIndex);
        clickActionButton(cellTarget,actionName,columnDefinition);
    }


    private enum ActionState{
        ENABLED, DISABLED, HIDDEN, VISIBLE
    }

    private class ActionToCheck{
        final String actionName;
        final ActionState state;
        final boolean checkState;

        ActionToCheck(String actionName) {
            this.actionName = actionName;
            this.state = ActionState.VISIBLE;
            this.checkState = false;
        }

        ActionToCheck(String actionName, ActionState state) {
            this.actionName = actionName;
            this.state = state;
            this.checkState = true;
        }


    }
}
