package pt.ptinovacao.na.portal.it.web.utils.driver.asserts;

import org.openqa.selenium.TimeoutException;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import pt.ptinovacao.na.portal.it.web.utils.driver.ItWebDriver;
import pt.ptinovacao.na.portal.it.web.utils.Retry;

import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.junit.Assert.*;

/**
 * <AUTHOR> <<EMAIL>>, 30-05-2017.
 */
public class WebElementAssert extends DriverAssert{
    private final Supplier<WebElement> element;
    private final String elementInfo;

    public WebElementAssert(ItWebDriver driver, String elementName){
        this.element = () -> driver.waitElementPresent(elementName);
        this.driver = driver;
        this.elementInfo = driver.elementInfo(elementName);
    }

    public WebElementAssert(ItWebDriver driver, WebElement element, String info){
        this.element = () -> element;
        this.driver = driver;
        this.elementInfo = info;
    }

    public WebElementAssert containsText(final String text){
        return containsText(text, true);
    }

    public WebElementAssert containsText(final String text, boolean isRegexPattern){
        try {
            if(isRegexPattern){
                final Pattern compile = Pattern.compile(text);
                driver.waitUntil((WebDriver webDriver) -> checkIfContainsText(compile));
            } else {
                driver.waitUntil((WebDriver webDriver) -> checkIfContainsText(text));
            }
        } catch (TimeoutException e){
            String content = element.get().getText();
            String failMsg = String.format("text content of %s \n should contain: \"%s\";\n\n Element text:\n\"%s\"", elementInfo, text, content);
            throw new RuntimeException(failMsg, e);
        }
        return this;
    }
    public WebElementAssert doesNotContainsText(final String text){
        return doesNotContainsText(text, true);
    }


    public WebElementAssert doesNotContainsText(final String text, boolean isRegexPattern){
        try {
            if(isRegexPattern){
                final Pattern compile = Pattern.compile(text);
                driver.waitUntil((WebDriver webDriver) -> checkIfDoesNotContainsText(compile));
            } else {
                driver.waitUntil((WebDriver webDriver) -> checkIfDoesNotContainsText(text));
            }
        } catch (TimeoutException e){
            String content = element.get().getText();
            String failMsg = String.format("text content of %s \n should not contain: \"%s\";\n\n Element text:\n\"%s\"", elementInfo, text, content);
            throw new RuntimeException(failMsg, e);
        }
        return this;
    }

    public WebElementAssert textEquals(final String text){
        try{
            driver.waitUntil(webDriver -> text.equals(Retry.get(() -> ItWebDriver.grabText(element.get()))));
        } catch (TimeoutException e){
            String content = element.get().getText();
            String failMsg = String.format("text content of %s \n should equal: \"%s\";\n\n  Element text:\n\"%s\"", elementInfo,text,content);
            throw new RuntimeException(failMsg, e);
        }
        return this;
    }

    public WebElementAssert doesNotHaveAttribute(String attribute){
        String content = element.get().getAttribute(attribute);
        String failMsg = String.format("%s has attribute %s with value %s", elementInfo,attribute,content);
        assertNull(failMsg, content);
        return this;
    }

    public WebElementAssert hasAttribute(String attribute){
        String content = element.get().getAttribute(attribute);
        String failMsg = String.format("%s does not have attribute %s", elementInfo,attribute);
        assertNotNull(failMsg, content);
        return this;
    }

    public WebElementAssert attributeValueEquals(String attribute, String text){
        String content = element.get().getAttribute(attribute);
        String failMsg = String.format("attribute value of %s \n should equal: \"%s\";\n\n  value:\n\"%s\"", elementInfo,text,content);
        assertEquals(failMsg, text, content);
        return this;
    }

    public WebElementAssert isVisible(){
        String failMsg = String.format("element %s does not exist", elementInfo);
        assertTrue(failMsg, element.get().isDisplayed());
        return this;
    }

    public WebElementAssert isNotVisible(){
        String failMsg = String.format("element %s exists", elementInfo);
        assertFalse(failMsg, element.get().isDisplayed());
        return this;
    }

    public WebElementAssert isEnabled(){
        String failMsg = String.format("element %s is disabled", elementInfo);
        assertTrue(failMsg, element.get().isEnabled());
        return this;
    }

    public WebElementAssert isDisabled(){
        String failMsg = String.format("element %s exists", elementInfo);
        assertFalse(failMsg, element.get().isEnabled());
        return this;
    }

    public WebElementAssert containsClass(final String classes){
        driver.waitUntil((WebDriver e) -> hasClass(classes));
        return this;
    }

    public WebElementAssert notContainsClass(final String classes){
        driver.waitUntil((WebDriver e) -> !hasClass(classes));
        return this;
    }


    private boolean hasClass(String classesToCheckStr){
        String[] classesToCheck = classesToCheckStr.split(" ", -1);
        for (String elementClasses : element.get().getAttribute("class").split(" ", -1)) {
            int validClasses = 0;
            for (String classToCheck : classesToCheck) {
                if (elementClasses.equals(classToCheck)) {
                    validClasses++;
                }
            }
            if (validClasses == classesToCheck.length) {
                return true;
            }
        }
        return false;
    }

    private boolean checkIfDoesNotContainsText(final String text){
        return !checkIfContainsText(text);
    }

    private boolean checkIfDoesNotContainsText(final Pattern pattern){
        return !checkIfContainsText(pattern);
    }


    private boolean checkIfContainsText(final String text){
        return ItWebDriver.grabText(element.get()).contains(text);
    }

    private boolean checkIfContainsText(final Pattern pattern){
        String content = ItWebDriver.grabText(element.get());
        Matcher matcher = pattern.matcher(content);
        return matcher.find();
    }
}