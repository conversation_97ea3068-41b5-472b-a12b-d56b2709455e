package pt.ptinovacao.na.portal.it.web.utils.config.loader;

import com.google.common.base.MoreObjects;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Set;

/**
 *
 * Provides type-safe assess to config files.
 *
 * Used to access the configs in a type-safe way (with auto-complete and debugging information), check for missing config files
 * and directories, warn existence of unnecessary config files.
 *
 * <AUTHOR> <<EMAIL>>
 *
 */
@SuppressWarnings("unused")
public class ConfigFile {

    public static final String CONFIG_PATH = MoreObjects.firstNonNull(System.getenv("it.web.config.dir.path"), "src/it/resources/configs");

    private static final ArrayList<ConfigFile> listOfFiles = new ArrayList<>();

    //                   ROOT CONFIG FILES
    public static final ConfigFile TEST_PROFILES          = getTestProfilesConfigFile();
    public static final ConfigFile BROWSER_PROFILES       = new ConfigFile(RootFileNames.BROWSER_PROFILES       );
    public static final ConfigFile AUTH_PROFILES          = new ConfigFile(RootFileNames.AUTH_PROFILES          );
    public static final ConfigFile TAGS_MAPPING           = new ConfigFile(RootFileNames.TAGS_MAPPING           );
    public static final ConfigFile CREDENTIALS            = new ConfigFile(RootFileNames.CREDENTIALS            );
    public static final ConfigFile USERS                  = new ConfigFile(RootFileNames.USERS                  );
    public static final ConfigFile CONFIRM_MODAL_CONTENTS = new ConfigFile(RootFileNames.CONFIRM_MODAL_CONTENTS );

    //                   ROOT CONFIG FOLDERS
    public static final ModuleConfigFiles BASE_MODULE     = new ModuleConfigFiles(FolderNames.BASE_MODULE   );

	
	public static ArrayList<ConfigFile> files(){
        return listOfFiles;
    }

    private final String name;
    private boolean loaded = false;
    private final boolean rootConfig;

    private static ConfigFile getTestProfilesConfigFile(){
        String fileName = MoreObjects.firstNonNull(System.getProperty("it.web.profiles.config.file"), RootFileNames.TEST_PROFILES);
        if(fileName.endsWith(".yml")){
            fileName = StringUtils.removeEnd(fileName, ".yml");
        }
        return new ConfigFile(fileName);
    }

    protected ConfigFile(String s) {
        name = s;
        rootConfig = true;
        listOfFiles.add(this);
    }

    protected ConfigFile(String s, String parentFolder) {
        name = parentFolder + '/' + s;
        rootConfig = false;
        listOfFiles.add(this);
    }

    public boolean equalsName(String otherName){
        return (otherName != null) && name.equals(otherName);
    }

    public String fileName(){
        return name;
    }
    public String toString(){
       return name;
    }

    public boolean isLoaded() {
        return loaded;
    }

    public void setLoaded(boolean loaded) {
        this.loaded = loaded;
    }

    public boolean isRootConfig() {
        return rootConfig;
    }

    public static HashMap<String, ModuleConfigFiles> getModulesFolderMap(){
        return ModuleConfigFiles.foldersMap();
    }

    public static Set<String> getModuleFolders(){
        return ModuleConfigFiles.foldersMap().keySet();
    }

    /**
     * The files inside a module should be declared here
     */
    public static class ModuleConfigFiles{

        private static final HashMap<String, ModuleConfigFiles> folderMap = new HashMap<>();

        public final ConfigFile WEB_ELEMENT_SELECTORS;
        public final ConfigFile FORMS_CHARACTERISTICS;
        public final ConfigFile CLICK_ACTIONS;
        public final ConfigFile MAPPINGS;
        public final ConfigFile TABLE_CONFIGS;
        public final ConfigFile NOTIFICATION_CONTENTS;
        public final ConfigFile VIEWS;
        public final ConfigFile SNAPSHOTS_MAP;
        public final String moduleName;

        public ModuleConfigFiles(String folderName) {
            folderMap.put(folderName, this);
            moduleName = folderName;
            WEB_ELEMENT_SELECTORS = newConfigFile(FolderFileNames.WEB_ELEMENT_SELECTORS);
            FORMS_CHARACTERISTICS = newConfigFile(FolderFileNames.FORMS_CHARACTERISTICS);
            TABLE_CONFIGS         = newConfigFile(FolderFileNames.TABLE_CONFIGS);
            NOTIFICATION_CONTENTS = newConfigFile(FolderFileNames.NOTIFICATION_CONTENTS);
            MAPPINGS              = newConfigFile(FolderFileNames.MAPPINGS);
            VIEWS                 = newConfigFile(FolderFileNames.VIEWS);
            SNAPSHOTS_MAP         = newConfigFile(FolderFileNames.SNAPSHOTS_MAP          );
            CLICK_ACTIONS         = newConfigFile(FolderFileNames.CLICK_ACTIONS          );

        }

        protected ConfigFile newConfigFile(String fileName){
            return new ConfigFile(fileName, moduleName);
        }

        public static HashMap<String, ModuleConfigFiles> foldersMap(){
            return folderMap;
        }
    }

    public interface FolderNames {
        String BASE_MODULE     = "base";
    }

    interface RootFileNames {
        String TEST_PROFILES          = "test_profiles_pgsql";
        String BROWSER_PROFILES       = "browser_profiles";
        String AUTH_PROFILES          = "auth_profiles";
        String TAGS_MAPPING           = "tags_mapping";
        String CONFIRM_MODAL_CONTENTS = "confirmation_modal_contents";
        String CREDENTIALS            = "credentials";
        String USERS                  = "users";

    }

    interface FolderFileNames{
        String NOTIFICATION_CONTENTS  = "notification_contents";
        String WEB_ELEMENT_SELECTORS = "web_element_selectors";
        String CLICK_ACTIONS         = "click_actions";
        String FORMS_CHARACTERISTICS = "forms_characteristic";
        String TABLE_CONFIGS         = "table_configs";
        String MAPPINGS              = "mappings";
        String VIEWS                 = "views";
        String SNAPSHOTS_MAP          = "snapshots_map";
    }
}
