package pt.ptinovacao.na.portal.it.web.utils.models.actors.formHelper.field.interactions;

import org.openqa.selenium.WebElement;
import pt.ptinovacao.na.portal.it.web.utils.models.actors.formHelper.assertion.FieldAssertionError;
import pt.ptinovacao.na.portal.it.web.utils.models.data.common.UUIDInsert;
import pt.ptinovacao.na.portal.it.web.utils.uuid.UUID_Manager;

import java.util.ArrayList;
import java.util.List;

public final class RadioField extends SelectableFieldInteraction {

    RadioField(String fieldName, String elementId, String elementIdSelector, UUIDInsert uuidInsert, UUID_Manager.UUIDContext uuidContext) {
        super(fieldName, elementId, elementIdSelector, uuidInsert, uuidContext);
    }

    @Override
    public String setFieldValue(String value) {
        WebElement radio = driver().element(container(), "form field", getElementIdSelector());
        driver().scrollToElementIfNeeded(radio);
        List<WebElement> radioOptions = driver().elements(radio, "form radio options");
        for (WebElement optionEl : radioOptions) {
            String optionElValue = optionEl.getAttribute("value");
            if (optionElValue.equalsIgnoreCase(value.replace(" ","_")) ||
                optionElValue.equalsIgnoreCase(value)) {
                    clickOption(optionEl);
                    break;
            }
        }
        return value;
    }

    @Override
    public String getFieldValue() {
        WebElement radio = driver().waitElementVisible(container(), "form field", getElementIdSelector());
        List<WebElement> radioOptions = driver().elements(radio, "form radio options");
        for (WebElement optionEl : radioOptions) {
            if (optionEl.isSelected()) {
                return optionEl.getAttribute("value");
            }
        }
        return "";
    }

    @Override
    public void checkValueExists(List<String> values) {
        List<String> fieldOptions = getFieldOptions();
        for (String option : fieldOptions) {
            if (!fieldOptions.contains(option)) {
                throw new FieldAssertionError(getFieldName(), fieldOptions.toString(), option);
            }
        }
    }

    @Override
    public void checkValueEquals(final String expectedValue) {
        String fieldValue = getFieldValue();


        if(!fieldValue.equalsIgnoreCase(expectedValue.replace(" ","_")) &&
                !fieldValue.equalsIgnoreCase(expectedValue)){
            throw new FieldAssertionError(getFieldName(),fieldValue, expectedValue);
        }
    }

    @Override
    public void checkValueEquals(List<String> values) {
        List<String> fieldOptions = getFieldOptions();
        if (fieldOptions.size() != values.size()) {
            throw new FieldAssertionError(getFieldName(), fieldOptions.toString(), values.toString());
        }
        for (String option : values) {
            if (!fieldOptions.contains(option)) {
                throw new FieldAssertionError(getFieldName(), fieldOptions.toString(), option);
            }
        }
    }

    @Override
    public void checkValueNotExists(String value) {
        try {
            checkValueEquals(value);
        } catch(FieldAssertionError ex){
            // valid assertion
        }
        throw new FieldAssertionError(getFieldName(), getFieldValue(), value);
    }

    private List<String> getFieldOptions() {
        WebElement radio = driver().element(container(), "form field", getElementIdSelector());
        List<WebElement> radioOptions = driver().elements(radio, "form radio options");
        List<String> values = new ArrayList<>();
        for (WebElement buttonValue : radioOptions) {
            values.add(buttonValue.getAttribute("value"));
        }
        return values;
    }

    private void clickOption(WebElement optionEl){
        driver().scrollToElementIfNeeded(optionEl);
        optionEl.click();
    }

}
