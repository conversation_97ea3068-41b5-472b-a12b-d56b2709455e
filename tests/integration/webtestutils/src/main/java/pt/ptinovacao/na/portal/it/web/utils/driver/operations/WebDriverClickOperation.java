package pt.ptinovacao.na.portal.it.web.utils.driver.operations;

import org.slf4j.Logger;
import org.openqa.selenium.WebDriverException;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import pt.ptinovacao.na.portal.it.web.utils.Retry;
import pt.ptinovacao.na.portal.it.web.utils.driver.ItWebDriver;
import pt.ptinovacao.na.portal.it.web.utils.driver.WebElementReference;
import pt.ptinovacao.na.portal.it.web.utils.logger.ITLogger;

import java.util.List;
import java.util.function.Supplier;
import java.util.stream.Collectors;

public class WebDriverClickOperation {

    private static final Logger LOGGER = ITLogger.getLogger(WebDriverClickOperation.class);
    private final ItWebDriver driver;
    private final int numOfRetries;
    private boolean didTryToClick;

    private WebDriverClickOperation(DriverClickOperation driver){
        this.driver = driver.getDriver();
        numOfRetries = Retry.getDefaultMaxNumOfRetries();
    }


    public static WebDriverClickOperation usingDriver(DriverClickOperation driver){
        return new WebDriverClickOperation(driver);
    }

    public WebElement click(final String elementKey, final Object... args){
        return click(() -> driver.getDriver().waitElementVisible(elementKey, args), elementKey);
    }

    public WebElement click(final WebElement parent, final String elementKey, final Object... args){
        return click(() -> driver.getDriver().waitElementVisible(parent, elementKey, args), elementKey);
    }

    public WebElement click(final WebElement target){
        return click(() -> target, target.toString());
    }

    public WebElement click(final WebElementReference target){
        return click(target::queryElement, target.getElementName());
    }
    public WebElement click(Supplier<WebElement> target, String name){
        return executeClick(target, name);
    }


    private WebElement executeClick(Supplier<WebElement> target, String name){
        final WebElement[] clicked = new WebElement[1];
        Retry.call( () -> {
            driver.scrollToElementIfNeeded(target.get());
            driver.clickValidator().prepareClick(name);
            final int defaultTimeout = driver.getDefaultTimeout();
            driver.setDefaultTimeout(Math.max(1, defaultTimeout / numOfRetries));

            try {
                return Retry.get(() ->  clicked[0] = _clickOnce(target), (Retry.RetryError e) -> {
                    Throwable throwable = e.getThrowable();
                    if (throwable instanceof WebDriverException) {
                        driver.originalDriver().getPageSource();
                        LOGGER.info("is a WebDriverException, continue");
                    } else if (throwable instanceof RuntimeException) {
                        throw (RuntimeException) throwable;
                    } else throw new RuntimeException("error trying to click " + name, throwable);

                });
            } catch (Retry.RetryException e) {
                throw new UnableToClickElementException(name, e);
            } finally {
                driver.setDefaultTimeout(defaultTimeout);
            }
        },(Retry.RetryError e) -> {}, 2);

        return clicked[0];
    }


    private WebElement _clickOnce(Supplier<WebElement> supplier){
        if(didTryToClick){
            LOGGER.info("Validating previous click happened");
            if(driver.clickValidator().didClickHappen()){
                return null;
            }
        }

        WebElement target = supplier.get();

        List<Boolean> results = Retry.get(() -> {
            Object jsResults = driver.execJS("[" +
                    "$(arguments[0]).closest('.modal').length > 0," +
                    "$(arguments[0]).closest('table').find('.overlay').length > 0," +
                    "$(arguments[0]).closest('#toast-container').length > 0," +
                    "$(arguments[0]).closest('.select2-results').length > 0" +
                    "];", target);
            if(jsResults == null){
                throw new NullPointerException();
            }
            if(!(jsResults instanceof List)){
                throw new RuntimeException("result is not a  instance of List, is a instance of " +
                        jsResults.getClass().getCanonicalName());
            }
            List<?> jsResultList = (List<?>) jsResults;
            if(!jsResultList.stream().allMatch(val -> val instanceof Boolean)){
                throw new RuntimeException("result is not a of List of booleans");
            }
            return jsResultList.stream().map(val -> (Boolean) val).collect(Collectors.toList());
        }, (e) -> {
            if(e.getThrowable() instanceof NullPointerException){
                LOGGER.info("Error executing js checks, retrying");
            } else {
                throw new RuntimeException("failed to execute script", e.getThrowable());
            }
        });



        Boolean isModalDescendant = results.get(0);
        Boolean isWaitingTableDescendant = results.get(1);
        Boolean isNotificationDescendant = results.get(2);
        Boolean isSelect2ResultsDescendant = results.get(3);

        if (Boolean.FALSE.equals(isSelect2ResultsDescendant) && Boolean.FALSE.equals(isModalDescendant)
                && Boolean.FALSE.equals(isNotificationDescendant)) {
            driver.waitElementHidden("modal backdrop");
        }
        if (Boolean.TRUE.equals(isWaitingTableDescendant)) {
            driver.waitElementHidden("table overlay");
        }
        driver.waitElementHidden("loading notification");

        LOGGER.info("Clicking for the first time");
        new WebDriverWait(driver.originalDriver(), driver.getDefaultTimeout()).ignoring(NullPointerException.class)
                .until(ExpectedConditions.elementToBeClickable(target)).click();

        didTryToClick = true;
        driver.clickValidator().validateClick();

        return target;
    }


    public static class UnableToClickElementException extends RuntimeException{
        public UnableToClickElementException(String elementName, Throwable cause){
            super("unable to click element \"" + elementName + '"', cause);
        }
    }

}
