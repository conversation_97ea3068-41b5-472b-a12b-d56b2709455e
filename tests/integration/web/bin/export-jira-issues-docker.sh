if [ -z "$JIRA_ISSUES" ] ; then
    echo 'JIRA_ISSUES is not defined.'
     exit 1
fi
echo 'Using JIRA_ISSUES: ' $JIRA_ISSUES
if [ -z "$FEATURES_DIR" ] ; then
    echo 'FEATURES_DIR is not defined.'
     exit 1
fi
echo 'Using FEATURES_DIR: ' $FEATURES_DIR

rm -r ${FEATURES_DIR}
mkdir ${FEATURES_DIR}

DOCKER_IMAGE="gestoss-dev-ci.c.ptin.corppt.com/gestoss/el7"
echo "running docker run --rm -i -v ${FEATURES_DIR}:/app/jira-features ${DOCKER_IMAGE} sh ..."
docker run --rm -i -v ${FEATURES_DIR}:/app/jira-features ${DOCKER_IMAGE} sh <<EOF
cd /app/jira-features
sed -e 's/^com.cuke.tool.jira.username=.*/com.cuke.tool.jira.username=ci-na-portal/' \
    -e 's/^com.cuke.tool.jira.password=.*/com.cuke.tool.jira.password=c1-na-portal/' \
    -e 's/^com.cuke.tool.jira.base.path=.*//' \
    /opt/xpandit/xray/conf/cuketool.properties.sample > /opt/xpandit/xray/conf/cuketool.properties
echo "run cmd: cuke -op Export -key ${JIRA_ISSUES}"
cuke -op Export -key "${JIRA_ISSUES}"
unzip *.zip
rm *.zip
chown $(id -u):$(id -g) *
EOF

