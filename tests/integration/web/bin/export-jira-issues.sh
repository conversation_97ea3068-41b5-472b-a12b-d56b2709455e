#!/bin/sh

if [ -z "$JIRA_ISSUES" ] ; then
    echo 'JIRA_ISSUES is not defined.'
     exit 1
fi
echo 'Using JIRA_ISSUES: ' $JIRA_ISSUES
if [ -z "$FEATURES_DIR" ] ; then
    echo 'FEATURES_DIR is not defined.'
     exit 1
fi
echo 'Using FEATURES_DIR: ' $FEATURES_DIR

echo "run cmd: cuke -op Export -key ${JIRA_ISSUES}"
cuke -op Export -key "${JIRA_ISSUES}"
rm -r ${FEATURES_DIR}
mkdir ${FEATURES_DIR}
mv *.zip "${FEATURES_DIR}"
cd "${FEATURES_DIR}"
unzip *.zip
rm *.zip