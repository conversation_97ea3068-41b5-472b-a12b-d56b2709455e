package pt.ptinovacao.na.portal.it.helpers.monitoring;

import org.apache.commons.lang3.StringUtils;
import org.openqa.selenium.WebElement;
import pt.ptinovacao.na.portal.it.models.timeline.Activity;
import pt.ptinovacao.na.portal.it.web.utils.driver.ItWebDriver;
import pt.ptinovacao.ssov.seleniumwrapper.ElementFinder;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

public class TimelineHelper {

    private ItWebDriver driver;
    private WebElement timeline;
    private static WebElement currentActivity;
    public interface Timeline {
        String CONTAINER = "order timeline container";
        String ACTIVITIES = "order timeline activities";
        String ACTIVITY_BY_NAME = "order timeline activity by name";
        String ACTIVITY_NAME = "order timeline activity name";
        String ACTIVITY_DURATION = "order timeline activity duration";
        String ACTIVITY_MESSAGE = "order timeline activity message";
        String ACTIVITY_NOTIFICATION_ICON = "order timeline activity notification icon";
        String ACTIVITY_DECISION_ICON = "order timeline activity decision icon";
        Map<String, String> ACTIVITY_STATUS = new HashMap<String, String>(){{
            put("success", "order timeline activity status success");
            put("waiting", "order timeline activity status waiting");
            put("error", "order timeline activity status error");
            put("pending", "order timeline activity status pending");
        }};
    }

    public WebElement setAndGetCurrentActivity(WebElement activity) {
        currentActivity = activity;
        return activity;
    }

    public WebElement setAndGetCurrentActivity(String activityName) {
        return this.setAndGetCurrentActivity(getActivityElement(activityName));
    }

    public WebElement getCurrentActivity() {
        return currentActivity;
    }

    public void checkTimeline(List<Activity> activitiesExpected) {
        Map<String, WebElement> timelineActivities = getActivitiesMap();
        checkHasSameActivitiesNumber(activitiesExpected, timelineActivities);
        checkTimelineOrder(activitiesExpected, timelineActivities);
        activitiesExpected.forEach(activity -> checkActivity(activity, timelineActivities.get(activity.getName())));
    }

    private void checkHasSameActivitiesNumber(List<Activity> activitiesExpected, Map<String, WebElement> timelineActivities){
        assertEquals("Expected " + activitiesExpected.size() + " activities but found " + timelineActivities.size(), activitiesExpected.size(), timelineActivities.size());
    }

    private void checkTimelineOrder(List<Activity> activitiesExpected, Map<String, WebElement> timelineActivities){
        ListIterator<Activity> activitiesIterator = activitiesExpected.listIterator();
        for (String activityName : timelineActivities.keySet()) {
            if (activitiesIterator.hasNext()) {
                assertEquals("Activity " + activityName + " is out of order", activityName, activitiesIterator.next().getName());
            }
        }
    }

    private void checkActivity(Activity activity, WebElement activityEl) {
        String name = activity.getName();

        if (StringUtils.isNotEmpty(activity.getSystem())) {
            name = String.format("%s (%s)", name, activity.getSystem());
        }

        if (StringUtils.isNotEmpty(activity.getDuration())) {
            driver.assertThat.element(activityEl, Timeline.ACTIVITY_DURATION, "").textEquals(activity.getDuration());
        }

        if (StringUtils.isNotEmpty(activity.getMessage())) {
            driver.assertThat.element(activityEl, Timeline.ACTIVITY_MESSAGE, "").textEquals(activity.getMessage());
        }

        driver.assertThat.element(activityEl, Timeline.ACTIVITY_NAME, "").textEquals(name);
        driver.waitElementVisible(activityEl, Timeline.ACTIVITY_STATUS.get(activity.getStatus()));

        if (activity.hasNotification()) {
            driver.waitElementVisible(activityEl, Timeline.ACTIVITY_NOTIFICATION_ICON);
        } else {
            assertElementIsNotPresent("Notification icon shoul't be present", activityEl, Timeline.ACTIVITY_NOTIFICATION_ICON);
        }

        if(StringUtils.isNotEmpty(activity.getType())) {
            String realType = activityEl.findElement(ElementFinder.instance.by(Timeline.ACTIVITY_NAME)).getAttribute("class");
            assertEquals(realType,activity.getType());
            if(activity.getType().equals("decision")) {
                try {
                    driver.waitElementVisible(activityEl, Timeline.ACTIVITY_DECISION_ICON);
                } catch (Exception e) {
                    assertElementIsNotPresent("Decision icon shoul't be present", activityEl, Timeline.ACTIVITY_DECISION_ICON);
                }
            }
        }

        ActivityActionsHelper.using(driver, activityEl).checkActions(activity.getActions());
    }

    public void openActivityModal(String activityName) {
        getActivityLinkElement(activityName).click();
        driver.wait(2); // wait for modal animation
    }

    private List<WebElement> getActivitiesElements() {
        return driver.elements(timeline, Timeline.ACTIVITIES);
    }

    public WebElement getActivityElement(String activityName) {
        return driver.element(timeline, Timeline.ACTIVITY_BY_NAME, activityName);
    }

    private WebElement getActivityLinkElement(String activityName){
        for (WebElement activityElement : driver.elements(timeline, Timeline.ACTIVITY_NAME)) {
            String name = activityElement.getText();
            if (name.equalsIgnoreCase(activityName)) {
                return activityElement;
            }
        }
        throw new RuntimeException("Activity \"" + activityName + "\" not found on timeline.");
    }

    private Map<String, WebElement> getActivitiesMap() {
        Map<String, WebElement> activities = new LinkedHashMap<>();
        for (WebElement activity : getActivitiesElements()) {
            activities.put(activity.getAttribute("data-node-name"), activity);
        }
        return activities;
    }

    private void assertElementIsNotPresent(String message, WebElement nodeEl, String elementName){
        assertTrue(message, driver.elements(nodeEl, elementName).isEmpty());
    }

    private TimelineHelper(ItWebDriver driver) {
        this.driver = driver;
    }

    public static TimelineHelper using(final ItWebDriver driver) {
        final TimelineHelper timelineHelper = new TimelineHelper(driver);
        timelineHelper.setContainer(Timeline.CONTAINER);
        return timelineHelper;
    }

    private void setContainer(final String target) {
        timeline = driver.element(target);
    }

}
