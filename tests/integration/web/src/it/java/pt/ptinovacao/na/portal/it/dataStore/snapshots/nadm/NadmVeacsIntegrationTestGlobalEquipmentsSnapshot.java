package pt.ptinovacao.na.portal.it.dataStore.snapshots.nadm;

import org.slf4j.Logger;
import pt.ptinovacao.na.portal.it.config.module.NADMConfig;
import pt.ptinovacao.na.portal.it.web.utils.dataStore.Snapshot;
import pt.ptinovacao.na.portal.it.web.utils.logger.ITLogger;
import pt.ptinovacao.nacommons.testapi.HttpRequest;

import javax.ws.rs.core.MediaType;

public class NadmVeacsIntegrationTestGlobalEquipmentsSnapshot implements Snapshot {
    protected static Logger LOGGER = ITLogger.getLogger(Snapshot.class);


    private static final String baseUrl = "http://"+ NADMConfig.getBackendHost()+":"+NADMConfig.getBackendPort();

    private static final String globalEquipmentPostUrl = baseUrl +"/management-nadm/"+NADMConfig.getVeacsDBProfile()+"/nadm/"+NADMConfig.getVeacsDBName()+"/naacs/create/globalEquipment";
    private static final String templatePostUrl = baseUrl +"/management-nadm/"+NADMConfig.getVeacsDBProfile()+"/nadm/"+NADMConfig.getVeacsDBName()+"/naacs/create/templates";
    private static final String globalEquipmentDeleteUrl = baseUrl +"/management-nadm/"+NADMConfig.getVeacsDBProfile()+"/nadm/"+NADMConfig.getVeacsDBName()+"/naacs/delete/globalEquipment";
    private static final String templateDeleteUrl = baseUrl +"/management-nadm/"+NADMConfig.getVeacsDBProfile()+"/nadm/"+NADMConfig.getVeacsDBName()+"/naacs/delete/templates";

    private static final String TEMPLATE_DATA = "<clients>\n" +
            "        <client>\n" +
            "            <name required=\"true\" type=\"id\"/>                    </client>                </clients>\n" +
            "    <equipments>\n" +
            "        <equipment label=\"Equipment\" labelKey=\"NADM.Equipment\" type=\"group\">\n" +
            "            <name label=\"MAC Address\" labelKey=\"NADM.name\" regex=\"^([a-fA-F0-9]{2}(:)){5}[a-fA-F0-9]{2}$\" regexKey=\"NADM.regex.mac\" regexLabel=\"Error: invalid MAC address\" required=\"true\" type=\"id\"/>\n" +
            "            <equipmentData>\n" +
            "                <genericPhone label=\"Generic Phone\" labelKey=\"NADM.genericPhone\" type=\"group\">\n" +
            "                    <customKeys label=\"Custom Keys\" labelKey=\"NADM.genericPhone.customKeys\" type=\"instanceGroup\">\n" +
            "                        <customKey label=\"Custom Key\" labelKey=\"NADM.genericPhone.customKeys.customKey\" maxInstances=\"5\" type=\"instance\">\n" +
            "                            <keyId label=\"Key ID\" labelKey=\"NADM.genericPhone.customKeys.customKey.keyId\" type=\"options\">\n" +
            "                                <opt>1</opt>\n" +
            "                                <opt>2</opt>\n" +
            "                                <opt>3</opt>\n" +
            "                                <opt>4</opt>\n" +
            "                                <opt>5</opt>                                        </keyId>\n" +
            "                            <line label=\"Line\" labelKey=\"NADM.genericPhone.customKeys.customKey.line\" type=\"options\">\n" +
            "                                <opt>1</opt>\n" +
            "                                <opt>2</opt>                                        </line>\n" +
            "                            <name label=\"Name\" labelKey=\"NADM.genericPhone.customKeys.customKey.name\" type=\"string\"/>\n" +
            "                            <monitor label=\"Monitor\" labelKey=\"NADM.genericPhone.customKeys.customKey.monitor\" type=\"string\"/>\n" +
            "                            <type label=\"Custom key type\" labelKey=\"NADM.genericPhone.customKeys.customKey.type\" type=\"options\">\n" +
            "                                <opt label=\"Speed Dial\" labelKey=\"customKey.type.opt.SpeedDial\">speedDial</opt>\n" +
            "                                <opt label=\"Aggregate Line\" labelKey=\"customKey.type.opt.aggregateLine\" linesOnly=\"true\">aggregateLine</opt>                                        </type>                                    </customKey>                                </customKeys>\n" +
            "                    <stationName label=\"Station Name\" labelKey=\"NADM.genericPhone.stationName\" type=\"string\"/>  " +
            "                          </genericPhone>                        </equipmentData>                    </equipment>                </equipments>";

    private String integrationTestGlobalEquipment(String manufacturer, String model, String version){
        return "<request>" +
           "<description>na-portal - create-or-delete-global-equipment</description>"+
           "<data>"+
              "<NadmRequest>"+
                   "<equipments>"+
                       "<equipment>"+
                           "<equipmentData>"+
                               "<deviceInfo>"+
                                   "<manufacturer>"+ manufacturer +"</manufacturer>"+
                                   "<modelName>"+ model +"</modelName>"+
                                   "<softwareVersion>"+ version +"</softwareVersion>"+
                               "</deviceInfo>"+
                           "</equipmentData>"+
                        "</equipment>"+
                    "</equipments>"+
                "</NadmRequest>"+
              "</data>"+
        "</request>";
    }

    private String integrationTestTemplate(String manufacturer, String model, String version){
        return "<request>" +
                "<description>na-portal - create-or-delete-global-equipment</description>"+
                "<data>"+
                "<NadmRequest>"+
                "<equipments>"+
                "<equipment>"+
                "<equipmentData>"+
                "<deviceInfo>"+
                "<manufacturer>"+ manufacturer +"</manufacturer>"+
                "<modelName>"+ model +"</modelName>"+
                "<softwareVersion>"+ version +"</softwareVersion>"+
                "</deviceInfo>"+
                "</equipmentData>"+
                "</equipment>"+
                "</equipments>"+
                "<template>" +
                TEMPLATE_DATA +
                "</template>" +
                "</NadmRequest>"+
                "</data>"+
                "</request>";
    }

    @Override
    public void appendToDB() {
        LOGGER.info("***************** Push snapshot to DB *************************");
        new HttpRequest().post(globalEquipmentPostUrl).mediaType(MediaType.APPLICATION_XML_TYPE).payload(integrationTestGlobalEquipment("IntegrationTest","IntegrationTest","toDelete")).expectedStatus(200).run();
        new HttpRequest().post(templatePostUrl).mediaType(MediaType.APPLICATION_XML_TYPE).payload(integrationTestTemplate("IntegrationTest","IntegrationTest","toDelete")).expectedStatus(200).run();
        LOGGER.info("***************** Finished snapshot push *************************");
    }

    @Override
    public void removeFromDB() {
        LOGGER.info("***************** Pop snapshot from DB *************************");
        new HttpRequest().post(globalEquipmentDeleteUrl).mediaType(MediaType.APPLICATION_XML_TYPE).payload(integrationTestGlobalEquipment("IntegrationTest","IntegrationTest","toDelete")).expectedStatus(200).run();
        new HttpRequest().post(templateDeleteUrl).mediaType(MediaType.APPLICATION_XML_TYPE).payload(integrationTestGlobalEquipment("IntegrationTest","IntegrationTest","toDelete")).expectedStatus(200).run();
        LOGGER.info("***************** Finished snapshot pop *************************");
    }
}
