package pt.ptinovacao.na.portal.it.models.graph;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class CucumberNode {
    private String name;
    private String type;
    private String id;
    private String edges;
    private String connectionsLabels;
    private String notification;
    private String status;
    private String system;
    private String actions;


    /*public CucumberNode(String name, String type, String connections, String id, String notification, String status, String system, String actions){
        new CucumberNode(name, type, connections, null, id, notification, status, system, actions);
    }*/

    public CucumberNode(String name, String type, String edges, String connectionsLabels, String id, String notification, String status, String system, String actions){
        this.name = name;
        this.type = type;
        this.id = id;
        this.edges = edges;
        this.connectionsLabels = connectionsLabels;
        this.notification = notification;
        this.status = status;
        this.system = system;
        this.actions = actions;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<String> getEdges() {
        if (!"".equals(this.edges.trim())) {
            return Arrays.asList(this.edges.trim().split("\\s*,\\s*"));
        }
        return new ArrayList<>();
    }

    public List<String> getConnectionsLabels() {
        if (this.connectionsLabels == null) {
            return new ArrayList<>();
        }
        return Arrays.asList(this.connectionsLabels.trim().split("\\s*,\\s*",-1));

    }

    public void setConnectionsLabel(String connectionsLabels) {
        this.connectionsLabels = connectionsLabels;
    }

    public void setEdges(String edges) {
        this.edges = edges;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public boolean hasNotification(){
        return notification.equals("yes");
    }

    public String getNotification() {
        return notification;
    }

    public void setNotification(String notification) {
        this.notification = notification;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSystem() {
        return system;
    }

    public void setSystem(String system) {
        this.system = system;
    }

    public String getActionsAsString() {
        return actions;
    }

    public List<String> getActions() {
        if (StringUtils.isNotEmpty(actions)) {
            return Arrays.asList(actions.trim().split("\\s*,\\s*"));
        }
        return new ArrayList<>();
    }

    public void setActions(String actions) {
        this.actions = actions;
    }

}
