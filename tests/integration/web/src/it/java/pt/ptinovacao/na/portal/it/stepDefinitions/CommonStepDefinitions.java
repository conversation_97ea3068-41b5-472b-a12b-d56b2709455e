package pt.ptinovacao.na.portal.it.stepDefinitions;

import org.slf4j.Logger;
import pt.ptinovacao.na.portal.it.driver.LazyLoadDriver;
import pt.ptinovacao.na.portal.it.driver.NAWebDriver;
import pt.ptinovacao.na.portal.it.web.utils.dataStore.Snapshot;
import pt.ptinovacao.na.portal.it.web.utils.helpers.LoginHelper;
import pt.ptinovacao.na.portal.it.web.utils.helpers.ViewHelper;
import pt.ptinovacao.na.portal.it.web.utils.logger.ITLogger;
import pt.ptinovacao.ssov.seleniumwrapper.HeadlessWrapper;

import java.util.*;


/**
 * Common Step definitions class
 * <p>
 * This class contains common variables that are shared between subclasses
 */
public abstract class CommonStepDefinitions {
    public static final HeadlessWrapper headless = new HeadlessWrapper();
    protected static final ThreadLocal<List<Snapshot>> snapshots = ThreadLocal.withInitial(ArrayList::new);
    protected static final ThreadLocal<Set<String>> _currentTestTags = ThreadLocal.withInitial(HashSet::new);

    protected static final Logger LOGGER = ITLogger.getLogger("stepDefinitions");

    protected static LazyLoadDriver driverLoader = null;

    protected static NAWebDriver driver() {
        return driverLoader.getDriver();
    }

    protected static Set<String> getCurrentTestTags() {
        return _currentTestTags.get();
    }


    protected static String getLanguage() {
        return LoginHelper.getCurrentUser().getI18n().getLanguage();
    }

}
