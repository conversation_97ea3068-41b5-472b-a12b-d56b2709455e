package pt.ptinovacao.na.portal.it.models.actors.table;

import org.openqa.selenium.WebElement;
import pt.ptinovacao.na.portal.it.stepDefinitions.CommonStepDefinitions;
import pt.ptinovacao.na.portal.it.web.utils.helpers.TagsHelper;
import pt.ptinovacao.na.portal.it.web.utils.models.actors.table.interaction.CustomColumnInteraction;
import pt.ptinovacao.na.portal.it.web.utils.models.data.table_configs.TableColumnDefinition;

public class TagsColumn extends CommonStepDefinitions implements CustomColumnInteraction.InteractionInterface  {

    @Override
    public boolean verifyCellMatch(WebElement cellElement, String cellTextToMatch, TableColumnDefinition columnDefinition) {
        if(cellTextToMatch.startsWith("*") && cellTextToMatch.endsWith("*")){
            return TagsHelper.using(driver()).contains(cellElement, cellTextToMatch.substring(1, cellTextToMatch.length() - 1));
        } else {
            return TagsHelper.using(driver()).assertIsEqual(cellElement, cellTextToMatch);
        }
    }

}