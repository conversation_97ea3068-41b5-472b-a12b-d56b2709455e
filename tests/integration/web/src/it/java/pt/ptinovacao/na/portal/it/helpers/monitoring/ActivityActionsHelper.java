package pt.ptinovacao.na.portal.it.helpers.monitoring;

import org.openqa.selenium.WebElement;
import pt.ptinovacao.na.portal.it.models.timeline.ReportItem;
import pt.ptinovacao.na.portal.it.web.utils.driver.ItWebDriver;
import pt.ptinovacao.na.portal.it.web.utils.helpers.FormHelper;
import pt.ptinovacao.na.portal.it.web.utils.helpers.ModalHelper;

import java.util.Collections;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

public class ActivityActionsHelper {

    private ItWebDriver driver;
    private Map<String, WebElement> actionElements;
    private interface Actions {
        String REPORT = "report";
        String RETRY = "retry";
        String SKIP = "skip";
        String COMPLETE = "complete";
    }

    private ActivityActionsHelper(ItWebDriver driver, Map<String, WebElement> actionElements){
        this.driver = driver;
        this.actionElements = actionElements;
    }

    public static ActivityActionsHelper using(ItWebDriver driver, WebElement context) {
        return new ActivityActionsHelper(driver, getActivityActions(driver, context));
    }

    private static Map<String, WebElement> getActivityActions(ItWebDriver driver, WebElement activityEl){
        List<WebElement> actionElements = driver.elements(activityEl, "order timeline activity actions");
        Map<String, WebElement> actionsMap = new LinkedHashMap<>();
        for (WebElement actionEl : actionElements) {
            actionsMap.put(actionEl.getAttribute("data-action"), actionEl);
        }
        return actionsMap;
    }

    public void checkActions(List<String> actionsExpected) {
        checkHasSameActionsNumber(actionsExpected);
        checkActionsOrder(actionsExpected);
    }

    public void checkReport(List<ReportItem> reportItemsExpected) {
        // open report modal
        actionElements.get(Actions.REPORT).click();

        // wait for modal and get action items
        WebElement modal = driver.waitElementVisible("modal open execution activity action");
        List<WebElement> actionItemsElements = driver.elements(modal,"order timeline activity report actions");

        // check action items
        checkHasSameActionItems(reportItemsExpected, actionItemsElements);
        checkActionItems(reportItemsExpected, actionItemsElements);
    }

    private void checkHasSameActionItems(List<ReportItem> actionItemsExpected, List<WebElement> actionItemsElements){
        assertEquals("Expected " + actionItemsExpected.size() + " action items but found " + actionItemsElements.size(), actionItemsExpected.size(), actionItemsElements.size());
    }

    private void checkActionItems(List<ReportItem> actionItemsExpected, List<WebElement> actionItemsElements){
        Iterator<WebElement> iterator = actionItemsElements.iterator();
        actionItemsExpected.forEach(actionItemExpected -> {
            if (iterator.hasNext()) {
                WebElement actionItemEl = iterator.next();
                String title = driver.element(actionItemEl, "order timeline activity report action title").getText().trim();
                String message = driver.element(actionItemEl, "order timeline activity report action notes").getText().trim();
                Pattern titlePattern = Pattern.compile(actionItemExpected.getTitle());
                assertTrue("Title \"" + title + "\" does not match with " + actionItemExpected.getTitle(), titlePattern.matcher(title).matches());
                assertEquals("Message \"" + message + "\" does not match with \"" + actionItemExpected.getMessage() + "\".", message, actionItemExpected.getMessage());
            }
        });
        assertEquals("Expected " + actionItemsExpected.size() + " action items but found " + actionItemsElements.size(), actionItemsExpected.size(), actionItemsElements.size());
    }

    public void retry(String cause) {
        actionElements.get(Actions.RETRY).click();
        submitAction(cause);
    }

    public void skip(String cause) {
        actionElements.get(Actions.SKIP).click();
        submitAction(cause);
    }

    public void complete(String cause) {
        actionElements.get(Actions.COMPLETE).click();
        submitAction(cause);
    }

    private void waitLoadingFades(){
        //driver.waitLoadingFades();
        driver.waitElementHidden(10, "order execution loading");
    }

    private void submitAction(String cause){
        List<FormHelper.FormElems> values = Collections.singletonList(new FormHelper.FormElems("message", cause));
        ModalHelper.using(driver).setModalFormValues("monitoring action modal", values).ok().waitModalDisappears();
        waitLoadingFades();
    }

    private void checkActionsOrder(List<String> actionsExpected){
        Iterator<String> actionsIterator = actionElements.keySet().iterator();
        for (String action : actionsExpected) {
            if (actionsIterator.hasNext()){
                assertEquals("Action " + action + " is out of order", action, actionsIterator.next());
            }
        }
    }

    private void checkHasSameActionsNumber(List<String> actionsExpected){
        assertEquals("Expected " + actionsExpected.size() + " actions but found " + actionElements.size(), actionsExpected.size(), actionElements.size());
    }

}
