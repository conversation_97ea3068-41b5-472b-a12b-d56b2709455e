package pt.ptinovacao.na.portal.it.driver;

import com.google.common.base.Throwables;
import cucumber.api.Scenario;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.openqa.selenium.WebDriver;
import pt.ptinovacao.na.portal.it.TestProfile;
import pt.ptinovacao.na.portal.it.config.ModuleLoader;
import pt.ptinovacao.na.portal.it.web.utils.logger.ITLogger;

import java.io.File;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Collection;
import java.util.Map;

import static pt.ptinovacao.na.portal.it.stepDefinitions.CommonStepDefinitions.headless;

public class LazyLoadDriver {
    protected static final Logger LOGGER = ITLogger.getLogger(LazyLoadDriver.class);

    private final Scenario scenario;
    private File parentFolder = null;
    private String videoFileName = null;
    private boolean isRecording = false;
    private NAWebDriver driver = null;

    public LazyLoadDriver(Scenario scenario) {
        this.scenario = scenario;
    }

    public boolean isLoaded() {
        return driver != null;
    }

    private void loadDriver() throws Exception {
        TestProfile testProfile = TestProfile.instance;
        Collection<String> tagNames = scenario.getSourceTagNames();
        Map<String, Object> profile = testProfile.getBrowserProfile();
        WebDriver webdriver = new NABrowserWrapper().startBrowser(profile);
        driver = new NAWebDriver(webdriver);
        ModuleLoader.logFoldersToLoad(scenario);
        ModuleLoader.configureScenarioElementForms(scenario);
        ModuleLoader.configureElementSelectors(scenario, driver);
        ModuleLoader.configureElementClickValidators(scenario, driver);
        ModuleLoader.configureTables(scenario);
        ModuleLoader.configureNotifications(scenario);
        ModuleLoader.configureMappings(scenario);
        ModuleLoader.configureViews(scenario, driver);

        boolean toRecord = headless.getDisplayNumber() > 0 && testProfile.isRecordEnabled()
                && !tagNames.contains("@norecord");

        if (toRecord) {
            if (parentFolder == null) {
                DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd-HH:mm");
                Calendar cal = Calendar.getInstance();
                String timestamp = dateFormat.format(cal.getTime());//2014/08/06 16:00
                parentFolder = new File(TestProfile.instance.recordPath(), "na-it-web-videos/" + timestamp + "/");
            }
            String id = scenario.getId();
            String scFeature = id.substring(0, id.indexOf(';'));
            String scName = id.substring(id.indexOf(';') + 1);
            File folder = new File(parentFolder, scFeature);
            FileUtils.forceMkdir(folder);

            ProcessBuilder pb = new ProcessBuilder("unclutter", "-idle", "0");
            Map<String, String> env = pb.environment();
            env.put("DISPLAY", ":" + headless.getDisplayNumber());
            pb.start();


            videoFileName = String.format("%s/%s.mp4", folder.getAbsolutePath(), scName);
            LOGGER.debug("recording to " + videoFileName);
            headless.captureCurrentDisplay(videoFileName);
            isRecording = true;
        }

        driver.setDefaultTimeout((Integer) profile.get("timeout"));
        TestProfile.logTimeZones(driver);
        webdriver.get(testProfile.getUrl());
    }

    public Scenario getScenario() {
        return scenario;
    }

    public String getVideoFileName() {
        return videoFileName;
    }

    public boolean isRecordingVideo() {
        return isRecording;
    }

    public NAWebDriver getDriver() {
        if (!isLoaded()) {
            try {
                loadDriver();
            } catch (Exception e) {
                throw Throwables.propagate(e);
            }
        }
        return driver;
    }

}
