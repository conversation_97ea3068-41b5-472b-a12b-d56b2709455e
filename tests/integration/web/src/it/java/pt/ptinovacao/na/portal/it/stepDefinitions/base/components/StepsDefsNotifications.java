package pt.ptinovacao.na.portal.it.stepDefinitions.base.components;

import cucumber.api.java.en.Then;
import pt.ptinovacao.na.portal.it.web.utils.helpers.NotificationHelper;
import pt.ptinovacao.na.portal.it.stepDefinitions.CommonStepDefinitions;

import java.util.List;


/**
 * Step definitions related interaction with the application's notifications.
 */
public class StepsDefsNotifications extends CommonStepDefinitions {

    @Then("^an? \"(.+)\" error notification should appear")
    public static void an_error_notification_should_appear(String notificationName) {
        NotificationHelper.using(driver())
                .grabContentFromConfig(notificationName)
                .compareNotificationToConfig()
                .closeNotification();
    }


    @Then("^an? \"(.+)\" error notification with single button with params should appear")
    public static void an_error_notification_with_single_button_with_params_should_appear(String notificationName, List<NotificationHelper.ParamValue> values) {
        NotificationHelper.using(driver())
            .notificationHasSingleButton()
            .grabContentFromConfig(notificationName)
            .compareNotificationToConfig(values);
    }

    @Then("^an? \"(.+)\" alert notification should appear")
    public static void an_alert_notification_should_appear(String notificationName) {
        NotificationHelper.using(driver())
                .grabContentFromConfig(notificationName)
                .compareAlertToConfig()
                .closeNotification();
    }


    @Then("^an? \"(.+)\" success notification should appear")
    public static void an_success_notification_should_appear(String notificationName) {
        NotificationHelper.using(driver()).verifySuccessNotificationIfVisible(notificationName);
    }

    @Then("^an? \"(.+)\" success notification with params should appear:$")
    public static void an_success_notification_with_params_should_appear(String notificationName, List<NotificationHelper.ParamValue> values) {
        NotificationHelper.using(driver()).verifySuccessNotificationIfVisible(notificationName, values);
    }

    @Then("^an? \"(.+)\" info notification should appear")
    public static void an_info_notification_should_appear(String notificationName) {
        NotificationHelper.using(driver()).verifyInfoNotificationIfVisible(notificationName);
    }

    @Then("^no error notification should appear")
    public static void no_notification_should_appear() {
        NotificationHelper.using(driver()).expectNoErrorNotification();
    }

    @Then("^no error notification is visible")
    public static void no_notification_is_visible() {
        NotificationHelper.using(driver()).expectNoErrorNotification();
    }

}
