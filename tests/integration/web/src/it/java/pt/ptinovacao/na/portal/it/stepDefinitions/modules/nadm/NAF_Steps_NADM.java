package pt.ptinovacao.na.portal.it.stepDefinitions.modules.nadm;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import cucumber.api.java.en.And;
import cucumber.api.java.en.Then;
import cucumber.api.java.en.When;
import org.junit.Assert;
import org.openqa.selenium.TimeoutException;
import org.openqa.selenium.WebElement;
import pt.ptinovacao.na.portal.it.helpers.go.OptionalFieldsHelper;
import pt.ptinovacao.na.portal.it.stepDefinitions.CommonStepDefinitions;
import pt.ptinovacao.na.portal.it.stepDefinitions.base.StepDefsBasic;
import pt.ptinovacao.na.portal.it.stepDefinitions.base.components.StepDefsSelect2;
import pt.ptinovacao.na.portal.it.stepDefinitions.base.components.StepsDefsForm;
import pt.ptinovacao.na.portal.it.web.utils.Retry;
import pt.ptinovacao.na.portal.it.web.utils.downloader.FileDownloader;
import pt.ptinovacao.na.portal.it.web.utils.downloader.LocalFileReader;
import pt.ptinovacao.na.portal.it.web.utils.helpers.*;
import pt.ptinovacao.na.portal.it.web.utils.uuid.UUID_Manager;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;


/**
 * <AUTHOR> Gonçalves <<EMAIL>>, 14-07-2016.
 */
public class NAF_Steps_NADM extends CommonStepDefinitions {


    public static final String VEACS_FORM_SUFIX = " page search";
    public static final String VEACS_NAVIGATION_BUTTON_PREFIX = "nadm navigation sidebar ";
    public static final String VEACS_ASSC_PREFIX = "nadm equipment association ";
    public static final String VEACS_PAGE_PREFIX = "veacs ";

    public static final String BUTTON_CHARACTERIZE = "characterize button";
    public static final String BUTTON_CREATE = "create button";

    private static List<String> rowInfo;
    private static String filePath;


    @When("^(?:I )?search for the following \"(global equipment|equipment|client|service|client equipment)\":$")
    public void iSearchForTheFollowingMMV(String pageName, List<FormHelper.FormElems> values) throws Throwable {
        driver().waitLoadingFades();
        driver().click("clear form button");
        StepsDefsForm.set_form(pageName + VEACS_FORM_SUFIX, values);
        driver().click("search button");
    }

    @When("^(?:I )?contextualize for the following \"(equipment characteristics|equipment characteristics modal|service characteristics)\":$")
    public void iCharacterize(String pageName, List<FormHelper.FormElems> values) throws Throwable {
        StepsDefsForm.set_form(pageName + VEACS_FORM_SUFIX, values);
        driver().click(BUTTON_CHARACTERIZE);
    }


    @When("^(?:I )?view NADM client \"([^\"]*)\"$")
    public void iViewNADMClient(String clientId) throws Throwable {
        final String pageName = "client";
        Retry.call(() -> {
            driver().click("nadm navigation sidebar client");
            try {
                driver().waitElementVisible(4, "modal");
                ModalHelper.using(driver()).ok();
            } catch (TimeoutException e) {
                //ignore
            }
            ViewHelper.using(driver()).checkOnView(VEACS_PAGE_PREFIX + pageName);
        });

        FormHelper.using(driver()).setForm(pageName + VEACS_FORM_SUFIX).setField("client",clientId).submit();
        List<Map<String, String>> mapList = Lists.newArrayList();
        mapList.add(ImmutableMap.of(
                "client", clientId,
                "action", "view (enabled)"
        ));
        TableHelper.using(driver()).setTable(pageName).matchesMapList(mapList).clickAction("view", 0);
    }

    @When("^(?:I )?set NADM service edit form to:$")
    public void isetNADMservice_edit_form(List<FormHelper.FormElems> values) throws Throwable {
        UUID_Manager.onContextRun(UUID_Manager.UUIDContext.GENERATE, () -> {
            FormHelper.using(driver()).setForm("service page edit").setFields(values);
        });
    }

    @When("^(?:I )?set NADM create client form to:$")
    public void isetNADMClient_create_form(List<FormHelper.FormElems> values) throws Throwable {
        UUID_Manager.onContextRun(UUID_Manager.UUIDContext.GENERATE, () -> {
            FormHelper.using(driver()).setForm("client create").setFields(values);
        });
    }

    @When("^(?:I )?clean NADM create client form:$")
    public void isetNADMClient_clean_create_form(List<FormHelper.FormElems> values) throws Throwable {
        FormHelper.using(driver()).setForm("client create").setFields(values);
    }

    @When("^I set optional fields \"([^\"]*)\"$")
    public void set_optional_fields(String values){
        OptionalFieldsHelper.chooseOptionalFields(values);
    }

    @When("^I choose optional fields \"([^\"]*)\"$")
    public void choose_optional_fields(String values){
        OptionalFieldsHelper.chooseOptionalFieldsAndSubmit(values);
    }
    
    @When("^(?:I )?go to search page of NADM \"(global equipment|equipment|client|service)\"$")
    public void iGoToSearchPageOf(final String pageName) throws Throwable {
        Retry.call(() -> {
            driver().click(VEACS_NAVIGATION_BUTTON_PREFIX + pageName);
            driver().waitLoadingFades();
            ViewHelper.using(driver()).checkOnView(VEACS_PAGE_PREFIX + pageName);
        },(Retry.RetryError e) -> {
            //ignore
        }, 3);
    }

    @Then("^the results of the \"(global equipment|equipment|client|service|client equipment)\" search should have the following values:$")
    public void theResultsOfTheMMVSearchShouldHaveTheFollowingValues(String pageName, List<Map<String,String>> values) throws Throwable {
        TableHelper.using(driver()).setTable(pageName).matchesMapList(values);
    }

    @When("^(?:I )?select MMV \"(manufacturer|model|version)\" to value \"([^\"]*)\"$")
    public void  iSelectMMVToValue(String selectedMMV, String value) throws Throwable {
        StepDefsSelect2.multiselectValueOf(selectedMMV, value);
    }

    @Then("^(?:I )?expect the MMV \"(manufacturer|model|version)\" listing to be \"([^\"]*)\"$")
    public void iExpectTheMMVListingToBe(String expectedMMV, List<String> values) throws Throwable {
        StepDefsSelect2.multiSelectBoxHaveValues(expectedMMV, values);
    }

    @Then("^The association \"(voice profile details|custom key details)\" modal should have the following values:$")
    public void The_association_modal_should_have_the_following_values(String type, List<Map<String,String>> values)  throws Throwable {
        driver().wait(1); //// FIXME: 29-11-2016 Como a modal é carregada via JS no momento de leitura esta deve estar temporariamente vazia.
        for (Map<String, String> stringMap : values) {
            for (Map.Entry<String, String> stringEntry : stringMap.entrySet()) {
                driver().assertThat.element(VEACS_ASSC_PREFIX  + type + " " +stringEntry.getKey()).containsText(stringEntry.getValue());
            }
        }
    }

    @Then("^The association \"(custom key)\" field \"([^\"]*)\" should have the following options: \"([^\"]*)\"$")
    public void The_association_modal_should_have_the_following_options(String type, String field, String values)  throws Throwable {
        List<FormHelper.FormElems> elems= new ArrayList<>();
        elems.add(new FormHelper.FormElems(field, values));
        FormHelper.using(driver()).setForm("equipment association edit "+type).checkFieldContainsOptions(elems);
    }

    @Then("^The association \"(custom key)\" field \"([^\"]*)\" should have options \"([^\"]*)\" when field \"([^\"]*)\" have any of the following values: \"([^\"]*)\"$")
    public void The_association_modal_should_have_options(String type, String field, String values, String dependencyField, List<String> dependencyFieldValues)  throws Throwable {
        List<FormHelper.FormElems> elems= new ArrayList<>();
        elems.add(new FormHelper.FormElems(field, values));
        final FormHelper formHelper = FormHelper.using(driver()).setForm("equipment association edit " + type);
        for(String val:dependencyFieldValues){
            formHelper.setField(new FormHelper.FormElems(dependencyField, val)).checkFieldContainsOptions(elems);
        }

    }

    @When("^I delete first NADM equipment")
    public void I_clone_the_operation_created() throws Throwable {
        TableHelper.using(driver()).setTable("equipment").clickAction(SearchHelper.Action.REMOVE.value(), 0);
        ModalHelper.using(driver()).ok();
    }
    @When("^I delete first \"(global equipment|equipment|client|service|client equipment)\"")
    public void I_delete_the_first_equipment_listed(String pageName) throws Throwable {
        TableHelper.using(driver()).setTable(pageName).clickAction(SearchHelper.Action.REMOVE.value(), 0);
        ModalHelper.using(driver()).ok();
    }

    @When("^I view first NADM (global equipment|equipment|client|service|client equipment)")
    public void I_view_first_equipment_listed(String pageName) throws Throwable {
        TableHelper.using(driver()).setTable(pageName).clickAction(SearchHelper.Action.VIEW.value(), 0);
    }

    @When("^I delete a NADM \"(global equipment|equipment|client|service|client equipment)\" resource")
    public void I_delete_an_NADM_equipment(String resourceName) throws Throwable {
        int row = 0;
        final TableHelper tableHelper = TableHelper.using(driver()).grabTableInfo();
        rowInfo = tableHelper.getTableInfo().row(row);
        LOGGER.info("###ROW-INFO"+rowInfo);
        tableHelper.clickAction("remove", row);
        ModalHelper.using(driver()).ok();
    }

    @And("^the NADM (equipment|client|service|client equipment) is removed successfully$")
    public void the_NADM_equipment_is_removed_successfully(String resourceName) {
        final FormHelper formHelper = FormHelper.using(driver()).setForm(resourceName + VEACS_FORM_SUFIX);
        int idColumnIdx = 0;
        switch (resourceName){
            case "equipment":
                idColumnIdx = 1;
                formHelper.setField("identifier", rowInfo.get(idColumnIdx)).submit();
                break;
            case "client":
                formHelper.setField("client", rowInfo.get(idColumnIdx)).submit();
                break;
            case "service":
                formHelper.setField("service", rowInfo.get(idColumnIdx)).submit();
                break;
        }
        TableHelper.using(driver()).assertEmpty();
        // Express the Regexp above with the code you wish you had
        //throw new PendingException();
    }

    @And("^the NADM equipment has tags \"(.*)\"")
    public void the_NADM_equipment_has_tags(String tags) throws Throwable {
        final WebElement tagsElement = driver().waitElementVisible("global equipment details tags completed");
        driver().originalDriver().getPageSource();
        TagsHelper.using(driver()).contains(tagsElement, tags);
    }

    @And("^the NADM equipment has no tags")
    public void the_NADM_equipment_has_no_tags() throws Throwable {
        final WebElement tagsElement = driver().waitElementVisible("global equipment details tags completed");
        driver().originalDriver().getPageSource();
        TagsHelper.using(driver()).assertIsEqual(tagsElement, "");
    }

    @Then("^the NADM \"(service)\" detail should have the following .+:$")
    public void I_should_see_following_characteristics(String resourceName, List<FormHelper.FormElems> elements) {
        driver().waitElementVisible("nadm service characteristics tab");

        for (FormHelper.FormElems elem : elements) {
            StepDefsBasic.element_content_is("nadm view service characteristics " + elem.field, elem.value);
        }
    }

    @Then("^the NADM save button is disabled")
    public void the_NADM_save_button_is_disabled() {
        StepDefsBasic.element_is_disabled("save button");
    }

    @Then("^the NADM characterize button is disabled")
    public void the_NADM_characterize_button_is_disabled() {
        StepDefsBasic.element_is_disabled(BUTTON_CHARACTERIZE);
    }

    @Then("^the NADM create button is not present")
    public void the_NADM_create_button_is_absent() {
        StepDefsBasic.element_is_absent(BUTTON_CREATE);
    }

    @Then("^the NADM create client equipment button is not present")
    public void the_NADM_create_client_equipment_button_is_absent() {
        StepDefsBasic.element_is_absent("create client equipment button");
    }

    //Associations steps:
    
    @When("^I search for just an association named \"(.*)\"$")
    public void I_search_for_an_association_named(String name){
        Retry.call( () -> {
            driver().click("associations tab");
        }, (Retry.RetryError e) -> {}, 2);
        FormHelper.using(driver()).setForm("equipment association search")
                .setField("name",name)
                .checkFieldIsDisabled("type");
    }

    @When("^I search for an association named \"(.*)\" of type \"(.*)\"$")
    public void I_search_for_an_association_named_of_type(String name, String type){
        Retry.call( () -> {
            driver().click("associations tab");
        }, (Retry.RetryError e) -> {}, 2);
        FormHelper.using(driver()).setForm("equipment association search")
                .setField("name",name)
                .setField("type",type);
    }

    @When("^The following associations should be listed:$")
    public void  The_following_associations_should_be_listed(List<Map<String,String>> elements){
        TableHelper.using(driver()).setTable("equipment association").matchesMapList(elements);

    }

    @When("^The following associations should be listed after row (\\d):$")
    public void  The_following_associations_should_be_listed( int rowIndex, List<Map<String,String>> elements){
        TableHelper.using(driver()).setTable("equipment association").matchesMapList(elements,rowIndex);

    }

    @When("^I remove the association in line 1$")
    public void I_remove_the_association_in_line(){
        TableHelper.using(driver()).setTable("equipment association").clickAction("remove", "action", 0);
        driver().click("modal button ok");
    }

    @When("^I remove the association in line (\\d)$")
    public void I_remove_the_association_in_line(int rowLine){
        TableHelper.using(driver()).setTable("equipment association").clickAction("remove", "action", rowLine - 1);
        driver().click("modal button ok");
    }


    @When("I create in the first row a Voice Profile with the service \"(.*)\"$")
    public void I_create_in_the_first_row_with_the_service(String service){
        FormHelper.using(driver()).setForm("equipment association select type").setField("line", "1");
        driver().wait(1);
        FormHelper.using(driver()).setForm("equipment association wizard search service").setField("search", service);
        driver().click("wizard button search service");
        driver().wait(1);
        driver().click("wizard search service first row radio");
        driver().wait(5);
        ModalHelper.using(driver()).ok("modal button ok");
    }

    @When("I create in the 17 row a Hsi with the service \"(.*)\"$")
    public void I_create_hsi_row_with_the_service(String service){
        FormHelper.using(driver()).setForm("equipment association select type").setField("line", "17");
        driver().wait(1);
        FormHelper.using(driver()).setForm("equipment association wizard search service").setField("search", service);
        driver().click("wizard button search service");
        driver().wait(1);
        driver().click("wizard search service first row radio");
        driver().wait(5);
        ModalHelper.using(driver()).ok("modal button ok");
    }

    @When("I create a Custom Key with the following values:$")
    public void I_create_a_with_the_service(List<FormHelper.FormElems> elements){
        FormHelper.using(driver()).setForm("equipment association edit custom key").setFields(elements);
        driver().wait(1);
        driver().click("modal button ok");
    }

    @Then("^an? notification of successful removal of the \"(.+)\" (equipment|client|service|client equipment) should appear")
    public static void an_success_notification_with_params_should_appear(String name, String type){
        String notificationName = "";

        switch (type) {
            case "equipment" : {
                notificationName = "NADM equipment removed";
                break;
            }
            case "service" : {
                notificationName = "NADM service removed";
                break;
            }
            case "client" : {
                notificationName = "NADM client removed";
                break;
            }
            case "client equipment" : {
                notificationName = "client equipment removed";
                break;
            }
        }

        NotificationHelper.using(driver()).verifySuccessNotificationIfVisible(notificationName,
                Collections.singletonList(new NotificationHelper.ParamValue("name", UUID_Manager.getUniqueNameOrName(name, UUID_Manager.UUIDConversion.ALPHANUMERIC))));
    }

    @When("^I hide columns \"([^\"]*)\" of nadm table \"([^\"]*)\"$")
    public void iHideColumnsOfNadmTable(List<String> columns, String tableName) {
        TableHelper.using(driver()).setTable(tableName).hideColumnsNew(columns);
    }

    @Then("^nadm table columns \"([^\"]*)\" are visible$")
    public void nadmTableColumnsAreVisible(List<String> columns)  {
        TableHelper.using(driver()).checkColumnsVisible(columns);
    }

    @Then("^nadm table columns \"([^\"]*)\" are hidden$")
    public void nadmTableColumnsAreHidden(List<String> columns) {
        TableHelper.using(driver()).checkColumnsHidden(columns);
    }

    private void exportNadmEquipmentsToCsv(String buttonToExport){
        WebElement downloadLink = driver().waitElementVisible(buttonToExport);
        boolean fileExists = false;

        if (downloadLink != null) {
            filePath = downloadFile(downloadLink);
            if(filePath != null && !filePath.isEmpty()) {
                LOGGER.info("downloaded to filepath: " + filePath);
                File fileToOpen = new File(filePath);
                fileExists = fileToOpen.exists() && fileToOpen.length() > 0;
            } else {
                LOGGER.warn("Could not download link: " + downloadLink);
            }
        }
        assertTrue(fileExists);
    }

    @When("^I export all nadm equipments to csv$")
    public void iExportAllNadmEquipmentsToCsv() {
        String buttonToExport = "nadm equipment export all to csv";
        exportNadmEquipmentsToCsv(buttonToExport);
    }

    private String downloadFile(WebElement downloadLink) {
        FileDownloader downloader = new FileDownloader(driver().originalDriver().manage().getCookies());
        try {
            return downloader.fileDownloader(downloadLink);
        } catch (Exception e) {
            throw new RuntimeException("Error downloading from link element", e);
        }
    }


    @Then("^all values of (\\d+)(?:st|nd|rd|th) column of donwloaded csv matches \"([^\"]*)\"$")
    public void allValuesOfNdColumnOfDonwloadedCsvMatches(int columnNumber, String columnValue) {
        LocalFileReader fileReader = new LocalFileReader();
        List<List<String>> downloadedTable = fileReader.readCSVFromPath(filePath);
        for(int currentLineNo = 0, e = downloadedTable.size(); currentLineNo < e; ++currentLineNo){
            List<String> line = downloadedTable.get(currentLineNo);
            if(!line.get(columnNumber - 1).equals(columnValue)) {
                throw new RuntimeException(String.format("line no %d, content '%s' does not match %s on column %d",
                        currentLineNo + 1, String.join(";", line), columnValue, columnNumber
                ));
            }
        }
    }

    @When("^I export massively all ([^ ]* )?nadm equipments to csv$")
    public void iExportMassivelyAllNadmEquipmentsToCsv(String exportFilter) {
        TableHelper.using(driver()).selectAllRows();
        boolean exportAll = exportFilter == null;
        if(exportAll) driver().click("nadm select all equipments from filter button");
        String buttonToExport = "nadm bulk operation dropdown action export to csv";
        driver().waitElementVisible("nadm bulk operation button").click();
        exportNadmEquipmentsToCsv(buttonToExport);
    }

    @When("^I export massively all nadm equipments to csv except (\\d+)(?:st|nd|rd|th) row$")
    public void iExportMassivelyAllNadmEquipmentsToCsvExcept(int rowNumber) {
        TableHelper.using(driver()).selectAllRows();
        driver().click("nadm select all equipments from filter button");
        TableHelper.using(driver()).selectRow(rowNumber-1);
        String buttonToExport = "nadm bulk operation dropdown action export to csv";
        driver().click("nadm bulk operation button");
        exportNadmEquipmentsToCsv(buttonToExport);
    }

    @When("^I show columns \"([^\"]*)\" of nadm table \"([^\"]*)\"$")
    public void iShowColumnsOfNadmTable(List<String> columns, String tableName) {
        TableHelper.using(driver()).setTable(tableName).showColumnsNew(columns);
    }

    @Then("^The (\\d+)(?:st|nd|rd|th) row of downloaded csv matches (\\d+)(?:st|nd|rd|th) row of nadm equipments table$")
    public void theRowOfDownloadedCsvMatchesRowOfNadmEquipmentsTable(int csvRowNumber, int tableRowNumber) {
        Map<String,String> row = TableHelper.using(driver()).grabTableInfo().getLineData(tableRowNumber-1);
        LocalFileReader fileReader = new LocalFileReader();
        List<String> new_row = new ArrayList<>(row.values());
        assertTrue(fileReader.externalRowMatchesCSVRow(filePath,new_row,csvRowNumber));
    }

    @And("^The number of lines of the downloaded csv file are equal to the number of selected rows$")
    public void theNumberOfLinesOfTheDownloadedCsvFileAreEqualToTheNumberOfSelectedRows() {
        int totalSelectedRecords = Integer.parseInt(driver().waitElementVisible("nadm equipment total records selected").getText());
        LocalFileReader fileReader = new LocalFileReader();
        int totalLinesCSV = fileReader.readCSVFromPath(filePath).size();
        assertEquals(totalLinesCSV, totalSelectedRecords);
    }

    @When("^I (?:select|unselect) all records$")
    public void iSelectAllRecords() {
        driver().click("nadm select all equipments from filter button");
    }

    @Then("^The firmwareFilename is equal to \"([^\"]*)\"$")
    public void theFirmwareFilenameIsEqualTo(String firmwareFilename)  {
        Retry.call(() -> {
            Assert.assertEquals(driver()
                    .element("nadm global equipment view firmwareFilename").getText(), "Ficheiro de firmware: " + firmwareFilename);
        }, (Retry.RetryError e) -> {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException ex) {
                ex.printStackTrace();
            }
        }, 2);
    }

    @When("^I click (\\d+)st row action \"([^\"]*)\" for upload file \"([^\"]*)\"$")
    public void iClickStRowActionForUploadFile(int rowNumber, String actionName, String fileName)  {
        TableHelper.using(driver()).clickAction(actionName, rowNumber - 1);
        FileUpload.using(driver()).uploadFileFromResource(fileName, "update firmware file button");
    }
}

