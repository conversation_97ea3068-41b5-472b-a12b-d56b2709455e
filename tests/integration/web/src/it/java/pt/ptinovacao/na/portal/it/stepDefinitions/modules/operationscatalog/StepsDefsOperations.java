package pt.ptinovacao.na.portal.it.stepDefinitions.modules.operationscatalog;

import cucumber.api.java.en.Given;
import cucumber.api.java.en.Then;
import cucumber.api.java.en.When;
import org.openqa.selenium.WebElement;
import pt.ptinovacao.na.operations.catalog.versions.manager.scenario.oc.OCVersionState;
import pt.ptinovacao.na.portal.it.TestProfile;
import pt.ptinovacao.na.portal.it.dataStore.snapshots.operationscatalog.OCScenario;
import pt.ptinovacao.na.portal.it.dataStore.snapshots.operationscatalog.OCScenarioWithProductionEntityVersion;
import pt.ptinovacao.na.portal.it.dataStore.snapshots.operationscatalog.OCScenarioWithTwoEntityVersions;
import pt.ptinovacao.na.portal.it.dataStore.snapshots.operationscatalog.OCScenarioWithoutEntityVersion;
import pt.ptinovacao.na.portal.it.stepDefinitions.CommonStepDefinitions;
import pt.ptinovacao.na.portal.it.stepDefinitions.base.actions.StepsDefsPageNavigation;
import pt.ptinovacao.na.portal.it.stepDefinitions.base.components.StepsDefsForm;
import pt.ptinovacao.na.portal.it.web.utils.Mappings;
import pt.ptinovacao.na.portal.it.web.utils.helpers.FormHelper;
import pt.ptinovacao.na.portal.it.web.utils.helpers.LoginHelper;
import pt.ptinovacao.na.portal.it.web.utils.helpers.ModalHelper;
import pt.ptinovacao.na.portal.it.web.utils.helpers.TableHelper;
import pt.ptinovacao.na.portal.it.web.utils.helpers.TagsHelper;
import pt.ptinovacao.na.portal.it.web.utils.helpers.ViewHelper;
import pt.ptinovacao.na.portal.it.web.utils.helpers.select.Select2;
import pt.ptinovacao.na.portal.it.web.utils.uuid.UUID_Manager;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Step definitions specific to Operations Catalog that have no good use cases to be used in another module
 */
public class StepsDefsOperations extends CommonStepDefinitions {

    private List<Map<String, String>> previouslyCheckedInputTable;
    private List<Map<String, String>> previouslyCheckedOutputTable;

    @When("^(?:I )go to (cfs|rfs|resource) create page$")
    public void create_cfs(String operationType) throws Throwable {
        driver().click("new operation button");
        switch (operationType.toUpperCase()) {
            case "CFS": {
                ViewHelper.using(driver()).checkOnView(Cfs.CREATE_PAGE);
                break;
            }
            case "RFS": {
                ViewHelper.using(driver()).checkOnView(Rfs.CREATE_PAGE);
                break;
            }
            case "RESOURCE": {
                ViewHelper.using(driver()).checkOnView(Resource.CREATE_PAGE);
                break;
            }
        }
    }

    @When("^(?:I )go to (cfs|rfs|resources) search page$")
    public void gotosearchOperation(String operationType) throws Throwable {
        driver().click("navigation tab " + operationType);
    }

    @When("^(?:I )edit first (cfs|rfs|resource)")
    public void edit_first_operation(String operationType) throws Throwable {
        switch (operationType.toUpperCase()) {
            case "CFS": {
                TableHelper.using(driver()).setTable(Cfs.SEARCH_TABLE).clickAction(TableActions.EDIT, 0);
                ViewHelper.using(driver()).checkOnView(Cfs.EDIT_PAGE);
                break;
            }
            case "RFS": {
                TableHelper.using(driver()).setTable(Rfs.SEARCH_TABLE).clickAction(TableActions.EDIT, 0);
                ViewHelper.using(driver()).checkOnView(Rfs.EDIT_PAGE);
                break;
            }
            case "RESOURCE": {
                TableHelper.using(driver()).setTable(Resource.SEARCH_TABLE).clickAction(TableActions.EDIT, 0);
                ViewHelper.using(driver()).checkOnView(Resource.EDIT_PAGE);
                break;
            }
        }
    }

    @When("^(?:I )view first (cfs|rfs|resource)")
    public void view_first_operation(String operationType) throws Throwable {
        switch (operationType.toUpperCase()) {
            case "CFS": {
                TableHelper.using(driver()).setTable(Cfs.SEARCH_TABLE).clickAction(TableActions.VIEW, 0);
                ViewHelper.using(driver()).checkOnView(Cfs.VIEW_PAGE);
                break;
            }
            case "RFS": {
                TableHelper.using(driver()).setTable(Rfs.SEARCH_TABLE).clickAction(TableActions.VIEW, 0);
                ViewHelper.using(driver()).checkOnView(Rfs.VIEW_PAGE);
                break;
            }
            case "RESOURCE": {
                TableHelper.using(driver()).setTable(Resource.SEARCH_TABLE).clickAction(TableActions.VIEW, 0);
                ViewHelper.using(driver()).checkOnView(Resource.VIEW_PAGE);
                break;
            }
        }
    }

    @When("^(?:I )delete first input attribute$")
    public void delete_input_attribute() throws Throwable {
        TableHelper.using(driver()).setTable(Operation.TABLE_INPUT_ATTRIBUTES).clickAction(TableActions.DELETE, 0);
    }

    @When("^(?:I )delete input attribute \"([^\"]*)\"$")
    public void delete_input_attribute(String attributeName) throws Throwable {
        driver().click("delete named input attribute button", attributeName);
    }

    @When("^(?:I )delete first output attribute$")
    public void delete_output_attribute() throws Throwable {
        TableHelper.using(driver()).setTable(Operation.TABLE_OUTPUT_ATTRIBUTES).clickAction(TableActions.DELETE, 0);
    }

    @When("^(?:I )delete output attribute \"([^\"]*)\"$")
    public void delete_output_attribute(String attributeName) throws Throwable {
        WebElement deleteButton = driver().waitElementVisible("delete named output attribute button", attributeName);
        driver().scrollToElementIfNeeded(deleteButton);
        deleteButton.click();
    }

    @When("^(?:I )edit first input attribute with the following values:$")
    public void edit_input_attribute(List<FormHelper.FormElems> values) throws Throwable {
        TableHelper.using(driver()).setTable(Operation.TABLE_INPUT_ATTRIBUTES).clickAction(TableActions.EDIT, 0);
        StepsDefsForm.submit_form(Operation.FORM_INPUT_ATTRIBUTES, values);
        driver().waitLoadingFades();
    }

    @When("^(?:I )edit the following values of input attribute \"([^\"]*)\":$")
    public void edit_input_attribute(String name, List<FormHelper.FormElems> values) throws Throwable {
        driver().click("edit named input attribute button", name);
        StepsDefsForm.submit_form(Operation.FORM_INPUT_ATTRIBUTES, values);
    }

    private List<Map<String, String>> cleanAttributeValues(List<Map<String, String>> table) {
        return table.stream().map(row -> row.entrySet().stream().collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> {
                    if (entry.getKey().equals("validation rule")) {

                        final ArrayList<String> arrayList = new ArrayList<>();

                        Arrays.asList(entry.getValue().split(", ")).forEach(val -> {
                            if (arrayList.isEmpty()) {
                                arrayList.add(val);
                                return;
                            }
                            String lastValue = arrayList.get(arrayList.size() - 1);
                            if (lastValue.chars().filter(ch -> ch == '(').count() > lastValue.chars().filter(ch -> ch == ')').count()) {
                                arrayList.set(arrayList.size() - 1, lastValue + ", " + val);
                            } else {
                                arrayList.add(val);
                            }
                        });


                        return arrayList.stream().map(validationRule -> {
                            if (validationRule.contains("(")) {
                                return Pattern.quote(validationRule);
                            } else if (validationRule.trim().equals("--")) {
                                return Pattern.quote(validationRule);
                            } else {
                                return validationRule + "\\(.*\\)";
                            }
                        }).collect(Collectors.joining(", "));

                    } else {
                        return Pattern.quote(entry.getValue());
                    }

                }
        ))).collect(Collectors.toList());
    }

    @Then("^The operation input attributes table has the following values:$")
    public void input_attributes_table(List<Map<String, String>> table) throws Throwable {
        previouslyCheckedInputTable = table;
        TableHelper.using(driver()).setTable(Operation.TABLE_INPUT_ATTRIBUTES).matchesMapList(cleanAttributeValues(table));
    }

    @Then("^The operation output attributes table has the following values:$")
    public void output_attributes_table(List<Map<String, String>> table) throws Throwable {
        previouslyCheckedOutputTable = table;
        TableHelper.using(driver()).setTable(Operation.TABLE_OUTPUT_ATTRIBUTES).matchesMapList(cleanAttributeValues(table));
    }

    @Then("^The (?:cloned )?operation have the same attributes$")
    public void same_attributes_tables() throws Throwable {
        input_attributes_table(previouslyCheckedInputTable);
        output_attributes_table(previouslyCheckedOutputTable);
    }

    @When("^(?:I )view cfs \"([^\"]*)\" on version \"([^\"]*)\" with following characteristics:$")
    public void viewCfsWithVersion(String operationName, String versionName, Map<String, String> characteristics) throws Throwable {
        navigateToOperationWithVersion(operationName, versionName, characteristics.get("entity"), Cfs.SEARCH_FORM, Cfs.SEARCH_TABLE);
        validateOperationViewPage(Cfs.VIEW_PAGE, operationName, versionName, characteristics);
    }

    @Then("^[Tt]he (cfs|rfs|resource) view has the following characteristics:$")
    public void viewCfsWithVersion(String operationType, Map<String, String> characteristics) throws Throwable {
        switch (operationType) {
            case "cfs":
                validateOperationViewPage(Cfs.VIEW_PAGE, characteristics);
                return;
            case "rfs":
                validateOperationViewPage(Rfs.VIEW_PAGE, characteristics);
                return;
            case "resource":
                validateOperationViewPage(Resource.VIEW_PAGE, characteristics);
        }
    }

    @When("^(?:I )view (cfs|rfs|resource) \"([^\"]*)\" of entity \"([^\"]*)\"")
    public void viewOperationOfEntity(String operationType, String operationName, String entityName) throws Throwable {
        switch (operationType) {
            case "cfs":
                navigateToOperation(operationName, entityName, Cfs.SEARCH_FORM, Cfs.SEARCH_TABLE);
                return;
            case "rfs":
                navigateToOperation(operationName, entityName, Rfs.SEARCH_FORM, Rfs.SEARCH_TABLE);
                return;
            case "resource":
                navigateToOperation(operationName, entityName, Resource.SEARCH_FORM, Resource.SEARCH_TABLE);
        }
    }

    @When("^(?:I )view rfs \"([^\"]*)\" on version \"([^\"]*)\" with following characteristics:$")
    public void viewRfsWithVersion(String operationName, String versionName, Map<String, String> characteristics) throws Throwable {
        navigateToOperationWithVersion(operationName, versionName, characteristics.get("entity"), Rfs.SEARCH_FORM, Rfs.SEARCH_TABLE);
        validateOperationViewPage(Rfs.VIEW_PAGE, operationName, versionName, characteristics);
    }

    @When("^(?:I )view resource \"([^\"]*)\" on version \"([^\"]*)\" with following characteristics:$")
    public void viewResourceWithVersion(String operationName, String versionName, Map<String, String> characteristics) throws Throwable {
        navigateToOperationWithVersion(operationName, versionName, characteristics.get("entity"), Resource.SEARCH_FORM, Resource.SEARCH_TABLE);
        validateOperationViewPage(Resource.VIEW_PAGE, operationName, versionName, characteristics);
    }

    @When("^(?:I )change the (?:cfs|rfs|resource) state to \"(blocked|in service|simulated)\" from view page")
    public void I_change_version_state_to_from_view_page(String state) {
        driver().click(Operation.VIEW_MORE_OPTIONS_BUTTON);
        driver().click(Operation.VIEW_CHANGE_STATE_TO, state.toUpperCase());
        ModalHelper.using(driver()).ok();
        driver().waitLoadingFades();
    }

    @When("^I change the \"(cfs|rfs|resource)\" state to \"(blocked|in service|simulated)\"")
    public void I_change_operation_state_to(String operationType, String state) {
        TableHelper.using(driver()).setTable(operationType + " search").clickAction("transition to " + state, 0);
        ModalHelper.using(driver()).ok();
    }

    @When("^(?:I )add input attribute \"(.*?)\" with the following characteristics:$")
    public static void add_input_attribute(String attributeName, Map<String, String> values) {
        driver().click("new input attribute button");
        List<FormHelper.FormElems> fields = values.entrySet().stream().filter(entry -> !entry.getKey().equals("name"))
                .map(entry -> new FormHelper.FormElems(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
        FormHelper.using(driver()).setForm(Operation.FORM_INPUT_ATTRIBUTES).onField("name", formField -> {
            String searchText = attributeName;
            if (searchText.contains("(")) {
                searchText = searchText.substring(0, searchText.indexOf("(") - 1).trim();
            }
            Select2.fromField(driver(), formField.getElementIdSelector()).open().selectValOnSearch(attributeName, searchText);
        }).setAndSaveFields(fields).submit();
        driver().waitLoadingFades();
    }

    @When("^(?:I )add output attribute \"(.*?)\"")
    public static void add_output_attribute(String attributeName) {
        FormHelper.using(driver()).setForm(Operation.FORM_OUTPUT_ATTRIBUTES).onField("name", formField -> {
            String searchText = attributeName;
            if (searchText.contains("(")) {
                searchText = searchText.substring(0, searchText.indexOf("(") - 1).trim();
            }
            Select2.fromField(driver(), formField.getElementIdSelector()).open().selectValOnSearch(attributeName, searchText);
        }).submit();
    }

    @Given("^the operations catalog version \"([^\"]*)\" is created$")
    public void theOperationsCatalogVersionIsCreated(String version) throws Throwable {
        final OCScenario scenario = new OCScenario().withVersionName(version);
        snapshots.get().add(scenario);
        scenario.appendToDB();
    }

    @Given("^the operations catalog version \"([^\"]*)\" with production entity version is created$")
    public void theOperationsCatalogVersionWithProductionEntityVersionsIsCreated(String version) throws Throwable {
        final OCScenarioWithProductionEntityVersion scenario = new OCScenarioWithProductionEntityVersion().withVersionName(version);
        snapshots.get().add(scenario);
        scenario.appendToDB();
    }

    @Given("^the operations catalog version \"([^\"]*)\" with two entity versions is created$")
    public void theOperationsCatalogVersionWithTwoEntityVersionsIsCreated(String version) throws Throwable {
        final OCScenarioWithTwoEntityVersions scenario = new OCScenarioWithTwoEntityVersions().withVersionName(version);
        snapshots.get().add(scenario);
        scenario.appendToDB();
    }

    @Given("^the operations catalog version \"([^\"]*)\" with removed associated entity version \"([^\"]*)\" is created$")
    public void theOperationsCatalogVersionWithRemovedAssociatedEntityIsCreated(String ocVersion, String ecVersion) throws Throwable {
        final OCScenarioWithoutEntityVersion scenario = new OCScenarioWithoutEntityVersion()
                .withVersionName(ocVersion)
                .withEntityVersionName(ecVersion);
        snapshots.get().add(scenario);
        scenario.appendToDB();
    }

    @Given("^the invalid operations catalog version \"([^\"]*)\" is created$")
    public void theInvalidOperationsCatalogVersionIsCreated(String version) throws Throwable {
        final OCScenario scenario = new OCScenario().withVersionName(version).inValid();
        snapshots.get().add(scenario);
        scenario.appendToDB();
    }

    @Given("^the operations catalog version \"([^\"]*)\" on \"([^\"]*)\" is created$")
    public void theOperationsCatalogVersionOnStateIsCreated(String version, String state) throws Throwable {
        final OCScenario scenario = new OCScenario().withVersionName(version).withVersionState(OCVersionState.valueOf(state.toUpperCase()));
        snapshots.get().add(scenario);
        scenario.appendToDB();
    }

    @When("^(?:I )?clone the (?:cfs|rfs|resource) from the view page$")
    public void iCloneOperationFromViewPage() throws Throwable {
        driver().click("operation view clone button");
        ModalHelper.using(driver()).ok();
    }

    @When("^(?:I )?delete the (?:cfs|rfs|resource) from the view page$")
    public void iDeleteOperationFromViewPage() throws Throwable {
        driver().click("operation view delete button");
        ModalHelper.using(driver()).ok();
    }

    @When("^(?:I )?clone (cfs|rfs|resource) \"([^\"]*)\" of entity \"([^\"]*)\" from search page$")
    public void iCloneCfsOfEntityFromSearchPage(String operationType, String name, String entity) throws Throwable {
        switch (operationType) {
            case "cfs":
                FormHelper.using(driver()).setForm(Cfs.SEARCH_FORM)
                        .setField(Cfs.FORM_FIELD_NAME, name)
                        .setField(Cfs.FORM_FIELD_ENTITY, entity)
                        .submit();
                TableHelper.using(driver()).setTable(Cfs.SEARCH_TABLE).clickAction("clone", 0);
                break;
            case "rfs":
                FormHelper.using(driver()).setForm(Rfs.SEARCH_FORM)
                        .setField(Rfs.FORM_FIELD_NAME, name)
                        .setField(Rfs.FORM_FIELD_ENTITY, entity)
                        .submit();
                TableHelper.using(driver()).setTable(Rfs.SEARCH_TABLE).clickAction("clone", 0);
                break;
            case "resource":
                FormHelper.using(driver()).setForm(Resource.SEARCH_FORM)
                        .setField(Resource.FORM_FIELD_NAME, name)
                        .setField(Resource.FORM_FIELD_ENTITY, entity)
                        .submit();
                TableHelper.using(driver()).setTable(Resource.SEARCH_TABLE).clickAction("clone", 0);
                break;
        }
        ModalHelper.using(driver()).ok();
    }

    @Then("the contextualized version is invalid")
    public void theContextualizedVersionIsInvalid() {
        driver().waitElementHidden("version context invalid option label");
    }

    @Then("the contextualized version is valid")
    public void theContextualizedVersionIsValid() {
        driver().waitElementVisible("version context option label");
        driver().waitElementHidden("version context invalid option label");
        driver().waitElementVisible("version context option label");
    }

    @Then("^The contextualized version is \"([^\"]*)\" in \"(development|testing|abandoned|production|blocked|deprecated)\" state$")
    public void the_contextualized_version_is_in_state(String versionKey, String versionState) throws Throwable {
        driver().assertThat.element("version context combo box label")
                .textEquals(UUID_Manager.getUniqueNameOrName(versionKey, UUID_Manager.UUIDConversion.ALPHANUMERIC));
        driver().assertThat.element("version context combo box badge")
                .textEquals(getStateI18n("version state", versionState));

    }


    /**
     * Navigate to a specific operation view page.
     * Contextualize with given version and search with operation name, then click view action on table.
     */
    private void navigateToOperationWithVersion(String name, String version, String entity, String searchForm, String searchTable) throws Throwable {
        StepsDefsVersions.contextualize_search_to_version(version);
        navigateToOperation(name, entity, searchForm, searchTable);
    }

    /**
     * Navigate to a specific operation view page.
     * Contextualize with given version and search with operation name, then click view action on table.
     */


    private void navigateToOperation(String name, String entity, String searchForm, String searchTable) throws Throwable {
        FormHelper.using(driver()).setForm(searchForm)
                .setField(Operation.FORM_FIELD_NAME, name)
                .setField(Operation.FORM_FIELD_ENTITY, entity)
                .submit();

        // Click fist row view action
        TableHelper.using(driver()).setTable(searchTable).clickAction(TableActions.VIEW, 0);
    }

    private void validateOperationViewPage(String page, String operationName, String versionName, Map<String, String> characteristics) {
        Map<String, String> extendedCharacteristics = new HashMap<>(characteristics);
        extendedCharacteristics.put(Operation.VIEW_VERSION_NAME, versionName);
        extendedCharacteristics.put(Operation.VIEW_NAME, operationName);
        validateOperationViewPage(page, extendedCharacteristics);
    }

    private void validateOperationViewPage(String page, Map<String, String> characteristics) {
        StepsDefsPageNavigation.I_am_on_page(page);
        String elem = "operation view characteristics %s";

        Map<String, String> extendedCharacteristics = new HashMap<>(characteristics);
        extendedCharacteristics.computeIfPresent(Operation.VIEW_VERSION_NAME, (key, name) -> UUID_Manager.getUniqueNameOrName(name, UUID_Manager.UUIDConversion.ALPHANUMERIC));

        for (Map.Entry<String, String> characteristic : extendedCharacteristics.entrySet()) {
            String elementKey = String.format(elem, characteristic.getKey());
            String value = characteristic.getValue();

            if (characteristic.getKey().equals("version state")) {
                driver().waitElementVisible(String.format(elem, "version badge " + value.toLowerCase()));
                value = getStateI18n(characteristic.getKey(), characteristic.getValue());
            } else if (characteristic.getKey().equals("state")) {
                driver().waitElementVisible(String.format(elem, "state icon " + value.toLowerCase()));
                value = getStateI18n(characteristic.getKey(), characteristic.getValue());
            }

            if (characteristic.getKey().equals("next states")) {

                final Set<String> validStates = new HashSet<>(Arrays.asList("blocked", "in service", "simulated"));

                driver().click(Operation.VIEW_MORE_OPTIONS_BUTTON);
                List<String> nextValues = Stream.of(characteristic.getValue().split(","))
                        .map(String::trim)
                        .map(String::toLowerCase)
                        .map(string -> string.replaceAll("\\s+", " "))
                        .distinct()
                        .filter(validStates::contains)
                        .collect(Collectors.toList());

                for (String validState : validStates) {
                    if (nextValues.contains(validState)) {
                        driver().waitElementVisible(Operation.VIEW_CHANGE_STATE_TO, validState.toUpperCase());
                    } else {
                        driver().waitElementHidden(Operation.VIEW_CHANGE_STATE_TO, validState.toUpperCase());
                    }
                }
                driver().click(Operation.VIEW_MORE_OPTIONS_BUTTON);

                continue;
            }


            if (characteristic.getKey().equals("status")) {
                switch (characteristic.getKey().trim().toLowerCase()) {
                    case "valid":
                        driver().waitElementHidden(elementKey);
                        break;
                    case "invalid":
                        driver().waitElementVisible(elementKey);
                        break;
                }
                continue;
            }

            if (characteristic.getKey().equals("tags")) {
                TagsHelper.using(driver()).assertIsEqual(driver().element(elementKey), value);
                continue;
            }

            driver().assertThat.element(elementKey).textEquals(value);
        }
    }

    static String getStateI18n(String prefix, String sufix) {
        LoginHelper.using(driver(), TestProfile.instance.getAuthProfile());
        Locale lang = LoginHelper.getCurrentUser().getI18n();
        String i18nKey = String.format("%s i18n %s %s", prefix, lang.getLanguage(), sufix);
        return Mappings.getOrElse(i18nKey, null);
    }

    private interface Operation {
        String FORM_FIELD_NAME = "name";
        String FORM_FIELD_ENTITY = "entity";
        String FORM_INPUT_ATTRIBUTES = "operation input attribute form";
        String FORM_OUTPUT_ATTRIBUTES = "operation output attribute form";
        String VIEW_NAME = "name";
        String VIEW_VERSION_NAME = "version name";
        String TABLE_INPUT_ATTRIBUTES = "operation input attributes";
        String TABLE_OUTPUT_ATTRIBUTES = "operation output attributes";
        String VIEW_MORE_OPTIONS_BUTTON = "operation view more options button";
        String VIEW_CHANGE_STATE_TO = "operation view change state to";
    }

    private interface Cfs extends Operation {
        String SEARCH_FORM = "cfs side search";
        String SEARCH_TABLE = "cfs search";
        String VIEW_PAGE = "operations cfs view";
        String EDIT_PAGE = "operations cfs edit";
        String CREATE_PAGE = "operations cfs create";
    }

    private interface Rfs extends Operation {
        String SEARCH_FORM = "rfs side search";
        String SEARCH_TABLE = "rfs search";
        String VIEW_PAGE = "operations rfs view";
        String EDIT_PAGE = "operations rfs edit";
        String CREATE_PAGE = "operations rfs create";
    }

    private interface Resource extends Operation {
        String SEARCH_FORM = "resource side search";
        String SEARCH_TABLE = "resource search";
        String VIEW_PAGE = "operations resource view";
        String EDIT_PAGE = "operations resource edit";
        String CREATE_PAGE = "operations resource create";
    }

}