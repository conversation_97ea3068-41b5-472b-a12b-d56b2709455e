package pt.ptinovacao.na.portal.it.driver;

import org.slf4j.Logger;
import org.openqa.selenium.WebDriver;
import pt.ptinovacao.na.portal.it.web.utils.driver.ItWebDriver;
import pt.ptinovacao.na.portal.it.web.utils.logger.ITLogger;

public class NAWebDriver extends ItWebDriver {

    private static ThreadLocal<pt.ptinovacao.na.portal.it.driver.NAWebDriver> runningWebDriver = new ThreadLocal<>();
    private static final Logger LOGGER = ITLogger.getLogger(pt.ptinovacao.na.portal.it.driver.NAWebDriver.class);

    public NAWebDriver(WebDriver driver) {
        super(driver);
        if (current() != null && !current().isClosed()) {
            LOGGER.warn("initializing WebDriver when there is another running");
        }
        runningWebDriver.set(this);
    }

    public static pt.ptinovacao.na.portal.it.driver.NAWebDriver current() {
        return runningWebDriver.get();
    }






}
