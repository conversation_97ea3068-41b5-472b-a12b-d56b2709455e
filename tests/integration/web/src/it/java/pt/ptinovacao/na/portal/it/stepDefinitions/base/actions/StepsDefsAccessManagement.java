package pt.ptinovacao.na.portal.it.stepDefinitions.base.actions;

import cucumber.api.java.en.When;
import pt.ptinovacao.na.portal.it.TestProfile;
import pt.ptinovacao.na.portal.it.stepDefinitions.CommonStepDefinitions;
import pt.ptinovacao.na.portal.it.web.utils.Retry;
import pt.ptinovacao.na.portal.it.web.utils.helpers.ViewHelper;

/**
 * Created by omar on 04-11-2015.
 *
 * Step definitions related to upload of files.
 *
 */
public class StepsDefsAccessManagement extends CommonStepDefinitions {


    @When("^I go to access management page")
    public static void I_go_to_access_management_page() {
        driver().click("topBar tools button");
        driver().click("topBar tools access management button");
    }

    @When("^I should be on access management page")
    public static void I_should_be_on_access_management_page() {
        Retry.call( () -> {
            driver().assertThat.page().urlContains(TestProfile.instance.getAuthProfile().getAccessManagementEndpoint());
            ViewHelper.using(driver()).checkOnView(TestProfile.instance.getAccessManagementView());
        }, (Retry.RetryError e) -> {}, 3);
    }
}
