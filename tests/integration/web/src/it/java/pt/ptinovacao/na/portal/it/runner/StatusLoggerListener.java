package pt.ptinovacao.na.portal.it.runner;

import org.slf4j.Logger;
import org.junit.runner.Description;
import org.junit.runner.notification.RunListener;
import pt.ptinovacao.na.portal.it.web.utils.logger.ITLogger;

import java.time.Duration;
import java.util.Date;

/**
 * <AUTHOR> <<EMAIL>>, 10-04-2017.
 * <p>
 * Logs the test status in the home for each step
 */
public class StatusLoggerListener extends RunListener {
    private static final Logger LOGGER = ITLogger.getLogger(StatusLoggerListener.class);

    private final Date startTime;
    private long runningStepNumber = 0;
    private final long numOfSteps;

    public StatusLoggerListener(long numOfSteps) {
        startTime = new Date();
        this.numOfSteps = numOfSteps;
    }

    @Override
    public void testStarted(Description description) throws Exception {
        if (description.isTest()) {
            LOGGER.info("executing statement " + description);
            runningStepNumber++;
        }
    }

    @Override
    public void testFinished(Description description) throws Exception {
        if (description.isTest()) {
            final Date currentTime = new Date();
            final long passedTime = currentTime.getTime() - startTime.getTime();
            final long estimatedAllTestsDuration = passedTime * numOfSteps / runningStepNumber;
            final Duration remainingTime = Duration.ofMillis(estimatedAllTestsDuration - passedTime);
            final String readableRemainingTime = remainingTime.toString().substring(2).replaceAll("(\\d[HMS])(?!$)", "$1 ").toLowerCase();
            final String readablePassedTime = Duration.ofMillis(passedTime).toString().substring(2).replaceAll("(\\d[HMS])(?!$)", "$1 ").toLowerCase();
            final double percentageDone = (runningStepNumber * 100) / ((double) numOfSteps);
            LOGGER.info("executed statement " + description);
            LOGGER.info(String.format("step %d of %d done (%.2f%%), Time passed: %s, ETR: %s ", runningStepNumber, numOfSteps, percentageDone, readablePassedTime, readableRemainingTime));
        }
        super.testFinished(description);
    }

    @Override
    public void testIgnored(Description description) throws Exception {
        if (description.isTest()) {
            runningStepNumber++;
            final Date currentTime = new Date();
            final long passedTime = currentTime.getTime() - startTime.getTime();
            final long estimatedAllTestsDuration = passedTime * numOfSteps / runningStepNumber;
            final Duration remainingTime = Duration.ofMillis(estimatedAllTestsDuration - passedTime);
            final String readableRemainingTime = remainingTime.toString().substring(2).replaceAll("(\\d[HMS])(?!$)", "$1 ").toLowerCase();
            final String readablePassedTime = Duration.ofMillis(passedTime).toString().substring(2).replaceAll("(\\d[HMS])(?!$)", "$1 ").toLowerCase();
            final double percentageDone = (runningStepNumber * 100) / ((double) numOfSteps);
            LOGGER.info(String.format("step %d of %d ignored (%.2f%%), Time passed: %s, ETR: %s ", runningStepNumber, numOfSteps, percentageDone, readablePassedTime, readableRemainingTime));
        }
        super.testIgnored(description);
    }

}
