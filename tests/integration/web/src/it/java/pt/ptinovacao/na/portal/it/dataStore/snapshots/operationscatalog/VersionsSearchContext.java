package pt.ptinovacao.na.portal.it.dataStore.snapshots.operationscatalog;

import org.slf4j.Logger;
import pt.ptinovacao.na.operations.catalog.versions.manager.scenario.oc.OcTestHelper;
import pt.ptinovacao.na.portal.it.config.module.OperationsCatalogConfig;
import pt.ptinovacao.na.portal.it.web.utils.dataStore.Snapshot;
import pt.ptinovacao.na.portal.it.web.utils.logger.ITLogger;
import pt.ptinovacao.na.portal.it.web.utils.uuid.UUID_Manager;

import java.util.ArrayList;
import java.util.List;

public class VersionsSearchContext implements Snapshot {
    private static final Logger LOGGER = ITLogger.getLogger(Snapshot.class);

    private static final int NUMBER_OF_VERSIONS = 50;
    private static final String VERSION_NAME = "version pagination test - version ";
    private static final String EC_VERSION = "SELFNET-TESTES-1.0.0";

    private final List<Long> createdVersions = new ArrayList<>(NUMBER_OF_VERSIONS);

    @Override
    public void appendToDB() {
        OcTestHelper helper = getHelper();

        LOGGER.info("***************** Push snapshot to DB *************************");

        for (int i = 0; i < NUMBER_OF_VERSIONS; i++) {
            String versionName = UUID_Manager.generateUniqueName(VERSION_NAME + i + " ", UUID_Manager.UUIDConversion.ALPHANUMERIC);
            createdVersions.add(helper.createVersion(versionName, EC_VERSION, true));
        }

        LOGGER.info("***************** Finished snapshot push *************************");
    }

    @Override
    public void removeFromDB() {
        LOGGER.info("***************** Remove snapshot from DB *************************");
        OcTestHelper helper = getHelper();

        for (Long versionId : createdVersions) {
            helper.deleteVersion(versionId);
        }

        LOGGER.info("**************** Finished snapshot removal ************************");
    }

    private OcTestHelper getHelper() {
        String ocHost = OperationsCatalogConfig.getOCHost();
        int ocPort = OperationsCatalogConfig.getOCPort();
        return new OcTestHelper(ocHost, ocPort);
    }
}
