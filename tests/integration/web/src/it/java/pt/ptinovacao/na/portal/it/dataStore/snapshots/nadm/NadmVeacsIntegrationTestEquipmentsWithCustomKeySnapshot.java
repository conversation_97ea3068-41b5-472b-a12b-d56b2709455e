package pt.ptinovacao.na.portal.it.dataStore.snapshots.nadm;

import org.slf4j.Logger;
import pt.ptinovacao.na.portal.it.config.module.NADMConfig;
import pt.ptinovacao.na.portal.it.web.utils.dataStore.Snapshot;
import pt.ptinovacao.na.portal.it.web.utils.logger.ITLogger;
import pt.ptinovacao.nacommons.testapi.HttpRequest;

import javax.ws.rs.core.MediaType;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class NadmVeacsIntegrationTestEquipmentsWithCustomKeySnapshot implements Snapshot {
    protected static Logger LOGGER = ITLogger.getLogger(Snapshot.class);

    private static final String baseUrl = "http://"+ NADMConfig.getBackendHost()+":"+NADMConfig.getBackendPort();
    private static final String postUrl = baseUrl + "/management-nadm/"+NADMConfig.getVeacsDBProfile()+"/nadm/"+NADMConfig.getVeacsDBName()+"/naacs";

    private static final String associationPostUrl = postUrl + "/update/equipment:resources";
    private static final String equipmentPostUrl = postUrl + "/create/equipment";
    private static final String deleteUrl = baseUrl +"/nadm/bulk/"+NADMConfig.getVeacsDBProfile()+"/"+NADMConfig.getVeacsDBName()+"/actions/delete";

    private String integrationTestEquipment(String id, String clientName){
        return "<request>\n" +
                "    <description>na-portal - create-Equipment</description>\n" +
                "    <data>\n" +
                "        <NadmRequest>\n" +
                "            <equipments>\n" +
                "                <equipment>\n" +
                "                    <clientName>"+clientName+"</clientName>\n" +
                "                    <equipmentData>\n" +
                "                        <deviceInfo>\n" +
                "                            <modelName>SIP-T48G</modelName>\n" +
                "                            <softwareVersion>**********</softwareVersion>\n" +
                "                            <manufacturer>yealink</manufacturer>\n" +
                "                        </deviceInfo>\n" +
                "                    </equipmentData>\n" +
                "                    <name>"+id+"</name>\n" +
                "                </equipment>\n" +
                "            </equipments>\n" +
                "        </NadmRequest>\n" +
                "    </data>\n" +
                "</request>";
    }

    private String integrationTestEquipmentAssociation(String id, String clientName) {
        return "<request>\n" +
                "   <description>na-portal - associations</description>\n" +
                "   <data>\n" +
                "   	<NadmRequest>\n" +
                "   		<equipments>\n" +
                "   			<equipment>\n" +
                "   				<name>"+id+"</name>\n" +
                "   				<equipmentData>\n" +
                "   				    <genericPhone>\n" +
                "   				        <customKeys>\n" +
                "   				            <customKey>\n" +
                "                                   <line>1</line>\n" +
                "                                   <name>DialUp</name>\n" +
                "                                   <keyId>2</keyId>\n" +
                "                                   <monitor>Integration</monitor>\n" +
                "                                   <type>aggregateLine</type>\n" +
                "   				            </customKey>\n" +
                "   				        </customKeys>\n" +
                "   				    </genericPhone>\n" +
                "   				</equipmentData>\n" +
                "   				<services>\n" +
                "   					<service>\n" +
                "   						<clientName>"+clientName+"</clientName>\n" +
                "   						<serviceData>\n" +
                "   							<voiceProfile>\n" +
                "   								<proxy>service.unify.telecom.pt</proxy>\n" +
                "   								<line>1</line>\n" +
                "   							</voiceProfile>\n" +
                "   						</serviceData>\n" +
                "   						<name><EMAIL></name>\n" +
                "   					</service>\n" +
                "   				</services>\n" +
                "   			</equipment>\n" +
                "   		</equipments>\n" +
                "   	</NadmRequest>\n" +
                "   </data>\n" +
                "</request>\n";

    }

    private final List<String> ids = Arrays.asList(
            "FF:FF:FF:FF:00:01",
            "FF:FF:FF:FF:00:02",
            "FF:FF:FF:FF:00:03"
    );

    private String deleteEquipmentsPayload(){
        return "{\"equipments\": ["+ ids.stream().map(id -> '"'+id+'"').collect(Collectors.joining(",")) +"]}";
    }

    @Override
    public void appendToDB() {
        LOGGER.info("***************** Push snapshot to DB *************************");
        for (String id: ids) {
            String clientName = "veacs";
            new HttpRequest().post(equipmentPostUrl).mediaType(MediaType.APPLICATION_XML_TYPE).payload(integrationTestEquipment(id, clientName)).expectedStatus(200).run();
            new HttpRequest().post(associationPostUrl).mediaType(MediaType.APPLICATION_XML_TYPE).payload(integrationTestEquipmentAssociation(id, clientName)).expectedStatus(200).run();
        }
        LOGGER.info("***************** Finished snapshot push *************************");
    }

    @Override
    public void removeFromDB() {
        LOGGER.info("***************** Pop snapshot from DB *************************");
        new HttpRequest().post(deleteUrl).mediaType(MediaType.APPLICATION_JSON_TYPE).payload(deleteEquipmentsPayload()).expectedStatus(200).run();
        LOGGER.info("***************** Finished snapshot pop *************************");

    }
}
