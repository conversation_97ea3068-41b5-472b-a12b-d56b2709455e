@NADM-MODULE @NADM_EDIT_VIEW
Feature: [ NADM ]

  Scenario: [NADM-WEB] Test edit of configurations from a equipment on all layers
  Given I am logged in as "veacs admin"
  And  I am on "veacs dashboard" page
  And I click "nadm navigation sidebar global equipment"
  When I search for the following "global equipment":
    | field         | value                             |
    | manufacturer  | siemens                           |
    | model         | OpenStage40+KeyModule+KeyModule   |
    | version       | V2R2.47.4                         |
  Then the results of the "global equipment" search should have the following values:
    |   manufacturer  |   model                            |  version    | action                              |
    |   siemens       |  OpenStage40\+KeyModule\+KeyModule |  V2R2.47.4  | edit (enabled), view (enabled)      |
  When I click first row action "edit" of column "action"
  And I am on "veacs global equipment edit" page
  Then I can reset the following values of table form "table form" to inherited "date format,time format,timezone,ip address"
  When I go to search page of NADM "global equipment"
  And I search for the following "global equipment":
    | field         | value                             |
    | manufacturer  | siemens                           |
    | model         | OpenStage40+KeyModule+KeyModule   |
    | version       | V2R2.47.4                         |
  And the results of the "global equipment" search should have the following values:
    |   manufacturer  |   model                            |  version    | action                              |
    |   siemens       |  OpenStage40\+KeyModule\+KeyModule |  V2R2.47.4  | edit (enabled), view (enabled)      |
  And I click first row action "edit" of column "action"
  And I am on "veacs global equipment edit" page
  And I submit table form "table form" with the following values:
    | field        | value       |
    | date format  | DD/MM/YY    |
    | time format  | 24hr        |
    | timezone     | 1:00        |
    | ip address   | *********** |
  Then the table form "table form" should have the following values
    | field        | value       |
    | date format  | DD/MM/YY    |
    | time format  | 24hr        |
    | timezone     | 1:00        |
    | ip address   | *********** |
  And the table form "table form" should have the following context values:
    | field        | context     |
    | date format  |             |
    | time format  |             |
    | timezone     |             |
    | ip address   |             |
  When I view NADM client "99995"
  And  I search for the following "client equipment":
  | field         | value                             |
  | manufacturer  | siemens                           |
  | model         | OpenStage40+KeyModule+KeyModule   |
  | version       | V2R2.47.4                         |
  And the results of the "client equipment" search should have the following values:
  |   manufacturer  |   model                            |  version    | action                              |
  |   siemens       |  OpenStage40\+KeyModule\+KeyModule |  V2R2.47.4  | edit (enabled), view (enabled)      |
  And I click first row action "edit" of column "action"
  And I can reset the following values of table form "modal table form" to inherited "date format,time format,timezone,ip address"
  When I view NADM client "99995"
  And  I search for the following "client equipment":
  | field         | value                             |
  | manufacturer  | siemens                           |
  | model         | OpenStage40+KeyModule+KeyModule   |
  | version       | V2R2.47.4                         |
  And the results of the "client equipment" search should have the following values:
  |   manufacturer  |   model                            |  version    | action                              |
  |   siemens       |  OpenStage40\+KeyModule\+KeyModule |  V2R2.47.4  | edit (enabled), view (enabled)      |
  And I click first row action "edit" of column "action"
  And I submit table form "modal table form" with the following values:
  | field        | value       |
  | timezone     | 0:00        |
  | ip address   | *********** |
  And I click first row action "view" of column "action"
  Then the table form "modal table form" should have the following context values:
  | field        | context     |
  | date format  |  global     |
  | time format  |  global     |
  | timezone     |             |
  | ip address   |             |
  And I click "modal table form cancel button"
  When I click "nadm navigation sidebar equipment"
  And I search for the following "equipment":
  | field         | value                             |
  | manufacturer  | siemens                           |
  | model         | OpenStage40+KeyModule+KeyModule   |
  | version       | V2R2.47.4                         |
  | client        | 99995                             |
  And the results of the "equipment" search should have the following values:
  | identifier        |   manufacturer  |   model                            |  version   | client | action                        |
  | 00:00:00:00:00:11 |   siemens       |  OpenStage40\+KeyModule\+KeyModule |  V2R2.47.4 | 99995  | edit (enabled), view (enabled)|
  And I click first row action "edit" of column "action"
  And I can reset the following values of table form "table form" to inherited "date format,time format,timezone,ip address"
  And I click "nadm navigation sidebar equipment"
  And I search for the following "equipment":
  | field         | value                             |
  | manufacturer  | siemens                           |
  | model         | OpenStage40+KeyModule+KeyModule   |
  | version       | V2R2.47.4                         |
  | client        | 99995                             |
  And the results of the "equipment" search should have the following values:
  | identifier        |   manufacturer  |   model                            |  version   | client | action                        |
  | 00:00:00:00:00:11 |   siemens       |  OpenStage40\+KeyModule\+KeyModule |  V2R2.47.4 | 99995  | edit (enabled), view (enabled)|
  And I click first row action "edit" of column "action"
  Then the table form "table form" should have the following context values:
  | field        | context     |
  | date format  |  global     |
  | time format  |  global     |
  | timezone     |  client     |
  | ip address   |  client     |