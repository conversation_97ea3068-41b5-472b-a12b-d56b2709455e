@MONITORING @MONITORING-VALIDATION-TABS
Feature: Search date filter by

  @MONITORING
  Scenario: [MONITORING-WEB] View an order with validation errors and navigate between validation body tabs
    Given I am "administrator" on "order search" page
    When I search by order id "4786a4c4-6006-40da-8b85-67345156545a14"
            # summary
    Then The order summary has the following values:
      | name          |  4786a4c4-6006-40da-8b85-67345156545a14 - SALES_ORDER     |
      | state         | rejected                                                  |
      | system        | user_no_version                                           |
      | description   |                                                           |
      | system entry  | .+ ([01]?[0-9]\|2[0-3]):[0-5][0-9]:[0-5][0-9]             |
      | last update   | .+ ([01]?[0-9]\|2[0-3]):[0-5][0-9]:[0-5][0-9]             |
      | elapsed time  | h\u00E1 .+                                                |
            # verify tab's and specific validation's status
    And Validation has error
    And Structure validation has error
    And Semantics validation is not validated
    And a validation page status "Order has invalid or missing parameters (see details) (CL_01000 - Invalid order structure)." is visible.
    When I click "order validation tab response"
    Then the "order validation tab response" is active
            # Error message's table has all detail's errors
    And the first rows of table "error messages" matches table:
      |    code    |           message                       |      description                 |
      |  CL_01002  | Invalid or missing mandatory parameter  |  Order Category is missing       |
      |  CL_01002  | Invalid or missing mandatory parameter  |  Requester Callback is missing   |
      |  CL_01002  | Invalid or missing mandatory parameter  |  Order OrderItem is empty        |
            # Navigation between code and table, and has a valid original response with all error descriptions
    When I change to validation response code view.
    Then Original validation response has the following error descriptions:
      | Order has invalid or missing parameters (see details) |
      | Order Category is missing                             |
      | Requester Callback is missing                         |
      | Order OrderItem is empty                              |
            # Navigation between code and table view, and validate global error message
    When I change to validation response table view.
    Then the "table of validation error messages" is visible
    When I click "order validation tab request"
    Then the "order validation tab request" is active
    And original validation request has the following description:
      | Workflow Engine Test - Execution of simulated operation |
