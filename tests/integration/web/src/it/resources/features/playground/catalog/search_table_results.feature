@catalog-module

Feature: tables of catalog search must be dynamic, gracefully dynamic!


  Scenario: base - go to catalog main page using shortcut
    Given I am logged in as "administrator"
    When I go to page "catalog main"

  @olts
  Scenario: check select tree visible and single label appears
    Given I am logged in as "administrator"
    When I go to page "catalog main"
    And I select tree "catalog type" value to "Lista OLTs"
    Then the fields "na_catalog_input_name" should appear
