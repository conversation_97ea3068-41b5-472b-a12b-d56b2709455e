@HISTNA
Feature: Maintain search context

  Scenario: [HISTNA-WEB] The table page length, sidebar collapse and columns visibility should be saved on order search page even after the search is triggered
    Given I am "administrator" on "history order search" page
    When I collapse search sidebar
    When I set table "orders" to show 30 results per page
    And I hide table columns "order key, application DN"
    And I go to 2nd table page
    And I click 1st row action "view"
    Then I should be on "history order view" page
    When I go back
    Then I should be on "history order search" page
    And the search sidebar is collapsed
    And I should be on 2nd table page
    And the table columns "order key, application DN" are hidden
    And the table is configured to show 30 results per page

  Scenario: [HISTNA-WEB] The table page length and columns visibility should be saved on order search page even after the search is triggered
    Given I am "administrator" on "history rfs search" page
    When I hide columns "client ID, RFS Name, application DN" of table "rfs"
    And I go to 2nd table page
    And I click 1st row action "view"
    Then I should be on "history rfs view" page
    When I go back
    Then I should be on "history rfs search" page
    And I should be on 2nd table page
    And the table columns "client ID, RFS Name, application DN" are hidden
    When I show table columns "RFS Name, application DN"
    And I click 1st row action "view"
    Then I should be on "history rfs view" page
    When I go back
    Then I should be on "history rfs search" page
    And I should be on 2nd table page
    And the table columns "RFS Name, application DN" are visible
    But the table columns "client ID" are hidden

