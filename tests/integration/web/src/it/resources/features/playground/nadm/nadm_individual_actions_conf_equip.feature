@NADM_DELETE
Feature: [NADM]


  @GLOBAL-EQUIPMENT_DELETE @NADM
  Scenario: [NADM-WEB] An equipment configuration with access equipments associated shouldn't be able to remove equipments and vice versa
    Given the data related to "global veacs equipments" is loaded
    And I am logged in as "veacs admin"
    And  I am on "veacs dashboard" page
    When I click "nadm navigation sidebar global equipment"
    And I search for the following "global equipment":
      | field         | value                             |
      | manufacturer  | IntegrationTest                   |
      | model         | Disable                           |
      | version       | Base                              |
    Then the results of the "global equipment" search should have the following values:
      |   manufacturer     |   model      |  version    |  N equipments    | action                                                 |
      |   IntegrationTest  |   Disable    |   Base      |  [1-9]*          | edit (enabled), view (enabled), remove (disabled)      |
    When I search for the following "global equipment":
      | field         | value                               |
      | manufacturer  | IntegrationTest                     |
      | model         | IntegrationTest                     |
      | version       | toDelete                            |
    Then the results of the "global equipment" search should have the following values:
      |   manufacturer    |   model          |  version    |  N equipments    | action                                                 |
      |  IntegrationTest  | IntegrationTest  |   toDelete  |  0               | edit (enabled), view (enabled), remove (enabled)       |
    When I click 1st row action "remove"
    And I confirm the equipment configuration removal
    Then a "global equipment successfully removed" success notification with params should appear:
      | param         | value                               |
      | manufacturer  | IntegrationTest                     |
      | model         | IntegrationTest                     |
      | version       | toDelete                            |
    And the table "global equipment" is empty
    And the form "global equipment page search" does not contain the following options:
      | field         | value                             |
      | model         | IntegrationTest                   |
      | version       | toDelete                          |

  @GLOBAL-EQUIPMENT_EXTERN_ACTION @NADM @NADM-GO-MPT
  Scenario: [NADM] Individual action 'createTemplateWithNewSoftwareVersion' in equipment configuration executed correctly with manufacturer, model and version automatically filled
    Given I am logged in as "veacs admin"
    And  I am on "veacs dashboard" page
    And I order the "version" column of table "global equipment" in the "ascending" order
    When I set table "global equipment" to show 30 results per page
    And I click 1st row action "view"
    Then I should be on "veacs global equipment view" page
    When I go back
    Then the table is configured to show 30 results per page
    And The order of the values column "version" should be "ascending"
    When I search for the following "global equipment":
      | field         | value                             |
      | manufacturer  | IntegrationTest                   |
      | model         | LinesOnly                         |
      | version       | True                              |
    Then the results of the "global equipment" search should have the following values:
      |   manufacturer    |   model         |  version    |  N equipments    | action                              |
      |   IntegrationTest |  LinesOnly      |  True       |  [0-9]*          | edit (enabled), view (enabled)      |
    When I click 1st row action "criar template com nova versão"
    Then the form "modal individual operation createTemplateWithNewSoftwareVersion" should have the following values:
      | field        | value                           |
      | manufacturer | IntegrationTest                 |
      | model        | LinesOnly                       |
      | version      | True                            |
      | new version  |                                 |
    And I submit form "modal individual operation createTemplateWithNewSoftwareVersion" with the following values:
      | field         | value            |
      | new version   | True-TEST        |
    Then an "individual action performed" success notification with params should appear:
      | param   | value                          |
      | name    | criar template com nova versão |
    When I click "nadm watch operation progress link"
    Then I should be watching the following NADM individual operation as a GO operation on a new tab:
      | field          | value                                |
      | family         | VEACS                                |
      | rfs            | rfsDeviceManagement                  |
      | operation      | createTemplateWithNewSoftwareVersion |
      | user           | ADMIN_VEACS                          |
    When I click "details request tab"
    Then "details request payload text" content contains text "<manufacturer>IntegrationTest</manufacturer>"
    And "details request payload text" content contains text "<modelName>LinesOnly</modelName>"
    And "details request payload text" content contains text "<softwareVersion>True</softwareVersion>"
    And "details request payload text" content contains text "<newSoftwareVersion>True-TEST</newSoftwareVersion>"
    When I close GO operation view tab
    Then I should be on "veacs dashboard" page

  @GLOBAL-EQUIPMENT_EXTERN_ACTION-WITH-FILE @NADM @NADM-GO-MPT
  Scenario: [NADM-WEB] Correctly uploaded fimware file and invidual action executed correctly with firmwareFilename automatically filled
    Given I am "veacs admin" on "veacs global equipment" page
    When I search for the following "global equipment":
      | field         | value                             |
      | manufacturer  | siemens                           |
      | model         | OpenStage40+KeyModule+KeyModule   |
      | version       | V2R2.47.4                         |
    Then the results of the "global equipment" search should have the following values:
      |   manufacturer  |   model                            |  version    | action                              |
      |   siemens       |  OpenStage40\+KeyModule\+KeyModule |  V2R2.47.4  | edit (enabled), view (enabled)      |
    When I click 1st row action "criar template com nova versão"
    Then the form "modal individual operation createTemplateWithNewSoftwareVersion" should have the following values:
      | field        | value                           |
      | manufacturer | siemens                         |
      | model        | OpenStage40+KeyModule+KeyModule |
      | version      | V2R2.47.4                       |
      | new version  |                                 |
    When I choose optional fields "firmwareFilename"
    And I submit form "modal individual operation createTemplateWithNewSoftwareVersion" with the following values:
      | field            | value                          |
      | new version      | V9000R2.47.4                   |
      | firmwareFilename | Altice_D2260G_1.6.0r1_SFR      |
    Then an "individual action performed" success notification with params should appear:
      | param   | value                          |
      | name    | criar template com nova versão |
    When I click "nadm watch operation progress link"
    Then I should be watching the following NADM individual operation as a GO operation on a new tab:
      | field          | value                                |
      | family         | VEACS                                |
      | rfs            | rfsDeviceManagement                  |
      | operation      | createTemplateWithNewSoftwareVersion |
      | user           | ADMIN_VEACS                          |
    When I click "details request tab"
    Then "details request payload text" content contains text "<manufacturer>siemens</manufacturer"
    And "details request payload text" content contains text "<modelName>OpenStage40+KeyModule+KeyModule</modelName>"
    And "details request payload text" content contains text "<softwareVersion>V2R2.47.4</softwareVersion>"
    And "details request payload text" content contains text "<newSoftwareVersion>V9000R2.47.4</newSoftwareVersion>"
    And "details request payload text" content contains text "<firmwareFilename>Altice_D2260G_1.6.0r1_SFR</firmwareFilename>"
    When I close GO operation view tab
    Then I should be on "veacs global equipment" page

  @GLOBAL-EQUIPMENT_UPLOAD_FIRMWARE_FILE @NADM
  Scenario: [NADM-WEB] Upload firmware file and then update global equipment with firmware file name
    Given I am logged in as "veacs admin"
    And  I am on "veacs dashboard" page
    When I click "nadm navigation sidebar global equipment"
    When I search for the following "global equipment":
      | field         | value                             |
      | manufacturer  | IntegrationTest                   |
      | model         | LinesOnly                         |
      | version       | True                              |
    Then the results of the "global equipment" search should have the following values:
      |   manufacturer    |   model         |  version    |  N equipments    | action                              |
      |   IntegrationTest |  LinesOnly      |  True       |  [0-9]*          | edit (enabled), view (enabled)      |
    When I click 1st row action "upload firmwareFile"
    Then a "global equipment successfully uploaded" success notification with params should appear:
      | param         | value            |
      | manufacturer  | IntegrationTest  |
      | model         | LinesOnly        |
      | version       | True             |
    When I click first row action "view" of column "action"
    Then The firmwareFilename is equal to "Altice_D2260G_1.6.0r1_SFR"

