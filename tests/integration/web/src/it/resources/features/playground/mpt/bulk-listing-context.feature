@MPT
Feature: Maintain search context

  Scenario: [HISTNA-WEB] The table page length, sidebar collapse and columns visibility should be saved on order search page even after the search is triggered
    Given I am "administrator" on "mpt main" page
    When I collapse search sidebar
    When I set table "mpt bulk search result" to show 30 results per page
    And I hide table columns "user, family, operation"
    And I go to 2nd table page
    And I click 1st row action "view"
    Then I should be on "mpt bulk view characteristics detail" page
    When I go back
    Then I should be on "mpt main" page
    And the search sidebar is collapsed
    And I should be on 2nd table page
    And the table columns "user, family, operation" are hidden
    And the table is configured to show 30 results per page

