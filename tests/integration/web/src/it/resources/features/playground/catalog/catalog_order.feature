@CATALOGORDER
Feature: [ CATALOG ]




  @TEST_NAPORTAL-X @TESTSET_NAPORTAL-Y @CATALOGORDER @catalog
  Scenario: Order Search by attributes
    Given I am logged in as "catalog admin"
    And I am on "catalog dashboard" page
    And I delete all of the entries of "Lista Telefones" with field "name" with value "Telemovel *"
    And searching for the following values of type "Lista Telefones" should return nothing:
      | field         | value                |
      | catalog type  | Lista Telefones      |
      | name          | Telemovel *          |

    And I create a new "Lista Telefones" entry with the following values from the "dashboard" page:
      | field         | value             |
      | name          | Telemovel A       |
      | state         | Inativo           |
      | external code | 111111            |
      | phone number  | 220000003         |
    And I create a new "Lista Telefones" entry with the following values from the "dashboard" page:
      | field         | value             |
      | name          | Telemovel B       |
      | state         | Ativo             |
      | external code | 222222            |
      | phone number  | 220000002         |
    And I create a new "Lista Telefones" entry with the following values from the "dashboard" page:
      | field         | value             |
      | name          | Telemovel C       |
      | state         | Inativo           |
      | external code | 333333            |
      | phone number  | 220000001         |


    When I search for a catalog of type "Lista Telefones" by the field "name" with value "Telemovel *" from "search" page
    And I order the "name" column of table "Lista Telefones" in "descending" order
    Then The order of the values column "name" of table "Lista Telefones" should have the order "Telemovel C, Telemovel B, Telemovel A"
    When I order the "external code" column in "ascending" order
    Then The order of the values column "external code" of table "Lista Telefones" should have the order "111111, 222222, 333333"



    #Clean up
    When I delete all of the entries of "Lista Telefones" with field "name" with value "Telemovel *"
    Then searching for the following values of type "Lista Telefones" should return nothing:
      | field         | value                |
      | catalog type  | Lista Telefones      |
      | name          | Telemovel *          |

    #When I delete the first entry of "Lista Telefones" with field "name" with value "Telemovel A"
    #Then searching for the following values of type "Lista Telefones" should return nothing:
    #  | field         | value                |
    #  | catalog type  | Lista Telefones      |
    #  | name          | Telemovel A          |
#
    #When I delete the first entry of "Lista Telefones" with field "name" with value "Telemovel B"
    #Then searching for the following values of type "Lista Telefones" should return nothing:
    #  | field         | value                |
    #  | catalog type  | Lista Telefones      |
    #  | name          | Telemovel B          |
#
    #When I delete the first entry of "Lista Telefones" with field "name" with value "Telemovel C"
    #Then searching for the following values of type "Lista Telefones" should return nothing:
    #  | field         | value                |
    #  | catalog type  | Lista Telefones      |
    #  | name          | Telemovel C          |

