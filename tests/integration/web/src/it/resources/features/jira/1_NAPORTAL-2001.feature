@REQ_NAPORTAL-2001
Feature: [NADM][SRV][P] Listar Serviços
	#O utilizador deverá poder efetuar pesquisas sobre serviços configurados no sistema.
	#
	#Para tal deverá ser facilitada uma área de pesquisa que permita contextualizar pelos seguintes parâmetros:
	#* Cliente - Cliente a quem está associada a instância de equipamento  (wildcard);
	#* Serviço - Identificador único do serviço em sistema (wildcard);
	#
	#O resultado da filtragem deverá ser apresentado no formato tabular com os seguintes campos:
	#||Campo||Ordenação||Descrição||
	#| Cliente | Sim | Identificador do cliente no sistema |
	#| Serviço | Sim | nome do fabricante do equipamento |
	#| Modelos  | Não | Contagem de Modelos (FMV) que o cliente tem associados a si |
	#| Equipamentos |  Não | Contagem de Instâncias de equipamentos de Cliente (Aprovisionados + Não Aprovisionados) | 
	#| Serviços | Não | Contagem de instâncias de serviços associados ao cliente |
	#Ações disponíveis:
	#* Criar ( (i) não será implementado nesta versão)
	#* Tabela:
	#** Editar - Deverá transitar para o formulário de edição de Serviço. Validado por Recurso Update.
	#** Consultar - Deverá transitar para o formulário de consulta de Serviço. Validado por Recurso Read.
	#** Remover ( (i) não será implementado nesta versão)
	#
	#{panel}
	#(i) Neste requisito estão excluídas as seguintes funcionalidades da tabela:
	#* Agrupar por
	#* Favorito
	#* Exportação
	#
	#Deverá ser suportada paginação seguindo as regras fuxi.
	#{panel}

	
	@TEST_NAPORTAL-2404 @TESTSET_NAPORTAL-22327 @TESTSET_NAPORTAL-16669 @TESTSET_NAPORTAL-9368 @TESTSET_NAPORTAL-10957 @TESTSET_NAPORTAL-12928 @TESTSET_NAPORTAL-16670 @TESTSET_NAPORTAL-19587 @TESTSET_NAPORTAL-21830 @TESTSET_NAPORTAL-21277 @TESTSET_NAPORTAL-11448 @TESTSET_NAPORTAL-12416 @TESTSET_NAPORTAL-14357 @TESTSET_NAPORTAL-15334 @TESTSET_NAPORTAL-19599 @TESTSET_NAPORTAL-19702 @TESTSET_NAPORTAL-2317 @TESTSET_NAPORTAL-7599 @TESTSET_NAPORTAL-5209 @TESTSET_NAPORTAL-10171 @TESTSET_NAPORTAL-9610 @TESTSET_NAPORTAL-8935 @TESTSET_NAPORTAL-3669 @TESTSET_NAPORTAL-15904 @TESTSET_NAPORTAL-13285 @TESTSET_NAPORTAL-9363 @TESTSET_NAPORTAL-7600 @TESTSET_NAPORTAL-5366 @TESTSET_NAPORTAL-5003 @TESTSET_NAPORTAL-5002 @TESTSET_NAPORTAL-3984 @TESTSET_NAPORTAL-3981 @TESTSET_NAPORTAL-3901 @TESTSET_NAPORTAL-10981 @TESTSET_NAPORTAL-4163 @TESTSET_NAPORTAL-3980 @TESTSET_NAPORTAL-5208 @TESTSET_NAPORTAL-18112 @NADM
	Scenario: [NADM-WEB] Validate VEACS operator user authorizations
		Given I am logged in as "veacs user authorized to edit devices"
		And  I am on "veacs dashboard" page
		And I click "nadm navigation sidebar global equipment"
		Then the results of the "global equipment" search should have the following values:
		  |       action                     |
		  |  edit (enabled), view (enabled)  |
		And I click "nadm navigation sidebar equipment"
		Then the results of the "equipment" search should have the following values:
		  |       action                     |
		  |  edit (enabled), view (enabled)  |
		And I click "nadm navigation sidebar client"
		Then the results of the "client" search should have the following values:
		  |       action                     |
		  |  view (enabled)                  |
		And I click "nadm navigation sidebar service"
		Then the results of the "service" search should have the following values:
		  |       action     |
		  |  edit (enabled)  |	

	
	@TEST_NAPORTAL-2395 @TESTSET_NAPORTAL-22327 @TESTSET_NAPORTAL-16669 @TESTSET_NAPORTAL-8935 @TESTSET_NAPORTAL-9368 @TESTSET_NAPORTAL-9610 @TESTSET_NAPORTAL-10957 @TESTSET_NAPORTAL-11448 @TESTSET_NAPORTAL-21830 @TESTSET_NAPORTAL-19599 @TESTSET_NAPORTAL-15334 @TESTSET_NAPORTAL-21277 @TESTSET_NAPORTAL-10171 @TESTSET_NAPORTAL-2317 @TESTSET_NAPORTAL-18112 @TESTSET_NAPORTAL-3981 @TESTSET_NAPORTAL-5002 @TESTSET_NAPORTAL-7599 @TESTSET_NAPORTAL-12416 @TESTSET_NAPORTAL-5366 @TESTSET_NAPORTAL-4163 @TESTSET_NAPORTAL-3984 @TESTSET_NAPORTAL-9363 @TESTSET_NAPORTAL-7600 @TESTSET_NAPORTAL-5209 @TESTSET_NAPORTAL-5208 @TESTSET_NAPORTAL-5003 @TESTSET_NAPORTAL-3980 @TESTSET_NAPORTAL-16670 @TESTSET_NAPORTAL-3901 @TESTSET_NAPORTAL-15904 @TESTSET_NAPORTAL-3669 @TESTSET_NAPORTAL-10981 @TESTSET_NAPORTAL-14357 @TESTSET_NAPORTAL-19702 @TESTSET_NAPORTAL-13285 @TESTSET_NAPORTAL-12928 @TESTSET_NAPORTAL-19587 @NADM
	Scenario: [NADM-WEB] Service search with only consult authorization
		Given I am logged in as "veacs user unauthorized to edit devices"
		And  I am on "veacs dashboard" page
		And I click "nadm navigation sidebar service"
		When I search for the following "service":
		  | field   | value         |
		  | service | cc.nom*       |
		  | type    | Voice Profile |
		  | client  | *c*           |
		Then the results of the "service" search should have the following values:
		  | service  | type          | client | action          |
		  | cc.nom.* | Voice Profile | cc     | edit (disabled) |	

	
	@TEST_NAPORTAL-2402 @TESTSET_NAPORTAL-22327 @TESTSET_NAPORTAL-16669 @TESTSET_NAPORTAL-8935 @TESTSET_NAPORTAL-19702 @TESTSET_NAPORTAL-21277 @TESTSET_NAPORTAL-21830 @TESTSET_NAPORTAL-14357 @TESTSET_NAPORTAL-5366 @TESTSET_NAPORTAL-9610 @TESTSET_NAPORTAL-10171 @TESTSET_NAPORTAL-11448 @TESTSET_NAPORTAL-12928 @TESTSET_NAPORTAL-19599 @TESTSET_NAPORTAL-2317 @TESTSET_NAPORTAL-7600 @TESTSET_NAPORTAL-13285 @TESTSET_NAPORTAL-3984 @TESTSET_NAPORTAL-9363 @TESTSET_NAPORTAL-5003 @TESTSET_NAPORTAL-9368 @TESTSET_NAPORTAL-12416 @TESTSET_NAPORTAL-10957 @TESTSET_NAPORTAL-7599 @TESTSET_NAPORTAL-5209 @TESTSET_NAPORTAL-5208 @TESTSET_NAPORTAL-5002 @TESTSET_NAPORTAL-4163 @TESTSET_NAPORTAL-16670 @TESTSET_NAPORTAL-3980 @TESTSET_NAPORTAL-3901 @TESTSET_NAPORTAL-15904 @TESTSET_NAPORTAL-3669 @TESTSET_NAPORTAL-10981 @TESTSET_NAPORTAL-15334 @TESTSET_NAPORTAL-3981 @TESTSET_NAPORTAL-18112 @TESTSET_NAPORTAL-19587 @NADM
	Scenario: [NADM-WEB] Validate VEACS admin authorizations
		Given I am logged in as "veacs admin"
		And  I am on "veacs dashboard" page
		And I click "nadm navigation sidebar global equipment"
		Then the results of the "global equipment" search should have the following values:
		  |       action                     |
		  |  edit (enabled), view (enabled)  |
		And I click "nadm navigation sidebar equipment"
		Then the results of the "equipment" search should have the following values:
		  |       action                     |
		  |  edit (enabled), view (enabled)  |
		And I click "nadm navigation sidebar client"
		Then the results of the "client" search should have the following values:
		  |       action                     |
		  |   view (enabled)                 |
		And I click "nadm navigation sidebar service"
		Then the results of the "service" search should have the following values:
		  |       action     |
		  |  edit (enabled)  |	

	
	@TEST_NAPORTAL-2403 @TESTSET_NAPORTAL-22327 @TESTSET_NAPORTAL-16669 @TESTSET_NAPORTAL-8935 @TESTSET_NAPORTAL-9368 @TESTSET_NAPORTAL-10981 @TESTSET_NAPORTAL-14357 @TESTSET_NAPORTAL-19587 @TESTSET_NAPORTAL-21277 @TESTSET_NAPORTAL-21830 @TESTSET_NAPORTAL-19702 @TESTSET_NAPORTAL-5366 @TESTSET_NAPORTAL-9363 @TESTSET_NAPORTAL-10957 @TESTSET_NAPORTAL-19599 @TESTSET_NAPORTAL-2317 @TESTSET_NAPORTAL-12928 @TESTSET_NAPORTAL-3980 @TESTSET_NAPORTAL-11448 @TESTSET_NAPORTAL-5209 @TESTSET_NAPORTAL-9610 @TESTSET_NAPORTAL-15904 @TESTSET_NAPORTAL-5003 @TESTSET_NAPORTAL-12416 @TESTSET_NAPORTAL-10171 @TESTSET_NAPORTAL-7600 @TESTSET_NAPORTAL-5208 @TESTSET_NAPORTAL-5002 @TESTSET_NAPORTAL-4163 @TESTSET_NAPORTAL-3984 @TESTSET_NAPORTAL-3981 @TESTSET_NAPORTAL-3901 @TESTSET_NAPORTAL-3669 @TESTSET_NAPORTAL-15334 @TESTSET_NAPORTAL-16670 @TESTSET_NAPORTAL-7599 @TESTSET_NAPORTAL-18112 @TESTSET_NAPORTAL-13285 @NADM
	Scenario: [NADM-WEB] Validate VEACS consult user authorizations
		Given I am logged in as "veacs user unauthorized to edit devices"
		And  I am on "veacs dashboard" page
		And I click "nadm navigation sidebar global equipment"
		Then the results of the "global equipment" search should have the following values:
		  |       action                     |
		  |  edit (disabled), view (enabled) |
		And I click "nadm navigation sidebar equipment"
		Then the results of the "equipment" search should have the following values:
		  |       action                     |
		  |  edit (disabled), view (enabled) |
		And I click "nadm navigation sidebar client"
		Then the results of the "client" search should have the following values:
		  |       action                     |
		  |  view (enabled)                  |
		And I click "nadm navigation sidebar service"
		Then the results of the "service" search should have the following values:
		  |       action     |
		  |  edit (disabled) |