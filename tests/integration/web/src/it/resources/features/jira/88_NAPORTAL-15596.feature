@REQ_NAPORTAL-15596
Feature: [Order Follow-up] Visualizar interações com sistemas externos - History
	#*What*:
	#Permitir visualizar no acompanhamento de ordem todas as interações com um sistema externo no âmbito de uma atividade.
	#
	#*Why*:
	#
	#
	#*How*:
	# Carregando no atividade na timeline ou no grafo deverá ser lançada uma modal onde é possível ver o registo de comunicação com outro sistema.
	# !image-2020-08-24-09-52-06-660.png|thumbnail! 
	#Na modal existe uma tab de execução onde é possível ver o fluxo atual de comunicação do plugin de comunicação com um sistema externo.
	# !image-2020-08-24-09-52-53-699.png|thumbnail! 
	#Existe outra tab de histórico que tem todo o resultado de comunicação anterior, ações que foram realizadas de forma manual ou automática e também as notificações consequentes.
	# !image-2020-08-24-09-57-48-929.png|thumbnail!

	
	@TEST_NAPORTAL-19289 @TESTSET_NAPORTAL-22327 @TESTSET_NAPORTAL-16669 @TESTSET_NAPORTAL-19702 @TESTSET_NAPORTAL-21277 @TESTSET_NAPORTAL-21830 @TESTSET_NAPORTAL-19599 @TESTSET_NAPORTAL-18112 @TESTSET_NAPORTAL-19587 @NAPORTAL-21613 @MONITORING
	Scenario: CT038 - [MONITORING-WEB] View external system interactions of an activity
		Given I am "administrator" on "order search" page
		When I search by order id "a8d844d8-af39-43da-bd72-b14c3e8a890711"
		Then I should be on "order view" page
		And I change to execution tab
		Then the first rows of table "order item execution" matches table:
			| operation                            | service id    | state           | duration        | end date                                             | has process |
			| external-system - CFS - CFS.SUM      | 123           | completed       | .+ segundos?    | .+ ([01]?[0-9]\|2[0-3]):[0-5][0-9]:[0-5][0-9]        | yes         |
		And The following workflow graph is rendered:
			| id  | name                           | type     | status    | system   | notification |  edges		| actions |
		    | 1   | 				               | entry    |           |          |              |  2            |         |
			| 2   | external-system-activity       | activity | success   |  DUMMY   |              |  3,4          |         |
			| 3   | end-activity                   | exit     |           |          |              |               |         |
			| 4   | automatic-rollback-activity    | exit     |           |          |              |               |         |
		And The timeline is rendered with following activities:
			| id  | name                       | status   | system | notification | actions |
			| 1   | startActivity              | success  |        | no           |         |
			| 2   | external-system-activity   | success  | DUMMY  | no           |         |
			| 3   | end-activity               | success  |        | no           |         |
		When I open activity "external-system-activity (DUMMY)" modal
		Then external system interactions modal has the following values on Execution tab:
			| request payload         | request payload        |
			| sync response payload   | sync response payload  |
			| async response payload  | async response payload |
		When I go to external system interactions modal History tab
		Then the first rows of table "order view execution activity history" matches table:
			| description | author | actions |
			|             | System | view    |