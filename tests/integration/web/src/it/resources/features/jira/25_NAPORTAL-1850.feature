@REQ_NAPORTAL-1850
Feature: [NW][CATNA][Frontend][Pagina Inicial][Security] Página de Consulta/Criação/Atualização de entidades (validar permissões)
	#O utilizador apenas deverá poder executar operações de *Criação*, *Consulta*, *Atualização* ou *Remoção* caso tenha acesso de *Create*, *Read*, *Update* ou *Delete* respetivamente sobre o recurso *catalog*
	#
	#A listagem de Tipos de Catálogo deverá ser restringida pelo perfil do utilizador (domínio)

	
	@TEST_NAPORTAL-1938 @TESTSET_NAPORTAL-22327 @TESTSET_NAPORTAL-16669 @TESTSET_NAPORTAL-8935 @TESTSET_NAPORTAL-9368 @TESTSET_NAPORTAL-10171 @TESTSET_NAPORTAL-11448 @TESTSET_NAPORTAL-19702 @TESTSET_NAPORTAL-21830 @TESTSET_NAPORTAL-21277 @TESTSET_NAPORTAL-14357 @TESTSET_NAPORTAL-16670 @TESTSET_NAPORTAL-19599 @TESTSET_NAPORTAL-5366 @TESTSET_NAPORTAL-9363 @TESTSET_NAPORTAL-10957 @TESTSET_NAPORTAL-12928 @TESTSET_NAPORTAL-1899 @TESTSET_NAPORTAL-15904 @TESTSET_NAPORTAL-5003 @TESTSET_NAPORTAL-1997 @TESTSET_NAPORTAL-12416 @TESTSET_NAPORTAL-9610 @TESTSET_NAPORTAL-7599 @TESTSET_NAPORTAL-5209 @TESTSET_NAPORTAL-5208 @TESTSET_NAPORTAL-5002 @TESTSET_NAPORTAL-3984 @TESTSET_NAPORTAL-3981 @TESTSET_NAPORTAL-3980 @TESTSET_NAPORTAL-3901 @TESTSET_NAPORTAL-3669 @TESTSET_NAPORTAL-15334 @TESTSET_NAPORTAL-7600 @TESTSET_NAPORTAL-4163 @TESTSET_NAPORTAL-18112 @TESTSET_NAPORTAL-13285 @TESTSET_NAPORTAL-10981 @TESTSET_NAPORTAL-19587 @CATNA
	Scenario: [CATNA-WEB] Security of search, create, edit, clone and delete catalog instances
		Given I am logged in as "catalog admin"
		And I am on "catalog dashboard" page
		
		When I create a new "Lista Telefones" entry with the following values from the "dashboard" page:
		  | field         | value             |
		  | name          | Telefone1         |
		  | state         | Ativo             |
		  | external code | 444522            |
		  | phone number  | 22291937          |
		
		Then I should be on view page of "Lista Telefones" with the following values:
		  | view name      | view state   | view external code   | view telephone number |
		  | Telefone1      | Ativo        | 444522               | 22291937              |
		
		
		When I create a new "Lista OLTs" entry with the following values from the "search" page:
		  | field         | value             |
		  | name          | OLTPorto          |
		  | state         | Ativo             |
		  | external code | 113EX21           |
		  | ip            | ************      |
		  | login         | admin             |
		
		Then I should be on view page of "Lista OLTs" with the following values:
		  | view name      | view state   | view external code   | view ip      | view login |
		  | OLTPorto       | Ativo        | 113EX21              | ************ |  admin     |
		
		#End of test data set up
		
		And I switch to user "catalog user with read permissions"
		And I am on "catalog dashboard" page
		Then I shouldn't be able to create a new catalog
		And I should be able to search for catalogs of type "Lista OLTs, Lista Telefones" from "dashboard" page
		When I search for a catalog of type "Lista Telefones" by the field "name" with value "Telefone1" from "search" page
		Then the table columns of "table telephone list" contains:
		  | column  | value                                  |
		  | action  | view (visible), more options (hidden)  |
		  | name    | Telefone1                              |
		
		And I switch to user "catalog user authorized to consult Telephone List and Operation in OLT"
		And I am on "catalog dashboard" page
		Then I should be able to create a new catalog instance of type "Lista OLTs"
		And I should be able to search only for catalogs of type "Lista OLTs, Lista Telefones" from "search" page
		#Telephone List
		When I search for a catalog of type "Lista Telefones" by the field "name" with value "Telefone1" from "search" page
		Then the table columns of "table telephone list" contains:
		  | column  | value                                  |
		  | action  | view (visible), more options (hidden)  |
		  | name    | Telefone1                              |
		#OLT
		When I search for a catalog of type "Lista OLTs" by the field "name" with value "OLTPorto" from "search" page
		Then the table columns of "table OLT list" contains:
		  | column  | value                                  |
		  | action  | view (visible), clone (visible)        |
		  | name    | OLTPorto                               |
		
		#Clean up of test data
		And I switch to user "catalog admin"
		And I am on "catalog dashboard" page
		And I delete the first entry of "Lista Telefones" with field "name" with value "Telefone1"
		Then searching for the following values of type "Lista Telefones" should return nothing:
		 | field         | value                |
		 | catalog type  | Lista Telefones      |
		 | name          | Telefone1            |
		
		When I delete the first entry of "Lista OLTs" with field "name" with value "OLTPorto"
		Then searching for the following values of type "Lista OLTs" should return nothing:
		  | field         | value                |
		  | catalog type  | Lista OLTs           |
		  | name          | OLTPorto             |