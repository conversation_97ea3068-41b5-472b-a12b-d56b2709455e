@REQ_NAPORTAL-13379
Feature: [MONITORING] Ações no contexto do detalhe da ordem
	#Deverá ser possível a executar as seguinte ações no contexto do detalhe da ordem:
	#* Cancel
	#* Pause
	#* Skip
	#* Retry
	#* Complete
	#
	#Sempre que seja pedida uma ação deve ser pedido o motivo pela qual se pretende realizar à semelhança de quando são executadas no detalhe do workflow no contexto de uma ordem.
	#Deve ser apresentado junto à ação o icone respetivo.
	#
	#Segue o local onde colocar as ações:
	# !screenshot-1.png|thumbnail!

	
	@TEST_NAPORTAL-13587 @TESTSET_NAPORTAL-22327 @TESTSET_NAPORTAL-21830 @TESTSET_NAPORTAL-21277 @TESTSET_NAPORTAL-18112 @TESTSET_NAPORTAL-19702 @TESTSET_NAPORTAL-13285 @TESTSET_NAPORTAL-19599 @TESTSET_NAPORTAL-15334 @TESTSET_NAPORTAL-16670 @TESTSET_NAPORTAL-15904 @TESTSET_NAPORTAL-10981 @TESTSET_NAPORTAL-16669 @TESTSET_NAPORTAL-14357 @TESTSET_NAPORTAL-19587 @MONITORING
	Scenario: [MONITORING-WEB] Cancel an order in the order view page using the dropdown button
		Given I am "administrator" on "order search" page
		When I search by order id "4abdbc19-ba61-4032-989e-82301b7660a3"
		Then I should be on "order view" page
		When I cancel the order with reason "order view cancel test"
		Then an "MONITORING order canceled successfully" success notification with params should appear:
		  | param   | value                                |
		  | order   | 4abdbc19-ba61-4032-989e-82301b7660a3 |