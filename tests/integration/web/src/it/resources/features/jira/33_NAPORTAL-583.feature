@REQ_NAPORTAL-583
Feature: [MPT] O Utilizador deverá poder exportar o ficheiro original do Bulk
	#O utilizador deverá poder efetuar o download do ficheiro original do Bulk, que foi submetido na criação.

	
	@TEST_NAPORTAL-872 @TESTSET_NAPORTAL-22327 @TESTSET_NAPORTAL-16669 @TESTSET_NAPORTAL-8935 @TESTSET_NAPORTAL-9368 @TESTSET_NAPORTAL-21277 @TESTSET_NAPORTAL-21830 @TESTSET_NAPORTAL-19599 @TESTSET_NAPORTAL-19702 @TESTSET_NAPORTAL-10171 @TESTSET_NAPORTAL-862 @TESTSET_NAPORTAL-7600 @TESTSET_NAPORTAL-12416 @TESTSET_NAPORTAL-3901 @TESTSET_NAPORTAL-5209 @TESTSET_NAPORTAL-11448 @TESTSET_NAPORTAL-10957 @TESTSET_NAPORTAL-4163 @TESTSET_NAPORTAL-10981 @TESTSET_NAPORTAL-15904 @TESTSET_NAPORTAL-12928 @TESTSET_NAPORTAL-9610 @TESTSET_NAPORTAL-5366 @TESTSET_NAPORTAL-5003 @TESTSET_NAPORTAL-5002 @TESTSET_NAPORTAL-3984 @TESTSET_NAPORTAL-16670 @TESTSET_NAPORTAL-3980 @TESTSET_NAPORTAL-3669 @TESTSET_NAPORTAL-15334 @TESTSET_NAPORTAL-5208 @TESTSET_NAPORTAL-9363 @TESTSET_NAPORTAL-3981 @TESTSET_NAPORTAL-7599 @TESTSET_NAPORTAL-18112 @TESTSET_NAPORTAL-14357 @TESTSET_NAPORTAL-13285 @TESTSET_NAPORTAL-19587 @MPT
	Scenario: [MPT-WEB] Searching for bulk results, filters with and without wildcard(*)
		Given I am logged in as "mpt user authorized to create operation"
		When I go to page "mpt main"
		And I create mpt operation "estadoONT" of family "gpon" with files "estadoONT_sample.csv"
		And I create mpt operation "testeLatenciaSoma" of family "SUM" with files "SUM.csv"
		When I submit the following "bulk" search:
		    | field         | value                |
		    | start date    | yesterday            |
		    | user          | *MPT                 |
		    | family        | gpon                 |
		    | operation     | estado*              |
		    | file          | *ONT*                |
		    | state         | Terminado            |
		Then the table columns of "mpt bulk search result" contains:
		    | column        | value                |
		    | user          | ^.*MPT$              |
		    | family        | gpon                 |
		    | operation     | ^estado.*$           |
		    | file          | ^.*ONT.*$            |
		    | state         | Terminado            |
		When I open first "bulk" detail
		Then I go back
		And the form maintains the same values
		And I wait 1 seconds
		When I open first "bulk" detail
		Then I download "original" file
		And I download "result" file
		Then I should see following bulk characteristics
		    | field         | value                |
		    | filename      | estadoONT_sample.csv |
		    | family        | gpon                 |
		    | operation     | estadoONT            |
		When I select "results" tab
		And I wait 1 seconds
		And I submit the following "bulk operation" search:
		    | field                 | value        |
		    | status                | NOK          |
		    | execution state       | Terminado    |
		    | OLT_FABRICANTE        | ALCATEL      |
		    | OLT_MODELO            | *SAM*        |
		    | OLT_VERSAO            | 4.7.*        |
		Then the table columns of "mpt bulk detail search results" contains:
		    | column                | value        |
		    | key                   | 01ZZ01/50    |
		    | state                 | NOK          |
		    | execution             | Terminado    |
		    | OLT_FABRICANTE        | ALCATEL      |
		    | OLT_MODELO            | ISAM7342     |
		    | OLT_VERSAO            | 4.7.13       |
		When I open first "bulk operation" detail
		Then I should see following bulk result characteristics
		    | field        | value                 |
		    | filename     | estadoONT_sample.csv  |
		    | family       | gpon                  |
		    | operation    | estadoONT             |
		    | line         | 5                     |
		    | username     | OPER_MPT              |
		    | status       | NOK                   |
		When I go back
		Then the form maintains the same values	

	
	@TEST_NAPORTAL-11449 @TESTSET_NAPORTAL-22327 @TESTSET_NAPORTAL-15334 @TESTSET_NAPORTAL-16670 @TESTSET_NAPORTAL-19702 @TESTSET_NAPORTAL-21830 @TESTSET_NAPORTAL-19587 @TESTSET_NAPORTAL-21277 @TESTSET_NAPORTAL-10981 @TESTSET_NAPORTAL-13285 @TESTSET_NAPORTAL-14357 @TESTSET_NAPORTAL-19599 @TESTSET_NAPORTAL-11448 @TESTSET_NAPORTAL-12416 @TESTSET_NAPORTAL-15904 @TESTSET_NAPORTAL-16669 @TESTSET_NAPORTAL-18112 @TESTSET_NAPORTAL-12928 @MPT
	Scenario: [MPT-WEB] Create and edit "No Limit" schedule
		Given I am logged in as "mpt user authorized to create operation"
		When I go to page "mpt main"
		Then I create mpt bulk till schedule step
			| field               | value            |
			| family              | SUM              |
			| operation           | testeSleep       |
			| description         | Sleep test.      |
			| files               | sleep.csv        |
			| scheduleType        | schedule         |
		And add schedule "first" successfully
			| field               | value            |
			| start date          | 01-04-2050 12:00 |
			| term-type           | noLimit          |
		And finish mpt bulk wizard
		Then I edit mpt bulk till schedule step after transiting to the "Agendado" state
			| field               | value            |
			| description         | Sleep test.      |
			| files               | sleep.csv        |
			| scheduleType        | schedule         |
		And edit interval "1", unique interval from schedule "first"
			| field               | value            |
			| start date          | 02-04-2050 13:00 |
			| term-type           | noLimit          |
		And finish mpt bulk wizard
		When I "view" bulk after transiting to the "Agendado" state
		Then I should see following bulk characteristics
			| field               | value            |
			| filename            | sleep.csv        |
			| family              | SUM              |
			| operation           | testeSleep       |
			| description         | Sleep test.      |
		And the first rows of table "bulk schedule" matches table:
			| Data Início       | Data Fim  | Hora Início | Duração | Periodicidade |
			| 02-04-2050 13:00  |    --     |    --       |    --   |    --         |
		And the first rows of table "bulk transitions" matches table:
			| Estado      |
			| Agendado    |
			| Pendente    |
			| A Validar   |
		And I download "original" file
		And I cannot download "result" file