@REQ_NAPORTAL-1163
Feature: [ GO ] [ Administração ] [ Wizzard Criação ] O utilizador deverá poder configurar novas operações a partir de um template
	#O Utilizador com perfil de Administrador GO poderá criar operações genéricas a partir de templates pai, para tal deverá ter acesso a um wizzard de criação de operações.
	#A  instanciação do wizzard de criação de operações, poderá ter origem o botão de ação criar operação ou na edição de uma operação já configurada.
	#Apenas as  operações resultantes de configuração pelo utilizador poderão ser editáveis e apenas operações  de sistema deverão dar origem a novas tarefas.
	#
	#O wizzard de criação de operações com base em templates terá dois passos:
	## Passo 1: Caracterização da Operação, passo em que é selecionada a operação original, contextualizada pela Família, Rfs e Operação. Tanto a Família como o RFS deverão ser filtrados pelo contexto de autorização do utilizador.
	## Passo 2: Caracterização dos Parâmetros da Operação, passo em que são configurados os valores a efetuar override, e o nome da nova operação que deverá ser único no contexto Família, Rfs.
	#** Na população dos campos a fazer override as regras de negocio definidas pelo template pai deverão ser respeitadas. (ex. Se um campo tiver definida uma expressão regular o seu override deverá respeitar a mesma)
	#** Caso o utilizador tente finalizar o fluxo de configuração sem efetuar override a nenhum campo este não deverá poder finalizar a operação de configuração.

	
	@TEST_NAPORTAL-1687 @TESTSET_NAPORTAL-22327 @TESTSET_NAPORTAL-16669 @TESTSET_NAPORTAL-8935 @TESTSET_NAPORTAL-9610 @TESTSET_NAPORTAL-11448 @TESTSET_NAPORTAL-12416 @TESTSET_NAPORTAL-21830 @TESTSET_NAPORTAL-21277 @TESTSET_NAPORTAL-14357 @TESTSET_NAPORTAL-19702 @TESTSET_NAPORTAL-5366 @TESTSET_NAPORTAL-10171 @TESTSET_NAPORTAL-19599 @TESTSET_NAPORTAL-1657 @TESTSET_NAPORTAL-4163 @TESTSET_NAPORTAL-9363 @TESTSET_NAPORTAL-10957 @TESTSET_NAPORTAL-7600 @TESTSET_NAPORTAL-5209 @TESTSET_NAPORTAL-5208 @TESTSET_NAPORTAL-5003 @TESTSET_NAPORTAL-5002 @TESTSET_NAPORTAL-3984 @TESTSET_NAPORTAL-3980 @TESTSET_NAPORTAL-3901 @TESTSET_NAPORTAL-15904 @TESTSET_NAPORTAL-3669 @TESTSET_NAPORTAL-16670 @TESTSET_NAPORTAL-15334 @TESTSET_NAPORTAL-9368 @TESTSET_NAPORTAL-7599 @TESTSET_NAPORTAL-3981 @TESTSET_NAPORTAL-18112 @TESTSET_NAPORTAL-13285 @TESTSET_NAPORTAL-12928 @TESTSET_NAPORTAL-10981 @TESTSET_NAPORTAL-19587 @GO
	Scenario: [GO-WEB] validating GO Templates Wizard parameter steps
		Given I am logged in
		And I am on "go template search" page
		When I go to parameter tab of template creation of template "ALL_COMPONENTS_estadoONT" of rfs "rfsGponDiagnostics" and family "GPON"
		And I complete wizard
		Then an "required fields" error notification should appear
		When I open edit of GO template parameter "ONT_ID"
		And I complete wizard
		Then an "GO template parameter edit mode" error notification should appear
		When I set GO template parameter "ONT_ID" to "66s"
		Then the GO template parameter "ONT_ID" has a "must be a number" alert notification
		When I cancel edit of GO template parameter "ONT_ID"
		And I set GO template name to "validation_test"
		And I go to previous step
		And I go to next step
		And I complete wizard
		Then I should be on "go template search" page
		When I delete created GO template "validation_test" of rfs "rfsGponDiagnostics" and family "GPON"
		Then I should be on "go template search" page