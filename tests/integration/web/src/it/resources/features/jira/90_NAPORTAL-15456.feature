@REQ_NAPORTAL-15456
Feature: [Order Follow-up] Visualizar nomes nos ramos de decisão
	#*What*:
	#Deverá ser possível visualizar uma label em cada ramo de decisão.
	#
	#*Why*:
	#Desta forma o utilizador do módulo de monitoria consegue ter mais contexto sobre a razão pela qual foi escolhido aquele ramo e não o outro.
	#
	#*How*:
	#Ler a informação a partir do modelo de persistência interno e apresentar.
	#
	#Segue um exemplo do resultado final:
	# !image-2020-08-04-10-53-02-471.png|thumbnail!

	
	@TEST_NAPORTAL-19290 @TESTSET_NAPORTAL-22327 @TESTSET_NAPORTAL-16669 @TESTSET_NAPORTAL-19702 @TESTSET_NAPORTAL-21277 @TESTSET_NAPORTAL-21830 @TESTSET_NAPORTAL-19599 @TESTSET_NAPORTAL-18112 @TESTSET_NAPORTAL-19587 @NAPORTAL-21613 @MONITORING
	Scenario: CT039 - [MONITORING-WEB] Validate that a decision edge label between activities is correctly displayed
		Given I am "administrator" on "order search" page
		When I search by order id "9b34334d-c87b-49c9-8fcb-AAAAA90016988"
		Then I should be on "order view" page
		Then The order summary has the following values:
		  | name          | 9b34334d-c87b-49c9-8fcb-AAAAA90016988 - CFS SUM Na Portal Test - Decision Workflow  |
		  | system        | tester_workflow_engine                                                              |
		  | description   | Workflow Na Portal Engine Test - Execution of operation                             |
		When I change to execution tab.
		Then the first rows of table "order item execution" matches table:
		  | operation                                    | service id             | state                            | duration       | end date                                             | has process |
		  | decision-workflow-na-portal - CFS - CFS.SUM  | 123-NA-PORTAL-DECISION | held - Error .+                  | .+             | .+ ([01]?[0-9]\|2[0-3]):[0-5][0-9]:[0-5][0-9]        | yes         |
		And The following workflow graph is rendered:
		  | id			    | name                                        | type   	 | status    	| system   | notification	|  edges		|  connectionsLabels             | actions          |
		  | 1	            |                                             | entry  	 |    	     	|    	   | 				|  2	        |  	                             |                  |
		  | 2	            | Order item is valid - starting execution    | activity | success      |    	   | 				|  3	        |  	                             |                  |
		  | 3	            | decision                                    | decision | success      |    	   | 				|  4,5	        |  ,Label to configure network	 |                  |
		  | 4	            | Network configuration finished              | activity | not executed |    	   | 				|  6	        |  	                             |                  |
		  | 5	            | Configure network                           | activity | success      |    	   | 				|  6	        |  	                             |                  |
		  | 6	            | Register in the inventory                   | activity | success      |    	   | 				|  7	        |  	                             |                  |
		  | 7	            | Inventory registration finished	          | activity | success      |    	   | 				|  8	        |  	                             |                  |
		  | 8	            | LR PonPhysicalConnection			          | activity | success     	|          | 				|  9   	        |     	                         |                  |
		  | 9	            | VOIP                   			          | activity | success     	|          | 				|  10  	        |   	                         |                  |
		  | 10	            | RFS CPE                   			      | activity | error     	|          | 				|  11  	        |    	                         | skip, retry      |
		  | 11	            |               		                      | exit     |           	|          | 				|      	        |      	                         |                  |
		And The timeline is rendered with following activities:
		  | id	    | name                       				| status   | system  | notification	| actions      | type     |
		  | 1       | startActivity                             | success  |         |              |              | start    |
		  | 2	    | Order item is valid - starting execution  | success  |         |              |              | base     |
		  | 3	    | decision                                  | success  |         |              |              | decision |
		  | 4	    | Configure network	                        | success  |         |              |              | base     |
		  | 5	    | Register in the inventory                 | success  |         |              |              | base     |
		  | 6	    | Inventory registration finished	        | success  |         |              |              | base     |
		  | 7	    | LR PonPhysicalConnection			        | success  |         |              |              | base     |
		  | 8	    | VOIP                   			        | success  |         |              |              | base     |
		  | 9	    | RFS CPE                   			    | error    |         |              | skip, retry  | base     |