@REQ_NAPORTAL-10031
Feature: [Order Follow-up] Suportar gestão de atividades manuais
	#*What*
	# Quando um order item está num estado {{PENDING}} é necessário fazer realizar uma ação que não está prevista realizar de forma automática no contexto do workflow. A gestão de atividades manuais é feita usando os icones localizados ao lado da atividade que se pode ver no screenshot abaixo.
	#
	# !screenshot-1.png! 
	#
	#Existem várias ações que se podem realizar sobre a atividade:
	#||Icone||Significado||Action||
	#| !screenshot-2.png|thumbnail!|Dar a tarefa como concluída.
	# Clicando no icone deve ser visualizada uma modal, com caixa de texto, para adicionar uma mensagem que fica associada à ação.
	# Tal como definido no seguinte screenshot:
	# !screenshot-3.png|thumbnail! |complete|
	#| !screenshot-4.png|thumbnail! |Ignorar esta atividade e passar para a próxima.|skip|
	#
	#*Why*
	# Quando temos uma atividade deste tipo prevista no workflow é necessário que ele pare e fique à espera que alguém possa intervir nele com as ações acima especificadas.
	#
	#*How*
	# A disponibilização das ações é baseada no que o serviço de backend retorna que é possível fazer nessa atividade. Quando é clicado em cada uma das ações devem existir APIs de backend que sejam responsáveis por elas. Essas APIs estão especificadas na página wiki [Workflow & Activity Model Definition|http://wiki.ptin.corppt.com/pages/viewpage.action?pageId=1088323820].

	
	@TEST_NAPORTAL-19283 @TESTSET_NAPORTAL-22327 @TESTSET_NAPORTAL-16669 @TESTSET_NAPORTAL-19599 @TESTSET_NAPORTAL-21277 @TESTSET_NAPORTAL-21830 @TESTSET_NAPORTAL-19587 @TESTSET_NAPORTAL-19702 @TESTSET_NAPORTAL-18112 @NAPORTAL-21613 @MONITORING
	Scenario: CT033 - [MONITORING-WEB]  Action complete activity on workflow graph and timeline
		Given I am "administrator" on "order search" page
		When I search by order id "2d2459d6-4ad5-4104-8ece-087c2c0c7e98"
		When I change to execution tab.
		When I select item with id "0b00ac4a-aa42-456d-9ddd-77553d2a400d"
		Then The following workflow graph is rendered:
			| id			| name                                        | type   	 | status    	| system   | notification	|  edges        | actions                 |
			| 1	            |                                             | entry  	 |    	     	|    	   | 				|  2	        |                         |
			| 2	            | Order item is valid - starting execution	  | activity | success      |    	   | 	yes			|  3	        | report                  |
			| 3	            | Configure network		                      | activity | pending   	| NA       | 				|  4   	        | complete, skip          |
			| 4	            |               		                      | exit     |           	|          | 				|      	        |                         |
		Then The timeline is rendered with following activities:
			| id	| name                       				| status   | system  | notification	| actions               |
			| 2		| Order item is valid - starting execution	| success  | 		 | 	yes			| report                |
			| 3		| Configure network                  	    | pending  | NA	 	 | 	no			| complete, skip        |
		When I complete activity "Configure network" because "Instalação confirmada".
		Then an "MONITORING activity completed" success notification should appear