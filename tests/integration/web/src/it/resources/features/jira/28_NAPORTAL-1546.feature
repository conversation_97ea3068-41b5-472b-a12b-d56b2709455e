@REQ_NAPORTAL-1546
Feature: [NW][CATNA][Frontend][Pagina Inicial] Página de Consulta/Criação/Atualização de entidades
	#O utilizador deverá ter acesso a uma página para Consultar/Criar/Atualizar catálogo.
	#Os campos disponibilizar dependem do configurado no tipo de entidade
	#
	#Seguir o especificado pela FUXI.

	
	@TEST_NAPORTAL-1854 @TESTSET_NAPORTAL-22327 @TESTSET_NAPORTAL-9368 @TESTSET_NAPORTAL-19702 @TESTSET_NAPORTAL-21830 @TESTSET_NAPORTAL-21277 @TESTSET_NAPORTAL-19599 @TESTSET_NAPORTAL-8935 @TESTSET_NAPORTAL-9363 @TESTSET_NAPORTAL-10957 @TESTSET_NAPORTAL-11448 @TESTSET_NAPORTAL-12416 @TESTSET_NAPORTAL-12928 @TESTSET_NAPORTAL-1997 @TESTSET_NAPORTAL-1848 @TESTSET_NAPORTAL-1899 @TESTSET_NAPORTAL-5208 @TESTSET_NAPORTAL-5003 @TESTSET_NAPORTAL-10171 @TESTSET_NAPORTAL-7599 @TESTSET_NAPORTAL-15904 @TESTSET_NAPORTAL-14357 @TESTSET_NAPORTAL-13285 @TESTSET_NAPORTAL-9610 @TESTSET_NAPORTAL-7600 @TESTSET_NAPORTAL-5366 @TESTSET_NAPORTAL-5209 @TESTSET_NAPORTAL-5002 @TESTSET_NAPORTAL-3984 @TESTSET_NAPORTAL-16670 @TESTSET_NAPORTAL-3980 @TESTSET_NAPORTAL-3901 @TESTSET_NAPORTAL-3669 @TESTSET_NAPORTAL-18112 @TESTSET_NAPORTAL-19587 @TESTSET_NAPORTAL-4163 @TESTSET_NAPORTAL-3981 @TESTSET_NAPORTAL-15334 @CATNA
	Scenario: [CATNA-WEB] Search, create, edit, clone and delete catalog instances
		Given I am logged in
		  And I am on "catalog dashboard" page
		
		  # CREATE
		  When I create a new "Lista Telefones" entry with the following values from the "dashboard" page:
		    | field         | value             |
		    | name          | Telefone1         |
		    | state         | Ativo             |
		    | external code | 444522            |
		    | phone number  | 22291937          |
		
		  Then I should be on view page of "Lista Telefones" with the following values:
		    | view name      | view state   | view external code   | view telephone number |
		    | Telefone1      | Ativo        | 444522               | 22291937              |
		     ## End of create
		
		  # SEARCH
		  When I search for the following catalogs from "dashboard" page:
		    | field         | value                |
		    | catalog type  | Lista Telefones      |
		    | name          | Telefone1            |
		    | state         | Ativo                |
		    | external code | 444522               |
		    | phone number  | 22291937             |
		  # Not using the table configs... yet...
		  Then the results table of elements of type "Lista Telefones" should match table:
		    | name           | state    | external code    | telephone number  |
		    | Telefone1      | Ativo    | 444522           | 22291937          |
		
		  #  EDIT
		  When I edit the first entry of "Lista Telefones" with name "Telefone1" to the following values:
		    | field         | value             |
		    | name          | Telefone2         |
		    | state         | Ativo             |
		    | external code | 4445              |
		    | phone number  | 22291937          |
		  Then I should be on view page of "Lista Telefones" with the following values:
		    | view name      | view state   | view external code   | view telephone number |
		    | Telefone2      | Ativo        | 4445                 | 22291937              |
		
		  # CLONE
		  When I clone the first entry of "Lista Telefones" with name "Telefone2" to the following values:
		    | field         | value             |
		    | name          | Telefone3         |
		    | state         | Ativo             |
		    | external code | 1234              |
		    | phone number  | 33441122          |
		  Then I should be on view page of "Lista Telefones" with the following values:
		    | view name      | view state   | view external code   | view telephone number |
		    | Telefone3      | Ativo        | 1234                 | 33441122              |
		
		  # No repeated names test
		  When I create a new "Lista Telefones" entry with the following values from the "search" page:
		    | field         | value             |
		    | name          | Telefone2         |
		    | state         | Ativo             |
		    | external code | 444522            |
		    | phone number  | 22291937          |
		  Then it should warn me of a repeated "Telefone2" name
		
		    # DELETE CLONED
		  When I delete the first entry of "Lista Telefones" with field "name" with value "Telefone3"
		  Then searching for the following values of type "Lista Telefones" should return nothing:
		    | field         | value                |
		    | catalog type  | Lista Telefones      |
		    | name          | Telefone3            |
		
		   # DELETE ORIGINAL
		  When I delete the first entry of "Lista Telefones" with field "name" with value "Telefone2"
		  Then searching for the following values of type "Lista Telefones" should return nothing:
		    | field         | value                |
		    | catalog type  | Lista Telefones      |
		    | name          | Telefone2            |