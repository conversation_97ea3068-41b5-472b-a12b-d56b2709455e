@REQ_NAPORTAL-5376
Feature: [OPERATIONS_CATALOG][VERSION][SEARCH] Pesquisa de versões de catálogo
	#O utilizador deverá poder pesquisar as versões existentes no catálogo de operações.

	#Testa a pesquisa de versões com wildcards
	@TEST_NAPORTAL-5703 @TESTSET_NAPORTAL-22327 @TESTSET_NAPORTAL-16669 @TESTSET_NAPORTAL-8935 @TESTSET_NAPORTAL-11448 @TESTSET_NAPORTAL-19599 @TESTSET_NAPORTAL-19702 @TESTSET_NAPORTAL-21830 @TESTSET_NAPORTAL-21277 @TESTSET_NAPORTAL-10981 @TESTSET_NAPORTAL-16670 @TESTSET_NAPORTAL-10171 @TESTSET_NAPORTAL-9368 @TESTSET_NAPORTAL-9363 @TESTSET_NAPORTAL-9610 @TESTSET_NAPORTAL-5366 @TESTSET_NAPORTAL-15904 @TESTSET_NAPORTAL-14357 @TESTSET_NAPORTAL-12928 @TESTSET_NAPORTAL-18112 @TESTSET_NAPORTAL-15334 @TESTSET_NAPORTAL-13285 @TESTSET_NAPORTAL-12416 @TESTSET_NAPORTAL-10957 @TESTSET_NAPORTAL-19587 @OPERATIONS_CATALOG
	Scenario: [OPERATIONS_CATALOG][VERSIONS] Test version search with wildcards
		Given I am "administrator" on "operations versions search" page
		When I submit form "versions search" with the following values:
		  | field             | value                 |
		  | name              | SELFNET-TESTES-1.0.0  |
		Then the table columns of "versions search" contains:
		  | column            | value                 |
		  | name              | SELFNET-TESTES-1.0.0  |
		When I submit form "versions search" with the following values:
		  | field             | value                 |
		  | name              | SELFNET*              |
		Then the table columns of "versions search" contains:
		  | column            | value                 |
		  | name              | SELFNET.*             |
		When I submit form "versions search" with the following values:
		  | field             | value                 |
		  | name              | *1.0.0                |
		  | state             | Desenvolvimento       |
		Then the table columns of "versions search" contains:
		  | column            | value                 |
		  | name              | .*1\.0\.0$            |
		  | state             | Desenvolvimento       |
		When I submit form "versions search" with the following values:
		  | field             | value                 |
		  | name              | *TESTES*              |
		  | state             | Desenvolvimento       |
		  | entity version    | SELFNET-TESTES-1.0.0  |
		Then the table columns of "versions search" contains:
		  | column            | value                 |
		  | name              | .*TESTES.*            |
		  | state             | Desenvolvimento       |
		  | entity version    | SELFNET-TESTES-1.0.0  |