@REQ_NAPORTAL-3758
Feature: [VEACS] Configuração de agregação de linhas
	#{quote}
	#A configuração de agregação de linhas apenas deve ser aplicada em teclas onde possam ser configuradas linhas.
	#Para identificar essa necessidade foi acrescentado um atributo “linesOnly”, tal como se pode ver no exemplo seguinte:
	#
	#<opt label="Aggregate Line" linesOnly="true">aggregateLine</opt>
	#{quote}
	#
	#Para suportar esta funcionalidade deverá ser possível criar dependência entre componentes web.
	#Para o caso, apenas deverão ser suportadas dependências entre _selectable_ _components_ e _options_.
	#
	#*exemplo:*
	#
	#Se S1 tem opção 1, 2, 3 , então S2 irá ter dependência para o valor selecionado em S1 sendo isso representado por Opção1 de S2 depends on S1 in [1,2]

	
	@TEST_NAPORTAL-16676 @TESTSET_NAPORTAL-22327 @TESTSET_NAPORTAL-16669 @TESTSET_NAPORTAL-21277 @TESTSET_NAPORTAL-21830 @TESTSET_NAPORTAL-19587 @TESTSET_NAPORTAL-19702 @TESTSET_NAPORTAL-16670 @TESTSET_NAPORTAL-18112 @TESTSET_NAPORTAL-19599 @NADM
	Scenario: [NADM-WEB] Validate option fiters in accordance to it's dependencicy
		Given I am logged in as "veacs admin"
		And  I am on "veacs dashboard" page
		And I click "nadm navigation sidebar equipment"
		And  I search for the following "equipment":
		  | field           | value             |
		  | manufacturer    | cisco   			|
		  | model           | SPA501G+SPA500S   |
		  | version         | 7.5.2b            |
		And I click 2nd row action "edit" of column "action" of table "equipment"
		And I click "associations tab"
		And I click 3rd row action "edit-custom-key" of column "action" of table "equipment association"
		Then the form "equipment association edit custom key" should have the following values
		  | field  | value      |
		  | keyid  |  5         |
		And The association "custom key" field "type" should have the following options: "Speed Dial,BLF"
		Then The association "custom key" field "type" should have options "Speed Dial" when field "keyid" have any of the following values: "3,4,5"
		Then The association "custom key" field "type" should have options "Speed Dial,BLF" when field "keyid" have any of the following values: "1,2"
		When I set form "equipment association edit custom key" values to:
		  | field  | value               |
		  | type   |  BLF		         |
		Then The association "custom key" field "keyid" should have the following options: "1,2"
		When I set form "equipment association edit custom key" values to:
		  | field  | value         |
		  | type   |  Speed Dial   |
		Then The association "custom key" field "keyid" should have the following options: "1,2,3,4,5"
		When I set form "equipment association edit custom key" values to:
		  | field   | value         |
		  | keyid   |  2            |
		  | line    |  1            |
		  | name    |  Integration  |
		  | monitor |  Test         |
		  | type    |  Speed Dial   |
		And confirm the modification
		And I click 3rd row action "view-custom-key" of column "action" of table "equipment association"
		Then The association "custom key details" modal should have the following values:
		  | key id | line  |    name     | monitor |    type     |
		  |   2    |   1   | Integration |  Test   |  speedDial  |