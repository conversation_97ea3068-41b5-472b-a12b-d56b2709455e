@REQ_NAPORTAL-5709
Feature: [OPERATIONS_CATALOG][OPERATIONS][RFS] view - General Characteristics
	#O utilizador deverá poder consultar a página de características gerais.
	# # listar atributos de entrada na sua hierarquia, obrigatoriedade e regras de validação
	# # listar atributos de execução (apenas tabela! Funções de enriquecimento não serão implementada neste issue.)
	# # listar atributos de saída agrupados por entidade que os contém.
	#
	#Ver documento
	# [http://wiki.ptin.corppt.com/pages/viewpage.action?pageId=798163173&preview=/798163173/922419278/NAFUXI-82_%5BSWE-Catalogo%5DCatalogoOperacoes_mockupsBaixa_V6_2.pdf]

	
	@TEST_NAPORTAL-5838 @TESTSET_NAPORTAL-22327 @TESTSET_NAPORTAL-16669 @TESTSET_NAPORTAL-8935 @TESTSET_NAPORTAL-9610 @TESTSET_NAPORTAL-10171 @TESTSET_NAPORTAL-21830 @TESTSET_NAPORTAL-21277 @TESTSET_NAPORTAL-15334 @TESTSET_NAPORTAL-19702 @TESTSET_NAPORTAL-5366 @TESTSET_NAPORTAL-9363 @TESTSET_NAPORTAL-9368 @TESTSET_NAPORTAL-10957 @TESTSET_NAPORTAL-19599 @TESTSET_NAPORTAL-18112 @TESTSET_NAPORTAL-11448 @TESTSET_NAPORTAL-12928 @TESTSET_NAPORTAL-14357 @TESTSET_NAPORTAL-10981 @TESTSET_NAPORTAL-16670 @TESTSET_NAPORTAL-15904 @TESTSET_NAPORTAL-13285 @TESTSET_NAPORTAL-12416 @TESTSET_NAPORTAL-19587 @OPERATIONS_CATALOG
	Scenario: [OPERATIONS-CATALOG][RFS] View characteristics tab and check the attributes
		Given I am "administrator" on "operations rfs search" page
		When I view rfs "ADD" on version "SELFNET-TESTES-1.0.3" with following characteristics:
		  | description   | ic scenario     |
		  | state         | blocked         |
		  | entity        | RFS.FLOWT       |
		  | version state | production      |
		  | tags          | selfnet         |
		  | workflow      | generic         |
		And The operation input attributes table has the following values:
		  | attribute							     | cardinality | validation rule       	               |
		  | appManagerAppInstanceUrl 			     | 0..1        | Dimensão( 15, 15 )                    |
		  | cloneIpAddress                   	     | 0..1        | Expressão regular, Dimensão( 15, 15 ) |
		  | host ( componentType1 / componentType2 ) | 0..1        | Dimensão( 7, 15 )                     |
		  | sdnCtrlHost                              | 0..1        | --                                    |
		And The operation output attributes table has the following values:
		  | attributes			|
		  | srcIpAddress 		|
		  | virtualSwitchId 	|