@REQ_NAPORTAL-19689
Feature: [MONITORING] Permitir pesquisa por código e mensagem de status usando base de dados do SWE
	#*What:*
	#Deverão ser adicionados novos critérios de pesquisa por código e mensagem de status.
	#!image-2019-07-22-09-32-45-679.png|thumbnail!
	#
	#*Why:*
	#A filtragem por código e mensagem de status permite encontrar facilmente ordens que estejam em estados HELD ou em estados finais como FAILED resultantes de um determinado problema associado a esse código ou mensagem.
	#
	#*How;*
	#Acedendo à listagem de todos códigos e mensagens de status persistidos na base de dados do SWE:
	# * BD: swe
	# * Collection: Orders
	# * Path: internal.orderProcesses.x.statusMessage
	#
	#Para identificar o código ou mensagem a selecionar deve ser disponibilizada a capacidade de pesquisa ou no código ou na mensagem associada usando os seguintes forms:
	#!image-2022-09-01-22-49-47-204.png!
	#
	#!image-2022-09-01-22-51-14-724.png!
	#
	# 

	
	@TEST_NAPORTAL-19703 @TESTSET_NAPORTAL-22327 @TESTSET_NAPORTAL-21277 @TESTSET_NAPORTAL-21830 @TESTSET_NAPORTAL-19702 @NAPORTAL-21613 @MONITORING
	Scenario: CT042 - [Monitoring - Web] Configure filters and validate filter expression for response code and response message filters
		Given I am "administrator" on "order search" page
		When I switch to search by "filters"		
		When I submit orders form with the following values:
		  | field          | value                               |
		  | response code  | OK                                  |
		  | category       | CFS SUM Test - notification         |
		  | date range     | Last week                           |
		Then the first rows of table "order search" matches table:
		  |             id                         | category                    | system                 |  last update                                  |
		  | d4d494d8-af39-4dda-bd72-f14c3e8a890713 | CFS SUM Test - notification | tester_workflow_engine | .+ ([01]?[0-9]\|2[0-3]):[0-5][0-9]:[0-5][0-9] |
		  | a8d844d8-af39-43da-bd72-b14c3e8a890711 | CFS SUM Test - notification | tester_workflow_engine | .+ ([01]?[0-9]\|2[0-3]):[0-5][0-9]:[0-5][0-9] |
		When I submit orders form with the following values:
		  | field             | value                                  |
		  | response code     | NOK                                    |
		  | response message  | Error invoking*					       |
		  | category          | *CFS SUM*                              |
		  | date range        | Last week                              |
		Then the first rows of table "order search" matches table:
		  |             id                           | category                                         					| system                 |
		  | 8b34334d-c87b-49c9-8fdd-677790016911     |  CFS SUM Na Portal Test - In Progress, Held and Failed Workflow 	    | tester_workflow_engine |
		  | 8b34334d-c87b-49c9-8fcb-677790016911     |  CFS SUM Na Portal Test - Completed and Held Workflow 			    | tester_workflow_engine |