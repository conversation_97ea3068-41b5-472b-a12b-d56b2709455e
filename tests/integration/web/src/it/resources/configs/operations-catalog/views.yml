# README on ../base/views.yml

#### VERSION

operations versions search:
  verification:
    - url contains: /operationscatalog/versions/home
    - element: table of versions search
    - element: search sidebar
  direction from home:
    through module menu:
      - click: operational view tab catalog
      - click: operational view tab catalog operations versions
  direction from inside module:
    - click: megamenu catalog
    - click: megamenu catalog operations versions

operations versions view:
  verification:
    - url matches: ^.*\/operationscatalog\/versions\/view\/[0-9]+$

operations versions edit:
  verification:
    - url matches: ^.*\/operationscatalog\/versions\/edit\/[0-9]+$

operations versions create:
  verification:
    - url matches: ^.*\/operationscatalog\/versions\/create$

#### CFS

operations cfs search:
  verification:
    - url contains: /operationscatalog/cfs/home
    - element: table of cfs search
    - element: search sidebar
  direction from home:
    through module menu:
      - click: operational view tab catalog
      - click: operational view tab catalog operations cfs
  direction from inside module:
    - click: megamenu catalog
    - click: megamenu catalog operations cfs

operations cfs view:
  verification:
    - url matches: ^.*\/operationscatalog\/cfs\/view\/[0-9]+$

operations cfs create:
  verification:
    - url matches: ^.*\/operationscatalog\/cfs\/create\/[0-9]+$

operations cfs edit:
  verification:
    - url matches: ^.*\/operationscatalog\/cfs\/edit\/[0-9]+$


#### RFS

operations rfs search:
  verification:
    - url contains: /operationscatalog/rfs/home
    - element: table of rfs search
    - element: search sidebar
  direction from home:
    through module menu:
      - click: operational view tab catalog
      - click: operational view tab catalog operations rfs
  direction from inside module:
    - click: megamenu catalog
    - click: megamenu catalog operations rfs

operations rfs view:
  verification:
    - url matches: ^.*\/operationscatalog\/rfs\/view\/[0-9]+$

operations rfs create:
  verification:
    - url matches: ^.*\/operationscatalog\/rfs\/create\/[0-9]+$

operations rfs edit:
  verification:
    - url matches: ^.*\/operationscatalog\/rfs\/edit\/[0-9]+$

#### Resource

operations resources search:
  verification:
    - url contains: /operationscatalog/resources/home
    - element: table of resources search
    - element: search sidebar
  direction from home:
    through module menu:
      - click: operational view tab catalog
      - click: operational view tab catalog operations resources
  direction from inside module:
    - click: megamenu catalog
    - click: megamenu catalog operations resources


operations resource view:
  verification:
    - url matches: ^.*\/operationscatalog\/resources\/view\/[0-9]+$

operations resource create:
  verification:
    - url matches: ^.*\/operationscatalog\/resources\/create\/[0-9]+$

operations resource edit:
  verification:
    - url matches: ^.*\/operationscatalog\/resources\/edit\/[0-9]+$

#### Response Codes

operations response codes search:
  verification:
    - url contains: /operationscatalog/responsecodes/home
    - element: table of response codes search
    - element: search sidebar
  direction from home:
    through module menu:
      - click: operational view tab catalog
      - click: operational view tab catalog operations response codes
  direction from inside module:
    - click: megamenu catalog
    - click: megamenu catalog operations attributes


