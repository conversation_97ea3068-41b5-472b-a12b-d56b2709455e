order search: !table
  features:
    - pagination
  dynamicColumns:
  elementKey: table of orders search
  columns:
    check box:
      css_class: selection-column
      label: {i18n: {key: ""}}
      type: SELECTBOX
      meta: !action-column-meta
        actions:
          select:
            css: .check_box
          select all:
            css: .check_box
    id:
      css_class: orderID
      label: {i18n: {key: ""}}
      type: TEXT
    category:
      css_class: category
      label: {i18n: {key: ""}}
      type: TEXT
    description:
      css_class: description
      label: {i18n: {key: ""}}
      type: TEXT
    state:
      css_class: state
      label: {i18n: {key: ""}}
      type: TEXT
    customer:
      css_class: Customer
      label: {i18n: {key: ""}}
      type: TEXT
    system:
      css_class: System
      label: {i18n: {key: ""}}
      type: TEXT
    system entry:
      css_class: entry
      label: {i18n: {key: ""}}
      type: TEXT
    last update:
      css_class: Last
      label: {i18n: {key: ""}}
      type: TEXT
    order id:
      css_class: orderID
      label: {i18n: {key: ""}}
      type: ACTIONS
      meta: !action-column-meta
        actions:
          view:
            css: a[data-action="view"]
          expand:
            css: i.fa-caret-up
          collapse:
            css: i.fa-caret-down
    actions:
      css_class: actions
      label: {i18n: {key: ""}}
      type: ACTIONS
      meta: !action-column-meta
        actions:
          more options: { css: "button i.glyphicon-cog" }
          cancel:
            css: "a[data-action=\"cancel\"]"
            subActionOf: "more options"
          skip:
            css: "a[data-action=\"skip\"]"
            subActionOf: "more options"
          retry:
            css: "a[data-action=\"retry\"]"
            subActionOf: "more options"

order items: !table
  features:
  dynamicColumns:
  elementKey: table of orders items
  columns:
    operation:
      css_class: operationService
      label: {i18n: {key: ""}}
      type: CUSTOM
      meta:
        implementation: "pt.ptinovacao.na.portal.it.models.actors.table.MoitoringOperationServiceColumn"
    description:
      css_class: description
      label: {i18n: {key: ""}}
      type: TEXT
    service id:
      css_class: serviceId
      label: {i18n: {key: ""}}
      type: TEXT
    state:
      css_class: state
      label: {i18n: {key: ""}}
      type: TEXT
    has process:
      css_class: hasProcess
      label: {i18n: {key: ""}}
      type: CUSTOM
      meta:
        implementation: "pt.ptinovacao.na.portal.it.models.actors.table.MoitoringHasProcessColumn"
    operation action:
      css_class: operationService
      label: {i18n: {key: ""}}
      type: ACTIONS
      meta: !action-column-meta
        actions:
          view:
            css: a.serviceLink
          expand:
            css: i.fa-caret-right
          collapse:
            css: i.fa-caret-down


error messages:
  elementKey: table of validation error messages
  columns:
    code:
      css_class: code
      type: TEXT
    message:
      css_class: message
      type: TEXT
    description:
      css_class: description
      type: TEXT

response delivery attempts:
  elementKey: table of response delivery attemps
  columns:
    retry number:
      css_class: retryNumber
      type: TEXT
    date:
      css_class: date
      type: TEXT
    state:
      css_class: state
      type: TEXT
    message:
      css_class: message
      type: TEXT

activity delivery attempts:
  elementKey: table of activity delivery attemps
  columns:
    id:
      css_class: id
      type: TEXT
    date:
      css_class: date
      type: TEXT
    state:
      css_class: state
      type: TEXT
    message:
      css_class: message
      type: TEXT

order item execution:
  elementKey: table of order execution
  columns:
      operation:
        css_class: operationCategory
        type: CUSTOM
        meta:
          implementation: "pt.ptinovacao.na.portal.it.models.actors.table.MoitoringOperationServiceColumn"
      service id:
        css_class: serviceId
        type: TEXT
      state:
        css_class: state
        type: TEXT
      duration:
        css_class: duration
        type: TEXT
      end date:
        css_class: endDate
        type: TEXT
      has process:
        css_class: hasProcess
        type: CUSTOM
        meta:
          implementation: "pt.ptinovacao.na.portal.it.models.actors.table.MoitoringHasProcessColumn"
      action:
        css_class: operationService
        label: {i18n: {key: ""}}
        type: ACTIONS
        meta: !action-column-meta
          actions:
            view:
              css: i.fuxicons-eye

order view execution activity history:
  elementKey: order execution external system interaction history table
  columns:
    datetime:
      css_class: datetime
      type: DATE
      meta:
        dateFormat: "dd-MM-yyyy HH:mm:ss.SSS"
    description:
      css_class: description
      type: TEXT
    author:
      css_class: author
      type: TEXT
    actions:
      css_class: actions
      label: {i18n: {key: ""}}
      type: ACTIONS
      meta: !action-column-meta
        actions:
          view:
            css: i.fuxicons-eye

order view notification details:
  elementKey: order notification details table
  columns:
    datetime:
      css_class: dateTime
      type: TEXT
    type:
      css_class: type
      type: TEXT
      meta: !action-column-meta
        actions:
          open:
            css: a[data-toggle="system-details-modal"]
    summary:
      css_class: summary
      label: {i18n: {key: ""}}
      type: TEXT
    state:
      css_class: state
      type: TEXT
    retry:
      css_class: state
      label: { i18n: { key: "" } }
      type: ACTIONS
      meta: !action-column-meta
        actions:
          retry:
            css: i.fa-undo
    expand:
      css_class: dateTime
      label: { i18n: { key: "" } }
      type: ACTIONS
      meta: !action-column-meta
        actions:
          expand:
            css: button.dropdown-toggle

order view modal system details history:
  elementKey: order view modal system details history table
  columns:
    index:
      css_class: id
      type: TEXT
    date:
      css_class: date
      type: TEXT
    state:
      css_class: state
      type: TEXT
    message:
      css_class: message
      type: TEXT
