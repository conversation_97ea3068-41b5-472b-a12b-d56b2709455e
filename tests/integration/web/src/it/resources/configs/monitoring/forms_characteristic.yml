order search:
  id: entity-header
  submit button element: orders search submit button
  fields:
    state:            { type: select,  id: filter-state            }
    category:         { type: text  ,  id: filter-category         }
    response code:    { type: text  ,  id: filter-response-code    }
    response message: { type: text  ,  id: filter-response-message }
    system:           { type: select,  id: filter-system           }
    operation:        { type: select,  id: filter-operation        }
    service type:     { type: select,  id: filter-service-type     }
    order id:         { type: text,    id: id-search-input         }
    service id:       { type: text,    id: id-search-input         }
    filter by:        { type: select,  id: filter-by-select-box    }
    date range:
      type: date
      id: search-date
      dateFormat: dd-MM-yyyy HH:mm

order create definition:
  id: details-form
  fields:
    system:       { type: select,        id: system          }
    user:         { type: select,        id: user            }
    version:      { type: text,          id: version         }
    operations:   { type: multi_select,  id: operations      }

order create attribute:
  id: order-attribute-form
  fields:
    external id:         { type: text,  id: order-attribute-form-input--external-id        }
    category:            { type: text,  id: order-attribute-form-input--category           }
    related party id:    { type: text,  id: order-attribute-form-input--related-party-id   }
    related party role:  { type: text,  id: order-attribute-form-input--related-party-role }

order create operation:
  id: order-operation-form
  fields:
    base characteristics:         { type: text,  id: base-characteristics     }
    specific characteristics:     { type: text,  id: specific-characteristics }

monitoring action modal:
  id: monitoring-modal
  submit button element: modal button confirm
  cancel button element: modal button cancel
  fields:
    message:    { type: text,    id: monitoring-action-message       }
