### MAIN VIEW ###

module go:
  css: a[data-portal-module="go"]

menu go:
  css: "#go.simplemenu"

admin of sidebar go:
  css: a.nav-generic-admin-operations

sidebar navigation:
  operations: {css: a.nav-generic-operations}
  admin templates: {css: a.nav-generic-admin-operations}


operational button go: {id: operational-button-go }
na tab go:
  css: a[href="#go"]
  operation main: { class: go.mega.menu.navinitial   }
  template: { class: go.home.menu.admin }
  
view details link: { class: operation-details-button }
clone operation link: { class: clone-me }

template switch:
 class: bootstrap-switch
 deactivate: { class: bootstrap-switch-success }
 activate: { class: bootstrap-switch-danger }
 active: { class: bootstrap-switch-on }
 inactive: { class: bootstrap-switch-off }


table:
  operations: {id: datatableOperations }
  templates: {id: datatableTemplates }
  mappings template: {id: datatableMappingsTemplate }
  lrsresponse: {id: datatableOperationDetailsLR_wrapper }
  cell require attention icon: { css: ".fuxicons.fuxicons-warning.yellow" }
  cell added mapping icon: { css: ".fa.fa-plus" }
  cell removed mapping icon: { css: ".fa.fa-minus" }
  cell required icon: { css: ".fx-required-field" }

new operation button: {id: new-operation-button }
new template button: {id: new-operation-button }


wizard tab:
  characteristics: {id: navWizStep1 }
  operation: {id: navWizStep2 }
  
details:
  description:
    family:    { id: descriptionBlock-family    }
    operation: { id: descriptionBlock-operation }
    rfs:       { id: descriptionBlock-rfs       }
    user:      { id: descriptionBlock-user      }
  request:
    tab: { css: '.fx-expand[data-target="#payload-req"]' }
    payload text: { id: payload-request-content }

template parameter row:
  css: 'tr[data-label="%s"]'
  action cell:    {xpath: "./td[contains(@class, 'actions-cell')]"}
  edit button:    {xpath: ".//a[contains(@class, 'edit-param')]"}
  confirm button: {xpath: ".//button[contains(@class, 'confirm-edit')]"}
  cancel button:  {xpath: ".//button[contains(@class, 'cancel-edit')]"}
  edit text component: {css: .parameter-edit-text-component}
  allowed values: { css: .allowed-values }
  default values: { css: .default-values }

wizard template parameter name field: {id: templateNameField}

choose optional fields:
  button: { css: ".button--operation-optional-fields" }
  dropdown:
    css: ".dropdown--operation-optional-fields"
    apply: {css: ".dropdown--operation-optional-fields .dropdown__apply" }
    cancel: {css: ".dropdown--operation-optional-fields .dropdown__cancel" }

view link eyedetails: { css: a.btn-link-in-table > .fuxicons.fuxicons-eye }