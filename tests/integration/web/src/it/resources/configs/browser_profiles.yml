##############################################
#            BROWSER PROFILES                #
##############################################
#
#  Configuration file of the browser profiles, used by selenium wrapper.



profile: default_profile

### VARIABLES START

__variables:
  #minimum/default resolution - THIS IS NOT A BROWSER PROFILE!!!!!
  minimum_resolution: &min_resolution
    width: 1920
    height: 1080

  #selenium hub
  #selenium_hub: &selenium_hub
  #  hub: "http://10.112.84.85:5555/wd/hub"


  local_profile: &local_profile
    provider: selenium-webdriver
    driver: selenium
    timeout: 30
    resolution:
      << : *min_resolution

  common_remote_profile: &remote_profile
    provider: selenium-webdriver
    driver: remote
    timeout: 30
    resolution:
      << : *min_resolution
    grid:
      hub: "http://nacommons-selenium-web-tests.c.ptin.corppt.com:4444/wd/hub"

  local_docker_profile: &local_docker_profile
    <<: *remote_profile
    grid:
      hub: "http://localhost:4444/wd/hub"


  firefox_profile: &firefox_profile
    profile:
      intl.accept_languages: pt
      browser.download.dir: ''
      browser.download.folderList: 2
      app.update.auto: false
      app.update.enabled: false
      browser.startup.page: 0
      browser.helperApps.neverAsk.saveToDisk: 'text/csv,application/pdf,application/x-tar-gz, application/x-gzip, application/x-compressed, application/octet-stream, application/zip, image/jpeg, image/png,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/plain,text/html,text/xml,application/xhtml+xml'
      pdfjs.disabled: true
      #network.proxy.type: 1
      #network.proxy.http: **************
      #network.proxy.http_port: 3128
      #network.proxy.ssl: **************
      #network.proxy.ssl_port: 3128
      #network.proxy.no_proxies_on: 'localhost,127.0.0.1, 10.0.0.0/8'
      #browser.privatebrowsing.autostart: False

### VARIABLES END

##############################################
#            BROWSER PROFILES                #
##############################################

#default_profile:
#  provider: selenium-webdriver
#  driver: selenium
#  browser: Firefox
#  << : *timeout_low_delay
#  resolution:
#    << : *min_resolution
#  profile:
#    log: INFO
#    intl.accept_languages: pt
#    browser.download.dir: ''
#    browser.download.folderList: 2
#    app.update.auto: false
#    app.update.enabled: false
#    browser.startup.page: 0
#    browser.helperApps.neverAsk.saveToDisk: 'application/pdf,application/x-tar-gz, application/x-gzip, application/x-compressed, application/octet-stream, application/zip'

default_profile:
  << : *local_profile
  browser: Firefox
  profile:
    intl.accept_languages: pt
    browser.download.folderList: 2
    app.update.auto: false
    app.update.enabled: false
    browser.startup.page: 0
    browser.helperApps.neverAsk.saveToDisk: 'application/pdf,application/x-tar-gz, application/x-gzip, application/x-compressed, application/octet-stream, application/zip, image/jpeg, image/png'
    logging:
      type: browser
      level: warning

FF_HEADLESS:
  << : *local_profile
  browser: Firefox
  headless: true
  profile:
    intl.accept_languages: pt
    browser.download.dir: ''
    browser.download.folderList: 2


CHROME:
  <<: *local_profile
  browser: Chrome

REMOTE_FF:
  provider: selenium-webdriver
  browser: Firefox
  << : [*remote_profile, *firefox_profile]
  capabilities:
    logging:
      type: browser
      level: warning


LOCAL_DOCKER_FF:
  provider: selenium-webdriver
  browser: Firefox
  <<: [*local_docker_profile, *firefox_profile]
  capabilities:
    logging:
      type: browser
      level: warning

REMOTE_CHROME:
  << : *remote_profile
  browser: Chrome
  capabilities:
    "acceptSslCerts": true
    "acceptInsecureCerts": true
    logging:
      type: browser
      level: warning

LOCAL_DOCKER_CHROME:
  << : *local_docker_profile
  browser: Chrome
  capabilities:
    "acceptSslCerts": true
    "acceptInsecureCerts": true
    logging:
      type: browser
      level: warning

REMOTE_IE11:
  << : *remote_profile
  browser: IE
  version: 11
  capabilities:
    "ie.ensureCleanSession": true
    "enablePersistentHover": false
    "requireWindowFocus": false
    "unexpectedAlertBehaviour": true
    "acceptSslCerts": true
    "ignoreProtectedModeSettings": true
    logging:
      type: browser
      level: warning