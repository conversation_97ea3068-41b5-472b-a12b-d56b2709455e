#
#                           LOGIN MAPPING
#
#   This file maps a descriptive name of the user with it's credentials
#
#     Used in cucumber features to define the robot user to log in the system to
#   test the system.
#
#     Example steps:
#       I am logged in
#       I am logged in as "<user name>"
#
#     Usage:
#       defaultLogin: <default user name to login (the user that will be logged on the "I am logged in" gherkin step)>
#       users:
#         <user description>:
#           credentials: <user username credential>


defaultLogin: administrator
users:
############ ADMIN PORTAL

  administrator:              { credentials: ADMIN_PORTAL     }
  "english administrator":    { credentials: ADMIN_PORTAL_EN  }
  "french administrator":     { credentials: ADMIN_PORTAL_FR  }
  "user with global access":  { credentials: ADMIN_PORTAL     }

############  MPT

  "user with mpt access":                                                   { credentials: OPER_MPT_CONSULT }
  "mpt user with oper access":                                              { credentials: OPER_MPT }
  "mpt user without oper access":                                           { credentials: OPER_MPT_CONSULT }
  "mpt user authorized to create operation":                                { credentials: OPER_MPT }
  "mpt user unauthorized to create operation":                              { credentials: OPER_MPT_CONSULT }
  "mpt user with different dgs":                                            { credentials: OPER_MPT_EXFAM }
  "mpt user without dgs":                                                   { credentials: OPER_MPT_DUMMY }


############ MONITORING

  "monit user unauthorized to perform actions":                             { credentials: CONSULT_MONIT }

############ GO

  "go user unauthorized to execute operation":                              { credentials: OPER_GO_CONSULT }
  "go user authorized to execute operation":                                { credentials: OPER_GO }
  "go user with different domains between read and execution permissions":  { credentials: GO_R_X_DIFFDOMAINS }
  "go user with admin access":                                              { credentials: ADMIN_GO }
  "go user without admin access":                                           { credentials: OPER_GO }
  "dummy go":                                                               { credentials: OPER_GO_DUMMY }

############ CATALOG

  "catalog admin":                                                          { credentials: ADMIN_CATALOG }
  "catalog user with read and create permissions":                          { credentials: CATALOG_R_C_DIFFDOM }
  "catalog user with operation permissions":                                { credentials: OPER_CATALOG }
  "catalog user with read permissions":                                     { credentials: OPER_CATALOG_CONSULT }
  "catalog user with dummy previleges":                                     { credentials: OPER_CATALOG_DUMMY }
  "catalog user authorized to consult Telephone List and Operation in OLT": { credentials: CATALOG_R_C_DIFFDOM }

####NADM
############ VEACS

  "veacs admin":                                { credentials: ADMIN_VEACS }
  "veacs user with dummy privileges":           { credentials: OPER_VEACS_DUMMY }
  "veacs user unauthorized to edit devices":    { credentials: OPER_VEACS_CONSULT }
  "veacs user unauthorized to create devices":  { credentials: OPER_VEACS_CONSULT }
  "veacs user authorized to create devices":    { credentials: ADMIN_VEACS }
  "veacs user authorized to edit devices":      { credentials: OPER_VEACS }

# --------------------------
# --- OPERATIONS CATALOG ---
# --------------------------

  "operations catalog admin":                                                          { credentials: ADMIN_OPERATIONS_CATALOG }
  "operations catalog user with operation permissions":                                { credentials: OPER_OPERATIONS_CATALOG }
  "operations catalog user with dummy privileges":                                     { credentials: OPER_OPERATIONS_CATALOG_DUMMY }
  "operations catalog user with read permissions":                                     { credentials: CONSULT_OPERATIONS_CATALOG }