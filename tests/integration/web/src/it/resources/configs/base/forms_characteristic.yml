#                            README
#
#               FORMS CHARACTERISTICS FILE
#
#  Usage:
#      < form name >:
#         id: < form's DOM element's id attribute >
#         filter: < optional parameter, meta information about filter >
#           load: < name of filter, when named filter is loaded, it load to check if it is done correctly, verifies if the content of the fields of this form >
#           save:  < name of filter, indicates that this is the form to use when saving a filter >
#           edit:  < name of filter, indicates that this is the form to use when editing a filter >
#         field: < map of fields >
#           type: < type of the field >
#           id: < field's DOM element's id attribute >
#           dateFormat: < date field only, date format to be used on the field when defining dates with a natural language (e.g. today) >
#           values: < radio and checkbox field only, map of multiple buttons >
#              < name of value >:
#                id: < field value's DOM element's id attribute >
#         submit button element: < optional parameter, substitutes the default submit button (<input type="submit">  inside form's DOM element ) >
#
