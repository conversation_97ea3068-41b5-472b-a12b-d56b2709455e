
# ╔══════════════════════╗
# ║   VEACS Sub-Module   ║
# ╚══════════════════════╝
# -VEACS

veacs dashboard:
  type: page
  verification:
    - url contains: /veacs/nadm/home
  direction from home:
    through operational button:
        - click: operational button veacs

veacs global equipment:
  type: page
  verification:
    - url contains: /veacs/nadm/globalequipment
    - element: nadm table global equipment
    - element: nadm navigation sidebar active tab global equipment
  direction from home:
      through operational button:
          - click: operational button veacs
          - click: nadm navigation sidebar global equipment
      as user with single module access:
        - click: nadm navigation sidebar global equipment


veacs global equipment edit:
  type: page
  verification:
    - url contains: /veacs/nadm/globalequipment
    - url ends: /edit
    - element: nadm edit form
    - element: nadm navigation sidebar active tab global equipment

veacs global equipment view:
  type: page
  verification:
    - url contains: /veacs/nadm/globalequipment
    - element: edit button
    - element: nadm edit form
    - element: nadm navigation sidebar active tab global equipment

veacs equipment association:
  type: page
  verification:
    - url contains: /veacs/nadm/equipment
    - url contains: /associations
    - element: nadm equipment association form

veacs equipment:
  type: page
  verification:
    - url contains: /veacs/nadm/equipment
    - element: nadm table equipment
    - element: nadm navigation sidebar active tab equipment
  direction from home:
      through operational button:
          - click: operational button veacs
          - click: nadm navigation sidebar equipment
      as user with single module access:
          - click: nadm navigation sidebar equipment

veacs client:
  type: page
  verification:
    - url contains: /veacs/nadm/client
    - element: nadm table client
    - element: nadm navigation sidebar active tab client
  direction from home:
      through operational button:
          - click: operational button veacs
          - click: nadm navigation sidebar client
      as user with single module access:
          - click: nadm navigation sidebar client

veacs client view:
  type: page
  verification:
    - url contains: /veacs/nadm/client

veacs equipment view:
  type: page
  verification:
    - url contains: /veacs/nadm/equipment

veacs client equipment view:
  type: modal
  verification:
    - url contains: /veacs/nadm/client
    - element: nadm view equipment model form

veacs client equipment edit:
  type: page
  verification:
    - url contains: /veacs/nadm/client
    - url ends: /edit
    - element: nadm edit form

veacs service:
  type: page
  verification:
    - url contains: /veacs/nadm/service
    - element: nadm table service
    - element: nadm navigation sidebar active tab service
  direction from home:
      through operational button:
          - click: operational button veacs
          - click: nadm navigation sidebar service
      as user with single module access:
          - click: nadm navigation sidebar service

veacs service view:
  type: modal
  verification:
    - url contains: /veacs/nadm/service
    - element: nadm service characteristics tab

veacs service edit:
  type: page
  verification:
    - url contains: /veacs/nadm/service
    - url ends: /edit
