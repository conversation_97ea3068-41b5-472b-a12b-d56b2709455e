<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>pt.ptinovacao.na-portal</groupId>
        <artifactId>na-portal-tests</artifactId>
        <version>10.3.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <groupId>pt.ptinovacao.na-portal.it</groupId>
    <artifactId>na-portal-integration-tests</artifactId>
    <packaging>pom</packaging>

    <properties>
        <!-- default ci.environment -->
        <ci.environment>pgsql</ci.environment>
    </properties>

    <modules>
        <module>core</module>
        <module>create-entities-operations-versions</module>
        <!-- TODO review web tests -->
        <!--<module>web</module>-->
        <!--<module>webtestutils</module>-->
        <module>web-data-snapshots</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <!-- Removing scope provided because (at least) of na-operations-tests-utils -->
                <groupId>pt.ptinovacao.na-commons</groupId>
                <artifactId>na-commons-http-client-api</artifactId>
                <version>${version.pt.ptinovacao.na-commons}</version>
            </dependency>
            <dependency>
                <!-- Removing scope provided because (at least) of na-operations-tests-utils -->
                <groupId>pt.ptinovacao.na-commons</groupId>
                <artifactId>na-commons-http-client-impl</artifactId>
                <version>${version.pt.ptinovacao.na-commons}</version>
            </dependency>
            <!-- tests dependencies -->
            <dependency>
                <groupId>com.googlecode.jmockit</groupId>
                <artifactId>jmockit</artifactId>
                <version>${version.com.googlecode.jmockit}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.rubiconproject.oss</groupId>
                <artifactId>jchronic</artifactId>
                <version>${version.jchronic}</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${version.commons-codec}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${version.junit}</version>
            </dependency>
            <dependency>
                <groupId>log4j</groupId>
                <artifactId>log4j</artifactId>
                <version>${version.log4j}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${version.org.apache.httpcomponents}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>${version.org.apache.httpcomponents.httpcore}</version>
            </dependency>
            <dependency>
                <groupId>org.specs2</groupId>
                <artifactId>specs2_2.9.1</artifactId>
                <version>${version.org.specs2.specs2}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.specs2</groupId>
                <artifactId>specs2-scalaz-core_2.9.1</artifactId>
                <version>${version.org.specs2.specs2-scalaz-core}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na</groupId>
                <artifactId>na-operations-tests-utils</artifactId>
                <version>${version.pt.ptinovacao.na}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na</groupId>
                <artifactId>na-operations-catalog-versions-manager</artifactId>
                <version>${version.pt.ptinovacao.na}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-commons</groupId>
                <artifactId>na-commons-model</artifactId>
                <version>${version.pt.ptinovacao.na-commons}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.na-portal.it</groupId>
                <artifactId>na-portal-integration-tests-web-test-utils</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.ssov</groupId>
                <artifactId>ssov-cucumber-tmap-formatter</artifactId>
                <version>${version.pt.ptinovacao.ssov}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.ssov</groupId>
                <artifactId>ssov-integration-tests-framework</artifactId>
                <version>${version.pt.ptinovacao.ssov}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.ssov</groupId>
                <artifactId>ssov-junit-cucumber</artifactId>
                <version>${version.pt.ptinovacao.ssov}</version>
            </dependency>
            <dependency>
                <groupId>pt.ptinovacao.ssov</groupId>
                <artifactId>ssov-selenium-wrapper</artifactId>
                <version>${version.pt.ptinovacao.ssov}</version>
            </dependency>
            <dependency>
                <groupId>xml-apis</groupId>
                <artifactId>xml-apis</artifactId>
                <version>${version.xml-apis}</version>
                <scope>test</scope>
            </dependency>

            <!-- cucumber dependencies -->
            <dependency>
                <groupId>info.cukes</groupId>
                <artifactId>cucumber-java</artifactId>
                <version>${version.info.cukes}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>info.cukes</groupId>
                <artifactId>cucumber-junit</artifactId>
                <version>${version.info.cukes}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>info.cukes</groupId>
                <artifactId>cucumber-picocontainer</artifactId>
                <version>${version.info.cukes}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>info.cukes</groupId>
                <artifactId>gherkin</artifactId>
                <version>${version.info.cukes.gherkin}</version>
            </dependency>
            <dependency>
                <groupId>info.cukes</groupId>
                <artifactId>cucumber-core</artifactId>
                <version>${version.info.cukes}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${version.com.fasterxml.jackson}</version>
            </dependency>

            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-api</artifactId>
                <version>${version.org.seleniumhq.selenium}</version>
            </dependency>
            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-ie-driver</artifactId>
                <version>${version.org.seleniumhq.selenium}</version>
            </dependency>
            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-firefox-driver</artifactId>
                <version>${version.org.seleniumhq.selenium}</version>
            </dependency>
            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-chrome-driver</artifactId>
                <version>${version.org.seleniumhq.selenium}</version>
            </dependency>
            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-support</artifactId>
                <version>${version.org.seleniumhq.selenium}</version>
            </dependency>
            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-remote-driver</artifactId>
                <version>${version.org.seleniumhq.selenium}</version>
            </dependency>

            <dependency>
                <groupId>net.schmizz</groupId>
                <artifactId>sshj</artifactId>
                <version>${version.net.schmizz}</version>
            </dependency>

            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-http</artifactId>
                <version>${version.org.eclipse.jetty}</version>
            </dependency>

            <dependency>
                <groupId>org.glassfish.hk2</groupId>
                <artifactId>hk2-utils</artifactId>
                <version>${version.org.glassfish.hk2.external}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.hk2</groupId>
                <artifactId>hk2-api</artifactId>
                <version>${version.org.glassfish.hk2.external}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>add-source</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>src/main/scala</source>
                                <source>src/main/java</source>
                            </sources>
                        </configuration>
                    </execution>
                    <execution>
                        <id>add-test-source</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-test-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>src/it/scala</source>
                                <source>src/it/java</source>
                            </sources>
                        </configuration>
                    </execution>
                    <execution>
                        <id>add-resource</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>add-resource</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>src/main/resources</directory>
                                </resource>
                                <resource>
                                    <directory>src/it/resources</directory>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>integration-tests</id>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>org.apache.maven.plugins</groupId>
                            <artifactId>maven-failsafe-plugin</artifactId>
                            <version>${version.org.apache.maven.surefire}</version>
                            <configuration>
                                <includes>
                                    <include>**/*IT.*</include>
                                </includes>
                            </configuration>
                            <executions>
                                <execution>
                                    <id>integration-test</id>
                                    <goals>
                                        <goal>integration-test</goal>
                                    </goals>
                                </execution>
                                <execution>
                                    <id>verify</id>
                                    <goals>
                                        <goal>verify</goal>
                                    </goals>
                                    <configuration>
                                        <testFailureIgnore>true</testFailureIgnore>
                                    </configuration>
                                </execution>
                            </executions>
                        </plugin>

                        <plugin>
                            <groupId>org.codehaus.mojo</groupId>
                            <artifactId>exec-maven-plugin</artifactId>
                            <executions>
                                <execution>
                                    <id>startup</id>
                                    <phase>pre-integration-test</phase>
                                    <goals>
                                        <goal>java</goal>
                                    </goals>
                                    <configuration>
                                        <mainClass>pt.ptinovacao.ssov.it.integration.Main</mainClass>
                                        <arguments>
                                            <argument>pt.ptinovacao.na.portal.it.Setup</argument>
                                            <argument>startup</argument>
                                            <argument>/conf/setup.properties</argument>
                                        </arguments>
                                    </configuration>
                                </execution>

                                <execution>
                                    <id>cleanup</id>
                                    <phase>post-integration-test</phase>
                                    <goals>
                                        <goal>java</goal>
                                    </goals>
                                    <configuration>
                                        <mainClass>pt.ptinovacao.ssov.it.integration.Main</mainClass>
                                        <arguments>
                                            <argument>pt.ptinovacao.na.portal.it.Setup</argument>
                                            <argument>cleanup</argument>
                                            <argument>/conf/setup.properties</argument>
                                        </arguments>
                                    </configuration>
                                </execution>
                            </executions>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
    </profiles>
</project>
