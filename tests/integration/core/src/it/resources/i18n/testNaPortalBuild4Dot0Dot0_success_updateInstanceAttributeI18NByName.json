{
  "name": "$testName",
  "description": "$testDescription",
  "parent": "http://localhost/netwin/catalog-entities/NA_CATALOG",
  "i18nLabel": {
    "i18nTranslations": [
      {
        "locale": "pt",
        "translation": "Equipamentos de Testes"
      },
      {
        "locale": "en",
        "translation": "Test Equipments"
      }
    ]
  },
  "instanceAttributes": [
    {
      "name": "IP",
      "description": "Ip do Equipamentos",
      "required": true,
      "catAttributeType": "http://localhost/netwin/catalog-attributetypes/string100",
      "i18nLabel": {
        "i18nTranslations": [
          $i18n
        ]
      }
    }
  ]
}