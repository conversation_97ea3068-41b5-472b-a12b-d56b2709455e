{"name": "$testName", "i18nLabel": {"i18nTranslations": [{"locale": "pt", "translation": "Equipamento"}, {"locale": "en", "translation": "Equipment"}]}, "description": "$testDescription", "parent": "http://localhost/netwin/catalog-entities/NA_CATALOG", "instanceAttributes": [{"name": "", "dynamic": true, "i18nLabel": {"i18nTranslations": [{"locale": "pt", "translation": "IP"}, {"locale": "en", "translation": "IP"}]}, "description": "Ip do Equipamento", "required": true, "catAttributeType": "http://localhost/netwin/catalog-attributetypes/string100"}, {"name": "ta<PERSON><PERSON>", "dynamic": true, "i18nLabel": {"i18nTranslations": [{"locale": "pt", "translation": "<PERSON><PERSON><PERSON>"}, {"locale": "en", "translation": "Size"}]}, "description": "Tam<PERSON><PERSON>", "required": true, "catAttributeType": "http://localhost/netwin/catalog-attributetypes/string100"}], "uIContexts": [{"name": "entity", "action": "VIEW", "uIElements": [{"name": "ip", "editable": false, "visible": true, "uiComponentWidth": "FULL", "uiElementWidth": 1, "uiComponent": "INPUT", "line": 12, "col": 1, "catInsAttribute": {"name": "ip"}, "uiGroup": {}}]}]}