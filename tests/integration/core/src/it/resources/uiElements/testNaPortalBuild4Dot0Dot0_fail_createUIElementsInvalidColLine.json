{
  "name": "$testName",
  "description": "$testDescription",
  "parent": "http://localhost/netwin/catalog-entities/NA_CATALOG",
  "i18nLabel": {
    "i18nTranslations": [
      {
        "locale": "pt",
        "translation": "Equipamento"
      }
    ]
  },
  "instanceAttributes": [
    {
      "name": "ip",
      "i18nLabel": {
        "i18nTranslations": [
          {
            "locale": "pt",
            "translation": "IP"
          }
        ]
      },
      "description": "IP do equipamento",
      "required": true,
      "catAttributeType": "http://localhost/netwin/catalog-attributetypes/string100"
    }
  ],
  "uIContexts": [
    {
      "name": "entity",
      "action": "VIEW",
      "uIElements": [
        {
          "name": "ip",
          "editable": false,
          "visible": true,
          "uiComponentWidth": "FULL",
          "uiElementWidth": 1,
          "uiComponent": "INPUT",
          "line": $line$,
          "col": $col$,
          "catInsAttribute": {
            "name": "ip"
          },
          "uiGroup": {
            "name": "caracterizacao.1.caracterizacaoespecifica.1"
          }
        }
      ]
    }
  ]
}