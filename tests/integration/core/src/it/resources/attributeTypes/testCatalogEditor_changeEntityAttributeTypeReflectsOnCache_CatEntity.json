{"name": "$name", "description": "Test catalog entity", "parent": "http://localhost/netwin/catalog-entities/NA_CATALOG", "i18nLabel": {"i18nTranslations": [{"locale": "en", "translation": "Test entity"}]}, "instanceAttributes": [{"name": "testInstanceAttribute", "i18nLabel": {"i18nTranslations": [{"locale": "en", "translation": "Test instance attribute"}]}, "description": "Test instance attribute", "required": true, "catAttributeType": "http://localhost/netwin/catalog-attributetypes/$catAttributeType"}], "uIContexts": [{"name": "entity", "action": "VIEW", "uIElements": [{"catInsAttribute": {"name": "testInstanceAttribute"}, "col": 1, "editable": true, "line": 1, "name": "batatas", "uiComponent": "INPUT", "uiComponentWidth": "FULL", "uiElementWidth": 1, "uiGroup": {"name": "caracterizacao.1.caracterizacaoespecifica.1"}, "visible": true}]}]}