<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>pt.ptinovacao.na-portal</groupId>
        <artifactId>na-portal</artifactId>
        <version>10.55.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>na-portal-tests</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>integration</module>
    </modules>

    <properties>
        <version.app.getxray>0.8.0</version.app.getxray>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${version.com.google.guava.tests}</version>
            </dependency>
            <dependency>
                <groupId>app.getxray</groupId>
                <artifactId>xray-junit-extensions</artifactId>
                <version>${version.app.getxray}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
