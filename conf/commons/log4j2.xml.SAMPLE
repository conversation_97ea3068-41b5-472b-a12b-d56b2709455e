<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="warn">

	{% if log4j2 is defined -%}
		<Appenders>

	    {% if 'graylog' in log4j2 %}
            {%- set appender = log4j2.graylog -%}
            <Socket name="{{ appender.appender_name }}" protocol="tcp" host="{{ graylog_host }}" port="{{ graylog_port }}" >
                {% if 'thresholdFilter' in appender %}<ThresholdFilter level="{{ appender.thresholdFilter }}" onMatch="ACCEPT" onMismatch="DENY"/>{% endif %}
                <GelfLayout host="{% if appender.use_host_in_prefix | string | lower == 'true'  %}{{ host }}_{% endif %}${hostName}"
                    includeThreadContext="true" includeNullDelimiter="true"
                    {% if 'compressionType' in appender -%}
                        compressionType="{{ appender.compressionType }}"
                    {% else -%}
                        compressionType="OFF"
                    {%- endif -%}
                    {% if 'compressionThreshold' in appender %}compressionThreshold="{{ appender.compressionThreshold }}"{% endif %}
                    {% if 'layout' in appender -%}
                        messagePattern="{{ appender.layout }}"
                    {% else -%}
                        messagePattern="%X{message}"
                    {%- endif %}
                >
                     <KeyValuePair key="application" value="{{ appender.application_name }}"/>
                     <KeyValuePair key="service" value="{{ appender.service_name }}"/>
                     <KeyValuePair key="environment" value="{{ application_environment }}"/>
                </GelfLayout>
            </Socket>
        {% endif %}

        {% for appenderCustomName in rollingFile_appenders_customization %}
            {% if 'rollingFile' in log4j2 %}
                {% set _ = log4j2["rollingFile"].update({ appenderCustomName : rollingFile_appenders_customization[appenderCustomName] }) %}
            {% else %}
                {% set _ = log4j2.update({ "rollingFile": { appenderCustomName : rollingFile_appenders_customization[appenderCustomName] }}) %}
            {% endif %}
        {% endfor %}
	    {% if 'rollingFile' in log4j2 -%}
	        {%- set appenders = log4j2["rollingFile"] -%}
	        {% for appenderName in appenders -%}
	            {% set appender = appenders[appenderName] %}
		        <RollingFile
		            name="{{ appenderName }}"
		            fileName="{{ appender.file }}"
                    filePattern="{{ appender.file | replace(".log", "") }}.{% if 'timeInterval' in appender -%}%d{yyyy-MM-dd-HH}-{% endif %}%i.log.gz"
		            createOnDemand="true"
                    {% if 'filePermissions' in appender -%}filePermissions="{{ appender.filePermissions }}"{%- endif %}
                    {% if 'fileOwner' in appender -%}fileOwner="{{ appender.fileOwner }}"{%- endif %}
                    {% if 'fileGroup' in appender %}fileGroup="{{ appender.fileGroup }}"{%- endif %}
                >
                    {% if 'thresholdFilter' in appender -%}
                        <ThresholdFilter level="{{ appender.thresholdFilter }}" onMatch="ACCEPT" onMismatch="DENY"/>
                    {% endif -%}
		            <PatternLayout>
			            <Pattern>
			                {%- if 'layout' in appender -%}
		                       {{ appender.layout }}
		                    {%- else -%}
			                    %d{yyyy-MM-dd'T'HH:mm:ss.SSS}{GMT}  %m%n
		                    {% endif %}
			            </Pattern>
		            </PatternLayout>
					{% if 'maxFileSize' in appender -%}
                        <Policies>
	                        <SizeBasedTriggeringPolicy size="{{ appender.maxFileSize }}"/>
	                    </Policies>
                    {%- endif %}
                    {% if 'timeInterval' in appender and 'timeModulate' in appender -%}
                        <Policies>
                            <TimeBasedTriggeringPolicy interval="{{ appender.timeInterval }}" modulate="{{ appender.timeModulate }}"/>
                        </Policies>
                    {%- endif %}
                    {% if 'maxBackupIndex' in appender -%}
                        <DefaultRolloverStrategy max="{{ appender.maxBackupIndex }}"/>
                    {%- endif %}
		        </RollingFile>
	        {% endfor %}
	    {% endif %}

	    {% if 'console' in log4j2 -%}
	            {%- set appender = log4j2.console -%}
	            <Console name="{{ appender.name }}">
					{% if 'thresholdFilter' in appender -%}
                        <ThresholdFilter level="{{ appender.thresholdFilter }}" onMatch="ACCEPT" onMismatch="DENY"/>
                    {% endif -%}
                    <GelfLayout host="${hostName}"
		                includeThreadContext="true"
		                includeNullDelimiter="true"
		                {% if 'compressionType' in appender -%}
		                    compressionType="{{ appender.compressionType }}"
		                {% else -%}
	                        compressionType="OFF"
	                    {%- endif -%}
		                {% if 'compressionThreshold' in appender %}compressionThreshold="{{ appender.compressionThreshold }}"{% endif %}
		                {% if 'layout' in appender -%}
	                        messagePattern="{{ appender.layout }}"
	                    {% else -%}
	                        messagePattern="%m%n"
	                    {%- endif %}
	                >
		                <KeyValuePair key="application" value="{{ appender.name }}"/>
	                    <KeyValuePair key="environment" value="{{ application_environment }}"/>
		            </GelfLayout>
		        </Console>
		{% endif %}

	    </Appenders>

	    <Loggers>

		    {% for logger in log4j2["loggers"] %}
	           <Logger name="{{ logger.name }}" level="{{ logger.level }}" additivity="{{ logger.additivity if "additivity" in logger else "false" }}" >
	                {% for ref in logger.refs -%}
                    <AppenderRef {% if ':' in ref %}ref="{{ ref.split(':')[0] }}" level="{{ ref.split(':')[1] }}"{% else %} ref="{{ ref }}"{% endif %} />
                    {%- endfor %}
	           </Logger>

	       {% endfor %}

	       {% for logger in loggers_customization %}
	          <Logger name="{{ logger.name }}" level="{{ logger.level }}" additivity="{{ logger.additivity if "additivity" in logger else "false" }}" >
                    {% for ref in logger.refs -%}
                    <AppenderRef {% if ':' in ref %}ref="{{ ref.split(':')[0] }}" level="{{ ref.split(':')[1] }}"{% else %} ref="{{ ref }}"{% endif %} />
                    {%- endfor %}
	          </Logger>

	      {% endfor %}

			<!-- ======================= -->
		    <!-- Setup the Root category -->
		    <!-- ======================= -->
		    {% set root = log4j2.root -%}
		    <Root level="{{ root.level }}">
		        {% for ref in root.refs -%}
                <AppenderRef {% if ':' in ref %}ref="{{ ref.split(':')[0] }}" level="{{ ref.split(':')[1] }}"{% else %} ref="{{ ref }}"{% endif %} />
                {%- endfor %}
		    </Root>

	   </Loggers>
	{%- endif %}

</Configuration>