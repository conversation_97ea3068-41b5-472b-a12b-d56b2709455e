<?xml version="1.0" encoding="UTF-8"?>
<settings xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.1.0 http://maven.apache.org/xsd/settings-1.1.0.xsd" xmlns="http://maven.apache.org/SETTINGS/1.1.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <mirrors>
    <mirror>
      <id>PtinMaven</id>
      <mirrorOf>*</mirrorOf>
      <name>Mirror interno ao SSO na PT Inovacao</name>
      <url>https://maven.ptin.corppt.com/repo</url>
    </mirror>
  </mirrors>
  <servers>
    <server>
      <id>releases</id>
      <username>ci-na-commons</username>
      <password>c1-na-commons</password>
    </server>
    <server>
      <id>snapshots</id>
      <username>ci-na-commons</username>
      <password>c1-na-commons</password>
    </server>
  </servers>
  <profiles>
    <profile>
      <id>sonar</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <properties>
        <sonar.host.url>http://gestoss-sonar.c.ptin.corppt.com:9000</sonar.host.url>
      </properties>
    </profile>
    <profile>
      <id>jenkins-sonar</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <properties>
        <sonar.host.url>http://gestoss-sonar.c.ptin.corppt.com:9000</sonar.host.url>
      </properties>
    </profile>
  </profiles>
  <pluginGroups>
    <pluginGroup>org.sonarsource.scanner.maven</pluginGroup>
  </pluginGroups>
</settings>
