#!/bin/bash

URL_ORIGIN=${URL_ORIGIN:-"http://na-swe-it:9004"}
HEALTH_URL=${URL_ORIGIN}/health

TIMEOUT=300000 #milliseconds
START_TIME=$(date +'%s%3N')

print_message_or_abort(){
  ELLAPSED_TIME=$(( $(date +'%s%3N') - START_TIME  ))
  if (( ELLAPSED_TIME > TIMEOUT )); then
    echo "Timeout of $TIMEOUT ms passed"
    exit 1
  else
    echo "$1... ellapsed time: $ELLAPSED_TIME ms"
  fi
}

print_message(){
  ELLAPSED_TIME=$(( $(date +'%s%3N') - START_TIME  ))
  echo "$1... ellapsed time: $ELLAPSED_TIME ms"
}

until [ "$(curl -s -o /dev/null -w "%{http_code}" "$HEALTH_URL")" -eq 200 ]; do
  print_message_or_abort "Waiting swe on $URL_ORIGIN to be ready";
  sleep 1;
done

print_message "swe on $URL_ORIGIN ready"


jq -c '.[]' data/external-systems.json | xargs -I {} -d'\n' curl -v -d {} -H "Content-Type: application/json" --max-time 10 -X POST $URL_ORIGIN/ptinovacao/external-systems-handling/external-systems
jq -c '.[]' data/users.json | xargs -I {} -d'\n' curl -v -d {} -H "Content-Type: application/json" --max-time 10 -X POST $URL_ORIGIN/ptinovacao/external-systems-handling/users