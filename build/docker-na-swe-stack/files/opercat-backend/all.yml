# -------------------------------------------------------------------------------------------------------------------- #
# --- NA Operations Catalog
# -------------------------------------------------------------------------------------------------------------------- #

persistence_ds_local_pool_initial: 0
persistence_ds_local_pool_max: 30
persistence_ds_local_pool_maxIdle: 1
persistence_ds_local_pool_maxWait: 15000
persistence_ds_local_accessToUnderlyingConnectionAllowed: "true"

persistence_ds_global_pool_initial: 1
persistence_ds_global_pool_max: 5
persistence_ds_global_pool_maxIdle: 1
persistence_ds_global_pool_maxWait: 15000
persistence_ds_global_accessToUnderlyingConnectionAllowed: "true"

persistence_ds_workspace_pool_initial: 0
persistence_ds_workspace_pool_max: 5
persistence_ds_workspace_pool_maxIdle: 1
persistence_ds_workspace_pool_maxWait: 15000
persistence_ds_workspace_accessToUnderlyingConnectionAllowed: "true"

persistence_transaction_timeout: 60
javax_persistence_query_timeout: 50

zkClient_maxElapsedTimeInMs: 10000
zkClient_sleepRetriesInMs: 500

persistence_mongodb_serverSelectionTimeout: -1


na_oper_cat_bind_ip: 0.0.0.0
na_oper_cat_backend_host: naportal-ci.c.ptin.corppt.com #melhorar
na_oper_cat_backend_port: 9015
na_oper_cat_exposed_port: 39001
na_oper_cat_netty_port_offset: 10
na_oper_cat_netty_port: "{{ na_oper_cat_backend_port + na_oper_cat_netty_port_offset }}"

na_oper_cat_jetty_threads_min: 20
na_oper_cat_jetty_threads_max: 20
na_oper_cat_jetty_idle_timeout: 60000

zookeeper_port: 2181

na_db_type: postgresql
na_db_port: 5432
na_db_service: na

na_db_host: postgresql
na_db_pass: na
na_db_user: na

nossis_tsc_cache_eviction_time: 0L

persistence_mongodb_auth_username: na
persistence_mongodb_auth_password: na
persistence_mongodb_auth_database: admin

persistence_mongodb_database_name: na-oc-docker
mongodb_uri: "mongodb://na:<EMAIL>:27017/admin"
zkClient_zookeeperNodes: zookeeper:2181

na_rabbitmq_uri: rabbitmq:5672
na_rabbitmq_username: admin
na_rabbitmq_password: 1234

nossistsc_rabbitmq_exchange: nossistsc
nossistsc_rabbitmq_cluster_type: Local
nossistsc_rabbitmq_prefetch: 10
nossistsc_rabbitmq_binding_key: nossistsc.ci.docker
nossistsc_rabbitmq_queue: nossistsc-opercat-ci-docker

opercat_rabbitmq_exchange: opercat
opercat_rabbitmq_retries: 3
opercat_rabbitmq_retry_interval: 500
opercat_rabbitmq_routing_key: opercat.ci.docker

hostname_rt: "{{ na_oper_cat_backend_host }}"

log4j2:
  rollingFile:
    SERVER:
      file: "/var/log/alticelabs/na/na-operations-catalog/server.log"
      maxFileSize: "100MB"
      maxBackupIndex: "12"
    CONFIG_PROPERTY:
      file: "/var/log/alticelabs/na/na-operations-catalog/configProperty.log"
      maxFileSize: "100MB"
      maxBackupIndex: "12"
    AUDITING:
      file: "/var/log/alticelabs/na/na-operations-catalog/audit.log"
      maxFileSize: "100MB"
      maxBackupIndex: "12"
    NOTIFICATION:
      file: "/var/log/alticelabs/na/na-operations-catalog/notification.log"
      maxFileSize: "100MB"
      maxBackupIndex: "12"
  loggers:
    - { name: "pt.ptinovacao.asf.configuration", level: "info", refs: ["CONFIG_PROPERTY"], additivity: "false" }
    - { name: "pt.ptinovacao.netwin.auditing", level: "info", refs: [ "AUDITING" ], additivity: "false" }
    - { name: "pt.ptinovacao.na.notification.subscriber", level: "info", refs: ["NOTIFICATION"], additivity: "false" }
    - { name: "pt.ptinovacao.nacommons.asf.rabbitmq.provider", level: "info", refs: ["NOTIFICATION"], additivity: "false" }
    - { name: "pt.ptinovacao", level: "info", refs: [ "SERVER" ], additivity: "false" }
    - { name: "org.hibernate", level: "info", refs: ["SERVER"], additivity: "false" }
  root:
    level: "info"
    refs: [ "SERVER" ]