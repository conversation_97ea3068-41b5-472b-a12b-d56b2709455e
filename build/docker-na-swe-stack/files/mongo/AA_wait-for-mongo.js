var conn;
var interval = 250;   // 250 Milliseconds polling
var timeout  = 30000; // 30 Seconds timeout
var startTime = new Date();
var safeSleep = 2500;
const mongoUri = _getEnv("MONGO_URI")

while(conn === undefined) {
  try {
    var elapsed = (new Date() - startTime);
    print("Attempting to connect to db, elapsed: " + elapsed + "ms");
    conn = new Mongo(mongoUri);
  } catch(error) {
    print(error);
    if ((new Date() - startTime) >= timeout) {
      print("ERROR: Failed to establish connection within timeout (" + timeout + "ms)");
      quit(1);
    } else {
      sleep(interval);
    }
  }
}

sleep(safeSleep);
print("MongoDB connection established in " + (new Date() - startTime) + "ms");