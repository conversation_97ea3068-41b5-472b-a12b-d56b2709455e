version: "3.5"

services:

  zookeeper:
     hostname: zookeeper
     container_name: zookeeper
     image: rdocker.ptin.corppt.com/extsw/zookeeper:3.6.3
     ports:
       - 32181:2181

  rabbitmq:
     hostname: rabbitmq
     container_name: rabbitmq
     image: rdocker.ptin.corppt.com/na-commons/na-commons-rabbitmq-server:5.1
     volumes:
       - ./files/rabbitmq/all.yml:/config/all.yml
       - ./files/rabbitmq/wait-for-rabbitmq.sh:/wait-for-rabbitmq.sh
     environment:
       - RABBITMQ_USE_LONGNAME=False
       - HOSTNAME=rabbitmq
     ports:
       - 5672:5672
       - 15672:15672

  postgresql:
    hostname: postgresql
    container_name: postgresql
    image: postgres:16
    volumes:
      - ./pgsql-db/:/var/lib/pgsql/16/
    environment:
      - POSTGRES_USER=ptin_admin
      - POSTGRES_PASSWORD=PTIN_ADMIN
      - POSTGRES_DB=postgresc
      - PGDATA=/var/lib/pgsql/data
    ports:
      - 35432:5432

  postgresql-nossistsc-conf:
    image: rdocker.ptin.corppt.com/nossis-tsc/nossis-tsc-db-conf-pgsql12:5.1
    hostname: postgresql-nossistsc-conf
    container_name: postgresql-nossistsc-conf
    volumes:
      - ./files/nossistsc-db-conf/all.yml:/config/all.yml
      - ./pgsql-db/nossistsc/data:/var/lib/pgsql/16/nossistsc/data
      - ./pgsql-db/nossistsc/indx:/var/lib/pgsql/16/nossistsc/indx
    environment:
      - DB_HOST=postgresql
      - DB_PORT=5432
      - DB_DBA_USER=ptin_admin
      - DB_DBA_PWD=PTIN_ADMIN
      - DB_NAME=nossistsc
    entrypoint: bash -c "/entrypoint.sh --create-or-update"

  postgresql-opercat-conf:
    image: rdocker.ptin.corppt.com/na/na-operations-catalog-db-conf-pgsql12:10.1
    hostname: postgresql-opercat-conf
    container_name: postgresql-opercat-conf
    volumes:
       - ./files/opercat-db-conf/all.yml:/config/all.yml
       - ./pgsql-db/na/data:/var/lib/pgsql/16/na/data
       - ./pgsql-db/na/indx:/var/lib/pgsql/16/na/indx
    environment:
      - DB_HOST=postgresql
      - DB_PORT=5432
      - DB_DBA_USER=ptin_admin
      - DB_DBA_PWD=PTIN_ADMIN
      - DB_NAME=na
    entrypoint: bash -c "/entrypoint.sh --create-or-update"

  nossistsc-backend:
    image: rdocker.ptin.corppt.com/nossis-tsc/nossis-tsc-backend:5.1
    hostname: nossistsc-backend
    container_name: nossistsc-backend
    depends_on:
      - postgresql
      - zookeeper
      - rabbitmq
    volumes:
      - ./files/nossistsc-backend/all.yml:/config/all.yml
      - ./logs/nossis-tsc:/var/log/alticelabs/nossis-tsc/backend/
    ports:
      - 39000:9015

  opercat-backend:
    image: rdocker.ptin.corppt.com/na/na-operations-catalog:10.1
    container_name: opercat-backend
    depends_on:
      - postgresql
      - zookeeper
      - rabbitmq
    environment:
      - TESTING_MODE=true
    volumes:
      - ./files/opercat-backend/all.yml:/config/all.yml
      - ./logs/oper-cat:/var/log/alticelabs/na/na-operations-catalog/
    ports:
      - 39001:9015

  mongo-sidecar:
    image: rdocker.ptin.corppt.com/na-commons/na-commons-mongo-client-sidecar:5.1
    hostname: mongo-sidecar
    container_name: mongo-sidecar
    volumes:
      - ./files/mongo/AA_wait-for-mongo.js:/docker-entrypoint-scripts.d/AA_wait-for-mongo.js
      - ./files/mongo/configure-users.js:/docker-entrypoint-scripts.d/configure-users.js
      - /opt/alticelabs/na/conf/na-it-swe-mongo-scripts/addWorkflows.js:/docker-entrypoint-scripts.d/na-it-swe-mongo-scripts/addWorkflows.js
      - /opt/alticelabs/na/conf/na-it-swe-mongo-scripts/errorHandling.js:/docker-entrypoint-scripts.d/na-it-swe-mongo-scripts/errorHandling.js
    environment:
      - MONGO_URI=mongodb://naportal-ci.c.ptin.corppt.com:27017/admin

  na-swe-it:
    image: rdocker.ptin.corppt.com/na/na-swe-it:10.1
    hostname: na-swe-it
    container_name: na-swe-it
    depends_on:
      - rabbitmq
    volumes:
      - ./files/swe-it/all.yml:/config/all.yml
      - ./logs/swe-it:/var/log/alticelabs/na/na-swe/
    ports:
      - 39004:9004

  na-swe-sidecar:
    build: ./files/swe-sidecar
    hostname: na-swe-sidecar
    depends_on:
      - na-swe-it
    container_name: na-swe-sidecar

  na-order-followup:
    image: rdocker.ptin.corppt.com/na/na-order-followup:10.1
    hostname: na-order-followup
    container_name: na-order-followup
    volumes:
      - ./files/followup/all.yml:/config/all.yml
      - ./logs/order-followup:/var/log/alticelabs/na/na-order-followup/
    ports:
      - 39006:9006

  nossis-order-follow-up:
    image: ghcr.io/alticelabsprojects/nossis-order-follow-up/nossis-order-follow-up-service:1.1.0-main-2d964e0
    hostname: nossis-order-followup
    container_name: nossis-order-followup
    ports:
      - 39007:9006
    environment:
      - noof.configuration.clients.na-order-followup.uri.origin=http://na-order-followup:9006
      - quarkus.log.handler.gelf.enabled=false
      - quarkus.log.console.enable=true

  na-commons-network-stream-gateway:
    container_name: na-commons-network-stream-gateway
    image: rdocker.ptin.corppt.com/na-commons/na-commons-network-stream-gateway:5.1
    hostname: na-commons-network-stream-gateway
    volumes:
      - ./files/network-stream-gateway/all.yml:/config/all.yml
    environment:
      - network_stream_gateway_mongodb_uri=mongodb://naportal-ci.c.ptin.corppt.com:27017
    ports:
      - "9020:9020"
      - "9021:9021"

  na-it-data-snapshots:
    image: rdocker.ptin.corppt.com/na/na-it-data-snapshots:10.1
    hostname: na-it-data-snapshots
    container_name: na-it-data-snapshots
    depends_on:
      - opercat-backend
      - nossistsc-backend
      - na-swe-it
    volumes:
      - ./files/na-it-data-snapshots/all.yml:/config/all.yml
      - ./files/na-it-data-snapshots/wait-for-backend.sh:/wait-for-backend.sh
    entrypoint: bash -c "/wait-for-backend.sh && /entrypoint.sh /config/all.yml /opt/alticelabs -d /config/defaults.yml"

  na-portal-it-data-snapshots:
    image: rdocker.ptin.corppt.com/na-portal-tst/na-portal-it-web-data-snapshots:10.3.0
    hostname: na-portal-it-data-snapshots
    container_name: na-portal-it-data-snapshots
    depends_on:
      - opercat-backend
      - nossistsc-backend
      - na-swe-it
    volumes:
      - ./files/na-portal-it-data-snapshots/all.yml:/config/all.yml
      - ./files/na-portal-it-data-snapshots/wait-for-backend.sh:/wait-for-backend.sh
    ports:
      - 35001:35001
    entrypoint: bash -c "/wait-for-backend.sh && /entrypoint.sh /config/all.yml /opt/alticelabs"

  na-portal-it-create-entities-operations-versions:
    image: rdocker.ptin.corppt.com/na-portal-tst/na-portal-it-create-entities-operations-versions:10.3.0
    hostname: na-portal-it-create-entities-operations-versions
    container_name: na-portal-it-create-entities-operations-versions
    command: "pt.ptinovacao.na.portal.it.create.entities.operations.versions.NaPortalScenarioBuilder"
    depends_on:
      - opercat-backend
      - nossistsc-backend

  na-portal-it-request-callback-server:
    build: ./files/request-callback-server
    image: na-portal-it-request-callback-server
    hostname: na-portal-it-request-callback-server
    container_name: na-portal-it-request-callback-server
    privileged: true
    ports:
      - 35000:8080
