#!groovy
@Library('sso-shared') _

pipeline {

    parameters {
        string(name: 'browser', defaultValue: 'chrome', description: 'choose browser for integration tests')
        booleanParam(name: 'skip_installation', defaultValue: false, description: 'check to disable installation stages')
        booleanParam(name: 'skip_na_scenarios', defaultValue: false, description: 'check to disable installation stages for scenario-na-oc-swe+order-followup-backend and scenario-na-lite scenarios')
        booleanParam(name: 'skip_tests_core', defaultValue: false, description: 'check to disable test stages for core scenarios')
        booleanParam(name: 'skip_tests_web_dm', defaultValue: false, description: 'check to disable test stages for DM web scenarios')
        booleanParam(name: 'skip_tests_web_fulfillment', defaultValue: false, description: 'check to disable test stages for Fulfillment web scenarios')
        string(name: 'tests_web_version', defaultValue: '3.0.0-main', description: 'choose nossis-orchestration-test image version for web tests')
    }

    environment {

        ANSIBLE_CI_PREPARE_ENV_PLAYBOOK='/opt/alticelabs/automation/na-portal/inventory/ci/prepare_env.yml'
        ANSIBLE_CI_PLAYBOOK='/opt/alticelabs/automation/na-portal/inventory/ci/install_full.yml'
        ANSIBLE_CI_IAM_CONFIGURE_PLAYBOOK='/opt/alticelabs/automation/na-portal/ci-na-portal-iam-configurator.yml'
        ANSIBLE_CI_HOST_FILE='/opt/alticelabs/automation/na-portal/inventory/ci/pgsql/hosts.ini'
        TENANT='naportal'
        CI_USERNAME='ci-na-portal'
        CI_PASSWORD='c1-na-portal'
        MVN_WEB_DOWN_TIME = '2'

        PRSYNC_USER='ci-na-portal'
        PRSYNC_PASS='c1-na-portal'
        NA_LITE_PORT='18080'
        NOSSIS_TSC_BACKEND_PORT = '39000'
        NA_OPER_CAT_BACKEND_PORT = '39001'

        NA_ORDER_FOLLOWUP_MONGO_PORT = '39017'

        CI_NAPORTAL_HOST = 'naportal-ci.c.ptin.corppt.com'

        // Service Ports
        JBOSS_MPT_GO_PORT = 8280
        CORE_REFERENCE_DATA_PORT = 9015
        NAEM_DBMANAGER_PORT = 8180
        FRONTEND_PORTAL_PORT = 19000
        FRONTEND_OPERATIONS_CATALOG_PORT = 19002
        FRONTEND_REFERENCE_DATA_PORT = 19003
        FRONTEND_MONITORING_PORT = 19001
        FRONTEND_ENTITIES_CATALOG_PORT = 29001
        FRONTEND_GO_PORT = 19005
        FRONTEND_MPT_PORT = 19006
        FRONTEND_NADM_PORT = 19004
        FRONTEND_DIAGNOSTICS_PORT = 19008
        WEB_BALANCER_PORT = 443

        MONGODB_URI_INIT="mongodb://naportal-ci.c.ptin.corppt.com:27017"

        VEACS_DUMP_FILE_NAME="naportal-veacs-dump-r5.tar.gz"
        IT_ENV_DB = 'pgsql'
        IT_ENV = "$IT_ENV_DB-IAM"

        GITHUB = credentials('ci-sso-alb-id')

        PROJECT_VERSION = readMavenPom().getVersion().trim()
        PROJECT_FIX_VERSION = "${PROJECT_VERSION}".replace("-SNAPSHOT", "")
    }

    options {
        timestamps()
        disableConcurrentBuilds()
        lock 'naportal-ci'
        rateLimitBuilds(
                throttle: [
                        enabled: false
                ]
        )
    }

    agent {
        dockerfile {
            label 'docker && internet'
            dir "build/jenkins-docker"
            args '-u 2222:2223 -v /home/<USER>/.ssh:/home/<USER>/.ssh -v /home/<USER>/m2 --privileged -v ${WORKSPACE}/docker:/var/lib/docker'
        }
    }

    triggers {
        parameterizedCron('''
		#
		H 3 * * 1,3,5,7 %browser=chrome
		H 3 * * 2,4,6 %browser=firefox
	''')
    }


    stages {

        stage('prepare-installation') {
            parallel {
                stage('pcloud') {
                    when { expression { !params.skip_installation } }
                    steps {
                        sh 'echo $CI_PASSWORD | /opt/alticelabs/pdsngtools/bin/pcloud login $TENANT $CI_USERNAME 2> /dev/null'
                        sh 'echo "Current server list" ; /opt/alticelabs/pdsngtools/bin/pcloud server list'
                        sh '/opt/alticelabs/pdsngtools/bin/pcloud server revert --force naportal-ci'
                        catchError(buildResult: 'SUCCESS', stageResult: 'SUCCESS') {
                            sh "/opt/alticelabs/pdsngtools/bin/pcloud server start naportal-ci"
                        }
                        sh 'echo "Current server list" ; /opt/alticelabs/pdsngtools/bin/pcloud server list'
                        sh '/opt/alticelabs/pdsngtools/bin/pcloud logout'
                    }
                }
                stage('install-automation') {
                    when { expression { !params.skip_installation } }
                    environment {
                        PROJECT_VERSION = sh(script: "mvn help:evaluate -Dexpression=project.version -q -DforceStdout", returnStdout: true)
                                .trim()
                                .replaceAll("-SNAPSHOT", "")
                        NOSSIS_TSC_VERSION = sh(script: "mvn help:evaluate -Dexpression=version.pt.ptinovacao.nossis-tsc -q -DforceStdout", returnStdout: true)
                                .trim()
                                .replaceAll("-SNAPSHOT", "")
                        NOSSIS_FRMWRK_VERSION = sh(script: "mvn help:evaluate -Dexpression=version.pt.ptinovacao.nossis-frmwrk -q -DforceStdout", returnStdout: true)
                                .trim()
                                .replaceAll("-SNAPSHOT", "")
                    }
                    steps {
                        script {
                            echo "Install automation for version ${PROJECT_VERSION}"
                            sh "sudo yum install -y na-portal-automation-${PROJECT_VERSION}* nossis-tsc-automation-${NOSSIS_TSC_VERSION}* nossis-frmwrk-automation-${NOSSIS_FRMWRK_VERSION}*"
                        }
                    }
                }
                stage('clean') {
                    when { expression { !params.skip_installation } }
                    steps {
                        sh "ls -1 | grep branch- | xargs -n1 -P5 rm -rf"
                    }
                }
            }
        }

        stage('wait-for-hosts-to-be-ready') {
            when { expression { !params.skip_installation } }
            steps {
                sh "ANSIBLE_HOST_KEY_CHECKING=False ansible -i $ANSIBLE_CI_HOST_FILE env-pdocker -m wait_for_connection -a \"timeout=300\""
            }
        }

        stage('prepare-env') {
            when { expression { !params.skip_installation } }
            steps {
                sh "ANSIBLE_HOST_KEY_CHECKING=False ansible-playbook $ANSIBLE_CI_PREPARE_ENV_PLAYBOOK -i $ANSIBLE_CI_HOST_FILE -vv"
            }
        }

        stage('apply-pdocker-playbook-env') {
            when { expression { !params.skip_installation } }
            steps {
                sh "ANSIBLE_HOST_KEY_CHECKING=False ansible-playbook -i $ANSIBLE_CI_HOST_FILE /opt/alticelabs/automation/nossis-frmwrk/pdocker.yml -vv"
            }
        }

        stage('install-env') {
            when { expression { !params.skip_installation } }
            steps {
                sh '''
                    sshpass -p ptin_admin ssh -o StrictHostKeyChecking=no ptin_admin@${CI_NAPORTAL_HOST} -t "
                        export MONGO_URI=${MONGODB_URI_INIT}
                    
                        sudo install -d -m 0777 -o 2016 -g 2016 /var/tmp/na-portal/catalogs
                        
                        [ -d ~/na-portal-stack ] && cd ~/na-portal-stack && sudo docker-compose down
                        
                        cd
                        sudo rm -rf ~/na-portal-stack

                        svn co https://svn.ptin.corppt.com/repo/na-portal/${BRANCH_NAME}/buildfiles/docker-compose/na-portal ~/na-portal-stack --username=ci-na-portal --password=c1-na-portal --non-interactive
                        cd ~/na-portal-stack
                        sudo install -d -m 0777 -o ptin_admin -g ptin_admin files/mongo/data/db/ logs/nadm/
                        sudo -E bash -c 'docker-compose up mongo-sidecar-it'
                        sudo -E bash -c 'docker-compose up -d nadm na-portal-data-management-sidecar'
                    "
                '''
            }
        }

        stage('run-automation') {
            parallel {
                stage('scenario-na-lite') {
                    when { expression { !params.skip_na_scenarios && !params.skip_installation } }
                    steps {
                        sh '''
                          sshpass -p ptin_admin ssh -o StrictHostKeyChecking=no ptin_admin@${CI_NAPORTAL_HOST} -t "
                            export NA_LITE_PORT=${NA_LITE_PORT}

                            [ -d ~/docker-naftth-ptp ] && cd ~/docker-naftth-ptp && sudo docker-compose down
                            
                            cd 
                            sudo rm -rf ~/docker-naftth-ptp

                            svn co https://svn.ptin.corppt.com/repo/na-portal/${BRANCH_NAME}/build/docker-naftth-ptp ~/docker-naftth-ptp --username=ci-na-portal --password=c1-na-portal --non-interactive
                            cd ~/docker-naftth-ptp
                            sudo -E docker-compose up -d --build
                          "
                        '''
                    }
                }
                stage('scenario-na-oc-swe+order-followup-backend') {
                    when { expression { !params.skip_na_scenarios && !params.skip_installation } }
                    steps {
                        sh '''
                          sshpass -p ptin_admin ssh -o StrictHostKeyChecking=no ptin_admin@${CI_NAPORTAL_HOST} -t "
                            sudo docker login ghcr.io -u ${GITHUB_USR} -p ${GITHUB_PSW}

                            [ -d ~/docker-na-swe-stack ] && cd ~/docker-na-swe-stack && sudo docker-compose down
                            
                            cd
                            sudo rm -rf ~/docker-na-swe-stack
                            sudo yum erase -y na-it-swe-mongo-scripts*
                            sudo yum install -y na-it-swe-mongo-scripts-9.0*
                            
                            svn co https://svn.ptin.corppt.com/repo/na-portal/${BRANCH_NAME}/build/docker-na-swe-stack ~/docker-na-swe-stack --username=ci-na-portal --password=c1-na-portal --non-interactive
                            
                            cd ~/docker-na-swe-stack

                            mkdir -p pgsql-db/ && sudo chmod 777 pgsql-db/
                            sudo install -d -m 0755 -o 999 -g 999 pgsql-db/na pgsql-db/na/data pgsql-db/na/indx
                            sudo install -d -m 0755 -o 999 -g 999 pgsql-db/nossistsc pgsql-db/nossistsc/data pgsql-db/nossistsc/indx
                            
                            sudo chmod 777 logs/swe-it logs/oper-cat logs/nossis-tsc logs/order-followup
                            sudo docker-compose pull --ignore-pull-failures
                            sudo docker-compose up -d rabbitmq
                            sudo docker-compose up -d postgresql zookeeper na-portal-it-request-callback-server
                            sudo docker-compose up mongo-sidecar
                            sudo docker-compose exec -T rabbitmq bash -c "/wait-for-rabbitmq.sh"
                            sudo docker-compose up postgresql-opercat-conf postgresql-nossistsc-conf
                            sudo docker-compose up -d opercat-backend nossistsc-backend na-commons-network-stream-gateway na-order-followup na-swe-it
                            sudo docker-compose up -d nossis-order-follow-up
                            sudo docker-compose up na-swe-sidecar
                            sudo docker-compose up na-it-data-snapshots
                            sudo docker-compose up na-portal-it-data-snapshots na-portal-it-create-entities-operations-versions
                          "
                        '''
                    }
                }
                stage('run_iam_configure') {
                    when { expression { !params.skip_installation } }
                    steps {
                        lock(resource: "naportal-ci-iam-configure") {
                            sh "ANSIBLE_STRATEGY=linear ANSIBLE_HOST_KEY_CHECKING=False ANSIBLE_FILTER_PLUGINS=/opt/alticelabs/automation/na-commons/filter_plugins:/opt/alticelabs/automation/iam/filter_plugins ansible-playbook $ANSIBLE_CI_IAM_CONFIGURE_PLAYBOOK -i $ANSIBLE_CI_HOST_FILE -T 120 -vv"
                        }
                    }
                }
                stage('run_ansible_pgsql') {
                    when { expression { !params.skip_installation } }
                    steps {
                        sh "ANSIBLE_STRATEGY=linear ANSIBLE_HOST_KEY_CHECKING=False ANSIBLE_FILTER_PLUGINS=/opt/alticelabs/automation/na-commons/filter_plugins:/opt/alticelabs/automation/iam/filter_plugins ansible-playbook $ANSIBLE_CI_PLAYBOOK -i $ANSIBLE_CI_HOST_FILE -T 120 --extra-vars \"src_dir=${WORKSPACE}/build/db/pgsql na_lite_host_port=${CI_NAPORTAL_HOST}:${NA_LITE_PORT}\" -vv"
                    }
                }
            }
        }

        stage('health-check'){
            parallel {
                stage('jboss-mpt-go') {
                    steps {
                        healthCheck("$CI_NAPORTAL_HOST:$JBOSS_MPT_GO_PORT/health", "${STAGE_NAME}")
                    }
                }
                stage('core-reference-data') {
                    steps {
                        healthCheck("$CI_NAPORTAL_HOST:$CORE_REFERENCE_DATA_PORT/health", "${STAGE_NAME}")
                    }
                }
                stage('naem-dbmanager') {
                    steps {
                        healthCheck("$CI_NAPORTAL_HOST:$NAEM_DBMANAGER_PORT/health", "${STAGE_NAME}")
                    }
                }
                stage('frontend-portal') {
                    steps {
                        healthCheck("$CI_NAPORTAL_HOST:$FRONTEND_PORTAL_PORT/health", "${STAGE_NAME}")
                    }
                }
                stage('frontend-operations-catalog') {
                    steps {
                        healthCheck("$CI_NAPORTAL_HOST:$FRONTEND_OPERATIONS_CATALOG_PORT/health", "${STAGE_NAME}")
                    }
                }
                stage('frontend-reference-data') {
                    steps {
                        healthCheck("$CI_NAPORTAL_HOST:$FRONTEND_REFERENCE_DATA_PORT/health", "${STAGE_NAME}")
                    }
                }
                stage('frontend-monitoring') {
                    steps {
                        healthCheck("$CI_NAPORTAL_HOST:$FRONTEND_MONITORING_PORT/health", "${STAGE_NAME}")
                    }
                }
                stage('frontend-entities-catalog') {
                    steps {
                        healthCheck("$CI_NAPORTAL_HOST:$FRONTEND_ENTITIES_CATALOG_PORT/health", "${STAGE_NAME}")
                    }
                }
                stage('frontend-mpt') {
                    steps {
                        healthCheck("$CI_NAPORTAL_HOST:$FRONTEND_MPT_PORT/health", "${STAGE_NAME}")
                    }
                }
                stage('frontend-go') {
                    steps {
                        healthCheck("$CI_NAPORTAL_HOST:$FRONTEND_GO_PORT/health", "${STAGE_NAME}")
                    }
                }
                stage('frontend-nadm') {
                    steps {
                        healthCheck("$CI_NAPORTAL_HOST:$FRONTEND_NADM_PORT/health", "${STAGE_NAME}")
                    }
                }
                stage('frontend-diagnostics') {
                    steps {
                        healthCheck("$CI_NAPORTAL_HOST:$FRONTEND_DIAGNOSTICS_PORT/health", "${STAGE_NAME}")
                    }
                }
                stage('web-balancer') {
                    steps {
                        healthCheck("https://$CI_NAPORTAL_HOST:$WEB_BALANCER_PORT/health", "${STAGE_NAME}")
                    }
                }
            }
        }

        stage('copy branches') {
            when { expression { (!params.skip_tests_core || !params.skip_tests_web_dm || !params.skip_tests_web_fulfillment) } }
            steps {
                sh 'pwd && ls -la'
                sh 'rm -rf /tmp/branch-* ./branch-*'
                sh 'cp -r . /tmp/branch-pgsql-core'
                sh 'cp -r . /tmp/branch-pgsql-web'
                sh 'mv -f /tmp/branch-* .'
            }
        }

        stage('Setup docker') {
            when { expression { !params.skip_tests_web_dm || !params.skip_tests_web_fulfillment } }
            steps {
                sh 'ln -s $DOCKER_CONFIG/config.json ~/.docker/'
                sh 'sudo bash -c "/docker-ce/start.sh"'
                sh 'mvn -version'
                sh 'docker info'
                sh "docker login ghcr.io -u '${GITHUB_USR}' -p ${GITHUB_PSW}"
            }
        }

        stage('populate-data') {
            parallel {
                stage('nadm-db') {
                    when { expression { !params.skip_installation } }
                    steps {
                        sh "rm -rf /tmp/veacs && mkdir /tmp/veacs && sshpass -p $PRSYNC_PASS rsync -e 'ssh -o StrictHostKeyChecking=no' -avz --delete $<EMAIL>:./$VEACS_DUMP_FILE_NAME /tmp/veacs"
                        sh "cd /tmp/veacs && tar -xvf $VEACS_DUMP_FILE_NAME"
                        sh "mongo naportal-ci.c.ptin.corppt.com/veacs -u naportal -p naportal --authenticationDatabase naportal --eval \"db.dropDatabase()\""
                        sh "/usr/bin/mongorestore --uri=$MONGODB_URI_INIT -d veacs /tmp/veacs/backup"
                    }
                }
                // These catalogs are being populated here because they are mantained by NADM AUS teams.
                // - The catalog instances named DOCSIS* have several catalog definitions useful for development purposes
                // - These instances should not be used for integration tests as they may be externally modified
                // The remaining catalogs (and their instances) used in Integration Tests are populated using "data dumps" with SQL scripts.
                stage('data-management-nadm-aus-catalogs') {
                    when { expression { !params.skip_tests_core } }
                    steps {
                        sh '''
                          sshpass -p ptin_admin ssh -o StrictHostKeyChecking=no ptin_admin@${CI_NAPORTAL_HOST} -t "

                            mkdir -p /tmp/nadm-aus-omp-refdata
                            echo '---' > /tmp/nadm-aus-omp-refdata/all.yml
                            echo 'na_portal_be_nbi_host: ${CI_NAPORTAL_HOST}' >> /tmp/nadm-aus-omp-refdata/all.yml
                            echo 'na_portal_be_nossis_inv_port: ${CORE_REFERENCE_DATA_PORT}' >> /tmp/nadm-aus-omp-refdata/all.yml

                            sudo docker run -v /tmp/nadm-aus-omp-refdata/all.yml:/config/all.yml rdocker.ptin.corppt.com/nadm-aus/nadm-aus-omp-refdata:1
                          "
                        '''
                    }
                }
            }
        }


        stage('core-tests') {
            when { expression { !params.skip_tests_core } }
            steps {
                publishTestReport(
                        "na-portal-tests",
                        "branch-pgsql-core/tests/integration/core",
                        "integration-tests",
                        "na-portal-integration-tests-core - pgsql"
                )
            }
            post {
                always {
                    junit 'branch-pgsql-core/tests/integration/core/target/failsafe-reports/*.xml'
                }
            }
        }

        stage('test-web') {
            stages {
                stage('dm-tests') {
                    when { expression { !params.skip_tests_web_dm } }
                    steps {
                        script {
                            catchError(buildResult: "FAILURE", stageResult: "FAILURE") {
                                dir('branch-pgsql-web/tests/integration/web'){
                                    sh "mkdir dm"
                                    sh """
                                docker run --name playwright-pgsql-web-dm --env-file 'conf/playwright-ci/.env' \
                                    ghcr.io/alticelabsprojects/nossis-orchestration-tests/nossis-orchestration-tests-frontend:${params.tests_web_version} \
                                    --project='*:mpt:*' \
                                    --project='*:go:*' \
                                    --project='*:nadm:*' \
                                    --project='*:data-management:*' \
                                    --workers='1' || true
                            """
                                    sh "docker cp playwright-pgsql-web-dm:/playwright/reports dm/reports"
                                }

                                publishHTML target: [
                                        allowMissing         : true,
                                        alwaysLinkToLastBuild: false,
                                        keepAll              : true,
                                        reportDir            : 'branch-pgsql-web/tests/integration/web/dm/reports/html',
                                        reportFiles          : 'index.html',
                                        reportName           : 'DM Tests Report',
                                ]
                                junit 'branch-pgsql-web/tests/integration/web/dm/reports/junit.xml'
                            }
                        }
                    }

                }

                stage('fulfillment-tests') {
                    when { expression { !params.skip_tests_web_fulfillment } }
                    steps {
                        script {
                            catchError(buildResult: "FAILURE", stageResult: "FAILURE") {
                                dir('branch-pgsql-web/tests/integration/web'){
                                    sh "mkdir -p fulfillment"
                                    sh """
                            docker run --name playwright-pgsql-web-fulfillment --env-file 'conf/playwright-ci/.env' \
                                ghcr.io/alticelabsprojects/nossis-orchestration-tests/nossis-orchestration-tests-frontend:${params.tests_web_version} \
                                --project='*:entities-catalog:*' \
                                --project='*:operations-catalog:*' \
                                --project='*:monitoring:*' \
                                --workers='1' || true
                            """
                                    sh "docker cp playwright-pgsql-web-fulfillment:/playwright/reports fulfillment/reports"
                                }

                                publishHTML target: [
                                        allowMissing         : true,
                                        alwaysLinkToLastBuild: false,
                                        keepAll              : true,
                                        reportDir            : 'branch-pgsql-web/tests/integration/web/fulfillment/reports/html',
                                        reportFiles          : 'index.html',
                                        reportName           : 'Fulfillment Tests Report',
                                ]
                                junit 'branch-pgsql-web/tests/integration/web/fulfillment/reports/junit.xml'
                            }
                        }
                    }
                }
            }

        }

        stage("teardown docker") {
            when { expression { !params.skip_tests_web_dm || !params.skip_tests_web_fulfillment } }
            steps {
                sh "docker images"
                sh "docker logout ghcr.io"
                sh 'sudo bash -c "/docker-ce/prune.sh"'
                sh 'sudo bash -c "/docker-ce/stop.sh"'
            }
        }

        stage('post results') {
            steps {
                sleep time: 5, unit: "SECONDS"
            }
            post {
                always {
                    sh "ls -1 | grep branch- | xargs -n1 -P5 rm -rf"
                    sh '/opt/alticelabs/pdsngtools/bin/pcloud logout'
                }
                changed {
                    sendNotifications(currentBuild.result, '${FULFILLMENT_RECIPIENTS}')
                }
                cleanup {
                    sh 'sudo bash -c "rm -rf ${WORKSPACE}/docker ${WORKSPACE}/*tar.gz"'
                }
            }
        }

    }
}

def healthCheck(hc_url, serviceName) {
    sh """
        set +ex
        RETRIES=20

        echo "Health checking $serviceName service..."

        while [ \$RETRIES -gt 0 ]
        do
            OUTPUT=\$(curl --insecure -s -f -o /dev/null -w '%{http_code}' $hc_url)
            if [ "\$OUTPUT" -eq "200" ]; then
                echo "$serviceName is up and running!"
                break
            fi
            ((RETRIES=RETRIES-1))
            echo "$serviceName service is down! \$RETRIES retries left. Sleeping 5 seconds."
            sleep 5
        done
        if [ \$RETRIES -le 0 ]; then
            echo "$serviceName service unavailable. Please confirm environment installation."
            exit 1
        fi
    """
}

def publishTestReport(String label, String src, String itMavenProfile, String summary) {
    def pServerInstance = env.SERVER_INSTANCE ?: '9922f776-632e-41c2-bf65-2d1f4f36aa6a'
    def pJIRASite = env.JIRA_SITE ?: 'JIRA_alabs'
    def jiraIssueKey = null

    try {
        if (!src || !label || !itMavenProfile) {
            error("Missing required parameters: 'src', 'label', or 'itMavenProfile'.")
        }

        timeout(time: 2, unit: 'HOURS') {
            stage("Running tests: ${label}") {
                ssoMaven('-f ' + src + '/pom.xml clean install -fn -P' + itMavenProfile + ' -Dbd.env="$IT_ENV_DB" -Djava.util.logging.manager=org.jboss.logmanager.LogManager')
                archiveArtifacts artifacts: "${src}/target/failsafe-reports/junit-test-report-xray.xml", allowEmptyArchive: false
            }

            stage("Creating Jira Issue: ${label}") {
                def testExecIssue = [fields: [
                        project          : ['key': 'NAPORTAL'],
                        summary          : "Continuous Integration Execution Report for NAPORTAL v${PROJECT_FIX_VERSION} (${summary})",
                        description      : "NAPORTAL version: ${PROJECT_FIX_VERSION}, revision: ${env.SVN_REVISION}",
                        issuetype        : ['id': '10008'],
                        customfield_10206: env.SVN_REVISION,
                        fixVersions      : [['name': PROJECT_FIX_VERSION]],
                        assignee         : ['name': 'Unassigned']
                ]]

                def jiraIssue = jiraNewIssue issue: testExecIssue, site: pJIRASite
                jiraIssueKey = jiraIssue.data.key
                echo "Success: Created Jira issue: ${jiraIssueKey}"
            }

            stage("Upload Test Results to XRAY") {
                step([$class               : 'XrayImportBuilder',
                      endpointName         : '/junit',
                      importFilePath       : "${src}/target/failsafe-reports/junit-test-report-xray.xml",
                      importToSameExecution: 'true',
                      projectKey           : 'NAPORTAL',
                      testExecKey          : jiraIssueKey,
                      serverInstance       : pServerInstance])
                echo "Success: Test results uploaded to XRAY successfully."
            }

            stage("Transition Jira Issue: ${jiraIssueKey}") {
                def transitionToInProgress = [transition: [id: '2']] // ID for 'In Progress'
                def transitionToClose = [transition: [id: '3']]      // ID for 'Close'

                jiraTransitionIssue idOrKey: jiraIssueKey, input: transitionToInProgress, site: pJIRASite
                jiraTransitionIssue idOrKey: jiraIssueKey, input: transitionToClose, site: pJIRASite
                echo "Success: Transitioned Jira issue: ${jiraIssueKey}"
            }
        }

    } catch (e) {
        echo "Error occurred during 'publishTestReport' execution: ${e.message}"
        echo "Stack trace: ${e}"
        currentBuild.result = 'FAILURE'
        throw e
    }
}
