#!groovy
@Library('sso-shared') _

pipeline {
    parameters {
        string(name: '<PERSON><PERSON><PERSON>', defaultValue: 'trunk' )
        booleanParam(name: 'GITHUB_DEPLOY', defaultValue: true, description: 'Enable/Disable github docker deployment')
    }

    environment {
        GITHUB = credentials('ci-sso-alb-id')
    }

    options {
        timestamps()
        disableConcurrentBuilds()
    }

    agent {
        dockerfile {
            dir '.'
            label 'docker && internet'
            args '-u 2222:2223 -v /home/<USER>/.ssh:/home/<USER>/.ssh -v /home/<USER>/m2 --privileged'
        }
    }

    stages {
        stage('Checkout') {
            steps{
                script {
                    checkout changelog: false,
                            poll: false,
                            scm: [
                                    $class: 'SubversionSCM',
                                    filterChangelog: false,
                                    ignoreDirPropChanges: false,
                                    locations: [
                                            [
                                                    cancelProcessOnExternalsFail: true,
                                                    credentialsId: 'fernando-g-almeida-id',
                                                    depthOption: 'infinity',
                                                    ignoreExternalsOption: true,
                                                    local: 'branch',
                                                    remote: 'https://svn.ptin.corppt.com/repo/na-portal/' + params.BRANCH
                                            ]

                                    ],
                                    quietOperation: true,
                                    workspaceUpdater: [
                                            $class: 'CheckoutUpdater'
                                    ]
                            ]
                }
            }
        }

        stage('Prepare container') {
            steps {
                sh 'ln -s $DOCKER_CONFIG/config.json ~/.docker/'
                sh 'sudo bash -c "/docker-ce/start.sh"'
                sh 'mvn -version'
                sh 'docker info'
            }
        }

        stage('Docker Github registry login') {
            when {
                expression {
                    return params.GITHUB_DEPLOY
                }
            }
            steps {
                sh "docker login ghcr.io -u '${GITHUB_USR}' -p ${GITHUB_PSW}"
            }

        }

        stage('Build images') {
            parallel {
                stage('Harbor (testing)') {
                    when {
                        expression {
                            return (params.BRANCH.startsWith('trunk') || params.BRANCH.startsWith('branches')) && !params.GITHUB_DEPLOY
                        }
                    }
                    steps {
                        ssoMaven("clean deploy -T 1C -Pdocker,docker-dev -f branch/src/dist/docker/pom.xml")
                    }
                }
                stage('Harbor (release)') {
                    when {
                        expression {
                            return params.BRANCH.startsWith('tags') && !params.GITHUB_DEPLOY
                        }
                    }
                    steps {
                        ssoMaven("clean deploy -T 1C -Pdocker,docker-release -f branch/src/dist/docker/pom.xml")
                    }
                }
                stage('Harbor & Github (testing)') {
                    when {
                        expression {
                            return (params.BRANCH.startsWith('trunk') || params.BRANCH.startsWith('branches')) && params.GITHUB_DEPLOY
                        }
                    }
                    steps {
                        ssoMaven("clean deploy -T 1C -Pdocker,docker-dev,github,github-dev -f branch/src/dist/docker/pom.xml")
                    }
                }
                stage('Harbor & Github (release)') {
                    when {
                        expression {
                            return params.BRANCH.startsWith('tags') && params.GITHUB_DEPLOY
                        }
                    }
                    steps {
                        ssoMaven("clean deploy -T 1C -Pdocker,docker-release,github,github-release -f branch/src/dist/docker/pom.xml")
                    }
                }
            }
        }

        stage("Docker registry logout") {
            steps {
                sh "docker images"
                sh "docker logout ghcr.io"
                sh 'sudo bash -c "/docker-ce/prune.sh"'
                sh 'sudo bash -c "/docker-ce/stop.sh"'
            }
        }
    }

    post {
        changed {
            sendNotifications(currentBuild.result, '${FULFILLMENT_RECIPIENTS}')
        }
    }
}
