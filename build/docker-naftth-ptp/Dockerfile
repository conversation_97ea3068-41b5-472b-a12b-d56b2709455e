FROM rdocker.ptin.corppt.com/na-commons/na-commons-centos6-jdk8:3.1

ENV JAVA_HOME=/usr/java/default

COPY files/entrypoint.sh /entrypoint.sh

RUN yum install -y repo-ext repo-sol-misc repo-sol-ptp repo-prod && \
    yum install -y na-lite*-7* na-plugins-all-5.10* na-im-na naftth-ptp-base && \
    yumdownloader naftth-ptp-na && \
    rpm -i --nodeps naftth-ptp-na* && \
    rm -f naftth-ptp-na && \
    yum clean all --enablerepo=* && \
    rm -rf /var/cache/yum && \
    chmod +x /entrypoint.sh

ENTRYPOINT ["/entrypoint.sh"]
