SET client_encoding = 'UTF8';
SET check_function_bodies = false;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = ts_naportal_data;

INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (1, 1, 0, 'requestInfo');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (2, 1, 1, 'responseNA');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (3, 1, 2, 'requestParamsNumber');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (4, 1, 3, 'errorCode');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (5, 2, 56, 'TERMINAL_VERSAO_SOFTWARE');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (6, 2, 58, 'TERMINAL_IP');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (7, 2, 69, 'STATION_NAME');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (8, 2, 70, 'DATA_SAIDA');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (9, 3, 0, 'OPERATION');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (10, 4, 9, 'nrTelefone');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (11, 4, 13, 'operState');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (12, 4, 14, 'registered');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (13, 5, 0, 'requestInfo');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (14, 5, 1, 'responseNA');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (15, 5, 2, 'requestParamsNumber');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (17, 6, 0, 'statusExecution');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (18, 6, 1, 'statusMessage');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (33, 5, 3, 'errorCode');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (39, 8, 0, 'requestInfo');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (40, 8, 1, 'responseNA');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (41, 8, 2, 'requestParamsNumber');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (42, 8, 3, 'errorCode');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (43, 9, 0, 'CLIENTNAME');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (45, 10, 0, 'CLIENTNAME');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (46, 10, 1, 'NAME');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (47, 10, 2, 'MODELNAME');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (48, 10, 3, 'DATASAIDA');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (49, 11, 0, 'CLIENTNAME');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (50, 11, 1, 'NAME');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (51, 11, 2, 'MANUFACTURER');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (52, 11, 3, 'MODELNAME');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (53, 11, 4, 'SOFTWARE_VERSION');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (54, 11, 5, 'DATASAIDA');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (55, 12, 0, 'ENTERPRISEALIAS');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (56, 12, 1, 'SIPURI');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (57, 12, 2, 'DATASAIDA');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (58, 13, 0, 'NAME');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (59, 13, 1, 'MANUFACTURER');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (60, 13, 2, 'MODELNAME');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (61, 13, 3, 'SOFTWAREVERSION');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (62, 13, 4, 'SIPURI');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (63, 13, 5, 'LINE');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (64, 13, 6, 'DATASAIDA');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (65, 14, 0, 'NAME');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (66, 14, 1, 'ADVANCEDDATAB64');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (67, 14, 2, 'DATASAIDA');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (68, 15, 0, 'NAME');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (69, 15, 1, 'NEWNAME');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (70, 15, 2, 'DATASAIDA');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (71, 16, 0, 'ENTERPRISEALIAS');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (72, 16, 1, 'NAME');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (73, 16, 2, 'SIPURI');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (74, 16, 3, 'DATASAIDA');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (75, 17, 0, 'ENTERPRISEALIAS');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (76, 17, 1, 'SIPURI');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (77, 17, 2, 'PASSWORD');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (78, 17, 3, 'DATASAIDA');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (79, 18, 0, 'CLIENTNAME');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (81, 19, 0, 'CLIENTNAME');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (82, 19, 1, 'NAME');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (83, 19, 2, 'MODELNAME');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (84, 19, 3, 'DATASAIDA');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (85, 20, 0, 'NAME');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (87, 21, 0, 'ENTERPRISEALIAS');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (90, 21, 1, 'SIPURI');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (91, 21, 2, 'DATASAIDA');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (92, 20, 1, 'DATASAIDA');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (93, 9, 1, 'DATASAIDA');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (94, 18, 1, 'DATASAIDA');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (95, 22, 0, 'requestInfo');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (96, 22, 1, 'responseNA');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (97, 22, 2, 'requestParamsNumber');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (98, 22, 3, 'errorCode');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (99, 23, 0, 'NAME');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (100, 23, 1, 'NEWNAME');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (101, 23, 2, 'DATE');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (102, 23, 3, 'STATUSEXECUTION');
INSERT INTO na_portal.mpt_resp_hdr_cat (id, fk_oper_cat, "position", name) VALUES (103, 23, 4, 'STATUSMESSAGE');

SELECT pg_catalog.setval('na_portal.seq_mpt_resp_hdr_cat', 206, false);