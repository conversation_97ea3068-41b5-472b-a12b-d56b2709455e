#!/bin/bash
set -e

if [ -z "$PORTAL_SVN_REPO" ]; then
  echo "PORTAL_SVN_REPO is not set"
  exit 1
fi
if [ -z "$PORTAL_REPO_DIR" ]; then
  echo "PORTAL_REPO_DIR is not set"
  exit 1
fi
if [ -z "$USERNAME" ]; then
  echo "Username not set"
  exit 1
fi
if [ -z "$PASSWORD" ]; then
  echo "Password not set"
  exit 1
fi
if [ -z "$RELEASE_VERSION" ]; then
  echo "Release version not set"
  exit 1
fi
if [ -z "$MI_SVN_REPO" ]; then
  echo "MI_SVN_REPO is not set"
  exit 1
fi
if [ -z "$MI_SVN_BRANCH" ]; then
  echo "MI_SVN_BRANCH is not set"
  exit 1
fi
if [ -z "$MI_REPO_DIR" ]; then
  echo "MI_REPO_DIR is not set"
  exit 1
fi
if [ -z "$TASK_KEY" ]; then
  echo "TASK_KEY is not set"
  exit 1
fi

rm -rf "$PORTAL_REPO_DIR"
svn co "$PORTAL_SVN_REPO" "$PORTAL_REPO_DIR" \
  --username "$USERNAME" \
  --password "$PASSWORD" \
  --no-auth-cache \
  --trust-server-cert \
  --non-interactive \
  --depth empty
 cd "$PORTAL_REPO_DIR" || { echo "SVN checkout failed, could not find repository locally"; exit 1; }
 svn up "pom.xml" \
  --username "$USERNAME" \
  --password "$PASSWORD" \
  --no-auth-cache \
  --trust-server-cert \
  --non-interactive

if [ "$ENFORCE_VERSION_PARITY" == "true" ]; then
  # shellcheck disable=SC2016
  MVN_VERSION=$(yq -e '.project.version' pom.xml)

  SNAPSHOT_VERSION=${MVN_VERSION/-SNAPSHOT/}

  if [ "$SNAPSHOT_VERSION" != "$RELEASE_VERSION" ]; then
    echo "Release version ($RELEASE_VERSION) does not match pom.xml snapshot version ($SNAPSHOT_VERSION)" 1>&2
    exit 1
  fi
fi

# shellcheck disable=SC2016
PDS_MIN=$(grep -oP '(?<=pdsngtools-migrate-lib\${db.type} &gt;= )[\d]+' ./pom.xml) || true
# shellcheck disable=SC2016
PDS_MAX=$(grep -oP '(?<=pdsngtools-migrate-lib\${db.type} &lt; )[\d]+' ./pom.xml) || true

if [[ -n "$PDS_MIN" && 6 -ne "$PDS_MIN" ]] || [[ -n "$PDS_MAX" && 7 -ne "$PDS_MAX" ]]; then
  echo "PDS VERSION is not supported, supported value is 6 > value >= 7" 1>&2
  exit 1
fi

VERSION_MAJOR=$(echo "$RELEASE_VERSION" | cut -d'.' -f1)
VERSION_MINOR=$(echo "$RELEASE_VERSION" | cut -d'.' -f2)
VERSION_RELEASE=$(echo "$RELEASE_VERSION" | cut -d'.' -f3)
if [[ "$VERSION_RELEASE" == *"SNAPSHOT" ]] ; then
  echo "Cannot release a SNAPSHOT version" 1>&2
  exit 1
fi

rm -rf "$MI_REPO_DIR"
svn co "$MI_SVN_REPO/$MI_SVN_BRANCH" "$MI_REPO_DIR" \
  --username "$USERNAME" \
  --password "$PASSWORD" \
  --no-auth-cache \
  --trust-server-cert \
  --non-interactive
cd "$MI_REPO_DIR" || { echo "SVN checkout failed, could not find repository locally"; exit 1; }

MI_DIR="$MI_REPO_DIR/mi"

echo "Looking for next major and minor migration scripts"

HAS_NEXT_MINOR=$(find "$MI_DIR" -name "*$VERSION_MAJOR.$((VERSION_MINOR + 1)).0*")
HAS_NEXT_MAJOR=$(find "$MI_DIR" -name "*$((VERSION_MAJOR + 1)).0.0*")

echo "Creating forward migration scripts for $RELEASE_VERSION"
if [ -n "$HAS_NEXT_MINOR" ]; then
  echo "Detected next minor version, creating migration script for $RELEASE_VERSION -> $VERSION_MAJOR.$((VERSION_MINOR + 1)).0"
  FILE_NAME="$VERSION_MAJOR.$VERSION_MINOR.$VERSION_RELEASE-$VERSION_MAJOR.$((VERSION_MINOR + 1)).0.mi"
  touch "$MI_DIR/$FILE_NAME"
  (cd "$MI_DIR" || exit 1; svn add "$FILE_NAME")
elif [ -n "$HAS_NEXT_MAJOR" ]; then
  echo "Detected next major version, creating migration script for $RELEASE_VERSION -> $((VERSION_MAJOR + 1)).0.0"
  FILE_NAME="$VERSION_MAJOR.$VERSION_MINOR.$VERSION_RELEASE-$((VERSION_MAJOR + 1)).0.0.mi"
  touch "$MI_DIR/$FILE_NAME"
  (cd "$MI_DIR" || exit 1; svn add "$FILE_NAME")
else
  echo "Failed to detect a next version, no forward migration script will be created"
fi

echo "Creating backward migration scripts for $RELEASE_VERSION"
if [ "$VERSION_RELEASE" -gt 0 ]; then
  HAS_PREVIOUS_VERSION="$(ls "$MI_DIR" | grep -Po "$VERSION_MAJOR\.$VERSION_MINOR\.$((VERSION_RELEASE - 1))")"
  if [ -z "$HAS_PREVIOUS_VERSION" ]; then
    echo "Invalid release version, could not find previous version" 1>&2
    exit 1
  fi
  echo "Detected new release is a revision, creating migration script for $VERSION_MAJOR.$VERSION_MINOR.$((VERSION_RELEASE - 1)) -> $RELEASE_VERSION"

  FILE_NAME="$VERSION_MAJOR.$VERSION_MINOR.$((VERSION_RELEASE - 1))-$VERSION_MAJOR.$VERSION_MINOR.$VERSION_RELEASE.mi"
  touch "$MI_DIR/$FILE_NAME"

  (cd "$MI_DIR" || exit 1; svn add "$FILE_NAME")

elif [ "$VERSION_MINOR" -gt 0 ]; then
  LATEST_REVISION=$(ls "$MI_DIR" | grep -Po "$VERSION_MAJOR\.$((VERSION_MINOR - 1))\.[0-9]+" | cut -d "." -f3 | sort -nr | head -n1)

  HAS_PREVIOUS_VERSION="$(ls "$MI_DIR" | grep -Po "$VERSION_MAJOR\.$((VERSION_MINOR - 1))\.$LATEST_REVISION")"
  if [ -z "$HAS_PREVIOUS_VERSION" ]; then
    echo "Invalid release version, could not find previous version" 1>&2
    exit 1
  fi
  echo "Detected new release is a minor, creating migration script for $VERSION_MAJOR.$((VERSION_MINOR - 1)).$LATEST_REVISION -> $RELEASE_VERSION"

  FILE_NAME="$VERSION_MAJOR.$((VERSION_MINOR - 1)).$LATEST_REVISION-$VERSION_MAJOR.$VERSION_MINOR.$VERSION_RELEASE.mi"
  touch "$MI_DIR/$FILE_NAME"

  (cd "$MI_DIR" || exit 1; svn add "$FILE_NAME")
elif [ "$VERSION_MAJOR" -gt 0 ]; then
  LATEST_MINOR=$(ls "$MI_DIR" | grep -Po "$((VERSION_MAJOR - 1))\.[0-9]+\.[0-9]+" | cut -d "." -f2 | sort -nr | head -n1)
  LATEST_REVISION=$(ls "$MI_DIR" | grep -Po "$((VERSION_MAJOR - 1))\.$LATEST_MINOR\.[0-9]+" | cut -d "." -f3 | sort -nr | head -n1)

  HAS_PREVIOUS_VERSION="$(ls "$MI_DIR" | grep -Po "$((VERSION_MAJOR - 1))\.$LATEST_MINOR\.$LATEST_REVISION")"
    if [ -z "$HAS_PREVIOUS_VERSION" ]; then
    echo "Invalid release version, could not find previous version" 1>&2
      exit 1
    fi
  echo "Detected new release is a major, creating migration script for $((VERSION_MAJOR - 1)).$LATEST_MINOR.$LATEST_REVISION -> $RELEASE_VERSION"

  FILE_NAME="$((VERSION_MAJOR - 1)).$LATEST_MINOR.$LATEST_REVISION-$VERSION_MAJOR.$VERSION_MINOR.$VERSION_RELEASE.mi"
  touch "$MI_DIR/$FILE_NAME"

  (cd "$MI_DIR" || exit 1; svn add "$FILE_NAME")
fi
echo "Done creating migration scripts"

ACTUAL_VERSION="$(< .build.v tr -d '\n')"
NEW_VERSION="$((ACTUAL_VERSION + 1))"
[ -a .build.v ] && echo "$ACTUAL_VERSION" > .build.v
[ -a .build.t ] && echo "final" > .build.t

OUT="$(mktemp /tmp/output.XXXXXXXXXX)" || { echo "Failed to create temp file"; exit 1; }

echo "Closing mi version $ACTUAL_VERSION"
cat <<EOF > "$OUT"
Task $TASK_KEY - release: closing version $ACTUAL_VERSION

https://jira.ptin.corppt.com/browse/$TASK_KEY
EOF
svn commit -F "$OUT" \
  --username "$USERNAME" \
  --password "$PASSWORD" \
  --no-auth-cache \
  --trust-server-cert \
  --non-interactive

echo "Tagging mi version $ACTUAL_VERSION"
cat <<EOF > "$OUT"
Task $TASK_KEY - release: tagging version $ACTUAL_VERSION

https://jira.ptin.corppt.com/browse/$TASK_KEY
EOF
svn copy "$MI_SVN_REPO/$MI_SVN_BRANCH" "$MI_SVN_REPO/tags/$ACTUAL_VERSION" -F "$OUT" \
  --username "$USERNAME" \
  --password "$PASSWORD" \
  --no-auth-cache \
  --trust-server-cert \
  --non-interactive

echo "Opening mi version $NEW_VERSION"
[ -a .build.v ] && echo "$NEW_VERSION" > .build.v
[ -a .build.t ] && echo "beta" > .build.t

cat <<EOF > "$OUT"
Task $TASK_KEY - release: opening version $NEW_VERSION

https://jira.ptin.corppt.com/browse/$TASK_KEY
EOF
svn commit -F "$OUT" \
  --username "$USERNAME" \
  --password "$PASSWORD" \
  --no-auth-cache \
  --trust-server-cert \
  --non-interactive

cd "$PORTAL_REPO_DIR" || { echo "SVN checkout failed, could not find repository locally"; exit 1; }
sed -Ei 's|[0-9]+</rpm.requires.na-portal-mi-db>|'"$ACTUAL_VERSION"'</rpm.requires.na-portal-mi-db>|' pom.xml

if (svn status | grep -q pom.xml); then
  echo "Updating mi-db version in project pom.xml"

  cat <<EOF > "$OUT"
Task $TASK_KEY - release: update mi-db to version $ACTUAL_VERSION

https://jira.ptin.corppt.com/browse/$TASK_KEY
EOF

  svn commit -F "$OUT" \
    --username "$USERNAME" \
    --password "$PASSWORD" \
    --no-auth-cache \
    --trust-server-cert \
    --non-interactive
fi